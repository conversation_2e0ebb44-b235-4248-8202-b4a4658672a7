-- Fix for existing organizations that don't have a default supplier
-- Run this script in your Supabase SQL Editor

-- Step 1: First, add the is_default column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'suppliers' AND column_name = 'is_default'
    ) THEN
        ALTER TABLE public.suppliers
        ADD COLUMN is_default BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added is_default column to suppliers table';
    ELSE
        RAISE NOTICE 'is_default column already exists';
    END IF;
END $$;

-- Step 2: Create default supplier for organizations that don't have one
DO $$
DECLARE
    org_record RECORD;
    supplier_count INTEGER;
    new_supplier_id UUID;
    owner_id UUID;
    total_orgs INTEGER := 0;
    fixed_orgs INTEGER := 0;
BEGIN
    -- Count total organizations
    SELECT COUNT(*) INTO total_orgs FROM public.organizations;
    RAISE NOTICE 'Found % organizations to check', total_orgs;
    
    -- Loop through all organizations
    FOR org_record IN SELECT id, name FROM public.organizations LOOP
        -- Check if organization already has a default supplier
        SELECT COUNT(*) INTO supplier_count
        FROM public.suppliers
        WHERE organization_id = org_record.id AND is_default = TRUE;
        
        -- Create default supplier if it doesn't exist
        IF supplier_count = 0 THEN
            -- Get the organization owner
            SELECT user_id INTO owner_id
            FROM public.organization_members
            WHERE organization_id = org_record.id 
            AND role = 'owner'
            LIMIT 1;
            
            -- If no owner found, use a system user
            IF owner_id IS NULL THEN
                owner_id := '00000000-0000-0000-0000-000000000000'::UUID;
            END IF;
            
            -- Create the default supplier
            INSERT INTO public.suppliers (
                organization_id,
                name,
                contact_person,
                email,
                phone,
                address,
                notes,
                is_default,
                created_at,
                updated_at
            ) VALUES (
                org_record.id,
                'Default Supplier',
                'To Be Determined',
                NULL,
                NULL,
                NULL,
                'This is the default supplier used for purchase orders when the actual supplier is not yet determined. You can edit the purchase order later to assign the correct supplier.',
                TRUE,
                NOW(),
                NOW()
            ) RETURNING id INTO new_supplier_id;
            
            fixed_orgs := fixed_orgs + 1;
            RAISE NOTICE 'Created default supplier (%) for organization: % ("%")', new_supplier_id, org_record.id, org_record.name;
        ELSE
            RAISE NOTICE 'Organization % ("%") already has a default supplier', org_record.id, org_record.name;
        END IF;
    END LOOP;
    
    RAISE NOTICE '=== SUMMARY ===';
    RAISE NOTICE 'Total organizations: %', total_orgs;
    RAISE NOTICE 'Organizations fixed: %', fixed_orgs;
    RAISE NOTICE 'Organizations already had default supplier: %', (total_orgs - fixed_orgs);
END $$;

-- Step 3: Verify the results
SELECT 
    o.name as organization_name,
    s.name as default_supplier_name,
    s.id as supplier_id,
    s.is_default
FROM public.organizations o
LEFT JOIN public.suppliers s ON o.id = s.organization_id AND s.is_default = TRUE
ORDER BY o.name;

-- Step 4: Show summary
DO $$
DECLARE
    total_orgs INTEGER;
    orgs_with_default INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_orgs FROM public.organizations;
    SELECT COUNT(DISTINCT s.organization_id) INTO orgs_with_default 
    FROM public.suppliers s 
    WHERE s.is_default = TRUE;
    
    RAISE NOTICE '=== FINAL VERIFICATION ===';
    RAISE NOTICE 'Total organizations: %', total_orgs;
    RAISE NOTICE 'Organizations with default supplier: %', orgs_with_default;
    
    IF total_orgs = orgs_with_default THEN
        RAISE NOTICE '✅ SUCCESS: All organizations now have default suppliers!';
    ELSE
        RAISE NOTICE '⚠️  WARNING: % organizations still missing default suppliers', (total_orgs - orgs_with_default);
    END IF;
END $$;
