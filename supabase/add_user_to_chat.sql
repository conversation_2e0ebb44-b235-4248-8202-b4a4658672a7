-- Add current user to existing chat conversation
-- Run this after disabling <PERSON><PERSON> to ensure user can access the chat

-- Add user as participant to the conversation that was causing access denied error
INSERT INTO public.chat_participants (
  conversation_id,
  user_id,
  is_admin,
  created_at,
  updated_at
) 
VALUES (
  'ee6a7138-43f4-41d5-9121-9aa59378ab59',  -- The conversation ID from the error
  '3429c4a8-578b-4d4a-97d6-e087d7d96c27',  -- The user ID from the error
  false,  -- Not an admin
  NOW(),
  NOW()
) 
ON CONFLICT (conversation_id, user_id) DO NOTHING;  -- Don't duplicate if already exists

-- Verify the user is now a participant
SELECT 
  'User added as participant' as status,
  cp.id,
  cp.conversation_id,
  cp.user_id,
  cp.is_admin,
  cc.name as conversation_name,
  cc.organization_id
FROM public.chat_participants cp
JOIN public.chat_conversations cc ON cp.conversation_id = cc.id
WHERE cp.conversation_id = 'ee6a7138-43f4-41d5-9121-9aa59378ab59'
  AND cp.user_id = '3429c4a8-578b-4d4a-97d6-e087d7d96c27';

-- Also check all conversations in the organization
SELECT 
  'All conversations in org' as status,
  cc.id,
  cc.name,
  cc.is_group,
  cc.created_by,
  COUNT(cp.user_id) as participant_count
FROM public.chat_conversations cc
LEFT JOIN public.chat_participants cp ON cc.id = cp.conversation_id
WHERE cc.organization_id = 'f643b42b-cb9e-4e8d-a3cb-a271e75a6f65'
GROUP BY cc.id, cc.name, cc.is_group, cc.created_by
ORDER BY cc.created_at DESC;
