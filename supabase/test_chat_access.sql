-- Test script to verify chat system access after migration
-- Run this after the migration to ensure everything works

-- Test 1: Check if the helper function works
SELECT public.user_in_organization(
  '3429c4a8-578b-4d4a-97d6-e087d7d96c27'::UUID, 
  'f643b42b-cb9e-4e8d-a3cb-a271e75a6f65'::UUID
) AS user_in_org_test;

-- Test 2: Check organization members table
SELECT 
  user_id,
  organization_id,
  role
FROM public.organization_members 
WHERE organization_id = 'f643b42b-cb9e-4e8d-a3cb-a271e75a6f65'
LIMIT 5;

-- Test 3: Check chat conversations
SELECT 
  id,
  organization_id,
  name,
  is_group,
  created_by,
  created_at
FROM public.chat_conversations 
WHERE organization_id = 'f643b42b-cb9e-4e8d-a3cb-a271e75a6f65'
LIMIT 5;

-- Test 4: Check chat participants
SELECT 
  cp.id,
  cp.conversation_id,
  cp.user_id,
  cp.is_admin,
  cc.organization_id
FROM public.chat_participants cp
JOIN public.chat_conversations cc ON cp.conversation_id = cc.id
WHERE cc.organization_id = 'f643b42b-cb9e-4e8d-a3cb-a271e75a6f65'
LIMIT 5;

-- Test 5: Check if RLS policies are working
-- This should return conversations only for the authenticated user's organization
SET LOCAL role TO authenticated;
SET LOCAL request.jwt.claims TO '{"sub": "3429c4a8-578b-4d4a-97d6-e087d7d96c27"}';

SELECT 
  id,
  organization_id,
  name,
  created_by
FROM public.chat_conversations
LIMIT 3;

-- Reset role
RESET role;
RESET request.jwt.claims;

-- Test 6: Verify no infinite recursion in policies
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
  cc.id,
  cc.name,
  cp.user_id
FROM public.chat_conversations cc
JOIN public.chat_participants cp ON cc.id = cp.conversation_id
WHERE cc.organization_id = 'f643b42b-cb9e-4e8d-a3cb-a271e75a6f65'
LIMIT 1;
