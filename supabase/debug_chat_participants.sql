-- Debug script to check chat participants and fix access issues

-- Check if the user exists in organization_members
SELECT 
  'Organization Members Check' as check_type,
  user_id,
  organization_id,
  role
FROM public.organization_members 
WHERE user_id = '3429c4a8-578b-4d4a-97d6-e087d7d96c27'
  AND organization_id = 'f643b42b-cb9e-4e8d-a3cb-a271e75a6f65';

-- Check existing conversations
SELECT 
  'Existing Conversations' as check_type,
  id,
  organization_id,
  name,
  is_group,
  created_by,
  created_at
FROM public.chat_conversations 
WHERE organization_id = 'f643b42b-cb9e-4e8d-a3cb-a271e75a6f65';

-- Check participants for conversation ee6a7138-43f4-41d5-9121-9aa59378ab59
SELECT 
  'Conversation Participants' as check_type,
  cp.id,
  cp.conversation_id,
  cp.user_id,
  cp.is_admin,
  cp.last_read_message_id,
  cc.organization_id
FROM public.chat_participants cp
JOIN public.chat_conversations cc ON cp.conversation_id = cc.id
WHERE cp.conversation_id = 'ee6a7138-43f4-41d5-9121-9aa59378ab59';

-- If the user is not a participant, add them
-- (Only run this if the user should have access to this conversation)
INSERT INTO public.chat_participants (
  conversation_id,
  user_id,
  is_admin
) 
SELECT 
  'ee6a7138-43f4-41d5-9121-9aa59378ab59',
  '3429c4a8-578b-4d4a-97d6-e087d7d96c27',
  false
WHERE NOT EXISTS (
  SELECT 1 FROM public.chat_participants 
  WHERE conversation_id = 'ee6a7138-43f4-41d5-9121-9aa59378ab59'
    AND user_id = '3429c4a8-578b-4d4a-97d6-e087d7d96c27'
);

-- Verify the user is now a participant
SELECT 
  'Verification After Insert' as check_type,
  cp.id,
  cp.conversation_id,
  cp.user_id,
  cp.is_admin
FROM public.chat_participants cp
WHERE cp.conversation_id = 'ee6a7138-43f4-41d5-9121-9aa59378ab59'
  AND cp.user_id = '3429c4a8-578b-4d4a-97d6-e087d7d96c27';
