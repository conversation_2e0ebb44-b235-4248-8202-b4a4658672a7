# Supabase Edge Functions

This directory contains Edge Functions for the POS system. Edge Functions are serverless functions that run on Supabase's infrastructure.

## Available Functions

### 1. `send-invitation-email`

This function sends an invitation email to a user. It takes an invitation object and generates an invitation URL that the user can use to accept the invitation.

#### Request Format

```json
{
  "invitation": {
    "invitation_id": "uuid",
    "token": "secure-token",
    "email": "<EMAIL>",
    "organization_name": "My Organization",
    "inviter_name": "<PERSON>",
    "role": "admin",
    "organization_id": "uuid"
  }
}
```

#### Response Format

```json
{
  "success": true,
  "message": "Invitation email sent",
  "invitation_url": "http://localhost:5173/auth/accept-invitation?token=secure-token"
}
```

## Shared Code

The `_shared` directory contains code that is shared between multiple functions:

- `cors.ts`: CORS headers for cross-origin requests

## Deployment

To deploy the Edge Functions, run the following command:

```bash
./scripts/deploy-functions.sh
```

This will deploy all functions to Supabase.

## Local Development

To run the functions locally, use the Supabase CLI:

```bash
supabase start
supabase functions serve
```

Then you can test the functions using curl or any HTTP client:

```bash
curl -X POST http://localhost:54321/functions/v1/send-invitation-email \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"invitation":{"invitation_id":"123","token":"abc","email":"<EMAIL>","organization_name":"Test Org","inviter_name":"Admin","role":"member"}}'
```

## Production Configuration

In production, you should set the following environment variables:

- `SITE_URL`: The URL of your application (default: `http://localhost:5173`)
- `SENDGRID_API_KEY`: Your SendGrid API key for sending emails

You can set these variables using the Supabase CLI:

```bash
supabase secrets set SITE_URL=https://your-app.com
supabase secrets set SENDGRID_API_KEY=your-api-key
```
