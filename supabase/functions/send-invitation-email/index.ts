// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

// @ts-ignore: Deno types
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders } from '../_shared/cors.ts';

// @ts-ignore: Deno namespace
const SITE_URL = Deno.env.get('SITE_URL') || 'http://localhost:5173';

interface InvitationEmailPayload {
  invitation_id: string;
  token: string;
  email: string;
  organization_name: string;
  inviter_name: string;
  role: string;
  organization_id?: string;
}

interface RequestType {
  method: string;
  headers: {
    get(name: string): string | null;
  };
  json(): Promise<any>;
}

serve(async (req: RequestType) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Parse the request body first to get the invitation data
    const { invitation } = (await req.json()) as { invitation: InvitationEmailPayload };

    if (!invitation) {
      return new Response(JSON.stringify({ error: 'Missing invitation data' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Extract the JWT token from the Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({ error: 'Missing or invalid Authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // In a production environment, you would:
    // 1. Create a Supabase client with the Auth context of the logged in user
    // 2. Verify the user's permissions to send invitations
    // 3. Log the invitation in a database

    // For now, we'll skip these steps and just simulate sending an email

    // Generate the invitation URL
    const invitationUrl = `${SITE_URL}/auth/accept-invitation?token=${invitation.token}`;
    console.log('Invitation URL:', invitationUrl);

    // Log the email details for debugging
    console.log('Would send invitation email to:', invitation.email);
    console.log('With invitation URL:', invitationUrl);
    console.log('Organization:', invitation.organization_name);
    console.log('Inviter:', invitation.inviter_name);
    console.log('Role:', invitation.role);

    // Here's where you would send the actual email
    // For example, using SendGrid:
    /*
    const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Deno.env.get('SENDGRID_API_KEY')}`,
      },
      body: JSON.stringify({
        personalizations: [
          {
            to: [{ email: invitation.email }],
            subject: `Invitation to join ${invitation.organization_name}`,
          },
        ],
        from: { email: '<EMAIL>', name: 'Your App' },
        content: [
          {
            type: 'text/html',
            value: `
              <h1>You've been invited to join ${invitation.organization_name}</h1>
              <p>${invitation.inviter_name} has invited you to join ${invitation.organization_name} as a ${invitation.role}.</p>
              <p>Click the link below to accept the invitation:</p>
              <p><a href="${invitationUrl}">Accept Invitation</a></p>
              <p>This invitation will expire in 48 hours.</p>
            `,
          },
        ],
      }),
    })
    */

    // For now, we'll just return success with the invitation URL
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Invitation email would be sent in production',
        invitation_url: invitationUrl,
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    );
  } catch (error) {
    console.error('Error processing invitation:', error);
    return new Response(
      JSON.stringify({
        error: error.message,
        success: false,
        // Still return a simulated URL for testing purposes
        invitation_url: `${SITE_URL}/auth/accept-invitation?token=simulated-token-due-to-error`,
      }),
      {
        status: 200, // Return 200 even on error for testing purposes
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    );
  }
});
