-- Migration to implement a customer loyalty points system
-- This adds support for configurable loyalty programs per organization

-- Create loyalty program settings table
CREATE TABLE IF NOT EXISTS public.loyalty_program_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    is_enabled BOOLEAN NOT NULL DEFAULT false,
    points_earning_rate DECIMAL(10, 6) NOT NULL DEFAULT 1.00, -- Points per currency unit
    points_redemption_rate DECIMAL(10, 2) NOT NULL DEFAULT 0.01, -- Currency value per point
    minimum_points_for_redemption INTEGER NOT NULL DEFAULT 100,
    points_expiration_months INTEGER, -- NULL means points never expire
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(organization_id)
);

-- Create loyalty tiers table
CREATE TABLE IF NOT EXISTS public.loyalty_tiers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL,
    minimum_points INTEGER NOT NULL,
    points_multiplier DECIMAL(3, 2) NOT NULL DEFAULT 1.00,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create customer loyalty profiles table
CREATE TABLE IF NOT EXISTS public.customer_loyalty_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    current_points_balance INTEGER NOT NULL DEFAULT 0,
    lifetime_points_earned INTEGER NOT NULL DEFAULT 0,
    lifetime_points_redeemed INTEGER NOT NULL DEFAULT 0,
    tier_id UUID REFERENCES public.loyalty_tiers(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(customer_id, organization_id)
);

-- Create product-specific loyalty settings table
CREATE TABLE IF NOT EXISTS public.product_loyalty_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
    category_id UUID REFERENCES public.categories(id) ON DELETE CASCADE,
    points_multiplier DECIMAL(3, 2) NOT NULL DEFAULT 1.00,
    is_excluded_from_earning BOOLEAN NOT NULL DEFAULT false,
    is_excluded_from_redemption BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CHECK (product_id IS NOT NULL OR category_id IS NOT NULL)
);

-- Create loyalty transactions table
CREATE TABLE IF NOT EXISTS public.loyalty_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL, -- 'earn', 'redeem', 'expire', 'adjust'
    points INTEGER NOT NULL,
    sale_id UUID REFERENCES public.sales(id) ON DELETE SET NULL,
    notes TEXT,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create loyalty promotions table
CREATE TABLE IF NOT EXISTS public.loyalty_promotions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    points_multiplier DECIMAL(3, 2) NOT NULL DEFAULT 2.00,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
    category_id UUID REFERENCES public.categories(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add loyalty_points_used column to sales table
ALTER TABLE public.sales ADD COLUMN IF NOT EXISTS loyalty_points_used INTEGER DEFAULT 0;
ALTER TABLE public.sales ADD COLUMN IF NOT EXISTS loyalty_points_earned INTEGER DEFAULT 0;

-- Add loyalty_points_discount column to sales table
ALTER TABLE public.sales ADD COLUMN IF NOT EXISTS loyalty_points_discount DECIMAL(12, 2) DEFAULT 0;

-- Create function to calculate points for a sale
CREATE OR REPLACE FUNCTION calculate_loyalty_points(
    p_organization_id UUID,
    p_customer_id UUID,
    p_sale_id UUID,
    p_total_amount DECIMAL
) RETURNS INTEGER AS $$
DECLARE
    v_points INTEGER := 0;
    v_settings RECORD;
    v_customer_tier RECORD;
    v_multiplier DECIMAL(3, 2) := 1.0;
BEGIN
    -- Check if loyalty program is enabled for this organization
    SELECT * INTO v_settings FROM loyalty_program_settings
    WHERE organization_id = p_organization_id AND is_enabled = true;

    -- If no settings or program is disabled, return 0 points
    IF NOT FOUND THEN
        RETURN 0;
    END IF;

    -- Get customer tier multiplier if exists
    SELECT t.points_multiplier INTO v_multiplier
    FROM customer_loyalty_profiles p
    JOIN loyalty_tiers t ON p.tier_id = t.id
    WHERE p.customer_id = p_customer_id AND p.organization_id = p_organization_id;

    -- If no tier found, use default multiplier of 1.0
    IF v_multiplier IS NULL THEN
        v_multiplier := 1.0;
    END IF;

    -- Calculate points based on total amount and earning rate
    v_points := FLOOR(p_total_amount * v_settings.points_earning_rate * v_multiplier);

    RETURN v_points;
END;
$$ LANGUAGE plpgsql;

-- Create function to redeem points
CREATE OR REPLACE FUNCTION redeem_loyalty_points(
    p_organization_id UUID,
    p_customer_id UUID,
    p_points INTEGER
) RETURNS DECIMAL AS $$
DECLARE
    v_settings RECORD;
    v_customer_profile RECORD;
    v_discount_value DECIMAL(12, 2) := 0;
BEGIN
    -- Check if loyalty program is enabled for this organization
    SELECT * INTO v_settings FROM loyalty_program_settings
    WHERE organization_id = p_organization_id AND is_enabled = true;

    -- If no settings or program is disabled, return 0 discount
    IF NOT FOUND THEN
        RETURN 0;
    END IF;

    -- Get customer loyalty profile
    SELECT * INTO v_customer_profile FROM customer_loyalty_profiles
    WHERE customer_id = p_customer_id AND organization_id = p_organization_id;

    -- If no profile or not enough points, return 0 discount
    IF NOT FOUND OR v_customer_profile.current_points_balance < p_points THEN
        RETURN 0;
    END IF;

    -- Calculate discount value based on redemption rate
    v_discount_value := p_points * v_settings.points_redemption_rate;

    RETURN v_discount_value;
END;
$$ LANGUAGE plpgsql;

-- Drop any existing RLS policies for the new tables
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON public.loyalty_program_settings;
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON public.customer_loyalty_profiles;
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON public.loyalty_tiers;
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON public.product_loyalty_settings;
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON public.loyalty_transactions;
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON public.loyalty_promotions;

-- Create RLS policies for the new tables
CREATE POLICY "Enable all access for authenticated users" ON public.loyalty_program_settings FOR ALL TO authenticated USING (true) WITH CHECK (true);
CREATE POLICY "Enable all access for authenticated users" ON public.customer_loyalty_profiles FOR ALL TO authenticated USING (true) WITH CHECK (true);
CREATE POLICY "Enable all access for authenticated users" ON public.loyalty_tiers FOR ALL TO authenticated USING (true) WITH CHECK (true);
CREATE POLICY "Enable all access for authenticated users" ON public.product_loyalty_settings FOR ALL TO authenticated USING (true) WITH CHECK (true);
CREATE POLICY "Enable all access for authenticated users" ON public.loyalty_transactions FOR ALL TO authenticated USING (true) WITH CHECK (true);
CREATE POLICY "Enable all access for authenticated users" ON public.loyalty_promotions FOR ALL TO authenticated USING (true) WITH CHECK (true);
