-- Create a function to calculate base quantity from UoM quantity
CREATE OR REPLACE FUNCTION calculate_base_quantity(
  p_product_id UUID,
  p_uom_id UUID,
  p_quantity NUMERIC
)
RETURNS NUMERIC AS $$
DECLARE
  v_conversion_factor NUMERIC;
  v_base_quantity NUMERIC;
BEGIN
  -- Get the conversion factor for this UoM
  SELECT conversion_factor INTO v_conversion_factor
  FROM public.product_uoms
  WHERE product_id = p_product_id
  AND uom_id = p_uom_id;
  
  -- If no conversion factor found, return the original quantity
  IF v_conversion_factor IS NULL THEN
    RETURN p_quantity;
  END IF;
  
  -- Calculate the base quantity
  v_base_quantity := p_quantity * v_conversion_factor;
  
  RETURN v_base_quantity;
END;
$$ LANGUAGE plpgsql;

-- Create a function to calculate UoM quantity from base quantity
CREATE OR REPLACE FUNCTION calculate_uom_quantity(
  p_product_id UUID,
  p_uom_id UUID,
  p_base_quantity NUMERIC
)
RETURNS NUMERIC AS $$
DECLARE
  v_conversion_factor NUMERIC;
  v_uom_quantity NUMERIC;
BEGIN
  -- Get the conversion factor for this UoM
  SELECT conversion_factor INTO v_conversion_factor
  FROM public.product_uoms
  WHERE product_id = p_product_id
  AND uom_id = p_uom_id;
  
  -- If no conversion factor found, return the original quantity
  IF v_conversion_factor IS NULL OR v_conversion_factor = 0 THEN
    RETURN p_base_quantity;
  END IF;
  
  -- Calculate the UoM quantity
  v_uom_quantity := p_base_quantity / v_conversion_factor;
  
  RETURN v_uom_quantity;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically calculate base_quantity when quantity or uom_id changes

-- For purchase_request_items
CREATE OR REPLACE FUNCTION update_pri_base_quantity()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.quantity IS NOT NULL AND NEW.uom_id IS NOT NULL THEN
    NEW.base_quantity := calculate_base_quantity(NEW.product_id, NEW.uom_id, NEW.quantity);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_pri_base_quantity
BEFORE INSERT OR UPDATE OF quantity, uom_id ON public.purchase_request_items
FOR EACH ROW
EXECUTE FUNCTION update_pri_base_quantity();

-- For purchase_order_items
CREATE OR REPLACE FUNCTION update_poi_base_quantity()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.quantity IS NOT NULL AND NEW.uom_id IS NOT NULL THEN
    NEW.base_quantity := calculate_base_quantity(NEW.product_id, NEW.uom_id, NEW.quantity);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_poi_base_quantity
BEFORE INSERT OR UPDATE OF quantity, uom_id ON public.purchase_order_items
FOR EACH ROW
EXECUTE FUNCTION update_poi_base_quantity();

-- For inventory_receipt_items
CREATE OR REPLACE FUNCTION update_iri_base_quantity()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.quantity IS NOT NULL AND NEW.uom_id IS NOT NULL THEN
    NEW.base_quantity := calculate_base_quantity(NEW.product_id, NEW.uom_id, NEW.quantity);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_iri_base_quantity
BEFORE INSERT OR UPDATE OF quantity, uom_id ON public.inventory_receipt_items
FOR EACH ROW
EXECUTE FUNCTION update_iri_base_quantity();

-- For sale_items
CREATE OR REPLACE FUNCTION update_si_base_quantity()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.quantity IS NOT NULL AND NEW.uom_id IS NOT NULL THEN
    NEW.base_quantity := calculate_base_quantity(NEW.product_id, NEW.uom_id, NEW.quantity);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_si_base_quantity
BEFORE INSERT OR UPDATE OF quantity, uom_id ON public.sale_items
FOR EACH ROW
EXECUTE FUNCTION update_si_base_quantity();
