-- Create purchase_requests table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.purchase_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  request_number VARCHAR(50) NOT NULL,
  requester_id UUID NOT NULL REFERENCES auth.users(id),
  status VARCHAR(20) NOT NULL DEFAULT 'draft',
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(organization_id, request_number)
);

-- Create purchase_request_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.purchase_request_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  purchase_request_id UUID NOT NULL REFERENCES public.purchase_requests(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES public.products(id),
  quantity NUMERIC(15, 5) NOT NULL,
  uom_id UUID NOT NULL REFERENCES public.units_of_measurement(id),
  base_quantity NUMERIC(15, 5) NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create index on purchase_request_items
CREATE INDEX IF NOT EXISTS idx_purchase_request_items_purchase_request_id ON public.purchase_request_items(purchase_request_id);
CREATE INDEX IF NOT EXISTS idx_purchase_request_items_product_id ON public.purchase_request_items(product_id);
CREATE INDEX IF NOT EXISTS idx_purchase_request_items_uom_id ON public.purchase_request_items(uom_id);

-- Create trigger to update updated_at on purchase_requests
CREATE OR REPLACE FUNCTION public.update_purchase_requests_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_purchase_requests_updated_at ON public.purchase_requests;
CREATE TRIGGER update_purchase_requests_updated_at
BEFORE UPDATE ON public.purchase_requests
FOR EACH ROW
EXECUTE FUNCTION public.update_purchase_requests_updated_at();

-- Create trigger to update updated_at on purchase_request_items
CREATE OR REPLACE FUNCTION public.update_purchase_request_items_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_purchase_request_items_updated_at ON public.purchase_request_items;
CREATE TRIGGER update_purchase_request_items_updated_at
BEFORE UPDATE ON public.purchase_request_items
FOR EACH ROW
EXECUTE FUNCTION public.update_purchase_request_items_updated_at();

-- Create trigger to calculate base_quantity on purchase_request_items
CREATE OR REPLACE FUNCTION public.calculate_purchase_request_item_base_quantity()
RETURNS TRIGGER AS $$
DECLARE
  conversion_factor NUMERIC;
BEGIN
  -- Get the conversion factor from the product_uoms table
  SELECT pu.conversion_factor INTO conversion_factor
  FROM public.product_uoms pu
  WHERE pu.product_id = NEW.product_id AND pu.uom_id = NEW.uom_id;
  
  -- If conversion factor is found, calculate base_quantity
  IF conversion_factor IS NOT NULL THEN
    NEW.base_quantity := NEW.quantity * conversion_factor;
  ELSE
    -- If no conversion factor is found, use quantity as base_quantity
    NEW.base_quantity := NEW.quantity;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS calculate_purchase_request_item_base_quantity ON public.purchase_request_items;
CREATE TRIGGER calculate_purchase_request_item_base_quantity
BEFORE INSERT OR UPDATE OF quantity, uom_id ON public.purchase_request_items
FOR EACH ROW
EXECUTE FUNCTION public.calculate_purchase_request_item_base_quantity();

-- Enable RLS on purchase_requests
ALTER TABLE public.purchase_requests ENABLE ROW LEVEL SECURITY;

-- Enable RLS on purchase_request_items
ALTER TABLE public.purchase_request_items ENABLE ROW LEVEL SECURITY;

-- Create permissive policy for purchase_requests
CREATE POLICY "Allow all authenticated users to manage purchase requests"
ON public.purchase_requests
FOR ALL
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');

-- Create permissive policy for purchase_request_items
CREATE POLICY "Allow all authenticated users to manage purchase request items"
ON public.purchase_request_items
FOR ALL
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');
