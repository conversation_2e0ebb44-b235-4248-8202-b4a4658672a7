-- Migration: Remove Auto-Creation Triggers and Functions
-- Keep only manual payable creation functionality

-- =====================================================
-- 1. DROP ALL AUTO-CREATION TRIGGERS
-- =====================================================

-- Drop auto-creation triggers
DROP TRIGGER IF EXISTS trigger_auto_create_payable_from_receipt ON public.inventory_receipts;
DROP TRIGGER IF EXISTS trigger_auto_create_payable_from_receipt_debug ON public.inventory_receipts;
DROP TRIGGER IF EXISTS trigger_simple_receipt_debug ON public.inventory_receipts;

-- =====================================================
-- 2. DROP AUTO-CREATION FUNCTIONS
-- =====================================================

-- Drop auto-creation functions
DROP FUNCTION IF EXISTS auto_create_payable_from_receipt();
DROP FUNCTION IF EXISTS auto_create_payable_from_receipt_debug();
DROP FUNCTION IF EXISTS simple_receipt_debug();

-- =====================================================
-- 3. DROP DEBUG FUNCTIONS (Keep only essential ones)
-- =====================================================

-- Drop debug functions that are no longer needed
DROP FUNCTION IF EXISTS test_payable_creation(UUID);
DROP FUNCTION IF EXISTS manual_create_payable_from_receipt(UUID);

-- =====================================================
-- 4. KEEP ESSENTIAL FUNCTIONS
-- =====================================================

-- Keep these functions as they're still useful:
-- - check_payable_workflow_readiness() - for system health checks
-- - resolve_payable_source_metadata() - for payable details
-- - calculate_payable_aging() - for aging reports
-- - validate_payable_multi_tenancy() - for security
-- - validate_payment_multi_tenancy() - for security
-- - validate_withholding_tax() - for business logic
-- - update_payable_balance() - for payment processing
-- - prevent_paid_payable_deletion() - for audit protection

-- =====================================================
-- 5. CLEAN UP COMMENTS AND DOCUMENTATION
-- =====================================================

-- Add comments to clarify the new manual-only approach
COMMENT ON TABLE public.payables IS 'Payables table - Manual creation only via SendToPayableButton component';

-- Update function comments
COMMENT ON FUNCTION check_payable_workflow_readiness() IS 'Check system readiness for manual payable creation';

-- =====================================================
-- 6. VERIFICATION
-- =====================================================

DO $$
BEGIN
    -- Verify auto-creation triggers are removed
    IF EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name LIKE '%auto_create_payable%'
    ) THEN
        RAISE WARNING 'Auto-creation triggers still exist!';
    ELSE
        RAISE NOTICE '✅ Auto-creation triggers successfully removed';
    END IF;
    
    -- Verify auto-creation functions are removed
    IF EXISTS (
        SELECT 1 FROM information_schema.routines 
        WHERE routine_name LIKE '%auto_create_payable%'
    ) THEN
        RAISE WARNING 'Auto-creation functions still exist!';
    ELSE
        RAISE NOTICE '✅ Auto-creation functions successfully removed';
    END IF;
    
    -- Verify essential functions still exist
    IF EXISTS (
        SELECT 1 FROM information_schema.routines 
        WHERE routine_name = 'check_payable_workflow_readiness'
    ) THEN
        RAISE NOTICE '✅ Essential functions preserved';
    ELSE
        RAISE WARNING 'Essential functions missing!';
    END IF;
    
    RAISE NOTICE '🎉 Migration completed - Manual payable creation only';
    RAISE NOTICE '📝 Use SendToPayableButton component for creating payables';
END $$;
