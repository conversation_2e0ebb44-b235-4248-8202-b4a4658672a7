-- Migration: Update database types for payables tables
-- This ensures TypeScript types are properly generated

-- Add payment_terms_days to suppliers if not exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'suppliers' AND column_name = 'payment_terms_days'
    ) THEN
        ALTER TABLE public.suppliers
        ADD COLUMN payment_terms_days INTEGER DEFAULT 30;
        
        COMMENT ON COLUMN public.suppliers.payment_terms_days IS 'Default payment terms in days for calculating due dates';
    END IF;
END $$;

-- Ensure payables and payable_payments tables exist with correct structure
-- (This is mainly for type generation - tables should already exist from previous migration)

-- Verify payables table structure
DO $$
BEGIN
    -- Check if payables table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payables') THEN
        RAISE EXCEPTION 'Payables table does not exist. Please run migration 00070_centralized_payables_system.sql first.';
    END IF;
    
    -- Check if payable_payments table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payable_payments') THEN
        RAISE EXCEPTION 'Payable_payments table does not exist. Please run migration 00070_centralized_payables_system.sql first.';
    END IF;
    
    RAISE NOTICE 'Payables tables verified successfully for type generation';
END $$;
