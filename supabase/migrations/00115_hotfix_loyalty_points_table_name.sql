-- HOTFIX: Fix loyalty points table name in database functions
-- The functions were referencing 'customer_loyalty_points' but the correct table is 'customer_loyalty_profiles'

-- Drop all possible versions of the create_sale_with_items function
DO $$
DECLARE
    func_record RECORD;
BEGIN
    -- Find all functions with the name create_sale_with_items and drop them
    FOR func_record IN
        SELECT
            p.proname,
            pg_get_function_identity_arguments(p.oid) as args
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname = 'create_sale_with_items'
    LOOP
        EXECUTE 'DROP FUNCTION IF EXISTS public.create_sale_with_items(' || func_record.args || ') CASCADE';
    END LOOP;
END $$;

CREATE OR REPLACE FUNCTION public.create_sale_with_items(
  p_organization_id UUID,
  p_customer_id UUID,
  p_invoice_number TEXT,
  p_status TEXT,
  p_subtotal DECIMAL,
  p_tax_amount DECIMAL,
  p_discount_amount DECIMAL,
  p_total_amount DECIMAL,
  p_payment_method TEXT,
  p_notes TEXT,
  p_created_by UUID,
  p_items JSONB,
  p_loyalty_points_used INTEGER DEFAULT 0,
  p_loyalty_points_discount DECIMAL DEFAULT 0,
  p_cash_tendered DECIMAL DEFAULT NULL,
  p_change_amount DECIMAL DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  v_sale_id UUID;
  v_item JSONB;
  v_product_id UUID;
  v_quantity INTEGER;
  v_base_quantity NUMERIC;
  v_uom_id UUID;
  v_loyalty_points_earned INTEGER := 0;
  v_is_eligible BOOLEAN;
BEGIN
  -- Calculate loyalty points earned if customer is provided
  IF p_customer_id IS NOT NULL THEN
    -- Check if customer is eligible for loyalty points
    SELECT is_customer_loyalty_eligible(p_customer_id) INTO v_is_eligible;

    IF v_is_eligible THEN
      SELECT calculate_loyalty_points(p_organization_id, p_customer_id, NULL, p_total_amount)
      INTO v_loyalty_points_earned;
    END IF;
  END IF;

  -- Create the sale record
  INSERT INTO public.sales (
    organization_id,
    customer_id,
    invoice_number,
    sale_date,
    status,
    subtotal,
    tax_amount,
    discount_amount,
    total_amount,
    payment_method,
    cash_tendered,
    change_amount,
    notes,
    created_by,
    loyalty_points_used,
    loyalty_points_earned,
    loyalty_points_discount
  ) VALUES (
    p_organization_id,
    p_customer_id,
    p_invoice_number,
    NOW(),
    p_status,
    p_subtotal,
    p_tax_amount,
    p_discount_amount,
    p_total_amount,
    p_payment_method,
    p_cash_tendered,
    p_change_amount,
    p_notes,
    p_created_by,
    p_loyalty_points_used,
    v_loyalty_points_earned,
    p_loyalty_points_discount
  ) RETURNING id INTO v_sale_id;

  -- Process each item
  FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
  LOOP
    -- Extract values from the item JSON
    v_product_id := (v_item->>'product_id')::UUID;
    v_quantity := (v_item->>'quantity')::INTEGER;
    v_base_quantity := (v_item->>'base_quantity')::NUMERIC;
    v_uom_id := (v_item->>'uom_id')::UUID;

    -- Insert the sale item (WITHOUT created_by column)
    INSERT INTO public.sale_items (
      sale_id,
      product_id,
      quantity,
      unit_price,
      uom_id,
      base_quantity,
      tax_rate,
      tax_amount,
      discount_amount,
      total_amount,
      notes
    ) VALUES (
      v_sale_id,
      v_product_id,
      v_quantity,
      (v_item->>'unit_price')::DECIMAL,
      v_uom_id,
      v_base_quantity,
      (v_item->>'tax_rate')::DECIMAL,
      (v_item->>'tax_amount')::DECIMAL,
      (v_item->>'discount_amount')::DECIMAL,
      (v_item->>'total_amount')::DECIMAL,
      v_item->>'notes'
    );

    -- Create inventory transaction for this sale item
    INSERT INTO public.inventory_transactions (
      organization_id,
      product_id,
      transaction_type,
      quantity,
      uom_id,
      reference_id,
      reference_type,
      notes,
      created_by
    ) VALUES (
      p_organization_id,
      v_product_id,
      'sale',
      -v_base_quantity,  -- Negative quantity for sales
      v_uom_id,
      v_sale_id,
      'sale',
      'Sale: ' || p_invoice_number,
      p_created_by
    );
  END LOOP;

  -- Update customer loyalty points if applicable
  IF p_customer_id IS NOT NULL THEN
    -- Handle loyalty profile update (create or update)
    INSERT INTO customer_loyalty_profiles (
      customer_id,
      organization_id,
      current_points_balance,
      lifetime_points_earned,
      lifetime_points_redeemed
    )
    VALUES (
      p_customer_id,
      p_organization_id,
      v_loyalty_points_earned - p_loyalty_points_used, -- Net points change
      v_loyalty_points_earned, -- Points earned this transaction
      p_loyalty_points_used    -- Points redeemed this transaction
    )
    ON CONFLICT (customer_id, organization_id)
    DO UPDATE SET
      current_points_balance = customer_loyalty_profiles.current_points_balance + v_loyalty_points_earned - p_loyalty_points_used,
      lifetime_points_earned = customer_loyalty_profiles.lifetime_points_earned + v_loyalty_points_earned,
      lifetime_points_redeemed = customer_loyalty_profiles.lifetime_points_redeemed + p_loyalty_points_used,
      updated_at = NOW();

    -- Record loyalty points earned transaction if points were earned
    IF v_loyalty_points_earned > 0 THEN
      INSERT INTO loyalty_transactions (
        customer_id,
        organization_id,
        transaction_type,
        points,
        sale_id,
        notes,
        created_by
      ) VALUES (
        p_customer_id,
        p_organization_id,
        'earn',
        v_loyalty_points_earned,
        v_sale_id,
        'Points earned from sale ' || p_invoice_number,
        p_created_by
      );
    END IF;

    -- Record loyalty points redeemed transaction if points were used
    IF p_loyalty_points_used > 0 THEN
      INSERT INTO loyalty_transactions (
        customer_id,
        organization_id,
        transaction_type,
        points,
        sale_id,
        notes,
        created_by
      ) VALUES (
        p_customer_id,
        p_organization_id,
        'redeem',
        -p_loyalty_points_used, -- Negative points for redemption
        v_sale_id,
        'Points redeemed for discount on sale ' || p_invoice_number,
        p_created_by
      );
    END IF;
  END IF;

  -- Return the sale ID
  RETURN jsonb_build_object('sale_id', v_sale_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.create_sale_with_items TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION public.create_sale_with_items IS 'Creates a sale with items and handles loyalty points using the correct customer_loyalty_profiles table';
