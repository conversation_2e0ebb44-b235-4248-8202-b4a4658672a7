-- This migration creates a function that completely bypasses <PERSON><PERSON>
-- to create user profiles

-- Drop the function if it exists
DROP FUNCTION IF EXISTS public.create_profile_bypass_rls(uuid, text, text);

-- Create a function that bypasses <PERSON><PERSON> completely
CREATE OR REPLACE FUNCTION public.create_profile_bypass_rls(
  user_id uuid,
  first_name text,
  last_name text
) RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER -- This runs with the privileges of the function creator
SET search_path = public -- Prevent search path injection
AS $$
DECLARE
  v_result json;
BEGIN
  -- Disable R<PERSON> temporarily for this transaction
  ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
  
  BEGIN
    -- Insert the profile
    INSERT INTO public.profiles (id, first_name, last_name)
    VALUES (user_id, first_name, last_name);
    
    -- Re-enable RLS
    ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
    
    -- Return success with profile data
    v_result := json_build_object(
      'success', true,
      'profile', json_build_object(
        'id', user_id,
        'first_name', first_name,
        'last_name', last_name
      )
    );
    
    RETURN v_result;
  EXCEPTION
    WHEN OTHERS THEN
      -- Re-enable RLS even if there's an error
      ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
      
      -- Return error information
      v_result := json_build_object(
        'success', false,
        'error', SQLERRM,
        'error_detail', SQLSTATE
      );
      
      RETURN v_result;
  END;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.create_profile_bypass_rls(uuid, text, text) TO authenticated;

-- Add a comment explaining the function
COMMENT ON FUNCTION public.create_profile_bypass_rls(uuid, text, text) IS 
'Creates a user profile, bypassing RLS completely. 
This function should only be used when necessary, as it temporarily disables RLS.';
