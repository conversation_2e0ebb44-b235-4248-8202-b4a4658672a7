-- Migration: Fix payables source_id type for payroll integration
-- Changes source_id from UUID to TEXT to support compound identifiers

-- =====================================================
-- 1. CHANGE SOURCE_ID TYPE FROM UUID TO TEXT
-- =====================================================

-- First, drop the unique constraint temporarily
ALTER TABLE public.payables DROP CONSTRAINT IF EXISTS unique_source_per_org;

-- Change the column type from UUID to TEXT
ALTER TABLE public.payables ALTER COLUMN source_id TYPE TEXT;

-- Recreate the unique constraint with TEXT type
ALTER TABLE public.payables
ADD CONSTRAINT unique_source_per_org
UNIQUE (organization_id, source_type, source_id);

-- Add comment explaining the change
COMMENT ON COLUMN public.payables.source_id IS 'Source identifier - can be UUID for single sources or compound string for grouped sources (e.g., payroll period + type)';

-- =====================================================
-- 2. UPDATE EXISTING DATA (IF ANY)
-- =====================================================

-- No data migration needed as we're just changing the type constraint
-- Existing UUID values will remain valid as TEXT

-- =====================================================
-- 3. UPDATE VALIDATION FUNCTION FOR PAYROLL
-- =====================================================

-- Update the validation function to handle new payroll source_id formats
CREATE OR REPLACE FUNCTION validate_payable_multi_tenancy()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate supplier belongs to same organization
    IF NEW.supplier_id IS NOT NULL THEN
        IF NOT EXISTS (
            SELECT 1 FROM public.suppliers
            WHERE id = NEW.supplier_id AND organization_id = NEW.organization_id
        ) THEN
            RAISE EXCEPTION 'SECURITY VIOLATION: Supplier % does not belong to organization %',
                NEW.supplier_id, NEW.organization_id;
        END IF;
    END IF;

    -- Validate employee belongs to same organization
    IF NEW.employee_id IS NOT NULL THEN
        IF NOT EXISTS (
            SELECT 1 FROM public.employees
            WHERE id = NEW.employee_id AND organization_id = NEW.organization_id
        ) THEN
            RAISE EXCEPTION 'SECURITY VIOLATION: Employee % does not belong to organization %',
                NEW.employee_id, NEW.organization_id;
        END IF;
    END IF;

    -- Validate source_id belongs to same organization based on source_type
    IF NEW.source_type = 'purchase_receipt' THEN
        IF NOT EXISTS (
            SELECT 1 FROM public.inventory_receipts
            WHERE id = NEW.source_id::uuid AND organization_id = NEW.organization_id
        ) THEN
            RAISE EXCEPTION 'SECURITY VIOLATION: Purchase receipt % does not belong to organization %',
                NEW.source_id, NEW.organization_id;
        END IF;
    ELSIF NEW.source_type = 'payroll' THEN
        -- For payroll, source_id can be:
        -- 1. A payroll_item_id (UUID) for employee salaries
        -- 2. A compound string like "period_id-contribution_type" for government remittances

        -- Try to validate as payroll_item_id first
        IF NEW.source_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
            -- It's a UUID, check if it's a valid payroll_item_id
            IF NOT EXISTS (
                SELECT 1 FROM public.payroll_items
                WHERE id = NEW.source_id::uuid AND organization_id = NEW.organization_id
            ) THEN
                RAISE EXCEPTION 'SECURITY VIOLATION: Payroll item % does not belong to organization %',
                    NEW.source_id, NEW.organization_id;
            END IF;
        ELSE
            -- It's a compound string, extract the payroll_period_id part
            DECLARE
                period_id_part TEXT;
            BEGIN
                -- Extract the UUID part before the first dash (if compound)
                period_id_part := split_part(NEW.source_id, '-', 1) || '-' ||
                                 split_part(NEW.source_id, '-', 2) || '-' ||
                                 split_part(NEW.source_id, '-', 3) || '-' ||
                                 split_part(NEW.source_id, '-', 4) || '-' ||
                                 split_part(NEW.source_id, '-', 5);

                -- Validate the payroll period exists and belongs to organization
                IF NOT EXISTS (
                    SELECT 1 FROM public.payroll_periods
                    WHERE id = period_id_part::uuid AND organization_id = NEW.organization_id
                ) THEN
                    RAISE EXCEPTION 'SECURITY VIOLATION: Payroll period % does not belong to organization %',
                        period_id_part, NEW.organization_id;
                END IF;
            END;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. UPDATE SOURCE METADATA FUNCTION
-- =====================================================

-- Drop the existing function first (required when changing return type)
DROP FUNCTION IF EXISTS resolve_payable_source_metadata(UUID);

-- Create the updated source metadata function to handle TEXT source_id
CREATE OR REPLACE FUNCTION resolve_payable_source_metadata(payable_id UUID)
RETURNS TABLE(
    source_type TEXT,
    source_id TEXT,
    source_name TEXT,
    source_description TEXT,
    source_date DATE,
    source_amount NUMERIC(12,2),
    source_url TEXT
) AS $$
DECLARE
    payable_record RECORD;
    period_id_part TEXT;
BEGIN
    -- Get the payable record
    SELECT p.source_type, p.source_id, p.amount, p.invoice_date, p.organization_id
    INTO payable_record
    FROM public.payables p
    WHERE p.id = payable_id;

    IF NOT FOUND THEN
        RETURN;
    END IF;

    -- Handle different source types
    IF payable_record.source_type = 'purchase_receipt' THEN
        -- For purchase receipts, source_id should be a UUID
        -- Calculate total amount from receipt items since inventory_receipts doesn't have total_amount
        RETURN QUERY
        SELECT
            payable_record.source_type,
            payable_record.source_id,
            COALESCE(ir.receipt_number, 'Unknown Receipt') as source_name,
            CONCAT('Purchase Receipt - ', ir.receipt_number) as source_description,
            ir.receipt_date::DATE as source_date,
            COALESCE(
                (SELECT SUM(iri.quantity * iri.unit_cost)
                 FROM public.inventory_receipt_items iri
                 WHERE iri.inventory_receipt_id = ir.id),
                0
            ) as source_amount,
            CONCAT('/inventory/receipts/', ir.id) as source_url
        FROM public.inventory_receipts ir
        WHERE ir.id = payable_record.source_id::uuid
        AND ir.organization_id = payable_record.organization_id;

    ELSIF payable_record.source_type = 'payroll' THEN
        -- For payroll, handle both UUID (payroll items) and compound (government remittances)

        -- Check if it's a UUID format (payroll item)
        IF payable_record.source_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
            -- It's a payroll item ID
            RETURN QUERY
            SELECT
                payable_record.source_type,
                payable_record.source_id,
                CONCAT(e.first_name, ' ', e.last_name, ' - ', pp.name) as source_name,
                CONCAT('Employee Salary - ', e.first_name, ' ', e.last_name, ' for period ', pp.name) as source_description,
                pp.end_date::DATE as source_date,
                pi.net_pay as source_amount,
                CONCAT('/payroll/periods/', pp.id) as source_url
            FROM public.payroll_items pi
            JOIN public.employees e ON e.id = pi.employee_id
            JOIN public.payroll_periods pp ON pp.id = pi.payroll_period_id
            WHERE pi.id = payable_record.source_id::uuid
            AND pi.organization_id = payable_record.organization_id;
        ELSE
            -- It's a compound string (government remittance)
            -- Extract the period ID part
            period_id_part := split_part(payable_record.source_id, '-', 1) || '-' ||
                             split_part(payable_record.source_id, '-', 2) || '-' ||
                             split_part(payable_record.source_id, '-', 3) || '-' ||
                             split_part(payable_record.source_id, '-', 4) || '-' ||
                             split_part(payable_record.source_id, '-', 5);

            RETURN QUERY
            SELECT
                payable_record.source_type,
                payable_record.source_id,
                CONCAT('Government Remittance - ', pp.name) as source_name,
                CONCAT('Government contributions and taxes for payroll period ', pp.name) as source_description,
                pp.end_date::DATE as source_date,
                payable_record.amount as source_amount,
                CONCAT('/payroll/periods/', pp.id) as source_url
            FROM public.payroll_periods pp
            WHERE pp.id = period_id_part::uuid
            AND pp.organization_id = payable_record.organization_id;
        END IF;

    ELSIF payable_record.source_type = 'utility_bill' THEN
        -- For utility bills, return basic info
        RETURN QUERY
        SELECT
            payable_record.source_type,
            payable_record.source_id,
            CONCAT('Utility Bill - ', payable_record.source_id) as source_name,
            CONCAT('Utility bill payment') as source_description,
            payable_record.invoice_date::DATE as source_date,
            payable_record.amount as source_amount,
            NULL::TEXT as source_url;

    ELSIF payable_record.source_type = 'government_remittance' THEN
        -- For government remittances, return basic info
        RETURN QUERY
        SELECT
            payable_record.source_type,
            payable_record.source_id,
            CONCAT('Government Remittance - ', payable_record.source_id) as source_name,
            CONCAT('Government tax or contribution payment') as source_description,
            payable_record.invoice_date::DATE as source_date,
            payable_record.amount as source_amount,
            NULL::TEXT as source_url;

    ELSIF payable_record.source_type = 'loan_repayment' THEN
        -- For loan repayments, return basic info
        RETURN QUERY
        SELECT
            payable_record.source_type,
            payable_record.source_id,
            CONCAT('Loan Repayment - ', payable_record.source_id) as source_name,
            CONCAT('Loan repayment installment') as source_description,
            payable_record.invoice_date::DATE as source_date,
            payable_record.amount as source_amount,
            NULL::TEXT as source_url;

    ELSE
        -- For manual entries and other unknown source types
        RETURN QUERY
        SELECT
            payable_record.source_type,
            payable_record.source_id,
            CONCAT('Manual Entry - ', payable_record.source_id) as source_name,
            CONCAT('Manually created payable of type: ', payable_record.source_type) as source_description,
            payable_record.invoice_date::DATE as source_date,
            payable_record.amount as source_amount,
            NULL::TEXT as source_url;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. VERIFICATION
-- =====================================================

-- Verify the column type change
DO $$
BEGIN
    -- Check if the column type is now TEXT
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'payables'
        AND column_name = 'source_id'
        AND data_type = 'text'
    ) THEN
        RAISE EXCEPTION 'source_id column type change failed';
    END IF;

    -- Check if the unique constraint exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE table_name = 'payables'
        AND constraint_name = 'unique_source_per_org'
        AND constraint_type = 'UNIQUE'
    ) THEN
        RAISE EXCEPTION 'unique_source_per_org constraint recreation failed';
    END IF;

    RAISE NOTICE 'Migration completed successfully: source_id is now TEXT with updated validation';
END $$;
