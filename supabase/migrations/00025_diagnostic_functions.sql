-- Function to check if a table exists
CREATE OR REPLACE FUNCTION public.check_table_exists(table_name text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  table_exists boolean;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = check_table_exists.table_name
  ) INTO table_exists;
  
  RETURN table_exists;
END;
$$;

-- Function to run diagnostic queries safely
CREATE OR REPLACE FUNCTION public.run_diagnostic_query(query_text text)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result jsonb;
  is_select boolean;
BEGIN
  -- Check if the query is a SELECT query
  is_select := query_text ~* '^SELECT';
  
  IF NOT is_select THEN
    RAISE EXCEPTION 'Only SELECT queries are allowed for diagnostics';
  END IF;
  
  -- Execute the query and return the result as JSON
  EXECUTE 'SELECT jsonb_agg(row_to_json(t)) FROM (' || query_text || ') t' INTO result;
  
  RETURN result;
END;
$$;

-- Grant permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.check_table_exists TO authenticated;
GRANT EXECUTE ON FUNCTION public.run_diagnostic_query TO authenticated;
