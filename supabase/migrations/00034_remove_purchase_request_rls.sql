-- Drop existing RLS policies for purchase_requests
DROP POLICY IF EXISTS "Users can view their organization's purchase requests" ON public.purchase_requests;
DROP POLICY IF EXISTS "Users can create purchase requests in their organization" ON public.purchase_requests;
DROP POLICY IF EXISTS "Only owners and admins can update purchase requests" ON public.purchase_requests;
DROP POLICY IF EXISTS "Only owners and admins can delete purchase requests" ON public.purchase_requests;

-- Create a single permissive policy for purchase_requests
CREATE POLICY "Allow all authenticated users to manage purchase requests"
ON public.purchase_requests
FOR ALL
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');

-- Drop existing RLS policies for purchase_request_items
DROP POLICY IF EXISTS "Users can view their organization's purchase request items" ON public.purchase_request_items;
DROP POLICY IF EXISTS "Users can create purchase request items in their organization" ON public.purchase_request_items;
DROP POLICY IF EXISTS "Only owners and admins can update purchase request items" ON public.purchase_request_items;
DROP POLICY IF EXISTS "Only owners and admins can delete purchase request items" ON public.purchase_request_items;

-- Create a single permissive policy for purchase_request_items
CREATE POLICY "Allow all authenticated users to manage purchase request items"
ON public.purchase_request_items
FOR ALL
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');

-- Drop existing RLS policies for purchase_orders
DROP POLICY IF EXISTS "Users can view their organization's purchase orders" ON public.purchase_orders;
DROP POLICY IF EXISTS "Users can create purchase orders in their organization" ON public.purchase_orders;
DROP POLICY IF EXISTS "Only owners and admins can update purchase orders" ON public.purchase_orders;
DROP POLICY IF EXISTS "Only owners and admins can delete purchase orders" ON public.purchase_orders;

-- Create a single permissive policy for purchase_orders
CREATE POLICY "Allow all authenticated users to manage purchase orders"
ON public.purchase_orders
FOR ALL
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');

-- Drop existing RLS policies for purchase_order_items
DROP POLICY IF EXISTS "Users can view their organization's purchase order items" ON public.purchase_order_items;
DROP POLICY IF EXISTS "Users can create purchase order items in their organization" ON public.purchase_order_items;
DROP POLICY IF EXISTS "Only owners and admins can update purchase order items" ON public.purchase_order_items;
DROP POLICY IF EXISTS "Only owners and admins can delete purchase order items" ON public.purchase_order_items;

-- Create a single permissive policy for purchase_order_items
CREATE POLICY "Allow all authenticated users to manage purchase order items"
ON public.purchase_order_items
FOR ALL
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');
