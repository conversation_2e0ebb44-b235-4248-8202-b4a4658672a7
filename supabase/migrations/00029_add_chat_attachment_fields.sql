-- Add file attachment fields to chat_messages table
DO $$
BEGIN
    -- Check if the columns already exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'chat_messages' AND column_name = 'attachment_type') THEN
        ALTER TABLE public.chat_messages ADD COLUMN attachment_type TEXT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'chat_messages' AND column_name = 'attachment_url') THEN
        ALTER TABLE public.chat_messages ADD COLUMN attachment_url TEXT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'chat_messages' AND column_name = 'attachment_name') THEN
        ALTER TABLE public.chat_messages ADD COLUMN attachment_name TEXT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'chat_messages' AND column_name = 'attachment_size') THEN
        ALTER TABLE public.chat_messages ADD COLUMN attachment_size INTEGER;
    END IF;
END $$;

-- Create a function to get file extension if it doesn't exist
CREATE OR REPLACE FUNCTION public.get_file_extension(filename TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN LOWER(SUBSTRING(filename FROM '\.([^\.]+)$'));
END;
$$;

-- Create a function to check if a file is an image if it doesn't exist
CREATE OR REPLACE FUNCTION public.is_image_file(filename TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  ext TEXT;
BEGIN
  ext := public.get_file_extension(filename);
  RETURN ext IN ('.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg');
END;
$$;
