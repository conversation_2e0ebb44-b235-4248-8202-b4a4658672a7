-- Migration to create a flexible tagging system for multiple entity types
-- with multi-tenancy support

-- Create an enum for entity types that can be tagged (if it doesn't exist)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'taggable_entity_type') THEN
    CREATE TYPE public.taggable_entity_type AS ENUM (
      'product',
      'supplier',
      'customer',
      'purchase_request',
      'purchase_order'
    );
  END IF;
END
$$;

-- Create a table for tag definitions if it doesn't exist
CREATE TABLE IF NOT EXISTS public.tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  color TEXT, -- Hex color code or name
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  -- Ensure tag names are unique within an organization
  UNIQUE (organization_id, name)
);

-- Create a junction table to connect tags to entities if it doesn't exist
CREATE TABLE IF NOT EXISTS public.tagged_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tag_id UUID NOT NULL REFERENCES public.tags(id) ON DELETE CASCADE,
  entity_type public.taggable_entity_type NOT NULL,
  entity_id UUID NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  -- Ensure each entity can have a specific tag only once
  UNIQUE (tag_id, entity_type, entity_id)
);

-- Add indexes for better query performance if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tags_organization_id') THEN
    CREATE INDEX idx_tags_organization_id ON public.tags(organization_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tagged_items_tag_id') THEN
    CREATE INDEX idx_tagged_items_tag_id ON public.tagged_items(tag_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tagged_items_entity') THEN
    CREATE INDEX idx_tagged_items_entity ON public.tagged_items(entity_type, entity_id);
  END IF;
END
$$;

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_tags_updated_at') THEN
    CREATE TRIGGER update_tags_updated_at
    BEFORE UPDATE ON public.tags
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();
  END IF;
END
$$;

-- RLS policies will be added later

-- Create helper functions for tag operations

-- Function to add a tag to an entity
CREATE OR REPLACE FUNCTION public.add_tag_to_entity(
  p_tag_id UUID,
  p_entity_type public.taggable_entity_type,
  p_entity_id UUID
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_tag_exists BOOLEAN;
  v_result UUID;
BEGIN
  -- Check if the tag exists
  SELECT EXISTS (
    SELECT 1 FROM public.tags t
    WHERE t.id = p_tag_id
  ) INTO v_tag_exists;

  IF NOT v_tag_exists THEN
    RAISE EXCEPTION 'Tag not found';
  END IF;

  -- Insert the tagged item
  INSERT INTO public.tagged_items (
    tag_id,
    entity_type,
    entity_id,
    created_by
  )
  VALUES (
    p_tag_id,
    p_entity_type,
    p_entity_id,
    auth.uid()
  )
  ON CONFLICT (tag_id, entity_type, entity_id) DO NOTHING
  RETURNING id INTO v_result;

  RETURN v_result;
END;
$$;

-- Function to remove a tag from an entity
CREATE OR REPLACE FUNCTION public.remove_tag_from_entity(
  p_tag_id UUID,
  p_entity_type public.taggable_entity_type,
  p_entity_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_tag_exists BOOLEAN;
  v_rows_deleted INTEGER;
BEGIN
  -- Check if the tag exists
  SELECT EXISTS (
    SELECT 1 FROM public.tags t
    WHERE t.id = p_tag_id
  ) INTO v_tag_exists;

  IF NOT v_tag_exists THEN
    RAISE EXCEPTION 'Tag not found';
  END IF;

  -- Delete the tagged item
  DELETE FROM public.tagged_items
  WHERE tag_id = p_tag_id
  AND entity_type = p_entity_type
  AND entity_id = p_entity_id;

  GET DIAGNOSTICS v_rows_deleted = ROW_COUNT;

  RETURN v_rows_deleted > 0;
END;
$$;

-- Function to get all tags for an entity
CREATE OR REPLACE FUNCTION public.get_entity_tags(
  p_entity_type public.taggable_entity_type,
  p_entity_id UUID
)
RETURNS TABLE (
  id UUID,
  name TEXT,
  description TEXT,
  color TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT t.id, t.name, t.description, t.color
  FROM public.tags t
  JOIN public.tagged_items ti ON t.id = ti.tag_id
  WHERE ti.entity_type = p_entity_type
  AND ti.entity_id = p_entity_id
  ORDER BY t.name;
END;
$$;

-- Function to find entities by tag
CREATE OR REPLACE FUNCTION public.find_entities_by_tag(
  p_tag_id UUID,
  p_entity_type public.taggable_entity_type DEFAULT NULL
)
RETURNS TABLE (
  entity_type public.taggable_entity_type,
  entity_id UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT ti.entity_type, ti.entity_id
  FROM public.tagged_items ti
  JOIN public.tags t ON ti.tag_id = t.id
  WHERE ti.tag_id = p_tag_id
  AND (p_entity_type IS NULL OR ti.entity_type = p_entity_type);
END;
$$;

-- Function to get tags with usage count
CREATE OR REPLACE FUNCTION public.get_tags_with_count(
  p_organization_id UUID
)
RETURNS TABLE (
  id UUID,
  organization_id UUID,
  name TEXT,
  description TEXT,
  color TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  usage_count BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT
    t.id,
    t.organization_id,
    t.name,
    t.description,
    t.color,
    t.created_at,
    t.updated_at,
    COUNT(ti.id)::BIGINT AS usage_count
  FROM
    public.tags t
  LEFT JOIN
    public.tagged_items ti ON t.id = ti.tag_id
  WHERE
    t.organization_id = p_organization_id
  GROUP BY
    t.id
  ORDER BY
    t.name;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.add_tag_to_entity(UUID, public.taggable_entity_type, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.remove_tag_from_entity(UUID, public.taggable_entity_type, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_entity_tags(public.taggable_entity_type, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.find_entities_by_tag(UUID, public.taggable_entity_type) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_tags_with_count(UUID) TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.tags IS 'Stores tag definitions for various entities with multi-tenant support';
COMMENT ON TABLE public.tagged_items IS 'Junction table connecting tags to various entity types';
COMMENT ON COLUMN public.tags.color IS 'Optional color for visual representation of the tag (hex code or name)';
COMMENT ON COLUMN public.tagged_items.entity_type IS 'Type of entity being tagged (product, supplier, etc.)';
COMMENT ON COLUMN public.tagged_items.entity_id IS 'UUID of the entity being tagged';

-- Create a view for easier querying of tagged entities with their tags
CREATE OR REPLACE VIEW public.entity_tags_view AS
SELECT
  ti.entity_type,
  ti.entity_id,
  t.id AS tag_id,
  t.name AS tag_name,
  t.description AS tag_description,
  t.color AS tag_color,
  t.organization_id
FROM
  public.tagged_items ti
JOIN
  public.tags t ON ti.tag_id = t.id;

COMMENT ON VIEW public.entity_tags_view IS 'View for easily querying entities with their associated tags';
