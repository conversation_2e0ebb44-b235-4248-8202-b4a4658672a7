-- Disable RLS policies for chat system temporarily
-- This allows development and testing without policy conflicts
-- We'll implement proper RLS later

-- Drop all existing chat policies (ignore errors if they don't exist)
DROP POLICY IF EXISTS "Users can view their conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can create conversations in their organizations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can create conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation admins can update conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can view conversations in their organizations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation creators can update their conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation creators can delete their conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "chat_conversations_select_policy" ON public.chat_conversations;
DROP POLICY IF EXISTS "chat_conversations_insert_policy" ON public.chat_conversations;
DROP POLICY IF EXISTS "chat_conversations_update_policy" ON public.chat_conversations;
DROP POLICY IF EXISTS "chat_conversations_delete_policy" ON public.chat_conversations;
DROP POLICY IF EXISTS "chat_conversations_org_access" ON public.chat_conversations;
DROP POLICY IF EXISTS "chat_conversations_create_org" ON public.chat_conversations;

DROP POLICY IF EXISTS "Users can view participants in their conversations" ON public.chat_participants;
DROP POLICY IF EXISTS "Conversation admins can manage participants" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can create their own participant record" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can update their own participant status" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can create participant records for conversations they're in" ON public.chat_participants;
DROP POLICY IF EXISTS "Conversation creators can delete participants" ON public.chat_participants;
DROP POLICY IF EXISTS "chat_participants_select_policy" ON public.chat_participants;
DROP POLICY IF EXISTS "chat_participants_insert_policy" ON public.chat_participants;
DROP POLICY IF EXISTS "chat_participants_update_policy" ON public.chat_participants;
DROP POLICY IF EXISTS "chat_participants_delete_policy" ON public.chat_participants;
DROP POLICY IF EXISTS "chat_participants_access" ON public.chat_participants;

DROP POLICY IF EXISTS "Users can view messages in their conversations" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can send messages to their conversations" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can update their own messages" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can delete their own messages" ON public.chat_messages;
DROP POLICY IF EXISTS "chat_messages_select_policy" ON public.chat_messages;
DROP POLICY IF EXISTS "chat_messages_insert_policy" ON public.chat_messages;
DROP POLICY IF EXISTS "chat_messages_update_policy" ON public.chat_messages;
DROP POLICY IF EXISTS "chat_messages_delete_policy" ON public.chat_messages;
DROP POLICY IF EXISTS "chat_messages_access" ON public.chat_messages;
DROP POLICY IF EXISTS "chat_messages_participant_only" ON public.chat_messages;

DROP POLICY IF EXISTS "Users can view message status in their conversations" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can update their own message status" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can update their own message read status" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can view message status for their conversations" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can create their own message status" ON public.chat_message_status;
DROP POLICY IF EXISTS "chat_message_status_select_policy" ON public.chat_message_status;
DROP POLICY IF EXISTS "chat_message_status_insert_policy" ON public.chat_message_status;
DROP POLICY IF EXISTS "chat_message_status_update_policy" ON public.chat_message_status;
DROP POLICY IF EXISTS "chat_message_status_own" ON public.chat_message_status;

-- Drop any problematic functions
DROP FUNCTION IF EXISTS public.is_conversation_participant(UUID, UUID);
DROP FUNCTION IF EXISTS public.is_conversation_admin(UUID, UUID);
DROP FUNCTION IF EXISTS public.get_user_conversations();
DROP FUNCTION IF EXISTS public.user_in_organization(UUID, UUID);

-- Disable RLS completely for all chat tables
ALTER TABLE public.chat_conversations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_participants DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_message_status DISABLE ROW LEVEL SECURITY;

-- Add indexes for better performance (if they don't exist)
CREATE INDEX IF NOT EXISTS idx_chat_conversations_organization_id ON public.chat_conversations(organization_id);
CREATE INDEX IF NOT EXISTS idx_chat_conversations_created_by ON public.chat_conversations(created_by);
CREATE INDEX IF NOT EXISTS idx_chat_participants_user_id ON public.chat_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_participants_conversation_id ON public.chat_participants(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_id ON public.chat_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sender_id ON public.chat_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON public.chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_message_status_user_id ON public.chat_message_status(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_message_status_message_id ON public.chat_message_status(message_id);

-- Add table comments
COMMENT ON TABLE public.chat_conversations IS 'Chat conversations - RLS DISABLED for development';
COMMENT ON TABLE public.chat_participants IS 'Chat participants - RLS DISABLED for development';
COMMENT ON TABLE public.chat_messages IS 'Chat messages - RLS DISABLED for development';
COMMENT ON TABLE public.chat_message_status IS 'Message read status - RLS DISABLED for development';
