-- Migration for Employee Management System
-- This migration adds tables for employee management with multi-tenant support

-- Create departments table
CREATE TABLE public.departments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    manager_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (organization_id, name)
);

-- Create employment_types table
CREATE TABLE public.employment_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (organization_id, name)
);

-- Create job_positions table
CREATE TABLE public.job_positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    title TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (organization_id, title, department_id)
);

-- Create employees table
CREATE TABLE public.employees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    employee_number TEXT,
    first_name TEXT NOT NULL,
    middle_name TEXT,
    last_name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    date_of_birth DATE,
    gender TEXT CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
    marital_status TEXT CHECK (marital_status IN ('single', 'married', 'divorced', 'widowed', 'separated')),
    nationality TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    postal_code TEXT,
    country TEXT,
    emergency_contact_name TEXT,
    emergency_contact_phone TEXT,
    emergency_contact_relationship TEXT,
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    position_id UUID REFERENCES public.job_positions(id) ON DELETE SET NULL,
    employment_type_id UUID REFERENCES public.employment_types(id) ON DELETE SET NULL,
    hire_date DATE,
    end_date DATE,
    status TEXT CHECK (status IN ('active', 'on_leave', 'terminated', 'resigned', 'retired')),
    is_active BOOLEAN DEFAULT TRUE,
    profile_image_url TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (organization_id, employee_number)
);

-- Create employee_government_ids table
CREATE TABLE public.employee_government_ids (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    sss_number TEXT,
    philhealth_number TEXT,
    pagibig_number TEXT,
    tin_number TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create employee_salary table
CREATE TABLE public.employee_salary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    basic_salary DECIMAL(15, 2) NOT NULL,
    effective_date DATE NOT NULL,
    end_date DATE,
    currency TEXT DEFAULT 'PHP',
    pay_frequency TEXT CHECK (pay_frequency IN ('monthly', 'semi_monthly', 'weekly', 'daily')),
    tax_exemption_status TEXT,
    allowances JSONB DEFAULT '{}'::JSONB,
    deductions JSONB DEFAULT '{}'::JSONB,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create employee_documents table
CREATE TABLE public.employee_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    document_type TEXT NOT NULL,
    document_name TEXT NOT NULL,
    file_url TEXT NOT NULL,
    file_type TEXT,
    file_size INTEGER,
    uploaded_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create employee_dependents table
CREATE TABLE public.employee_dependents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    first_name TEXT NOT NULL,
    middle_name TEXT,
    last_name TEXT NOT NULL,
    relationship TEXT NOT NULL,
    date_of_birth DATE,
    is_beneficiary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create a function to check if a user has access to an employee's data
-- This will be used later when implementing RLS policies
CREATE OR REPLACE FUNCTION public.has_employee_access(employee_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    v_organization_id UUID;
    v_user_role TEXT;
BEGIN
    -- Get the organization ID for the employee
    SELECT organization_id INTO v_organization_id
    FROM public.employees
    WHERE id = employee_uuid;

    -- If employee not found, return false
    IF v_organization_id IS NULL THEN
        RETURN FALSE;
    END IF;

    -- Get the user's role in the organization
    SELECT role INTO v_user_role
    FROM public.organization_members
    WHERE organization_id = v_organization_id
    AND user_id = auth.uid();

    -- If user is not a member of the organization, return false
    IF v_user_role IS NULL THEN
        RETURN FALSE;
    END IF;

    -- If user is owner, admin, or HR, return true
    IF v_user_role IN ('owner', 'admin') THEN
        RETURN TRUE;
    END IF;

    -- Otherwise, return false
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update role permissions to include employee management
UPDATE public.role_permissions
SET permissions = jsonb_set(
    permissions,
    '{employees}',
    '{"view": true, "create": true, "update": true, "delete": true}'
)
WHERE role IN ('owner', 'admin');

UPDATE public.role_permissions
SET permissions = jsonb_set(
    permissions,
    '{employees}',
    '{"view": false, "create": false, "update": false, "delete": false}'
)
WHERE role NOT IN ('owner', 'admin');

-- Create storage bucket for employee documents
INSERT INTO storage.buckets (id, name, public)
VALUES ('employee-documents', 'employee-documents', false)
ON CONFLICT (id) DO NOTHING;
