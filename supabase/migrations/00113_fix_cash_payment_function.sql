-- Fix cash payment function without duplicate constraints
-- This migration only updates the function to fix the profile_id error

-- Add cash payment columns if they don't exist
DO $$
BEGIN
    -- Add cash_tendered column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'sales' AND column_name = 'cash_tendered') THEN
        ALTER TABLE public.sales ADD COLUMN cash_tendered DECIMAL(12, 2) DEFAULT NULL;
    END IF;

    -- Add change_amount column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'sales' AND column_name = 'change_amount') THEN
        ALTER TABLE public.sales ADD COLUMN change_amount DECIMAL(12, 2) DEFAULT NULL;
    END IF;
END $$;

-- Drop the existing function first to avoid conflicts
DROP FUNCTION IF EXISTS public.create_sale_with_items(
  UUID, UUID, TEXT, TEXT, DECIMAL, DECIMAL, DECIMAL, DECIMAL, TEXT, TEXT, UUID, JSONB, INTEGER, DECIMAL, DECIMAL, DECIMAL
);

-- Create the corrected function with cash payment support
CREATE OR REPLACE FUNCTION public.create_sale_with_items(
  p_organization_id UUID,
  p_customer_id UUID,
  p_invoice_number TEXT,
  p_status TEXT,
  p_subtotal DECIMAL,
  p_tax_amount DECIMAL,
  p_discount_amount DECIMAL,
  p_total_amount DECIMAL,
  p_payment_method TEXT,
  p_notes TEXT,
  p_created_by UUID,
  p_items JSONB,
  p_loyalty_points_used INTEGER DEFAULT 0,
  p_loyalty_points_discount DECIMAL DEFAULT 0,
  p_cash_tendered DECIMAL DEFAULT NULL,
  p_change_amount DECIMAL DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  v_sale_id UUID;
  v_item JSONB;
  v_product_id UUID;
  v_quantity INTEGER;
  v_base_quantity NUMERIC;
  v_uom_id UUID;
  v_loyalty_points_earned INTEGER := 0;
  v_is_eligible BOOLEAN;
BEGIN
  -- Calculate loyalty points earned if customer is provided and points are not being redeemed
  IF p_customer_id IS NOT NULL AND p_loyalty_points_used = 0 THEN
    -- Check if customer is eligible for loyalty points
    SELECT loyalty_eligible INTO v_is_eligible
    FROM customers
    WHERE id = p_customer_id;

    -- Only calculate points if customer is eligible (or no record exists, default to eligible)
    IF v_is_eligible IS NULL OR v_is_eligible = true THEN
      v_loyalty_points_earned := calculate_loyalty_points(p_organization_id, p_customer_id, NULL, p_total_amount);
    END IF;
  END IF;

  -- Create the sale record
  INSERT INTO public.sales (
    organization_id,
    customer_id,
    invoice_number,
    sale_date,
    status,
    subtotal,
    tax_amount,
    discount_amount,
    total_amount,
    payment_method,
    cash_tendered,
    change_amount,
    notes,
    created_by,
    loyalty_points_used,
    loyalty_points_earned,
    loyalty_points_discount
  ) VALUES (
    p_organization_id,
    p_customer_id,
    p_invoice_number,
    NOW(),
    p_status,
    p_subtotal,
    p_tax_amount,
    p_discount_amount,
    p_total_amount,
    p_payment_method,
    p_cash_tendered,
    p_change_amount,
    p_notes,
    p_created_by,
    p_loyalty_points_used,
    v_loyalty_points_earned,
    p_loyalty_points_discount
  ) RETURNING id INTO v_sale_id;

  -- Process each item
  FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
  LOOP
    -- Extract values from the item JSON
    v_product_id := (v_item->>'product_id')::UUID;
    v_quantity := (v_item->>'quantity')::INTEGER;
    v_base_quantity := (v_item->>'base_quantity')::NUMERIC;
    v_uom_id := (v_item->>'uom_id')::UUID;

    -- Insert the sale item
    INSERT INTO public.sale_items (
      sale_id,
      product_id,
      quantity,
      unit_price,
      uom_id,
      base_quantity,
      tax_rate,
      tax_amount,
      discount_amount,
      total_amount,
      notes
    ) VALUES (
      v_sale_id,
      v_product_id,
      v_quantity,
      (v_item->>'unit_price')::DECIMAL,
      v_uom_id,
      v_base_quantity,
      (v_item->>'tax_rate')::DECIMAL,
      (v_item->>'tax_amount')::DECIMAL,
      (v_item->>'discount_amount')::DECIMAL,
      (v_item->>'total_amount')::DECIMAL,
      v_item->>'notes'
    );

    -- Create inventory transaction for this sale item
    -- This will trigger the inventory update through the trigger
    INSERT INTO public.inventory_transactions (
      organization_id,
      product_id,
      transaction_type,
      quantity,
      uom_id,
      reference_id,
      reference_type,
      notes,
      created_by
    ) VALUES (
      p_organization_id,
      v_product_id,
      'sale',
      -v_base_quantity,  -- Negative quantity for sales
      v_uom_id,
      v_sale_id,
      'sale',
      'Sale: ' || p_invoice_number,
      p_created_by
    );
  END LOOP;

  -- Update customer loyalty points if applicable
  IF p_customer_id IS NOT NULL THEN
    -- Deduct used points
    IF p_loyalty_points_used > 0 THEN
      UPDATE customer_loyalty_profiles
      SET current_points_balance = current_points_balance - p_loyalty_points_used,
          lifetime_points_redeemed = lifetime_points_redeemed + p_loyalty_points_used,
          updated_at = NOW()
      WHERE customer_id = p_customer_id AND organization_id = p_organization_id;
    END IF;

    -- Add earned points
    IF v_loyalty_points_earned > 0 THEN
      INSERT INTO customer_loyalty_profiles (
        customer_id,
        organization_id,
        current_points_balance,
        lifetime_points_earned,
        lifetime_points_redeemed
      )
      VALUES (p_customer_id, p_organization_id, v_loyalty_points_earned, v_loyalty_points_earned, 0)
      ON CONFLICT (customer_id, organization_id)
      DO UPDATE SET
        current_points_balance = customer_loyalty_profiles.current_points_balance + v_loyalty_points_earned,
        lifetime_points_earned = customer_loyalty_profiles.lifetime_points_earned + v_loyalty_points_earned,
        updated_at = NOW();
    END IF;
  END IF;

  -- Return the sale ID
  RETURN jsonb_build_object('sale_id', v_sale_id);
END;
$$ LANGUAGE plpgsql;
