-- Migration: Default Supplier System
-- Creates a "Default Supplier" for each organization that cannot be deleted
-- This allows purchase orders to be created without requiring a specific supplier

-- Step 1: Add is_default flag to suppliers table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'suppliers' AND column_name = 'is_default'
    ) THEN
        ALTER TABLE public.suppliers
        ADD COLUMN is_default BOOLEAN DEFAULT FALSE;
        
        COMMENT ON COLUMN public.suppliers.is_default IS 'Indicates if this is the default supplier for the organization (cannot be deleted)';
    END IF;
END $$;

-- Step 2: Create unique constraint to ensure only one default supplier per organization
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'suppliers_organization_default_unique'
    ) THEN
        ALTER TABLE public.suppliers DROP CONSTRAINT suppliers_organization_default_unique;
    END IF;
    
    -- Create partial unique index for default suppliers
    DROP INDEX IF EXISTS suppliers_organization_default_unique;
    CREATE UNIQUE INDEX suppliers_organization_default_unique 
    ON public.suppliers (organization_id) 
    WHERE is_default = TRUE;
END $$;

-- Step 3: Create function to create default supplier for an organization
CREATE OR REPLACE FUNCTION create_default_supplier(p_organization_id UUID)
RETURNS UUID AS $$
DECLARE
    v_supplier_id UUID;
    v_owner_id UUID;
BEGIN
    -- Get the organization owner to use as created_by
    SELECT user_id INTO v_owner_id
    FROM public.organization_members
    WHERE organization_id = p_organization_id 
    AND role = 'owner'
    LIMIT 1;
    
    -- If no owner found, use a system user (this shouldn't happen in normal cases)
    IF v_owner_id IS NULL THEN
        v_owner_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;
    
    -- Create the default supplier
    INSERT INTO public.suppliers (
        organization_id,
        name,
        contact_person,
        email,
        phone,
        address,
        notes,
        is_default,
        created_at,
        updated_at
    ) VALUES (
        p_organization_id,
        'Default Supplier',
        'To Be Determined',
        NULL,
        NULL,
        NULL,
        'This is the default supplier used for purchase orders when the actual supplier is not yet determined. You can edit the purchase order later to assign the correct supplier.',
        TRUE,
        NOW(),
        NOW()
    ) RETURNING id INTO v_supplier_id;
    
    RETURN v_supplier_id;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create default suppliers for all existing organizations
DO $$
DECLARE
    org_record RECORD;
    supplier_count INTEGER;
BEGIN
    FOR org_record IN SELECT id FROM public.organizations LOOP
        -- Check if organization already has a default supplier
        SELECT COUNT(*) INTO supplier_count
        FROM public.suppliers
        WHERE organization_id = org_record.id AND is_default = TRUE;
        
        -- Create default supplier if it doesn't exist
        IF supplier_count = 0 THEN
            PERFORM create_default_supplier(org_record.id);
            RAISE NOTICE 'Created default supplier for organization: %', org_record.id;
        END IF;
    END LOOP;
END $$;

-- Step 5: Create trigger to automatically create default supplier for new organizations
CREATE OR REPLACE FUNCTION create_default_supplier_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Create default supplier for the new organization
    PERFORM create_default_supplier(NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS create_default_supplier_on_org_creation ON public.organizations;

-- Create trigger for new organizations
CREATE TRIGGER create_default_supplier_on_org_creation
    AFTER INSERT ON public.organizations
    FOR EACH ROW
    EXECUTE FUNCTION create_default_supplier_trigger();

-- Step 6: Add constraint to prevent deletion of default suppliers
CREATE OR REPLACE FUNCTION prevent_default_supplier_deletion()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.is_default = TRUE THEN
        RAISE EXCEPTION 'Cannot delete the default supplier. This supplier is required for the organization.';
    END IF;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS prevent_default_supplier_deletion_trigger ON public.suppliers;

-- Create trigger to prevent deletion of default suppliers
CREATE TRIGGER prevent_default_supplier_deletion_trigger
    BEFORE DELETE ON public.suppliers
    FOR EACH ROW
    EXECUTE FUNCTION prevent_default_supplier_deletion();

-- Step 7: Add function to get default supplier for an organization
CREATE OR REPLACE FUNCTION get_default_supplier(p_organization_id UUID)
RETURNS UUID AS $$
DECLARE
    v_supplier_id UUID;
BEGIN
    SELECT id INTO v_supplier_id
    FROM public.suppliers
    WHERE organization_id = p_organization_id 
    AND is_default = TRUE
    LIMIT 1;
    
    -- If no default supplier exists, create one
    IF v_supplier_id IS NULL THEN
        v_supplier_id := create_default_supplier(p_organization_id);
    END IF;
    
    RETURN v_supplier_id;
END;
$$ LANGUAGE plpgsql;

-- Step 8: Verification and final messages
DO $$
DECLARE
    org_count INTEGER;
    supplier_count INTEGER;
BEGIN
    -- Count organizations
    SELECT COUNT(*) INTO org_count FROM public.organizations;
    
    -- Count default suppliers
    SELECT COUNT(*) INTO supplier_count 
    FROM public.suppliers 
    WHERE is_default = TRUE;
    
    RAISE NOTICE 'Default Supplier System Setup Complete!';
    RAISE NOTICE 'Organizations: %, Default Suppliers: %', org_count, supplier_count;
    RAISE NOTICE 'Each organization now has a "Default Supplier" that cannot be deleted.';
    RAISE NOTICE 'Purchase orders can now be created using the default supplier when the actual supplier is unknown.';
END $$;
