-- Migration to automatically create 'pcs' UoM for new products
-- This ensures every product has at least one UoM by default

-- Create a function to automatically create a 'pcs' UoM for new products
CREATE OR REPLACE FUNCTION auto_create_product_uom()
RETURNS TRIGGER AS $$
DECLARE
  v_pcs_uom_id UUID;
BEGIN
  -- First, check if the product already has any UoMs
  IF EXISTS (
    SELECT 1 FROM product_uoms
    WHERE product_id = NEW.id
    AND organization_id = NEW.organization_id
  ) THEN
    -- Product already has UoMs, no need to create default
    RETURN NEW;
  END IF;

  -- Get the 'pcs' UoM ID for this organization
  SELECT id INTO v_pcs_uom_id
  FROM units_of_measurement
  WHERE code = 'pcs'
  AND organization_id = NEW.organization_id
  LIMIT 1;

  -- If 'pcs' UoM doesn't exist, create it
  IF v_pcs_uom_id IS NULL THEN
    INSERT INTO units_of_measurement (
      organization_id,
      code,
      name,
      description,
      is_active
    ) VALUES (
      NEW.organization_id,
      'pcs',
      'Pieces',
      'Individual units or items',
      true
    ) RETURNING id INTO v_pcs_uom_id;
  END IF;

  -- Create the product UoM association
  INSERT INTO product_uoms (
    product_id,
    uom_id,
    organization_id,
    conversion_factor,
    is_default,
    is_purchasing_unit,
    is_selling_unit
  ) VALUES (
    NEW.id,
    v_pcs_uom_id,
    NEW.organization_id,
    1, -- 1:1 conversion factor for base unit
    true, -- Set as default
    true, -- Use for purchasing
    true  -- Use for selling
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically create a 'pcs' UoM for new products
DROP TRIGGER IF EXISTS auto_create_product_uom_trigger ON public.products;
CREATE TRIGGER auto_create_product_uom_trigger
AFTER INSERT ON public.products
FOR EACH ROW
EXECUTE FUNCTION auto_create_product_uom();

-- Also create 'pcs' UoM for existing products that don't have any UoMs
DO $$
DECLARE
  product_record RECORD;
  v_pcs_uom_id UUID;
BEGIN
  -- Loop through all products that don't have UoMs
  FOR product_record IN (
    SELECT p.id, p.organization_id, p.name
    FROM products p
    WHERE NOT EXISTS (
      SELECT 1 FROM product_uoms pu
      WHERE pu.product_id = p.id
    )
  )
  LOOP
    -- Get or create 'pcs' UoM for this organization
    SELECT id INTO v_pcs_uom_id
    FROM units_of_measurement
    WHERE code = 'pcs'
    AND organization_id = product_record.organization_id
    LIMIT 1;

    -- If 'pcs' UoM doesn't exist, create it
    IF v_pcs_uom_id IS NULL THEN
      INSERT INTO units_of_measurement (
        organization_id,
        code,
        name,
        description,
        is_active
      ) VALUES (
        product_record.organization_id,
        'pcs',
        'Pieces',
        'Individual units or items',
        true
      ) RETURNING id INTO v_pcs_uom_id;
    END IF;

    -- Create the product UoM association
    INSERT INTO product_uoms (
      product_id,
      uom_id,
      organization_id,
      conversion_factor,
      is_default,
      is_purchasing_unit,
      is_selling_unit
    ) VALUES (
      product_record.id,
      v_pcs_uom_id,
      product_record.organization_id,
      1, -- 1:1 conversion factor for base unit
      true, -- Set as default
      true, -- Use for purchasing
      true  -- Use for selling
    );

    RAISE NOTICE 'Created default UoM for product %', product_record.name;
  END LOOP;
END $$;
