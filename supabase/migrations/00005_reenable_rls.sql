-- This migration file is for re-enabling RLS after testing
-- You can run this migration when you're ready to secure your database again

-- Re-enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_settings ENABLE ROW LEVEL SECURITY;

-- Make sure all the necessary policies exist
-- For profiles
DROP POLICY IF EXISTS "Authenticated users can insert their own profile" ON public.profiles;
CREATE POLICY "Authenticated users can insert their own profile" 
ON public.profiles 
FOR INSERT 
WITH CHECK (
    auth.uid() = id
);

-- For organizations
DROP POLICY IF EXISTS "Authenticated users can create organizations" ON public.organizations;
CREATE POLICY "Authenticated users can create organizations" 
ON public.organizations 
FOR INSERT 
WITH CHECK (
    auth.role() = 'authenticated'
);

-- For organization members
DROP POLICY IF EXISTS "Authenticated users can add themselves as organization members" ON public.organization_members;
CREATE POLICY "Authenticated users can add themselves as organization members" 
ON public.organization_members 
FOR INSERT 
WITH CHECK (
    auth.uid() = user_id
);

-- For organization settings
DROP POLICY IF EXISTS "Authenticated users can create organization settings" ON public.organization_settings;
CREATE POLICY "Authenticated users can create organization settings" 
ON public.organization_settings 
FOR INSERT 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.organization_members
        WHERE organization_id = organization_settings.organization_id
        AND user_id = auth.uid()
    )
);
