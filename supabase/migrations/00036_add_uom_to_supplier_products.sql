-- Add UoM reference to supplier_products
ALTER TABLE public.supplier_products
ADD COLUMN uom_id UUID REFERENCES public.units_of_measurement(id) ON DELETE RESTRICT;

-- Add organization_id to supplier_products table for proper multi-tenancy if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'supplier_products'
        AND column_name = 'organization_id'
    ) THEN
        ALTER TABLE public.supplier_products
        ADD COLUMN organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE;
        
        -- Update existing supplier_products with the correct organization_id from their suppliers
        UPDATE public.supplier_products sp
        SET organization_id = s.organization_id
        FROM public.suppliers s
        WHERE sp.supplier_id = s.id;
        
        -- Make organization_id required
        ALTER TABLE public.supplier_products
        ALTER COLUMN organization_id SET NOT NULL;
    END IF;
END $$;

-- Update existing supplier_products with default UoM (pieces)
UPDATE public.supplier_products sp
SET uom_id = (
  SELECT pu.uom_id
  FROM public.product_uoms pu
  WHERE pu.product_id = sp.product_id
  AND pu.is_purchasing_unit = true
  LIMIT 1
)
WHERE sp.uom_id IS NULL;

-- If no purchasing UoM found, use the default UoM
UPDATE public.supplier_products sp
SET uom_id = (
  SELECT pu.uom_id
  FROM public.product_uoms pu
  WHERE pu.product_id = sp.product_id
  AND pu.is_default = true
  LIMIT 1
)
WHERE sp.uom_id IS NULL;

-- If still no UoM found, use the 'pieces' UoM from the organization
UPDATE public.supplier_products sp
SET uom_id = (
  SELECT u.id
  FROM public.units_of_measurement u
  WHERE u.organization_id = sp.organization_id
  AND u.code = 'pcs'
  LIMIT 1
)
WHERE sp.uom_id IS NULL;

-- Add a trigger to update the UoM when a product is added to a supplier
CREATE OR REPLACE FUNCTION set_default_supplier_product_uom()
RETURNS TRIGGER AS $$
BEGIN
  -- If UoM is not provided, try to find a purchasing UoM
  IF NEW.uom_id IS NULL THEN
    -- First try to find a purchasing UoM
    SELECT pu.uom_id INTO NEW.uom_id
    FROM public.product_uoms pu
    WHERE pu.product_id = NEW.product_id
    AND pu.is_purchasing_unit = true
    LIMIT 1;
    
    -- If no purchasing UoM found, use the default UoM
    IF NEW.uom_id IS NULL THEN
      SELECT pu.uom_id INTO NEW.uom_id
      FROM public.product_uoms pu
      WHERE pu.product_id = NEW.product_id
      AND pu.is_default = true
      LIMIT 1;
    END IF;
    
    -- If still no UoM found, use the 'pieces' UoM from the organization
    IF NEW.uom_id IS NULL THEN
      SELECT u.id INTO NEW.uom_id
      FROM public.units_of_measurement u
      JOIN public.suppliers s ON s.id = NEW.supplier_id
      WHERE u.organization_id = s.organization_id
      AND u.code = 'pcs'
      LIMIT 1;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_supplier_product_uom
BEFORE INSERT ON public.supplier_products
FOR EACH ROW
EXECUTE FUNCTION set_default_supplier_product_uom();
