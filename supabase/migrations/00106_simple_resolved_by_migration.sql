-- Simple migration to add resolved_by column to float_inventory table
-- Run this SQL in your Supabase dashboard SQL editor

-- Step 1: Add the resolved_by column to track who resolved the float inventory
ALTER TABLE public.float_inventory
ADD COLUMN IF NOT EXISTS resolved_by UUID REFERENCES auth.users(id);

-- Step 2: Add an index for better performance when querying by resolved_by
CREATE INDEX IF NOT EXISTS idx_float_inventory_resolved_by 
ON public.float_inventory(resolved_by);

-- Step 3: Add a comment to document the column
COMMENT ON COLUMN public.float_inventory.resolved_by 
IS 'References the user who resolved this float inventory item';

-- That's it! The application will handle the rest gracefully.
