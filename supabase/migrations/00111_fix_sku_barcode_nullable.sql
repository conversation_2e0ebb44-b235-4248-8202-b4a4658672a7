-- Fix SKU and Barcode constraints to allow multiple NULL values
-- This allows small businesses to create products without SKU or barcode

-- Drop existing unique constraints
ALTER TABLE public.products DROP CONSTRAINT IF EXISTS products_organization_id_sku_key;
ALTER TABLE public.products DROP CONSTRAINT IF EXISTS products_organization_id_barcode_key;

-- Create partial unique indexes that ignore NULL values
-- This allows multiple products with NULL SKU/barcode but ensures uniqueness when they are provided
CREATE UNIQUE INDEX products_organization_sku_unique 
ON public.products (organization_id, sku) 
WHERE sku IS NOT NULL;

CREATE UNIQUE INDEX products_organization_barcode_unique 
ON public.products (organization_id, barcode) 
WHERE barcode IS NOT NULL;
