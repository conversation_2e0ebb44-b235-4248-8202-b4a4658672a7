-- Create a function to check if a user has a specific permission
CREATE OR REPLACE FUNCTION public.has_permission(
    p_organization_id UUID,
    p_user_id UUID,
    p_module TEXT,
    p_action TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_role TEXT;
    v_permissions JSONB;
    v_has_permission BOOLEAN;
BEGIN
    -- Get the user's role in the organization
    SELECT role INTO v_role
    FROM public.organization_members
    WHERE organization_id = p_organization_id AND user_id = p_user_id;
    
    -- If the user is not a member of the organization, return false
    IF v_role IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- If the user is an owner, they have all permissions
    IF v_role = 'owner' THEN
        RETURN TRUE;
    END IF;
    
    -- Get the permissions for the user's role
    SELECT permissions INTO v_permissions
    FROM public.role_permissions
    WHERE organization_id = p_organization_id AND role = v_role;
    
    -- Check if the user has the specific permission
    IF v_permissions IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Check if the module exists in the permissions
    IF NOT v_permissions ? p_module THEN
        RETURN FALSE;
    END IF;
    
    -- Check if the action exists in the module permissions
    IF NOT v_permissions->p_module ? p_action THEN
        RETURN FALSE;
    END IF;
    
    -- Get the permission value
    v_has_permission := (v_permissions->p_module->>p_action)::BOOLEAN;
    
    RETURN v_has_permission;
END;
$$;

-- Create a function to get all permissions for a user in an organization
CREATE OR REPLACE FUNCTION public.get_user_permissions(
    p_organization_id UUID,
    p_user_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_role TEXT;
    v_permissions JSONB;
BEGIN
    -- Get the user's role in the organization
    SELECT role INTO v_role
    FROM public.organization_members
    WHERE organization_id = p_organization_id AND user_id = p_user_id;
    
    -- If the user is not a member of the organization, return empty permissions
    IF v_role IS NULL THEN
        RETURN '{}'::JSONB;
    END IF;
    
    -- If the user is an owner, return all permissions as true
    IF v_role = 'owner' THEN
        RETURN '{
            "dashboard": {"view": true},
            "products": {"view": true, "create": true, "update": true, "delete": true},
            "inventory": {"view": true, "create": true, "update": true, "delete": true},
            "sales": {"view": true, "create": true, "update": true, "delete": true, "process": true},
            "purchases": {"view": true, "create": true, "update": true, "delete": true, "approve": true},
            "customers": {"view": true, "create": true, "update": true, "delete": true},
            "suppliers": {"view": true, "create": true, "update": true, "delete": true},
            "reports": {"view": true, "export": true},
            "users": {"view": true, "create": true, "update": true, "delete": true, "change_role": true, "reset_password": true},
            "settings": {"view": true, "update": true}
        }'::JSONB;
    END IF;
    
    -- Get the permissions for the user's role
    SELECT permissions INTO v_permissions
    FROM public.role_permissions
    WHERE organization_id = p_organization_id AND role = v_role;
    
    -- If no permissions are found, return empty permissions
    IF v_permissions IS NULL THEN
        RETURN '{}'::JSONB;
    END IF;
    
    RETURN v_permissions;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.has_permission(UUID, UUID, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_permissions(UUID, UUID) TO authenticated;
