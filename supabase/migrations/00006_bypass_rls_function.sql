-- This migration creates a function that completely bypasses RLS
-- to create organizations and related records

-- Drop the function if it exists
DROP FUNCTION IF EXISTS public.create_organization_bypass_rls(text, text, uuid);

-- Create a function that bypasses <PERSON><PERSON> completely
CREATE OR REPLACE FUNCTION public.create_organization_bypass_rls(
  org_name text,
  org_slug text,
  user_id uuid
) RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER -- This runs with the privileges of the function creator
SET search_path = public -- Prevent search path injection
AS $$
DECLARE
  v_org_id uuid;
  v_result json;
BEGIN
  -- Disable RLS temporarily for this transaction
  -- This is a more direct approach than relying on policies
  ALTER TABLE public.organizations DISABLE ROW LEVEL SECURITY;
  ALTER TABLE public.organization_members DISABLE ROW LEVEL SECURITY;
  ALTER TABLE public.organization_settings DISABLE ROW LEVEL SECURITY;

  BEGIN
    -- Insert the organization
    INSERT INTO public.organizations (name, slug)
    VALUES (org_name, org_slug)
    RETURNING id INTO v_org_id;

    -- Add the user as an owner
    INSERT INTO public.organization_members (organization_id, user_id, role)
    VALUES (v_org_id, user_id, 'owner');

    -- Create default settings
    INSERT INTO public.organization_settings (organization_id, settings)
    VALUES (v_org_id, json_build_object(
      'currency', 'USD',
      'tax_rate', 0,
      'business_hours', json_build_object(
        'monday', json_build_object('open', '09:00', 'close', '17:00'),
        'tuesday', json_build_object('open', '09:00', 'close', '17:00'),
        'wednesday', json_build_object('open', '09:00', 'close', '17:00'),
        'thursday', json_build_object('open', '09:00', 'close', '17:00'),
        'friday', json_build_object('open', '09:00', 'close', '17:00'),
        'saturday', json_build_object('open', '', 'close', ''),
        'sunday', json_build_object('open', '', 'close', '')
      )
    ));

    -- Create default supplier (if function exists)
    BEGIN
      PERFORM create_default_supplier(v_org_id);
    EXCEPTION WHEN undefined_function THEN
      -- Function doesn't exist yet, skip this step
      NULL;
    END;

    -- Re-enable RLS
    ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.organization_members ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.organization_settings ENABLE ROW LEVEL SECURITY;

    -- Return success with organization data
    v_result := json_build_object(
      'success', true,
      'organization', json_build_object(
        'id', v_org_id,
        'name', org_name,
        'slug', org_slug
      )
    );

    RETURN v_result;
  EXCEPTION
    WHEN OTHERS THEN
      -- Re-enable RLS even if there's an error
      ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.organization_members ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.organization_settings ENABLE ROW LEVEL SECURITY;

      -- Return error information
      v_result := json_build_object(
        'success', false,
        'error', SQLERRM,
        'error_detail', SQLSTATE
      );

      RETURN v_result;
  END;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.create_organization_bypass_rls(text, text, uuid) TO authenticated;

-- Add a comment explaining the function
COMMENT ON FUNCTION public.create_organization_bypass_rls(text, text, uuid) IS
'Creates an organization and related records, bypassing RLS completely.
This function should only be used when necessary, as it temporarily disables RLS.';
