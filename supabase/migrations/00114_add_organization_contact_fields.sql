-- Add contact information fields to organizations table
-- This migration adds address, phone, email, and website fields for use in receipts and business documents

-- Add new columns to organizations table
ALTER TABLE public.organizations 
ADD COLUMN IF NOT EXISTS address TEXT,
ADD COLUMN IF NOT EXISTS phone TEXT,
ADD COLUMN IF NOT EXISTS email TEXT,
ADD COLUMN IF NOT EXISTS website TEXT;

-- Add comments for documentation
COMMENT ON COLUMN public.organizations.address IS 'Business address for receipts and documents';
COMMENT ON COLUMN public.organizations.phone IS 'Business phone number for receipts and documents';
COMMENT ON COLUMN public.organizations.email IS 'Business email address for receipts and documents';
COMMENT ON COLUMN public.organizations.website IS 'Business website URL for receipts and documents';

-- Update the updated_at timestamp when any of these fields change
CREATE OR REPLACE FUNCTION update_organization_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger if it doesn't exist
DROP TRIGGER IF EXISTS update_organizations_updated_at ON public.organizations;
CREATE TRIGGER update_organizations_updated_at
    BEFORE UPDATE ON public.organizations
    FOR EACH ROW
    EXECUTE FUNCTION update_organization_updated_at();
