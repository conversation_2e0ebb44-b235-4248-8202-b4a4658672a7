-- First, make sure <PERSON><PERSON> is enabled on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies that might be causing issues
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can create organizations" ON public.organizations;
DROP POLICY IF EXISTS "Organization admins can insert new members" ON public.organization_members;
DROP POLICY IF EXISTS "Users can create organization settings" ON public.organization_settings;
DROP POLICY IF EXISTS "Anyone can insert profiles" ON public.profiles;
DROP POLICY IF EXISTS "Anyone can insert organizations" ON public.organizations;
DROP POLICY IF EXISTS "Anyone can insert organization members" ON public.organization_members;
DROP POLICY IF EXISTS "Anyone can insert organization settings" ON public.organization_settings;
DROP POLICY IF EXISTS "Authenticated users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Authenticated users can create organizations" ON public.organizations;
DROP POLICY IF EXISTS "Authenticated users can add themselves as organization members" ON public.organization_members;
DROP POLICY IF EXISTS "Authenticated users can create organization settings" ON public.organization_settings;

-- Create a policy for authenticated users to insert their own profile
CREATE POLICY "Authenticated users can insert their own profile"
ON public.profiles
FOR INSERT
WITH CHECK (
    auth.uid() = id
);

-- Temporarily disable RLS for organizations to allow creation
ALTER TABLE public.organizations DISABLE ROW LEVEL SECURITY;

-- Create a policy for authenticated users to create organizations
-- This will be used when RLS is re-enabled
CREATE POLICY "Authenticated users can create organizations"
ON public.organizations
FOR INSERT
WITH CHECK (
    true
);

-- Temporarily disable RLS for organization members to allow creation
ALTER TABLE public.organization_members DISABLE ROW LEVEL SECURITY;

-- Create a policy for authenticated users to add themselves as organization members
-- This will be used when RLS is re-enabled
CREATE POLICY "Authenticated users can add themselves as organization members"
ON public.organization_members
FOR INSERT
WITH CHECK (
    true
);

-- Temporarily disable RLS for organization settings to allow creation
ALTER TABLE public.organization_settings DISABLE ROW LEVEL SECURITY;

-- Create a policy for authenticated users to create organization settings
-- This will be used when RLS is re-enabled
CREATE POLICY "Authenticated users can create organization settings"
ON public.organization_settings
FOR INSERT
WITH CHECK (
    true
);
