-- Fix chat system multi-tenancy and clean up unused functions
-- This migration addresses the organization filtering issue and improves security

-- First, drop all existing chat policies that might depend on functions
DROP POLICY IF EXISTS "Users can view their conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can create conversations in their organizations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can create conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation admins can update conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can view participants in their conversations" ON public.chat_participants;
DROP POLICY IF EXISTS "Conversation admins can manage participants" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can create their own participant record" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can update their own participant status" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can view messages in their conversations" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can send messages to their conversations" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can update their own messages" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can view message status in their conversations" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can update their own message status" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can update their own message read status" ON public.chat_message_status;

-- Now we can safely drop the functions
DROP FUNCTION IF EXISTS public.is_conversation_participant(UUID, UUID);
DROP FUNCTION IF EXISTS public.is_conversation_admin(UUID, UUID);
DROP FUNCTION IF EXISTS public.get_user_conversations();

-- Re-enable RLS for chat tables with proper policies
ALTER TABLE public.chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_message_status ENABLE ROW LEVEL SECURITY;

-- Create new simplified RLS policies with proper multi-tenancy

-- Drop any existing policies that might conflict
DROP POLICY IF EXISTS "Users can view conversations in their organizations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation creators can update their conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation creators can delete their conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can create participant records for conversations they're in" ON public.chat_participants;
DROP POLICY IF EXISTS "Conversation creators can delete participants" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can delete their own messages" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can view message status for their conversations" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can create their own message status" ON public.chat_message_status;

-- Chat Conversations Policies
CREATE POLICY "Users can view conversations in their organizations"
ON public.chat_conversations
FOR SELECT
USING (
    organization_id IN (SELECT public.get_user_organizations())
    AND id IN (
        SELECT conversation_id
        FROM public.chat_participants
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Users can create conversations in their organizations"
ON public.chat_conversations
FOR INSERT
WITH CHECK (
    organization_id IN (SELECT public.get_user_organizations())
    AND created_by = auth.uid()
);

CREATE POLICY "Conversation creators can update their conversations"
ON public.chat_conversations
FOR UPDATE
USING (
    created_by = auth.uid()
    AND organization_id IN (SELECT public.get_user_organizations())
);

CREATE POLICY "Conversation creators can delete their conversations"
ON public.chat_conversations
FOR DELETE
USING (
    created_by = auth.uid()
    AND organization_id IN (SELECT public.get_user_organizations())
);

-- Chat Participants Policies
CREATE POLICY "Users can view participants in their conversations"
ON public.chat_participants
FOR SELECT
USING (
    conversation_id IN (
        SELECT conversation_id
        FROM public.chat_participants
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Users can create participant records for conversations they're in"
ON public.chat_participants
FOR INSERT
WITH CHECK (
    conversation_id IN (
        SELECT id
        FROM public.chat_conversations
        WHERE created_by = auth.uid()
        AND organization_id IN (SELECT public.get_user_organizations())
    )
);

CREATE POLICY "Users can update their own participant status"
ON public.chat_participants
FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Conversation creators can delete participants"
ON public.chat_participants
FOR DELETE
USING (
    conversation_id IN (
        SELECT id
        FROM public.chat_conversations
        WHERE created_by = auth.uid()
        AND organization_id IN (SELECT public.get_user_organizations())
    )
);

-- Chat Messages Policies
CREATE POLICY "Users can view messages in their conversations"
ON public.chat_messages
FOR SELECT
USING (
    conversation_id IN (
        SELECT conversation_id
        FROM public.chat_participants
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Users can send messages to their conversations"
ON public.chat_messages
FOR INSERT
WITH CHECK (
    conversation_id IN (
        SELECT conversation_id
        FROM public.chat_participants
        WHERE user_id = auth.uid()
    )
    AND sender_id = auth.uid()
);

CREATE POLICY "Users can update their own messages"
ON public.chat_messages
FOR UPDATE
USING (sender_id = auth.uid());

CREATE POLICY "Users can delete their own messages"
ON public.chat_messages
FOR DELETE
USING (sender_id = auth.uid());

-- Chat Message Status Policies
CREATE POLICY "Users can view message status for their conversations"
ON public.chat_message_status
FOR SELECT
USING (
    message_id IN (
        SELECT id
        FROM public.chat_messages
        WHERE conversation_id IN (
            SELECT conversation_id
            FROM public.chat_participants
            WHERE user_id = auth.uid()
        )
    )
);

CREATE POLICY "Users can create their own message status"
ON public.chat_message_status
FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own message status"
ON public.chat_message_status
FOR UPDATE
USING (user_id = auth.uid());

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_chat_conversations_organization_id ON public.chat_conversations(organization_id);
CREATE INDEX IF NOT EXISTS idx_chat_conversations_created_by ON public.chat_conversations(created_by);
CREATE INDEX IF NOT EXISTS idx_chat_participants_user_id ON public.chat_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_participants_conversation_id ON public.chat_participants(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_id ON public.chat_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sender_id ON public.chat_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON public.chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_message_status_user_id ON public.chat_message_status(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_message_status_message_id ON public.chat_message_status(message_id);

-- Add comments for documentation
COMMENT ON TABLE public.chat_conversations IS 'Chat conversations with proper multi-tenant isolation';
COMMENT ON TABLE public.chat_participants IS 'Chat participants with organization-based access control';
COMMENT ON TABLE public.chat_messages IS 'Chat messages with participant-based access control';
COMMENT ON TABLE public.chat_message_status IS 'Message read status with user-based access control';
