-- Drop any existing policies for the chat_attachments bucket
DROP POLICY IF EXISTS "Allow authenticated users to upload files" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to read files" ON storage.objects;
DROP POLICY IF EXISTS "Allow file owners to update their files" ON storage.objects;
DROP POLICY IF EXISTS "Allow file owners to delete their files" ON storage.objects;

-- Create a simple policy to allow anyone to read files (public access)
CREATE POLICY "Allow public access to chat attachments"
ON storage.objects
FOR SELECT
TO public
USING (
  bucket_id = 'chat-attachments'
);

-- Create a policy to allow authenticated users to upload files
CREATE POLICY "Allow authenticated users to manage chat attachments"
ON storage.objects
FOR ALL
TO authenticated
USING (
  bucket_id = 'chat-attachments'
)
WITH CHECK (
  bucket_id = 'chat-attachments'
);

-- Create a storage bucket for chat attachments if it doesn't exist
-- Note: This requires superuser privileges and might need to be done manually
-- or through the Supabase dashboard
DO $$
BEGIN
    -- Drop the old bucket if it exists
    IF EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'chat_attachments') THEN
        -- This is a dangerous operation and might require manual intervention
        -- DELETE FROM storage.buckets WHERE id = 'chat_attachments';
        RAISE NOTICE 'The old bucket chat_attachments exists. Please delete it manually from the Supabase dashboard.';
    END IF;

    -- Create the new bucket with public access
    IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'chat-attachments') THEN
        INSERT INTO storage.buckets (id, name, public)
        VALUES ('chat-attachments', 'chat-attachments', true);

        -- Update the bucket to allow public access
        UPDATE storage.buckets
        SET public = true,
            file_size_limit = 10485760, -- 10MB
            allowed_mime_types = ARRAY[
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp',
                'image/svg+xml',
                'application/pdf'
            ]
        WHERE id = 'chat-attachments';
    ELSE
        -- If bucket exists, just ensure it's public
        UPDATE storage.buckets SET public = true WHERE id = 'chat-attachments';
    END IF;
END $$;
