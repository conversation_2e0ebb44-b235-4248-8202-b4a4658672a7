-- Migration to add float inventory tracking
-- This allows proper tracking of negative inventory (items sold without stock)

-- Create the float inventory table
CREATE TABLE IF NOT EXISTS public.float_inventory (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES public.organizations(id),
  product_id UUID NOT NULL REFERENCES public.products(id),
  quantity NUMERIC NOT NULL,
  sale_id UUID NOT NULL REFERENCES public.sales(id),
  sale_item_id UUID NOT NULL REFERENCES public.sale_items(id),
  resolved BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved_at TIMESTAMP WITH TIME ZONE,
  notes TEXT,

  -- Ensure float quantity is always positive
  CONSTRAINT positive_float_quantity CHECK (quantity > 0)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS float_inventory_product_id_idx ON public.float_inventory(product_id);
CREATE INDEX IF NOT EXISTS float_inventory_resolved_idx ON public.float_inventory(resolved);
CREATE INDEX IF NOT EXISTS float_inventory_organization_id_idx ON public.float_inventory(organization_id);

-- Add inventory settings to organization_settings
ALTER TABLE public.organization_settings
ADD COLUMN IF NOT EXISTS inventory_settings JSONB DEFAULT '{"allow_negative_inventory": true, "warn_on_low_inventory": true, "auto_create_purchase_requests": true}';

-- Check if conversion_factor column exists in inventory_transactions, add it if not
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'inventory_transactions'
    AND column_name = 'conversion_factor'
  ) THEN
    ALTER TABLE public.inventory_transactions
    ADD COLUMN conversion_factor NUMERIC DEFAULT 1;
  END IF;
END $$;

-- Create a function to track float inventory during sales
CREATE OR REPLACE FUNCTION track_float_inventory()
RETURNS TRIGGER AS $$
DECLARE
  v_current_stock NUMERIC;
  v_sale_quantity NUMERIC;
  v_float_quantity NUMERIC;
  v_sale_record RECORD;
  v_pcs_uom_id UUID;
  v_sale_item_id UUID;
BEGIN
  -- Get the current stock quantity
  SELECT stock_quantity INTO v_current_stock
  FROM products
  WHERE id = NEW.product_id;

  -- The sale quantity is the negative of the inventory transaction quantity
  v_sale_quantity := ABS(NEW.quantity);

  -- Check if this transaction would result in negative inventory
  IF v_current_stock < v_sale_quantity AND NEW.transaction_type = 'sale' THEN
    -- Calculate how much inventory is floating (negative)
    v_float_quantity := v_sale_quantity - v_current_stock;

    -- Get the 'pcs' UoM ID
    SELECT id INTO v_pcs_uom_id
    FROM units_of_measurement
    WHERE code = 'pcs' AND organization_id = NEW.organization_id
    LIMIT 1;

    -- If no 'pcs' UoM found, use the UoM from the current transaction
    IF v_pcs_uom_id IS NULL THEN
      v_pcs_uom_id := NEW.uom_id;

      -- If we still don't have a UoM ID, try to create a 'pcs' UoM
      IF v_pcs_uom_id IS NULL THEN
        INSERT INTO units_of_measurement (
          organization_id,
          code,
          name,
          description,
          is_active
        ) VALUES (
          NEW.organization_id,
          'pcs',
          'Pieces',
          'Individual units or items',
          true
        ) RETURNING id INTO v_pcs_uom_id;
      END IF;
    END IF;

    -- Get the sale information
    SELECT s.id INTO v_sale_record
    FROM sales s
    WHERE s.id = NEW.reference_id;

    -- Get the sale item ID
    SELECT id INTO v_sale_item_id
    FROM sale_items
    WHERE sale_id = v_sale_record.id AND product_id = NEW.product_id
    LIMIT 1;

    -- If we found the sale, create a float inventory record
    IF v_sale_record.id IS NOT NULL THEN
      INSERT INTO public.float_inventory (
        organization_id,
        product_id,
        quantity,
        sale_id,
        sale_item_id,
        resolved
      ) VALUES (
        NEW.organization_id,
        NEW.product_id,
        v_float_quantity,
        v_sale_record.id,
        v_sale_item_id,
        false
      );
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to track float inventory on sales
DROP TRIGGER IF EXISTS track_float_inventory_trigger ON public.inventory_transactions;
CREATE TRIGGER track_float_inventory_trigger
AFTER INSERT ON public.inventory_transactions
FOR EACH ROW
WHEN (NEW.transaction_type = 'sale')
EXECUTE FUNCTION track_float_inventory();

-- Create a function to resolve float inventory when receiving new stock
CREATE OR REPLACE FUNCTION resolve_float_inventory()
RETURNS TRIGGER AS $$
DECLARE
  v_remaining_quantity NUMERIC;
  float_record RECORD;
  v_pcs_uom_id UUID;
BEGIN
  -- Only process for receipt/purchase transactions that increase inventory
  IF NEW.transaction_type NOT IN ('receipt', 'purchase', 'adjustment') OR NEW.quantity <= 0 THEN
    RETURN NEW;
  END IF;

  -- Get the 'pcs' UoM ID
  SELECT id INTO v_pcs_uom_id
  FROM units_of_measurement
  WHERE code = 'pcs' AND organization_id = NEW.organization_id
  LIMIT 1;

  -- If no 'pcs' UoM found, use the UoM from the current transaction
  IF v_pcs_uom_id IS NULL THEN
    v_pcs_uom_id := NEW.uom_id;

    -- If we still don't have a UoM ID, try to create a 'pcs' UoM
    IF v_pcs_uom_id IS NULL THEN
      INSERT INTO units_of_measurement (
        organization_id,
        code,
        name,
        description,
        is_active
      ) VALUES (
        NEW.organization_id,
        'pcs',
        'Pieces',
        'Individual units or items',
        true
      ) RETURNING id INTO v_pcs_uom_id;
    END IF;
  END IF;

  -- Start with the full received quantity
  v_remaining_quantity := NEW.quantity;

  -- Process each unresolved float inventory record for this product
  FOR float_record IN (
    SELECT id, quantity
    FROM public.float_inventory
    WHERE product_id = NEW.product_id
    AND resolved = false
    ORDER BY created_at ASC
  )
  LOOP
    -- If we have enough quantity to resolve this float
    IF v_remaining_quantity >= float_record.quantity THEN
      -- Mark the float as resolved
      UPDATE public.float_inventory
      SET resolved = true,
          resolved_at = NOW()
      WHERE id = float_record.id;

      v_remaining_quantity := v_remaining_quantity - float_record.quantity;
    ELSE
      -- Partially resolve the float
      UPDATE public.float_inventory
      SET quantity = quantity - v_remaining_quantity,
          resolved_at = CASE WHEN quantity - v_remaining_quantity <= 0 THEN NOW() ELSE NULL END,
          resolved = CASE WHEN quantity - v_remaining_quantity <= 0 THEN true ELSE false END
      WHERE id = float_record.id;

      v_remaining_quantity := 0;
      EXIT; -- No more quantity to resolve floats
    END IF;

    -- If we've used all the received quantity, exit the loop
    IF v_remaining_quantity <= 0 THEN
      EXIT;
    END IF;
  END LOOP;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to resolve float inventory on receipt
DROP TRIGGER IF EXISTS resolve_float_inventory_trigger ON public.inventory_transactions;
CREATE TRIGGER resolve_float_inventory_trigger
AFTER INSERT ON public.inventory_transactions
FOR EACH ROW
WHEN (NEW.transaction_type IN ('receipt', 'purchase', 'adjustment') AND NEW.quantity > 0)
EXECUTE FUNCTION resolve_float_inventory();

-- Drop all policies for float_inventory until proper access control is implemented
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON public.float_inventory;
CREATE POLICY "Enable all access for authenticated users"
  ON public.float_inventory
  USING (true)
  WITH CHECK (true);

-- Enable RLS on the float_inventory table
ALTER TABLE public.float_inventory ENABLE ROW LEVEL SECURITY;
