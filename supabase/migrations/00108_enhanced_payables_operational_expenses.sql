-- Migration: Enhanced Payables System for Operational Expenses
-- Phase 1: Foundation - Expense Types, Enhanced Vendors, Basic Workflows
-- Extends existing payables system for comprehensive expense management

-- =====================================================
-- 1. EXPENSE TYPES MANAGEMENT
-- =====================================================

-- Expense types/categories for better classification
CREATE TABLE IF NOT EXISTS public.expense_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    
    -- Type details
    name TEXT NOT NULL,
    code TEXT NOT NULL, -- Short code for accounting (e.g., RENT, UTIL, SUPP)
    description TEXT,
    
    -- Classification
    category TEXT NOT NULL CHECK (category IN (
        'operational', 'administrative', 'financial', 'maintenance', 
        'professional_services', 'utilities', 'office_supplies', 'travel'
    )),
    
    -- Approval and accounting settings
    requires_approval BOOLEAN DEFAULT true,
    approval_limit NUMERIC(12,2) DEFAULT 0, -- Amount requiring higher approval
    default_account_code TEXT, -- Chart of accounts mapping
    
    -- Recurring expense settings
    is_recurring_type BOOLEAN DEFAULT false,
    default_frequency TEXT CHECK (default_frequency IN (
        'monthly', 'quarterly', 'semi_annual', 'annual', 'weekly', 'bi_weekly'
    )),
    
    -- Status and audit
    is_active BOOLEAN DEFAULT true,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_expense_type_code_per_org UNIQUE (organization_id, code),
    CONSTRAINT unique_expense_type_name_per_org UNIQUE (organization_id, name)
);

-- =====================================================
-- 2. ENHANCED VENDOR/SUPPLIER MANAGEMENT
-- =====================================================

-- Extend suppliers table for service providers
DO $$
BEGIN
    -- Add service categories if not exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'suppliers' AND column_name = 'service_categories'
    ) THEN
        ALTER TABLE public.suppliers
        ADD COLUMN service_categories TEXT[], -- Array of service types
        ADD COLUMN vendor_type TEXT DEFAULT 'goods' CHECK (vendor_type IN ('goods', 'services', 'both')),
        ADD COLUMN tax_id_number TEXT, -- BIR TIN for Philippines
        ADD COLUMN is_vat_registered BOOLEAN DEFAULT false,
        ADD COLUMN default_withholding_tax_rate NUMERIC(5,2) DEFAULT 0,
        ADD COLUMN performance_rating NUMERIC(3,2) DEFAULT 0 CHECK (performance_rating >= 0 AND performance_rating <= 5),
        ADD COLUMN last_performance_review DATE,
        ADD COLUMN preferred_payment_method TEXT DEFAULT 'bank_transfer' CHECK (preferred_payment_method IN (
            'cash', 'check', 'bank_transfer', 'gcash', 'paymaya', 'credit_card', 'other'
        ));
        
        COMMENT ON COLUMN public.suppliers.service_categories IS 'Array of service types: utilities, maintenance, professional, etc.';
        COMMENT ON COLUMN public.suppliers.vendor_type IS 'Type of vendor: goods, services, or both';
        COMMENT ON COLUMN public.suppliers.performance_rating IS 'Vendor performance rating 0-5 stars';
    END IF;
END $$;

-- Vendor contacts for multiple contact points
CREATE TABLE IF NOT EXISTS public.vendor_contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    supplier_id UUID NOT NULL REFERENCES public.suppliers(id) ON DELETE CASCADE,
    
    -- Contact details
    contact_type TEXT NOT NULL CHECK (contact_type IN (
        'primary', 'billing', 'technical', 'emergency', 'sales'
    )),
    name TEXT NOT NULL,
    title TEXT,
    email TEXT,
    phone TEXT,
    mobile TEXT,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_primary_contact_per_supplier UNIQUE (supplier_id, contact_type)
        DEFERRABLE INITIALLY DEFERRED
);

-- =====================================================
-- 3. RECURRING EXPENSES MANAGEMENT
-- =====================================================

-- Recurring expense templates
CREATE TABLE IF NOT EXISTS public.recurring_expenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    
    -- Template details
    name TEXT NOT NULL,
    description TEXT,
    expense_type_id UUID REFERENCES public.expense_types(id) ON DELETE SET NULL,
    
    -- Payee information
    supplier_id UUID REFERENCES public.suppliers(id) ON DELETE SET NULL,
    employee_id UUID REFERENCES public.employees(id) ON DELETE SET NULL,
    
    -- Financial details
    amount NUMERIC(12,2) NOT NULL CHECK (amount > 0),
    vat_amount NUMERIC(12,2) DEFAULT 0,
    withholding_tax_rate NUMERIC(5,2) DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'PHP',
    
    -- Recurrence settings
    frequency TEXT NOT NULL CHECK (frequency IN (
        'monthly', 'quarterly', 'semi_annual', 'annual', 'weekly', 'bi_weekly'
    )),
    start_date DATE NOT NULL,
    end_date DATE, -- NULL for indefinite
    next_due_date DATE NOT NULL,
    
    -- Payment terms
    payment_terms_days INTEGER DEFAULT 30,
    auto_create_payable BOOLEAN DEFAULT false, -- Auto-create payables
    
    -- Status and audit
    is_active BOOLEAN DEFAULT true,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_single_payee_recurring CHECK (
        (supplier_id IS NOT NULL AND employee_id IS NULL) OR
        (supplier_id IS NULL AND employee_id IS NOT NULL)
    )
);

-- =====================================================
-- 4. EXTEND EXISTING PAYABLES FOR OPERATIONAL EXPENSES
-- =====================================================

-- Add new source types to existing payables table
DO $$
BEGIN
    -- Update the check constraint to include new operational expense types
    ALTER TABLE public.payables DROP CONSTRAINT IF EXISTS payables_source_type_check;
    ALTER TABLE public.payables ADD CONSTRAINT payables_source_type_check 
    CHECK (source_type IN (
        'purchase_receipt', 'payroll', 'utility_bill', 'government_remittance', 
        'loan_repayment', 'manual_entry', 'recurring_expense', 'office_supplies',
        'maintenance', 'professional_services', 'rent', 'insurance', 'subscription'
    ));
    
    -- Add expense type reference if not exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'payables' AND column_name = 'expense_type_id'
    ) THEN
        ALTER TABLE public.payables
        ADD COLUMN expense_type_id UUID REFERENCES public.expense_types(id) ON DELETE SET NULL,
        ADD COLUMN recurring_expense_id UUID REFERENCES public.recurring_expenses(id) ON DELETE SET NULL,
        ADD COLUMN department TEXT, -- For expense allocation
        ADD COLUMN project_code TEXT, -- For project-based expenses
        ADD COLUMN approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN (
            'pending', 'approved', 'rejected', 'requires_higher_approval'
        )),
        ADD COLUMN approved_by UUID REFERENCES auth.users(id),
        ADD COLUMN approved_at TIMESTAMPTZ,
        ADD COLUMN rejection_reason TEXT;
        
        COMMENT ON COLUMN public.payables.expense_type_id IS 'Reference to expense type for categorization';
        COMMENT ON COLUMN public.payables.recurring_expense_id IS 'Reference to recurring expense template if auto-created';
        COMMENT ON COLUMN public.payables.department IS 'Department for expense allocation';
        COMMENT ON COLUMN public.payables.project_code IS 'Project code for project-based expenses';
    END IF;
END $$;

-- =====================================================
-- 5. APPROVAL WORKFLOWS
-- =====================================================

-- Approval workflow configuration
CREATE TABLE IF NOT EXISTS public.approval_workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    
    -- Workflow details
    name TEXT NOT NULL,
    description TEXT,
    
    -- Conditions
    expense_type_id UUID REFERENCES public.expense_types(id) ON DELETE CASCADE,
    min_amount NUMERIC(12,2) DEFAULT 0,
    max_amount NUMERIC(12,2), -- NULL for no limit
    
    -- Approval levels
    level_1_approver_role TEXT, -- Role-based approval
    level_1_amount_limit NUMERIC(12,2),
    level_2_approver_role TEXT,
    level_2_amount_limit NUMERIC(12,2),
    level_3_approver_role TEXT, -- Board/CEO level
    
    -- Settings
    requires_receipt BOOLEAN DEFAULT true,
    auto_approve_below_limit BOOLEAN DEFAULT false,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_workflow_per_expense_type UNIQUE (organization_id, expense_type_id)
);

-- =====================================================
-- 6. PERFORMANCE INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_expense_types_organization_id ON public.expense_types(organization_id);
CREATE INDEX IF NOT EXISTS idx_expense_types_category ON public.expense_types(category);
CREATE INDEX IF NOT EXISTS idx_expense_types_is_active ON public.expense_types(is_active);

CREATE INDEX IF NOT EXISTS idx_vendor_contacts_supplier_id ON public.vendor_contacts(supplier_id);
CREATE INDEX IF NOT EXISTS idx_vendor_contacts_contact_type ON public.vendor_contacts(contact_type);

CREATE INDEX IF NOT EXISTS idx_recurring_expenses_organization_id ON public.recurring_expenses(organization_id);
CREATE INDEX IF NOT EXISTS idx_recurring_expenses_next_due_date ON public.recurring_expenses(next_due_date);
CREATE INDEX IF NOT EXISTS idx_recurring_expenses_is_active ON public.recurring_expenses(is_active);

CREATE INDEX IF NOT EXISTS idx_payables_expense_type_id ON public.payables(expense_type_id);
CREATE INDEX IF NOT EXISTS idx_payables_approval_status ON public.payables(approval_status);
CREATE INDEX IF NOT EXISTS idx_payables_department ON public.payables(department);

CREATE INDEX IF NOT EXISTS idx_approval_workflows_organization_id ON public.approval_workflows(organization_id);
CREATE INDEX IF NOT EXISTS idx_approval_workflows_expense_type_id ON public.approval_workflows(expense_type_id);

-- =====================================================
-- 7. BUSINESS LOGIC FUNCTIONS
-- =====================================================

-- Calculate next due date for recurring expenses
CREATE OR REPLACE FUNCTION calculate_next_due_date(
    current_date DATE,
    frequency TEXT
) RETURNS DATE AS $$
BEGIN
    CASE frequency
        WHEN 'weekly' THEN RETURN current_date + INTERVAL '1 week';
        WHEN 'bi_weekly' THEN RETURN current_date + INTERVAL '2 weeks';
        WHEN 'monthly' THEN RETURN current_date + INTERVAL '1 month';
        WHEN 'quarterly' THEN RETURN current_date + INTERVAL '3 months';
        WHEN 'semi_annual' THEN RETURN current_date + INTERVAL '6 months';
        WHEN 'annual' THEN RETURN current_date + INTERVAL '1 year';
        ELSE RETURN current_date + INTERVAL '1 month'; -- Default to monthly
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Update recurring expense next due date
CREATE OR REPLACE FUNCTION update_recurring_expense_next_due()
RETURNS TRIGGER AS $$
BEGIN
    -- Update next due date when a payable is created from recurring expense
    IF NEW.recurring_expense_id IS NOT NULL THEN
        UPDATE public.recurring_expenses
        SET next_due_date = calculate_next_due_date(NEW.due_date, frequency),
            updated_at = NOW()
        WHERE id = NEW.recurring_expense_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 8. TRIGGERS
-- =====================================================

-- Update recurring expense next due date when payable is created
DROP TRIGGER IF EXISTS trigger_update_recurring_expense_next_due ON public.payables;
CREATE TRIGGER trigger_update_recurring_expense_next_due
    AFTER INSERT ON public.payables
    FOR EACH ROW
    WHEN (NEW.recurring_expense_id IS NOT NULL)
    EXECUTE FUNCTION update_recurring_expense_next_due();

-- Update timestamp triggers for new tables
DROP TRIGGER IF EXISTS trigger_expense_types_updated_at ON public.expense_types;
CREATE TRIGGER trigger_expense_types_updated_at
    BEFORE UPDATE ON public.expense_types
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_recurring_expenses_updated_at ON public.recurring_expenses;
CREATE TRIGGER trigger_recurring_expenses_updated_at
    BEFORE UPDATE ON public.recurring_expenses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 9. SECURITY (RLS DISABLED FOR NOW)
-- =====================================================

-- Disable RLS for new tables (as requested)
ALTER TABLE public.expense_types DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.vendor_contacts DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.recurring_expenses DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.approval_workflows DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- 10. DOCUMENTATION
-- =====================================================

COMMENT ON TABLE public.expense_types IS 'Expense type definitions for categorizing operational expenses';
COMMENT ON TABLE public.vendor_contacts IS 'Multiple contact points per vendor for different purposes';
COMMENT ON TABLE public.recurring_expenses IS 'Templates for recurring operational expenses';
COMMENT ON TABLE public.approval_workflows IS 'Configurable approval workflows for expense management';

COMMENT ON FUNCTION calculate_next_due_date(DATE, TEXT) IS 'Calculates next due date based on frequency';
COMMENT ON FUNCTION update_recurring_expense_next_due() IS 'Updates recurring expense next due date when payable is created';
