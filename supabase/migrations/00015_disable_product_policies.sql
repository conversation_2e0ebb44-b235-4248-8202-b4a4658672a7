-- This migration disables all RLS policies for products and categories tables
-- We'll implement proper policies later

-- Drop all policies for products table
DROP POLICY IF EXISTS "Users can view products in their organizations" ON public.products;
DROP POLICY IF EXISTS "Only owners, admins, and inventory managers can create products" ON public.products;
DROP POLICY IF EXISTS "Only owners, admins, and inventory managers can update products" ON public.products;
DROP POLICY IF EXISTS "Only owners and admins can delete products" ON public.products;

-- Drop all policies for categories table
DROP POLICY IF EXISTS "Users can view categories in their organizations" ON public.categories;
DROP POLICY IF EXISTS "Only owners, admins, and inventory managers can create categories" ON public.categories;
DROP POLICY IF EXISTS "Only owners, admins, and inventory managers can update categories" ON public.categories;
DROP POLICY IF EXISTS "Only owners and admins can delete categories" ON public.categories;

-- Disable RLS on products table
ALTER TABLE public.products DISABLE ROW LEVEL SECURITY;

-- Disable RLS on categories table
ALTER TABLE public.categories DISABLE ROW LEVEL SECURITY;

-- Create a simple policy that allows all operations for authenticated users
-- This is a fallback in case <PERSON><PERSON> is re-enabled
CREATE POLICY "Allow all operations for authenticated users" ON public.products
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

CREATE POLICY "Allow all operations for authenticated users" ON public.categories
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

-- Create storage bucket for product images if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'product-images') THEN
        INSERT INTO storage.buckets (id, name, public) VALUES ('product-images', 'product-images', true);
    END IF;
END $$;

-- Drop existing storage policies if they exist
DROP POLICY IF EXISTS "Anyone can view product images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload product images" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own product images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own product images" ON storage.objects;

-- Create simple storage policies that allow authenticated users to do anything with product images
CREATE POLICY "Anyone can view product images"
ON storage.objects FOR SELECT
USING (bucket_id = 'product-images');

CREATE POLICY "Authenticated users can upload product images"
ON storage.objects FOR INSERT
WITH CHECK (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
);

CREATE POLICY "Authenticated users can update product images"
ON storage.objects FOR UPDATE
USING (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
);

CREATE POLICY "Authenticated users can delete product images"
ON storage.objects FOR DELETE
USING (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
);
