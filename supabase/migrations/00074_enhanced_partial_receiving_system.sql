-- Enhanced Partial Receiving System Migration
-- Comprehensive handling of partial, over, damaged, and rejected items

-- =====================================================
-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON> INVENTORY RECEIPT ITEMS TABLE
-- =====================================================

-- Add comprehensive receiving status tracking
ALTER TABLE public.inventory_receipt_items
ADD COLUMN IF NOT EXISTS received_quantity NUMERIC(18,8) DEFAULT 0,
ADD COLUMN IF NOT EXISTS accepted_quantity NUMERIC(18,8) DEFAULT 0,
ADD COLUMN IF NOT EXISTS rejected_quantity NUMERIC(18,8) DEFAULT 0,
ADD COLUMN IF NOT EXISTS over_received_quantity NUMERIC(18,8) DEFAULT 0,
ADD COLUMN IF NOT EXISTS over_received_approved BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS over_received_approved_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS over_received_approved_at TIMESTAMPTZ;

-- Add detailed rejection tracking
ALTER TABLE public.inventory_receipt_items
ADD COLUMN IF NOT EXISTS rejection_reason TEXT,
ADD COLUMN IF NOT EXISTS rejection_category TEXT CHECK (rejection_category IN ('damaged', 'defective', 'wrong_item', 'expired', 'quality_fail', 'other')),
ADD COLUMN IF NOT EXISTS rejection_disposition TEXT CHECK (rejection_disposition IN ('return_to_supplier', 'dispose', 'rework', 'use_as_is', 'pending')),
ADD COLUMN IF NOT EXISTS rejection_cost_impact NUMERIC(10,2) DEFAULT 0;

-- Add receiving workflow status
ALTER TABLE public.inventory_receipt_items
ADD COLUMN IF NOT EXISTS receiving_status TEXT DEFAULT 'pending' 
CHECK (receiving_status IN ('pending', 'partial', 'complete', 'over_received', 'rejected', 'mixed'));

-- Add inspection and approval workflow
ALTER TABLE public.inventory_receipt_items
ADD COLUMN IF NOT EXISTS inspection_required BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS inspection_completed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS inspection_completed_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS inspection_completed_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS inspection_notes TEXT;

-- Add supplier notification tracking
ALTER TABLE public.inventory_receipt_items
ADD COLUMN IF NOT EXISTS supplier_notified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS supplier_notified_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS supplier_response TEXT,
ADD COLUMN IF NOT EXISTS supplier_response_date TIMESTAMPTZ;

-- =====================================================
-- 2. CREATE RECEIVING TRANSACTIONS TABLE
-- =====================================================

-- Track individual receiving transactions for audit trail
CREATE TABLE IF NOT EXISTS public.receiving_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    inventory_receipt_id UUID NOT NULL REFERENCES public.inventory_receipts(id) ON DELETE CASCADE,
    inventory_receipt_item_id UUID NOT NULL REFERENCES public.inventory_receipt_items(id) ON DELETE CASCADE,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('received', 'accepted', 'rejected', 'returned', 'adjusted')),
    quantity NUMERIC(18,8) NOT NULL,
    unit_cost NUMERIC(10,2) NOT NULL,
    total_cost NUMERIC(12,2) GENERATED ALWAYS AS (quantity * unit_cost) STORED,
    reason TEXT,
    notes TEXT,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_receiving_transactions_receipt ON public.receiving_transactions(inventory_receipt_id);
CREATE INDEX IF NOT EXISTS idx_receiving_transactions_item ON public.receiving_transactions(inventory_receipt_item_id);
CREATE INDEX IF NOT EXISTS idx_receiving_transactions_org ON public.receiving_transactions(organization_id);

-- =====================================================
-- 3. CREATE RECEIVING DISCREPANCIES TABLE
-- =====================================================

-- Track and manage receiving discrepancies
CREATE TABLE IF NOT EXISTS public.receiving_discrepancies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    inventory_receipt_id UUID NOT NULL REFERENCES public.inventory_receipts(id) ON DELETE CASCADE,
    inventory_receipt_item_id UUID NOT NULL REFERENCES public.inventory_receipt_items(id) ON DELETE CASCADE,
    discrepancy_type TEXT NOT NULL CHECK (discrepancy_type IN ('quantity_variance', 'quality_issue', 'price_variance', 'delivery_timing', 'documentation')),
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    description TEXT NOT NULL,
    expected_value TEXT,
    actual_value TEXT,
    financial_impact NUMERIC(12,2) DEFAULT 0,
    status TEXT DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'closed', 'escalated')),
    resolution_notes TEXT,
    resolved_by UUID REFERENCES auth.users(id),
    resolved_at TIMESTAMPTZ,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_receiving_discrepancies_receipt ON public.receiving_discrepancies(inventory_receipt_id);
CREATE INDEX IF NOT EXISTS idx_receiving_discrepancies_status ON public.receiving_discrepancies(status);
CREATE INDEX IF NOT EXISTS idx_receiving_discrepancies_severity ON public.receiving_discrepancies(severity);

-- =====================================================
-- 4. ENHANCED FUNCTIONS FOR RECEIVING CALCULATIONS
-- =====================================================

-- Function to calculate receiving status for an item
CREATE OR REPLACE FUNCTION calculate_receiving_status(
    p_expected_quantity NUMERIC,
    p_received_quantity NUMERIC,
    p_accepted_quantity NUMERIC,
    p_rejected_quantity NUMERIC
) RETURNS TEXT AS $$
BEGIN
    -- If nothing received yet
    IF p_received_quantity = 0 THEN
        RETURN 'pending';
    END IF;
    
    -- If over-received
    IF p_received_quantity > p_expected_quantity THEN
        RETURN 'over_received';
    END IF;
    
    -- If fully received and all accepted
    IF p_received_quantity = p_expected_quantity AND p_rejected_quantity = 0 THEN
        RETURN 'complete';
    END IF;
    
    -- If some items rejected
    IF p_rejected_quantity > 0 THEN
        IF p_accepted_quantity + p_rejected_quantity = p_expected_quantity THEN
            RETURN 'mixed';
        ELSE
            RETURN 'partial';
        END IF;
    END IF;
    
    -- If partially received
    IF p_received_quantity < p_expected_quantity THEN
        RETURN 'partial';
    END IF;
    
    -- Default
    RETURN 'partial';
END;
$$ LANGUAGE plpgsql;

-- Function to update receiving status automatically
CREATE OR REPLACE FUNCTION update_receiving_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate and update receiving status
    NEW.receiving_status := calculate_receiving_status(
        COALESCE(NEW.expected_quantity, NEW.quantity),
        COALESCE(NEW.received_quantity, 0),
        COALESCE(NEW.accepted_quantity, 0),
        COALESCE(NEW.rejected_quantity, 0)
    );
    
    -- Update over_received_quantity
    IF NEW.received_quantity > COALESCE(NEW.expected_quantity, NEW.quantity) THEN
        NEW.over_received_quantity := NEW.received_quantity - COALESCE(NEW.expected_quantity, NEW.quantity);
    ELSE
        NEW.over_received_quantity := 0;
    END IF;
    
    -- Update variance_quantity
    NEW.variance_quantity := NEW.received_quantity - COALESCE(NEW.expected_quantity, NEW.quantity);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic status updates
DROP TRIGGER IF EXISTS update_receiving_status_trigger ON public.inventory_receipt_items;
CREATE TRIGGER update_receiving_status_trigger
    BEFORE INSERT OR UPDATE OF received_quantity, accepted_quantity, rejected_quantity, expected_quantity
    ON public.inventory_receipt_items
    FOR EACH ROW
    EXECUTE FUNCTION update_receiving_status();

-- =====================================================
-- 5. PAYABLES INTEGRATION FUNCTIONS
-- =====================================================

-- Function to calculate payable amount based on actual received/accepted quantities
CREATE OR REPLACE FUNCTION calculate_receipt_payable_amount(
    p_receipt_id UUID
) RETURNS TABLE (
    total_amount NUMERIC(12,2),
    accepted_amount NUMERIC(12,2),
    rejected_amount NUMERIC(12,2),
    over_received_amount NUMERIC(12,2),
    payable_amount NUMERIC(12,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        SUM(iri.quantity * iri.unit_cost) as total_amount,
        SUM(COALESCE(iri.accepted_quantity, 0) * iri.unit_cost) as accepted_amount,
        SUM(COALESCE(iri.rejected_quantity, 0) * iri.unit_cost) as rejected_amount,
        SUM(COALESCE(iri.over_received_quantity, 0) * iri.unit_cost) as over_received_amount,
        SUM(
            CASE 
                WHEN iri.over_received_approved THEN 
                    COALESCE(iri.accepted_quantity, 0) * iri.unit_cost + 
                    COALESCE(iri.over_received_quantity, 0) * iri.unit_cost
                ELSE 
                    COALESCE(iri.accepted_quantity, 0) * iri.unit_cost
            END
        ) as payable_amount
    FROM public.inventory_receipt_items iri
    WHERE iri.inventory_receipt_id = p_receipt_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. RECEIVING WORKFLOW FUNCTIONS
-- =====================================================

-- Function to process receiving transaction
CREATE OR REPLACE FUNCTION process_receiving_transaction(
    p_organization_id UUID,
    p_receipt_item_id UUID,
    p_transaction_type TEXT,
    p_quantity NUMERIC,
    p_reason TEXT DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    v_receipt_id UUID;
    v_unit_cost NUMERIC;
BEGIN
    -- Get receipt info
    SELECT inventory_receipt_id, unit_cost 
    INTO v_receipt_id, v_unit_cost
    FROM public.inventory_receipt_items 
    WHERE id = p_receipt_item_id;
    
    -- Insert receiving transaction
    INSERT INTO public.receiving_transactions (
        organization_id, inventory_receipt_id, inventory_receipt_item_id,
        transaction_type, quantity, unit_cost, reason, notes, created_by
    ) VALUES (
        p_organization_id, v_receipt_id, p_receipt_item_id,
        p_transaction_type, p_quantity, v_unit_cost, p_reason, p_notes, p_user_id
    );
    
    -- Update item quantities based on transaction type
    IF p_transaction_type = 'received' THEN
        UPDATE public.inventory_receipt_items 
        SET received_quantity = COALESCE(received_quantity, 0) + p_quantity
        WHERE id = p_receipt_item_id;
    ELSIF p_transaction_type = 'accepted' THEN
        UPDATE public.inventory_receipt_items 
        SET accepted_quantity = COALESCE(accepted_quantity, 0) + p_quantity
        WHERE id = p_receipt_item_id;
    ELSIF p_transaction_type = 'rejected' THEN
        UPDATE public.inventory_receipt_items 
        SET rejected_quantity = COALESCE(rejected_quantity, 0) + p_quantity
        WHERE id = p_receipt_item_id;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. COMMENTS AND DOCUMENTATION
-- =====================================================

COMMENT ON TABLE public.receiving_transactions IS 'Audit trail for all receiving transactions';
COMMENT ON TABLE public.receiving_discrepancies IS 'Track and manage receiving discrepancies';
COMMENT ON FUNCTION calculate_receiving_status IS 'Calculate receiving status based on quantities';
COMMENT ON FUNCTION calculate_receipt_payable_amount IS 'Calculate payable amount based on accepted quantities';
COMMENT ON FUNCTION process_receiving_transaction IS 'Process individual receiving transactions';

-- =====================================================
-- 8. VERIFICATION AND COMPLETION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ Enhanced Partial Receiving System migration completed successfully!';
    RAISE NOTICE '🔧 New Features:';
    RAISE NOTICE '   - Comprehensive quantity tracking (received, accepted, rejected, over-received)';
    RAISE NOTICE '   - Receiving transactions audit trail';
    RAISE NOTICE '   - Discrepancy management system';
    RAISE NOTICE '   - Automatic status calculations';
    RAISE NOTICE '   - Payables integration with accepted quantities';
    RAISE NOTICE '   - Inspection and approval workflows';
    RAISE NOTICE '   - Supplier notification tracking';
    RAISE NOTICE '🚀 Ready for enhanced receiving workflows!';
END $$;
