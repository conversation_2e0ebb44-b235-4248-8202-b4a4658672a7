-- Create QC checklist templates table
CREATE TABLE public.qc_checklist_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (organization_id, name)
);

-- Create QC checklist items table
CREATE TABLE public.qc_checklist_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID NOT NULL REFERENCES public.qc_checklist_templates(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    item_type TEXT NOT NULL CHECK (item_type IN ('boolean', 'numeric', 'text', 'select')),
    is_required BOOLEAN NOT NULL DEFAULT TRUE,
    min_value NUMERIC,
    max_value NUMERIC,
    unit TEXT,
    options JSONB, -- For select type items, stores the available options
    pass_criteria TEXT, -- JSON string with criteria for passing
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create product QC template assignments table
CREATE TABLE public.product_qc_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    template_id UUID NOT NULL REFERENCES public.qc_checklist_templates(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (product_id, template_id)
);

-- Create QC inspection records table
CREATE TABLE public.qc_inspections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    inventory_receipt_item_id UUID REFERENCES public.inventory_receipt_items(id) ON DELETE SET NULL,
    return_order_item_id UUID REFERENCES public.return_order_items(id) ON DELETE SET NULL,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE RESTRICT,
    template_id UUID NOT NULL REFERENCES public.qc_checklist_templates(id) ON DELETE RESTRICT,
    inspector_id UUID NOT NULL REFERENCES auth.users(id),
    inspection_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    status TEXT NOT NULL CHECK (status IN ('passed', 'failed', 'pending')),
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create QC inspection results table
CREATE TABLE public.qc_inspection_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inspection_id UUID NOT NULL REFERENCES public.qc_inspections(id) ON DELETE CASCADE,
    checklist_item_id UUID NOT NULL REFERENCES public.qc_checklist_items(id) ON DELETE CASCADE,
    result_value TEXT, -- Stores the result as text, regardless of the item type
    numeric_value NUMERIC, -- For numeric results, to enable range queries
    is_passed BOOLEAN NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (inspection_id, checklist_item_id)
);

-- Add trigger to update updated_at columns
CREATE OR REPLACE FUNCTION update_qc_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_qc_checklist_templates_updated_at
BEFORE UPDATE ON public.qc_checklist_templates
FOR EACH ROW
EXECUTE FUNCTION update_qc_updated_at_column();

CREATE TRIGGER update_qc_checklist_items_updated_at
BEFORE UPDATE ON public.qc_checklist_items
FOR EACH ROW
EXECUTE FUNCTION update_qc_updated_at_column();

CREATE TRIGGER update_product_qc_templates_updated_at
BEFORE UPDATE ON public.product_qc_templates
FOR EACH ROW
EXECUTE FUNCTION update_qc_updated_at_column();

CREATE TRIGGER update_qc_inspections_updated_at
BEFORE UPDATE ON public.qc_inspections
FOR EACH ROW
EXECUTE FUNCTION update_qc_updated_at_column();

CREATE TRIGGER update_qc_inspection_results_updated_at
BEFORE UPDATE ON public.qc_inspection_results
FOR EACH ROW
EXECUTE FUNCTION update_qc_updated_at_column();

-- Add RLS policies
CREATE POLICY "Allow all authenticated users to manage QC checklist templates"
ON public.qc_checklist_templates
FOR ALL
USING (true)
WITH CHECK (true);

CREATE POLICY "Allow all authenticated users to manage QC checklist items"
ON public.qc_checklist_items
FOR ALL
USING (true)
WITH CHECK (true);

CREATE POLICY "Allow all authenticated users to manage product QC templates"
ON public.product_qc_templates
FOR ALL
USING (true)
WITH CHECK (true);

CREATE POLICY "Allow all authenticated users to manage QC inspections"
ON public.qc_inspections
FOR ALL
USING (true)
WITH CHECK (true);

CREATE POLICY "Allow all authenticated users to manage QC inspection results"
ON public.qc_inspection_results
FOR ALL
USING (true)
WITH CHECK (true);

-- Create function to evaluate if a QC result passes criteria
CREATE OR REPLACE FUNCTION evaluate_qc_result(
    item_type TEXT,
    result_value TEXT,
    numeric_value NUMERIC,
    pass_criteria TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    criteria JSONB;
    passed BOOLEAN := FALSE;
BEGIN
    -- If no criteria, default to passed
    IF pass_criteria IS NULL OR pass_criteria = '' THEN
        RETURN TRUE;
    END IF;

    -- Parse the criteria
    BEGIN
        criteria := pass_criteria::JSONB;
    EXCEPTION WHEN OTHERS THEN
        -- If criteria is not valid JSON, default to passed
        RETURN TRUE;
    END;

    -- Evaluate based on item type
    CASE item_type
        WHEN 'boolean' THEN
            -- For boolean, check if the result matches the expected value
            IF criteria ? 'expected' THEN
                passed := (result_value = criteria->>'expected');
            ELSE
                passed := (result_value = 'true');
            END IF;

        WHEN 'numeric' THEN
            -- For numeric, check if the value is within the specified range
            IF criteria ? 'min' AND criteria ? 'max' THEN
                passed := (numeric_value >= (criteria->>'min')::NUMERIC AND numeric_value <= (criteria->>'max')::NUMERIC);
            ELSIF criteria ? 'min' THEN
                passed := (numeric_value >= (criteria->>'min')::NUMERIC);
            ELSIF criteria ? 'max' THEN
                passed := (numeric_value <= (criteria->>'max')::NUMERIC);
            ELSE
                passed := TRUE;
            END IF;

        WHEN 'select' THEN
            -- For select, check if the result is in the list of passing values
            IF criteria ? 'passing_values' AND criteria->'passing_values' @> to_jsonb(result_value) THEN
                passed := TRUE;
            ELSE
                passed := FALSE;
            END IF;

        WHEN 'text' THEN
            -- For text, always pass unless there's a specific regex pattern to match
            IF criteria ? 'pattern' THEN
                passed := result_value ~ (criteria->>'pattern');
            ELSE
                passed := TRUE;
            END IF;

        ELSE
            -- Default to passed for unknown types
            passed := TRUE;
    END CASE;

    RETURN passed;
END;
$$ LANGUAGE plpgsql;
