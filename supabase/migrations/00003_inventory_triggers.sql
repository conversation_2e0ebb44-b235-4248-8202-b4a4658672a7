-- Create a function to update product stock quantity
CREATE OR REPLACE FUNCTION update_product_stock()
RETURNS TRIGGER AS $$
DECLARE
    org_id UUID;
BEGIN
    -- Get the organization_id for the product
    SELECT organization_id INTO org_id FROM public.products WHERE id = NEW.product_id;
    
    -- Insert a new inventory transaction
    INSERT INTO public.inventory_transactions (
        organization_id,
        product_id,
        transaction_type,
        quantity,
        reference_id,
        reference_type,
        notes,
        created_by
    ) VALUES (
        org_id,
        NEW.product_id,
        CASE
            WHEN TG_TABLE_NAME = 'inventory_receipt_items' THEN 'purchase'
            WHEN TG_TABLE_NAME = 'sale_items' THEN 'sale'
        END,
        CASE
            WHEN TG_TABLE_NAME = 'inventory_receipt_items' THEN NEW.quantity
            WHEN TG_TABLE_NAME = 'sale_items' THEN -NEW.quantity
        END,
        CASE
            WHEN TG_TABLE_NAME = 'inventory_receipt_items' THEN NEW.inventory_receipt_id
            WHEN TG_TABLE_NAME = 'sale_items' THEN NEW.sale_id
        END,
        TG_TABLE_NAME,
        'Automatic inventory update',
        auth.uid()
    );
    
    -- Update the product stock quantity
    UPDATE public.products
    SET 
        stock_quantity = stock_quantity + (
            CASE
                WHEN TG_TABLE_NAME = 'inventory_receipt_items' THEN NEW.quantity
                WHEN TG_TABLE_NAME = 'sale_items' THEN -NEW.quantity
            END
        ),
        updated_at = NOW()
    WHERE id = NEW.product_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for inventory updates
CREATE TRIGGER update_stock_on_receipt
AFTER INSERT ON public.inventory_receipt_items
FOR EACH ROW
EXECUTE FUNCTION update_product_stock();

CREATE TRIGGER update_stock_on_sale
AFTER INSERT ON public.sale_items
FOR EACH ROW
EXECUTE FUNCTION update_product_stock();

-- Create a function to check stock levels and create purchase requests
CREATE OR REPLACE FUNCTION check_stock_levels()
RETURNS TRIGGER AS $$
DECLARE
    below_min_stock BOOLEAN;
    req_id UUID;
    req_number TEXT;
    org_id UUID;
BEGIN
    -- Check if stock is below minimum level
    below_min_stock := NEW.stock_quantity <= NEW.min_stock_level;
    
    -- If stock is below minimum and it's a sale transaction that triggered this
    IF below_min_stock AND TG_OP = 'UPDATE' AND NEW.stock_quantity < OLD.stock_quantity THEN
        -- Get the organization ID
        org_id := NEW.organization_id;
        
        -- Generate a request number
        req_number := 'PR-' || to_char(NOW(), 'YYYYMMDD') || '-' || 
                      (SELECT COUNT(*) + 1 FROM public.purchase_requests 
                       WHERE organization_id = org_id 
                       AND created_at::date = CURRENT_DATE);
        
        -- Create a purchase request
        INSERT INTO public.purchase_requests (
            organization_id,
            request_number,
            requester_id,
            status,
            notes
        ) VALUES (
            org_id,
            req_number,
            auth.uid(),
            'draft',
            'Automatically generated due to low stock level'
        ) RETURNING id INTO req_id;
        
        -- Add the product to the purchase request
        INSERT INTO public.purchase_request_items (
            purchase_request_id,
            product_id,
            quantity,
            notes
        ) VALUES (
            req_id,
            NEW.id,
            NEW.min_stock_level - NEW.stock_quantity + 5, -- Order enough to get above min level plus buffer
            'Automatically added due to low stock level'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for stock level checks
CREATE TRIGGER check_product_stock_levels
AFTER UPDATE OF stock_quantity ON public.products
FOR EACH ROW
EXECUTE FUNCTION check_stock_levels();
