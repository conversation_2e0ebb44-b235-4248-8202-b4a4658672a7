-- Migration: Add pay_based_on_time_entries setting to payroll_settings
-- This critical field controls whether payroll is calculated based on actual time entries or scheduled periods

-- Add the pay_based_on_time_entries column
ALTER TABLE public.payroll_settings 
ADD COLUMN IF NOT EXISTS pay_based_on_time_entries BOOLEAN NOT NULL DEFAULT true;

-- Add comment to explain the purpose
COMMENT ON COLUMN public.payroll_settings.pay_based_on_time_entries IS 
'When true, payroll is calculated based on actual time entries. When false, uses scheduled period length.';

-- Update existing records to use time-based calculation (more accurate)
UPDATE public.payroll_settings 
SET pay_based_on_time_entries = true 
WHERE pay_based_on_time_entries IS NULL;

-- Verify the change
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'payroll_settings'
        AND column_name = 'pay_based_on_time_entries'
    ) THEN
        RAISE EXCEPTION 'pay_based_on_time_entries column not added to payroll_settings';
    END IF;
    
    RAISE NOTICE 'Migration completed: pay_based_on_time_entries field added to payroll_settings';
END $$;
