-- Add status field to inventory_receipts table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipts'
        AND column_name = 'status'
    ) THEN
        ALTER TABLE public.inventory_receipts
        ADD COLUMN status TEXT NOT NULL DEFAULT 'completed'
        CHECK (status IN ('draft', 'completed', 'cancelled'));
    END IF;
END $$;

-- Add quality control fields to inventory_receipt_items
DO $$
BEGIN
    -- Add qc_status column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'qc_status'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN qc_status TEXT DEFAULT 'passed'
        CHECK (qc_status IN ('passed', 'failed', 'quarantine'));
    END IF;

    -- Add damaged_quantity column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'damaged_quantity'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN damaged_quantity NUMERIC(18,8) DEFAULT 0;
    END IF;

    -- Add damage_reason column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'damage_reason'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN damage_reason TEXT;
    END IF;
END $$;

-- Add batch/lot tracking fields to inventory_receipt_items
DO $$
BEGIN
    -- Add lot_number column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'lot_number'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN lot_number TEXT;
    END IF;

    -- Add expiry_date column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'expiry_date'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN expiry_date TIMESTAMPTZ;
    END IF;

    -- Add expected_quantity column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'expected_quantity'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN expected_quantity NUMERIC(18,8);
    END IF;

    -- Add variance_quantity column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'variance_quantity'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN variance_quantity NUMERIC(18,8);
    END IF;

    -- Add variance_reason column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'variance_reason'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN variance_reason TEXT;
    END IF;

    -- Add serial_numbers column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'serial_numbers'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN serial_numbers TEXT[];
    END IF;
END $$;

-- Add early/late delivery tracking
DO $$
BEGIN
    -- Add expected_date column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipts'
        AND column_name = 'expected_date'
    ) THEN
        ALTER TABLE public.inventory_receipts
        ADD COLUMN expected_date TIMESTAMPTZ;
    END IF;

    -- Add is_early_delivery column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipts'
        AND column_name = 'is_early_delivery'
    ) THEN
        ALTER TABLE public.inventory_receipts
        ADD COLUMN is_early_delivery BOOLEAN DEFAULT FALSE;
    END IF;

    -- Add is_late_delivery column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipts'
        AND column_name = 'is_late_delivery'
    ) THEN
        ALTER TABLE public.inventory_receipts
        ADD COLUMN is_late_delivery BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- Add price variance tracking
DO $$
BEGIN
    -- Add expected_unit_cost column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'expected_unit_cost'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN expected_unit_cost NUMERIC(10, 2);
    END IF;

    -- Add cost_variance column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'cost_variance'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN cost_variance NUMERIC(10, 2);
    END IF;

    -- Add cost_variance_approved column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'cost_variance_approved'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN cost_variance_approved BOOLEAN DEFAULT FALSE;
    END IF;

    -- Add cost_variance_approved_by column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'cost_variance_approved_by'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN cost_variance_approved_by UUID REFERENCES auth.users(id);
    END IF;

    -- Add cost_variance_approved_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'inventory_receipt_items'
        AND column_name = 'cost_variance_approved_at'
    ) THEN
        ALTER TABLE public.inventory_receipt_items
        ADD COLUMN cost_variance_approved_at TIMESTAMPTZ;
    END IF;
END $$;

-- Create a function to update purchase order status when receipt is created
CREATE OR REPLACE FUNCTION update_purchase_order_status_on_receipt()
RETURNS TRIGGER AS $$
DECLARE
    po_id UUID;
    total_ordered NUMERIC;
    total_received NUMERIC;
BEGIN
    -- Get the purchase order ID
    SELECT purchase_order_id INTO po_id FROM public.inventory_receipts WHERE id = NEW.inventory_receipt_id;

    -- If there's no purchase order, exit
    IF po_id IS NULL THEN
        RETURN NEW;
    END IF;

    -- Update the received quantity for the purchase order item
    UPDATE public.purchase_order_items
    SET received_quantity = received_quantity + NEW.quantity
    WHERE id = NEW.purchase_order_item_id;

    -- Calculate total ordered and received quantities
    SELECT
        SUM(quantity) as ordered,
        SUM(received_quantity) as received
    INTO
        total_ordered,
        total_received
    FROM public.purchase_order_items
    WHERE purchase_order_id = po_id;

    -- Update the purchase order status based on received quantities
    IF total_received >= total_ordered THEN
        UPDATE public.purchase_orders
        SET status = 'received'
        WHERE id = po_id;
    ELSIF total_received > 0 THEN
        UPDATE public.purchase_orders
        SET status = 'partially_received'
        WHERE id = po_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update purchase order status when receipt item is created
CREATE TRIGGER update_po_status_on_receipt
AFTER INSERT ON public.inventory_receipt_items
FOR EACH ROW
EXECUTE FUNCTION update_purchase_order_status_on_receipt();

-- Create a function to check for early/late delivery
CREATE OR REPLACE FUNCTION check_delivery_timing()
RETURNS TRIGGER AS $$
DECLARE
    po_expected_date TIMESTAMPTZ;
BEGIN
    -- Get the expected delivery date from the purchase order
    SELECT expected_delivery_date INTO po_expected_date
    FROM public.purchase_orders
    WHERE id = NEW.purchase_order_id;

    -- If there's no expected date, exit
    IF po_expected_date IS NULL THEN
        RETURN NEW;
    END IF;

    -- Set the expected date on the receipt
    NEW.expected_date := po_expected_date;

    -- Check if early or late
    IF NEW.receipt_date < po_expected_date THEN
        NEW.is_early_delivery := TRUE;
    ELSIF NEW.receipt_date > po_expected_date THEN
        NEW.is_late_delivery := TRUE;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to check delivery timing when receipt is created
CREATE TRIGGER check_delivery_timing_trigger
BEFORE INSERT ON public.inventory_receipts
FOR EACH ROW
EXECUTE FUNCTION check_delivery_timing();
