-- Clean migration to fix chat system multi-tenancy
-- This migration properly handles existing policies and functions

-- Step 1: Drop ALL existing chat policies first (comprehensive list)
DROP POLICY IF EXISTS "Users can view their conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can create conversations in their organizations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can create conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation admins can update conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can view conversations in their organizations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation creators can update their conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation creators can delete their conversations" ON public.chat_conversations;

DROP POLICY IF EXISTS "Users can view participants in their conversations" ON public.chat_participants;
DROP POLICY IF EXISTS "Conversation admins can manage participants" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can create their own participant record" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can update their own participant status" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can create participant records for conversations they're in" ON public.chat_participants;
DROP POLICY IF EXISTS "Conversation creators can delete participants" ON public.chat_participants;

DROP POLICY IF EXISTS "Users can view messages in their conversations" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can send messages to their conversations" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can update their own messages" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can delete their own messages" ON public.chat_messages;

DROP POLICY IF EXISTS "Users can view message status in their conversations" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can update their own message status" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can update their own message read status" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can view message status for their conversations" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can create their own message status" ON public.chat_message_status;

-- Step 2: Drop functions that might be referenced by policies
DROP FUNCTION IF EXISTS public.is_conversation_participant(UUID, UUID);
DROP FUNCTION IF EXISTS public.is_conversation_admin(UUID, UUID);
DROP FUNCTION IF EXISTS public.get_user_conversations();

-- Step 3: Ensure RLS is enabled
ALTER TABLE public.chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_message_status ENABLE ROW LEVEL SECURITY;

-- Step 4: Create new clean policies with proper multi-tenancy

-- Chat Conversations Policies
CREATE POLICY "chat_conversations_select_policy"
ON public.chat_conversations
FOR SELECT
USING (
    organization_id IN (SELECT public.get_user_organizations())
    AND id IN (
        SELECT conversation_id 
        FROM public.chat_participants 
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "chat_conversations_insert_policy"
ON public.chat_conversations
FOR INSERT
WITH CHECK (
    organization_id IN (SELECT public.get_user_organizations())
    AND created_by = auth.uid()
);

CREATE POLICY "chat_conversations_update_policy"
ON public.chat_conversations
FOR UPDATE
USING (
    created_by = auth.uid()
    AND organization_id IN (SELECT public.get_user_organizations())
);

CREATE POLICY "chat_conversations_delete_policy"
ON public.chat_conversations
FOR DELETE
USING (
    created_by = auth.uid()
    AND organization_id IN (SELECT public.get_user_organizations())
);

-- Chat Participants Policies
CREATE POLICY "chat_participants_select_policy"
ON public.chat_participants
FOR SELECT
USING (
    conversation_id IN (
        SELECT conversation_id 
        FROM public.chat_participants 
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "chat_participants_insert_policy"
ON public.chat_participants
FOR INSERT
WITH CHECK (
    conversation_id IN (
        SELECT id 
        FROM public.chat_conversations 
        WHERE created_by = auth.uid()
        AND organization_id IN (SELECT public.get_user_organizations())
    )
);

CREATE POLICY "chat_participants_update_policy"
ON public.chat_participants
FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "chat_participants_delete_policy"
ON public.chat_participants
FOR DELETE
USING (
    conversation_id IN (
        SELECT id 
        FROM public.chat_conversations 
        WHERE created_by = auth.uid()
        AND organization_id IN (SELECT public.get_user_organizations())
    )
);

-- Chat Messages Policies
CREATE POLICY "chat_messages_select_policy"
ON public.chat_messages
FOR SELECT
USING (
    conversation_id IN (
        SELECT conversation_id 
        FROM public.chat_participants 
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "chat_messages_insert_policy"
ON public.chat_messages
FOR INSERT
WITH CHECK (
    conversation_id IN (
        SELECT conversation_id 
        FROM public.chat_participants 
        WHERE user_id = auth.uid()
    )
    AND sender_id = auth.uid()
);

CREATE POLICY "chat_messages_update_policy"
ON public.chat_messages
FOR UPDATE
USING (sender_id = auth.uid());

CREATE POLICY "chat_messages_delete_policy"
ON public.chat_messages
FOR DELETE
USING (sender_id = auth.uid());

-- Chat Message Status Policies
CREATE POLICY "chat_message_status_select_policy"
ON public.chat_message_status
FOR SELECT
USING (
    message_id IN (
        SELECT id 
        FROM public.chat_messages 
        WHERE conversation_id IN (
            SELECT conversation_id 
            FROM public.chat_participants 
            WHERE user_id = auth.uid()
        )
    )
);

CREATE POLICY "chat_message_status_insert_policy"
ON public.chat_message_status
FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "chat_message_status_update_policy"
ON public.chat_message_status
FOR UPDATE
USING (user_id = auth.uid());

-- Step 5: Add performance indexes
CREATE INDEX IF NOT EXISTS idx_chat_conversations_organization_id ON public.chat_conversations(organization_id);
CREATE INDEX IF NOT EXISTS idx_chat_conversations_created_by ON public.chat_conversations(created_by);
CREATE INDEX IF NOT EXISTS idx_chat_participants_user_id ON public.chat_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_participants_conversation_id ON public.chat_participants(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_id ON public.chat_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sender_id ON public.chat_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON public.chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_message_status_user_id ON public.chat_message_status(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_message_status_message_id ON public.chat_message_status(message_id);

-- Step 6: Add table comments
COMMENT ON TABLE public.chat_conversations IS 'Chat conversations with proper multi-tenant isolation';
COMMENT ON TABLE public.chat_participants IS 'Chat participants with organization-based access control';
COMMENT ON TABLE public.chat_messages IS 'Chat messages with participant-based access control';
COMMENT ON TABLE public.chat_message_status IS 'Message read status with user-based access control';
