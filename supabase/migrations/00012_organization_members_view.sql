-- Create a view to make it easier to get organization members with their profiles
CREATE OR REPLACE VIEW public.organization_members_with_profiles AS
SELECT
    om.id,
    om.organization_id,
    om.user_id,
    om.role,
    om.created_at,
    om.updated_at,
    p.first_name,
    p.last_name,
    p.avatar_url
FROM
    public.organization_members om
LEFT JOIN
    public.profiles p ON om.user_id = p.id
WHERE
    -- Apply security filter directly in the view definition
    -- This ensures only members of the same organization can be seen
    EXISTS (
        SELECT 1
        FROM public.organization_members my_orgs
        WHERE my_orgs.organization_id = om.organization_id
        AND my_orgs.user_id = auth.uid()
    );

-- Add a comment to the view
COMMENT ON VIEW public.organization_members_with_profiles IS 'View that joins organization members with their profiles for easier querying, with built-in security filter';

-- Note: We can't use RLS policies on views, so we've incorporated the security logic
-- directly into the view definition above.

-- Alternative approach: If you need more flexibility, you could create a function that
-- returns a table of organization members with their profiles, and apply security checks
-- within the function. For example:
/*
CREATE OR REPLACE FUNCTION public.get_organization_members_with_profiles(org_id UUID)
RETURNS TABLE (
    id UUID,
    organization_id UUID,
    user_id UUID,
    role TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    first_name TEXT,
    last_name TEXT,
    avatar_url TEXT
) AS $$
BEGIN
    -- Check if the user is a member of the organization
    IF NOT EXISTS (
        SELECT 1
        FROM public.organization_members
        WHERE organization_id = org_id
        AND user_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'User is not a member of this organization';
    END IF;

    -- Return the members with their profiles
    RETURN QUERY
    SELECT
        om.id,
        om.organization_id,
        om.user_id,
        om.role,
        om.created_at,
        om.updated_at,
        p.first_name,
        p.last_name,
        p.avatar_url
    FROM
        public.organization_members om
    LEFT JOIN
        public.profiles p ON om.user_id = p.id
    WHERE
        om.organization_id = org_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
*/
