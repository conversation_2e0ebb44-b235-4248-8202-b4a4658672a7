-- Add rejected column to invitations table
ALTER TABLE public.invitations ADD COLUMN IF NOT EXISTS rejected BOOLEAN DEFAULT FALSE;

-- Update RLS policies to include rejected check
DROP POLICY IF EXISTS "Users can view their own invitations" ON public.invitations;
CREATE POLICY "Users can view their own invitations" 
ON public.invitations 
FOR SELECT 
USING (
  email = auth.current_user()->>'email' AND 
  accepted_at IS NULL AND 
  rejected IS NULL
);

-- Add index for faster queries
CREATE INDEX IF NOT EXISTS idx_invitations_rejected ON public.invitations (rejected);
