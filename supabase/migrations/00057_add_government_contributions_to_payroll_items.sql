-- Migration to add government contribution columns to payroll_items table
-- This allows for proper tracking of SSS, PhilHealth, and Pag-IBIG contributions

-- Add columns for government contributions
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS sss_contribution NUMERIC(12, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS philhealth_contribution NUMERIC(12, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS pagibig_contribution NUMERIC(12, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS taxable_income NUMERIC(12, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS withholding_tax NUMERIC(12, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS payment_date TIMESTAMPTZ;

-- Add comments to explain the purpose of each column
COMMENT ON COLUMN public.payroll_items.sss_contribution IS 'Employee contribution to SSS';
COMMENT ON COLUMN public.payroll_items.philhealth_contribution IS 'Employee contribution to PhilHealth';
COMMENT ON COLUMN public.payroll_items.pagibig_contribution IS 'Employee contribution to Pag-IBIG';
COMMENT ON COLUMN public.payroll_items.taxable_income IS 'Taxable income after deductions';
COMMENT ON COLUMN public.payroll_items.withholding_tax IS 'Withholding tax amount';
COMMENT ON COLUMN public.payroll_items.payment_date IS 'Date when the payroll item was paid';
