-- Disable RLS for development on relevant tables

ALTER TABLE public.payables DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.payroll_periods DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.suppliers DISABLE ROW LEVEL SECURITY; -- Changed from vendors

-- Add any other tables that might be causing RLS issues during development
-- For example:
-- ALTER TABLE public.another_table DISABLE ROW LEVEL SECURITY;

-- If specific policies on public.payables (or other tables) were intended to be commented out,
-- those policy names would be needed. For now, this script focuses on disabling RLS.
-- The original comment for a policy on "enhanced_payables" is removed as the table does not exist.
