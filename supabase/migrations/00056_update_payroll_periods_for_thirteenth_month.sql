-- Migration to update payroll_periods table to include is_thirteenth_month field
-- This allows for proper tracking of 13th month pay periods

-- Add is_thirteenth_month column to payroll_periods table
ALTER TABLE public.payroll_periods ADD COLUMN IF NOT EXISTS is_thirteenth_month BOOLEAN DEFAULT false;

-- Add comment to explain the purpose of the column
COMMENT ON COLUMN public.payroll_periods.is_thirteenth_month IS 'Whether this period is for 13th month pay';
