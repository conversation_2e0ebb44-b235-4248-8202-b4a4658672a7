-- Add file attachment fields to chat_messages table
ALTER TABLE public.chat_messages
ADD COLUMN attachment_type TEXT,
ADD COLUMN attachment_url TEXT,
ADD COLUMN attachment_name TEXT,
ADD COLUMN attachment_size INTEGER;

-- Create a function to get file extension
CREATE OR REPLACE FUNCTION public.get_file_extension(filename TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN LOWER(SUBSTRING(filename FROM '\.([^\.]+)$'));
END;
$$;

-- Create a function to check if a file is an image
CREATE OR REPLACE FUNCTION public.is_image_file(filename TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  ext TEXT;
BEGIN
  ext := public.get_file_extension(filename);
  RETURN ext IN ('.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg');
END;
$$;

-- Create a policy to allow authenticated users to upload files to storage
CREATE POLICY "Allow authenticated users to upload files" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'chat_attachments' AND
  (auth.uid())::text = (storage.foldername(name))[1]
);

-- Create a policy to allow authenticated users to read files
CREATE POLICY "Allow authenticated users to read files" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'chat_attachments'
);

-- Create a policy to allow file owners to update their files
CREATE POLICY "Allow file owners to update their files" 
ON storage.objects 
FOR UPDATE 
TO authenticated 
USING (
  bucket_id = 'chat_attachments' AND
  (auth.uid())::text = (storage.foldername(name))[1]
);

-- Create a policy to allow file owners to delete their files
CREATE POLICY "Allow file owners to delete their files" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'chat_attachments' AND
  (auth.uid())::text = (storage.foldername(name))[1]
);

-- Create a storage bucket for chat attachments if it doesn't exist
-- Note: This requires superuser privileges and might need to be done manually
-- or through the Supabase dashboard
