-- Add conversion_factor column to inventory_receipt_items table
ALTER TABLE public.inventory_receipt_items
ADD COLUMN conversion_factor NUMERIC(18,8) DEFAULT 1;

-- Update existing inventory_receipt_items with conversion factors from product_uoms
UPDATE public.inventory_receipt_items iri
SET conversion_factor = pu.conversion_factor
FROM public.product_uoms pu
WHERE iri.product_id = pu.product_id
AND iri.uom_id = pu.uom_id
AND iri.conversion_factor = 1;

-- Create or replace function to calculate base quantity for inventory receipt items
CREATE OR REPLACE FUNCTION update_inventory_receipt_item_base_quantity()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.quantity IS NOT NULL AND NEW.conversion_factor IS NOT NULL THEN
    NEW.base_quantity := NEW.quantity * NEW.conversion_factor;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update base_quantity when quantity or conversion_factor changes
DROP TRIGGER IF EXISTS update_iri_base_quantity_trigger ON public.inventory_receipt_items;
CREATE TRIGGER update_iri_base_quantity_trigger
BEFORE INSERT OR UPDATE OF quantity, conversion_factor ON public.inventory_receipt_items
FOR EACH ROW
EXECUTE FUNCTION update_inventory_receipt_item_base_quantity();

-- Drop existing policies on inventory_receipt_items if they exist
DROP POLICY IF EXISTS "Allow all authenticated users to manage inventory receipt items" ON public.inventory_receipt_items;

-- Create a permissive policy for inventory_receipt_items
CREATE POLICY "Allow all authenticated users to manage inventory receipt items"
ON public.inventory_receipt_items
FOR ALL
USING (true)
WITH CHECK (true);
