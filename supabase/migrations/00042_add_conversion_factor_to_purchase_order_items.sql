-- Add conversion_factor column to purchase_order_items table
ALTER TABLE public.purchase_order_items
ADD COLUMN conversion_factor NUMERIC(18,8) DEFAULT 1;

-- Update existing purchase_order_items with conversion factors from product_uoms
UPDATE public.purchase_order_items poi
SET conversion_factor = pu.conversion_factor
FROM public.product_uoms pu
WHERE poi.product_id = pu.product_id
AND poi.uom_id = pu.uom_id
AND poi.conversion_factor = 1;

-- Create a function to update base_quantity when quantity or conversion_factor changes
CREATE OR REPLACE FUNCTION update_purchase_order_item_base_quantity()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate base_quantity only if both quantity and conversion_factor are not null
  IF NEW.quantity IS NOT NULL AND NEW.conversion_factor IS NOT NULL THEN
    NEW.base_quantity := NEW.quantity * NEW.conversion_factor;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update base_quantity
DROP TRIGGER IF EXISTS calculate_poi_base_quantity ON public.purchase_order_items;
CREATE TRIGGER update_poi_base_quantity_trigger
BEFORE INSERT OR UPDATE OF quantity, conversion_factor ON public.purchase_order_items
FOR EACH ROW
EXECUTE FUNCTION update_purchase_order_item_base_quantity();
