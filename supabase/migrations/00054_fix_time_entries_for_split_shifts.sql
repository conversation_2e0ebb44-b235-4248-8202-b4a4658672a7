-- Migration to properly handle split shifts in time entries
-- This migration:
-- 1. Drops the unique constraint on employee_id and date
-- 2. Adds a shift_group field to group related split shifts

-- First, drop the unique constraint if it exists
ALTER TABLE public.time_entries DROP CONSTRAINT IF EXISTS time_entries_employee_id_date_key;

-- Add a shift_group field to group related split shifts
ALTER TABLE public.time_entries ADD COLUMN IF NOT EXISTS shift_group TEXT;

-- Add an index on the shift_group field for faster queries
CREATE INDEX IF NOT EXISTS time_entries_shift_group_idx ON public.time_entries (shift_group);

-- Add a comment explaining the purpose
COMMENT ON TABLE public.time_entries IS 'Time entries for employees. Multiple entries per employee per day are allowed for split shifts.';
COMMENT ON COLUMN public.time_entries.shift_group IS 'Used to group related split shifts. Format: employee_id-YYYY-MM-DD';
