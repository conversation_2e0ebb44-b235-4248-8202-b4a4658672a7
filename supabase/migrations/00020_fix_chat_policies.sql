-- Fix infinite recursion in chat policies
-- This migration addresses the issue with circular references in RLS policies

-- First, drop the problematic policies
DROP POLICY IF EXISTS "Users can view participants in their conversations" ON public.chat_participants;
DROP POLICY IF EXISTS "Conversation admins can manage participants" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can view messages in their conversations" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can send messages to their conversations" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can view message status in their conversations" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can view their conversations" ON public.chat_conversations;

-- Create a function to check if a user is a participant in a conversation
CREATE OR REPLACE FUNCTION public.is_conversation_participant(conversation_uuid UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM public.chat_participants
        WHERE conversation_id = conversation_uuid
        AND user_id = user_uuid
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user is an admin of a conversation
CREATE OR REPLACE FUNCTION public.is_conversation_admin(conversation_uuid UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM public.chat_participants
        WHERE conversation_id = conversation_uuid
        AND user_id = user_uuid
        AND is_admin = TRUE
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get all conversations a user is part of
CREATE OR REPLACE FUNCTION public.get_user_conversations()
RETURNS SETOF UUID AS $$
BEGIN
    RETURN QUERY
    SELECT conversation_id
    FROM public.chat_participants
    WHERE user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the policies using the new functions to avoid circular references

-- RLS Policies for chat_participants table
CREATE POLICY "Users can view participants in their conversations"
ON public.chat_participants
FOR SELECT
USING (
    public.is_conversation_participant(conversation_id, auth.uid())
);

CREATE POLICY "Conversation admins can manage participants"
ON public.chat_participants
FOR INSERT
WITH CHECK (
    public.is_conversation_admin(conversation_id, auth.uid())
);

-- RLS Policies for chat_messages table
CREATE POLICY "Users can view messages in their conversations"
ON public.chat_messages
FOR SELECT
USING (
    public.is_conversation_participant(conversation_id, auth.uid())
);

CREATE POLICY "Users can send messages to their conversations"
ON public.chat_messages
FOR INSERT
WITH CHECK (
    public.is_conversation_participant(conversation_id, auth.uid())
    AND sender_id = auth.uid()
);

-- RLS Policies for chat_message_status table
CREATE POLICY "Users can view message status in their conversations"
ON public.chat_message_status
FOR SELECT
USING (
    message_id IN (
        SELECT id
        FROM public.chat_messages
        WHERE public.is_conversation_participant(conversation_id, auth.uid())
    )
);

-- Add a policy for users to create their own participant record
-- This is needed for the initial creation of a conversation
CREATE POLICY "Users can create their own participant record"
ON public.chat_participants
FOR INSERT
WITH CHECK (
    user_id = auth.uid()
);

-- Add a policy for users to create conversations
DROP POLICY IF EXISTS "Users can create conversations" ON public.chat_conversations;
CREATE POLICY "Users can create conversations"
ON public.chat_conversations
FOR INSERT
WITH CHECK (
    organization_id IN (SELECT public.get_user_organizations())
    AND created_by = auth.uid()
);

-- Add a policy for users to view conversations they're part of
DROP POLICY IF EXISTS "Users can view their conversations" ON public.chat_conversations;
CREATE POLICY "Users can view their conversations"
ON public.chat_conversations
FOR SELECT
USING (
    id IN (SELECT public.get_user_conversations())
);
