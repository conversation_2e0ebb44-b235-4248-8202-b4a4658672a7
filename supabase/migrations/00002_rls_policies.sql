-- Create a function to check if a user is a member of an organization
CREATE OR REPLACE FUNCTION public.is_organization_member(organization_uuid UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM public.organization_members
        WHERE organization_id = organization_uuid
        AND user_id = user_uuid
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get the current user's organizations
CREATE OR REPLACE FUNCTION public.get_user_organizations()
RETURNS SETOF UUID AS $$
BEGIN
    RETURN QUERY
    SELECT organization_id
    FROM public.organization_members
    WHERE user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user has a specific role in an organization
CREATE OR REPLACE FUNCTION public.has_organization_role(organization_uuid UUID, user_uuid UUID, required_role TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM public.organization_members
        WHERE organization_id = organization_uuid
        AND user_id = user_uuid
        AND role = required_role
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies for organizations table
CREATE POLICY "Users can view their organizations"
ON public.organizations
FOR SELECT
USING (
    id IN (SELECT public.get_user_organizations())
);

CREATE POLICY "Organization owners can update their organizations"
ON public.organizations
FOR UPDATE
USING (
    public.has_organization_role(id, auth.uid(), 'owner')
);

CREATE POLICY "Organization owners can delete their organizations"
ON public.organizations
FOR DELETE
USING (
    public.has_organization_role(id, auth.uid(), 'owner')
);

-- RLS Policies for organization_members table
CREATE POLICY "Users can view members of their organizations"
ON public.organization_members
FOR SELECT
USING (
    organization_id IN (SELECT public.get_user_organizations())
);

CREATE POLICY "Organization admins can insert new members"
ON public.organization_members
FOR INSERT
WITH CHECK (
    public.has_organization_role(organization_id, auth.uid(), 'admin') OR
    public.has_organization_role(organization_id, auth.uid(), 'owner') OR
    -- Allow users to add themselves as the first member (owner) of a new organization
    (NOT EXISTS (
        SELECT 1 FROM public.organization_members
        WHERE organization_id = organization_id
    ) AND user_id = auth.uid())
);

CREATE POLICY "Organization admins can update members"
ON public.organization_members
FOR UPDATE
USING (
    (public.has_organization_role(organization_id, auth.uid(), 'admin') OR
    public.has_organization_role(organization_id, auth.uid(), 'owner')) AND
    user_id != auth.uid() -- Cannot update own role
);

CREATE POLICY "Organization admins can delete members"
ON public.organization_members
FOR DELETE
USING (
    (public.has_organization_role(organization_id, auth.uid(), 'admin') OR
    public.has_organization_role(organization_id, auth.uid(), 'owner')) AND
    user_id != auth.uid() -- Cannot delete self
);

-- RLS Policies for profiles table
CREATE POLICY "Users can view profiles in their organizations"
ON public.profiles
FOR SELECT
USING (
    id IN (
        SELECT user_id
        FROM public.organization_members
        WHERE organization_id IN (SELECT public.get_user_organizations())
    ) OR id = auth.uid()
);

CREATE POLICY "Users can update their own profile"
ON public.profiles
FOR UPDATE
USING (
    id = auth.uid()
);

CREATE POLICY "Users can insert their own profile"
ON public.profiles
FOR INSERT
WITH CHECK (
    id = auth.uid()
);

-- Policy to allow users to create their first organization
CREATE POLICY "Users can create organizations"
ON public.organizations
FOR INSERT
WITH CHECK (
    true
);

-- Policy to allow users to create organization settings
CREATE POLICY "Users can create organization settings"
ON public.organization_settings
FOR INSERT
WITH CHECK (
    organization_id IN (SELECT public.get_user_organizations()) OR
    -- Allow creating settings for a new organization
    EXISTS (
        SELECT 1 FROM public.organizations o
        LEFT JOIN public.organization_members om ON o.id = om.organization_id
        WHERE o.id = organization_id
        AND (om.user_id = auth.uid() OR om.user_id IS NULL)
    )
);

-- RLS Policies for all organization-related tables
-- This is a generic approach that can be applied to all tables with organization_id
DO $$
DECLARE
    t text;
BEGIN
    FOR t IN
        SELECT table_name
        FROM information_schema.columns
        WHERE column_name = 'organization_id'
        AND table_schema = 'public'
        AND table_name NOT IN ('organizations', 'organization_members')
    LOOP
        -- Select policy
        EXECUTE format('
            CREATE POLICY "Users can view %I in their organizations"
            ON public.%I
            FOR SELECT
            USING (
                organization_id IN (SELECT public.get_user_organizations())
            )
        ', t, t);

        -- Insert policy
        EXECUTE format('
            CREATE POLICY "Users can insert %I in their organizations"
            ON public.%I
            FOR INSERT
            WITH CHECK (
                organization_id IN (SELECT public.get_user_organizations())
            )
        ', t, t);

        -- Update policy
        EXECUTE format('
            CREATE POLICY "Users can update %I in their organizations"
            ON public.%I
            FOR UPDATE
            USING (
                organization_id IN (SELECT public.get_user_organizations())
            )
        ', t, t);

        -- Delete policy
        EXECUTE format('
            CREATE POLICY "Users can delete %I in their organizations"
            ON public.%I
            FOR DELETE
            USING (
                organization_id IN (SELECT public.get_user_organizations())
            )
        ', t, t);
    END LOOP;
END;
$$ LANGUAGE plpgsql;
