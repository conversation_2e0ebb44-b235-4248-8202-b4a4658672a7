-- Migration to update payroll_items table to include time entry details
-- This allows for proper tracking of regular, overtime, rest day, and holiday pay

-- Add columns for time entry details
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS regular_pay NUMERIC(12, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS overtime_pay NUMERIC(12, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS rest_day_pay NUMERIC(12, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS holiday_pay NUMERIC(12, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS night_differential_pay NUMERIC(12, 2) DEFAULT 0;

-- Add columns for hour tracking
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS total_regular_hours NUMERIC(8, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS total_overtime_hours NUMERIC(8, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS total_rest_day_hours NUMERIC(8, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS total_holiday_hours NUMERIC(8, 2) DEFAULT 0;
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS total_night_diff_hours NUMERIC(8, 2) DEFAULT 0;

-- Add a column to track if this is a 13th month pay
ALTER TABLE public.payroll_items ADD COLUMN IF NOT EXISTS is_thirteenth_month BOOLEAN DEFAULT false;

-- Add comments to explain the purpose of each column
COMMENT ON COLUMN public.payroll_items.regular_pay IS 'Pay for regular working hours';
COMMENT ON COLUMN public.payroll_items.overtime_pay IS 'Pay for overtime hours (125% of regular rate)';
COMMENT ON COLUMN public.payroll_items.rest_day_pay IS 'Pay for rest day hours (130% of regular rate)';
COMMENT ON COLUMN public.payroll_items.holiday_pay IS 'Pay for holiday hours (200% for regular holidays, 130% for special holidays)';
COMMENT ON COLUMN public.payroll_items.night_differential_pay IS 'Pay for night differential hours (10% additional for hours between 10PM-6AM)';
COMMENT ON COLUMN public.payroll_items.total_regular_hours IS 'Total regular hours worked in the period';
COMMENT ON COLUMN public.payroll_items.total_overtime_hours IS 'Total overtime hours worked in the period';
COMMENT ON COLUMN public.payroll_items.total_rest_day_hours IS 'Total hours worked on rest days in the period';
COMMENT ON COLUMN public.payroll_items.total_holiday_hours IS 'Total hours worked on holidays in the period';
COMMENT ON COLUMN public.payroll_items.total_night_diff_hours IS 'Total night differential hours in the period';
COMMENT ON COLUMN public.payroll_items.is_thirteenth_month IS 'Whether this payroll item is for 13th month pay';
