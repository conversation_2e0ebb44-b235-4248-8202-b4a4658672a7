-- Create a function to get the last message for each conversation
CREATE OR REPLACE FUNCTION public.get_last_messages_for_conversations(conversation_ids UUID[])
RETURNS TABLE (
  id UUID,
  conversation_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH ranked_messages AS (
    SELECT 
      m.*,
      ROW_NUMBER() OVER (PARTITION BY m.conversation_id ORDER BY m.created_at DESC) as rn
    FROM 
      public.chat_messages m
    WHERE 
      m.conversation_id = ANY(conversation_ids)
  )
  SELECT 
    id, conversation_id, sender_id, content, created_at, updated_at
  FROM 
    ranked_messages
  WHERE 
    rn = 1;
END;
$$;

-- Create a function to get unread counts for a user across multiple conversations
CREATE OR REPLACE FUNCTION public.get_unread_counts_for_user(
  user_id UUID,
  conversation_ids UUID[]
)
<PERSON><PERSON><PERSON>NS TABLE (
  conversation_id UUID,
  unread_count BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH last_read AS (
    SELECT 
      p.conversation_id,
      m.created_at as last_read_time
    FROM 
      public.chat_participants p
    LEFT JOIN 
      public.chat_messages m ON p.last_read_message_id = m.id
    WHERE 
      p.user_id = user_id
      AND p.conversation_id = ANY(conversation_ids)
  )
  SELECT 
    lr.conversation_id,
    COUNT(m.id)::BIGINT as unread_count
  FROM 
    last_read lr
  LEFT JOIN 
    public.chat_messages m ON 
      m.conversation_id = lr.conversation_id 
      AND (
        (lr.last_read_time IS NOT NULL AND m.created_at > lr.last_read_time)
        OR 
        (lr.last_read_time IS NULL)
      )
      AND m.sender_id != user_id
  GROUP BY 
    lr.conversation_id;
END;
$$;

-- Grant execute permissions on the functions
GRANT EXECUTE ON FUNCTION public.get_last_messages_for_conversations(UUID[]) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_unread_counts_for_user(UUID, UUID[]) TO authenticated;
