-- Temporarily disable RLS policies for inventory_receipts and inventory_receipt_items tables
-- We'll implement proper access control later

-- Drop existing policies on inventory_receipt_items if they exist
DROP POLICY IF EXISTS "Allow all authenticated users to manage inventory receipt items" ON public.inventory_receipt_items;

-- Create a permissive policy for inventory_receipt_items
CREATE POLICY "Allow all authenticated users to manage inventory receipt items"
ON public.inventory_receipt_items
FOR ALL
USING (true)
WITH CHECK (true);

-- Drop existing policies on inventory_receipts if they exist
DROP POLICY IF EXISTS "Allow all authenticated users to manage inventory receipts" ON public.inventory_receipts;

-- Create a permissive policy for inventory_receipts
CREATE POLICY "Allow all authenticated users to manage inventory receipts"
ON public.inventory_receipts
FOR ALL
USING (true)
WITH CHECK (true);

-- Make sure RLS is enabled for both tables
ALTER TABLE public.inventory_receipts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory_receipt_items ENABLE ROW LEVEL SECURITY;
