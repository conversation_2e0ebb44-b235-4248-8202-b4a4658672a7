-- Create a function to get float inventory summary by product
CREATE OR REPLACE FUNCTION public.get_float_inventory_summary(p_organization_id UUID)
RETURNS TABLE (
  product_id UUID,
  product_name TEXT,
  total_float_quantity NUMERIC,
  unresolved_float_quantity NUMERIC,
  oldest_float_date TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id AS product_id,
    p.name AS product_name,
    COALESCE(SUM(fi.quantity), 0) AS total_float_quantity,
    COALESCE(SUM(CASE WHEN fi.resolved = false THEN fi.quantity ELSE 0 END), 0) AS unresolved_float_quantity,
    MIN(fi.created_at) AS oldest_float_date
  FROM 
    products p
  LEFT JOIN 
    float_inventory fi ON p.id = fi.product_id AND fi.organization_id = p_organization_id
  WHERE 
    p.organization_id = p_organization_id
  GROUP BY 
    p.id, p.name
  HAVING 
    COALESCE(SUM(CASE WHEN fi.resolved = false THEN fi.quantity ELSE 0 END), 0) > 0
  ORDER BY 
    unresolved_float_quantity DESC;
END;
$$ LANGUAGE plpgsql;
