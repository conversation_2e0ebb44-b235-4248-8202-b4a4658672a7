-- Note: Products and categories tables already exist in the initial schema
-- This migration only adds RLS policies and storage for product images

-- Drop existing policies for categories if they exist
DROP POLICY IF EXISTS "Users can view categories in their organizations" ON public.categories;
DROP POLICY IF EXISTS "Only owners, admins, and inventory managers can create categories" ON public.categories;
DROP POLICY IF EXISTS "Only owners, admins, and inventory managers can update categories" ON public.categories;
DROP POLICY IF EXISTS "Only owners and admins can delete categories" ON public.categories;

-- Create RLS policies for categories
CREATE POLICY "Users can view categories in their organizations"
ON public.categories
FOR SELECT
USING (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Only owners, admins, and inventory managers can create categories"
ON public.categories
FOR INSERT
WITH CHECK (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'inventory_manager')
    )
);

CREATE POLICY "Only owners, admins, and inventory managers can update categories"
ON public.categories
FOR UPDATE
USING (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'inventory_manager')
    )
);

CREATE POLICY "Only owners and admins can delete categories"
ON public.categories
FOR DELETE
USING (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
);

-- Drop existing policies for products if they exist
DROP POLICY IF EXISTS "Users can view products in their organizations" ON public.products;
DROP POLICY IF EXISTS "Only owners, admins, and inventory managers can create products" ON public.products;
DROP POLICY IF EXISTS "Only owners, admins, and inventory managers can update products" ON public.products;
DROP POLICY IF EXISTS "Only owners and admins can delete products" ON public.products;

-- Create RLS policies for products
CREATE POLICY "Users can view products in their organizations"
ON public.products
FOR SELECT
USING (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Only owners, admins, and inventory managers can create products"
ON public.products
FOR INSERT
WITH CHECK (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'inventory_manager')
    )
);

CREATE POLICY "Only owners, admins, and inventory managers can update products"
ON public.products
FOR UPDATE
USING (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'inventory_manager')
    )
);

CREATE POLICY "Only owners and admins can delete products"
ON public.products
FOR DELETE
USING (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
);

-- Create storage bucket for product images if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'product-images') THEN
        INSERT INTO storage.buckets (id, name, public) VALUES ('product-images', 'product-images', true);
    END IF;
END $$;

-- Drop existing storage policies if they exist
DROP POLICY IF EXISTS "Anyone can view product images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload product images" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own product images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own product images" ON storage.objects;

-- Create storage policy for product images
CREATE POLICY "Anyone can view product images"
ON storage.objects FOR SELECT
USING (bucket_id = 'product-images');

CREATE POLICY "Authenticated users can upload product images"
ON storage.objects FOR INSERT
WITH CHECK (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
);

CREATE POLICY "Users can update their own product images"
ON storage.objects FOR UPDATE
USING (
    bucket_id = 'product-images' AND
    auth.uid() = owner
);

CREATE POLICY "Users can delete their own product images"
ON storage.objects FOR DELETE
USING (
    bucket_id = 'product-images' AND
    auth.uid() = owner
);
