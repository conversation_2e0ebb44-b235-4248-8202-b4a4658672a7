-- Add onboarding completion tracking to profiles table
-- This fixes the issue where onboarding completion was only tracked in localStorage

-- Add onboarding_completed column to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT FALSE;

-- Add onboarding_completed_at timestamp
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS onboarding_completed_at TIMESTAMPTZ;

-- For existing users who have profiles and organization memberships, 
-- mark them as having completed onboarding
UPDATE public.profiles 
SET 
    onboarding_completed = TRUE,
    onboarding_completed_at = COALESCE(updated_at, created_at)
WHERE 
    id IN (
        SELECT DISTINCT user_id 
        FROM public.organization_members 
        WHERE role = 'owner'
    )
    AND onboarding_completed IS NOT TRUE;

-- Add index for faster queries
CREATE INDEX IF NOT EXISTS idx_profiles_onboarding_completed 
ON public.profiles(onboarding_completed);

-- Add comments for documentation
COMMENT ON COLUMN public.profiles.onboarding_completed IS 'Whether the user has completed the initial onboarding process';
COMMENT ON COLUMN public.profiles.onboarding_completed_at IS 'Timestamp when the user completed onboarding';

-- Create a function to mark onboarding as completed
CREATE OR REPLACE FUNCTION public.mark_onboarding_completed(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Update the profile to mark onboarding as completed
    UPDATE public.profiles 
    SET 
        onboarding_completed = TRUE,
        onboarding_completed_at = NOW(),
        updated_at = NOW()
    WHERE id = user_id;
    
    -- Return true if the update was successful
    RETURN FOUND;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.mark_onboarding_completed(UUID) TO authenticated;
