-- Update the transaction_type check constraint to include 'receipt'

-- First, drop the existing constraint
ALTER TABLE public.inventory_transactions 
DROP CONSTRAINT IF EXISTS inventory_transactions_transaction_type_check;

-- Then add it back with 'receipt' included
ALTER TABLE public.inventory_transactions
ADD CONSTRAINT inventory_transactions_transaction_type_check
CHECK (transaction_type IN ('purchase', 'sale', 'adjustment', 'transfer', 'receipt', 'return'));

-- Drop any existing RLS policies for inventory_transactions
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON public.inventory_transactions;

-- Add a temporary policy that allows all operations for authenticated users
CREATE POLICY "Enable all access for authenticated users" 
ON public.inventory_transactions
FOR ALL 
TO authenticated
USING (true)
WITH CHECK (true);
