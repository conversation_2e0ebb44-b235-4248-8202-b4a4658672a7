-- Migration: Multiple Attachments System for Payables and Payments
-- Adds support for multiple image attachments for audit trails

-- =====================================================
-- 1. ATTACHMENTS TABLE
-- =====================================================

-- Create attachments table for payables and payments
CREATE TABLE IF NOT EXISTS public.payable_attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    
    -- Polymorphic relationship (can attach to payables or payments)
    attachable_type TEXT NOT NULL CHECK (attachable_type IN ('payable', 'payment')),
    attachable_id UUID NOT NULL, -- References payables.id or payable_payments.id
    
    -- File information
    file_name TEXT NOT NULL,
    file_url TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_type TEXT NOT NULL, -- MIME type
    file_extension TEXT NOT NULL,
    
    -- Metadata
    description TEXT,
    is_primary BOOLEAN DEFAULT FALSE, -- Mark one as primary/main attachment
    
    -- Audit fields
    uploaded_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_payable_attachments_attachable 
ON public.payable_attachments(attachable_type, attachable_id);

CREATE INDEX IF NOT EXISTS idx_payable_attachments_org 
ON public.payable_attachments(organization_id);

-- Add trigger for updated_at
CREATE TRIGGER set_updated_at_payable_attachments
BEFORE UPDATE ON public.payable_attachments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 2. RLS POLICIES
-- =====================================================


ALTER TABLE public.payable_attachments DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- 3. STORAGE BUCKET
-- =====================================================

-- Create storage bucket for payable attachments
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'payable-attachments',
  'payable-attachments',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf', 'text/plain']
) ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 4. HELPER FUNCTIONS
-- =====================================================

-- Function to get attachments for a payable or payment
CREATE OR REPLACE FUNCTION get_payable_attachments(
    p_attachable_type TEXT,
    p_attachable_id UUID
) RETURNS TABLE (
    id UUID,
    file_name TEXT,
    file_url TEXT,
    file_size INTEGER,
    file_type TEXT,
    description TEXT,
    is_primary BOOLEAN,
    uploaded_by UUID,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pa.id,
        pa.file_name,
        pa.file_url,
        pa.file_size,
        pa.file_type,
        pa.description,
        pa.is_primary,
        pa.uploaded_by,
        pa.created_at
    FROM public.payable_attachments pa
    WHERE pa.attachable_type = p_attachable_type
    AND pa.attachable_id = p_attachable_id
    ORDER BY pa.is_primary DESC, pa.created_at ASC;
END;
$$ LANGUAGE plpgsql;

-- Function to set primary attachment
CREATE OR REPLACE FUNCTION set_primary_attachment(
    p_attachment_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    v_attachable_type TEXT;
    v_attachable_id UUID;
BEGIN
    -- Get the attachment details
    SELECT attachable_type, attachable_id 
    INTO v_attachable_type, v_attachable_id
    FROM public.payable_attachments
    WHERE id = p_attachment_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Clear existing primary flags for this attachable
    UPDATE public.payable_attachments
    SET is_primary = FALSE
    WHERE attachable_type = v_attachable_type
    AND attachable_id = v_attachable_id;
    
    -- Set new primary
    UPDATE public.payable_attachments
    SET is_primary = TRUE
    WHERE id = p_attachment_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. MIGRATION DATA
-- =====================================================

-- Migrate existing single attachments to new system
-- For payables with invoice_url
INSERT INTO public.payable_attachments (
    organization_id,
    attachable_type,
    attachable_id,
    file_name,
    file_url,
    file_size,
    file_type,
    file_extension,
    description,
    is_primary,
    uploaded_by
)
SELECT 
    p.organization_id,
    'payable',
    p.id,
    'invoice.pdf', -- Default name
    p.invoice_url,
    0, -- Unknown size
    'application/pdf', -- Assume PDF
    'pdf',
    'Migrated invoice attachment',
    TRUE,
    p.created_by
FROM public.payables p
WHERE p.invoice_url IS NOT NULL
AND p.invoice_url != ''
ON CONFLICT DO NOTHING;

-- For payments with attachment_url
INSERT INTO public.payable_attachments (
    organization_id,
    attachable_type,
    attachable_id,
    file_name,
    file_url,
    file_size,
    file_type,
    file_extension,
    description,
    is_primary,
    uploaded_by
)
SELECT 
    (SELECT organization_id FROM public.payables WHERE id = pp.payable_id),
    'payment',
    pp.id,
    'payment_proof.pdf', -- Default name
    pp.attachment_url,
    0, -- Unknown size
    'application/pdf', -- Assume PDF
    'pdf',
    'Migrated payment proof',
    TRUE,
    pp.created_by
FROM public.payable_payments pp
WHERE pp.attachment_url IS NOT NULL
AND pp.attachment_url != ''
ON CONFLICT DO NOTHING;
