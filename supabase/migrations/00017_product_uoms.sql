-- Create product_uoms table
CREATE TABLE public.product_uoms (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
  uom_id UUID NOT NULL REFERENCES public.units_of_measurement(id) ON DELETE RESTRICT,
  conversion_factor NUMERIC(18,8) NOT NULL,  -- multiply by this to get base UoM qty
  is_default BOOLEAN NOT NULL DEFAULT FALSE,
  is_purchasing_unit BOOLEAN NOT NULL DEFAULT FALSE,
  is_selling_unit BOOLEAN NOT NULL DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (product_id, uom_id)
);

-- Add trigger for updated_at
CREATE TRIGGER set_updated_at
BEFORE UPDATE ON public.product_uoms
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE public.product_uoms ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their organization's product UoMs"
ON public.product_uoms
FOR SELECT
USING (
  product_id IN (
    SELECT id
    FROM public.products
    WHERE organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  )
);

CREATE POLICY "Users can create product UoMs in their organization"
ON public.product_uoms
FOR INSERT
WITH CHECK (
  product_id IN (
    SELECT id
    FROM public.products
    WHERE organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  )
);

CREATE POLICY "Only owners and admins can update product UoMs"
ON public.product_uoms
FOR UPDATE
USING (
  product_id IN (
    SELECT id
    FROM public.products
    WHERE organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  )
);

CREATE POLICY "Only owners and admins can delete product UoMs"
ON public.product_uoms
FOR DELETE
USING (
  product_id IN (
    SELECT id
    FROM public.products
    WHERE organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  )
);

-- Create a function to ensure only one default UoM per product
CREATE OR REPLACE FUNCTION check_default_uom()
RETURNS TRIGGER AS $$
BEGIN
  -- If this is being set as the default
  IF NEW.is_default THEN
    -- Update any existing default for this product to not be the default
    UPDATE public.product_uoms
    SET is_default = FALSE
    WHERE product_id = NEW.product_id
    AND id != NEW.id
    AND is_default = TRUE;
  END IF;
  
  -- Ensure at least one UoM is marked as default
  IF NOT EXISTS (
    SELECT 1 FROM public.product_uoms
    WHERE product_id = NEW.product_id
    AND is_default = TRUE
  ) THEN
    -- If no default exists, make this one the default
    NEW.is_default := TRUE;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER ensure_one_default_uom
BEFORE INSERT OR UPDATE ON public.product_uoms
FOR EACH ROW
EXECUTE FUNCTION check_default_uom();

-- For each existing product, assign a default UoM (pieces)
INSERT INTO public.product_uoms (
  product_id, 
  uom_id, 
  conversion_factor, 
  is_default,
  is_purchasing_unit,
  is_selling_unit,
  created_by
)
SELECT 
  p.id, 
  u.id, 
  1, -- 1:1 conversion factor
  TRUE, -- Set as default
  TRUE, -- Set as purchasing unit
  TRUE, -- Set as selling unit
  (
    SELECT user_id 
    FROM public.organization_members 
    WHERE organization_id = p.organization_id 
    AND role = 'owner' 
    LIMIT 1
  )
FROM public.products p
JOIN public.units_of_measurement u 
  ON p.organization_id = u.organization_id 
  AND u.code = 'pcs'
WHERE NOT EXISTS (
  SELECT 1 
  FROM public.product_uoms pu 
  WHERE pu.product_id = p.id
);
