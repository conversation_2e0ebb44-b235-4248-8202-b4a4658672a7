-- Create a stored procedure to handle sale creation with items in a transaction
CREATE OR REPLACE FUNCTION public.create_sale_with_items(
  p_organization_id UUID,
  p_customer_id UUID,
  p_invoice_number TEXT,
  p_status TEXT,
  p_subtotal DECIMAL,
  p_tax_amount DECIMAL,
  p_discount_amount DECIMAL,
  p_total_amount DECIMAL,
  p_payment_method TEXT,
  p_notes TEXT,
  p_created_by UUID,
  p_items JSONB
) RETURNS JSONB AS $$
DECLARE
  v_sale_id UUID;
  v_item JSONB;
  v_product_id UUID;
  v_quantity INTEGER;
  v_base_quantity NUMERIC;
  v_uom_id UUID;
  v_product_record RECORD;
  v_new_stock INTEGER;
BEGIN
  -- Create the sale record
  INSERT INTO public.sales (
    organization_id,
    customer_id,
    invoice_number,
    sale_date,
    status,
    subtotal,
    tax_amount,
    discount_amount,
    total_amount,
    payment_method,
    notes,
    created_by
  ) VALUES (
    p_organization_id,
    p_customer_id,
    p_invoice_number,
    NOW(),
    p_status,
    p_subtotal,
    p_tax_amount,
    p_discount_amount,
    p_total_amount,
    p_payment_method,
    p_notes,
    p_created_by
  ) RETURNING id INTO v_sale_id;

  -- Process each item
  FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
  LOOP
    -- Extract values from the item JSON
    v_product_id := (v_item->>'product_id')::UUID;
    v_quantity := (v_item->>'quantity')::INTEGER;
    v_base_quantity := (v_item->>'base_quantity')::NUMERIC;
    v_uom_id := (v_item->>'uom_id')::UUID;

    -- Insert the sale item
    INSERT INTO public.sale_items (
      sale_id,
      product_id,
      quantity,
      unit_price,
      uom_id,
      base_quantity,
      tax_rate,
      tax_amount,
      discount_amount,
      total_amount,
      notes
    ) VALUES (
      v_sale_id,
      v_product_id,
      v_quantity,
      (v_item->>'unit_price')::DECIMAL,
      v_uom_id,
      v_base_quantity,
      (v_item->>'tax_rate')::DECIMAL,
      (v_item->>'tax_amount')::DECIMAL,
      (v_item->>'discount_amount')::DECIMAL,
      (v_item->>'total_amount')::DECIMAL,
      v_item->>'notes'
    );

    -- Get the current product stock
    SELECT * INTO v_product_record
    FROM public.products
    WHERE id = v_product_id
    FOR UPDATE;  -- Lock the row for update

    -- Calculate new stock level
    v_new_stock := v_product_record.stock_quantity - v_base_quantity::INTEGER;

    -- Update the product stock
    UPDATE public.products
    SET 
      stock_quantity = v_new_stock,
      updated_at = NOW()
    WHERE id = v_product_id;

    -- Create inventory transaction for this sale item
    INSERT INTO public.inventory_transactions (
      organization_id,
      product_id,
      transaction_type,
      quantity,
      uom_id,
      reference_id,
      reference_type,
      notes,
      created_by
    ) VALUES (
      p_organization_id,
      v_product_id,
      'sale',
      -v_base_quantity,  -- Negative quantity for sales
      v_uom_id,
      v_sale_id,
      'sale',
      'Sale: ' || p_invoice_number,
      p_created_by
    );
  END LOOP;

  -- Return the sale ID
  RETURN jsonb_build_object('sale_id', v_sale_id);
END;
$$ LANGUAGE plpgsql;

-- Drop any existing RLS policies for sales and sale_items
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON public.sales;
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON public.sale_items;

-- Create RLS policies for sales
CREATE POLICY "Enable all access for authenticated users"
ON public.sales
USING (organization_id IN (
  SELECT organization_id FROM public.organization_members
  WHERE user_id = auth.uid()
))
WITH CHECK (organization_id IN (
  SELECT organization_id FROM public.organization_members
  WHERE user_id = auth.uid()
));

-- Create RLS policies for sale_items
CREATE POLICY "Enable all access for authenticated users"
ON public.sale_items
USING (sale_id IN (
  SELECT id FROM public.sales
  WHERE organization_id IN (
    SELECT organization_id FROM public.organization_members
    WHERE user_id = auth.uid()
  )
))
WITH CHECK (sale_id IN (
  SELECT id FROM public.sales
  WHERE organization_id IN (
    SELECT organization_id FROM public.organization_members
    WHERE user_id = auth.uid()
  )
));

-- Enable RLS on sales and sale_items
ALTER TABLE public.sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sale_items ENABLE ROW LEVEL SECURITY;
