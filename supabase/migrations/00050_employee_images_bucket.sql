-- Migration to create the employee-images storage bucket
-- This bucket will be used to store employee profile images
-- This bucket is public for simplicity and has no RLS policies

-- Function to create the storage bucket
CREATE OR REPLACE FUNCTION create_employee_images_bucket()
RETURNS void AS $$
DECLARE
  bucket_name text := 'employee-images';
  bucket_id text;
  public_policy boolean := true; -- Make the bucket public
  file_size_limit bigint := 5242880; -- 5MB
  allowed_mime_types text[] := array['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
BEGIN
  -- Check if the bucket already exists
  SELECT id INTO bucket_id FROM storage.buckets WHERE name = bucket_name;

  IF bucket_id IS NULL THEN
    -- Create the bucket as public
    INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
    VALUES (bucket_name, bucket_name, public_policy, file_size_limit, allowed_mime_types);

    RAISE NOTICE 'Created public employee-images bucket';
  ELSE
    -- Update the bucket to be public if it exists but isn't public
    UPDATE storage.buckets
    SET public = true
    WHERE name = bucket_name AND public = false;

    RAISE NOTICE 'Bucket employee-images already exists, ensured it is public';
  END IF;

  -- Create a policy that allows any authenticated user to upload to this bucket
  -- First, drop any existing policies for this bucket
  DROP POLICY IF EXISTS "Allow all users to upload to employee-images bucket" ON storage.objects;

  -- Create a policy that allows any authenticated user to upload to this bucket
  CREATE POLICY "Allow all users to upload to employee-images bucket"
  ON storage.objects FOR INSERT
  WITH CHECK (bucket_id = 'employee-images');

  -- Create a policy that allows any authenticated user to update objects in this bucket
  DROP POLICY IF EXISTS "Allow all users to update objects in employee-images bucket" ON storage.objects;
  CREATE POLICY "Allow all users to update objects in employee-images bucket"
  ON storage.objects FOR UPDATE
  USING (bucket_id = 'employee-images');

  -- Create a policy that allows any authenticated user to delete objects in this bucket
  DROP POLICY IF EXISTS "Allow all users to delete objects in employee-images bucket" ON storage.objects;
  CREATE POLICY "Allow all users to delete objects in employee-images bucket"
  ON storage.objects FOR DELETE
  USING (bucket_id = 'employee-images');

  -- Create a policy that allows anyone to select objects in this bucket
  DROP POLICY IF EXISTS "Allow anyone to select objects in employee-images bucket" ON storage.objects;
  CREATE POLICY "Allow anyone to select objects in employee-images bucket"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'employee-images');

  RAISE NOTICE 'Created policies for employee-images bucket';
END;
$$ LANGUAGE plpgsql;

-- Execute the function to create the bucket
SELECT create_employee_images_bucket();

-- Clean up the function after use
DROP FUNCTION create_employee_images_bucket();
