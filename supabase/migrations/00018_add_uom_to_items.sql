-- Add UoM references to purchase_request_items
ALTER TABLE public.purchase_request_items
  ADD COLUMN uom_id UUID REFERENCES public.units_of_measurement(id) ON DELETE RESTRICT;

-- Add UoM references to purchase_order_items
ALTER TABLE public.purchase_order_items
  ADD COLUMN uom_id UUID REFERENCES public.units_of_measurement(id) ON DELETE RESTRICT;

-- Add UoM references to inventory_receipt_items
ALTER TABLE public.inventory_receipt_items
  ADD COLUMN uom_id UUID REFERENCES public.units_of_measurement(id) ON DELETE RESTRICT;

-- Add UoM references to sale_items
ALTER TABLE public.sale_items
  ADD COLUMN uom_id UUID REFERENCES public.units_of_measurement(id) ON DELETE RESTRICT;

-- Add UoM references to inventory_transactions
ALTER TABLE public.inventory_transactions
  ADD COLUMN uom_id UUID REFERENCES public.units_of_measurement(id) ON DELETE RESTRICT;

-- Update existing records with default UoM (pieces)
UPDATE public.purchase_request_items pri
SET uom_id = (
  SELECT u.id
  FROM public.units_of_measurement u
  JOIN public.purchase_requests pr ON pri.purchase_request_id = pr.id
  WHERE u.organization_id = pr.organization_id
  AND u.code = 'pcs'
  LIMIT 1
);

UPDATE public.purchase_order_items poi
SET uom_id = (
  SELECT u.id
  FROM public.units_of_measurement u
  JOIN public.purchase_orders po ON poi.purchase_order_id = po.id
  WHERE u.organization_id = po.organization_id
  AND u.code = 'pcs'
  LIMIT 1
);

UPDATE public.inventory_receipt_items iri
SET uom_id = (
  SELECT u.id
  FROM public.units_of_measurement u
  JOIN public.inventory_receipts ir ON iri.inventory_receipt_id = ir.id
  WHERE u.organization_id = ir.organization_id
  AND u.code = 'pcs'
  LIMIT 1
);

UPDATE public.sale_items si
SET uom_id = (
  SELECT u.id
  FROM public.units_of_measurement u
  JOIN public.sales s ON si.sale_id = s.id
  WHERE u.organization_id = s.organization_id
  AND u.code = 'pcs'
  LIMIT 1
);

UPDATE public.inventory_transactions it
SET uom_id = (
  SELECT u.id
  FROM public.units_of_measurement u
  WHERE u.organization_id = it.organization_id
  AND u.code = 'pcs'
  LIMIT 1
);

-- Make UoM required for new records
ALTER TABLE public.purchase_request_items
  ALTER COLUMN uom_id SET NOT NULL;

ALTER TABLE public.purchase_order_items
  ALTER COLUMN uom_id SET NOT NULL;

ALTER TABLE public.inventory_receipt_items
  ALTER COLUMN uom_id SET NOT NULL;

ALTER TABLE public.sale_items
  ALTER COLUMN uom_id SET NOT NULL;

ALTER TABLE public.inventory_transactions
  ALTER COLUMN uom_id SET NOT NULL;

-- Add base_quantity column to track quantities in base UoM
ALTER TABLE public.purchase_request_items
  ADD COLUMN base_quantity NUMERIC(18,8);

ALTER TABLE public.purchase_order_items
  ADD COLUMN base_quantity NUMERIC(18,8);

ALTER TABLE public.inventory_receipt_items
  ADD COLUMN base_quantity NUMERIC(18,8);

ALTER TABLE public.sale_items
  ADD COLUMN base_quantity NUMERIC(18,8);

-- Update base_quantity for existing records (initially same as quantity)
UPDATE public.purchase_request_items
SET base_quantity = quantity;

UPDATE public.purchase_order_items
SET base_quantity = quantity;

UPDATE public.inventory_receipt_items
SET base_quantity = quantity;

UPDATE public.sale_items
SET base_quantity = quantity;

-- Make base_quantity required for new records
ALTER TABLE public.purchase_request_items
  ALTER COLUMN base_quantity SET NOT NULL;

ALTER TABLE public.purchase_order_items
  ALTER COLUMN base_quantity SET NOT NULL;

ALTER TABLE public.inventory_receipt_items
  ALTER COLUMN base_quantity SET NOT NULL;

ALTER TABLE public.sale_items
  ALTER COLUMN base_quantity SET NOT NULL;
