-- Create a function to check if a user can reset another user's password
CREATE OR REPLACE FUNCTION public.can_reset_user_password(
    p_organization_id UUID,
    p_admin_id UUID,
    p_user_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_admin_role TEXT;
    v_user_role TEXT;
BEGIN
    -- Check if the admin is an owner or admin in the organization
    SELECT role INTO v_admin_role
    FROM public.organization_members
    WHERE organization_id = p_organization_id AND user_id = p_admin_id;

    -- If the admin is not an owner or admin, return an error
    IF v_admin_role IS NULL OR v_admin_role NOT IN ('owner', 'admin') THEN
        RETURN jsonb_build_object(
            'can_reset', FALSE,
            'error', 'You do not have permission to reset passwords'
        );
    END IF;

    -- Check if the user is a member of the organization
    SELECT role INTO v_user_role
    FROM public.organization_members
    WHERE organization_id = p_organization_id AND user_id = p_user_id;

    -- If the user is not a member of the organization, return an error
    IF v_user_role IS NULL THEN
        RETURN jsonb_build_object(
            'can_reset', FALSE,
            'error', 'User is not a member of this organization'
        );
    END IF;

    -- If the admin is not an owner and is trying to reset an owner's password, return an error
    IF v_admin_role != 'owner' AND v_user_role = 'owner' THEN
        RETURN jsonb_build_object(
            'can_reset', FALSE,
            'error', 'Only owners can reset an owner''s password'
        );
    END IF;

    -- If we get here, the admin can reset the user's password
    RETURN jsonb_build_object(
        'can_reset', TRUE
    );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.can_reset_user_password(UUID, UUID, UUID) TO authenticated;
