-- Migration for Facial Recognition Time Tracking System
-- This migration adds tables for facial recognition and time tracking

-- Create face_descriptors table for storing facial recognition data
CREATE TABLE public.face_descriptors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    descriptor_data JSONB NOT NULL, -- Face descriptor as numerical array
    confidence_threshold DECIMAL(3,2) DEFAULT 0.6,
    enrollment_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_updated TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    enrollment_quality_score DECIMAL(3,2),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (employee_id, organization_id)
);

-- Extend existing time_entries table with facial recognition fields
-- Add facial recognition method tracking
ALTER TABLE public.time_entries ADD COLUMN IF NOT EXISTS clock_in_method TEXT CHECK (clock_in_method IN ('facial_recognition', 'pin', 'manual', 'qr_code', 'badge'));
ALTER TABLE public.time_entries ADD COLUMN IF NOT EXISTS clock_out_method TEXT CHECK (clock_out_method IN ('facial_recognition', 'pin', 'manual', 'qr_code', 'badge'));

-- Add confidence scores for facial recognition
ALTER TABLE public.time_entries ADD COLUMN IF NOT EXISTS clock_in_confidence DECIMAL(3,2);
ALTER TABLE public.time_entries ADD COLUMN IF NOT EXISTS clock_out_confidence DECIMAL(3,2);

-- Add location tracking (optional)
ALTER TABLE public.time_entries ADD COLUMN IF NOT EXISTS clock_in_location TEXT;
ALTER TABLE public.time_entries ADD COLUMN IF NOT EXISTS clock_out_location TEXT;

-- Add approval workflow fields
ALTER TABLE public.time_entries ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE public.time_entries ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES auth.users(id);
ALTER TABLE public.time_entries ADD COLUMN IF NOT EXISTS approved_at TIMESTAMPTZ;

-- Add notes field if it doesn't exist
ALTER TABLE public.time_entries ADD COLUMN IF NOT EXISTS notes TEXT;

-- Create employee_pins table for PIN-based fallback authentication
CREATE TABLE public.employee_pins (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    pin_hash TEXT NOT NULL, -- Hashed PIN for security
    salt TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_used TIMESTAMPTZ,
    failed_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (employee_id, organization_id)
);

-- Create time_tracking_settings table for organization-specific configurations
CREATE TABLE public.time_tracking_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    facial_recognition_enabled BOOLEAN DEFAULT TRUE,
    pin_fallback_enabled BOOLEAN DEFAULT TRUE,
    manual_override_enabled BOOLEAN DEFAULT TRUE,
    confidence_threshold DECIMAL(3,2) DEFAULT 0.6,
    max_pin_attempts INTEGER DEFAULT 3,
    pin_lockout_duration INTEGER DEFAULT 300, -- seconds
    auto_clock_out_enabled BOOLEAN DEFAULT FALSE,
    auto_clock_out_time TIME DEFAULT '18:00:00',
    break_tracking_enabled BOOLEAN DEFAULT FALSE,
    overtime_calculation_enabled BOOLEAN DEFAULT TRUE,
    location_tracking_enabled BOOLEAN DEFAULT FALSE,
    require_photo_verification BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (organization_id)
);

-- Create recognition_logs table for audit and analytics
CREATE TABLE public.recognition_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    employee_id UUID REFERENCES public.employees(id) ON DELETE SET NULL,
    recognition_type TEXT CHECK (recognition_type IN ('clock_in', 'clock_out', 'enrollment', 'verification')) NOT NULL,
    method_used TEXT CHECK (method_used IN ('facial_recognition', 'pin', 'manual', 'qr_code', 'badge')) NOT NULL,
    success BOOLEAN NOT NULL,
    confidence_score DECIMAL(3,2),
    error_message TEXT,
    ip_address INET,
    user_agent TEXT,
    device_info JSONB,
    processing_time_ms INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_face_descriptors_employee ON public.face_descriptors(employee_id);
CREATE INDEX IF NOT EXISTS idx_face_descriptors_org ON public.face_descriptors(organization_id);
CREATE INDEX IF NOT EXISTS idx_face_descriptors_active ON public.face_descriptors(is_active);

-- Add new indexes for facial recognition fields on existing time_entries table
CREATE INDEX IF NOT EXISTS idx_time_entries_clock_in_method ON public.time_entries(clock_in_method);
CREATE INDEX IF NOT EXISTS idx_time_entries_clock_out_method ON public.time_entries(clock_out_method);
CREATE INDEX IF NOT EXISTS idx_time_entries_created_by ON public.time_entries(created_by);

CREATE INDEX idx_employee_pins_employee ON public.employee_pins(employee_id);
CREATE INDEX idx_employee_pins_org ON public.employee_pins(organization_id);
CREATE INDEX idx_employee_pins_active ON public.employee_pins(is_active);

CREATE INDEX idx_recognition_logs_org ON public.recognition_logs(organization_id);
CREATE INDEX idx_recognition_logs_employee ON public.recognition_logs(employee_id);
CREATE INDEX idx_recognition_logs_created ON public.recognition_logs(created_at);
CREATE INDEX idx_recognition_logs_type ON public.recognition_logs(recognition_type);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_face_descriptors_updated_at BEFORE UPDATE ON public.face_descriptors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_employee_pins_updated_at BEFORE UPDATE ON public.employee_pins FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_time_tracking_settings_updated_at BEFORE UPDATE ON public.time_tracking_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Note: time_entries table already has updated_at trigger from previous migrations

-- Note: Hour calculation logic is handled by the existing time entry service

-- Create default time tracking settings for existing organizations
INSERT INTO public.time_tracking_settings (organization_id)
SELECT id FROM public.organizations
WHERE id NOT IN (SELECT organization_id FROM public.time_tracking_settings);

-- Add RLS policies (will be implemented later when proper access control is ready)
-- For now, we'll rely on application-level security

COMMENT ON TABLE public.face_descriptors IS 'Stores facial recognition descriptors for employees';
COMMENT ON TABLE public.time_entries IS 'Tracks employee clock-in and clock-out times';
COMMENT ON TABLE public.employee_pins IS 'Stores hashed PINs for fallback authentication';
COMMENT ON TABLE public.time_tracking_settings IS 'Organization-specific time tracking configurations';
COMMENT ON TABLE public.recognition_logs IS 'Audit log for all recognition attempts';
