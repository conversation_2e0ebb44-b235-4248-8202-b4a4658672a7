-- Migration to update database types for payroll tables
-- This migration doesn't change the database schema, it just updates the TypeScript types

-- No actual schema changes needed, this is just to trigger a regeneration of the database types
COMMENT ON TABLE public.payroll_items IS 'Payroll items for employees (individual payslips)';
COMMENT ON TABLE public.payroll_periods IS 'Payroll periods for the organization';
COMMENT ON TABLE public.payroll_allowances IS 'Allowances for payroll items';
COMMENT ON TABLE public.payroll_deductions IS 'Deductions for payroll items';
