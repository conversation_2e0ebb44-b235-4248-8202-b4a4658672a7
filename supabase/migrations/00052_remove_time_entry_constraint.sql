-- Migration to remove the unique constraint on employee_id and date in time_entries table
-- This allows multiple time entries for the same employee on the same day

-- Drop the existing constraint
ALTER TABLE public.time_entries DROP CONSTRAINT IF EXISTS time_entries_employee_id_date_key;

-- Note: This change allows multiple time entries per employee per day,
-- which is useful for tracking multiple shifts or different types of work
-- performed by the same employee on the same day.
