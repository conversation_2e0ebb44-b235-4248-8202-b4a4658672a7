-- Migration to add comprehensive float inventory reporting
-- This provides detailed reporting for float inventory to improve operations

-- Add additional columns to float_inventory table for better reporting
ALTER TABLE public.float_inventory
ADD COLUMN IF NOT EXISTS resolution_type TEXT CHECK (resolution_type IN ('automatic', 'manual', NULL)),
ADD COLUMN IF NOT EXISTS resolution_transaction_id UUID REFERENCES public.inventory_transactions(id),
ADD COLUMN IF NOT EXISTS resolution_notes TEXT;

-- Create a function to get detailed float inventory report
CREATE OR REPLACE FUNCTION public.get_float_inventory_report(
  p_organization_id UUID,
  p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_product_id UUID DEFAULT NULL,
  p_resolved BOOLEAN DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  product_id UUID,
  product_name TEXT,
  product_sku TEXT,
  quantity NUMERIC,
  sale_id UUID,
  sale_number TEXT,
  sale_date TIMESTAMP WITH TIME ZONE,
  customer_name TEXT,
  resolved BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolution_type TEXT,
  resolution_notes TEXT,
  days_to_resolve INTEGER,
  days_unresolved INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    fi.id,
    fi.product_id,
    p.name AS product_name,
    p.sku AS product_sku,
    fi.quantity,
    fi.sale_id,
    s.invoice_number AS sale_number,
    s.sale_date,
    c.name AS customer_name,
    fi.resolved,
    fi.created_at,
    fi.resolved_at,
    fi.resolution_type,
    COALESCE(fi.resolution_notes, fi.notes) AS resolution_notes,
    -- Days to resolve (for resolved items)
    CASE WHEN fi.resolved THEN
      EXTRACT(DAY FROM (fi.resolved_at - fi.created_at))::INTEGER
    ELSE
      NULL
    END AS days_to_resolve,
    -- Days unresolved (for unresolved items)
    CASE WHEN NOT fi.resolved THEN
      EXTRACT(DAY FROM (NOW() - fi.created_at))::INTEGER
    ELSE
      NULL
    END AS days_unresolved
  FROM
    float_inventory fi
  JOIN
    products p ON fi.product_id = p.id
  JOIN
    sales s ON fi.sale_id = s.id
  LEFT JOIN
    customers c ON s.customer_id = c.id
  WHERE
    fi.organization_id = p_organization_id
    AND (p_start_date IS NULL OR fi.created_at >= p_start_date)
    AND (p_end_date IS NULL OR fi.created_at <= p_end_date)
    AND (p_product_id IS NULL OR fi.product_id = p_product_id)
    AND (p_resolved IS NULL OR fi.resolved = p_resolved)
  ORDER BY
    fi.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get float inventory summary by product
CREATE OR REPLACE FUNCTION public.get_float_inventory_summary_by_product(
  p_organization_id UUID,
  p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE (
  product_id UUID,
  product_name TEXT,
  product_sku TEXT,
  total_float_quantity NUMERIC,
  unresolved_float_quantity NUMERIC,
  resolved_float_quantity NUMERIC,
  resolution_rate NUMERIC,
  avg_days_to_resolve NUMERIC,
  oldest_unresolved_date TIMESTAMP WITH TIME ZONE,
  oldest_unresolved_days INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.sku AS product_sku,
    COALESCE(SUM(fi.quantity), 0) AS total_float_quantity,
    COALESCE(SUM(CASE WHEN fi.resolved = false THEN fi.quantity ELSE 0 END), 0) AS unresolved_float_quantity,
    COALESCE(SUM(CASE WHEN fi.resolved = true THEN fi.quantity ELSE 0 END), 0) AS resolved_float_quantity,
    -- Resolution rate (percentage of resolved quantity)
    CASE
      WHEN COALESCE(SUM(fi.quantity), 0) > 0 THEN
        ROUND((COALESCE(SUM(CASE WHEN fi.resolved = true THEN fi.quantity ELSE 0 END), 0) /
        COALESCE(SUM(fi.quantity), 0)) * 100, 2)
      ELSE 0
    END AS resolution_rate,
    -- Average days to resolve
    ROUND(AVG(CASE WHEN fi.resolved THEN
      EXTRACT(DAY FROM (fi.resolved_at - fi.created_at))
    ELSE NULL END), 1) AS avg_days_to_resolve,
    -- Oldest unresolved date
    MIN(CASE WHEN fi.resolved = false THEN fi.created_at ELSE NULL END) AS oldest_unresolved_date,
    -- Days since oldest unresolved
    EXTRACT(DAY FROM (NOW() - MIN(CASE WHEN fi.resolved = false THEN fi.created_at ELSE NULL END)))::INTEGER AS oldest_unresolved_days
  FROM
    products p
  LEFT JOIN
    float_inventory fi ON p.id = fi.product_id
      AND fi.organization_id = p_organization_id
      AND (p_start_date IS NULL OR fi.created_at >= p_start_date)
      AND (p_end_date IS NULL OR fi.created_at <= p_end_date)
  WHERE
    p.organization_id = p_organization_id
  GROUP BY
    p.id, p.name, p.sku
  ORDER BY
    unresolved_float_quantity DESC, total_float_quantity DESC;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get float inventory summary by date
CREATE OR REPLACE FUNCTION public.get_float_inventory_summary_by_date(
  p_organization_id UUID,
  p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE (
  date_group DATE,
  total_float_quantity NUMERIC,
  resolved_float_quantity NUMERIC,
  unresolved_float_quantity NUMERIC,
  resolution_rate NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    DATE(fi.created_at) AS date_group,
    COALESCE(SUM(fi.quantity), 0) AS total_float_quantity,
    COALESCE(SUM(CASE WHEN fi.resolved = true THEN fi.quantity ELSE 0 END), 0) AS resolved_float_quantity,
    COALESCE(SUM(CASE WHEN fi.resolved = false THEN fi.quantity ELSE 0 END), 0) AS unresolved_float_quantity,
    -- Resolution rate (percentage of resolved quantity)
    CASE
      WHEN COALESCE(SUM(fi.quantity), 0) > 0 THEN
        ROUND((COALESCE(SUM(CASE WHEN fi.resolved = true THEN fi.quantity ELSE 0 END), 0) /
        COALESCE(SUM(fi.quantity), 0)) * 100, 2)
      ELSE 0
    END AS resolution_rate
  FROM
    float_inventory fi
  WHERE
    fi.organization_id = p_organization_id
    AND (p_start_date IS NULL OR fi.created_at >= p_start_date)
    AND (p_end_date IS NULL OR fi.created_at <= p_end_date)
  GROUP BY
    DATE(fi.created_at)
  ORDER BY
    date_group DESC;
END;
$$ LANGUAGE plpgsql;
