-- Add conversion_factor and base_price columns to supplier_products table
ALTER TABLE public.supplier_products
ADD COLUMN conversion_factor NUMERIC(18,8) DEFAULT 1,
ADD COLUMN base_price NUMERIC(10,2);

-- Update existing supplier_products with conversion factors from product_uoms
UPDATE public.supplier_products sp
SET conversion_factor = pu.conversion_factor
FROM public.product_uoms pu
WHERE sp.product_id = pu.product_id
AND sp.uom_id = pu.uom_id
AND sp.conversion_factor = 1;

-- Calculate and update base_price for existing records
UPDATE public.supplier_products
SET base_price = unit_price * conversion_factor
WHERE unit_price IS NOT NULL AND conversion_factor IS NOT NULL;

-- Create a function to update base_price when unit_price or conversion_factor changes
CREATE OR REPLACE FUNCTION update_supplier_product_base_price()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate base_price only if both unit_price and conversion_factor are not null
  IF NEW.unit_price IS NOT NULL AND NEW.conversion_factor IS NOT NULL THEN
    NEW.base_price := NEW.unit_price * NEW.conversion_factor;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update base_price
CREATE TRIGGER update_supplier_product_base_price_trigger
BEFORE INSERT OR UPDATE ON public.supplier_products
FOR EACH ROW
EXECUTE FUNCTION update_supplier_product_base_price();

-- Update the set_default_supplier_product_uom function to also set conversion_factor and organization_id
CREATE OR REPLACE FUNCTION set_default_supplier_product_uom()
RETURNS TRIGGER AS $$
BEGIN
  -- If organization_id is not provided, get it from the supplier
  IF NEW.organization_id IS NULL THEN
    SELECT s.organization_id INTO NEW.organization_id
    FROM public.suppliers s
    WHERE s.id = NEW.supplier_id
    LIMIT 1;

    -- If we couldn't get the organization_id, log an error
    IF NEW.organization_id IS NULL THEN
      RAISE EXCEPTION 'Could not determine organization_id for supplier_id %', NEW.supplier_id;
    END IF;
  END IF;

  -- If UoM is not provided, try to find a purchasing UoM
  IF NEW.uom_id IS NULL THEN
    -- First try to find a purchasing UoM
    SELECT pu.uom_id INTO NEW.uom_id
    FROM public.product_uoms pu
    WHERE pu.product_id = NEW.product_id
    AND pu.is_purchasing_unit = true
    LIMIT 1;

    -- If no purchasing UoM found, use the default UoM
    IF NEW.uom_id IS NULL THEN
      SELECT pu.uom_id INTO NEW.uom_id
      FROM public.product_uoms pu
      WHERE pu.product_id = NEW.product_id
      AND pu.is_default = true
      LIMIT 1;
    END IF;

    -- If still no UoM found, use the 'pieces' UoM from the organization
    IF NEW.uom_id IS NULL THEN
      SELECT u.id INTO NEW.uom_id
      FROM public.units_of_measurement u
      WHERE u.organization_id = NEW.organization_id
      AND u.code = 'pcs'
      LIMIT 1;
    END IF;
  END IF;

  -- Set conversion_factor from product_uoms if not provided
  IF NEW.conversion_factor IS NULL OR NEW.conversion_factor = 1 THEN
    SELECT pu.conversion_factor INTO NEW.conversion_factor
    FROM public.product_uoms pu
    WHERE pu.product_id = NEW.product_id
    AND pu.uom_id = NEW.uom_id
    LIMIT 1;

    -- If no conversion factor found, default to 1
    IF NEW.conversion_factor IS NULL THEN
      NEW.conversion_factor := 1;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
