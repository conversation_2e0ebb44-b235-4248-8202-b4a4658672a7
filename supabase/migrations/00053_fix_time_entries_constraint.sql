-- Migration to ensure the unique constraint on employee_id and date in time_entries table is removed
-- This allows multiple time entries for the same employee on the same day (split shifts)

-- First, check if the constraint exists and drop it
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'time_entries_employee_id_date_key' 
    AND conrelid = 'time_entries'::regclass
  ) THEN
    ALTER TABLE public.time_entries DROP CONSTRAINT time_entries_employee_id_date_key;
  END IF;
END
$$;

-- Add a comment explaining the purpose
COMMENT ON TABLE public.time_entries IS 'Time entries for employees. Multiple entries per employee per day are allowed for split shifts.';
