-- Migration: Fix Enhanced Payables System Function Error
-- Fixes the syntax error with reserved keyword 'current_date'

-- =====================================================
-- FIX BUSINESS LOGIC FUNCTIONS
-- =====================================================

-- Drop the problematic function first
DROP FUNCTION IF EXISTS calculate_next_due_date(DATE, TEXT);

-- Recreate with correct parameter name (avoid reserved keyword)
CREATE OR REPLACE FUNCTION calculate_next_due_date(
    input_date DATE,
    frequency TEXT
) RETURNS DATE AS $$
BEGIN
    CASE frequency
        WHEN 'weekly' THEN RETURN input_date + INTERVAL '1 week';
        WHEN 'bi_weekly' THEN RETURN input_date + INTERVAL '2 weeks';
        WHEN 'monthly' THEN RETURN input_date + INTERVAL '1 month';
        WHEN 'quarterly' THEN RETURN input_date + INTERVAL '3 months';
        WHEN 'semi_annual' THEN RETURN input_date + INTERVAL '6 months';
        WHEN 'annual' THEN RETURN input_date + INTERVAL '1 year';
        ELSE RETURN input_date + INTERVAL '1 month'; -- Default to monthly
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Update the trigger function to use the corrected function
CREATE OR REPLACE FUNCTION update_recurring_expense_next_due()
RETURNS TRIGGER AS $$
BEGIN
    -- Update next due date when a payable is created from recurring expense
    IF NEW.recurring_expense_id IS NOT NULL THEN
        UPDATE public.recurring_expenses
        SET next_due_date = calculate_next_due_date(NEW.due_date, frequency),
            updated_at = NOW()
        WHERE id = NEW.recurring_expense_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger
DROP TRIGGER IF EXISTS trigger_update_recurring_expense_next_due ON public.payables;
CREATE TRIGGER trigger_update_recurring_expense_next_due
    AFTER INSERT ON public.payables
    FOR EACH ROW
    WHEN (NEW.recurring_expense_id IS NOT NULL)
    EXECUTE FUNCTION update_recurring_expense_next_due();

-- Update documentation
COMMENT ON FUNCTION calculate_next_due_date(DATE, TEXT) IS 'Calculates next due date based on frequency (fixed parameter name)';
