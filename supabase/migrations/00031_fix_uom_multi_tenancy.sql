-- Add organization_id to product_uoms table for proper multi-tenancy
ALTER TABLE public.product_uoms
ADD COLUMN organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE;

-- Update existing product_uoms with the correct organization_id from their products
UPDATE public.product_uoms pu
SET organization_id = p.organization_id
FROM public.products p
WHERE pu.product_id = p.id;

-- Make organization_id required
ALTER TABLE public.product_uoms
ALTER COLUMN organization_id SET NOT NULL;

-- Add unique constraint to prevent duplicate UoMs for a product
ALTER TABLE public.product_uoms
ADD CONSTRAINT product_uoms_product_id_uom_id_unique UNIQUE (product_id, uom_id);

-- Update RLS policies for product_uoms to filter by organization_id
DROP POLICY IF EXISTS "Users can view their organization's product UoMs" ON public.product_uoms;
CREATE POLICY "Users can view their organization's product UoMs"
ON public.product_uoms
FOR SELECT
USING (
  organization_id IN (
    SELECT organization_id
    FROM public.organization_members
    WHERE user_id = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can create product UoMs in their organization" ON public.product_uoms;
CREATE POLICY "Users can create product UoMs in their organization"
ON public.product_uoms
FOR INSERT
WITH CHECK (
  organization_id IN (
    SELECT organization_id
    FROM public.organization_members
    WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
  )
);

DROP POLICY IF EXISTS "Only owners and admins can update product UoMs" ON public.product_uoms;
CREATE POLICY "Only owners and admins can update product UoMs"
ON public.product_uoms
FOR UPDATE
USING (
  organization_id IN (
    SELECT organization_id
    FROM public.organization_members
    WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
  )
);

DROP POLICY IF EXISTS "Only owners and admins can delete product UoMs" ON public.product_uoms;
CREATE POLICY "Only owners and admins can delete product UoMs"
ON public.product_uoms
FOR DELETE
USING (
  organization_id IN (
    SELECT organization_id
    FROM public.organization_members
    WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
  )
);

-- Create a function to ensure UoMs belong to the same organization as the product
CREATE OR REPLACE FUNCTION check_uom_organization()
RETURNS TRIGGER AS $$
DECLARE
  product_org_id UUID;
  uom_org_id UUID;
BEGIN
  -- Get the organization_id of the product
  SELECT organization_id INTO product_org_id
  FROM public.products
  WHERE id = NEW.product_id;
  
  -- Get the organization_id of the UoM
  SELECT organization_id INTO uom_org_id
  FROM public.units_of_measurement
  WHERE id = NEW.uom_id;
  
  -- Check if they match
  IF product_org_id != uom_org_id THEN
    RAISE EXCEPTION 'The UoM must belong to the same organization as the product';
  END IF;
  
  -- Set the organization_id to match the product's organization
  NEW.organization_id := product_org_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER ensure_matching_organization
BEFORE INSERT OR UPDATE ON public.product_uoms
FOR EACH ROW
EXECUTE FUNCTION check_uom_organization();

-- Add index for better performance
CREATE INDEX idx_product_uoms_organization_id ON public.product_uoms(organization_id);
CREATE INDEX idx_product_uoms_product_id ON public.product_uoms(product_id);
CREATE INDEX idx_product_uoms_uom_id ON public.product_uoms(uom_id);

-- Add index for units_of_measurement
CREATE INDEX idx_units_of_measurement_organization_id ON public.units_of_measurement(organization_id);
CREATE INDEX idx_units_of_measurement_code ON public.units_of_measurement(code);
