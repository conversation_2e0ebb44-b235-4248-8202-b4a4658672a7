-- Migration: Payroll to Payables Integration
-- Adds tracking and mapping between payroll and payables systems

-- =====================================================
-- 1. PAYROLL PERIOD PAYABLES TRACKING
-- =====================================================

-- Add payables tracking to payroll_periods
ALTER TABLE public.payroll_periods 
ADD COLUMN IF NOT EXISTS payables_created BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS payables_created_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS payables_created_by UUID REFERENCES auth.users(id);

-- Add comments
COMMENT ON COLUMN public.payroll_periods.payables_created IS 'Flag indicating if payables have been created from this payroll period';
COMMENT ON COLUMN public.payroll_periods.payables_created_at IS 'Timestamp when payables were created';
COMMENT ON COLUMN public.payroll_periods.payables_created_by IS 'User who created the payables';

-- =====================================================
-- 2. PAYROLL-PAYABLES MAPPING TABLE
-- =====================================================

-- Track which payables were created from which payroll items
CREATE TABLE IF NOT EXISTS public.payroll_payable_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    payroll_period_id UUID NOT NULL REFERENCES public.payroll_periods(id) ON DELETE CASCADE,
    payroll_item_id UUID REFERENCES public.payroll_items(id) ON DELETE CASCADE, -- NULL for aggregated payables
    payable_id UUID NOT NULL REFERENCES public.payables(id) ON DELETE CASCADE,
    payable_type TEXT NOT NULL CHECK (payable_type IN (
        'employee_salary', 'sss_contribution', 'philhealth_contribution',
        'pagibig_contribution', 'withholding_tax', 'loan_deduction', 'other'
    )),
    amount NUMERIC(12,2) NOT NULL CHECK (amount >= 0),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES auth.users(id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_payroll_payable_mappings_org_id ON public.payroll_payable_mappings(organization_id);
CREATE INDEX IF NOT EXISTS idx_payroll_payable_mappings_period_id ON public.payroll_payable_mappings(payroll_period_id);
CREATE INDEX IF NOT EXISTS idx_payroll_payable_mappings_payable_id ON public.payroll_payable_mappings(payable_id);
CREATE INDEX IF NOT EXISTS idx_payroll_payable_mappings_type ON public.payroll_payable_mappings(payable_type);

-- Add comments
COMMENT ON TABLE public.payroll_payable_mappings IS 'Mapping table tracking which payables were created from payroll periods and items';
COMMENT ON COLUMN public.payroll_payable_mappings.payroll_item_id IS 'NULL for aggregated payables (e.g., government remittances)';
COMMENT ON COLUMN public.payroll_payable_mappings.payable_type IS 'Type of payable: employee_salary, government contributions, deductions';

-- =====================================================
-- 3. GOVERNMENT AGENCIES AS SUPPLIERS
-- =====================================================

-- Function to add government agencies as suppliers for an organization
CREATE OR REPLACE FUNCTION add_government_agencies_as_suppliers(p_organization_id UUID)
RETURNS VOID AS $$
BEGIN
    -- Insert government agencies as suppliers if they don't exist
    INSERT INTO public.suppliers (
        organization_id, name, supplier_type, payment_terms_days, 
        contact_person, email, phone, address, notes, created_at
    )
    SELECT 
        p_organization_id,
        agency.name,
        'government',
        agency.payment_terms,
        agency.contact_person,
        agency.email,
        agency.phone,
        agency.address,
        agency.notes,
        NOW()
    FROM (
        VALUES 
            ('Social Security System (SSS)', 30, 'SSS Collection Department', '<EMAIL>', '(02) 8920-6401', 'East Avenue, Diliman, Quezon City', 'Monthly remittance due by 30th of following month'),
            ('Philippine Health Insurance Corporation (PhilHealth)', 30, 'PhilHealth Collection', '<EMAIL>', '(02) 8441-7442', 'Citystate Centre, Shaw Boulevard, Pasig City', 'Monthly remittance due by 30th of following month'),
            ('Home Development Mutual Fund (Pag-IBIG)', 30, 'Pag-IBIG Collection', '<EMAIL>', '(02) 8724-4244', 'The Pag-IBIG Fund Building, North Triangle, Quezon City', 'Monthly remittance due by 30th of following month'),
            ('Bureau of Internal Revenue (BIR)', 15, 'BIR Collection', '<EMAIL>', '(02) 8538-3200', 'BIR National Office Building, Diliman, Quezon City', 'Withholding tax remittance due by 15th of following month')
    ) AS agency(name, payment_terms, contact_person, email, phone, address, notes)
    WHERE NOT EXISTS (
        SELECT 1 FROM public.suppliers s 
        WHERE s.organization_id = p_organization_id 
        AND s.name = agency.name
        AND s.supplier_type = 'government'
    );
END;
$$ LANGUAGE plpgsql;

-- Add supplier_type column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'suppliers' AND column_name = 'supplier_type'
    ) THEN
        ALTER TABLE public.suppliers
        ADD COLUMN supplier_type TEXT DEFAULT 'vendor' CHECK (supplier_type IN ('vendor', 'government', 'contractor', 'service_provider'));
        
        COMMENT ON COLUMN public.suppliers.supplier_type IS 'Type of supplier: vendor, government, contractor, service_provider';
    END IF;
END $$;

-- =====================================================
-- 4. PAYROLL PAYABLES SUMMARY FUNCTION
-- =====================================================

-- Function to get payroll payables summary for a period
CREATE OR REPLACE FUNCTION get_payroll_payables_summary(
    p_organization_id UUID,
    p_payroll_period_id UUID
)
RETURNS TABLE(
    payable_type TEXT,
    count INTEGER,
    total_amount NUMERIC(12,2),
    due_date DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ppm.payable_type,
        COUNT(*)::INTEGER,
        SUM(ppm.amount),
        MIN(p.due_date)
    FROM public.payroll_payable_mappings ppm
    JOIN public.payables p ON p.id = ppm.payable_id
    WHERE ppm.organization_id = p_organization_id
    AND ppm.payroll_period_id = p_payroll_period_id
    GROUP BY ppm.payable_type
    ORDER BY ppm.payable_type;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. SECURITY & PERMISSIONS
-- =====================================================

-- Disable RLS for now (as requested)
ALTER TABLE public.payroll_payable_mappings DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- 6. DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION add_government_agencies_as_suppliers(UUID) IS 'Adds standard Philippine government agencies as suppliers for payroll remittances';
COMMENT ON FUNCTION get_payroll_payables_summary(UUID, UUID) IS 'Returns summary of payables created from a payroll period grouped by type';
