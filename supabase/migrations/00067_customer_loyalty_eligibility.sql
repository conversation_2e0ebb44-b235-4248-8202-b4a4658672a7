-- Add loyalty_eligible field to customers table
ALTER TABLE public.customers ADD COLUMN IF NOT EXISTS loyalty_eligible BOOLEAN DEFAULT false;

-- Create function to check if customer is eligible for loyalty points
CREATE OR REPLACE FUNCTION is_customer_loyalty_eligible(
  p_customer_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
  v_is_eligible BOOLEAN;
BEGIN
  -- Check if customer exists and is eligible
  SELECT loyalty_eligible INTO v_is_eligible
  FROM customers
  WHERE id = p_customer_id;
  
  -- Return false if customer not found or not eligible
  RETURN COALESCE(v_is_eligible, false);
END;
$$ LANGUAGE plpgsql;

-- Update the calculate_loyalty_points function to check eligibility
CREATE OR REPLACE FUNCTION calculate_loyalty_points(
    p_organization_id UUID,
    p_customer_id UUID,
    p_sale_id UUID,
    p_total_amount DECIMAL
) RETURNS INTEGER AS $$
DECLARE
    v_points INTEGER := 0;
    v_settings RECORD;
    v_customer_tier RECORD;
    v_multiplier DECIMAL(3, 2) := 1.0;
    v_is_eligible BOOLEAN;
BEGIN
    -- Check if loyalty program is enabled for this organization
    SELECT * INTO v_settings FROM loyalty_program_settings 
    WHERE organization_id = p_organization_id AND is_enabled = true;
    
    -- If no settings or program is disabled, return 0 points
    IF NOT FOUND THEN
        RETURN 0;
    END IF;
    
    -- Check if customer is eligible for loyalty points
    SELECT is_customer_loyalty_eligible(p_customer_id) INTO v_is_eligible;
    
    -- If customer is not eligible, return 0 points
    IF NOT v_is_eligible THEN
        RETURN 0;
    END IF;
    
    -- Get customer tier multiplier if exists
    SELECT t.points_multiplier INTO v_multiplier
    FROM customer_loyalty_profiles p
    JOIN loyalty_tiers t ON p.tier_id = t.id
    WHERE p.customer_id = p_customer_id AND p.organization_id = p_organization_id;
    
    -- If no tier found, use default multiplier of 1.0
    IF v_multiplier IS NULL THEN
        v_multiplier := 1.0;
    END IF;
    
    -- Calculate points based on total amount and earning rate
    v_points := FLOOR(p_total_amount * v_settings.points_earning_rate * v_multiplier);
    
    RETURN v_points;
END;
$$ LANGUAGE plpgsql;

-- Update the create_sale_with_items procedure to check eligibility
CREATE OR REPLACE FUNCTION public.create_sale_with_items(
  p_organization_id UUID,
  p_customer_id UUID,
  p_invoice_number TEXT,
  p_status TEXT,
  p_subtotal DECIMAL,
  p_tax_amount DECIMAL,
  p_discount_amount DECIMAL,
  p_total_amount DECIMAL,
  p_payment_method TEXT,
  p_notes TEXT,
  p_created_by UUID,
  p_items JSONB,
  p_loyalty_points_used INTEGER DEFAULT 0,
  p_loyalty_points_discount DECIMAL DEFAULT 0
) RETURNS JSONB AS $$
DECLARE
  v_sale_id UUID;
  v_item JSONB;
  v_product_id UUID;
  v_quantity INTEGER;
  v_base_quantity NUMERIC;
  v_uom_id UUID;
  v_loyalty_points_earned INTEGER := 0;
  v_customer_profile_id UUID;
  v_is_eligible BOOLEAN;
BEGIN
  -- Calculate loyalty points earned if customer is provided and points are not being redeemed
  IF p_customer_id IS NOT NULL AND p_loyalty_points_used = 0 THEN
    -- Check if customer is eligible for loyalty points
    SELECT is_customer_loyalty_eligible(p_customer_id) INTO v_is_eligible;
    
    -- Only calculate points if customer is eligible
    IF v_is_eligible THEN
      -- Calculate points based on the total amount
      v_loyalty_points_earned := calculate_loyalty_points(
        p_organization_id, 
        p_customer_id, 
        NULL, -- Sale ID will be set later
        p_total_amount
      );
    END IF;
  END IF;

  -- Create the sale record
  INSERT INTO public.sales (
    organization_id,
    customer_id,
    invoice_number,
    sale_date,
    status,
    subtotal,
    tax_amount,
    discount_amount,
    total_amount,
    payment_method,
    notes,
    created_by,
    loyalty_points_used,
    loyalty_points_earned,
    loyalty_points_discount
  ) VALUES (
    p_organization_id,
    p_customer_id,
    p_invoice_number,
    NOW(),
    p_status,
    p_subtotal,
    p_tax_amount,
    p_discount_amount,
    p_total_amount,
    p_payment_method,
    p_notes,
    p_created_by,
    p_loyalty_points_used,
    v_loyalty_points_earned,
    p_loyalty_points_discount
  ) RETURNING id INTO v_sale_id;

  -- Process each item in the sale
  FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
  LOOP
    -- Extract values from the item JSON
    v_product_id := (v_item->>'product_id')::UUID;
    v_quantity := (v_item->>'quantity')::INTEGER;
    v_base_quantity := COALESCE((v_item->>'base_quantity')::NUMERIC, v_quantity);
    v_uom_id := (v_item->>'uom_id')::UUID;

    -- Create the sale item
    INSERT INTO public.sale_items (
      sale_id,
      product_id,
      quantity,
      unit_price,
      uom_id,
      base_quantity,
      tax_rate,
      tax_amount,
      discount_amount,
      total_amount,
      notes
    ) VALUES (
      v_sale_id,
      v_product_id,
      v_quantity,
      (v_item->>'unit_price')::DECIMAL,
      v_uom_id,
      v_base_quantity,
      COALESCE((v_item->>'tax_rate')::DECIMAL, 0),
      COALESCE((v_item->>'tax_amount')::DECIMAL, 0),
      COALESCE((v_item->>'discount_amount')::DECIMAL, 0),
      (v_item->>'unit_price')::DECIMAL * v_quantity - COALESCE((v_item->>'discount_amount')::DECIMAL, 0),
      v_item->>'notes'
    );

    -- Create inventory transaction for this sale item
    -- This will trigger the inventory update through the trigger
    INSERT INTO public.inventory_transactions (
      organization_id,
      product_id,
      transaction_type,
      quantity,
      uom_id,
      reference_id,
      reference_type,
      notes,
      created_by
    ) VALUES (
      p_organization_id,
      v_product_id,
      'sale',
      -v_base_quantity,  -- Negative quantity for sales
      v_uom_id,
      v_sale_id,
      'sale',
      'Sale: ' || p_invoice_number,
      p_created_by
    );
  END LOOP;

  -- Handle loyalty points if customer exists and is eligible
  IF p_customer_id IS NOT NULL THEN
    -- Check if customer is eligible for loyalty points
    SELECT is_customer_loyalty_eligible(p_customer_id) INTO v_is_eligible;
    
    IF v_is_eligible THEN
      -- Get or create customer loyalty profile
      SELECT id INTO v_customer_profile_id FROM customer_loyalty_profiles
      WHERE customer_id = p_customer_id AND organization_id = p_organization_id;

      IF v_customer_profile_id IS NULL THEN
        -- Create new loyalty profile for customer
        INSERT INTO customer_loyalty_profiles (
          customer_id,
          organization_id,
          current_points_balance,
          lifetime_points_earned,
          lifetime_points_redeemed
        ) VALUES (
          p_customer_id,
          p_organization_id,
          v_loyalty_points_earned, -- Initial balance is just the points earned
          v_loyalty_points_earned, -- Initial lifetime points
          0                       -- No points redeemed yet
        ) RETURNING id INTO v_customer_profile_id;
      ELSE
        -- Update existing loyalty profile
        UPDATE customer_loyalty_profiles
        SET 
          current_points_balance = current_points_balance + v_loyalty_points_earned - p_loyalty_points_used,
          lifetime_points_earned = lifetime_points_earned + v_loyalty_points_earned,
          lifetime_points_redeemed = lifetime_points_redeemed + p_loyalty_points_used,
          updated_at = NOW()
        WHERE id = v_customer_profile_id;
      END IF;

      -- Record loyalty points earned transaction if points were earned
      IF v_loyalty_points_earned > 0 THEN
        INSERT INTO loyalty_transactions (
          customer_id,
          organization_id,
          transaction_type,
          points,
          sale_id,
          notes,
          created_by
        ) VALUES (
          p_customer_id,
          p_organization_id,
          'earn',
          v_loyalty_points_earned,
          v_sale_id,
          'Points earned from sale ' || p_invoice_number,
          p_created_by
        );
      END IF;

      -- Record loyalty points redeemed transaction if points were used
      IF p_loyalty_points_used > 0 THEN
        INSERT INTO loyalty_transactions (
          customer_id,
          organization_id,
          transaction_type,
          points,
          sale_id,
          notes,
          created_by
        ) VALUES (
          p_customer_id,
          p_organization_id,
          'redeem',
          -p_loyalty_points_used, -- Negative points for redemption
          v_sale_id,
          'Points redeemed for discount on sale ' || p_invoice_number,
          p_created_by
        );
      END IF;
    END IF;
  END IF;

  -- Return the sale ID
  RETURN jsonb_build_object('sale_id', v_sale_id);
END;
$$ LANGUAGE plpgsql;
