-- Fix refund table foreign key constraints
-- This migration fixes the existing refund tables to use auth.users instead of employees

-- Drop existing refund tables if they exist (since they have wrong constraints)
DROP TABLE IF EXISTS public.store_credit_transactions CASCADE;
DROP TABLE IF EXISTS public.store_credits CASCADE;
DROP TABLE IF EXISTS public.refund_items CASCADE;
DROP TABLE IF EXISTS public.refunds CASCADE;

-- Recreate refunds table with correct constraints
CREATE TABLE public.refunds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    refund_number TEXT NOT NULL,
    original_sale_id UUID NOT NULL REFERENCES public.sales(id) ON DELETE RESTRICT,
    customer_id UUID REFERENCES public.customers(id) ON DELETE SET NULL,

    -- Refund details
    refund_type TEXT NOT NULL CHECK (refund_type IN ('full', 'partial', 'exchange', 'store_credit')),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'processed', 'cancelled')),
    reason TEXT NOT NULL CHECK (reason IN ('defective', 'wrong_item', 'customer_changed_mind', 'damaged_in_transit', 'not_as_described', 'duplicate_order', 'other')),
    reason_notes TEXT,

    -- Financial details
    subtotal DECIMAL(12,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    restocking_fee DECIMAL(12,2) NOT NULL DEFAULT 0,
    refund_method TEXT NOT NULL CHECK (refund_method IN ('cash', 'card', 'store_credit', 'original_payment')),

    -- Approval workflow
    requires_approval BOOLEAN NOT NULL DEFAULT false,
    approved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    approved_at TIMESTAMPTZ,
    approval_notes TEXT,

    -- Processing details
    processed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    processed_at TIMESTAMPTZ,

    -- Audit fields
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- Constraints
    UNIQUE(organization_id, refund_number)
);

-- Recreate refund_items table
CREATE TABLE public.refund_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    refund_id UUID NOT NULL REFERENCES public.refunds(id) ON DELETE CASCADE,
    sale_item_id UUID NOT NULL REFERENCES public.sale_items(id) ON DELETE RESTRICT,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE RESTRICT,

    -- Item details
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,

    -- Return condition
    condition TEXT NOT NULL DEFAULT 'new' CHECK (condition IN ('new', 'used', 'damaged', 'defective')),
    restore_inventory BOOLEAN NOT NULL DEFAULT true,
    notes TEXT,

    -- Audit fields
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Recreate store_credits table
CREATE TABLE public.store_credits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
    refund_id UUID REFERENCES public.refunds(id) ON DELETE SET NULL,

    -- Credit details
    credit_number TEXT NOT NULL,
    original_amount DECIMAL(12,2) NOT NULL,
    remaining_balance DECIMAL(12,2) NOT NULL,

    -- Validity
    issued_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    is_active BOOLEAN NOT NULL DEFAULT true,

    -- Audit fields
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- Constraints
    UNIQUE(organization_id, credit_number),
    CHECK (remaining_balance >= 0),
    CHECK (remaining_balance <= original_amount)
);

-- Recreate store_credit_transactions table
CREATE TABLE public.store_credit_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_credit_id UUID NOT NULL REFERENCES public.store_credits(id) ON DELETE CASCADE,
    sale_id UUID REFERENCES public.sales(id) ON DELETE SET NULL,

    -- Transaction details
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('issued', 'used', 'expired', 'cancelled')),
    amount DECIMAL(12,2) NOT NULL,
    balance_before DECIMAL(12,2) NOT NULL,
    balance_after DECIMAL(12,2) NOT NULL,
    notes TEXT,

    -- Audit fields
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_refunds_organization_id ON public.refunds(organization_id);
CREATE INDEX idx_refunds_original_sale_id ON public.refunds(original_sale_id);
CREATE INDEX idx_refunds_customer_id ON public.refunds(customer_id);
CREATE INDEX idx_refunds_status ON public.refunds(status);
CREATE INDEX idx_refunds_created_at ON public.refunds(created_at);
CREATE INDEX idx_refunds_refund_number ON public.refunds(organization_id, refund_number);

CREATE INDEX idx_refund_items_refund_id ON public.refund_items(refund_id);
CREATE INDEX idx_refund_items_sale_item_id ON public.refund_items(sale_item_id);
CREATE INDEX idx_refund_items_product_id ON public.refund_items(product_id);

CREATE INDEX idx_store_credits_organization_id ON public.store_credits(organization_id);
CREATE INDEX idx_store_credits_customer_id ON public.store_credits(customer_id);
CREATE INDEX idx_store_credits_credit_number ON public.store_credits(organization_id, credit_number);
CREATE INDEX idx_store_credits_active ON public.store_credits(is_active);

CREATE INDEX idx_store_credit_transactions_store_credit_id ON public.store_credit_transactions(store_credit_id);
CREATE INDEX idx_store_credit_transactions_sale_id ON public.store_credit_transactions(sale_id);
CREATE INDEX idx_store_credit_transactions_created_at ON public.store_credit_transactions(created_at);
