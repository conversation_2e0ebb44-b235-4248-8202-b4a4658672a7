-- Migration: Centralized Payables System
-- Philippines BIR-aligned, Multi-tenant secure, Future-ready
-- Supports: Purchase Receipts, Payroll, Utilities, Government Remittances, Loans, Manual Entries

-- 🔒 CRITICAL: Multi-tenant security enforced at database level
-- 💡 Incorporates all recommended enhancements
-- 🚀 Production-ready with comprehensive features

-- =====================================================
-- 1. CORE TABLES
-- =====================================================

-- Main payables table with multi-source support
CREATE TABLE IF NOT EXISTS public.payables (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,

    -- Multi-source tracking (extensible design)
    source_type TEXT NOT NULL CHECK (source_type IN (
        'purchase_receipt', 'payroll', 'utility_bill',
        'government_remittance', 'loan_repayment', 'manual_entry'
    )),
    source_id UUID NOT NULL, -- Points to relevant source table

    -- Payee information (XOR constraint: supplier OR employee, not both)
    supplier_id UUID REFERENCES public.suppliers(id) ON DELETE SET NULL,
    employee_id UUID REFERENCES public.employees(id) ON DELETE SET NULL,

    -- Document details
    reference_number TEXT NOT NULL,
    invoice_date DATE NOT NULL,
    payable_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    due_date DATE NOT NULL,

    -- Financial amounts (Philippines peso precision)
    amount NUMERIC(12,2) NOT NULL CHECK (amount >= 0),
    vat_amount NUMERIC(12,2) DEFAULT 0 CHECK (vat_amount >= 0),
    withholding_tax_rate NUMERIC(5,2) DEFAULT 0 CHECK (withholding_tax_rate >= 0 AND withholding_tax_rate <= 100),
    withholding_tax_amount NUMERIC(12,2) DEFAULT 0 CHECK (withholding_tax_amount >= 0),
    balance NUMERIC(12,2) NOT NULL CHECK (balance >= 0),

    -- Currency and status
    currency TEXT NOT NULL DEFAULT 'PHP',
    status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('draft', 'open', 'partially_paid', 'paid', 'cancelled')),

    -- Optional categorization for reporting
    category TEXT DEFAULT NULL, -- e.g., 'office_supplies', 'salary', 'rent', 'utilities'

    -- Attachments and notes
    invoice_url TEXT,
    notes TEXT,

    -- Journal entry readiness for accounting integration
    journal_entry_id UUID DEFAULT NULL, -- FK to future journal_entries table
    posted_to_ledger BOOLEAN DEFAULT FALSE,

    -- Audit fields
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- Constraints
    CONSTRAINT unique_source_per_org UNIQUE (organization_id, source_type, source_id),
    CONSTRAINT check_single_payee CHECK (
        (supplier_id IS NOT NULL AND employee_id IS NULL) OR
        (supplier_id IS NULL AND employee_id IS NOT NULL) OR
        (supplier_id IS NULL AND employee_id IS NULL AND source_type = 'manual_entry')
    )
);

-- Payment history table
CREATE TABLE IF NOT EXISTS public.payable_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payable_id UUID NOT NULL REFERENCES public.payables(id) ON DELETE CASCADE,

    -- Payment details
    payment_date DATE NOT NULL,
    amount_paid NUMERIC(12,2) NOT NULL CHECK (amount_paid > 0),
    payment_method TEXT NOT NULL CHECK (payment_method IN (
        'cash', 'check', 'bank_transfer', 'gcash', 'paymaya', 'credit_card', 'other'
    )),
    reference_number TEXT, -- Check No., GCash ref, OR #

    -- Attachments and notes
    attachment_url TEXT,
    remarks TEXT,

    -- Audit fields
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- =====================================================
-- 2. PERFORMANCE INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_payables_organization_id ON public.payables(organization_id);
CREATE INDEX IF NOT EXISTS idx_payables_source_type_id ON public.payables(source_type, source_id);
CREATE INDEX IF NOT EXISTS idx_payables_supplier_id ON public.payables(supplier_id);
CREATE INDEX IF NOT EXISTS idx_payables_employee_id ON public.payables(employee_id);
CREATE INDEX IF NOT EXISTS idx_payables_status ON public.payables(status);
CREATE INDEX IF NOT EXISTS idx_payables_due_date ON public.payables(due_date);
CREATE INDEX IF NOT EXISTS idx_payables_category ON public.payables(category);
CREATE INDEX IF NOT EXISTS idx_payables_posted_to_ledger ON public.payables(posted_to_ledger);
CREATE INDEX IF NOT EXISTS idx_payable_payments_payable_id ON public.payable_payments(payable_id);
CREATE INDEX IF NOT EXISTS idx_payable_payments_payment_date ON public.payable_payments(payment_date);

-- =====================================================
-- 3. MULTI-TENANCY SECURITY FUNCTIONS (CRITICAL)
-- =====================================================

-- Validates all relationships belong to same organization
CREATE OR REPLACE FUNCTION validate_payable_multi_tenancy()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate supplier belongs to same organization
    IF NEW.supplier_id IS NOT NULL THEN
        IF NOT EXISTS (
            SELECT 1 FROM public.suppliers
            WHERE id = NEW.supplier_id AND organization_id = NEW.organization_id
        ) THEN
            RAISE EXCEPTION 'SECURITY VIOLATION: Supplier % does not belong to organization %',
                NEW.supplier_id, NEW.organization_id;
        END IF;
    END IF;

    -- Validate employee belongs to same organization
    IF NEW.employee_id IS NOT NULL THEN
        IF NOT EXISTS (
            SELECT 1 FROM public.employees
            WHERE id = NEW.employee_id AND organization_id = NEW.organization_id
        ) THEN
            RAISE EXCEPTION 'SECURITY VIOLATION: Employee % does not belong to organization %',
                NEW.employee_id, NEW.organization_id;
        END IF;
    END IF;

    -- Validate source_id belongs to same organization based on source_type
    IF NEW.source_type = 'purchase_receipt' THEN
        IF NOT EXISTS (
            SELECT 1 FROM public.inventory_receipts
            WHERE id = NEW.source_id AND organization_id = NEW.organization_id
        ) THEN
            RAISE EXCEPTION 'SECURITY VIOLATION: Purchase receipt % does not belong to organization %',
                NEW.source_id, NEW.organization_id;
        END IF;
    ELSIF NEW.source_type = 'payroll' THEN
        IF NOT EXISTS (
            SELECT 1 FROM public.payroll_periods
            WHERE id = NEW.source_id AND organization_id = NEW.organization_id
        ) THEN
            RAISE EXCEPTION 'SECURITY VIOLATION: Payroll period % does not belong to organization %',
                NEW.source_id, NEW.organization_id;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Multi-tenancy validation for payments
CREATE OR REPLACE FUNCTION validate_payment_multi_tenancy()
RETURNS TRIGGER AS $$
DECLARE
    payable_org_id UUID;
BEGIN
    SELECT organization_id INTO payable_org_id
    FROM public.payables WHERE id = NEW.payable_id;

    IF payable_org_id IS NULL THEN
        RAISE EXCEPTION 'SECURITY VIOLATION: Payable % not found', NEW.payable_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. BUSINESS LOGIC FUNCTIONS
-- =====================================================

-- Validates withholding tax calculation (Philippines BIR compliance)
CREATE OR REPLACE FUNCTION validate_withholding_tax()
RETURNS TRIGGER AS $$
DECLARE
    calculated_wht NUMERIC(12,2);
    net_amount NUMERIC(12,2);
BEGIN
    IF NEW.withholding_tax_rate > 0 THEN
        net_amount := NEW.amount - COALESCE(NEW.vat_amount, 0);
        calculated_wht := net_amount * NEW.withholding_tax_rate / 100;

        -- Allow small rounding differences (up to 0.01)
        IF ABS(NEW.withholding_tax_amount - calculated_wht) > 0.01 THEN
            RAISE EXCEPTION 'Withholding tax amount (%) does not match calculated amount (%) for rate %. Net amount: %, Rate: %',
                NEW.withholding_tax_amount, calculated_wht, NEW.withholding_tax_rate, net_amount, NEW.withholding_tax_rate;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Updates payable balance and status after payment changes
CREATE OR REPLACE FUNCTION update_payable_balance()
RETURNS TRIGGER AS $$
DECLARE
    total_paid NUMERIC(12,2);
    payable_amount NUMERIC(12,2);
    new_balance NUMERIC(12,2);
    new_status TEXT;
    target_payable_id UUID;
BEGIN
    target_payable_id := COALESCE(NEW.payable_id, OLD.payable_id);

    -- Get payable amount
    SELECT amount INTO payable_amount
    FROM public.payables WHERE id = target_payable_id;

    -- Calculate total payments
    SELECT COALESCE(SUM(amount_paid), 0) INTO total_paid
    FROM public.payable_payments WHERE payable_id = target_payable_id;

    -- Calculate new balance and status
    new_balance := GREATEST(payable_amount - total_paid, 0);

    IF new_balance <= 0 THEN
        new_status := 'paid';
    ELSIF new_balance < payable_amount THEN
        new_status := 'partially_paid';
    ELSE
        new_status := 'open';
    END IF;

    -- Update payable
    UPDATE public.payables
    SET balance = new_balance, status = new_status, updated_at = NOW()
    WHERE id = target_payable_id;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Auto-creation function removed - using manual creation only via SendToPayableButton

-- Prevents deletion of paid payables (audit protection)
CREATE OR REPLACE FUNCTION prevent_paid_payable_deletion()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status = 'paid' THEN
        RAISE EXCEPTION 'Cannot delete paid payable %. Audit protection enabled.', OLD.id;
    END IF;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Updates timestamp on payable changes
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. REPORTING & ANALYTICS FUNCTIONS
-- =====================================================

-- Resolves source_id to human-readable metadata
CREATE OR REPLACE FUNCTION resolve_payable_source_metadata(p_payable_id UUID)
RETURNS TABLE(
    source_reference TEXT,
    source_description TEXT,
    source_date DATE,
    source_amount NUMERIC(12,2)
) AS $$
DECLARE
    payable_record RECORD;
BEGIN
    SELECT source_type, source_id, organization_id
    INTO payable_record
    FROM public.payables WHERE id = p_payable_id;

    IF payable_record.source_type = 'purchase_receipt' THEN
        RETURN QUERY
        SELECT
            ir.receipt_number::TEXT,
            ('Inventory Receipt from PO: ' || COALESCE(po.order_number, 'N/A'))::TEXT,
            ir.receipt_date::DATE,
            COALESCE(SUM(iri.quantity * iri.unit_cost), 0)::NUMERIC(12,2)
        FROM public.inventory_receipts ir
        LEFT JOIN public.purchase_orders po ON ir.purchase_order_id = po.id
        LEFT JOIN public.inventory_receipt_items iri ON ir.id = iri.inventory_receipt_id
        WHERE ir.id = payable_record.source_id
        AND ir.organization_id = payable_record.organization_id
        GROUP BY ir.receipt_number, po.order_number, ir.receipt_date;

    ELSIF payable_record.source_type = 'payroll' THEN
        RETURN QUERY
        SELECT
            pp.name::TEXT,
            ('Payroll Period: ' || pp.start_date::TEXT || ' to ' || pp.end_date::TEXT)::TEXT,
            pp.payment_date::DATE,
            COALESCE(SUM(pi.net_pay), 0)::NUMERIC(12,2)
        FROM public.payroll_periods pp
        LEFT JOIN public.payroll_items pi ON pp.id = pi.payroll_period_id
        WHERE pp.id = payable_record.source_id
        AND pp.organization_id = payable_record.organization_id
        GROUP BY pp.name, pp.start_date, pp.end_date, pp.payment_date;

    ELSE
        RETURN QUERY
        SELECT
            'Manual Entry'::TEXT,
            'Manually created payable'::TEXT,
            CURRENT_DATE,
            0::NUMERIC(12,2);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Calculates aging buckets for payables reporting
CREATE OR REPLACE FUNCTION calculate_payable_aging(
    p_organization_id UUID,
    p_as_of_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
    payable_id UUID,
    reference_number TEXT,
    supplier_name TEXT,
    amount NUMERIC(12,2),
    balance NUMERIC(12,2),
    due_date DATE,
    days_overdue INTEGER,
    aging_bucket TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.reference_number,
        COALESCE(s.name, CONCAT(e.first_name, ' ', e.last_name), 'Unknown')::TEXT,
        p.amount,
        p.balance,
        p.due_date,
        (p_as_of_date - p.due_date)::INTEGER,
        CASE
            WHEN p_as_of_date <= p.due_date THEN 'Current'
            WHEN p_as_of_date - p.due_date <= 30 THEN '1-30 days'
            WHEN p_as_of_date - p.due_date <= 60 THEN '31-60 days'
            WHEN p_as_of_date - p.due_date <= 90 THEN '61-90 days'
            ELSE 'Over 90 days'
        END::TEXT
    FROM public.payables p
    LEFT JOIN public.suppliers s ON p.supplier_id = s.id
    LEFT JOIN public.employees e ON p.employee_id = e.id
    WHERE p.organization_id = p_organization_id
    AND p.status IN ('open', 'partially_paid')
    AND p.balance > 0
    ORDER BY p.due_date ASC;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. TRIGGERS (Security & Business Logic)
-- =====================================================

-- Drop existing triggers if they exist (for safe re-running)
DROP TRIGGER IF EXISTS trigger_validate_payable_multi_tenancy ON public.payables;
DROP TRIGGER IF EXISTS trigger_validate_payment_multi_tenancy ON public.payable_payments;
DROP TRIGGER IF EXISTS trigger_validate_withholding_tax ON public.payables;
DROP TRIGGER IF EXISTS trigger_update_payable_balance_on_payment_insert ON public.payable_payments;
DROP TRIGGER IF EXISTS trigger_update_payable_balance_on_payment_update ON public.payable_payments;
DROP TRIGGER IF EXISTS trigger_update_payable_balance_on_payment_delete ON public.payable_payments;
DROP TRIGGER IF EXISTS trigger_auto_create_payable_from_receipt ON public.inventory_receipts;
DROP TRIGGER IF EXISTS trigger_prevent_paid_payable_deletion ON public.payables;
DROP TRIGGER IF EXISTS trigger_payables_updated_at ON public.payables;

-- Multi-tenancy security triggers (CRITICAL)
DROP TRIGGER IF EXISTS trigger_validate_payable_multi_tenancy ON public.payables;
CREATE TRIGGER trigger_validate_payable_multi_tenancy
    BEFORE INSERT OR UPDATE ON public.payables
    FOR EACH ROW
    EXECUTE FUNCTION validate_payable_multi_tenancy();

DROP TRIGGER IF EXISTS trigger_validate_payment_multi_tenancy ON public.payable_payments;
CREATE TRIGGER trigger_validate_payment_multi_tenancy
    BEFORE INSERT OR UPDATE ON public.payable_payments
    FOR EACH ROW
    EXECUTE FUNCTION validate_payment_multi_tenancy();

-- Business logic triggers
DROP TRIGGER IF EXISTS trigger_validate_withholding_tax ON public.payables;
CREATE TRIGGER trigger_validate_withholding_tax
    BEFORE INSERT OR UPDATE ON public.payables
    FOR EACH ROW
    WHEN (NEW.withholding_tax_rate > 0)
    EXECUTE FUNCTION validate_withholding_tax();

DROP TRIGGER IF EXISTS trigger_update_payable_balance_on_payment_insert ON public.payable_payments;
CREATE TRIGGER trigger_update_payable_balance_on_payment_insert
    AFTER INSERT ON public.payable_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_payable_balance();

DROP TRIGGER IF EXISTS trigger_update_payable_balance_on_payment_update ON public.payable_payments;
CREATE TRIGGER trigger_update_payable_balance_on_payment_update
    AFTER UPDATE ON public.payable_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_payable_balance();

DROP TRIGGER IF EXISTS trigger_update_payable_balance_on_payment_delete ON public.payable_payments;
CREATE TRIGGER trigger_update_payable_balance_on_payment_delete
    AFTER DELETE ON public.payable_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_payable_balance();

-- Auto-creation trigger removed - using manual creation only

-- Audit protection triggers
DROP TRIGGER IF EXISTS trigger_prevent_paid_payable_deletion ON public.payables;
CREATE TRIGGER trigger_prevent_paid_payable_deletion
    BEFORE DELETE ON public.payables
    FOR EACH ROW
    EXECUTE FUNCTION prevent_paid_payable_deletion();

DROP TRIGGER IF EXISTS trigger_payables_updated_at ON public.payables;
CREATE TRIGGER trigger_payables_updated_at
    BEFORE UPDATE ON public.payables
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 7. SUPPLIER ENHANCEMENTS
-- =====================================================

-- Add payment terms to suppliers if not exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'suppliers' AND column_name = 'payment_terms_days'
    ) THEN
        ALTER TABLE public.suppliers
        ADD COLUMN payment_terms_days INTEGER DEFAULT 30;

        COMMENT ON COLUMN public.suppliers.payment_terms_days IS 'Default payment terms in days for calculating due dates';
    END IF;
END $$;

-- =====================================================
-- 8. SECURITY & PERMISSIONS
-- =====================================================

-- Disable RLS for now (as requested)
ALTER TABLE public.payables DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.payable_payments DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- 9. DOCUMENTATION & COMMENTS
-- =====================================================

COMMENT ON TABLE public.payables IS 'Centralized payables ledger supporting multiple source types with multi-tenant security';
COMMENT ON TABLE public.payable_payments IS 'Payment history for payables with support for partial payments';

COMMENT ON COLUMN public.payables.source_type IS 'Source type: purchase_receipt, payroll, utility_bill, government_remittance, loan_repayment, manual_entry';
COMMENT ON COLUMN public.payables.source_id IS 'UUID pointing to the relevant source table record (validated for multi-tenancy)';
COMMENT ON COLUMN public.payables.category IS 'Optional category for grouping: office_supplies, salary, rent, utilities, etc.';
COMMENT ON COLUMN public.payables.withholding_tax_rate IS 'EWT rate as percentage (0-100) for Philippines BIR compliance';
COMMENT ON COLUMN public.payables.withholding_tax_amount IS 'Calculated EWT amount (validated against rate)';
COMMENT ON COLUMN public.payables.journal_entry_id IS 'FK to future journal_entries table for accounting integration';
COMMENT ON COLUMN public.payables.posted_to_ledger IS 'Flag indicating if payable has been posted to general ledger';

COMMENT ON FUNCTION validate_payable_multi_tenancy() IS 'CRITICAL: Prevents cross-organization data leakage by validating all relationships';
COMMENT ON FUNCTION resolve_payable_source_metadata(UUID) IS 'Resolves source_id to human-readable metadata for UI display';
COMMENT ON FUNCTION calculate_payable_aging(UUID, DATE) IS 'Calculates aging buckets for payables reporting';

-- =====================================================
-- 10. FINAL VALIDATION
-- =====================================================

DO $$
BEGIN
    -- Verify critical constraints exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE table_name = 'payables' AND constraint_name = 'check_single_payee'
    ) THEN
        RAISE EXCEPTION 'CRITICAL: XOR constraint check_single_payee was not created';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE table_name = 'payables' AND constraint_name = 'unique_source_per_org'
    ) THEN
        RAISE EXCEPTION 'CRITICAL: Unique constraint unique_source_per_org was not created';
    END IF;

    -- Verify security functions exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.routines
        WHERE routine_name = 'validate_payable_multi_tenancy'
    ) THEN
        RAISE EXCEPTION 'CRITICAL: Multi-tenancy security function was not created';
    END IF;

    RAISE NOTICE '✅ Centralized Payables System migration completed successfully!';
    RAISE NOTICE '🔒 Multi-tenancy security: ENABLED';
    RAISE NOTICE '💰 Philippines BIR compliance: READY';
    RAISE NOTICE '🚀 Manual creation workflow: ACTIVE (SendToPayableButton)';
    RAISE NOTICE '📊 Reporting functions: AVAILABLE';
    RAISE NOTICE '🔧 Future extensibility: PREPARED';
END $$;