-- Migration to enhance the profiles table with additional columns
-- for the enhanced user profile feature

-- Add new columns to the profiles table
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS bio TEXT,
ADD COLUMN IF NOT EXISTS job_title TEXT,
ADD COLUMN IF NOT EXISTS department TEXT,
ADD COLUMN IF NOT EXISTS phone_number TEXT,
ADD COLUMN IF NOT EXISTS location TEXT;

-- Update the profile bypass function to include the new fields
DROP FUNCTION IF EXISTS public.create_profile_bypass_rls(uuid, text, text);

CREATE OR REPLACE FUNCTION public.create_profile_bypass_rls(
  user_id uuid,
  first_name text,
  last_name text,
  bio text DEFAULT NULL,
  job_title text DEFAULT NULL,
  department text DEFAULT NULL,
  phone_number text DEFAULT NULL,
  location text DEFAULT NULL
) RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER -- This runs with the privileges of the function creator
SET search_path = public -- Prevent search path injection
AS $$
DECLARE
  v_result json;
BEGIN
  -- Disable R<PERSON> temporarily for this transaction
  ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
  
  BEGIN
    -- Insert the profile with all fields
    INSERT INTO public.profiles (
      id, 
      first_name, 
      last_name, 
      bio, 
      job_title, 
      department, 
      phone_number, 
      location
    )
    VALUES (
      user_id, 
      first_name, 
      last_name, 
      bio, 
      job_title, 
      department, 
      phone_number, 
      location
    );
    
    -- Re-enable RLS
    ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
    
    -- Return success with profile data
    v_result := json_build_object(
      'success', true,
      'profile', json_build_object(
        'id', user_id,
        'first_name', first_name,
        'last_name', last_name,
        'bio', bio,
        'job_title', job_title,
        'department', department,
        'phone_number', phone_number,
        'location', location
      )
    );
    
    RETURN v_result;
  EXCEPTION
    WHEN OTHERS THEN
      -- Re-enable RLS even if there's an error
      ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
      
      -- Return error information
      v_result := json_build_object(
        'success', false,
        'error', SQLERRM,
        'error_detail', SQLSTATE
      );
      
      RETURN v_result;
  END;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.create_profile_bypass_rls(
  uuid, text, text, text, text, text, text, text
) TO authenticated;

-- Add a comment explaining the function
COMMENT ON FUNCTION public.create_profile_bypass_rls(
  uuid, text, text, text, text, text, text, text
) IS 
'Creates a user profile with extended fields, bypassing RLS completely. 
This function should only be used when necessary, as it temporarily disables RLS.';

-- Update the organization_members_with_profiles view to include the new fields
CREATE OR REPLACE VIEW public.organization_members_with_profiles AS
SELECT
    om.id,
    om.organization_id,
    om.user_id,
    om.role,
    om.created_at,
    om.updated_at,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.bio,
    p.job_title,
    p.department,
    p.phone_number,
    p.location
FROM
    public.organization_members om
LEFT JOIN
    public.profiles p ON om.user_id = p.id
WHERE
    -- Apply security filter directly in the view definition
    -- This ensures only members of the same organization can be seen
    EXISTS (
        SELECT 1
        FROM public.organization_members my_orgs
        WHERE my_orgs.organization_id = om.organization_id
        AND my_orgs.user_id = auth.uid()
    );

-- Add comments to the new columns
COMMENT ON COLUMN public.profiles.bio IS 'User biography or about me text';
COMMENT ON COLUMN public.profiles.job_title IS 'User job title or position';
COMMENT ON COLUMN public.profiles.department IS 'Department or team the user belongs to';
COMMENT ON COLUMN public.profiles.phone_number IS 'User contact phone number';
COMMENT ON COLUMN public.profiles.location IS 'User location (city, country, etc.)';
