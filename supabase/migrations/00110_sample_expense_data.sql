-- Migration: Sample Data for Enhanced Payables System Testing
-- Creates sample expense types and configurations for testing

-- =====================================================
-- SAMPLE EXPENSE TYPES
-- =====================================================

-- Note: This will only insert sample data if no expense types exist
-- This prevents duplicate data on multiple runs

DO $$
DECLARE
    org_id UUID;
    admin_user_id UUID;
BEGIN
    -- Get the first organization (for testing purposes)
    SELECT id INTO org_id FROM public.organizations LIMIT 1;
    
    -- Get the first admin user (for testing purposes)
    SELECT id INTO admin_user_id FROM auth.users LIMIT 1;
    
    -- Only proceed if we have an organization and user
    IF org_id IS NOT NULL AND admin_user_id IS NOT NULL THEN
        -- Check if expense types already exist for this organization
        IF NOT EXISTS (SELECT 1 FROM public.expense_types WHERE organization_id = org_id) THEN
            
            -- Insert sample expense types
            INSERT INTO public.expense_types (
                organization_id, name, code, description, category, 
                requires_approval, approval_limit, default_account_code, 
                is_recurring_type, default_frequency, created_by
            ) VALUES
            -- Operational Expenses
            (org_id, 'Office Rent', 'RENT', 'Monthly office space rental', 'operational', true, 50000.00, '5100', true, 'monthly', admin_user_id),
            (org_id, 'Utilities - Electricity', 'ELEC', 'Monthly electricity bills', 'utilities', true, 15000.00, '5200', true, 'monthly', admin_user_id),
            (org_id, 'Utilities - Water', 'WATER', 'Monthly water bills', 'utilities', true, 5000.00, '5201', true, 'monthly', admin_user_id),
            (org_id, 'Internet & Telecommunications', 'TELECOM', 'Internet and phone services', 'utilities', true, 8000.00, '5202', true, 'monthly', admin_user_id),
            
            -- Office Supplies
            (org_id, 'Office Supplies', 'SUPPLIES', 'General office supplies and materials', 'office_supplies', true, 10000.00, '5300', false, null, admin_user_id),
            (org_id, 'Computer Equipment', 'COMPUTER', 'Computers, laptops, and IT equipment', 'office_supplies', true, 100000.00, '5301', false, null, admin_user_id),
            (org_id, 'Office Furniture', 'FURNITURE', 'Desks, chairs, and office furniture', 'office_supplies', true, 50000.00, '5302', false, null, admin_user_id),
            
            -- Professional Services
            (org_id, 'Legal Services', 'LEGAL', 'Legal consultation and services', 'professional_services', true, 25000.00, '5400', false, null, admin_user_id),
            (org_id, 'Accounting Services', 'ACCT', 'External accounting and bookkeeping', 'professional_services', true, 20000.00, '5401', true, 'monthly', admin_user_id),
            (org_id, 'IT Consulting', 'ITCONS', 'IT consulting and technical services', 'professional_services', true, 30000.00, '5402', false, null, admin_user_id),
            
            -- Maintenance
            (org_id, 'Equipment Maintenance', 'MAINT', 'Equipment repair and maintenance', 'maintenance', true, 15000.00, '5500', false, null, admin_user_id),
            (org_id, 'Building Maintenance', 'BMAINT', 'Building and facility maintenance', 'maintenance', true, 20000.00, '5501', false, null, admin_user_id),
            (org_id, 'Vehicle Maintenance', 'VMAINT', 'Vehicle repair and maintenance', 'maintenance', true, 12000.00, '5502', false, null, admin_user_id),
            
            -- Administrative
            (org_id, 'Insurance Premiums', 'INSUR', 'Business insurance premiums', 'administrative', true, 25000.00, '5600', true, 'quarterly', admin_user_id),
            (org_id, 'Business Licenses', 'LICENSE', 'Government licenses and permits', 'administrative', true, 10000.00, '5601', true, 'annual', admin_user_id),
            (org_id, 'Bank Charges', 'BANK', 'Banking fees and charges', 'financial', true, 5000.00, '5700', true, 'monthly', admin_user_id),
            
            -- Travel
            (org_id, 'Business Travel', 'TRAVEL', 'Business travel and accommodation', 'travel', true, 20000.00, '5800', false, null, admin_user_id),
            (org_id, 'Fuel & Transportation', 'FUEL', 'Vehicle fuel and transportation costs', 'travel', true, 8000.00, '5801', false, null, admin_user_id),
            
            -- Financial
            (org_id, 'Loan Interest', 'INTEREST', 'Interest payments on business loans', 'financial', true, 50000.00, '5900', true, 'monthly', admin_user_id),
            (org_id, 'Government Taxes', 'TAXES', 'Business taxes and government fees', 'financial', true, 100000.00, '5901', true, 'quarterly', admin_user_id);
            
            RAISE NOTICE 'Sample expense types created successfully for organization %', org_id;
        ELSE
            RAISE NOTICE 'Expense types already exist for organization %, skipping sample data creation', org_id;
        END IF;
    ELSE
        RAISE NOTICE 'No organization or user found, skipping sample data creation';
    END IF;
END $$;

-- =====================================================
-- SAMPLE APPROVAL WORKFLOWS
-- =====================================================

DO $$
DECLARE
    org_id UUID;
    admin_user_id UUID;
    expense_type_rec RECORD;
BEGIN
    -- Get the first organization (for testing purposes)
    SELECT id INTO org_id FROM public.organizations LIMIT 1;
    
    -- Get the first admin user (for testing purposes)
    SELECT id INTO admin_user_id FROM auth.users LIMIT 1;
    
    -- Only proceed if we have an organization and user
    IF org_id IS NOT NULL AND admin_user_id IS NOT NULL THEN
        -- Check if approval workflows already exist for this organization
        IF NOT EXISTS (SELECT 1 FROM public.approval_workflows WHERE organization_id = org_id) THEN
            
            -- Create approval workflows for high-value expense types
            FOR expense_type_rec IN 
                SELECT id, name, approval_limit 
                FROM public.expense_types 
                WHERE organization_id = org_id 
                AND approval_limit >= 20000.00
            LOOP
                INSERT INTO public.approval_workflows (
                    organization_id, name, description, expense_type_id,
                    min_amount, max_amount, level_1_approver_role, level_1_amount_limit,
                    level_2_approver_role, level_2_amount_limit, level_3_approver_role,
                    requires_receipt, auto_approve_below_limit, created_by
                ) VALUES (
                    org_id,
                    expense_type_rec.name || ' Approval Workflow',
                    'Approval workflow for ' || expense_type_rec.name || ' expenses',
                    expense_type_rec.id,
                    0.00,
                    NULL, -- No upper limit
                    'manager',
                    expense_type_rec.approval_limit * 0.5, -- Manager can approve up to 50% of limit
                    'director',
                    expense_type_rec.approval_limit, -- Director can approve up to full limit
                    'ceo', -- CEO for amounts above limit
                    true, -- Requires receipt
                    false, -- No auto-approval
                    admin_user_id
                );
            END LOOP;
            
            RAISE NOTICE 'Sample approval workflows created successfully for organization %', org_id;
        ELSE
            RAISE NOTICE 'Approval workflows already exist for organization %, skipping sample data creation', org_id;
        END IF;
    END IF;
END $$;

-- =====================================================
-- HELPFUL QUERIES FOR TESTING
-- =====================================================

-- View all created expense types
-- SELECT 
--     et.name,
--     et.code,
--     et.category,
--     et.approval_limit,
--     et.is_recurring_type,
--     et.default_frequency
-- FROM public.expense_types et
-- ORDER BY et.category, et.name;

-- View all created approval workflows
-- SELECT 
--     aw.name,
--     et.name as expense_type,
--     aw.level_1_approver_role,
--     aw.level_1_amount_limit,
--     aw.level_2_approver_role,
--     aw.level_2_amount_limit
-- FROM public.approval_workflows aw
-- JOIN public.expense_types et ON aw.expense_type_id = et.id
-- ORDER BY et.name;

-- =====================================================
-- DOCUMENTATION
-- =====================================================

COMMENT ON COLUMN public.expense_types.approval_limit IS 'Amount requiring higher approval - sample data ranges from 5,000 to 100,000 PHP';
COMMENT ON COLUMN public.expense_types.is_recurring_type IS 'Sample data includes monthly, quarterly, and annual recurring expenses';
COMMENT ON COLUMN public.approval_workflows.level_1_amount_limit IS 'Manager approval limit set to 50% of expense type limit';
COMMENT ON COLUMN public.approval_workflows.level_2_amount_limit IS 'Director approval limit set to 100% of expense type limit';
