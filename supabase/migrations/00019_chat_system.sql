-- Migration to create a chat system with support for one-on-one and group chats
-- with multi-tenant isolation using Supabase realtime

-- Create chat_conversations table
CREATE TABLE IF NOT EXISTS public.chat_conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    name TEXT, -- NULL for one-on-one chats, required for group chats
    is_group BOOLEAN NOT NULL DEFAULT FALSE,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create chat_participants table
CREATE TABLE IF NOT EXISTS public.chat_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES public.chat_conversations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    is_admin BOOLEAN NOT NULL DEFAULT FALSE,
    last_read_message_id UUID,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(conversation_id, user_id)
);

-- Create chat_messages table
CREATE TABLE IF NOT EXISTS public.chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES public.chat_conversations(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES auth.users(id),
    content TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create chat_message_status table
CREATE TABLE IF NOT EXISTS public.chat_message_status (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES public.chat_messages(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    read_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(message_id, user_id)
);

-- Apply the updated_at trigger to the new tables
DO $$
DECLARE
    t text;
BEGIN
    FOR t IN
        SELECT table_name
        FROM information_schema.columns
        WHERE column_name = 'updated_at'
        AND table_schema = 'public'
        AND table_name IN ('chat_conversations', 'chat_participants', 'chat_messages', 'chat_message_status')
    LOOP
        EXECUTE format('CREATE TRIGGER set_updated_at
                        BEFORE UPDATE ON public.%I
                        FOR EACH ROW
                        EXECUTE FUNCTION update_updated_at_column()', t);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Enable Row Level Security on all chat tables
ALTER TABLE public.chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_message_status ENABLE ROW LEVEL SECURITY;

-- RLS Policies for chat_conversations table
CREATE POLICY "Users can view their conversations"
ON public.chat_conversations
FOR SELECT
USING (
    id IN (
        SELECT conversation_id
        FROM public.chat_participants
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Users can create conversations in their organizations"
ON public.chat_conversations
FOR INSERT
WITH CHECK (
    organization_id IN (SELECT public.get_user_organizations())
    AND created_by = auth.uid()
);

CREATE POLICY "Conversation admins can update conversations"
ON public.chat_conversations
FOR UPDATE
USING (
    id IN (
        SELECT conversation_id
        FROM public.chat_participants
        WHERE user_id = auth.uid() AND is_admin = TRUE
    )
);

-- RLS Policies for chat_participants table
CREATE POLICY "Users can view participants in their conversations"
ON public.chat_participants
FOR SELECT
USING (
    conversation_id IN (
        SELECT conversation_id
        FROM public.chat_participants
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Conversation admins can manage participants"
ON public.chat_participants
FOR INSERT
WITH CHECK (
    conversation_id IN (
        SELECT conversation_id
        FROM public.chat_participants
        WHERE user_id = auth.uid() AND is_admin = TRUE
    )
);

CREATE POLICY "Users can update their own participant status"
ON public.chat_participants
FOR UPDATE
USING (
    user_id = auth.uid()
);

-- RLS Policies for chat_messages table
CREATE POLICY "Users can view messages in their conversations"
ON public.chat_messages
FOR SELECT
USING (
    conversation_id IN (
        SELECT conversation_id
        FROM public.chat_participants
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Users can send messages to their conversations"
ON public.chat_messages
FOR INSERT
WITH CHECK (
    conversation_id IN (
        SELECT conversation_id
        FROM public.chat_participants
        WHERE user_id = auth.uid()
    )
    AND sender_id = auth.uid()
);

CREATE POLICY "Users can update their own messages"
ON public.chat_messages
FOR UPDATE
USING (
    sender_id = auth.uid()
);

-- RLS Policies for chat_message_status table
CREATE POLICY "Users can view message status in their conversations"
ON public.chat_message_status
FOR SELECT
USING (
    message_id IN (
        SELECT id
        FROM public.chat_messages
        WHERE conversation_id IN (
            SELECT conversation_id
            FROM public.chat_participants
            WHERE user_id = auth.uid()
        )
    )
);

CREATE POLICY "Users can update their own message status"
ON public.chat_message_status
FOR INSERT
WITH CHECK (
    user_id = auth.uid()
);

CREATE POLICY "Users can update their own message read status"
ON public.chat_message_status
FOR UPDATE
USING (
    user_id = auth.uid()
);

-- Enable realtime for chat tables
BEGIN;
  -- Enable the realtime publication for chat tables
  ALTER PUBLICATION supabase_realtime ADD TABLE public.chat_conversations;
  ALTER PUBLICATION supabase_realtime ADD TABLE public.chat_participants;
  ALTER PUBLICATION supabase_realtime ADD TABLE public.chat_messages;
  ALTER PUBLICATION supabase_realtime ADD TABLE public.chat_message_status;
COMMIT;

-- Add comments for documentation
COMMENT ON TABLE public.chat_conversations IS 'Stores chat conversations with multi-tenant support';
COMMENT ON TABLE public.chat_participants IS 'Links users to conversations they participate in';
COMMENT ON TABLE public.chat_messages IS 'Stores messages sent in conversations';
COMMENT ON TABLE public.chat_message_status IS 'Tracks read status of messages for each user';

COMMENT ON COLUMN public.chat_conversations.name IS 'Name of the conversation (null for one-on-one chats)';
COMMENT ON COLUMN public.chat_conversations.is_group IS 'Whether this is a group chat or one-on-one chat';
COMMENT ON COLUMN public.chat_participants.is_admin IS 'Whether the user is an admin of the conversation (can add/remove users)';
COMMENT ON COLUMN public.chat_participants.last_read_message_id IS 'ID of the last message read by this user';
COMMENT ON COLUMN public.chat_message_status.is_read IS 'Whether the message has been read by the user';
COMMENT ON COLUMN public.chat_message_status.read_at IS 'When the message was read by the user';
