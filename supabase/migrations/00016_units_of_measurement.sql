-- Create units_of_measurement table
CREATE TABLE public.units_of_measurement (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  code TEXT NOT NULL,            -- e.g. 'pcs', 'box', 'kg'
  name TEXT NOT NULL,            -- e.g. 'Pieces', 'Box', 'Kilogram'
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (organization_id, code)
);

-- Add trigger for updated_at
CREATE TRIGGER set_updated_at
BEFORE UPDATE ON public.units_of_measurement
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE public.units_of_measurement ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their organization's units of measurement"
ON public.units_of_measurement
FOR SELECT
USING (
  organization_id IN (
    SELECT organization_id
    FROM public.organization_members
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can create units of measurement in their organization"
ON public.units_of_measurement
FOR INSERT
WITH CHECK (
  organization_id IN (
    SELECT organization_id
    FROM public.organization_members
    WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
  )
);

CREATE POLICY "Only owners and admins can update units of measurement"
ON public.units_of_measurement
FOR UPDATE
USING (
  organization_id IN (
    SELECT organization_id
    FROM public.organization_members
    WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
  )
);

CREATE POLICY "Only owners and admins can delete units of measurement"
ON public.units_of_measurement
FOR DELETE
USING (
  organization_id IN (
    SELECT organization_id
    FROM public.organization_members
    WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
  )
);

-- Insert default units of measurement for each organization
INSERT INTO public.units_of_measurement (organization_id, code, name, description, created_by)
SELECT 
  o.id, 
  'pcs', 
  'Pieces', 
  'Individual units or items', 
  (SELECT user_id FROM public.organization_members WHERE organization_id = o.id AND role = 'owner' LIMIT 1)
FROM public.organizations o;

INSERT INTO public.units_of_measurement (organization_id, code, name, description, created_by)
SELECT 
  o.id, 
  'box', 
  'Box', 
  'Standard box packaging', 
  (SELECT user_id FROM public.organization_members WHERE organization_id = o.id AND role = 'owner' LIMIT 1)
FROM public.organizations o;

INSERT INTO public.units_of_measurement (organization_id, code, name, description, created_by)
SELECT 
  o.id, 
  'kg', 
  'Kilogram', 
  'Weight measurement in kilograms', 
  (SELECT user_id FROM public.organization_members WHERE organization_id = o.id AND role = 'owner' LIMIT 1)
FROM public.organizations o;

INSERT INTO public.units_of_measurement (organization_id, code, name, description, created_by)
SELECT 
  o.id, 
  'liter', 
  'Liter', 
  'Volume measurement in liters', 
  (SELECT user_id FROM public.organization_members WHERE organization_id = o.id AND role = 'owner' LIMIT 1)
FROM public.organizations o;
