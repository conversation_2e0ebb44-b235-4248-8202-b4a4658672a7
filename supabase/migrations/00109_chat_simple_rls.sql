-- Simple RLS policies for chat system to avoid infinite recursion
-- This migration creates straightforward policies without complex subqueries

-- First, drop all existing chat policies
DROP POLICY IF EXISTS "Users can view their conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can create conversations in their organizations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can create conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation admins can update conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Users can view conversations in their organizations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation creators can update their conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "Conversation creators can delete their conversations" ON public.chat_conversations;
DROP POLICY IF EXISTS "chat_conversations_select_policy" ON public.chat_conversations;
DROP POLICY IF EXISTS "chat_conversations_insert_policy" ON public.chat_conversations;
DROP POLICY IF EXISTS "chat_conversations_update_policy" ON public.chat_conversations;
DROP POLICY IF EXISTS "chat_conversations_delete_policy" ON public.chat_conversations;

DROP POLICY IF EXISTS "Users can view participants in their conversations" ON public.chat_participants;
DROP POLICY IF EXISTS "Conversation admins can manage participants" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can create their own participant record" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can update their own participant status" ON public.chat_participants;
DROP POLICY IF EXISTS "Users can create participant records for conversations they're in" ON public.chat_participants;
DROP POLICY IF EXISTS "Conversation creators can delete participants" ON public.chat_participants;
DROP POLICY IF EXISTS "chat_participants_select_policy" ON public.chat_participants;
DROP POLICY IF EXISTS "chat_participants_insert_policy" ON public.chat_participants;
DROP POLICY IF EXISTS "chat_participants_update_policy" ON public.chat_participants;
DROP POLICY IF EXISTS "chat_participants_delete_policy" ON public.chat_participants;

DROP POLICY IF EXISTS "Users can view messages in their conversations" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can send messages to their conversations" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can update their own messages" ON public.chat_messages;
DROP POLICY IF EXISTS "Users can delete their own messages" ON public.chat_messages;
DROP POLICY IF EXISTS "chat_messages_select_policy" ON public.chat_messages;
DROP POLICY IF EXISTS "chat_messages_insert_policy" ON public.chat_messages;
DROP POLICY IF EXISTS "chat_messages_update_policy" ON public.chat_messages;
DROP POLICY IF EXISTS "chat_messages_delete_policy" ON public.chat_messages;

DROP POLICY IF EXISTS "Users can view message status in their conversations" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can update their own message status" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can update their own message read status" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can view message status for their conversations" ON public.chat_message_status;
DROP POLICY IF EXISTS "Users can create their own message status" ON public.chat_message_status;
DROP POLICY IF EXISTS "chat_message_status_select_policy" ON public.chat_message_status;
DROP POLICY IF EXISTS "chat_message_status_insert_policy" ON public.chat_message_status;
DROP POLICY IF EXISTS "chat_message_status_update_policy" ON public.chat_message_status;

-- Drop any problematic functions
DROP FUNCTION IF EXISTS public.is_conversation_participant(UUID, UUID);
DROP FUNCTION IF EXISTS public.is_conversation_admin(UUID, UUID);
DROP FUNCTION IF EXISTS public.get_user_conversations();

-- Disable RLS temporarily to avoid recursion issues
ALTER TABLE public.chat_conversations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_participants DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_message_status DISABLE ROW LEVEL SECURITY;

-- Create a simple helper function to check if user is in organization
CREATE OR REPLACE FUNCTION public.user_in_organization(user_id UUID, org_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Handle null inputs
  IF user_id IS NULL OR org_id IS NULL THEN
    RETURN FALSE;
  END IF;

  RETURN EXISTS (
    SELECT 1 FROM public.organization_members
    WHERE organization_members.user_id = user_in_organization.user_id
    AND organization_members.organization_id = user_in_organization.org_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Re-enable RLS
ALTER TABLE public.chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_message_status ENABLE ROW LEVEL SECURITY;

-- Create simple, non-recursive policies

-- Chat Conversations: Only allow access to conversations in user's organizations
CREATE POLICY "chat_conversations_org_access"
ON public.chat_conversations
FOR ALL
USING (
  auth.uid() IS NOT NULL AND public.user_in_organization(auth.uid(), organization_id)
);

-- Chat Participants: Allow users to see all participants in conversations they have access to
CREATE POLICY "chat_participants_access"
ON public.chat_participants
FOR ALL
USING (
  auth.uid() IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.chat_conversations
    WHERE chat_conversations.id = chat_participants.conversation_id
    AND public.user_in_organization(auth.uid(), chat_conversations.organization_id)
  )
);

-- Chat Messages: Allow access to messages in conversations user has access to
CREATE POLICY "chat_messages_access"
ON public.chat_messages
FOR ALL
USING (
  auth.uid() IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.chat_conversations
    WHERE chat_conversations.id = chat_messages.conversation_id
    AND public.user_in_organization(auth.uid(), chat_conversations.organization_id)
  )
);

-- Chat Message Status: Allow users to manage their own message status
CREATE POLICY "chat_message_status_own"
ON public.chat_message_status
FOR ALL
USING (auth.uid() IS NOT NULL AND user_id = auth.uid());

-- Add additional constraint for message creation - users can only send messages to conversations they participate in
CREATE POLICY "chat_messages_participant_only"
ON public.chat_messages
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.chat_participants
    WHERE chat_participants.conversation_id = chat_messages.conversation_id
    AND chat_participants.user_id = auth.uid()
  )
  AND sender_id = auth.uid()
);

-- Add constraint for conversation creation - must be in the organization
CREATE POLICY "chat_conversations_create_org"
ON public.chat_conversations
FOR INSERT
WITH CHECK (
  public.user_in_organization(auth.uid(), organization_id)
  AND created_by = auth.uid()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_chat_conversations_organization_id ON public.chat_conversations(organization_id);
CREATE INDEX IF NOT EXISTS idx_chat_conversations_created_by ON public.chat_conversations(created_by);
CREATE INDEX IF NOT EXISTS idx_chat_participants_user_id ON public.chat_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_participants_conversation_id ON public.chat_participants(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_id ON public.chat_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sender_id ON public.chat_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON public.chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_message_status_user_id ON public.chat_message_status(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_message_status_message_id ON public.chat_message_status(message_id);

-- Add table comments
COMMENT ON TABLE public.chat_conversations IS 'Chat conversations with organization-based access control';
COMMENT ON TABLE public.chat_participants IS 'Chat participants with simple access control';
COMMENT ON TABLE public.chat_messages IS 'Chat messages with organization-based access control';
COMMENT ON TABLE public.chat_message_status IS 'Message read status with user-based access control';
