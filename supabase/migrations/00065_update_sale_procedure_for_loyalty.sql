-- Update the create_sale_with_items function to handle loyalty points
CREATE OR REPLACE FUNCTION public.create_sale_with_items(
  p_organization_id UUID,
  p_customer_id UUID,
  p_invoice_number TEXT,
  p_status TEXT,
  p_subtotal DECIMAL,
  p_tax_amount DECIMAL,
  p_discount_amount DECIMAL,
  p_total_amount DECIMAL,
  p_payment_method TEXT,
  p_notes TEXT,
  p_created_by UUID,
  p_items JSONB,
  p_loyalty_points_used INTEGER DEFAULT 0,
  p_loyalty_points_discount DECIMAL DEFAULT 0
) RETURNS JSONB AS $$
DECLARE
  v_sale_id UUID;
  v_item JSONB;
  v_product_id UUID;
  v_quantity INTEGER;
  v_base_quantity NUMERIC;
  v_uom_id UUID;
  v_loyalty_points_earned INTEGER := 0;
  v_customer_profile_id UUID;
BEGIN
  -- Calculate loyalty points earned if customer is provided and points are not being redeemed
  IF p_customer_id IS NOT NULL AND p_loyalty_points_used = 0 THEN
    -- Calculate points based on the total amount
    v_loyalty_points_earned := calculate_loyalty_points(
      p_organization_id, 
      p_customer_id, 
      NULL, -- Sale ID will be set later
      p_total_amount
    );
  END IF;

  -- Create the sale record
  INSERT INTO public.sales (
    organization_id,
    customer_id,
    invoice_number,
    sale_date,
    status,
    subtotal,
    tax_amount,
    discount_amount,
    total_amount,
    payment_method,
    notes,
    created_by,
    loyalty_points_used,
    loyalty_points_earned,
    loyalty_points_discount
  ) VALUES (
    p_organization_id,
    p_customer_id,
    p_invoice_number,
    NOW(),
    p_status,
    p_subtotal,
    p_tax_amount,
    p_discount_amount,
    p_total_amount,
    p_payment_method,
    p_notes,
    p_created_by,
    p_loyalty_points_used,
    v_loyalty_points_earned,
    p_loyalty_points_discount
  ) RETURNING id INTO v_sale_id;

  -- Process each item in the sale
  FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
  LOOP
    -- Extract values from the item JSON
    v_product_id := (v_item->>'product_id')::UUID;
    v_quantity := (v_item->>'quantity')::INTEGER;
    v_base_quantity := COALESCE((v_item->>'base_quantity')::NUMERIC, v_quantity);
    v_uom_id := (v_item->>'uom_id')::UUID;

    -- Create the sale item
    INSERT INTO public.sale_items (
      sale_id,
      product_id,
      quantity,
      unit_price,
      uom_id,
      base_quantity,
      tax_rate,
      tax_amount,
      discount_amount,
      total_amount,
      notes
    ) VALUES (
      v_sale_id,
      v_product_id,
      v_quantity,
      (v_item->>'unit_price')::DECIMAL,
      v_uom_id,
      v_base_quantity,
      COALESCE((v_item->>'tax_rate')::DECIMAL, 0),
      COALESCE((v_item->>'tax_amount')::DECIMAL, 0),
      COALESCE((v_item->>'discount_amount')::DECIMAL, 0),
      (v_item->>'unit_price')::DECIMAL * v_quantity - COALESCE((v_item->>'discount_amount')::DECIMAL, 0),
      v_item->>'notes'
    );

    -- Create inventory transaction for this sale item
    -- This will trigger the inventory update through the trigger
    INSERT INTO public.inventory_transactions (
      organization_id,
      product_id,
      transaction_type,
      quantity,
      uom_id,
      reference_id,
      reference_type,
      notes,
      created_by
    ) VALUES (
      p_organization_id,
      v_product_id,
      'sale',
      -v_base_quantity,  -- Negative quantity for sales
      v_uom_id,
      v_sale_id,
      'sale',
      'Sale: ' || p_invoice_number,
      p_created_by
    );
  END LOOP;

  -- Handle loyalty points if customer exists
  IF p_customer_id IS NOT NULL THEN
    -- Get or create customer loyalty profile
    SELECT id INTO v_customer_profile_id FROM customer_loyalty_profiles
    WHERE customer_id = p_customer_id AND organization_id = p_organization_id;

    IF v_customer_profile_id IS NULL THEN
      -- Create new loyalty profile for customer
      INSERT INTO customer_loyalty_profiles (
        customer_id,
        organization_id,
        current_points_balance,
        lifetime_points_earned,
        lifetime_points_redeemed
      ) VALUES (
        p_customer_id,
        p_organization_id,
        v_loyalty_points_earned, -- Initial balance is just the points earned
        v_loyalty_points_earned, -- Initial lifetime points
        0                       -- No points redeemed yet
      ) RETURNING id INTO v_customer_profile_id;
    ELSE
      -- Update existing loyalty profile
      UPDATE customer_loyalty_profiles
      SET 
        current_points_balance = current_points_balance + v_loyalty_points_earned - p_loyalty_points_used,
        lifetime_points_earned = lifetime_points_earned + v_loyalty_points_earned,
        lifetime_points_redeemed = lifetime_points_redeemed + p_loyalty_points_used,
        updated_at = NOW()
      WHERE id = v_customer_profile_id;
    END IF;

    -- Record loyalty points earned transaction if points were earned
    IF v_loyalty_points_earned > 0 THEN
      INSERT INTO loyalty_transactions (
        customer_id,
        organization_id,
        transaction_type,
        points,
        sale_id,
        notes,
        created_by
      ) VALUES (
        p_customer_id,
        p_organization_id,
        'earn',
        v_loyalty_points_earned,
        v_sale_id,
        'Points earned from sale ' || p_invoice_number,
        p_created_by
      );
    END IF;

    -- Record loyalty points redeemed transaction if points were used
    IF p_loyalty_points_used > 0 THEN
      INSERT INTO loyalty_transactions (
        customer_id,
        organization_id,
        transaction_type,
        points,
        sale_id,
        notes,
        created_by
      ) VALUES (
        p_customer_id,
        p_organization_id,
        'redeem',
        -p_loyalty_points_used, -- Negative points for redemption
        v_sale_id,
        'Points redeemed for discount on sale ' || p_invoice_number,
        p_created_by
      );
    END IF;
  END IF;

  -- Return the sale ID
  RETURN jsonb_build_object('sale_id', v_sale_id);
END;
$$ LANGUAGE plpgsql;
