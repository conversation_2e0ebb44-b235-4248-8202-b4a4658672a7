-- Fix the set_default_supplier_product_uom function to respect explicitly set UoM values
CREATE OR REPLACE FUNCTION set_default_supplier_product_uom()
RETURNS TRIGGER AS $$
BEGIN
  -- Log the incoming data for debugging
  RAISE NOTICE 'Supplier product trigger called with uom_id: %, conversion_factor: %', NEW.uom_id, NEW.conversion_factor;

  -- If organization_id is not provided, get it from the supplier
  IF NEW.organization_id IS NULL THEN
    SELECT s.organization_id INTO NEW.organization_id
    FROM public.suppliers s
    WHERE s.id = NEW.supplier_id
    LIMIT 1;

    -- If we couldn't get the organization_id, log an error
    IF NEW.organization_id IS NULL THEN
      RAISE EXCEPTION 'Could not determine organization_id for supplier_id %', NEW.supplier_id;
    END IF;
  END IF;

  -- Only set UoM if it's NULL - respect explicitly set UoM values
  IF NEW.uom_id IS NULL THEN
    -- First try to find a purchasing UoM
    SELECT pu.uom_id INTO NEW.uom_id
    FROM public.product_uoms pu
    WHERE pu.product_id = NEW.product_id
    AND pu.is_purchasing_unit = true
    LIMIT 1;

    -- If no purchasing UoM found, use the default UoM
    IF NEW.uom_id IS NULL THEN
      SELECT pu.uom_id INTO NEW.uom_id
      FROM public.product_uoms pu
      WHERE pu.product_id = NEW.product_id
      AND pu.is_default = true
      LIMIT 1;
    END IF;

    -- If still no UoM found, use the 'pieces' UoM from the organization
    IF NEW.uom_id IS NULL THEN
      SELECT u.id INTO NEW.uom_id
      FROM public.units_of_measurement u
      WHERE u.organization_id = NEW.organization_id
      AND u.code = 'pcs'
      LIMIT 1;
    END IF;
  END IF;

  -- Only set conversion_factor if it's NULL or 1 (default) and we have a UoM
  IF (NEW.conversion_factor IS NULL OR NEW.conversion_factor = 1) AND NEW.uom_id IS NOT NULL THEN
    -- Try to get the conversion factor from product_uoms
    SELECT pu.conversion_factor INTO NEW.conversion_factor
    FROM public.product_uoms pu
    WHERE pu.product_id = NEW.product_id
    AND pu.uom_id = NEW.uom_id
    LIMIT 1;

    -- If no conversion factor found, default to 1
    IF NEW.conversion_factor IS NULL THEN
      NEW.conversion_factor := 1;
    END IF;
  END IF;

  -- Set UoM name if it's NULL and we have a UoM
  IF NEW.uom_name IS NULL AND NEW.uom_id IS NOT NULL THEN
    SELECT CONCAT(name, ' (', code, ')') INTO NEW.uom_name
    FROM public.units_of_measurement
    WHERE id = NEW.uom_id;
  END IF;

  -- Log the final data for debugging
  RAISE NOTICE 'Supplier product trigger completed with uom_id: %, conversion_factor: %', NEW.uom_id, NEW.conversion_factor;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS set_supplier_product_uom ON public.supplier_products;

-- Create the trigger for INSERT operations
CREATE TRIGGER set_supplier_product_uom
BEFORE INSERT ON public.supplier_products
FOR EACH ROW
EXECUTE FUNCTION set_default_supplier_product_uom();

-- Create a trigger for UPDATE operations as well
CREATE TRIGGER update_supplier_product_uom
BEFORE UPDATE ON public.supplier_products
FOR EACH ROW
EXECUTE FUNCTION set_default_supplier_product_uom();
