-- Create a stored procedure to handle loyalty points adjustment
CREATE OR REPLACE FUNCTION public.adjust_loyalty_points(
  p_organization_id UUID,
  p_customer_id UUID,
  p_points INTEGER,
  p_notes TEXT,
  p_created_by UUID
) RETURNS VOID AS $$
DECLARE
  v_customer_profile_id UUID;
  v_current_balance INTEGER;
BEGIN
  -- Get or create customer loyalty profile
  SELECT id, current_points_balance INTO v_customer_profile_id, v_current_balance 
  FROM customer_loyalty_profiles
  WHERE customer_id = p_customer_id AND organization_id = p_organization_id;

  IF v_customer_profile_id IS NULL THEN
    -- Create new loyalty profile for customer
    INSERT INTO customer_loyalty_profiles (
      customer_id,
      organization_id,
      current_points_balance,
      lifetime_points_earned,
      lifetime_points_redeemed
    ) VALUES (
      p_customer_id,
      p_organization_id,
      CASE WHEN p_points > 0 THEN p_points ELSE 0 END, -- Initial balance
      CASE WHEN p_points > 0 THEN p_points ELSE 0 END, -- Initial lifetime points earned
      CASE WHEN p_points < 0 THEN ABS(p_points) ELSE 0 END -- Initial lifetime points redeemed
    ) RETURNING id INTO v_customer_profile_id;
  ELSE
    -- Update existing loyalty profile
    UPDATE customer_loyalty_profiles
    SET 
      current_points_balance = current_points_balance + p_points,
      lifetime_points_earned = CASE 
        WHEN p_points > 0 THEN lifetime_points_earned + p_points 
        ELSE lifetime_points_earned 
      END,
      lifetime_points_redeemed = CASE 
        WHEN p_points < 0 THEN lifetime_points_redeemed + ABS(p_points) 
        ELSE lifetime_points_redeemed 
      END,
      updated_at = NOW()
    WHERE id = v_customer_profile_id;
  END IF;

  -- Record loyalty transaction
  INSERT INTO loyalty_transactions (
    customer_id,
    organization_id,
    transaction_type,
    points,
    notes,
    created_by
  ) VALUES (
    p_customer_id,
    p_organization_id,
    'adjust',
    p_points,
    p_notes,
    p_created_by
  );
END;
$$ LANGUAGE plpgsql;
