-- Migration: Fix payroll deductions and allowances schema
-- Adds missing 'name' column and improves Philippines payroll compliance

-- =====================================================
-- 1. ADD MISSING NAME COLUMN TO PAYROLL DEDUCTIONS
-- =====================================================

-- Add name column to payroll_deductions table
ALTER TABLE public.payroll_deductions
ADD COLUMN IF NOT EXISTS name TEXT;

-- Update existing records to have proper names based on type
UPDATE public.payroll_deductions
SET name = CASE
    WHEN type = 'sss' THEN 'SSS Contribution'
    WHEN type = 'philhealth' THEN 'PhilHealth Contribution'
    WHEN type = 'pagibig' THEN 'Pag-IBIG Contribution'
    WHEN type = 'tax' THEN 'Withholding Tax'
    WHEN type = 'loan' THEN 'Loan Deduction'
    WHEN type = 'other' THEN COALESCE(description, 'Other Deduction')
    ELSE 'Unknown Deduction'
END
WHERE name IS NULL;

-- =====================================================
-- 2. ADD MISSING NAME COLUMN TO PAYROLL ALLOWANCES
-- =====================================================

-- Add name column to payroll_allowances table (for consistency)
ALTER TABLE public.payroll_allowances
ADD COLUMN IF NOT EXISTS name TEXT;

-- Update existing records to have proper names based on type
UPDATE public.payroll_allowances
SET name = CASE
    WHEN type = 'transportation' THEN 'Transportation Allowance'
    WHEN type = 'meal' THEN 'Meal Allowance'
    WHEN type = 'housing' THEN 'Housing Allowance'
    WHEN type = 'other' THEN COALESCE(description, 'Other Allowance')
    ELSE 'Unknown Allowance'
END
WHERE name IS NULL;

-- =====================================================
-- 3. ENHANCE DEDUCTION TYPES FOR PHILIPPINES COMPLIANCE
-- =====================================================

-- Update deduction types to include more Philippines-specific deductions
ALTER TABLE public.payroll_deductions
DROP CONSTRAINT IF EXISTS payroll_deductions_type_check;

ALTER TABLE public.payroll_deductions
ADD CONSTRAINT payroll_deductions_type_check
CHECK (type IN (
    'sss', 'philhealth', 'pagibig', 'tax', 'loan', 'advance',
    'uniform', 'tardiness', 'absence', 'other'
));

-- =====================================================
-- 4. ENHANCE ALLOWANCE TYPES FOR PHILIPPINES COMPLIANCE
-- =====================================================

-- Update allowance types to include more Philippines-specific allowances
ALTER TABLE public.payroll_allowances
DROP CONSTRAINT IF EXISTS payroll_allowances_type_check;

ALTER TABLE public.payroll_allowances
ADD CONSTRAINT payroll_allowances_type_check
CHECK (type IN (
    'transportation', 'meal', 'housing', 'communication', 'clothing',
    'overtime', 'night_differential', 'holiday', 'hazard', 'other'
));

-- =====================================================
-- 5. ADD EDITABLE GOVERNMENT CONTRIBUTIONS SUPPORT
-- =====================================================

-- Add columns to payroll_items for manual government contribution overrides
ALTER TABLE public.payroll_items
ADD COLUMN IF NOT EXISTS sss_contribution_override NUMERIC(12, 2);

ALTER TABLE public.payroll_items
ADD COLUMN IF NOT EXISTS philhealth_contribution_override NUMERIC(12, 2);

ALTER TABLE public.payroll_items
ADD COLUMN IF NOT EXISTS pagibig_contribution_override NUMERIC(12, 2);

ALTER TABLE public.payroll_items
ADD COLUMN IF NOT EXISTS withholding_tax_override NUMERIC(12, 2);

-- Add flag to indicate if contributions are manually overridden
ALTER TABLE public.payroll_items
ADD COLUMN IF NOT EXISTS contributions_manually_edited BOOLEAN DEFAULT FALSE;

-- =====================================================
-- 6. ADD PAYROLL APPROVAL WORKFLOW
-- =====================================================

-- Add approval tracking columns
ALTER TABLE public.payroll_periods
ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES auth.users(id);

ALTER TABLE public.payroll_periods
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMPTZ;

ALTER TABLE public.payroll_periods
ADD COLUMN IF NOT EXISTS payables_created BOOLEAN DEFAULT FALSE;

ALTER TABLE public.payroll_periods
ADD COLUMN IF NOT EXISTS payables_created_by UUID REFERENCES auth.users(id);

ALTER TABLE public.payroll_periods
ADD COLUMN IF NOT EXISTS payables_created_at TIMESTAMPTZ;

-- =====================================================
-- 7. ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON COLUMN public.payroll_deductions.name IS 'Human-readable name for the deduction (e.g., "SSS Contribution")';
COMMENT ON COLUMN public.payroll_allowances.name IS 'Human-readable name for the allowance (e.g., "Transportation Allowance")';

COMMENT ON COLUMN public.payroll_items.sss_contribution_override IS 'Manual override for SSS contribution amount';
COMMENT ON COLUMN public.payroll_items.philhealth_contribution_override IS 'Manual override for PhilHealth contribution amount';
COMMENT ON COLUMN public.payroll_items.pagibig_contribution_override IS 'Manual override for Pag-IBIG contribution amount';
COMMENT ON COLUMN public.payroll_items.withholding_tax_override IS 'Manual override for withholding tax amount';
COMMENT ON COLUMN public.payroll_items.contributions_manually_edited IS 'Flag indicating if government contributions were manually edited';

COMMENT ON COLUMN public.payroll_periods.approved_by IS 'User who approved the payroll period';
COMMENT ON COLUMN public.payroll_periods.approved_at IS 'Timestamp when payroll period was approved';
COMMENT ON COLUMN public.payroll_periods.payables_created IS 'Flag indicating if payables have been created for this period';
COMMENT ON COLUMN public.payroll_periods.payables_created_by IS 'User who created payables for this period';
COMMENT ON COLUMN public.payroll_periods.payables_created_at IS 'Timestamp when payables were created';

-- =====================================================
-- 8. EMPLOYEE CONTRIBUTION PREFERENCES
-- =====================================================

-- Create table to store employee-specific contribution preferences
CREATE TABLE IF NOT EXISTS public.employee_contribution_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    sss_contribution_override NUMERIC(12, 2),
    philhealth_contribution_override NUMERIC(12, 2),
    pagibig_contribution_override NUMERIC(12, 2),
    withholding_tax_override NUMERIC(12, 2),
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Ensure one active preference per employee per organization
    UNIQUE(organization_id, employee_id, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_employee_contribution_preferences_org_employee
ON public.employee_contribution_preferences(organization_id, employee_id);

CREATE INDEX IF NOT EXISTS idx_employee_contribution_preferences_active
ON public.employee_contribution_preferences(organization_id, employee_id, is_active)
WHERE is_active = TRUE;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_employee_contribution_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_employee_contribution_preferences_updated_at
    BEFORE UPDATE ON public.employee_contribution_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_employee_contribution_preferences_updated_at();

-- Add comments for documentation
COMMENT ON TABLE public.employee_contribution_preferences IS 'Stores employee-specific government contribution preferences for automatic application in future payroll periods';
COMMENT ON COLUMN public.employee_contribution_preferences.sss_contribution_override IS 'Employee-specific SSS contribution override amount';
COMMENT ON COLUMN public.employee_contribution_preferences.philhealth_contribution_override IS 'Employee-specific PhilHealth contribution override amount';
COMMENT ON COLUMN public.employee_contribution_preferences.pagibig_contribution_override IS 'Employee-specific Pag-IBIG contribution override amount';
COMMENT ON COLUMN public.employee_contribution_preferences.withholding_tax_override IS 'Employee-specific withholding tax override amount';
COMMENT ON COLUMN public.employee_contribution_preferences.is_active IS 'Flag indicating if this preference set is currently active';
COMMENT ON COLUMN public.employee_contribution_preferences.notes IS 'Optional notes about why these overrides are applied';

-- =====================================================
-- 9. VERIFICATION
-- =====================================================

-- Verify the changes
DO $$
BEGIN
    -- Check if name column exists in payroll_deductions
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'payroll_deductions'
        AND column_name = 'name'
    ) THEN
        RAISE EXCEPTION 'name column not added to payroll_deductions';
    END IF;

    -- Check if name column exists in payroll_allowances
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'payroll_allowances'
        AND column_name = 'name'
    ) THEN
        RAISE EXCEPTION 'name column not added to payroll_allowances';
    END IF;

    RAISE NOTICE 'Migration completed successfully: payroll schema enhanced for Philippines compliance';
END $$;
