-- Drop the function if it exists
DROP FUNCTION IF EXISTS create_organization(TEXT, TEXT, UUID);

-- Create a function to create an organization and bypass RLS
CREATE OR REPLACE FUNCTION create_organization(
  p_name TEXT,
  p_slug TEXT,
  p_user_id UUID
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER -- This is important to bypass RLS
AS $$
DECLARE
  v_org_id UUID;
BEGIN
  -- Insert the organization
  INSERT INTO public.organizations (name, slug)
  VALUES (p_name, p_slug)
  RETURNING id INTO v_org_id;

  -- Add the user as an owner
  INSERT INTO public.organization_members (organization_id, user_id, role)
  VALUES (v_org_id, p_user_id, 'owner');

  -- Create default settings
  INSERT INTO public.organization_settings (organization_id, settings)
  VALUES (v_org_id, '{
    "currency": "USD",
    "tax_rate": 0,
    "business_hours": {
      "monday": { "open": "09:00", "close": "17:00" },
      "tuesday": { "open": "09:00", "close": "17:00" },
      "wednesday": { "open": "09:00", "close": "17:00" },
      "thursday": { "open": "09:00", "close": "17:00" },
      "friday": { "open": "09:00", "close": "17:00" },
      "saturday": { "open": "", "close": "" },
      "sunday": { "open": "", "close": "" }
    }
  }'::jsonb);

  -- Create default supplier (if function exists)
  BEGIN
    PERFORM create_default_supplier(v_org_id);
  EXCEPTION WHEN undefined_function THEN
    -- Function doesn't exist yet, skip this step
    NULL;
  END;

  RETURN v_org_id;
END;
$$;
