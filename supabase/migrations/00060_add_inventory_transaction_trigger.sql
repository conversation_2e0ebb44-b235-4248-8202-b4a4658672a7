-- Migration to add trigger for creating inventory transactions from inventory receipt items

-- First, check the existing transaction_type constraint and modify if needed
DO $$
BEGIN
  -- Check if we need to add 'receipt' to the transaction_type check constraint
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint
    WHERE conname = 'inventory_transactions_transaction_type_check'
    AND conrelid = 'inventory_transactions'::regclass
    AND pg_get_constraintdef(oid) LIKE '%receipt%'
  ) THEN
    -- Add 'receipt' to the allowed transaction types
    ALTER TABLE inventory_transactions
    DROP CONSTRAINT IF EXISTS inventory_transactions_transaction_type_check;

    ALTER TABLE inventory_transactions
    ADD CONSTRAINT inventory_transactions_transaction_type_check
    CHECK (transaction_type IN ('purchase', 'sale', 'adjustment', 'transfer', 'receipt', 'return'));
  END IF;
END $$;

-- Create a function that will be called by the trigger
CREATE OR REPLACE FUNCTION create_inventory_transaction_from_receipt_item()
RETURNS TRIGGER AS $$
DECLARE
  receipt_record RECORD;
  transaction_type TEXT;
BEGIN
  -- Get the receipt information
  SELECT * INTO receipt_record FROM inventory_receipts WHERE id = NEW.inventory_receipt_id;

  -- Determine transaction type based on receipt status
  IF receipt_record.status = 'completed' THEN
    -- Try to use 'receipt' as the transaction type, but fall back to 'purchase' if needed
    -- This is in case the constraint hasn't been updated yet
    BEGIN
      transaction_type := 'receipt';
    EXCEPTION WHEN OTHERS THEN
      -- If 'receipt' is not allowed, use 'purchase' as a fallback
      transaction_type := 'purchase';
    END;
  ELSE
    -- Don't create transactions for draft receipts
    RETURN NEW;
  END IF;

  -- Calculate the base quantity using the conversion factor
  -- This is the quantity that will be used for inventory tracking
  -- NEW.base_quantity := NEW.quantity * NEW.conversion_factor;

  -- Insert a new record into inventory_transactions
  INSERT INTO inventory_transactions (
    organization_id,
    product_id,
    transaction_type,
    quantity,
    uom_id,
    reference_id,
    reference_type,
    notes,
    created_by,
    created_at
  ) VALUES (
    receipt_record.organization_id,
    NEW.product_id,
    transaction_type,
    NEW.base_quantity, -- Use the calculated base quantity
    NEW.uom_id,
    NEW.id, -- Reference to the receipt item
    'inventory_receipt_item',
    'Inventory receipt from ' || COALESCE(receipt_record.receipt_number, 'unknown receipt'),
    receipt_record.created_by,
    NOW()
  );

  -- Return the NEW record to complete the trigger
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger that fires after an insert on inventory_receipt_items
DROP TRIGGER IF EXISTS create_inventory_transaction_trigger ON inventory_receipt_items;
CREATE TRIGGER create_inventory_transaction_trigger
AFTER INSERT ON inventory_receipt_items
FOR EACH ROW
EXECUTE FUNCTION create_inventory_transaction_from_receipt_item();

-- Create a function to handle inventory receipt status changes
CREATE OR REPLACE FUNCTION handle_inventory_receipt_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- If the status changed from draft to completed
  IF OLD.status = 'draft' AND NEW.status = 'completed' THEN
    -- Create inventory transactions for all items in this receipt
    -- Try to use 'receipt' as the transaction type, but fall back to 'purchase' if needed
    DECLARE
      transaction_type TEXT;
    BEGIN
      -- Try to use 'receipt', but fall back to 'purchase' if needed
      BEGIN
        transaction_type := 'receipt';
      EXCEPTION WHEN OTHERS THEN
        transaction_type := 'purchase';
      END;

      INSERT INTO inventory_transactions (
        organization_id,
        product_id,
        transaction_type,
        quantity,
        uom_id,
        reference_id,
        reference_type,
        notes,
        created_by,
        created_at
      )
      SELECT
        NEW.organization_id,
        iri.product_id,
        transaction_type,
        iri.base_quantity,
        iri.uom_id,
        iri.id,
        'inventory_receipt_item',
        'Inventory receipt from ' || COALESCE(NEW.receipt_number, 'unknown receipt'),
        NEW.created_by,
        NOW()
    FROM inventory_receipt_items iri
    WHERE iri.inventory_receipt_id = NEW.id
    AND NOT EXISTS (
      -- Check if transaction already exists for this item
      SELECT 1 FROM inventory_transactions it
      WHERE it.reference_id = iri.id
      AND it.reference_type = 'inventory_receipt_item'
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger that fires after an update on inventory_receipts
DROP TRIGGER IF EXISTS handle_receipt_status_change_trigger ON inventory_receipts;
CREATE TRIGGER handle_receipt_status_change_trigger
AFTER UPDATE ON inventory_receipts
FOR EACH ROW
WHEN (OLD.status IS DISTINCT FROM NEW.status)
EXECUTE FUNCTION handle_inventory_receipt_status_change();

-- Add a function to update product stock quantities
CREATE OR REPLACE FUNCTION update_product_stock_from_transaction()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the product's stock quantity
  UPDATE products
  SET
    stock_quantity = COALESCE(stock_quantity, 0) + NEW.quantity,
    updated_at = NOW()
  WHERE id = NEW.product_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger that fires after an insert on inventory_transactions
DROP TRIGGER IF EXISTS update_product_stock_trigger ON inventory_transactions;
CREATE TRIGGER update_product_stock_trigger
AFTER INSERT ON inventory_transactions
FOR EACH ROW
EXECUTE FUNCTION update_product_stock_from_transaction();
