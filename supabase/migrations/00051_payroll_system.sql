-- Migration for Payroll System
-- This migration adds tables for payroll processing with Philippine tax laws compliance

-- Payroll Periods
CREATE TABLE IF NOT EXISTS public.payroll_periods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    payment_date DATE NOT NULL,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'approved', 'paid')),
    is_thirteenth_month BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Payroll Items (individual employee payslips)
CREATE TABLE IF NOT EXISTS public.payroll_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    payroll_period_id UUID NOT NULL REFERENCES public.payroll_periods(id) ON DELETE CASCADE,
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'calculated', 'approved', 'paid')),
    basic_pay NUMERIC(12, 2) NOT NULL DEFAULT 0,
    gross_pay NUMERIC(12, 2) NOT NULL DEFAULT 0,
    net_pay NUMERIC(12, 2) NOT NULL DEFAULT 0,
    total_deductions NUMERIC(12, 2) NOT NULL DEFAULT 0,
    total_allowances NUMERIC(12, 2) NOT NULL DEFAULT 0,
    total_overtime NUMERIC(12, 2) NOT NULL DEFAULT 0,
    regular_hours NUMERIC(8, 2) NOT NULL DEFAULT 0,
    overtime_hours NUMERIC(8, 2) NOT NULL DEFAULT 0,
    absent_days NUMERIC(5, 2) NOT NULL DEFAULT 0,
    late_minutes NUMERIC(8, 2) NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (payroll_period_id, employee_id)
);

-- Payroll Deductions
CREATE TABLE IF NOT EXISTS public.payroll_deductions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    payroll_item_id UUID NOT NULL REFERENCES public.payroll_items(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('sss', 'philhealth', 'pagibig', 'tax', 'loan', 'other')),
    amount NUMERIC(12, 2) NOT NULL DEFAULT 0,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Payroll Allowances
CREATE TABLE IF NOT EXISTS public.payroll_allowances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    payroll_item_id UUID NOT NULL REFERENCES public.payroll_items(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('transportation', 'meal', 'housing', 'other')),
    amount NUMERIC(12, 2) NOT NULL DEFAULT 0,
    description TEXT,
    taxable BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Payroll Settings
CREATE TABLE IF NOT EXISTS public.payroll_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    pay_schedule TEXT NOT NULL DEFAULT 'semi-monthly' CHECK (pay_schedule IN ('monthly', 'semi-monthly', 'weekly')),
    semi_monthly_days JSONB DEFAULT '[15, 30]'::jsonb,
    pay_based_on_time_entries BOOLEAN NOT NULL DEFAULT true,
    sss_employer_contribution_rate NUMERIC(5, 2) NOT NULL DEFAULT 8.5,
    philhealth_employer_contribution_rate NUMERIC(5, 2) NOT NULL DEFAULT 2.0,
    pagibig_employer_contribution_rate NUMERIC(5, 2) NOT NULL DEFAULT 2.0,
    tax_table_version TEXT NOT NULL DEFAULT '2023',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (organization_id)
);

-- Time Entries
CREATE TABLE IF NOT EXISTS public.time_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    time_in TIMESTAMPTZ,
    time_out TIMESTAMPTZ,
    break_start TIMESTAMPTZ,
    break_end TIMESTAMPTZ,
    status TEXT NOT NULL DEFAULT 'present' CHECK (status IN ('present', 'absent', 'leave', 'holiday')),
    regular_hours NUMERIC(8, 2) DEFAULT 0,
    overtime_hours NUMERIC(8, 2) DEFAULT 0,
    night_diff_hours NUMERIC(8, 2) DEFAULT 0,
    is_rest_day BOOLEAN NOT NULL DEFAULT false,
    is_holiday BOOLEAN NOT NULL DEFAULT false,
    holiday_type TEXT CHECK (holiday_type IN ('regular', 'special')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (employee_id, date)
);

-- Employee Salary
CREATE TABLE IF NOT EXISTS public.employee_salary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    rate_type TEXT NOT NULL CHECK (rate_type IN ('monthly', 'daily', 'hourly')),
    rate_amount NUMERIC(12, 2) NOT NULL,
    effective_date DATE NOT NULL,
    end_date DATE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Employee Allowances
CREATE TABLE IF NOT EXISTS public.employee_allowances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('transportation', 'meal', 'housing', 'other')),
    amount NUMERIC(12, 2) NOT NULL,
    frequency TEXT NOT NULL CHECK (frequency IN ('per_payroll', 'monthly', 'annual')),
    taxable BOOLEAN NOT NULL DEFAULT true,
    description TEXT,
    effective_date DATE NOT NULL,
    end_date DATE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Employee Deductions
CREATE TABLE IF NOT EXISTS public.employee_deductions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    employee_id UUID NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('loan', 'advance', 'other')),
    amount NUMERIC(12, 2) NOT NULL,
    frequency TEXT NOT NULL CHECK (frequency IN ('per_payroll', 'monthly', 'one_time')),
    remaining_amount NUMERIC(12, 2),
    description TEXT,
    effective_date DATE NOT NULL,
    end_date DATE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Tax Tables
CREATE TABLE IF NOT EXISTS public.tax_tables (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    version TEXT NOT NULL,
    income_from NUMERIC(12, 2) NOT NULL,
    income_to NUMERIC(12, 2) NOT NULL,
    base_tax NUMERIC(12, 2) NOT NULL,
    excess_rate NUMERIC(5, 2) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (version, income_from, income_to)
);

-- SSS Contribution Table
CREATE TABLE IF NOT EXISTS public.sss_contribution_table (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    version TEXT NOT NULL,
    salary_from NUMERIC(12, 2) NOT NULL,
    salary_to NUMERIC(12, 2) NOT NULL,
    employee_contribution NUMERIC(12, 2) NOT NULL,
    employer_contribution NUMERIC(12, 2) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (version, salary_from, salary_to)
);

-- PhilHealth Contribution Table
CREATE TABLE IF NOT EXISTS public.philhealth_contribution_table (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    version TEXT NOT NULL,
    salary_from NUMERIC(12, 2) NOT NULL,
    salary_to NUMERIC(12, 2) NOT NULL,
    employee_contribution_rate NUMERIC(5, 2) NOT NULL,
    employer_contribution_rate NUMERIC(5, 2) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (version, salary_from, salary_to)
);

-- Pag-IBIG Contribution Table
CREATE TABLE IF NOT EXISTS public.pagibig_contribution_table (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    version TEXT NOT NULL,
    salary_from NUMERIC(12, 2) NOT NULL,
    salary_to NUMERIC(12, 2) NOT NULL,
    employee_contribution_rate NUMERIC(5, 2) NOT NULL,
    employer_contribution_rate NUMERIC(5, 2) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (version, salary_from, salary_to)
);

-- Holidays
CREATE TABLE IF NOT EXISTS public.holidays (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    date DATE NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('regular', 'special')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (organization_id, date)
);

-- Note: RLS policies are intentionally disabled for now to make development and testing easier
-- We will implement proper RLS policies later

-- Disable RLS for all payroll tables
ALTER TABLE public.payroll_periods DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.payroll_items DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.payroll_deductions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.payroll_allowances DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.payroll_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_entries DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.employee_salary DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.employee_allowances DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.employee_deductions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.tax_tables DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.sss_contribution_table DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.philhealth_contribution_table DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.pagibig_contribution_table DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.holidays DISABLE ROW LEVEL SECURITY;

-- RLS policies will be implemented in a future migration
-- This allows anyone to process payroll data during development
