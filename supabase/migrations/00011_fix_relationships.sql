-- Add explicit foreign key relationship between organization_members and profiles if it doesn't exist
DO $$
BEGIN
    -- Check if the constraint already exists
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'organization_members_user_id_fkey'
    ) THEN
        -- Add the constraint if it doesn't exist
        ALTER TABLE public.organization_members
        ADD CONSTRAINT organization_members_user_id_fkey
        FOREIGN KEY (user_id)
        REFERENCES public.profiles(id)
        ON DELETE CASCADE;
    END IF;
END $$;

-- Update the README to include this migration
COMMENT ON TABLE public.organization_members IS 'Members of organizations with their roles';
COMMENT ON TABLE public.profiles IS 'User profiles with personal information';

-- Add explicit comment on the relationship if the constraint exists
DO $$
BEGIN
    -- Check if the constraint exists before adding a comment
    IF EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'organization_members_user_id_fkey'
    ) THEN
        -- Add the comment if the constraint exists
        COMMENT ON CONSTRAINT organization_members_user_id_fkey ON public.organization_members IS 'Each organization member must have a profile';
    END IF;
END $$;
