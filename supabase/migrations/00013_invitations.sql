-- Create a table to store user invitations
CREATE TABLE IF NOT EXISTS public.invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member', 'cashier', 'inventory_manager', 'purchaser', 'employee')),
    invited_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMPTZ NOT NULL,
    accepted_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (organization_id, email)
);

-- Create trigger for updated_at column
CREATE TRIGGER set_updated_at
BEFORE UPDATE ON public.invitations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on the invitations table
ALTER TABLE public.invitations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for invitations
CREATE POLICY "Users can view invitations for their organizations" 
ON public.invitations 
FOR SELECT 
USING (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Users with proper permissions can create invitations" 
ON public.invitations 
FOR INSERT 
WITH CHECK (
    -- Check if the user has permission to invite users
    EXISTS (
        SELECT 1
        FROM public.organization_members
        WHERE organization_id = organization_id
        AND user_id = auth.uid()
        AND role IN ('owner', 'admin')
    )
);

CREATE POLICY "Users with proper permissions can update invitations" 
ON public.invitations 
FOR UPDATE 
USING (
    -- Check if the user has permission to update invitations
    EXISTS (
        SELECT 1
        FROM public.organization_members
        WHERE organization_id = organization_id
        AND user_id = auth.uid()
        AND role IN ('owner', 'admin')
    )
);

CREATE POLICY "Users with proper permissions can delete invitations" 
ON public.invitations 
FOR DELETE 
USING (
    -- Check if the user has permission to delete invitations
    EXISTS (
        SELECT 1
        FROM public.organization_members
        WHERE organization_id = organization_id
        AND user_id = auth.uid()
        AND role IN ('owner', 'admin')
    )
);

-- Create a function to generate a secure invitation token
CREATE OR REPLACE FUNCTION public.generate_invitation_token()
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    v_token TEXT;
BEGIN
    -- Generate a random token (32 characters)
    SELECT encode(gen_random_bytes(24), 'hex') INTO v_token;
    RETURN v_token;
END;
$$;

-- Create a function to create an invitation and send an email
CREATE OR REPLACE FUNCTION public.invite_user(
    p_organization_id UUID,
    p_email TEXT,
    p_role TEXT,
    p_invited_by UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_token TEXT;
    v_expires_at TIMESTAMPTZ;
    v_organization_name TEXT;
    v_inviter_name TEXT;
    v_invitation_id UUID;
    v_result JSONB;
BEGIN
    -- Check if the user has permission to invite users
    IF NOT EXISTS (
        SELECT 1
        FROM public.organization_members
        WHERE organization_id = p_organization_id
        AND user_id = p_invited_by
        AND role IN ('owner', 'admin')
    ) THEN
        RETURN jsonb_build_object(
            'success', FALSE,
            'error', 'You do not have permission to invite users'
        );
    END IF;
    
    -- Get the organization name
    SELECT name INTO v_organization_name
    FROM public.organizations
    WHERE id = p_organization_id;
    
    -- Get the inviter's name
    SELECT first_name || ' ' || last_name INTO v_inviter_name
    FROM public.profiles
    WHERE id = p_invited_by;
    
    -- Generate a token and expiration date (48 hours from now)
    SELECT public.generate_invitation_token() INTO v_token;
    SELECT NOW() + INTERVAL '48 hours' INTO v_expires_at;
    
    -- Create the invitation
    INSERT INTO public.invitations (
        organization_id,
        email,
        role,
        invited_by,
        token,
        expires_at
    )
    VALUES (
        p_organization_id,
        p_email,
        p_role,
        p_invited_by,
        v_token,
        v_expires_at
    )
    RETURNING id INTO v_invitation_id;
    
    -- In a real implementation, you would send an email here
    -- For now, we'll just return the invitation details
    
    RETURN jsonb_build_object(
        'success', TRUE,
        'invitation_id', v_invitation_id,
        'token', v_token,
        'email', p_email,
        'organization_name', v_organization_name,
        'inviter_name', v_inviter_name,
        'expires_at', v_expires_at
    );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.invite_user(UUID, TEXT, TEXT, UUID) TO authenticated;
