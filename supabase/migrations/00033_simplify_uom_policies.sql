-- Drop existing RLS policies for units_of_measurement
DROP POLICY IF EXISTS "Users can view their organization's units of measurement" ON public.units_of_measurement;
DROP POLICY IF EXISTS "Users can create units of measurement in their organization" ON public.units_of_measurement;
DROP POLICY IF EXISTS "Only owners and admins can update units of measurement" ON public.units_of_measurement;
DROP POLICY IF EXISTS "Only owners and admins can delete units of measurement" ON public.units_of_measurement;

-- Create a single permissive policy for units_of_measurement
CREATE POLICY "Allow all authenticated users to manage units of measurement"
ON public.units_of_measurement
FOR ALL
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');

-- Drop existing RLS policies for product_uoms
DROP POLICY IF EXISTS "Users can view their organization's product UoMs" ON public.product_uoms;
DROP POLICY IF EXISTS "Users can create product UoMs in their organization" ON public.product_uoms;
DROP POLICY IF EXISTS "Only owners and admins can update product UoMs" ON public.product_uoms;
DROP POLICY IF EXISTS "Only owners and admins can delete product UoMs" ON public.product_uoms;

-- Create a single permissive policy for product_uoms
CREATE POLICY "Allow all authenticated users to manage product UoMs"
ON public.product_uoms
FOR ALL
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');

-- Keep the organization_id column and constraints for data integrity
-- but make the policies more permissive for now

-- Comment out the trigger that enforces organization matching
-- We'll keep the trigger function for future use, but disable the trigger itself
DROP TRIGGER IF EXISTS ensure_matching_organization ON public.product_uoms;

-- Create a simpler trigger that just sets the organization_id
CREATE OR REPLACE FUNCTION set_product_uom_organization()
RETURNS TRIGGER AS $$
DECLARE
  product_org_id UUID;
BEGIN
  -- Get the organization_id of the product
  SELECT organization_id INTO product_org_id
  FROM public.products
  WHERE id = NEW.product_id;
  
  -- Set the organization_id to match the product's organization
  NEW.organization_id := product_org_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER set_product_uom_organization_trigger
BEFORE INSERT OR UPDATE ON public.product_uoms
FOR EACH ROW
EXECUTE FUNCTION set_product_uom_organization();
