-- Disable <PERSON><PERSON> for refund tables during development
-- Following the established practice of disabling <PERSON><PERSON> during development
-- This migration should be run after 00113_fix_refund_constraints.sql

-- Disable RLS for refund tables
ALTER TABLE public.refunds DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.refund_items DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.store_credits DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.store_credit_transactions DISABLE ROW LEVEL SECURITY;

-- Drop any existing policies (if any)
DROP POLICY IF EXISTS "refunds_policy" ON public.refunds;
DROP POLICY IF EXISTS "refund_items_policy" ON public.refund_items;
DROP POLICY IF EXISTS "store_credits_policy" ON public.store_credits;
DROP POLICY IF EXISTS "store_credit_transactions_policy" ON public.store_credit_transactions;
