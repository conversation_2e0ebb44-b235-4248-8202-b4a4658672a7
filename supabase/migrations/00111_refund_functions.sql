-- Create refund tables and functions for the POS system
-- This migration creates the complete refund functionality

-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS generate_refund_number(UUID);
DROP FUNCTION IF EXISTS generate_credit_number(UUID);
DROP FUNCTION IF EXISTS update_product_stock(UUID, INTEGER);

-- Create refunds table
CREATE TABLE IF NOT EXISTS public.refunds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    refund_number TEXT NOT NULL,
    original_sale_id UUID NOT NULL REFERENCES public.sales(id) ON DELETE RESTRICT,
    customer_id UUID REFERENCES public.customers(id) ON DELETE SET NULL,

    -- Refund details
    refund_type TEXT NOT NULL CHECK (refund_type IN ('full', 'partial', 'exchange', 'store_credit')),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'processed', 'cancelled')),
    reason TEXT NOT NULL CHECK (reason IN ('defective', 'wrong_item', 'customer_changed_mind', 'damaged_in_transit', 'not_as_described', 'duplicate_order', 'other')),
    reason_notes TEXT,

    -- Financial details
    subtotal DECIMAL(12,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    restocking_fee DECIMAL(12,2) NOT NULL DEFAULT 0,
    refund_method TEXT NOT NULL CHECK (refund_method IN ('cash', 'card', 'store_credit', 'original_payment')),

    -- Approval workflow
    requires_approval BOOLEAN NOT NULL DEFAULT false,
    approved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    approved_at TIMESTAMPTZ,
    approval_notes TEXT,

    -- Processing details
    processed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    processed_at TIMESTAMPTZ,

    -- Audit fields
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- Constraints
    UNIQUE(organization_id, refund_number)
);

-- Create refund_items table
CREATE TABLE IF NOT EXISTS public.refund_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    refund_id UUID NOT NULL REFERENCES public.refunds(id) ON DELETE CASCADE,
    sale_item_id UUID NOT NULL REFERENCES public.sale_items(id) ON DELETE RESTRICT,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE RESTRICT,

    -- Item details
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,

    -- Return condition
    condition TEXT NOT NULL DEFAULT 'new' CHECK (condition IN ('new', 'used', 'damaged', 'defective')),
    restore_inventory BOOLEAN NOT NULL DEFAULT true,
    notes TEXT,

    -- Audit fields
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create store_credits table for store credit refunds
CREATE TABLE IF NOT EXISTS public.store_credits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
    refund_id UUID REFERENCES public.refunds(id) ON DELETE SET NULL,

    -- Credit details
    credit_number TEXT NOT NULL,
    original_amount DECIMAL(12,2) NOT NULL,
    remaining_balance DECIMAL(12,2) NOT NULL,

    -- Validity
    issued_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    is_active BOOLEAN NOT NULL DEFAULT true,

    -- Audit fields
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- Constraints
    UNIQUE(organization_id, credit_number),
    CHECK (remaining_balance >= 0),
    CHECK (remaining_balance <= original_amount)
);

-- Create store_credit_transactions table for tracking credit usage
CREATE TABLE IF NOT EXISTS public.store_credit_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_credit_id UUID NOT NULL REFERENCES public.store_credits(id) ON DELETE CASCADE,
    sale_id UUID REFERENCES public.sales(id) ON DELETE SET NULL,

    -- Transaction details
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('issued', 'used', 'expired', 'cancelled')),
    amount DECIMAL(12,2) NOT NULL,
    balance_before DECIMAL(12,2) NOT NULL,
    balance_after DECIMAL(12,2) NOT NULL,
    notes TEXT,

    -- Audit fields
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_refunds_organization_id ON public.refunds(organization_id);
CREATE INDEX IF NOT EXISTS idx_refunds_original_sale_id ON public.refunds(original_sale_id);
CREATE INDEX IF NOT EXISTS idx_refunds_customer_id ON public.refunds(customer_id);
CREATE INDEX IF NOT EXISTS idx_refunds_status ON public.refunds(status);
CREATE INDEX IF NOT EXISTS idx_refunds_created_at ON public.refunds(created_at);
CREATE INDEX IF NOT EXISTS idx_refunds_refund_number ON public.refunds(organization_id, refund_number);

CREATE INDEX IF NOT EXISTS idx_refund_items_refund_id ON public.refund_items(refund_id);
CREATE INDEX IF NOT EXISTS idx_refund_items_sale_item_id ON public.refund_items(sale_item_id);
CREATE INDEX IF NOT EXISTS idx_refund_items_product_id ON public.refund_items(product_id);

CREATE INDEX IF NOT EXISTS idx_store_credits_organization_id ON public.store_credits(organization_id);
CREATE INDEX IF NOT EXISTS idx_store_credits_customer_id ON public.store_credits(customer_id);
CREATE INDEX IF NOT EXISTS idx_store_credits_credit_number ON public.store_credits(organization_id, credit_number);
CREATE INDEX IF NOT EXISTS idx_store_credits_active ON public.store_credits(is_active);

CREATE INDEX IF NOT EXISTS idx_store_credit_transactions_store_credit_id ON public.store_credit_transactions(store_credit_id);
CREATE INDEX IF NOT EXISTS idx_store_credit_transactions_sale_id ON public.store_credit_transactions(sale_id);
CREATE INDEX IF NOT EXISTS idx_store_credit_transactions_created_at ON public.store_credit_transactions(created_at);

-- Function to generate refund numbers
CREATE OR REPLACE FUNCTION generate_refund_number(org_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  next_number INTEGER;
  new_refund_number TEXT;
  current_year TEXT;
BEGIN
  -- Get current year
  current_year := EXTRACT(YEAR FROM NOW())::TEXT;

  -- Get the next refund number for this organization and year
  SELECT COALESCE(MAX(
    CASE
      WHEN r.refund_number ~ ('^REF-' || current_year || '-[0-9]+$')
      THEN CAST(SUBSTRING(r.refund_number FROM LENGTH('REF-' || current_year || '-') + 1) AS INTEGER)
      ELSE 0
    END
  ), 0) + 1
  INTO next_number
  FROM public.refunds r
  WHERE r.organization_id = org_id;

  -- Format: REF-YYYY-NNNN
  new_refund_number := 'REF-' || current_year || '-' || LPAD(next_number::TEXT, 4, '0');

  RETURN new_refund_number;
END;
$$;

-- Function to generate store credit numbers
CREATE OR REPLACE FUNCTION generate_credit_number(org_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  next_number INTEGER;
  new_credit_number TEXT;
  current_year TEXT;
BEGIN
  -- Get current year
  current_year := EXTRACT(YEAR FROM NOW())::TEXT;

  -- Get the next credit number for this organization and year
  SELECT COALESCE(MAX(
    CASE
      WHEN sc.credit_number ~ ('^SC-' || current_year || '-[0-9]+$')
      THEN CAST(SUBSTRING(sc.credit_number FROM LENGTH('SC-' || current_year || '-') + 1) AS INTEGER)
      ELSE 0
    END
  ), 0) + 1
  INTO next_number
  FROM public.store_credits sc
  WHERE sc.organization_id = org_id;

  -- Format: SC-YYYY-NNNN
  new_credit_number := 'SC-' || current_year || '-' || LPAD(next_number::TEXT, 4, '0');

  RETURN new_credit_number;
END;
$$;

-- Function to handle refund inventory transactions based on item condition
CREATE OR REPLACE FUNCTION process_refund_inventory(
  product_id UUID,
  quantity INTEGER,
  item_condition TEXT,
  restore_inventory BOOLEAN,
  refund_id UUID,
  user_id UUID DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  org_id UUID;
  current_user_id UUID;
  transaction_type TEXT;
  stock_change INTEGER := 0;
  notes_text TEXT;
BEGIN
  -- Get organization ID from product
  SELECT organization_id INTO org_id FROM products WHERE id = product_id;

  -- Use provided user_id or get current user
  current_user_id := COALESCE(user_id, auth.uid());

  -- Determine transaction type and stock change based on condition
  CASE item_condition
    WHEN 'new' THEN
      IF restore_inventory THEN
        transaction_type := 'return';
        stock_change := quantity;
        notes_text := 'Refund - Item returned in new condition, restored to inventory';
      ELSE
        transaction_type := 'adjustment';
        stock_change := 0;
        notes_text := 'Refund - Item in new condition but not restored to inventory';
      END IF;
    WHEN 'used' THEN
      IF restore_inventory THEN
        transaction_type := 'return';
        stock_change := quantity;
        notes_text := 'Refund - Used item returned, restored to inventory';
      ELSE
        transaction_type := 'adjustment';
        stock_change := 0;
        notes_text := 'Refund - Used item returned but not restored to inventory';
      END IF;
    WHEN 'damaged' THEN
      transaction_type := 'adjustment';
      stock_change := 0;
      notes_text := 'Refund - Damaged item, sent for disposal/repair, not restored to inventory';
    WHEN 'defective' THEN
      transaction_type := 'adjustment';
      stock_change := 0;
      notes_text := 'Refund - Defective item, sent for warranty/disposal, not restored to inventory';
    ELSE
      transaction_type := 'adjustment';
      stock_change := 0;
      notes_text := 'Refund - Unknown condition, not restored to inventory';
  END CASE;

  -- Update product stock only if we're restoring inventory
  IF stock_change > 0 THEN
    UPDATE products
    SET stock_quantity = COALESCE(stock_quantity, 0) + stock_change,
        updated_at = NOW()
    WHERE id = product_id;
  END IF;

  -- Always create an inventory transaction record for tracking
  INSERT INTO inventory_transactions (
    organization_id,
    product_id,
    transaction_type,
    quantity,
    reference_id,
    reference_type,
    notes,
    created_by,
    created_at
  ) VALUES (
    org_id,
    product_id,
    transaction_type,
    quantity, -- Always record the actual quantity being processed
    refund_id,
    'refund',
    notes_text || ' (Refund ID: ' || refund_id || ')',
    current_user_id,
    NOW()
  );
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION generate_refund_number TO authenticated;
GRANT EXECUTE ON FUNCTION generate_credit_number TO authenticated;
GRANT EXECUTE ON FUNCTION process_refund_inventory TO authenticated;
