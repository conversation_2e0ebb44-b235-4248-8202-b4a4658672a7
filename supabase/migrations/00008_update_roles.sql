-- Update the role check constraint in organization_members table
ALTER TABLE public.organization_members DROP CONSTRAINT IF EXISTS organization_members_role_check;
ALTER TABLE public.organization_members ADD CONSTRAINT organization_members_role_check 
CHECK (role IN ('owner', 'admin', 'member', 'cashier', 'inventory_manager', 'purchaser', 'employee'));

-- Create a table to store role permissions
CREATE TABLE IF NOT EXISTS public.role_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member', 'cashier', 'inventory_manager', 'purchaser', 'employee')),
    permissions JSONB NOT NULL DEFAULT '{}'::JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (organization_id, role)
);

-- Create trigger for updated_at column
CREATE TRIGGER set_updated_at
BEFORE UPDATE ON public.role_permissions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on the new table
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;

-- Insert default permissions for each role
INSERT INTO public.role_permissions (organization_id, role, permissions)
SELECT 
    id AS organization_id,
    'owner',
    '{
        "dashboard": {"view": true},
        "products": {"view": true, "create": true, "update": true, "delete": true},
        "inventory": {"view": true, "create": true, "update": true, "delete": true},
        "sales": {"view": true, "create": true, "update": true, "delete": true, "process": true},
        "purchases": {"view": true, "create": true, "update": true, "delete": true, "approve": true},
        "customers": {"view": true, "create": true, "update": true, "delete": true},
        "suppliers": {"view": true, "create": true, "update": true, "delete": true},
        "reports": {"view": true, "export": true},
        "users": {"view": true, "create": true, "update": true, "delete": true, "change_role": true, "reset_password": true},
        "settings": {"view": true, "update": true}
    }'::JSONB
FROM public.organizations
ON CONFLICT (organization_id, role) DO NOTHING;

INSERT INTO public.role_permissions (organization_id, role, permissions)
SELECT 
    id AS organization_id,
    'admin',
    '{
        "dashboard": {"view": true},
        "products": {"view": true, "create": true, "update": true, "delete": true},
        "inventory": {"view": true, "create": true, "update": true, "delete": true},
        "sales": {"view": true, "create": true, "update": true, "delete": true, "process": true},
        "purchases": {"view": true, "create": true, "update": true, "delete": true, "approve": true},
        "customers": {"view": true, "create": true, "update": true, "delete": true},
        "suppliers": {"view": true, "create": true, "update": true, "delete": true},
        "reports": {"view": true, "export": true},
        "users": {"view": true, "create": true, "update": true, "delete": false, "change_role": true, "reset_password": true},
        "settings": {"view": true, "update": true}
    }'::JSONB
FROM public.organizations
ON CONFLICT (organization_id, role) DO NOTHING;

INSERT INTO public.role_permissions (organization_id, role, permissions)
SELECT 
    id AS organization_id,
    'cashier',
    '{
        "dashboard": {"view": true},
        "products": {"view": true, "create": false, "update": false, "delete": false},
        "inventory": {"view": true, "create": false, "update": false, "delete": false},
        "sales": {"view": true, "create": true, "update": true, "delete": false, "process": true},
        "purchases": {"view": false, "create": false, "update": false, "delete": false, "approve": false},
        "customers": {"view": true, "create": true, "update": true, "delete": false},
        "suppliers": {"view": false, "create": false, "update": false, "delete": false},
        "reports": {"view": true, "export": false},
        "users": {"view": false, "create": false, "update": false, "delete": false, "change_role": false, "reset_password": false},
        "settings": {"view": false, "update": false}
    }'::JSONB
FROM public.organizations
ON CONFLICT (organization_id, role) DO NOTHING;

INSERT INTO public.role_permissions (organization_id, role, permissions)
SELECT 
    id AS organization_id,
    'inventory_manager',
    '{
        "dashboard": {"view": true},
        "products": {"view": true, "create": true, "update": true, "delete": false},
        "inventory": {"view": true, "create": true, "update": true, "delete": false},
        "sales": {"view": true, "create": false, "update": false, "delete": false, "process": false},
        "purchases": {"view": true, "create": true, "update": true, "delete": false, "approve": false},
        "customers": {"view": false, "create": false, "update": false, "delete": false},
        "suppliers": {"view": true, "create": true, "update": true, "delete": false},
        "reports": {"view": true, "export": true},
        "users": {"view": false, "create": false, "update": false, "delete": false, "change_role": false, "reset_password": false},
        "settings": {"view": false, "update": false}
    }'::JSONB
FROM public.organizations
ON CONFLICT (organization_id, role) DO NOTHING;

INSERT INTO public.role_permissions (organization_id, role, permissions)
SELECT 
    id AS organization_id,
    'purchaser',
    '{
        "dashboard": {"view": true},
        "products": {"view": true, "create": false, "update": false, "delete": false},
        "inventory": {"view": true, "create": false, "update": false, "delete": false},
        "sales": {"view": false, "create": false, "update": false, "delete": false, "process": false},
        "purchases": {"view": true, "create": true, "update": true, "delete": false, "approve": false},
        "customers": {"view": false, "create": false, "update": false, "delete": false},
        "suppliers": {"view": true, "create": true, "update": true, "delete": false},
        "reports": {"view": true, "export": false},
        "users": {"view": false, "create": false, "update": false, "delete": false, "change_role": false, "reset_password": false},
        "settings": {"view": false, "update": false}
    }'::JSONB
FROM public.organizations
ON CONFLICT (organization_id, role) DO NOTHING;

INSERT INTO public.role_permissions (organization_id, role, permissions)
SELECT 
    id AS organization_id,
    'employee',
    '{
        "dashboard": {"view": true},
        "products": {"view": true, "create": false, "update": false, "delete": false},
        "inventory": {"view": true, "create": false, "update": false, "delete": false},
        "sales": {"view": true, "create": false, "update": false, "delete": false, "process": false},
        "purchases": {"view": false, "create": false, "update": false, "delete": false, "approve": false},
        "customers": {"view": true, "create": false, "update": false, "delete": false},
        "suppliers": {"view": false, "create": false, "update": false, "delete": false},
        "reports": {"view": false, "export": false},
        "users": {"view": false, "create": false, "update": false, "delete": false, "change_role": false, "reset_password": false},
        "settings": {"view": false, "update": false}
    }'::JSONB
FROM public.organizations
ON CONFLICT (organization_id, role) DO NOTHING;

INSERT INTO public.role_permissions (organization_id, role, permissions)
SELECT 
    id AS organization_id,
    'member',
    '{
        "dashboard": {"view": true},
        "products": {"view": true, "create": false, "update": false, "delete": false},
        "inventory": {"view": true, "create": false, "update": false, "delete": false},
        "sales": {"view": true, "create": false, "update": false, "delete": false, "process": false},
        "purchases": {"view": false, "create": false, "update": false, "delete": false, "approve": false},
        "customers": {"view": true, "create": false, "update": false, "delete": false},
        "suppliers": {"view": false, "create": false, "update": false, "delete": false},
        "reports": {"view": false, "export": false},
        "users": {"view": false, "create": false, "update": false, "delete": false, "change_role": false, "reset_password": false},
        "settings": {"view": false, "update": false}
    }'::JSONB
FROM public.organizations
ON CONFLICT (organization_id, role) DO NOTHING;

-- Create RLS policies for role_permissions
CREATE POLICY "Users can view role permissions in their organizations" 
ON public.role_permissions 
FOR SELECT 
USING (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Owners and admins can update role permissions" 
ON public.role_permissions 
FOR UPDATE 
USING (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
);

CREATE POLICY "Owners and admins can insert role permissions" 
ON public.role_permissions 
FOR INSERT 
WITH CHECK (
    organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
);
