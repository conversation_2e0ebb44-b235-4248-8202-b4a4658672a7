-- Create return_orders table
CREATE TABLE public.return_orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL REFERENCES public.suppliers(id) ON DELETE RESTRICT,
    inventory_receipt_id UUID REFERENCES public.inventory_receipts(id) ON DELETE SET NULL,
    return_number TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('draft', 'pending', 'approved', 'completed', 'cancelled')),
    return_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    notes TEXT,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (organization_id, return_number)
);

-- Create return_order_items table
CREATE TABLE public.return_order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    return_order_id UUID NOT NULL REFERENCES public.return_orders(id) ON DELETE CASCADE,
    inventory_receipt_item_id UUID REFERENCES public.inventory_receipt_items(id) ON DELETE SET NULL,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE RESTRICT,
    quantity NUMERIC(18,8) NOT NULL,
    uom_id UUID NOT NULL REFERENCES public.units_of_measurement(id) ON DELETE RESTRICT,
    base_quantity NUMERIC(18,8) NOT NULL,
    unit_cost NUMERIC(10, 2) NOT NULL,
    return_reason TEXT NOT NULL,
    lot_number TEXT,
    serial_numbers TEXT[],
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add trigger to update base_quantity for return_order_items
CREATE OR REPLACE FUNCTION update_return_order_item_base_quantity()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.quantity IS NOT NULL AND NEW.uom_id IS NOT NULL THEN
    NEW.base_quantity := calculate_base_quantity(NEW.product_id, NEW.uom_id, NEW.quantity);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_roi_base_quantity
BEFORE INSERT OR UPDATE OF quantity, uom_id ON public.return_order_items
FOR EACH ROW
EXECUTE FUNCTION update_return_order_item_base_quantity();

-- Create a function to update inventory when return is completed
CREATE OR REPLACE FUNCTION update_inventory_on_return_completion()
RETURNS TRIGGER AS $$
DECLARE
    org_id UUID;
    item   RECORD;            -- ← declare loop variable as a generic record
BEGIN
    -- Only proceed if status is changing to 'completed'
    IF NEW.status = 'completed'
       AND (OLD.status IS NULL OR OLD.status <> 'completed') THEN

        -- Get the organization ID
        org_id := NEW.organization_id;

        -- For each return item, create an inventory transaction and update product stock
        FOR item IN
            SELECT * 
              FROM public.return_order_items 
             WHERE return_order_id = NEW.id
        LOOP
            -- Create inventory transaction
            INSERT INTO public.inventory_transactions (
                organization_id,
                product_id,
                transaction_type,
                quantity,
                uom_id,
                reference_id,
                reference_type,
                notes,
                created_by
            ) VALUES (
                org_id,
                item.product_id,
                'return',
                -item.quantity,              -- Negative because it's leaving inventory
                item.uom_id,
                NEW.id,
                'return_orders',
                'Return to supplier: ' || NEW.return_number,
                NEW.created_by
            );

            -- Update product stock
            UPDATE public.products
               SET stock_quantity = stock_quantity - item.base_quantity,
                   updated_at     = NOW()
             WHERE id = item.product_id;
        END LOOP;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


-- Create trigger for inventory updates on return completion
CREATE TRIGGER update_inventory_on_return_completion
AFTER UPDATE OF status ON public.return_orders
FOR EACH ROW
EXECUTE FUNCTION update_inventory_on_return_completion();

-- Add RLS policies for return_orders
CREATE POLICY "Allow all authenticated users to manage return orders"
ON public.return_orders
FOR ALL
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');

-- Add RLS policies for return_order_items
CREATE POLICY "Allow all authenticated users to manage return order items"
ON public.return_order_items
FOR ALL
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');
