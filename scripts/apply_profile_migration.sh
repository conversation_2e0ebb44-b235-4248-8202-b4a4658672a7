#!/bin/bash

# Script to apply the profile enhancement migration

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Supabase CLI is not installed. Please install it first."
    echo "Visit https://supabase.com/docs/guides/cli for installation instructions."
    exit 1
fi

# Get the current directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Check if the migration file exists
MIGRATION_FILE="$PROJECT_DIR/supabase/migrations/00017_enhance_profiles.sql"
if [ ! -f "$MIGRATION_FILE" ]; then
    echo "Migration file not found: $MIGRATION_FILE"
    exit 1
fi

echo "Applying profile enhancement migration..."

# Start Supabase local development environment if it's not running
supabase status &> /dev/null || supabase start

# Apply the migration
supabase db reset --db-url=postgresql://postgres:postgres@localhost:54322/postgres

echo "Migration applied successfully!"
echo "The profiles table now has the following additional columns:"
echo "- bio: User biography or about me text"
echo "- job_title: User job title or position"
echo "- department: Department or team the user belongs to"
echo "- phone_number: User contact phone number"
echo "- location: User location (city, country, etc.)"

echo "You can now use these fields in your application."
