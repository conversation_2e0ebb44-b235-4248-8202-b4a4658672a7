#!/bin/bash

# This script deploys all Edge Functions to Supabase

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null
then
    echo "Supabase CLI is not installed. Please install it first."
    echo "https://supabase.com/docs/guides/cli"
    exit 1
fi

# Check if we're logged in
if ! supabase functions list --no-interactive &> /dev/null
then
    echo "You need to login to Supabase first. Run 'supabase login' and try again."
    exit 1
fi

# Deploy all functions
echo "Deploying Edge Functions to Supabase..."
supabase functions deploy --no-verify-jwt

echo "Setting up CORS for functions..."
supabase functions deploy cors --no-verify-jwt

echo "Deployment complete!"
echo "You can now use the Edge Functions in your application."
