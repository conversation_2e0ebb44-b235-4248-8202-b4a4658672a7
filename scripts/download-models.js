#!/usr/bin/env node

/**
 * Download face-api.js models script
 * Downloads the required models for facial recognition
 */

import https from 'https';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const MODELS_DIR = path.join(__dirname, '..', 'public', 'models');
const BASE_URL = 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights';

// Required models for our facial recognition system
const MODELS = [
  // Tiny Face Detector
  'tiny_face_detector_model-weights_manifest.json',
  'tiny_face_detector_model-shard1',
  
  // Face Landmarks 68
  'face_landmark_68_model-weights_manifest.json',
  'face_landmark_68_model-shard1',
  
  // Face Recognition
  'face_recognition_model-weights_manifest.json',
  'face_recognition_model-shard1',
  'face_recognition_model-shard2',
  
  // Face Expression (optional, for enhanced features)
  'face_expression_model-weights_manifest.json',
  'face_expression_model-shard1'
];

/**
 * Download a file from URL
 */
function downloadFile(url, destination) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(destination);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(destination, () => {}); // Delete the file on error
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

/**
 * Create models directory if it doesn't exist
 */
function ensureModelsDirectory() {
  if (!fs.existsSync(MODELS_DIR)) {
    fs.mkdirSync(MODELS_DIR, { recursive: true });
    console.log(`Created models directory: ${MODELS_DIR}`);
  }
}

/**
 * Check if model already exists
 */
function modelExists(modelName) {
  const modelPath = path.join(MODELS_DIR, modelName);
  return fs.existsSync(modelPath);
}

/**
 * Download all required models
 */
async function downloadModels() {
  console.log('🤖 Downloading face-api.js models...\n');
  
  ensureModelsDirectory();
  
  let downloaded = 0;
  let skipped = 0;
  
  for (const model of MODELS) {
    const modelPath = path.join(MODELS_DIR, model);
    const modelUrl = `${BASE_URL}/${model}`;
    
    if (modelExists(model)) {
      console.log(`⏭️  Skipping ${model} (already exists)`);
      skipped++;
      continue;
    }
    
    try {
      console.log(`⬇️  Downloading ${model}...`);
      await downloadFile(modelUrl, modelPath);
      console.log(`✅ Downloaded ${model}`);
      downloaded++;
    } catch (error) {
      console.error(`❌ Failed to download ${model}:`, error.message);
      process.exit(1);
    }
  }
  
  console.log(`\n🎉 Model download complete!`);
  console.log(`📊 Downloaded: ${downloaded}, Skipped: ${skipped}, Total: ${MODELS.length}`);
  
  // Calculate total size
  let totalSize = 0;
  for (const model of MODELS) {
    const modelPath = path.join(MODELS_DIR, model);
    if (fs.existsSync(modelPath)) {
      const stats = fs.statSync(modelPath);
      totalSize += stats.size;
    }
  }
  
  const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);
  console.log(`💾 Total size: ${totalSizeMB} MB`);
  
  console.log('\n🚀 Face recognition models are ready!');
  console.log('You can now use the facial recognition features in your application.');
}

/**
 * Main execution
 */
if (import.meta.url === `file://${process.argv[1]}`) {
  downloadModels().catch((error) => {
    console.error('❌ Error downloading models:', error);
    process.exit(1);
  });
}

export { downloadModels };
