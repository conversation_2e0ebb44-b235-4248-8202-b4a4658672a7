-- Fix the float inventory reporting function to use correct column name
-- Run this SQL in your Supabase dashboard SQL editor

-- Update the function to use the correct column name
CREATE OR REPLACE FUNCTION public.get_float_inventory_report(
  p_organization_id UUID,
  p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_product_id UUID DEFAULT NULL,
  p_resolved BOOLEAN DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  product_id UUID,
  product_name TEXT,
  product_sku TEXT,
  quantity NUMERIC,
  sale_id UUID,
  sale_number TEXT,
  sale_date TIMESTAMP WITH TIME ZONE,
  customer_name TEXT,
  resolved BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolution_type TEXT,
  resolution_notes TEXT,
  days_to_resolve INTEGER,
  days_unresolved INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    fi.id,
    fi.product_id,
    p.name AS product_name,
    p.sku AS product_sku,
    fi.quantity,
    fi.sale_id,
    s.invoice_number AS sale_number,
    s.sale_date,
    c.name AS customer_name,
    fi.resolved,
    fi.created_at,
    fi.resolved_at,
    fi.resolution_type,
    COALESCE(fi.resolution_notes, fi.notes) AS resolution_notes,
    -- Days to resolve (for resolved items)
    CASE WHEN fi.resolved THEN 
      EXTRACT(DAY FROM (fi.resolved_at - fi.created_at))::INTEGER
    ELSE 
      NULL 
    END AS days_to_resolve,
    -- Days unresolved (for unresolved items)
    CASE WHEN NOT fi.resolved THEN 
      EXTRACT(DAY FROM (NOW() - fi.created_at))::INTEGER
    ELSE 
      NULL 
    END AS days_unresolved
  FROM 
    float_inventory fi
  JOIN 
    products p ON fi.product_id = p.id
  JOIN 
    sales s ON fi.sale_id = s.id
  LEFT JOIN 
    customers c ON s.customer_id = c.id
  WHERE 
    fi.organization_id = p_organization_id
    AND (p_start_date IS NULL OR fi.created_at >= p_start_date)
    AND (p_end_date IS NULL OR fi.created_at <= p_end_date)
    AND (p_product_id IS NULL OR fi.product_id = p_product_id)
    AND (p_resolved IS NULL OR fi.resolved = p_resolved)
  ORDER BY 
    fi.created_at DESC;
END;
$$ LANGUAGE plpgsql;
