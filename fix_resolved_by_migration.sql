-- Fix for adding resolved_by field to float_inventory table
-- Run this SQL in your Supabase dashboard SQL editor

-- Step 1: Add the resolved_by column to track who resolved the float inventory
ALTER TABLE public.float_inventory
ADD COLUMN IF NOT EXISTS resolved_by UUID REFERENCES auth.users(id);

-- Step 2: Drop the existing function to avoid return type conflicts
DROP FUNCTION IF EXISTS public.get_float_inventory_report;

-- Step 3: Recreate the function with the new return type including user information
CREATE OR REPLACE FUNCTION public.get_float_inventory_report(
  p_organization_id UUID,
  p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_product_id UUID DEFAULT NULL,
  p_resolved BOOLEAN DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  product_id UUID,
  product_name TEXT,
  product_sku TEXT,
  quantity NUMERIC,
  sale_id UUID,
  sale_number TEXT,
  sale_date TIMESTAMP WITH TIME ZONE,
  customer_name TEXT,
  resolved BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolution_type TEXT,
  resolution_notes TEXT,
  resolved_by_id UUID,
  resolved_by_name TEXT,
  days_to_resolve INTEGER,
  days_unresolved INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    fi.id,
    fi.product_id,
    p.name AS product_name,
    p.sku AS product_sku,
    fi.quantity,
    fi.sale_id,
    s.invoice_number AS sale_number,
    s.sale_date,
    c.name AS customer_name,
    fi.resolved,
    fi.created_at,
    fi.resolved_at,
    fi.resolution_type,
    COALESCE(fi.resolution_notes, fi.notes) AS resolution_notes,
    fi.resolved_by AS resolved_by_id,
    COALESCE(
      CONCAT(pr.first_name, ' ', pr.last_name),
      pr.first_name,
      pr.last_name,
      'Unknown User'
    ) AS resolved_by_name,
    -- Days to resolve (for resolved items)
    CASE WHEN fi.resolved THEN 
      EXTRACT(DAY FROM (fi.resolved_at - fi.created_at))::INTEGER
    ELSE 
      NULL 
    END AS days_to_resolve,
    -- Days unresolved (for unresolved items)
    CASE WHEN NOT fi.resolved THEN 
      EXTRACT(DAY FROM (NOW() - fi.created_at))::INTEGER
    ELSE 
      NULL 
    END AS days_unresolved
  FROM 
    float_inventory fi
  JOIN 
    products p ON fi.product_id = p.id
  JOIN 
    sales s ON fi.sale_id = s.id
  LEFT JOIN 
    customers c ON s.customer_id = c.id
  LEFT JOIN
    profiles pr ON fi.resolved_by = pr.id
  WHERE 
    fi.organization_id = p_organization_id
    AND (p_start_date IS NULL OR fi.created_at >= p_start_date)
    AND (p_end_date IS NULL OR fi.created_at <= p_end_date)
    AND (p_product_id IS NULL OR fi.product_id = p_product_id)
    AND (p_resolved IS NULL OR fi.resolved = p_resolved)
  ORDER BY 
    fi.created_at DESC;
END;
$$ LANGUAGE plpgsql;
