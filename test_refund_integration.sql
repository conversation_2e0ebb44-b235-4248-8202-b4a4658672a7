-- Test script to create sample refund data for dashboard testing
-- This script creates a sample refund to verify the dashboard integration

-- Create a simple test refund for today
INSERT INTO public.refunds (
    organization_id,
    refund_number,
    original_sale_id,
    customer_id,
    refund_type,
    status,
    reason,
    reason_notes,
    subtotal,
    tax_amount,
    total_amount,
    restocking_fee,
    refund_method,
    requires_approval,
    created_by,
    created_at,
    processed_at,
    processed_by
)
SELECT
    o.id as organization_id,
    'REF-TEST-001' as refund_number,
    s.id as original_sale_id,
    s.customer_id,
    'partial' as refund_type,
    'processed' as status,
    'defective' as reason,
    'Test refund for dashboard verification' as reason_notes,
    150.00 as subtotal,
    0.00 as tax_amount,
    150.00 as total_amount,
    0.00 as restocking_fee,
    'cash' as refund_method,
    false as requires_approval,
    u.id as created_by,
    NOW() as created_at,
    NOW() as processed_at,
    u.id as processed_by
FROM public.organizations o
CROSS JOIN auth.users u
CROSS JOIN public.sales s
WHERE s.organization_id = o.id
AND s.status = 'completed'
LIMIT 1
ON CONFLICT (refund_number) DO NOTHING;

-- Create refund item
INSERT INTO public.refund_items (
    refund_id,
    sale_item_id,
    product_id,
    quantity,
    unit_price,
    total_price,
    condition,
    restore_inventory,
    notes
)
SELECT
    r.id as refund_id,
    si.id as sale_item_id,
    si.product_id,
    1 as quantity,
    150.00 as unit_price,
    150.00 as total_price,
    'defective' as condition,
    false as restore_inventory,
    'Test refund item' as notes
FROM public.refunds r
CROSS JOIN public.sale_items si
WHERE r.refund_number = 'REF-TEST-001'
AND si.sale_id = r.original_sale_id
LIMIT 1
ON CONFLICT DO NOTHING;

-- Verify the refund was created
SELECT 
    r.refund_number,
    r.total_amount,
    r.status,
    r.created_at,
    s.invoice_number as original_sale
FROM public.refunds r
LEFT JOIN public.sales s ON r.original_sale_id = s.id
ORDER BY r.created_at DESC
LIMIT 5;
