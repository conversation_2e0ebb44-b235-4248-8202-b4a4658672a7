-- Quick test refund creation
-- Run this in Supabase SQL Editor to create a test refund

-- Get organization and user IDs
WITH test_data AS (
  SELECT 
    o.id as org_id,
    u.id as user_id,
    s.id as sale_id,
    s.customer_id
  FROM public.organizations o
  CROSS JOIN auth.users u
  CROSS JOIN public.sales s
  WHERE s.organization_id = o.id 
  AND s.status = 'completed'
  LIMIT 1
)
INSERT INTO public.refunds (
  organization_id,
  refund_number,
  original_sale_id,
  customer_id,
  refund_type,
  status,
  reason,
  reason_notes,
  subtotal,
  tax_amount,
  total_amount,
  restocking_fee,
  refund_method,
  requires_approval,
  created_by,
  created_at,
  processed_at,
  processed_by
)
SELECT 
  org_id,
  'REF-DASHBOARD-TEST',
  sale_id,
  customer_id,
  'partial',
  'processed',
  'defective',
  'Dashboard integration test',
  250.00,
  0.00,
  250.00,
  0.00,
  'cash',
  false,
  user_id,
  NOW(),
  NOW(),
  user_id
FROM test_data
ON CONFLICT (refund_number) DO UPDATE SET
  total_amount = 250.00,
  updated_at = NOW();

-- Verify the refund was created
SELECT 
  refund_number,
  total_amount,
  status,
  created_at,
  organization_id
FROM public.refunds 
WHERE refund_number = 'REF-DASHBOARD-TEST';
