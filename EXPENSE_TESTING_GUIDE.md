# Enhanced Payables System - Testing Guide

## 🚀 Quick Start Testing

### 1. Run Database Migrations

```bash
# Apply the new migrations
supabase db push

# Or if using direct SQL
psql -h your-host -d your-db -f supabase/migrations/00108_enhanced_payables_operational_expenses.sql
psql -h your-host -d your-db -f supabase/migrations/00109_fix_operational_expenses_function.sql
psql -h your-host -d your-db -f supabase/migrations/00110_sample_expense_data.sql
```

### 2. Access the New Features

Navigate to the new **EXPENSES** section in the sidebar:

- **Dashboard** (`/expenses/dashboard`) - Overview and analytics
- **Quick Entry** (`/expenses/quick-entry`) - Fast expense creation
- **Expense Types** (`/expenses/types`) - Manage expense categories
- **Recurring Expenses** (`/expenses/recurring`) - Recurring expense templates
- **All Payables** (`/expenses/payables`) - Enhanced payables view

## 📊 Testing Scenarios

### Scenario 1: Expense Types Management

1. **Navigate to Expense Types** (`/expenses/types`)
2. **View Sample Data**: You should see 20 pre-created expense types
3. **Create New Type**:
   - Click "Add Expense Type"
   - Name: "Software Subscriptions"
   - Code: "SOFTWARE"
   - Category: "Administrative"
   - Approval Limit: ₱15,000
   - Check "Can be Recurring" → Select "Monthly"
4. **Test Filtering**: Filter by category, search by name
5. **Edit Existing**: Modify approval limits or descriptions

### Scenario 2: Quick Expense Entry

1. **Navigate to Quick Entry** (`/expenses/quick-entry`)
2. **Create Office Rent Expense**:
   - Expense Type: "Office Rent"
   - Reference Number: Auto-generated (RENT-123456)
   - Supplier: Select any supplier
   - Amount: ₱25,000
   - Invoice Date: Today
   - Due Date: Auto-calculated (30 days)
   - Notes: "Monthly office rent payment"
3. **Verify Auto-Calculations**:
   - Due date should auto-populate
   - Reference number should auto-generate
   - Summary should show gross, VAT, WHT amounts
4. **Submit and Verify**: Check success message and form reset

### Scenario 3: Recurring Expenses

1. **Navigate to Recurring Expenses** (`/expenses/recurring`)
2. **Create Monthly Utility Bill**:
   - Name: "Monthly Electricity Bill"
   - Expense Type: "Utilities - Electricity"
   - Supplier: Select utility company
   - Amount: ₱12,000
   - Frequency: Monthly
   - Start Date: First of current month
   - Payment Terms: 15 days
   - Check "Auto-create payables when due"
3. **Test Manual Payable Creation**:
   - Click the document icon to create payable immediately
   - Verify payable appears in Enhanced Payables
4. **Check Due Date Tracking**: Verify next due date is calculated correctly

### Scenario 4: Enhanced Payables

1. **Navigate to All Payables** (`/expenses/payables`)
2. **View Enhanced Features**:
   - Source type filtering (Manual Entry, Recurring, etc.)
   - Approval status tracking
   - Department allocation
   - Overdue indicators
3. **Create Manual Payable**:
   - Source Type: "Professional Services"
   - Expense Type: "Legal Services"
   - Supplier: Law firm
   - Amount: ₱30,000
   - Department: "Legal"
   - Withholding Tax: 10%
4. **Test Filtering**: Filter by status, approval status, source type

### Scenario 5: Expense Dashboard

1. **Navigate to Dashboard** (`/expenses/dashboard`)
2. **Verify Metrics**:
   - Pending Approvals count
   - Overdue Payments count
   - Upcoming Recurring Expenses
   - Active Categories count
3. **Check Analytics**:
   - Expenses by Category chart
   - Upcoming Recurring Expenses list
   - Due date alerts (overdue vs due soon)
4. **Test Refresh**: Click refresh to update data

## 🧪 Advanced Testing

### Test Approval Workflows

1. **Create High-Value Expense** (above approval limits)
2. **Verify Approval Status**: Should be "Pending"
3. **Check Workflow Routing**: Based on amount and expense type

### Test Recurring Expense Automation

1. **Create Recurring Expense** with past due date
2. **Verify Auto-Creation**: If enabled, payable should be created
3. **Check Next Due Date**: Should be calculated correctly

### Test Multi-Currency Support

1. **Create Expense** with different currency
2. **Verify Calculations**: WHT and VAT should calculate correctly

### Test Department Allocation

1. **Create Expenses** for different departments
2. **Filter by Department**: Verify filtering works
3. **Check Reporting**: Department-wise expense tracking

## 🔍 Data Validation

### Verify Sample Data

```sql
-- Check expense types
SELECT name, code, category, approval_limit, is_recurring_type 
FROM expense_types 
ORDER BY category, name;

-- Check approval workflows
SELECT aw.name, et.name as expense_type, aw.level_1_amount_limit
FROM approval_workflows aw
JOIN expense_types et ON aw.expense_type_id = et.id;

-- Check created payables
SELECT reference_number, source_type, amount, approval_status, created_at
FROM payables 
WHERE source_type IN ('manual_entry', 'recurring_expense')
ORDER BY created_at DESC;
```

### Verify Calculations

1. **Withholding Tax**: Should be calculated as (Amount × Rate) / 100
2. **Due Dates**: Should respect supplier payment terms
3. **Next Due Dates**: Should follow frequency patterns
4. **Balance Calculations**: Should be Amount - Withholding Tax

## 🐛 Common Issues & Solutions

### Issue: Components Not Loading
**Solution**: Check if all imports are correct in Router.tsx

### Issue: Database Errors
**Solution**: Ensure migrations ran successfully and RLS is disabled

### Issue: Sample Data Not Created
**Solution**: Check if organization and users exist in database

### Issue: Auto-calculations Not Working
**Solution**: Verify supplier payment terms are set

### Issue: Navigation Not Showing
**Solution**: Check SidebaritemsNew.ts has expense menu items

## 📈 Performance Testing

### Load Testing
1. **Create 100+ expense types**
2. **Create 500+ recurring expenses**
3. **Generate 1000+ payables**
4. **Test pagination and filtering performance**

### UI Responsiveness
1. **Test on mobile devices**
2. **Verify responsive design**
3. **Check loading states**
4. **Validate form interactions**

## ✅ Success Criteria

- [ ] All 5 expense components load without errors
- [ ] Sample data is created successfully
- [ ] CRUD operations work for all entities
- [ ] Auto-calculations function correctly
- [ ] Filtering and search work properly
- [ ] Dashboard shows accurate metrics
- [ ] Responsive design works on mobile
- [ ] Navigation integrates seamlessly
- [ ] Database constraints are enforced
- [ ] Multi-tenancy security is maintained

## 🎯 Next Phase Preparation

After successful testing, you'll be ready for:
- **Phase 3**: Bulk upload, advanced reporting, mobile interface
- **Phase 4**: Banking integration, OCR, accounting exports

## 📞 Support

If you encounter issues:
1. Check browser console for JavaScript errors
2. Verify database migration status
3. Check network requests in developer tools
4. Validate data integrity with SQL queries

Happy testing! 🚀
