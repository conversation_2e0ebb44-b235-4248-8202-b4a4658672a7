# 🧪 Payables System Testing Guide

## 🚀 **Quick Start**

### 1. **Apply Migrations**
```bash
# Apply the main payables system migration
# File: supabase/migrations/00070_centralized_payables_system.sql

# Apply the type verification migration  
# File: supabase/migrations/00071_update_database_types_for_payables.sql
```

### 2. **Verify Installation**
```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_name IN ('payables', 'payable_payments');

-- Check if functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_name IN ('validate_payable_multi_tenancy', 'resolve_payable_source_metadata');

-- Check if triggers exist
SELECT trigger_name FROM information_schema.triggers 
WHERE trigger_name LIKE '%payable%';
```

## 🔒 **Critical Security Tests**

### Test 1: Multi-Tenancy Validation
```sql
-- This should FAIL with security violation
INSERT INTO payables (
    organization_id, 
    supplier_id, 
    source_type, 
    source_id, 
    reference_number, 
    invoice_date, 
    due_date, 
    amount, 
    balance, 
    created_by
) VALUES (
    'org1-uuid',
    'supplier-from-org2-uuid', -- Different organization
    'manual_entry',
    'test-source-id',
    'TEST-001',
    '2024-01-01',
    '2024-01-31',
    1000.00,
    1000.00,
    'user-uuid'
);
-- Expected: ERROR with "SECURITY VIOLATION" message
```

### Test 2: XOR Constraint Validation
```sql
-- This should FAIL - cannot have both supplier_id AND employee_id
INSERT INTO payables (
    organization_id, 
    supplier_id, 
    employee_id, -- Both supplier and employee - should fail
    source_type, 
    source_id, 
    reference_number, 
    invoice_date, 
    due_date, 
    amount, 
    balance, 
    created_by
) VALUES (
    'org1-uuid',
    'supplier-uuid',
    'employee-uuid',
    'manual_entry',
    'test-source-id',
    'TEST-002',
    '2024-01-01',
    '2024-01-31',
    1000.00,
    1000.00,
    'user-uuid'
);
-- Expected: ERROR with constraint violation
```

### Test 3: Withholding Tax Validation
```sql
-- This should FAIL - incorrect withholding tax calculation
INSERT INTO payables (
    organization_id, 
    supplier_id, 
    source_type, 
    source_id, 
    reference_number, 
    invoice_date, 
    due_date, 
    amount, 
    vat_amount,
    withholding_tax_rate,
    withholding_tax_amount, -- Incorrect amount
    balance, 
    created_by
) VALUES (
    'org1-uuid',
    'supplier-uuid',
    'manual_entry',
    'test-source-id',
    'TEST-003',
    '2024-01-01',
    '2024-01-31',
    1000.00,
    120.00, -- VAT
    5.00,   -- 5% withholding tax rate
    100.00, -- Should be (1000-120)*0.05 = 44.00
    1000.00,
    'user-uuid'
);
-- Expected: ERROR with withholding tax calculation mismatch
```

## 🔄 **Auto-Creation Workflow Tests**

### Test 4: Purchase Receipt → Payable Auto-Creation
1. **Create Purchase Request**
   - Navigate to `/purchases/requests`
   - Create a new purchase request

2. **Convert to Purchase Order**
   - Approve the purchase request
   - Convert to purchase order

3. **Create Inventory Receipt**
   - Navigate to inventory receipts
   - Create receipt from the purchase order
   - Add items with quantities and costs

4. **Trigger Auto-Creation**
   - Set receipt status to 'completed'
   - Check `/payables` - new payable should appear automatically

5. **Verify Auto-Created Payable**
   - Correct supplier linked
   - Proper VAT calculation (12%)
   - Due date based on supplier payment terms
   - Reference number format: 'INV-{receipt_number}'

## 💳 **Payment Processing Tests**

### Test 5: Payment Creation and Balance Updates
1. **Navigate to Payables**
   - Go to `/payables`
   - Click on any open payable

2. **Add Payment**
   - Click "Add Payment"
   - Enter payment details:
     - Date: Current date
     - Amount: Partial amount (less than balance)
     - Method: Any method
     - Reference: Test reference

3. **Verify Updates**
   - Balance should decrease by payment amount
   - Status should change to 'partially_paid'
   - Payment should appear in payment history

4. **Complete Payment**
   - Add another payment for remaining balance
   - Status should change to 'paid'
   - Balance should be 0

## 🎨 **UI Functionality Tests**

### Test 6: Payables List Features
1. **Filtering**
   - Test status filters (Open, Paid, etc.)
   - Test source type filters
   - Test search functionality

2. **Pagination**
   - Test page navigation
   - Test items per page changes
   - Verify item counts are correct

3. **Summary Cards**
   - Verify total payables count
   - Check total amounts
   - Verify overdue calculations

### Test 7: Payable Details View
1. **Basic Information**
   - All payable details display correctly
   - Supplier/employee information shows

2. **Source Details Tab**
   - Source metadata resolves correctly
   - Shows linked document information
   - Security confirmation message displays

3. **Payment History Tab**
   - All payments listed correctly
   - Payment methods display properly
   - Dates and amounts are accurate

## 📊 **Reporting Tests**

### Test 8: Source Metadata Resolution
```sql
-- Test the source metadata function
SELECT * FROM resolve_payable_source_metadata('payable-uuid');
-- Should return source reference, description, date, amount
```

### Test 9: Aging Report
```sql
-- Test aging calculation
SELECT * FROM calculate_payable_aging('org-uuid', '2024-01-15');
-- Should return payables grouped by aging buckets
```

## ⚠️ **Error Handling Tests**

### Test 10: Invalid Operations
1. **Delete Paid Payable**
   - Try to delete a payable with status 'paid'
   - Should fail with audit protection message

2. **Overpayment**
   - Try to create payment > remaining balance
   - Should fail with validation error

3. **Cross-Organization Access**
   - Try to access payable from different organization
   - Should fail or return empty results

## ✅ **Success Criteria**

### Security Tests
- [ ] Multi-tenancy validation blocks cross-org data
- [ ] XOR constraint prevents invalid payee combinations
- [ ] Withholding tax validation works correctly

### Workflow Tests
- [ ] Auto-creation works from inventory receipts
- [ ] VAT calculation is accurate (12%)
- [ ] Payment terms are applied correctly

### UI Tests
- [ ] All filtering and search functions work
- [ ] Pagination operates correctly
- [ ] Source details display properly
- [ ] Payment processing updates balances

### Reporting Tests
- [ ] Source metadata resolves correctly
- [ ] Aging calculations are accurate
- [ ] Summary statistics are correct

## 🚨 **If Tests Fail**

1. **Check Migration Status**
   ```sql
   SELECT * FROM supabase_migrations.schema_migrations 
   WHERE version LIKE '%payables%';
   ```

2. **Verify Trigger Status**
   ```sql
   SELECT * FROM information_schema.triggers 
   WHERE trigger_name LIKE '%payable%';
   ```

3. **Check Function Definitions**
   ```sql
   SELECT routine_name, routine_definition 
   FROM information_schema.routines 
   WHERE routine_name LIKE '%payable%';
   ```

4. **Review Error Logs**
   - Check Supabase dashboard for detailed error messages
   - Look for constraint violations or function errors

## 🎯 **Expected Results**

After successful testing:
- ✅ **Security**: Multi-tenancy protection active
- ✅ **Automation**: Auto-creation workflow functional  
- ✅ **Compliance**: Philippines BIR features working
- ✅ **UI**: All components responsive and functional
- ✅ **Reporting**: Analytics and aging reports accurate

**The Centralized Payables System should be fully operational and secure!** 🚀
