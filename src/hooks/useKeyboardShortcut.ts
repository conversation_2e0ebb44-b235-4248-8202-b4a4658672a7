import { useEffect, useCallback, useRef } from 'react';

type KeyboardShortcutOptions = {
  /**
   * Whether the shortcut should be active
   * @default true
   */
  isEnabled?: boolean;
  
  /**
   * Whether to prevent the default browser action
   * @default true
   */
  preventDefault?: boolean;
  
  /**
   * Whether to stop the event propagation
   * @default false
   */
  stopPropagation?: boolean;
  
  /**
   * Callback to execute when the shortcut is triggered
   */
  callback: (e: KeyboardEvent) => void;
};

type KeyCombination = {
  /**
   * The key to listen for (e.g., 's', 'Enter', 'Escape')
   */
  key: string;
  
  /**
   * Whether the Alt key should be pressed
   * @default false
   */
  alt?: boolean;
  
  /**
   * Whether the Ctrl key should be pressed
   * @default false
   */
  ctrl?: boolean;
  
  /**
   * Whether the Shift key should be pressed
   * @default false
   */
  shift?: boolean;
  
  /**
   * Whether the Meta key (Command on Mac) should be pressed
   * @default false
   */
  meta?: boolean;
};

/**
 * Hook to register keyboard shortcuts
 * @param keyCombination The key combination to listen for
 * @param options Options for the keyboard shortcut
 */
export const useKeyboardShortcut = (
  keyCombination: KeyCombination,
  options: KeyboardShortcutOptions
) => {
  const { 
    isEnabled = true, 
    preventDefault = true, 
    stopPropagation = false, 
    callback 
  } = options;
  
  // Use a ref to store the callback to avoid re-registering the event listener
  // when the callback changes
  const callbackRef = useRef(callback);
  
  // Update the callback ref when the callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  // Create the event handler
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Check if the shortcut is enabled
      if (!isEnabled) return;
      
      // Check if the key combination matches
      const isAltPressed = keyCombination.alt ? event.altKey : !event.altKey;
      const isCtrlPressed = keyCombination.ctrl ? event.ctrlKey : !event.ctrlKey;
      const isShiftPressed = keyCombination.shift ? event.shiftKey : !event.shiftKey;
      const isMetaPressed = keyCombination.meta ? event.metaKey : !event.metaKey;
      const isKeyPressed = event.key.toLowerCase() === keyCombination.key.toLowerCase();
      
      // If we're checking for modifiers, make sure they match exactly
      const modifiersMatch = (
        (!keyCombination.alt || isAltPressed) &&
        (!keyCombination.ctrl || isCtrlPressed) &&
        (!keyCombination.shift || isShiftPressed) &&
        (!keyCombination.meta || isMetaPressed)
      );
      
      // If the key combination matches, execute the callback
      if (isKeyPressed && modifiersMatch) {
        if (preventDefault) {
          event.preventDefault();
        }
        
        if (stopPropagation) {
          event.stopPropagation();
        }
        
        callbackRef.current(event);
      }
    },
    [
      isEnabled,
      preventDefault,
      stopPropagation,
      keyCombination.key,
      keyCombination.alt,
      keyCombination.ctrl,
      keyCombination.shift,
      keyCombination.meta
    ]
  );
  
  // Register and unregister the event listener
  useEffect(() => {
    if (isEnabled) {
      document.addEventListener('keydown', handleKeyDown);
    }
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isEnabled, handleKeyDown]);
};

/**
 * Format a keyboard shortcut for display
 * @param keyCombination The key combination to format
 * @returns A formatted string representation of the shortcut
 */
export const formatKeyboardShortcut = (keyCombination: KeyCombination): string => {
  const parts: string[] = [];
  
  // Add modifiers
  if (keyCombination.ctrl) parts.push('Ctrl');
  if (keyCombination.alt) parts.push('Alt');
  if (keyCombination.shift) parts.push('Shift');
  if (keyCombination.meta) parts.push(navigator.platform.includes('Mac') ? '⌘' : 'Win');
  
  // Add the key
  let key = keyCombination.key;
  
  // Format special keys
  switch (key.toLowerCase()) {
    case 'enter':
      key = '↵';
      break;
    case 'escape':
      key = 'Esc';
      break;
    case 'arrowup':
      key = '↑';
      break;
    case 'arrowdown':
      key = '↓';
      break;
    case 'arrowleft':
      key = '←';
      break;
    case 'arrowright':
      key = '→';
      break;
    case 'delete':
      key = 'Del';
      break;
    default:
      // Capitalize single letters
      if (key.length === 1) {
        key = key.toUpperCase();
      }
  }
  
  parts.push(key);
  
  // Join with + symbol
  return parts.join(' + ');
};

/**
 * Component props for the KeyboardShortcutTooltip component
 */
export type KeyboardShortcutTooltipProps = {
  /**
   * The key combination to display
   */
  shortcut: KeyCombination;
  
  /**
   * Additional class names to apply to the tooltip
   */
  className?: string;
};
