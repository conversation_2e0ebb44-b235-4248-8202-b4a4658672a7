import { useState, useEffect, useCallback, useRef } from 'react';
import {
  saveToLocalStorage,
  loadFromLocalStorage,
  clearFromLocalStorage,
  formatRelativeTime
} from '../services/autoSave';

interface AutoSaveOptions<T> {
  /**
   * The key to use for localStorage
   */
  key: string;

  /**
   * The initial data
   */
  initialData: T;

  /**
   * The interval in milliseconds to auto-save
   * @default 5000 (5 seconds)
   */
  interval?: number;

  /**
   * Whether to load saved data on mount
   * @default true
   */
  loadOnMount?: boolean;

  /**
   * Whether auto-save is enabled
   * @default true
   */
  enabled?: boolean;

  /**
   * Callback when data is saved
   */
  onSave?: (data: T) => void;

  /**
   * Callback when data is loaded
   */
  onLoad?: (data: T) => void;

  /**
   * Callback when data is cleared
   */
  onClear?: () => void;
}

interface AutoSaveResult<T> {
  /**
   * The current data
   */
  data: T;

  /**
   * Set the data (will trigger auto-save)
   */
  setData: React.Dispatch<React.SetStateAction<T>>;

  /**
   * Save the data immediately
   */
  save: () => void;

  /**
   * Clear the saved data
   */
  clear: () => void;

  /**
   * Whether there is saved data
   */
  hasSavedData: boolean;

  /**
   * The timestamp of the last save
   */
  lastSaved: string | null;

  /**
   * The formatted relative time of the last save
   */
  lastSavedFormatted: string;

  /**
   * Whether the data has been modified since the last save
   */
  isDirty: boolean;

  /**
   * Whether the data is currently being saved
   */
  isSaving: boolean;
}

/**
 * Hook for auto-saving form data to localStorage
 */
export function useAutoSave<T extends unknown>({
  key,
  initialData,
  interval = 5000,
  loadOnMount = true,
  enabled = true,
  onSave,
  onLoad,
  onClear
}: AutoSaveOptions<T>): AutoSaveResult<T> {
  // State
  const [data, setData] = useState<T>(initialData);
  const [lastSaved, setLastSaved] = useState<string | null>(null);
  const [isDirty, setIsDirty] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasSavedData, setHasSavedData] = useState(false);

  // Refs
  const dataRef = useRef(data);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Update the data ref when data changes
  useEffect(() => {
    dataRef.current = data;
    setIsDirty(true);
  }, [data]);

  // Load saved data on mount
  useEffect(() => {
    if (loadOnMount) {
      const savedData = loadFromLocalStorage<T>(key);
      if (savedData) {
        setData(savedData.data);
        setLastSaved(savedData.timestamp);
        setHasSavedData(true);
        setIsDirty(false);

        if (onLoad) {
          onLoad(savedData.data);
        }
      }
    }
  }, [key, loadOnMount, onLoad]);

  // Check if there is saved data
  useEffect(() => {
    try {
      const hasData = localStorage.getItem(key) !== null;
      setHasSavedData(hasData);
    } catch (error) {
      console.error('Error checking for saved data:', error);
    }
  }, [key]);

  // Save function
  const save = useCallback(() => {
    if (!enabled) return;

    setIsSaving(true);

    try {
      const currentData = dataRef.current;
      saveToLocalStorage(key, currentData);

      const timestamp = new Date().toISOString();
      setLastSaved(timestamp);
      setIsDirty(false);
      setHasSavedData(true);

      if (onSave) {
        onSave(currentData);
      }
    } catch (error) {
      console.error('Error saving data:', error);
    } finally {
      setIsSaving(false);
    }
  }, [enabled, key, onSave]);

  // Clear function
  const clear = useCallback(() => {
    clearFromLocalStorage(key);
    setLastSaved(null);
    setIsDirty(false);
    setHasSavedData(false);

    if (onClear) {
      onClear();
    }
  }, [key, onClear]);

  // Set up auto-save timer
  useEffect(() => {
    if (!enabled) return;

    // Clear any existing timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Set up new timer
    timerRef.current = setInterval(() => {
      if (isDirty) {
        save();
      }
    }, interval);

    // Clean up on unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [enabled, interval, isDirty, save]);

  // Format the last saved timestamp
  const lastSavedFormatted = lastSaved ? formatRelativeTime(lastSaved) : 'Never';

  return {
    data,
    setData,
    save,
    clear,
    hasSavedData,
    lastSaved,
    lastSavedFormatted,
    isDirty,
    isSaving
  };
}
