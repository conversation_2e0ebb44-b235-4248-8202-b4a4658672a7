import { useState, useCallback } from 'react';

// Define validation rule types
type ValidationRule<T> = {
  /**
   * The validation function that returns true if valid, false if invalid
   */
  validate: (value: any, formData: T) => boolean;

  /**
   * The error message to display if validation fails
   */
  message: string;
};

// Define field validation config
type FieldValidationConfig<T> = {
  [K in keyof T]?: ValidationRule<T>[];
};

// Define form validation config
type FormValidationConfig<T> = {
  /**
   * Field-level validation rules
   */
  fields: FieldValidationConfig<T>;

  /**
   * Form-level validation rules
   */
  form?: ValidationRule<T>[];
};

// Define validation errors type
type ValidationErrors<T> = {
  [K in keyof T]?: string[];
};

// Define form validation result
interface FormValidationResult<T> {
  /**
   * The current form data
   */
  formData: T;

  /**
   * Set the form data
   */
  setFormData: React.Dispatch<React.SetStateAction<T>>;

  /**
   * Field-level validation errors
   */
  errors: ValidationErrors<T>;

  /**
   * Form-level validation errors
   */
  formErrors: string[];

  /**
   * Whether the form is valid
   */
  isValid: boolean;

  /**
   * Validate a specific field
   */
  validateField: (field: keyof T) => boolean;

  /**
   * Validate the entire form
   */
  validateForm: () => boolean;

  /**
   * Handle input change
   */
  handleChange: (field: keyof T, value: any) => void;

  /**
   * Reset the form data and errors
   */
  resetForm: () => void;

  /**
   * Set the form data without validation
   */
  setFormDataWithoutValidation: (data: T) => void;
}

/**
 * Hook for form validation with better error messages
 */
export function useFormValidation<T extends Record<string, any>>(
  initialData: T,
  validationConfig: FormValidationConfig<T>
): FormValidationResult<T> {
  // State
  const [formData, setFormData] = useState<T>(initialData);
  const [errors, setErrors] = useState<ValidationErrors<T>>({});
  const [formErrors, setFormErrors] = useState<string[]>([]);

  // Validate a specific field
  const validateField = useCallback(
    (field: keyof T): boolean => {
      const fieldRules = validationConfig.fields[field];

      if (!fieldRules) {
        return true;
      }

      const fieldErrors: string[] = [];

      for (const rule of fieldRules) {
        if (!rule.validate(formData[field], formData)) {
          fieldErrors.push(rule.message);
        }
      }

      setErrors(prev => ({
        ...prev,
        [field]: fieldErrors.length > 0 ? fieldErrors : undefined
      }));

      return fieldErrors.length === 0;
    },
    [formData, validationConfig.fields]
  );

  // Validate the entire form
  const validateForm = useCallback((): boolean => {
    let isValid = true;
    const newErrors: ValidationErrors<T> = {};
    const newFormErrors: string[] = [];

    // Validate each field
    for (const field in validationConfig.fields) {
      const fieldRules = validationConfig.fields[field as keyof T];

      if (fieldRules) {
        const fieldErrors: string[] = [];

        for (const rule of fieldRules) {
          if (!rule.validate(formData[field as keyof T], formData)) {
            fieldErrors.push(rule.message);
          }
        }

        if (fieldErrors.length > 0) {
          newErrors[field as keyof T] = fieldErrors;
          isValid = false;
        }
      }
    }

    // Validate form-level rules
    if (validationConfig.form) {
      for (const rule of validationConfig.form) {
        if (!rule.validate(null, formData)) {
          newFormErrors.push(rule.message);
          isValid = false;
        }
      }
    }

    setErrors(newErrors);
    setFormErrors(newFormErrors);

    return isValid;
  }, [formData, validationConfig.fields, validationConfig.form]);

  // Handle input change
  const handleChange = useCallback(
    (field: keyof T, value: any) => {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));

      // Validate the field after a short delay
      setTimeout(() => {
        validateField(field);
      }, 100);
    },
    [validateField]
  );

  // Reset the form
  const resetForm = useCallback(() => {
    setFormData(initialData);
    setErrors({});
    setFormErrors([]);
  }, [initialData]);

  // Set form data without validation
  const setFormDataWithoutValidation = useCallback((data: T) => {
    setFormData(data);
  }, []);

  // Calculate if the form is valid
  const isValid = Object.keys(errors).length === 0 && formErrors.length === 0;

  return {
    formData,
    setFormData,
    errors,
    formErrors,
    isValid,
    validateField,
    validateForm,
    handleChange,
    resetForm,
    setFormDataWithoutValidation
  };
}

// Common validation rules
export const ValidationRules = {
  required: <T extends unknown>(message = 'This field is required'): ValidationRule<T> => ({
    validate: (value) => {
      if (value === null || value === undefined) return false;
      if (typeof value === 'string') return value.trim() !== '';
      if (Array.isArray(value)) return value.length > 0;
      return true;
    },
    message
  }),

  minLength: <T extends unknown>(min: number, message = `Must be at least ${min} characters`): ValidationRule<T> => ({
    validate: (value) => {
      if (typeof value !== 'string') return true;
      return value.length >= min;
    },
    message
  }),

  maxLength: <T extends unknown>(max: number, message = `Must be no more than ${max} characters`): ValidationRule<T> => ({
    validate: (value) => {
      if (typeof value !== 'string') return true;
      return value.length <= max;
    },
    message
  }),

  email: <T extends unknown>(message = 'Please enter a valid email address'): ValidationRule<T> => ({
    validate: (value) => {
      if (!value) return true;
      if (typeof value !== 'string') return false;

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value);
    },
    message
  }),

  numeric: <T extends unknown>(message = 'Please enter a valid number'): ValidationRule<T> => ({
    validate: (value) => {
      if (!value) return true;
      return !isNaN(Number(value));
    },
    message
  }),

  min: <T extends unknown>(min: number, message = `Must be at least ${min}`): ValidationRule<T> => ({
    validate: (value) => {
      if (!value) return true;
      return Number(value) >= min;
    },
    message
  }),

  max: <T extends unknown>(max: number, message = `Must be no more than ${max}`): ValidationRule<T> => ({
    validate: (value) => {
      if (!value) return true;
      return Number(value) <= max;
    },
    message
  }),

  pattern: <T extends unknown>(pattern: RegExp, message = 'Invalid format'): ValidationRule<T> => ({
    validate: (value) => {
      if (!value) return true;
      if (typeof value !== 'string') return false;
      return pattern.test(value);
    },
    message
  }),

  custom: <T extends unknown>(validateFn: (value: any, formData: T) => boolean, message: string): ValidationRule<T> => ({
    validate: validateFn,
    message
  })
};
