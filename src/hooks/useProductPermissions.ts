import { usePermission } from '../context/PermissionContext';
import { useAuth } from '../context/AuthContext';
import { useOrganization } from '../context/OrganizationContext';

export const useProductPermissions = () => {
  const { checkPermission } = usePermission();
  const { user } = useAuth();
  const { currentOrganization, currentMember } = useOrganization();

  const canViewProducts = checkPermission('products', 'view');
  const canCreateProducts = checkPermission('products', 'create');
  const canUpdateProducts = checkPermission('products', 'update');
  const canDeleteProducts = checkPermission('products', 'delete');

  // Check if the user is an owner, admin, or inventory manager
  const isOwnerAdminOrManager =
    currentMember?.role === 'owner' ||
    currentMember?.role === 'admin' ||
    currentMember?.role === 'inventory_manager';

  // If the user is an owner, they should have all permissions
  const isOwner = currentMember?.role === 'owner';
  const isAdmin = currentMember?.role === 'admin';

  return {
    canViewProducts: isOwner || canViewProducts,
    canCreateProducts: isOwner || (canCreateProducts && isOwnerAdminOrManager),
    canUpdateProducts: isOwner || (canUpdateProducts && isOwnerAdminOrManager),
    canDeleteProducts: isOwner || isAdmin || (canDeleteProducts && (isOwner || isAdmin)),
    isOwnerAdminOrManager,
    currentMember,
    user,
    currentOrganization,
  };
};
