import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Card, But<PERSON>, Table, Badge, Spinner, Alert, TextInput, Select } from "flowbite-react";
import {
  HiOutlinePlus,
  HiOutlineExclamation,
  HiOutlineSearch,
  HiOutlineFilter,
  HiOutlineRefresh,
  HiOutlineDocumentText,
  HiOutlineShoppingBag,
  HiOutlineOfficeBuilding
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getPurchaseOrders } from '../../services/purchaseOrder';
import { formatDate, formatDateTime } from '../../utils/formatters';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import EmptyState from '../../components/common/EmptyState';
import Pagination from '../../components/common/Pagination';

const PurchaseOrders = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  // State variables
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [purchaseOrders, setPurchaseOrders] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Fetch purchase orders with filtering
  const fetchPurchaseOrders = async (search = searchQuery, status = statusFilter) => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      // Prepare filter options
      const options: any = {};
      if (search) options.searchQuery = search;
      if (status !== 'all') options.status = status;

      const { purchaseOrders: orders, error: fetchError } = await getPurchaseOrders(
        currentOrganization.id,
        options
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setPurchaseOrders(orders || []);
      }
    } catch (err: any) {
      console.error('Error fetching purchase orders:', err);
      setError(err.message || 'An error occurred while fetching purchase orders');
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchPurchaseOrders();
  }, [currentOrganization]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchPurchaseOrders(searchQuery, statusFilter);
  };

  // Handle status filter change
  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newStatus = e.target.value;
    setStatusFilter(newStatus);
    fetchPurchaseOrders(searchQuery, newStatus);
  };

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchPurchaseOrders(searchQuery, statusFilter);
  };

  // Navigate to create page
  const handleCreateClick = () => {
    navigate('/purchases/orders/create');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'info';
      case 'partially_received':
        return 'warning';
      case 'received':
        return 'success';
      case 'cancelled':
        return 'failure';
      default:
        return 'gray';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'sent':
        return 'Sent';
      case 'partially_received':
        return 'Partially Received';
      case 'received':
        return 'Received';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(purchaseOrders.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentOrders = purchaseOrders.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <HiOutlineShoppingBag className="mr-2 h-6 w-6" />
              Purchase Orders
            </h1>
            <p className="text-gray-500">
              Create and manage purchase orders sent to suppliers.
            </p>
          </div>

          {/* Improved create button with better visibility */}
          <Button
            color="primary"
            onClick={handleCreateClick}
            className="px-4 py-2 font-medium shadow-sm hover:shadow-md transition-all"
            size="lg"
          >
            <HiOutlinePlus className="mr-2 h-5 w-5" />
            Create Purchase Order
          </Button>
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        {/* Search and filter bar */}
        <div className="flex flex-col md:flex-row gap-3 mb-4">
          <form onSubmit={handleSearch} className="flex-grow">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <HiOutlineSearch className="w-5 h-5 text-gray-500" />
              </div>
              <TextInput
                type="search"
                placeholder="Search by order number or supplier..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10"
              />
              <Button type="submit" color="blue" size="sm" className="absolute right-1 top-1 bottom-1">
                Search
              </Button>
            </div>
          </form>

          <div className="flex gap-2">
            <div className="min-w-[170px]">
              <Select
                value={statusFilter}
                onChange={handleStatusChange}
                icon={HiOutlineFilter}
              >
                <option value="all">All Status</option>
                <option value="draft">Draft</option>
                <option value="sent">Sent</option>
                <option value="partially_received">Partially Received</option>
                <option value="received">Received</option>
                <option value="cancelled">Cancelled</option>
              </Select>
            </div>

            <Button
              color="light"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <HiOutlineRefresh className={`h-5 w-5 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-12">
            <Spinner size="xl" />
            <span className="ml-2">Loading purchase orders...</span>
          </div>
        ) : purchaseOrders.length === 0 ? (
          <EmptyState
            icon={<HiOutlineShoppingBag className="h-12 w-12" />}
            title="No purchase orders found"
            description={
              searchQuery || statusFilter !== 'all'
                ? "Try changing your search terms or filters"
                : "Use the 'Create Purchase Order' button above to get started"
            }
            actionText={
              searchQuery || statusFilter !== 'all'
                ? "Clear filters"
                : undefined  // Remove the second create button
            }
            onAction={
              searchQuery || statusFilter !== 'all'
                ? () => {
                    setSearchQuery('');
                    setStatusFilter('all');
                    fetchPurchaseOrders('', 'all');
                  }
                : undefined  // Remove action when no filters are applied
            }
          />
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Order Number</Table.HeadCell>
                <Table.HeadCell>Supplier</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>Date</Table.HeadCell>
                <Table.HeadCell>Total</Table.HeadCell>
                <Table.HeadCell>
                  <span className="sr-only">Actions</span>
                </Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {currentOrders.map((order) => (
                  <Table.Row
                    key={order.id}
                    className="bg-white dark:border-gray-700 dark:bg-gray-800 hover:bg-gray-50"
                    onClick={() => navigate(`/purchases/orders/${order.id}`)}
                    style={{ cursor: 'pointer' }}
                  >
                    <Table.Cell className="font-medium">
                      <div className="flex items-center">
                        <HiOutlineDocumentText className="mr-2 h-5 w-5 text-gray-500" />
                        <span>{order.order_number}</span>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center">
                        <HiOutlineOfficeBuilding className="mr-2 h-4 w-4 text-gray-500" />
                        <span>{order.supplier_name}</span>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge color={getStatusColor(order.status)}>
                        {getStatusLabel(order.status)}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <div title={formatDateTime(order.order_date)}>
                        {formatDate(order.order_date)}
                      </div>
                    </Table.Cell>
                    <Table.Cell className="font-medium">
                      {formatWithCurrency(order.total_amount)}
                    </Table.Cell>
                    <Table.Cell>
                      <Button
                        color="light"
                        size="xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(`/purchases/orders/${order.id}`);
                        }}
                      >
                        View
                      </Button>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        {/* Pagination */}
        {purchaseOrders.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={purchaseOrders.length}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="orders"
          />
        )}
      </Card>
    </div>
  );
};

export default PurchaseOrders;
