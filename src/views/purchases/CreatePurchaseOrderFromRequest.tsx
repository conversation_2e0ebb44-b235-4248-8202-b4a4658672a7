import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  TextInput,
  Textarea,
  Label,
  Alert,
  Spinner,
  Table,
  Select
} from 'flowbite-react';
import {
  HiOutlineChevronLeft,
  HiOutlineExclamation,
  HiOutlineDocumentText,
  HiOutlineRefresh
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../lib/supabase';
import { getPurchaseRequestById } from '../../services/purchaseRequest';
import { createPurchaseOrderFromRequest } from '../../services/purchaseOrder';
import { getSuppliers, getDefaultSupplier, Supplier } from '../../services/supplier';
import { getProductUoms, convertQuantity } from '../../services/productUom';
import {
  getSupplierProductsByProductIds,
  getSupplierProductByProductId,
  addOrUpdateSupplierProduct
} from '../../services/supplierProduct';
import { formatDate, formatQuantityWithSeparators } from '../../utils/formatters';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import UomSelector from '../../components/uom/UomSelector';
import PurchaseOrderSupplierProductButton from '../../components/suppliers/PurchaseOrderSupplierProductButton';
import EnhancedNumberInput from '../../components/common/EnhancedNumberInput';
import { getUnitsOfMeasurement, UnitOfMeasurement } from '../../services/uom';

interface ProductUom {
  id: string;
  product_id: string;
  uom_id: string;
  conversion_factor: number;
  is_default: boolean;
  is_purchasing_unit: boolean;
  is_selling_unit: boolean;
  created_by: string | null;
  created_at: string;
  updated_at: string;
  organization_id: string;
  uom: {
    id: string;
    code: string;
    name: string;
    is_active: boolean;
    created_at: string;
    created_by: string;
    updated_at: string;
    description: string;
    organization_id: string;
  };
}

interface PurchaseOrderItem {
  requestItemId: string;
  productId: string;
  productName: string;
  quantity: number;
  originalQuantity: number;
  originalUomId: string;
  originalUomName: string;
  uomId: string;
  unitPrice: number;
  conversionFactor: number; // Required field, not optional
  notes?: string;
  productUoms: ProductUom[]; // Array of product UoMs with conversion factors
}

const CreatePurchaseOrderFromRequest = () => {
  const { id: requestId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const formatWithCurrency = useCurrencyFormatter();

  // State for the form
  const [supplierId, setSupplierId] = useState<string>('');
  const [expectedDeliveryDate, setExpectedDeliveryDate] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [items, setItems] = useState<PurchaseOrderItem[]>([]);

  // State for data loading
  const [purchaseRequest, setPurchaseRequest] = useState<any>(null);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [uoms, setUoms] = useState<UnitOfMeasurement[]>([]);
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load purchase request and suppliers
  useEffect(() => {
    const fetchData = async () => {
      if (!currentOrganization || !requestId) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch purchase request
        const { purchaseRequest: pr, error: prError } = await getPurchaseRequestById(
          currentOrganization.id,
          requestId
        );

        if (prError) {
          setError(prError);
          return;
        }

        if (!pr) {
          setError('Purchase request not found');
          return;
        }

        setPurchaseRequest(pr);

        // Get product IDs from purchase request
        const productIds = pr.items.map((item: any) => item.product_id);

        // Fetch all UOMs for each product
        const productUomsPromises = productIds.map(productId => getProductUoms(productId, currentOrganization.id));
        const productUomsResults = await Promise.all(productUomsPromises);

        // Create a map of product ID to its UOMs
        const productUomsMap = productIds.reduce((map, productId, index) => {
          map[productId] = productUomsResults[index].productUoms || [];
          return map;
        }, {} as Record<string, any[]>);

        // Initialize items from purchase request with zero prices
        // We'll update prices when a supplier is selected
        const poItems: PurchaseOrderItem[] = pr.items.map((item: any) => ({
          requestItemId: item.id,
          productId: item.product_id,
          productName: item.product?.name || 'Unknown Product',
          quantity: item.quantity,
          originalQuantity: item.quantity,
          originalUomId: item.uom_id,
          originalUomName: item.uom?.name || 'Unknown Unit',
          uomId: item.uom_id,
          unitPrice: 0, // Start with zero price until supplier is selected
          conversionFactor: item.conversion_factor || 1, // Use conversion factor from purchase request or default to 1
          notes: item.notes,
          productUoms: productUomsMap[item.product_id] || [] // Add all UOMs for this product
        }));

        setItems(poItems);

        // Fetch suppliers
        const { suppliers: supplierData, error: suppliersError } = await getSuppliers(currentOrganization.id);

        if (suppliersError) {
          console.error('Error fetching suppliers:', suppliersError);
        } else {
          setSuppliers(supplierData);

          // Don't auto-select any supplier - let user choose or use default
          // The system will automatically use default supplier if none is selected
        }

        // Fetch UoMs
        const { uoms: uomData, error: uomsError } = await getUnitsOfMeasurement(currentOrganization.id);

        if (uomsError) {
          console.error('Error fetching UoMs:', uomsError);
        } else {
          setUoms(uomData);
        }
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError(err.message || 'An error occurred while fetching data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentOrganization, requestId]);

  // Update prices when supplier changes
  useEffect(() => {
    if (supplierId && items.length > 0) {
      updatePricesForSupplier(supplierId);
    }
  }, [supplierId]);

  // Update all item prices for a supplier
  const updatePricesForSupplier = async (supplierId: string) => {
    // Get all product IDs that have been added to the order
    const productIds = items.map(item => item.productId);

    if (productIds.length === 0) return;

    try {
      const { supplierProducts, error } = await getSupplierProductsByProductIds(supplierId, productIds);

      if (error) {
        console.error('Error fetching supplier products:', error);
        return;
      }

      if (supplierProducts.length > 0) {
        // Create a map of product ID to supplier price and UoM
        const priceMap = {} as Record<string, {
          price: number;
          uomId: string | null;
          uomName: string | null;
          conversionFactor: number;
        }>;

        // First pass: collect all supplier products with prices and UoMs
        for (const sp of supplierProducts) {

          // Always create an entry in the price map, even if price is null
          priceMap[sp.product_id] = {
            price: sp.unit_price || 0,
            uomId: sp.uom_id || null,
            uomName: sp.uom?.name || sp.uom_name || null,
            conversionFactor: sp.conversion_factor || 1
          };

          // Handle UoM information if available
          if (sp.uom_id) {
            // Process UoM silently
          }
        }

        // For products not in the supplier_products table, set price to 0
        productIds.forEach(id => {
          if (!priceMap[id]) {
            priceMap[id] = {
              price: 0,
              uomId: null,
              uomName: null,
              conversionFactor: 1
            };
          }
        });

        // Update the prices and UoMs for all items
        setItems(prev => {
          const updatedItems = prev.map(item => {
            const productInfo = priceMap[item.productId];
            if (productInfo) {
              // Create updated item with price
              const updatedItem = {
                ...item,
                unitPrice: productInfo.price
              };

              // If supplier has a specific UoM for this product, update it
              if (productInfo.uomId) {
                // Always update the UoM to ensure we're using the supplier's preferred UoM
                updatedItem.uomId = productInfo.uomId;
              }

              return updatedItem;
            }
            return item;
          });

          return updatedItems;
        });
      } else {
        // If no supplier products, try to get default product prices

        // If no supplier products, try to get default product prices
        const { data: products } = await supabase
          .from('products')
          .select('id, unit_price')
          .in('id', productIds);

        if (products && products.length > 0) {
          // Create a map of product ID to default price
          const priceMap = products.reduce((map, product) => {
            if (product.unit_price) {
              map[product.id] = product.unit_price;
            }
            return map;
          }, {} as Record<string, number>);

          // Update the prices for all items
          setItems(prev => {
            return prev.map(item => {
              if (priceMap[item.productId]) {
                return {
                  ...item,
                  unitPrice: priceMap[item.productId]
                };
              }
              return item;
            });
          });
        }
      }
    } catch (err) {
      console.error('Error updating prices for supplier:', err);
    }
  };

  // Handle item field changes
  const handleItemChange = async (index: number, field: keyof PurchaseOrderItem, value: any) => {
    // Update the item in state
    setItems(prev => {
      const newItems = [...prev];
      newItems[index] = {
        ...newItems[index],
        [field]: value
      };
      return newItems;
    });

    // If the unit price was changed and we have a supplier selected, add/update the supplier product
    if (field === 'unitPrice' && supplierId && currentOrganization) {
      const item = items[index];

      try {
        // If the price is greater than 0, add/update the supplier product
        if (value > 0) {
          const { success, supplierProduct, error } = await addOrUpdateSupplierProduct(
            supplierId,
            item.productId,
            value,
            currentOrganization.id,
            item.uomId // Pass the current UoM ID
          );

          if (!success) {
            console.error('Error adding/updating supplier product:', error);
          }
        }
        // If the price is set to 0, check if this is intentional (product not supplied by this supplier)
        else if (value === 0) {

          // Check if this product is supplied by this supplier
          const { supplierProduct } = await getSupplierProductByProductId(
            supplierId,
            item.productId
          );

          // If it is a supplier product with a price, confirm with the user
          if (supplierProduct && supplierProduct.unit_price) {
            const confirmRemove = window.confirm(
              `This product is currently supplied by this supplier at a price of ${supplierProduct.unit_price}. ` +
              `Setting the price to 0 indicates this product is not supplied by this supplier. ` +
              `Do you want to continue?`
            );

            if (confirmRemove) {
              // Update the supplier product with a null price
              const { success } = await addOrUpdateSupplierProduct(
                supplierId,
                item.productId,
                0,
                currentOrganization.id,
                item.uomId // Pass the current UoM ID
              );

              // Process silently
            } else {
              // Revert to the previous price
              setItems(prev => {
                const newItems = [...prev];
                newItems[index] = {
                  ...newItems[index],
                  unitPrice: supplierProduct.unit_price || 0
                };
                return newItems;
              });
            }
          }
        }
      } catch (err) {
        console.error('Error in handleItemChange when adding supplier product:', err);
      }
    }
  };

  // Handle UoM change with quantity conversion
  const handleUomChange = async (index: number, newUomId: string) => {
    if (!currentOrganization) return;

    const item = items[index];

    try {
      // Find the conversion factor in the item's productUoms array
      let newConversionFactor = 1;

      // We should always have the productUoms array with all UoMs for this product
      if (item.productUoms && item.productUoms.length > 0) {
        const matchingUom = item.productUoms.find(pu => pu.uom_id === newUomId);
        if (matchingUom) {
          newConversionFactor = Number(matchingUom.conversion_factor);
        }
      }


      // Convert quantity from original UoM to new UoM
      const { convertedQuantity, error: conversionError } = await convertQuantity(
        item.productId,
        item.quantity,
        item.uomId,
        newUomId,
        currentOrganization.id
      );

      if (conversionError) {
        return;
      }

      // Update the item with new UoM, converted quantity, and conversion factor
      setItems(prev => {
        const newItems = [...prev];
        newItems[index] = {
          ...newItems[index],
          uomId: newUomId,
          quantity: convertedQuantity || item.quantity,
          conversionFactor: newConversionFactor
        };
        return newItems;
      });

      // If we have a supplier selected, try to get the supplier price for this UoM
      if (supplierId) {
        try {
          const { supplierProduct } = await getSupplierProductByProductId(supplierId, item.productId);

          let price = null;

          // Check if we have a supplier product with a price
          if (supplierProduct?.unit_price) {
            price = supplierProduct.unit_price;
          }
          // If supplier product exists but has no price, or if no supplier product exists
          else {
            price = 0;
          }

          // If we found a price, update the item
          if (price !== null) {
            // Update the unit price for this item
            setItems(prev => {
              const newItems = [...prev];
              newItems[index] = {
                ...newItems[index],
                unitPrice: price || 0
              };
              return newItems;
            });
          }
        } catch (err) {
          // Silent error handling in production
        }
      }
    } catch (err: any) {
      // Silent error handling in production
    }
  };

  // Reset an item to its original values
  const handleResetItem = async (index: number) => {
    if (!currentOrganization) return;

    const item = items[index];

    // Get the original conversion factor from the productUoms array
    let originalConversionFactor = 1;
    try {
      // We should always have the productUoms array with all UoMs for this product
      if (item.productUoms && item.productUoms.length > 0) {
        const matchingUom = item.productUoms.find(pu => pu.uom_id === item.originalUomId);
        if (matchingUom) {
          originalConversionFactor = Number(matchingUom.conversion_factor);
        }
      }
    } catch (err) {
      // Silent error handling in production
    }

    setItems(prev => {
      const newItems = [...prev];
      newItems[index] = {
        ...newItems[index],
        quantity: newItems[index].originalQuantity,
        uomId: newItems[index].originalUomId,
        conversionFactor: originalConversionFactor
      };
      return newItems;
    });
  };

  // Calculate total amount
  const calculateTotal = () => {
    return items.reduce((sum, item) => {
      // Find the correct conversion factor from productUoms based on the selected uomId
      const correctConversionFactor = item.productUoms?.find(pu => pu.uom_id === item.uomId)?.conversion_factor;

      // Use the found conversion factor, or fall back to item.conversionFactor, or default to 1
      const conversionFactor = Number(correctConversionFactor || item.conversionFactor || 1);

      // Calculate as (quantity * conversion factor) * unit price
      const itemTotal = (item.quantity * conversionFactor) * item.unitPrice;

      return sum + itemTotal;
    }, 0);
  };

  // Submit the form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization || !user || !requestId) {
      setError('Missing required data');
      return;
    }

    // If no supplier is selected, use the default supplier
    let finalSupplierId = supplierId;
    if (!finalSupplierId) {
      const { supplier: defaultSupplier, error: defaultSupplierError } = await getDefaultSupplier(currentOrganization.id);
      if (defaultSupplier) {
        finalSupplierId = defaultSupplier.id;
      } else {
        setError('No supplier available. Please contact support.');
        return;
      }
    }

    // Validate items
    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      if (item.quantity <= 0) {
        setError(`Please enter a valid quantity for item #${i + 1}`);
        return;
      }

      if (item.unitPrice <= 0) {
        setError(`Please enter a valid unit price for item #${i + 1}`);
        return;
      }
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Prepare the data for creating a purchase order
      const poData = {
        supplierId: finalSupplierId,
        expectedDeliveryDate: expectedDeliveryDate ? new Date(expectedDeliveryDate).toISOString() : undefined,
        notes: notes || undefined,
        items: items.map(item => {
          // Find the correct conversion factor from productUoms
          const correctConversionFactor = item.productUoms?.find(pu => pu.uom_id === item.uomId)?.conversion_factor;

          // Use the found conversion factor, or fall back to item.conversionFactor, or default to 1
          const conversionFactor = Number(correctConversionFactor || item.conversionFactor || 1);

          return {
            requestItemId: item.requestItemId,
            quantity: item.quantity,
            uomId: item.uomId,
            unitPrice: item.unitPrice,
            conversionFactor: conversionFactor
          };
        })
      };

      // Create the purchase order
      const { purchaseOrder, error: createError } = await createPurchaseOrderFromRequest(
        currentOrganization.id,
        requestId,
        poData
      );

      if (createError) {
        setError(createError);
      } else if (purchaseOrder) {
        // Navigate to the purchase order details
        navigate(`/purchases/orders/${purchaseOrder.id}`);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while creating the purchase order');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate(`/purchases/requests/${requestId}`);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error && !purchaseRequest) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamation}>
          <h3 className="font-medium">Error</h3>
          <p>{error}</p>
          <div className="mt-4">
            <Button color="gray" onClick={() => navigate('/purchases/requests')}>
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back to Purchase Requests
            </Button>
          </div>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <HiOutlineDocumentText className="mr-2 h-6 w-6" />
              Create Purchase Order
            </h1>
            <p className="text-gray-500">
              Creating purchase order from request: {purchaseRequest?.request_number}
            </p>
          </div>

          <Button color="gray" onClick={handleCancel}>
            <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
            Cancel
          </Button>
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Supplier */}
            <div>
              <div className="mb-2 block">
                <Label htmlFor="supplier_id" value="Supplier (Optional)" />
                <p className="text-xs text-gray-500 mt-1">
                  Leave empty to use default supplier, or select a specific supplier
                </p>
              </div>
              <Select
                id="supplier_id"
                value={supplierId}
                onChange={(e) => setSupplierId(e.target.value)}
              >
                <option value="">Use Default Supplier</option>
                {suppliers.filter(s => !s.is_default).map(supplier => (
                  <option key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </option>
                ))}
              </Select>
              {!supplierId && (
                <p className="text-xs text-blue-600 mt-1">
                  ℹ️ Using "{suppliers.find(s => s.is_default)?.name || 'Default Supplier'}" - you can change the supplier later
                </p>
              )}
            </div>

            {/* Expected Delivery Date */}
            <div>
              <div className="mb-2 block">
                <Label htmlFor="expected_delivery_date" value="Expected Delivery Date" />
              </div>
              <TextInput
                id="expected_delivery_date"
                type="date"
                value={expectedDeliveryDate}
                onChange={(e) => setExpectedDeliveryDate(e.target.value)}
                min={new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>

          {/* Notes */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="notes" value="Notes" />
            </div>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Enter any additional notes"
              rows={3}
            />
          </div>

          {/* Items */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <Label value="Items" />
            </div>

            <div className="overflow-x-auto">
              <Table>
                <Table.Head>
                  <Table.HeadCell>Product</Table.HeadCell>
                  <Table.HeadCell>Quantity</Table.HeadCell>
                  <Table.HeadCell>Unit</Table.HeadCell>
                  <Table.HeadCell>Unit Price</Table.HeadCell>
                  <Table.HeadCell>Total</Table.HeadCell>
                  <Table.HeadCell>
                    <span className="sr-only">Actions</span>
                  </Table.HeadCell>
                </Table.Head>
                <Table.Body className="divide-y">
                  {items.map((item, index) => (
                    <Table.Row key={index} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                      <Table.Cell className="font-medium">
                        <button
                          onClick={() => navigate(`/products/details/${item.productId}`)}
                          className="text-primary-600 hover:text-primary-800 hover:underline focus:outline-none"
                          title="View product details"
                        >
              {item.productName}
                        </button>
                      </Table.Cell>
                      <Table.Cell>
                        <EnhancedNumberInput
                          min="0.01"
                          step="0.01"
                          value={item.quantity}
                          onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                          onBlur={(e) => {
                            // If field is empty on blur, reset to 1
                            if (e.target.value === '' || parseFloat(e.target.value) <= 0) {
                              handleItemChange(index, 'quantity', 1);
                            }
                          }}
                          required
                          className="w-24"
                          autoSelect={true}
                          preventScrollChange={true}
                        />
                      </Table.Cell>
                      <Table.Cell>
                        <UomSelector
                          productId={item.productId}
                          value={item.uomId}
                          onChange={(uomId) => handleUomChange(index, uomId)}
                          filter="purchasing"
                          className="w-32"
                          preloadedUoms={item.productUoms}
                        />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex items-center gap-2">
                          <EnhancedNumberInput
                            min="0.01"
                            step="0.01"
                            value={item.unitPrice}
                            onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                            onBlur={(e) => {
                              // If field is empty on blur, reset to 0.01
                              if (e.target.value === '' || parseFloat(e.target.value) < 0) {
                                handleItemChange(index, 'unitPrice', 0.01);
                              }
                            }}
                            required
                            className="w-24"
                            autoSelect={true}
                            preventScrollChange={true}
                          />
                          {supplierId && (
                            <PurchaseOrderSupplierProductButton
                              supplierId={supplierId}
                              product={{ id: item.productId, name: item.productName }}
                              onSuccess={(unitPrice, uomId, conversionFactor = 1) => {
                                // Update the item with the new price, UoM, and conversion factor
                                const newItems = [...items];
                                newItems[index] = {
                                  ...newItems[index],
                                  unitPrice: unitPrice,
                                  conversionFactor: conversionFactor,
                                  ...(uomId && { uomId })
                                };
                                setItems(newItems);
                              }}
                              currentUnitPrice={item.unitPrice}
                              currentUomId={item.uomId}
                            />
                          )}
                        </div>
                      </Table.Cell>

                      <Table.Cell>
                        {(() => {
                          // Find the correct conversion factor from productUoms based on the selected uomId
                          const correctConversionFactor = item.productUoms?.find(pu => pu.uom_id === item.uomId)?.conversion_factor;

                          // Use the found conversion factor, or fall back to item.conversionFactor, or default to 1
                          const conversionFactor = Number(correctConversionFactor || item.conversionFactor || 1);

                          // Calculate the total
                          const total = (item.quantity * conversionFactor) * item.unitPrice;

                          return (
                            <>
                              {formatWithCurrency(total)}
                              <div className="text-xs text-gray-500">
                                {formatQuantityWithSeparators(item.quantity)} {uoms.find(u => u.id === item.uomId)?.code || 'unit'} × {formatWithCurrency(item.unitPrice)}
                              </div>
                            </>
                          );
                        })()}
                      </Table.Cell>
                      <Table.Cell>
                        <Button
                          color="light"
                          size="xs"
                          onClick={() => handleResetItem(index)}
                          title="Reset to original quantity and unit"
                        >
                          <HiOutlineRefresh className="h-4 w-4" />
                        </Button>
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </div>

            <div className="flex justify-end mt-4">
              <div className="text-lg font-semibold">
                Total: {formatWithCurrency(calculateTotal())}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button type="submit" color="primary" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Creating...
                </>
              ) : (
                'Create Purchase Order'
              )}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default CreatePurchaseOrderFromRequest;
