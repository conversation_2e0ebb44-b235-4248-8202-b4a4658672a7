import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  TextInput,
  Textarea,
  Label,
  Alert,
  Spinner,
  Table,
  Select,
  Modal,
  Tooltip
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlineTrash,
  HiOutlineExclamation,
  HiOutlineChevronLeft,
  HiOutlineCog,
  HiOutlineSave,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { Product } from '../../services/product';
import UomSelector from '../../components/uom/UomSelector';
import QuickUomSetup from '../../components/uom/QuickUomSetup';
import EnhancedProductSearchSelector from '../../components/products/EnhancedProductSearchSelector';
import { ProductUom, UnitOfMeasurement } from '../../types/uom.types';
import { createPurchaseRequest, PurchaseRequestInsert, PurchaseRequestItemInsert } from '../../services/purchaseRequest';
import { productCache } from '../../services/productCache';
import { useAutoSave } from '../../hooks/useAutoSave';
import { useKeyboardShortcut, formatKeyboardShortcut } from '../../hooks/useKeyboardShortcut';
import { useFormValidation, ValidationRules } from '../../hooks/useFormValidation';
import FormInput from '../../components/common/FormInput';
import FormErrorSummary from '../../components/common/FormErrorSummary';
import EnhancedNumberInput from '../../components/common/EnhancedNumberInput';
import AutoSaveIndicator from '../../components/common/AutoSaveIndicator';
import ConfirmationDialog from '../../components/common/ConfirmationDialog';
import KeyboardShortcutTooltip from '../../components/common/KeyboardShortcutTooltip';

// Define the purchase request item type
interface PurchaseRequestItem {
  id?: string;
  product_id: string;
  product?: Product;  // Store the full product object for easy access
  quantity: number;
  uom_id: string;
  uom?: ProductUom & { uom: UnitOfMeasurement };
  notes?: string;
}

// Define the purchase request type
interface PurchaseRequest {
  organization_id: string;
  request_number: string;
  requester_id: string;
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'cancelled';
  notes?: string;
  items: PurchaseRequestItem[];
}

const CreatePurchaseRequest = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();

  // Form validation configuration
  const validationConfig = {
    fields: {
      notes: [
        ValidationRules.maxLength<Omit<PurchaseRequest, 'items'>>(500, 'Notes must be less than 500 characters')
      ]
    },
    form: [
      {
        validate: (_, formData: any) => {
          return formData.items && formData.items.length > 0;
        },
        message: 'Please add at least one item to the purchase request'
      }
    ]
  };

  // Use form validation hook
  const {
    formData,
    setFormData,
    errors,
    formErrors,
    isValid,
    validateForm,
    handleChange,
    setFormDataWithoutValidation
  } = useFormValidation<Omit<PurchaseRequest, 'items'> & { items: PurchaseRequestItem[] }>({
    organization_id: currentOrganization?.id || '',
    request_number: '',
    requester_id: user?.id || '',
    status: 'draft',
    notes: '',
    items: []
  }, validationConfig);

  // State for products (used as a cache)
  const [products, setProducts] = useState<Product[]>([]);

  // State for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for UoM setup modal
  const [showUomSetupModal, setShowUomSetupModal] = useState(false);
  const [selectedProductForUom, setSelectedProductForUom] = useState<string>('');

  // State for confirmation dialog
  const [showLeaveConfirmation, setShowLeaveConfirmation] = useState(false);
  const [navigationTarget, setNavigationTarget] = useState<string | null>(null);

  // Auto-save functionality
  const {
    save: saveFormData,
    clear: clearSavedFormData,
    lastSavedFormatted,
    isDirty,
    isSaving,
    hasSavedData
  } = useAutoSave({
    key: 'purchase-request-draft',
    initialData: formData,
    interval: 5000,
    enabled: true,
    onSave: (data) => {
      // Optional callback when data is saved
    }
  });

  // Keyboard shortcuts
  useKeyboardShortcut(
    { key: 's', ctrl: true },
    {
      callback: () => {
        if (!isSubmitting) {
          handleSubmit();
        }
      },
      isEnabled: true
    }
  );

  useKeyboardShortcut(
    { key: 'a', ctrl: true },
    {
      callback: () => {
        handleAddItem();
      },
      isEnabled: true
    }
  );

  // Helper function to get items from form data
  const items = formData.items || [];

  // Helper function to update items
  const setItems = (updater: (items: PurchaseRequestItem[]) => PurchaseRequestItem[]) => {
    if (typeof updater === 'function') {
      const newItems = updater(items);
      setFormDataWithoutValidation({
        ...formData,
        items: newItems
      });
    } else {
      setFormDataWithoutValidation({
        ...formData,
        items: updater
      });
    }
  };

  // Generate a request number when the component mounts
  useEffect(() => {
    if (currentOrganization) {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

      setFormData(prev => ({
        ...prev,
        organization_id: currentOrganization.id,
        request_number: `PR-${year}${month}${day}-${random}`
      }));
    }
  }, [currentOrganization]);

  // Fetch some initial products when the component mounts (for the cache)
  useEffect(() => {
    const fetchProducts = async () => {
      if (!currentOrganization) return;

      try {
        // Use the product cache service to search for products
        const { products: productData } = await productCache.searchProducts(
          currentOrganization.id,
          '', // Empty search query to get all products
          1,  // First page
          20  // Limit to 20 products for initial load
        );

        setProducts(productData);
      } catch (err: any) {
        console.error('Error fetching products:', err);
      }
    };

    fetchProducts();
  }, [currentOrganization]);

  // Handle form field changes for input elements
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    handleChange(name as any, value);
  };

  // Add a new item to the purchase request
  const handleAddItem = () => {
    if (products.length === 0) return;

    const newItem: PurchaseRequestItem = {
      product_id: '',
      quantity: 1,
      uom_id: ''
    };

    setItems(prev => [...prev, newItem]);
  };

  // Remove an item from the purchase request
  const handleRemoveItem = (index: number) => {
    setItems(prev => prev.filter((_, i) => i !== index));
  };

  // Handle item field changes
  const handleItemChange = (index: number, field: keyof PurchaseRequestItem, value: any) => {
    setItems(prev => {
      const newItems = [...prev];
      newItems[index] = {
        ...newItems[index],
        [field]: value
      };

      // If product_id changed, reset uom_id
      if (field === 'product_id') {
        newItems[index].uom_id = '';
        newItems[index].uom = undefined;
      }

      return newItems;
    });

    // If product_id changed, set the default UoM from the product's UoMs
    if (field === 'product_id' && value) {
      // The product should be available in the item.product property
      // or we need to find it using the findProduct function
      const setDefaultUom = async () => {
        try {
          // First check if we already have the product in the item
          if (items[index].product) {
            const product = items[index].product;
            
            // If we have the product and it has UoMs
            if (product && product.product_uoms && product.product_uoms.length > 0) {
              // Find the default UoM
              const defaultUom = product.product_uoms.find(u => u.is_default);

              if (defaultUom) {
                // Update the item with the default UoM
                setItems(prevItems => {
                  const updatedItems = [...prevItems];
                  // Make sure we're updating the correct item (in case the array changed)
                  if (updatedItems[index] && updatedItems[index].product_id === value) {
                    updatedItems[index].uom_id = defaultUom.uom_id;
                    updatedItems[index].uom = defaultUom;
                  }
                  return updatedItems;
                });
              }
            }
          } else {
            // If we don't have the product, fetch it
            const product = await findProduct(value);

            if (product) {
              // Store the product in the item for future reference
              setItems(prevItems => {
                const updatedItems = [...prevItems];
                if (updatedItems[index] && updatedItems[index].product_id === value) {
                  updatedItems[index].product = product;
                }
                return updatedItems;
              });

              // If the product has UoMs, set the default one
              if (product.product_uoms && product.product_uoms.length > 0) {
                const defaultUom = product.product_uoms.find(u => u.is_default);

                if (defaultUom) {
                  // Update the item with the default UoM
                  setItems(prevItems => {
                    const updatedItems = [...prevItems];
                    // Make sure we're updating the correct item (in case the array changed)
                    if (updatedItems[index] && updatedItems[index].product_id === value) {
                      updatedItems[index].uom_id = defaultUom.uom_id;
                      updatedItems[index].uom = defaultUom;
                    }
                    return updatedItems;
                  });
                } else if (product.product_uoms.length > 0) {
                  // If no default UoM is found, use the first one
                  const firstUom = product.product_uoms[0];

                  setItems(prevItems => {
                    const updatedItems = [...prevItems];
                    if (updatedItems[index] && updatedItems[index].product_id === value) {
                      updatedItems[index].uom_id = firstUom.uom_id;
                      updatedItems[index].uom = firstUom;
                    }
                    return updatedItems;
                  });
                }
              }
            }
          }
        } catch (err) {
          console.error('Error setting default UoM:', err);
        }
      };

      // Execute the async function
      setDefaultUom();
    }
  };

  // Handle UoM change
  const handleUomChange = (index: number, uomId: string, uom?: ProductUom & { uom: UnitOfMeasurement }) => {
    if (!uomId || !uom) {
      return;
    }

    // Update the item with the new UoM
    setItems(prev => {
      const newItems = [...prev];

      // Make sure we're updating the correct item
      if (index >= 0 && index < newItems.length) {
        // Create a new object to ensure React detects the change
        newItems[index] = {
          ...newItems[index],
          uom_id: uomId,
          uom: {
            ...uom
          }
        };
      }

      return newItems;
    });
  };

  // Handle navigation with unsaved changes check
  const handleNavigation = (path: string) => {
    // If there are unsaved changes, show confirmation dialog
    if (isDirty) {
      setNavigationTarget(path);
      setShowLeaveConfirmation(true);
    } else {
      // No unsaved changes, navigate directly
      navigate(path);
    }
  };

  // Confirm navigation and clear saved data
  const confirmNavigation = () => {
    if (navigationTarget) {
      clearSavedFormData();
      navigate(navigationTarget);
    }
    setShowLeaveConfirmation(false);
  };

  // Submit the form
  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    if (!currentOrganization || !user) {
      setError('Organization or user not found');
      return;
    }

    // Validate the form
    const isFormValid = validateForm();

    if (!isFormValid) {
      // Form validation failed, show error summary
      setError('Please fix the validation errors before submitting');
      return;
    }

    // Additional validation for items
    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      if (!item.product_id) {
        setError(`Please select a product for item #${i + 1}`);
        return;
      }

      if (!item.uom_id) {
        setError(`Please select a unit of measurement for item #${i + 1}`);
        return;
      }

      if (item.quantity <= 0) {
        setError(`Please enter a valid quantity for item #${i + 1}`);
        return;
      }
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Save form data one last time before submission
      saveFormData();

      // Prepare the purchase request data
      const purchaseRequestData: PurchaseRequestInsert = {
        organization_id: currentOrganization.id,
        request_number: formData.request_number,
        requester_id: user.id,
        status: 'draft',
        notes: formData.notes || null
      };

      // Prepare the items data
      const itemsData: Omit<PurchaseRequestItemInsert, 'purchase_request_id'>[] = items.map(item => ({
        product_id: item.product_id,
        quantity: item.quantity,
        uom_id: item.uom_id,
        base_quantity: item.quantity, // This will be calculated by the database trigger
        notes: item.notes || null
      }));

      // Create the purchase request
      const { error: createError } = await createPurchaseRequest(
        purchaseRequestData,
        itemsData
      );

      if (createError) {
        console.error('Error creating purchase request:', createError);
        setError(createError);
      } else {
        // Clear saved data
        clearSavedFormData();

        // Navigate to the purchase requests list
        navigate('/purchases/requests');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while creating the purchase request');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Find a product by ID - first check in items, then in products array, then in cache
  const findProduct = useCallback(async (productId: string): Promise<Product | undefined> => {
    // First check if the product is stored in any of the items
    const itemWithProduct = items.find(item => item.product_id === productId && item.product);
    if (itemWithProduct && itemWithProduct.product) {
      return itemWithProduct.product;
    }

    // If not found in items, check the products array
    const productInArray = products.find(p => p.id === productId);
    if (productInArray) {
      return productInArray;
    }

    // If not found in memory, try to get it from the cache
    if (currentOrganization) {
      try {
        const cachedProduct = await productCache.getProduct(currentOrganization.id, productId);
        if (cachedProduct) {
          // Add to local products array for future reference
          setProducts(prev => {
            if (!prev.some(p => p.id === cachedProduct.id)) {
              return [...prev, cachedProduct];
            }
            return prev;
          });
          return cachedProduct;
        }
      } catch (err) {
        console.error('Error fetching product from cache:', err);
      }
    }

    return undefined;
  }, [items, products, currentOrganization]);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Create Purchase Request</h1>
            <AutoSaveIndicator
              isSaving={isSaving}
              isDirty={isDirty}
              lastSavedFormatted={lastSavedFormatted}
              className="mt-1"
            />
          </div>
          <div className="flex gap-2">
            <Button
              color="gray"
              onClick={() => handleNavigation('/purchases/requests')}
              title="Go back to purchase requests list"
            >
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back
            </Button>
            <Tooltip content="Save purchase request (Ctrl+S)">
              <Button
                color="primary"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                <HiOutlineSave className="mr-2 h-5 w-5" />
                Save
              </Button>
            </Tooltip>
          </div>
        </div>

        {/* Error summary */}
        <FormErrorSummary
          errors={errors}
          formErrors={formErrors}
          fieldLabels={{
            notes: 'Notes',
            items: 'Items'
          }}
          show={!isValid && Object.keys(errors).length > 0 || formErrors.length > 0}
          className="mb-4"
        />

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Request Number */}
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="request_number" value="Request Number" className="text-sm font-medium" />
                <Tooltip content="Automatically generated unique identifier for this purchase request">
                  <HiOutlineInformationCircle className="ml-1 h-4 w-4 text-gray-400 hover:text-gray-600" />
                </Tooltip>
              </div>
              <TextInput
                id="request_number"
                name="request_number"
                value={formData.request_number}
                onChange={(e) => handleChange('request_number', e.target.value)}
                disabled
              />
            </div>

            {/* Status */}
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="status" value="Status" className="text-sm font-medium" />
                <Tooltip content="Purchase requests start as drafts and can be submitted for approval later">
                  <HiOutlineInformationCircle className="ml-1 h-4 w-4 text-gray-400 hover:text-gray-600" />
                </Tooltip>
              </div>
              <Select
                id="status"
                name="status"
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
                disabled
              >
                <option value="draft">Draft</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="cancelled">Cancelled</option>
              </Select>
            </div>
          </div>

          {/* Notes */}
          <div>
            <div className="flex items-center mb-1">
              <Label htmlFor="notes" value="Notes" className="text-sm font-medium" />
              <Tooltip content="Add any additional information about this purchase request">
                <HiOutlineInformationCircle className="ml-1 h-4 w-4 text-gray-400 hover:text-gray-600" />
              </Tooltip>
            </div>
            <Textarea
              id="notes"
              name="notes"
              value={formData.notes || ''}
              onChange={(e) => handleChange('notes', e.target.value)}
              placeholder="Enter any additional notes"
              rows={3}
              color={errors.notes && errors.notes.length > 0 ? 'failure' : undefined}
              helperText={
                errors.notes && errors.notes.length > 0 ? (
                  <ul className="mt-1 text-sm text-red-600 list-disc list-inside">
                    {errors.notes.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                ) : null
              }
            />
          </div>

          {/* Items */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center">
                <Label value="Items" className="text-sm font-medium" />
                <Tooltip content="Products to be included in this purchase request">
                  <HiOutlineInformationCircle className="ml-1 h-4 w-4 text-gray-400 hover:text-gray-600" />
                </Tooltip>
              </div>
              <div className="flex items-center gap-2">
                <KeyboardShortcutTooltip shortcut={{ key: 'a', ctrl: true }} />
                <Button size="xs" color="primary" onClick={handleAddItem} title="Add item (Ctrl+A)">
                  <HiOutlinePlus className="mr-2 h-4 w-4" />
                  Add Item
                </Button>
              </div>
            </div>

            {items.length === 0 ? (
              <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
                No items added. Click "Add Item" to add products to this purchase request.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <Table.Head>
                    <Table.HeadCell>Product</Table.HeadCell>
                    <Table.HeadCell>Quantity</Table.HeadCell>
                    <Table.HeadCell>Unit</Table.HeadCell>
                    <Table.HeadCell>Notes</Table.HeadCell>
                    <Table.HeadCell>
                      <span className="sr-only">Actions</span>
                    </Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {items.map((item, index) => (
                      <Table.Row key={index} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                        <Table.Cell>
                          <EnhancedProductSearchSelector
                            value={item.product_id}
                            onChange={(productId: string, product: Product | null) => {
                              // Store the product object in the item for easy access
                              if (product) {
                                setItems(prev => {
                                  const newItems = [...prev];
                                  newItems[index] = {
                                    ...newItems[index],
                                    product_id: productId,
                                    product: product
                                  };
                                  return newItems;
                                });
                              } else {
                                // If no product, just update the product_id
                                handleItemChange(index, 'product_id', productId);
                              }
                            }}
                            required
                            className="text-sm"
                            placeholder="Search for a product..."
                            pageSize={5}
                            instanceId={`pr-product-${index}`}
                          />
                        </Table.Cell>
                        <Table.Cell>
                          <EnhancedNumberInput
                            min="0.01"
                            step="0.01"
                            value={item.quantity}
                            onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                            onBlur={(e) => {
                              // If field is empty on blur, reset to 1
                              if (e.target.value === '' || parseFloat(e.target.value) <= 0) {
                                handleItemChange(index, 'quantity', 1);
                              }
                            }}
                            required
                            className="text-sm"
                            sizing="sm"
                            autoSelect={true}
                            preventScrollChange={true}
                          />
                        </Table.Cell>
                        <Table.Cell>
                          {/* Debug info: {item.product_id ? `Product ID: ${item.product_id}, Has product object: ${!!item.product}` : 'No product selected'} */}
                          {item.product_id ? (
                            <div className="flex items-center space-x-2">
                              <div className="flex-grow">
                                <UomSelector
                                  key={`uom-selector-${item.product_id}-${index}`}
                                  productId={item.product_id}
                                  value={item.uom_id || ''}
                                  onChange={(uomId) => {
                                    // Update the UoM ID directly
                                    setItems(prev => {
                                      const newItems = [...prev];
                                      if (index >= 0 && index < newItems.length) {
                                        newItems[index] = {
                                          ...newItems[index],
                                          uom_id: uomId
                                        };
                                      }
                                      return newItems;
                                    });
                                  }}
                                  onUomChange={(uom) => {
                                    handleUomChange(index, uom.uom_id, uom);
                                  }}
                                  filter="all"
                                  disabled={false} // Allow UoM selection
                                  preloadedUoms={item.product?.product_uoms}
                                />
                              </div>
                              <div>
                                <Button
                                  size="xs"
                                  color="light"
                                  className="p-1"
                                  title="Setup Units of Measurement"
                                  onClick={() => {
                                    setSelectedProductForUom(item.product_id);
                                    setShowUomSetupModal(true);
                                  }}
                                >
                                  <HiOutlineCog className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-2">
                              <div className="flex-grow">
                                <div className="text-sm text-gray-500 p-2 border border-gray-300 rounded-lg bg-gray-50">
                                  Select a product first
                                </div>
                              </div>
                              <div className="w-8"></div>
                            </div>
                          )}
                        </Table.Cell>
                        <Table.Cell>
                          <TextInput
                            value={item.notes || ''}
                            onChange={(e) => handleItemChange(index, 'notes', e.target.value)}
                            placeholder="Optional notes"
                            className="text-sm"
                            sizing="sm"
                          />
                        </Table.Cell>
                        <Table.Cell>
                          <Button color="failure" size="xs" onClick={() => handleRemoveItem(index)}>
                            <HiOutlineTrash className="h-4 w-4" />
                          </Button>
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <KeyboardShortcutTooltip shortcut={{ key: 's', ctrl: true }} className="mr-2" />
              <span className="text-sm text-gray-500">Press Ctrl+S to save</span>
            </div>
            <Button type="submit" color="primary" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Saving...
                </>
              ) : (
                'Create Purchase Request'
              )}
            </Button>
          </div>
        </form>
      </Card>

      {/* UoM Setup Modal */}
      <Modal
        show={showUomSetupModal}
        onClose={() => setShowUomSetupModal(false)}
        size="xl"
      >
        <Modal.Header>Configure Units of Measurement for Purchasing</Modal.Header>
        <Modal.Body>
          {selectedProductForUom && (
            <QuickUomSetup
              productId={selectedProductForUom}
              onComplete={() => {
                setShowUomSetupModal(false);
                // Force a re-render of the UomSelector by clearing and re-setting the product
                const affectedItemIndex = items.findIndex(item => item.product_id === selectedProductForUom);
                if (affectedItemIndex >= 0) {
                  const productId = items[affectedItemIndex].product_id;
                  setItems(prev => {
                    const newItems = [...prev];
                    newItems[affectedItemIndex] = {
                      ...newItems[affectedItemIndex],
                      uom_id: '' // Clear the UoM to force re-selection
                    };
                    return newItems;
                  });
                  // Wait a moment and then re-set the product to trigger a refresh
                  setTimeout(() => {
                    setItems(prev => {
                      const newItems = [...prev];
                      newItems[affectedItemIndex] = {
                        ...newItems[affectedItemIndex],
                        product_id: productId // Re-set the same product ID to trigger a refresh
                      };
                      return newItems;
                    });
                  }, 100);
                }
              }}
            />
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowUomSetupModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Confirmation Dialog for Unsaved Changes */}
      <ConfirmationDialog
        show={showLeaveConfirmation}
        title="Unsaved Changes"
        message="You have unsaved changes. Are you sure you want to leave this page? Your changes will be lost."
        confirmText="Leave Page"
        cancelText="Stay on Page"
        confirmColor="failure"
        onClose={() => setShowLeaveConfirmation(false)}
        onConfirm={confirmNavigation}
      />
    </div>
  );
};

export default CreatePurchaseRequest;
