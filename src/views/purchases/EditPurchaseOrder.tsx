import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  TextInput,
  Textarea,
  Label,
  Alert,
  Spinner,
  Table,
  Select,
  Tooltip
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlineTrash,
  HiOutlineExclamation,
  HiOutlineChevronLeft,
  HiOutlineSave
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { Product } from '../../services/product';
import { Supplier, getSuppliers } from '../../services/supplier';
import { UnitOfMeasurement, getUnitsOfMeasurement } from '../../services/uom';
import { useProductUomCache } from '../../context/ProductUomContext';
import { batchFetchProductUoms } from '../../utils/batchFetchProductUoms';
import { ProductUom } from '../../types/uom.types'; // Add this import
import {
  getPurchaseOrderById,
  updatePurchaseOrder,
  PurchaseOrderWithItems
} from '../../services/purchaseOrder';
import EnhancedProductSearchSelector from '../../components/products/EnhancedProductSearchSelector';
import UomSelector from '../../components/uom/UomSelector';
import { formatDate } from '../../utils/formatters';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import EnhancedNumberInput from '../../components/common/EnhancedNumberInput';

// Define the item type for the form
interface PurchaseOrderItem {
  id?: string;
  productId: string;
  product?: Product;
  quantity: number;
  uomId: string;
  unitPrice: number;
  conversionFactor?: number;
  productUoms: ProductUom[]; // New property to hold all product UOMs
}

const EditPurchaseOrder = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const productUomCache = useProductUomCache();
  const formatWithCurrency = useCurrencyFormatter();

  const [purchaseOrder, setPurchaseOrder] = useState<PurchaseOrderWithItems | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [supplierId, setSupplierId] = useState<string>('');
  const [expectedDeliveryDate, setExpectedDeliveryDate] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [items, setItems] = useState<PurchaseOrderItem[]>([]);

  // Reference data
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [uoms, setUoms] = useState<UnitOfMeasurement[]>([]);

  useEffect(() => {
    const fetchPurchaseOrder = async () => {
      if (!id || !currentOrganization) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch the purchase order
        const { purchaseOrder: po, error: poError } = await getPurchaseOrderById(
          currentOrganization.id,
          id
        );

        if (poError) {
          setError(poError);
          return;
        }

        if (!po) {
          setError('Purchase order not found');
          return;
        }

        // Only allow editing draft purchase orders
        if (po.status !== 'draft') {
          setError('Only draft purchase orders can be edited');
          return;
        }

        setPurchaseOrder(po);

        // Set form values
        setSupplierId(po.supplier_id);
        setExpectedDeliveryDate(po.expected_delivery_date ? formatDate(po.expected_delivery_date, 'yyyy-MM-dd') : '');
        setNotes(po.notes || '');

        // Preload all product UoMs for the items
        const productIds = po.items.map(item => item.product_id).filter(Boolean);
        let productUomsByProduct: Record<string, ProductUom[]> = {};

        if (productIds.length > 0 && currentOrganization) {
          try {
            const productUoms = await batchFetchProductUoms(productIds, currentOrganization.id);

            productUomsByProduct = productUoms;

            // Add all fetched UoMs to the cache
            Object.entries(productUoms).forEach(([productId, uoms]) => {
              if (uoms && uoms.length > 0) {
                productUomCache.addToCache(productId, uoms);
              }
            });
          } catch (err) {
            console.error('Error preloading product UoMs:', err);
          }
        }

        // Transform items for the form
        const formItems: PurchaseOrderItem[] = po.items.map(item => {
          const pUoms = productUomsByProduct[item.product_id] || [];
          // Find the UoM record for the one this line originally used
          const matching = pUoms.find(u => u.uom_id === item.uom_id);

          return {
            id: item.id,
            productId: item.product_id,
            product: item.product,
            quantity: item.quantity,
            uomId: item.uom_id,
            unitPrice: item.unit_price,
            productUoms: pUoms,
            conversionFactor: Number(item.conversion_factor ?? matching?.conversion_factor ?? 1)
          };
        });

        setItems(formItems);

        // Fetch suppliers
        const { suppliers: supplierList, error: suppliersError } = await getSuppliers(
          currentOrganization.id
        );

        if (suppliersError) {
          console.error('Error fetching suppliers:', suppliersError);
        } else {
          setSuppliers(supplierList);
        }

        // Fetch UoMs
        const { uoms: uomList, error: uomsError } = await getUnitsOfMeasurement(
          currentOrganization.id
        );

        if (uomsError) {
          console.error('Error fetching UoMs:', uomsError);
        } else {
          setUoms(uomList);
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the purchase order');
      } finally {
        setLoading(false);
      }
    };

    fetchPurchaseOrder();
  }, [id, currentOrganization]);

  const handleAddItem = () => {
    setItems([
      ...items,
      {
        productId: '',
        quantity: 1,
        uomId: '',
        unitPrice: 0,
        conversionFactor: 1,
        productUoms: [] // Initialize empty product UOMs array for new items
      }
    ]);
  };

  const handleRemoveItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const handleItemChange = (index: number, field: keyof PurchaseOrderItem, value: any) => {
    const newItems = [...items];
    newItems[index] = {
      ...newItems[index],
      [field]: value
    };
    setItems(newItems);
  };

  const calculateTotal = () => {
    return items.reduce((sum, item) => {
      // Look up the conversion factor from product UOMs first
      const cf =
        item.productUoms.find(pu => pu.uom_id === item.uomId)?.conversion_factor
        ?? item.conversionFactor
        ?? 1;

      // Calculate as quantity * conversion factor * unit price
      const itemTotal = item.quantity * cf * item.unitPrice;

      return sum + itemTotal;
    }, 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization || !id) {
      setError('Organization or purchase order ID not found');
      return;
    }

    // Validate form
    if (!supplierId) {
      setError('Please select a supplier');
      return;
    }

    // Validate items
    if (items.length === 0) {
      setError('Please add at least one item');
      return;
    }

    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      if (!item.productId) {
        setError(`Please select a product for item #${i + 1}`);
        return;
      }

      if (!item.uomId) {
        setError(`Please select a unit of measurement for item #${i + 1}`);
        return;
      }

      if (item.quantity <= 0) {
        setError(`Please enter a valid quantity for item #${i + 1}`);
        return;
      }

      if (item.unitPrice < 0) {
        setError(`Please enter a valid unit price for item #${i + 1}`);
        return;
      }
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Update the purchase order
      const { error: updateError } = await updatePurchaseOrder(
        currentOrganization.id,
        id,
        {
          supplierId,
          expectedDeliveryDate: expectedDeliveryDate || undefined,
          notes: notes || undefined,
          items: items.map(item => ({
            ...item,
            // Ensure conversion factor is included and is a number
            conversionFactor: Number(item.conversionFactor || 1)
          }))
        }
      );

      if (updateError) {
        setError(updateError);
      } else {
        // Navigate back to the purchase order details page
        navigate(`/purchases/orders/${id}`);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while updating the purchase order');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamation}>
          <h3 className="font-medium">Error</h3>
          <p>{error}</p>
          <div className="mt-4">
            <Button color="gray" onClick={() => navigate(`/purchases/orders/${id}`)}>
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back to Purchase Order
            </Button>
          </div>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Edit Purchase Order</h1>
            {purchaseOrder && (
              <p className="text-gray-500">
                Order Number: {purchaseOrder.order_number}
              </p>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              color="gray"
              onClick={() => navigate(`/purchases/orders/${id}`)}
              title="Go back to purchase order details"
            >
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back
            </Button>
            <Tooltip content="Save changes">
              <Button
                color="primary"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                <HiOutlineSave className="mr-2 h-5 w-5" />
                Save
              </Button>
            </Tooltip>
          </div>
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Supplier */}
            <div>
              <div className="mb-1">
                <Label htmlFor="supplier_id" value="Supplier" className="text-sm font-medium" />
              </div>
              <Select
                id="supplier_id"
                value={supplierId}
                onChange={(e) => setSupplierId(e.target.value)}
                required
              >
                <option value="">Select a supplier</option>
                {suppliers.map((supplier) => (
                  <option key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </option>
                ))}
              </Select>
            </div>

            {/* Expected Delivery Date */}
            <div>
              <div className="mb-1">
                <Label htmlFor="expected_delivery_date" value="Expected Delivery Date" className="text-sm font-medium" />
              </div>
              <TextInput
                id="expected_delivery_date"
                type="date"
                value={expectedDeliveryDate}
                onChange={(e) => setExpectedDeliveryDate(e.target.value)}
              />
            </div>
          </div>

          {/* Notes */}
          <div>
            <div className="mb-1">
              <Label htmlFor="notes" value="Notes" className="text-sm font-medium" />
            </div>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Enter any additional notes"
              rows={3}
            />
          </div>

          {/* Items */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <Label value="Items" className="text-sm font-medium" />
              <Button size="xs" color="primary" onClick={handleAddItem}>
                <HiOutlinePlus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </div>

            {items.length === 0 ? (
              <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
                No items added. Click "Add Item" to add products to this purchase order.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <Table.Head>
                    <Table.HeadCell>Product</Table.HeadCell>
                    <Table.HeadCell>Quantity</Table.HeadCell>
                    <Table.HeadCell>Unit</Table.HeadCell>
                    <Table.HeadCell>Unit Price</Table.HeadCell>
                    <Table.HeadCell>Total</Table.HeadCell>
                    <Table.HeadCell>
                      <span className="sr-only">Actions</span>
                    </Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {items.map((item, index) => (
                      <Table.Row key={index} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                        <Table.Cell>
                          <EnhancedProductSearchSelector
                            value={item.productId}
                            onChange={(productId: string, product: Product | null) => {
                              // Store the product object in the item for easy access
                              if (product) {
                                const newItems = [...items];
                                newItems[index] = {
                                  ...newItems[index],
                                  productId,
                                  product
                                };
                                setItems(newItems);
                              } else {
                                handleItemChange(index, 'productId', productId);
                              }
                            }}
                            required
                            className="text-sm"
                            placeholder="Search for a product..."
                            pageSize={5}
                          />
                        </Table.Cell>
                        <Table.Cell>
                          <EnhancedNumberInput
                            min="0.01"
                            step="0.01"
                            value={item.quantity}
                            onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                            onBlur={(e) => {
                              // If field is empty on blur, reset to 1
                              if (e.target.value === '' || parseFloat(e.target.value) <= 0) {
                                handleItemChange(index, 'quantity', 1);
                              }
                            }}
                            required
                            className="text-sm"
                            sizing="sm"
                            autoSelect={true}
                            preventScrollChange={true}
                          />
                        </Table.Cell>
                        <Table.Cell>
                          {item.productId ? (
                            <UomSelector
                              key={`uom-selector-${item.productId}-${index}`}
                              productId={item.productId}
                              value={item.uomId || ''}
                              onChange={(uomId) => handleItemChange(index, 'uomId', uomId)}
                              onUomChange={(uom) => {
                                // Update both the UoM ID and the conversion factor
                                const newItems = [...items];
                                newItems[index] = {
                                  ...newItems[index],
                                  uomId: uom.uom_id,
                                  conversionFactor: Number(uom.conversion_factor ?? 1),
                                };

                                // Update the state with the new items array
                                setItems(newItems);
                              }}
                              filter="all"
                              disabled={false}
                              preloadedUoms={item.product?.product_uoms}
                            />
                          ) : (
                            <div className="text-sm text-gray-500 p-2 border border-gray-300 rounded-lg bg-gray-50">
                              Select a product first
                            </div>
                          )}
                        </Table.Cell>
                        <Table.Cell>
                          <EnhancedNumberInput
                            min="0"
                            step="0.01"
                            value={item.unitPrice}
                            onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                            onBlur={(e) => {
                              // If field is empty on blur, reset to 0.01
                              if (e.target.value === '' || parseFloat(e.target.value) < 0) {
                                handleItemChange(index, 'unitPrice', 0.01);
                              }
                            }}
                            required
                            className="text-sm"
                            sizing="sm"
                            autoSelect={true}
                            preventScrollChange={true}
                          />
                        </Table.Cell>
                        <Table.Cell>
                          {(() => {
                            // Lookup CF off the productUoms array first
                            const cf =
                              item.productUoms.find(pu => pu.uom_id === item.uomId)?.conversion_factor
                              ?? item.conversionFactor
                              ?? 1;

                            const lineTotal = item.quantity * cf * item.unitPrice;
                            const code = uoms.find(u => u.id === item.uomId)?.code ?? 'unit';

                            return (
                              <>
                                {formatWithCurrency(lineTotal)}
                                <div className="text-xs text-gray-500">
                                  {item.quantity} {code} × CF:{cf} × {formatWithCurrency(item.unitPrice)}
                                </div>
                              </>
                            );
                          })()}
                        </Table.Cell>
                        <Table.Cell>
                          <Button color="failure" size="xs" onClick={() => handleRemoveItem(index)}>
                            <HiOutlineTrash className="h-4 w-4" />
                          </Button>
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
            )}

            <div className="flex justify-end mt-4">
              <div className="text-lg font-semibold">
                Total: {formatWithCurrency(calculateTotal())}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button type="submit" color="primary" disabled={isSubmitting} onClick={handleSubmit}>
              {isSubmitting ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default EditPurchaseOrder;
