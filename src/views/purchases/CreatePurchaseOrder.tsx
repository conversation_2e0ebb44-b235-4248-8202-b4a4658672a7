import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  TextInput,
  Textarea,
  Label,
  Alert,
  Spinner,
  Table,
  Select
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlineTrash,
  HiOutlineExclamation,
  HiOutlineChevronLeft,
  HiOutlineDocumentText
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../lib/supabase';
import { createPurchaseOrder } from '../../services/purchaseOrder';
import { getSuppliers, getDefaultSupplier, Supplier } from '../../services/supplier';
import { getProducts, Product } from '../../services/product';
import { getUnitsOfMeasurement, UnitOfMeasurement } from '../../services/uom';
import {
  getSupplierProductsByProductIds,
  getSupplierProductByProductId,
  addOrUpdateSupplierProduct
} from '../../services/supplierProduct';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { formatQuantityWithSeparators } from '../../utils/formatters';
import UomSelector from '../../components/uom/UomSelector';
import PurchaseOrderSupplierProductButton from '../../components/suppliers/PurchaseOrderSupplierProductButton';
import EnhancedNumberInput from '../../components/common/EnhancedNumberInput';

interface PurchaseOrderItem {
  productId: string;
  productName: string;
  quantity: number;
  uomId: string;
  unitPrice: number;
  conversionFactor: number; // Required field, not optional
  notes?: string;
}

const CreatePurchaseOrder = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const formatWithCurrency = useCurrencyFormatter();

  // State for the form
  const [supplierId, setSupplierId] = useState<string>('');
  const [expectedDeliveryDate, setExpectedDeliveryDate] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [items, setItems] = useState<PurchaseOrderItem[]>([]);

  // State for data loading
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [uoms, setUoms] = useState<UnitOfMeasurement[]>([]);
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load suppliers and products
  useEffect(() => {
    const fetchData = async () => {
      if (!currentOrganization) return;

      setLoading(true);
      setError(null);

      try {
        // Try to load items from localStorage first
        const storedItems = localStorage.getItem('purchaseOrderItems');
        const storedSupplierId = localStorage.getItem('purchaseOrderSupplierId');

        if (storedItems) {
          try {
            const parsedItems = JSON.parse(storedItems);
            setItems(parsedItems);
          } catch (parseErr) {
            console.error('Error parsing stored items:', parseErr);
            localStorage.removeItem('purchaseOrderItems');
          }
        }

        // Fetch suppliers
        const { suppliers: supplierData, error: suppliersError } = await getSuppliers(currentOrganization.id);

        if (suppliersError) {
          console.error('Error fetching suppliers:', suppliersError);
          setError(suppliersError);
          return;
        }

        setSuppliers(supplierData);

        // If we have a stored supplier ID, use it
        if (storedSupplierId && supplierData.some(s => s.id === storedSupplierId)) {
          setSupplierId(storedSupplierId);
        }
        // If there's only one supplier and no stored ID, select it by default
        else if (supplierData.length === 1 && !storedSupplierId) {
          setSupplierId(supplierData[0].id);
        }
        // If no stored supplier, try to get the default supplier
        else if (!storedSupplierId) {
          const defaultSupplier = supplierData.find(s => s.is_default);
          if (defaultSupplier) {
            setSupplierId(defaultSupplier.id);
          }
        }

        // Fetch products
        const { products: productData, error: productsError } = await getProducts(currentOrganization.id);

        if (productsError) {
          console.error('Error fetching products:', productsError);
          setError(productsError);
          return;
        }

        setProducts(productData);

        // Fetch UoMs
        const { uoms: uomData, error: uomsError } = await getUnitsOfMeasurement(currentOrganization.id);

        if (uomsError) {
          console.error('Error fetching UoMs:', uomsError);
          setError(uomsError);
          return;
        }

        setUoms(uomData);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError(err.message || 'An error occurred while fetching data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Clean up function to remove localStorage items when component unmounts
    return () => {
      // We don't want to clear localStorage when navigating away temporarily
      // localStorage.removeItem('purchaseOrderItems');
      // localStorage.removeItem('purchaseOrderSupplierId');
    };
  }, [currentOrganization]);

  // Update prices when supplier changes
  useEffect(() => {
    if (supplierId) {
      // Store the supplier ID in localStorage
      localStorage.setItem('purchaseOrderSupplierId', supplierId);

      // Update prices if we have items
      if (items.length > 0) {
        updatePricesForSupplier(supplierId);
      }
    }
  }, [supplierId]);

  // Update all item prices for a supplier
  const updatePricesForSupplier = async (supplierId: string) => {
    // Get all product IDs that have been added to the order
    const productIds = items
      .filter(item => item.productId)
      .map(item => item.productId);

    if (productIds.length === 0) return;

    try {
      // Update prices for all products in the order

      const { supplierProducts, error } = await getSupplierProductsByProductIds(supplierId, productIds);

      if (error) {
        console.error('Error fetching supplier products:', error);
        return;
      }

      // Process supplier products

      if (supplierProducts.length > 0) {
        // Create a map of product ID to supplier price, UoM, and conversion factor
        const priceMap = {} as Record<string, { price: number; uomId: string | null; conversionFactor: number }>;

        // First pass: collect all supplier products with prices, UoMs, and conversion factors
        for (const sp of supplierProducts) {
          // Process each supplier product

          // Set price, UoM, and conversion factor if available
          if (sp.unit_price) {
            // First check if the UoM has a supplier_price
            if (sp.uom && sp.uom.supplier_price !== undefined && sp.uom.supplier_price !== null) {
              // Use supplier price from UoM
              priceMap[sp.product_id] = {
                price: sp.uom.supplier_price,
                uomId: sp.uom_id || null,
                conversionFactor: sp.conversion_factor || 1
              };
            } else {
              // Use supplier price from supplier_product
              priceMap[sp.product_id] = {
                price: sp.unit_price,
                uomId: sp.uom_id || null,
                conversionFactor: sp.conversion_factor || 1
              };
            }

            // UoM and conversion factor information is stored in the priceMap
          } else {
            // If supplier price is null, set price to 0 (not supplied by this supplier)
            priceMap[sp.product_id] = {
              price: 0,
              uomId: null,
              conversionFactor: 1
            };
          }
        }

        // For products not in the supplier_products table, set price to 0 and try to get default UoM
        const fetchDefaultUoms = async () => {
          for (const id of productIds) {
            if (!priceMap[id]) {
              // Product is not supplied by this supplier, set price to 0
              priceMap[id] = {
                price: 0,
                uomId: null,
                conversionFactor: 1
              };

              // Try to get the default UoM from the product
              try {
                // Try to fetch the default UoM for this product

                // First check if the product has a default UoM
                const { data: product, error: productError } = await supabase
                  .from('products')
                  .select(`
                    id,
                    default_uom_id
                  `)
                  .eq('id', id)
                  .single();

                if (productError) {
                  console.error('Error fetching product:', productError);
                } else if (product && product.default_uom_id) {
                  // Product has a default UoM

                  // Get conversion factor
                  const { data: productUoms, error: productUomsError } = await supabase
                    .from('product_uoms')
                    .select('conversion_factor')
                    .eq('product_id', id)
                    .eq('uom_id', product.default_uom_id)
                    .maybeSingle();

                  if (productUomsError) {
                    console.error('Error fetching product UoM conversion factor:', productUomsError);
                  } else if (productUoms) {
                    // Found conversion factor for this UoM
                    priceMap[id] = {
                      price: 0,
                      uomId: product.default_uom_id,
                      conversionFactor: productUoms.conversion_factor
                    };
                  } else {
                    priceMap[id] = {
                      price: 0,
                      uomId: product.default_uom_id,
                      conversionFactor: 1
                    };
                  }
                } else {
                  // If no default UoM, try to get any UoM for this product
                  // No default UoM found, look for any UoM for this product

                  const { data: productUoms, error: productUomsError } = await supabase
                    .from('product_uoms')
                    .select(`
                      uom_id,
                      conversion_factor
                    `)
                    .eq('product_id', id)
                    .limit(1);

                  if (productUomsError) {
                    console.error('Error fetching product UoMs:', productUomsError);
                  } else if (productUoms && productUoms.length > 0) {
                    // Found a UoM for this product

                    priceMap[id] = {
                      price: 0,
                      uomId: productUoms[0].uom_id,
                      conversionFactor: productUoms[0].conversion_factor
                    };
                  }
                }
              } catch (err) {
                console.error('Error fetching default UoM for product:', err);
              }
            }
          }
        };

        await fetchDefaultUoms();

        console.log('Price map created:', priceMap);

        // Update the prices and UoMs for all items
        setItems(prev => {
          const updatedItems = prev.map(item => {
            if (item.productId && priceMap[item.productId]) {
              const productInfo = priceMap[item.productId];
              console.log(`Updating price for product ${item.productId} to ${productInfo.price}`);

              // Create updated item with price and conversion factor
              const updatedItem = {
                ...item,
                unitPrice: productInfo.price,
                conversionFactor: productInfo.conversionFactor
              };

              console.log(`Updating conversion factor for product ${item.productId} to ${productInfo.conversionFactor}`);

              // If supplier has a specific UoM for this product, update it
              if (productInfo.uomId) {
                console.log(`Updating UoM for product ${item.productId} to ${productInfo.uomId}`);

                // Only update UoM if it's different from the current one
                if (updatedItem.uomId !== productInfo.uomId) {
                  // We'll need to convert the quantity when changing UoM
                  // For now, just update the UoM and handle quantity conversion later
                  updatedItem.uomId = productInfo.uomId;
                }
              }

              return updatedItem;
            }
            return item;
          });

          console.log('Updated items:', updatedItems);
          return updatedItems;
        });
      } else {
        // No supplier products found, check for default product prices

        // If no supplier products, try to get default product prices and UoMs
        const { data: products } = await supabase
          .from('products')
          .select(`
            id,
            unit_price,
            default_uom_id
          `)
          .in('id', productIds);

        if (products && products.length > 0) {
          // Use default product prices and UoMs

          // Create a map to store product info
          const productMap: Record<string, { price: number; uomId: string | null; conversionFactor: number }> = {};

          // Fetch UoMs and conversion factors for all products
          for (const product of products) {
            if (product.unit_price) {
              // Initialize with default values
              productMap[product.id] = {
                price: product.unit_price,
                uomId: product.default_uom_id || null,
                conversionFactor: 1
              };

              // If product has a default UoM, get the conversion factor
              if (product.default_uom_id) {
                try {
                  const { data: productUoms, error: productUomsError } = await supabase
                    .from('product_uoms')
                    .select('conversion_factor')
                    .eq('product_id', product.id)
                    .eq('uom_id', product.default_uom_id)
                    .maybeSingle();

                  if (productUomsError) {
                    console.error('Error fetching product UoM conversion factor:', productUomsError);
                  } else if (productUoms) {
                    // Found conversion factor for this product
                    productMap[product.id].conversionFactor = productUoms.conversion_factor;
                  }
                } catch (err) {
                  console.error('Error fetching conversion factor:', err);
                }
              } else {
                // If no default UoM, try to get any UoM for this product
                try {
                  const { data: productUoms, error: productUomsError } = await supabase
                    .from('product_uoms')
                    .select(`
                      uom_id,
                      conversion_factor
                    `)
                    .eq('product_id', product.id)
                    .limit(1);

                  if (productUomsError) {
                    console.error('Error fetching product UoMs:', productUomsError);
                  } else if (productUoms && productUoms.length > 0) {
                    // Found UoM with conversion factor for this product
                    productMap[product.id].uomId = productUoms[0].uom_id;
                    productMap[product.id].conversionFactor = productUoms[0].conversion_factor;
                  }
                } catch (err) {
                  console.error('Error fetching UoMs:', err);
                }
              }
            }
          }

          // Product map created with prices and UoMs

          // Update the items with the product information
          setItems(prev => {
            return prev.map(item => {
              if (item.productId && productMap[item.productId]) {
                const productInfo = productMap[item.productId];
                return {
                  ...item,
                  unitPrice: productInfo.price,
                  ...(productInfo.uomId && { uomId: productInfo.uomId }),
                  conversionFactor: productInfo.conversionFactor
                };
              }
              return item;
            });
          });
        }
      }
    } catch (err) {
      console.error('Error updating prices for supplier:', err);
    }
  };

  // Add a new item to the purchase order
  const handleAddItem = () => {
    if (products.length === 0) return;

    const newItem: PurchaseOrderItem = {
      productId: '',
      productName: '',
      quantity: 1,
      uomId: '',
      unitPrice: 0,
      conversionFactor: 1 // Initialize with conversion factor of 1
    };

    setItems(prev => {
      const newItems = [...prev, newItem];
      // Store the updated items in localStorage
      localStorage.setItem('purchaseOrderItems', JSON.stringify(newItems));
      return newItems;
    });
  };

  // Remove an item from the purchase order
  const handleRemoveItem = (index: number) => {
    setItems(prev => {
      const newItems = prev.filter((_, i) => i !== index);
      // Store the updated items in localStorage
      localStorage.setItem('purchaseOrderItems', JSON.stringify(newItems));
      return newItems;
    });
  };

  // Handle item field changes
  const handleItemChange = async (index: number, field: keyof PurchaseOrderItem, value: any) => {
    // Update the item in state
    setItems(prev => {
      const newItems = [...prev];
      newItems[index] = {
        ...newItems[index],
        [field]: value
      };

      // If productId changed, update productName and reset uomId and conversionFactor
      if (field === 'productId') {
        const product = products.find(p => p.id === value);
        newItems[index].productName = product?.name || '';
        newItems[index].uomId = '';
        newItems[index].conversionFactor = 1; // Reset conversion factor

        // If we have a supplier selected, try to get the supplier price
        if (supplierId) {
          fetchSupplierPrice(supplierId, value, index);
        }
      }

      // If uomId changed, reset the conversion factor until we fetch it
      if (field === 'uomId') {
        // We'll fetch the actual conversion factor in the onChange handler of UomSelector
        newItems[index].conversionFactor = 1;
      }

      // Store the updated items in localStorage
      localStorage.setItem('purchaseOrderItems', JSON.stringify(newItems));

      return newItems;
    });

    // If the unit price was changed and we have a supplier selected, add/update the supplier product
    if (field === 'unitPrice' && supplierId && currentOrganization) {
      const item = items[index];

      // Only proceed if we have a product selected
      if (item.productId) {
        try {
          // If the price is greater than 0, add/update the supplier product
          if (value > 0) {
            // Unit price changed, add/update the supplier product

            // If we have a UoM selected, get its details
            let uomName = undefined;
            let conversionFactor = undefined;

            if (item.uomId) {
              try {
                // Get UoM details
                const { data: uomData, error: uomError } = await supabase
                  .from('units_of_measurement')
                  .select('id, name, code')
                  .eq('id', item.uomId)
                  .single();

                if (uomError) {
                  console.error('Error fetching UoM details:', uomError);
                } else if (uomData) {
                  // Use the UoM details
                  uomName = `${uomData.name} (${uomData.code})`;

                  // Get conversion factor
                  const { data: productUoms, error: productUomsError } = await supabase
                    .from('product_uoms')
                    .select('conversion_factor')
                    .eq('product_id', item.productId)
                    .eq('uom_id', item.uomId)
                    .maybeSingle();

                  if (productUomsError) {
                    console.error('Error fetching product UoM conversion factor:', productUomsError);
                  } else if (productUoms) {
                    // Use the conversion factor
                    conversionFactor = productUoms.conversion_factor;
                  }
                }
              } catch (err) {
                console.error('Exception fetching UoM details:', err);
              }
            }

            const { success, supplierProduct, error } = await addOrUpdateSupplierProduct(
              supplierId,
              item.productId,
              value,
              currentOrganization.id,
              item.uomId, // Pass the current UoM ID
              uomName,    // Pass the UoM name
              conversionFactor // Pass the conversion factor
            );

            if (success) {
              // Successfully added/updated supplier product
            } else {
              console.error('Error adding/updating supplier product:', error);
            }
          }
          // If the price is set to 0, check if this is intentional (product not supplied by this supplier)
          else if (value === 0) {
            // Unit price set to 0, check if this is a supplier product

            // Check if this product is supplied by this supplier
            const { supplierProduct } = await getSupplierProductByProductId(
              supplierId,
              item.productId
            );

            // If it is a supplier product with a price, confirm with the user
            if (supplierProduct && supplierProduct.unit_price) {
              const confirmRemove = window.confirm(
                `This product is currently supplied by this supplier at a price of ${supplierProduct.unit_price}. ` +
                `Setting the price to 0 indicates this product is not supplied by this supplier. ` +
                `Do you want to continue?`
              );

              if (confirmRemove) {
                // Update the supplier product with a null price
                // Get UoM details if we have a UoM selected
                let uomName = undefined;
                let conversionFactor = undefined;

                if (item.uomId) {
                  try {
                    // Get UoM details
                    const { data: uomData, error: uomError } = await supabase
                      .from('units_of_measurement')
                      .select('id, name, code')
                      .eq('id', item.uomId)
                      .single();

                    if (uomError) {
                      console.error('Error fetching UoM details:', uomError);
                    } else if (uomData) {
                      // Use the UoM details
                      uomName = `${uomData.name} (${uomData.code})`;

                      // Get conversion factor
                      const { data: productUoms, error: productUomsError } = await supabase
                        .from('product_uoms')
                        .select('conversion_factor')
                        .eq('product_id', item.productId)
                        .eq('uom_id', item.uomId)
                        .maybeSingle();

                      if (productUomsError) {
                        console.error('Error fetching product UoM conversion factor:', productUomsError);
                      } else if (productUoms) {
                        // Use the conversion factor
                        conversionFactor = productUoms.conversion_factor;
                      }
                    }
                  } catch (err) {
                    console.error('Exception fetching UoM details for zero price update:', err);
                  }
                }

                const { success } = await addOrUpdateSupplierProduct(
                  supplierId,
                  item.productId,
                  0,
                  currentOrganization.id,
                  item.uomId, // Pass the current UoM ID
                  uomName,    // Pass the UoM name
                  conversionFactor // Pass the conversion factor
                );

                if (success) {
                  // Successfully updated supplier product with 0 price
                }
              } else {
                // Revert to the previous price
                setItems(prev => {
                  const newItems = [...prev];
                  newItems[index] = {
                    ...newItems[index],
                    unitPrice: supplierProduct.unit_price || 0
                  };
                  return newItems;
                });
              }
            }
          }
        } catch (err) {
          console.error('Error in handleItemChange when adding supplier product:', err);
        }
      }
    }
  };

  // Fetch supplier price for a product
  const fetchSupplierPrice = async (supplierId: string, productId: string, itemIndex: number) => {
    try {
      // Fetch supplier price for this product

      // First, get the product's default UoM
      const { data: defaultUom, error: defaultUomError } = await supabase
        .from('product_uoms')
        .select(`
          uom_id,
          conversion_factor,
          is_default,
          uom:uom_id (
            id,
            code,
            name
          )
        `)
        .eq('product_id', productId)
        .eq('is_default', true)
        .maybeSingle();

      if (defaultUomError) {
        console.error('Error fetching product default UoM:', defaultUomError);
      }

      // Now try to get the supplier product price
      const { supplierProduct } = await getSupplierProductByProductId(supplierId, productId);

      // Process the supplier product result

      let price = null;
      let uomId = null;
      let uomName = null;
      let conversionFactor = 1;

      // Check if we have a supplier product with a price
      if (supplierProduct) {
        // Found a supplier product

        // Set price (default to 0 if not available)
        // First check if the UoM has a supplier_price
        if (supplierProduct.uom && supplierProduct.uom.supplier_price !== undefined && supplierProduct.uom.supplier_price !== null) {
          // Use supplier price from UoM
          price = supplierProduct.uom.supplier_price;
        } else {
          // Use price from supplier_product
          price = supplierProduct.unit_price || 0;
        }

        // Always use the default UoM from the product
        if (defaultUom && defaultUom.uom_id) {
          uomId = defaultUom.uom_id;
          conversionFactor = defaultUom.conversion_factor || 1;

          if (defaultUom.uom) {
            uomName = `${defaultUom.uom.name} (${defaultUom.uom.code})`;
          }

          console.log(`Using default UoM: ${uomName} (${uomId}) with conversion factor: ${conversionFactor}`);

          // If the supplier product has a different UoM, update it
          if (supplierProduct.uom_id !== uomId) {
            console.log(`Updating supplier product UoM to default: ${uomId}`);

            const { error: updateError } = await supabase
              .from('supplier_products')
              .update({
                uom_id: uomId,
                conversion_factor: conversionFactor,
                uom_name: uomName,
                base_price: price ? price * conversionFactor : null
              })
              .eq('id', supplierProduct.id);

            if (updateError) {
              console.error('Error updating supplier product with default UoM:', updateError);
            } else {
              // Successfully updated supplier product with default UoM
            }
          }
        }
        // If no default UoM found, use whatever UoM the supplier product has
        else if (supplierProduct.uom_id) {
          uomId = supplierProduct.uom_id;
          uomName = supplierProduct.uom?.name || supplierProduct.uom_name || 'Unknown';
          conversionFactor = supplierProduct.conversion_factor || 1;
          // Using supplier product UoM with conversion factor
        }
      }
      // If no supplier product exists
      else {
        // Product is not supplied by this supplier, set price to 0
        price = 0;

        // Use the default UoM from the product
        if (defaultUom && defaultUom.uom_id) {
          uomId = defaultUom.uom_id;
          conversionFactor = defaultUom.conversion_factor || 1;

          if (defaultUom.uom) {
            uomName = `${defaultUom.uom.name} (${defaultUom.uom.code})`;
          }

          // Using default UoM with conversion factor

          // Create a new supplier product with the default UoM
          if (currentOrganization) {
            try {
              // Create a new supplier product with the default UoM

              const { data, error } = await supabase
                .from('supplier_products')
                .insert({
                  supplier_id: supplierId,
                  product_id: productId,
                  unit_price: 0,
                  uom_id: uomId,
                  uom_name: uomName,
                  conversion_factor: conversionFactor,
                  organization_id: currentOrganization.id,
                  base_price: 0
                })
                .select()
                .single();

              if (error) {
                console.error('Error creating supplier product with default UoM:', error);
              } else {
                // Successfully created supplier product with default UoM
              }
            } catch (err) {
              console.error('Exception creating supplier product with default UoM:', err);
            }
          }
        } else {
          console.warn('No default UoM found for product and no supplier product exists');

          // Try to find any UoM for this product
          try {
            // Look for any UoM for this product

            const { data: productUoms, error: productUomsError } = await supabase
              .from('product_uoms')
              .select(`
                uom_id,
                conversion_factor,
                uom:uom_id (
                  id,
                  name,
                  code
                )
              `)
              .eq('product_id', productId)
              .limit(1);

            if (productUomsError) {
              console.error('Error fetching product UoMs:', productUomsError);
            } else if (productUoms && productUoms.length > 0) {
              // Found a UoM for this product

              if (productUoms[0].uom) {
                uomId = productUoms[0].uom_id;
                uomName = `${productUoms[0].uom.name} (${productUoms[0].uom.code})`;
                conversionFactor = productUoms[0].conversion_factor;

                // Using found UoM with conversion factor
              }
            } else {
              // No UoMs found for this product
            }
          } catch (err) {
            console.error('Error fetching any UoM for product:', err);
          }
        }
      }

      // Update the item with the supplier product information
      setItems(prev => {
        const newItems = [...prev];
        const updatedItem = {
          ...newItems[itemIndex],
          unitPrice: price,
          conversionFactor: conversionFactor // Store the conversion factor
        };

        // If supplier product has a UoM, use it
        if (uomId) {
          // Set UoM for this product
          updatedItem.uomId = uomId;
        }

        // Set conversion factor for this product

        newItems[itemIndex] = updatedItem;
        return newItems;
      });

      // Store the item data in localStorage to persist across page refreshes
      const storedItems = [...items];
      if (storedItems[itemIndex]) {
        storedItems[itemIndex] = {
          ...storedItems[itemIndex],
          unitPrice: price,
          conversionFactor: conversionFactor,
          ...(uomId && { uomId })
        };
        localStorage.setItem('purchaseOrderItems', JSON.stringify(storedItems));
        // Store updated items in localStorage
      }
    } catch (err) {
      console.error('Error fetching supplier price:', err);
    }
  };

  // Calculate total amount
  const calculateTotal = () => {
    // Calculate total for all items

    return items.reduce((sum, item) => {
      // Make sure we're using the correct conversion factor
      // Force it to be a number to avoid any type issues
      const conversionFactor = Number(item.conversionFactor);
      // Calculate item total with conversion factor

      // Calculate as (quantity * conversion factor) * unit price
      const itemTotal = (item.quantity * conversionFactor) * item.unitPrice;
      // Return the item total

      return sum + itemTotal;
    }, 0);
  };

  // Submit the form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization || !user) {
      setError('Missing required data');
      return;
    }

    // If no supplier is selected, use the default supplier
    let finalSupplierId = supplierId;
    if (!finalSupplierId) {
      const defaultSupplier = suppliers.find(s => s.is_default);
      if (defaultSupplier) {
        finalSupplierId = defaultSupplier.id;
      } else {
        setError('No supplier available. Please contact support.');
        return;
      }
    }

    if (items.length === 0) {
      setError('Please add at least one item to the purchase order');
      return;
    }

    // Validate items
    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      if (!item.productId) {
        setError(`Please select a product for item #${i + 1}`);
        return;
      }

      if (!item.uomId) {
        setError(`Please select a unit of measurement for item #${i + 1}`);
        return;
      }

      if (item.quantity <= 0) {
        setError(`Please enter a valid quantity for item #${i + 1}`);
        return;
      }

      // Allow zero or positive prices (zero for items not supplied by this supplier)
      if (typeof item.unitPrice !== 'number' || item.unitPrice < 0 || isNaN(item.unitPrice)) {
        setError(`Please enter a valid unit price (0 or greater) for item #${i + 1}`);
        return;
      }
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Prepare the data for creating a purchase order
      const poData = {
        supplierId: finalSupplierId,
        expectedDeliveryDate: expectedDeliveryDate ? new Date(expectedDeliveryDate).toISOString() : undefined,
        notes: notes || undefined,
        items: items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          uomId: item.uomId,
          unitPrice: item.unitPrice,
          conversionFactor: item.conversionFactor || 1
        }))
      };

      // Create the purchase order with the prepared data

      // Create the purchase order
      const { purchaseOrder, error: createError } = await createPurchaseOrder(
        currentOrganization.id,
        poData
      );

      if (createError) {
        setError(createError);
      } else if (purchaseOrder) {
        // Clear localStorage since we've successfully created the purchase order
        localStorage.removeItem('purchaseOrderItems');
        localStorage.removeItem('purchaseOrderSupplierId');
        // Clear localStorage after successful purchase order creation

        // Navigate to the purchase order details
        navigate(`/purchases/orders/${purchaseOrder.id}`);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while creating the purchase order');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate('/purchases/orders');
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <HiOutlineDocumentText className="mr-2 h-6 w-6" />
              Create Purchase Order
            </h1>
            <p className="text-gray-500">
              Create a new purchase order for a supplier
            </p>
          </div>

          <Button color="gray" onClick={handleCancel}>
            <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
            Cancel
          </Button>
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Supplier */}
            <div>
              <div className="mb-2 block">
                <Label htmlFor="supplier_id" value="Supplier (Optional)" />
                <p className="text-xs text-gray-500 mt-1">
                  Leave empty to use default supplier, or select a specific supplier
                </p>
              </div>
              <Select
                id="supplier_id"
                value={supplierId}
                onChange={(e) => setSupplierId(e.target.value)}
              >
                <option value="">Use Default Supplier</option>
                {suppliers.filter(s => !s.is_default).map(supplier => (
                  <option key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </option>
                ))}
              </Select>
              {!supplierId && (
                <p className="text-xs text-blue-600 mt-1">
                  ℹ️ Using "{suppliers.find(s => s.is_default)?.name || 'Default Supplier'}" - you can change the supplier later
                </p>
              )}
            </div>

            {/* Expected Delivery Date */}
            <div>
              <div className="mb-2 block">
                <Label htmlFor="expected_delivery_date" value="Expected Delivery Date" />
              </div>
              <TextInput
                id="expected_delivery_date"
                type="date"
                value={expectedDeliveryDate}
                onChange={(e) => setExpectedDeliveryDate(e.target.value)}
                min={new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>

          {/* Notes */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="notes" value="Notes" />
            </div>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Enter any additional notes"
              rows={3}
            />
          </div>

          {/* Items */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <Label value="Items" />
              <Button size="xs" color="primary" onClick={handleAddItem}>
                <HiOutlinePlus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </div>

            {items.length === 0 ? (
              <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
                No items added. Click "Add Item" to add products to this purchase order.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <Table.Head>
                    <Table.HeadCell>Product</Table.HeadCell>
                    <Table.HeadCell>Quantity</Table.HeadCell>
                    <Table.HeadCell>Unit</Table.HeadCell>
                    <Table.HeadCell>Unit Price</Table.HeadCell>
                    <Table.HeadCell>Total</Table.HeadCell>
                    <Table.HeadCell>
                      <span className="sr-only">Actions</span>
                    </Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {items.map((item, index) => (
                      <Table.Row key={index} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                        <Table.Cell>
                          <Select
                            value={item.productId}
                            onChange={(e) => handleItemChange(index, 'productId', e.target.value)}
                            required
                            className="text-sm"
                          >
                            <option value="">Select a product</option>
                            {products.map(product => (
                              <option key={product.id} value={product.id}>
                                {product.name}
                              </option>
                            ))}
                          </Select>
                        </Table.Cell>
                        <Table.Cell>
                          <EnhancedNumberInput
                            min="0.01"
                            step="0.01"
                            value={item.quantity}
                            onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                            onBlur={(e) => {
                              // If field is empty on blur, reset to 1
                              if (e.target.value === '' || parseFloat(e.target.value) <= 0) {
                                handleItemChange(index, 'quantity', 1);
                              }
                            }}
                            required
                            className="w-24 text-sm"
                            sizing="sm"
                            autoSelect={true}
                            preventScrollChange={true}
                          />
                        </Table.Cell>
                        <Table.Cell>
                          {item.productId ? (
                            <div className="flex items-center space-x-2">
                              <div className="flex-grow">
                                <UomSelector
                                  key={`uom-selector-${item.productId}-${index}`}
                                  productId={item.productId}
                                  value={item.uomId}
                                  onChange={(selectedUomId) => {
                                    // Update the item with the new UoM
                                    setItems(prev => {
                                      const newItems = [...prev];
                                      newItems[index] = {
                                        ...newItems[index],
                                        uomId: selectedUomId
                                      };
                                      return newItems;
                                    });
                                  }}
                                  onUomChange={(selectedUom) => {
                                    // UoM changed, update with new conversion factor

                                    // Update the item with the new UoM and conversion factor
                                    setItems(prev => {
                                      const newItems = [...prev];
                                      newItems[index] = {
                                        ...newItems[index],
                                        uomId: selectedUom.uom_id,
                                        conversionFactor: selectedUom.conversion_factor
                                      };

                                      // Store the updated items in localStorage
                                      localStorage.setItem('purchaseOrderItems', JSON.stringify(newItems));
                                      // Store updated items with new UoM and conversion factor

                                      return newItems;
                                    });

                                    // If we have a supplier and product, update the supplier product with the new UoM
                                    if (supplierId && items[index].productId && currentOrganization) {
                                      (async () => {
                                        try {
                                          const uomName = `${selectedUom.uom.name} (${selectedUom.uom.code})`;

                                          // Update the supplier product with the new UoM
                                          const { success, supplierProduct, error } = await addOrUpdateSupplierProduct(
                                            supplierId,
                                            items[index].productId,
                                            items[index].unitPrice,
                                            currentOrganization.id,
                                            selectedUom.uom_id,
                                            uomName,
                                            selectedUom.conversion_factor
                                          );

                                          if (success) {
                                            // Successfully updated supplier product with new UoM
                                          } else {
                                            console.error('Error updating supplier product with new UoM:', error);
                                          }
                                        } catch (err) {
                                          console.error('Exception updating supplier product with new UoM:', err);
                                        }
                                      })();
                                    }
                                  }}
                                  filter="purchasing"
                                  className="text-sm"
                                />
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-2">
                              <div className="flex-grow">
                                <Select disabled className="text-sm">
                                  <option value="">Select a product first</option>
                                </Select>
                              </div>
                            </div>
                          )}
                        </Table.Cell>
                        <Table.Cell>
                          <div className="flex items-center gap-2">
                            <EnhancedNumberInput
                              min="0"
                              step="0.01"
                              value={item.unitPrice}
                              onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                              onBlur={(e) => {
                                // If field is empty on blur, reset to 0.01
                                if (e.target.value === '' || parseFloat(e.target.value) < 0) {
                                  handleItemChange(index, 'unitPrice', 0.01);
                                }
                              }}
                              required
                              className="w-24 text-sm"
                              sizing="sm"
                              autoSelect={true}
                              preventScrollChange={true}
                            />
                            {supplierId && item.productId && (
                              <PurchaseOrderSupplierProductButton
                                supplierId={supplierId}
                                product={products.find(p => p.id === item.productId) || { id: item.productId, name: item.productName }}
                                onSuccess={(unitPrice, uomId, conversionFactor = 1) => {
                                  console.log(`Supplier product updated for item ${index}: price=${unitPrice}, uomId=${uomId}, conversionFactor=${conversionFactor}`);

                                  // Update the item with the new price, UoM, and conversion factor
                                  const newItems = [...items];
                                  newItems[index] = {
                                    ...newItems[index],
                                    unitPrice: unitPrice,
                                    conversionFactor: conversionFactor,
                                    ...(uomId && { uomId })
                                  };
                                  setItems(newItems);

                                  // Store the updated items in localStorage
                                  localStorage.setItem('purchaseOrderItems', JSON.stringify(newItems));
                                  console.log('Stored updated items in localStorage after supplier product update');
                                }}
                                currentUnitPrice={item.unitPrice}
                                currentUomId={item.uomId}
                              />
                            )}
                          </div>
                        </Table.Cell>

                        <Table.Cell>
                          {(() => {
                            // Calculate the total using the conversion factor
                            const conversionFactor = Number(item.conversionFactor || 1);
                            const total = (item.quantity * conversionFactor) * item.unitPrice;

                            return (
                              <>
                                {formatWithCurrency(total)}
                                <div className="text-xs text-gray-500">
                                  {formatQuantityWithSeparators(item.quantity)} {item.uomId ? (uoms.find(u => u.id === item.uomId)?.code || 'unit') : 'unit'} × {formatWithCurrency(item.unitPrice)}
                                </div>
                              </>
                            );
                          })()}
                        </Table.Cell>
                        <Table.Cell>
                          <Button
                            color="failure"
                            size="xs"
                            onClick={() => handleRemoveItem(index)}
                          >
                            <HiOutlineTrash className="h-4 w-4" />
                          </Button>
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
            )}

            <div className="flex justify-end mt-4">
              <div className="text-lg font-semibold">
                Total: {formatWithCurrency(calculateTotal())}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button type="submit" color="primary" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Creating...
                </>
              ) : (
                'Create Purchase Order'
              )}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default CreatePurchaseOrder;
