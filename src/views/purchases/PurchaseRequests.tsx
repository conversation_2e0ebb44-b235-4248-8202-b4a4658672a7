import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { <PERSON>, But<PERSON>, Table, Badge, Spinner, Alert, TextInput, Select, Tabs } from "flowbite-react";
import {
  HiOutlinePlus,
  HiOutlineExclamation,
  HiOutlineUser,
  HiOutlineSearch,
  HiOutlineFilter,
  HiOutlineRefresh,
  HiOutlineDocumentText,
  HiOutlineClipboardList
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { getPurchaseRequests } from '../../services/purchaseRequest';
import { formatDate, formatDateTime } from '../../utils/formatters';
import EmptyState from '../../components/common/EmptyState';
import Pagination from '../../components/common/Pagination';

const PurchaseRequests = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();

  // State variables
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [purchaseRequests, setPurchaseRequests] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Fetch purchase requests with filtering
  const fetchPurchaseRequests = async (search = searchQuery, status = statusFilter) => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      // Prepare filter options
      const options: any = {};
      if (search) options.searchQuery = search;
      if (status !== 'all') options.status = status;

      // Use the getPurchaseRequests service
      const { purchaseRequests: requests, error: fetchError } = await getPurchaseRequests(
        currentOrganization.id,
        options
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setPurchaseRequests(requests || []);
      }
    } catch (err: any) {
      console.error('Error fetching purchase requests:', err);
      setError(err.message || 'An error occurred while fetching purchase requests');
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchPurchaseRequests();
  }, [currentOrganization]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchPurchaseRequests(searchQuery, statusFilter);
  };

  // Handle status filter change
  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newStatus = e.target.value;
    setStatusFilter(newStatus);
    fetchPurchaseRequests(searchQuery, newStatus);
  };

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchPurchaseRequests(searchQuery, statusFilter);
  };

  // Navigate to create page
  const handleCreateClick = () => {
    navigate('/purchases/requests/create');
  };

  // Status color mapping
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'success';
      case 'pending': return 'warning';
      case 'rejected': return 'failure';
      case 'cancelled': return 'dark';
      default: return 'gray'; // for draft
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(purchaseRequests.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentRequests = purchaseRequests.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <HiOutlineClipboardList className="mr-2 h-6 w-6" />
              Purchase Requests
            </h1>
            <p className="text-gray-500">
              Create and manage purchase requests for inventory replenishment.
            </p>
          </div>

          <Button color="primary" onClick={handleCreateClick}>
            <HiOutlinePlus className="mr-2 h-5 w-5" />
            Create Request
          </Button>
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        {/* Search and filter bar */}
        <div className="flex flex-col md:flex-row gap-3 mb-4">
          <form onSubmit={handleSearch} className="flex-grow">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <HiOutlineSearch className="w-5 h-5 text-gray-500" />
              </div>
              <TextInput
                type="search"
                placeholder="Search by request number..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10"
              />
              <Button type="submit" color="blue" size="sm" className="absolute right-1 top-1 bottom-1">
                Search
              </Button>
            </div>
          </form>

          <div className="flex gap-2">
            <div className="min-w-[150px]">
              <Select
                value={statusFilter}
                onChange={handleStatusChange}
                icon={HiOutlineFilter}
              >
                <option value="all">All Status</option>
                <option value="draft">Draft</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="cancelled">Cancelled</option>
              </Select>
            </div>

            <Button
              color="light"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <HiOutlineRefresh className={`h-5 w-5 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-12">
            <Spinner size="xl" />
            <span className="ml-2">Loading purchase requests...</span>
          </div>
        ) : purchaseRequests.length === 0 ? (
          <EmptyState
            icon={<HiOutlineDocumentText className="h-12 w-12" />}
            title="No purchase requests found"
            description={
              searchQuery || statusFilter !== 'all'
                ? "Try changing your search terms or filters"
                : "Create your first purchase request to get started"
            }
            actionText={
              searchQuery || statusFilter !== 'all'
                ? "Clear filters"
                : "Create Purchase Request"
            }
            onAction={
              searchQuery || statusFilter !== 'all'
                ? () => {
                    setSearchQuery('');
                    setStatusFilter('all');
                    fetchPurchaseRequests('', 'all');
                  }
                : handleCreateClick
            }
          />
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Request Number</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>Requester</Table.HeadCell>
                <Table.HeadCell>Date Created</Table.HeadCell>
                <Table.HeadCell>
                  <span className="sr-only">Actions</span>
                </Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {currentRequests.map((request) => (
                  <Table.Row
                    key={request.id}
                    className="bg-white dark:border-gray-700 dark:bg-gray-800 hover:bg-gray-50"
                    onClick={() => navigate(`/purchases/requests/${request.id}`)}
                    style={{ cursor: 'pointer' }}
                  >
                    <Table.Cell className="font-medium">
                      <div className="flex items-center">
                        <HiOutlineDocumentText className="mr-2 h-5 w-5 text-gray-500" />
                        <span>{request.request_number}</span>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge color={getStatusColor(request.status)}>
                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      {user && request.requester_id === user.id ? (
                        <div className="flex items-center">
                          <HiOutlineUser className="mr-1 h-4 w-4 text-blue-500" />
                          <span className="text-blue-500 font-medium">You</span>
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <HiOutlineUser className="mr-1 h-4 w-4 text-gray-500" />
                          <span>{request.requester_name || 'Unknown User'}</span>
                        </div>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <div title={formatDateTime(request.created_at)}>
                        {formatDate(request.created_at)}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Button
                        color="light"
                        size="xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(`/purchases/requests/${request.id}`);
                        }}
                      >
                        View
                      </Button>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        {/* Pagination */}
        {purchaseRequests.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={purchaseRequests.length}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="requests"
          />
        )}
      </Card>
    </div>
  );
};

export default PurchaseRequests;
