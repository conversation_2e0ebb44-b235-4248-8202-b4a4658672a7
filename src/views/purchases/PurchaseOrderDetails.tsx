import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Table,
  Badge,
  Spinner,
  <PERSON><PERSON>,
  Toolt<PERSON>
} from 'flowbite-react';
import {
  HiOutlineChevronLeft,
  HiOutlineExclamation,
  HiOutlineDocumentText,
  HiOutlineOfficeBuilding,
  HiOutlineCalendar,
  HiOutlineClipboardList,
  HiOutlineCurrencyDollar,
  HiOutlinePencil,
  HiOutlinePrinter,
  HiOutlineUser,
  HiOutlineClipboardCheck,
  HiOutlineShoppingCart
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getPurchaseOrderById, sendPurchaseOrderToSupplier } from '../../services/purchaseOrder';
import { formatDate, formatDateTime, formatQuantityWithSeparators } from '../../utils/formatters';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

const PurchaseOrderDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  const [purchaseOrder, setPurchaseOrder] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSending, setIsSending] = useState(false);

  useEffect(() => {
    const fetchPurchaseOrder = async () => {
      if (!id || !currentOrganization) return;

      setLoading(true);
      setError(null);

      try {
        // Modify the API call to explicitly request creator information
        const { purchaseOrder: order, error: fetchError } = await getPurchaseOrderById(
          currentOrganization.id,
          id
        );

        if (fetchError) {
          setError(fetchError);
        } else if (!order) {
          setError('Purchase order not found');
        } else {
          if (order) {
            setPurchaseOrder(order);
          }
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the purchase order');
      } finally {
        setLoading(false);
      }
    };

    fetchPurchaseOrder();
  }, [id, currentOrganization]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'info';
      case 'partially_received':
        return 'warning';
      case 'received':
        return 'success';
      case 'cancelled':
        return 'failure';
      default:
        return 'gray';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'sent':
        return 'Sent';
      case 'partially_received':
        return 'Partially Received';
      case 'received':
        return 'Received';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const handleBackClick = () => {
    navigate('/purchases/orders');
  };

  const handleSendToSupplier = async () => {
    if (!currentOrganization || !id) return;

    setIsSending(true);
    setError(null);

    try {
      const { purchaseOrder: updatedPO, error: updateError } = await sendPurchaseOrderToSupplier(
        currentOrganization.id,
        id
      );

      if (updateError) {
        setError(updateError);
      } else if (updatedPO) {
        setPurchaseOrder(updatedPO);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while sending the purchase order');
    } finally {
      setIsSending(false);
    }
  };

  const handlePrint = () => {
    if (!purchaseOrder) return;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const printContent = generatePurchaseOrderPrintHTML(purchaseOrder);
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    }
  };

  // Check if purchase order is completely received
  const isCompletelyReceived = () => {
    if (!purchaseOrder?.items || purchaseOrder.items.length === 0) return false;

    return purchaseOrder.items.every((item: any) => {
      const receivedQty = item.received_quantity || 0;
      return receivedQty >= item.quantity;
    });
  };

  // Check if purchase order can receive items
  const canReceiveItems = () => {
    return (purchaseOrder.status === 'sent' || purchaseOrder.status === 'partially_received') && !isCompletelyReceived();
  };

  const generatePurchaseOrderPrintHTML = (po: any) => {
    const currentDate = new Date().toLocaleDateString();

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Purchase Order - ${po.order_number}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              font-size: 12px;
              line-height: 1.4;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #000;
              padding-bottom: 20px;
              margin-bottom: 20px;
            }
            .company-name {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 5px;
            }
            .document-title {
              font-size: 18px;
              font-weight: bold;
              margin-top: 15px;
            }
            .info-section {
              display: flex;
              justify-content: space-between;
              margin-bottom: 20px;
            }
            .info-box {
              width: 48%;
            }
            .info-box h3 {
              margin: 0 0 10px 0;
              font-size: 14px;
              font-weight: bold;
              border-bottom: 1px solid #ccc;
              padding-bottom: 5px;
            }
            .info-box p {
              margin: 5px 0;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            th, td {
              border: 1px solid #000;
              padding: 8px;
              text-align: left;
            }
            th {
              background-color: #f0f0f0;
              font-weight: bold;
            }
            .text-right {
              text-align: right;
            }
            .text-center {
              text-align: center;
            }
            .total-section {
              margin-top: 20px;
              text-align: right;
            }
            .total-row {
              font-size: 16px;
              font-weight: bold;
              margin-top: 10px;
            }
            .notes-section {
              margin-top: 30px;
              border-top: 1px solid #ccc;
              padding-top: 15px;
            }
            .signature-section {
              margin-top: 40px;
              display: flex;
              justify-content: space-between;
            }
            .signature-box {
              width: 30%;
              text-align: center;
            }
            .signature-line {
              border-top: 1px solid #000;
              margin-top: 40px;
              padding-top: 5px;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <!-- Header -->
          <div class="header">
            <div class="company-name">${currentOrganization?.name || 'Your Company Name'}</div>
            <div>${currentOrganization?.address || ''}</div>
            <div class="document-title">PURCHASE ORDER</div>
          </div>

          <!-- Purchase Order Info -->
          <div class="info-section">
            <div class="info-box">
              <h3>Purchase Order Details</h3>
              <p><strong>PO Number:</strong> ${po.order_number}</p>
              <p><strong>Date:</strong> ${formatDate(po.order_date)}</p>
              <p><strong>Expected Delivery:</strong> ${po.expected_delivery_date ? formatDate(po.expected_delivery_date) : 'Not specified'}</p>
              <p><strong>Status:</strong> ${po.status.charAt(0).toUpperCase() + po.status.slice(1)}</p>
            </div>
            <div class="info-box">
              <h3>Supplier Information</h3>
              <p><strong>Supplier:</strong> ${po.supplier?.name || 'Unknown Supplier'}</p>
              <p><strong>Contact:</strong> ${po.supplier?.contact_person || 'N/A'}</p>
              <p><strong>Phone:</strong> ${po.supplier?.phone || 'N/A'}</p>
              <p><strong>Email:</strong> ${po.supplier?.email || 'N/A'}</p>
            </div>
          </div>

          <!-- Items Table -->
          <table>
            <thead>
              <tr>
                <th style="width: 40%">Product</th>
                <th style="width: 10%" class="text-center">Quantity</th>
                <th style="width: 10%" class="text-center">Unit</th>
                <th style="width: 15%" class="text-right">Unit Price</th>
                <th style="width: 15%" class="text-right">Total</th>
                <th style="width: 10%" class="text-center">Received</th>
              </tr>
            </thead>
            <tbody>
              ${po.items.map((item: any) => {
                const total = item.base_quantity
                  ? item.base_quantity * item.unit_price
                  : item.quantity * item.unit_price;

                return `
                  <tr>
                    <td>
                      <strong>${item.product?.name || 'Unknown Product'}</strong>
                      ${item.product?.sku ? `<br><small>SKU: ${item.product.sku}</small>` : ''}
                    </td>
                    <td class="text-center">${formatQuantityWithSeparators(item.quantity)}</td>
                    <td class="text-center">${item.uom?.code || 'pcs'}</td>
                    <td class="text-right">${formatWithCurrency(item.unit_price)}</td>
                    <td class="text-right">${formatWithCurrency(total)}</td>
                    <td class="text-center">${formatQuantityWithSeparators(item.received_quantity || 0)}</td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>

          <!-- Total Section -->
          <div class="total-section">
            <div class="total-row">
              Total Amount: ${formatWithCurrency(
                po.items.reduce((sum: number, item: any) => {
                  const itemTotal = item.base_quantity
                    ? item.base_quantity * item.unit_price
                    : item.quantity * item.unit_price;
                  return sum + itemTotal;
                }, 0)
              )}
            </div>
          </div>

          <!-- Notes Section -->
          ${po.notes ? `
            <div class="notes-section">
              <h3>Notes:</h3>
              <p>${po.notes}</p>
            </div>
          ` : ''}

          <!-- Signature Section -->
          <div class="signature-section">
            <div class="signature-box">
              <div class="signature-line">Prepared By</div>
            </div>
            <div class="signature-box">
              <div class="signature-line">Approved By</div>
            </div>
            <div class="signature-box">
              <div class="signature-line">Received By</div>
            </div>
          </div>

          <!-- Footer -->
          <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #666;">
            Generated on ${currentDate} | Purchase Order: ${po.order_number}
          </div>
        </body>
      </html>
    `;
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamation}>
          <h3 className="font-medium">Error</h3>
          <p>{error}</p>
          <div className="mt-4">
            <Button color="gray" onClick={handleBackClick}>
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back to Purchase Orders
            </Button>
          </div>
        </Alert>
      </div>
    );
  }

  if (!purchaseOrder) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="warning" icon={HiOutlineExclamation}>
          <h3 className="font-medium">Purchase Order Not Found</h3>
          <p>The requested purchase order could not be found.</p>
          <div className="mt-4">
            <Button color="gray" onClick={handleBackClick}>
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back to Purchase Orders
            </Button>
          </div>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <HiOutlineDocumentText className="mr-2 h-6 w-6" />
              Purchase Order: {purchaseOrder.order_number}
            </h1>
            <div className="flex flex-wrap gap-4 mt-2">
              <div className="flex items-center">
                <HiOutlineOfficeBuilding className="mr-2 h-5 w-5 text-gray-500" />
                <button
                  onClick={() => navigate(`/suppliers/${purchaseOrder.supplier_id}`)}
                  className="text-blue-600 hover:text-blue-800 hover:underline font-medium focus:outline-none"
                  title="View supplier details"
                >
                  {purchaseOrder.supplier_name}
                </button>
              </div>
              <div className="flex items-center text-gray-500">
                <HiOutlineCalendar className="mr-1 h-5 w-5" />
                <span>Date: {formatDate(purchaseOrder.order_date)}</span>
              </div>
              <div className="flex items-center">
                <Badge color={getStatusColor(purchaseOrder.status)}>
                  {getStatusLabel(purchaseOrder.status)}
                </Badge>
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button color="gray" onClick={handleBackClick}>
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back
            </Button>
            <Tooltip content="Edit Purchase Order">
              <Button color="primary" onClick={() => navigate(`/purchases/orders/edit/${purchaseOrder.id}`)}>
                <HiOutlinePencil className="h-5 w-5" />
              </Button>
            </Tooltip>
            <Tooltip content="Print Purchase Order">
              <Button color="light" onClick={handlePrint}>
                <HiOutlinePrinter className="h-5 w-5" />
              </Button>
            </Tooltip>

            {/* Send to Supplier button at the top */}
            {purchaseOrder.status === 'draft' && (
              <Button
                color="success"
                onClick={handleSendToSupplier}
                disabled={isSending}
              >
                {isSending ? (
                  <Spinner size="sm" className="mr-2" />
                ) : (
                  <HiOutlineShoppingCart className="mr-2 h-5 w-5" />
                )}
                Send to Supplier
              </Button>
            )}

            {/* Receive Items button at the top */}
            {canReceiveItems() && (
              <Button
                color="success"
                onClick={() => navigate(`/inventory/receipts/create?purchaseOrderId=${purchaseOrder.id}`)}
              >
                <HiOutlineClipboardCheck className="mr-2 h-5 w-5" />
                Receive Items
              </Button>
            )}
          </div>
        </div>

        {/* Remove this entire section */}
        {/* <div className="mb-6">
          <h2 className="text-lg font-semibold mb-3">Order Information</h2>
          <div className="p-3 bg-gray-50 rounded-lg mb-3">
            <div className="flex items-center mb-2">
              <span className="text-sm text-gray-500 mr-2">Supplier:</span>
              <span className="font-medium">{purchaseOrder.supplier_name}</span>
            </div>
            <div className="flex items-center mb-2">
              <span className="text-sm text-gray-500 mr-2">Order Date:</span>
              <span className="font-medium">{formatDate(purchaseOrder.order_date)}</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-2">Total Amount:</span>
              <span className="font-medium">{formatWithCurrency(purchaseOrder.total_amount)}</span>
            </div>
          </div>
        </div> */}

        {/* Items Section */}
        <div>
          <h2 className="text-lg font-semibold mb-2 flex items-center">
            <HiOutlineClipboardList className="mr-2 h-5 w-5" />
            Items
          </h2>

          {purchaseOrder.items && purchaseOrder.items.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <Table.Head>
                  <Table.HeadCell>Product</Table.HeadCell>
                  <Table.HeadCell>Quantity</Table.HeadCell>
                  <Table.HeadCell>Unit</Table.HeadCell>
                  <Table.HeadCell>Unit Price</Table.HeadCell>
                  <Table.HeadCell>Total</Table.HeadCell>
                  <Table.HeadCell>Received</Table.HeadCell>
                </Table.Head>
                <Table.Body className="divide-y">
                  {purchaseOrder.items.map((item: any) => (
                    <Table.Row key={item.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                      <Table.Cell className="font-medium">
                        {item.product ? (
                          <button
                            onClick={() => navigate(`/products/details/${item.product.id}`)}
                            className="text-primary-600 hover:text-primary-800 hover:underline focus:outline-none"
                            title="View product details"
                          >
                            {item.product.name}
                          </button>
                        ) : (
                          'Unknown Product'
                        )}
                        {item.product?.sku && (
                          <div className="text-xs text-gray-500">
                            SKU: {item.product.sku}
                          </div>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        {formatQuantityWithSeparators(item.quantity)}
                      </Table.Cell>
                      <Table.Cell>
                        {item.uom?.name || 'Unknown Unit'}
                        {item.uom?.code && <span className="text-xs text-gray-500 block">({item.uom.code})</span>}
                      </Table.Cell>
                      <Table.Cell>
                        {formatWithCurrency(item.unit_price)}
                      </Table.Cell>

                      <Table.Cell>
                        {(() => {
                          // Use base_quantity if available, otherwise calculate with quantity
                          const total = item.base_quantity
                            ? item.base_quantity * item.unit_price
                            : item.quantity * item.unit_price;

                          return (
                            <>
                              {formatWithCurrency(total)}
                              <div className="text-xs text-gray-500">
                                {formatQuantityWithSeparators(item.quantity)} {item.uom?.code || 'unit'} × {formatWithCurrency(item.unit_price)}
                              </div>
                            </>
                          );
                        })()}
                      </Table.Cell>
                      <Table.Cell>
                        {formatQuantityWithSeparators(item.received_quantity || 0)}
                        {item.received_quantity > 0 && item.quantity > item.received_quantity && (
                          <Badge color="warning" className="ml-2">Partial</Badge>
                        )}
                        {item.received_quantity >= item.quantity && (
                          <Badge color="success" className="ml-2">Complete</Badge>
                        )}
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>

              <div className="flex justify-end mt-4">
                <div className="text-lg font-semibold">
                  Total: {formatWithCurrency(
                    // Recalculate the total to ensure it's accurate
                    purchaseOrder.items.reduce((sum: number, item: any) => {
                      // Use base_quantity if available, otherwise use quantity
                      const itemTotal = item.base_quantity
                        ? item.base_quantity * item.unit_price
                        : item.quantity * item.unit_price;
                      return sum + itemTotal;
                    }, 0)
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
              No items found in this purchase order.
            </div>
          )}
        </div>

        {/* Add Source Purchase Request Section */}
        {purchaseOrder.purchase_request_id && (
          <div className="mt-4">
            <h2 className="text-lg font-semibold mb-2 flex items-center">
              <HiOutlineClipboardList className="mr-2 h-5 w-5" />
              Source Purchase Request
            </h2>
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <HiOutlineClipboardList className="mr-2 h-5 w-5 text-gray-500" />
                  <button
                    onClick={() => navigate(`/purchases/requests/${purchaseOrder.purchase_request_id}`)}
                    className="text-blue-600 hover:text-blue-800 hover:underline font-medium focus:outline-none"
                    title="View purchase request details"
                  >
                    {purchaseOrder.purchase_request_number || `Request #${purchaseOrder.purchase_request_id.substring(0, 8)}`}
                  </button>
                </div>
                <Button
                  color="light"
                  size="xs"
                  onClick={() => navigate(`/purchases/requests/${purchaseOrder.purchase_request_id}`)}
                >
                  View Request
                </Button>
              </div>
              {purchaseOrder.purchase_request_status && (
                <div className="mt-2 flex items-center">
                  <span className="text-sm text-gray-500 mr-2">Status:</span>
                  <Badge color={getRequestStatusColor(purchaseOrder.purchase_request_status)}>
                    {getRequestStatusLabel(purchaseOrder.purchase_request_status)}
                  </Badge>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end mt-6 gap-2">
          {purchaseOrder.status === 'draft' && (
            <Button
              color="success"
              onClick={handleSendToSupplier}
              disabled={isSending}
            >
              {isSending ? (
                <Spinner size="sm" className="mr-2" />
              ) : (
                <HiOutlineShoppingCart className="mr-2 h-5 w-5" />
              )}
              Send to Supplier
            </Button>
          )}

          {canReceiveItems() && (
            <Button
              color="success"
              onClick={() => navigate(`/inventory/receipts/create?purchaseOrderId=${purchaseOrder.id}`)}
            >
              <HiOutlineClipboardCheck className="mr-2 h-5 w-5" />
              Receive Items
            </Button>
          )}
        </div>

        {/* System Information */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h2 className="text-sm font-medium text-gray-500 mb-2">System Information</h2>
          <div className="flex flex-wrap gap-x-6 gap-y-2">
            <div>
              <p className="text-xs text-gray-500">Purchase Order ID</p>
              <p className="text-sm">{purchaseOrder.id}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Order Number</p>
              <p className="text-sm">{purchaseOrder.order_number}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Status</p>
              <p className="text-sm">{getStatusLabel(purchaseOrder.status)}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Created</p>
              <p className="text-sm">{formatDateTime(purchaseOrder.created_at)}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Last Updated</p>
              <p className="text-sm">{formatDateTime(purchaseOrder.updated_at)}</p>
            </div>
            {purchaseOrder.created_by && (
              <div>
                <p className="text-xs text-gray-500">Created By</p>
                <p className="text-sm">{purchaseOrder.creator_name || purchaseOrder.created_by}</p>
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PurchaseOrderDetails;

// Add these helper functions if they don't already exist
const getRequestStatusColor = (status: string) => {
  switch (status) {
    case 'approved':
      return 'success';
    case 'pending':
      return 'warning';
    case 'rejected':
      return 'failure';
    case 'cancelled':
      return 'dark';
    default:
      return 'gray';
  }
};

const getRequestStatusLabel = (status: string) => {
  return status.charAt(0).toUpperCase() + status.slice(1);
};
