import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Badge,
  Modal,
  Table
} from 'flowbite-react';
import {
  HiOutlineExclamation,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineArrowLeft,
  HiOutlineTag,
  HiOutlineQrcode,
  HiOutlineCurrencyDollar,
  HiOutlineShoppingCart,
  HiOutlineDocumentText,
  HiOutlineCollection,
  HiOutlineExclamationCircle,
  HiOutlineScale
} from 'react-icons/hi';
import { getProductById, deleteProduct, Product } from '../../services/product';
import { useOrganization } from '../../context/OrganizationContext';
import { useProductPermissions } from '../../hooks/useProductPermissions';
import { getProductUoms } from '../../services/productUom';
import { ProductUom, UnitOfMeasurement } from '../../types/uom.types';
import { getProductTags, addTagToProduct, removeTagFromProduct } from '../../services/productTagService';
import { Tag } from '../../types/tagging.types';
import TagSelector from '../../components/tags/TagSelector';
import TagList from '../../components/tags/TagList';

const ProductDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { currentOrganization } = useOrganization();
  const navigate = useNavigate();
  const { canUpdateProducts, canDeleteProducts } = useProductPermissions();

  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [productUoms, setProductUoms] = useState<(ProductUom & { uom: UnitOfMeasurement })[]>([]);
  const [uomsLoading, setUomsLoading] = useState(true);
  const [uomsError, setUomsError] = useState<string | null>(null);

  // Tags state
  const [productTags, setProductTags] = useState<Tag[]>([]);
  const [isLoadingTags, setIsLoadingTags] = useState(false);
  const [tagsError, setTagsError] = useState<string | null>(null);

  // Delete confirmation modal
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      if (!currentOrganization || !id) return;

      setIsLoading(true);
      setError(null);

      try {
        const { product: productData, error: fetchError } = await getProductById(
          currentOrganization.id,
          id
        );

        if (fetchError) {
          setError(fetchError);
        } else if (productData) {
          setProduct(productData);
        } else {
          setError('Product not found');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the product');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProduct();
  }, [currentOrganization, id]);

  // Fetch product UoMs
  useEffect(() => {
    const fetchProductUoms = async () => {
      if (!currentOrganization || !id) return;

      setUomsLoading(true);
      setUomsError(null);

      try {
        const { productUoms: uomData, error: fetchError } = await getProductUoms(
          id,
          currentOrganization.id
        );

        if (fetchError) {
          setUomsError(fetchError);
        } else {
          setProductUoms(uomData);
        }
      } catch (err: any) {
        setUomsError(err.message || 'An error occurred while fetching product UoMs');
      } finally {
        setUomsLoading(false);
      }
    };

    if (product) {
      fetchProductUoms();
    }
  }, [currentOrganization, id, product]);

  // Fetch product tags
  useEffect(() => {
    const fetchProductTags = async () => {
      if (!id) return;

      setIsLoadingTags(true);
      setTagsError(null);

      try {
        const { tags, error } = await getProductTags(id);

        if (error) {
          setTagsError(error);
        } else {
          setProductTags(tags);
        }
      } catch (err: any) {
        setTagsError(err.message || 'An error occurred while fetching tags');
      } finally {
        setIsLoadingTags(false);
      }
    };

    fetchProductTags();
  }, [id]);

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!currentOrganization || !product) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const { success, error: deleteErr } = await deleteProduct(
        currentOrganization.id,
        product.id
      );

      if (deleteErr) {
        setDeleteError(deleteErr);
      } else if (success) {
        setShowDeleteConfirm(false);
        // Navigate to the product list page
        navigate('/products');
      }
    } catch (err: any) {
      setDeleteError(err.message || 'An error occurred while deleting the product');
    } finally {
      setIsDeleting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatQuantityWithSeparators = (quantity: number | null | undefined): string => {
    if (quantity === null || quantity === undefined) {
      return '0';
    }
    return new Intl.NumberFormat('en-US').format(quantity);
  };

  // Handle tag changes
  const handleTagsChange = async (newTags: Tag[]) => {
    if (!id) return;

    // Find tags that were added
    const addedTags = newTags.filter(
      newTag => !productTags.some(existingTag => existingTag.id === newTag.id)
    );

    // Find tags that were removed
    const removedTags = productTags.filter(
      existingTag => !newTags.some(newTag => newTag.id === existingTag.id)
    );

    // Update the state with the new tags immediately for better UX
    setProductTags(newTags);

    // Add new tags
    for (const tag of addedTags) {
      try {
        await addTagToProduct(id, tag.id);
      } catch (err) {
        console.error(`Failed to add tag ${tag.name}:`, err);
        // If there's an error, revert the tag addition in the UI
        setProductTags(prevTags => prevTags.filter(t => t.id !== tag.id));
      }
    }

    // Remove deleted tags
    for (const tag of removedTags) {
      try {
        await removeTagFromProduct(id, tag.id);
      } catch (err) {
        console.error(`Failed to remove tag ${tag.name}:`, err);
        // If there's an error, revert the tag removal in the UI
        setProductTags(prevTags => [...prevTags, tag]);
      }
    }
  };

  if (!currentOrganization) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">No Organization Selected</h3>
            <p>
              Please select an organization to view product details.
            </p>
          </Alert>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        </Card>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">Error</h3>
            <p>{error || 'Product not found'}</p>
            <div className="mt-4">
              <Link to="/products">
                <Button color="gray" size="sm">
                  <HiOutlineArrowLeft className="mr-2 h-4 w-4" />
                  Back to Products
                </Button>
              </Link>
            </div>
          </Alert>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        {/* Header with actions */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div className="flex items-center">
            <h1 className="text-2xl font-bold">{product.name}</h1>
            <Badge
              color={product.is_active ? 'success' : 'gray'}
              className="ml-3"
            >
              {product.is_active ? 'Active' : 'Inactive'}
            </Badge>
          </div>

          <div className="flex gap-2">
            <Link to="/products">
              <Button color="gray">
                <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
                Back
              </Button>
            </Link>
            {canUpdateProducts && (
              <Link to={`/products/edit/${product.id}`}>
                <Button color="primary">
                  <HiOutlinePencil className="mr-2 h-5 w-5" />
                  Edit
                </Button>
              </Link>
            )}
            {canDeleteProducts && (
              <Button color="failure" onClick={handleDeleteClick}>
                <HiOutlineTrash className="mr-2 h-5 w-5" />
                Delete
              </Button>
            )}
          </div>
        </div>

        {/* Product information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Product Image */}
          <div className="md:col-span-1">
            {product.image_url ? (
              <img
                src={product.image_url}
                alt={product.name}
                className="w-full h-auto rounded-lg object-cover shadow-md"
              />
            ) : (
              <div className="w-full aspect-square bg-gray-200 rounded-lg flex items-center justify-center text-gray-500">
                <span className="text-lg">No Image Available</span>
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="md:col-span-2 space-y-8">
            {/* Basic Information */}
            <div>
              <h2 className="text-xl font-semibold mb-4">Product Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  {/* SKU */}
                  <div className="flex items-start">
                    <HiOutlineTag className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">SKU</p>
                      <p>{product.sku || 'Not specified'}</p>
                    </div>
                  </div>

                  {/* Barcode */}
                  <div className="flex items-start">
                    <HiOutlineQrcode className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Barcode</p>
                      <p>{product.barcode || 'Not specified'}</p>
                    </div>
                  </div>

                  {/* Category */}
                  <div className="flex items-start">
                    <HiOutlineCollection className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Category</p>
                      <p>
                        {product.category_id
                          ? ((product as any).category?.name || 'Category')
                          : 'Uncategorized'}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  {/* Selling Price */}
                  <div className="flex items-start">
                    <HiOutlineCurrencyDollar className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Selling Price</p>
                      <p className="font-medium">{formatCurrency(product.unit_price)}</p>
                    </div>
                  </div>

                  {/* Cost Price */}
                  <div className="flex items-start">
                    <HiOutlineShoppingCart className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Cost Price</p>
                      <p>{product.cost_price ? formatCurrency(product.cost_price) : 'Not specified'}</p>
                    </div>
                  </div>


                </div>
              </div>
            </div>

            {/* Inventory Information */}
            <div>
              <h2 className="text-xl font-semibold mb-4">Inventory Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start">
                  <div className={`mt-1 mr-3 h-5 w-5 text-${
                    (product.stock_quantity || 0) <= (product.min_stock_level || 0)
                      ? 'red'
                      : 'green'
                  }-500`}>
                    {(product.stock_quantity || 0) <= (product.min_stock_level || 0) ? (
                      <HiOutlineExclamationCircle />
                    ) : (
                      <HiOutlineShoppingCart />
                    )}
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Current Stock</p>
                    <div className="flex items-center">
                      <p className={`font-medium ${
                        (product.stock_quantity || 0) <= (product.min_stock_level || 0)
                          ? 'text-red-500'
                          : 'text-green-500'
                      }`}>
                        {formatQuantityWithSeparators(product.stock_quantity || 0)} units
                      </p>
                      {(product.stock_quantity || 0) <= (product.min_stock_level || 0) && (
                        <Badge color="failure" className="ml-2">Low Stock</Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-start">
                  <HiOutlineExclamationCircle className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Minimum Stock Level</p>
                    <p>{formatQuantityWithSeparators(product.min_stock_level || 0)} units</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Tags */}
            <div>
              <h2 className="text-xl font-semibold mb-4">Tags</h2>
              <div className="flex items-start">
                <HiOutlineTag className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                <div className="w-full">
                  {isLoadingTags ? (
                    <div className="flex items-center">
                      <Spinner size="sm" className="mr-2" />
                      <span className="text-gray-500">Loading tags...</span>
                    </div>
                  ) : tagsError ? (
                    <Alert color="failure" className="mb-4">
                      {tagsError}
                    </Alert>
                  ) : (
                    <div>
                      {canUpdateProducts ? (
                        <TagSelector
                          entityType="product"
                          entityId={product.id}
                          selectedTags={productTags}
                          onTagsChange={handleTagsChange}
                        />
                      ) : productTags.length > 0 ? (
                        <TagList tags={productTags} />
                      ) : (
                        <p className="text-gray-500">No tags assigned to this product.</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Description */}
            {product.description && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Description</h2>
                <div className="flex items-start">
                  <HiOutlineDocumentText className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="whitespace-pre-line">{product.description}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Units of Measurement */}
            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <HiOutlineScale className="mr-2 h-5 w-5" />
                Units of Measurement
              </h2>

              {uomsError && (
                <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
                  {uomsError}
                </Alert>
              )}

              {uomsLoading ? (
                <div className="flex justify-center items-center p-4">
                  <Spinner size="md" />
                </div>
              ) : productUoms.length === 0 ? (
                <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
                  <p>No units of measurement defined for this product</p>
                  {canUpdateProducts && (
                    <div className="mt-2">
                      <Link to={`/products/edit/${product.id}`}>
                        <Button color="primary" size="xs">
                          <HiOutlinePencil className="mr-2 h-4 w-4" />
                          Add Units in Edit Mode
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table hoverable>
                    <Table.Head>
                      <Table.HeadCell>Unit</Table.HeadCell>
                      <Table.HeadCell>Conversion Factor</Table.HeadCell>
                      <Table.HeadCell>Default</Table.HeadCell>
                      <Table.HeadCell>Purchasing</Table.HeadCell>
                      <Table.HeadCell>Selling</Table.HeadCell>
                    </Table.Head>
                    <Table.Body className="divide-y">
                      {productUoms.map((item) => (
                        <Table.Row key={item.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                          <Table.Cell className="font-medium">
                            {item.uom.name} ({item.uom.code})
                          </Table.Cell>
                          <Table.Cell>
                            {item.conversion_factor}
                          </Table.Cell>
                          <Table.Cell>
                            {item.is_default ? (
                              <Badge color="success">Default</Badge>
                            ) : (
                              <span className="text-gray-400">No</span>
                            )}
                          </Table.Cell>
                          <Table.Cell>
                            {item.is_purchasing_unit ? (
                              <Badge color="info">Yes</Badge>
                            ) : (
                              <span className="text-gray-400">No</span>
                            )}
                          </Table.Cell>
                          <Table.Cell>
                            {item.is_selling_unit ? (
                              <Badge color="info">Yes</Badge>
                            ) : (
                              <span className="text-gray-400">No</span>
                            )}
                          </Table.Cell>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>

                  {canUpdateProducts && (
                    <div className="mt-4 flex justify-end">
                      <Link to={`/products/edit/${product.id}`}>
                        <Button color="primary" size="xs">
                          <HiOutlinePencil className="mr-2 h-4 w-4" />
                          Manage Units
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* System Information */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h2 className="text-sm font-medium text-gray-500 mb-2">System Information</h2>
          <div className="flex flex-wrap gap-4">
            <div>
              <p className="text-xs text-gray-500">Product ID</p>
              <p className="text-sm">{product.id}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Created</p>
              <p className="text-sm">{new Date(product.created_at).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Last Updated</p>
              <p className="text-sm">{new Date(product.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)}>
        <Modal.Header>Confirm Deletion</Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-red-500" />
            <h3 className="mb-5 text-lg font-normal text-gray-500">
              Are you sure you want to delete {product.name}?
            </h3>
            {deleteError && (
              <Alert color="failure" className="mb-4">
                {deleteError}
              </Alert>
            )}
            <div className="flex justify-center gap-4">
              <Button color="failure" onClick={confirmDelete} disabled={isDeleting}>
                {isDeleting ? <Spinner size="sm" /> : 'Yes, delete'}
              </Button>
              <Button color="gray" onClick={() => setShowDeleteConfirm(false)}>
                No, cancel
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default ProductDetails;
