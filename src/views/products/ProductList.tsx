import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Card,
  Table,
  <PERSON><PERSON>,
  Spinner,
  <PERSON>ert,
  Badge,
  TextInput,
  Dropdown,
  Checkbox
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineSearch,
  HiOutlineDotsVertical,
  HiOutlineExclamation,
  HiOutlineEye,
  HiOutlineTag,
  HiOutlineDownload,
  HiOutlineUpload
} from 'react-icons/hi';
import { getProducts, Product, deleteProduct } from '../../services/product';
import { useOrganization } from '../../context/OrganizationContext';
import { useProductPermissions } from '../../hooks/useProductPermissions';
import { useAuth } from '../../context/AuthContext';
import { isOrganizationOwner } from '../../services/userManagement';
import { getProductTags } from '../../services/productTagService';
import { addTagToProduct, removeTagFromProduct } from '../../services/productTagService';
import { Tag } from '../../types/tagging.types';
import TagList from '../../components/tags/TagList';
import BulkTagManager from '../../components/tags/BulkTagManager';
import TagFilter from '../../components/tags/TagFilter';
import Pagination from '../../components/common/Pagination';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { exportProducts } from '../../utils/excelExport';
import ProductImport from '../../components/products/ProductImport';
import { ImportResult } from '../../services/productImport';

const ProductList = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const { canCreateProducts, canUpdateProducts, canDeleteProducts } = useProductPermissions();
  const formatWithCurrency = useCurrencyFormatter();

  const [isOwner, setIsOwner] = useState<boolean | null>(null);
  const [permissionsLoading, setPermissionsLoading] = useState(true);

  // Check if the user is an owner directly from the database
  useEffect(() => {
    const checkOwnerStatus = async () => {
      if (!currentOrganization || !user) {
        setPermissionsLoading(false);
        return;
      }

      try {
        const { isOwner: ownerStatus, error: ownerError } = await isOrganizationOwner(
          currentOrganization.id,
          user.id
        );

        if (ownerError) {
          // Error occurred while checking owner status
        }

        setIsOwner(ownerStatus);
      } catch (err) {
        // Error in checkOwnerStatus
      } finally {
        setPermissionsLoading(false);
      }
    };

    checkOwnerStatus();
  }, [currentOrganization, user]);

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const [productsPerPage, setProductsPerPage] = useState(10);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);

  // Bulk selection and tag management
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [showBulkTagManager, setShowBulkTagManager] = useState(false);

  // Import modal
  const [showImportModal, setShowImportModal] = useState(false);

  // Tag filtering
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);

  // Product tags
  const [productTags, setProductTags] = useState<Record<string, Tag[]>>({});

  const fetchProducts = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { products: productData, count, error: fetchError } = await getProducts(
        currentOrganization.id,
        {
          searchQuery: searchQuery || undefined,
          limit: productsPerPage,
          offset: (currentPage - 1) * productsPerPage,
          tagIds: selectedTagIds.length > 0 ? selectedTagIds : undefined
        }
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setProducts(productData);
        setTotalProducts(count);

        // Fetch tags for each product
        fetchProductTags(productData);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching products');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [currentOrganization, currentPage, searchQuery, selectedTagIds, productsPerPage]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page when searching
    fetchProducts();
  };

  // Fetch tags for products
  const fetchProductTags = async (products: Product[]) => {
    const tagsMap: Record<string, Tag[]> = {};

    // Create an array of promises for fetching tags
    const tagPromises = products.map(async (product) => {
      try {
        const { tags } = await getProductTags(product.id);
        tagsMap[product.id] = tags;
      } catch (error) {
        console.error(`Error fetching tags for product ${product.id}:`, error);
        tagsMap[product.id] = [];
      }
    });

    // Wait for all promises to resolve
    await Promise.all(tagPromises);

    // Update state with the fetched tags
    setProductTags(tagsMap);
  };

  const handleDeleteClick = (product: Product) => {
    setProductToDelete(product);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!currentOrganization || !productToDelete) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const { success, error: deleteErr } = await deleteProduct(
        currentOrganization.id,
        productToDelete.id
      );

      if (deleteErr) {
        setDeleteError(deleteErr);
      } else if (success) {
        setShowDeleteConfirm(false);
        setProductToDelete(null);
        fetchProducts(); // Refresh the list
      }
    } catch (err: any) {
      setDeleteError(err.message || 'An error occurred while deleting the product');
    } finally {
      setIsDeleting(false);
    }
  };



  const totalPages = Math.ceil(totalProducts / productsPerPage);

  // Handle bulk selection
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedProducts(products.map(product => product.id));
    } else {
      setSelectedProducts([]);
    }
  };

  const handleSelectProduct = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  // Handle bulk tag operations
  const handleBulkTagging = async (tagIds: string[], action: 'add' | 'remove') => {
    if (!currentOrganization || selectedProducts.length === 0 || tagIds.length === 0) return;

    const operations = [];

    for (const productId of selectedProducts) {
      for (const tagId of tagIds) {
        if (action === 'add') {
          operations.push(addTagToProduct(productId, tagId));
        } else {
          operations.push(removeTagFromProduct(productId, tagId));
        }
      }
    }

    try {
      await Promise.all(operations);
      // Refresh the product list to show updated tags
      fetchProducts();
      return Promise.resolve();
    } catch (error) {
      console.error('Error during bulk tagging:', error);
      return Promise.reject(error);
    }
  };

  // Export products to Excel
  const handleExportProducts = () => {
    if (products.length === 0) {
      setError('No products to export');
      return;
    }
    exportProducts(products);
  };

  // Handle import completion
  const handleImportComplete = (result: ImportResult) => {
    if (result.successCount > 0) {
      // Refresh the product list to show imported products
      fetchProducts();
      setSelectedProducts([]); // Clear selection
    }
    setShowImportModal(false);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Products</h1>
            <p className="text-gray-500">
              Manage your product catalog here. You can add, edit, and delete products.
            </p>
          </div>

          <div className="flex gap-2">
            <Button color="light" onClick={handleExportProducts}>
              <HiOutlineDownload className="mr-2 h-4 w-4" />
              Export
            </Button>
            {(canCreateProducts || isOwner) && (
              <Button color="light" onClick={() => setShowImportModal(true)}>
                <HiOutlineUpload className="mr-2 h-4 w-4" />
                Import
              </Button>
            )}
            {permissionsLoading ? (
              <Button color="primary" disabled>
                <Spinner size="sm" className="mr-2" />
                Loading...
              </Button>
            ) : (canCreateProducts || isOwner) && (
              <Link to="/products/create">
                <Button color="primary">
                  <HiOutlinePlus className="mr-2 h-5 w-5" />
                  Add Product
                </Button>
              </Link>
            )}
          </div>
        </div>

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="mb-4">
          <div className="flex gap-2">
            <TextInput
              id="search"
              type="text"
              placeholder="Search products by name, SKU, or barcode"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
              icon={HiOutlineSearch}
            />
            <Button type="submit">
              Search
            </Button>
          </div>
        </form>

        {/* Tag Filter */}
        <div className="mb-4">
          <TagFilter
            selectedTags={selectedTagIds}
            onTagsChange={(tagIds) => {
              setSelectedTagIds(tagIds);
              setCurrentPage(1); // Reset to first page when filtering
            }}
          />
        </div>

        {/* Bulk Actions */}
        {selectedProducts.length > 0 && (
          <div className="mb-4 flex items-center justify-between bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
            <div className="text-sm">
              <span className="font-medium">{selectedProducts.length}</span> products selected
            </div>
            <div className="flex gap-2">
              <Button
                size="xs"
                color="light"
                onClick={() => setSelectedProducts([])}
              >
                Clear Selection
              </Button>
              <Button
                size="xs"
                color="info"
                onClick={() => setShowBulkTagManager(true)}
              >
                <HiOutlineTag className="mr-1 h-4 w-4" />
                Manage Tags
              </Button>
            </div>
          </div>
        )}

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        {deleteError && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {deleteError}
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : products.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 mb-4">No products found</p>
            {permissionsLoading ? (
              <Button color="primary" size="sm" disabled>
                <Spinner size="sm" className="mr-2" />
                Loading...
              </Button>
            ) : (canCreateProducts || isOwner) && (
              <Link to="/products/create">
                <Button color="primary" size="sm">
                  <HiOutlinePlus className="mr-2 h-4 w-4" />
                  Add Your First Product
                </Button>
              </Link>
            )}
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <Table hoverable>
                <Table.Head>
                  <Table.HeadCell className="w-4">
                    <Checkbox
                      checked={selectedProducts.length === products.length && products.length > 0}
                      onChange={handleSelectAll}
                    />
                  </Table.HeadCell>
                  <Table.HeadCell>Product</Table.HeadCell>
                  <Table.HeadCell>SKU</Table.HeadCell>
                  <Table.HeadCell>Price</Table.HeadCell>
                  <Table.HeadCell>Stock</Table.HeadCell>
                  <Table.HeadCell>Status</Table.HeadCell>
                  <Table.HeadCell>Tags</Table.HeadCell>
                  <Table.HeadCell>
                    <span className="sr-only">Actions</span>
                  </Table.HeadCell>
                </Table.Head>
                <Table.Body className="divide-y">
                  {products.map((product) => (
                    <Table.Row key={product.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                      <Table.Cell className="w-4">
                        <Checkbox
                          checked={selectedProducts.includes(product.id)}
                          onChange={() => handleSelectProduct(product.id)}
                        />
                      </Table.Cell>
                      <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                        <div className="flex items-center gap-3">
                          {product.image_url ? (
                            <img
                              src={product.image_url}
                              alt={product.name}
                              className="h-10 w-10 rounded-md object-cover"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center text-gray-500">
                              No img
                            </div>
                          )}
                          <div>
                            <Link
                              to={`/products/details/${product.id}`}
                              className="font-medium hover:text-primary hover:underline"
                            >
                              {product.name}
                            </Link>
                            <p className="text-xs text-gray-500">
                              {product.category_id
                                ? ((product as any).category?.name || 'Category')
                                : 'Uncategorized'}
                            </p>
                          </div>
                        </div>
                      </Table.Cell>
                      <Table.Cell>{product.sku || '-'}</Table.Cell>
                      <Table.Cell>{formatWithCurrency(product.unit_price)}</Table.Cell>
                      <Table.Cell>
                        <div className="flex items-center">
                          <span className={`mr-2 ${
                            (product.stock_quantity || 0) <= (product.min_stock_level || 0)
                              ? 'text-red-500'
                              : 'text-green-500'
                          }`}>
                            {product.stock_quantity || 0}
                          </span>
                          {(product.stock_quantity || 0) <= (product.min_stock_level || 0) && (
                            <Badge color="failure" size="sm">Low</Badge>
                          )}
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <Badge
                          color={product.is_active ? 'success' : 'gray'}
                          className="whitespace-nowrap"
                        >
                          {product.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell>
                        {productTags[product.id] && productTags[product.id].length > 0 ? (
                          <div className="flex flex-wrap gap-1 max-w-xs">
                            <TagList
                              tags={productTags[product.id]}
                              maxDisplay={3}
                              showTooltip={true}
                            />
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm italic">No tags</span>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex items-center">
                          <Dropdown
                            label=""
                            dismissOnClick={true}
                            renderTrigger={() => (
                              <Button color="gray" size="xs">
                                <HiOutlineDotsVertical className="h-4 w-4" />
                              </Button>
                            )}
                          >
                            <Dropdown.Item as={Link} to={`/products/details/${product.id}`} className="text-blue-600 hover:bg-blue-50">
                              <HiOutlineEye className="mr-2 h-4 w-4" />
                              View Details
                            </Dropdown.Item>
                            {canUpdateProducts && (
                              <Dropdown.Item as={Link} to={`/products/edit/${product.id}`} className="text-primary hover:bg-primary/10">
                                <HiOutlinePencil className="mr-2 h-4 w-4" />
                                Edit
                              </Dropdown.Item>
                            )}
                            {canDeleteProducts && (
                              <Dropdown.Item onClick={() => handleDeleteClick(product)} className="text-red-600 hover:bg-red-50">
                                <HiOutlineTrash className="mr-2 h-4 w-4" />
                                Delete
                              </Dropdown.Item>
                            )}
                          </Dropdown>
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </div>

            {/* Pagination */}
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={productsPerPage}
              totalItems={totalProducts}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={(newItemsPerPage) => {
                setProductsPerPage(newItemsPerPage);
                setCurrentPage(1); // Reset to first page when changing items per page
              }}
              itemName="products"
            />
          </>
        )}
      </Card>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && productToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="max-w-md w-full">
            <h3 className="text-xl font-bold mb-2">Delete Product</h3>
            <p className="text-gray-700 mb-4">
              Are you sure you want to delete <strong>{productToDelete.name}</strong>? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-2">
              <Button
                color="gray"
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setProductToDelete(null);
                }}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                color="failure"
                onClick={confirmDelete}
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Deleting...
                  </>
                ) : (
                  'Delete'
                )}
              </Button>
            </div>
          </Card>
        </div>
      )}

      {/* Bulk Tag Manager Modal */}
      <BulkTagManager
        show={showBulkTagManager}
        onClose={() => setShowBulkTagManager(false)}
        selectedIds={selectedProducts}
        entityType="product"
        onApplyTags={handleBulkTagging}
      />

      {/* Product Import Modal */}
      <ProductImport
        show={showImportModal}
        onClose={() => setShowImportModal(false)}
        onImportComplete={handleImportComplete}
      />
    </div>
  );
};

export default ProductList;
