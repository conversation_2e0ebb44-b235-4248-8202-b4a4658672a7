import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>, Spinner, Ta<PERSON>, TabItem } from 'flowbite-react';
import { HiOutlineExclamation } from 'react-icons/hi';
import ProductForm from '../../components/products/ProductForm';
import ProductUomManager from '../../components/products/ProductUomManager';
import { getProductById, updateProduct, Product } from '../../services/product';
import { useOrganization } from '../../context/OrganizationContext';
import { useProductPermissions } from '../../hooks/useProductPermissions';

const EditProduct = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const { canUpdateProducts, isOwnerAdminOrManager } = useProductPermissions();

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('basic');

  useEffect(() => {
    const fetchProduct = async () => {
      if (!currentOrganization || !id) return;

      setLoading(true);
      setError(null);

      try {
        const { product: productData, error: fetchError } = await getProductById(
          currentOrganization.id,
          id
        );

        if (fetchError) {
          setError(fetchError);
        } else if (productData) {
          setProduct(productData);
        } else {
          setError('Product not found');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the product');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [currentOrganization, id]);

  const handleSubmit = async (productData: Partial<Product>) => {
    if (!currentOrganization || !id) {
      setError('No organization selected or product ID missing');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    // Create a clean product data object without the category property
    const cleanProductData = { ...productData };
    if ('category' in cleanProductData) {
      delete cleanProductData.category;
    }

    try {
      const { product: updatedProduct, error: updateError } = await updateProduct(
        currentOrganization.id,
        id,
        cleanProductData
      );

      if (updateError) {
        setError(updateError);
      } else if (updatedProduct) {
        // Navigate to the product list page
        navigate('/products');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while updating the product');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!canUpdateProducts) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">Permission Denied</h3>
            <p>
              You don't have permission to edit products. Only owners, admins, and inventory managers can edit products.
            </p>
          </Alert>
        </Card>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        </Card>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">Error</h3>
            <p>{error || 'Product not found'}</p>
          </Alert>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Edit Product: {product.name}</h1>
        </div>

        <Tabs
          aria-label="Product tabs"
          variant="underline"
          onActiveTabChange={(tab) => setActiveTab(String(tab))}
        >
          <TabItem title="Basic Information" active={activeTab === '0'}>
            <ProductForm
              initialData={product}
              onSubmit={handleSubmit}
              isSubmitting={isSubmitting}
              error={error || undefined}
            />
          </TabItem>
          <TabItem title="Units of Measurement" active={activeTab === '1'}>
            {product && <ProductUomManager productId={product.id} />}
          </TabItem>
        </Tabs>
      </Card>
    </div>
  );
};

export default EditProduct;
