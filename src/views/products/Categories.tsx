import { useState, useEffect } from 'react';
import {
  Card,
  Table,
  <PERSON><PERSON>,
  Spinner,
  <PERSON>ert,
  Modal,
  TextInput,
  Textarea,
  Label,
  Dropdown
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlineExclamation,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineDotsVertical
} from 'react-icons/hi';
import {
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  Category
} from '../../services/product';
import { useOrganization } from '../../context/OrganizationContext';
import { useProductPermissions } from '../../hooks/useProductPermissions';
import Pagination from '../../components/common/Pagination';

const Categories = () => {
  const { currentOrganization } = useOrganization();
  const { canCreateProducts, canUpdateProducts } = useProductPermissions();

  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState<'add' | 'edit'>('add');
  const [currentCategory, setCurrentCategory] = useState<Category | null>(null);
  const [categoryName, setCategoryName] = useState('');
  const [categoryDescription, setCategoryDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Delete confirmation
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  const fetchCategories = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { categories: categoryData, count, error: fetchError } = await getCategories(
        currentOrganization.id,
        {
          searchQuery: searchQuery || undefined,
          limit: itemsPerPage,
          offset: (currentPage - 1) * itemsPerPage,
        }
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setCategories(categoryData);
        setTotalCount(count);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching categories');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, [currentOrganization, currentPage, searchQuery, itemsPerPage]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
    fetchCategories();
  };

  // Open modal for adding a new category
  const openAddModal = () => {
    setModalMode('add');
    setCurrentCategory(null);
    setCategoryName('');
    setCategoryDescription('');
    setSubmitError(null);
    setShowModal(true);
  };

  // Open modal for editing a category
  const openEditModal = (category: Category) => {
    setModalMode('edit');
    setCurrentCategory(category);
    setCategoryName(category.name);
    setCategoryDescription(category.description || '');
    setSubmitError(null);
    setShowModal(true);
  };

  // Open delete confirmation modal
  const openDeleteModal = (category: Category) => {
    setCategoryToDelete(category);
    setDeleteError(null);
    setShowDeleteConfirm(true);
  };

  // Handle form submission (create or update)
  const handleSubmitCategory = async () => {
    if (!currentOrganization) return;

    if (!categoryName.trim()) {
      setSubmitError('Category name is required');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      if (modalMode === 'add') {
        // Create new category
        const { category, error: createError } = await createCategory(
          currentOrganization.id,
          categoryName.trim(),
          categoryDescription.trim() || undefined
        );

        if (createError) {
          setSubmitError(createError);
        } else if (category) {
          setShowModal(false);
          setCategoryName('');
          setCategoryDescription('');
          fetchCategories(); // Refresh the list
        }
      } else {
        // Update existing category
        if (!currentCategory) return;

        const { category, error: updateError } = await updateCategory(
          currentOrganization.id,
          currentCategory.id,
          {
            name: categoryName.trim(),
            description: categoryDescription.trim() || null
          }
        );

        if (updateError) {
          setSubmitError(updateError);
        } else if (category) {
          setShowModal(false);
          setCategoryName('');
          setCategoryDescription('');
          fetchCategories(); // Refresh the list
        }
      }
    } catch (err: any) {
      setSubmitError(err.message || `An error occurred while ${modalMode === 'add' ? 'creating' : 'updating'} the category`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle category deletion
  const handleDeleteCategory = async () => {
    if (!currentOrganization || !categoryToDelete) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const { success, error: deleteErr } = await deleteCategory(
        currentOrganization.id,
        categoryToDelete.id
      );

      if (deleteErr) {
        setDeleteError(deleteErr);
      } else if (success) {
        setShowDeleteConfirm(false);
        setCategoryToDelete(null);
        fetchCategories(); // Refresh the list
      }
    } catch (err: any) {
      setDeleteError(err.message || 'An error occurred while deleting the category');
    } finally {
      setIsDeleting(false);
    }
  };

  const canManageCategories = canCreateProducts && canUpdateProducts;

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Product Categories</h1>
            <p className="text-gray-500">
              Manage your product categories here. Categories help you organize your products.
            </p>
          </div>

          {canManageCategories && (
            <Button color="primary" onClick={openAddModal}>
              <HiOutlinePlus className="mr-2 h-5 w-5" />
              Add Category
            </Button>
          )}
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="mb-6">
          <div className="flex gap-2">
            <TextInput
              id="search"
              type="text"
              placeholder="Search categories by name or description"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
            <Button type="submit">
              Search
            </Button>
          </div>
        </form>

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : categories.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 mb-4">No categories found</p>
            {canManageCategories && (
              <Button color="primary" size="sm" onClick={openAddModal}>
                <HiOutlinePlus className="mr-2 h-4 w-4" />
                Add Your First Category
              </Button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Category Name</Table.HeadCell>
                <Table.HeadCell>Description</Table.HeadCell>
                <Table.HeadCell>Products</Table.HeadCell>
                {canManageCategories && (
                  <Table.HeadCell>
                    <span className="sr-only">Actions</span>
                  </Table.HeadCell>
                )}
              </Table.Head>
              <Table.Body className="divide-y">
                {categories.map((category) => (
                  <Table.Row key={category.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                    <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                      {category.name}
                    </Table.Cell>
                    <Table.Cell>
                      {category.description || <span className="text-gray-400">No description</span>}
                    </Table.Cell>
                    <Table.Cell>
                      {/* In a real implementation, you would show the count of products in this category */}
                      <span className="text-gray-500">-</span>
                    </Table.Cell>
                    {canManageCategories && (
                      <Table.Cell>
                        <div className="flex items-center">
                          <Dropdown
                            label=""
                            dismissOnClick={true}
                            renderTrigger={() => (
                              <Button color="gray" size="xs">
                                <HiOutlineDotsVertical className="h-4 w-4" />
                              </Button>
                            )}
                          >
                            <Dropdown.Item onClick={() => openEditModal(category)}>
                              <HiOutlinePencil className="mr-2 h-4 w-4" />
                              Edit
                            </Dropdown.Item>
                            <Dropdown.Item onClick={() => openDeleteModal(category)}>
                              <HiOutlineTrash className="mr-2 h-4 w-4" />
                              Delete
                            </Dropdown.Item>
                          </Dropdown>
                        </div>
                      </Table.Cell>
                    )}
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / itemsPerPage)}
          itemsPerPage={itemsPerPage}
          totalItems={totalCount}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1); // Reset to first page when changing items per page
          }}
          itemName="categories"
        />
      </Card>

      {/* Category Modal (Add/Edit) */}
      <Modal show={showModal} onClose={() => setShowModal(false)}>
        <Modal.Header>{modalMode === 'add' ? 'Add New Category' : 'Edit Category'}</Modal.Header>
        <Modal.Body>
          {submitError && (
            <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
              {submitError}
            </Alert>
          )}

          <div className="space-y-6">
            <div>
              <div className="mb-2 block">
                <Label htmlFor="categoryName" value="Category Name *" />
              </div>
              <TextInput
                id="categoryName"
                value={categoryName}
                onChange={(e) => setCategoryName(e.target.value)}
                required
              />
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="categoryDescription" value="Description" />
              </div>
              <Textarea
                id="categoryDescription"
                value={categoryDescription}
                onChange={(e) => setCategoryDescription(e.target.value)}
                rows={3}
              />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="primary" onClick={handleSubmitCategory} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                {modalMode === 'add' ? 'Adding...' : 'Saving...'}
              </>
            ) : (
              modalMode === 'add' ? 'Add Category' : 'Save Changes'
            )}
          </Button>
          <Button color="gray" onClick={() => setShowModal(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)} size="md">
        <Modal.Header>Delete Category</Modal.Header>
        <Modal.Body>
          {deleteError && (
            <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
              {deleteError}
            </Alert>
          )}

          <p className="text-gray-700">
            Are you sure you want to delete <strong>{categoryToDelete?.name}</strong>?
          </p>
          <p className="text-gray-500 mt-2">
            This action cannot be undone. Products in this category will not be deleted, but they will no longer be associated with this category.
          </p>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="failure"
            onClick={handleDeleteCategory}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
          <Button color="gray" onClick={() => setShowDeleteConfirm(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default Categories;
