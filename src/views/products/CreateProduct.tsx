import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, Al<PERSON>, Spinner } from 'flowbite-react';
import { HiOutlineExclamation } from 'react-icons/hi';
import ProductForm from '../../components/products/ProductForm';
import { createProduct, Product } from '../../services/product';
import { useOrganization } from '../../context/OrganizationContext';
import { useProductPermissions } from '../../hooks/useProductPermissions';
import { useAuth } from '../../context/AuthContext';
import { isOrganizationOwner } from '../../services/userManagement';

const CreateProduct = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentOrganization, currentMember } = useOrganization();
  const { canCreateProducts, isOwnerAdminOrManager } = useProductPermissions();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOwner, setIsOwner] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  // Check if the user is an owner directly from the database
  useEffect(() => {
    const checkOwnerStatus = async () => {
      if (!currentOrganization || !user) {
        setLoading(false);
        return;
      }

      try {
        const { isOwner: ownerStatus, error: ownerError } = await isOrganizationOwner(
          currentOrganization.id,
          user.id
        );

        if (ownerError) {
          // Error checking owner status
        }

        setIsOwner(ownerStatus);
      } catch (err) {
        // Error in checkOwnerStatus
      } finally {
        setLoading(false);
      }
    };

    checkOwnerStatus();
  }, [currentOrganization, user]);

  const handleSubmit = async (productData: Partial<Product>) => {
    if (!currentOrganization) {
      setError('No organization selected');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const { product, error: createError } = await createProduct(
        currentOrganization.id,
        productData as Omit<Product, 'id' | 'organization_id' | 'created_at' | 'updated_at'>
      );

      if (createError) {
        setError(createError);
      } else if (product) {
        // Navigate to the product list page
        navigate('/products');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while creating the product');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
            <p className="ml-4">Checking permissions...</p>
          </div>
        </Card>
      </div>
    );
  }

  // Allow owners to create products regardless of permission check
  // Uses canCreateProducts from useProductPermissions() and isOwner from direct DB check
  if (!canCreateProducts && !isOwner) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">Permission Denied</h3>
            <p>
              You don't have permission to create products. Only owners, admins, and inventory managers can create products.
            </p>
          </Alert>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Create New Product</h1>
        </div>

        <ProductForm
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          error={error || undefined}
        />
      </Card>
    </div>
  );
};

export default CreateProduct;
