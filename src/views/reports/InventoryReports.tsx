import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> } from 'flowbite-react';
import { useOrganization } from 'src/context/OrganizationContext';
import { HiOutlineDocumentReport, HiOutlineExclamationCircle, HiOutlineRefresh } from 'react-icons/hi';
import { getProducts } from '../../services/product';
import { getInventoryTransactions } from '../../services/inventoryTransaction';
import { formatCurrency } from '../../utils/formatters';
import PageHeader from '../../components/common/PageHeader';
import InventoryValueReport from '../../components/reports/inventory/InventoryValueReport';
import LowStockReport from '../../components/reports/inventory/LowStockReport';
import InventoryMovementReport from '../../components/reports/inventory/InventoryMovementReport';
import InventoryAgingReport from '../../components/reports/inventory/InventoryAgingReport';

const InventoryReports: React.FC = () => {
  const { currentOrganization: organization } = useOrganization();
  const [activeTab, setActiveTab] = useState<string>('value');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [lowStockItems, setLowStockItems] = useState<any[]>([]);
  const [totalInventoryValue, setTotalInventoryValue] = useState<number>(0);
  const [inventoryTransactions, setInventoryTransactions] = useState<any[]>([]);
  const [initialized, setInitialized] = useState<boolean>(false);

  const fetchData = async () => {
    console.log('Fetching data, organization:', organization);
    if (!organization || !organization.id) {
      console.warn('No organization or organization ID available');
      setError('Please select an organization first');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Fetch products
      console.log('Making API call to getProducts with organization ID:', organization.id);
      const productResult = await getProducts(organization.id);
      console.log('Product API result:', productResult);

      if (productResult.error) {
        throw new Error(productResult.error);
      }

      // Use products data directly without mapping
      const productsData = productResult.products || [];

      console.log(`Received ${productsData.length} products`, productsData[0]);
      setProducts(productsData);

      // Calculate low stock items
      const lowStock = productsData.filter(product =>
        (product.stock_quantity || 0) <= (product.min_stock_level || 0)
      );
      setLowStockItems(lowStock);
      console.log(`Found ${lowStock.length} low stock items`);

      // Calculate total inventory value using unit_price
      const totalValue = productsData.reduce((sum, product) =>
        sum + ((product.stock_quantity || 0) * (product.unit_price || 0)), 0
      );
      setTotalInventoryValue(totalValue);

      // Fetch inventory transactions for movement data
      const transactionResult = await getInventoryTransactions(organization.id, {
        limit: 1000, // Get a large number of transactions for better analysis
        sortOrder: 'desc'
      });

      if (transactionResult.error) {
        console.warn('Error fetching inventory transactions:', transactionResult.error);
        // Continue with product data even if transactions fail
      } else {
        console.log(`Received ${transactionResult.transactions.length} inventory transactions`);
        setInventoryTransactions(transactionResult.transactions);
      }

      setInitialized(true);
    } catch (err: any) {
      console.error('Error fetching inventory report data:', err);
      setError(err.message || 'Failed to load inventory report data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when organization changes or component mounts
  useEffect(() => {
    console.log('Organization changed or component mounted:', organization);
    if (organization) {
      console.log('Organization available, fetching data');
      fetchData();
    } else {
      console.log('No organization available, waiting...');
      setIsLoading(false); // Stop loading if no organization
    }
  }, [organization]);

  // Log state for debugging when data changes
  useEffect(() => {
    console.log('Current state:', {
      activeTab,
      isLoading,
      organization: organization?.name || 'No organization',
      productsCount: products.length,
      lowStockCount: lowStockItems.length,
      hasError: !!error,
      initialized
    });
  }, [activeTab, isLoading, products.length, lowStockItems.length, error, organization, initialized]);

  const handleRefresh = () => {
    fetchData();
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <PageHeader
        title="Inventory Reports"
        description="View and analyze inventory data"
        icon={<HiOutlineDocumentReport className="h-8 w-8" />}
        actions={
          <Button color="light" onClick={handleRefresh}>
            <HiOutlineRefresh className="mr-2 h-5 w-5" />
            Refresh
          </Button>
        }
      />

      {error && (
        <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
          <span className="font-medium">Error!</span> {error}
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <div className="flex flex-col items-center">
            <h5 className="text-lg font-bold text-gray-900 dark:text-white">Total Inventory Value</h5>
            {isLoading ? (
              <Spinner size="md" className="my-2" />
            ) : (
              <p className="text-2xl font-bold text-blue-600">{formatCurrency(totalInventoryValue)}</p>
            )}
            <p className="text-sm text-gray-500">Based on current stock levels</p>
          </div>
        </Card>

        <Card>
          <div className="flex flex-col items-center">
            <h5 className="text-lg font-bold text-gray-900 dark:text-white">Total Products</h5>
            {isLoading ? (
              <Spinner size="md" className="my-2" />
            ) : (
              <p className="text-2xl font-bold text-green-600">{products.length}</p>
            )}
            <p className="text-sm text-gray-500">Products in inventory</p>
          </div>
        </Card>

        <Card>
          <div className="flex flex-col items-center">
            <h5 className="text-lg font-bold text-gray-900 dark:text-white">Low Stock Items</h5>
            {isLoading ? (
              <Spinner size="md" className="my-2" />
            ) : (
              <p className="text-2xl font-bold text-red-600">{lowStockItems.length}</p>
            )}
            <p className="text-sm text-gray-500">Items below minimum stock level</p>
          </div>
        </Card>
      </div>

      <Card>
        <Tabs
          aria-label="Inventory report tabs"
          onActiveTabChange={(tabIndex) => {
            console.log('Tab changed to index:', tabIndex);
            // Map numeric tab index to our string-based tab states
            const tabStates = ['value', 'low-stock', 'movement', 'aging'];
            const newActiveTab = tabStates[tabIndex];
            console.log('Setting activeTab to:', newActiveTab);
            setActiveTab(newActiveTab);
          }}
        >
          <Tabs.Item title="Inventory Value">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Spinner size="xl" />
              </div>
            ) : products.length > 0 ? (
              <InventoryValueReport products={products} />
            ) : (
              <div className="p-6 text-center text-gray-500">
                No product data available. {error ? `Error: ${error}` : ''}
              </div>
            )}
          </Tabs.Item>

          <Tabs.Item title="Low Stock">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Spinner size="xl" />
              </div>
            ) : lowStockItems.length > 0 ? (
              <LowStockReport products={lowStockItems} />
            ) : (
              <div className="p-6 text-center text-gray-500">
                No low stock items found. {error ? `Error: ${error}` : ''}
              </div>
            )}
          </Tabs.Item>

          <Tabs.Item title="Inventory Movement">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Spinner size="xl" />
              </div>
            ) : products.length > 0 ? (
              <InventoryMovementReport
                products={products}
                transactions={inventoryTransactions}
              />
            ) : (
              <div className="p-6 text-center text-gray-500">
                No product data available. {error ? `Error: ${error}` : ''}
              </div>
            )}
          </Tabs.Item>

          <Tabs.Item title="Inventory Aging">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Spinner size="xl" />
              </div>
            ) : products.length > 0 ? (
              <InventoryAgingReport
                products={products}
                transactions={inventoryTransactions}
              />
            ) : (
              <div className="p-6 text-center text-gray-500">
                No product data available. {error ? `Error: ${error}` : ''}
              </div>
            )}
          </Tabs.Item>
        </Tabs>
      </Card>
    </div>
  );
};

export default InventoryReports;
