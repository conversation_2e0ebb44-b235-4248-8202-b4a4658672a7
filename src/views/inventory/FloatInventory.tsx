import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Table,
  TextInput,
  <PERSON>ge,
  Spinner,
  <PERSON><PERSON>,
  Modal,
  Label,
  Pagination
} from 'flowbite-react';
import {
  HiOutlineSearch,
  HiOutlineAdjustments,
  HiOutlineRefresh,
  HiOutlineExclamation,
  HiOutlineCheck,
  HiOutlineX,
  HiOutlineDocumentReport
} from 'react-icons/hi';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useOrganization } from '../../context/OrganizationContext';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';
import { formatDate, formatDateTime } from '../../utils/formatters';
import { formatWithCurrency } from '../../utils/formatters';
import {
  getFloatInventory,
  getFloatInventorySummary,
  resolveFloatInventory,
  updateInventorySettings,
  FloatInventory
} from '../../services/floatInventory';
import { toast } from 'react-hot-toast';

const FloatInventoryView: React.FC = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const { settings } = useOrganizationSettings();

  // State
  const [floatItems, setFloatItems] = useState<FloatInventory[]>([]);
  const [summary, setSummary] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage] = useState(20);
  const [showResolved, setShowResolved] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showResolveModal, setShowResolveModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<FloatInventory | null>(null);
  const [resolveNotes, setResolveNotes] = useState('');
  const [resolving, setResolving] = useState(false);
  const [inventorySettings, setInventorySettings] = useState({
    allow_negative_inventory: true,
    warn_on_low_inventory: true,
    auto_create_purchase_requests: true
  });
  const [showSettingsModal, setShowSettingsModal] = useState(false);

  // Fetch float inventory
  useEffect(() => {
    const fetchData = async () => {
      if (!currentOrganization) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch float inventory
        const { floatItems: items, count, error } = await getFloatInventory(
          currentOrganization.id,
          {
            resolved: showResolved,
            limit: itemsPerPage,
            offset: (currentPage - 1) * itemsPerPage
          }
        );

        if (error) {
          throw new Error(error);
        }

        setFloatItems(items);
        setTotalItems(count);

        // Fetch summary
        const { summary: summaryData, error: summaryError } = await getFloatInventorySummary(
          currentOrganization.id
        );

        if (summaryError) {
          throw new Error(summaryError);
        }

        setSummary(summaryData);
      } catch (err: any) {
        setError(err.message || 'Failed to fetch float inventory');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentOrganization, currentPage, showResolved, itemsPerPage]);

  // Handle resolve
  const handleResolve = async () => {
    if (!user || !selectedItem) return;

    setResolving(true);

    try {
      const { success, error } = await resolveFloatInventory(
        selectedItem.id,
        user.id,
        resolveNotes
      );

      if (error) {
        throw new Error(error);
      }

      if (success) {
        toast.success('Float inventory resolved successfully');
        setShowResolveModal(false);

        // Refresh data
        const { floatItems: items, count } = await getFloatInventory(
          currentOrganization!.id,
          {
            resolved: showResolved,
            limit: itemsPerPage,
            offset: (currentPage - 1) * itemsPerPage
          }
        );

        setFloatItems(items);
        setTotalItems(count);
      }
    } catch (err: any) {
      toast.error(err.message || 'Failed to resolve float inventory');
    } finally {
      setResolving(false);
    }
  };

  // Handle settings update
  const handleUpdateSettings = async () => {
    if (!currentOrganization) return;

    try {
      const { success, error } = await updateInventorySettings(
        currentOrganization.id,
        inventorySettings
      );

      if (error) {
        throw new Error(error);
      }

      if (success) {
        toast.success('Inventory settings updated successfully');
        setShowSettingsModal(false);
      }
    } catch (err: any) {
      toast.error(err.message || 'Failed to update inventory settings');
    }
  };

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Float Inventory Management</h1>
        <div className="flex gap-2">
          <Link to="/inventory/float/report">
            <Button size="sm" color="warning">
              <HiOutlineDocumentReport className="mr-2 h-5 w-5" />
              Reports
            </Button>
          </Link>
          <Button
            size="sm"
            color="light"
            onClick={() => setShowSettingsModal(true)}
          >
            <HiOutlineAdjustments className="mr-2 h-5 w-5" />
            Settings
          </Button>
        </div>
      </div>

      {/* Info Card */}
      <Card className="mb-4">
        <h2 className="text-lg font-semibold mb-2">What is Float Inventory?</h2>
        <p className="text-gray-700 mb-2">
          Float inventory occurs when items are sold without sufficient stock. This creates a "negative" inventory balance that needs to be resolved when new stock arrives.
        </p>
        <p className="text-gray-700">
          The system automatically tracks float inventory and resolves it when new stock is received. You can also manually resolve float inventory using the actions below.
        </p>
      </Card>

      {/* Summary Card */}
      <Card className="mb-4">
        <h2 className="text-lg font-semibold mb-4">Float Inventory Summary</h2>
        {loading && summary.length === 0 ? (
          <div className="flex justify-center items-center p-4">
            <Spinner size="xl" />
          </div>
        ) : summary.length === 0 ? (
          <Alert color="success">
            <span className="font-medium">Good news!</span> You have no unresolved float inventory.
          </Alert>
        ) : (
          <Table>
            <Table.Head>
              <Table.HeadCell>Product</Table.HeadCell>
              <Table.HeadCell>Unresolved Quantity</Table.HeadCell>
              <Table.HeadCell>Since</Table.HeadCell>
              <Table.HeadCell>Actions</Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y">
              {summary.map((item) => (
                <Table.Row key={item.product_id} className="bg-white">
                  <Table.Cell className="whitespace-nowrap font-medium text-gray-900">
                    <Link to={`/inventory/details/${item.product_id}`} className="text-blue-600 hover:underline">
                      {item.product_name}
                    </Link>
                  </Table.Cell>
                  <Table.Cell>{item.unresolved_float_quantity}</Table.Cell>
                  <Table.Cell>{formatDate(item.oldest_float_date)}</Table.Cell>
                  <Table.Cell>
                    <Link to={`/inventory/receipts/create?product=${item.product_id}`}>
                      <Button size="xs" color="info">
                        Receive Stock
                      </Button>
                    </Link>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        )}
      </Card>

      {/* Float Inventory List */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Float Inventory Details</h2>
          <div className="flex gap-2">
            <Button
              size="xs"
              color={showResolved ? 'light' : 'gray'}
              onClick={() => setShowResolved(!showResolved)}
            >
              {showResolved ? 'Hide Resolved' : 'Show Resolved'}
            </Button>
          </div>
        </div>

        {loading && floatItems.length === 0 ? (
          <div className="flex justify-center items-center p-4">
            <Spinner size="xl" />
          </div>
        ) : error ? (
          <Alert color="failure">
            <span className="font-medium">Error:</span> {error}
          </Alert>
        ) : floatItems.length === 0 ? (
          <Alert color="info">
            <span className="font-medium">No float inventory found.</span> {showResolved ? 'Try showing unresolved items.' : ''}
          </Alert>
        ) : (
          <>
            <Table>
              <Table.Head>
                <Table.HeadCell>Product</Table.HeadCell>
                <Table.HeadCell>Quantity</Table.HeadCell>
                <Table.HeadCell>Sale</Table.HeadCell>
                <Table.HeadCell>Date</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>Actions</Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {floatItems.map((item) => (
                  <Table.Row key={item.id} className="bg-white">
                    <Table.Cell className="whitespace-nowrap font-medium text-gray-900">
                      <div className="flex flex-col">
                        <Link to={`/products/details/${item.product_id}`} className="text-blue-600 hover:underline">
                          {item.product?.name || 'Unknown Product'}
                        </Link>
                        <Link to={`/inventory/float/${item.id}`} className="text-sm text-gray-500 hover:underline">
                          View Float Details
                        </Link>
                      </div>
                    </Table.Cell>
                    <Table.Cell>{item.quantity}</Table.Cell>
                    <Table.Cell>
                      <Link to={`/sales/details/${item.sale_id}`} className="text-blue-600 hover:underline">
                        {item.sale?.invoice_number || 'Unknown Sale'}
                      </Link>
                    </Table.Cell>
                    <Table.Cell>{formatDateTime(item.created_at)}</Table.Cell>
                    <Table.Cell>
                      <Badge color={item.resolved ? 'success' : 'warning'}>
                        {item.resolved ? 'Resolved' : 'Unresolved'}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      {!item.resolved && (
                        <Button
                          size="xs"
                          color="success"
                          onClick={() => {
                            setSelectedItem(item);
                            setShowResolveModal(true);
                          }}
                        >
                          Resolve
                        </Button>
                      )}
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>

            {/* Pagination */}
            <div className="flex justify-center mt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(totalItems / itemsPerPage)}
                onPageChange={setCurrentPage}
                showIcons
              />
            </div>
          </>
        )}
      </Card>

      {/* Resolve Modal */}
      <Modal
        show={showResolveModal}
        onClose={() => setShowResolveModal(false)}
        size="md"
      >
        <Modal.Header>
          Resolve Float Inventory
        </Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>
              You are about to manually resolve float inventory for:
              <span className="font-bold block mt-1">{selectedItem?.product?.name}</span>
            </p>
            <p>
              Quantity: <span className="font-bold">{selectedItem?.quantity}</span>
            </p>
            <p className="text-sm text-gray-500">
              This will create an inventory adjustment to resolve the float. The product's stock quantity will be increased.
            </p>
            <div>
              <Label htmlFor="notes" value="Notes (optional)" />
              <TextInput
                id="notes"
                type="text"
                placeholder="Enter reason for manual resolution"
                value={resolveNotes}
                onChange={(e) => setResolveNotes(e.target.value)}
                className="mt-1"
              />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowResolveModal(false)}>
            Cancel
          </Button>
          <Button
            color="success"
            onClick={handleResolve}
            disabled={resolving}
          >
            {resolving ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Resolving...
              </>
            ) : (
              'Resolve Float'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Settings Modal */}
      <Modal
        show={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        size="md"
      >
        <Modal.Header>
          Inventory Settings
        </Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                id="allow_negative_inventory"
                type="checkbox"
                checked={inventorySettings.allow_negative_inventory}
                onChange={(e) => setInventorySettings({
                  ...inventorySettings,
                  allow_negative_inventory: e.target.checked
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="allow_negative_inventory" className="ml-2 text-sm font-medium text-gray-900">
                Allow Negative Inventory (Float)
              </label>
            </div>
            <p className="text-xs text-gray-500 ml-6">
              When enabled, sales can proceed even when there is insufficient stock. The system will track this as "float inventory".
            </p>

            <div className="flex items-center">
              <input
                id="warn_on_low_inventory"
                type="checkbox"
                checked={inventorySettings.warn_on_low_inventory}
                onChange={(e) => setInventorySettings({
                  ...inventorySettings,
                  warn_on_low_inventory: e.target.checked
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="warn_on_low_inventory" className="ml-2 text-sm font-medium text-gray-900">
                Show Low Inventory Warnings
              </label>
            </div>
            <p className="text-xs text-gray-500 ml-6">
              When enabled, warnings will be shown when selling products with insufficient stock.
            </p>

            <div className="flex items-center">
              <input
                id="auto_create_purchase_requests"
                type="checkbox"
                checked={inventorySettings.auto_create_purchase_requests}
                onChange={(e) => setInventorySettings({
                  ...inventorySettings,
                  auto_create_purchase_requests: e.target.checked
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="auto_create_purchase_requests" className="ml-2 text-sm font-medium text-gray-900">
                Auto-Create Purchase Requests
              </label>
            </div>
            <p className="text-xs text-gray-500 ml-6">
              When enabled, purchase requests will be automatically created when inventory falls below minimum levels.
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowSettingsModal(false)}>
            Cancel
          </Button>
          <Button
            color="blue"
            onClick={handleUpdateSettings}
          >
            Save Settings
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default FloatInventoryView;
