import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Card,
  Button,
  TextInput,
  Label,
  Select,
  Textarea,
  Alert,
  Spinner
} from 'flowbite-react';
import { HiOutlineArrowLeft, HiOutlineSave, HiOutlineExclamation } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { getProductByIdWithUoms } from '../../services/product';
import { createInventoryTransaction } from '../../services/inventoryTransaction';
import { formatQuantity } from '../../utils/formatters';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { supabase } from '../../lib/supabase';
import EnhancedNumberInput from '../../components/common/EnhancedNumberInput';

const AdjustInventory = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const formatWithCurrency = useCurrencyFormatter();

  // Product state
  const [product, setProduct] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Adjustment state
  const [quantity, setQuantity] = useState<string>('');
  const [adjustmentType, setAdjustmentType] = useState<'add' | 'subtract'>('add');
  const [reason, setReason] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [referenceNumber, setReferenceNumber] = useState<string>('');
  const [locationFrom, setLocationFrom] = useState<string>('');
  const [locationTo, setLocationTo] = useState<string>('');
  const [expirationDate, setExpirationDate] = useState<string>('');
  const [submitting, setSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  useEffect(() => {
    if (currentOrganization && id) {
      fetchProduct();
    }
  }, [currentOrganization, id]);

  const fetchProduct = async () => {
    if (!currentOrganization || !id) return;

    setLoading(true);
    setError(null);

    try {
      const { product: productData, error: productError } = await getProductByIdWithUoms(
        currentOrganization.id,
        id
      );

      if (productError) {
        setError(productError);
      } else if (productData) {
        setProduct(productData);
      } else {
        setError('Product not found');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching the product');
    } finally {
      setLoading(false);
    }
  };

  // Determine which additional fields to show based on the selected reason
  const getAdditionalFields = () => {
    // Fields to show for different reason categories
    const showReferenceNumber = [
      'Physical count adjustment',
      'Cycle count adjustment',
      'Customer return',
      'Supplier return correction',
      'System correction',
      'Initial stock setup'
    ].includes(reason);

    const showLocations = [
      'Transfer between locations',
      'Transfer to production',
      'Transfer from production'
    ].includes(reason);

    const showExpirationDate = [
      'Expired items',
      'Initial stock setup'
    ].includes(reason);

    return { showReferenceNumber, showLocations, showExpirationDate };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization || !product || !quantity || !reason || !user) {
      setSubmitError('Please fill in all required fields');
      return;
    }

    const parsedQuantity = parseFloat(quantity);
    if (isNaN(parsedQuantity) || parsedQuantity < 0.1) {
      setSubmitError('Please enter a valid quantity (minimum 0.1)');
      return;
    }

    // Validate additional fields based on reason
    const { showReferenceNumber, showLocations } = getAdditionalFields();

    if (showReferenceNumber && !referenceNumber) {
      setSubmitError('Please enter a reference number');
      return;
    }

    if (showLocations) {
      if (adjustmentType === 'subtract' && !locationTo) {
        setSubmitError('Please enter the destination location');
        return;
      }
      if (adjustmentType === 'add' && !locationFrom) {
        setSubmitError('Please enter the source location');
        return;
      }
    }

    setSubmitting(true);
    setSubmitError(null);

    try {
      // Get the default UoM for the product, with fallback logic
      let defaultUom = product.product_uoms?.find((pu: any) => pu.is_default);
      let uomId = defaultUom?.uom_id || null;

      // If no default UOM found, try to use any available UOM
      if (!uomId && product.product_uoms && product.product_uoms.length > 0) {
        defaultUom = product.product_uoms[0];
        uomId = defaultUom?.uom_id;
      }

      // If still no UOM, try to create a default 'pieces' UOM
      if (!uomId) {
        try {
          // Try to find 'pieces' UOM in the system
          const { data: piecesUom } = await supabase
            .from('units_of_measurement')
            .select('*')
            .eq('organization_id', currentOrganization.id)
            .eq('code', 'pcs')
            .single();

          if (piecesUom) {
            // Create a product UOM entry for pieces
            const { data: newProductUom, error: createError } = await supabase
              .from('product_uoms')
              .insert({
                product_id: product.id,
                uom_id: piecesUom.id,
                organization_id: currentOrganization.id,
                conversion_factor: 1,
                is_default: true,
                is_purchasing_unit: true,
                is_selling_unit: true
              })
              .select()
              .single();

            if (!createError && newProductUom) {
              uomId = piecesUom.id;
            }
          }
        } catch (err) {
          console.warn('Could not create default UOM:', err);
        }
      }

      if (!uomId) {
        throw new Error('No unit of measure available for this product. Please add a unit of measure first.');
      }

      // Calculate the actual quantity to adjust (positive for add, negative for subtract)
      const adjustmentQuantity = adjustmentType === 'add' ? parsedQuantity : -parsedQuantity;

      // Build the notes with additional information
      let adjustmentNotes = `${reason}`;

      if (referenceNumber) {
        adjustmentNotes += ` (Ref: ${referenceNumber})`;
      }

      if (locationFrom && adjustmentType === 'add') {
        adjustmentNotes += ` (From: ${locationFrom})`;
      }

      if (locationTo && adjustmentType === 'subtract') {
        adjustmentNotes += ` (To: ${locationTo})`;
      }

      if (expirationDate) {
        adjustmentNotes += ` (Exp: ${expirationDate})`;
      }

      if (notes) {
        adjustmentNotes += `: ${notes}`;
      }

      console.log('Creating inventory transaction with user ID:', user.id);

      // Create the inventory transaction
      const { transaction, error: transactionError } = await createInventoryTransaction(
        currentOrganization.id,
        user.id, // Use the current user's ID from the auth context
        {
          productId: product.id,
          transactionType: 'adjustment',
          quantity: adjustmentQuantity,
          uomId: uomId,
          notes: adjustmentNotes,
        }
      );

      if (transactionError) {
        throw new Error(transactionError);
      }

      setSubmitSuccess(true);

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/inventory');
      }, 2000);
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while adjusting inventory');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        </Card>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">Error</h3>
            <p>{error || 'Product not found'}</p>
            <div className="mt-4">
              <Link to="/inventory">
                <Button color="gray" size="sm">
                  <HiOutlineArrowLeft className="mr-2 h-4 w-4" />
                  Back to Inventory
                </Button>
              </Link>
            </div>
          </Alert>
        </Card>
      </div>
    );
  }

  // Get the default UoM with fallback
  let defaultUom = product.product_uoms?.find((pu: any) => pu.is_default);
  if (!defaultUom && product.product_uoms && product.product_uoms.length > 0) {
    defaultUom = product.product_uoms[0];
  }
  const uomCode = defaultUom?.uom?.code || 'pcs';

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Adjust Inventory</h1>
            <p className="text-gray-500">
              {product.name} {product.sku && `(SKU: ${product.sku})`}
            </p>
          </div>
          <Link to="/inventory">
            <Button color="light">
              <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
              Back to Inventory
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h2 className="text-lg font-semibold mb-2">Current Stock Information</h2>
            <div className="space-y-2">
              <div>
                <span className="text-gray-500">Current Stock:</span>
                <span className="ml-2 font-medium">{formatQuantity(product.stock_quantity || 0, uomCode)}</span>
              </div>
              <div>
                <span className="text-gray-500">Cost Price:</span>
                <span className="ml-2 font-medium">{formatWithCurrency(product.cost_price)}</span>
              </div>
              <div>
                <span className="text-gray-500">Total Value:</span>
                <span className="ml-2 font-medium">
                  {formatWithCurrency((product.stock_quantity || 0) * Number(product.cost_price || 0))}
                </span>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {submitSuccess ? (
              <Alert color="success">
                <h3 className="font-medium">Inventory Adjusted Successfully</h3>
                <p>Redirecting to inventory list...</p>
              </Alert>
            ) : (
              <>
                {submitError && (
                  <Alert color="failure">
                    <h3 className="font-medium">Error</h3>
                    <p>{submitError}</p>
                  </Alert>
                )}

                <div>
                  <Label htmlFor="adjustmentType" value="Adjustment Type" />
                  <Select
                    id="adjustmentType"
                    value={adjustmentType}
                    onChange={(e) => {
                      setAdjustmentType(e.target.value as 'add' | 'subtract');
                      // Reset location fields when adjustment type changes
                      setLocationFrom('');
                      setLocationTo('');
                    }}
                    required
                  >
                    <option value="add">Add to Inventory</option>
                    <option value="subtract">Remove from Inventory</option>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="quantity" value="Quantity" />
                  <div className="flex">
                    <EnhancedNumberInput
                      id="quantity"
                      min="0.1"
                      step="0.1"
                      value={quantity}
                      onChange={(e) => setQuantity(e.target.value)}
                      onBlur={(e) => {
                        // If field is empty on blur, reset to empty string
                        if (e.target.value === '' || parseFloat(e.target.value) < 0.1) {
                          setQuantity('');
                        }
                      }}
                      required
                      className="flex-1"
                      autoSelect={true}
                      preventScrollChange={true}
                      placeholder="0.1"
                    />
                    <div className="flex items-center bg-gray-100 px-3 rounded-r-lg">
                      {uomCode}
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="reason" value="Reason" />
                  <Select
                    id="reason"
                    value={reason}
                    onChange={(e) => {
                      setReason(e.target.value);
                      // Reset additional fields when reason changes
                      setReferenceNumber('');
                      setLocationFrom('');
                      setLocationTo('');
                      setExpirationDate('');
                    }}
                    required
                  >
                    <option value="">Select a reason</option>
                    <optgroup label="Count Adjustments">
                      <option value="Physical count adjustment">Physical count adjustment</option>
                      <option value="Cycle count adjustment">Cycle count adjustment</option>
                      <option value="System correction">System correction</option>
                    </optgroup>
                    <optgroup label="Write-offs">
                      <option value="Damaged goods">Damaged goods</option>
                      <option value="Expired items">Expired items</option>
                      <option value="Theft or loss">Theft or loss</option>
                      <option value="Quality control rejection">Quality control rejection</option>
                      <option value="Scrapped items">Scrapped items</option>
                    </optgroup>
                    <optgroup label="Internal Operations">
                      <option value="Internal use">Internal use</option>
                      <option value="Samples">Samples</option>
                      <option value="Promotional items">Promotional items</option>
                      <option value="Employee use">Employee use</option>
                    </optgroup>
                    <optgroup label="Returns">
                      <option value="Returned to inventory">Returned to inventory</option>
                      <option value="Customer return">Customer return</option>
                      <option value="Supplier return correction">Supplier return correction</option>
                    </optgroup>
                    <optgroup label="Transfers">
                      <option value="Transfer between locations">Transfer between locations</option>
                      <option value="Transfer to production">Transfer to production</option>
                      <option value="Transfer from production">Transfer from production</option>
                    </optgroup>
                    <optgroup label="Other">
                      <option value="Initial stock setup">Initial stock setup</option>
                      <option value="Donation">Donation</option>
                      <option value="Other">Other</option>
                    </optgroup>
                  </Select>
                </div>

                {/* Additional fields based on reason */}
                {getAdditionalFields().showReferenceNumber && (
                  <div>
                    <Label htmlFor="referenceNumber" value="Reference Number" />
                    <TextInput
                      id="referenceNumber"
                      type="text"
                      value={referenceNumber}
                      onChange={(e) => setReferenceNumber(e.target.value)}
                      placeholder="Enter reference number"
                      required
                    />
                  </div>
                )}

                {getAdditionalFields().showLocations && (
                  <>
                    {adjustmentType === 'add' && (
                      <div>
                        <Label htmlFor="locationFrom" value="Source Location" />
                        <TextInput
                          id="locationFrom"
                          type="text"
                          value={locationFrom}
                          onChange={(e) => setLocationFrom(e.target.value)}
                          placeholder="Enter source location"
                          required
                        />
                      </div>
                    )}

                    {adjustmentType === 'subtract' && (
                      <div>
                        <Label htmlFor="locationTo" value="Destination Location" />
                        <TextInput
                          id="locationTo"
                          type="text"
                          value={locationTo}
                          onChange={(e) => setLocationTo(e.target.value)}
                          placeholder="Enter destination location"
                          required
                        />
                      </div>
                    )}
                  </>
                )}

                {getAdditionalFields().showExpirationDate && (
                  <div>
                    <Label htmlFor="expirationDate" value="Expiration Date" />
                    <TextInput
                      id="expirationDate"
                      type="date"
                      value={expirationDate}
                      onChange={(e) => setExpirationDate(e.target.value)}
                    />
                  </div>
                )}

                <div>
                  <Label htmlFor="notes" value="Additional Notes" />
                  <Textarea
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={3}
                    placeholder="Enter any additional details about this adjustment"
                  />
                </div>

                <Button
                  type="submit"
                  color="primary"
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <HiOutlineSave className="mr-2 h-5 w-5" />
                      Save Adjustment
                    </>
                  )}
                </Button>
              </>
            )}
          </form>
        </div>
      </Card>
    </div>
  );
};

export default AdjustInventory;
