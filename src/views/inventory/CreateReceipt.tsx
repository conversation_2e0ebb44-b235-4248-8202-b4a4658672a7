import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Card,
  Button,
  TextInput,
  Textarea,
  Label,
  Alert,
  Spinner,
  Table,
  Select,
  Datepicker,
  Checkbox,
  Modal
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlineTrash,
  HiOutlineExclamation,
  HiOutlineChevronLeft,
  HiOutlineDocumentText,
  HiOutlineClipboardCheck,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { Product, getProducts } from '../../services/product';
import { UnitOfMeasurement, getUnitsOfMeasurement } from '../../services/uom';
import { getPurchaseOrderById } from '../../services/purchaseOrder';
import { createInventoryReceipt } from '../../services/inventoryReceipt';
import EnhancedProductSearchSelector from '../../components/products/EnhancedProductSearchSelector';
import UomSelector from '../../components/uom/UomSelector';
import SerialNumberInput from '../../components/inventory/SerialNumberInput';
import QcInspectionForm from '../../components/inventory/QcInspectionForm';
import EnhancedNumberInput from '../../components/common/EnhancedNumberInput';
import { formatDate } from '../../utils/formatters';

// Define the item type for the form
interface ReceiptItem {
  purchaseOrderItemId?: string;
  productId: string;
  product?: Product;
  quantity: number;
  uomId: string;
  unitCost: number;
  expectedQuantity?: number;
  qcStatus: string;
  damagedQuantity: number;
  damageReason?: string;
  lotNumber?: string;
  expiryDate?: string;
  serialNumbers?: string[];
  productUoms: any[]; // Array of product UOMs
  conversionFactor?: number; // Conversion factor for the selected UoM
  notReceived?: boolean; // Flag for items not received
}

const CreateInventoryReceipt = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();

  // Get purchase order ID from URL if available
  const purchaseOrderId = searchParams.get('purchaseOrderId');

  // State for the form
  const [receiptDate, setReceiptDate] = useState<Date>(new Date());
  const [notes, setNotes] = useState<string>('');
  const [items, setItems] = useState<ReceiptItem[]>([]);
  const [status, setStatus] = useState<string>('completed');
  const [purchaseOrder, setPurchaseOrder] = useState<any>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [uoms, setUoms] = useState<UnitOfMeasurement[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showQcModal, setShowQcModal] = useState<boolean>(false);
  const [showQcInspectionModal, setShowQcInspectionModal] = useState<boolean>(false);
  const [currentItemIndex, setCurrentItemIndex] = useState<number>(-1);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [progress, setProgress] = useState(0);
  const [expandedRowIndex, setExpandedRowIndex] = useState<number | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);

  // Toggle row expansion for additional details
  const toggleRowExpand = (pageIndex: number) => {
    // We only need to track the visual index since we're only showing current page items
    setExpandedRowIndex(expandedRowIndex === pageIndex ? null : pageIndex);
  };

  // Add this helper function to load UoM details
  const loadUomDetails = async (uomId: string, organizationId: string) => {
    if (!uomId || !organizationId) return null;

    try {
      // Use the API service instead of direct supabase access
      const { uoms, error } = await getUnitsOfMeasurement(
        organizationId,
        { id: uomId }
      );

      if (error || !uoms || uoms.length === 0) {
        return null;
      }

      return uoms[0];
    } catch (err) {
      return null;
    }
  };

  // Update the useEffect for initializing items from purchase order
  useEffect(() => {
    const fetchInitialData = async () => {
      if (!currentOrganization) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch products
        const { products: productList, error: productsError } = await getProducts(
          currentOrganization.id
        );

        if (productsError) {
          // Handle error
        } else {
          setProducts(productList);
        }

        // Fetch UoMs
        const { uoms: uomList, error: uomsError } = await getUnitsOfMeasurement(
          currentOrganization.id
        );

        if (uomsError) {
          // Handle error
        } else {
          setUoms(uomList);
        }

        // If we have a purchase order ID, fetch the purchase order
        if (purchaseOrderId) {
          const { purchaseOrder: po, error: poError } = await getPurchaseOrderById(
            currentOrganization.id,
            purchaseOrderId,
            { includeDetails: true } // Request detailed info including UoMs
          );

          if (poError) {
            setError(`Error loading purchase order: ${poError}`);
          } else if (po) {
            setPurchaseOrder(po);

            // Load each item with its UoM details
            const receiptItemPromises = po.items.map(async (item: any) => {
              // Processing PO item

              // Get UoM details if not already included
              let uomDetails = item.uom;
              if (!uomDetails && item.uom_id) {
                uomDetails = await loadUomDetails(item.uom_id, currentOrganization.id);
              }

              // Determine conversion factor
              let conversionFactor = 1;

              // First try from purchase_order_item.conversion_factor
              if (item.conversion_factor !== undefined && item.conversion_factor !== null) {
                conversionFactor = parseFloat(item.conversion_factor);
              }
              // Then try from product_uoms table
              else if (item.product && item.product.product_uoms) {
                const productUom = item.product.product_uoms.find((pu: any) =>
                  pu.uom_id === item.uom_id && pu.product_id === item.product_id);

                if (productUom && productUom.conversion_factor) {
                  conversionFactor = parseFloat(productUom.conversion_factor);
                }
              }

              if (isNaN(conversionFactor) || conversionFactor <= 0) {
                conversionFactor = 1;
              }

              // Create a productUom entry with the proper UoM info
              const productUoms = [];
              if (uomDetails) {
                productUoms.push({
                  id: `temp-${Date.now()}-${Math.random()}`,
                  product_id: item.product_id,
                  uom_id: item.uom_id,
                  uom: uomDetails,
                  conversion_factor: conversionFactor,
                  is_default: true,
                  is_purchasing_unit: true,
                  is_selling_unit: false,
                  organization_id: currentOrganization.id,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                });
              }

              return {
                purchaseOrderItemId: item.id,
                productId: item.product_id,
                product: item.product,
                quantity: item.quantity - (item.received_quantity || 0),
                uomId: item.uom_id,
                unitCost: item.unit_price,
                expectedQuantity: item.quantity - (item.received_quantity || 0),
                qcStatus: 'passed',
                damagedQuantity: 0,
                productUoms: productUoms,
                conversionFactor: conversionFactor,
                uom: uomDetails // Store the UoM details directly
              };
            });

            const receiptItems = await Promise.all(receiptItemPromises);

            // Filter out items that have already been fully received (quantity <= 0)
            const filteredItems = receiptItems.filter(item => item.quantity > 0);
            setItems(filteredItems);

            // Initialize pagination
            setTotalPages(Math.ceil(filteredItems.length / itemsPerPage));
          }
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while loading data');
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();
  }, [currentOrganization, purchaseOrderId, itemsPerPage]);

  // Get current items for the current page
  const getCurrentPageItems = () => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return items.slice(indexOfFirstItem, indexOfLastItem);
  };

  // Function to change page
  const handlePageChange = (pageNumber: number) => {
    // Collapse any expanded rows when changing pages
    setExpandedRowIndex(null);
    setCurrentPage(pageNumber);
  };

  // Function to change items per page
  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newItemsPerPage = parseInt(e.target.value);
    setItemsPerPage(newItemsPerPage);
    // Reset to first page when changing items per page
    setCurrentPage(1);
    setTotalPages(Math.ceil(items.length / newItemsPerPage));
  };

  const handleAddItem = () => {
    const newItems = [
      ...items,
      {
        productId: '',
        quantity: 1,
        uomId: '',
        unitCost: 0,
        qcStatus: 'passed',
        damagedQuantity: 0,
        productUoms: [],
        conversionFactor: 1
      }
    ];
    setItems(newItems);

    // Update pagination
    setTotalPages(Math.ceil(newItems.length / itemsPerPage));

    // If we're on the last page and it's full, go to the next page
    const currentItemsOnPage = items.length % itemsPerPage;
    if (currentItemsOnPage === 0 || currentPage === totalPages) {
      setCurrentPage(Math.ceil(newItems.length / itemsPerPage));
    }
  };

  const handleRemoveItem = (pageIndex: number) => {
    // Convert page-specific index to actual array index
    const actualIndex = (currentPage - 1) * itemsPerPage + pageIndex;

    const newItems = [...items];
    newItems.splice(actualIndex, 1);
    setItems(newItems);

    // Update pagination
    const newTotalPages = Math.max(1, Math.ceil(newItems.length / itemsPerPage));
    setTotalPages(newTotalPages);

    // If the current page is now empty (except for page 1), go to the previous page
    if (currentPage > 1 && currentPage > newTotalPages) {
      setCurrentPage(newTotalPages);
    }
  };

  const handleItemChange = (pageIndex: number, field: keyof ReceiptItem, value: any) => {
    // Convert page-specific index to actual array index
    const actualIndex = (currentPage - 1) * itemsPerPage + pageIndex;

    const newItems = [...items];
    newItems[actualIndex] = { ...newItems[actualIndex], [field]: value };
    setItems(newItems);
  };

  const handleProductChange = async (pageIndex: number, productId: string, product?: Product) => {
    // Convert page-specific index to actual array index
    const actualIndex = (currentPage - 1) * itemsPerPage + pageIndex;

    const newItems = [...items];
    newItems[actualIndex] = {
      ...newItems[actualIndex],
      productId,
      product,
      // Reset UoM when product changes
      uomId: '',
      productUoms: [], // We'll need to fetch these separately
      conversionFactor: 1 // Reset conversion factor
    };
    setItems(newItems);
  };

  const handleQcClick = (pageIndex: number) => {
    // Convert page-specific index to actual array index
    const actualIndex = (currentPage - 1) * itemsPerPage + pageIndex;
    setCurrentItemIndex(actualIndex);
    setShowQcModal(true);
  };

  const handleQcInspectionClick = (pageIndex: number) => {
    // Convert page-specific index to actual array index
    const actualIndex = (currentPage - 1) * itemsPerPage + pageIndex;
    setCurrentItemIndex(actualIndex);
    setShowQcInspectionModal(true);
  };

  const handleQcInspectionComplete = (inspectionData: any) => {
    const newItems = [...items];
    const item = newItems[currentItemIndex];

    // Update the QC status based on the inspection result
    item.qcStatus = inspectionData.status;

    // If there are any notes, add them to the damage reason field
    if (inspectionData.notes) {
      item.damageReason = inspectionData.notes;
    }

    setItems(newItems);
    setShowQcInspectionModal(false);
  };

  // Handle marking an item as not received
  const handleToggleReceived = (pageIndex: number) => {
    // Convert page-specific index to actual array index
    const actualIndex = (currentPage - 1) * itemsPerPage + pageIndex;

    const newItems = [...items];
    const item = newItems[actualIndex];

    // Toggle the notReceived status
    item.notReceived = !item.notReceived;

    // If marked as not received, set quantity to 0, otherwise reset to expected
    if (item.notReceived) {
      item.quantity = 0;
    } else if (item.expectedQuantity !== undefined) {
      item.quantity = item.expectedQuantity;
    } else {
      item.quantity = 1;
    }

    setItems(newItems);
  };

  const validateForm = () => {
    if (!currentOrganization || !user) {
      setError('Organization or user information is missing');
      return false;
    }

    // Validate form
    if (items.length === 0) {
      setError('Please add at least one item');
      return false;
    }

    // Check if at least one item has a quantity > 0
    let hasPositiveQuantity = false;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (!item.productId) {
        setError(`Item #${i + 1}: Please select a product`);
        return false;
      }
      if (!item.uomId) {
        setError(`Item #${i + 1}: Please select a unit of measurement`);
        return false;
      }

      // Allow zero quantities for items not received
      if (item.quantity < 0) {
        setError(`Item #${i + 1}: Quantity cannot be negative`);
        return false;
      }

      if (item.quantity > 0) {
        hasPositiveQuantity = true;
      }
    }

    // Ensure at least one item has a quantity > 0
    if (!hasPositiveQuantity) {
      setError('At least one item must have a quantity greater than 0');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // If status is completed, show confirmation dialog
    if (status === 'completed') {
      setShowConfirmation(true);
    } else {
      // For draft status, proceed without confirmation
      await submitReceipt();
    }
  };

  const submitReceipt = async () => {
    if (!currentOrganization) return;

    setIsSubmitting(true);
    setError(null);
    setProgress(0);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + 10;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 300);

      // Make sure each item's conversion factor is a valid number
      const formattedItems = items.map(item => {
        // Ensure conversion_factor is a valid number
        const conversionFactor = typeof item.conversionFactor === 'number' ?
          item.conversionFactor :
          parseFloat(String(item.conversionFactor));

        return {
          ...item,
          conversionFactor: !isNaN(conversionFactor) && conversionFactor > 0 ? conversionFactor : 1
        };
      });

      const { receipt, error: createError } = await createInventoryReceipt(
        currentOrganization.id,
        {
          purchaseOrderId: purchaseOrder?.id,
          receiptDate: receiptDate.toISOString(),
          notes,
          status,
          items: formattedItems.map(item => ({
            purchaseOrderItemId: item.purchaseOrderItemId,
            productId: item.productId,
            quantity: item.quantity,
            uomId: item.uomId,
            unitCost: item.unitCost,
            expectedQuantity: item.expectedQuantity,
            qcStatus: item.qcStatus,
            damagedQuantity: item.damagedQuantity,
            damageReason: item.damageReason,
            lotNumber: item.lotNumber,
            expiryDate: item.expiryDate ? new Date(item.expiryDate).toISOString() : undefined,
            serialNumbers: item.serialNumbers,
            conversionFactor: item.conversionFactor // Use the validated value
          }))
        }
      );

      clearInterval(progressInterval);
      setProgress(100);

      if (createError) {
        setError(createError);
      } else if (receipt) {
        // Wait a moment to show 100% progress
        setTimeout(() => {
          // Navigate to the receipt details
          navigate(`/inventory/receipts/${receipt.id}`);
        }, 500);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while creating the inventory receipt');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate(purchaseOrderId ? `/purchases/orders/${purchaseOrderId}` : '/inventory/receipts');
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-4">
      <Card className="shadow-sm">
        <div className="flex justify-between items-center mb-5 border-b pb-4">
          <div>
            <h1 className="text-xl font-bold flex items-center text-gray-800">
              <HiOutlineClipboardCheck className="mr-2 h-6 w-6 text-primary-600" />
              {purchaseOrder
                ? `Receive Items for PO: ${purchaseOrder.order_number}`
                : 'Create Inventory Receipt'}
            </h1>
            <p className="text-sm text-gray-500 mt-1">
              {purchaseOrder
                ? `From ${purchaseOrder.supplier_name}`
                : 'Record items received into inventory'}
            </p>
          </div>

          <Button color="gray" size="sm" onClick={handleCancel} className="px-4">
            <HiOutlineChevronLeft className="mr-1 h-4 w-4" />
            Cancel
          </Button>
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-12 gap-6 mb-6">
            <div className="col-span-12 md:col-span-3">
              <Label htmlFor="receiptDate" value="Receipt Date" className="font-medium mb-1.5 block" />
              <Datepicker
                id="receiptDate"
                value={receiptDate}
                onSelectedDateChanged={setReceiptDate}
                required
                className="w-full"
              />
            </div>

            <div className="col-span-12 md:col-span-3">
              <Label htmlFor="status" value="Status" className="font-medium mb-1.5 block" />
              <Select
                id="status"
                value={status}
                onChange={(e) => setStatus(e.target.value)}
                required
                className="w-full"
              >
                <option value="draft">Draft</option>
                <option value="completed">Completed</option>
              </Select>
            </div>

            <div className="col-span-12 md:col-span-6">
              <Label htmlFor="notes" value="Notes" className="font-medium mb-1.5 block" />
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={2}
                placeholder="Enter any notes about this receipt..."
                className="w-full"
              />
            </div>
          </div>

          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800 flex items-center">
                <HiOutlineDocumentText className="mr-2 h-5 w-5 text-primary-600" />
                Items to Receive
              </h2>
              {!purchaseOrder && items.length === 0 && (
                <Button color="primary" size="sm" onClick={handleAddItem} className="px-4 py-2 font-medium">
                  <HiOutlinePlus className="mr-2 h-4 w-4" />
                  Add First Item
                </Button>
              )}
            </div>

            {items.length === 0 ? (
              <div className="p-6 bg-gray-50 rounded-lg text-center border border-gray-200">
                <HiOutlineDocumentText className="mx-auto h-10 w-10 text-gray-400 mb-2" />
                <p className="text-gray-600">
                  {purchaseOrder
                    ? 'No items to receive from this purchase order'
                    : 'Click "Add First Item" to add products to this receipt'}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto border rounded-lg">
                {/* Table header */}
                <Table className="min-w-full table-fixed" hoverable={true}>
                  <Table.Head className="bg-gray-50">
                    <Table.HeadCell className="w-3/10 py-3">Product</Table.HeadCell>
                    <Table.HeadCell className="w-2/10 py-3">Received Qty</Table.HeadCell>
                    <Table.HeadCell className="w-2/10 py-3">UoM</Table.HeadCell>
                    <Table.HeadCell className="w-1.5/10 py-3">Unit Cost</Table.HeadCell>
                    <Table.HeadCell className="w-0.5/10 py-3 text-center">Not Received</Table.HeadCell>
                    <Table.HeadCell className="w-1/10 py-3">Actions</Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {getCurrentPageItems().map((item, index) => {
                      // Calculate receipt status for styling
                      const receiptStatus = item.notReceived
                        ? 'not-received'
                        : item.expectedQuantity !== undefined
                          ? item.quantity === 0
                            ? 'not-received'
                            : item.quantity < item.expectedQuantity
                            ? 'partial'
                            : item.quantity > item.expectedQuantity
                            ? 'over'
                            : 'full'
                          : 'default';

                      // Status-based styling
                      const rowStyles = {
                        'not-received': 'bg-gray-50 text-gray-500',
                        'partial': 'bg-yellow-50',
                        'over': 'bg-blue-50',
                        'full': 'bg-green-50',
                        'default': ''
                      };

                      // Status indicator for clarity
                      const statusIndicator = {
                        'not-received': '◯ Not Received',
                        'partial': '◑ Partial',
                        'over': '⦿ Over',
                        'full': '● Complete',
                        'default': ''
                      };

                      return [
                        <Table.Row
                          key={`item-${index}`}
                          className={rowStyles[receiptStatus]}
                        >
                          {/* Product Info */}
                          <Table.Cell className="whitespace-normal py-3 align-top">
                            <div className="flex flex-col">
                              <span className="font-medium text-gray-800">{item.product?.name || `Product #${index + 1}`}</span>
                              {item.product?.sku && (
                                <span className="text-xs text-gray-500 mt-0.5">SKU: {item.product.sku}</span>
                              )}
                              {item.expectedQuantity !== undefined && (
                                <span className="text-xs text-gray-600 mt-1 flex items-center">
                                  <span className="bg-gray-100 px-2 py-0.5 rounded">
                                    Expected: {item.expectedQuantity} {item.productUoms[0]?.uom?.name || ''}
                                  </span>
                                </span>
                              )}
                              {receiptStatus !== 'default' && (
                                <span className={`text-xs mt-1.5 ${
                                  receiptStatus === 'full' ? 'text-green-600' :
                                  receiptStatus === 'partial' ? 'text-yellow-600' :
                                  receiptStatus === 'over' ? 'text-blue-600' :
                                  'text-gray-500'
                                }`}>
                                  {statusIndicator[receiptStatus]}
                                </span>
                              )}
                              {!purchaseOrder && (
                                <div className="mt-2">
                                  <EnhancedProductSearchSelector
                                    products={products}
                                    selectedProductId={item.productId}
                                    onProductChange={(productId, product) =>
                                      handleProductChange(index, productId, product)
                                    }
                                    placeholder="Select product..."
                                  />
                                </div>
                              )}
                            </div>
                          </Table.Cell>

                          {/* Quantity */}
                          <Table.Cell className="align-middle">
                            <div className="flex flex-col gap-1">
                              <Label htmlFor={`quantity-${index}`} value="Quantity" className="text-xs text-gray-500 mb-1" />
                              <EnhancedNumberInput
                                value={item.quantity}
                                onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                                onBlur={(e) => {
                                  // If field is empty on blur, reset to 0
                                  if (e.target.value === '' || parseFloat(e.target.value) < 0) {
                                    handleItemChange(index, 'quantity', 0);
                                  }
                                }}
                                min="0"
                                step="0.01"
                                required
                                sizing="sm"
                                disabled={item.notReceived}
                                className={item.notReceived ? "bg-gray-100" : ""}
                                autoSelect={true}
                                preventScrollChange={true}
                              />
                            </div>
                          </Table.Cell>

                          {/* UoM */}
                          <Table.Cell className="align-middle">
                            <div className="flex flex-col gap-1">
                              <Label htmlFor={`uom-${index}`} value="Unit of Measure" className="text-xs text-gray-500 mb-1" />
                              <UomSelector
                                key={`uom-selector-${item.productId}-${index}`}
                                productId={item.productId}
                                value={item.uomId}
                                onChange={(uomId) => {
                                  handleItemChange(index, 'uomId', uomId);
                                }}
                                onUomChange={(uom) => {
                                  handleItemChange(index, 'uomId', uom.uom_id);
                                  handleItemChange(index, 'uom', uom.uom);
                                  const conversionFactor = parseFloat(uom.conversion_factor);
                                  if (!isNaN(conversionFactor) && conversionFactor > 0) {
                                    handleItemChange(index, 'conversionFactor', conversionFactor);
                                  }
                                }}
                                preloadedUoms={item.productUoms}
                                disabled={purchaseOrder !== null || item.notReceived}
                                className={`text-xs ${item.notReceived ? "bg-gray-100" : ""}`}
                              />
                            </div>
                          </Table.Cell>

                          {/* Unit Cost */}
                          <Table.Cell className="align-middle">
                            <div className="flex flex-col gap-1">
                              <Label htmlFor={`unitCost-${index}`} value="Unit Cost" className="text-xs text-gray-500 mb-1" />
                              <EnhancedNumberInput
                                value={item.unitCost}
                                onChange={(e) => handleItemChange(index, 'unitCost', parseFloat(e.target.value) || 0)}
                                onBlur={(e) => {
                                  // If field is empty on blur, reset to 0.01
                                  if (e.target.value === '' || parseFloat(e.target.value) < 0) {
                                    handleItemChange(index, 'unitCost', 0.01);
                                  }
                                }}
                                min="0"
                                step="0.01"
                                required
                                sizing="sm"
                                disabled={item.notReceived}
                                className={item.notReceived ? "bg-gray-100" : ""}
                                autoSelect={true}
                                preventScrollChange={true}
                              />
                            </div>
                          </Table.Cell>

                          {/* Not Received Toggle */}
                          <Table.Cell className="text-center align-middle">
                            <div className="flex flex-col items-center justify-center h-full">
                              <Label htmlFor={`not-received-${index}`} className="sr-only">Not Received</Label>
                              <div className="relative inline-flex items-center">
                                <Checkbox
                                  id={`not-received-${index}`}
                                  checked={item.notReceived || false}
                                  onChange={() => handleToggleReceived(index)}
                                  className="focus:ring-primary-500"
                                />
                                <span className="ml-2 text-xs text-gray-500 whitespace-nowrap">Not received</span>
                              </div>
                            </div>
                          </Table.Cell>

                          {/* Actions */}
                          <Table.Cell className="align-middle">
                            <div className="flex gap-1.5 justify-center">
                              <Button
                                size="xs"
                                color={expandedRowIndex === index ? "light" : "gray"}
                                onClick={() => toggleRowExpand(index)}
                                title={expandedRowIndex === index ? "Hide details" : "Show details"}
                                className="px-2 py-1"
                              >
                                <HiOutlineInformationCircle className="h-4 w-4" />
                              </Button>
                              <Button
                                size="xs"
                                color="light"
                                onClick={() => handleQcClick(index)}
                                disabled={item.notReceived}
                                title="Quality Control"
                                className="px-2 py-1"
                              >
                                <span className="text-xs font-medium">QC</span>
                              </Button>
                              {!purchaseOrder && (
                                <Button
                                  size="xs"
                                  color="failure"
                                  onClick={() => handleRemoveItem(index)}
                                  title="Remove item"
                                  className="px-2 py-1"
                                >
                                  <HiOutlineTrash className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </Table.Cell>
                        </Table.Row>,

                        /* Expandable details row */
                        expandedRowIndex === index && (
                          <Table.Row className="bg-gray-50 border-t-0" key={`expanded-${index}`}>
                            <Table.Cell colSpan={6}>
                              <div className="p-4 text-sm border-t border-gray-200">
                                <div className="flex items-center mb-2 text-primary-600 border-b pb-2">
                                  <HiOutlineInformationCircle className="mr-2 h-4 w-4" />
                                  <span className="font-medium">Additional Details</span>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                  {/* Lot/Batch Number */}
                                  <div className="bg-white p-3 rounded shadow-sm">
                                    <Label htmlFor={`lotNumber-${index}`} value="Lot/Batch Number" className="text-xs font-medium text-gray-700 mb-1 block" />
                                    <TextInput
                                      id={`lotNumber-${index}`}
                                      type="text"
                                      value={item.lotNumber || ''}
                                      onChange={(e) => handleItemChange(index, 'lotNumber', e.target.value)}
                                      placeholder="Enter lot or batch number"
                                      sizing="sm"
                                      disabled={item.notReceived}
                                      className="w-full"
                                    />
                                  </div>

                                  {/* Expiry Date */}
                                  <div className="bg-white p-3 rounded shadow-sm">
                                    <Label htmlFor={`expiryDate-${index}`} value="Expiry Date" className="text-xs font-medium text-gray-700 mb-1 block" />
                                    <Datepicker
                                      id={`expiryDate-${index}`}
                                      value={item.expiryDate ? new Date(item.expiryDate) : undefined}
                                      onSelectedDateChanged={(date) =>
                                        handleItemChange(index, 'expiryDate', date.toISOString())
                                      }
                                      placeholder="Select expiry date"
                                      disabled={item.notReceived}
                                      className="w-full"
                                    />
                                  </div>

                                  {/* QC Quick Status */}
                                  <div className="bg-white p-3 rounded shadow-sm">
                                    <Label htmlFor={`qcStatus-${index}`} value="QC Status" className="text-xs font-medium text-gray-700 mb-1 block" />
                                    <Select
                                      id={`qcStatus-${index}`}
                                      value={item.qcStatus}
                                      onChange={(e) => handleItemChange(index, 'qcStatus', e.target.value)}
                                      sizing="sm"
                                      disabled={item.notReceived}
                                      className="w-full"
                                    >
                                      <option value="passed">Passed</option>
                                      <option value="failed">Failed</option>
                                      <option value="quarantine">Quarantine</option>
                                    </Select>
                                  </div>

                                  {/* Serial Numbers */}
                                  <div className="md:col-span-3 mt-1 bg-white p-3 rounded shadow-sm">
                                    <Label value="Serial Numbers" className="text-xs font-medium text-gray-700 mb-1 block" />
                                    <SerialNumberInput
                                      serialNumbers={item.serialNumbers || []}
                                      onChange={(serialNumbers) => handleItemChange(index, 'serialNumbers', serialNumbers)}
                                      quantity={item.quantity}
                                      disabled={item.notReceived}
                                    />
                                  </div>
                                </div>
                              </div>
                            </Table.Cell>
                          </Table.Row>
                        )
                      ];
                    }).flat()}
                  </Table.Body>
                </Table>

                {/* Pagination Controls */}
                <div className="flex flex-wrap items-center justify-between border-t border-gray-200 bg-gray-50 px-4 py-3">
                  <div className="flex items-center space-x-2 text-sm text-gray-700">
                    <span>Show</span>
                    <select
                      value={itemsPerPage}
                      onChange={handleItemsPerPageChange}
                      className="rounded-md border-gray-300 py-1 text-sm focus:border-primary-500 focus:ring-primary-500"
                    >
                      <option value="5">5</option>
                      <option value="10">10</option>
                      <option value="25">25</option>
                      <option value="50">50</option>
                      <option value="100">100</option>
                    </select>
                    <span>items per page</span>
                  </div>

                  <div className="flex items-center">
                    <span className="text-sm text-gray-700 mr-3">
                      Showing {items.length > 0 ? ((currentPage - 1) * itemsPerPage) + 1 : 0} to {Math.min(currentPage * itemsPerPage, items.length)} of {items.length} items
                    </span>

                    <div className="flex space-x-1">
                      <Button
                        color="light"
                        size="xs"
                        onClick={() => handlePageChange(1)}
                        disabled={currentPage === 1}
                        className="px-2"
                      >
                        First
                      </Button>
                      <Button
                        color="light"
                        size="xs"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="px-2"
                      >
                        Prev
                      </Button>

                      <div className="flex items-center space-x-1">
                        {/* Show page numbers */}
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          // Show 2 pages before and after current page, or the first 5 pages
                          let pageToShow;
                          if (totalPages <= 5) {
                            // If 5 or fewer pages, show all pages
                            pageToShow = i + 1;
                          } else if (currentPage <= 3) {
                            // If at the beginning, show first 5 pages
                            pageToShow = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            // If at the end, show last 5 pages
                            pageToShow = totalPages - 4 + i;
                          } else {
                            // Otherwise show current page and 2 pages before and after
                            pageToShow = currentPage - 2 + i;
                          }

                          return (
                            <Button
                              key={pageToShow}
                              color={currentPage === pageToShow ? "primary" : "light"}
                              size="xs"
                              onClick={() => handlePageChange(pageToShow)}
                              className="px-3"
                            >
                              {pageToShow}
                            </Button>
                          );
                        })}
                      </div>

                      <Button
                        color="light"
                        size="xs"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="px-2"
                      >
                        Next
                      </Button>
                      <Button
                        color="light"
                        size="xs"
                        onClick={() => handlePageChange(totalPages)}
                        disabled={currentPage === totalPages}
                        className="px-2"
                      >
                        Last
                      </Button>
                    </div>
                  </div>
                </div>

                {!purchaseOrder && (
                  <div className="flex justify-center mt-4 mb-1 py-2">
                    <Button
                      color="primary"
                      size="sm"
                      onClick={handleAddItem}
                      className="border px-4 py-2 font-medium"
                      outline={true}
                    >
                      <HiOutlinePlus className="mr-2 h-4 w-4" />
                      Add Another Item
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Legend */}
          {items.length > 0 && (
            <div className="mb-6 mt-2 bg-gray-50 p-3 rounded-lg border border-gray-200">
              <p className="text-sm font-medium text-gray-700 mb-2">Receipt Status Legend:</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                  <span>Complete</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                  <span>Partial</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                  <span>Over Received</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-gray-500 mr-2"></div>
                  <span>Not Received</span>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end gap-3 mt-6 pt-4 border-t">
            <Button color="gray" size="md" onClick={handleCancel} className="px-5 py-2.5 font-medium">
              Cancel
            </Button>
            <Button
              type="submit"
              color="primary"
              size="md"
              disabled={isSubmitting || items.length === 0}
              className="px-5 py-2.5 font-medium"
            >
              {isSubmitting ? <Spinner size="sm" className="mr-2" /> : null}
              {status === 'draft' ? 'Save as Draft' : 'Complete Receipt'}
            </Button>
          </div>
        </form>
      </Card>

      {/* QC Modal */}
      <Modal show={showQcModal} onClose={() => setShowQcModal(false)} size="lg">
        <Modal.Header className="border-b bg-gray-50">
          <div className="flex items-center">
            <span className="text-primary-600 mr-2">
              <HiOutlineClipboardCheck className="h-5 w-5" />
            </span>
            Quality Control Details
          </div>
        </Modal.Header>
        <Modal.Body className="p-4">
          {currentItemIndex >= 0 && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="qcStatus" value="QC Status" className="font-medium mb-1.5 block" />
                <Select
                  id="qcStatus"
                  value={items[currentItemIndex].qcStatus}
                  onChange={(e) => handleItemChange(currentItemIndex, 'qcStatus', e.target.value)}
                  className="w-full"
                >
                  <option value="passed">Passed</option>
                  <option value="failed">Failed</option>
                  <option value="quarantine">Quarantine</option>
                </Select>
              </div>

              <div>
                <Label htmlFor="damagedQuantity" value="Damaged Quantity" className="font-medium mb-1.5 block" />
                <EnhancedNumberInput
                  value={items[currentItemIndex].damagedQuantity}
                  onChange={(e) => handleItemChange(currentItemIndex, 'damagedQuantity', parseFloat(e.target.value) || 0)}
                  onBlur={(e) => {
                    // If field is empty on blur, reset to 0
                    if (e.target.value === '' || parseFloat(e.target.value) < 0) {
                      handleItemChange(currentItemIndex, 'damagedQuantity', 0);
                    }
                  }}
                  min="0"
                  step="0.01"
                  className="w-full"
                  autoSelect={true}
                  preventScrollChange={true}
                />
                <p className="text-xs text-gray-500 mt-1">Enter the quantity of damaged or rejected items</p>
              </div>

              <div>
                <Label htmlFor="damageReason" value="Damage Reason / Notes" className="font-medium mb-1.5 block" />
                <Textarea
                  id="damageReason"
                  value={items[currentItemIndex].damageReason || ''}
                  onChange={(e) => handleItemChange(currentItemIndex, 'damageReason', e.target.value)}
                  rows={3}
                  placeholder="Describe the damage or quality issue..."
                  className="w-full"
                />
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="border-t bg-gray-50">
          <div className="flex justify-end w-full">
            <Button
              color="primary"
              onClick={() => setShowQcModal(false)}
              className="px-4 py-2 font-medium"
            >
              Save & Close
            </Button>
          </div>
        </Modal.Footer>
      </Modal>

      {/* QC Inspection Modal */}
      <Modal show={showQcInspectionModal} onClose={() => setShowQcInspectionModal(false)} size="4xl">
        <Modal.Header>Quality Control Inspection</Modal.Header>
        <Modal.Body>
          {currentItemIndex >= 0 && items[currentItemIndex] && (
            <QcInspectionForm
              productId={items[currentItemIndex].productId}
              onComplete={handleQcInspectionComplete}
              onCancel={() => setShowQcInspectionModal(false)}
            />
          )}
        </Modal.Body>
      </Modal>

      {/* Confirmation Modal */}
      <Modal show={showConfirmation} onClose={() => setShowConfirmation(false)} size="lg">
        <Modal.Header className="border-b bg-gray-50">
          <div className="flex items-center">
            <span className="text-primary-600 mr-2">
              <HiOutlineClipboardCheck className="h-5 w-5" />
            </span>
            Complete Inventory Receipt
          </div>
        </Modal.Header>
        <Modal.Body className="p-5">
          <div className="space-y-5">
            <div className="flex items-start">
              <div className="flex-shrink-0 bg-blue-100 p-2 rounded-full text-blue-600 mr-3">
                <HiOutlineInformationCircle className="h-6 w-6" />
              </div>
              <p className="text-gray-700">
                You are about to complete this inventory receipt with {items.length} item{items.length !== 1 ? 's' : ''}.
                This will update your inventory levels and cannot be easily undone.
              </p>
            </div>

            {/* Receipt Summary */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h4 className="font-medium text-sm mb-3 text-gray-700">Receipt Summary:</h4>

              {(() => {
                // Calculate summary statistics
                const fullReceived = items.filter(item =>
                  item.expectedQuantity && item.quantity === item.expectedQuantity
                ).length;

                const partialReceived = items.filter(item =>
                  item.expectedQuantity && item.quantity > 0 && item.quantity < item.expectedQuantity
                ).length;

                const notReceived = items.filter(item =>
                  item.quantity === 0
                ).length;

                const overReceived = items.filter(item =>
                  item.expectedQuantity && item.quantity > item.expectedQuantity
                ).length;

                return (
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    {fullReceived > 0 && (
                      <div className="flex items-center bg-white p-2 rounded shadow-sm">
                        <div className="w-4 h-4 rounded-full bg-green-500 mr-2 flex-shrink-0"></div>
                        <span>{fullReceived} item{fullReceived !== 1 ? 's' : ''} fully received</span>
                      </div>
                    )}

                    {partialReceived > 0 && (
                      <div className="flex items-center bg-white p-2 rounded shadow-sm">
                        <div className="w-4 h-4 rounded-full bg-yellow-500 mr-2 flex-shrink-0"></div>
                        <span>{partialReceived} item{partialReceived !== 1 ? 's' : ''} partially received</span>
                      </div>
                    )}

                    {notReceived > 0 && (
                      <div className="flex items-center bg-white p-2 rounded shadow-sm">
                        <div className="w-4 h-4 rounded-full bg-gray-500 mr-2 flex-shrink-0"></div>
                        <span>{notReceived} item{notReceived !== 1 ? 's' : ''} not received</span>
                      </div>
                    )}

                    {overReceived > 0 && (
                      <div className="flex items-center bg-white p-2 rounded shadow-sm">
                        <div className="w-4 h-4 rounded-full bg-blue-500 mr-2 flex-shrink-0"></div>
                        <span>{overReceived} item{overReceived !== 1 ? 's' : ''} over received</span>
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>

            <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200 flex items-center text-yellow-700">
              <HiOutlineExclamation className="h-5 w-5 mr-2 flex-shrink-0" />
              <p className="text-sm">
                Are you sure you want to proceed?
              </p>
            </div>

            {isSubmitting && (
              <div className="mt-5">
                <div className="mb-2 text-sm font-medium flex items-center justify-between">
                  <span>Processing inventory receipt...</span>
                  <span className="text-primary-600">{progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-primary-600 h-2.5 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer className="border-t bg-gray-50">
          <div className="flex justify-end gap-3 w-full">
            <Button
              color="gray"
              size="md"
              onClick={() => setShowConfirmation(false)}
              disabled={isSubmitting}
              className="px-4 py-2 font-medium"
            >
              Cancel
            </Button>
            <Button
              color="primary"
              size="md"
              onClick={submitReceipt}
              disabled={isSubmitting}
              className="px-4 py-2 font-medium"
            >
              {isSubmitting ? <Spinner size="sm" className="mr-2" /> : null}
              Complete Receipt
            </Button>
          </div>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default CreateInventoryReceipt;
