import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Dropdown,
  Spinner,
  Table,
  TextInput,
  Tooltip
} from "flowbite-react";
import { Link } from "react-router-dom";
import {

  HiArrowDown,
  Hi<PERSON>rrowUp,
  HiChartBar,
  HiDownload,
  HiExclamation,
  HiOutlineClipboardList,
  HiOutlineExclamation,
  HiOutlinePlus,
  HiSearch,
  HiFilter,
  HiOutlineRefresh,
  HiOutlineDownload
} from "react-icons/hi";
import { getProducts, Product } from "../../services/product";
import { useOrganization } from "../../context/OrganizationContext";

import { useCurrencyFormatter } from "../../utils/currencyFormatter";
import { useOrganizationSettings } from "../../context/OrganizationSettingsContext";
import Pagination from "../../components/common/Pagination";
import { formatQuantityWithSeparators } from "../../utils/formatters";
import { exportInventory } from "../../utils/excelExport";

const InventoryList = () => {
  // State variables
  const [loading, setLoading] = useState<boolean>(true);
  const [products, setProducts] = useState<Product[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [sortBy, setSortBy] = useState<string>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [filterStatus, setFilterStatus] = useState<string>("all");

  // Get currency formatter
  const formatWithCurrency = useCurrencyFormatter();

  // Get organization context - use destructuring to get all needed values at once
  const { currentOrganization: organization, organizations, setCurrentOrganization, loading: orgLoading, error: orgError } = useOrganization();

  // Check for organization context and handle organization selection
  useEffect(() => {
    console.log("Organization context value:", organization ?
      `ID: ${organization.id}, Name: ${organization.name}` : "Not available");

    console.log("Organization context state:", {
      availableOrgs: organizations?.length || 0,
      loading: orgLoading,
      error: orgError ? orgError.message : "No error"
    });

    // Case 1: We have an organization, use it to fetch products
    if (organization) {
      console.log("Using current organization:", organization.name);
      fetchProducts();
      return;
    }

    // Case 2: No organization selected, but we have organizations available
    // Automatically select the first one
    if (!organization && organizations?.length > 0 && !orgLoading) {
      console.log("Organizations available but none selected. Setting first one:", organizations[0].name);
      // Set the current organization through context
      setCurrentOrganization(organizations[0]);
      // Also store in localStorage for persistence
      localStorage.setItem('selectedOrganizationId', organizations[0].id);
      // Don't fetch products here, the effect will run again when organization is set
      return;
    }

    // Case 3: No organization available and not loading - show fallback UI
    if (!organization && !orgLoading && organizations?.length === 0) {
      console.log("No organizations available and not loading - showing fallback UI");
      setLoading(false);
    }
  }, [organization, organizations, orgLoading, orgError, setCurrentOrganization]);

  // Fetch products from the API
  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);

      // Safety check for organization existence
      if (!organization || !organization.id) {
        console.warn("Organization ID not available when fetchProducts was called");

        // If organizations are available but not selected, try to use the first one
        if (organizations?.length > 0) {
          console.log("Using first organization as fallback:", organizations[0].name);

          const { products: fetchedProducts, error: fetchError } = await getProducts(
            organizations[0].id,
            {
              sortBy,
              sortOrder,
              searchQuery: searchQuery.length > 0 ? searchQuery : undefined,
            }
          );

          if (fetchError) {
            throw new Error(fetchError);
          }

          setProducts(fetchedProducts || []);
          return;
        } else {
          throw new Error("Organization ID is not available");
        }
      }

      console.log("Fetching products for organization:", organization.id);

      const { products: fetchedProducts, error: fetchError } = await getProducts(
        organization.id,
        {
          sortBy,
          sortOrder,
          searchQuery: searchQuery.length > 0 ? searchQuery : undefined,
        }
      );

      console.log("API response:", fetchedProducts ? `Found ${fetchedProducts.length} products` : "No products returned", fetchError || "No error");

      if (fetchError) {
        throw new Error(fetchError);
      }

      // Set empty array if no products returned to prevent undefined issues
      setProducts(fetchedProducts || []);
    } catch (err) {
      console.error("Error fetching products:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch products");
      // Add fallback mock data for development - remove in production
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  // Filtered products based on search and filter criteria
  const filteredProducts = products.filter((product) => {
    // Filter by status
    if (filterStatus === "in_stock" && (product.stock_quantity || 0) <= 0) return false;
    if (filterStatus === "low_stock" && ((product.stock_quantity || 0) > (product.min_stock_level || 0) || (product.stock_quantity || 0) <= 0)) return false;
    if (filterStatus === "out_of_stock" && (product.stock_quantity || 0) > 0) return false;

    // Filter by search query
    if (!searchQuery) return true;

    return (
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (product.sku && product.sku.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (product.barcode && product.barcode.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  });

  // Sort products based on sort criteria
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case "name":
        comparison = a.name.localeCompare(b.name);
        break;
      case "stock_quantity":
        comparison = (a.stock_quantity || 0) - (b.stock_quantity || 0);
        break;
      case "unit_price":
        comparison = (Number(a.unit_price) || 0) - (Number(b.unit_price) || 0);
        break;
      case "cost_price":
        comparison = (Number(a.cost_price) || 0) - (Number(b.cost_price) || 0);
        break;
      default:
        comparison = a.name.localeCompare(b.name);
    }

    return sortOrder === "desc" ? -comparison : comparison;
  });

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedProducts.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(sortedProducts.length / itemsPerPage);

  // Handle sort
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  // Get stock status badge
  const getStockStatusBadge = (product: Product) => {
    if (!product.stock_quantity || product.stock_quantity <= 0) {
      return <Badge color="failure">Out of Stock</Badge>;
    } else if (product.min_stock_level && product.stock_quantity <= product.min_stock_level) {
      return <Badge color="warning">Low Stock</Badge>;
    } else {
      return <Badge color="success">In Stock</Badge>;
    }
  };

  // Calculate inventory metrics
  const totalProducts = products.length;
  const lowStockCount = products.filter(p => (p.stock_quantity || 0) > 0 && p.min_stock_level && (p.stock_quantity || 0) <= p.min_stock_level).length;
  const outOfStockCount = products.filter(p => !p.stock_quantity || p.stock_quantity <= 0).length;
  const totalInventoryValue = products.reduce((sum, product) =>
    sum + (product.stock_quantity || 0) * Number(product.cost_price || 0), 0
  );

  // Handle refresh
  const handleRefresh = () => {
    fetchProducts();
  };

  // Export inventory to Excel
  const handleExportInventory = () => {
    if (products.length === 0) {
      setError('No inventory data to export');
      return;
    }
    exportInventory(products);
  };



  // Add a timeout to ensure loading doesn't get stuck
  useEffect(() => {
    if (loading) {
      // Force loading to end after 5 seconds to prevent infinite spinner
      const timeout = setTimeout(() => {
        if (loading) {
          console.log("Loading timeout reached, forcing loading to end");
          setLoading(false);

          if (!error && products.length === 0) {
            setError("Unable to fetch inventory data. Please try again later.");
          }
        }
      }, 3000); // Reduced to 3 seconds for better UX

      return () => clearTimeout(timeout);
    }
  }, [loading, products.length, error]);

  // Display loading state
  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <Spinner size="xl" />
        <p className="mt-4 text-gray-600">Loading inventory data...</p>
        <Button
          color="light"
          size="sm"
          className="mt-4"
          onClick={() => {
            setLoading(false);
            setProducts([]);
            setError("Loading canceled. Click refresh to try again.");
          }}
        >
          Cancel Loading
        </Button>
      </div>
    );
  }

  // Only show organization not available message if we've checked all options
  if (!organization && !loading && !orgLoading && organizations?.length === 0) {
    return (
      <Card className="mb-4">
        <div className="p-6 text-center">
          <HiExclamation className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h5 className="text-xl font-bold mb-2">No Organizations Available</h5>
          <p className="text-gray-500 mb-4">
            Please create an organization before managing inventory.
          </p>
          <Link to="/organization/create">
            <Button
              color="primary"
            >
              Create Organization
            </Button>
          </Link>
        </div>
      </Card>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Inventory Management</h1>
          <p className="text-gray-500 mt-1">
            View and manage your inventory levels. Track stock quantities and monitor low stock items.
          </p>
        </div>
        <div className="flex gap-2">
          <Button color="light" onClick={handleExportInventory}>
            <HiOutlineDownload className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Link to="/inventory/receipts/create">
            <Button color="primary" className="bg-primary hover:bg-primary-700">
              <HiOutlinePlus className="mr-2 h-5 w-5" />
              Receive Inventory
            </Button>
          </Link>
          <Link to="/inventory/transactions">
            <Button color="light">
              <HiOutlineClipboardList className="mr-2 h-5 w-5" />
              Transactions
            </Button>
          </Link>
          <Link to="/inventory/float">
            <Button color="warning" className="bg-yellow-500 hover:bg-yellow-600 text-white">
              <HiOutlineExclamation className="mr-2 h-5 w-5" />
              Float Inventory
            </Button>
          </Link>
        </div>
      </div>

      {/* Inventory Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <Card className="border-l-4 border-blue-500">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="text-sm text-gray-500">Total Products</h5>
              <p className="text-2xl font-bold">{totalProducts}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <HiChartBar className="h-6 w-6 text-blue-500" />
            </div>
          </div>
        </Card>

        <Card className="border-l-4 border-yellow-500">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="text-sm text-gray-500">Low Stock Items</h5>
              <p className="text-2xl font-bold">{lowStockCount}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <HiExclamation className="h-6 w-6 text-yellow-500" />
            </div>
          </div>
        </Card>

        <Card className="border-l-4 border-red-500">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="text-sm text-gray-500">Out of Stock</h5>
              <p className="text-2xl font-bold">{outOfStockCount}</p>
            </div>
            <div className="p-3 bg-red-100 rounded-full">
              <HiExclamation className="h-6 w-6 text-red-500" />
            </div>
          </div>
        </Card>

        <Card className="border-l-4 border-green-500">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="text-sm text-gray-500">Total Value</h5>
              <p className="text-2xl font-bold">
                {formatWithCurrency(totalInventoryValue)}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <HiOutlineRefresh className="h-6 w-6 text-green-500" />
            </div>
          </div>
        </Card>

        <Card className="border-l-4 border-orange-500">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="text-sm text-gray-500">Float Inventory</h5>
              <p className="text-2xl font-bold">
                {products.filter(p => (p.stock_quantity || 0) < 0).length}
              </p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <HiExclamation className="h-6 w-6 text-orange-500" />
            </div>
          </div>
        </Card>
      </div>

      {/* Inventory List */}
      <Card className="mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
          <div className="flex items-center">
            <h5 className="text-xl font-bold">Inventory List</h5>
            <Tooltip content="Refresh inventory data">
              <Button
                color="light"
                size="xs"
                pill
                className="ml-2"
                onClick={handleRefresh}
              >
                <HiOutlineRefresh />
              </Button>
            </Tooltip>
          </div>

          <div className="flex flex-col md:flex-row gap-2">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <HiSearch className="text-gray-500" />
              </div>
              <TextInput
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Dropdown
              label={
                <div className="flex items-center">
                  <HiFilter className="mr-2" />
                  <span>
                    {filterStatus === "all"
                      ? "All Items"
                      : filterStatus === "in_stock"
                        ? "In Stock"
                        : filterStatus === "low_stock"
                          ? "Low Stock"
                          : "Out of Stock"
                    }
                  </span>
                </div>
              }
              color="light"
            >
              <Dropdown.Item onClick={() => setFilterStatus("all")}>All Items</Dropdown.Item>
              <Dropdown.Item onClick={() => setFilterStatus("in_stock")}>In Stock</Dropdown.Item>
              <Dropdown.Item onClick={() => setFilterStatus("low_stock")}>Low Stock</Dropdown.Item>
              <Dropdown.Item onClick={() => setFilterStatus("out_of_stock")}>Out of Stock</Dropdown.Item>
            </Dropdown>

            <Button color="light">
              <HiDownload className="mr-2 h-5 w-5" />
              Export
            </Button>
          </div>
        </div>



        <div className="overflow-x-auto">
          <Table hoverable>
            <Table.Head>
              <Table.HeadCell
                className="cursor-pointer"
                onClick={() => handleSort("name")}
              >
                <div className="flex items-center">
                  Product Name
                  {sortBy === "name" && (
                    sortOrder === "asc" ?
                      <HiArrowUp className="ml-1" /> :
                      <HiArrowDown className="ml-1" />
                  )}
                </div>
              </Table.HeadCell>
              <Table.HeadCell>SKU / Barcode</Table.HeadCell>
              <Table.HeadCell
                className="cursor-pointer"
                onClick={() => handleSort("stock_quantity")}
              >
                <div className="flex items-center">
                  Quantity
                  {sortBy === "stock_quantity" && (
                    sortOrder === "asc" ?
                      <HiArrowUp className="ml-1" /> :
                      <HiArrowDown className="ml-1" />
                  )}
                </div>
              </Table.HeadCell>
              <Table.HeadCell
                className="cursor-pointer"
                onClick={() => handleSort("min_stock_level")}
              >
                <div className="flex items-center">
                  Min Stock
                  {sortBy === "min_stock_level" && (
                    sortOrder === "asc" ?
                      <HiArrowUp className="ml-1" /> :
                      <HiArrowDown className="ml-1" />
                  )}
                </div>
              </Table.HeadCell>
              <Table.HeadCell>Status</Table.HeadCell>
              <Table.HeadCell
                className="cursor-pointer"
                onClick={() => handleSort("unit_price")}
              >
                <div className="flex items-center">
                  Unit Price
                  {sortBy === "unit_price" && (
                    sortOrder === "asc" ?
                      <HiArrowUp className="ml-1" /> :
                      <HiArrowDown className="ml-1" />
                  )}
                </div>
              </Table.HeadCell>
              <Table.HeadCell
                className="cursor-pointer"
                onClick={() => handleSort("cost_price")}
              >
                <div className="flex items-center">
                  Cost
                  {sortBy === "cost_price" && (
                    sortOrder === "asc" ?
                      <HiArrowUp className="ml-1" /> :
                      <HiArrowDown className="ml-1" />
                  )}
                </div>
              </Table.HeadCell>
              <Table.HeadCell>Value</Table.HeadCell>
              <Table.HeadCell>Actions</Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y">
              {currentItems.length > 0 ? (
                currentItems.map((product) => (
                  <Table.Row key={product.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                    <Table.Cell className="font-medium text-gray-900 dark:text-white">
                      <Link to={`/inventory/details/${product.id}`} className="text-blue-600 hover:underline">
                        {product.name}
                      </Link>
                      {product.category && (
                        <div className="text-xs text-gray-500 mt-1">
                          {product.category.name}
                        </div>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {product.sku && <div>SKU: {product.sku}</div>}
                      {product.barcode && <div>Barcode: {product.barcode}</div>}
                    </Table.Cell>
                    <Table.Cell>
                      <span className="font-semibold">{formatQuantityWithSeparators(product.stock_quantity || 0)}</span>
                    </Table.Cell>
                    <Table.Cell>
                      {formatQuantityWithSeparators(product.min_stock_level || 0)}
                    </Table.Cell>
                    <Table.Cell>{getStockStatusBadge(product)}</Table.Cell>
                    <Table.Cell>{formatWithCurrency(product.unit_price)}</Table.Cell>
                    <Table.Cell>{formatWithCurrency(product.cost_price)}</Table.Cell>
                    <Table.Cell className="font-medium">
                      {formatWithCurrency((product.stock_quantity || 0) * Number(product.cost_price || 0))}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex space-x-2">
                        <Link to={`/inventory/adjust/${product.id}`}>
                          <Button size="xs" color="light">
                            Adjust
                          </Button>
                        </Link>
                        <Link to={`/inventory/details/${product.id}`}>
                          <Button size="xs" color="light">
                            History
                          </Button>
                        </Link>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={8} className="text-center py-4">
                    {searchQuery || filterStatus !== "all"
                      ? "No products match your search criteria."
                      : "No products found in inventory."}
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          itemsPerPage={itemsPerPage}
          totalItems={sortedProducts.length}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1); // Reset to first page when changing items per page
          }}
          itemName="products"
        />
      </Card>

      {/* Low Stock Alert */}
      {lowStockCount > 0 && (
        <Card className="bg-yellow-50 border-yellow-200 mb-6">
          <div className="flex items-center">
            <HiExclamation className="h-6 w-6 text-yellow-500 mr-2" />
            <h2 className="text-lg font-semibold text-yellow-700">Low Stock Alert</h2>
          </div>
          <p className="text-yellow-600 mb-4">
            You have {lowStockCount} items that are running low on inventory.
          </p>
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="text-xs text-gray-700 uppercase bg-yellow-100">
                <tr>
                  <th className="px-4 py-2">Product Name</th>
                  <th className="px-4 py-2">Current Stock</th>
                  <th className="px-4 py-2">Min. Stock Level</th>
                  <th className="px-4 py-2">Action</th>
                </tr>
              </thead>
              <tbody>
                {products
                  .filter(p => (p.stock_quantity || 0) > 0 && p.min_stock_level && (p.stock_quantity || 0) <= p.min_stock_level)
                  .slice(0, 5)
                  .map((product) => (
                    <tr key={product.id} className="border-b border-yellow-200">
                      <td className="px-4 py-2 font-medium">
                        <Link to={`/inventory/details/${product.id}`} className="text-blue-600 hover:underline">
                          {product.name}
                        </Link>
                      </td>
                      <td className="px-4 py-2">{formatQuantityWithSeparators(product.stock_quantity)}</td>
                      <td className="px-4 py-2">{formatQuantityWithSeparators(product.min_stock_level)}</td>
                      <td className="px-4 py-2">
                        <Link to={`/inventory/receipts/create?product=${product.id}`}>
                          <Button size="xs" color="warning">Order More</Button>
                        </Link>
                      </td>
                    </tr>
                  ))
                }
              </tbody>
            </table>
          </div>
          {lowStockCount > 5 && (
            <div className="text-right mt-2">
              <Button color="warning" size="xs">
                View All {lowStockCount} Low Stock Items
              </Button>
            </div>
          )}
        </Card>
      )}

      {/* Error notification with retry button */}
      {error && (
        <div className="mt-4">
          <Card color="failure">
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium text-lg">Error Loading Inventory</p>
                <p className="mt-2">{error}</p>
                {products.length > 0 && (
                  <p className="mt-2 italic text-sm">Note: Showing sample or partial data.</p>
                )}
              </div>
              <Button
                color="failure"
                onClick={() => {
                  setError(null);
                  setLoading(true);
                  fetchProducts();
                }}
              >
                <HiOutlineRefresh className="mr-2 h-5 w-5" />
                Retry
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default InventoryList;
