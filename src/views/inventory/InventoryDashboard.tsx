import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Spinner,
  Badge,
  Dropdown,
  Table,
  TextInput,
  Pagination
} from 'flowbite-react';
import {
  HiOutlineArrowUp,
  HiOutlineArrowDown,
  HiOutlineSearch,
  HiOutlineExclamation,
  HiOutlineClock,
  HiOutlineRefresh,
  HiOutlineDocumentReport,
  HiOutlinePlus,
  HiOutlineClipboardList
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getProducts, Product } from '../../services/product';
import { formatQuantityWithSeparators } from '../../utils/formatters';

// Mock data for charts - In a real app, this would come from your backend
const inventoryValueData = [
  { month: 'Jan', value: 12500 },
  { month: 'Feb', value: 13200 },
  { month: 'Mar', value: 14800 },
  { month: 'Apr', value: 14100 },
  { month: 'May', value: 15600 },
  { month: 'Jun', value: 17200 },
];

const topMovingProducts = [
  { id: '1', name: 'Product A', quantity: 245, trend: 'up' },
  { id: '2', name: 'Product B', quantity: 187, trend: 'up' },
  { id: '3', name: 'Product C', quantity: 152, trend: 'down' },
  { id: '4', name: 'Product D', quantity: 124, trend: 'up' },
  { id: '5', name: 'Product E', quantity: 98, trend: 'down' },
];

const InventoryDashboard: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const [products, setProducts] = useState<Product[]>([]);
  const [lowStockItems, setLowStockItems] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // For inventory list
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    const fetchInventoryData = async () => {
      if (!currentOrganization) return;

      setLoading(true);
      setError(null);

      try {
        const { products: fetchedProducts, error: productsError } = await getProducts(
          currentOrganization.id,
          {
            sortBy,
            sortOrder,
            limit: 100, // Get more products than we need for the list to also identify low stock
          }
        );

        if (productsError) {
          setError(productsError);
        } else {
          setProducts(fetchedProducts);

          // Identify low stock items
          const lowItems = fetchedProducts.filter(
            product => product.stock_quantity <= product.min_stock_level
          );
          setLowStockItems(lowItems);
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching inventory data');
      } finally {
        setLoading(false);
      }
    };

    fetchInventoryData();
  }, [currentOrganization, sortBy, sortOrder]);

  // Filtered products based on search query
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (product.sku && product.sku.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (product.barcode && product.barcode.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredProducts.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  // Handle sort
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Stock status badges
  const getStockStatusBadge = (product: Product) => {
    if (product.stock_quantity <= 0) {
      return <Badge color="failure">Out of Stock</Badge>;
    } else if (product.stock_quantity <= product.min_stock_level) {
      return <Badge color="warning">Low Stock</Badge>;
    } else {
      return <Badge color="success">In Stock</Badge>;
    }
  };

  // Calculate total inventory value
  const totalInventoryValue = products.reduce(
    (sum, product) => sum + (product.stock_quantity * Number(product.cost_price || 0)),
    0
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Inventory Management</h1>
        <div className="flex gap-2">
          <Link to="/inventory/receipts/new">
            <Button color="primary">
              <HiOutlinePlus className="mr-2 h-5 w-5" />
              Receive Inventory
            </Button>
          </Link>
          <Link to="/inventory/transactions">
            <Button color="light">
              <HiOutlineClipboardList className="mr-2 h-5 w-5" />
              Transactions
            </Button>
          </Link>
        </div>
      </div>

      {/* Dashboard Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Total Products Card */}
        <Card className="border-l-4 border-blue-500">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="text-sm text-gray-500">Total Products</h5>
              <p className="text-2xl font-bold">{products.length}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <HiOutlineDocumentReport className="h-6 w-6 text-blue-500" />
            </div>
          </div>
        </Card>

        {/* Low Stock Items Card */}
        <Card className="border-l-4 border-yellow-500">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="text-sm text-gray-500">Low Stock Items</h5>
              <p className="text-2xl font-bold">{lowStockItems.length}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <HiOutlineExclamation className="h-6 w-6 text-yellow-500" />
            </div>
          </div>
        </Card>

        {/* Out of Stock Items Card */}
        <Card className="border-l-4 border-red-500">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="text-sm text-gray-500">Out of Stock</h5>
              <p className="text-2xl font-bold">
                {products.filter(p => p.stock_quantity <= 0).length}
              </p>
            </div>
            <div className="p-3 bg-red-100 rounded-full">
              <HiOutlineClock className="h-6 w-6 text-red-500" />
            </div>
          </div>
        </Card>

        {/* Inventory Value Card */}
        <Card className="border-l-4 border-green-500">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="text-sm text-gray-500">Total Value</h5>
              <p className="text-2xl font-bold">
                ${totalInventoryValue.toFixed(2)}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <HiOutlineRefresh className="h-6 w-6 text-green-500" />
            </div>
          </div>
        </Card>
      </div>

      {/* Low Stock Alert */}
      {lowStockItems.length > 0 && (
        <div className="mb-6">
          <Card className="bg-yellow-50 border-yellow-200">
            <div className="flex items-center">
              <HiOutlineExclamation className="h-6 w-6 text-yellow-500 mr-2" />
              <h2 className="text-lg font-semibold text-yellow-700">Low Stock Alert</h2>
            </div>
            <p className="text-yellow-600 mb-4">
              You have {lowStockItems.length} items that are running low on inventory.
            </p>
            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left">
                <thead className="text-xs text-gray-700 uppercase bg-yellow-100">
                  <tr>
                    <th className="px-4 py-2">Product Name</th>
                    <th className="px-4 py-2">Current Stock</th>
                    <th className="px-4 py-2">Min. Stock Level</th>
                    <th className="px-4 py-2">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {lowStockItems.slice(0, 5).map((item) => (
                    <tr key={item.id} className="border-b border-yellow-200">
                      <td className="px-4 py-2 font-medium">{item.name}</td>
                      <td className="px-4 py-2">{formatQuantityWithSeparators(item.stock_quantity)}</td>
                      <td className="px-4 py-2">{formatQuantityWithSeparators(item.min_stock_level)}</td>
                      <td className="px-4 py-2">
                        <Link to={`/inventory/receipts/new?product=${item.id}`}>
                          <Button size="xs" color="warning">Order More</Button>
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {lowStockItems.length > 5 && (
              <div className="text-right mt-2">
                <Button color="warning" size="xs">
                  View All {lowStockItems.length} Low Stock Items
                </Button>
              </div>
            )}
          </Card>
        </div>
      )}

      {/* Top Moving Products */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card>
          <h5 className="text-xl font-bold mb-4">Top Moving Products</h5>
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                <tr>
                  <th className="px-4 py-2">Product</th>
                  <th className="px-4 py-2">Units Sold</th>
                  <th className="px-4 py-2">Trend</th>
                </tr>
              </thead>
              <tbody>
                {topMovingProducts.map((product) => (
                  <tr key={product.id} className="border-b">
                    <td className="px-4 py-2 font-medium">{product.name}</td>
                    <td className="px-4 py-2">{product.quantity}</td>
                    <td className="px-4 py-2">
                      {product.trend === 'up' ? (
                        <div className="flex items-center text-green-500">
                          <HiOutlineArrowUp className="mr-1" />
                          <span>Up</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-red-500">
                          <HiOutlineArrowDown className="mr-1" />
                          <span>Down</span>
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>

        {/* Recent Inventory Transactions */}
        <Card>
          <div className="flex justify-between items-center mb-4">
            <h5 className="text-xl font-bold">Recent Transactions</h5>
            <Link to="/inventory/transactions">
              <Button size="xs" color="light">View All</Button>
            </Link>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                <tr>
                  <th className="px-4 py-2">Product</th>
                  <th className="px-4 py-2">Quantity</th>
                  <th className="px-4 py-2">Type</th>
                  <th className="px-4 py-2">Date</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="px-4 py-2 font-medium">Product A</td>
                  <td className="px-4 py-2">+50</td>
                  <td className="px-4 py-2"><Badge color="success">Received</Badge></td>
                  <td className="px-4 py-2">Today, 10:30 AM</td>
                </tr>
                <tr className="border-b">
                  <td className="px-4 py-2 font-medium">Product B</td>
                  <td className="px-4 py-2">-15</td>
                  <td className="px-4 py-2"><Badge color="failure">Sale</Badge></td>
                  <td className="px-4 py-2">Today, 9:45 AM</td>
                </tr>
                <tr className="border-b">
                  <td className="px-4 py-2 font-medium">Product C</td>
                  <td className="px-4 py-2">-8</td>
                  <td className="px-4 py-2"><Badge color="failure">Sale</Badge></td>
                  <td className="px-4 py-2">Yesterday, 4:20 PM</td>
                </tr>
                <tr className="border-b">
                  <td className="px-4 py-2 font-medium">Product D</td>
                  <td className="px-4 py-2">+25</td>
                  <td className="px-4 py-2"><Badge color="success">Received</Badge></td>
                  <td className="px-4 py-2">Yesterday, 1:15 PM</td>
                </tr>
                <tr className="border-b">
                  <td className="px-4 py-2 font-medium">Product E</td>
                  <td className="px-4 py-2">-3</td>
                  <td className="px-4 py-2"><Badge color="warning">Adjustment</Badge></td>
                  <td className="px-4 py-2">May 21, 2023</td>
                </tr>
              </tbody>
            </table>
          </div>
        </Card>
      </div>

      {/* Inventory List */}
      <Card className="mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
          <h5 className="text-xl font-bold">Inventory List</h5>

          <div className="flex gap-2">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <HiOutlineSearch className="text-gray-500" />
              </div>
              <TextInput
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Dropdown label="Filter" color="light">
              <Dropdown.Item>All Products</Dropdown.Item>
              <Dropdown.Item>In Stock</Dropdown.Item>
              <Dropdown.Item>Low Stock</Dropdown.Item>
              <Dropdown.Item>Out of Stock</Dropdown.Item>
            </Dropdown>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <Table.Head>
              <Table.HeadCell
                onClick={() => handleSort('name')}
                className="cursor-pointer"
              >
                Product Name
                {sortBy === 'name' && (
                  sortOrder === 'asc' ?
                    <HiOutlineArrowUp className="inline ml-1" /> :
                    <HiOutlineArrowDown className="inline ml-1" />
                )}
              </Table.HeadCell>
              <Table.HeadCell>SKU/Barcode</Table.HeadCell>
              <Table.HeadCell
                onClick={() => handleSort('stock_quantity')}
                className="cursor-pointer"
              >
                Quantity
                {sortBy === 'stock_quantity' && (
                  sortOrder === 'asc' ?
                    <HiOutlineArrowUp className="inline ml-1" /> :
                    <HiOutlineArrowDown className="inline ml-1" />
                )}
              </Table.HeadCell>
              <Table.HeadCell>Status</Table.HeadCell>
              <Table.HeadCell
                onClick={() => handleSort('cost_price')}
                className="cursor-pointer"
              >
                Value
                {sortBy === 'cost_price' && (
                  sortOrder === 'asc' ?
                    <HiOutlineArrowUp className="inline ml-1" /> :
                    <HiOutlineArrowDown className="inline ml-1" />
                )}
              </Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y">
              {currentItems.length > 0 ? (
                currentItems.map(product => (
                  <Table.Row key={product.id} className="bg-white">
                    <Table.Cell className="font-medium">
                      <Link to={`/products/${product.id}`} className="text-blue-600 hover:underline">
                        {product.name}
                      </Link>
                    </Table.Cell>
                    <Table.Cell>
                      {product.sku && <div>SKU: {product.sku}</div>}
                      {product.barcode && <div>Barcode: {product.barcode}</div>}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center">
                        <span className="font-semibold">{formatQuantityWithSeparators(product.stock_quantity)}</span>
                        {product.min_stock_level > 0 && (
                          <span className="text-xs text-gray-500 ml-1">
                            (Min: {formatQuantityWithSeparators(product.min_stock_level)})
                          </span>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {getStockStatusBadge(product)}
                    </Table.Cell>
                    <Table.Cell>
                      ${(Number(product.cost_price || 0) * product.stock_quantity).toFixed(2)}
                    </Table.Cell>
                  </Table.Row>
                ))
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={5} className="text-center py-4">
                    {searchQuery ? 'No products match your search.' : 'No products found.'}
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table>
        </div>

        {/* Pagination */}
        {filteredProducts.length > itemsPerPage && (
          <div className="flex justify-center mt-4">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              showIcons
            />
          </div>
        )}
      </Card>

      {error && (
        <div className="mt-4">
          <Card color="failure">
            <p className="font-medium">Error</p>
            <p>{error}</p>
          </Card>
        </div>
      )}
    </div>
  );
};

export default InventoryDashboard;
