import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ner,
  <PERSON><PERSON>,
  Badge,
  <PERSON>,
  Modal
} from 'flowbite-react';
import {
  HiOutlineExclamation,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineArrowLeft,
  HiOutlineDocumentText,
  HiOutlineRefresh,
  HiOutlineCheck
} from 'react-icons/hi';
import { getInventoryReceiptById } from '../../services/inventoryReceipt';
import { createTransactionsFromReceiptItems } from '../../services/inventoryTransaction';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import SendToPayableButton from '../../components/inventory/SendToPayableButton';
import { checkReceiptPayableStatus } from '../../services/payables';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

const InventoryReceiptDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const formatWithCurrency = useCurrencyFormatter();

  const [receipt, setReceipt] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for creating transactions
  const [isCreatingTransactions, setIsCreatingTransactions] = useState(false);
  const [transactionError, setTransactionError] = useState<string | null>(null);
  const [transactionSuccess, setTransactionSuccess] = useState(false);
  const [showTransactionModal, setShowTransactionModal] = useState(false);

  // State for payable status
  const [hasPendingPayable, setHasPendingPayable] = useState(false);
  const [payableCheckLoading, setPayableCheckLoading] = useState(false);

  useEffect(() => {
    const fetchReceipt = async () => {
      if (!currentOrganization || !id) return;

      setIsLoading(true);
      setError(null);

      try {
        const { receipt: receiptData, error: fetchError } = await getInventoryReceiptById(
          currentOrganization.id,
          id
        );

        if (fetchError) {
          setError(fetchError);
        } else if (receiptData) {
          setReceipt(receiptData);

          // Check payable status if receipt is completed
          if (receiptData.status === 'completed') {
            checkPayableStatus(receiptData.id);
          }
        } else {
          setError('Receipt not found');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the receipt');
      } finally {
        setIsLoading(false);
      }
    };

    fetchReceipt();
  }, [currentOrganization, id]);

  // Function to check if receipt has been sent to payables
  const checkPayableStatus = async (receiptId: string) => {
    if (!currentOrganization) return;

    setPayableCheckLoading(true);
    try {
      const { hasPendingPayable: hasPayable } = await checkReceiptPayableStatus(
        currentOrganization.id,
        receiptId
      );
      setHasPendingPayable(hasPayable);
    } catch (error) {
      console.error('Error checking payable status:', error);
    } finally {
      setPayableCheckLoading(false);
    }
  };

  const handleCreateTransactions = async () => {
    if (!currentOrganization || !id || !user) return;

    setIsCreatingTransactions(true);
    setTransactionError(null);
    setTransactionSuccess(false);

    try {
      const { success, error: createError } = await createTransactionsFromReceiptItems(
        currentOrganization.id,
        id,
        user.id
      );

      if (createError) {
        setTransactionError(createError);
      } else {
        setTransactionSuccess(true);
        // Refresh the receipt data
        const { receipt: refreshedReceipt } = await getInventoryReceiptById(
          currentOrganization.id,
          id
        );
        if (refreshedReceipt) {
          setReceipt(refreshedReceipt);
        }
      }
    } catch (err: any) {
      setTransactionError(err.message || 'An error occurred while creating transactions');
    } finally {
      setIsCreatingTransactions(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (!currentOrganization) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">No Organization Selected</h3>
            <p>
              Please select an organization to view receipt details.
            </p>
          </Alert>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        </Card>
      </div>
    );
  }

  if (error || !receipt) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">Error</h3>
            <p>{error || 'Receipt not found'}</p>
            <div className="mt-4">
              <Link to="/inventory/receipts">
                <Button color="gray" size="sm">
                  <HiOutlineArrowLeft className="mr-2 h-4 w-4" />
                  Back to Receipts
                </Button>
              </Link>
            </div>
          </Alert>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        {/* Header with actions */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Receipt #{receipt.receipt_number}</h1>
            <p className="text-gray-500">
              {receipt.purchase_order ? `From PO #${receipt.purchase_order.order_number}` : 'Manual Receipt'}
            </p>
          </div>

          <div className="flex gap-2">
            <Link to="/inventory/receipts">
              <Button color="gray">
                <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
                Back
              </Button>
            </Link>

            {receipt.status === 'completed' && (
              <>
                <Button
                  color="primary"
                  onClick={() => setShowTransactionModal(true)}
                >
                  <HiOutlineRefresh className="mr-2 h-5 w-5" />
                  Create Transactions
                </Button>

                {!hasPendingPayable && (
                  <SendToPayableButton
                    receiptId={receipt.id}
                    receiptNumber={receipt.receipt_number}
                    receiptStatus={receipt.status}
                  />
                )}

                {hasPendingPayable && (
                  <Badge color="success" className="flex items-center">
                    <HiOutlineCheck className="mr-1 h-4 w-4" />
                    Sent to Payables
                  </Badge>
                )}
              </>
            )}
          </div>
        </div>

        {/* Receipt information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Receipt Information</h2>
            <div className="bg-gray-50 p-4 rounded-lg space-y-3">
              <div>
                <p className="text-sm text-gray-500">Receipt Number</p>
                <p className="font-medium">{receipt.receipt_number}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Date</p>
                <p>{formatDate(receipt.receipt_date)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <Badge
                  color={receipt.status === 'completed' ? 'success' : 'warning'}
                >
                  {receipt.status === 'completed' ? 'Completed' : 'Draft'}
                </Badge>
              </div>
            </div>
          </div>

          {receipt.purchase_order && (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Purchase Order</h2>
              <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                <div>
                  <p className="text-sm text-gray-500">PO Number</p>
                  <p className="font-medium">{receipt.purchase_order.order_number}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Supplier</p>
                  <p>{receipt.purchase_order.supplier_name || 'Unknown Supplier'}</p>
                </div>
                <div>
                  <Link to={`/purchases/orders/${receipt.purchase_order.id}`}>
                    <Button size="xs" color="gray">
                      View Purchase Order
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Notes</h2>
            <div className="bg-gray-50 p-4 rounded-lg min-h-[100px]">
              {receipt.notes ? (
                <p className="whitespace-pre-line">{receipt.notes}</p>
              ) : (
                <p className="text-gray-400 italic">No notes</p>
              )}
            </div>
          </div>
        </div>

        {/* Items */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Items</h2>
          <div className="overflow-x-auto">
            <Table>
              <Table.Head>
                <Table.HeadCell>Product</Table.HeadCell>
                <Table.HeadCell>Quantity</Table.HeadCell>
                <Table.HeadCell>Unit</Table.HeadCell>
                <Table.HeadCell>Unit Cost</Table.HeadCell>
                <Table.HeadCell>Total</Table.HeadCell>
                <Table.HeadCell>QC Status</Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {receipt.items.map((item: any) => (
                  <Table.Row key={item.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                    <Table.Cell>
                      <div>
                        <div className="font-medium">{item.product?.name || 'Unknown Product'}</div>
                        {item.product?.sku && (
                          <div className="text-xs text-gray-500">SKU: {item.product.sku}</div>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>{item.quantity}</Table.Cell>
                    <Table.Cell>{item.uom?.name || 'Unknown'}</Table.Cell>
                    <Table.Cell>{formatWithCurrency(item.unit_cost)}</Table.Cell>
                    <Table.Cell>{formatWithCurrency(item.quantity * item.unit_cost)}</Table.Cell>
                    <Table.Cell>
                      <Badge
                        color={
                          item.qc_status === 'passed'
                            ? 'success'
                            : item.qc_status === 'failed'
                            ? 'failure'
                            : 'warning'
                        }
                      >
                        {item.qc_status || 'Not Checked'}
                      </Badge>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        </div>

        {/* System Information */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h2 className="text-sm font-medium text-gray-500 mb-2">System Information</h2>
          <div className="flex flex-wrap gap-4">
            <div>
              <p className="text-xs text-gray-500">Receipt ID</p>
              <p className="text-sm">{receipt.id}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Created By</p>
              <p className="text-sm">{receipt.creator_name || 'Unknown'}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Created</p>
              <p className="text-sm">{new Date(receipt.created_at).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Last Updated</p>
              <p className="text-sm">{new Date(receipt.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Transaction Creation Modal */}
      <Modal show={showTransactionModal} onClose={() => setShowTransactionModal(false)}>
        <Modal.Header>Create Inventory Transactions</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>
              This will create inventory transactions for all items in this receipt. This is usually done
              automatically when a receipt is completed, but you can use this function if transactions
              were not created properly.
            </p>
            <p>
              Are you sure you want to create inventory transactions for this receipt?
            </p>

            {transactionError && (
              <Alert color="failure" icon={HiOutlineExclamation}>
                {transactionError}
              </Alert>
            )}

            {transactionSuccess && (
              <Alert color="success" icon={HiOutlineCheck}>
                Inventory transactions created successfully!
              </Alert>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="gray"
            onClick={() => setShowTransactionModal(false)}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={handleCreateTransactions}
            disabled={isCreatingTransactions}
          >
            {isCreatingTransactions ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Creating...
              </>
            ) : (
              <>
                <HiOutlineDocumentText className="mr-2 h-5 w-5" />
                Create Transactions
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default InventoryReceiptDetails;
