import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Table,
  Badge,
  Spinner,
  <PERSON><PERSON>,
  Toolt<PERSON>
} from 'flowbite-react';
import {
  HiOutlineChevronLeft,
  HiOutlineExclamation,
  HiOutlineDocumentText,
  HiOutlineOfficeBuilding,
  HiOutlineCalendar,
  HiOutlineClipboardList,
  HiOutlineCurrencyDollar,
  HiOutlinePencil,
  HiOutlinePrinter,
  HiOutlineUser,
  HiOutlineInformationCircle,
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineExclamationCircle
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getInventoryReceiptById } from '../../services/inventoryReceipt';
import { formatDate, formatDateTime } from '../../utils/formatters';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

const InventoryReceiptDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  const [receipt, setReceipt] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);

  // Pagination helper functions
  const getCurrentPageItems = () => {
    if (!receipt || !receipt.items) return [];
    
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return receipt.items.slice(indexOfFirstItem, indexOfLastItem);
  };

  // Function to change page
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Function to change items per page
  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newItemsPerPage = parseInt(e.target.value);
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
    if (receipt?.items) {
      setTotalPages(Math.ceil(receipt.items.length / newItemsPerPage));
    }
  };

  useEffect(() => {
    const fetchReceipt = async () => {
      if (!id || !currentOrganization) return;

      setLoading(true);
      setError(null);

      try {
        const { receipt: receiptData, error: receiptError } = await getInventoryReceiptById(
          currentOrganization.id,
          id
        );

        if (receiptError) {
          setError(receiptError);
        } else if (receiptData) {
          setReceipt(receiptData);
          // Calculate total pages when receipt data loads
          if (receiptData?.items?.length) {
            setTotalPages(Math.ceil(receiptData.items.length / itemsPerPage));
          }
        } else {
          setError('Receipt not found');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the receipt');
      } finally {
        setLoading(false);
      }
    };

    fetchReceipt();
  }, [id, currentOrganization, itemsPerPage]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge color="warning">Draft</Badge>;
      case 'completed':
        return <Badge color="success">Completed</Badge>;
      case 'cancelled':
        return <Badge color="failure">Cancelled</Badge>;
      default:
        return <Badge color="info">{status}</Badge>;
    }
  };

  const getQcStatusBadge = (status: string) => {
    switch (status) {
      case 'passed':
        return <Badge color="success" icon={HiOutlineCheckCircle}>Passed</Badge>;
      case 'failed':
        return <Badge color="failure" icon={HiOutlineXCircle}>Failed</Badge>;
      case 'quarantine':
        return <Badge color="warning" icon={HiOutlineExclamationCircle}>Quarantine</Badge>;
      default:
        return <Badge color="info">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error || !receipt) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamation}>
          {error || 'Receipt not found'}
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/inventory/receipts')}>
            <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
            Back to Receipts
          </Button>
        </div>
      </div>
    );
  }

  // Calculate total pages for pagination
  useEffect(() => {
    if (receipt?.items) {
      setTotalPages(Math.ceil(receipt.items.length / itemsPerPage));
    }
  }, [receipt, itemsPerPage]);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <HiOutlineDocumentText className="mr-2 h-6 w-6" />
              Receipt: {receipt.receipt_number}
            </h1>
            <div className="flex flex-wrap gap-4 mt-2">
              {receipt.purchase_order && (
                <div className="flex items-center">
                  <HiOutlineClipboardList className="mr-2 h-5 w-5 text-gray-500" />
                  <button
                    onClick={() => navigate(`/purchases/orders/${receipt.purchase_order.id}`)}
                    className="text-blue-600 hover:text-blue-800 hover:underline font-medium focus:outline-none"
                    title="View purchase order details"
                  >
                    PO: {receipt.purchase_order.order_number}
                  </button>
                </div>
              )}

              {receipt.purchase_order?.supplier_name && (
                <div className="flex items-center">
                  <HiOutlineOfficeBuilding className="mr-2 h-5 w-5 text-gray-500" />
                  <button
                    onClick={() => navigate(`/suppliers/${receipt.purchase_order.supplier_id}`)}
                    className="text-blue-600 hover:text-blue-800 hover:underline font-medium focus:outline-none"
                    title="View supplier details"
                  >
                    {receipt.purchase_order.supplier_name}
                  </button>
                </div>
              )}

              <div className="flex items-center">
                <HiOutlineCalendar className="mr-2 h-5 w-5 text-gray-500" />
                <span>{formatDate(receipt.receipt_date)}</span>
              </div>

              <div className="flex items-center">
                <HiOutlineUser className="mr-2 h-5 w-5 text-gray-500" />
                <span>Created by: {receipt.creator_name || 'Staff'}</span>
              </div>

              <div className="flex items-center">
                {getStatusBadge(receipt.status)}
              </div>

              {receipt.is_early_delivery && (
                <div className="flex items-center">
                  <Badge color="info" icon={HiOutlineInformationCircle}>Early Delivery</Badge>
                </div>
              )}

              {receipt.is_late_delivery && (
                <div className="flex items-center">
                  <Badge color="warning" icon={HiOutlineExclamationCircle}>Late Delivery</Badge>
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button color="gray" onClick={() => navigate('/inventory/receipts')}>
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back
            </Button>
            <Button color="gray" onClick={() => window.print()}>
              <HiOutlinePrinter className="mr-2 h-5 w-5" />
              Print
            </Button>
            {receipt.status === 'completed' && (
              <Button
                color="warning"
                onClick={() => navigate(`/inventory/returns/create?receiptId=${receipt.id}`)}
              >
                <HiOutlineExclamationCircle className="mr-2 h-5 w-5" />
                Create Return
              </Button>
            )}
          </div>
        </div>

        {receipt.notes && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2">Notes</h3>
            <p className="text-gray-700">{receipt.notes}</p>
          </div>
        )}

        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">Received Items</h2>

          {receipt.items && receipt.items.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <Table.Head>
                  <Table.HeadCell>Product</Table.HeadCell>
                  <Table.HeadCell>Quantity</Table.HeadCell>
                  <Table.HeadCell>Unit</Table.HeadCell>
                  <Table.HeadCell>Unit Cost</Table.HeadCell>
                  <Table.HeadCell>Total</Table.HeadCell>
                  <Table.HeadCell>QC Status</Table.HeadCell>
                  <Table.HeadCell>Lot/Batch</Table.HeadCell>
                </Table.Head>
                <Table.Body className="divide-y">
                  {getCurrentPageItems().map((item: any) => (
                    <Table.Row key={item.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                      <Table.Cell className="font-medium">
                        {item.product ? (
                          <button
                            onClick={() => navigate(`/products/details/${item.product.id}`)}
                            className="text-primary-600 hover:text-primary-800 hover:underline focus:outline-none"
                          >
                            {item.product.name}
                          </button>
                        ) : (
                          'Unknown Product'
                        )}
                        {item.product?.sku && (
                          <div className="text-xs text-gray-500">SKU: {item.product.sku}</div>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        <div className="font-medium">{item.quantity}</div>
                        {item.expected_quantity && item.expected_quantity !== item.quantity && (
                          <div className="text-xs">
                            <span className={item.quantity < item.expected_quantity ? 'text-red-500' : 'text-blue-500'}>
                              {item.quantity < item.expected_quantity ? 'Under' : 'Over'} by {Math.abs(item.quantity - item.expected_quantity)}
                            </span>
                          </div>
                        )}
                        {item.damaged_quantity > 0 && (
                          <div className="text-xs text-red-500">
                            Damaged: {item.damaged_quantity}
                          </div>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        {item.uom?.name || 'Unit'}
                        <div className="text-xs text-gray-500">{item.uom?.code}</div>
                      </Table.Cell>
                      <Table.Cell>
                        {formatWithCurrency(item.unit_cost)}
                        {item.expected_unit_cost && item.expected_unit_cost !== item.unit_cost && (
                          <div className="text-xs">
                            <span className={item.unit_cost > item.expected_unit_cost ? 'text-red-500' : 'text-green-500'}>
                              {item.unit_cost > item.expected_unit_cost ? 'Above' : 'Below'} expected
                            </span>
                          </div>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        {formatWithCurrency(item.quantity * item.unit_cost)}
                      </Table.Cell>
                      <Table.Cell>
                        {getQcStatusBadge(item.qc_status || 'passed')}
                        {item.damage_reason && (
                          <Tooltip content={item.damage_reason}>
                            <div className="text-xs text-red-500 cursor-help mt-1 underline">
                              View reason
                            </div>
                          </Tooltip>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        {item.lot_number ? (
                          <div>
                            <div className="font-medium">{item.lot_number}</div>
                            {item.expiry_date && (
                              <div className="text-xs text-gray-500">
                                Expires: {formatDate(item.expiry_date)}
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-500">N/A</span>
                        )}

                        {item.serial_numbers && item.serial_numbers.length > 0 && (
                          <div className="mt-2">
                            <div className="text-xs font-medium text-gray-700 mb-1">
                              Serial Numbers ({item.serial_numbers.length})
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {item.serial_numbers.slice(0, 3).map((sn: string, idx: number) => (
                                <Badge key={idx} color="info" size="xs">{sn}</Badge>
                              ))}
                              {item.serial_numbers.length > 3 && (
                                <Tooltip content={item.serial_numbers.slice(3).join(', ')}>
                                  <Badge color="gray" size="xs">+{item.serial_numbers.length - 3} more</Badge>
                                </Tooltip>
                              )}
                            </div>
                          </div>
                        )}
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
              No items in this receipt
            </div>
          )}
        </div>

        {/* Pagination controls */}
        {totalPages > 1 && (
          <div className="flex flex-col md:flex-row items-center justify-between mt-4">
            <div className="flex items-center gap-2 mb-2 md:mb-0">
              <span className="text-sm text-gray-700">Items per page:</span>
              <select
                value={itemsPerPage}
                onChange={handleItemsPerPageChange}
                className="border rounded-md p-1 text-sm"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={200}>200</option>
              </select>
              <span className="text-sm text-gray-700">
                Showing {receipt?.items && ((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, receipt?.items?.length || 0)} of {receipt?.items?.length || 0} items
              </span>
            </div>

            <div className="flex items-center gap-2 flex-wrap">
              <Button
                size="xs"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(1)}
              >
                First
              </Button>
              <Button
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
                className="text-sm"
              >
                Previous
              </Button>
              
              {/* Page numbers */}
              <div className="flex gap-1">
                {[...Array(totalPages)].map((_, index) => {
                  // Show current page, first, last, and nearby pages
                  if (
                    index === 0 ||
                    index === totalPages - 1 ||
                    (index >= currentPage - 2 && index <= currentPage + 2)
                  ) {
                    return (
                      <Button
                        key={index}
                        color={currentPage === index + 1 ? "blue" : "gray"}
                        onClick={() => handlePageChange(index + 1)}
                        size="xs"
                        className="min-w-[32px]"
                      >
                        {index + 1}
                      </Button>
                    );
                  } else if (
                    index === currentPage - 3 ||
                    index === currentPage + 3
                  ) {
                    return <span key={index} className="px-1">...</span>;
                  }
                  return null;
                })}
              </div>
              
              <Button
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
                className="text-sm"
              >
                Next
              </Button>
              <Button
                size="xs"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(totalPages)}
              >
                Last
              </Button>
            </div>
          </div>
        )}

        <div className="text-sm text-gray-500 mt-4">
          <div>Created: {formatDateTime(receipt.created_at)}</div>
          <div>Last Updated: {formatDateTime(receipt.updated_at)}</div>
        </div>
      </Card>
    </div>
  );
};

export default InventoryReceiptDetails;
