import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  Card,
  <PERSON>ert,
  Spinner,
  <PERSON>ton,
  Badge,
  Modal,
  TextInput,
  Textarea,
  Select
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineExclamation,
  HiOutlineUser,
  HiOutlineCalendar,
  HiOutlineCube,
  HiOutlineReceiptTax,
  HiOutlineDocumentText,
  HiOutlineCheck,
  HiOutlineX,
  HiOutlinePencil
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { getFloatInventoryById, resolveFloatInventory, FloatInventoryDetail } from '../../services/floatInventory';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { formatDateTime } from '../../utils/formatters';

const FloatInventoryDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const formatWithCurrency = useCurrencyFormatter();

  const [floatItem, setFloatItem] = useState<FloatInventoryDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showResolveModal, setShowResolveModal] = useState(false);
  const [resolving, setResolving] = useState(false);
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [resolutionType, setResolutionType] = useState('manual');

  useEffect(() => {
    const fetchFloatItem = async () => {
      if (!currentOrganization || !id) return;

      setLoading(true);
      setError(null);

      try {
        const { floatItem: itemData, error: fetchError } = await getFloatInventoryById(
          currentOrganization.id,
          id
        );

        if (fetchError) {
          setError(fetchError);
        } else if (itemData) {
          setFloatItem(itemData);
        } else {
          setError('Float inventory item not found');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the float inventory item');
      } finally {
        setLoading(false);
      }
    };

    fetchFloatItem();
  }, [currentOrganization, id]);

  const handleResolve = async () => {
    if (!floatItem || !user) return;

    setResolving(true);
    try {
      const { success, error: resolveError } = await resolveFloatInventory(
        floatItem.id,
        user.id,
        resolutionNotes || 'Manually resolved from details page'
      );

      if (resolveError) {
        setError(resolveError);
      } else if (success) {
        // Refresh the data
        const { floatItem: updatedItem } = await getFloatInventoryById(
          currentOrganization!.id,
          id!
        );
        if (updatedItem) {
          setFloatItem(updatedItem);
        }
        setShowResolveModal(false);
        setResolutionNotes('');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to resolve float inventory');
    } finally {
      setResolving(false);
    }
  };

  const getDaysInfo = () => {
    if (!floatItem) return null;

    const createdAt = new Date(floatItem.created_at);
    const now = new Date();
    const resolvedAt = floatItem.resolved_at ? new Date(floatItem.resolved_at) : null;

    if (floatItem.resolved && resolvedAt) {
      const daysToResolve = Math.floor((resolvedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24));
      return {
        type: 'resolved',
        days: daysToResolve,
        label: `Resolved in ${daysToResolve} day${daysToResolve !== 1 ? 's' : ''}`
      };
    } else {
      const daysUnresolved = Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24));
      return {
        type: 'unresolved',
        days: daysUnresolved,
        label: `Unresolved for ${daysUnresolved} day${daysUnresolved !== 1 ? 's' : ''}`
      };
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure">
          <span className="font-medium">Error:</span> {error}
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/inventory/float')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Float Inventory
          </Button>
        </div>
      </div>
    );
  }

  if (!floatItem) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="warning">
          <span className="font-medium">Float Inventory Item Not Found</span>
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/inventory/float')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Float Inventory
          </Button>
        </div>
      </div>
    );
  }

  const daysInfo = getDaysInfo();

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button
            color="gray"
            onClick={() => navigate('/inventory/float')}
            className="mr-4"
          >
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <HiOutlineExclamation className="mr-2 h-6 w-6" />
              Float Inventory Details
            </h1>
            <p className="text-gray-600">
              {floatItem.product_name} - {floatItem.quantity} units
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          {!floatItem.resolved && (
            <Button
              color="success"
              onClick={() => setShowResolveModal(true)}
            >
              <HiOutlineCheck className="mr-2 h-5 w-5" />
              Resolve
            </Button>
          )}
          <Link to="/inventory/float/report">
            <Button color="light">
              <HiOutlineDocumentText className="mr-2 h-5 w-5" />
              View Reports
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Float Information */}
          <Card>
            <h2 className="text-xl font-semibold mb-4">Float Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <HiOutlineCube className="mr-3 h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Product</p>
                  <p className="font-medium">
                    <Link
                      to={`/products/details/${floatItem.product_id}`}
                      className="text-blue-600 hover:underline"
                    >
                      {floatItem.product_name}
                    </Link>
                  </p>
                  {floatItem.product_sku && (
                    <p className="text-sm text-gray-500">SKU: {floatItem.product_sku}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center">
                <HiOutlineReceiptTax className="mr-3 h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Related Sale</p>
                  <p className="font-medium">
                    <Link
                      to={`/sales/details/${floatItem.sale_id}`}
                      className="text-blue-600 hover:underline"
                    >
                      {floatItem.sale_number}
                    </Link>
                  </p>
                  <p className="text-sm text-gray-500">
                    {formatDateTime(floatItem.sale_date)}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <HiOutlineCalendar className="mr-3 h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Created</p>
                  <p className="font-medium">{formatDateTime(floatItem.created_at)}</p>
                  {daysInfo && (
                    <p className={`text-sm ${daysInfo.type === 'unresolved' ? 'text-red-600' : 'text-green-600'}`}>
                      {daysInfo.label}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center">
                <HiOutlineUser className="mr-3 h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Customer</p>
                  <p className="font-medium">
                    {floatItem.customer_name ? (
                      <Link
                        to={`/customers/details/${floatItem.customer_id}`}
                        className="text-blue-600 hover:underline"
                      >
                        {floatItem.customer_name}
                      </Link>
                    ) : (
                      'Walk-in Customer'
                    )}
                  </p>
                </div>
              </div>
            </div>
          </Card>

          {/* Resolution Information */}
          {floatItem.resolved && (
            <Card>
              <h2 className="text-xl font-semibold mb-4">Resolution Information</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center">
                  <HiOutlineCalendar className="mr-3 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Resolved Date</p>
                    <p className="font-medium">
                      {floatItem.resolved_at ? formatDateTime(floatItem.resolved_at) : 'N/A'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center">
                  <HiOutlineDocumentText className="mr-3 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Resolution Type</p>
                    <Badge
                      color={floatItem.resolution_type === 'automatic' ? 'success' : 'info'}
                    >
                      {floatItem.resolution_type || 'manual'}
                    </Badge>
                  </div>
                </div>

                {floatItem.resolved_by_id && (
                  <div className="flex items-center">
                    <HiOutlineUser className="mr-3 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Resolved By</p>
                      <p className="font-medium">{floatItem.resolved_by_name || 'User'}</p>
                    </div>
                  </div>
                )}
              </div>

              {floatItem.resolution_notes && (
                <div className="mt-4 border-t pt-4">
                  <p className="text-sm text-gray-500 mb-2">Resolution Notes</p>
                  <p className="text-gray-700">{floatItem.resolution_notes}</p>
                </div>
              )}
            </Card>
          )}
        </div>

        {/* Status and Actions */}
        <div>
          <Card>
            <h2 className="text-xl font-semibold mb-4">Status</h2>

            <div className="space-y-4">
              <div className="text-center">
                <Badge
                  color={floatItem.resolved ? 'success' : 'warning'}
                  className="text-lg px-4 py-2"
                >
                  {floatItem.resolved ? 'Resolved' : 'Unresolved'}
                </Badge>
              </div>

              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">
                  {floatItem.quantity} units
                </p>
                <p className="text-sm text-gray-500">Float Quantity</p>
              </div>

              {daysInfo && (
                <div className="text-center">
                  <p className={`text-lg font-semibold ${daysInfo.type === 'unresolved' ? 'text-red-600' : 'text-green-600'}`}>
                    {daysInfo.days} days
                  </p>
                  <p className="text-sm text-gray-500">
                    {daysInfo.type === 'unresolved' ? 'Unresolved' : 'To resolve'}
                  </p>
                </div>
              )}

              {!floatItem.resolved && (
                <div className="space-y-2">
                  <Button
                    color="success"
                    className="w-full"
                    onClick={() => setShowResolveModal(true)}
                  >
                    <HiOutlineCheck className="mr-2 h-5 w-5" />
                    Resolve Float
                  </Button>

                  <Link to={`/inventory/receipts/create?product=${floatItem.product_id}`}>
                    <Button color="info" className="w-full">
                      <HiOutlineCube className="mr-2 h-5 w-5" />
                      Receive Stock
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>

      {/* Resolve Modal */}
      <Modal show={showResolveModal} onClose={() => setShowResolveModal(false)}>
        <Modal.Header>Resolve Float Inventory</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <div>
              <label className="block mb-2 text-sm font-medium">Resolution Type</label>
              <Select
                value={resolutionType}
                onChange={(e) => setResolutionType(e.target.value)}
              >
                <option value="manual">Manual Resolution</option>
                <option value="automatic">Automatic Resolution</option>
              </Select>
            </div>

            <div>
              <label className="block mb-2 text-sm font-medium">Resolution Notes</label>
              <Textarea
                value={resolutionNotes}
                onChange={(e) => setResolutionNotes(e.target.value)}
                placeholder="Enter notes about how this float inventory was resolved..."
                rows={4}
              />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="success"
            onClick={handleResolve}
            disabled={resolving}
          >
            {resolving ? <Spinner size="sm" className="mr-2" /> : <HiOutlineCheck className="mr-2 h-5 w-5" />}
            Resolve
          </Button>
          <Button
            color="gray"
            onClick={() => setShowResolveModal(false)}
          >
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default FloatInventoryDetails;
