import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { <PERSON>, But<PERSON>, Table, Badge, Spinner, Alert, TextInput, Select } from "flowbite-react";
import {
  HiOutlinePlus,
  HiOutlineExclamation,
  HiOutlineSearch,
  HiOutlineFilter,
  HiOutlineRefresh,
  HiOutlineDocumentText,
  HiOutlineClipboardList,
  HiOutlineOfficeBuilding,
  HiOutlineCalendar
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getInventoryReceipts, InventoryReceiptWithItems } from '../../services/inventoryReceipt';
import { formatDate } from '../../utils/formatters';
import EmptyState from '../../components/common/EmptyState';
import { getReceiptsPayableStatus } from '../../services/payables';

const InventoryReceipts = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();

  const [receipts, setReceipts] = useState<InventoryReceiptWithItems[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [payableFilter, setPayableFilter] = useState(''); // 'sent', 'not_sent', or ''
  const [receiptsPayableStatus, setReceiptsPayableStatus] = useState<Record<string, boolean>>({});

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);

  useEffect(() => {
    if (currentOrganization) {
      fetchReceipts();
    }
  }, [currentOrganization]);

  const fetchReceipts = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const filters = {
        searchQuery: searchQuery || undefined,
        status: statusFilter || undefined,
      };

      const { receipts: receiptsList, error: receiptsError } = await getInventoryReceipts(
        currentOrganization.id,
        filters
      );

      if (receiptsError) {
        setError(receiptsError);
      } else {
        setReceipts(receiptsList);
        setTotalPages(Math.ceil(receiptsList.length / itemsPerPage)); // Calculate total pages

        // Fetch payable status for all receipts
        if (receiptsList.length > 0) {
          const receiptIds = receiptsList.map(receipt => receipt.id);
          const { receiptsStatus } = await getReceiptsPayableStatus(currentOrganization.id, receiptIds);
          setReceiptsPayableStatus(receiptsStatus);
        }
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching inventory receipts');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    fetchReceipts();
  };

  const handleReset = () => {
    setSearchQuery('');
    setStatusFilter('');
    fetchReceipts();
  };

  const handleCreateClick = () => {
    navigate('/inventory/receipts/create');
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (items: number) => {
    setItemsPerPage(items);
    setCurrentPage(1); // Reset to first page on items per page change
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge color="warning">Draft</Badge>;
      case 'completed':
        return <Badge color="success">Completed</Badge>;
      case 'cancelled':
        return <Badge color="failure">Cancelled</Badge>;
      default:
        return <Badge color="info">{status}</Badge>;
    }
  };

  // Calculate paginated receipts
  const indexOfLastReceipt = currentPage * itemsPerPage;
  const indexOfFirstReceipt = indexOfLastReceipt - itemsPerPage;
  const currentReceipts = receipts.slice(indexOfFirstReceipt, indexOfLastReceipt);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <HiOutlineClipboardList className="mr-2 h-6 w-6" />
              Inventory Receipts
            </h1>
            <p className="text-gray-500">
              Manage goods receipt notes (GRNs) for inventory received from suppliers.
            </p>
          </div>

          <Button
            color="primary"
            onClick={handleCreateClick}
            className="px-4 py-2 font-medium shadow-sm hover:shadow-md transition-all"
            size="lg"
          >
            <HiOutlinePlus className="mr-2 h-5 w-5" />
            Create Receipt
          </Button>
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <TextInput
              id="search"
              type="text"
              placeholder="Search by receipt number..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={HiOutlineSearch}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          <div className="w-full md:w-48">
            <Select
              id="status"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">All Statuses</option>
              <option value="draft">Draft</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </Select>
          </div>
          <div className="w-full md:w-48">
            <Select
              id="payableStatus"
              value={payableFilter}
              onChange={(e) => setPayableFilter(e.target.value)}
            >
              <option value="">All Payable Status</option>
              <option value="sent">Sent to Payables</option>
              <option value="not_sent">Not Sent to Payables</option>
            </Select>
          </div>
          <div className="flex gap-2">
            <Button color="primary" onClick={handleSearch}>
              <HiOutlineFilter className="mr-2 h-5 w-5" />
              Filter
            </Button>
            <Button color="gray" onClick={handleReset}>
              <HiOutlineRefresh className="mr-2 h-5 w-5" />
              Reset
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Spinner size="xl" />
          </div>
        ) : receipts.length === 0 ? (
          <EmptyState
            title="No inventory receipts found"
            description="Create your first inventory receipt to start tracking goods received from suppliers."
            icon={<HiOutlineClipboardList className="h-12 w-12" />}
          />
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Receipt Number</Table.HeadCell>
                <Table.HeadCell>Purchase Order</Table.HeadCell>
                <Table.HeadCell>Supplier</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>Payable Status</Table.HeadCell>
                <Table.HeadCell>Date</Table.HeadCell>
                <Table.HeadCell>
                  <span className="sr-only">Actions</span>
                </Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {currentReceipts.map((receipt) => (
                  <Table.Row
                    key={receipt.id}
                    className="bg-white dark:border-gray-700 dark:bg-gray-800 hover:bg-gray-50"
                    onClick={() => navigate(`/inventory/receipts/${receipt.id}`)}
                    style={{ cursor: 'pointer' }}
                  >
                    <Table.Cell className="font-medium">
                      <div className="flex items-center">
                        <HiOutlineDocumentText className="mr-2 h-5 w-5 text-gray-500" />
                        {receipt.receipt_number}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {receipt.purchase_order ? (
                        <Link
                          to={`/purchases/orders/${receipt.purchase_order.id}`}
                          onClick={(e) => e.stopPropagation()}
                          className="text-blue-600 hover:text-blue-800 hover:underline"
                        >
                          {receipt.purchase_order.order_number}
                        </Link>
                      ) : (
                        <span className="text-gray-500">Direct Receipt</span>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {receipt.purchase_order?.supplier_name ? (
                        <div className="flex items-center">
                          <HiOutlineOfficeBuilding className="mr-2 h-5 w-5 text-gray-500" />
                          <Link
                            to={`/suppliers/${receipt.purchase_order.supplier_id}`}
                            onClick={(e) => e.stopPropagation()}
                            className="text-blue-600 hover:text-blue-800 hover:underline"
                          >
                            {receipt.purchase_order.supplier_name}
                          </Link>
                        </div>
                      ) : (
                        <span className="text-gray-500">N/A</span>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {getStatusBadge(receipt.status)}
                    </Table.Cell>
                    <Table.Cell>
                      {receipt.status === 'completed' ? (
                        receiptsPayableStatus[receipt.id] ? (
                          <Badge color="success">Sent to Payables</Badge>
                        ) : (
                          <Badge color="warning">Not Sent</Badge>
                        )
                      ) : (
                        <Badge color="gray">N/A</Badge>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center">
                        <HiOutlineCalendar className="mr-2 h-5 w-5 text-gray-500" />
                        {formatDate(receipt.receipt_date)}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex gap-2">
                        <Button
                          size="xs"
                          color="gray"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/inventory/receipts/${receipt.id}`);
                          }}
                        >
                          View
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>

            {/* Pagination controls */}
            <div className="flex flex-col md:flex-row items-center justify-between mt-4">
              <div className="flex items-center gap-2 mb-2 md:mb-0">
                <span className="text-sm text-gray-500">Items per page:</span>
                <Select
                  id="itemsPerPage"
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(parseInt(e.target.value))}
                  className="w-20"
                  sizing="sm"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </Select>
                <span className="text-sm text-gray-500">
                  Showing {indexOfFirstReceipt + 1} to {Math.min(indexOfLastReceipt, receipts.length)} of {receipts.length} results
                </span>
              </div>
              <div className="flex gap-2 items-center flex-wrap">
                <Button
                  color="gray"
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  size="xs"
                >
                  First
                </Button>
                <Button
                  color="gray"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  size="sm"
                >
                  Previous
                </Button>

                {/* Page number buttons */}
                <div className="flex gap-1">
                  {[...Array(totalPages)].map((_, index) => {
                    // Show current page, first, last, and nearby pages
                    if (
                      index === 0 ||
                      index === totalPages - 1 ||
                      (index >= currentPage - 2 && index <= currentPage + 2)
                    ) {
                      return (
                        <Button
                          key={index}
                          color={currentPage === index + 1 ? "blue" : "gray"}
                          onClick={() => handlePageChange(index + 1)}
                          size="xs"
                          className="min-w-[32px]"
                        >
                          {index + 1}
                        </Button>
                      );
                    } else if (
                      index === currentPage - 3 ||
                      index === currentPage + 3
                    ) {
                      return <span key={index}>...</span>;
                    }
                    return null;
                  })}
                </div>

                <Button
                  color="gray"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  size="sm"
                >
                  Next
                </Button>
                <Button
                  color="gray"
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                  size="xs"
                >
                  Last
                </Button>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default InventoryReceipts;
