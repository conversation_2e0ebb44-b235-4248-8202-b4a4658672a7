import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import {
  Card,
  Table,
  <PERSON><PERSON>,
  Spinner,
  Alert,
  Badge,
  TextInput,
  Select,
  Datepicker,
  Tabs
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineExclamation,
  HiOutlineDocumentDownload,
  HiOutlineRefresh,
  HiOutlineSearch,
  HiOutlineFilter,
  HiOutlineAdjustments,
  HiOutlineChevronLeft,
  HiOutlineChevronRight,
  HiOutlineChartBar
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getProductById } from '../../services/product';
import { getInventoryTransactionsByProduct } from '../../services/inventoryTransaction';
import { formatDate, formatDateTime, formatQuantity } from '../../utils/formatters';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import 'chart.js/auto';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
);

const InventoryDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  // Product state
  const [product, setProduct] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Transactions state
  const [transactions, setTransactions] = useState<any[]>([]);
  const [transactionsLoading, setTransactionsLoading] = useState(true);
  const [transactionsError, setTransactionsError] = useState<string | null>(null);

  // Filter state
  const [transactionType, setTransactionType] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Active tab
  const [activeTab, setActiveTab] = useState('details');

  // Chart options
  const chartOptions = {
    responsive: true,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    scales: {
      y: {
        type: 'linear' as const,
        display: true,
        position: 'left' as const,
        title: {
          display: true,
          text: 'Running Total'
        }
      },
      y1: {
        type: 'linear' as const,
        display: true,
        position: 'right' as const,
        grid: {
          drawOnChartArea: false,
        },
        title: {
          display: true,
          text: 'Daily Transactions'
        }
      },
    },
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Inventory Movement Over Time',
      },
    },
  };

  useEffect(() => {
    if (currentOrganization && id) {
      fetchProduct();
      fetchTransactions();
    }
  }, [currentOrganization, id]);

  const fetchProduct = async () => {
    if (!currentOrganization || !id) return;

    setLoading(true);
    setError(null);

    try {
      const { product: productData, error: productError } = await getProductById(
        currentOrganization.id,
        id
      );

      if (productError) {
        setError(productError);
      } else if (productData) {
        setProduct(productData);
      } else {
        setError('Product not found');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching the product');
    } finally {
      setLoading(false);
    }
  };

  const fetchTransactions = async () => {
    if (!currentOrganization || !id) return;

    setTransactionsLoading(true);
    setTransactionsError(null);

    try {
      const { transactions: transactionData, error: transactionError } = await getInventoryTransactionsByProduct(
        currentOrganization.id,
        id
      );

      if (transactionError) {
        setTransactionsError(transactionError);
      } else {
        setTransactions(transactionData);
      }
    } catch (err: any) {
      setTransactionsError(err.message || 'An error occurred while fetching transactions');
    } finally {
      setTransactionsLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchProduct();
    fetchTransactions();
  };

  const handleFilter = () => {
    // This would be implemented to filter transactions
    // For now, we'll just refresh all transactions
    fetchTransactions();
  };

  const handleReset = () => {
    setTransactionType('');
    setStartDate(null);
    setEndDate(null);
    setSearchQuery('');
    fetchTransactions();
  };

  const getTransactionTypeBadge = (type: string) => {
    switch (type.toLowerCase()) {
      case 'purchase':
      case 'receipt':
        return <Badge color="success">Purchase</Badge>;
      case 'sale':
        return <Badge color="info">Sale</Badge>;
      case 'adjustment':
        return <Badge color="warning">Adjustment</Badge>;
      case 'transfer':
        return <Badge color="purple">Transfer</Badge>;
      case 'return':
        return <Badge color="pink">Return</Badge>;
      default:
        return <Badge color="gray">{type}</Badge>;
    }
  };

  const getStockStatusBadge = (product: any) => {
    if (!product.stock_quantity || product.stock_quantity <= 0) {
      return <Badge color="failure">Out of Stock</Badge>;
    } else if (product.min_stock_level && product.stock_quantity <= product.min_stock_level) {
      return <Badge color="warning">Low Stock</Badge>;
    } else {
      return <Badge color="success">In Stock</Badge>;
    }
  };

  // Calculate inventory metrics
  const calculateInventoryValue = () => {
    if (!product) return 0;
    return (product.stock_quantity || 0) * (product.cost_price || 0);
  };

  // Get the default UoM
  const getDefaultUom = () => {
    if (!product || !product.product_uoms || product.product_uoms.length === 0) {
      return null;
    }

    const defaultUom = product.product_uoms.find((pu: any) => pu.is_default);
    return defaultUom ? defaultUom.uom : null;
  };

  // Prepare chart data
  const prepareChartData = () => {
    if (!transactions || transactions.length === 0) {
      return null;
    }

    // Sort transactions by date
    const sortedTransactions = [...transactions].sort((a, b) =>
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );

    // Group transactions by date
    const transactionsByDate = sortedTransactions.reduce((acc: any, transaction: any) => {
      const date = new Date(transaction.created_at).toISOString().split('T')[0];

      if (!acc[date]) {
        acc[date] = {
          receipts: 0,
          sales: 0,
          adjustments: 0,
          runningTotal: 0
        };
      }

      // Update counts based on transaction type
      const quantity = Number(transaction.quantity);

      if (['receipt', 'purchase'].includes(transaction.transaction_type.toLowerCase())) {
        acc[date].receipts += quantity;
        acc[date].runningTotal += quantity;
      } else if (transaction.transaction_type.toLowerCase() === 'sale') {
        acc[date].sales += Math.abs(quantity);
        acc[date].runningTotal -= Math.abs(quantity);
      } else if (transaction.transaction_type.toLowerCase() === 'adjustment') {
        acc[date].adjustments += quantity;
        acc[date].runningTotal += quantity;
      }

      return acc;
    }, {});

    // Calculate running total
    let runningTotal = 0;
    const dates = Object.keys(transactionsByDate).sort();

    const chartData = {
      labels: dates,
      datasets: [
        {
          label: 'Running Total',
          data: dates.map(date => {
            runningTotal += transactionsByDate[date].runningTotal;
            return runningTotal;
          }),
          borderColor: 'rgb(53, 162, 235)',
          backgroundColor: 'rgba(53, 162, 235, 0.5)',
          tension: 0.3,
          yAxisID: 'y',
          type: 'line' as const
        },
        {
          label: 'Receipts',
          data: dates.map(date => transactionsByDate[date].receipts),
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
          borderColor: 'rgb(75, 192, 192)',
          borderWidth: 1,
          yAxisID: 'y1',
          type: 'bar' as const
        },
        {
          label: 'Sales',
          data: dates.map(date => transactionsByDate[date].sales),
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          borderColor: 'rgb(255, 99, 132)',
          borderWidth: 1,
          yAxisID: 'y1',
          type: 'bar' as const
        },
        {
          label: 'Adjustments',
          data: dates.map(date => transactionsByDate[date].adjustments),
          backgroundColor: 'rgba(255, 205, 86, 0.5)',
          borderColor: 'rgb(255, 205, 86)',
          borderWidth: 1,
          yAxisID: 'y1',
          type: 'bar' as const
        }
      ]
    };

    return chartData;
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        </Card>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">Error</h3>
            <p>{error || 'Product not found'}</p>
            <div className="mt-4">
              <Link to="/inventory">
                <Button color="gray" size="sm">
                  <HiOutlineArrowLeft className="mr-2 h-4 w-4" />
                  Back to Inventory
                </Button>
              </Link>
            </div>
          </Alert>
        </Card>
      </div>
    );
  }

  const defaultUom = getDefaultUom();

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">{product.name}</h1>
            <p className="text-gray-500">
              {product.sku && `SKU: ${product.sku}`}
              {product.barcode && ` | Barcode: ${product.barcode}`}
            </p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Link to="/inventory">
              <Button color="light">
                <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
                Back to Inventory
              </Button>
            </Link>
            <Button color="light" onClick={handleRefresh}>
              <HiOutlineRefresh className="mr-2 h-5 w-5" />
              Refresh
            </Button>
          </div>
        </div>
      </Card>

      <Card>
        <div className="mb-4 border-b border-gray-200">
          <ul className="flex flex-wrap -mb-px text-sm font-medium text-center" role="tablist">
            <li className="mr-2" role="presentation">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${
                  activeTab === 'details'
                    ? 'text-blue-600 border-blue-600'
                    : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                }`}
                type="button"
                role="tab"
                onClick={() => setActiveTab('details')}
              >
                <div className="flex items-center">
                  <HiOutlineAdjustments className="mr-2 h-5 w-5" />
                  Product Details
                </div>
              </button>
            </li>
            <li className="mr-2" role="presentation">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${
                  activeTab === 'chart'
                    ? 'text-blue-600 border-blue-600'
                    : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                }`}
                type="button"
                role="tab"
                onClick={() => setActiveTab('chart')}
              >
                <div className="flex items-center">
                  <HiOutlineChartBar className="mr-2 h-5 w-5" />
                  Inventory Movement
                </div>
              </button>
            </li>
            <li className="mr-2" role="presentation">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${
                  activeTab === 'transactions'
                    ? 'text-blue-600 border-blue-600'
                    : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                }`}
                type="button"
                role="tab"
                onClick={() => setActiveTab('transactions')}
              >
                <div className="flex items-center">
                  <HiOutlineDocumentDownload className="mr-2 h-5 w-5" />
                  Transaction History
                </div>
              </button>
            </li>
          </ul>
        </div>

        {activeTab === 'details' && (
          <div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h5 className="text-xl font-bold mb-4">Inventory Information</h5>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500">Current Stock</p>
                    <p className="text-xl font-bold">
                      {formatQuantity(product.stock_quantity || 0, defaultUom?.code)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    {getStockStatusBadge(product)}
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Reorder Point</p>
                    <p>{formatQuantity(product.min_stock_level || 0, defaultUom?.code)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Inventory Value</p>
                    <p>{formatWithCurrency(calculateInventoryValue())}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h5 className="text-xl font-bold mb-4">Product Information</h5>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500">Category</p>
                    <p>{product.category?.name || 'Uncategorized'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Unit Price</p>
                    <p>{formatWithCurrency(product.unit_price || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Cost Price</p>
                    <p>{formatWithCurrency(product.cost_price || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <Badge color={product.is_active ? 'success' : 'gray'}>
                      {product.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h5 className="text-xl font-bold mb-4">Units of Measurement</h5>
                {product.product_uoms && product.product_uoms.length > 0 ? (
                  <div className="space-y-4">
                    {product.product_uoms.map((pu: any) => (
                      <div key={pu.id} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex justify-between">
                          <p className="font-medium">{pu.uom.name}</p>
                          {pu.is_default && (
                            <Badge color="blue">Default</Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-500">Code: {pu.uom.code}</p>
                        <p className="text-sm text-gray-500">
                          Conversion Factor: {pu.conversion_factor}
                        </p>
                        <div className="flex gap-2 mt-2">
                          {pu.is_purchasing_unit && (
                            <Badge color="purple" size="sm">Purchasing</Badge>
                          )}
                          {pu.is_selling_unit && (
                            <Badge color="green" size="sm">Selling</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No units of measurement defined</p>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
              <h5 className="text-xl font-bold mb-4">Product Description</h5>
              <p className="whitespace-pre-line">
                {product.description || 'No description available'}
              </p>
            </div>
          </div>
        )}

        {activeTab === 'chart' && (
          <div className="mb-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
              <h5 className="text-xl font-bold">Inventory Movement Chart</h5>
            </div>

            {transactionsLoading ? (
              <div className="flex justify-center items-center py-8">
                <Spinner size="xl" />
              </div>
            ) : transactionsError ? (
              <Alert color="failure" icon={HiOutlineExclamation}>
                {transactionsError}
              </Alert>
            ) : transactions.length === 0 ? (
              <div className="p-8 text-center">
                <p className="text-gray-500">No transactions found for this product</p>
              </div>
            ) : (
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="h-96">
                  {prepareChartData() && (
                    <Bar
                      options={chartOptions}
                      data={prepareChartData() || {labels: [], datasets: []}}
                    />
                  )}
                </div>
                <div className="mt-4 text-sm text-gray-500 text-center">
                  <p>This chart shows the inventory movement over time, including purchases, sales, and adjustments.</p>
                  <p>The line represents the running total inventory level.</p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'transactions' && (
          <div className="mb-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
              <h5 className="text-xl font-bold">Transaction History</h5>
              <Button color="light" onClick={() => window.print()}>
                <HiOutlineDocumentDownload className="mr-2 h-5 w-5" />
                Export
              </Button>
            </div>

            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <TextInput
                  id="search"
                  type="text"
                  placeholder="Search transactions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  icon={HiOutlineSearch}
                />
              </div>
              <div className="w-full md:w-48">
                <Select
                  id="transactionType"
                  value={transactionType}
                  onChange={(e) => setTransactionType(e.target.value)}
                >
                  <option value="">All Types</option>
                  <option value="purchase">Purchases</option>
                  <option value="receipt">Receipts</option>
                  <option value="sale">Sales</option>
                  <option value="adjustment">Adjustments</option>
                  <option value="transfer">Transfers</option>
                  <option value="return">Returns</option>
                </Select>
              </div>
              <div className="w-full md:w-48">
                <Datepicker
                  value={startDate ? formatDate(startDate.toISOString()) : ''}
                  onSelectedDateChanged={setStartDate}
                  placeholder="Start Date"
                />
              </div>
              <div className="w-full md:w-48">
                <Datepicker
                  value={endDate ? formatDate(endDate.toISOString()) : ''}
                  onSelectedDateChanged={setEndDate}
                  placeholder="End Date"
                />
              </div>
              <div className="flex gap-2">
                <Button color="primary" onClick={handleFilter}>
                  <HiOutlineFilter className="mr-2 h-5 w-5" />
                  Filter
                </Button>
                <Button color="gray" onClick={handleReset}>
                  <HiOutlineRefresh className="mr-2 h-5 w-5" />
                  Reset
                </Button>
              </div>
            </div>

            {transactionsLoading ? (
              <div className="flex justify-center items-center py-8">
                <Spinner size="xl" />
              </div>
            ) : transactionsError ? (
              <Alert color="failure" icon={HiOutlineExclamation}>
                {transactionsError}
              </Alert>
            ) : transactions.length === 0 ? (
              <div className="p-8 text-center">
                <p className="text-gray-500">No transactions found for this product</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table hoverable>
                  <Table.Head>
                    <Table.HeadCell>Date/Time</Table.HeadCell>
                    <Table.HeadCell>Type</Table.HeadCell>
                    <Table.HeadCell>Quantity</Table.HeadCell>
                    <Table.HeadCell>Reference</Table.HeadCell>
                    <Table.HeadCell>Notes</Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {transactions.map((transaction) => (
                      <Table.Row key={transaction.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                        <Table.Cell>
                          {formatDateTime(transaction.created_at)}
                        </Table.Cell>
                        <Table.Cell>
                          {getTransactionTypeBadge(transaction.transaction_type)}
                        </Table.Cell>
                        <Table.Cell>
                          {transaction.quantity}
                        </Table.Cell>
                        <Table.Cell>
                          {transaction.reference_type && transaction.reference_id ? (
                            <span className="text-sm">
                              {transaction.reference_type.replace(/_/g, ' ')}
                            </span>
                          ) : (
                            <span className="text-gray-500">-</span>
                          )}
                        </Table.Cell>
                        <Table.Cell>
                          <div className="max-w-xs truncate">
                            {transaction.notes || '-'}
                          </div>
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
            )}
          </div>
        )}
      </Card>
    </div>
  );
};

export default InventoryDetails;
