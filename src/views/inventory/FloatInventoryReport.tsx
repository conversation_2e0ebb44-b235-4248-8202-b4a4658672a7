import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Spin<PERSON>,
  <PERSON><PERSON>,
  TextInput,
  Select
} from 'flowbite-react';
import {
  HiOutlineDocumentReport,
  HiOutlineExclamation,
  HiOutlineCalendar,
  HiOutlineCube,
  HiOutlineBell
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import {
  getFloatInventoryReport,
  getFloatInventorySummaryByProduct,
  getFloatInventorySummaryByDate,
  FloatInventoryReport as FloatReport,
  FloatInventorySummaryByProduct,
  FloatInventorySummaryByDate
} from '../../services/floatInventory';
import { DataTable } from '../../components/DataTable';
import { LineChart, BarChart } from '../../components/Charts';

const FloatInventoryReport: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<string>('detailed');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [resolvedFilter, setResolvedFilter] = useState<string>('all');

  // Data states
  const [detailedReport, setDetailedReport] = useState<FloatReport[]>([]);
  const [productSummary, setProductSummary] = useState<FloatInventorySummaryByProduct[]>([]);
  const [dateSummary, setDateSummary] = useState<FloatInventorySummaryByDate[]>([]);
  const [products, setProducts] = useState<any[]>([]);

  // Alert states
  const [showAlerts, setShowAlerts] = useState<boolean>(false);
  const [alertThreshold, setAlertThreshold] = useState<number>(7); // Days
  const [alertItems, setAlertItems] = useState<FloatReport[]>([]);

  // Load products for filter dropdown
  useEffect(() => {
    const loadProducts = async () => {
      if (currentOrganization?.id) {
        // For now, we'll skip loading products since we don't have the service
        // You can implement this later when the products service is available
        setProducts([]);
      }
    };

    loadProducts();
  }, [currentOrganization]);

  // Load report data based on filters
  useEffect(() => {
    const loadReportData = async () => {
      if (!currentOrganization?.id) return;

      setLoading(true);
      setError(null);

      try {
        // Format dates for API
        const formattedStartDate = startDate ? startDate.toISOString() : undefined;
        const formattedEndDate = endDate ? endDate.toISOString() : undefined;

        // Determine resolved filter value
        let resolvedValue: boolean | undefined;
        if (resolvedFilter === 'resolved') resolvedValue = true;
        else if (resolvedFilter === 'unresolved') resolvedValue = false;

        // Load detailed report
        const { report, error: reportError } = await getFloatInventoryReport(
          currentOrganization.id,
          {
            startDate: formattedStartDate,
            endDate: formattedEndDate,
            productId: selectedProduct || undefined,
            resolved: resolvedValue
          }
        );

        if (reportError) throw new Error(reportError);
        setDetailedReport(report);

        // Check for alerts
        const unresolvedItems = report.filter(item => !item.resolved);
        const alertableItems = unresolvedItems.filter(
          item => item.days_unresolved && item.days_unresolved >= alertThreshold
        );
        setAlertItems(alertableItems);

        // Load product summary if on that tab
        if (activeTab === 'product') {
          const { summary, error: summaryError } = await getFloatInventorySummaryByProduct(
            currentOrganization.id,
            {
              startDate: formattedStartDate,
              endDate: formattedEndDate
            }
          );

          if (summaryError) throw new Error(summaryError);
          setProductSummary(summary);
        }

        // Load date summary if on that tab
        if (activeTab === 'date') {
          const { summary, error: dateError } = await getFloatInventorySummaryByDate(
            currentOrganization.id,
            {
              startDate: formattedStartDate,
              endDate: formattedEndDate
            }
          );

          if (dateError) throw new Error(dateError);
          setDateSummary(summary);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to load report data');
      } finally {
        setLoading(false);
      }
    };

    loadReportData();
  }, [currentOrganization, activeTab, startDate, endDate, selectedProduct, resolvedFilter, alertThreshold]);

  // Prepare data for charts
  const productChartData = {
    labels: productSummary.slice(0, 10).map(item => item.product_name),
    datasets: [
      {
        label: 'Unresolved Quantity',
        data: productSummary.slice(0, 10).map(item => item.unresolved_float_quantity),
        backgroundColor: 'rgba(239, 68, 68, 0.5)',
        borderColor: 'rgb(239, 68, 68)',
      },
      {
        label: 'Resolved Quantity',
        data: productSummary.slice(0, 10).map(item => item.resolved_float_quantity),
        backgroundColor: 'rgba(34, 197, 94, 0.5)',
        borderColor: 'rgb(34, 197, 94)',
      }
    ]
  };

  const dateChartData = {
    labels: dateSummary.slice(0, 30).map(item => new Date(item.date_group).toLocaleDateString()),
    datasets: [
      {
        label: 'Total Float Quantity',
        data: dateSummary.slice(0, 30).map(item => item.total_float_quantity),
        borderColor: 'rgb(59, 130, 246)',
        tension: 0.1,
        fill: false
      }
    ]
  };

  // Column definitions for tables
  const detailedColumns = [
    {
      name: 'Product',
      selector: (row: FloatReport) => (
        <Link
          to={`/products/details/${row.product_id}`}
          className="text-blue-600 hover:underline"
        >
          {row.product_name}
        </Link>
      )
    },
    { name: 'SKU', selector: (row: FloatReport) => row.product_sku || 'N/A' },
    { name: 'Quantity', selector: (row: FloatReport) => row.quantity },
    {
      name: 'Sale #',
      selector: (row: FloatReport) => (
        <Link
          to={`/sales/details/${row.sale_id}`}
          className="text-blue-600 hover:underline"
        >
          {row.sale_number}
        </Link>
      )
    },
    { name: 'Sale Date', selector: (row: FloatReport) => new Date(row.sale_date).toLocaleDateString() },
    { name: 'Customer', selector: (row: FloatReport) => row.customer_name || 'N/A' },
    {
      name: 'Status',
      selector: (row: FloatReport) => (
        <Badge color={row.resolved ? 'success' : 'warning'}>
          {row.resolved ? 'Resolved' : 'Unresolved'}
        </Badge>
      )
    },
    {
      name: 'Days',
      selector: (row: FloatReport) => (
        row.resolved
          ? `Resolved in ${row.days_to_resolve} days`
          : `Unresolved for ${row.days_unresolved} days`
      )
    },
    {
      name: 'Actions',
      selector: (row: FloatReport) => (
        <div className="flex space-x-2">
          <Button
            size="xs"
            color="light"
            onClick={() => navigate(`/inventory/float/${row.id}`)}
          >
            View
          </Button>
          {!row.resolved && (
            <Button
              size="xs"
              color="success"
              onClick={() => navigate(`/inventory/float/${row.id}/resolve`)}
            >
              Resolve
            </Button>
          )}
        </div>
      )
    }
  ];

  const productSummaryColumns = [
    {
      name: 'Product',
      selector: (row: FloatInventorySummaryByProduct) => (
        <Link
          to={`/products/details/${row.product_id}`}
          className="text-blue-600 hover:underline"
        >
          {row.product_name}
        </Link>
      )
    },
    { name: 'SKU', selector: (row: FloatInventorySummaryByProduct) => row.product_sku || 'N/A' },
    { name: 'Total Quantity', selector: (row: FloatInventorySummaryByProduct) => row.total_float_quantity },
    { name: 'Unresolved', selector: (row: FloatInventorySummaryByProduct) => row.unresolved_float_quantity },
    { name: 'Resolved', selector: (row: FloatInventorySummaryByProduct) => row.resolved_float_quantity },
    {
      name: 'Resolution Rate',
      selector: (row: FloatInventorySummaryByProduct) => (
        <Badge color={row.resolution_rate > 80 ? 'success' : row.resolution_rate > 50 ? 'warning' : 'failure'}>
          {row.resolution_rate}%
        </Badge>
      )
    },
    {
      name: 'Avg Days to Resolve',
      selector: (row: FloatInventorySummaryByProduct) => (
        row.avg_days_to_resolve ? row.avg_days_to_resolve.toFixed(1) : 'N/A'
      )
    },
    {
      name: 'Oldest Unresolved',
      selector: (row: FloatInventorySummaryByProduct) => (
        row.oldest_unresolved_days
          ? `${row.oldest_unresolved_days} days ago`
          : 'None'
      )
    }
  ];

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold flex items-center">
          <HiOutlineDocumentReport className="mr-2 h-8 w-8" />
          Float Inventory Reports
        </h1>

        <div className="flex space-x-2">
          <Button
            color={showAlerts ? 'warning' : 'light'}
            onClick={() => setShowAlerts(!showAlerts)}
          >
            <HiOutlineBell className="mr-2 h-5 w-5" />
            {alertItems.length > 0 && (
              <Badge color="failure" className="ml-2">
                {alertItems.length}
              </Badge>
            )}
            Alerts
          </Button>

          <Button
            color="primary"
            onClick={() => navigate('/inventory/float')}
          >
            <HiOutlineExclamation className="mr-2 h-5 w-5" />
            Float Inventory
          </Button>
        </div>
      </div>

      {/* Alerts Section */}
      {showAlerts && alertItems.length > 0 && (
        <Alert color="warning" className="mb-4">
          <div className="font-medium mb-2">
            {alertItems.length} items have been unresolved for {alertThreshold}+ days
          </div>
          <ul className="list-disc pl-5 space-y-1">
            {alertItems.slice(0, 5).map(item => (
              <li key={item.id}>
                {item.product_name} - {item.quantity} units - Unresolved for {item.days_unresolved} days
              </li>
            ))}
            {alertItems.length > 5 && (
              <li>And {alertItems.length - 5} more items...</li>
            )}
          </ul>
          <div className="mt-2">
            <label className="mr-2">Alert threshold:</label>
            <Select
              value={alertThreshold}
              onChange={(e) => setAlertThreshold(parseInt(e.target.value))}
              className="inline-block w-24"
            >
              <option value="3">3 days</option>
              <option value="7">7 days</option>
              <option value="14">14 days</option>
              <option value="30">30 days</option>
            </Select>
          </div>
        </Alert>
      )}

      {/* Filters Section */}
      <Card className="mb-6">
        <h2 className="text-lg font-semibold mb-4">Filters</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block mb-2">Date Range Start</label>
            <TextInput
              type="date"
              value={startDate ? startDate.toISOString().split('T')[0] : ''}
              onChange={(e) => setStartDate(e.target.value ? new Date(e.target.value) : null)}
              className="w-full"
            />
          </div>
          <div>
            <label className="block mb-2">Date Range End</label>
            <TextInput
              type="date"
              value={endDate ? endDate.toISOString().split('T')[0] : ''}
              onChange={(e) => setEndDate(e.target.value ? new Date(e.target.value) : null)}
              className="w-full"
            />
          </div>
          <div>
            <label className="block mb-2">Product</label>
            <Select
              value={selectedProduct || ''}
              onChange={(e) => setSelectedProduct(e.target.value || null)}
              className="w-full"
            >
              <option value="">All Products</option>
              {products.map(product => (
                <option key={product.id} value={product.id}>
                  {product.name}
                </option>
              ))}
            </Select>
          </div>
          <div>
            <label className="block mb-2">Status</label>
            <Select
              value={resolvedFilter}
              onChange={(e) => setResolvedFilter(e.target.value)}
              className="w-full"
            >
              <option value="all">All</option>
              <option value="resolved">Resolved</option>
              <option value="unresolved">Unresolved</option>
            </Select>
          </div>
        </div>
      </Card>

      {/* Tabs Section */}
      <Tabs
        onActiveTabChange={(tab: number) => setActiveTab(['detailed', 'product', 'date'][tab])}
      >
        <Tabs.Item
          title="Detailed Report"
          icon={HiOutlineDocumentReport}
          active={activeTab === 'detailed'}
        >
          {loading ? (
            <div className="flex justify-center p-8">
              <Spinner size="xl" />
            </div>
          ) : error ? (
            <Alert color="failure">{error}</Alert>
          ) : detailedReport.length === 0 ? (
            <Alert color="info">No float inventory items found with the current filters.</Alert>
          ) : (
            <DataTable
              columns={detailedColumns}
              data={detailedReport}
              pagination
            />
          )}
        </Tabs.Item>

        <Tabs.Item
          title="Product Summary"
          icon={HiOutlineCube}
          active={activeTab === 'product'}
        >
          {loading ? (
            <div className="flex justify-center p-8">
              <Spinner size="xl" />
            </div>
          ) : error ? (
            <Alert color="failure">{error}</Alert>
          ) : productSummary.length === 0 ? (
            <Alert color="info">No product summary data found with the current filters.</Alert>
          ) : (
            <>
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-4">Top 10 Products with Float Inventory</h3>
                <div className="h-80">
                  <BarChart data={productChartData} />
                </div>
              </div>

              <DataTable
                columns={productSummaryColumns}
                data={productSummary}
                pagination
              />
            </>
          )}
        </Tabs.Item>

        <Tabs.Item
          title="Date Trends"
          icon={HiOutlineCalendar}
          active={activeTab === 'date'}
        >
          {loading ? (
            <div className="flex justify-center p-8">
              <Spinner size="xl" />
            </div>
          ) : error ? (
            <Alert color="failure">{error}</Alert>
          ) : dateSummary.length === 0 ? (
            <Alert color="info">No date trend data found with the current filters.</Alert>
          ) : (
            <>
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-4">Float Inventory Trends Over Time</h3>
                <div className="h-80">
                  <LineChart data={dateChartData} />
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left text-gray-500">
                  <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                    <tr>
                      <th className="px-6 py-3">Date</th>
                      <th className="px-6 py-3">Total Quantity</th>
                      <th className="px-6 py-3">Resolved</th>
                      <th className="px-6 py-3">Unresolved</th>
                      <th className="px-6 py-3">Resolution Rate</th>
                    </tr>
                  </thead>
                  <tbody>
                    {dateSummary.map((item, index) => (
                      <tr key={index} className="bg-white border-b">
                        <td className="px-6 py-4">{new Date(item.date_group).toLocaleDateString()}</td>
                        <td className="px-6 py-4">{item.total_float_quantity}</td>
                        <td className="px-6 py-4">{item.resolved_float_quantity}</td>
                        <td className="px-6 py-4">{item.unresolved_float_quantity}</td>
                        <td className="px-6 py-4">
                          <Badge color={item.resolution_rate > 80 ? 'success' : item.resolution_rate > 50 ? 'warning' : 'failure'}>
                            {item.resolution_rate}%
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </Tabs.Item>
      </Tabs>
    </div>
  );
};

export default FloatInventoryReport;
