import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  But<PERSON>,
  Spinner,
  <PERSON>ert,
  TextInput,
  Dropdown,
  Pagination,
  Badge,
  Select,
  Datepicker
} from "flowbite-react";
import { Link } from 'react-router-dom';
import {
  HiOutlineSearch,
  HiOutlineFilter,
  HiOutlineRefresh,
  HiOutlineExclamation,
  HiOutlineCalendar,
  HiOutlineDocumentDownload,
  HiOutlineChevronLeft,
  HiOutlineChevronRight
} from "react-icons/hi";
import { useOrganization } from '../../context/OrganizationContext';
import { getInventoryTransactions } from '../../services/inventoryTransaction';
import { formatDate, formatDateTime } from '../../utils/formatters';

const InventoryTransactions = () => {
  const { currentOrganization } = useOrganization();
  const [transactions, setTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [transactionType, setTransactionType] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    if (currentOrganization) {
      fetchTransactions();
    }
  }, [currentOrganization, currentPage, itemsPerPage, sortBy, sortOrder]);

  const fetchTransactions = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const options = {
        limit: itemsPerPage,
        offset: (currentPage - 1) * itemsPerPage,
        sortBy,
        sortOrder,
        transactionType: transactionType || undefined,
        startDate: startDate ? startDate.toISOString() : undefined,
        endDate: endDate ? endDate.toISOString() : undefined,
        searchQuery: searchQuery || undefined
      };

      const { transactions: fetchedTransactions, count, error: fetchError } =
        await getInventoryTransactions(currentOrganization.id, options);

      if (fetchError) {
        setError(fetchError);
      } else {
        setTransactions(fetchedTransactions);
        setTotalCount(count);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching transactions');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
    fetchTransactions();
  };

  const handleReset = () => {
    setSearchQuery('');
    setTransactionType('');
    setStartDate(null);
    setEndDate(null);
    setSortBy('created_at');
    setSortOrder('desc');
    setCurrentPage(1);
    fetchTransactions();
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const getTransactionTypeBadge = (type: string) => {
    switch (type.toLowerCase()) {
      case 'purchase':
      case 'receipt':
        return <Badge color="success">Purchase</Badge>;
      case 'sale':
        return <Badge color="info">Sale</Badge>;
      case 'adjustment':
        return <Badge color="warning">Adjustment</Badge>;
      case 'transfer':
        return <Badge color="purple">Transfer</Badge>;
      case 'return':
        return <Badge color="pink">Return</Badge>;
      default:
        return <Badge color="gray">{type}</Badge>;
    }
  };

  const formatQuantity = (quantity: number, uom: any) => {
    return `${quantity} ${uom?.code || ''}`;
  };

  const totalPages = Math.ceil(totalCount / itemsPerPage);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Inventory Transactions</h1>
            <p className="text-gray-500">
              View all inventory transactions including purchases, sales, adjustments, and transfers.
            </p>
          </div>
          <div className="flex gap-2">
            <Link to="/inventory">
              <Button color="light">
                Back to Inventory
              </Button>
            </Link>
            <Button color="light" onClick={() => window.print()}>
              <HiOutlineDocumentDownload className="mr-2 h-5 w-5" />
              Export
            </Button>
          </div>
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <TextInput
              id="search"
              type="text"
              placeholder="Search by product name or SKU..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={HiOutlineSearch}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          <div className="w-full md:w-48">
            <Select
              id="transactionType"
              value={transactionType}
              onChange={(e) => setTransactionType(e.target.value)}
            >
              <option value="">All Types</option>
              <option value="purchase">Purchases</option>
              <option value="receipt">Receipts</option>
              <option value="sale">Sales</option>
              <option value="adjustment">Adjustments</option>
              <option value="transfer">Transfers</option>
              <option value="return">Returns</option>
            </Select>
          </div>
          <div className="w-full md:w-48">
            <Datepicker
              value={startDate ? formatDate(startDate.toISOString()) : ''}
              onSelectedDateChanged={setStartDate}
              placeholder="Start Date"
            />
          </div>
          <div className="w-full md:w-48">
            <Datepicker
              value={endDate ? formatDate(endDate.toISOString()) : ''}
              onSelectedDateChanged={setEndDate}
              placeholder="End Date"
            />
          </div>
          <div className="flex gap-2">
            <Button color="primary" onClick={handleSearch}>
              <HiOutlineFilter className="mr-2 h-5 w-5" />
              Filter
            </Button>
            <Button color="gray" onClick={handleReset}>
              <HiOutlineRefresh className="mr-2 h-5 w-5" />
              Reset
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Spinner size="xl" />
          </div>
        ) : transactions.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 mb-4">No transactions found</p>
            <Button color="light" onClick={handleReset}>
              <HiOutlineRefresh className="mr-2 h-5 w-5" />
              Reset Filters
            </Button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell
                  className="cursor-pointer"
                  onClick={() => handleSort('created_at')}
                >
                  <div className="flex items-center">
                    Date/Time
                    {sortBy === 'created_at' && (
                      sortOrder === 'asc' ?
                        <HiOutlineChevronRight className="ml-1" /> :
                        <HiOutlineChevronLeft className="ml-1" />
                    )}
                  </div>
                </Table.HeadCell>
                <Table.HeadCell
                  className="cursor-pointer"
                  onClick={() => handleSort('product.name')}
                >
                  <div className="flex items-center">
                    Product
                    {sortBy === 'product.name' && (
                      sortOrder === 'asc' ?
                        <HiOutlineChevronRight className="ml-1" /> :
                        <HiOutlineChevronLeft className="ml-1" />
                    )}
                  </div>
                </Table.HeadCell>
                <Table.HeadCell>Type</Table.HeadCell>
                <Table.HeadCell
                  className="cursor-pointer"
                  onClick={() => handleSort('quantity')}
                >
                  <div className="flex items-center">
                    Quantity
                    {sortBy === 'quantity' && (
                      sortOrder === 'asc' ?
                        <HiOutlineChevronRight className="ml-1" /> :
                        <HiOutlineChevronLeft className="ml-1" />
                    )}
                  </div>
                </Table.HeadCell>
                <Table.HeadCell>Reference</Table.HeadCell>
                <Table.HeadCell>Notes</Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {transactions.map((transaction) => (
                  <Table.Row key={transaction.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                    <Table.Cell>
                      <Link to={`/inventory/transactions/${transaction.id}`} className="text-blue-600 hover:underline">
                        {formatDateTime(transaction.created_at)}
                      </Link>
                    </Table.Cell>
                    <Table.Cell>
                      <div>
                        <div className="font-medium">
                          {transaction.product_id ? (
                            <Link to={`/inventory/details/${transaction.product_id}`} className="text-blue-600 hover:underline">
                              {transaction.product?.name || 'Unknown Product'}
                            </Link>
                          ) : (
                            transaction.product?.name || 'Unknown Product'
                          )}
                        </div>
                        {transaction.product?.sku && (
                          <div className="text-xs text-gray-500">
                            SKU: {transaction.product.sku}
                          </div>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {getTransactionTypeBadge(transaction.transaction_type)}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.quantity}
                    </Table.Cell>
                    <Table.Cell>
                      {transaction.reference_type && transaction.reference_id ? (
                        <span className="text-sm">
                          {transaction.reference_type.replace(/_/g, ' ')}
                        </span>
                      ) : (
                        <span className="text-gray-500">-</span>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="max-w-xs truncate">
                        {transaction.notes || '-'}
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        {/* Pagination */}
        {!loading && transactions.length > 0 && (
          <div className="flex flex-col md:flex-row items-center justify-between mt-4">
            <div className="flex items-center gap-2 mb-2 md:mb-0">
              <span className="text-sm text-gray-500">Items per page:</span>
              <Select
                id="itemsPerPage"
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value));
                  setCurrentPage(1); // Reset to first page when changing items per page
                }}
                className="w-20"
                size="sm"
              >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </Select>
              <span className="text-sm text-gray-500">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, totalCount)} to {Math.min(currentPage * itemsPerPage, totalCount)} of {totalCount} results
              </span>
            </div>
            <div className="flex mt-2 md:mt-0">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                showIcons
              />
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default InventoryTransactions;
