import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ge
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineExclamation,
  HiOutlineDocumentDownload
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getInventoryTransactionById } from '../../services/inventoryTransaction';
import { formatDateTime } from '../../utils/formatters';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

const TransactionDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  // Transaction state
  const [transaction, setTransaction] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (currentOrganization && id) {
      fetchTransaction();
    }
  }, [currentOrganization, id]);

  const fetchTransaction = async () => {
    if (!currentOrganization || !id) return;

    setLoading(true);
    setError(null);

    try {
      const { transaction: transactionData, error: transactionError } = await getInventoryTransactionById(
        currentOrganization.id,
        id
      );

      if (transactionError) {
        setError(transactionError);
      } else if (transactionData) {
        setTransaction(transactionData);
      } else {
        setError('Transaction not found');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching the transaction');
    } finally {
      setLoading(false);
    }
  };

  const getTransactionTypeBadge = (type: string) => {
    switch (type.toLowerCase()) {
      case 'purchase':
      case 'receipt':
        return <Badge color="success">Purchase</Badge>;
      case 'sale':
        return <Badge color="info">Sale</Badge>;
      case 'adjustment':
        return <Badge color="warning">Adjustment</Badge>;
      case 'transfer':
        return <Badge color="purple">Transfer</Badge>;
      case 'return':
        return <Badge color="pink">Return</Badge>;
      default:
        return <Badge color="gray">{type}</Badge>;
    }
  };

  const handleExport = () => {
    if (!transaction) return;

    // Create CSV content
    const csvContent = [
      ['Transaction ID', 'Date/Time', 'Type', 'Product', 'Quantity', 'Reference', 'Notes'],
      [
        transaction.id,
        formatDateTime(transaction.created_at),
        transaction.transaction_type,
        transaction.product?.name || 'Unknown Product',
        transaction.quantity,
        transaction.reference_type && transaction.reference_id
          ? `${transaction.reference_type.replace(/_/g, ' ')} (${transaction.reference_id})`
          : '-',
        transaction.notes || '-'
      ]
    ].map(row => row.join(',')).join('\n');

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `transaction_${transaction.id}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        </Card>
      </div>
    );
  }

  if (error || !transaction) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">Error</h3>
            <p>{error || 'Transaction not found'}</p>
            <div className="mt-4">
              <Link to="/inventory/transactions">
                <Button color="gray" size="sm">
                  <HiOutlineArrowLeft className="mr-2 h-4 w-4" />
                  Back to Transactions
                </Button>
              </Link>
            </div>
          </Alert>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Transaction Details</h1>
            <p className="text-gray-500">
              ID: {transaction.id}
            </p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Link to="/inventory/transactions">
              <Button color="light">
                <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
                Back to Transactions
              </Button>
            </Link>
            <Button color="light" onClick={handleExport}>
              <HiOutlineDocumentDownload className="mr-2 h-5 w-5" />
              Export CSV
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h5 className="text-xl font-bold mb-4">Transaction Information</h5>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Date/Time</p>
                <p className="font-medium">{formatDateTime(transaction.created_at)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Type</p>
                <div>{getTransactionTypeBadge(transaction.transaction_type)}</div>
              </div>
              <div>
                <p className="text-sm text-gray-500">Quantity</p>
                <p className="font-medium">{transaction.quantity}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Reference</p>
                <p>
                  {transaction.reference_type && transaction.reference_id
                    ? `${transaction.reference_type.replace(/_/g, ' ')} (${transaction.reference_id})`
                    : '-'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h5 className="text-xl font-bold mb-4">Product Information</h5>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Product</p>
                {transaction.product ? (
                  <Link to={`/inventory/details/${transaction.product_id}`} className="text-blue-600 hover:underline">
                    {transaction.product.name}
                  </Link>
                ) : (
                  <p>Unknown Product</p>
                )}
              </div>
              {transaction.product?.sku && (
                <div>
                  <p className="text-sm text-gray-500">SKU</p>
                  <p>{transaction.product.sku}</p>
                </div>
              )}
              {transaction.uom && (
                <div>
                  <p className="text-sm text-gray-500">Unit of Measure</p>
                  <p>{transaction.uom.name} ({transaction.uom.code})</p>
                </div>
              )}
              {transaction.product?.cost_price && (
                <div>
                  <p className="text-sm text-gray-500">Cost Price</p>
                  <p>{formatWithCurrency(transaction.product.cost_price)}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h5 className="text-xl font-bold mb-4">Notes</h5>
          <p className="whitespace-pre-line">
            {transaction.notes || 'No notes available'}
          </p>
        </div>

        <div className="mt-6 text-sm text-gray-500">
          <p>Created: {formatDateTime(transaction.created_at)}</p>
          {transaction.updated_at && transaction.updated_at !== transaction.created_at && (
            <p>Last Updated: {formatDateTime(transaction.updated_at)}</p>
          )}
        </div>
      </Card>
    </div>
  );
};

export default TransactionDetails;
