import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>, Spinner, Table, Badge, Button } from 'flowbite-react';
import { HiOutlineTag, HiOutlineExclamation, HiOutlineArrowLeft } from 'react-icons/hi';
import { findEntitiesByTag, getTagById } from '../../services/tagService';
import { getProductById } from '../../services/product';
import { getCustomerById } from '../../services/customer';
import { getSupplierById } from '../../services/supplier';
import { useOrganization } from '../../context/OrganizationContext';
import { Tag, TaggableEntityType } from '../../types/tagging.types';

interface TaggedEntity {
  id: string;
  name: string;
  type: TaggableEntityType;
  detailsUrl: string;
}

const TaggedItemsView: React.FC = () => {
  const { tagId } = useParams<{ tagId: string }>();
  const { currentOrganization } = useOrganization();
  const navigate = useNavigate();

  const [tag, setTag] = useState<Tag | null>(null);
  const [entities, setEntities] = useState<TaggedEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'products' | 'customers' | 'suppliers'>('all');

  // Fetch tag details
  useEffect(() => {
    const fetchTagDetails = async () => {
      if (!tagId) return;

      try {
        const { tag, error } = await getTagById(tagId);

        if (error) {
          console.error('Error fetching tag details:', error);
        } else if (tag) {
          setTag(tag);
        }
      } catch (err) {
        console.error('Error in fetchTagDetails:', err);
      }
    };

    fetchTagDetails();
  }, [tagId]);

  useEffect(() => {
    const fetchTaggedEntities = async () => {
      if (!tagId || !currentOrganization) return;

      setLoading(true);
      setError(null);

      try {
        // Find all entities with this tag
        const { entities: taggedEntities, error: findError } = await findEntitiesByTag(tagId);

        if (findError) {
          setError(findError);
          setLoading(false);
          return;
        }

        if (!taggedEntities || taggedEntities.length === 0) {
          setEntities([]);
          setLoading(false);
          return;
        }

        // Fetch details for each entity
        const entityDetails: TaggedEntity[] = [];

        for (const entity of taggedEntities) {
          try {
            if (entity.entity_type === 'product') {
              const { product } = await getProductById(currentOrganization.id, entity.entity_id);
              if (product) {
                entityDetails.push({
                  id: product.id,
                  name: product.name,
                  type: 'product',
                  detailsUrl: `/products/details/${product.id}`
                });
              }
            } else if (entity.entity_type === 'customer') {
              const { customer } = await getCustomerById(currentOrganization.id, entity.entity_id);
              if (customer) {
                entityDetails.push({
                  id: customer.id,
                  name: customer.name,
                  type: 'customer',
                  detailsUrl: `/customers/details/${customer.id}`
                });
              }
            } else if (entity.entity_type === 'supplier') {
              const { supplier } = await getSupplierById(currentOrganization.id, entity.entity_id);
              if (supplier) {
                entityDetails.push({
                  id: supplier.id,
                  name: supplier.name,
                  type: 'supplier',
                  detailsUrl: `/suppliers/${supplier.id}`
                });
              }
            }
          } catch (err) {
            console.error(`Error fetching details for ${entity.entity_type} ${entity.entity_id}:`, err);
          }
        }

        setEntities(entityDetails);
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching tagged entities');
      } finally {
        setLoading(false);
      }
    };

    fetchTaggedEntities();
  }, [tagId, currentOrganization]);

  // Filter entities based on active tab
  const filteredEntities = activeTab === 'all'
    ? entities
    : entities.filter(entity => {
        if (activeTab === 'products') return entity.type === 'product';
        if (activeTab === 'customers') return entity.type === 'customer';
        if (activeTab === 'suppliers') return entity.type === 'supplier';
        return false;
      });

  const getEntityTypeLabel = (type: TaggableEntityType) => {
    switch (type) {
      case 'product': return 'Product';
      case 'customer': return 'Customer';
      case 'supplier': return 'Supplier';
      case 'purchase_request': return 'Purchase Request';
      case 'purchase_order': return 'Purchase Order';
      default: return type;
    }
  };

  const getEntityTypeColor = (type: TaggableEntityType) => {
    switch (type) {
      case 'product': return 'blue';
      case 'customer': return 'green';
      case 'supplier': return 'purple';
      case 'purchase_request': return 'yellow';
      case 'purchase_order': return 'red';
      default: return 'gray';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <HiOutlineTag className="h-6 w-6 mr-2 text-blue-500" />
              <h1 className="text-2xl font-bold">Tagged Items</h1>
            </div>
            <Button color="light" size="sm" onClick={() => navigate('/settings/tags')}>
              <HiOutlineArrowLeft className="mr-2 h-4 w-4" />
              Back to Tags
            </Button>
          </div>

          {tag && (
            <div className="flex items-center mb-4">
              <div
                className="w-4 h-4 rounded-full mr-2"
                style={{ backgroundColor: tag.color || '#3b82f6' }}
              />
              <span className="text-lg font-medium">{tag.name}</span>
            </div>
          )}

          <p className="text-gray-500">
            Viewing all items tagged with {tag ? `"${tag.name}"` : "this tag"}
          </p>
        </div>

        {error ? (
          <Alert color="failure" icon={HiOutlineExclamation}>
            {error}
          </Alert>
        ) : loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : entities.length === 0 ? (
          <Alert color="info">
            No items found with this tag.
          </Alert>
        ) : (
          <>
            <div className="mb-4">
              <div className="border-b border-gray-200 dark:border-gray-700">
                <ul className="flex flex-wrap -mb-px text-sm font-medium text-center">
                  <li className="mr-2">
                    <button
                      className={`inline-block p-4 rounded-t-lg ${
                        activeTab === 'all'
                          ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500'
                          : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
                      }`}
                      onClick={() => setActiveTab('all')}
                    >
                      All ({entities.length})
                    </button>
                  </li>
                  {entities.some(e => e.type === 'product') && (
                    <li className="mr-2">
                      <button
                        className={`inline-block p-4 rounded-t-lg ${
                          activeTab === 'products'
                            ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500'
                            : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
                        }`}
                        onClick={() => setActiveTab('products')}
                      >
                        Products ({entities.filter(e => e.type === 'product').length})
                      </button>
                    </li>
                  )}
                  {entities.some(e => e.type === 'customer') && (
                    <li className="mr-2">
                      <button
                        className={`inline-block p-4 rounded-t-lg ${
                          activeTab === 'customers'
                            ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500'
                            : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
                        }`}
                        onClick={() => setActiveTab('customers')}
                      >
                        Customers ({entities.filter(e => e.type === 'customer').length})
                      </button>
                    </li>
                  )}
                  {entities.some(e => e.type === 'supplier') && (
                    <li className="mr-2">
                      <button
                        className={`inline-block p-4 rounded-t-lg ${
                          activeTab === 'suppliers'
                            ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500'
                            : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
                        }`}
                        onClick={() => setActiveTab('suppliers')}
                      >
                        Suppliers ({entities.filter(e => e.type === 'supplier').length})
                      </button>
                    </li>
                  )}
                </ul>
              </div>
            </div>

            {filteredEntities.length === 0 ? (
              <Alert color="info" className="mt-4">
                No items found in the selected category.
              </Alert>
            ) : (
              <Table>
                <Table.Head>
                  <Table.HeadCell>Name</Table.HeadCell>
                  <Table.HeadCell>Type</Table.HeadCell>
                  <Table.HeadCell>Actions</Table.HeadCell>
                </Table.Head>
                <Table.Body className="divide-y">
                  {filteredEntities.map(entity => (
                    <Table.Row key={`${entity.type}-${entity.id}`} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                      <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                        {entity.name}
                      </Table.Cell>
                      <Table.Cell>
                        <Badge color={getEntityTypeColor(entity.type as TaggableEntityType)}>
                          {getEntityTypeLabel(entity.type as TaggableEntityType)}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell>
                        <Link
                          to={entity.detailsUrl}
                          className="font-medium text-blue-600 hover:underline dark:text-blue-500"
                        >
                          View Details
                        </Link>
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            )}
          </>
        )}
      </Card>
    </div>
  );
};

export default TaggedItemsView;
