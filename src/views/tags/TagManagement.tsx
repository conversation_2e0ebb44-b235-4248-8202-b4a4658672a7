import React from 'react';
import { Card, Breadcrumb } from 'flowbite-react';
import { HiOutlineTag, HiOutlineHome } from 'react-icons/hi';
import { Link } from 'react-router-dom';
import TagManager from '../../components/tags/TagManager';

const TagManagement: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Tag Management</h1>
        <Breadcrumb className="hidden md:flex">
          <Breadcrumb.Item href="/" icon={HiOutlineHome}>
            Home
          </Breadcrumb.Item>
          <Breadcrumb.Item href="/settings">Settings</Breadcrumb.Item>
          <Breadcrumb.Item>Tag Management</Breadcrumb.Item>
        </Breadcrumb>
      </div>

      <Card className="mb-4">
        <div className="p-4">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <HiOutlineTag className="mr-2 h-6 w-6 text-blue-500" />
            About Tags
          </h2>
          <p className="mb-3">
            Tags help you organize and categorize your data across the system. You can add tags to:
          </p>
          <ul className="list-disc pl-6 mb-4 space-y-1">
            <li>Products - categorize products by attributes, seasons, promotions, etc.</li>
            <li>Suppliers - identify supplier specialties, reliability, or geographic regions</li>
            <li>Customers - segment customers by preferences, purchase history, or demographics</li>
            <li>Purchase Requests - track request priorities, departments, or approval status</li>
            <li>Purchase Orders - organize orders by shipping method, payment terms, or urgency</li>
          </ul>
          <p>
            Create tags with descriptive names and optional colors to make them easily identifiable.
            You can then filter and search by tags throughout the application.
          </p>
        </div>
      </Card>

      <TagManager />
    </div>
  );
};

export default TagManagement;
