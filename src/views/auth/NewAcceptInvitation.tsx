import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, TextInput, Label } from 'flowbite-react';
import { supabase } from '../../lib/supabase';
import { HiOutlineMail, HiOutlineLockClosed, HiOutlineUser } from 'react-icons/hi';

interface InvitationDetails {
  id: string;
  organization_id: string;
  email: string;
  role: string;
  token: string;
  expires_at: string;
  organization_name?: string;
  inviter_name?: string;
}

const NewAcceptInvitation = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const rawToken = searchParams.get('token');
  const invitationId = searchParams.get('id');

  // Make sure to decode the token if it's URL-encoded
  const token = rawToken ? decodeURIComponent(rawToken) : null;

  console.log('Raw token from URL:', rawToken);
  console.log('Decoded token:', token);

  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [invitation, setInvitation] = useState<InvitationDetails | null>(null);
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [userExists, setUserExists] = useState<boolean>(false);

  useEffect(() => {
    const fetchInvitation = async () => {
      console.log('NewAcceptInvitation component mounted');
      console.log('URL parameters:', { token, invitationId });

      if (!token && !invitationId) {
        setError('Invalid invitation link. No token or ID provided.');
        setLoading(false);
        return;
      }

      try {
        // Get all invitations for debugging
        const { data: allInvitations } = await supabase
          .from('invitations')
          .select('id, token, email, expires_at, accepted_at')
          .limit(10);

        console.log('All recent invitations:', allInvitations);

        // Try to find the invitation using different approaches
        let invitation = null;
        let invitationError = null;

        // First try: Use both ID and token if available
        if (invitationId && token) {
          console.log('Trying to find invitation with both ID and token');
          const { data, error } = await supabase
            .from('invitations')
            .select('*, organizations(name), profiles(first_name, last_name)')
            .eq('id', invitationId)
            .eq('token', token)
            .is('accepted_at', null)
            .single();

          console.log('Query result (ID + token):', { data, error });

          if (data && !error) {
            invitation = data;
          } else {
            invitationError = error;
          }
        }

        // Second try: Use ID only if available and first try failed
        if (!invitation && invitationId) {
          console.log('Trying to find invitation with ID only');
          const { data, error } = await supabase
            .from('invitations')
            .select('*, organizations(name), profiles(first_name, last_name)')
            .eq('id', invitationId)
            .is('accepted_at', null)
            .single();

          console.log('Query result (ID only):', { data, error });

          if (data && !error) {
            invitation = data;
          } else if (!invitationError) {
            invitationError = error;
          }
        }

        // Third try: Use token only if available and previous tries failed
        if (!invitation && token) {
          console.log('Trying to find invitation with token only');
          const { data, error } = await supabase
            .from('invitations')
            .select('*, organizations(name), profiles(first_name, last_name)')
            .eq('token', token)
            .is('accepted_at', null)
            .single();

          console.log('Query result (token only):', { data, error });

          if (data && !error) {
            invitation = data;
          } else if (!invitationError) {
            invitationError = error;
          }
        }

        // Fourth try: Try with a different token format (some characters might be escaped)
        if (!invitation && token) {
          // Try with different token formats
          const alternativeTokens = [
            token.replace(/\+/g, ' '),  // Replace + with space
            token.replace(/\s/g, '+'),  // Replace space with +
            encodeURIComponent(token),  // Fully encode
            decodeURIComponent(token)   // Fully decode
          ];

          for (const altToken of alternativeTokens) {
            if (altToken === token) continue; // Skip if same as original

            console.log('Trying alternative token format:', altToken);
            const { data, error } = await supabase
              .from('invitations')
              .select('*, organizations(name), profiles(first_name, last_name)')
              .eq('token', altToken)
              .is('accepted_at', null)
              .single();

            console.log('Query result (alt token):', { data, error });

            if (data && !error) {
              invitation = data;
              break;
            }
          }
        }

        if (!invitation) {
          console.error('Could not find invitation with any method');
          console.error('Last error:', invitationError);
          setError('Invalid or expired invitation.');
          setLoading(false);
          return;
        }

        const data = invitation;

        console.log('Found invitation:', data);

        // We already checked for !invitation above, so this should never happen
        if (!data) {
          setError('Invalid or expired invitation.');
          setLoading(false);
          return;
        }

        // Check if the invitation has expired
        const expiresAt = new Date(data.expires_at);
        if (expiresAt < new Date()) {
          setError('This invitation has expired.');
          setLoading(false);
          return;
        }

        // Format the invitation data
        const invitationDetails: InvitationDetails = {
          id: data.id,
          organization_id: data.organization_id,
          email: data.email,
          role: data.role,
          token: data.token,
          expires_at: data.expires_at,
          organization_name: data.organizations?.name,
          inviter_name: data.profiles ? `${data.profiles.first_name} ${data.profiles.last_name}` : undefined
        };

        setInvitation(invitationDetails);
        setEmail(invitationDetails.email);

        // Check if a user with this email already exists
        const { data: userData, error: userError } = await supabase.auth.signInWithOtp({
          email: invitationDetails.email,
          options: {
            shouldCreateUser: false
          }
        });

        if (!userError && userData) {
          setUserExists(true);
        }
      } catch (err: any) {
        console.error('Error fetching invitation:', err);
        setError('Failed to load invitation details.');
      } finally {
        setLoading(false);
      }
    };

    fetchInvitation();
  }, [token, invitationId, navigate]);

  const handleAcceptInvitation = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!invitation) return;

    setSubmitting(true);
    setError(null);

    try {
      let userId: string | undefined;

      // If the user doesn't exist, create a new account
      if (!userExists) {
        if (!firstName || !lastName) {
          setError('Please provide your first and last name.');
          setSubmitting(false);
          return;
        }

        if (password.length < 8) {
          setError('Password must be at least 8 characters long.');
          setSubmitting(false);
          return;
        }

        // Create a new user
        const { data: authData, error: authError } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              first_name: firstName,
              last_name: lastName
            }
          }
        });

        if (authError || !authData.user) {
          setError(authError?.message || 'Failed to create account.');
          setSubmitting(false);
          return;
        }

        userId = authData.user.id;

        // Create a profile for the new user
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            id: userId,
            first_name: firstName,
            last_name: lastName
          });

        if (profileError) {
          console.error('Error creating profile:', profileError);
          // Continue anyway, as the user has been created
        }
      } else {
        // Sign in the existing user
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password
        });

        if (signInError || !signInData.user) {
          setError(signInError?.message || 'Invalid email or password.');
          setSubmitting(false);
          return;
        }

        userId = signInData.user.id;
      }

      // Add the user to the organization
      const { error: memberError } = await supabase
        .from('organization_members')
        .insert({
          organization_id: invitation.organization_id,
          user_id: userId,
          role: invitation.role
        });

      if (memberError) {
        if (memberError.code === '23505') { // Unique violation
          console.log('User is already a member of this organization');
          // Continue anyway
        } else {
          setError('Failed to add you to the organization. Please try again.');
          setSubmitting(false);
          return;
        }
      }

      // Mark the invitation as accepted
      const { error: updateError } = await supabase
        .from('invitations')
        .update({ accepted_at: new Date().toISOString() })
        .eq('id', invitation.id);

      if (updateError) {
        console.error('Error updating invitation:', updateError);
        // Continue anyway, as the user has been added to the organization
      }

      // Redirect to the dashboard
      navigate('/dashboard');
    } catch (err: any) {
      console.error('Error accepting invitation:', err);
      setError(err.message || 'Failed to accept invitation.');
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <Card className="w-full max-w-md">
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <Card className="w-full max-w-md">
          <h1 className="text-2xl font-bold mb-4">Invitation Error</h1>
          <Alert color="failure">
            {error}
          </Alert>
          <div className="mt-4">
            <Button onClick={() => navigate('/auth/login')}>
              Go to Login
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <h1 className="text-2xl font-bold mb-4">Accept Invitation</h1>

        {invitation && (
          <div className="mb-6">
            <p className="text-gray-700">
              You've been invited to join <strong>{invitation.organization_name}</strong> as a <strong>{invitation.role}</strong>.
            </p>
            {invitation.inviter_name && (
              <p className="text-gray-500 text-sm mt-1">
                Invited by {invitation.inviter_name}
              </p>
            )}
          </div>
        )}

        <form onSubmit={handleAcceptInvitation}>
          <div className="mb-4">
            <div className="mb-2 block">
              <Label htmlFor="email" value="Email Address" />
            </div>
            <TextInput
              id="email"
              type="email"
              icon={HiOutlineMail}
              value={email}
              disabled
            />
          </div>

          {!userExists && (
            <>
              <div className="mb-4">
                <div className="mb-2 block">
                  <Label htmlFor="firstName" value="First Name" />
                </div>
                <TextInput
                  id="firstName"
                  type="text"
                  icon={HiOutlineUser}
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  required
                />
              </div>

              <div className="mb-4">
                <div className="mb-2 block">
                  <Label htmlFor="lastName" value="Last Name" />
                </div>
                <TextInput
                  id="lastName"
                  type="text"
                  icon={HiOutlineUser}
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  required
                />
              </div>
            </>
          )}

          <div className="mb-6">
            <div className="mb-2 block">
              <Label htmlFor="password" value={userExists ? "Password" : "Create Password"} />
            </div>
            <TextInput
              id="password"
              type="password"
              icon={HiOutlineLockClosed}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            {!userExists && (
              <p className="mt-1 text-sm text-gray-500">
                Password must be at least 8 characters long
              </p>
            )}
          </div>

          <Button type="submit" className="w-full" disabled={submitting}>
            {submitting ? <Spinner size="sm" className="mr-2" /> : null}
            {userExists ? 'Sign In & Accept' : 'Create Account & Accept'}
          </Button>
        </form>
      </Card>
    </div>
  );
};

export default NewAcceptInvitation;
