import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, TextInput, Label } from 'flowbite-react';
import { supabase } from '../../lib/supabase';
import { HiOutlineMail, HiOutlineLockClosed, HiOutlineUser } from 'react-icons/hi';

interface InvitationDetails {
  id: string;
  organization_id: string;
  email: string;
  role: string;
  token: string;
  expires_at: string;
  organization_name?: string;
  inviter_name?: string;
}

const AcceptInvitation = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const token = searchParams.get('token');
  const invitationId = searchParams.get('id');

  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [invitation, setInvitation] = useState<InvitationDetails | null>(null);
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [userExists, setUserExists] = useState<boolean>(false);

  useEffect(() => {
    const fetchInvitation = async () => {
      console.log('AcceptInvitation component mounted');
      console.log('URL parameters:', { token, invitationId });

      if (!token && !invitationId) {
        setError('Invalid invitation link. No token or ID provided.');
        setLoading(false);
        return;
      }

      // If we have an invitation ID, that's even better for validation
      if (!invitationId) {
        console.log('No invitation ID provided in URL, will try to validate with token only');
      } else {
        console.log('Invitation ID provided:', invitationId);
      }

      if (!token) {
        console.log('No token provided in URL, will try to validate with ID only');
      } else {
        console.log('Token provided:', token);
      }

      // Debug: Check all invitations in the database
      try {
        const { data: allInvitations, error: allError } = await supabase
          .from('invitations')
          .select('id, token, email, expires_at, accepted_at')
          .limit(10);

        console.log('All recent invitations:', allInvitations);
        if (allError) {
          console.error('Error fetching all invitations:', allError);
        }
      } catch (err) {
        console.error('Error checking all invitations:', err);
      }

      try {
        // Check if the user is already logged in
        const { data: sessionData } = await supabase.auth.getSession();
        const currentUser = sessionData.session?.user;

        console.log('Validating invitation...');

        let invitationData = null;
        let invitationError = null;

        // First, try to find the invitation by ID if available
        if (invitationId) {
          console.log('Trying to find invitation by ID:', invitationId);
          const { data, error } = await supabase
            .from('invitations')
            .select('*, organizations(name), profiles(first_name, last_name)')
            .eq('id', invitationId)
            .is('accepted_at', null)
            .single();

          console.log('ID query result:', { data, error });

          if (data && !error) {
            console.log('Found invitation by ID');
            invitationData = data;
          } else {
            invitationError = error;
          }
        }

        // If we couldn't find by ID or no ID was provided, try by token
        if (!invitationData && token) {
          console.log('Trying to find invitation by token:', token);
          const { data, error } = await supabase
            .from('invitations')
            .select('*, organizations(name), profiles(first_name, last_name)')
            .eq('token', token)
            .is('accepted_at', null)
            .single();

          console.log('Token query result:', { data, error });

          if (data && !error) {
            console.log('Found invitation by token');
            invitationData = data;
          } else {
            invitationError = error;
          }
        }

        // If we couldn't find the invitation
        if (!invitationData) {
          console.log('Could not find invitation by ID or token');
          console.error('Invitation error:', invitationError);
          setError('Invalid or expired invitation.');
          setLoading(false);
          return;
        }

        const data = invitationData;

        // Check if the invitation has expired
        const expiresAt = new Date(data.expires_at);
        if (expiresAt < new Date()) {
          setError('This invitation has expired.');
          setLoading(false);
          return;
        }

        // Format the invitation data
        const invitationDetails: InvitationDetails = {
          id: data.id,
          organization_id: data.organization_id,
          email: data.email,
          role: data.role,
          token: data.token,
          expires_at: data.expires_at,
          organization_name: data.organizations?.name,
          inviter_name: data.profiles ? `${data.profiles.first_name} ${data.profiles.last_name}` : undefined
        };

        setInvitation(invitationDetails);
        setEmail(invitationDetails.email);

        // If the user is already logged in and the email matches the invitation
        if (currentUser && currentUser.email === invitationDetails.email) {
          // Automatically accept the invitation
          try {
            // Add the user to the organization
            const { error: memberError } = await supabase
              .from('organization_members')
              .insert({
                organization_id: invitationDetails.organization_id,
                user_id: currentUser.id,
                role: invitationDetails.role
              });

            if (memberError) {
              if (memberError.code === '23505') { // Unique violation
                // User is already a member of this organization
                setError('You are already a member of this organization.');
              } else {
                setError('Failed to add you to the organization. Please try again.');
              }
              setLoading(false);
              return;
            }

            // Mark the invitation as accepted
            await supabase
              .from('invitations')
              .update({ accepted_at: new Date().toISOString() })
              .eq('id', invitationDetails.id);

            // Redirect to the dashboard
            navigate('/dashboard');
            return;
          } catch (err: any) {
            console.error('Error auto-accepting invitation:', err);
            // Continue with manual acceptance
          }
        }

        // Check if a user with this email already exists
        const { data: userData, error: userError } = await supabase.auth.signInWithOtp({
          email: invitationDetails.email,
          options: {
            shouldCreateUser: false
          }
        });

        if (!userError && userData) {
          setUserExists(true);
        }
      } catch (err: any) {
        console.error('Error fetching invitation:', err);
        setError('Failed to load invitation details.');
      } finally {
        setLoading(false);
      }
    };

    fetchInvitation();
  }, [token, invitationId, navigate]);

  const handleAcceptInvitation = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!invitation) return;

    setSubmitting(true);
    setError(null);

    try {
      let userId: string | undefined;

      // If the user doesn't exist, create a new account
      if (!userExists) {
        if (!firstName || !lastName) {
          setError('Please provide your first and last name.');
          setSubmitting(false);
          return;
        }

        if (password.length < 8) {
          setError('Password must be at least 8 characters long.');
          setSubmitting(false);
          return;
        }

        // Create a new user
        const { data: authData, error: authError } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              first_name: firstName,
              last_name: lastName
            }
          }
        });

        if (authError || !authData.user) {
          setError(authError?.message || 'Failed to create account.');
          setSubmitting(false);
          return;
        }

        userId = authData.user.id;

        // Create a profile for the new user
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            id: userId,
            first_name: firstName,
            last_name: lastName
          });

        if (profileError) {
          console.error('Error creating profile:', profileError);
          // Continue anyway, as the user has been created
        }
      } else {
        // Sign in the existing user
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password
        });

        if (signInError || !signInData.user) {
          setError(signInError?.message || 'Invalid email or password.');
          setSubmitting(false);
          return;
        }

        userId = signInData.user.id;
      }

      // Add the user to the organization
      const { error: memberError } = await supabase
        .from('organization_members')
        .insert({
          organization_id: invitation.organization_id,
          user_id: userId,
          role: invitation.role
        });

      if (memberError) {
        setError('Failed to add you to the organization. Please try again.');
        setSubmitting(false);
        return;
      }

      // Mark the invitation as accepted
      const { error: updateError } = await supabase
        .from('invitations')
        .update({ accepted_at: new Date().toISOString() })
        .eq('id', invitation.id);

      if (updateError) {
        console.error('Error updating invitation:', updateError);
        // Continue anyway, as the user has been added to the organization
      }

      // Redirect to the dashboard
      navigate('/dashboard');
    } catch (err: any) {
      console.error('Error accepting invitation:', err);
      setError(err.message || 'Failed to accept invitation.');
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <Card className="w-full max-w-md">
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <Card className="w-full max-w-md">
          <h1 className="text-2xl font-bold mb-4">Invitation Error</h1>
          <Alert color="failure">
            {error}
          </Alert>
          <div className="mt-4">
            <Button onClick={() => navigate('/auth/login')}>
              Go to Login
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <h1 className="text-2xl font-bold mb-4">Accept Invitation</h1>

        {invitation && (
          <div className="mb-6">
            <p className="text-gray-700">
              You've been invited to join <strong>{invitation.organization_name}</strong> as a <strong>{invitation.role}</strong>.
            </p>
            {invitation.inviter_name && (
              <p className="text-gray-500 text-sm mt-1">
                Invited by {invitation.inviter_name}
              </p>
            )}
          </div>
        )}

        <form onSubmit={handleAcceptInvitation}>
          <div className="mb-4">
            <div className="mb-2 block">
              <Label htmlFor="email" value="Email Address" />
            </div>
            <TextInput
              id="email"
              type="email"
              icon={HiOutlineMail}
              value={email}
              disabled
            />
          </div>

          {!userExists && (
            <>
              <div className="mb-4">
                <div className="mb-2 block">
                  <Label htmlFor="firstName" value="First Name" />
                </div>
                <TextInput
                  id="firstName"
                  type="text"
                  icon={HiOutlineUser}
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  required
                />
              </div>

              <div className="mb-4">
                <div className="mb-2 block">
                  <Label htmlFor="lastName" value="Last Name" />
                </div>
                <TextInput
                  id="lastName"
                  type="text"
                  icon={HiOutlineUser}
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  required
                />
              </div>
            </>
          )}

          <div className="mb-6">
            <div className="mb-2 block">
              <Label htmlFor="password" value={userExists ? "Password" : "Create Password"} />
            </div>
            <TextInput
              id="password"
              type="password"
              icon={HiOutlineLockClosed}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            {!userExists && (
              <p className="mt-1 text-sm text-gray-500">
                Password must be at least 8 characters long
              </p>
            )}
          </div>

          <Button type="submit" className="w-full" disabled={submitting}>
            {submitting ? <Spinner size="sm" className="mr-2" /> : null}
            {userExists ? 'Sign In & Accept' : 'Create Account & Accept'}
          </Button>
        </form>
      </Card>
    </div>
  );
};

export default AcceptInvitation;
