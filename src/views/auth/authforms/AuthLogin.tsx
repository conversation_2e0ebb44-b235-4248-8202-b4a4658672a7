import { Button, Checkbox, Label, TextInput, Alert } from "flowbite-react";
import { Link, useNavigate } from "react-router-dom";
import { useState } from "react";
import { signIn, SignInData } from "../../../services/auth";
import { checkUserOrganizations } from "../../../services/userProfile";
import { getUserOrganizations } from "../../../services/organization";
import AuthCleanupButton from "../../../components/auth/AuthCleanupButton";

const AuthLogin = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<SignInData>({
    email: "",
    password: "",
  });
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [rememberDevice, setRememberDevice] = useState<boolean>(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id === "email" ? "email" : "password"]: value,
    }));
  };

  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      // Validate inputs
      if (!formData.email || !formData.password) {
        setError("Please enter both email and password");
        setLoading(false);
        return;
      }

      const { data, error } = await signIn(formData);

      if (error) {
        console.error("Login error:", error);
        throw error;
      }

      if (data.session) {
        setSuccess("Login successful!");

        try {
          // Check if user has multiple organizations
          const { hasOrganizations, organizations } = await checkUserOrganizations(data.user.id);

          if (!hasOrganizations) {
            // User has no organizations, redirect to onboarding flow
            setTimeout(() => {
              navigate("/onboarding");
            }, 1000);
          } else if (organizations && organizations.length === 1) {
            // User has only one organization, store it and redirect to dashboard
            const orgId = organizations[0].organization.id;
            localStorage.setItem('selectedOrganizationId', orgId);

            // Fetch the full organization details to update the context
            try {
              const allOrgs = await getUserOrganizations();
              const selectedOrg = allOrgs.find(org => org.id === orgId);

              if (selectedOrg) {
                // Force reload to ensure the organization context is updated
                window.location.href = "/";
              } else {
                setTimeout(() => {
                  navigate("/");
                }, 1000);
              }
            } catch (err) {
              console.error("Error fetching organization details:", err);
              setTimeout(() => {
                navigate("/");
              }, 1000);
            }
          } else {
            // User has multiple organizations, redirect to organization selection
            setTimeout(() => {
              navigate("/auth/select-organization");
            }, 1000);
          }
        } catch (err) {
          console.error("Error checking organizations:", err);
          // Default to dashboard if there's an error
          setTimeout(() => {
            navigate("/");
          }, 1000);
        }
      } else {
        console.error("No session returned from login");
        setError("Login failed. Please check your email and password.");
      }
    } catch (err: any) {
      console.error("Login error details:", err);

      // Handle specific error messages
      if (err.message.includes("Invalid login credentials")) {
        setError("Invalid email or password. Please try again.");
      } else if (err.message.includes("Email not confirmed")) {
        setError("Please confirm your email address before logging in.");
      } else if (err.message.includes("network")) {
        setError("Network error. Please check your internet connection and try again.");
      } else {
        setError(err.message || "An error occurred during login");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {error && (
        <Alert color="failure" className="mb-4">
          {error}
        </Alert>
      )}

      {success && (
        <Alert color="success" className="mb-4">
          {success}
        </Alert>
      )}
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <div className="mb-2 block">
            <Label htmlFor="email" value="Email" />
          </div>
          <TextInput
            id="email"
            type="email"
            sizing="md"
            required
            value={formData.email}
            onChange={handleChange}
            className="form-control form-rounded-xl"
          />
        </div>
        <div className="mb-4">
          <div className="mb-2 block">
            <Label htmlFor="password" value="Password" />
          </div>
          <TextInput
            id="password"
            type="password"
            sizing="md"
            required
            value={formData.password}
            onChange={handleChange}
            className="form-control form-rounded-xl"
          />
        </div>
        <div className="flex justify-between my-5">
          <div className="flex items-center gap-2">
            <Checkbox
              id="remember"
              className="checkbox"
              checked={rememberDevice}
              onChange={() => setRememberDevice(!rememberDevice)}
            />
            <Label
              htmlFor="remember"
              className="opacity-90 font-normal cursor-pointer"
            >
              Remember this Device
            </Label>
          </div>
          <Link to={"/auth/forgot-password"} className="text-primary text-sm font-medium">
            Forgot Password?
          </Link>
        </div>
        <Button
          type="submit"
          color={"primary"}
          className="w-full bg-primary text-white rounded-xl"
          disabled={loading}
        >
          {loading ? "Signing in..." : "Sign in"}
        </Button>

        {/* Auth cleanup button for troubleshooting */}
        <div className="mt-4 text-center">
          <p className="text-sm text-gray-500 mb-2">
            Having login issues?
          </p>
          <AuthCleanupButton variant="button" className="text-sm" />
        </div>
      </form>
    </>
  );
};

export default AuthLogin;
