import { Button, Label, TextInput, Alert } from "flowbite-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { signUp, SignUpData } from "../../../services/auth";

const AuthRegister = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<SignUpData>({
    email: "",
    password: "",
  });
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      // Validate password length
      if (formData.password.length < 6) {
        setError("Password must be at least 6 characters long");
        setLoading(false);
        return;
      }

      const { data, error } = await signUp(formData);

      if (error) {
        throw error;
      }

      // Check if email confirmation is required
      if (data?.user?.identities?.length === 0 || data?.user?.email_confirmed_at === null) {
        // Show confirmation message
        setSuccess("Registration successful! Please check your email to confirm your account. After confirming, you'll be able to log in and complete your profile setup.");
        // Clear the form
        setFormData({
          email: "",
          password: "",
        });
        return;
      }

      // If registration is successful and no confirmation needed, redirect to login page
      setSuccess("Registration successful! You can now log in. After logging in, you'll be prompted to complete your profile setup.");
      setTimeout(() => {
        navigate("/auth/login");
      }, 3000);
    } catch (err: any) {
      // Handle specific error messages
      if (err.message.includes("already registered")) {
        setError("This email is already registered. Please use a different email or try to log in.");
      } else if (err.message.includes("network")) {
        setError("Network error. Please check your internet connection and try again.");
      } else {
        setError(err.message || "An error occurred during registration");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {error && (
        <Alert color="failure" className="mb-4">
          {error}
        </Alert>
      )}

      {success && (
        <Alert color="success" className="mb-4">
          {success}
        </Alert>
      )}
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <div className="mb-2 block">
            <Label htmlFor="email" value="Email Address" />
          </div>
          <TextInput
            id="email"
            type="email"
            sizing="md"
            required
            value={formData.email}
            onChange={handleChange}
            className="form-control form-rounded-xl"
          />
        </div>
        <div className="mb-6">
          <div className="mb-2 block">
            <Label htmlFor="password" value="Password" />
          </div>
          <TextInput
            id="password"
            type="password"
            sizing="md"
            required
            value={formData.password}
            onChange={handleChange}
            className="form-control form-rounded-xl"
          />
          <p className="text-xs text-gray-500 mt-1">
            Password must be at least 6 characters long
          </p>
        </div>
        <div className="mb-4">
          <p className="text-sm text-gray-600">
            After registration, you'll be prompted to set up your profile and organization.
          </p>
        </div>
        <Button
          color={'primary'}
          type="submit"
          className="w-full"
          disabled={loading}
        >
          {loading ? "Creating Account..." : "Sign Up"}
        </Button>
      </form>
    </>
  );
};

export default AuthRegister;
