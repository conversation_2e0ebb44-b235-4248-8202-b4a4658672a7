import { But<PERSON>, Label, TextInput, Alert } from "flowbite-react";
import { Link } from "react-router";
import { useState } from "react";
import { resetPassword } from "../../../services/auth";
import FullLogo from "src/layouts/full/shared/logo/FullLogo";

const gradientStyle = {
  background: "linear-gradient(45deg, rgb(238, 119, 82,0.2), rgb(231, 60, 126,0.2), rgb(35, 166, 213,0.2), rgb(35, 213, 171,0.2))",
  backgroundSize: "400% 400%",
  animation: "gradient 15s ease infinite",
  height: "100vh",
};

const ForgotPassword = () => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setSuccess(false);
    setLoading(true);

    try {
      const { error } = await resetPassword(email);
      
      if (error) {
        throw error;
      }

      setSuccess(true);
    } catch (err: any) {
      setError(err.message || "An error occurred while sending the reset link");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={gradientStyle} className="relative overflow-hidden h-screen">
      <div className="flex h-full justify-center items-center px-4">
        <div className="rounded-xl shadow-md bg-white dark:bg-darkgray p-6 w-full md:w-96 border-none">
          <div className="flex flex-col gap-2 p-0 w-full">
            <div className="mx-auto">
              <FullLogo />
            </div>
            <p className="text-sm text-center text-dark my-3">Forgot Password</p>
            
            {success ? (
              <Alert color="success" className="mb-4">
                Password reset link has been sent to your email.
              </Alert>
            ) : (
              <>
                {error && (
                  <Alert color="failure" className="mb-4">
                    {error}
                  </Alert>
                )}
                <form onSubmit={handleSubmit}>
                  <div className="mb-4">
                    <div className="mb-2 block">
                      <Label htmlFor="email" value="Email" />
                    </div>
                    <TextInput
                      id="email"
                      type="email"
                      sizing="md"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="form-control form-rounded-xl"
                      placeholder="Enter your email address"
                    />
                  </div>
                  <Button 
                    type="submit" 
                    color={"primary"}  
                    className="w-full bg-primary text-white rounded-xl"
                    disabled={loading}
                  >
                    {loading ? "Sending..." : "Send Reset Link"}
                  </Button>
                </form>
              </>
            )}
            
            <div className="flex gap-2 text-base text-ld font-medium mt-6 items-center justify-center">
              <p>Remember your password?</p>
              <Link to="/auth/login" className="text-primary text-sm font-medium">
                Sign in
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
