import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> } from 'flowbite-react';
import { HiOutlineOfficeBuilding, HiOutlineExclamation } from 'react-icons/hi';
import { useAuth } from '../../../context/AuthContext';
import { useOrganization } from '../../../context/OrganizationContext';
import { Organization } from '../../../services/organization';
import { getUserOrganizations } from '../../../services/organization';
import { getOrganizationMembers } from '../../../services/userManagement';
import FullLogo from '../../../layouts/full/shared/logo/FullLogo';

const gradientStyle = {
  background: "linear-gradient(45deg, rgb(238, 119, 82,0.2), rgb(231, 60, 126,0.2), rgb(35, 166, 213,0.2), rgb(35, 213, 171,0.2))",
  backgroundSize: "400% 400%",
  animation: "gradient 15s ease infinite",
  height: "100vh",
};

const OrganizationSelect = () => {
  const { user } = useAuth();
  const { setCurrentOrganization: updateOrganizationContext } = useOrganization();
  const navigate = useNavigate();
  const [organizations, setOrganizations] = useState<(Organization & { userRole?: string })[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrganizations = async () => {
      if (!user) {
        navigate('/auth/login');
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch organizations the user belongs to
        const orgs = await getUserOrganizations();

        // For each organization, get the user's role
        const orgsWithRoles = await Promise.all(
          orgs.map(async (org) => {
            try {
              const { members } = await getOrganizationMembers(org.id);
              const userMember = members.find(m => m.user_id === user.id);
              return {
                ...org,
                userRole: userMember?.role || 'Unknown'
              };
            } catch (err) {
              console.error(`Error fetching role for org ${org.id}:`, err);
              return {
                ...org,
                userRole: 'Unknown'
              };
            }
          })
        );

        setOrganizations(orgsWithRoles);

        // If there's only one organization, automatically select it
        if (orgsWithRoles.length === 1) {
          console.log('Only one organization found, automatically selecting:', orgsWithRoles[0].name);
          handleSelectOrganization(orgsWithRoles[0]);
        }
      } catch (err: any) {
        console.error('Error fetching organizations:', err);
        setError(err.message || 'Failed to load organizations');
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizations();
  }, [user, navigate]);

  const handleSelectOrganization = (organization: Organization) => {
    // Store the selected organization ID in local storage
    localStorage.setItem('selectedOrganizationId', organization.id);

    // Update the organization context
    updateOrganizationContext(organization);

    // Redirect to the dashboard
    navigate('/');
  };

  if (loading) {
    return (
      <div style={gradientStyle} className="relative overflow-hidden h-screen">
        <div className="flex h-full justify-center items-center px-4">
          <Card className="w-full max-w-md">
            <div className="flex flex-col items-center justify-center p-4">
              <FullLogo />
              <h2 className="text-xl font-bold mt-4 mb-2">Loading Organizations</h2>
              <Spinner size="xl" />
              <p className="mt-4 text-gray-500">Please wait while we load your organizations...</p>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div style={gradientStyle} className="relative overflow-hidden h-screen">
      <div className="flex h-full justify-center items-center px-4">
        <Card className="w-full max-w-md">
          <div className="flex flex-col items-center mb-4">
            <FullLogo />
            <h2 className="text-xl font-bold mt-4">Select an Organization</h2>
            <p className="text-gray-500 text-sm mt-1">Choose which organization you want to access</p>
          </div>

          {error && (
            <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
              {error}
            </Alert>
          )}

          {organizations.length === 0 ? (
            <div className="text-center p-4">
              <p className="text-gray-500 mb-4">You don't have access to any organizations.</p>
              <Button color="primary" onClick={() => navigate('/organization/create')}>
                Create an Organization
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {organizations.map((org) => (
                <Button
                  key={org.id}
                  color="light"
                  className="w-full justify-start p-4 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => handleSelectOrganization(org)}
                >
                  <div className="flex items-center">
                    {org.logo_url ? (
                      <img
                        src={org.logo_url}
                        alt={org.name}
                        className="w-10 h-10 rounded-full mr-3"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                        <HiOutlineOfficeBuilding className="h-5 w-5 text-primary" />
                      </div>
                    )}
                    <div>
                      <p className="font-medium text-left">{org.name}</p>
                      <p className="text-xs text-gray-500 text-left">Role: {org.userRole}</p>
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          )}

          <div className="mt-4 text-center">
            <Button color="light" onClick={() => navigate('/organization/create')}>
              Create New Organization
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default OrganizationSelect;
