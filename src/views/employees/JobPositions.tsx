import { useState, useEffect } from 'react';
import {
  Card,
  <PERSON>,
  <PERSON><PERSON>,
  Spinner,
  <PERSON>ert,
  TextInput,
  Dropdown,
  Modal,
  Label,
  Textarea,
  Badge,
  Select,
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineSearch,
  HiOutlineDotsVertical,
  HiOutlineExclamation,
  HiOutlineBriefcase,
} from 'react-icons/hi';
import { getJobPositions, JobPosition, createJobPosition, updateJobPosition, deleteJobPosition } from '../../services/jobPosition';
import { getDepartments, Department } from '../../services/department';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import Pagination from '../../components/common/Pagination';

const JobPositions = () => {
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();

  // List state
  const [positions, setPositions] = useState<JobPosition[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [showActiveOnly, setShowActiveOnly] = useState<boolean>(true);

  // Modal states
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<boolean>(false);
  const [currentPosition, setCurrentPosition] = useState<JobPosition | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Form state
  const [positionTitle, setPositionTitle] = useState<string>('');
  const [positionDescription, setPositionDescription] = useState<string>('');
  const [departmentId, setDepartmentId] = useState<string>('');
  const [isActive, setIsActive] = useState<boolean>(true);

  const [itemsPerPage, setItemsPerPage] = useState(10);

  useEffect(() => {
    if (currentOrganization) {
      fetchPositions();
      fetchDepartments();
    }
  }, [currentOrganization, currentPage, searchQuery, selectedDepartment, showActiveOnly, itemsPerPage]);

  const fetchPositions = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { positions, count, error } = await getJobPositions(currentOrganization.id, {
        searchQuery,
        departmentId: selectedDepartment || undefined,
        isActive: showActiveOnly ? true : undefined,
        limit: itemsPerPage,
        offset: (currentPage - 1) * itemsPerPage,
      });

      if (error) {
        setError(error);
      } else {
        setPositions(positions);
        setTotalCount(count);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching job positions');
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    if (!currentOrganization) return;

    try {
      const { departments, error } = await getDepartments(currentOrganization.id, {
        isActive: true,
      });

      if (!error) {
        setDepartments(departments);
      }
    } catch (err) {
      console.error('Error fetching departments:', err);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
    fetchPositions();
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleAddClick = () => {
    // Reset form state
    setPositionTitle('');
    setPositionDescription('');
    setDepartmentId('');
    setIsActive(true);
    setSubmitError(null);
    setShowAddModal(true);
  };

  const handleEditClick = (position: JobPosition) => {
    setCurrentPosition(position);
    setPositionTitle(position.title);
    setPositionDescription(position.description || '');
    setDepartmentId(position.department_id || '');
    setIsActive(position.is_active !== false);
    setSubmitError(null);
    setShowEditModal(true);
  };

  const handleDeleteClick = (position: JobPosition) => {
    setCurrentPosition(position);
    setDeleteError(null);
    setShowDeleteConfirm(true);
  };

  const handleCreatePosition = async () => {
    if (!currentOrganization) return;

    if (!positionTitle.trim()) {
      setSubmitError('Position title is required');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { position, error } = await createJobPosition(
        currentOrganization.id,
        {
          title: positionTitle.trim(),
          description: positionDescription.trim() || null,
          department_id: departmentId || null,
          is_active: isActive,
        }
      );

      if (error) {
        setSubmitError(error);
      } else {
        setShowAddModal(false);
        fetchPositions(); // Refresh the list
      }
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while creating the job position');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdatePosition = async () => {
    if (!currentOrganization || !currentPosition) return;

    if (!positionTitle.trim()) {
      setSubmitError('Position title is required');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { position, error } = await updateJobPosition(
        currentOrganization.id,
        currentPosition.id,
        {
          title: positionTitle.trim(),
          description: positionDescription.trim() || null,
          department_id: departmentId || null,
          is_active: isActive,
        }
      );

      if (error) {
        setSubmitError(error);
      } else {
        setShowEditModal(false);
        fetchPositions(); // Refresh the list
      }
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while updating the job position');
    } finally {
      setIsSubmitting(false);
    }
  };

  const confirmDelete = async () => {
    if (!currentOrganization || !currentPosition) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const { success, error } = await deleteJobPosition(
        currentOrganization.id,
        currentPosition.id
      );

      if (error) {
        setDeleteError(error);
      } else if (success) {
        setShowDeleteConfirm(false);
        fetchPositions(); // Refresh the list
      }
    } catch (err: any) {
      setDeleteError(err.message || 'An error occurred while deleting the job position');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Job Positions</h1>
          <Button color="primary" onClick={handleAddClick}>
            <HiOutlinePlus className="mr-2 h-5 w-5" />
            Add Position
          </Button>
        </div>

        {error && (
          <Alert color="failure" className="mb-4">
            <HiOutlineExclamation className="h-4 w-4 mr-2" />
            {error}
          </Alert>
        )}

        <div className="mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <form onSubmit={handleSearch} className="md:col-span-1">
            <TextInput
              type="text"
              placeholder="Search positions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={HiOutlineSearch}
              rightIcon={() => (
                <Button type="submit" size="xs" color="light">
                  Search
                </Button>
              )}
            />
          </form>

          <Select
            value={selectedDepartment}
            onChange={(e) => setSelectedDepartment(e.target.value)}
            className="md:col-span-1"
          >
            <option value="">All Departments</option>
            {departments.map((dept) => (
              <option key={dept.id} value={dept.id}>
                {dept.name}
              </option>
            ))}
          </Select>

          <Select
            value={showActiveOnly ? 'active' : 'all'}
            onChange={(e) => setShowActiveOnly(e.target.value === 'active')}
            className="md:col-span-1"
          >
            <option value="active">Active Only</option>
            <option value="all">All</option>
          </Select>
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : positions.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 mb-4">No job positions found</p>
            <Button color="primary" size="sm" onClick={handleAddClick}>
              <HiOutlinePlus className="mr-2 h-4 w-4" />
              Add Your First Position
            </Button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Title</Table.HeadCell>
                <Table.HeadCell>Department</Table.HeadCell>
                <Table.HeadCell>Description</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>
                  <span className="sr-only">Actions</span>
                </Table.HeadCell>
              </Table.Head>
              <Table.Body>
                {positions.map((position) => (
                  <Table.Row key={position.id}>
                    <Table.Cell className="font-medium">
                      {position.title}
                    </Table.Cell>
                    <Table.Cell>
                      {position.department?.name || <span className="text-gray-400">Not assigned</span>}
                    </Table.Cell>
                    <Table.Cell>
                      {position.description || <span className="text-gray-400">No description</span>}
                    </Table.Cell>
                    <Table.Cell>
                      {position.is_active !== false ? (
                        <Badge color="success">Active</Badge>
                      ) : (
                        <Badge color="gray">Inactive</Badge>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex justify-end">
                        <Dropdown
                          label=""
                          dismissOnClick={true}
                          renderTrigger={() => (
                            <Button color="light" size="xs">
                              <HiOutlineDotsVertical className="h-4 w-4" />
                            </Button>
                          )}
                        >
                          <Dropdown.Item
                            icon={HiOutlinePencil}
                            onClick={() => handleEditClick(position)}
                          >
                            Edit
                          </Dropdown.Item>
                          <Dropdown.Item
                            icon={HiOutlineTrash}
                            onClick={() => handleDeleteClick(position)}
                          >
                            Delete
                          </Dropdown.Item>
                        </Dropdown>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / itemsPerPage)}
          itemsPerPage={itemsPerPage}
          totalItems={totalCount}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1); // Reset to first page when changing items per page
          }}
          itemName="positions"
        />
      </Card>

      {/* Add Position Modal */}
      <Modal
        show={showAddModal}
        onClose={() => setShowAddModal(false)}
        size="md"
      >
        <Modal.Header>
          Add New Job Position
        </Modal.Header>
        <Modal.Body>
          {submitError && (
            <Alert color="failure" className="mb-4">
              <HiOutlineExclamation className="h-4 w-4 mr-2" />
              {submitError}
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <div className="mb-2 block">
                <Label htmlFor="positionTitle" value="Position Title *" />
              </div>
              <TextInput
                id="positionTitle"
                value={positionTitle}
                onChange={(e) => setPositionTitle(e.target.value)}
                required
              />
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="departmentId" value="Department" />
              </div>
              <Select
                id="departmentId"
                value={departmentId}
                onChange={(e) => setDepartmentId(e.target.value)}
              >
                <option value="">Select Department</option>
                {departments.map((dept) => (
                  <option key={dept.id} value={dept.id}>
                    {dept.name}
                  </option>
                ))}
              </Select>
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="positionDescription" value="Description" />
              </div>
              <Textarea
                id="positionDescription"
                value={positionDescription}
                onChange={(e) => setPositionDescription(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                id="isActive"
                type="checkbox"
                checked={isActive}
                onChange={(e) => setIsActive(e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <Label htmlFor="isActive" value="Active Position" />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="primary" onClick={handleCreatePosition} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Saving...
              </>
            ) : (
              'Save Position'
            )}
          </Button>
          <Button color="gray" onClick={() => setShowAddModal(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Edit Position Modal */}
      <Modal
        show={showEditModal}
        onClose={() => setShowEditModal(false)}
        size="md"
      >
        <Modal.Header>
          Edit Job Position
        </Modal.Header>
        <Modal.Body>
          {submitError && (
            <Alert color="failure" className="mb-4">
              <HiOutlineExclamation className="h-4 w-4 mr-2" />
              {submitError}
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <div className="mb-2 block">
                <Label htmlFor="editPositionTitle" value="Position Title *" />
              </div>
              <TextInput
                id="editPositionTitle"
                value={positionTitle}
                onChange={(e) => setPositionTitle(e.target.value)}
                required
              />
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="editDepartmentId" value="Department" />
              </div>
              <Select
                id="editDepartmentId"
                value={departmentId}
                onChange={(e) => setDepartmentId(e.target.value)}
              >
                <option value="">Select Department</option>
                {departments.map((dept) => (
                  <option key={dept.id} value={dept.id}>
                    {dept.name}
                  </option>
                ))}
              </Select>
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="editPositionDescription" value="Description" />
              </div>
              <Textarea
                id="editPositionDescription"
                value={positionDescription}
                onChange={(e) => setPositionDescription(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                id="editIsActive"
                type="checkbox"
                checked={isActive}
                onChange={(e) => setIsActive(e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <Label htmlFor="editIsActive" value="Active Position" />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="primary" onClick={handleUpdatePosition} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Saving...
              </>
            ) : (
              'Update Position'
            )}
          </Button>
          <Button color="gray" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        show={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        size="md"
        popup
      >
        <Modal.Header />
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200" />
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              Are you sure you want to delete{' '}
              <span className="font-semibold">
                {currentPosition?.title}
              </span>
              ?
            </h3>
            {deleteError && (
              <Alert color="failure" className="mb-4">
                {deleteError}
              </Alert>
            )}
            <div className="flex justify-center gap-4">
              <Button
                color="failure"
                onClick={confirmDelete}
                disabled={isDeleting}
              >
                {isDeleting ? <Spinner size="sm" /> : 'Yes, delete'}
              </Button>
              <Button
                color="gray"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isDeleting}
              >
                No, cancel
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default JobPositions;
