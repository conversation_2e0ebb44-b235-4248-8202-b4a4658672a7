import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, <PERSON> } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON> } from 'flowbite-react';
import { HiOutlineHome, HiOutlineUsers, HiOutlinePencil, HiOutlineExclamation } from 'react-icons/hi';
import EmployeeForm from '../../components/employees/EmployeeForm';
import { getEmployeeById, updateEmployee, Employee, EmployeeWithDetails, EmployeeGovernmentId } from '../../services/employee';
import { useOrganization } from '../../context/OrganizationContext';

const EditEmployee = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();

  const [employee, setEmployee] = useState<EmployeeWithDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | undefined>();

  useEffect(() => {
    if (currentOrganization && id) {
      fetchEmployee();
    }
  }, [currentOrganization, id]);

  const fetchEmployee = async () => {
    if (!currentOrganization || !id) return;

    setLoading(true);
    setLoadError(null);

    try {
      const { employee, error } = await getEmployeeById(currentOrganization.id, id);

      if (error) {
        setLoadError(error);
      } else if (employee) {
        setEmployee(employee);
      } else {
        setLoadError('Employee not found');
      }
    } catch (err: any) {
      setLoadError(err.message || 'An error occurred while fetching the employee');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (
    employeeData: Partial<Employee>,
    governmentIds: Partial<EmployeeGovernmentId>
  ) => {
    if (!currentOrganization || !id) {
      setSubmitError('No organization selected or invalid employee ID');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(undefined);

    try {
      const { employee, error } = await updateEmployee(
        currentOrganization.id,
        id,
        employeeData,
        governmentIds
      );

      if (error) {
        setSubmitError(error);
      } else if (employee) {
        // Redirect to employee details page
        navigate(`/employees/details/${employee.id}`);
      }
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while updating the employee');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">


      <Card>
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">
            {loading ? 'Loading...' : `Edit Employee: ${employee?.first_name} ${employee?.last_name}`}
          </h1>
          <Link to="/employees" className="text-sm text-primary hover:underline">
            Back to Employees
          </Link>
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : loadError ? (
          <Alert color="failure">
            <HiOutlineExclamation className="h-4 w-4 mr-2" />
            {loadError}
          </Alert>
        ) : employee ? (
          <EmployeeForm
            initialData={employee}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            error={submitError}
          />
        ) : (
          <Alert color="failure">
            <HiOutlineExclamation className="h-4 w-4 mr-2" />
            Employee not found
          </Alert>
        )}
      </Card>
    </div>
  );
};

export default EditEmployee;
