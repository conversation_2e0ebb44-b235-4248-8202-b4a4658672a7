import { useState, useEffect } from 'react';
import {
  Card,
  Table,
  <PERSON><PERSON>,
  Spinner,
  Alert,
  TextInput,
  Dropdown,
  Modal,
  Label,
  Textarea,
  Badge,
  Select,
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineSearch,
  HiOutlineDotsVertical,
  HiOutlineExclamation,
  HiOutlineOfficeBuilding,
} from 'react-icons/hi';
import { getDepartments, Department, createDepartment, updateDepartment, deleteDepartment } from '../../services/department';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import Pagination from '../../components/common/Pagination';

const Departments = () => {
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();

  // List state
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showActiveOnly, setShowActiveOnly] = useState<boolean>(true);

  // Modal states
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<boolean>(false);
  const [currentDepartment, setCurrentDepartment] = useState<Department | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Form state
  const [departmentName, setDepartmentName] = useState<string>('');
  const [departmentDescription, setDepartmentDescription] = useState<string>('');
  const [isActive, setIsActive] = useState<boolean>(true);
  const [managerId, setManagerId] = useState<string>('');

  const [itemsPerPage, setItemsPerPage] = useState(10);

  useEffect(() => {
    if (currentOrganization) {
      fetchDepartments();
    }
  }, [currentOrganization, currentPage, searchQuery, showActiveOnly, itemsPerPage]);

  const fetchDepartments = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { departments, count, error } = await getDepartments(currentOrganization.id, {
        searchQuery,
        isActive: showActiveOnly ? true : undefined,
        limit: itemsPerPage,
        offset: (currentPage - 1) * itemsPerPage,
      });

      if (error) {
        setError(error);
      } else {
        setDepartments(departments);
        setTotalCount(count);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching departments');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
    fetchDepartments();
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleAddClick = () => {
    // Reset form state
    setDepartmentName('');
    setDepartmentDescription('');
    setIsActive(true);
    setManagerId('');
    setSubmitError(null);
    setShowAddModal(true);
  };

  const handleEditClick = (department: Department) => {
    setCurrentDepartment(department);
    setDepartmentName(department.name);
    setDepartmentDescription(department.description || '');
    setIsActive(department.is_active !== false);
    setManagerId(department.manager_id || '');
    setSubmitError(null);
    setShowEditModal(true);
  };

  const handleDeleteClick = (department: Department) => {
    setCurrentDepartment(department);
    setDeleteError(null);
    setShowDeleteConfirm(true);
  };

  const handleCreateDepartment = async () => {
    if (!currentOrganization) return;

    if (!departmentName.trim()) {
      setSubmitError('Department name is required');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { department, error } = await createDepartment(
        currentOrganization.id,
        {
          name: departmentName.trim(),
          description: departmentDescription.trim() || null,
          is_active: isActive,
          manager_id: managerId || null,
        }
      );

      if (error) {
        setSubmitError(error);
      } else {
        setShowAddModal(false);
        fetchDepartments(); // Refresh the list
      }
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while creating the department');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateDepartment = async () => {
    if (!currentOrganization || !currentDepartment) return;

    if (!departmentName.trim()) {
      setSubmitError('Department name is required');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { department, error } = await updateDepartment(
        currentOrganization.id,
        currentDepartment.id,
        {
          name: departmentName.trim(),
          description: departmentDescription.trim() || null,
          is_active: isActive,
          manager_id: managerId || null,
        }
      );

      if (error) {
        setSubmitError(error);
      } else {
        setShowEditModal(false);
        fetchDepartments(); // Refresh the list
      }
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while updating the department');
    } finally {
      setIsSubmitting(false);
    }
  };

  const confirmDelete = async () => {
    if (!currentOrganization || !currentDepartment) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const { success, error } = await deleteDepartment(
        currentOrganization.id,
        currentDepartment.id
      );

      if (error) {
        setDeleteError(error);
      } else if (success) {
        setShowDeleteConfirm(false);
        fetchDepartments(); // Refresh the list
      }
    } catch (err: any) {
      setDeleteError(err.message || 'An error occurred while deleting the department');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Departments</h1>
          <Button color="primary" onClick={handleAddClick}>
            <HiOutlinePlus className="mr-2 h-5 w-5" />
            Add Department
          </Button>
        </div>

        {error && (
          <Alert color="failure" className="mb-4">
            <HiOutlineExclamation className="h-4 w-4 mr-2" />
            {error}
          </Alert>
        )}

        <div className="mb-4 flex flex-col md:flex-row gap-4">
          <form onSubmit={handleSearch} className="flex-1">
            <TextInput
              type="text"
              placeholder="Search departments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={HiOutlineSearch}
              rightIcon={() => (
                <Button type="submit" size="xs" color="light">
                  Search
                </Button>
              )}
            />
          </form>

          <div className="flex items-center">
            <label className="mr-2">Show:</label>
            <Select
              value={showActiveOnly ? 'active' : 'all'}
              onChange={(e) => setShowActiveOnly(e.target.value === 'active')}
              className="w-32"
            >
              <option value="active">Active Only</option>
              <option value="all">All</option>
            </Select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : departments.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 mb-4">No departments found</p>
            <Button color="primary" size="sm" onClick={handleAddClick}>
              <HiOutlinePlus className="mr-2 h-4 w-4" />
              Add Your First Department
            </Button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Name</Table.HeadCell>
                <Table.HeadCell>Description</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>
                  <span className="sr-only">Actions</span>
                </Table.HeadCell>
              </Table.Head>
              <Table.Body>
                {departments.map((department) => (
                  <Table.Row key={department.id}>
                    <Table.Cell className="font-medium">
                      {department.name}
                    </Table.Cell>
                    <Table.Cell>
                      {department.description || <span className="text-gray-400">No description</span>}
                    </Table.Cell>
                    <Table.Cell>
                      {department.is_active !== false ? (
                        <Badge color="success">Active</Badge>
                      ) : (
                        <Badge color="gray">Inactive</Badge>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex justify-end">
                        <Dropdown
                          label=""
                          dismissOnClick={true}
                          renderTrigger={() => (
                            <Button color="light" size="xs">
                              <HiOutlineDotsVertical className="h-4 w-4" />
                            </Button>
                          )}
                        >
                          <Dropdown.Item
                            icon={HiOutlinePencil}
                            onClick={() => handleEditClick(department)}
                          >
                            Edit
                          </Dropdown.Item>
                          <Dropdown.Item
                            icon={HiOutlineTrash}
                            onClick={() => handleDeleteClick(department)}
                          >
                            Delete
                          </Dropdown.Item>
                        </Dropdown>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / itemsPerPage)}
          itemsPerPage={itemsPerPage}
          totalItems={totalCount}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1); // Reset to first page when changing items per page
          }}
          itemName="departments"
        />
      </Card>

      {/* Add Department Modal */}
      <Modal
        show={showAddModal}
        onClose={() => setShowAddModal(false)}
        size="md"
      >
        <Modal.Header>
          Add New Department
        </Modal.Header>
        <Modal.Body>
          {submitError && (
            <Alert color="failure" className="mb-4">
              <HiOutlineExclamation className="h-4 w-4 mr-2" />
              {submitError}
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <div className="mb-2 block">
                <Label htmlFor="departmentName" value="Department Name *" />
              </div>
              <TextInput
                id="departmentName"
                value={departmentName}
                onChange={(e) => setDepartmentName(e.target.value)}
                required
              />
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="departmentDescription" value="Description" />
              </div>
              <Textarea
                id="departmentDescription"
                value={departmentDescription}
                onChange={(e) => setDepartmentDescription(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                id="isActive"
                type="checkbox"
                checked={isActive}
                onChange={(e) => setIsActive(e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <Label htmlFor="isActive" value="Active Department" />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="primary" onClick={handleCreateDepartment} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Saving...
              </>
            ) : (
              'Save Department'
            )}
          </Button>
          <Button color="gray" onClick={() => setShowAddModal(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Edit Department Modal */}
      <Modal
        show={showEditModal}
        onClose={() => setShowEditModal(false)}
        size="md"
      >
        <Modal.Header>
          Edit Department
        </Modal.Header>
        <Modal.Body>
          {submitError && (
            <Alert color="failure" className="mb-4">
              <HiOutlineExclamation className="h-4 w-4 mr-2" />
              {submitError}
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <div className="mb-2 block">
                <Label htmlFor="editDepartmentName" value="Department Name *" />
              </div>
              <TextInput
                id="editDepartmentName"
                value={departmentName}
                onChange={(e) => setDepartmentName(e.target.value)}
                required
              />
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="editDepartmentDescription" value="Description" />
              </div>
              <Textarea
                id="editDepartmentDescription"
                value={departmentDescription}
                onChange={(e) => setDepartmentDescription(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                id="editIsActive"
                type="checkbox"
                checked={isActive}
                onChange={(e) => setIsActive(e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <Label htmlFor="editIsActive" value="Active Department" />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="primary" onClick={handleUpdateDepartment} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Saving...
              </>
            ) : (
              'Update Department'
            )}
          </Button>
          <Button color="gray" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        show={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        size="md"
        popup
      >
        <Modal.Header />
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200" />
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              Are you sure you want to delete{' '}
              <span className="font-semibold">
                {currentDepartment?.name}
              </span>
              ?
            </h3>
            {deleteError && (
              <Alert color="failure" className="mb-4">
                {deleteError}
              </Alert>
            )}
            <div className="flex justify-center gap-4">
              <Button
                color="failure"
                onClick={confirmDelete}
                disabled={isDeleting}
              >
                {isDeleting ? <Spinner size="sm" /> : 'Yes, delete'}
              </Button>
              <Button
                color="gray"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isDeleting}
              >
                No, cancel
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default Departments;
