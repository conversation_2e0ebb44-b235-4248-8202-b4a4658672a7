import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Card,
  Table,
  <PERSON><PERSON>,
  Spinner,
  <PERSON><PERSON>,
  Badge,
  TextInput,
  Dropdown,
  Select,
  Modal,
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineSearch,
  HiOutlineDotsVertical,
  HiOutlineExclamation,
  HiOutlineEye,
  HiOutlineOfficeBuilding,
  HiOutlineBriefcase,
  HiOutlineUserGroup,
  HiOutlineDownload,
  HiOutlineUpload,
} from 'react-icons/hi';
import { getEmployees, Employee, EmployeeWithDetails, deleteEmployee, createEmployee, updateEmployee, getEmployeeById, EmployeeGovernmentId } from '../../services/employee';
import { getDepartments, Department } from '../../services/department';
import { getJobPositions, JobPosition } from '../../services/jobPosition';
import { getEmploymentTypes, EmploymentType } from '../../services/employmentType';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import SimpleEmployeeForm from '../../components/employees/SimpleEmployeeForm';
import Pagination from '../../components/common/Pagination';
import { exportEmployees } from '../../utils/excelExport';
import GenericImport from '../../components/common/GenericImport';
import {
  previewEmployeeImport,
  importEmployees,
  downloadEmployeeImportTemplate,
  EmployeeImportResult
} from '../../services/employeeImport';

const EmployeeList = () => {
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const [employees, setEmployees] = useState<EmployeeWithDetails[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [positions, setPositions] = useState<JobPosition[]>([]);
  const [employmentTypes, setEmploymentTypes] = useState<EmploymentType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [selectedPosition, setSelectedPosition] = useState<string>('');
  const [selectedEmploymentType, setSelectedEmploymentType] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<boolean>(false);
  const [employeeToDelete, setEmployeeToDelete] = useState<Employee | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  // Modal states
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [currentEmployee, setCurrentEmployee] = useState<EmployeeWithDetails | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Import modal
  const [showImportModal, setShowImportModal] = useState(false);

  const [itemsPerPage, setItemsPerPage] = useState(10);

  useEffect(() => {
    if (currentOrganization) {
      fetchEmployees();
      fetchDepartments();
      fetchPositions();
      fetchEmploymentTypes();
    }
  }, [currentOrganization, currentPage, searchQuery, selectedDepartment, selectedPosition, selectedEmploymentType, selectedStatus, itemsPerPage]);

  const fetchEmployees = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { employees, count, error } = await getEmployees(currentOrganization.id, {
        searchQuery,
        departmentId: selectedDepartment || undefined,
        positionId: selectedPosition || undefined,
        employmentTypeId: selectedEmploymentType || undefined,
        status: selectedStatus || undefined,
        limit: itemsPerPage,
        offset: (currentPage - 1) * itemsPerPage,
      });

      if (error) {
        setError(error);
      } else {
        setEmployees(employees);
        setTotalCount(count);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching employees');
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    if (!currentOrganization) return;

    try {
      const { departments, error } = await getDepartments(currentOrganization.id);
      if (!error) {
        setDepartments(departments);
      }
    } catch (err) {
      console.error('Error fetching departments:', err);
    }
  };

  const fetchPositions = async () => {
    if (!currentOrganization) return;

    try {
      const { positions, error } = await getJobPositions(currentOrganization.id);
      if (!error) {
        setPositions(positions);
      }
    } catch (err) {
      console.error('Error fetching positions:', err);
    }
  };

  const fetchEmploymentTypes = async () => {
    if (!currentOrganization) return;

    try {
      const { employmentTypes, error } = await getEmploymentTypes(currentOrganization.id);
      if (!error) {
        setEmploymentTypes(employmentTypes);
      }
    } catch (err) {
      console.error('Error fetching employment types:', err);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
    fetchEmployees();
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleAddClick = () => {
    setSubmitError(null);
    setShowAddModal(true);
  };

  const handleEditClick = async (employee: Employee) => {
    if (!currentOrganization) return;

    setSubmitError(null);

    try {
      const { employee: employeeDetails, error } = await getEmployeeById(
        currentOrganization.id,
        employee.id
      );

      if (error) {
        console.error('Error fetching employee details:', error);
      } else if (employeeDetails) {
        // Store the complete employee details for reference
        setCurrentEmployee(employeeDetails);

        // Clean up the data before showing the modal
        // This ensures we're only passing IDs for related entities, not the whole objects
        setShowEditModal(true);
      }
    } catch (err) {
      console.error('Error fetching employee details:', err);
    }
  };

  const handleDeleteClick = (employee: Employee) => {
    setEmployeeToDelete(employee);
    setDeleteError(null);
    setShowDeleteConfirm(true);
  };

  const handleCreateEmployee = async (employeeData: Partial<Employee>, governmentIds: Partial<EmployeeGovernmentId>) => {
    if (!currentOrganization) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { employee, error } = await createEmployee(
        currentOrganization.id,
        employeeData as Omit<Employee, 'id' | 'organization_id' | 'created_at' | 'updated_at'>,
        governmentIds
      );

      if (error) {
        setSubmitError(error);
      } else {
        setShowAddModal(false);
        fetchEmployees(); // Refresh the list
      }
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while creating the employee');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateEmployee = async (employeeData: Partial<Employee>, governmentIds: Partial<EmployeeGovernmentId>) => {
    if (!currentOrganization || !currentEmployee) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { employee, error } = await updateEmployee(
        currentOrganization.id,
        currentEmployee.id,
        employeeData,
        governmentIds
      );

      if (error) {
        setSubmitError(error);
      } else {
        setShowEditModal(false);
        fetchEmployees(); // Refresh the list
      }
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while updating the employee');
    } finally {
      setIsSubmitting(false);
    }
  };

  const confirmDelete = async () => {
    if (!currentOrganization || !employeeToDelete) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const { success, error } = await deleteEmployee(
        currentOrganization.id,
        employeeToDelete.id
      );

      if (error) {
        setDeleteError(error);
      } else if (success) {
        setShowDeleteConfirm(false);
        fetchEmployees(); // Refresh the list
      }
    } catch (err: any) {
      setDeleteError(err.message || 'An error occurred while deleting the employee');
    } finally {
      setIsDeleting(false);
    }
  };

  // Export employees to Excel
  const handleExportEmployees = () => {
    if (employees.length === 0) {
      setError('No employees to export');
      return;
    }
    exportEmployees(employees);
  };

  // Handle import completion
  const handleImportComplete = (result: EmployeeImportResult) => {
    if (result.successCount > 0) {
      // Refresh the employee list to show imported employees
      fetchEmployees();
    }
    setShowImportModal(false);
  };

  const getStatusBadge = (status: string | null) => {
    if (!status) return null;

    switch (status) {
      case 'active':
        return <Badge color="success">Active</Badge>;
      case 'on_leave':
        return <Badge color="warning">On Leave</Badge>;
      case 'terminated':
        return <Badge color="failure">Terminated</Badge>;
      case 'resigned':
        return <Badge color="gray">Resigned</Badge>;
      case 'retired':
        return <Badge color="purple">Retired</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Employees</h1>
          <div className="flex gap-2">
            <Button color="light" onClick={handleExportEmployees}>
              <HiOutlineDownload className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button color="light" onClick={() => setShowImportModal(true)}>
              <HiOutlineUpload className="mr-2 h-4 w-4" />
              Import
            </Button>
            <Button color="primary" onClick={handleAddClick}>
              <HiOutlinePlus className="mr-2 h-5 w-5" />
              Add Employee
            </Button>
          </div>
        </div>

        {error && (
          <Alert color="failure" className="mb-4">
            <HiOutlineExclamation className="h-4 w-4 mr-2" />
            {error}
          </Alert>
        )}

        <div className="mb-4 grid grid-cols-1 md:grid-cols-5 gap-4">
          <form onSubmit={handleSearch} className="md:col-span-2">
            <TextInput
              type="text"
              placeholder="Search employees..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={HiOutlineSearch}
              rightIcon={() => (
                <Button type="submit" size="xs" color="light">
                  Search
                </Button>
              )}
            />
          </form>

          <Select
            value={selectedDepartment}
            onChange={(e) => setSelectedDepartment(e.target.value)}
            className="md:col-span-1"
          >
            <option value="">All Departments</option>
            {departments.map((dept) => (
              <option key={dept.id} value={dept.id}>
                {dept.name}
              </option>
            ))}
          </Select>

          <Select
            value={selectedPosition}
            onChange={(e) => setSelectedPosition(e.target.value)}
            className="md:col-span-1"
          >
            <option value="">All Positions</option>
            {positions.map((pos) => (
              <option key={pos.id} value={pos.id}>
                {pos.title}
              </option>
            ))}
          </Select>

          <Select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="md:col-span-1"
          >
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="on_leave">On Leave</option>
            <option value="terminated">Terminated</option>
            <option value="resigned">Resigned</option>
            <option value="retired">Retired</option>
          </Select>
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : employees.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 mb-4">No employees found</p>
            <Button color="primary" size="sm" onClick={handleAddClick}>
              <HiOutlinePlus className="mr-2 h-4 w-4" />
              Add Your First Employee
            </Button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Employee</Table.HeadCell>
                <Table.HeadCell>
                  <div className="flex items-center">
                    <HiOutlineOfficeBuilding className="mr-2 h-4 w-4 text-gray-500" />
                    Department
                  </div>
                </Table.HeadCell>
                <Table.HeadCell>
                  <div className="flex items-center">
                    <HiOutlineBriefcase className="mr-2 h-4 w-4 text-gray-500" />
                    Position
                  </div>
                </Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>
                  <span className="sr-only">Actions</span>
                </Table.HeadCell>
              </Table.Head>
              <Table.Body>
                {employees.map((employee) => (
                  <Table.Row key={employee.id}>
                    <Table.Cell>
                      <div className="flex items-center">
                        {employee.profile_image_url ? (
                          <div className="w-10 h-10 overflow-hidden rounded-full border border-gray-200 mr-3">
                            <img
                              src={employee.profile_image_url}
                              alt={`${employee.first_name} ${employee.last_name}`}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                            <span className="text-gray-500 font-semibold">
                              {employee.first_name?.[0]}{employee.last_name?.[0]}
                            </span>
                          </div>
                        )}
                        <div>
                          <Link
                            to={`/employees/details/${employee.id}`}
                            className="font-medium hover:text-primary hover:underline"
                          >
                            {employee.first_name} {employee.last_name}
                          </Link>
                          <p className="text-xs text-gray-500">
                            {employee.email || 'No email'}
                          </p>
                        </div>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center">
                        <HiOutlineOfficeBuilding className="mr-2 h-4 w-4 text-gray-500" />
                        {employee.department?.name || 'Not assigned'}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center">
                        <HiOutlineBriefcase className="mr-2 h-4 w-4 text-gray-500" />
                        {employee.position?.title || 'Not assigned'}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {getStatusBadge(employee.status)}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex justify-end">
                        <Dropdown
                          label=""
                          dismissOnClick={true}
                          renderTrigger={() => (
                            <Button color="light" size="xs">
                              <HiOutlineDotsVertical className="h-4 w-4" />
                            </Button>
                          )}
                        >
                          <Dropdown.Item icon={HiOutlineEye}>
                            <Link to={`/employees/details/${employee.id}`}>View Details</Link>
                          </Dropdown.Item>
                          <Dropdown.Item
                            icon={HiOutlinePencil}
                            onClick={() => handleEditClick(employee)}
                          >
                            Edit
                          </Dropdown.Item>
                          <Dropdown.Item
                            icon={HiOutlineTrash}
                            onClick={() => handleDeleteClick(employee)}
                          >
                            Delete
                          </Dropdown.Item>
                        </Dropdown>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / itemsPerPage)}
          itemsPerPage={itemsPerPage}
          totalItems={totalCount}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1); // Reset to first page when changing items per page
          }}
          itemName="employees"
        />
      </Card>

      {/* Add Employee Modal */}
      <Modal
        show={showAddModal}
        onClose={() => setShowAddModal(false)}
        size="xl"
      >
        <Modal.Header>
          Add New Employee
        </Modal.Header>
        <Modal.Body>
          <SimpleEmployeeForm
            onSubmit={handleCreateEmployee}
            isSubmitting={isSubmitting}
            error={submitError || undefined}
            onCancel={() => setShowAddModal(false)}
          />
        </Modal.Body>
      </Modal>

      {/* Edit Employee Modal */}
      <Modal
        show={showEditModal}
        onClose={() => setShowEditModal(false)}
        size="xl"
      >
        <Modal.Header>
          Edit Employee: {currentEmployee?.first_name} {currentEmployee?.last_name}
        </Modal.Header>
        <Modal.Body>
          {currentEmployee && (
            <SimpleEmployeeForm
              initialData={currentEmployee}
              onSubmit={handleUpdateEmployee}
              isSubmitting={isSubmitting}
              error={submitError || undefined}
              onCancel={() => setShowEditModal(false)}
            />
          )}
        </Modal.Body>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        show={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        size="md"
        popup
      >
        <Modal.Header />
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200" />
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              Are you sure you want to delete{' '}
              <span className="font-semibold">
                {employeeToDelete?.first_name} {employeeToDelete?.last_name}
              </span>
              ?
            </h3>
            {deleteError && (
              <Alert color="failure" className="mb-4">
                {deleteError}
              </Alert>
            )}
            <div className="flex justify-center gap-4">
              <Button
                color="failure"
                onClick={confirmDelete}
                disabled={isDeleting}
              >
                {isDeleting ? <Spinner size="sm" /> : 'Yes, delete'}
              </Button>
              <Button
                color="gray"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isDeleting}
              >
                No, cancel
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>

      {/* Employee Import Modal */}
      <GenericImport
        show={showImportModal}
        onClose={() => setShowImportModal(false)}
        onImportComplete={handleImportComplete}
        title="Import Employees"
        entityName="employees"
        previewFunction={previewEmployeeImport}
        importFunction={importEmployees}
        downloadTemplateFunction={downloadEmployeeImportTemplate}
      />
    </div>
  );
};

export default EmployeeList;
