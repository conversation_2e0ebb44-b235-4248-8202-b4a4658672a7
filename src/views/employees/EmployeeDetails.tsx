import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Badge,
  Button,
  Avatar,
  Modal,
} from 'flowbite-react';
import {
  HiOutlineUser,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineExclamation,
  HiOutlineOfficeBuilding,
  HiOutlineIdentification,
  HiOutlineDocumentText,
  HiOutlineMail,
  HiOutlinePhone,
  HiOutlineCalendar,
  HiOutlineGlobe,
  HiOutlineLocationMarker,
  HiOutlineArrowLeft,
  HiOutlineCamera,
  HiOutlineKey,
  HiOutlineCheck,
} from 'react-icons/hi';
import { getEmployeeById, EmployeeWithDetails, deleteEmployee } from '../../services/employee';
import { useOrganization } from '../../context/OrganizationContext';
import FaceEnrollment from '../../components/faceRecognition/FaceEnrollment';
import { getEmployeeFaceDescriptor, setEmployeePin } from '../../services/faceRecognition';

const EmployeeDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { currentOrganization } = useOrganization();

  const [employee, setEmployee] = useState<EmployeeWithDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<boolean>(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  // Face recognition states
  const [showFaceEnrollment, setShowFaceEnrollment] = useState<boolean>(false);
  const [hasFaceDescriptor, setHasFaceDescriptor] = useState<boolean>(false);
  const [checkingFaceDescriptor, setCheckingFaceDescriptor] = useState<boolean>(false);
  const [showPinSetup, setShowPinSetup] = useState<boolean>(false);
  const [pin, setPin] = useState<string>('');
  const [confirmPin, setConfirmPin] = useState<string>('');
  const [pinError, setPinError] = useState<string | null>(null);
  const [isSettingPin, setIsSettingPin] = useState<boolean>(false);

  useEffect(() => {
    if (currentOrganization && id) {
      fetchEmployee();
      checkFaceDescriptor();
    }
  }, [currentOrganization, id]);

  // Check employee data when it changes
  useEffect(() => {
    // Component will re-render when employee data is loaded
  }, [employee]);

  const fetchEmployee = async () => {
    if (!currentOrganization || !id) return;

    setLoading(true);
    setError(null);

    try {
      const { employee, error } = await getEmployeeById(currentOrganization.id, id);

      if (error) {
        setError(error);
      } else if (employee) {
        setEmployee(employee);
      } else {
        setError('Employee not found');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching the employee');
    } finally {
      setLoading(false);
    }
  };

  const checkFaceDescriptor = async () => {
    if (!currentOrganization || !id) return;

    setCheckingFaceDescriptor(true);
    try {
      const { descriptor, error } = await getEmployeeFaceDescriptor(
        currentOrganization.id,
        id
      );
      setHasFaceDescriptor(!!descriptor && !error);
    } catch (err) {
      console.error('Error checking face descriptor:', err);
      setHasFaceDescriptor(false);
    } finally {
      setCheckingFaceDescriptor(false);
    }
  };

  const handleFaceEnrollmentComplete = (success: boolean) => {
    setShowFaceEnrollment(false);
    if (success) {
      setHasFaceDescriptor(true);
    }
  };

  const handlePinSetup = async () => {
    if (!currentOrganization || !id) return;

    if (pin !== confirmPin) {
      setPinError('PINs do not match');
      return;
    }

    if (pin.length < 4) {
      setPinError('PIN must be at least 4 digits');
      return;
    }

    setIsSettingPin(true);
    setPinError(null);

    try {
      const result = await setEmployeePin(currentOrganization.id, id, pin);
      if (result.success) {
        setShowPinSetup(false);
        setPin('');
        setConfirmPin('');
      } else {
        setPinError(result.error || 'Failed to set PIN');
      }
    } catch (err: any) {
      setPinError(err.message || 'Failed to set PIN');
    } finally {
      setIsSettingPin(false);
    }
  };

  const handleDeleteClick = () => {
    setDeleteError(null);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!currentOrganization || !id) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const { success, error } = await deleteEmployee(
        currentOrganization.id,
        id
      );

      if (error) {
        setDeleteError(error);
      } else if (success) {
        // Redirect to employees list
        window.location.href = '/employees';
      }
    } catch (err: any) {
      setDeleteError(err.message || 'An error occurred while deleting the employee');
    } finally {
      setIsDeleting(false);
    }
  };

  const getStatusBadge = (status: string | null) => {
    if (!status) return null;

    switch (status) {
      case 'active':
        return <Badge color="success">Active</Badge>;
      case 'on_leave':
        return <Badge color="warning">On Leave</Badge>;
      case 'terminated':
        return <Badge color="failure">Terminated</Badge>;
      case 'resigned':
        return <Badge color="gray">Resigned</Badge>;
      case 'retired':
        return <Badge color="purple">Retired</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="container mx-auto px-4 py-8">

      {loading ? (
        <div className="flex justify-center items-center p-8">
          <Spinner size="xl" />
        </div>
      ) : error ? (
        <Alert color="failure">
          <HiOutlineExclamation className="h-4 w-4 mr-2" />
          {error}
        </Alert>
      ) : employee ? (
        <>
          <Card className="mb-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
              <div className="flex items-center">
                <div className="relative w-16 h-16 mr-4">
                  {employee.profile_image_url ? (
                    <img
                      src={employee.profile_image_url}
                      alt={`${employee.first_name} ${employee.last_name}`}
                      className="w-16 h-16 rounded-full object-cover border border-gray-200"
                    />
                  ) : (
                    <Avatar
                      size="lg"
                      rounded
                      placeholderInitials={`${employee.first_name?.[0]}${employee.last_name?.[0]}`}
                      className="w-16 h-16"
                    />
                  )}
                </div>
                <div>
                  <h1 className="text-2xl font-bold">
                    {employee.first_name} {employee.middle_name ? `${employee.middle_name} ` : ''}{employee.last_name}
                  </h1>
                  <div className="flex items-center mt-1">
                    {employee.position?.title && (
                      <span className="text-gray-600 mr-2">{employee.position.title}</span>
                    )}
                    {employee.department?.name && (
                      <span className="text-gray-500">• {employee.department.name}</span>
                    )}
                  </div>
                  <div className="mt-2">
                    {employee.status && getStatusBadge(employee.status)}
                  </div>
                </div>
              </div>
              <div className="flex space-x-2 mt-4 md:mt-0">
                <Link to="/employees">
                  <Button color="gray">
                    <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
                    Back
                  </Button>
                </Link>
                <Link to={`/employees/edit/${employee.id}`}>
                  <Button color="primary">
                    <HiOutlinePencil className="mr-2 h-5 w-5" />
                    Edit
                  </Button>
                </Link>
                <Button color="failure" onClick={handleDeleteClick}>
                  <HiOutlineTrash className="mr-2 h-5 w-5" />
                  Delete
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-200">
              {employee.email && (
                <div className="flex items-center">
                  <HiOutlineMail className="mr-2 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p>{employee.email}</p>
                  </div>
                </div>
              )}
              {employee.phone && (
                <div className="flex items-center">
                  <HiOutlinePhone className="mr-2 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p>{employee.phone}</p>
                  </div>
                </div>
              )}
              {employee.employee_number && (
                <div className="flex items-center">
                  <HiOutlineIdentification className="mr-2 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Employee ID</p>
                    <p>{employee.employee_number}</p>
                  </div>
                </div>
              )}
            </div>
          </Card>

          <div className="grid grid-cols-1 gap-6">
            {/* Personal Information Card */}
            <Card>
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <HiOutlineUser className="mr-2 h-5 w-5 text-gray-600" />
                Personal Information
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h3 className="text-lg font-medium mb-4 text-gray-700">Contact Information</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <HiOutlineMail className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <p>{employee.email || 'Not provided'}</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <HiOutlinePhone className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-500">Phone</p>
                        <p>{employee.phone || 'Not provided'}</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <HiOutlineCalendar className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-500">Date of Birth</p>
                        <p>{employee.date_of_birth ? formatDate(employee.date_of_birth) : 'Not provided'}</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <HiOutlineGlobe className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-500">Nationality</p>
                        <p>{employee.nationality || 'Not provided'}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4 text-gray-700">Address</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <HiOutlineLocationMarker className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-500">Address</p>
                        <p>{employee.address || 'Not provided'}</p>
                        {(employee.city || employee.state || employee.postal_code) && (
                          <p>
                            {employee.city && `${employee.city}, `}
                            {employee.state && `${employee.state} `}
                            {employee.postal_code && employee.postal_code}
                          </p>
                        )}
                        <p>{employee.country || ''}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <h3 className="text-lg font-medium mb-4 text-gray-700">Emergency Contact</h3>
                {employee.emergency_contact_name ? (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Name</p>
                      <p>{employee.emergency_contact_name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Phone</p>
                      <p>{employee.emergency_contact_phone || 'Not provided'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Relationship</p>
                      <p>{employee.emergency_contact_relationship || 'Not provided'}</p>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500">No emergency contact provided</p>
                )}
              </div>
            </Card>

            {/* Employment Details Card */}
            <Card>
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <HiOutlineOfficeBuilding className="mr-2 h-5 w-5 text-gray-600" />
                Employment Details
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h3 className="text-lg font-medium mb-4 text-gray-700">Job Information</h3>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-500">Employee Number</p>
                      <p>{employee.employee_number || 'Not assigned'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Department</p>
                      <p>{employee.department?.name || 'Not assigned'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Position</p>
                      <p>{employee.position?.title || 'Not assigned'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Employment Type</p>
                      <p>{employee.employment_type?.name || 'Not assigned'}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4 text-gray-700">Employment Status</h3>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-500">Status</p>
                      <div className="mt-1">{getStatusBadge(employee.status)}</div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Hire Date</p>
                      <p>{employee.hire_date ? formatDate(employee.hire_date) : 'Not provided'}</p>
                    </div>
                    {employee.end_date && (
                      <div>
                        <p className="text-sm text-gray-500">End Date</p>
                        <p>{formatDate(employee.end_date)}</p>
                      </div>
                    )}
                    <div>
                      <p className="text-sm text-gray-500">Active</p>
                      <p>{employee.is_active ? 'Yes' : 'No'}</p>
                    </div>
                    {employee.user_id && (
                      <div>
                        <p className="text-sm text-gray-500">System User Account</p>
                        <p>Linked to user account</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {employee.notes && (
                <div className="pt-4 border-t border-gray-200">
                  <h3 className="text-lg font-medium mb-4 text-gray-700">Notes</h3>
                  <p className="text-gray-700 whitespace-pre-line">{employee.notes}</p>
                </div>
              )}
            </Card>

            {/* Face Recognition & Security Card */}
            <Card>
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <HiOutlineCamera className="mr-2 h-5 w-5 text-gray-600" />
                Face Recognition & Security
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Face Recognition Status */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-gray-900">Facial Recognition</h3>
                    {checkingFaceDescriptor ? (
                      <Spinner size="sm" />
                    ) : hasFaceDescriptor ? (
                      <Badge color="success">
                        <HiOutlineCheck className="mr-1 h-3 w-3" />
                        Enrolled
                      </Badge>
                    ) : (
                      <Badge color="gray">Not Enrolled</Badge>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    {hasFaceDescriptor
                      ? 'Employee can use facial recognition for time tracking'
                      : 'Set up facial recognition for automated time tracking'
                    }
                  </p>
                  <Button
                    color={hasFaceDescriptor ? 'light' : 'primary'}
                    size="sm"
                    onClick={() => setShowFaceEnrollment(true)}
                  >
                    <HiOutlineCamera className="mr-2 h-4 w-4" />
                    {hasFaceDescriptor ? 'Re-enroll Face' : 'Enroll Face'}
                  </Button>
                </div>

                {/* PIN Setup */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-gray-900">PIN Authentication</h3>
                    <Badge color="info">Fallback Method</Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Set up a PIN as a backup authentication method for time tracking
                  </p>
                  <Button
                    color="light"
                    size="sm"
                    onClick={() => setShowPinSetup(true)}
                  >
                    <HiOutlineKey className="mr-2 h-4 w-4" />
                    Set PIN
                  </Button>
                </div>
              </div>
            </Card>

            {/* Government IDs Card */}
            <Card>
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <HiOutlineIdentification className="mr-2 h-5 w-5 text-gray-600" />
                Government IDs
              </h2>

              {Array.isArray(employee.government_ids) && employee.government_ids.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-sm text-gray-500">SSS Number</p>
                    <p>{employee.government_ids[0].sss_number || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">PhilHealth Number</p>
                    <p>{employee.government_ids[0].philhealth_number || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Pag-IBIG Number</p>
                    <p>{employee.government_ids[0].pagibig_number || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">TIN Number</p>
                    <p>{employee.government_ids[0].tin_number || 'Not provided'}</p>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">No government IDs provided</p>
              )}
            </Card>

            {/* System Information */}
            <div className="mt-2 pt-6 border-t border-gray-200">
              <h2 className="text-sm font-medium text-gray-500 mb-2">System Information</h2>
              <div className="flex flex-wrap gap-6">
                <div>
                  <p className="text-xs text-gray-500">Employee ID</p>
                  <p className="text-sm">{employee.id}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Created</p>
                  <p className="text-sm">{new Date(employee.created_at).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Last Updated</p>
                  <p className="text-sm">{new Date(employee.updated_at).toLocaleString()}</p>
                </div>
                {employee.department && (
                  <div>
                    <p className="text-xs text-gray-500">Department ID</p>
                    <p className="text-sm">{employee.department_id}</p>
                  </div>
                )}
                {employee.position && (
                  <div>
                    <p className="text-xs text-gray-500">Position ID</p>
                    <p className="text-sm">{employee.position_id}</p>
                  </div>
                )}
                {employee.employment_type && (
                  <div>
                    <p className="text-xs text-gray-500">Employment Type ID</p>
                    <p className="text-sm">{employee.employment_type_id}</p>
                  </div>
                )}
                {employee.user_id && (
                  <div>
                    <p className="text-xs text-gray-500">User Account ID</p>
                    <p className="text-sm">{employee.user_id}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      ) : (
        <Alert color="failure">
          <HiOutlineExclamation className="h-4 w-4 mr-2" />
          Employee not found
        </Alert>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        show={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        size="md"
        popup
      >
        <Modal.Header />
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200" />
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              Are you sure you want to delete{' '}
              <span className="font-semibold">
                {employee?.first_name} {employee?.last_name}
              </span>
              ?
            </h3>
            {deleteError && (
              <Alert color="failure" className="mb-4">
                {deleteError}
              </Alert>
            )}
            <div className="flex justify-center gap-4">
              <Button
                color="failure"
                onClick={confirmDelete}
                disabled={isDeleting}
              >
                {isDeleting ? <Spinner size="sm" /> : 'Yes, delete'}
              </Button>
              <Button
                color="gray"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isDeleting}
              >
                No, cancel
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>

      {/* Face Enrollment Modal */}
      {showFaceEnrollment && employee && (
        <Modal show={showFaceEnrollment} onClose={() => setShowFaceEnrollment(false)} size="6xl">
          <Modal.Body className="p-0">
            <FaceEnrollment
              employeeId={employee.id}
              employeeName={`${employee.first_name} ${employee.last_name}`}
              onEnrollmentComplete={handleFaceEnrollmentComplete}
              onCancel={() => setShowFaceEnrollment(false)}
            />
          </Modal.Body>
        </Modal>
      )}

      {/* PIN Setup Modal */}
      <Modal show={showPinSetup} onClose={() => setShowPinSetup(false)} size="md">
        <Modal.Header>Set Employee PIN</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <div>
              <label htmlFor="pin" className="block text-sm font-medium text-gray-700 mb-2">
                PIN (4-6 digits)
              </label>
              <input
                type="password"
                id="pin"
                value={pin}
                onChange={(e) => setPin(e.target.value.replace(/\D/g, '').slice(0, 6))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter PIN"
                maxLength={6}
              />
            </div>

            <div>
              <label htmlFor="confirmPin" className="block text-sm font-medium text-gray-700 mb-2">
                Confirm PIN
              </label>
              <input
                type="password"
                id="confirmPin"
                value={confirmPin}
                onChange={(e) => setConfirmPin(e.target.value.replace(/\D/g, '').slice(0, 6))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Confirm PIN"
                maxLength={6}
              />
            </div>

            {pinError && (
              <Alert color="failure">
                <HiOutlineExclamation className="h-4 w-4" />
                <span>{pinError}</span>
              </Alert>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="primary"
            onClick={handlePinSetup}
            disabled={isSettingPin || pin.length < 4 || pin !== confirmPin}
          >
            {isSettingPin ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Setting PIN...
              </>
            ) : (
              'Set PIN'
            )}
          </Button>
          <Button
            color="gray"
            onClick={() => {
              setShowPinSetup(false);
              setPin('');
              setConfirmPin('');
              setPinError(null);
            }}
          >
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default EmployeeDetails;
