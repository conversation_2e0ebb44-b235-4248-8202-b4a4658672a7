import { useState, useEffect } from 'react';
import { Card, Button, TextInput, Label, Select, Alert, Spinner, Table, Badge, Modal } from 'flowbite-react';
import { useAuth } from '../../context/AuthContext';
import { useOrganization } from '../../context/OrganizationContext';
import {
  inviteUserToOrganization,
  getOrganizationInvitations,
  deleteInvitation,
  resendInvitation,
  Role
} from '../../services/userManagement';
import { HiOutlineMail, HiOutlineTrash, HiOutlineRefresh, HiOutlineClipboardCopy, HiOutlineExclamation } from 'react-icons/hi';
import { formatDistanceToNow } from 'date-fns';
import Pagination from '../../components/common/Pagination';

const InviteUser = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  const [email, setEmail] = useState<string>('');
  const [role, setRole] = useState<Role>('member');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [invitations, setInvitations] = useState<any[]>([]);
  const [loadingInvitations, setLoadingInvitations] = useState<boolean>(false);
  const [invitationUrl, setInvitationUrl] = useState<string | null>(null);
  const [showLinkModal, setShowLinkModal] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [selectedInvitationId, setSelectedInvitationId] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const fetchInvitations = async () => {
    if (!currentOrganization) return;

    setLoadingInvitations(true);

    try {
      const { invitations: invitationData, error: invitationsError } = await getOrganizationInvitations(
        currentOrganization.id
      );

      if (invitationsError) {
        console.error('Error fetching invitations:', invitationsError);
      } else {
        setInvitations(invitationData);
      }
    } catch (err) {
      console.error('Error fetching invitations:', err);
    } finally {
      setLoadingInvitations(false);
    }
  };

  useEffect(() => {
    fetchInvitations();
  }, [currentOrganization]);

  const handleInvite = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization || !user) {
      setError('You must be logged in and have an organization selected');
      return;
    }

    if (!email) {
      setError('Email is required');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);
    setInvitationUrl(null);

    try {
      const { success, invitationUrl: url, error: inviteError } = await inviteUserToOrganization(
        currentOrganization.id,
        email,
        role,
        user.id
      );

      if (!success) {
        setError(inviteError || 'Failed to invite user');
      } else {
        // Even if there's a warning, consider it a success but show the warning
        if (inviteError) {
          setError(inviteError); // Show as a warning
        } else {
          setSuccess(`Invitation sent to ${email}`);
        }

        if (url) {
          setInvitationUrl(url);
          setShowLinkModal(true);
        }

        setEmail('');
        fetchInvitations();
      }
    } catch (err: any) {
      console.error('Error inviting user:', err);
      setError(err.message || 'Failed to invite user');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteInvitation = async () => {
    if (!selectedInvitationId) return;

    setActionLoading(true);

    try {
      const { success, error: deleteError } = await deleteInvitation(selectedInvitationId);

      if (!success) {
        setError(deleteError || 'Failed to delete invitation');
      } else {
        setSuccess('Invitation deleted successfully');
        fetchInvitations();
      }
    } catch (err: any) {
      setError(err.message || 'Failed to delete invitation');
    } finally {
      setActionLoading(false);
      setShowDeleteModal(false);
      setSelectedInvitationId(null);
    }
  };

  const handleResendInvitation = async (invitationId: string) => {
    setActionLoading(true);
    setError(null);
    setSuccess(null);
    setInvitationUrl(null);

    try {
      const { success, invitationUrl: url, error: resendError } = await resendInvitation(invitationId);

      if (!success) {
        setError(resendError || 'Failed to resend invitation');
      } else {
        // Even if there's a warning, consider it a success but show the warning
        if (resendError) {
          setError(resendError); // Show as a warning
        } else {
          setSuccess('Invitation resent successfully');
        }

        if (url) {
          setInvitationUrl(url);
          setShowLinkModal(true);
        }
      }
    } catch (err: any) {
      console.error('Error resending invitation:', err);
      setError(err.message || 'Failed to resend invitation');
    } finally {
      setActionLoading(false);
    }
  };

  const copyInvitationLink = () => {
    if (invitationUrl) {
      navigator.clipboard.writeText(invitationUrl);
      setSuccess('Invitation link copied to clipboard');
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'owner':
        return 'purple';
      case 'admin':
        return 'red';
      case 'cashier':
        return 'green';
      case 'inventory_manager':
        return 'blue';
      case 'purchaser':
        return 'yellow';
      case 'employee':
        return 'gray';
      default:
        return 'indigo';
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(invitations.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentInvitations = invitations.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <h1 className="text-2xl font-bold mb-4">Invite User</h1>

        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}

        {success && (
          <Alert color="success" className="mb-4">
            {success}
          </Alert>
        )}

        <form onSubmit={handleInvite}>
          <div className="mb-4">
            <div className="mb-2 block">
              <Label htmlFor="email" value="Email Address" />
            </div>
            <TextInput
              id="email"
              type="email"
              icon={HiOutlineMail}
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>

          <div className="mb-6">
            <div className="mb-2 block">
              <Label htmlFor="role" value="Role" />
            </div>
            <Select
              id="role"
              value={role}
              onChange={(e) => setRole(e.target.value as Role)}
              required
            >
              <option value="admin">Admin</option>
              <option value="member">Member</option>
              <option value="cashier">Cashier</option>
              <option value="inventory_manager">Inventory Manager</option>
              <option value="purchaser">Purchaser</option>
              <option value="employee">Employee</option>
            </Select>
            <p className="mt-1 text-sm text-gray-500">
              {role === 'admin' && 'Admins can manage users, products, inventory, and settings.'}
              {role === 'member' && 'Members have basic access to view products and inventory.'}
              {role === 'cashier' && 'Cashiers can process sales and manage customers.'}
              {role === 'inventory_manager' && 'Inventory managers can manage products and inventory.'}
              {role === 'purchaser' && 'Purchasers can create purchase orders and manage suppliers.'}
              {role === 'employee' && 'Employees have limited access to view products and inventory.'}
            </p>
          </div>

          <Button type="submit" disabled={loading}>
            {loading ? <Spinner size="sm" className="mr-2" /> : null}
            Send Invitation
          </Button>
        </form>
      </Card>

      <Card className="mt-8">
        <h2 className="text-xl font-bold mb-4">Pending Invitations</h2>

        {loadingInvitations ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : invitations.length === 0 ? (
          <p className="text-gray-500">No pending invitations</p>
        ) : (
          <Table>
            <Table.Head>
              <Table.HeadCell>Email</Table.HeadCell>
              <Table.HeadCell>Role</Table.HeadCell>
              <Table.HeadCell>Sent</Table.HeadCell>
              <Table.HeadCell>Expires</Table.HeadCell>
              <Table.HeadCell>Actions</Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y">
              {currentInvitations.map((invitation) => (
                <Table.Row key={invitation.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                  <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                    {invitation.email}
                  </Table.Cell>
                  <Table.Cell>
                    <Badge color={getRoleBadgeColor(invitation.role)}>
                      {invitation.role}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    {formatDistanceToNow(new Date(invitation.created_at), { addSuffix: true })}
                  </Table.Cell>
                  <Table.Cell>
                    {formatDistanceToNow(new Date(invitation.expires_at), { addSuffix: true })}
                  </Table.Cell>
                  <Table.Cell>
                    <div className="flex space-x-2">
                      <Button
                        color="light"
                        size="xs"
                        onClick={() => handleResendInvitation(invitation.id)}
                        disabled={actionLoading}
                      >
                        <HiOutlineRefresh className="h-4 w-4" />
                      </Button>
                      <Button
                        color="failure"
                        size="xs"
                        onClick={() => {
                          setSelectedInvitationId(invitation.id);
                          setShowDeleteModal(true);
                        }}
                        disabled={actionLoading}
                      >
                        <HiOutlineTrash className="h-4 w-4" />
                      </Button>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        )}

        {/* Pagination */}
        {invitations.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={invitations.length}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="invitations"
          />
        )}
      </Card>

      {/* Invitation Link Modal */}
      <Modal show={showLinkModal} onClose={() => setShowLinkModal(false)}>
        <Modal.Header>Invitation Link</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <Alert color="warning">
              <p className="font-medium">
                Email sending is not configured
              </p>
              <p className="text-sm mt-1">
                Please share this invitation link with the user manually:
              </p>
            </Alert>
            <div className="flex items-center space-x-2">
              <TextInput
                id="invitationLink"
                type="text"
                value={invitationUrl || ''}
                readOnly
                className="flex-1"
              />
              <Button color="light" onClick={copyInvitationLink}>
                <HiOutlineClipboardCopy className="h-5 w-5" />
              </Button>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
              <h4 className="font-medium mb-2">Instructions for the invited user:</h4>
              <ol className="list-decimal list-inside text-sm space-y-1">
                <li>Click the link above or copy and paste it into your browser</li>
                <li>Create an account or sign in if you already have one</li>
                <li>You'll be automatically added to the organization</li>
              </ol>
            </div>
            <Alert color="info">
              <p className="text-sm">
                This link will expire in 48 hours. The recipient will need to create an account or sign in to accept the invitation.
              </p>
            </Alert>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="primary" onClick={copyInvitationLink}>
            Copy Link
          </Button>
          <Button color="gray" onClick={() => setShowLinkModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} size="md" popup onClose={() => setShowDeleteModal(false)}>
        <Modal.Header />
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200" />
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              Are you sure you want to delete this invitation?
            </h3>
            <div className="flex justify-center gap-4">
              <Button color="failure" onClick={handleDeleteInvitation}>
                {actionLoading ? <Spinner size="sm" className="mr-2" /> : null}
                Yes, I'm sure
              </Button>
              <Button color="gray" onClick={() => setShowDeleteModal(false)}>
                No, cancel
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default InviteUser;
