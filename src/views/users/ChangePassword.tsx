import { useState } from 'react';
import { <PERSON>, Button, TextInput, Label, Alert, Spinner } from 'flowbite-react';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../lib/supabase';
import { HiOutlineLockClosed } from 'react-icons/hi';

const ChangePassword = () => {
  const { user } = useAuth();
  
  const [currentPassword, setCurrentPassword] = useState<string>('');
  const [newPassword, setNewPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      setError('You must be logged in to change your password');
      return;
    }
    
    if (newPassword !== confirmPassword) {
      setError('New passwords do not match');
      return;
    }
    
    if (newPassword.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }
    
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      // First, verify the current password by signing in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email || '',
        password: currentPassword,
      });
      
      if (signInError) {
        throw new Error('Current password is incorrect');
      }
      
      // Then, update the password
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      });
      
      if (updateError) {
        throw updateError;
      }
      
      setSuccess('Password changed successfully');
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (err: any) {
      console.error('Error changing password:', err);
      setError(err.message || 'Failed to change password');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <h1 className="text-2xl font-bold mb-4">Change Password</h1>
        
        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert color="success" className="mb-4">
            {success}
          </Alert>
        )}
        
        <form onSubmit={handleChangePassword}>
          <div className="mb-4">
            <div className="mb-2 block">
              <Label htmlFor="currentPassword" value="Current Password" />
            </div>
            <TextInput
              id="currentPassword"
              type="password"
              icon={HiOutlineLockClosed}
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              required
            />
          </div>
          
          <div className="mb-4">
            <div className="mb-2 block">
              <Label htmlFor="newPassword" value="New Password" />
            </div>
            <TextInput
              id="newPassword"
              type="password"
              icon={HiOutlineLockClosed}
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
            />
            <p className="mt-1 text-sm text-gray-500">
              Password must be at least 8 characters long
            </p>
          </div>
          
          <div className="mb-6">
            <div className="mb-2 block">
              <Label htmlFor="confirmPassword" value="Confirm New Password" />
            </div>
            <TextInput
              id="confirmPassword"
              type="password"
              icon={HiOutlineLockClosed}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
          </div>
          
          <Button type="submit" disabled={loading}>
            {loading ? <Spinner size="sm" className="mr-2" /> : null}
            Change Password
          </Button>
        </form>
      </Card>
    </div>
  );
};

export default ChangePassword;
