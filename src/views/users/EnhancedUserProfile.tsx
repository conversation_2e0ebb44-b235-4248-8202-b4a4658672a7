import { useState, useEffect, useRef } from 'react';
import {
  Card,
  Button,
  TextInput,
  Label,
  Alert,
  Spinner,
  Avatar,
  Tabs,
  Badge,
  Tooltip
} from 'flowbite-react';
import {
  HiOutlineUser,
  HiOutlineMail,
  HiOutlinePhotograph,
  HiOutlineCog,
  HiOutlineShieldCheck,
  HiOutlineOfficeBuilding,
  HiOutlineCamera,
  HiOutlineCheck,
  HiOutlineX,
  HiOutlinePencil
} from 'react-icons/hi';
import { useAuth } from '../../context/AuthContext';
import { useOrganization } from '../../context/OrganizationContext';
import { supabase } from '../../lib/supabase';
import ProfileInfoCard from '../../components/users/ProfileInfoCard';
import ActivityTimeline from '../../components/users/ActivityTimeline';
import SecuritySettings from '../../components/users/SecuritySettings';

const EnhancedUserProfile = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // User profile state
  const [profile, setProfile] = useState({
    firstName: '',
    lastName: '',
    email: '',
    role: '',
    avatarUrl: '',
    jobTitle: '',
    department: '',
    phoneNumber: '',
    location: '',
    bio: '',
    joinDate: '',
  });

  // UI state
  const [loading, setLoading] = useState<boolean>(true);
  const [saving, setSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<number>(0);

  // Activity data (mock data for now)
  const [activities, setActivities] = useState([
    {
      id: '1',
      type: 'login',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
      details: 'Logged in from Chrome on Mac OS X'
    },
    {
      id: '2',
      type: 'profile_update',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(),
      details: 'Updated profile information'
    },
    {
      id: '3',
      type: 'password_change',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),
      details: 'Changed password'
    }
  ]);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        // Fetch profile data
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .maybeSingle();

        if (error) {
          throw error;
        }

        // Fetch organization role
        const { data: memberData, error: memberError } = await supabase
          .from('organization_members')
          .select('role')
          .eq('user_id', user.id)
          .eq('organization_id', currentOrganization?.id)
          .single();

        if (memberError && memberError.code !== 'PGRST116') {
          console.error('Error fetching member role:', memberError);
        }

        if (data) {
          setProfile({
            firstName: data.first_name || '',
            lastName: data.last_name || '',
            email: user.email || '',
            role: memberData?.role || 'member',
            avatarUrl: data.avatar_url || '',
            jobTitle: data.job_title || '',
            department: data.department || '',
            phoneNumber: data.phone_number || '',
            location: data.location || '',
            bio: data.bio || '',
            joinDate: data.created_at || '',
          });
        }
      } catch (err: any) {
        console.error('Error fetching profile:', err);
        setError(err.message || 'Failed to fetch profile');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [user, currentOrganization]);

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      setError('You must be logged in to update your profile');
      return;
    }

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: profile.firstName,
          last_name: profile.lastName,
          job_title: profile.jobTitle,
          department: profile.department,
          phone_number: profile.phoneNumber,
          location: profile.location,
          bio: profile.bio,
        })
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      setSuccess('Profile updated successfully');
      setEditMode(false);
    } catch (err: any) {
      console.error('Error updating profile:', err);
      setError(err.message || 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const handleAvatarUpload = async (file: File) => {
    if (!user) {
      setError('You must be logged in to update your avatar');
      return;
    }

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const fileExt = file.name.split('.').pop();
      const filePath = `${user.id}-${Math.random().toString(36).substring(2)}.${fileExt}`;

      // Upload the file to storage
      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      // Get the public URL
      const { data: urlData } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      const avatarUrl = urlData.publicUrl;

      // Update the profile with the new avatar URL
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          avatar_url: avatarUrl,
        })
        .eq('id', user.id);

      if (updateError) {
        throw updateError;
      }

      setProfile({
        ...profile,
        avatarUrl
      });
      setSuccess('Avatar updated successfully');
    } catch (err: any) {
      console.error('Error uploading avatar:', err);
      setError(err.message || 'Failed to upload avatar');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfile({
      ...profile,
      [name]: value
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Profile Summary */}
        <div className="lg:col-span-1">
          <ProfileInfoCard
            profile={profile}
            onEditClick={() => setEditMode(true)}
            onAvatarUpload={handleAvatarUpload}
          />
        </div>

        {/* Right Column - Profile Details */}
        <div className="lg:col-span-2">
          <Card>
            {error && (
              <Alert color="failure" className="mb-4">
                {error}
              </Alert>
            )}

            {success && (
              <Alert color="success" className="mb-4">
                {success}
              </Alert>
            )}

            <div>
              <div className="mb-4 border-b border-gray-200">
                <ul className="flex flex-wrap -mb-px text-sm font-medium text-center">
                  <li className="mr-2" role="presentation">
                    <button
                      className={`inline-block p-4 border-b-2 rounded-t-lg ${
                        activeTab === 0
                          ? 'border-blue-600 text-blue-600'
                          : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                      }`}
                      type="button"
                      onClick={() => setActiveTab(0)}
                    >
                      <div className="flex items-center">
                        <HiOutlineUser className="mr-2 h-5 w-5" />
                        Profile Information
                      </div>
                    </button>
                  </li>
                  <li className="mr-2" role="presentation">
                    <button
                      className={`inline-block p-4 border-b-2 rounded-t-lg ${
                        activeTab === 1
                          ? 'border-blue-600 text-blue-600'
                          : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                      }`}
                      type="button"
                      onClick={() => setActiveTab(1)}
                    >
                      <div className="flex items-center">
                        <HiOutlineCog className="mr-2 h-5 w-5" />
                        Activity
                      </div>
                    </button>
                  </li>
                  <li className="mr-2" role="presentation">
                    <button
                      className={`inline-block p-4 border-b-2 rounded-t-lg ${
                        activeTab === 2
                          ? 'border-blue-600 text-blue-600'
                          : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                      }`}
                      type="button"
                      onClick={() => setActiveTab(2)}
                    >
                      <div className="flex items-center">
                        <HiOutlineShieldCheck className="mr-2 h-5 w-5" />
                        Security
                      </div>
                    </button>
                  </li>
                </ul>
              </div>

              {/* Profile Information Tab */}
              {activeTab === 0 && (
                <>
                  {editMode ? (
                    <form onSubmit={handleUpdateProfile} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="firstName" value="First Name" />
                          <TextInput
                            id="firstName"
                            name="firstName"
                            type="text"
                            value={profile.firstName}
                            onChange={handleInputChange}
                            required
                          />
                        </div>

                        <div>
                          <Label htmlFor="lastName" value="Last Name" />
                          <TextInput
                            id="lastName"
                            name="lastName"
                            type="text"
                            value={profile.lastName}
                            onChange={handleInputChange}
                            required
                          />
                        </div>

                        <div>
                          <Label htmlFor="email" value="Email Address" />
                          <TextInput
                            id="email"
                            name="email"
                            type="email"
                            value={profile.email}
                            disabled
                          />
                        </div>

                        <div>
                          <Label htmlFor="phoneNumber" value="Phone Number" />
                          <TextInput
                            id="phoneNumber"
                            name="phoneNumber"
                            type="tel"
                            value={profile.phoneNumber}
                            onChange={handleInputChange}
                          />
                        </div>

                        <div>
                          <Label htmlFor="jobTitle" value="Job Title" />
                          <TextInput
                            id="jobTitle"
                            name="jobTitle"
                            type="text"
                            value={profile.jobTitle}
                            onChange={handleInputChange}
                          />
                        </div>

                        <div>
                          <Label htmlFor="department" value="Department" />
                          <TextInput
                            id="department"
                            name="department"
                            type="text"
                            value={profile.department}
                            onChange={handleInputChange}
                          />
                        </div>

                        <div className="md:col-span-2">
                          <Label htmlFor="location" value="Location" />
                          <TextInput
                            id="location"
                            name="location"
                            type="text"
                            value={profile.location}
                            onChange={handleInputChange}
                          />
                        </div>

                        <div className="md:col-span-2">
                          <Label htmlFor="bio" value="Bio" />
                          <textarea
                            id="bio"
                            name="bio"
                            rows={4}
                            className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                            value={profile.bio}
                            onChange={handleInputChange}
                          />
                        </div>
                      </div>

                      <div className="flex justify-end space-x-3">
                        <Button
                          color="gray"
                          onClick={() => setEditMode(false)}
                          disabled={saving}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="submit"
                          disabled={saving}
                        >
                          {saving ? <Spinner size="sm" className="mr-2" /> : null}
                          Save Changes
                        </Button>
                      </div>
                    </form>
                  ) : (
                    <div className="space-y-6">
                      <div className="flex justify-between items-center">
                        <h3 className="text-xl font-semibold">Personal Information</h3>
                        <Button
                          color="light"
                          size="sm"
                          onClick={() => setEditMode(true)}
                        >
                          <HiOutlinePencil className="mr-2 h-4 w-4" />
                          Edit
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4">
                        <div>
                          <p className="text-sm font-medium text-gray-500">Full Name</p>
                          <p>{profile.firstName} {profile.lastName}</p>
                        </div>

                        <div>
                          <p className="text-sm font-medium text-gray-500">Email</p>
                          <p>{profile.email}</p>
                        </div>

                        <div>
                          <p className="text-sm font-medium text-gray-500">Phone</p>
                          <p>{profile.phoneNumber || 'Not specified'}</p>
                        </div>

                        <div>
                          <p className="text-sm font-medium text-gray-500">Location</p>
                          <p>{profile.location || 'Not specified'}</p>
                        </div>

                        <div>
                          <p className="text-sm font-medium text-gray-500">Job Title</p>
                          <p>{profile.jobTitle || 'Not specified'}</p>
                        </div>

                        <div>
                          <p className="text-sm font-medium text-gray-500">Department</p>
                          <p>{profile.department || 'Not specified'}</p>
                        </div>

                        {profile.bio && (
                          <div className="md:col-span-2 mt-4">
                            <p className="text-sm font-medium text-gray-500">Bio</p>
                            <p className="whitespace-pre-line">{profile.bio}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* Activity Tab */}
              {activeTab === 1 && (
                <ActivityTimeline activities={activities} />
              )}

              {/* Security Tab */}
              {activeTab === 2 && (
                <SecuritySettings userId={user?.id} />
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default EnhancedUserProfile;
