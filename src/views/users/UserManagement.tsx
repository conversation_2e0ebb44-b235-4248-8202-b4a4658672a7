import { useState, useEffect } from 'react';
import { Card, Table, Button, Badge, Modal, TextInput, Label, Select, Alert, Spinner } from 'flowbite-react';
import { useAuth } from '../../context/AuthContext';
import { useOrganization } from '../../context/OrganizationContext';
import { usePermission, PermissionGate } from '../../context/PermissionContext';
import {
  getOrganizationMembers,
  updateUserRole,
  adminResetPassword,
  canResetUserPassword,
  OrganizationMember,
  Role
} from '../../services/userManagement';
import { HiOutlinePencil, HiOutlineKey, HiOutlineTrash, HiOutlinePlus } from 'react-icons/hi';
import Pagination from '../../components/common/Pagination';

const UserManagement = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const { checkPermission } = usePermission();

  const [members, setMembers] = useState<OrganizationMember[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [showRoleModal, setShowRoleModal] = useState<boolean>(false);
  const [showPasswordModal, setShowPasswordModal] = useState<boolean>(false);
  const [selectedMember, setSelectedMember] = useState<OrganizationMember | null>(null);
  const [newRole, setNewRole] = useState<Role>('member');
  const [newPassword, setNewPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');
  const [modalLoading, setModalLoading] = useState<boolean>(false);
  const [modalError, setModalError] = useState<string | null>(null);

  const fetchMembers = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { members: orgMembers, error: membersError } = await getOrganizationMembers(currentOrganization.id);

      if (membersError) {
        setError(membersError);
      } else {
        setMembers(orgMembers);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch organization members');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMembers();
  }, [currentOrganization]);

  const handleRoleChange = async () => {
    if (!currentOrganization || !user || !selectedMember) return;

    setModalLoading(true);
    setModalError(null);

    try {
      const { success, error: roleError } = await updateUserRole(
        currentOrganization.id,
        selectedMember.user_id,
        newRole
      );

      if (!success) {
        setModalError(roleError || 'Failed to update role');
      } else {
        setShowRoleModal(false);
        setSuccess(`Role updated successfully for ${selectedMember.profile?.first_name} ${selectedMember.profile?.last_name}`);
        fetchMembers();
      }
    } catch (err: any) {
      setModalError(err.message || 'Failed to update role');
    } finally {
      setModalLoading(false);
    }
  };

  const handlePasswordReset = async () => {
    if (!currentOrganization || !user || !selectedMember) return;

    if (newPassword !== confirmPassword) {
      setModalError('Passwords do not match');
      return;
    }

    if (newPassword.length < 8) {
      setModalError('Password must be at least 8 characters long');
      return;
    }

    setModalLoading(true);
    setModalError(null);

    try {
      // First check if the user has permission to reset the password
      const { canReset, error: permissionError } = await canResetUserPassword(
        currentOrganization.id,
        user.id,
        selectedMember.user_id
      );

      if (!canReset) {
        setModalError(permissionError || 'You do not have permission to reset this user\'s password');
        setModalLoading(false);
        return;
      }

      // Now try to reset the password
      const { success, message, error: passwordError } = await adminResetPassword(
        currentOrganization.id,
        user.id,
        selectedMember.user_id,
        newPassword
      );

      if (!success) {
        setModalError(passwordError || 'Failed to reset password');
      } else {
        setShowPasswordModal(false);
        setSuccess(message || `Password reset successfully for ${selectedMember.profile?.first_name} ${selectedMember.profile?.last_name}`);
      }
    } catch (err: any) {
      setModalError(err.message || 'Failed to reset password');
    } finally {
      setModalLoading(false);
    }
  };

  const openRoleModal = (member: OrganizationMember) => {
    setSelectedMember(member);
    setNewRole(member.role as Role);
    setModalError(null);
    setShowRoleModal(true);
  };

  const openPasswordModal = (member: OrganizationMember) => {
    setSelectedMember(member);
    setNewPassword('');
    setConfirmPassword('');
    setModalError(null);
    setShowPasswordModal(true);
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'owner':
        return 'purple';
      case 'admin':
        return 'red';
      case 'cashier':
        return 'green';
      case 'inventory_manager':
        return 'blue';
      case 'purchaser':
        return 'yellow';
      case 'employee':
        return 'gray';
      default:
        return 'indigo';
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(members.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentMembers = members.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">User Management</h1>

          <PermissionGate module="users" action="create">
            <Button color="primary" size="sm">
              <HiOutlinePlus className="mr-2 h-4 w-4" />
              Invite User
            </Button>
          </PermissionGate>
        </div>

        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}

        {success && (
          <Alert color="success" className="mb-4">
            {success}
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : (
          <Table>
            <Table.Head>
              <Table.HeadCell>Name</Table.HeadCell>
              <Table.HeadCell>Email</Table.HeadCell>
              <Table.HeadCell>Role</Table.HeadCell>
              <Table.HeadCell>Actions</Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y">
              {currentMembers.map((member) => (
                <Table.Row key={member.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                  <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                    {member.profile ? `${member.profile.first_name || ''} ${member.profile.last_name || ''}` : 'Unknown User'}
                  </Table.Cell>
                  <Table.Cell>{member.profile?.email || 'No email available'}</Table.Cell>
                  <Table.Cell>
                    <Badge color={getRoleBadgeColor(member.role)}>
                      {member.role}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <div className="flex space-x-2">
                      <PermissionGate module="users" action="change_role">
                        <Button
                          color="light"
                          size="xs"
                          onClick={() => openRoleModal(member)}
                          disabled={
                            member.role === 'owner' &&
                            !checkPermission('users', 'delete') // Only users with delete permission can change owner role
                          }
                        >
                          <HiOutlinePencil className="h-4 w-4" />
                        </Button>
                      </PermissionGate>

                      <PermissionGate module="users" action="reset_password">
                        <Button
                          color="light"
                          size="xs"
                          onClick={() => openPasswordModal(member)}
                          disabled={
                            member.role === 'owner' &&
                            !checkPermission('users', 'delete') // Only users with delete permission can reset owner password
                          }
                        >
                          <HiOutlineKey className="h-4 w-4" />
                        </Button>
                      </PermissionGate>

                      <PermissionGate module="users" action="delete">
                        <Button
                          color="failure"
                          size="xs"
                          disabled={member.role === 'owner'} // Cannot delete owners
                        >
                          <HiOutlineTrash className="h-4 w-4" />
                        </Button>
                      </PermissionGate>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        )}

        {/* Pagination */}
        {members.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={members.length}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="users"
          />
        )}
      </Card>

      {/* Change Role Modal */}
      <Modal show={showRoleModal} onClose={() => setShowRoleModal(false)}>
        <Modal.Header>Change User Role</Modal.Header>
        <Modal.Body>
          {modalError && (
            <Alert color="failure" className="mb-4">
              {modalError}
            </Alert>
          )}

          <div className="space-y-6">
            <p>
              Changing role for: <strong>{selectedMember?.profile?.first_name} {selectedMember?.profile?.last_name}</strong>
            </p>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="role" value="Select Role" />
              </div>
              <Select
                id="role"
                value={newRole}
                onChange={(e) => setNewRole(e.target.value as Role)}
                required
              >
                <option value="admin">Admin</option>
                <option value="member">Member</option>
                <option value="cashier">Cashier</option>
                <option value="inventory_manager">Inventory Manager</option>
                <option value="purchaser">Purchaser</option>
                <option value="employee">Employee</option>
                {checkPermission('users', 'delete') && (
                  <option value="owner">Owner</option>
                )}
              </Select>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="primary" onClick={handleRoleChange} disabled={modalLoading}>
            {modalLoading ? <Spinner size="sm" className="mr-2" /> : null}
            Save Changes
          </Button>
          <Button color="gray" onClick={() => setShowRoleModal(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Reset Password Modal */}
      <Modal show={showPasswordModal} onClose={() => setShowPasswordModal(false)}>
        <Modal.Header>Reset User Password</Modal.Header>
        <Modal.Body>
          {modalError && (
            <Alert color="failure" className="mb-4">
              {modalError}
            </Alert>
          )}

          <div className="space-y-6">
            <p>
              Resetting password for: <strong>{selectedMember?.profile ? `${selectedMember.profile.first_name || ''} ${selectedMember.profile.last_name || ''}` : 'Unknown User'}</strong>
            </p>

            <Alert color="info">
              <p className="text-sm">
                <strong>Note:</strong> In this demo, password reset is simulated. In a production environment,
                this would call a secure server-side API to reset the password.
              </p>
            </Alert>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="newPassword" value="New Password" />
              </div>
              <TextInput
                id="newPassword"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                required
              />
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="confirmPassword" value="Confirm Password" />
              </div>
              <TextInput
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
              />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="primary" onClick={handlePasswordReset} disabled={modalLoading}>
            {modalLoading ? <Spinner size="sm" className="mr-2" /> : null}
            Reset Password
          </Button>
          <Button color="gray" onClick={() => setShowPasswordModal(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default UserManagement;
