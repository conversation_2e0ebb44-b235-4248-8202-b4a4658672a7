import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Button, Spinner, Avatar, TextInput, Tooltip } from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlinePaperAirplane,
  HiOutlineInformationCircle,
  HiOutlineUserGroup,
  HiOutlineSearch
} from 'react-icons/hi';
import { useChatContext } from '../../context/ChatContext';
import { useAuth } from '../../context/AuthContext';
import { formatDate } from '../../utils/formatters';
import CardBox from '../../components/shared/CardBox';
import ChatInfoSidebar from '../../components/chat/ChatInfoSidebar';
import EmptyState from '../../components/shared/EmptyState';
import MessageAttachment from '../../components/chat/MessageAttachment';
import FileUploadButton from '../../components/chat/FileUploadButton';
import ChatMessage from '../../components/chat/ChatMessage';
import ChatSearch from '../../components/chat/ChatSearch';
import defaultAvatar from '../../assets/images/svgs/icon-account.svg';

const ChatDetail: React.FC = () => {
  const { conversationId } = useParams<{ conversationId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    currentConversation,
    messages,
    loadingMessages,
    uploadingFile,
    error,
    searchQuery,
    setCurrentConversationId,
    sendNewMessage,
    loadMoreMessages,
    markAsRead,
    deleteUserMessage,
    editUserMessage,
    searchMessages,
    clearSearch
  } = useChatContext();
  const [messageText, setMessageText] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showInfoSidebar, setShowInfoSidebar] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [isScrolledUp, setIsScrolledUp] = useState(false);

  // Set current conversation when component mounts or conversationId changes
  useEffect(() => {
    if (conversationId) {
      setCurrentConversationId(conversationId);
    }

    return () => {
      // Clear current conversation when component unmounts
      setCurrentConversationId(null);
    };
  }, [conversationId, setCurrentConversationId]);

  // Scroll to bottom when new messages arrive, but only if not scrolled up
  useEffect(() => {
    if (messagesEndRef.current && !isScrolledUp) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isScrolledUp]);

  // Handle scroll events to detect when user scrolls up
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      // Consider "scrolled up" if more than 100px from bottom
      setIsScrolledUp(scrollHeight - scrollTop - clientHeight > 100);

      // Load more messages when scrolled to top (since we reversed the order)
      if (scrollTop === 0 && !loadingMessages) {
        // Show loading indicator when explicitly loading more messages
        loadMoreMessages(true);
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [loadMoreMessages]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();

    if (!messageText.trim() && !selectedFile) return;

    sendNewMessage(messageText, selectedFile || undefined)
      .then(() => {
        setMessageText('');
        setSelectedFile(null);
        setIsScrolledUp(false); // Reset scroll position to follow new messages
      })
      .catch((err) => {
        console.error('Error sending message:', err);
      });
  };

  const handleFileSelected = (file: File) => {
    setSelectedFile(file);
  };

  const handleBackClick = () => {
    navigate('/chat');
  };

  // Mark unread messages as read when user is viewing the chat
  useEffect(() => {
    if (!messages.length || !user?.id) return;

    const unreadMessageIds = messages
      .filter(msg => msg.sender_id !== user.id && !msg.is_read)
      .map(msg => msg.id);

    if (unreadMessageIds.length > 0) {
      // Mark messages as read after a short delay to ensure user is actually viewing them
      const timer = setTimeout(() => {
        markAsRead(unreadMessageIds);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [messages, user?.id, markAsRead]);

  // Get conversation display name
  const getConversationName = () => {
    if (!currentConversation) return 'Chat';

    if (currentConversation.is_group) {
      return currentConversation.name || 'Unnamed Group';
    }

    // For one-on-one chats, show the other person's name
    const otherParticipants = currentConversation.participants.filter(
      p => p.profiles && p.user_id !== user?.id
    );

    if (otherParticipants.length > 0) {
      const profile = otherParticipants[0].profiles;
      return profile?.first_name && profile?.last_name
        ? `${profile.first_name} ${profile.last_name}`
        : 'Unknown User';
    }

    return 'Chat';
  };

  // Get avatar for conversation
  const getConversationAvatar = () => {
    if (!currentConversation) return null;

    if (currentConversation.is_group) {
      return null; // Use default group avatar
    }

    // For one-on-one chats, show the other person's avatar
    const otherParticipants = currentConversation.participants.filter(
      p => p.profiles && p.user_id !== user?.id
    );

    if (otherParticipants.length > 0) {
      return otherParticipants[0].profiles?.avatar_url;
    }

    return null;
  };

  // Get participant count for group chats
  const getParticipantCount = () => {
    if (!currentConversation) return 0;
    return currentConversation.participants.length;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        {/* Chat Header */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center space-x-3">
            {currentConversation?.is_group ? (
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <HiOutlineUserGroup className="w-5 h-5 text-white" />
              </div>
            ) : (
              <div className="w-10 h-10 rounded-full overflow-hidden ring-2 ring-blue-200 dark:ring-blue-700">
                <Avatar
                  img={getConversationAvatar() || undefined}
                  rounded
                  size="md"
                  placeholderInitials={getConversationName().substring(0, 2)}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div>
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white">{getConversationName()}</h1>
              {currentConversation?.is_group ? (
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {getParticipantCount()} participants
                </p>
              ) : (
                <p className="text-xs text-green-500 dark:text-green-400">
                  Online
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-1">
            <Tooltip content="Search messages">
              <Button
                color="light"
                size="xs"
                onClick={() => setShowInfoSidebar(!showInfoSidebar)}
                className={`p-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors ${showInfoSidebar ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
              >
                <HiOutlineSearch className="w-4 h-4" />
              </Button>
            </Tooltip>
            <Tooltip content="Conversation info">
              <Button
                color="light"
                size="xs"
                onClick={() => setShowInfoSidebar(!showInfoSidebar)}
                className={`p-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors ${showInfoSidebar ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
              >
                <HiOutlineInformationCircle className="w-4 h-4" />
              </Button>
            </Tooltip>
            <Tooltip content="Back to messages">
              <Button
                color="light"
                size="xs"
                onClick={handleBackClick}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <HiOutlineArrowLeft className="w-4 h-4" />
              </Button>
            </Tooltip>
          </div>
        </div>

        {/* Search Bar */}
        {currentConversation && searchQuery && (
          <div className="mb-4">
            <ChatSearch />
          </div>
        )}

        {/* Chat Messages Container */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg mb-4">
          <div
            className="h-96 overflow-y-auto p-3 bg-gray-50 dark:bg-gray-800"
            ref={messagesContainerRef}
          >
            {loadingMessages && messages.length === 0 ? (
              <div className="flex justify-center items-center h-full">
                <div className="text-center">
                  <Spinner size="xl" className="mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">Loading messages...</p>
                </div>
              </div>
            ) : error ? (
              <div className="text-center text-red-500 p-6 bg-red-50 dark:bg-red-900/20 rounded-xl">
                <p className="font-medium">Error loading messages</p>
                <p className="text-sm mt-1">{error}</p>
              </div>
            ) : messages.length === 0 ? (
              <div className="flex justify-center items-center h-full">
                <EmptyState
                  title="No messages yet"
                  description="Start the conversation by sending a message"
                  icon={<HiOutlinePaperAirplane className="w-16 h-16 text-blue-500" />}
                />
              </div>
            ) : (
              <>
                {/* Only show load more indicator when explicitly loading more messages */}
                {loadingMessages && isScrolledUp && (
                  <div className="text-center py-4">
                    <Spinner size="sm" />
                    <p className="text-xs text-gray-500 mt-2">Loading more messages...</p>
                  </div>
                )}

                {/* Messages - group consecutive messages from the same sender */}
                <div className="space-y-2">
                  {messages.map((message, index) => {
                    // Check if this message is from the same sender as the previous one
                    const prevMessage = index > 0 ? messages[index - 1] : null;
                    const isConsecutive = prevMessage && prevMessage.sender_id === message.sender_id;

                    return (
                      <ChatMessage
                        key={message.id}
                        message={message}
                        showAvatar={!isConsecutive}
                      />
                    );
                  })}
                </div>
                <div ref={messagesEndRef} />
              </>
            )}
          </div>
        </div>

        {/* Message Input */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
          <form onSubmit={handleSendMessage} className="flex items-end space-x-3">
            <FileUploadButton
              onFileSelected={handleFileSelected}
              disabled={!currentConversation || uploadingFile}
              selectedFile={selectedFile}
            />
            <div className="flex-1">
              <div className="relative">
                <TextInput
                  id="message"
                  type="text"
                  placeholder={selectedFile ? "Add a message or send without text..." : "Type your message..."}
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  disabled={!currentConversation || uploadingFile}
                  className="w-full pr-12 py-2 text-sm rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage(e);
                    }
                  }}
                />
                {selectedFile && (
                  <div className="absolute top-1 right-12 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">
                    {selectedFile.name}
                  </div>
                )}
              </div>
            </div>
            <Button
              type="submit"
              color="primary"
              size="sm"
              disabled={(!messageText.trim() && !selectedFile) || !currentConversation || uploadingFile}
              className="px-4 py-2 rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-300 transition-colors"
            >
              {uploadingFile ? (
                <Spinner size="sm" />
              ) : (
                <HiOutlinePaperAirplane className="w-4 h-4 transform rotate-45" />
              )}
            </Button>
          </form>
        </div>
      </Card>

      {/* Info Sidebar */}
      {showInfoSidebar && currentConversation && (
        <ChatInfoSidebar
          conversation={currentConversation}
          onClose={() => setShowInfoSidebar(false)}
        />
      )}
    </div>
  );
};

export default ChatDetail;
