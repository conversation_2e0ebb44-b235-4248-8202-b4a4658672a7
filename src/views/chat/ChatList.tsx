import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, But<PERSON>, Spinner, Avatar, Badge, TextInput } from 'flowbite-react';
import { HiOutlineSearch, HiOutlinePlus, HiOutlineUserGroup } from 'react-icons/hi';
import { useChatContext } from '../../context/ChatContext';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { formatDate } from '../../utils/formatters';
import NewChatModal from '../../components/chat/NewChatModal';
import NewGroupModal from '../../components/chat/NewGroupModal';

import CardBox from '../../components/shared/CardBox';
import EmptyState from '../../components/shared/EmptyState';

const ChatList: React.FC = () => {
  const navigate = useNavigate();
  const { conversations, loadingConversations, error, refreshConversations, setCurrentConversationId } = useChatContext();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredConversations, setFilteredConversations] = useState(conversations);
  const [showNewChatModal, setShowNewChatModal] = useState(false);
  const [showNewGroupModal, setShowNewGroupModal] = useState(false);


  // Filter conversations based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredConversations(conversations);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = conversations.filter(conversation => {
      // For one-on-one chats, search in participant names
      if (!conversation.is_group) {
        const otherParticipants = conversation.participants.filter(
          p => p.profiles && p.user_id !== user?.id && (p.profiles.first_name || p.profiles.last_name)
        );

        return otherParticipants.some(p =>
          (p.profiles?.first_name?.toLowerCase().includes(query) ||
           p.profiles?.last_name?.toLowerCase().includes(query))
        );
      }

      // For group chats, search in the group name
      return conversation.name?.toLowerCase().includes(query);
    });

    setFilteredConversations(filtered);
  }, [searchQuery, conversations]);

  // Load conversations when component mounts - simple approach
  useEffect(() => {
    // Just load conversations once when component mounts
    if (conversations.length === 0) {
      refreshConversations(true);
    }
  }, []); // Empty dependency array - only run once

  const handleConversationClick = (conversationId: string) => {
    setCurrentConversationId(conversationId);
    navigate(`/chat/${conversationId}`);
  };

  const handleNewChatCreated = (conversationId: string) => {
    setShowNewChatModal(false);
    // Show loading indicator when creating a new chat
    refreshConversations(true).then(() => {
      navigate(`/chat/${conversationId}`);
    });
  };

  const handleNewGroupCreated = (conversationId: string) => {
    setShowNewGroupModal(false);
    // Show loading indicator when creating a new group
    refreshConversations(true).then(() => {
      navigate(`/chat/${conversationId}`);
    });
  };

  // Get conversation display name
  const getConversationName = (conversation: any) => {
    if (conversation.is_group) {
      return conversation.name || 'Unnamed Group';
    }

    // For one-on-one chats, show the other person's name
    const otherParticipants = conversation.participants.filter(
      (p: any) => p.profiles && p.user_id !== user?.id
    );

    if (otherParticipants.length > 0) {
      const profile = otherParticipants[0].profiles;
      return profile?.first_name && profile?.last_name
        ? `${profile.first_name} ${profile.last_name}`
        : 'Unknown User';
    }

    return 'Chat';
  };

  // Get avatar for conversation
  const getConversationAvatar = (conversation: any) => {
    if (conversation.is_group) {
      return null; // Use default group avatar
    }

    // For one-on-one chats, show the other person's avatar
    const otherParticipants = conversation.participants.filter(
      (p: any) => p.profiles && p.user_id !== user?.id
    );

    if (otherParticipants.length > 0) {
      return otherParticipants[0].profiles?.avatar_url;
    }

    return null;
  };

  // Get last message preview
  const getLastMessagePreview = (conversation: any) => {
    if (!conversation.last_message) {
      return 'No messages yet';
    }

    return conversation.last_message.content.length > 30
      ? `${conversation.last_message.content.substring(0, 30)}...`
      : conversation.last_message.content;
  };

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-900">
      {/* Header */}
      <div className="flex-shrink-0 border-b border-gray-200 dark:border-gray-700 p-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Messages</h1>
          <div className="flex space-x-2">
            <Button
              color="primary"
              size="sm"
              onClick={() => setShowNewChatModal(true)}
              className="flex items-center"
            >
              <HiOutlinePlus className="mr-2 h-4 w-4" />
              New Chat
            </Button>
            <Button
              color="light"
              size="sm"
              onClick={() => setShowNewGroupModal(true)}
              className="flex items-center"
            >
              <HiOutlineUserGroup className="mr-2 h-4 w-4" />
              Group
            </Button>

          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <TextInput
            id="search"
            type="text"
            icon={HiOutlineSearch}
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {loadingConversations ? (
          <div className="flex justify-center items-center h-64">
            <Spinner size="xl" />
          </div>
        ) : error ? (
          <div className="p-6">
            <CardBox>
              <div className="text-center text-red-500 p-4">
                <p>Error loading conversations: {error}</p>
                <Button color="light" onClick={() => refreshConversations(true)} className="mt-2">
                  Retry
                </Button>
              </div>
            </CardBox>
          </div>
        ) : filteredConversations.length === 0 ? (
          <div className="p-6">
            <EmptyState
              title="No conversations yet"
              description="Start a new chat or create a group to begin messaging"
              icon={<HiOutlineUserGroup className="w-12 h-12" />}
              actions={
                <div className="flex space-x-2 justify-center">
                  <Button color="primary" onClick={() => setShowNewChatModal(true)}>
                    Start a Chat
                  </Button>
                  <Button color="light" onClick={() => setShowNewGroupModal(true)}>
                    Create a Group
                  </Button>
                </div>
              }
            />
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-150"
                onClick={() => handleConversationClick(conversation.id)}
              >
                <div className="flex items-center space-x-3">
                  <div className="relative flex-shrink-0">
                    {conversation.is_group ? (
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <HiOutlineUserGroup className="w-6 h-6 text-white" />
                      </div>
                    ) : (
                      <div className="w-12 h-12 rounded-full overflow-hidden ring-2 ring-gray-200 dark:ring-gray-700">
                        <Avatar
                          img={getConversationAvatar(conversation)}
                          rounded
                          size="md"
                          placeholderInitials={getConversationName(conversation).substring(0, 2)}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    {conversation.unread_count > 0 && (
                      <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                        {conversation.unread_count > 9 ? '9+' : conversation.unread_count}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <h3 className={`font-medium truncate ${conversation.unread_count > 0 ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'}`}>
                        {getConversationName(conversation)}
                      </h3>
                      <span className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0 ml-2">
                        {conversation.last_message
                          ? formatDate(conversation.last_message.created_at, 'en-US', {
                              hour: 'numeric',
                              minute: 'numeric',
                              hour12: true
                            })
                          : ''}
                      </span>
                    </div>
                    <p className={`text-sm truncate mt-1 ${conversation.unread_count > 0 ? 'text-gray-600 dark:text-gray-400 font-medium' : 'text-gray-500 dark:text-gray-500'}`}>
                      {getLastMessagePreview(conversation)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modals */}
      <NewChatModal
        show={showNewChatModal}
        onClose={() => setShowNewChatModal(false)}
        onChatCreated={handleNewChatCreated}
      />
      <NewGroupModal
        show={showNewGroupModal}
        onClose={() => setShowNewGroupModal(false)}
        onGroupCreated={handleNewGroupCreated}
      />

    </div>
  );
};

export default ChatList;
