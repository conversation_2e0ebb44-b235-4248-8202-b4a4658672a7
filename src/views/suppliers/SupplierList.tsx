import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Spinner,
  Alert,
  TextInput,
  Dropdown,
  Modal
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineSearch,
  HiOutlineDotsVertical,
  HiOutlineExclamation,
  HiOutlineEye,
  HiOutlineDownload,
  HiOutlineUpload
} from 'react-icons/hi';
import { getSuppliers, Supplier, deleteSupplier } from '../../services/supplier';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import Pagination from '../../components/common/Pagination';
import { exportSuppliers } from '../../utils/excelExport';
import GenericImport from '../../components/common/GenericImport';
import {
  previewSupplierImport,
  importSuppliers,
  downloadSupplierImportTemplate,
  SupplierImportResult
} from '../../services/supplierImport';

const SupplierList = () => {
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const navigate = useNavigate();

  // State for suppliers data
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // State for search
  const [searchQuery, setSearchQuery] = useState('');
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // State for sorting
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // State for supplier creation
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // State for delete confirmation
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Import modal
  const [showImportModal, setShowImportModal] = useState(false);

  // Fetch suppliers when organization, page, search, or sort changes
  useEffect(() => {
    fetchSuppliers();
  }, [currentOrganization, currentPage, sortBy, sortOrder, pageSize]);

  // Handle search with debounce
  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      fetchSuppliers();
    }, 300);

    setSearchTimeout(timeout);

    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchQuery]);

  const fetchSuppliers = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { suppliers: supplierData, count, error: fetchError } = await getSuppliers(
        currentOrganization.id,
        {
          searchQuery,
          limit: pageSize,
          offset: (currentPage - 1) * pageSize,
          sortBy,
          sortOrder,
        }
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setSuppliers(supplierData);
        setTotalCount(count);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching suppliers');
    } finally {
      setLoading(false);
    }
  };

  // Navigate to supplier details page
  const navigateToDetails = (supplier: Supplier) => {
    navigate(`/suppliers/${supplier.id}`);
  };

  // Navigate to supplier edit page
  const navigateToEdit = (supplier: Supplier) => {
    navigate(`/suppliers/edit/${supplier.id}`);
  };

  // Navigate to create supplier page
  const navigateToCreate = () => {
    navigate('/suppliers/create');
  };

  // Open delete confirmation modal
  const openDeleteModal = (supplier: Supplier) => {
    setSupplierToDelete(supplier);
    setDeleteError(null);
    setShowDeleteConfirm(true);
  };

  // Check for delete parameter in URL
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const deleteId = params.get('delete');

    if (deleteId && currentOrganization) {
      // Find the supplier to delete
      const supplierToDelete = suppliers.find(s => s.id === deleteId);
      if (supplierToDelete) {
        openDeleteModal(supplierToDelete);
      }

      // Remove the parameter from URL
      navigate('/suppliers', { replace: true });
    }
  }, [suppliers, currentOrganization]);

  // Handle supplier deletion
  const handleDeleteSupplier = async () => {
    if (!currentOrganization || !supplierToDelete) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const { success, error } = await deleteSupplier(
        currentOrganization.id,
        supplierToDelete.id
      );

      if (error) {
        setDeleteError(error);
        return;
      }

      if (success) {
        // Remove the supplier from the list
        setSuppliers(suppliers.filter((s) => s.id !== supplierToDelete.id));

        // Close the confirmation modal
        setShowDeleteConfirm(false);

        // If we deleted the last item on the page and it's not the first page,
        // go to the previous page
        if (suppliers.length === 1 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        } else {
          // Otherwise, refresh the current page
          fetchSuppliers();
        }
      }
    } catch (err: any) {
      setDeleteError(err.message || 'An error occurred while deleting the supplier');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle page change
  const onPageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Calculate total pages
  // Export suppliers to Excel
  const handleExportSuppliers = () => {
    if (suppliers.length === 0) {
      setError('No suppliers to export');
      return;
    }
    exportSuppliers(suppliers);
  };

  // Handle import completion
  const handleImportComplete = (result: SupplierImportResult) => {
    if (result.successCount > 0) {
      // Refresh the supplier list to show imported suppliers
      fetchSuppliers();
    }
    setShowImportModal(false);
  };

  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Suppliers</h1>
            <p className="text-gray-500">
              Manage your supplier database. You can add, edit, and view supplier information.
            </p>
          </div>

          <div className="flex gap-2">
            <Button color="light" onClick={handleExportSuppliers}>
              <HiOutlineDownload className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button color="light" onClick={() => setShowImportModal(true)}>
              <HiOutlineUpload className="mr-2 h-4 w-4" />
              Import
            </Button>
            <Button color="primary" onClick={navigateToCreate}>
              <HiOutlinePlus className="mr-2 h-5 w-5" />
              Add Supplier
            </Button>
          </div>
        </div>

        {/* Search bar */}
        <div className="mb-4">
          <TextInput
            id="search"
            type="text"
            icon={HiOutlineSearch}
            placeholder="Search by name, email, or contact person"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : suppliers.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 mb-4">No suppliers found</p>
            <Button color="primary" size="sm" onClick={navigateToCreate}>
              <HiOutlinePlus className="mr-2 h-4 w-4" />
              Add Your First Supplier
            </Button>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <Table hoverable>
                <Table.Head>
                  <Table.HeadCell>Name</Table.HeadCell>
                  <Table.HeadCell>Contact Person</Table.HeadCell>
                  <Table.HeadCell>Email</Table.HeadCell>
                  <Table.HeadCell>Phone</Table.HeadCell>
                  <Table.HeadCell>City</Table.HeadCell>
                  <Table.HeadCell>Country</Table.HeadCell>
                  <Table.HeadCell>
                    <span className="sr-only">Actions</span>
                  </Table.HeadCell>
                </Table.Head>
                <Table.Body className="divide-y">
                  {suppliers.map((supplier) => (
                    <Table.Row key={supplier.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                      <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                        <button
                          onClick={() => navigateToDetails(supplier)}
                          className="hover:text-blue-600 hover:underline cursor-pointer focus:outline-none"
                        >
                          {supplier.name}
                        </button>
                      </Table.Cell>
                      <Table.Cell>
                        {supplier.contact_person || <span className="text-gray-400">-</span>}
                      </Table.Cell>
                      <Table.Cell>
                        {supplier.email || <span className="text-gray-400">-</span>}
                      </Table.Cell>
                      <Table.Cell>
                        {supplier.phone || <span className="text-gray-400">-</span>}
                      </Table.Cell>
                      <Table.Cell>
                        {supplier.city || <span className="text-gray-400">-</span>}
                      </Table.Cell>
                      <Table.Cell>
                        {supplier.country || <span className="text-gray-400">-</span>}
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex items-center space-x-2">
                          <Button
                            color="light"
                            size="xs"
                            onClick={() => navigateToDetails(supplier)}
                            title="View Details"
                          >
                            <HiOutlineEye className="h-4 w-4" />
                          </Button>
                          <Button
                            color="light"
                            size="xs"
                            onClick={() => navigateToEdit(supplier)}
                            title="Edit Supplier"
                          >
                            <HiOutlinePencil className="h-4 w-4" />
                          </Button>
                          <Button
                            color="light"
                            size="xs"
                            onClick={() => openDeleteModal(supplier)}
                            title="Delete Supplier"
                          >
                            <HiOutlineTrash className="h-4 w-4" />
                          </Button>
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </div>

            {/* Pagination */}
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={pageSize}
              totalItems={totalCount}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={(newPageSize) => {
                setPageSize(newPageSize);
                setCurrentPage(1); // Reset to first page when changing items per page
              }}
              itemName="suppliers"
            />
          </>
        )}
      </Card>



      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)} size="md">
        <Modal.Header>Confirm Deletion</Modal.Header>
        <Modal.Body>
          {deleteError && (
            <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
              {deleteError}
            </Alert>
          )}
          <p className="text-gray-500">
            Are you sure you want to delete the supplier <span className="font-semibold">{supplierToDelete?.name}</span>?
          </p>
          <p className="text-gray-500 mt-2">
            This action cannot be undone. This will permanently delete the supplier and remove all associated data.
          </p>
          <div className="flex justify-end space-x-3 mt-4">
            <Button
              color="gray"
              onClick={() => setShowDeleteConfirm(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              color="failure"
              onClick={handleDeleteSupplier}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Deleting...
                </>
              ) : (
                'Delete Supplier'
              )}
            </Button>
          </div>
        </Modal.Body>
      </Modal>

      {/* Supplier Import Modal */}
      <GenericImport
        show={showImportModal}
        onClose={() => setShowImportModal(false)}
        onImportComplete={handleImportComplete}
        title="Import Suppliers"
        entityName="suppliers"
        previewFunction={previewSupplierImport}
        importFunction={importSuppliers}
        downloadTemplateFunction={downloadSupplierImportTemplate}
      />
    </div>
  );
};

export default SupplierList;
