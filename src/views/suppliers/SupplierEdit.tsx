import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>, Spinner, Breadcrumb } from 'flowbite-react';
import { HiOutlineExclamation, HiHome } from 'react-icons/hi';
import { getSupplierById, updateSupplier, Supplier } from '../../services/supplier';
import { useOrganization } from '../../context/OrganizationContext';
import SupplierForm from '../../components/suppliers/SupplierForm';

const SupplierEdit = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSupplier = async () => {
      if (!currentOrganization || !id) return;

      setLoading(true);
      setError(null);

      try {
        const { supplier: supplierData, error: fetchError } = await getSupplierById(
          currentOrganization.id,
          id
        );

        if (fetchError) {
          setError(fetchError);
        } else if (supplierData) {
          setSupplier(supplierData);
        } else {
          setError('Supplier not found');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the supplier');
      } finally {
        setLoading(false);
      }
    };

    fetchSupplier();
  }, [currentOrganization, id]);

  const handleSubmit = async (supplierData: Partial<Supplier>) => {
    if (!currentOrganization || !id) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { supplier: updatedSupplier, error } = await updateSupplier(
        currentOrganization.id,
        id,
        supplierData as Partial<Omit<Supplier, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>
      );

      if (error) {
        setSubmitError(error);
      } else {
        // Navigate back to the supplier details page
        navigate(`/suppliers/${id}`);
      }
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while updating the supplier');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Breadcrumb>
          <Breadcrumb.Item href="/" icon={HiHome}>
            Dashboard
          </Breadcrumb.Item>
          <Breadcrumb.Item href="/suppliers">
            Suppliers
          </Breadcrumb.Item>
          {supplier && (
            <Breadcrumb.Item href={`/suppliers/${id}`}>
              {supplier.name}
            </Breadcrumb.Item>
          )}
          <Breadcrumb.Item>
            Edit
          </Breadcrumb.Item>
        </Breadcrumb>
      </div>

      <Card>
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Edit Supplier</h1>
          <p className="text-gray-500">
            Update supplier information
          </p>
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : supplier ? (
          <SupplierForm
            initialData={supplier}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            error={submitError || undefined}
            onCancel={() => navigate(`/suppliers/${id}`)}
          />
        ) : (
          <div className="p-8 text-center">
            <p className="text-gray-500">Supplier not found</p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default SupplierEdit;
