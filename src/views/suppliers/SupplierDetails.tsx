import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, Button, Alert, Spinner, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ge } from 'flowbite-react';
import {
  HiOutlineExclamation,
  HiOutlineChevronLeft,
  HiOutlinePencil,
  HiOutlineTrash,
  HiHome,
  HiOutlinePlus,
  HiOutlineTag
} from 'react-icons/hi';
import { getSupplierById, Supplier } from '../../services/supplier';
import { Product } from '../../services/product';
import { useOrganization } from '../../context/OrganizationContext';
import SupplierProductsList from '../../components/suppliers/SupplierProductsList';
import AddSingleProductButton from '../../components/suppliers/AddSingleProductButton';
import ProductSelectionModal from '../../components/suppliers/ProductSelectionModal';
import ProductConfirmationModal from '../../components/suppliers/ProductConfirmationModal';
import { getSupplierTags, addTagToSupplier, removeTagFromSupplier } from '../../services/supplierTagService';
import { Tag } from '../../types/tagging.types';
import TagSelector from '../../components/tags/TagSelector';
import TagList from '../../components/tags/TagList';

const SupplierDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();

  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Tags state
  const [supplierTags, setSupplierTags] = useState<Tag[]>([]);
  const [isLoadingTags, setIsLoadingTags] = useState(false);
  const [tagsError, setTagsError] = useState<string | null>(null);

  // State for product management and tabs
  const [activeTab, setActiveTab] = useState<'details' | 'products'>('details');
  const [productCount, setProductCount] = useState(0);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    const fetchSupplier = async () => {
      if (!currentOrganization || !id) return;

      setLoading(true);
      setError(null);

      try {
        const { supplier: supplierData, error: fetchError } = await getSupplierById(
          currentOrganization.id,
          id
        );

        if (fetchError) {
          setError(fetchError);
        } else if (supplierData) {
          setSupplier(supplierData);
        } else {
          setError('Supplier not found');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the supplier');
      } finally {
        setLoading(false);
      }
    };

    fetchSupplier();
  }, [currentOrganization, id]);

  // Fetch supplier tags
  useEffect(() => {
    const fetchSupplierTags = async () => {
      if (!id) return;

      setIsLoadingTags(true);
      setTagsError(null);

      try {
        const { tags, error } = await getSupplierTags(id);

        if (error) {
          setTagsError(error);
        } else {
          setSupplierTags(tags);
        }
      } catch (err: any) {
        setTagsError(err.message || 'An error occurred while fetching tags');
      } finally {
        setIsLoadingTags(false);
      }
    };

    fetchSupplierTags();
  }, [id]);

  // Display field value or placeholder
  const displayValue = (value: string | null | undefined, placeholder: string = '-') => {
    return value ? value : <span className="text-gray-400">{placeholder}</span>;
  };

  const handleEdit = () => {
    navigate(`/suppliers/edit/${id}`);
  };

  const handleDelete = () => {
    navigate(`/suppliers?delete=${id}`);
  };



  // Handle tag changes
  const handleTagsChange = async (newTags: Tag[]) => {
    if (!id) return;

    // Find tags that were added
    const addedTags = newTags.filter(
      newTag => !supplierTags.some(existingTag => existingTag.id === newTag.id)
    );

    // Find tags that were removed
    const removedTags = supplierTags.filter(
      existingTag => !newTags.some(newTag => newTag.id === existingTag.id)
    );

    // Update the state with the new tags immediately for better UX
    setSupplierTags(newTags);

    // Add new tags
    for (const tag of addedTags) {
      try {
        await addTagToSupplier(id, tag.id);
      } catch (err) {
        console.error(`Failed to add tag ${tag.name}:`, err);
        // If there's an error, revert the tag addition in the UI
        setSupplierTags(prevTags => prevTags.filter(t => t.id !== tag.id));
      }
    }

    // Remove deleted tags
    for (const tag of removedTags) {
      try {
        await removeTagFromSupplier(id, tag.id);
      } catch (err) {
        console.error(`Failed to remove tag ${tag.name}:`, err);
        // If there's an error, revert the tag removal in the UI
        setSupplierTags(prevTags => [...prevTags, tag]);
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Supplier Details</h1>
            <p className="text-gray-500">
              View detailed information about this supplier
            </p>
          </div>

          <div className="flex space-x-2">
            <Button color="gray" onClick={() => navigate('/suppliers')}>
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back
            </Button>
            <Button color="primary" onClick={handleEdit}>
              <HiOutlinePencil className="mr-2 h-5 w-5" />
              Edit
            </Button>
            <Button color="failure" onClick={handleDelete}>
              <HiOutlineTrash className="mr-2 h-5 w-5" />
              Delete
            </Button>
          </div>
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : supplier ? (
          <div>
            <div className="mb-4">
              <ul className="flex flex-wrap -mb-px text-sm font-medium text-center" role="tablist">
                <li className="mr-2" role="presentation">
                  <button
                    className={`inline-block p-4 border-b-2 rounded-t-lg ${
                      activeTab === 'details' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                    }`}
                    type="button"
                    role="tab"
                    aria-selected={activeTab === 'details'}
                    onClick={() => setActiveTab('details')}
                  >
                    Details
                  </button>
                </li>
                <li className="mr-2" role="presentation">
                  <button
                    className={`inline-block p-4 border-b-2 rounded-t-lg ${
                      activeTab === 'products' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                    }`}
                    type="button"
                    role="tab"
                    aria-selected={activeTab === 'products'}
                    onClick={() => setActiveTab('products')}
                  >
                    Products
                  </button>
                </li>
              </ul>
            </div>

            {/* Details Tab Content */}
            {activeTab === 'details' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Supplier Name</h3>
                        <p className="mt-1 text-base font-medium">{displayValue(supplier.name)}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Contact Person</h3>
                        <p className="mt-1 text-base">{displayValue(supplier.contact_person)}</p>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Email</h3>
                          <p className="mt-1 text-base">{displayValue(supplier.email)}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Phone</h3>
                          <p className="mt-1 text-base">{displayValue(supplier.phone)}</p>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Tax ID</h3>
                        <p className="mt-1 text-base">{displayValue(supplier.tax_id)}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold mb-4">Notes</h2>
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                      <p className="whitespace-pre-line">{displayValue(supplier.notes, 'No notes available')}</p>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold mb-4">Tags</h2>
                    <div className="flex items-start">
                      <HiOutlineTag className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                      <div className="w-full">
                        {isLoadingTags ? (
                          <div className="flex items-center">
                            <Spinner size="sm" className="mr-2" />
                            <span className="text-gray-500">Loading tags...</span>
                          </div>
                        ) : tagsError ? (
                          <Alert color="failure" className="mb-4">
                            {tagsError}
                          </Alert>
                        ) : (
                          <div>
                            <TagSelector
                              entityType="supplier"
                              entityId={supplier.id}
                              selectedTags={supplierTags}
                              onTagsChange={handleTagsChange}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h2 className="text-xl font-semibold mb-4">Address Information</h2>
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Street Address</h3>
                      <p className="mt-1 text-base">{displayValue(supplier.address)}</p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">City</h3>
                        <p className="mt-1 text-base">{displayValue(supplier.city)}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">State/Province</h3>
                        <p className="mt-1 text-base">{displayValue(supplier.state)}</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Postal Code</h3>
                        <p className="mt-1 text-base">{displayValue(supplier.postal_code)}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Country</h3>
                        <p className="mt-1 text-base">{displayValue(supplier.country)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Products Tab Content */}
            {activeTab === 'products' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <h2 className="text-xl font-semibold">Supplier Products</h2>
                    {productCount > 0 && (
                      <p className="text-sm text-gray-500 mt-1">
                        This supplier has {productCount} product{productCount !== 1 ? 's' : ''}
                      </p>
                    )}
                  </div>
                  <AddSingleProductButton
                    supplierId={id || ''}
                    onSuccess={() => setRefreshTrigger(prev => prev + 1)}
                    buttonSize="sm"
                    buttonText="Add Product"
                  />
                </div>

                <SupplierProductsList
                  supplierId={id || ''}
                  onProductCountChange={setProductCount}
                  refreshTrigger={refreshTrigger}
                />
              </div>
            )}

            {/* System Information */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <h2 className="text-sm font-medium text-gray-500 mb-2">System Information</h2>
              <div className="flex flex-wrap gap-4">
                <div>
                  <p className="text-xs text-gray-500">Supplier ID</p>
                  <p className="text-sm">{supplier?.id}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Created</p>
                  <p className="text-sm">{supplier?.created_at ? new Date(supplier.created_at).toLocaleString() : '-'}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Last Updated</p>
                  <p className="text-sm">{supplier?.updated_at ? new Date(supplier.updated_at).toLocaleString() : '-'}</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="p-8 text-center">
            <p className="text-gray-500">Supplier not found</p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default SupplierDetails;
