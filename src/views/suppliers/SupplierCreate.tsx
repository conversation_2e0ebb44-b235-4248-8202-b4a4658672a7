import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Alert, Breadcrumb } from 'flowbite-react';
import { HiOutlineExclamation, HiHome } from 'react-icons/hi';
import { createSupplier, Supplier } from '../../services/supplier';
import { useOrganization } from '../../context/OrganizationContext';
import SupplierForm from '../../components/suppliers/SupplierForm';

const SupplierCreate = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const handleSubmit = async (supplierData: Partial<Supplier>) => {
    if (!currentOrganization) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { supplier, error } = await createSupplier(
        currentOrganization.id,
        supplierData as Omit<Supplier, 'id' | 'organization_id' | 'created_at' | 'updated_at'>
      );

      if (error) {
        setSubmitError(error);
      } else if (supplier) {
        // Navigate to the supplier details page
        navigate(`/suppliers/${supplier.id}`);
      }
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while creating the supplier');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Breadcrumb>
          <Breadcrumb.Item href="/" icon={HiHome}>
            Dashboard
          </Breadcrumb.Item>
          <Breadcrumb.Item href="/suppliers">
            Suppliers
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            Create New Supplier
          </Breadcrumb.Item>
        </Breadcrumb>
      </div>

      <Card>
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Create New Supplier</h1>
          <p className="text-gray-500">
            Add a new supplier to your database
          </p>
        </div>

        {submitError && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {submitError}
          </Alert>
        )}

        <SupplierForm
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          error={submitError || undefined}
          onCancel={() => navigate('/suppliers')}
        />
      </Card>
    </div>
  );
};

export default SupplierCreate;
