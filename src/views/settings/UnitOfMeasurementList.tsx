import { useState, useEffect } from 'react';
import {
  Card,
  <PERSON>,
  <PERSON><PERSON>,
  Spinner,
  <PERSON>ert,
  TextInput,
  Badge,
  Modal
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineSearch,
  HiOutlineExclamation
} from 'react-icons/hi';
import {
  getUnitsOfMeasurement,
  createUnitOfMeasurement,
  updateUnitOfMeasurement,
  deleteUnitOfMeasurement
} from '../../services/uom';
import { UnitOfMeasurement } from '../../types/uom.types';
import { useOrganization } from '../../context/OrganizationContext';
import UomForm from '../../components/uom/UomForm';
import Pagination from '../../components/common/Pagination';

const UnitOfMeasurementList = () => {
  const { currentOrganization } = useOrganization();
  const [uoms, setUoms] = useState<UnitOfMeasurement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedUom, setSelectedUom] = useState<UnitOfMeasurement | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  const fetchUoms = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { uoms: uomData, error: fetchError } = await getUnitsOfMeasurement(
        currentOrganization.id,
        {
          searchQuery: searchQuery || undefined
        }
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setUoms(uomData);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching units of measurement');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUoms();
  }, [currentOrganization]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchUoms();
  };

  const handleAddClick = () => {
    setSelectedUom(null);
    setFormError(null);
    setShowAddModal(true);
  };

  const handleEditClick = (uom: UnitOfMeasurement) => {
    setSelectedUom(uom);
    setFormError(null);
    setShowEditModal(true);
  };

  const handleDeleteClick = (uom: UnitOfMeasurement) => {
    setSelectedUom(uom);
    setShowDeleteConfirm(true);
  };

  const handleAddSubmit = async (uomData: Partial<UnitOfMeasurement>) => {
    if (!currentOrganization) return;

    setIsSubmitting(true);
    setFormError(null);

    try {
      const { uom, error: createError } = await createUnitOfMeasurement(
        currentOrganization.id,
        {
          code: uomData.code || '',
          name: uomData.name || '',
          description: uomData.description,
          is_active: uomData.is_active !== undefined ? uomData.is_active : true,
          created_by: null // Will be set by the backend
        }
      );

      if (createError) {
        setFormError(createError);
        console.error('Error creating UoM:', createError);
      } else {
        console.log('UoM created successfully:', uom);
        setShowAddModal(false);
        fetchUoms();
      }
    } catch (err: any) {
      console.error('Exception creating UoM:', err);
      setFormError(err.message || 'An error occurred while creating the unit of measurement');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditSubmit = async (uomData: Partial<UnitOfMeasurement>) => {
    if (!currentOrganization || !selectedUom) return;

    setIsSubmitting(true);
    setFormError(null);

    try {
      const { uom, error: updateError } = await updateUnitOfMeasurement(
        currentOrganization.id,
        selectedUom.id,
        {
          code: uomData.code,
          name: uomData.name,
          description: uomData.description,
          is_active: uomData.is_active
        }
      );

      if (updateError) {
        setFormError(updateError);
        console.error('Error updating UoM:', updateError);
      } else {
        console.log('UoM updated successfully:', uom);
        setShowEditModal(false);
        fetchUoms();
      }
    } catch (err: any) {
      console.error('Exception updating UoM:', err);
      setFormError(err.message || 'An error occurred while updating the unit of measurement');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!currentOrganization || !selectedUom) return;

    setIsSubmitting(true);
    setFormError(null);

    try {
      const { success, error: deleteError } = await deleteUnitOfMeasurement(
        currentOrganization.id,
        selectedUom.id
      );

      if (deleteError) {
        setFormError(deleteError);
      } else if (success) {
        setShowDeleteConfirm(false);
        setSelectedUom(null);
        fetchUoms();
      }
    } catch (err: any) {
      setFormError(err.message || 'An error occurred while deleting the unit of measurement');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(uoms.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentUoms = uoms.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Units of Measurement</h1>
            <p className="text-gray-500">
              Manage units of measurement for your products
            </p>
          </div>

          <Button color="primary" onClick={handleAddClick}>
            <HiOutlinePlus className="mr-2 h-5 w-5" />
            Add Unit
          </Button>
        </div>

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="mb-6">
          <div className="flex gap-2">
            <TextInput
              id="search"
              type="text"
              placeholder="Search by name or code"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
              icon={HiOutlineSearch}
            />
            <Button type="submit">
              Search
            </Button>
          </div>
        </form>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : uoms.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 mb-4">No units of measurement found</p>
            <Button color="primary" size="sm" onClick={handleAddClick}>
              <HiOutlinePlus className="mr-2 h-4 w-4" />
              Add Your First Unit
            </Button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Code</Table.HeadCell>
                <Table.HeadCell>Name</Table.HeadCell>
                <Table.HeadCell>Description</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>
                  <span className="sr-only">Actions</span>
                </Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {currentUoms.map((uom) => (
                  <Table.Row key={uom.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                    <Table.Cell className="font-medium">
                      {uom.code}
                    </Table.Cell>
                    <Table.Cell>
                      {uom.name}
                    </Table.Cell>
                    <Table.Cell>
                      {uom.description || <span className="text-gray-400">No description</span>}
                    </Table.Cell>
                    <Table.Cell>
                      <Badge color={uom.is_active ? 'success' : 'gray'}>
                        {uom.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center space-x-2">
                        <Button color="light" size="xs" onClick={() => handleEditClick(uom)}>
                          <HiOutlinePencil className="h-4 w-4" />
                        </Button>
                        <Button color="failure" size="xs" onClick={() => handleDeleteClick(uom)}>
                          <HiOutlineTrash className="h-4 w-4" />
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        {/* Pagination */}
        {uoms.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={uoms.length}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="units"
          />
        )}
      </Card>

      {/* Add Modal */}
      <Modal show={showAddModal} onClose={() => setShowAddModal(false)}>
        <Modal.Header>Add Unit of Measurement</Modal.Header>
        <Modal.Body>
          <UomForm
            onSubmit={handleAddSubmit}
            isSubmitting={isSubmitting}
            error={formError || undefined}
          />
        </Modal.Body>
      </Modal>

      {/* Edit Modal */}
      <Modal show={showEditModal} onClose={() => setShowEditModal(false)}>
        <Modal.Header>Edit Unit of Measurement</Modal.Header>
        <Modal.Body>
          <UomForm
            initialData={selectedUom || undefined}
            onSubmit={handleEditSubmit}
            isSubmitting={isSubmitting}
            error={formError || undefined}
          />
        </Modal.Body>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)}>
        <Modal.Header>Confirm Deletion</Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-red-500" />
            <h3 className="mb-5 text-lg font-normal text-gray-500">
              Are you sure you want to delete {selectedUom?.name}?
            </h3>
            {formError && (
              <Alert color="failure" className="mb-4">
                {formError}
              </Alert>
            )}
            <div className="flex justify-center gap-4">
              <Button color="failure" onClick={handleDeleteConfirm} disabled={isSubmitting}>
                {isSubmitting ? <Spinner size="sm" /> : 'Yes, delete'}
              </Button>
              <Button color="gray" onClick={() => setShowDeleteConfirm(false)}>
                No, cancel
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default UnitOfMeasurementList;
