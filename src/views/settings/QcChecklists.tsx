import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Table,
  Badge,
  Spinner,
  <PERSON>ert,
  Modal,
  TextInput,
  Textarea,
  Label,
  Select,
  Checkbox,
  ToggleSwitch
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlineTrash,
  HiOutlineExclamation,
  HiOutlinePencil,
  HiOutlineClipboardCheck,
  HiOutlineDocumentText,
  HiOutlineCheck,
  HiOutlineX
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import {
  getQcChecklistTemplates,
  createQcChecklistTemplate,
  updateQcChecklistTemplate,
  deleteQcChecklistTemplate,
  QcChecklistTemplate
} from '../../services/qcChecklist';
import { formatDate } from '../../utils/formatters';
import Pagination from '../../components/common/Pagination';
import EmptyState from '../../components/common/EmptyState';

const QcChecklists = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();

  const [templates, setTemplates] = useState<QcChecklistTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<QcChecklistTemplate | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Form state
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [templateActive, setTemplateActive] = useState(true);
  const [items, setItems] = useState<any[]>([]);

  useEffect(() => {
    if (currentOrganization) {
      fetchTemplates();
    }
  }, [currentOrganization]);

  const fetchTemplates = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { templates: templateList, error: templatesError } = await getQcChecklistTemplates(
        currentOrganization.id
      );

      if (templatesError) {
        setError(templatesError);
      } else {
        setTemplates(templateList);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching QC checklist templates');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateClick = () => {
    // Reset form state
    setTemplateName('');
    setTemplateDescription('');
    setTemplateActive(true);
    setItems([
      {
        name: '',
        description: '',
        itemType: 'boolean',
        isRequired: true,
        minValue: null,
        maxValue: null,
        unit: '',
        options: null,
        passCriteria: null
      }
    ]);
    setShowCreateModal(true);
  };

  const handleEditClick = (template: QcChecklistTemplate) => {
    setSelectedTemplate(template);
    setTemplateName(template.name);
    setTemplateDescription(template.description || '');
    setTemplateActive(template.is_active);
    // We would need to fetch items here
    setShowCreateModal(true);
  };

  const handleDeleteClick = (template: QcChecklistTemplate) => {
    setSelectedTemplate(template);
    setShowDeleteModal(true);
  };

  const handleAddItem = () => {
    setItems([
      ...items,
      {
        name: '',
        description: '',
        itemType: 'boolean',
        isRequired: true,
        minValue: null,
        maxValue: null,
        unit: '',
        options: null,
        passCriteria: null
      }
    ]);
  };

  const handleRemoveItem = (index: number) => {
    const newItems = [...items];
    newItems.splice(index, 1);
    setItems(newItems);
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], [field]: value };
    setItems(newItems);
  };

  const handleSubmit = async () => {
    if (!currentOrganization) return;

    // Validate form
    if (!templateName.trim()) {
      setError('Template name is required');
      return;
    }

    if (items.length === 0) {
      setError('At least one checklist item is required');
      return;
    }

    for (let i = 0; i < items.length; i++) {
      if (!items[i].name.trim()) {
        setError(`Item #${i + 1}: Name is required`);
        return;
      }
    }

    setIsSubmitting(true);
    setError(null);

    try {
      if (selectedTemplate) {
        // Update existing template
        const { template, error: updateError } = await updateQcChecklistTemplate(
          currentOrganization.id,
          selectedTemplate.id,
          {
            name: templateName,
            description: templateDescription,
            isActive: templateActive
          }
        );

        if (updateError) {
          setError(updateError);
        } else {
          // Refresh the templates list
          fetchTemplates();
          setShowCreateModal(false);
        }
      } else {
        // Create new template
        const { template, error: createError } = await createQcChecklistTemplate(
          currentOrganization.id,
          {
            name: templateName,
            description: templateDescription,
            isActive: templateActive,
            items: items.map(item => ({
              name: item.name,
              description: item.description,
              itemType: item.itemType,
              isRequired: item.isRequired,
              minValue: item.minValue,
              maxValue: item.maxValue,
              unit: item.unit,
              options: item.options,
              passCriteria: item.passCriteria
            }))
          }
        );

        if (createError) {
          setError(createError);
        } else {
          // Refresh the templates list
          fetchTemplates();
          setShowCreateModal(false);
        }
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while saving the QC checklist template');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!currentOrganization || !selectedTemplate) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const { success, error: deleteError } = await deleteQcChecklistTemplate(
        currentOrganization.id,
        selectedTemplate.id
      );

      if (deleteError) {
        setError(deleteError);
      } else if (success) {
        // Refresh the templates list
        fetchTemplates();
        setShowDeleteModal(false);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while deleting the QC checklist template');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(templates.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentTemplates = templates.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <HiOutlineClipboardCheck className="mr-2 h-6 w-6" />
              QC Checklist Templates
            </h1>
            <p className="text-gray-500">
              Manage quality control checklist templates for your products
            </p>
          </div>

          <Button color="primary" onClick={handleCreateClick}>
            <HiOutlinePlus className="mr-2 h-5 w-5" />
            Create Template
          </Button>
        </div>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Spinner size="xl" />
          </div>
        ) : templates.length === 0 ? (
          <EmptyState
            title="No QC checklist templates found"
            description="Create your first QC checklist template to start defining quality control standards for your products."
            icon={<HiOutlineClipboardCheck className="h-12 w-12" />}
            actionLabel="Create Template"
            onActionClick={handleCreateClick}
          />
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Template Name</Table.HeadCell>
                <Table.HeadCell>Description</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>Created</Table.HeadCell>
                <Table.HeadCell>
                  <span className="sr-only">Actions</span>
                </Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {currentTemplates.map((template) => (
                  <Table.Row
                    key={template.id}
                    className="bg-white dark:border-gray-700 dark:bg-gray-800"
                  >
                    <Table.Cell className="font-medium">
                      <div className="flex items-center">
                        <HiOutlineDocumentText className="mr-2 h-5 w-5 text-gray-500" />
                        {template.name}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {template.description || <span className="text-gray-500">No description</span>}
                    </Table.Cell>
                    <Table.Cell>
                      {template.is_active ? (
                        <Badge color="success" icon={HiOutlineCheck}>Active</Badge>
                      ) : (
                        <Badge color="gray" icon={HiOutlineX}>Inactive</Badge>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {formatDate(template.created_at)}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex gap-2">
                        <Button
                          size="xs"
                          color="primary"
                          onClick={() => handleEditClick(template)}
                        >
                          <HiOutlinePencil className="h-4 w-4" />
                        </Button>
                        <Button
                          size="xs"
                          color="failure"
                          onClick={() => handleDeleteClick(template)}
                        >
                          <HiOutlineTrash className="h-4 w-4" />
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        {/* Pagination */}
        {templates.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={templates.length}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="templates"
          />
        )}
      </Card>

      {/* Create/Edit Modal */}
      <Modal show={showCreateModal} onClose={() => setShowCreateModal(false)} size="xl">
        <Modal.Header>
          {selectedTemplate ? 'Edit QC Checklist Template' : 'Create QC Checklist Template'}
        </Modal.Header>
        <Modal.Body>
          {error && (
            <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
              {error}
            </Alert>
          )}

          <div className="space-y-6">
            <div>
              <Label htmlFor="templateName" value="Template Name" />
              <TextInput
                id="templateName"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="templateDescription" value="Description" />
              <Textarea
                id="templateDescription"
                value={templateDescription}
                onChange={(e) => setTemplateDescription(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex items-center gap-2">
              <ToggleSwitch
                checked={templateActive}
                onChange={setTemplateActive}
                label="Active"
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-4">
                <Label value="Checklist Items" />
                <Button size="xs" color="primary" onClick={handleAddItem}>
                  <HiOutlinePlus className="mr-2 h-4 w-4" />
                  Add Item
                </Button>
              </div>

              {items.length === 0 ? (
                <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
                  No items added yet. Click "Add Item" to add your first checklist item.
                </div>
              ) : (
                <div className="space-y-6">
                  {items.map((item, index) => (
                    <div key={index} className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="font-medium">Item #{index + 1}</h3>
                        <Button
                          size="xs"
                          color="failure"
                          onClick={() => handleRemoveItem(index)}
                        >
                          <HiOutlineTrash className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor={`itemName-${index}`} value="Name" />
                          <TextInput
                            id={`itemName-${index}`}
                            value={item.name}
                            onChange={(e) => handleItemChange(index, 'name', e.target.value)}
                            required
                          />
                        </div>

                        <div>
                          <Label htmlFor={`itemType-${index}`} value="Type" />
                          <Select
                            id={`itemType-${index}`}
                            value={item.itemType}
                            onChange={(e) => handleItemChange(index, 'itemType', e.target.value)}
                            required
                          >
                            <option value="boolean">Yes/No</option>
                            <option value="numeric">Numeric</option>
                            <option value="text">Text</option>
                            <option value="select">Select</option>
                          </Select>
                        </div>

                        <div className="md:col-span-2">
                          <Label htmlFor={`itemDescription-${index}`} value="Description" />
                          <Textarea
                            id={`itemDescription-${index}`}
                            value={item.description}
                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                            rows={2}
                          />
                        </div>

                        <div className="flex items-center gap-2">
                          <Checkbox
                            id={`itemRequired-${index}`}
                            checked={item.isRequired}
                            onChange={(e) => handleItemChange(index, 'isRequired', e.target.checked)}
                          />
                          <Label htmlFor={`itemRequired-${index}`} value="Required" />
                        </div>

                        {item.itemType === 'numeric' && (
                          <>
                            <div>
                              <Label htmlFor={`itemUnit-${index}`} value="Unit" />
                              <TextInput
                                id={`itemUnit-${index}`}
                                value={item.unit || ''}
                                onChange={(e) => handleItemChange(index, 'unit', e.target.value)}
                                placeholder="e.g., mm, kg, °C"
                              />
                            </div>

                            <div>
                              <Label htmlFor={`itemMinValue-${index}`} value="Minimum Value" />
                              <TextInput
                                id={`itemMinValue-${index}`}
                                type="number"
                                value={item.minValue || ''}
                                onChange={(e) => handleItemChange(index, 'minValue', e.target.value ? parseFloat(e.target.value) : null)}
                              />
                            </div>

                            <div>
                              <Label htmlFor={`itemMaxValue-${index}`} value="Maximum Value" />
                              <TextInput
                                id={`itemMaxValue-${index}`}
                                type="number"
                                value={item.maxValue || ''}
                                onChange={(e) => handleItemChange(index, 'maxValue', e.target.value ? parseFloat(e.target.value) : null)}
                              />
                            </div>
                          </>
                        )}

                        {item.itemType === 'select' && (
                          <div className="md:col-span-2">
                            <Label htmlFor={`itemOptions-${index}`} value="Options (one per line)" />
                            <Textarea
                              id={`itemOptions-${index}`}
                              value={item.options ? item.options.join('\n') : ''}
                              onChange={(e) => {
                                const options = e.target.value.split('\n').filter(opt => opt.trim());
                                handleItemChange(index, 'options', options.length > 0 ? options : null);
                              }}
                              rows={3}
                              placeholder="Option 1&#10;Option 2&#10;Option 3"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowCreateModal(false)}>
            Cancel
          </Button>
          <Button color="primary" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? <Spinner size="sm" className="mr-2" /> : null}
            {selectedTemplate ? 'Update Template' : 'Create Template'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onClose={() => setShowDeleteModal(false)} size="md">
        <Modal.Header>Delete QC Checklist Template</Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-gray-400" />
            <h3 className="mb-5 text-lg font-normal text-gray-500">
              Are you sure you want to delete the template "{selectedTemplate?.name}"?
            </h3>
            <p className="text-sm text-gray-500 mb-5">
              This action cannot be undone. All QC checklists using this template will be affected.
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button color="failure" onClick={handleDelete} disabled={isSubmitting}>
            {isSubmitting ? <Spinner size="sm" className="mr-2" /> : null}
            Delete Template
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default QcChecklists;
