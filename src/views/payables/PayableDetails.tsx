import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Badge,
  Spinner,
  Al<PERSON>,
  Label
} from 'flowbite-react';
import { Tabs } from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineCreditCard,
  HiOutlinePencil,
  HiOutlineTrash
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { formatDate, formatDateTime } from '../../utils/formatters';
import {
  getPayableById,
  getPayablePayments,
  deletePayable,
  getPayableSourceMetadata
} from '../../services/payables';
import {
  PayableWithDetails,
  PayablePaymentWithDetails,
  PayableStatus,
  PayableSourceType,
  PaymentMethod
} from '../../types/payables.types';
import PageTitle from '../../components/shared/PageTitle';
import AttachmentGallery from '../../components/attachments/AttachmentGallery';
import PaymentHistoryCard from '../../components/payables/PaymentHistoryCard';

const PayableDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  // State
  const [payable, setPayable] = useState<PayableWithDetails | null>(null);
  const [payments, setPayments] = useState<PayablePaymentWithDetails[]>([]);
  const [sourceMetadata, setSourceMetadata] = useState<{
    source_type: string;
    source_id: string;
    source_name: string;
    source_description: string;
    source_date: string;
    source_amount: number;
    source_url?: string;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState(false);

  // Fetch payable details
  const fetchPayableDetails = async () => {
    if (!currentOrganization || !id) return;

    setLoading(true);
    setError(null);

    try {
      const { payable: fetchedPayable, error: payableError } = await getPayableById(
        currentOrganization.id,
        id
      );

      if (payableError) {
        setError(payableError);
        return;
      }

      if (!fetchedPayable) {
        setError('Payable not found');
        return;
      }

      setPayable(fetchedPayable);

      // Fetch payments
      const { payments: fetchedPayments, error: paymentsError } = await getPayablePayments(
        currentOrganization.id,
        id
      );

      if (!paymentsError && fetchedPayments) {
        setPayments(fetchedPayments);
      }

      // Fetch source metadata
      const { metadata, error: metadataError } = await getPayableSourceMetadata(
        currentOrganization.id,
        id
      );

      if (!metadataError && metadata) {
        setSourceMetadata(metadata);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch payable details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPayableDetails();
  }, [currentOrganization, id]);

  // Handle delete
  const handleDelete = async () => {
    if (!currentOrganization || !id || !payable) return;

    if (!confirm('Are you sure you want to delete this payable? This action cannot be undone.')) {
      return;
    }

    setDeleting(true);

    try {
      const { success, error: deleteError } = await deletePayable(
        currentOrganization.id,
        id
      );

      if (deleteError) {
        setError(deleteError);
        return;
      }

      if (success) {
        navigate('/payables');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to delete payable');
    } finally {
      setDeleting(false);
    }
  };

  // Get status badge
  const getStatusBadge = (status: PayableStatus) => {
    const statusConfig = {
      [PayableStatus.DRAFT]: { color: 'gray', label: 'Draft' },
      [PayableStatus.OPEN]: { color: 'blue', label: 'Open' },
      [PayableStatus.PARTIALLY_PAID]: { color: 'yellow', label: 'Partially Paid' },
      [PayableStatus.PAID]: { color: 'green', label: 'Paid' },
      [PayableStatus.CANCELLED]: { color: 'red', label: 'Cancelled' }
    };

    const config = statusConfig[status];
    return <Badge color={config.color}>{config.label}</Badge>;
  };

  // Get source type label
  const getSourceTypeLabel = (sourceType: PayableSourceType) => {
    const labels = {
      [PayableSourceType.PURCHASE_RECEIPT]: 'Purchase Receipt',
      [PayableSourceType.PAYROLL]: 'Payroll',
      [PayableSourceType.UTILITY_BILL]: 'Utility Bill',
      [PayableSourceType.GOVERNMENT_REMITTANCE]: 'Government Remittance',
      [PayableSourceType.LOAN_REPAYMENT]: 'Loan Repayment',
      [PayableSourceType.MANUAL_ENTRY]: 'Manual Entry'
    };
    return labels[sourceType] || sourceType;
  };

  // Get payment method label
  const getPaymentMethodLabel = (method: PaymentMethod) => {
    const labels = {
      [PaymentMethod.CASH]: 'Cash',
      [PaymentMethod.CHECK]: 'Check',
      [PaymentMethod.BANK_TRANSFER]: 'Bank Transfer',
      [PaymentMethod.GCASH]: 'GCash',
      [PaymentMethod.PAYMAYA]: 'PayMaya',
      [PaymentMethod.CREDIT_CARD]: 'Credit Card',
      [PaymentMethod.OTHER]: 'Other'
    };
    return labels[method] || method;
  };

  if (!currentOrganization) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error || !payable) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure">
          {error || 'Payable not found'}
        </Alert>
        <Button
          color="light"
          onClick={() => navigate('/payables')}
          className="mt-4"
        >
          <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
          Back to Payables
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button
            color="light"
            onClick={() => navigate('/payables')}
          >
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back
          </Button>
          <PageTitle
            title={`Payable ${payable.reference_number}`}
            subtitle={getSourceTypeLabel(payable.source_type as PayableSourceType)}
          />
        </div>

        <div className="flex gap-2">
          {payable.status !== PayableStatus.PAID && payable.status !== PayableStatus.CANCELLED && (
            <>
              <Button
                color="primary"
                onClick={() => navigate(`/payables/${payable.id}/pay`)}
              >
                <HiOutlineCreditCard className="mr-2 h-5 w-5" />
                Add Payment
              </Button>
              <Button
                color="light"
                onClick={() => navigate(`/payables/${payable.id}/edit`)}
              >
                <HiOutlinePencil className="mr-2 h-5 w-5" />
                Edit
              </Button>
            </>
          )}
          <Button
            color="failure"
            onClick={handleDelete}
            disabled={deleting || payments.length > 0}
          >
            <HiOutlineTrash className="mr-2 h-5 w-5" />
            {deleting ? 'Deleting...' : 'Delete'}
          </Button>
        </div>
      </div>

      <Tabs aria-label="Payable details tabs" variant="underline">
        <Tabs.Item active title="Details">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <h5 className="text-lg font-bold mb-4">Basic Information</h5>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">Reference Number:</span>
                  <span className="font-medium">{payable.reference_number}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Status:</span>
                  {getStatusBadge(payable.status as PayableStatus)}
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Source Type:</span>
                  <span>{getSourceTypeLabel(payable.source_type as PayableSourceType)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Invoice Date:</span>
                  <span>{formatDate(payable.invoice_date)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Due Date:</span>
                  <span>{formatDate(payable.due_date)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Currency:</span>
                  <span>{payable.currency}</span>
                </div>
              </div>
            </Card>

            {/* Supplier/Employee Information */}
            <Card>
              <h5 className="text-lg font-bold mb-4">
                {payable.supplier ? 'Supplier Information' : 'Employee Information'}
              </h5>
              {payable.supplier ? (
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Name:</span>
                    <span className="font-medium">{payable.supplier.name}</span>
                  </div>
                  {payable.supplier.contact_person && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Contact Person:</span>
                      <span>{payable.supplier.contact_person}</span>
                    </div>
                  )}
                  {payable.supplier.email && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Email:</span>
                      <span>{payable.supplier.email}</span>
                    </div>
                  )}
                  {payable.supplier.phone && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Phone:</span>
                      <span>{payable.supplier.phone}</span>
                    </div>
                  )}
                </div>
              ) : payable.employee ? (
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Name:</span>
                    <span className="font-medium">
                      {payable.employee.first_name} {payable.employee.last_name}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Employee Number:</span>
                    <span>{payable.employee.employee_number}</span>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">No supplier or employee information available</p>
              )}
            </Card>

            {/* Financial Information */}
            <Card>
              <h5 className="text-lg font-bold mb-4">Financial Information</h5>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">Gross Amount:</span>
                  <span className="font-medium">{formatWithCurrency(payable.amount)}</span>
                </div>
                {payable.vat_amount > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">VAT Amount:</span>
                    <span>{formatWithCurrency(payable.vat_amount)}</span>
                  </div>
                )}
                {payable.withholding_tax_amount > 0 && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Withholding Tax Rate:</span>
                      <span>{payable.withholding_tax_rate}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Withholding Tax Amount:</span>
                      <span>{formatWithCurrency(payable.withholding_tax_amount)}</span>
                    </div>
                  </>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-500">Total Paid:</span>
                  <span>{formatWithCurrency(payable.total_paid || 0)}</span>
                </div>
                <div className="flex justify-between border-t pt-3">
                  <span className="text-gray-500 font-medium">Outstanding Balance:</span>
                  <span className="font-bold text-lg">{formatWithCurrency(payable.balance)}</span>
                </div>
              </div>
            </Card>

            {/* Notes */}
            {payable.notes && (
              <Card>
                <h5 className="text-lg font-bold mb-4">Notes</h5>
                <p className="text-gray-700">{payable.notes}</p>
              </Card>
            )}
          </div>
        </Tabs.Item>

        <Tabs.Item title="Payment History">
          <Card>
            <h5 className="text-lg font-bold mb-4">Payment History</h5>
            {payments.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                  <HiOutlineCreditCard className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-gray-500 text-lg mb-2">No payments recorded</p>
                <p className="text-gray-400 text-sm">Payments will appear here once they are added</p>
              </div>
            ) : (
              <div className="space-y-4">
                {payments.map((payment, index) => (
                  <PaymentHistoryCard
                    key={payment.id}
                    payment={payment}
                    formatWithCurrency={formatWithCurrency}
                    formatDate={formatDate}
                    formatDateTime={formatDateTime}
                    getPaymentMethodLabel={getPaymentMethodLabel}
                    isLast={index === payments.length - 1}
                  />
                ))}
              </div>
            )}
          </Card>
        </Tabs.Item>

        <Tabs.Item title="Attachments">
          <AttachmentGallery
            attachableType="payable"
            attachableId={payable.id}
            editable={true}
            showUpload={true}
            maxFiles={20}
          />
        </Tabs.Item>

        <Tabs.Item title="Source Details">
          <Card>
            <h5 className="text-lg font-bold mb-4">Source Document Information</h5>
            {sourceMetadata ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label value="Source Name" />
                    {sourceMetadata.source_url ? (
                      <Button
                        color="light"
                        size="sm"
                        onClick={() => navigate(sourceMetadata.source_url!)}
                        className="p-0 text-left justify-start"
                      >
                        <span className="font-medium text-blue-600 hover:text-blue-800">
                          {sourceMetadata.source_name}
                        </span>
                      </Button>
                    ) : (
                      <p className="font-medium text-gray-900">{sourceMetadata.source_name}</p>
                    )}
                  </div>
                  <div>
                    <Label value="Source Date" />
                    <p className="text-gray-700">{formatDate(sourceMetadata.source_date)}</p>
                  </div>
                </div>
                <div>
                  <Label value="Description" />
                  <p className="text-gray-700">{sourceMetadata.source_description}</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label value="Source Amount" />
                    <p className="font-medium text-gray-900">
                      {formatWithCurrency(sourceMetadata.source_amount)}
                    </p>
                  </div>
                  <div>
                    <Label value="Source ID" />
                    <p className="text-gray-500 text-sm font-mono">{sourceMetadata.source_id}</p>
                  </div>
                </div>
                {sourceMetadata.source_url && (
                  <div className="pt-4 border-t">
                    <Button
                      color="primary"
                      size="sm"
                      onClick={() => navigate(sourceMetadata.source_url!)}
                    >
                      View Source Document
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">Loading source information...</p>
              </div>
            )}
          </Card>
        </Tabs.Item>
      </Tabs>
    </div>
  );
};

export default PayableDetails;
