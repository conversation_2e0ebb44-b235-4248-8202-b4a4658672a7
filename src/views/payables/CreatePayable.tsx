import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Alert,
  Label,
  TextInput,
  Select,
  Textarea
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineSave
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import {
  createPayable,
  validateWithholdingTax
} from '../../services/payables';
import {
  PayableSourceType,
  PaymentMethod,
  CreatePayableRequest
} from '../../types/payables.types';
import PageTitle from '../../components/shared/PageTitle';

const CreatePayable = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const formatWithCurrency = useCurrencyFormatter();

  // State
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<CreatePayableRequest>({
    source_type: PayableSourceType.MANUAL_ENTRY,
    source_id: 'manual-' + Date.now(), // Generate a unique source ID for manual entries
    reference_number: '',
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
    amount: 0,
    vat_amount: 0,
    withholding_tax_rate: 0,
    withholding_tax_amount: 0,
    currency: 'PHP',
    category: '',
    notes: ''
  });

  // Handle form input changes
  const handleInputChange = (field: keyof CreatePayableRequest, value: any) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      
      // Auto-calculate withholding tax when amount, VAT, or rate changes
      if (field === 'amount' || field === 'vat_amount' || field === 'withholding_tax_rate') {
        const { calculatedAmount } = validateWithholdingTax(
          Number(updated.amount) || 0,
          Number(updated.vat_amount) || 0,
          Number(updated.withholding_tax_rate) || 0
        );
        updated.withholding_tax_amount = calculatedAmount;
      }
      
      return updated;
    });
  };

  // Validate form
  const validateForm = (): string | null => {
    if (!formData.reference_number.trim()) {
      return 'Reference number is required';
    }

    if (!formData.invoice_date) {
      return 'Invoice date is required';
    }

    if (!formData.due_date) {
      return 'Due date is required';
    }

    if (!formData.amount || formData.amount <= 0) {
      return 'Amount must be greater than zero';
    }

    if (formData.vat_amount < 0) {
      return 'VAT amount cannot be negative';
    }

    if (formData.withholding_tax_rate < 0 || formData.withholding_tax_rate > 100) {
      return 'Withholding tax rate must be between 0 and 100';
    }

    return null;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization || !user) return;

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      const { success, error: payableError } = await createPayable(
        currentOrganization.id,
        formData,
        user.id
      );

      if (payableError) {
        setError(payableError);
        return;
      }

      if (success) {
        navigate('/payables');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create payable');
    } finally {
      setSubmitting(false);
    }
  };

  if (!currentOrganization) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <p className="text-gray-500">Loading organization...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-4 mb-6">
        <Button
          color="light"
          onClick={() => navigate('/payables')}
        >
          <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
          Back
        </Button>
        <PageTitle
          title="Create Manual Payable"
          subtitle="Add a manual payable entry"
        />
      </div>

      <Card>
        <h5 className="text-lg font-bold mb-4">Payable Information</h5>
        
        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="reference_number" value="Reference Number *" />
              <TextInput
                id="reference_number"
                type="text"
                placeholder="INV-001, BILL-001, etc."
                value={formData.reference_number}
                onChange={(e) => handleInputChange('reference_number', e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="category" value="Category" />
              <Select
                id="category"
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
              >
                <option value="">Select category</option>
                <option value="office_supplies">Office Supplies</option>
                <option value="utilities">Utilities</option>
                <option value="rent">Rent</option>
                <option value="maintenance">Maintenance</option>
                <option value="professional_services">Professional Services</option>
                <option value="other">Other</option>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="invoice_date" value="Invoice Date *" />
              <TextInput
                id="invoice_date"
                type="date"
                value={formData.invoice_date}
                onChange={(e) => handleInputChange('invoice_date', e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="due_date" value="Due Date *" />
              <TextInput
                id="due_date"
                type="date"
                value={formData.due_date}
                onChange={(e) => handleInputChange('due_date', e.target.value)}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="amount" value="Total Amount *" />
              <TextInput
                id="amount"
                type="number"
                step="0.01"
                min="0.01"
                value={formData.amount}
                onChange={(e) => handleInputChange('amount', Number(e.target.value))}
                required
              />
            </div>

            <div>
              <Label htmlFor="vat_amount" value="VAT Amount" />
              <TextInput
                id="vat_amount"
                type="number"
                step="0.01"
                min="0"
                value={formData.vat_amount}
                onChange={(e) => handleInputChange('vat_amount', Number(e.target.value))}
              />
            </div>

            <div>
              <Label htmlFor="withholding_tax_rate" value="Withholding Tax Rate (%)" />
              <TextInput
                id="withholding_tax_rate"
                type="number"
                step="0.01"
                min="0"
                max="100"
                value={formData.withholding_tax_rate}
                onChange={(e) => handleInputChange('withholding_tax_rate', Number(e.target.value))}
              />
            </div>
          </div>

          {formData.withholding_tax_rate > 0 && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <Label value="Calculated Withholding Tax Amount" />
              <p className="font-medium text-blue-900">
                {formatWithCurrency(formData.withholding_tax_amount)}
              </p>
              <p className="text-sm text-blue-700">
                Net Amount: {formatWithCurrency((formData.amount || 0) - (formData.vat_amount || 0))} × {formData.withholding_tax_rate}%
              </p>
            </div>
          )}

          <div>
            <Label htmlFor="notes" value="Notes" />
            <Textarea
              id="notes"
              rows={3}
              placeholder="Additional notes about this payable"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              color="light"
              onClick={() => navigate('/payables')}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              color="primary"
              disabled={submitting}
            >
              <HiOutlineSave className="mr-2 h-5 w-5" />
              {submitting ? 'Creating...' : 'Create Payable'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default CreatePayable;
