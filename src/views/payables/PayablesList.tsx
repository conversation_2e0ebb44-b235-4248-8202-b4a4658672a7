import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  <PERSON>ton,
  Badge,
  Spinner,
  Alert,
  TextInput,
  Select,
  Dropdown
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlineEye,
  HiOutlineCreditCard,
  HiOutlineDownload,
  HiOutlineFilter,
  HiOutlineSearch,
  HiOutlineRefresh
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { formatDate } from '../../utils/formatters';
import {
  getPayables,
  getPayablesSummary
} from '../../services/payables';
import {
  PayableWithDetails,
  PayableStatus,
  PayableSourceType,
  PayableSummary,
  PayableFilters
} from '../../types/payables.types';
import Pagination from '../../components/common/Pagination';
import { exportPayables } from '../../utils/excelExport';
import PageTitle from '../../components/shared/PageTitle';

const PayablesList = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  // State
  const [payables, setPayables] = useState<PayableWithDetails[]>([]);
  const [summary, setSummary] = useState<PayableSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Filters
  const [filters, setFilters] = useState<PayableFilters>({});
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch data
  const fetchPayables = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { payables: fetchedPayables, total_count, error: payablesError } = await getPayables(
        currentOrganization.id,
        {
          limit: itemsPerPage,
          offset: (currentPage - 1) * itemsPerPage,
          sortBy: 'created_at',
          sortOrder: 'desc',
          filters: {
            ...filters,
            search_query: searchQuery || undefined
          }
        }
      );

      if (payablesError) {
        setError(payablesError);
        return;
      }

      setPayables(fetchedPayables || []);
      setTotalCount(total_count || 0);

      // Fetch summary
      const { summary: fetchedSummary, error: summaryError } = await getPayablesSummary(
        currentOrganization.id
      );

      if (!summaryError && fetchedSummary) {
        setSummary(fetchedSummary);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch payables');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPayables();
  }, [currentOrganization, currentPage, itemsPerPage, filters, searchQuery]);

  // Get status badge
  const getStatusBadge = (status: PayableStatus) => {
    const statusConfig = {
      [PayableStatus.DRAFT]: { color: 'gray', label: 'Draft' },
      [PayableStatus.OPEN]: { color: 'blue', label: 'Open' },
      [PayableStatus.PARTIALLY_PAID]: { color: 'yellow', label: 'Partially Paid' },
      [PayableStatus.PAID]: { color: 'green', label: 'Paid' },
      [PayableStatus.CANCELLED]: { color: 'red', label: 'Cancelled' }
    };

    const config = statusConfig[status];
    return <Badge color={config.color}>{config.label}</Badge>;
  };

  // Get source type label
  const getSourceTypeLabel = (sourceType: PayableSourceType) => {
    const labels = {
      [PayableSourceType.PURCHASE_RECEIPT]: 'Purchase Receipt',
      [PayableSourceType.PAYROLL]: 'Payroll',
      [PayableSourceType.UTILITY_BILL]: 'Utility Bill',
      [PayableSourceType.GOVERNMENT_REMITTANCE]: 'Government Remittance',
      [PayableSourceType.LOAN_REPAYMENT]: 'Loan Repayment',
      [PayableSourceType.MANUAL_ENTRY]: 'Manual Entry'
    };
    return labels[sourceType] || sourceType;
  };

  // Calculate days overdue
  const getDaysOverdue = (dueDate: string) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = today.getTime() - due.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof PayableFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }));
    setCurrentPage(1);
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
    setCurrentPage(1);
  };

  // Export payables to Excel
  const handleExportPayables = () => {
    if (payables.length === 0) {
      setError('No payables to export');
      return;
    }
    exportPayables(payables);
  };

  if (!currentOrganization) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <PageTitle
        title="Accounts Payable"
        subtitle="Manage supplier invoices, payroll obligations, and other payables"
      />

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card className="border-l-4 border-blue-500">
            <div className="flex justify-between items-center">
              <div>
                <h5 className="text-sm text-gray-500">Total Payables</h5>
                <p className="text-2xl font-bold">{summary.total_payables}</p>
                <p className="text-sm text-gray-600">
                  {formatWithCurrency(summary.total_amount)}
                </p>
              </div>
            </div>
          </Card>

          <Card className="border-l-4 border-green-500">
            <div className="flex justify-between items-center">
              <div>
                <h5 className="text-sm text-gray-500">Total Paid</h5>
                <p className="text-2xl font-bold">
                  {formatWithCurrency(summary.total_paid)}
                </p>
              </div>
            </div>
          </Card>

          <Card className="border-l-4 border-yellow-500">
            <div className="flex justify-between items-center">
              <div>
                <h5 className="text-sm text-gray-500">Outstanding</h5>
                <p className="text-2xl font-bold">
                  {formatWithCurrency(summary.total_outstanding)}
                </p>
              </div>
            </div>
          </Card>

          <Card className="border-l-4 border-red-500">
            <div className="flex justify-between items-center">
              <div>
                <h5 className="text-sm text-gray-500">Overdue</h5>
                <p className="text-2xl font-bold">{summary.overdue_count}</p>
                <p className="text-sm text-gray-600">
                  {formatWithCurrency(summary.overdue_amount)}
                </p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
          <div className="flex items-center gap-2">
            <h5 className="text-xl font-bold">Payables List</h5>
            <Button
              color="light"
              size="xs"
              pill
              onClick={fetchPayables}
            >
              <HiOutlineRefresh />
            </Button>
          </div>

          <div className="flex flex-col md:flex-row gap-2">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <HiOutlineSearch className="text-gray-500" />
              </div>
              <TextInput
                type="text"
                placeholder="Search payables..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={filters.status || ''}
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="">All Status</option>
              <option value={PayableStatus.DRAFT}>Draft</option>
              <option value={PayableStatus.OPEN}>Open</option>
              <option value={PayableStatus.PARTIALLY_PAID}>Partially Paid</option>
              <option value={PayableStatus.PAID}>Paid</option>
              <option value={PayableStatus.CANCELLED}>Cancelled</option>
            </Select>

            <Select
              value={filters.source_type || ''}
              onChange={(e) => handleFilterChange('source_type', e.target.value)}
            >
              <option value="">All Sources</option>
              <option value={PayableSourceType.PURCHASE_RECEIPT}>Purchase Receipt</option>
              <option value={PayableSourceType.PAYROLL}>Payroll</option>
              <option value={PayableSourceType.UTILITY_BILL}>Utility Bill</option>
              <option value={PayableSourceType.GOVERNMENT_REMITTANCE}>Government Remittance</option>
              <option value={PayableSourceType.LOAN_REPAYMENT}>Loan Repayment</option>
              <option value={PayableSourceType.MANUAL_ENTRY}>Manual Entry</option>
            </Select>

            <Button
              color="light"
              onClick={clearFilters}
            >
              Clear Filters
            </Button>

            <Button
              color="light"
              onClick={handleExportPayables}
            >
              <HiOutlineDownload className="mr-2 h-4 w-4" />
              Export
            </Button>

            <Button
              color="primary"
              onClick={() => navigate('/payables/create')}
            >
              <HiOutlinePlus className="mr-2 h-5 w-5" />
              Add Payable
            </Button>
          </div>
        </div>

        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Spinner size="xl" />
          </div>
        ) : payables.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No payables found</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Reference</Table.HeadCell>
                <Table.HeadCell>Supplier/Employee</Table.HeadCell>
                <Table.HeadCell>Source</Table.HeadCell>
                <Table.HeadCell>Amount</Table.HeadCell>
                <Table.HeadCell>Balance</Table.HeadCell>
                <Table.HeadCell>Due Date</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>Actions</Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {payables.map((payable) => {
                  const daysOverdue = getDaysOverdue(payable.due_date);
                  return (
                    <Table.Row key={payable.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                      <Table.Cell className="font-medium">
                        {payable.reference_number}
                      </Table.Cell>
                      <Table.Cell>
                        {payable.supplier ? payable.supplier.name : 
                         payable.employee ? `${payable.employee.first_name} ${payable.employee.last_name}` : 
                         'N/A'}
                      </Table.Cell>
                      <Table.Cell>
                        {getSourceTypeLabel(payable.source_type as PayableSourceType)}
                      </Table.Cell>
                      <Table.Cell>
                        {formatWithCurrency(payable.amount)}
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex flex-col">
                          <span>{formatWithCurrency(payable.balance)}</span>
                          {daysOverdue > 0 && (
                            <span className="text-xs text-red-600">
                              {daysOverdue} days overdue
                            </span>
                          )}
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        {formatDate(payable.due_date)}
                      </Table.Cell>
                      <Table.Cell>
                        {getStatusBadge(payable.status as PayableStatus)}
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex gap-2">
                          <Button
                            color="light"
                            size="xs"
                            onClick={() => navigate(`/payables/${payable.id}`)}
                          >
                            <HiOutlineEye className="h-4 w-4" />
                          </Button>
                          {payable.status !== PayableStatus.PAID && payable.status !== PayableStatus.CANCELLED && (
                            <Button
                              color="primary"
                              size="xs"
                              onClick={() => navigate(`/payables/${payable.id}/pay`)}
                            >
                              <HiOutlineCreditCard className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>
          </div>
        )}

        {/* Pagination */}
        {totalCount > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil(totalCount / itemsPerPage)}
            itemsPerPage={itemsPerPage}
            totalItems={totalCount}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1);
            }}
            itemName="payables"
          />
        )}
      </Card>
    </div>
  );
};

export default PayablesList;
