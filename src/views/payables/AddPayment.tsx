import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Alert,
  Spinner,
  Label,
  TextInput,
  Select,
  Textarea
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineSave,
  HiOutlineUpload
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { formatDate } from '../../utils/formatters';
import {
  getPayableById,
  createPayment
} from '../../services/payables';
import AttachmentGallery from '../../components/attachments/AttachmentGallery';
import EnhancedNumberInput from '../../components/common/EnhancedNumberInput';
import {
  PayableWithDetails,
  PaymentMethod,
  CreatePaymentRequest
} from '../../types/payables.types';
import PageTitle from '../../components/shared/PageTitle';

const AddPayment = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const formatWithCurrency = useCurrencyFormatter();

  // State
  const [payable, setPayable] = useState<PayableWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentId, setPaymentId] = useState<string | null>(null);
  const [showAttachments, setShowAttachments] = useState(false);

  // Form state
  const [formData, setFormData] = useState<CreatePaymentRequest>({
    payable_id: id || '',
    payment_date: new Date().toISOString().split('T')[0],
    amount_paid: 0,
    payment_method: PaymentMethod.CASH,
    reference_number: '',
    attachment_url: '',
    remarks: ''
  });

  // Fetch payable details
  const fetchPayableDetails = async () => {
    if (!currentOrganization || !id) return;

    setLoading(true);
    setError(null);

    try {
      const { payable: fetchedPayable, error: payableError } = await getPayableById(
        currentOrganization.id,
        id
      );

      if (payableError) {
        setError(payableError);
        return;
      }

      if (!fetchedPayable) {
        setError('Payable not found');
        return;
      }

      setPayable(fetchedPayable);
      
      // Set default amount to remaining balance
      setFormData(prev => ({
        ...prev,
        amount_paid: Number(fetchedPayable.balance)
      }));
    } catch (err: any) {
      setError(err.message || 'Failed to fetch payable details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPayableDetails();
  }, [currentOrganization, id]);

  // Handle form input changes
  const handleInputChange = (field: keyof CreatePaymentRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Validate form
  const validateForm = (): string | null => {
    if (!formData.payment_date) {
      return 'Payment date is required';
    }

    if (!formData.amount_paid || formData.amount_paid <= 0) {
      return 'Payment amount must be greater than zero';
    }

    if (payable && formData.amount_paid > Number(payable.balance)) {
      return 'Payment amount cannot exceed the outstanding balance';
    }

    if (!formData.payment_method) {
      return 'Payment method is required';
    }

    return null;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization || !user || !payable) return;

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      const { success, payment, error: paymentError } = await createPayment(
        currentOrganization.id,
        formData,
        user.id
      );

      if (paymentError) {
        setError(paymentError);
        return;
      }

      if (success && payment) {
        setPaymentId(payment.id);
        setShowAttachments(true);
        // Don't navigate immediately - let user add attachments first
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create payment');
    } finally {
      setSubmitting(false);
    }
  };

  if (!currentOrganization) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error && !payable) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure">
          {error}
        </Alert>
        <Button
          color="light"
          onClick={() => navigate('/payables')}
          className="mt-4"
        >
          <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
          Back to Payables
        </Button>
      </div>
    );
  }

  if (!payable) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure">
          Payable not found
        </Alert>
        <Button
          color="light"
          onClick={() => navigate('/payables')}
          className="mt-4"
        >
          <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
          Back to Payables
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-4 mb-6">
        <Button
          color="light"
          onClick={() => navigate(`/payables/${payable.id}`)}
        >
          <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
          Back
        </Button>
        <PageTitle
          title="Add Payment"
          subtitle={`For payable ${payable.reference_number}`}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Payable Summary */}
        <Card>
          <h5 className="text-lg font-bold mb-4">Payable Summary</h5>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-500">Reference:</span>
              <span className="font-medium">{payable.reference_number}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Supplier/Employee:</span>
              <span>
                {payable.supplier ? payable.supplier.name : 
                 payable.employee ? `${payable.employee.first_name} ${payable.employee.last_name}` : 
                 'N/A'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Due Date:</span>
              <span>{formatDate(payable.due_date)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Total Amount:</span>
              <span>{formatWithCurrency(payable.amount)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Total Paid:</span>
              <span>{formatWithCurrency(payable.total_paid || 0)}</span>
            </div>
            <div className="flex justify-between border-t pt-3">
              <span className="text-gray-500 font-medium">Outstanding Balance:</span>
              <span className="font-bold text-lg">{formatWithCurrency(payable.balance)}</span>
            </div>
          </div>
        </Card>

        {/* Payment Form */}
        <div className="lg:col-span-2">
          <Card>
            <h5 className="text-lg font-bold mb-4">Payment Details</h5>
            
            {error && (
              <Alert color="failure" className="mb-4">
                {error}
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="payment_date" value="Payment Date" />
                  <TextInput
                    id="payment_date"
                    type="date"
                    value={formData.payment_date}
                    onChange={(e) => handleInputChange('payment_date', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="amount_paid" value="Payment Amount" />
                  <EnhancedNumberInput
                    id="amount_paid"
                    step="0.01"
                    min="0.01"
                    max={Number(payable.balance)}
                    value={formData.amount_paid}
                    onChange={(e) => handleInputChange('amount_paid', Number(e.target.value))}
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Maximum: {formatWithCurrency(payable.balance)}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="payment_method" value="Payment Method" />
                  <Select
                    id="payment_method"
                    value={formData.payment_method}
                    onChange={(e) => handleInputChange('payment_method', e.target.value as PaymentMethod)}
                    required
                  >
                    <option value={PaymentMethod.CASH}>Cash</option>
                    <option value={PaymentMethod.CHECK}>Check</option>
                    <option value={PaymentMethod.BANK_TRANSFER}>Bank Transfer</option>
                    <option value={PaymentMethod.GCASH}>GCash</option>
                    <option value={PaymentMethod.PAYMAYA}>PayMaya</option>
                    <option value={PaymentMethod.CREDIT_CARD}>Credit Card</option>
                    <option value={PaymentMethod.OTHER}>Other</option>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="reference_number" value="Reference Number" />
                  <TextInput
                    id="reference_number"
                    type="text"
                    placeholder="Check #, Transaction ID, etc."
                    value={formData.reference_number}
                    onChange={(e) => handleInputChange('reference_number', e.target.value)}
                  />
                </div>
              </div>



              <div>
                <Label htmlFor="remarks" value="Remarks" />
                <Textarea
                  id="remarks"
                  rows={3}
                  placeholder="Additional notes about this payment"
                  value={formData.remarks}
                  onChange={(e) => handleInputChange('remarks', e.target.value)}
                />
              </div>

              {/* Optional: Upload attachments after payment creation */}
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <HiOutlineUpload className="h-5 w-5 text-blue-600" />
                  <h6 className="font-medium text-blue-800">Payment Attachments</h6>
                </div>
                <p className="text-sm text-blue-700 mb-2">
                  You can upload receipts, bank statements, or other proof of payment after saving this payment.
                </p>
                <p className="text-xs text-blue-600">
                  Supported: Images (JPEG, PNG, GIF, WebP) and PDF files up to 10MB each
                </p>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  color="light"
                  onClick={() => navigate(`/payables/${payable.id}`)}
                  disabled={submitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  color="primary"
                  disabled={submitting}
                >
                  <HiOutlineSave className="mr-2 h-5 w-5" />
                  {submitting ? 'Saving...' : 'Save Payment'}
                </Button>
              </div>
            </form>

            {/* Payment Attachments Section */}
            {showAttachments && paymentId && (
              <div className="mt-6 border-t pt-6">
                <div className="bg-green-50 p-4 rounded-lg border border-green-200 mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <HiOutlineSave className="h-5 w-5 text-green-600" />
                    <h6 className="font-medium text-green-800">Payment Saved Successfully!</h6>
                  </div>
                  <p className="text-sm text-green-700">
                    Now you can upload receipts, bank statements, or other proof of payment documents.
                  </p>
                </div>

                <AttachmentGallery
                  attachableType="payment"
                  attachableId={paymentId}
                  editable={true}
                  showUpload={true}
                  maxFiles={10}
                />

                <div className="mt-4 flex justify-between">
                  <Button
                    color="light"
                    onClick={() => navigate(`/payables/${payable.id}`)}
                  >
                    Skip Attachments
                  </Button>
                  <Button
                    color="success"
                    onClick={() => navigate(`/payables/${payable.id}`)}
                  >
                    Complete & View Payable
                  </Button>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AddPayment;
