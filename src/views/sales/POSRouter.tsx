import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';
import { Spinner } from 'flowbite-react';
import { BusinessType } from '../../components/settings/BusinessTypeSelector';

/**
 * POSRouter component that redirects to the appropriate POS interface
 * based on the business type set in organization settings
 */
const POSRouter: React.FC = () => {
  const navigate = useNavigate();
  const { settings, loading } = useOrganizationSettings();

  useEffect(() => {
    if (!loading && settings) {
      const businessType = settings.business_type as BusinessType;
      
      // Redirect based on business type
      switch (businessType) {
        case 'retail':
          navigate('/sales/retail-pos');
          break;
        case 'supermarket':
          navigate('/sales/retail-pos');
          break;
        case 'restaurant':
        case 'cafe':
        case 'coffee_shop':
        case 'bar':
          navigate('/sales/pos');
          break;
        default:
          // Default to standard POS if business type is not set or unknown
          navigate('/sales/pos');
          break;
      }
    }
  }, [loading, settings, navigate]);

  // Show loading spinner while determining which POS to load
  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <Spinner size="xl" />
        <p className="mt-4 text-gray-600">Loading POS Terminal...</p>
      </div>
    </div>
  );
};

export default POSRouter;
