import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  Card,
  <PERSON><PERSON>,
  <PERSON>ner,
  <PERSON><PERSON>,
  Badge,
  Table
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineReceiptTax,
  HiOutlineUser,
  HiOutlineCalendar,
  HiOutlineCurrencyDollar,
  HiOutlineDocumentText,
  HiOutlinePrinter,
  HiOutlineEye
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getSaleById, Sale } from '../../services/sale';
import { RefundService } from '../../services/refund';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { formatDateTime } from '../../utils/formatters';
import SaleReceipt from '../../components/sales/SaleReceipt';

const SaleDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  const [sale, setSale] = useState<Sale | null>(null);
  const [refunds, setRefunds] = useState<any[]>([]);
  const [refundSummary, setRefundSummary] = useState({
    totalRefunded: 0,
    refundCount: 0,
    refundPercentage: 0,
    hasProcessedRefunds: false
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showReceiptModal, setShowReceiptModal] = useState(false);

  useEffect(() => {
    const fetchSale = async () => {
      if (!currentOrganization || !id) return;

      setLoading(true);
      setError(null);

      try {
        const { sale: saleData, error: fetchError } = await getSaleById(
          currentOrganization.id,
          id
        );

        if (fetchError) {
          setError(fetchError);
        } else if (saleData) {
          setSale(saleData);
          // Fetch refund data for this sale
          await fetchRefundData(saleData.id);
        } else {
          setError('Sale not found');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the sale');
      } finally {
        setLoading(false);
      }
    };

    fetchSale();
  }, [currentOrganization, id]);

  const fetchRefundData = async (saleId: string) => {
    if (!currentOrganization) return;

    try {
      const refundResponse = await RefundService.getRefunds(currentOrganization.id, {
        limit: 100
      });

      if (refundResponse.success && refundResponse.data) {
        const saleRefunds = refundResponse.data.filter(
          refund => refund.original_sale_id === saleId
        );

        setRefunds(saleRefunds);

        // Calculate refund summary
        const processedRefunds = saleRefunds.filter(r => r.status === 'processed');
        const totalRefunded = processedRefunds.reduce(
          (sum, r) => sum + Number(r.total_amount), 0
        );

        setRefundSummary({
          totalRefunded,
          refundCount: saleRefunds.length,
          refundPercentage: sale ? (totalRefunded / sale.total_amount) * 100 : 0,
          hasProcessedRefunds: processedRefunds.length > 0
        });
      }
    } catch (error) {
      console.error('Error fetching refund data:', error);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure">
          <span className="font-medium">Error:</span> {error}
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/sales/history')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Sales
          </Button>
        </div>
      </div>
    );
  }

  if (!sale) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="warning">
          <span className="font-medium">Sale Not Found</span>
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/sales/history')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Sales
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button
            color="gray"
            onClick={() => navigate('/sales/history')}
            className="mr-4"
          >
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <HiOutlineReceiptTax className="mr-2 h-6 w-6" />
              Sale Details
            </h1>
            <p className="text-gray-600">Invoice: {sale.invoice_number}</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            color="light"
            onClick={() => setShowReceiptModal(true)}
          >
            <HiOutlineEye className="mr-2 h-5 w-5" />
            View Receipt
          </Button>
          <Button
            color="blue"
            onClick={handlePrint}
          >
            <HiOutlinePrinter className="mr-2 h-5 w-5" />
            Print
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sale Information */}
        <div className="lg:col-span-2">
          <Card>
            <h2 className="text-xl font-semibold mb-4">Sale Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="flex items-center">
                <HiOutlineCalendar className="mr-3 h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Sale Date</p>
                  <p className="font-medium">{formatDateTime(sale.sale_date)}</p>
                </div>
              </div>

              <div className="flex items-center">
                <HiOutlineUser className="mr-3 h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Customer</p>
                  <p className="font-medium">
                    {sale.customer ? (
                      <Link 
                        to={`/customers/details/${sale.customer.id}`}
                        className="text-blue-600 hover:underline"
                      >
                        {sale.customer.name}
                      </Link>
                    ) : (
                      'Walk-in Customer'
                    )}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <HiOutlineCurrencyDollar className="mr-3 h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Payment Method</p>
                  <p className="font-medium">{sale.payment_method || 'Cash'}</p>
                </div>
              </div>

              <div className="flex items-center">
                <HiOutlineDocumentText className="mr-3 h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <Badge 
                    color={
                      sale.status === 'completed' ? 'success' :
                      sale.status === 'cancelled' ? 'failure' :
                      sale.status === 'refunded' ? 'warning' : 'gray'
                    }
                  >
                    {sale.status}
                  </Badge>
                </div>
              </div>
            </div>

            {sale.notes && (
              <div className="border-t pt-4">
                <p className="text-sm text-gray-500 mb-2">Notes</p>
                <p className="text-gray-700">{sale.notes}</p>
              </div>
            )}
          </Card>

          {/* Sale Items */}
          <Card className="mt-6">
            <h2 className="text-xl font-semibold mb-4">Items Sold</h2>
            
            {sale.items && sale.items.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <Table.Head>
                    <Table.HeadCell>Product</Table.HeadCell>
                    <Table.HeadCell>Quantity</Table.HeadCell>
                    <Table.HeadCell>Unit Price</Table.HeadCell>
                    <Table.HeadCell>Total</Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {sale.items.map((item, index) => (
                      <Table.Row key={item.id || index} className="bg-white">
                        <Table.Cell className="font-medium">
                          {item.product ? (
                            <Link 
                              to={`/products/details/${item.product.id}`}
                              className="text-blue-600 hover:underline"
                            >
                              {item.product.name}
                            </Link>
                          ) : (
                            'Unknown Product'
                          )}
                          {item.notes && (
                            <p className="text-sm text-gray-500 mt-1">{item.notes}</p>
                          )}
                        </Table.Cell>
                        <Table.Cell>{item.quantity}</Table.Cell>
                        <Table.Cell>{formatWithCurrency(item.unit_price)}</Table.Cell>
                        <Table.Cell className="font-medium">
                          {formatWithCurrency(item.unit_price * item.quantity)}
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
            ) : (
              <Alert color="info">
                No items found for this sale.
              </Alert>
            )}
          </Card>
        </div>

        {/* Sale Summary */}
        <div>
          <Card>
            <h2 className="text-xl font-semibold mb-4">Sale Summary</h2>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal:</span>
                <span className="font-medium">{formatWithCurrency(sale.subtotal)}</span>
              </div>
              
              {sale.discount_amount > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Discount:</span>
                  <span className="font-medium text-red-600">
                    -{formatWithCurrency(sale.discount_amount)}
                  </span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span className="text-gray-600">Tax:</span>
                <span className="font-medium">{formatWithCurrency(sale.tax_amount)}</span>
              </div>
              
              <div className="border-t pt-3">
                <div className="flex justify-between">
                  <span className="text-lg font-semibold">Total:</span>
                  <span className="text-lg font-bold text-green-600">
                    {formatWithCurrency(sale.total_amount)}
                  </span>
                </div>
              </div>
            </div>

            {/* Refund Information */}
            {refundSummary.refundCount > 0 && (
              <div className="border-t pt-4 mt-4">
                <h3 className="text-lg font-semibold mb-3 text-red-600">Refund Information</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Refunded:</span>
                    <span className="font-medium text-red-600">
                      -{formatWithCurrency(refundSummary.totalRefunded)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Refund Percentage:</span>
                    <span className="font-medium">
                      {refundSummary.refundPercentage.toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Net Amount:</span>
                    <span className="font-medium text-green-600">
                      {formatWithCurrency(sale?.total_amount - refundSummary.totalRefunded)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Refund Count:</span>
                    <span className="font-medium">{refundSummary.refundCount}</span>
                  </div>

                  {/* Refund Status Badge */}
                  <div className="pt-2">
                    {refundSummary.refundPercentage >= 100 ? (
                      <Badge color="failure" size="lg">
                        Fully Refunded
                      </Badge>
                    ) : refundSummary.refundPercentage > 0 ? (
                      <Badge color="warning" size="lg">
                        Partially Refunded
                      </Badge>
                    ) : (
                      <Badge color="gray" size="lg">
                        Refund Pending
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* Refund History */}
          {refunds.length > 0 && (
            <Card className="mt-6">
              <h2 className="text-xl font-semibold mb-4">Refund History</h2>
              <div className="space-y-4">
                {refunds.map((refund, index) => (
                  <div key={refund.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-medium">{refund.refund_number}</h3>
                        <p className="text-sm text-gray-600">
                          {formatDateTime(refund.created_at)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-red-600">
                          -{formatWithCurrency(refund.total_amount)}
                        </p>
                        <Badge
                          color={
                            refund.status === 'processed' ? 'success' :
                            refund.status === 'pending' ? 'warning' :
                            refund.status === 'rejected' ? 'failure' : 'gray'
                          }
                          size="sm"
                        >
                          {refund.status}
                        </Badge>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Reason:</span>
                        <span className="ml-2 capitalize">
                          {refund.reason.replace(/_/g, ' ')}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Method:</span>
                        <span className="ml-2 capitalize">
                          {refund.refund_method.replace(/_/g, ' ')}
                        </span>
                      </div>
                    </div>
                    {refund.reason_notes && (
                      <div className="mt-2 text-sm">
                        <span className="text-gray-600">Notes:</span>
                        <span className="ml-2">{refund.reason_notes}</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* Receipt Modal */}
      {showReceiptModal && sale && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Receipt Preview</h3>
              <Button
                color="gray"
                size="sm"
                onClick={() => setShowReceiptModal(false)}
              >
                Close
              </Button>
            </div>
            <SaleReceipt 
              sale={sale} 
              showPrintButton={true}
              onPrint={handlePrint}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default SaleDetails;
