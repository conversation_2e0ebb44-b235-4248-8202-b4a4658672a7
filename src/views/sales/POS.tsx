import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Button,
  TextInput,
  Badge,
  Spinner,
  Alert
} from 'flowbite-react';
import {
  HiOutlineSearch,
  HiOutlineQrcode,
  HiOutlineShoppingCart,
  HiOutlinePlus,
  HiOutlineMinus,
  HiOutlineTrash,
  HiOutlineTag,
  HiOutlineCash,
  HiOutlineCreditCard
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';
import { getProducts, Product } from '../../services/product';
import { Customer } from '../../services/customer';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import CustomerSelector from '../../components/sales/CustomerSelector';
import CartItemNotes from '../../components/sales/CartItemNotes';
import DiscountSelector from '../../components/sales/DiscountSelector';
import RestaurantControls from '../../components/sales/RestaurantControls';
import CafeControls from '../../components/sales/CafeControls';
import BarControls from '../../components/sales/BarControls';
import ClearCartConfirmation from '../../components/sales/ClearCartConfirmation';
import { BusinessType } from '../../components/settings/BusinessTypeSelector';

// Define cart item interface
interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  notes?: string;
}

const POSTerminal: React.FC = () => {
  const { currentOrganization, currentMember } = useOrganization();
  const { settings } = useOrganizationSettings();
  const formatWithCurrency = useCurrencyFormatter();
  const isOwner = currentMember?.role === 'owner';
  const [businessType, setBusinessType] = useState<BusinessType>('retail');
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [barcodeInput, setBarcodeInput] = useState('');
  const barcodeInputRef = useRef<HTMLInputElement>(null);
  const [discount, setDiscount] = useState<{ amount: number; isPercentage: boolean }>({
    amount: 0,
    isPercentage: false
  });
  const [showClearCartModal, setShowClearCartModal] = useState(false);

  // Restaurant-specific state
  const [selectedTable, setSelectedTable] = useState<any>(null);

  // Cafe-specific state
  const [isTakeaway, setIsTakeaway] = useState(false);

  // Bar-specific state
  const [isTabMode, setIsTabMode] = useState(false);

  // Get business type from organization settings
  useEffect(() => {
    if (settings && settings.business_type) {
      setBusinessType(settings.business_type as BusinessType);
    }
  }, [settings]);

  // Focus on barcode input when component mounts
  useEffect(() => {
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  }, []);

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      if (!currentOrganization) return;

      setLoading(true);
      setError(null);

      try {
        const { products: productData, error: fetchError } = await getProducts(
          currentOrganization.id,
          { limit: 100 }
        );

        if (fetchError) {
          setError(fetchError);
        } else {
          setProducts(productData);
          setFilteredProducts(productData);

          // Extract unique categories
          const uniqueCategories = Array.from(
            new Set(productData.map(p => p.category?.name || 'Uncategorized'))
          );
          setCategories(['all', ...uniqueCategories]);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch products');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [currentOrganization]);

  // Filter products based on search term and category
  useEffect(() => {
    let filtered = products;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        product =>
          product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (product.sku && product.sku.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (product.barcode && product.barcode.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(
        product =>
          (product.category?.name || 'Uncategorized') === selectedCategory
      );
    }

    setFilteredProducts(filtered);
  }, [products, searchTerm, selectedCategory]);

  // Handle barcode scan
  const handleBarcodeScan = (e: React.FormEvent) => {
    e.preventDefault();

    if (!barcodeInput) return;

    const product = products.find(p => p.barcode === barcodeInput);

    if (product) {
      addToCart(product);
      setBarcodeInput('');
    } else {
      setError(`Product with barcode ${barcodeInput} not found`);
      setTimeout(() => setError(null), 3000);
    }

    // Refocus on barcode input
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  };

  // Add product to cart
  const addToCart = (product: Product) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.product.id === product.id);

      if (existingItem) {
        // Update quantity if item already exists
        return prevCart.map(item =>
          item.product.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        // Add new item
        return [...prevCart, { id: product.id, product, quantity: 1 }];
      }
    });
  };

  // Update cart item quantity
  const updateCartItemQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    setCart(prevCart =>
      prevCart.map(item =>
        item.id === itemId
          ? { ...item, quantity: newQuantity }
          : item
      )
    );
  };

  // Remove item from cart
  const removeFromCart = (itemId: string) => {
    setCart(prevCart => prevCart.filter(item => item.id !== itemId));
  };

  // Add notes to cart item
  const addNotesToCartItem = (itemId: string, notes: string) => {
    setCart(prevCart =>
      prevCart.map(item =>
        item.id === itemId
          ? { ...item, notes }
          : item
      )
    );
  };

  // Calculate subtotal
  const calculateSubtotal = () => {
    return cart.reduce((total, item) => {
      return total + (item.product.unit_price * item.quantity);
    }, 0);
  };

  // Calculate discount
  const calculateDiscount = () => {
    const subtotal = calculateSubtotal();
    if (discount.isPercentage) {
      return subtotal * (discount.amount / 100);
    } else {
      return discount.amount;
    }
  };

  // Calculate subtotal after discount
  const calculateDiscountedSubtotal = () => {
    return calculateSubtotal() - calculateDiscount();
  };

  // Calculate tax based on organization settings
  const calculateTax = () => {
    // Get tax settings from organization settings
    const taxSettings = settings?.tax_settings || {
      vat_rate: 12, // Default to 12% VAT (Philippines standard)
      vat_inclusive: true, // Default to VAT-inclusive pricing
      tax_enabled: true // Default to enabled
    };

    // If tax is disabled, return 0
    if (!taxSettings.tax_enabled) {
      return 0;
    }

    const discountedSubtotal = calculateDiscountedSubtotal();

    // Calculate tax based on whether prices are VAT-inclusive or not
    if (taxSettings.vat_inclusive) {
      // For VAT-inclusive pricing, tax is already included in the price
      // Tax = Subtotal - (Subtotal / (1 + VAT rate))
      return discountedSubtotal - (discountedSubtotal / (1 + (taxSettings.vat_rate / 100)));
    } else {
      // For VAT-exclusive pricing, tax is added on top of the price
      // Tax = Subtotal * VAT rate
      return discountedSubtotal * (taxSettings.vat_rate / 100);
    }
  };

  // Calculate total
  const calculateTotal = () => {
    const taxSettings = settings?.tax_settings || {
      vat_rate: 12,
      vat_inclusive: true,
      tax_enabled: true
    };

    const discountedSubtotal = calculateDiscountedSubtotal();

    // If tax is disabled or prices are VAT-inclusive, the total is just the discounted subtotal
    // For VAT-inclusive pricing, the tax is already included in the price
    if (!taxSettings.tax_enabled || taxSettings.vat_inclusive) {
      return discountedSubtotal;
    } else {
      // For VAT-exclusive pricing, add the tax to the discounted subtotal
      return discountedSubtotal + calculateTax();
    }
  };

  // Handle discount application
  const handleApplyDiscount = (amount: number, isPercentage: boolean) => {
    setDiscount({ amount, isPercentage });
  };

  // Clear cart
  const clearCart = () => {
    if (cart.length > 0) {
      setShowClearCartModal(true);
    }
  };

  // Confirm clear cart
  const confirmClearCart = () => {
    setCart([]);
  };



  return (
    <div className="w-full h-full bg-gray-50">
      <div className="grid grid-cols-1 lg:grid-cols-4 h-full">
        {/* Left Column - Product Selection (wider) */}
        <div className="lg:col-span-3 p-4">
          <div className="bg-white rounded-lg shadow h-full flex flex-col">
            {/* Header with Business Type and Search */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex justify-between items-center mb-4">
                {/* Business Type Badge */}
                <div className="flex items-center">
                  <span className="text-sm font-medium mr-2">Business Type:</span>
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full capitalize">
                    {businessType}
                  </span>
                  {isOwner && (
                    <Button
                      size="xs"
                      color="light"
                      pill
                      className="ml-2"
                      onClick={() => window.open('/organization/settings', '_blank')}
                      title="Change in Organization Settings"
                    >
                      <HiOutlineTag className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Barcode Scanner */}
              <form onSubmit={handleBarcodeScan} className="mb-4">
                <div className="flex gap-2">
                  <TextInput
                    ref={barcodeInputRef}
                    type="text"
                    placeholder="Scan barcode or enter product code"
                    value={barcodeInput}
                    onChange={(e) => setBarcodeInput(e.target.value)}
                    className="flex-1"
                    icon={HiOutlineQrcode}
                  />
                  <Button type="submit" color="blue">
                    Add
                  </Button>
                </div>
              </form>

              {/* Error Message */}
              {error && (
                <Alert color="failure" className="mb-4">
                  {error}
                </Alert>
              )}

              {/* Product Search */}
              <div>
                <TextInput
                  type="text"
                  placeholder="Search products by name, SKU, or barcode"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                  icon={HiOutlineSearch}
                />
              </div>
            </div>

            {/* Category Tabs */}
            <div className="border-b border-gray-200 bg-gray-50">
              <div className="flex overflow-x-auto">
                {categories.map((category) => (
                  <button
                    key={category}
                    className={`px-4 py-3 text-sm font-medium whitespace-nowrap ${
                      selectedCategory === category
                        ? 'text-blue-600 border-b-2 border-blue-600 bg-white'
                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                    }`}
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category === 'all' ? 'All Products' : category}
                  </button>
                ))}
              </div>
            </div>

            {/* Product Grid */}
            <div className="flex-grow overflow-y-auto p-4">
              {loading ? (
                <div className="flex justify-center items-center h-full">
                  <Spinner size="xl" />
                </div>
              ) : filteredProducts.length === 0 ? (
                <div className="text-center py-8 h-full flex flex-col justify-center">
                  <HiOutlineSearch className="mx-auto h-12 w-12 text-gray-300" />
                  <p className="text-gray-500 mt-2">No products found</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                  {filteredProducts.map((product) => (
                    <div
                      key={product.id}
                      className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md cursor-pointer transition-all hover:border-blue-300 hover:scale-105"
                      onClick={() => addToCart(product)}
                    >
                      <div className="relative">
                        {product.image_url ? (
                          <img
                            src={product.image_url}
                            alt={product.name}
                            className="w-full h-32 object-cover rounded-t-lg"
                          />
                        ) : (
                          <div className="w-full h-32 bg-gray-100 flex items-center justify-center rounded-t-lg">
                            <HiOutlineTag className="h-10 w-10 text-gray-300" />
                          </div>
                        )}
                        {product.stock_quantity !== null && (
                          <div className="absolute top-2 right-2">
                            <Badge color={product.stock_quantity > 0 ? 'success' : 'failure'} size="sm">
                              {product.stock_quantity}
                            </Badge>
                          </div>
                        )}
                      </div>
                      <div className="p-3">
                        <h3 className="text-sm font-medium truncate mb-1">{product.name}</h3>
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-bold text-blue-600">
                            {formatWithCurrency(product.unit_price)}
                          </span>
                          <Button size="xs" color="light" className="p-1">
                            <HiOutlinePlus className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Column - Cart (Full Height with Fixed Header and Footer) */}
        <div className="bg-white shadow-lg border-l border-gray-200 h-screen sticky top-0 right-0 flex flex-col">
          {/* Fixed Top Section - Header and Customer Controls */}
          <div className="flex-shrink-0 bg-white z-10 shadow-sm">
            {/* Cart Header */}
            <div className="flex justify-between items-center p-3 border-b border-gray-200">
              <h2 className="text-lg font-bold">Current Sale</h2>
              <Button color="failure" size="xs" onClick={clearCart} className="px-2">
                <HiOutlineTrash className="h-3 w-3 mr-1" />
                Clear
              </Button>
            </div>

            {/* Customer and Business Controls Section - More Compact */}
            <div className="border-b border-gray-200 p-2">
              {/* Customer Selection */}
              <div className="mb-2">
                <CustomerSelector
                  onSelectCustomer={setCustomer}
                  selectedCustomerId={customer?.id}
                />
              </div>

              {/* Business-specific controls - ultra compact */}
              <div className="text-xs">
                {businessType === 'restaurant' && (
                  <RestaurantControls
                    onSelectTable={setSelectedTable}
                    selectedTable={selectedTable}
                  />
                )}

                {businessType === 'cafe' && (
                  <CafeControls
                    onToggleTakeaway={setIsTakeaway}
                    isTakeaway={isTakeaway}
                  />
                )}

                {businessType === 'bar' && (
                  <BarControls
                    onToggleTabMode={setIsTabMode}
                    isTabMode={isTabMode}
                  />
                )}
              </div>
            </div>
          </div>

          {/* Cart Items - Optimized for more vertical space */}
          <div className="flex-grow overflow-y-auto p-2 pb-4">
            {cart.length === 0 ? (
              <div className="text-center py-6 h-full flex flex-col justify-center items-center">
                <HiOutlineShoppingCart className="mx-auto h-12 w-12 text-gray-300" />
                <p className="mt-2 text-gray-500">Your cart is empty</p>
              </div>
            ) : (
              <div className="space-y-2 pb-2">
                {cart.map((item) => (
                  <div key={item.id} className="border-b border-gray-100 p-2 text-sm">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h3 className="font-medium text-gray-800 mr-2">{item.product.name}</h3>
                          <div className="flex items-center bg-gray-50 rounded-md ml-auto">
                            <Button
                              size="xs"
                              color="light"
                              className="rounded-l-md rounded-r-none border-0 p-1"
                              onClick={() => updateCartItemQuantity(item.id, item.quantity - 1)}
                            >
                              <HiOutlineMinus className="h-3 w-3" />
                            </Button>
                            <span className="px-1 min-w-[1.5rem] text-center text-xs font-medium">
                              {item.quantity}
                            </span>
                            <Button
                              size="xs"
                              color="light"
                              className="rounded-r-md rounded-l-none border-0 p-1"
                              onClick={() => updateCartItemQuantity(item.id, item.quantity + 1)}
                            >
                              <HiOutlinePlus className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <div className="flex items-center justify-between text-xs text-gray-500 mt-1">
                          <span>{formatWithCurrency(item.product.unit_price)} each</span>
                          <span className="font-bold text-gray-800">
                            {formatWithCurrency(item.product.unit_price * item.quantity)}
                          </span>
                        </div>
                        {item.notes && (
                          <div className="mt-1 text-xs italic text-gray-500 bg-gray-50 p-1 rounded">
                            {item.notes}
                          </div>
                        )}
                      </div>
                      <div className="ml-2 flex flex-col gap-1">
                        <CartItemNotes
                          itemId={item.id}
                          initialNotes={item.notes}
                          onSave={addNotesToCartItem}
                        />
                        <Button
                          size="xs"
                          color="failure"
                          className="p-1"
                          onClick={() => removeFromCart(item.id)}
                        >
                          <HiOutlineTrash className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
                </div>
              )}
            </div>

          {/* Cart Summary - Fixed at Bottom */}
          <div className="border-t border-gray-200 bg-gray-50 p-2 flex-shrink-0 sticky bottom-0 shadow-md z-10">
            <div className="space-y-1 mb-2">
              <div className="flex justify-between text-xs">
                <span className="text-gray-600">Subtotal</span>
                <span>{formatWithCurrency(calculateSubtotal())}</span>
              </div>

              {/* Discount */}
              <div className="flex justify-between items-center text-xs">
                <div className="flex items-center">
                  <span className="text-gray-600 mr-1">Discount</span>
                  <DiscountSelector
                    subtotal={calculateSubtotal()}
                    onApplyDiscount={handleApplyDiscount}
                    currentDiscount={discount}
                  />
                </div>
                <span className="text-red-500">-{formatWithCurrency(calculateDiscount())}</span>
              </div>

              <div className="flex justify-between text-xs">
                <span className="text-gray-600">
                  {settings?.tax_settings?.tax_enabled ?
                    `VAT ${settings?.tax_settings?.vat_rate || 12}%${settings?.tax_settings?.vat_inclusive ? ' (incl.)' : ''}` :
                    'No Tax'
                  }
                </span>
                <span>{formatWithCurrency(calculateTax())}</span>
              </div>

              <div className="flex justify-between font-bold text-base pt-1 border-t border-gray-200">
                <span>Total</span>
                <span className="text-blue-600">{formatWithCurrency(calculateTotal())}</span>
              </div>
            </div>

            {/* Payment Buttons */}
            <div className="grid grid-cols-2 gap-2">
              <Button
                color="success"
                size="sm"
                disabled={cart.length === 0}
                className="py-1"
              >
                <HiOutlineCash className="mr-1 h-4 w-4" />
                Cash
              </Button>
              <Button
                color="info"
                size="sm"
                disabled={cart.length === 0}
                className="py-1"
              >
                <HiOutlineCreditCard className="mr-1 h-4 w-4" />
                Card
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Clear Cart Confirmation Modal */}
      <ClearCartConfirmation
        show={showClearCartModal}
        onClose={() => setShowClearCartModal(false)}
        onConfirm={confirmClearCart}
        itemCount={cart.length}
      />
    </div>
  );
};

export default POSTerminal;
