import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Button,
  TextInput,
  <PERSON>bs,
  Bad<PERSON>,
  Spinner,
  <PERSON><PERSON>,
  Dropdown
} from 'flowbite-react';
import {
  HiOutlineSearch,
  HiOutlineQrcode,
  HiOutlineShoppingCart,
  HiOutlinePlus,
  HiOutlineMinus,
  HiOutlineTrash,
  HiOutlineUser,
  HiOutlineTag,
  HiOutlineCash,
  HiOutlineCreditCard,
  HiOutlineReceiptTax,
  HiOutlineTicket,
  HiOutlineDocumentText
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';
import { getProducts, Product } from '../../services/product';
import { Customer } from '../../services/customer';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import CustomerSelector from '../../components/sales/CustomerSelector';
import CartItemNotes from '../../components/sales/CartItemNotes';
import DiscountSelector from '../../components/sales/DiscountSelector';
import RestaurantControls from '../../components/sales/RestaurantControls';
import CafeControls from '../../components/sales/CafeControls';
import ClearCartConfirmation from '../../components/sales/ClearCartConfirmation';

// Define business types
type BusinessType = 'retail' | 'restaurant' | 'cafe';

// Define cart item interface
interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  notes?: string;
}

const POSTerminal: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const { settings } = useOrganizationSettings();
  const formatWithCurrency = useCurrencyFormatter();
  const [businessType, setBusinessType] = useState<BusinessType>('retail');
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [barcodeInput, setBarcodeInput] = useState('');
  const barcodeInputRef = useRef<HTMLInputElement>(null);
  const [discount, setDiscount] = useState<{ amount: number; isPercentage: boolean }>({
    amount: 0,
    isPercentage: false
  });
  const [showClearCartModal, setShowClearCartModal] = useState(false);

  // Restaurant-specific state
  const [selectedTable, setSelectedTable] = useState<any>(null);

  // Cafe-specific state
  const [isTakeaway, setIsTakeaway] = useState(false);

  // Focus on barcode input when component mounts
  useEffect(() => {
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  }, []);

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      if (!currentOrganization) return;

      setLoading(true);
      setError(null);

      try {
        const { products: productData, error: fetchError } = await getProducts(
          currentOrganization.id,
          { limit: 100 }
        );

        if (fetchError) {
          setError(fetchError);
        } else {
          setProducts(productData);
          setFilteredProducts(productData);

          // Extract unique categories
          const uniqueCategories = Array.from(
            new Set(productData.map(p => p.category?.name || 'Uncategorized'))
          );
          setCategories(['all', ...uniqueCategories]);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch products');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [currentOrganization]);

  // Filter products based on search term and category
  useEffect(() => {
    let filtered = products;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        product =>
          product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (product.sku && product.sku.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (product.barcode && product.barcode.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(
        product =>
          (product.category?.name || 'Uncategorized') === selectedCategory
      );
    }

    setFilteredProducts(filtered);
  }, [products, searchTerm, selectedCategory]);

  // Handle barcode scan
  const handleBarcodeScan = (e: React.FormEvent) => {
    e.preventDefault();

    if (!barcodeInput) return;

    const product = products.find(p => p.barcode === barcodeInput);

    if (product) {
      addToCart(product);
      setBarcodeInput('');
    } else {
      setError(`Product with barcode ${barcodeInput} not found`);
      setTimeout(() => setError(null), 3000);
    }

    // Refocus on barcode input
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  };

  // Add product to cart
  const addToCart = (product: Product) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.product.id === product.id);

      if (existingItem) {
        // Update quantity if item already exists
        return prevCart.map(item =>
          item.product.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        // Add new item
        return [...prevCart, { id: product.id, product, quantity: 1 }];
      }
    });
  };

  // Update cart item quantity
  const updateCartItemQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    setCart(prevCart =>
      prevCart.map(item =>
        item.id === itemId
          ? { ...item, quantity: newQuantity }
          : item
      )
    );
  };

  // Remove item from cart
  const removeFromCart = (itemId: string) => {
    setCart(prevCart => prevCart.filter(item => item.id !== itemId));
  };

  // Add notes to cart item
  const addNotesToCartItem = (itemId: string, notes: string) => {
    setCart(prevCart =>
      prevCart.map(item =>
        item.id === itemId
          ? { ...item, notes }
          : item
      )
    );
  };

  // Calculate subtotal
  const calculateSubtotal = () => {
    return cart.reduce((total, item) => {
      return total + (item.product.unit_price * item.quantity);
    }, 0);
  };

  // Calculate discount
  const calculateDiscount = () => {
    const subtotal = calculateSubtotal();
    if (discount.isPercentage) {
      return subtotal * (discount.amount / 100);
    } else {
      return discount.amount;
    }
  };

  // Calculate subtotal after discount
  const calculateDiscountedSubtotal = () => {
    return calculateSubtotal() - calculateDiscount();
  };

  // Calculate tax based on organization settings
  const calculateTax = () => {
    // Get tax settings from organization settings
    const taxSettings = settings?.tax_settings || {
      vat_rate: 0, // Default to 0% VAT if settings not available
      vat_inclusive: true, // Default to VAT-inclusive pricing
      tax_enabled: false // Default to disabled if settings not available
    };

    // If tax is disabled, return 0
    if (!taxSettings.tax_enabled) {
      return 0;
    }

    const discountedSubtotal = calculateDiscountedSubtotal();

    // Calculate tax based on whether prices are VAT-inclusive or not
    if (taxSettings.vat_inclusive) {
      // For VAT-inclusive pricing, tax is already included in the price
      return discountedSubtotal - (discountedSubtotal / (1 + (taxSettings.vat_rate / 100)));
    } else {
      // For VAT-exclusive pricing, tax is added on top of the price
      return discountedSubtotal * (taxSettings.vat_rate / 100);
    }
  };

  // Calculate total
  const calculateTotal = () => {
    const taxSettings = settings?.tax_settings || {
      vat_rate: 0,
      vat_inclusive: true,
      tax_enabled: false
    };

    const discountedSubtotal = calculateDiscountedSubtotal();

    // If tax is disabled or prices are VAT-inclusive, the total is just the discounted subtotal
    // For VAT-inclusive pricing, the tax is already included in the price
    if (!taxSettings.tax_enabled || taxSettings.vat_inclusive) {
      return discountedSubtotal;
    } else {
      // For VAT-exclusive pricing, add the tax to the discounted subtotal
      return discountedSubtotal + calculateTax();
    }
  };

  // Handle discount application
  const handleApplyDiscount = (amount: number, isPercentage: boolean) => {
    setDiscount({ amount, isPercentage });
  };

  // Clear cart
  const clearCart = () => {
    if (cart.length > 0) {
      setShowClearCartModal(true);
    }
  };

  // Confirm clear cart
  const confirmClearCart = () => {
    setCart([]);
  };

  // Handle business type change
  const handleBusinessTypeChange = (type: BusinessType) => {
    setBusinessType(type);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Product Selection */}
        <div className="lg:col-span-2">
          <Card>
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4">
              <h1 className="text-2xl font-bold">POS Terminal</h1>

              {/* Business Type Selector */}
              <div className="flex gap-2">
                <Button
                  size="sm"
                  color={businessType === 'retail' ? 'blue' : 'light'}
                  onClick={() => handleBusinessTypeChange('retail')}
                >
                  Retail
                </Button>
                <Button
                  size="sm"
                  color={businessType === 'restaurant' ? 'blue' : 'light'}
                  onClick={() => handleBusinessTypeChange('restaurant')}
                >
                  Restaurant
                </Button>
                <Button
                  size="sm"
                  color={businessType === 'cafe' ? 'blue' : 'light'}
                  onClick={() => handleBusinessTypeChange('cafe')}
                >
                  Cafe
                </Button>
              </div>
            </div>

            {/* Barcode Scanner */}
            <form onSubmit={handleBarcodeScan} className="mb-4">
              <div className="flex gap-2">
                <TextInput
                  ref={barcodeInputRef}
                  type="text"
                  placeholder="Scan barcode or enter product code"
                  value={barcodeInput}
                  onChange={(e) => setBarcodeInput(e.target.value)}
                  className="flex-1"
                  icon={HiOutlineQrcode}
                />
                <Button type="submit">
                  Add
                </Button>
              </div>
            </form>

            {/* Error Message */}
            {error && (
              <Alert color="failure" className="mb-4">
                {error}
              </Alert>
            )}

            {/* Product Search */}
            <div className="mb-4">
              <TextInput
                type="text"
                placeholder="Search products by name, SKU, or barcode"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
                icon={HiOutlineSearch}
              />
            </div>

            {/* Category Tabs */}
            <div className="mb-4 overflow-x-auto">
              <div className="flex border-b border-gray-200 overflow-x-auto">
                {categories.map((category) => (
                  <button
                    key={category}
                    className={`px-4 py-2 text-sm font-medium ${
                      selectedCategory === category
                        ? 'text-blue-600 border-b-2 border-blue-600'
                        : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category === 'all' ? 'All Products' : category}
                  </button>
                ))}
              </div>
            </div>

            {/* Product Grid */}
            {loading ? (
              <div className="flex justify-center items-center p-8">
                <Spinner size="xl" />
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No products found</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {filteredProducts.map((product) => (
                  <div
                    key={product.id}
                    className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md cursor-pointer transition-shadow"
                    onClick={() => addToCart(product)}
                  >
                    <div className="p-3">
                      {product.image_url ? (
                        <img
                          src={product.image_url}
                          alt={product.name}
                          className="w-full h-24 object-contain mb-2"
                        />
                      ) : (
                        <div className="w-full h-24 bg-gray-100 flex items-center justify-center mb-2">
                          <HiOutlineTag className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                      <h3 className="text-sm font-medium truncate">{product.name}</h3>
                      <div className="flex justify-between items-center mt-2">
                        <span className="text-sm font-bold">
                          {formatWithCurrency(product.unit_price)}
                        </span>
                        {product.stock_quantity !== null && (
                          <Badge color={product.stock_quantity > 0 ? 'success' : 'failure'} size="sm">
                            {product.stock_quantity} in stock
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>
        </div>

        {/* Right Column - Cart */}
        <div>
          <Card>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">Current Sale</h2>
              <Button color="failure" size="xs" onClick={clearCart}>
                <HiOutlineTrash className="mr-1 h-4 w-4" />
                Clear
              </Button>
            </div>

            {/* Customer Selection */}
            <div className="mb-4">
              <CustomerSelector
                onSelectCustomer={setCustomer}
                selectedCustomerId={customer?.id}
              />
            </div>

            {/* Business-specific controls */}
            {businessType === 'restaurant' && (
              <RestaurantControls
                onSelectTable={setSelectedTable}
                selectedTable={selectedTable}
              />
            )}

            {businessType === 'cafe' && (
              <CafeControls
                onToggleTakeaway={setIsTakeaway}
                isTakeaway={isTakeaway}
              />
            )}

            {/* Cart Items */}
            <div className="mb-4 max-h-96 overflow-y-auto">
              {cart.length === 0 ? (
                <div className="text-center py-8">
                  <HiOutlineShoppingCart className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-gray-500">Your cart is empty</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {cart.map((item) => (
                    <div key={item.id} className="bg-gray-50 p-3 rounded-lg">
                      <div className="flex justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium">{item.product.name}</h3>
                          <p className="text-sm text-gray-500">
                            {formatWithCurrency(item.product.unit_price)} each
                          </p>
                          {item.notes && (
                            <p className="text-xs text-gray-500 italic mt-1">
                              Note: {item.notes}
                            </p>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="font-bold">
                            {formatWithCurrency(item.product.unit_price * item.quantity)}
                          </p>
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <div className="flex items-center">
                          <Button
                            size="xs"
                            color="light"
                            onClick={() => updateCartItemQuantity(item.id, item.quantity - 1)}
                          >
                            <HiOutlineMinus className="h-3 w-3" />
                          </Button>
                          <span className="mx-2 min-w-[2rem] text-center">{item.quantity}</span>
                          <Button
                            size="xs"
                            color="light"
                            onClick={() => updateCartItemQuantity(item.id, item.quantity + 1)}
                          >
                            <HiOutlinePlus className="h-3 w-3" />
                          </Button>
                        </div>
                        <div className="flex gap-1">
                          <CartItemNotes
                            itemId={item.id}
                            initialNotes={item.notes}
                            onSave={addNotesToCartItem}
                          />
                          <Button
                            size="xs"
                            color="failure"
                            onClick={() => removeFromCart(item.id)}
                          >
                            <HiOutlineTrash className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Cart Summary */}
            <div className="border-t border-gray-200 pt-4 mb-4">
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Subtotal</span>
                <span>{formatWithCurrency(calculateSubtotal())}</span>
              </div>

              {/* Discount */}
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center">
                  <span className="text-gray-600 mr-2">Discount</span>
                  <DiscountSelector
                    subtotal={calculateSubtotal()}
                    onApplyDiscount={handleApplyDiscount}
                    currentDiscount={discount}
                  />
                </div>
                <span>-{formatWithCurrency(calculateDiscount())}</span>
              </div>

              <div className="flex justify-between mb-2">
                <span className="text-gray-600">
                  {(() => {
                    const taxSettings = settings?.tax_settings || {};
                    if (taxSettings.tax_enabled && taxSettings.vat_rate > 0) {
                      return `VAT ${taxSettings.vat_rate}%${taxSettings.vat_inclusive ? ' (incl.)' : ''}`;
                    } else {
                      return 'No Tax';
                    }
                  })()}
                </span>
                <span>{formatWithCurrency(calculateTax())}</span>
              </div>
              <div className="flex justify-between font-bold text-lg">
                <span>Total</span>
                <span>{formatWithCurrency(calculateTotal())}</span>
              </div>
            </div>

            {/* Payment Buttons */}
            <div className="grid grid-cols-2 gap-2">
              <Button color="success" disabled={cart.length === 0}>
                <HiOutlineCash className="mr-2 h-5 w-5" />
                Cash
              </Button>
              <Button color="info" disabled={cart.length === 0}>
                <HiOutlineCreditCard className="mr-2 h-5 w-5" />
                Card
              </Button>
            </div>
          </Card>
        </div>
      </div>

      {/* Clear Cart Confirmation Modal */}
      <ClearCartConfirmation
        show={showClearCartModal}
        onClose={() => setShowClearCartModal(false)}
        onConfirm={confirmClearCart}
        itemCount={cart.length}
      />
    </div>
  );
};

export default POSTerminal;
