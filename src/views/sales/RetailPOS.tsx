import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Button,
  TextInput,
  Badge,
  Spinner,
  Alert,
  Label,
  Modal
} from 'flowbite-react';
import ReactMarkdown from 'react-markdown';
import {
  HiOutlineQrcode,
  HiOutlineShoppingCart,
  HiOutlinePlus,
  HiOutlineMinus,
  HiOutlineTrash,
  HiOutlineCash,
  HiOutlineCreditCard,
  HiOutlineUser,
  HiOutlineGift,
  HiOutlineExclamationCircle,
  HiOutlineDocumentDownload
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';
import { useLoyalty } from '../../context/LoyaltyContext';
import { Product } from '../../services/product';
import { Customer } from '../../services/customer';
import { createSale, getSaleById } from '../../services/sale';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { formatQuantityWithSeparators, formatDateTime } from '../../utils/formatters';
import { supabase } from '../../lib/supabase';
import CustomerSelector from '../../components/sales/CustomerSelector';
import CartItemNotes from '../../components/sales/CartItemNotes';
import DiscountSelector from '../../components/sales/DiscountSelector';
import LoyaltyPointsRedemption from '../../components/sales/LoyaltyPointsRedemption';
import CustomerLoyaltyInfo from '../../components/sales/CustomerLoyaltyInfo';
import SaleReceipt from '../../components/sales/SaleReceipt';
import { getInventorySettings } from '../../services/floatInventory';
import CreateCustomerModal from '../../components/sales/CreateCustomerModal';
import RemoveItemConfirmation from '../../components/sales/RemoveItemConfirmation';
import ClearCartConfirmation from '../../components/sales/ClearCartConfirmation';
import SalesHistory from '../../components/sales/SalesHistory';
import RefundProcessor from '../../components/refund/RefundProcessor';
import TimeClock from '../../components/faceRecognition/TimeClock';

import { useAuth } from '../../context/AuthContext';
import EnhancedProductSearchSelector from '../../components/products/EnhancedProductSearchSelector';
import EnhancedNumberInput from '../../components/common/EnhancedNumberInput';
import { productCache } from '../../services/productCache';
import { toast } from 'react-hot-toast';

// Define cart item interface
interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  notes?: string;
}

const RetailPOS: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const { settings } = useOrganizationSettings();
  const { settings: loyaltySettings } = useLoyalty();
  const { user } = useAuth();
  const formatWithCurrency = useCurrencyFormatter();
  const [error, setError] = useState<string | null>(null);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [barcodeInput, setBarcodeInput] = useState('');
  const [quantityToAdd, setQuantityToAdd] = useState(1);
  const barcodeInputRef = useRef<HTMLInputElement>(null);
  const [discount, setDiscount] = useState<{ amount: number; isPercentage: boolean }>({
    amount: 0,
    isPercentage: false
  });
  const [loyaltyPointsUsed, setLoyaltyPointsUsed] = useState<number>(0);
  const [loyaltyPointsDiscount, setLoyaltyPointsDiscount] = useState<number>(0);
  const [showCreateCustomerModal, setShowCreateCustomerModal] = useState<boolean>(false);
  const [productSelectorKey, setProductSelectorKey] = useState<number>(0);
  const [showRemoveItemModal, setShowRemoveItemModal] = useState<boolean>(false);
  const [itemToRemove, setItemToRemove] = useState<{id: string, name: string} | null>(null);
  const [showClearCartModal, setShowClearCartModal] = useState(false);

  // Keyboard shortcuts modal
  const [showShortcutsModal, setShowShortcutsModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card'>('cash');
  const [cashTendered, setCashTendered] = useState<string>('');
  const [processingPayment, setProcessingPayment] = useState(false);

  // Refund modal
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [showTimeClockModal, setShowTimeClockModal] = useState(false);

  // Clear cart function - defined before it's used in the keyboard shortcut handler
  const clearCart = () => {
    if (cart.length > 0) {
      setShowClearCartModal(true);
    }
  };

  // Confirm clear cart
  const confirmClearCart = () => {
    setCart([]);
    setCustomer(null);
    setLoyaltyPointsUsed(0);
    setLoyaltyPointsDiscount(0);
    setDiscount({ amount: 0, isPercentage: false });
    setQuantityToAdd(1); // Reset quantity to 1
    // Force a re-render of the product selector to ensure UI is refreshed
    setProductSelectorKey(prevKey => prevKey + 1);
  };

  // Keyboard shortcut handler
  const handleKeyboardShortcuts = useCallback((e: KeyboardEvent) => {
    // Don't trigger shortcuts when typing in input fields
    if (
      e.target instanceof HTMLInputElement ||
      e.target instanceof HTMLTextAreaElement ||
      e.target instanceof HTMLSelectElement
    ) {
      return;
    }

    // Special handling for Ctrl+P (print) and Ctrl+K which have browser defaults
    if ((e.key === 'p' || e.key === 'P') && (e.ctrlKey || e.metaKey)) {
      console.log('Ctrl+P detected');
      e.preventDefault();
      e.stopPropagation();
      if (cart.length > 0) {
        setPaymentMethod('cash');
        setCashTendered(calculateTotal().toFixed(2));
        setShowPaymentModal(true);
      } else {
        console.log('Cart is empty, not showing payment modal');
      }
      return;
    }

    if ((e.key === 'k' || e.key === 'K') && (e.ctrlKey || e.metaKey)) {
      console.log('Ctrl+K detected');
      e.preventDefault();
      e.stopPropagation();
      if (cart.length > 0) {
        setPaymentMethod('card');
        setShowPaymentModal(true);
      } else {
        console.log('Cart is empty, not showing payment modal');
      }
      return;
    }

    // Prevent default behavior for our other shortcuts
    if (
      (e.key === 'f' && e.ctrlKey) || // Search (Ctrl+F)
      (e.key === 'b' && e.ctrlKey) || // Barcode focus (Ctrl+B)
      (e.key === 'd' && e.ctrlKey) || // Discount (Ctrl+D)
      (e.key === 'c' && e.ctrlKey) || // Customer (Ctrl+C)
      (e.key === '/' && e.ctrlKey) || // Help (Ctrl+/)
      e.key === 'F1' ||              // Help (F1)
      e.key === 'F2' ||              // Price check (F2)
      e.key === 'F3' ||              // Void item (F3)
      e.key === 'F4' ||              // Tax exempt (F4)
      e.key === 'F5' ||              // Refund (F5)
      e.key === 'F6' ||              // Time Clock (F6)
      e.key === 'Escape' ||          // Clear (Esc)
      e.key === '1' ||               // Quantity 1
      e.key === '5' ||               // Quantity 5
      e.key === '0'                  // Quantity 10
    ) {
      e.preventDefault();
    }

    // Handle shortcuts
    switch (true) {
      case e.key === 'f' && e.ctrlKey: // Ctrl+F: Focus on product search
        // Focus on the product search input (React Select component)
        const searchContainer = document.querySelector('.product-search-container');
        if (searchContainer) {
          const reactSelectInput = searchContainer.querySelector('input[type="text"]');
          if (reactSelectInput && reactSelectInput instanceof HTMLElement) {
            reactSelectInput.focus();
          }
        }
        break;

      case e.key === 'b' && e.ctrlKey: // Ctrl+B: Focus on barcode
        if (barcodeInputRef.current) barcodeInputRef.current.focus();
        break;

      case e.key === 'd' && e.ctrlKey: // Ctrl+D: Open discount
        // Find and click the discount button
        const discountButton = document.querySelector('[data-testid="discount-selector-button"]');
        if (discountButton && discountButton instanceof HTMLElement) {
          discountButton.click();
        }
        break;

      case e.key === 'c' && e.ctrlKey: // Ctrl+C: Open customer modal
        // Find the customer selector button and click it
        const customerButton = document.querySelector('[data-testid="customer-selector-button"]');
        if (customerButton && customerButton instanceof HTMLElement) {
          customerButton.click();
        }
        break;

      case e.key === '/' && e.ctrlKey || e.key === 'F1': // Ctrl+/ or F1: Show shortcuts
        setShowShortcutsModal(true);
        break;

      case e.key === 'F2': // F2: Price check
        setShowPriceCheckModal(true);
        break;

      case e.key === 'F3': // F3: Void selected item
        if (cart.length > 0) {
          // Get the last item in the cart
          const lastItemId = cart[cart.length - 1].id;
          removeFromCart(lastItemId);
        }
        break;

      case e.key === 'F4': // F4: Tax exempt
        console.log('Tax exempt shortcut triggered');
        break;

      case e.key === 'F5': // F5: Refund
        setShowRefundModal(true);
        break;

      case e.key === 'F6': // F6: Time Clock
        setShowTimeClockModal(true);
        break;

      case e.key === 'Escape': // Esc: Close modals or clear cart
        // First check if any modals are open and close them
        if (showShortcutsModal) {
          setShowShortcutsModal(false);
        } else if (showRefundModal) {
          setShowRefundModal(false);
        } else if (showTimeClockModal) {
          setShowTimeClockModal(false);
        } else if (showCreateCustomerModal) {
          setShowCreateCustomerModal(false);
        } else if (showRemoveItemModal) {
          setShowRemoveItemModal(false);
        } else if (showClearCartModal) {
          setShowClearCartModal(false);
        } else if (showPaymentModal) {
          setShowPaymentModal(false);
        } else if (showSuccessModal) {
          setShowSuccessModal(false);
        } else if (showReceiptModal) {
          setShowReceiptModal(false);
        } else if (showSalesListModal) {
          setShowSalesListModal(false);
        } else if (showPriceCheckModal) {
          setShowPriceCheckModal(false);
          setPriceCheckProduct(null);
        } else if (showManualModal) {
          setShowManualModal(false);
        } else if (cart.length > 0) {
          // Only show clear cart modal if no other modals are open and cart has items
          setShowClearCartModal(true);
        }
        break;

      case e.key === '1': // 1: Set quantity to 1
        setQuantityToAdd(1);
        break;

      case e.key === '5': // 5: Set quantity to 5
        setQuantityToAdd(5);
        break;

      case e.key === '0': // 0: Set quantity to 10
        setQuantityToAdd(10);
        break;

      default:
        break;
    }
  }, [cart, setPaymentMethod, setShowPaymentModal, showShortcutsModal, showRefundModal, showTimeClockModal, showCreateCustomerModal, showRemoveItemModal, showClearCartModal, showPaymentModal]);

  // Register keyboard shortcuts
  useEffect(() => {
    // Add the main keyboard shortcut handler
    window.addEventListener('keydown', handleKeyboardShortcuts);

    // Add specific handlers for Ctrl+P and Ctrl+K that are problematic
    const handleCtrlP = (e: KeyboardEvent) => {
      if ((e.key === 'p' || e.key === 'P') && (e.ctrlKey || e.metaKey)) {
        console.log('Direct Ctrl+P handler');
        e.preventDefault();
        e.stopPropagation();
        if (cart.length > 0) {
          setPaymentMethod('cash');
          setCashTendered(calculateTotal().toFixed(2));
          setShowPaymentModal(true);
        }
      }
    };

    const handleCtrlK = (e: KeyboardEvent) => {
      if ((e.key === 'k' || e.key === 'K') && (e.ctrlKey || e.metaKey)) {
        console.log('Direct Ctrl+K handler');
        e.preventDefault();
        e.stopPropagation();
        if (cart.length > 0) {
          setPaymentMethod('card');
          setShowPaymentModal(true);
        }
      }
    };

    // Add these handlers with capture phase to ensure they run first
    document.addEventListener('keydown', handleCtrlP, true);
    document.addEventListener('keydown', handleCtrlK, true);

    return () => {
      window.removeEventListener('keydown', handleKeyboardShortcuts);
      document.removeEventListener('keydown', handleCtrlP, true);
      document.removeEventListener('keydown', handleCtrlK, true);
    };
  }, [handleKeyboardShortcuts, cart]);

  // Listen for custom events from POSLayout buttons
  useEffect(() => {
    const handleShowSalesModal = () => {
      setShowSalesListModal(true);
    };

    const handleShowManualModal = () => {
      handleShowManual();
    };

    const handleShowShortcutsModalEvent = () => {
      setShowShortcutsModal(true);
    };

    const handleShowRefundModalEvent = () => {
      setShowRefundModal(true);
    };

    const handleShowTimeClockModalEvent = () => {
      setShowTimeClockModal(true);
    };

    // Add event listeners
    window.addEventListener('pos-show-sales-modal', handleShowSalesModal);
    window.addEventListener('pos-show-manual-modal', handleShowManualModal);
    window.addEventListener('pos-show-shortcuts-modal', handleShowShortcutsModalEvent);
    window.addEventListener('pos-show-refund-modal', handleShowRefundModalEvent);
    window.addEventListener('pos-show-timeclock-modal', handleShowTimeClockModalEvent);

    return () => {
      // Clean up event listeners
      window.removeEventListener('pos-show-sales-modal', handleShowSalesModal);
      window.removeEventListener('pos-show-manual-modal', handleShowManualModal);
      window.removeEventListener('pos-show-shortcuts-modal', handleShowShortcutsModalEvent);
      window.removeEventListener('pos-show-refund-modal', handleShowRefundModalEvent);
      window.removeEventListener('pos-show-timeclock-modal', handleShowTimeClockModalEvent);
    };
  }, []);

  // Focus on barcode input when component mounts (but don't force focus)
  useEffect(() => {
    // Only auto-focus on initial load, not when user is actively using other inputs
    const timer = setTimeout(() => {
      if (barcodeInputRef.current && !document.activeElement?.closest('.product-search-container')) {
        barcodeInputRef.current.focus();
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Preload some products for the cache
  useEffect(() => {
    const preloadProducts = async () => {
      if (!currentOrganization) return;

      try {
        // Use the product cache service to search for products
        await productCache.searchProducts(
          currentOrganization.id,
          '', // Empty search query to get popular products
          1,  // First page
          20  // Limit to 20 products for initial load
        );
      } catch (err: any) {
        console.error('Error preloading products:', err);
      }
    };

    preloadProducts();
  }, [currentOrganization]);

  // Handle barcode scan (for manual Enter key press)
  const handleBarcodeScan = async (e: React.FormEvent) => {
    e.preventDefault();
    await processBarcode(barcodeInput);
  };

  // Process barcode (shared function for both manual and automatic scanning)
  const processBarcode = async (barcode: string) => {
    if (!barcode || !currentOrganization) return;

    try {
      // Search for the product by barcode
      const { products: foundProducts } = await productCache.searchProducts(
        currentOrganization.id,
        barcode,
        1,
        10
      );

      // Find the product with matching barcode
      const product = foundProducts.find(p => p.barcode === barcode);

      if (product) {
        addToCart(product, quantityToAdd);
        setBarcodeInput('');
        setQuantityToAdd(1);

        // Show success feedback
        toast.success(`Added ${product.name} to cart`);
      } else {
        setError(`Product with barcode ${barcode} not found`);
        setTimeout(() => setError(null), 3000);
      }
    } catch (err: any) {
      setError(err.message || 'Error searching for product');
      setTimeout(() => setError(null), 3000);
    }

    // Refocus on barcode input
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  };

  // Handle barcode input change with automatic scanning
  const handleBarcodeInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setBarcodeInput(value);

    // Auto-scan when barcode reaches typical length (8-13 characters)
    // and contains only alphanumeric characters (typical barcode format)
    // Only auto-scan if the input was filled quickly (likely from a scanner)
    if (value.length >= 8 && value.length <= 13 && /^[a-zA-Z0-9]+$/.test(value)) {
      // Add a small delay to ensure the scanner has finished inputting
      setTimeout(async () => {
        // Only process if the input hasn't changed (scanner finished)
        // and if the barcode input is currently focused (not manually typed elsewhere)
        if (barcodeInputRef.current?.value === value && document.activeElement === barcodeInputRef.current) {
          await processBarcode(value);
        }
      }, 150);
    }
  };

  // Add product to cart
  const addToCart = (product: Product, quantity: number = 1) => {
    // Check if this would result in negative inventory
    const currentStock = product.stock_quantity || 0;
    const currentCartQuantity = cart
      .filter(item => item.product.id === product.id)
      .reduce((total, item) => total + item.quantity, 0);

    const wouldGoNegative = currentStock < (currentCartQuantity + quantity);

    if (wouldGoNegative && inventorySettings.warn_on_low_inventory) {
      // Show warning but allow the sale if configured
      toast.custom((t) => (
        <div className={`${t.visible ? 'animate-enter' : 'animate-leave'} max-w-md w-full bg-white shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5`}>
          <div className="flex-1 w-0 p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0 pt-0.5">
                <HiOutlineExclamationCircle className="h-6 w-6 text-yellow-500" />
              </div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-gray-900">
                  Low Inventory Warning
                </p>
                <p className="mt-1 text-sm text-gray-500">
                  Adding {quantity} of {product.name} will exceed available stock ({currentStock} available).
                  {!inventorySettings.allow_negative_inventory ? " This will create float inventory." : ""}
                </p>
              </div>
            </div>
          </div>
          <div className="flex border-l border-gray-200">
            <button
              onClick={() => toast.dismiss(t.id)}
              className="w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-indigo-600 hover:text-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              Dismiss
            </button>
          </div>
        </div>
      ), { duration: 5000 });
    }

    // Add to cart
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.product.id === product.id);

      if (existingItem) {
        // Update quantity if item already exists
        return prevCart.map(item =>
          item.product.id === product.id
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      } else {
        // Add new item
        return [...prevCart, { id: product.id, product, quantity }];
      }
    });
  };

  // Update cart item quantity
  const updateCartItemQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    setCart(prevCart =>
      prevCart.map(item =>
        item.id === itemId
          ? { ...item, quantity: newQuantity }
          : item
      )
    );
  };

  // Remove item from cart
  const removeFromCart = (itemId: string) => {
    // Find the item to remove
    const item = cart.find(item => item.id === itemId);

    if (item) {
      // Show confirmation dialog
      setItemToRemove({
        id: itemId,
        name: item.product.name
      });
      setShowRemoveItemModal(true);
    }
  };

  // Confirm remove item from cart
  const confirmRemoveFromCart = () => {
    if (itemToRemove) {
      setCart(prevCart => prevCart.filter(item => item.id !== itemToRemove.id));
      setItemToRemove(null);
    }
  };

  // Add notes to cart item
  const addNotesToCartItem = (itemId: string, notes: string) => {
    setCart(prevCart =>
      prevCart.map(item =>
        item.id === itemId
          ? { ...item, notes }
          : item
      )
    );
  };

  // Calculate subtotal
  const calculateSubtotal = () => {
    return cart.reduce((total, item) => {
      return total + (item.product.unit_price * item.quantity);
    }, 0);
  };

  // Calculate discount
  const calculateDiscount = () => {
    const subtotal = calculateSubtotal();
    if (discount.isPercentage) {
      return subtotal * (discount.amount / 100);
    } else {
      return discount.amount;
    }
  };

  // Calculate tax
  const calculateTax = () => {
    // Get tax settings from organization settings
    // First check if tax_rate is set to 0 in the main settings
    const taxRate = settings?.tax_rate;

    // Use tax_settings if available, otherwise create default settings
    const taxSettings = settings?.tax_settings || {
      vat_rate: 0, // Default to 0% VAT if settings not available
      vat_inclusive: true, // Default to VAT-inclusive pricing
      tax_enabled: false // Default to disabled if settings not available
    };

    // If tax_rate is explicitly set to 0 in the main settings, override the vat_rate
    const effectiveTaxRate = taxRate === 0 ? 0 : taxSettings.vat_rate;

    // If tax is disabled or rate is 0, return 0
    if (!taxSettings.tax_enabled || effectiveTaxRate === 0) {
      return 0;
    }

    const discountedSubtotal = calculateSubtotal() - calculateDiscount();

    // Calculate tax based on whether prices are VAT-inclusive or not
    if (taxSettings.vat_inclusive) {
      // For VAT-inclusive pricing, tax is already included in the price
      return discountedSubtotal - (discountedSubtotal / (1 + (effectiveTaxRate / 100)));
    } else {
      // For VAT-exclusive pricing, tax is added on top of the price
      return discountedSubtotal * (effectiveTaxRate / 100);
    }
  };

  // Calculate total
  const calculateTotal = () => {
    // Get tax settings from organization settings
    // First check if tax_rate is set to 0 in the main settings
    const taxRate = settings?.tax_rate;

    // Use tax_settings if available, otherwise create default settings
    const taxSettings = settings?.tax_settings || {
      vat_rate: 0,
      vat_inclusive: true,
      tax_enabled: false
    };

    // If tax_rate is explicitly set to 0 in the main settings, override the vat_rate
    const effectiveTaxRate = taxRate === 0 ? 0 : taxSettings.vat_rate;

    const discountedSubtotal = calculateSubtotal() - calculateDiscount();

    // Apply loyalty points discount
    const afterLoyaltyDiscount = discountedSubtotal - loyaltyPointsDiscount;

    // Ensure total doesn't go below zero
    const finalSubtotal = Math.max(0, afterLoyaltyDiscount);

    // If tax is disabled or rate is 0, return the subtotal
    if (!taxSettings.tax_enabled || effectiveTaxRate === 0) {
      return finalSubtotal;
    }

    // If prices are VAT-inclusive, the total is the same as the subtotal
    if (taxSettings.vat_inclusive) {
      return finalSubtotal;
    } else {
      // If prices are VAT-exclusive, add the tax to the subtotal
      return finalSubtotal + calculateTax();
    }
  };

  // Handle discount application
  const handleApplyDiscount = (amount: number, isPercentage: boolean) => {
    setDiscount({ amount, isPercentage });
  };

  // Handle loyalty points redemption
  const handleApplyLoyaltyPoints = (points: number, discount: number) => {
    setLoyaltyPointsUsed(points);
    setLoyaltyPointsDiscount(discount);
  };

  // Handle customer creation after successful creation
  const handleCustomerCreated = async (customerId: string) => {
    try {
      if (!currentOrganization) {
        console.log('No current organization found');
        return;
      }

      console.log('Fetching newly created customer with ID:', customerId);

      // Fetch the newly created customer
      const { data: newCustomer, error } = await supabase
        .from('customers')
        .select('*')
        .eq('id', customerId)
        .single();

      if (error) {
        console.error('Error fetching new customer:', error);
        return;
      }

      console.log('Customer fetched successfully:', newCustomer);

      // Set the new customer as the selected customer
      setCustomer(newCustomer);
    } catch (err) {
      console.error('Error handling new customer:', err);
    }
  };

  // Handle quantity change
  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0) {
      setQuantityToAdd(value);
    }
  };

  // Keyboard shortcuts help data
  const keyboardShortcuts = [
    // Navigation shortcuts
    { key: 'Ctrl+B', description: 'Focus on barcode input', category: 'Navigation' },
    { key: 'Ctrl+F', description: 'Focus on search input', category: 'Navigation' },
    { key: 'Ctrl+C', description: 'Focus on customer search', category: 'Navigation' },

    // Payment shortcuts
    { key: 'Ctrl+P', description: 'Cash payment', category: 'Payment' },
    { key: 'Ctrl+K', description: 'Card payment', category: 'Payment' },
    { key: 'Ctrl+D', description: 'Apply discount', category: 'Payment' },

    // Quantity shortcuts
    { key: '1', description: 'Set quantity to 1', category: 'Quantity' },
    { key: '5', description: 'Set quantity to 5', category: 'Quantity' },
    { key: '0', description: 'Set quantity to 10', category: 'Quantity' },

    // Function key shortcuts
    { key: 'F1', description: 'Show keyboard shortcuts', category: 'Help' },
    { key: 'F2', description: 'Price check', category: 'Functions' },
    { key: 'F3', description: 'Void selected item', category: 'Functions' },
    { key: 'F4', description: 'Toggle tax exempt', category: 'Functions' },
    { key: 'F5', description: 'Process refund', category: 'Functions' },
    { key: 'F7', description: 'View user manual', category: 'Help' },
    { key: 'F8', description: 'View sales list', category: 'Sales' },
    { key: 'F9', description: 'View receipt (after sale)', category: 'Receipt' },

    // Other shortcuts
    { key: 'Esc', description: 'Close modal / Clear cart', category: 'Navigation' },
    { key: 'Ctrl+/', description: 'Show keyboard shortcuts', category: 'Help' },
  ];

  // State for modals
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [showSalesListModal, setShowSalesListModal] = useState(false);
  const [showPriceCheckModal, setShowPriceCheckModal] = useState(false);
  const [showManualModal, setShowManualModal] = useState(false);
  const [completedSale, setCompletedSale] = useState<any>(null);
  const [priceCheckProduct, setPriceCheckProduct] = useState<Product | null>(null);
  const [manualContent, setManualContent] = useState<string>('');
  const [inventorySettings, setInventorySettings] = useState<{
    allow_negative_inventory: boolean;
    warn_on_low_inventory: boolean;
    auto_create_purchase_requests: boolean;
  }>({
    allow_negative_inventory: true,
    warn_on_low_inventory: true,
    auto_create_purchase_requests: true
  });

  // Fetch inventory settings
  useEffect(() => {
    const fetchInventorySettings = async () => {
      if (!currentOrganization) return;

      try {
        const { settings, error } = await getInventorySettings(currentOrganization.id);
        if (!error) {
          setInventorySettings(settings);
        }
      } catch (err) {
        console.error('Error fetching inventory settings:', err);
      }
    };

    fetchInventorySettings();
  }, [currentOrganization]);

  // Handle payment
  const handlePayment = async () => {
    if (!currentOrganization || !user) {
      setError('User or organization not found');
      return;
    }

    if (cart.length === 0) {
      setError('Cart is empty');
      return;
    }

    // Set processing state
    setProcessingPayment(true);
    setError(null);

    try {
      // Prepare sale items
      const saleItems = await Promise.all(cart.map(async (item) => {
        // Get the default UoM for the product
        let defaultUom = item.product.product_uoms?.find((pu: any) => pu.is_default);
        let uomId = defaultUom?.uom_id || null;

        // If no UoM found in the product data, try to get it from the product UoM service
        if (!uomId) {
          try {
            const { getDefaultProductUom } = await import('../../services/productUom');
            const { productUom } = await getDefaultProductUom(item.product.id, currentOrganization.id);
            uomId = productUom?.uom_id || null;
          } catch (err) {
            console.error('Error fetching default UoM for product:', item.product.id, err);
          }
        }

        // If still no UoM found, try to find a "pieces" UoM as fallback
        if (!uomId) {
          try {
            const { data: piecesUom } = await supabase
              .from('units_of_measurement')
              .select('id')
              .eq('organization_id', currentOrganization.id)
              .eq('code', 'pcs')
              .single();

            if (piecesUom) {
              uomId = piecesUom.id;
              console.log(`Using fallback 'pieces' UoM for product: ${item.product.name}`);
            }
          } catch (err) {
            console.error('Error finding pieces UoM:', err);
          }
        }

        // If still no UoM found, this is an error
        if (!uomId) {
          throw new Error(`No default unit of measure found for product: ${item.product.name}. Please set up units of measurement for this product.`);
        }

        // Calculate tax amount for this item
        // First check if tax_rate is set to 0 in the main settings
        const mainTaxRate = settings?.tax_rate;

        // Get tax settings
        const taxSettings = settings?.tax_settings || {};

        // If tax_rate is explicitly set to 0 in the main settings, override the vat_rate
        const effectiveTaxRate = mainTaxRate === 0 ? 0 : taxSettings.vat_rate;

        const taxRate = taxSettings.tax_enabled && effectiveTaxRate > 0 ?
          effectiveTaxRate : 0;

        // Calculate discount amount for this item (proportional to total discount)
        const itemSubtotal = item.product.unit_price * item.quantity;
        const totalSubtotal = calculateSubtotal();
        const itemDiscountAmount = totalSubtotal > 0 ?
          (itemSubtotal / totalSubtotal) * calculateDiscount() : 0;

        return {
          product_id: item.product.id,
          quantity: item.quantity,
          unit_price: item.product.unit_price,
          uom_id: uomId,
          base_quantity: item.quantity, // For now, base quantity is same as quantity
          tax_rate: taxRate,
          discount_amount: itemDiscountAmount,
          notes: item.notes || ''
        };
      }));

      // Calculate loyalty points earned if customer is eligible
      let loyaltyPointsEarned = 0;
      if (customer?.loyalty_eligible && loyaltySettings?.is_enabled) {
        // Get the earning rate from settings
        const earningRate = loyaltySettings?.points_earning_rate || 0.005; // Default to 0.005 (1 point per $200)



        // Calculate points by multiplying the total by the earning rate
        // This matches the calculation in the loyalty service (calculatePointsForAmount)
        loyaltyPointsEarned = Math.floor(calculateTotal() * earningRate);

        // Ensure we don't give negative points
        if (loyaltyPointsEarned < 0) loyaltyPointsEarned = 0;

        // Add a safety cap to prevent excessive points (just in case)
        const maxPointsPerTransaction = 100;
        if (loyaltyPointsEarned > maxPointsPerTransaction) {
          loyaltyPointsEarned = maxPointsPerTransaction;
        }
      }

      // Calculate cash payment details for cash transactions
      let cashTenderedAmount: number | undefined = undefined;
      let changeAmount: number | undefined = undefined;

      if (paymentMethod === 'cash' && cashTendered) {
        cashTenderedAmount = parseFloat(cashTendered);
        changeAmount = Math.max(0, cashTenderedAmount - calculateTotal());
      }

      // Create the sale
      const { sale, error: saleError } = await createSale(
        currentOrganization.id,
        user.id,
        {
          customer_id: customer?.id,
          items: saleItems,
          subtotal: calculateSubtotal(),
          tax_amount: calculateTax(),
          discount_amount: calculateDiscount(),
          total_amount: calculateTotal(),
          payment_method: paymentMethod,
          cash_tendered: cashTenderedAmount,
          change_amount: changeAmount,
          notes: customer ? `Sale to ${customer.name}` : undefined,
          loyalty_points_used: loyaltyPointsUsed,
          loyalty_points_discount: loyaltyPointsDiscount,
          loyalty_points_earned: loyaltyPointsEarned
        }
      );

      if (saleError) {
        throw new Error(saleError);
      }

      // Store the completed sale and show success modal
      setCompletedSale({...sale, loyalty_points_earned: loyaltyPointsEarned});

      // Close payment modal and show success modal
      setShowPaymentModal(false);
      setShowSuccessModal(true);

      // We've already stored the completed sale with customer info, so we can clear the customer state

      // Clear the cart and reset customer for new transaction
      confirmClearCart();

      // Explicitly set customer to null to ensure it's cleared
      // This is already called in clearCart(), but we call it again to be sure
      setCustomer(null);

      // Reset loyalty points
      setLoyaltyPointsUsed(0);
      setLoyaltyPointsDiscount(0);

      // Reset discount
      setDiscount({ amount: 0, isPercentage: false });

      // Force a re-render of the customer selector
      setProductSelectorKey(prevKey => prevKey + 1);

    } catch (err: any) {
      console.error('Error processing payment:', err);
      setError(err.message || 'An error occurred while processing the payment');

      // Keep the payment modal open
      setTimeout(() => {
        setError(null);
      }, 5000);
    } finally {
      setProcessingPayment(false);
    }
  };

  // Handle keyboard shortcuts for modals
  useEffect(() => {
    const handleModalKeys = (e: KeyboardEvent) => {
      // F9 key to view receipt
      if (e.key === 'F9' && showSuccessModal && completedSale) {
        e.preventDefault(); // Prevent browser default action
        setShowReceiptModal(true);
      }

      // F7 key to view user manual
      if (e.key === 'F7') {
        e.preventDefault(); // Prevent browser default action
        handleShowManual();
      }

      // F8 key to view sales list
      if (e.key === 'F8') {
        e.preventDefault(); // Prevent browser default action
        handleViewSalesList();
      }
    };

    window.addEventListener('keydown', handleModalKeys);
    return () => {
      window.removeEventListener('keydown', handleModalKeys);
    };
  }, [showSuccessModal, showReceiptModal, showSalesListModal, showPriceCheckModal, showManualModal, completedSale]);

  // Function to open sales history
  const handleViewSalesList = () => {
    if (!currentOrganization) return;
    setShowSalesListModal(true);
  };

  // Function to view a specific receipt
  const handleViewReceipt = async (saleId: string) => {
    if (!currentOrganization) return;

    setCompletedSale(null);
    setShowReceiptModal(true);
    setShowSalesListModal(false);

    try {
      const { sale, error } = await getSaleById(currentOrganization.id, saleId);

      if (error) {
        throw new Error(error);
      }

      setCompletedSale(sale);
    } catch (err: any) {
      console.error('Error fetching sale:', err);
      setError(err.message || 'Failed to fetch sale');
      setTimeout(() => setError(null), 3000);
    }
  };

  // Function to print receipt
  const handlePrintReceipt = () => {
    if (!completedSale) return;

    // Create a new window for printing
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (!printWindow) return;

    // Pre-format values for the template
    const formattedDate = formatDateTime(completedSale.sale_date);
    const formattedSubtotal = formatWithCurrency(completedSale.subtotal);
    const formattedTax = formatWithCurrency(completedSale.tax_amount);
    const formattedTotal = formatWithCurrency(completedSale.total_amount);
    const formattedDiscount = completedSale.discount_amount > 0 ? formatWithCurrency(completedSale.discount_amount) : '';
    const formattedLoyaltyDiscount = completedSale.loyalty_points_discount > 0 ? formatWithCurrency(completedSale.loyalty_points_discount) : '';
    const formattedCashTendered = completedSale.cash_tendered ? formatWithCurrency(completedSale.cash_tendered) : '';
    const formattedChange = completedSale.change_amount > 0 ? formatWithCurrency(completedSale.change_amount) : '';
    const cashierName = completedSale.cashier ?
      ((completedSale.cashier.first_name || '') + ' ' + (completedSale.cashier.last_name || '')).trim() || 'Unknown User' : '';

    // Generate the receipt HTML
    const receiptHTML = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt - ${completedSale.invoice_number}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              font-size: 14px;
              line-height: 1.4;
            }
            .receipt {
              max-width: 400px;
              margin: 0 auto;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #000;
              padding-bottom: 10px;
              margin-bottom: 15px;
            }
            .company-name {
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 5px;
            }
            .receipt-info {
              margin-bottom: 15px;
            }
            .receipt-info div {
              margin-bottom: 3px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin: 15px 0;
            }
            th, td {
              padding: 8px 4px;
              text-align: left;
              border-bottom: 1px solid #ddd;
            }
            th {
              font-weight: bold;
              background-color: #f5f5f5;
            }
            .text-right {
              text-align: right;
            }
            .text-center {
              text-align: center;
            }
            .summary {
              margin-top: 15px;
            }
            .summary-row {
              display: flex;
              justify-content: space-between;
              margin: 5px 0;
            }
            .total-row {
              border-top: 2px solid #000;
              padding-top: 8px;
              margin-top: 10px;
              font-weight: bold;
              font-size: 16px;
            }
            .footer {
              text-align: center;
              margin-top: 20px;
              font-size: 12px;
              color: #666;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="receipt">
            <!-- Header -->
            <div class="header">
              <div class="company-name">${currentOrganization?.name || 'Your Business'}</div>
              ${currentOrganization?.address ? `<div>${currentOrganization.address}</div>` : ''}
              ${currentOrganization?.phone || currentOrganization?.email ? `
                <div style="margin-top: 5px;">
                  ${currentOrganization?.phone ? `Phone: ${currentOrganization.phone}` : ''}
                  ${currentOrganization?.phone && currentOrganization?.email ? ' | ' : ''}
                  ${currentOrganization?.email ? `Email: ${currentOrganization.email}` : ''}
                </div>
              ` : ''}
              ${currentOrganization?.website ? `<div>${currentOrganization.website}</div>` : ''}
            </div>

            <!-- Receipt Info -->
            <div class="receipt-info">
              <div><strong>Receipt: ${completedSale.invoice_number}</strong></div>
              <div>Date: ${formattedDate}</div>
              ${completedSale.customer ? `<div>Customer: ${completedSale.customer.name}</div>` : ''}
              ${completedSale.cashier ? `<div>Cashier: ${cashierName}</div>` : ''}
            </div>

            <!-- Items Table -->
            <table>
              <thead>
                <tr>
                  <th>Item</th>
                  <th class="text-right">Qty</th>
                  <th class="text-right">Price</th>
                  <th class="text-right">Total</th>
                </tr>
              </thead>
              <tbody>
                ${completedSale.items?.map((item: any) => `
                  <tr>
                    <td>${item.product?.name || 'Product'}</td>
                    <td class="text-right">${item.quantity} pcs</td>
                    <td class="text-right">${formatWithCurrency(item.unit_price)}</td>
                    <td class="text-right">${formatWithCurrency(item.unit_price * item.quantity)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>

            <!-- Summary -->
            <div class="summary">
              <div class="summary-row">
                <span>Subtotal</span>
                <span>${formattedSubtotal}</span>
              </div>
              ${completedSale.discount_amount > 0 ? `
                <div class="summary-row">
                  <span>Discount</span>
                  <span>-${formattedDiscount}</span>
                </div>
              ` : ''}
              ${completedSale.loyalty_points_discount && completedSale.loyalty_points_discount > 0 ? `
                <div class="summary-row">
                  <span>Loyalty Points (${completedSale.loyalty_points_used} pts)</span>
                  <span>-${formattedLoyaltyDiscount}</span>
                </div>
              ` : ''}
              <div class="summary-row">
                <span>Tax</span>
                <span>${formattedTax}</span>
              </div>
              <div class="summary-row total-row">
                <span>Total</span>
                <span>${formattedTotal}</span>
              </div>
            </div>

            ${completedSale.payment_method === 'cash' && completedSale.cash_tendered ? `
              <div class="summary" style="border-top: 1px solid #ccc; padding-top: 10px; margin-top: 15px;">
                <div class="summary-row">
                  <span>Cash Tendered:</span>
                  <span>${formattedCashTendered}</span>
                </div>
                ${completedSale.change_amount && completedSale.change_amount > 0 ? `
                  <div class="summary-row">
                    <span>Change:</span>
                    <span>${formattedChange}</span>
                  </div>
                ` : ''}
              </div>
            ` : ''}

            <!-- Footer -->
            <div class="footer">
              <p>Thank you for your business!</p>
              <p>Payment Method: ${completedSale.payment_method}</p>
              ${completedSale.notes ? `<p style="font-style: italic;">${completedSale.notes}</p>` : ''}
              <p style="margin-top: 15px;">Generated on ${new Date().toLocaleDateString()}</p>
            </div>
          </div>

          <script>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              };
            };
          </script>
        </body>
      </html>
    `;

    // Write the HTML to the new window
    printWindow.document.write(receiptHTML);
    printWindow.document.close();
  };

  // Function to load and show the manual
  const handleShowManual = async () => {
    try {
      // Fetch the manual content
      const response = await fetch('/docs/POS_Terminal_User_Manual.md');
      if (!response.ok) {
        throw new Error('Failed to load manual');
      }

      const content = await response.text();
      setManualContent(content);
      setShowManualModal(true);
    } catch (err: any) {
      toast.error(err.message || 'Failed to load manual');
      // setError(err.message || 'Failed to load manual');
      // setTimeout(() => setError(null), 3000);
      // If we can't load the manual, set a fallback content
      setManualContent(`# POS Terminal User Manual\n\nThe manual could not be loaded. Please try again later or contact support.`);
      setShowManualModal(true);
    }
  };

  return (
    <div className="w-full h-full bg-gray-50 flex flex-col">
      {/* Success Modal */}
      <Modal
        show={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        size="xl"
      >
        <Modal.Header>
          Sale Completed Successfully
        </Modal.Header>
        <Modal.Body>
          <div className="space-y-4 text-center">
            <div className="flex justify-center">
              <div className="rounded-full bg-green-100 p-3">
                <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
            </div>

            <h3 className="text-lg font-medium text-gray-900">Sale Completed</h3>

            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-500">Invoice Number</p>
              <p className="text-lg font-bold">{completedSale?.invoice_number}</p>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-500">Total Amount</p>
              <p className="text-lg font-bold text-green-600">{formatWithCurrency(completedSale?.total_amount || 0)}</p>
            </div>

            {completedSale?.loyalty_points_earned > 0 && (
              <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <HiOutlineGift className="mr-2 h-5 w-5 text-purple-600" />
                    <p className="text-sm text-purple-700">Loyalty Points Earned</p>
                  </div>
                  <p className="text-lg font-bold text-purple-600">+{completedSale.loyalty_points_earned} points</p>
                </div>
              </div>
            )}

            {completedSale?.loyalty_points_used > 0 && (
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-blue-700">Loyalty Points Used</p>
                  <p className="text-lg font-bold text-blue-600">{completedSale.loyalty_points_used} points</p>
                </div>
              </div>
            )}

            <p className="text-sm text-gray-500">
              Press <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">ESC</kbd> to close this window
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <div className="flex justify-between w-full">
            <Button color="light" onClick={() => setShowSuccessModal(false)}>
              Close
            </Button>
            <Button color="info" onClick={() => setShowReceiptModal(true)}>
              <HiOutlineDocumentDownload className="mr-2 h-5 w-5" />
              View Receipt (F9)
            </Button>
          </div>
        </Modal.Footer>
      </Modal>

      {/* Payment Modal */}
      <Modal
        show={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        size="md"
      >
        <Modal.Header>
          {paymentMethod === 'cash' ? 'Cash Payment' : 'Card Payment'}
        </Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            {error && (
              <Alert color="failure" className="mb-4">
                {error}
              </Alert>
            )}

            <div className="text-center mb-4">
              <div className="text-2xl font-bold text-blue-600">
                {formatWithCurrency(calculateTotal())}
              </div>
              <p className="text-gray-500">Total Amount</p>
            </div>

            {paymentMethod === 'cash' ? (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="cash-tendered" value="Cash Tendered" />
                  <EnhancedNumberInput
                    value={cashTendered || calculateTotal().toFixed(2)}
                    onChange={(e) => setCashTendered(e.target.value)}
                    onBlur={(e) => {
                      // If field is empty on blur, reset to total amount
                      if (e.target.value === '' || parseFloat(e.target.value) < calculateTotal()) {
                        setCashTendered(calculateTotal().toFixed(2));
                      }
                    }}
                    min={calculateTotal()}
                    step="0.01"
                    className="mt-1"
                    autoSelect={true}
                    preventScrollChange={true}
                  />
                </div>
                <div>
                  <Label value="Change" />
                  <div className="p-2 bg-gray-50 rounded border mt-1 text-lg font-medium">
                    {formatWithCurrency(
                      Math.max(0, parseFloat(cashTendered || '0') - calculateTotal())
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="card-number" value="Card Number" />
                  <TextInput
                    id="card-number"
                    type="text"
                    placeholder="•••• •••• •••• ••••"
                    className="mt-1"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="expiry" value="Expiry Date" />
                    <TextInput
                      id="expiry"
                      type="text"
                      placeholder="MM/YY"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="cvv" value="CVV" />
                    <TextInput
                      id="cvv"
                      type="text"
                      placeholder="123"
                      className="mt-1"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <div className="flex justify-between w-full">
            <Button color="light" onClick={() => setShowPaymentModal(false)}>
              Cancel
            </Button>
            <Button
              color={paymentMethod === 'cash' ? 'success' : 'info'}
              onClick={handlePayment}
              disabled={processingPayment}
            >
              {processingPayment ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  {paymentMethod === 'cash' ? (
                    <HiOutlineCash className="mr-2 h-5 w-5" />
                  ) : (
                    <HiOutlineCreditCard className="mr-2 h-5 w-5" />
                  )}
                  Process Payment
                </>
              )}
            </Button>
          </div>
        </Modal.Footer>
      </Modal>

      {/* Receipt Modal */}
      <Modal
        show={showReceiptModal}
        onClose={() => setShowReceiptModal(false)}
        size="md"
      >
        <Modal.Header>
          Receipt
        </Modal.Header>
        <Modal.Body>
          {completedSale ? (
            <SaleReceipt
              sale={completedSale}
              showPrintButton={false}
            />
          ) : (
            <div className="text-center py-4">
              <Spinner size="xl" />
              <p className="mt-2">Loading receipt...</p>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <div className="flex justify-between w-full">
            <Button color="light" onClick={() => setShowReceiptModal(false)}>
              Close
            </Button>
            <Button color="success" onClick={handlePrintReceipt}>
              <HiOutlineDocumentDownload className="mr-2 h-5 w-5" />
              Print Receipt
            </Button>
          </div>
        </Modal.Footer>
      </Modal>

      {/* Price Check Modal */}
      <Modal
        show={showPriceCheckModal}
        onClose={() => {
          setShowPriceCheckModal(false);
          setPriceCheckProduct(null);
        }}
        size="md"
      >
        <Modal.Header>
          Price Check
        </Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <div className="mb-4">
              <Label htmlFor="price-check-product" value="Search Product" className="mb-2 block" />
              <EnhancedProductSearchSelector
                value={priceCheckProduct?.id || ""}
                onChange={(_productId, product) => {
                  if (product) {
                    setPriceCheckProduct(product);
                  }
                }}
                placeholder="Search by name, SKU, or barcode"
                pageSize={10}
                instanceId="price-check-product-search"
              />
            </div>

            {priceCheckProduct && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-start">
                  <div className="flex-grow">
                    <h3 className="text-lg font-medium">{priceCheckProduct.name}</h3>
                    <div className="text-sm text-gray-500 mt-1">
                      {priceCheckProduct.sku && <div>SKU: {priceCheckProduct.sku}</div>}
                      {priceCheckProduct.barcode && <div>Barcode: {priceCheckProduct.barcode}</div>}
                      <div>Stock: {formatQuantityWithSeparators(priceCheckProduct.stock_quantity || 0)} units</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatWithCurrency(priceCheckProduct.unit_price)}
                    </div>
                    {/* Sale price display would go here if implemented */}
                  </div>
                </div>

                <div className="mt-4 flex justify-end">
                  <Button
                    size="sm"
                    onClick={() => {
                      addToCart(priceCheckProduct);
                      setShowPriceCheckModal(false);
                      setPriceCheckProduct(null);
                    }}
                  >
                    <HiOutlinePlus className="mr-2 h-4 w-4" />
                    Add to Cart
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => {
            setShowPriceCheckModal(false);
            setPriceCheckProduct(null);
          }}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Sales History Component */}
      <SalesHistory
        show={showSalesListModal}
        onClose={() => setShowSalesListModal(false)}
        onViewReceipt={handleViewReceipt}
      />

      {/* User Manual Modal */}
      <Modal
        show={showManualModal}
        onClose={() => setShowManualModal(false)}
        size="7xl"
      >
        <Modal.Header>
          POS Terminal User Manual
        </Modal.Header>
        <Modal.Body className="max-h-[70vh] overflow-y-auto">
          <div className="prose prose-sm max-w-none">
            <ReactMarkdown>
              {manualContent}
            </ReactMarkdown>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowManualModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Keyboard Shortcuts Modal */}
      <Modal
        show={showShortcutsModal}
        onClose={() => setShowShortcutsModal(false)}
        size="md"
      >
        <Modal.Header>
          Keyboard Shortcuts
        </Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p className="text-sm text-gray-500 mb-4">
              Use these keyboard shortcuts to speed up your workflow in the POS terminal.
            </p>

            {/* Group shortcuts by category */}
            {['Navigation', 'Payment', 'Quantity', 'Functions', 'Help', 'Other'].map(category => {
              const categoryShortcuts = keyboardShortcuts.filter(s => s.category === category);
              if (categoryShortcuts.length === 0) return null;

              return (
                <div key={category} className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">{category}</h3>
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="py-2 px-4 text-left font-medium text-gray-700">Shortcut</th>
                        <th className="py-2 px-4 text-left font-medium text-gray-700">Action</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {categoryShortcuts.map((shortcut, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="py-2 px-4">
                            <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">
                              {shortcut.key}
                            </kbd>
                          </td>
                          <td className="py-2 px-4">{shortcut.description}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              );
            })}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowShortcutsModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Create Customer Modal */}
      <CreateCustomerModal
        show={showCreateCustomerModal}
        onClose={() => setShowCreateCustomerModal(false)}
        onCustomerCreated={handleCustomerCreated}
      />

      {/* Remove Item Confirmation Modal */}
      <RemoveItemConfirmation
        show={showRemoveItemModal}
        onClose={() => setShowRemoveItemModal(false)}
        onConfirm={confirmRemoveFromCart}
        itemName={itemToRemove?.name || ''}
      />

      {/* Clear Cart Confirmation Modal */}
      <ClearCartConfirmation
        show={showClearCartModal}
        onClose={() => setShowClearCartModal(false)}
        onConfirm={confirmClearCart}
        itemCount={cart.length}
      />

      {/* Sales History Modal */}
      <SalesHistory
        show={showSalesListModal}
        onClose={() => setShowSalesListModal(false)}
        onViewReceipt={handleViewReceipt}
      />

      {/* Refund Modal */}
      <Modal show={showRefundModal} onClose={() => setShowRefundModal(false)} size="7xl">
        <Modal.Header>Process Refund</Modal.Header>
        <Modal.Body>
          <RefundProcessor
            onRefundCreated={(refund) => {
              setShowRefundModal(false);
              // Show success message
              toast.success(`Refund ${refund.refund_number} created successfully`);
            }}
            onClose={() => setShowRefundModal(false)}
          />
        </Modal.Body>
      </Modal>

      {/* Time Clock Modal */}
      <Modal show={showTimeClockModal} onClose={() => setShowTimeClockModal(false)} size="5xl">
        <Modal.Body className="p-0">
          <TimeClock
            onTimeEntryCreated={(_employeeId, action) => {
              // Close modal after successful time entry
              setShowTimeClockModal(false);
              // Show success message
              toast.success(`Successfully ${action === 'clock_in' ? 'clocked in' : 'clocked out'}`);
            }}
            onCancel={() => {
              // Close modal when user cancels
              setShowTimeClockModal(false);
            }}
          />
        </Modal.Body>
      </Modal>

      <div className="grid grid-cols-1 lg:grid-cols-3 flex-1 min-h-0">
        {/* Left Column - Scanning and Search */}
        <div className="lg:col-span-1 p-4 h-full">
          <div className="bg-white rounded-lg shadow h-full flex flex-col">
            {/* Header */}
            <div className="p-4 border-b border-gray-200">

              {/* Customer Selection */}
              <div className="mb-4">
                <Label htmlFor="customer" value="Customer" className="mb-2 block" />
                <CustomerSelector
                  onSelectCustomer={setCustomer}
                  selectedCustomerId={customer?.id}
                  onCreateCustomer={() => setShowCreateCustomerModal(true)}
                />
              </div>

              {/* Barcode Scanner - Prominent */}
              <form onSubmit={handleBarcodeScan} className="mb-4">
                <div>
                  <Label htmlFor="barcode" value="Scan Barcode" className="mb-2 block font-bold" />
                  <div className="flex gap-2">
                    <TextInput
                      ref={barcodeInputRef}
                      id="barcode"
                      type="text"
                      placeholder="Focus here and scan barcode (auto-detects)"
                      value={barcodeInput}
                      onChange={handleBarcodeInputChange}
                      className="flex-1"
                      icon={HiOutlineQrcode}
                      sizing="lg"
                    />
                    <div className="w-20">
                      <input
                        type="number"
                        min="1"
                        value={quantityToAdd}
                        onFocus={(e) => {
                          // Select all text when focused for easy editing
                          e.target.select();
                        }}
                        onChange={handleQuantityChange}
                        onWheel={(e) => {
                          // Prevent scroll from changing the value
                          e.currentTarget.blur();
                        }}
                        className="w-full h-11 text-center text-sm font-medium bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-150 hover:border-gray-400"
                        placeholder="Qty"
                      />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Automatically adds to cart when barcode is scanned (or press Enter)</p>
                </div>
              </form>

              {/* Error Message */}
              {error && (
                <Alert color="failure" className="mb-4">
                  {error}
                </Alert>
              )}

              {/* Product Search */}
              <div className="mb-4 product-search-container">
                <Label htmlFor="search" value="Search Products" className="mb-2 block" />
                <div className="flex gap-2">
                  <div className="flex-grow">
                    <EnhancedProductSearchSelector
                      value=""
                      onChange={(_productId, product) => {
                        if (product) {
                          addToCart(product, quantityToAdd);
                          // Reset the selector by forcing a re-render with a new key
                          setProductSelectorKey(prevKey => prevKey + 1);
                        }
                      }}
                      placeholder="Search by name, SKU, or barcode"
                      pageSize={10}
                      instanceId={`pos-product-search-${productSelectorKey}`}
                      key={productSelectorKey}
                    />
                  </div>
                  <div className="w-20">
                    <input
                      type="number"
                      min="1"
                      value={quantityToAdd}
                      onFocus={(e) => {
                        // Select all text when focused for easy editing
                        e.target.select();
                      }}
                      onChange={handleQuantityChange}
                      onWheel={(e) => {
                        // Prevent scroll from changing the value
                        e.currentTarget.blur();
                      }}
                      className="w-full h-10 text-center text-sm font-medium bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-150 hover:border-gray-400"
                      placeholder="Qty"
                    />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-1">Type to search products by name, SKU, or barcode</p>
              </div>
            </div>

            {/* Quick Actions with Keyboard Shortcuts */}
            <div className="p-4 border-t border-gray-200 mt-auto">
              <h3 className="font-medium text-sm mb-2">Quick Quantity</h3>
              <div className="grid grid-cols-3 gap-2">
                <Button color="light" size="sm" onClick={() => setQuantityToAdd(1)}>
                  <div className="flex flex-col items-center">
                    <span>Qty: 1</span>
                    <span className="text-xs text-gray-500">1</span>
                  </div>
                </Button>
                <Button color="light" size="sm" onClick={() => setQuantityToAdd(5)}>
                  <div className="flex flex-col items-center">
                    <span>Qty: 5</span>
                    <span className="text-xs text-gray-500">5</span>
                  </div>
                </Button>
                <Button color="light" size="sm" onClick={() => setQuantityToAdd(10)}>
                  <div className="flex flex-col items-center">
                    <span>Qty: 10</span>
                    <span className="text-xs text-gray-500">0</span>
                  </div>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Cart (Larger) */}
        <div className="lg:col-span-2 p-4 h-full">
          <div className="bg-white rounded-lg shadow h-full flex flex-col">
            {/* Cart Header */}
            <div className="flex justify-between items-center p-4 border-b border-gray-200 flex-shrink-0">
              <h2 className="text-xl font-bold">Current Sale</h2>
              {cart.length > 0 && (
                <div className="flex gap-2">
                  <Button color="failure" size="sm" onClick={clearCart}>
                    <HiOutlineTrash className="mr-1 h-4 w-4" />
                    Clear
                  </Button>
                </div>
              )}
            </div>

            {/* Cart Items - Full Height */}
            <div className="flex-1 overflow-y-auto p-4 min-h-0">
              {cart.length === 0 ? (
                <div className="text-center py-12 h-full flex flex-col justify-center items-center">
                  <HiOutlineShoppingCart className="mx-auto h-16 w-16 text-gray-300" />
                  <p className="mt-4 text-gray-500 text-lg">Your cart is empty</p>
                  <p className="text-gray-400">Scan a barcode or search for products to add</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50 text-gray-700">
                      <tr>
                        <th className="py-3 px-4 text-left">Item</th>
                        <th className="py-3 px-4 text-center">Qty</th>
                        <th className="py-3 px-4 text-right">Price</th>
                        <th className="py-3 px-4 text-right">Total</th>
                        <th className="py-3 px-4 text-center">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {cart.map((item) => (
                        <tr key={item.id} className="hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <div className="font-medium">{item.product.name}</div>
                            <div className="text-xs text-gray-500">
                              {item.product.sku && <span className="mr-2">SKU: {item.product.sku}</span>}
                              {item.notes && (
                                <span className="italic text-gray-500">Note: {item.notes}</span>
                              )}
                            </div>
                          </td>
                          <td className="py-3 px-4 text-center">
                            <div className="flex items-center justify-center gap-1">
                              <button
                                type="button"
                                className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                                onClick={() => updateCartItemQuantity(item.id, item.quantity - 1)}
                                disabled={item.quantity <= 1}
                              >
                                <HiOutlineMinus className="h-4 w-4" />
                              </button>
                              <div className="relative">
                                <input
                                  type="number"
                                  min="1"
                                  value={item.quantity}
                                  onFocus={(e) => {
                                    // Select all text when focused for easy editing
                                    e.target.select();
                                  }}
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    // Allow empty value while typing
                                    if (value === '') {
                                      return;
                                    }
                                    const newQty = parseInt(value);
                                    if (!isNaN(newQty) && newQty > 0) {
                                      updateCartItemQuantity(item.id, newQty);
                                    }
                                  }}
                                  onBlur={(e) => {
                                    // If field is empty on blur, reset to 1
                                    if (e.target.value === '' || parseInt(e.target.value) <= 0) {
                                      updateCartItemQuantity(item.id, 1);
                                    }
                                  }}
                                  onWheel={(e) => {
                                    // Prevent scroll from changing the value
                                    e.currentTarget.blur();
                                  }}
                                  className="w-16 h-8 text-center text-sm font-medium bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-150 hover:border-gray-400"
                                />
                              </div>
                              <button
                                type="button"
                                className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                                onClick={() => updateCartItemQuantity(item.id, item.quantity + 1)}
                              >
                                <HiOutlinePlus className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-right">
                            {formatWithCurrency(item.product.unit_price)}
                          </td>
                          <td className="py-3 px-4 text-right font-medium">
                            {formatWithCurrency(item.product.unit_price * item.quantity)}
                          </td>
                          <td className="py-3 px-4 text-center">
                            <div className="flex justify-center gap-1">
                              <CartItemNotes
                                itemId={item.id}
                                initialNotes={item.notes}
                                onSave={addNotesToCartItem}
                              />
                              <Button
                                size="xs"
                                color="failure"
                                className="p-1"
                                onClick={() => removeFromCart(item.id)}
                                title="Remove item (requires confirmation code)"
                              >
                                <HiOutlineTrash className="h-3 w-3" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Cart Summary - Scrollable Content */}
            <div className="border-t border-gray-200 bg-gray-50 flex-shrink-0">
              {/* Scrollable Summary Content */}
              <div className="p-4 max-h-64 overflow-y-auto">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    {customer && (
                      <div className="mb-3 p-3 bg-blue-50 rounded-lg border border-blue-100">
                        <div className="flex items-center gap-2">
                          <HiOutlineUser className="h-4 w-4 text-blue-500" />
                          <span className="font-medium text-sm">{customer.name}</span>
                          {customer.loyalty_eligible === true && (
                            <Badge color="purple" size="xs">Loyalty</Badge>
                          )}
                        </div>
                        {customer.email && (
                          <div className="text-xs text-gray-600 mt-1">{customer.email}</div>
                        )}
                        {customer.phone && (
                          <div className="text-xs text-gray-600">{customer.phone}</div>
                        )}
                        <CustomerLoyaltyInfo customer={customer} />
                      </div>
                    )}

                    {/* Loyalty Points Redemption */}
                    <LoyaltyPointsRedemption
                      customer={customer}
                      onApplyPoints={handleApplyLoyaltyPoints}
                      disabled={cart.length === 0}
                      totalAmount={calculateTotal()}
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Subtotal</span>
                      <span>{formatWithCurrency(calculateSubtotal())}</span>
                    </div>

                    {/* Discount */}
                    <div className="flex justify-between items-center text-sm">
                      <div className="flex items-center">
                        <span className="text-gray-600 mr-1">Discount</span>
                        <DiscountSelector
                          subtotal={calculateSubtotal()}
                          onApplyDiscount={handleApplyDiscount}
                          currentDiscount={discount}
                        />
                      </div>
                      <span className="text-red-500">-{formatWithCurrency(calculateDiscount())}</span>
                    </div>

                    {/* Loyalty Points Discount */}
                    {loyaltyPointsDiscount > 0 && (
                      <div className="flex justify-between items-center text-sm">
                        <div className="flex items-center">
                          <span className="text-gray-600 mr-1">Loyalty Points</span>
                          <Badge color="purple" className="text-xs">
                            {loyaltyPointsUsed} points
                          </Badge>
                        </div>
                        <span className="text-purple-500">-{formatWithCurrency(loyaltyPointsDiscount)}</span>
                      </div>
                    )}

                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">
                        {(() => {
                          // First check if tax_rate is set to 0 in the main settings
                          const taxRate = settings?.tax_rate;

                          // Get tax settings
                          const taxSettings = settings?.tax_settings || {};

                          // If tax_rate is explicitly set to 0 in the main settings, override the vat_rate
                          const effectiveTaxRate = taxRate === 0 ? 0 : taxSettings.vat_rate;

                          if (taxSettings.tax_enabled && effectiveTaxRate > 0) {
                            return `VAT ${effectiveTaxRate}%${taxSettings.vat_inclusive ? ' (incl.)' : ''}`;
                          } else {
                            return 'No Tax';
                          }
                        })()}
                      </span>
                      <span>{formatWithCurrency(calculateTax())}</span>
                    </div>

                    <div className="flex justify-between font-bold text-lg pt-2 border-t border-gray-200">
                      <span>Total</span>
                      <span className="text-blue-600">{formatWithCurrency(calculateTotal())}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Fixed Payment Buttons */}
              <div className="border-t border-gray-300 p-4 bg-white">
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    color="success"
                    size="lg"
                    disabled={cart.length === 0}
                    className="py-3"
                    onClick={() => {
                      setPaymentMethod('cash');
                      setCashTendered(calculateTotal().toFixed(2));
                      setShowPaymentModal(true);
                    }}
                  >
                    <div className="flex items-center">
                      <HiOutlineCash className="mr-2 h-5 w-5" />
                      <span>Cash</span>
                      <span className="ml-2 text-xs bg-green-700 px-1.5 py-0.5 rounded">Ctrl+P</span>
                    </div>
                  </Button>
                  <Button
                    color="info"
                    size="lg"
                    disabled={cart.length === 0}
                    className="py-3"
                    onClick={() => {
                      setPaymentMethod('card');
                      setShowPaymentModal(true);
                    }}
                  >
                    <div className="flex items-center">
                      <HiOutlineCreditCard className="mr-2 h-5 w-5" />
                      <span>Card</span>
                      <span className="ml-2 text-xs bg-blue-700 px-1.5 py-0.5 rounded">Ctrl+K</span>
                    </div>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RetailPOS;
