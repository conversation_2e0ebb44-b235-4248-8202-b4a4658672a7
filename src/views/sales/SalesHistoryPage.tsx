import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Spinner,
  Table,
  Badge,
  TextInput,
  Label,
  Select,
  Card,
  Alert,
  Modal
} from 'flowbite-react';
import {
  HiOutlineCalendar,
  HiOutlineSearch,
  HiOutlineEye,
  HiOutlineDocumentReport,
  HiOutlineRefresh,
  HiOutlineFilter,
  HiOutlineDownload,
  HiOutlinePrinter,
  HiOutlineCash
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getSales, getSaleById } from '../../services/sale';
import { RefundService } from '../../services/refund';
import { formatDate, formatDateTime } from '../../utils/formatters';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import SaleReceipt from '../../components/sales/SaleReceipt';
import PageHeader from '../../components/common/PageHeader';
import Pagination from '../../components/common/Pagination';
import { exportSalesHistory } from '../../utils/excelExport';

const SalesHistoryPage: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  // Helper function to get refund status badge
  const getRefundStatusBadge = (refundData: any) => {
    if (!refundData || refundData.refundStatus === 'none') {
      return null;
    }

    switch (refundData.refundStatus) {
      case 'fully_refunded':
        return (
          <Badge color="failure" size="sm">
            Fully Refunded
          </Badge>
        );
      case 'partially_refunded':
        return (
          <Badge color="warning" size="sm">
            Partially Refunded ({refundData.refundPercentage.toFixed(1)}%)
          </Badge>
        );
      case 'pending_refund':
        return (
          <Badge color="gray" size="sm">
            Refund Pending
          </Badge>
        );
      default:
        return null;
    }
  };

  // Helper function to get refund summary text
  const getRefundSummary = (refundData: any) => {
    if (!refundData || refundData.refundStatus === 'none') {
      return null;
    }

    return (
      <div className="text-xs text-gray-600 mt-1">
        {refundData.refundCount} refund{refundData.refundCount !== 1 ? 's' : ''} •
        {formatWithCurrency(refundData.totalRefunded)} refunded
      </div>
    );
  };

  const [sales, setSales] = useState<any[]>([]);
  const [salesWithRefunds, setSalesWithRefunds] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [dateFilter, setDateFilter] = useState<'today' | 'yesterday' | 'week' | 'month' | 'custom'>('today');
  const [startDate, setStartDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [showReceiptModal, setShowReceiptModal] = useState<boolean>(false);
  const [selectedSale, setSelectedSale] = useState<any>(null);
  const [loadingSale, setLoadingSale] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  // Fetch sales data
  const fetchSales = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      // Calculate date range based on filter
      let calculatedStartDate = new Date();
      const calculatedEndDate = new Date();

      switch (dateFilter) {
        case 'today':
          calculatedStartDate = new Date();
          calculatedStartDate.setHours(0, 0, 0, 0);
          break;

        case 'yesterday':
          calculatedStartDate = new Date();
          calculatedStartDate.setDate(calculatedStartDate.getDate() - 1);
          calculatedStartDate.setHours(0, 0, 0, 0);
          calculatedEndDate.setDate(calculatedEndDate.getDate() - 1);
          calculatedEndDate.setHours(23, 59, 59, 999);
          break;

        case 'week':
          calculatedStartDate = new Date();
          calculatedStartDate.setDate(calculatedStartDate.getDate() - 7);
          calculatedStartDate.setHours(0, 0, 0, 0);
          break;

        case 'month':
          calculatedStartDate = new Date();
          calculatedStartDate.setMonth(calculatedStartDate.getMonth() - 1);
          calculatedStartDate.setHours(0, 0, 0, 0);
          break;

        case 'custom':
          calculatedStartDate = new Date(startDate);
          calculatedStartDate.setHours(0, 0, 0, 0);
          const customEndDate = new Date(endDate);
          customEndDate.setHours(23, 59, 59, 999);
          break;
      }

      // Get sales for the selected date range
      const { sales: salesData, error: salesError } = await getSales(currentOrganization.id, {
        startDate: calculatedStartDate.toISOString(),
        endDate: dateFilter === 'custom' ? new Date(endDate + 'T23:59:59').toISOString() : calculatedEndDate.toISOString(),
        limit: 100,
        sortBy: 'created_at',
        sortOrder: 'desc'
      });

      if (salesError) {
        throw new Error(salesError);
      }

      setSales(salesData);

      // Fetch refund information for each sale
      await fetchRefundData(salesData);

    } catch (err: any) {
      console.error('Error fetching sales:', err);
      setError(err.message || 'Failed to fetch sales');
    } finally {
      setLoading(false);
    }
  };

  // Fetch refund data for sales
  const fetchRefundData = async (salesData: any[]) => {
    if (!currentOrganization || !salesData.length) {
      setSalesWithRefunds(salesData);
      return;
    }

    try {
      // Fetch all refunds for the organization
      const refundResponse = await RefundService.getRefunds(currentOrganization.id, {
        limit: 1000 // Get all refunds to match with sales
      });

      if (refundResponse.success && refundResponse.data) {
        const refunds = refundResponse.data;

        // Create a map of sale ID to refund information
        const refundMap = new Map();

        refunds.forEach(refund => {
          const saleId = refund.original_sale_id;
          if (!refundMap.has(saleId)) {
            refundMap.set(saleId, {
              refunds: [],
              totalRefunded: 0,
              refundCount: 0,
              hasProcessedRefunds: false
            });
          }

          const saleRefundData = refundMap.get(saleId);
          saleRefundData.refunds.push(refund);
          saleRefundData.refundCount++;

          if (refund.status === 'processed') {
            saleRefundData.totalRefunded += Number(refund.total_amount);
            saleRefundData.hasProcessedRefunds = true;
          }
        });

        // Enhance sales data with refund information
        const enhancedSales = salesData.map(sale => {
          const refundData = refundMap.get(sale.id);

          if (refundData) {
            const refundPercentage = sale.total_amount > 0
              ? (refundData.totalRefunded / sale.total_amount) * 100
              : 0;

            let refundStatus = 'none';
            if (refundPercentage >= 100) {
              refundStatus = 'fully_refunded';
            } else if (refundPercentage > 0) {
              refundStatus = 'partially_refunded';
            } else if (refundData.refundCount > 0) {
              refundStatus = 'pending_refund';
            }

            return {
              ...sale,
              refundData: {
                ...refundData,
                refundPercentage,
                refundStatus
              }
            };
          }

          return {
            ...sale,
            refundData: {
              refunds: [],
              totalRefunded: 0,
              refundCount: 0,
              hasProcessedRefunds: false,
              refundPercentage: 0,
              refundStatus: 'none'
            }
          };
        });

        setSalesWithRefunds(enhancedSales);
      } else {
        setSalesWithRefunds(salesData);
      }
    } catch (error) {
      console.error('Error fetching refund data:', error);
      setSalesWithRefunds(salesData);
    }
  };

  // Fetch sales when component mounts or filter changes
  useEffect(() => {
    fetchSales();
  }, [dateFilter, startDate, endDate, currentOrganization]);

  // Filter sales by search term
  const filteredSales = salesWithRefunds.filter(sale => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      (sale.invoice_number && sale.invoice_number.toLowerCase().includes(searchLower)) ||
      (sale.customer && sale.customer.name && sale.customer.name.toLowerCase().includes(searchLower))
    );
  });

  // Calculate totals
  const totalSales = filteredSales.length;
  const totalAmount = filteredSales.reduce((sum, sale) => sum + sale.total_amount, 0);

  // Pagination logic
  const totalPages = Math.ceil(filteredSales.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentSales = filteredSales.slice(indexOfFirstItem, indexOfLastItem);

  // View receipt
  const handleViewReceipt = async (saleId: string) => {
    if (!currentOrganization) return;

    setLoadingSale(true);
    setSelectedSale(null);
    setShowReceiptModal(true);

    try {
      const { sale, error } = await getSaleById(currentOrganization.id, saleId);

      if (error) {
        throw new Error(error);
      }

      setSelectedSale(sale);
    } catch (err: any) {
      console.error('Error fetching sale:', err);
      setError(err.message || 'Failed to fetch sale');
    } finally {
      setLoadingSale(false);
    }
  };

  // Print receipt directly from table
  const handlePrintReceipt = async (saleId: string) => {
    if (!currentOrganization) return;

    try {
      const { sale, error } = await getSaleById(currentOrganization.id, saleId);

      if (error) {
        throw new Error(error);
      }

      printReceiptWindow(sale);
    } catch (err: any) {
      console.error('Error fetching sale for print:', err);
      setError(err.message || 'Failed to fetch sale for printing');
    }
  };

  // Print receipt from modal
  const handlePrintReceiptModal = () => {
    if (!selectedSale) return;
    printReceiptWindow(selectedSale);
  };

  // Export sales to Excel
  const handleExportSales = () => {
    if (filteredSales.length === 0) {
      setError('No sales data to export');
      return;
    }

    const dateRangeText = (() => {
      switch (dateFilter) {
        case 'today': return 'Today';
        case 'yesterday': return 'Yesterday';
        case 'week': return 'Last 7 Days';
        case 'month': return 'Last 30 Days';
        case 'custom': return `${startDate} to ${endDate}`;
        default: return 'All Time';
      }
    })();

    exportSalesHistory(filteredSales, dateRangeText);
  };

  // Create print window with receipt
  const printReceiptWindow = (sale: any) => {
    if (!sale || !currentOrganization) return;

    // Create a new window for printing
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (!printWindow) return;

    // Pre-format values for the template
    const formattedDate = formatDateTime(sale.sale_date);
    const formattedSubtotal = formatWithCurrency(sale.subtotal);
    const formattedTax = formatWithCurrency(sale.tax_amount);
    const formattedTotal = formatWithCurrency(sale.total_amount);
    const formattedDiscount = sale.discount_amount > 0 ? formatWithCurrency(sale.discount_amount) : '';
    const formattedLoyaltyDiscount = sale.loyalty_points_discount > 0 ? formatWithCurrency(sale.loyalty_points_discount) : '';
    const formattedCashTendered = sale.cash_tendered ? formatWithCurrency(sale.cash_tendered) : '';
    const formattedChange = sale.change_amount > 0 ? formatWithCurrency(sale.change_amount) : '';
    const cashierName = sale.cashier ?
      ((sale.cashier.first_name || '') + ' ' + (sale.cashier.last_name || '')).trim() || 'Unknown User' : '';

    // Generate the receipt HTML
    const receiptHTML = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt - ${sale.invoice_number}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              font-size: 14px;
              line-height: 1.4;
            }
            .receipt {
              max-width: 400px;
              margin: 0 auto;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #000;
              padding-bottom: 10px;
              margin-bottom: 15px;
            }
            .company-name {
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 5px;
            }
            .receipt-info {
              margin-bottom: 15px;
            }
            .receipt-info div {
              margin-bottom: 3px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin: 15px 0;
            }
            th, td {
              padding: 8px 4px;
              text-align: left;
              border-bottom: 1px solid #ddd;
            }
            th {
              font-weight: bold;
              background-color: #f5f5f5;
            }
            .text-right {
              text-align: right;
            }
            .text-center {
              text-align: center;
            }
            .summary {
              margin-top: 15px;
            }
            .summary-row {
              display: flex;
              justify-content: space-between;
              margin: 5px 0;
            }
            .total-row {
              border-top: 2px solid #000;
              padding-top: 8px;
              margin-top: 10px;
              font-weight: bold;
              font-size: 16px;
            }
            .footer {
              text-align: center;
              margin-top: 20px;
              font-size: 12px;
              color: #666;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="receipt">
            <!-- Header -->
            <div class="header">
              <div class="company-name">${currentOrganization?.name || 'Your Business'}</div>
              ${currentOrganization?.address ? `<div>${currentOrganization.address}</div>` : ''}
              ${currentOrganization?.phone || currentOrganization?.email ? `
                <div style="margin-top: 5px;">
                  ${currentOrganization?.phone ? `Phone: ${currentOrganization.phone}` : ''}
                  ${currentOrganization?.phone && currentOrganization?.email ? ' | ' : ''}
                  ${currentOrganization?.email ? `Email: ${currentOrganization.email}` : ''}
                </div>
              ` : ''}
              ${currentOrganization?.website ? `<div>${currentOrganization.website}</div>` : ''}
            </div>

            <!-- Receipt Info -->
            <div class="receipt-info">
              <div><strong>Receipt: ${sale.invoice_number}</strong></div>
              <div>Date: ${formattedDate}</div>
              ${sale.customer ? `<div>Customer: ${sale.customer.name}</div>` : ''}
              ${sale.cashier ? `<div>Cashier: ${cashierName}</div>` : ''}
            </div>

            <!-- Items Table -->
            <table>
              <thead>
                <tr>
                  <th>Item</th>
                  <th class="text-right">Qty</th>
                  <th class="text-right">Price</th>
                  <th class="text-right">Total</th>
                </tr>
              </thead>
              <tbody>
                ${sale.items?.map((item: any) => `
                  <tr>
                    <td>${item.product?.name || 'Product'}</td>
                    <td class="text-right">${item.quantity} pcs</td>
                    <td class="text-right">${formatWithCurrency(item.unit_price)}</td>
                    <td class="text-right">${formatWithCurrency(item.unit_price * item.quantity)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>

            <!-- Summary -->
            <div class="summary">
              <div class="summary-row">
                <span>Subtotal</span>
                <span>${formattedSubtotal}</span>
              </div>
              ${sale.discount_amount > 0 ? `
                <div class="summary-row">
                  <span>Discount</span>
                  <span>-${formattedDiscount}</span>
                </div>
              ` : ''}
              ${sale.loyalty_points_discount && sale.loyalty_points_discount > 0 ? `
                <div class="summary-row">
                  <span>Loyalty Points (${sale.loyalty_points_used} pts)</span>
                  <span>-${formattedLoyaltyDiscount}</span>
                </div>
              ` : ''}
              <div class="summary-row">
                <span>Tax</span>
                <span>${formattedTax}</span>
              </div>
              <div class="summary-row total-row">
                <span>Total</span>
                <span>${formattedTotal}</span>
              </div>
            </div>

            ${sale.payment_method === 'cash' && sale.cash_tendered ? `
              <div class="summary" style="border-top: 1px solid #ccc; padding-top: 10px; margin-top: 15px;">
                <div class="summary-row">
                  <span>Cash Tendered:</span>
                  <span>${formattedCashTendered}</span>
                </div>
                ${sale.change_amount && sale.change_amount > 0 ? `
                  <div class="summary-row">
                    <span>Change:</span>
                    <span>${formattedChange}</span>
                  </div>
                ` : ''}
              </div>
            ` : ''}

            <!-- Footer -->
            <div class="footer">
              <p>Thank you for your business!</p>
              <p>Payment Method: ${sale.payment_method}</p>
              ${sale.notes ? `<p style="font-style: italic;">${sale.notes}</p>` : ''}
              <p style="margin-top: 15px;">Generated on ${new Date().toLocaleDateString()}</p>
            </div>
          </div>

          <script>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              };
            };
          </script>
        </body>
      </html>
    `;

    // Write the HTML to the new window
    printWindow.document.write(receiptHTML);
    printWindow.document.close();
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <PageHeader
        title="Sales History"
        description="View and manage your sales records"
        icon={<HiOutlineDocumentReport className="h-8 w-8" />}
      />

      {/* Filters */}
      <Card className="mb-6">
        <div className="flex flex-wrap gap-4 items-end">
          <div className="flex-1 min-w-[200px]">
            <Label htmlFor="search" value="Search" />
            <TextInput
              id="search"
              type="text"
              icon={HiOutlineSearch}
              placeholder="Search by invoice # or customer"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="w-40">
            <Label htmlFor="dateFilter" value="Date Range" />
            <Select
              id="dateFilter"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value as any)}
            >
              <option value="today">Today</option>
              <option value="yesterday">Yesterday</option>
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="custom">Custom Range</option>
            </Select>
          </div>

          {dateFilter === 'custom' && (
            <>
              <div>
                <Label htmlFor="startDate" value="Start Date" />
                <TextInput
                  id="startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="endDate" value="End Date" />
                <TextInput
                  id="endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </>
          )}

          <Button color="blue" onClick={fetchSales}>
            <HiOutlineRefresh className="mr-2 h-4 w-4" />
            Refresh
          </Button>

          <Button color="light" onClick={handleExportSales}>
            <HiOutlineDownload className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </Card>

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <Card>
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 mr-4">
              <HiOutlineDocumentReport className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h5 className="text-gray-500 text-sm">Total Sales</h5>
              <p className="text-2xl font-bold">{totalSales}</p>
            </div>
          </div>
        </Card>
        <Card>
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 mr-4">
              <HiOutlineCash className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h5 className="text-gray-500 text-sm">Total Revenue</h5>
              <p className="text-2xl font-bold">{formatWithCurrency(totalAmount)}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Sales Table */}
      <Card>
        {loading ? (
          <div className="flex justify-center py-8">
            <Spinner size="xl" />
          </div>
        ) : error ? (
          <Alert color="failure">
            {error}
          </Alert>
        ) : filteredSales.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No sales found for the selected period
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Invoice #</Table.HeadCell>
                <Table.HeadCell>Date</Table.HeadCell>
                <Table.HeadCell>Customer</Table.HeadCell>
                <Table.HeadCell>Cashier</Table.HeadCell>
                <Table.HeadCell>Payment</Table.HeadCell>
                <Table.HeadCell>Amount</Table.HeadCell>
                <Table.HeadCell>Refund Status</Table.HeadCell>
                <Table.HeadCell>Actions</Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {currentSales.map((sale) => (
                  <Table.Row key={sale.id} className="bg-white">
                    <Table.Cell className="font-medium">
                      {sale.invoice_number}
                    </Table.Cell>
                    <Table.Cell>
                      {formatDateTime(sale.created_at)}
                    </Table.Cell>
                    <Table.Cell>
                      {sale.customer ? sale.customer.name : 'Walk-in Customer'}
                    </Table.Cell>
                    <Table.Cell>
                      {sale.cashier ? (
                        <div className="text-sm">
                          <div className="font-medium">
                            {`${sale.cashier.first_name || ''} ${sale.cashier.last_name || ''}`.trim() || 'Unknown User'}
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">Unknown</span>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <Badge color={sale.payment_method === 'cash' ? 'success' : 'info'}>
                        {sale.payment_method === 'cash' ? 'Cash' : 'Card'}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell className="font-medium">
                      <div>
                        {formatWithCurrency(sale.total_amount)}
                        {sale.refundData && sale.refundData.refundStatus !== 'none' && (
                          <div className="text-xs text-gray-500 mt-1">
                            Net: {formatWithCurrency(sale.total_amount - sale.refundData.totalRefunded)}
                          </div>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div>
                        {getRefundStatusBadge(sale.refundData)}
                        {getRefundSummary(sale.refundData)}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex gap-2">
                        <Button
                          size="xs"
                          color="light"
                          onClick={() => handleViewReceipt(sale.id)}
                        >
                          <HiOutlineEye className="mr-1 h-3 w-3" />
                          View
                        </Button>
                        <Button
                          size="xs"
                          color="light"
                          onClick={() => handlePrintReceipt(sale.id)}
                        >
                          <HiOutlinePrinter className="mr-1 h-3 w-3" />
                          Print
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        {/* Pagination */}
        {filteredSales.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={filteredSales.length}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="sales"
          />
        )}
      </Card>

      {/* Receipt Modal */}
      <Modal
        show={showReceiptModal}
        onClose={() => setShowReceiptModal(false)}
      >
        <Modal.Header>Sale Receipt</Modal.Header>
        <Modal.Body>
          {loadingSale ? (
            <div className="flex justify-center py-8">
              <Spinner size="xl" />
            </div>
          ) : selectedSale ? (
            <div>
              <SaleReceipt sale={selectedSale} showPrintButton={true} onPrint={handlePrintReceiptModal} />
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500">
              Receipt not found
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowReceiptModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default SalesHistoryPage;
