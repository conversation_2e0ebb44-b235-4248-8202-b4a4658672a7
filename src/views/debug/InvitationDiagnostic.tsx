import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Card, <PERSON>ton, Alert, Spinner, TextInput, Label, Textarea } from 'flowbite-react';
import { supabase } from '../../lib/supabase';

const InvitationDiagnostic: React.FC = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const invitationId = searchParams.get('id');
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [diagnosticResults, setDiagnosticResults] = useState<any>({});
  const [manualToken, setManualToken] = useState(token || '');
  const [manualId, setManualId] = useState(invitationId || '');
  const [sqlQuery, setSqlQuery] = useState('');
  const [sqlResult, setSqlResult] = useState<any>(null);
  const [sqlError, setSqlError] = useState<string | null>(null);
  const [sqlLoading, setSqlLoading] = useState(false);
  
  useEffect(() => {
    runDiagnostics();
  }, [token, invitationId]);
  
  const runDiagnostics = async () => {
    setLoading(true);
    setError(null);
    setDiagnosticResults({});
    
    try {
      const results: any = {
        params: { token, invitationId },
        timestamp: new Date().toISOString(),
      };
      
      // 1. Check database connection
      try {
        const { data, error } = await supabase.from('_test_connection').select('*').limit(1);
        results.connection = {
          success: !error,
          error: error ? error.message : null,
        };
      } catch (err: any) {
        results.connection = {
          success: false,
          error: err.message,
        };
      }
      
      // 2. Check if invitations table exists
      try {
        const { data, error } = await supabase.rpc('check_table_exists', { table_name: 'invitations' });
        results.table = {
          exists: data,
          error: error ? error.message : null,
        };
      } catch (err: any) {
        results.table = {
          exists: false,
          error: err.message,
        };
      }
      
      // 3. Get all invitations (limited to 10)
      try {
        const { data, error } = await supabase
          .from('invitations')
          .select('id, token, email, expires_at, accepted_at, created_at')
          .order('created_at', { ascending: false })
          .limit(10);
        
        results.allInvitations = {
          success: !error,
          count: data ? data.length : 0,
          data,
          error: error ? error.message : null,
        };
      } catch (err: any) {
        results.allInvitations = {
          success: false,
          count: 0,
          error: err.message,
        };
      }
      
      // 4. Try to find invitation by ID
      if (invitationId) {
        try {
          const { data, error } = await supabase
            .from('invitations')
            .select('*')
            .eq('id', invitationId)
            .single();
          
          results.byId = {
            success: !error && !!data,
            data,
            error: error ? error.message : null,
          };
        } catch (err: any) {
          results.byId = {
            success: false,
            error: err.message,
          };
        }
      }
      
      // 5. Try to find invitation by token
      if (token) {
        try {
          const { data, error } = await supabase
            .from('invitations')
            .select('*')
            .eq('token', token)
            .single();
          
          results.byToken = {
            success: !error && !!data,
            data,
            error: error ? error.message : null,
          };
        } catch (err: any) {
          results.byToken = {
            success: false,
            error: err.message,
          };
        }
      }
      
      // 6. Check if the token is URL-encoded
      if (token) {
        const decodedToken = decodeURIComponent(token);
        if (decodedToken !== token) {
          try {
            const { data, error } = await supabase
              .from('invitations')
              .select('*')
              .eq('token', decodedToken)
              .single();
            
            results.byDecodedToken = {
              success: !error && !!data,
              data,
              error: error ? error.message : null,
            };
          } catch (err: any) {
            results.byDecodedToken = {
              success: false,
              error: err.message,
            };
          }
        }
      }
      
      setDiagnosticResults(results);
    } catch (err: any) {
      setError(`Error running diagnostics: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  const handleManualCheck = () => {
    setManualToken(manualToken.trim());
    setManualId(manualId.trim());
    
    // Update URL with new parameters
    const params = new URLSearchParams();
    if (manualToken) params.set('token', manualToken);
    if (manualId) params.set('id', manualId);
    window.history.replaceState({}, '', `${window.location.pathname}?${params}`);
    
    runDiagnostics();
  };
  
  const handleRunSql = async () => {
    if (!sqlQuery.trim()) return;
    
    setSqlLoading(true);
    setSqlError(null);
    setSqlResult(null);
    
    try {
      const { data, error } = await supabase.rpc('run_diagnostic_query', {
        query_text: sqlQuery,
      });
      
      if (error) {
        setSqlError(error.message);
      } else {
        setSqlResult(data);
      }
    } catch (err: any) {
      setSqlError(err.message);
    } finally {
      setSqlLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Invitation System Diagnostic</h1>
      
      {error && (
        <Alert color="failure" className="mb-4">
          {error}
        </Alert>
      )}
      
      <div className="grid grid-cols-1 gap-6">
        <Card>
          <h2 className="text-xl font-bold mb-4">Check Invitation</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <div className="mb-2 block">
                <Label htmlFor="token" value="Invitation Token" />
              </div>
              <TextInput
                id="token"
                type="text"
                placeholder="Enter invitation token"
                value={manualToken}
                onChange={(e) => setManualToken(e.target.value)}
              />
            </div>
            
            <div>
              <div className="mb-2 block">
                <Label htmlFor="id" value="Invitation ID" />
              </div>
              <TextInput
                id="id"
                type="text"
                placeholder="Enter invitation ID"
                value={manualId}
                onChange={(e) => setManualId(e.target.value)}
              />
            </div>
          </div>
          
          <Button onClick={handleManualCheck} disabled={loading}>
            {loading ? <Spinner size="sm" className="mr-2" /> : null}
            Run Diagnostics
          </Button>
        </Card>
        
        <Card>
          <h2 className="text-xl font-bold mb-4">SQL Diagnostic</h2>
          
          <div className="mb-4">
            <div className="mb-2 block">
              <Label htmlFor="sql" value="SQL Query" />
            </div>
            <Textarea
              id="sql"
              placeholder="Enter SQL query"
              value={sqlQuery}
              onChange={(e) => setSqlQuery(e.target.value)}
              rows={3}
            />
            <p className="text-xs text-gray-500 mt-1">
              Example: SELECT * FROM invitations WHERE token = 'your_token'
            </p>
          </div>
          
          <Button onClick={handleRunSql} disabled={sqlLoading}>
            {sqlLoading ? <Spinner size="sm" className="mr-2" /> : null}
            Run SQL
          </Button>
          
          {sqlError && (
            <Alert color="failure" className="mt-4">
              {sqlError}
            </Alert>
          )}
          
          {sqlResult && (
            <div className="mt-4">
              <h3 className="font-bold mb-2">SQL Result:</h3>
              <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-auto max-h-60">
                {JSON.stringify(sqlResult, null, 2)}
              </pre>
            </div>
          )}
        </Card>
        
        <Card>
          <h2 className="text-xl font-bold mb-4">Diagnostic Results</h2>
          
          {loading ? (
            <div className="flex justify-center py-4">
              <Spinner size="lg" />
            </div>
          ) : (
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-auto max-h-[500px]">
              {JSON.stringify(diagnosticResults, null, 2)}
            </pre>
          )}
        </Card>
      </div>
    </div>
  );
};

export default InvitationDiagnostic;
