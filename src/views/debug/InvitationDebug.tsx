import React, { useState, useEffect } from 'react';
import { Card, Button, TextInput, Label, Alert, Spinner } from 'flowbite-react';
import { checkInvitation, createTestInvitation } from '../../services/invitationDebug';
import { supabase } from '../../lib/supabase';

const InvitationDebug: React.FC = () => {
  const [token, setToken] = useState('');
  const [invitationId, setInvitationId] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [invitationUrl, setInvitationUrl] = useState<string | null>(null);
  const [organizationStatus, setOrganizationStatus] = useState<'checking' | 'ok' | 'error'>('checking');
  const [organizationMessage, setOrganizationMessage] = useState<string | null>(null);

  // Check if the user has an organization and create one if needed
  useEffect(() => {
    const checkOrganization = async () => {
      try {
        setOrganizationStatus('checking');

        // Get the current user
        const { data: userData } = await supabase.auth.getUser();
        if (!userData.user) {
          setOrganizationStatus('error');
          setOrganizationMessage('User not authenticated');
          return;
        }

        // Check if the user is a member of any organization
        const { data: orgData, error: orgError } = await supabase
          .from('organization_members')
          .select('organization_id, role')
          .eq('user_id', userData.user.id);

        if (orgError) {
          console.error('Error checking organization membership:', orgError);
          setOrganizationStatus('error');
          setOrganizationMessage(`Error checking organization: ${orgError.message}`);
          return;
        }

        if (!orgData || orgData.length === 0) {
          // User is not a member of any organization, create one
          console.log('User is not a member of any organization, creating one...');

          // Create a new organization
          const { data: newOrg, error: createError } = await supabase
            .from('organizations')
            .insert({
              name: 'Debug Organization',
              created_by: userData.user.id
            })
            .select()
            .single();

          if (createError || !newOrg) {
            console.error('Error creating organization:', createError);
            setOrganizationStatus('error');
            setOrganizationMessage(`Error creating organization: ${createError?.message}`);
            return;
          }

          // Add the user as an owner of the organization
          const { error: memberError } = await supabase
            .from('organization_members')
            .insert({
              organization_id: newOrg.id,
              user_id: userData.user.id,
              role: 'owner'
            });

          if (memberError) {
            console.error('Error adding user to organization:', memberError);
            setOrganizationStatus('error');
            setOrganizationMessage(`Error adding user to organization: ${memberError.message}`);
            return;
          }

          setOrganizationStatus('ok');
          setOrganizationMessage(`Created new organization: ${newOrg.name}`);
        } else {
          // User is already a member of an organization
          setOrganizationStatus('ok');
          setOrganizationMessage(`User is a member of ${orgData.length} organization(s)`);
        }
      } catch (err: any) {
        console.error('Error in checkOrganization:', err);
        setOrganizationStatus('error');
        setOrganizationMessage(`Unexpected error: ${err.message}`);
      }
    };

    checkOrganization();
  }, []);

  const handleCheckInvitation = async () => {
    if (!token) {
      setError('Please enter a token');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);
    setResult(null);

    try {
      const checkResult = await checkInvitation(token, invitationId || undefined);
      setResult(checkResult);

      if (checkResult.exists) {
        setSuccess('Invitation found!');
      } else {
        setError(checkResult.error || 'Invitation not found');
      }
    } catch (err: any) {
      console.error('Error checking invitation:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTestInvitation = async () => {
    if (!email) {
      setError('Please enter an email');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);
    setResult(null);
    setInvitationUrl(null);

    try {
      const createResult = await createTestInvitation(email);

      if (createResult.success) {
        setSuccess('Test invitation created!');
        setInvitationUrl(createResult.invitationUrl || null);
      } else {
        setError(createResult.error || 'Failed to create test invitation');
      }
    } catch (err: any) {
      console.error('Error creating test invitation:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const copyInvitationLink = () => {
    if (invitationUrl) {
      navigator.clipboard.writeText(invitationUrl);
      setSuccess('Invitation link copied to clipboard');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Invitation System Debug</h1>

      {/* Organization Status */}
      {organizationStatus === 'checking' && (
        <Alert color="info" className="mb-6">
          <Spinner size="sm" className="mr-2" />
          Checking organization status...
        </Alert>
      )}

      {organizationStatus === 'error' && (
        <Alert color="failure" className="mb-6">
          <p className="font-medium">Organization Error</p>
          <p className="text-sm">{organizationMessage}</p>
        </Alert>
      )}

      {organizationStatus === 'ok' && (
        <Alert color="success" className="mb-6">
          <p className="font-medium">Organization Status</p>
          <p className="text-sm">{organizationMessage}</p>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Check Invitation */}
        <Card>
          <h2 className="text-xl font-bold mb-4">Check Invitation</h2>

          {error && (
            <Alert color="failure" className="mb-4">
              {error}
            </Alert>
          )}

          {success && (
            <Alert color="success" className="mb-4">
              {success}
            </Alert>
          )}

          <div className="mb-4">
            <div className="mb-2 block">
              <Label htmlFor="token" value="Invitation Token" />
            </div>
            <TextInput
              id="token"
              type="text"
              placeholder="Enter invitation token"
              value={token}
              onChange={(e) => setToken(e.target.value)}
            />
          </div>

          <div className="mb-4">
            <div className="mb-2 block">
              <Label htmlFor="invitationId" value="Invitation ID (optional)" />
            </div>
            <TextInput
              id="invitationId"
              type="text"
              placeholder="Enter invitation ID"
              value={invitationId}
              onChange={(e) => setInvitationId(e.target.value)}
            />
          </div>

          <Button
            onClick={handleCheckInvitation}
            disabled={loading || organizationStatus !== 'ok'}
          >
            {loading ? <Spinner size="sm" className="mr-2" /> : null}
            Check Invitation
          </Button>

          {result && (
            <div className="mt-4">
              <h3 className="font-bold mb-2">Result:</h3>
              <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-auto max-h-60">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </Card>

        {/* Create Test Invitation */}
        <Card>
          <h2 className="text-xl font-bold mb-4">Create Test Invitation</h2>

          <div className="mb-4">
            <div className="mb-2 block">
              <Label htmlFor="email" value="Email Address" />
            </div>
            <TextInput
              id="email"
              type="email"
              placeholder="Enter email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>

          <Button
            onClick={handleCreateTestInvitation}
            disabled={loading || organizationStatus !== 'ok'}
          >
            {loading ? <Spinner size="sm" className="mr-2" /> : null}
            Create Test Invitation
          </Button>

          {invitationUrl && (
            <div className="mt-4">
              <h3 className="font-bold mb-2">Invitation URL:</h3>
              <div className="flex items-center space-x-2">
                <TextInput
                  id="invitationLink"
                  type="text"
                  value={invitationUrl}
                  readOnly
                  className="flex-1"
                />
                <Button color="light" onClick={copyInvitationLink}>
                  Copy
                </Button>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default InvitationDebug;
