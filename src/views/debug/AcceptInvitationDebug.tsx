import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, Button, TextInput, Label, Alert, Spinner } from 'flowbite-react';
import { HiOutlineMail, HiOutlineLockClosed, HiOutlineUser } from 'react-icons/hi';
import { supabase } from '../../lib/supabase';

const AcceptInvitationDebug: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const rawToken = searchParams.get('token');
  const invitationId = searchParams.get('id');

  // Make sure to decode the token if it's URL-encoded
  const [token, setToken] = useState<string | null>(
    rawToken ? decodeURIComponent(rawToken) : null
  );
  const [manualId, setManualId] = useState<string | null>(invitationId);

  console.log('Raw token from URL:', rawToken);
  console.log('Decoded token:', token);
  const [loading, setLoading] = useState(false);
  const [invitation, setInvitation] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Registration form state
  const [showRegistration, setShowRegistration] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [registering, setRegistering] = useState(false);

  // Load invitation details on mount
  useEffect(() => {
    const fetchInvitation = async () => {
      if (!token && !invitationId) {
        return;
      }

      setLoading(true);
      setError(null);

      // Check if the user is already logged in
      const { data: sessionData } = await supabase.auth.getSession();
      const currentUser = sessionData.session?.user;

      // If not logged in, we'll show the registration form later
      const isLoggedIn = !!currentUser;

      try {
        console.log('Fetching invitation with:', { token, invitationId });

        // Get all invitations for debugging
        const { data: allInvitations } = await supabase
          .from('invitations')
          .select('id, token, email, expires_at, accepted_at')
          .limit(10);

        console.log('All recent invitations:', allInvitations);

        // Try to find the invitation using different approaches
        let invitation = null;
        let invitationError = null;

        // First try: Use ID if available
        if (invitationId) {
          console.log('Trying to find invitation with ID:', invitationId);

          // First, try a simple query without joins to see if the invitation exists
          const { data: simpleData, error: simpleError } = await supabase
            .from('invitations')
            .select('id, token, email, organization_id, role, expires_at, accepted_at')
            .eq('id', invitationId)
            .is('accepted_at', null)
            .single();

          console.log('Simple query result:', { data: simpleData, error: simpleError });

          // If the simple query works, try the full query
          if (simpleData && !simpleError) {
            try {
              const { data, error } = await supabase
                .from('invitations')
                .select('*, organizations(name)')
                .eq('id', invitationId)
                .is('accepted_at', null)
                .single();

              console.log('Full query result:', { data, error });

              if (data && !error) {
                invitation = data;
              } else {
                // If the full query fails but the simple one worked, use the simple data
                invitation = simpleData;
              }
            } catch (err) {
              console.error('Error in full query:', err);
              // Use the simple data if the full query throws an error
              invitation = simpleData;
            }
          } else {
            invitationError = simpleError;
          }
        }

        // Second try: Use token if available and first try failed
        if (!invitation && token) {
          console.log('Trying to find invitation with token:', token);

          // First, try a simple query without joins
          const { data: simpleData, error: simpleError } = await supabase
            .from('invitations')
            .select('id, token, email, organization_id, role, expires_at, accepted_at')
            .eq('token', token)
            .is('accepted_at', null)
            .single();

          console.log('Simple token query result:', { data: simpleData, error: simpleError });

          if (simpleData && !simpleError) {
            try {
              // If the simple query works, try the full query
              const { data, error } = await supabase
                .from('invitations')
                .select('*, organizations(name)')
                .eq('token', token)
                .is('accepted_at', null)
                .single();

              console.log('Full token query result:', { data, error });

              if (data && !error) {
                invitation = data;
              } else {
                // If the full query fails but the simple one worked, use the simple data
                invitation = simpleData;
              }
            } catch (err) {
              console.error('Error in full token query:', err);
              // Use the simple data if the full query throws an error
              invitation = simpleData;
            }
          } else if (!invitationError) {
            invitationError = simpleError;
          }
        }

        // Third try: Try with different token formats
        if (!invitation && token) {
          // Try with different token formats
          const alternativeTokens = [
            token.replace(/\+/g, ' '),  // Replace + with space
            token.replace(/\s/g, '+'),  // Replace space with +
            encodeURIComponent(token),  // Fully encode
            decodeURIComponent(token)   // Fully decode
          ];

          for (const altToken of alternativeTokens) {
            if (altToken === token) continue; // Skip if same as original

            console.log('Trying alternative token format:', altToken);

            // First, try a simple query without joins
            const { data: simpleData, error: simpleError } = await supabase
              .from('invitations')
              .select('id, token, email, organization_id, role, expires_at, accepted_at')
              .eq('token', altToken)
              .is('accepted_at', null)
              .single();

            console.log('Simple alt token query result:', { data: simpleData, error: simpleError });

            if (simpleData && !simpleError) {
              try {
                // If the simple query works, try the full query
                const { data, error } = await supabase
                  .from('invitations')
                  .select('*, organizations(name)')
                  .eq('token', altToken)
                  .is('accepted_at', null)
                  .single();

                console.log('Full alt token query result:', { data, error });

                if (data && !error) {
                  invitation = data;
                  break;
                } else {
                  // If the full query fails but the simple one worked, use the simple data
                  invitation = simpleData;
                  break;
                }
              } catch (err) {
                console.error('Error in full alt token query:', err);
                // Use the simple data if the full query throws an error
                invitation = simpleData;
                break;
              }
            }
          }
        }

        // Process the result
        if (invitation) {
          const data = invitation;
          console.log('Found invitation:', data);
          setInvitation(data);

          // Set the email from the invitation
          setEmail(data.email || '');

          // Check if the invitation has expired
          const expiresAt = new Date(data.expires_at);
          if (expiresAt < new Date()) {
            setError('This invitation has expired.');
          }

          // If user is not logged in, show registration form immediately
          if (!currentUser) {
            console.log('User not logged in, showing registration form');
            setShowRegistration(true);
          }
        } else {
          console.error('Could not find invitation with any method');
          console.error('Last error:', invitationError);
          setError('Invalid or expired invitation.');
        }

        // Already handled above
      } catch (err: any) {
        console.error('Error fetching invitation:', err);
        setError(err.message || 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchInvitation();
  }, [token, invitationId]);

  const handleRegisterAndAccept = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!invitation) {
      setError('No valid invitation found');
      return;
    }

    if (!firstName || !lastName) {
      setError('Please provide your first and last name');
      return;
    }

    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    setRegistering(true);
    setError(null);

    try {
      // Create a new user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName
          }
        }
      });

      if (authError || !authData.user) {
        setError(authError?.message || 'Failed to create account');
        setRegistering(false);
        return;
      }

      const userId = authData.user.id;

      // Create a profile for the new user
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: userId,
          first_name: firstName,
          last_name: lastName
        });

      if (profileError) {
        console.error('Error creating profile:', profileError);
        // Continue anyway, as the user has been created
      }

      // Add the user to the organization
      const { error: memberError } = await supabase
        .from('organization_members')
        .insert({
          organization_id: invitation.organization_id,
          user_id: userId,
          role: invitation.role
        });

      if (memberError) {
        console.error('Error adding user to organization:', memberError);
        setError('Error adding user to organization: ' + memberError.message);
        setRegistering(false);
        return;
      }

      // Mark the invitation as accepted
      const { error: updateError } = await supabase
        .from('invitations')
        .update({
          accepted_at: new Date().toISOString()
        })
        .eq('id', invitation.id);

      if (updateError) {
        console.error('Error updating invitation:', updateError);
        // Continue anyway, as the user has been added to the organization
      }

      setSuccess('Account created and invitation accepted! Redirecting to dashboard...');

      // Redirect to the dashboard after a short delay
      setTimeout(() => {
        navigate('/dashboard');
      }, 3000);
    } catch (err: any) {
      console.error('Error in registration:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setRegistering(false);
    }
  };

  // This function is for logged-in users to accept the invitation
  const handleAcceptInvitation = async () => {
    if (!invitation) {
      setError('No valid invitation found');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Check if the user is logged in
      const { data: sessionData } = await supabase.auth.getSession();
      const currentUser = sessionData.session?.user;

      if (!currentUser) {
        // Show registration form instead of redirecting
        setShowRegistration(true);
        setLoading(false);
        return;
      }

      console.log('Accepting invitation as user:', currentUser.id);

      // Add the user to the organization
      const { error: memberError } = await supabase
        .from('organization_members')
        .insert({
          organization_id: invitation.organization_id,
          user_id: currentUser.id,
          role: invitation.role,
        });

      if (memberError) {
        if (memberError.code === '23505') { // Unique violation
          console.log('User is already a member of this organization');
          // Continue anyway
        } else {
          console.error('Error adding user to organization:', memberError);
          setError(`Error adding user to organization: ${memberError.message}`);
          setLoading(false);
          return;
        }
      }

      // Mark the invitation as accepted
      const { error: updateError } = await supabase
        .from('invitations')
        .update({
          accepted_at: new Date().toISOString(),
        })
        .eq('id', invitation.id);

      if (updateError) {
        console.error('Error updating invitation:', updateError);
        // Continue anyway, as the user has been added to the organization
      }

      setSuccess('Invitation accepted successfully! You have been added to the organization.');

      // Redirect to the dashboard after a short delay
      setTimeout(() => {
        navigate('/dashboard');
      }, 3000);
    } catch (err: any) {
      console.error('Error accepting invitation:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-50 dark:bg-gray-900 px-4">
      <div className="w-full max-w-md">
        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}

        {success && (
          <Alert color="success" className="mb-4">
            {success}
          </Alert>
        )}

        <Card className="shadow-lg">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-8">
              <Spinner size="xl" />
              <p className="mt-4 text-gray-500">Loading invitation details...</p>
            </div>
          ) : invitation ? (
            <div>
              <div className="text-center mb-6">
                <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Join Organization</h1>
                <p className="text-gray-600 dark:text-gray-300 mt-2">
                  You've been invited to join an organization
                </p>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-6">
                <div className="text-center mb-2">
                  <p className="text-lg font-semibold text-blue-800 dark:text-blue-200">
                    {invitation.organizations?.name || 'Organization'}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-300">
                    has invited you to join as a <span className="font-medium capitalize">{invitation.role}</span>
                  </p>
                </div>

                <div className="flex justify-between text-sm text-blue-700 dark:text-blue-300 mt-3">
                  <span>{invitation.email}</span>
                  <span>Expires: {new Date(invitation.expires_at).toLocaleDateString()}</span>
                </div>
              </div>

            {showRegistration ? (
              <div>
                <h3 className="text-lg font-semibold text-center mb-4">Create Your Account</h3>
                <form onSubmit={handleRegisterAndAccept} className="space-y-4">
                  <div>
                    <div className="mb-2 block">
                      <Label htmlFor="email" value="Email Address" className="text-sm font-medium" />
                    </div>
                    <TextInput
                      id="email"
                      type="email"
                      value={email}
                      icon={HiOutlineMail}
                      disabled
                      className="bg-gray-50"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="mb-2 block">
                        <Label htmlFor="firstName" value="First Name" className="text-sm font-medium" />
                      </div>
                      <TextInput
                        id="firstName"
                        type="text"
                        value={firstName}
                        icon={HiOutlineUser}
                        onChange={(e) => setFirstName(e.target.value)}
                        placeholder="John"
                        required
                      />
                    </div>

                    <div>
                      <div className="mb-2 block">
                        <Label htmlFor="lastName" value="Last Name" className="text-sm font-medium" />
                      </div>
                      <TextInput
                        id="lastName"
                        type="text"
                        value={lastName}
                        icon={HiOutlineUser}
                        onChange={(e) => setLastName(e.target.value)}
                        placeholder="Doe"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <div className="mb-2 block">
                      <Label htmlFor="password" value="Create Password" className="text-sm font-medium" />
                    </div>
                    <TextInput
                      id="password"
                      type="password"
                      value={password}
                      icon={HiOutlineLockClosed}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Password must be at least 8 characters long
                    </p>
                  </div>

                  <Button
                    type="submit"
                    color="blue"
                    className="w-full mt-6"
                    disabled={registering}
                    size="lg"
                  >
                    {registering ? <Spinner size="sm" className="mr-2" /> : null}
                    Create Account & Join
                  </Button>

                  <div className="text-center mt-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Already have an account?</p>
                    <Button
                      color="light"
                      size="sm"
                      onClick={() => {
                        const returnUrl = `/auth/accept-invitation?token=${encodeURIComponent(token || '')}&id=${invitationId || ''}`;
                        navigate(`/auth/login?returnUrl=${encodeURIComponent(returnUrl)}`);
                      }}
                    >
                      Sign in instead
                    </Button>
                  </div>
                </form>
              </div>
            ) : (
              <div className="text-center">
                <Button
                  color="blue"
                  onClick={handleAcceptInvitation}
                  disabled={loading}
                  size="lg"
                  className="px-8"
                >
                  {loading ? <Spinner size="sm" className="mr-2" /> : null}
                  Accept Invitation
                </Button>
                <p className="text-sm text-gray-500 mt-4">
                  You'll be added to the organization after accepting
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-6">
            <div className="mb-6">
              <svg className="w-16 h-16 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <h3 className="text-lg font-semibold mt-4 mb-2">Invalid Invitation</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                The invitation link appears to be invalid or has expired.
              </p>
            </div>

            <div className="space-y-4 max-w-sm mx-auto">
              <p className="text-sm text-gray-500 mb-4">If you have the invitation details, you can enter them manually:</p>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="token" value="Invitation Token" className="text-sm font-medium" />
                </div>
                <TextInput
                  id="token"
                  type="text"
                  placeholder="Enter invitation token"
                  value={token || ''}
                  onChange={(e) => setToken(e.target.value)}
                  icon={HiOutlineMail}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="invitationId" value="Invitation ID" className="text-sm font-medium" />
                </div>
                <TextInput
                  id="invitationId"
                  type="text"
                  placeholder="Enter invitation ID"
                  value={manualId || ''}
                  onChange={(e) => setManualId(e.target.value)}
                  icon={HiOutlineUser}
                />
              </div>

              <Button
                color="blue"
                onClick={() => {
                  // Update URL with new parameters
                  const params = new URLSearchParams();
                  if (token) params.set('token', token);
                  if (manualId) params.set('id', manualId);
                  window.history.replaceState({}, '', `${window.location.pathname}?${params}`);
                  window.location.reload();
                }}
                disabled={loading}
                className="w-full mt-4"
              >
                Check Invitation
              </Button>

              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Need a new invitation?
                </p>
                <Button
                  color="light"
                  size="sm"
                  onClick={() => navigate('/auth/login')}
                >
                  Contact your administrator
                </Button>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  </div>
  );
};

export default AcceptInvitationDebug;
