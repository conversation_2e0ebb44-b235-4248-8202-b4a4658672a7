import { But<PERSON>, Label, TextInput, Alert, Tabs, Card } from "flowbite-react";
import { Link } from "react-router-dom";
import { useState, useEffect } from "react";
import { useOrganization } from "../../context/OrganizationContext";
import { useOrganizationSettings } from "../../context/OrganizationSettingsContext";
import { getOrganizationSettings, updateOrganizationSettings, updateOrganization } from "../../services/organization";
import BusinessTypeSelector from "../../components/settings/BusinessTypeSelector";
import TaxSettings from "../../components/settings/TaxSettings";
import ChatSettings from "../../components/settings/ChatSettings";
import LoyaltySettings from "../../components/settings/LoyaltySettings";
import CurrencySelector, { Currency } from "../../components/settings/CurrencySelector";
import { isOrganizationOwner } from "../../services/userManagement";

const OrganizationSettings = () => {
  const { currentOrganization, currentMember } = useOrganization();
  const { refreshSettings } = useOrganizationSettings();
  const [organizationName, setOrganizationName] = useState("");
  const [organizationAddress, setOrganizationAddress] = useState("");
  const [organizationPhone, setOrganizationPhone] = useState("");
  const [organizationEmail, setOrganizationEmail] = useState("");
  const [organizationWebsite, setOrganizationWebsite] = useState("");
  const [settings, setSettings] = useState<any>({});
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingSettings, setLoadingSettings] = useState<boolean>(true);
  const [isOwner, setIsOwner] = useState(false);

  useEffect(() => {
    if (currentOrganization) {
      setOrganizationName(currentOrganization.name);
      setOrganizationAddress(currentOrganization.address || "");
      setOrganizationPhone(currentOrganization.phone || "");
      setOrganizationEmail(currentOrganization.email || "");
      setOrganizationWebsite(currentOrganization.website || "");
      fetchOrganizationSettings();

      // Check if the current user is an owner
      if (currentMember) {
        setIsOwner(currentMember.role === 'owner');
      }
    }
  }, [currentOrganization, currentMember]);

  const fetchOrganizationSettings = async () => {
    if (!currentOrganization) return;

    try {
      setLoadingSettings(true);
      const orgSettings = await getOrganizationSettings(currentOrganization.id);
      setSettings(orgSettings.settings);
    } catch (err: any) {
      setError(err.message || "Failed to load organization settings");
    } finally {
      setLoadingSettings(false);
    }
  };

  const handleUpdateOrganization = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!currentOrganization) {
      setError("No organization selected");
      return;
    }

    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      await updateOrganization(currentOrganization.id, {
        name: organizationName,
        address: organizationAddress,
        phone: organizationPhone,
        email: organizationEmail,
        website: organizationWebsite
      });
      setSuccess("Organization updated successfully");
    } catch (err: any) {
      setError(err.message || "An error occurred while updating the organization");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSettings = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!currentOrganization) {
      setError("No organization selected");
      return;
    }

    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      // Ensure both tax_rate and tax_settings.vat_rate are updated
      const updatedSettings = {
        ...settings,
        tax_settings: {
          ...(settings.tax_settings || {}),
          vat_rate: settings.tax_rate || 0
        }
      };

      await updateOrganizationSettings(currentOrganization.id, updatedSettings);
      await refreshSettings(); // Refresh the settings context
      setSuccess("Settings updated successfully");
    } catch (err: any) {
      setError(err.message || "An error occurred while updating settings");
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (key: string, value: any) => {
    setSettings((prev: any) => ({
      ...prev,
      [key]: value
    }));
  };

  const handleCurrencyChange = (currency: Currency) => {
    setSettings((prev: any) => ({
      ...prev,
      currency: currency.code,
      currency_settings: {
        code: currency.code,
        name: currency.name,
        symbol: currency.symbol,
        locale: currency.locale
      }
    }));
  };

  if (!currentOrganization) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="warning">
          No organization selected. Please select or create an organization.
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Organization Settings</h1>

      {error && (
        <Alert color="failure" className="mb-4">
          {error}
        </Alert>
      )}

      {success && (
        <Alert color="success" className="mb-4">
          {success}
        </Alert>
      )}

      <Tabs aria-label="Organization settings tabs">
        <Tabs.Item active title="General">
          <div className="mt-4">
            <form onSubmit={handleUpdateOrganization}>
              <div className="mb-4">
                <div className="mb-2 block">
                  <Label htmlFor="organizationName" value="Organization Name" />
                </div>
                <TextInput
                  id="organizationName"
                  type="text"
                  sizing="md"
                  required
                  value={organizationName}
                  onChange={(e) => setOrganizationName(e.target.value)}
                  className="form-control form-rounded-xl"
                />
              </div>

              <div className="mb-4">
                <div className="mb-2 block">
                  <Label htmlFor="organizationAddress" value="Address" />
                </div>
                <TextInput
                  id="organizationAddress"
                  type="text"
                  sizing="md"
                  value={organizationAddress}
                  onChange={(e) => setOrganizationAddress(e.target.value)}
                  className="form-control form-rounded-xl"
                  placeholder="Enter your business address"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="organizationPhone" value="Phone Number" />
                  </div>
                  <TextInput
                    id="organizationPhone"
                    type="tel"
                    sizing="md"
                    value={organizationPhone}
                    onChange={(e) => setOrganizationPhone(e.target.value)}
                    className="form-control form-rounded-xl"
                    placeholder="Enter phone number"
                  />
                </div>

                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="organizationEmail" value="Email Address" />
                  </div>
                  <TextInput
                    id="organizationEmail"
                    type="email"
                    sizing="md"
                    value={organizationEmail}
                    onChange={(e) => setOrganizationEmail(e.target.value)}
                    className="form-control form-rounded-xl"
                    placeholder="Enter email address"
                  />
                </div>
              </div>

              <div className="mb-4">
                <div className="mb-2 block">
                  <Label htmlFor="organizationWebsite" value="Website" />
                </div>
                <TextInput
                  id="organizationWebsite"
                  type="url"
                  sizing="md"
                  value={organizationWebsite}
                  onChange={(e) => setOrganizationWebsite(e.target.value)}
                  className="form-control form-rounded-xl"
                  placeholder="https://www.example.com"
                />
              </div>

              <Button
                type="submit"
                color={"primary"}
                className="bg-primary text-white rounded-xl"
                disabled={loading}
              >
                {loading ? "Saving..." : "Save Changes"}
              </Button>
            </form>
          </div>
        </Tabs.Item>

        <Tabs.Item title="Business Settings">
          <div className="mt-4">
            {loadingSettings ? (
              <p>Loading settings...</p>
            ) : (
              <>
                {/* Currency Settings Info */}
                <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h3 className="text-lg font-semibold text-blue-900 mb-2">
                    💰 Currency Configuration
                  </h3>
                  <p className="text-blue-800 text-sm">
                    Select your business currency. This will be used throughout the system for pricing,
                    sales reports, and financial calculations. Philippines Peso (PHP) is set as the default.
                  </p>
                </div>

                <form onSubmit={handleUpdateSettings}>
                <div className="mb-6">
                  <CurrencySelector
                    value={settings.currency || "PHP"}
                    onChange={handleCurrencyChange}
                    disabled={loading}
                    showPreview={true}
                  />
                </div>

                <div className="mb-4">
                  <div className="mb-2 block">
                    <Label htmlFor="taxRate" value="Default Tax Rate (%)" />
                  </div>
                  <TextInput
                    id="taxRate"
                    type="number"
                    sizing="md"
                    required
                    value={settings.tax_rate || 0}
                    onChange={(e) => handleSettingChange("tax_rate", parseFloat(e.target.value))}
                    className="form-control form-rounded-xl"
                  />
                </div>

                <Button
                  type="submit"
                  color={"primary"}
                  className="bg-primary text-white rounded-xl"
                  disabled={loading}
                >
                  {loading ? "Saving..." : "Save Settings"}
                </Button>
              </form>
              </>
            )}

            {/* Business Type Selector - Only visible to owners */}
            {isOwner && (
              <div className="mt-8">
                <BusinessTypeSelector />
              </div>
            )}

            {/* Tax Settings - Only visible to owners */}
            <div className="mt-8">
              <TaxSettings isOwner={isOwner} />
            </div>

            {/* Chat Settings - Only visible to owners */}
            <div className="mt-8">
              <ChatSettings isOwner={isOwner} />
            </div>

            {/* Loyalty Settings */}
            <div className="mt-8">
              <LoyaltySettings isOwner={isOwner} />
            </div>
          </div>
        </Tabs.Item>

        <Tabs.Item title="Additional Settings">
          <div className="mt-4 space-y-4">
            <Card>
              <h3 className="text-lg font-medium">Inventory Settings</h3>
              <p className="text-gray-500 mb-4">
                Configure units of measurement and other inventory-related settings.
              </p>

              <div className="space-y-2">
                <Link to="/settings/uom">
                  <Button color="primary">
                    Manage Units of Measurement
                  </Button>
                </Link>
                <p className="text-sm text-gray-500">
                  Define and manage units of measurement for your products (pieces, boxes, kg, etc.)
                </p>
              </div>
            </Card>
          </div>
        </Tabs.Item>
      </Tabs>
    </div>
  );
};

export default OrganizationSettings;
