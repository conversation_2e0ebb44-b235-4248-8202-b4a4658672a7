import { But<PERSON>, Label, TextInput, <PERSON><PERSON>, Card } from "flowbite-react";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { createOrganization } from "../../services/organization";
import { useAuth } from "../../context/AuthContext";
import { useOrganization } from "../../context/OrganizationContext";

const CreateOrganization = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { organizations, setCurrentOrganization } = useOrganization();
  const [name, setName] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // Redirect to login if not logged in
  useEffect(() => {
    if (!user) {
      navigate("/auth/login");
    }
  }, [user, navigate]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!user) {
      setError("You must be logged in to create an organization");
      navigate("/auth/login");
      return;
    }

    if (!name.trim()) {
      setError("Organization name cannot be empty");
      return;
    }

    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      console.log("Creating organization:", name);

      // Show a more detailed message while creating
      setSuccess("Creating organization... This may take a moment.");

      const newOrg = await createOrganization(name, user.id);
      console.log("Organization created:", newOrg);

      setSuccess(`Organization "${newOrg.name}" created successfully!`);

      // Set the new organization as the current one
      setCurrentOrganization(newOrg);

      // Redirect to dashboard after a short delay
      setTimeout(() => {
        navigate("/");
      }, 1500);
    } catch (err: any) {
      console.error("Error creating organization:", err);

      // Show a more detailed error message
      if (err.message.includes("violates row-level security policy")) {
        setError("Permission error: Unable to create organization due to security policies. Please contact support.");
      } else {
        setError(err.message || "An error occurred while creating the organization");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-md mx-auto">
        <Card>
          <h1 className="text-2xl font-bold mb-2">Create New Organization</h1>
          <p className="text-sm text-gray-500 mb-6">
            Each organization is a separate tenant with isolated data
          </p>

          {error && (
            <Alert color="failure" className="mb-4">
              {error}
            </Alert>
          )}

          {success && (
            <Alert color="success" className="mb-4">
              {success}
            </Alert>
          )}

          {organizations && organizations.length > 0 && (
            <Alert color="info" className="mb-4">
              <h3 className="font-medium">You already have organizations</h3>
              <p className="text-sm">
                You currently have {organizations.length} organization(s). Creating a new one will give you a separate tenant with isolated data.
              </p>
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <div className="mb-2 block">
                <Label htmlFor="name" value="Organization Name" />
                <p className="text-xs text-gray-500 mt-1">
                  This will be the name of your business in the system
                </p>
              </div>
              <TextInput
                id="name"
                type="text"
                sizing="md"
                required
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="form-control form-rounded-xl"
                placeholder="Enter your organization name"
              />
            </div>

            <Button
              type="submit"
              color={"primary"}
              className="w-full bg-primary text-white rounded-xl"
              disabled={loading}
            >
              {loading ? "Creating..." : "Create Organization"}
            </Button>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default CreateOrganization;
