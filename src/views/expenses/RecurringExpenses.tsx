import React, { useState, useEffect } from 'react';
import { Card, But<PERSON>, Table, Badge, Spinner, Alert, Modal, TextInput, Select, Textarea, Checkbox } from 'flowbite-react';
import { HiPlus, HiPencil, HiTrash, HiPlay, <PERSON>Pause, HiDocument<PERSON>ext, <PERSON><PERSON><PERSON><PERSON>, Hi<PERSON> } from 'react-icons/hi';
import Pagination from '../../components/common/Pagination';
import { useAuth } from '../../context/AuthContext';
import { useOrganization } from '../../context/OrganizationContext';
import {
  getRecurringExpenses,
  createRecurringExpense,
  updateRecurringExpense,
  createPayableFromRecurringExpense,
  getExpenseTypes,
  createEnhancedPayable
} from '../../services/operationalExpenses';
import { getSuppliers } from '../../services/supplier';
import { getEmployees } from '../../services/employee';

import {
  RecurringExpense,
  CreateRecurringExpenseRequest,
  RecurringExpenseFilters,
  ExpenseType,
  RecurrenceFrequency,
  CreateEnhancedPayableRequest,
  EnhancedPayableSourceType
} from '../../types/operationalExpenses.types';

const RecurringExpenses: React.FC = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  // State management
  const [recurringExpenses, setRecurringExpenses] = useState<RecurringExpense[]>([]);
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'approve' | 'reject' | null>(null);
  const [selectedExpense, setSelectedExpense] = useState<RecurringExpense | null>(null);
  const [editingExpense, setEditingExpense] = useState<RecurringExpense | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [filters, setFilters] = useState<RecurringExpenseFilters>({});

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Form state
  const [formData, setFormData] = useState<CreateRecurringExpenseRequest>({
    name: '',
    description: '',
    expense_type_id: '',
    supplier_id: '',
    employee_id: '',
    amount: 0,
    vat_amount: 0,
    withholding_tax_rate: 0,
    frequency: RecurrenceFrequency.MONTHLY,
    start_date: new Date().toISOString().split('T')[0],
    end_date: '',
    payment_terms_days: 30,
    auto_create_payable: false
  });

  // Load data
  const loadData = async () => {
    if (!currentOrganization) return;

    try {
      setLoading(true);

      // Load all required data in parallel
      const [expensesResult, typesResult, suppliersResult, employeesResult] = await Promise.all([
        getRecurringExpenses(currentOrganization.id, filters),
        getExpenseTypes(currentOrganization.id, { is_active: true }),
        getSuppliers(currentOrganization.id),
        getEmployees(currentOrganization.id)
      ]);

      if (expensesResult.success && expensesResult.data) {
        setRecurringExpenses(expensesResult.data);
        setTotalCount(expensesResult.data.length);
      } else {
        setError(expensesResult.error || 'Failed to load recurring expenses');
      }

      if (typesResult.success && typesResult.data) {
        setExpenseTypes(typesResult.data);
      }

      if (suppliersResult.suppliers) {
        setSuppliers(suppliersResult.suppliers);
      }

      if (employeesResult.employees) {
        setEmployees(employeesResult.employees);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [currentOrganization, filters]);

  // Calculate pagination
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedRecurringExpenses = recurringExpenses.slice(startIndex, endIndex);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentOrganization || !user) return;

    try {
      setSubmitting(true);
      setError(null);

      // Clean up form data and ensure single payee constraint
      const cleanFormData = {
        ...formData,
        expense_type_id: formData.expense_type_id || undefined,
        supplier_id: formData.supplier_id || null,
        employee_id: formData.employee_id || null,
        end_date: formData.end_date || undefined
      };

      // Validate single payee constraint
      if (!cleanFormData.supplier_id && !cleanFormData.employee_id) {
        setError('Please select either a supplier or employee');
        return;
      }
      if (cleanFormData.supplier_id && cleanFormData.employee_id) {
        setError('Please select either a supplier OR employee, not both');
        return;
      }

      let result;
      if (editingExpense) {
        result = await updateRecurringExpense(editingExpense.id, cleanFormData);
      } else {
        result = await createRecurringExpense(currentOrganization.id, cleanFormData, user.id);
      }

      if (result.success) {
        setShowModal(false);
        setEditingExpense(null);
        resetForm();
        loadData();
      } else {
        setError(result.error || 'Failed to save recurring expense');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle edit
  const handleEdit = (expense: RecurringExpense) => {
    setEditingExpense(expense);
    setFormData({
      name: expense.name,
      description: expense.description || '',
      expense_type_id: expense.expense_type_id || '',
      supplier_id: expense.supplier_id || '',
      employee_id: expense.employee_id || '',
      amount: expense.amount,
      vat_amount: expense.vat_amount,
      withholding_tax_rate: expense.withholding_tax_rate,
      frequency: expense.frequency,
      start_date: expense.start_date,
      end_date: expense.end_date || '',
      payment_terms_days: expense.payment_terms_days,
      auto_create_payable: expense.auto_create_payable
    });
    setShowModal(true);
  };

  // Create payable from recurring expense
  const handleCreatePayable = async (expense: RecurringExpense) => {
    if (!currentOrganization || !user) return;

    try {
      const result = await createPayableFromRecurringExpense(
        currentOrganization.id,
        expense.id,
        user.id
      );

      if (result.success) {
        alert('Payable created successfully!');
        loadData(); // Refresh to update next due dates
      } else {
        setError(result.error || 'Failed to create payable');
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      expense_type_id: '',
      supplier_id: '',
      employee_id: '',
      amount: 0,
      vat_amount: 0,
      withholding_tax_rate: 0,
      frequency: RecurrenceFrequency.MONTHLY,
      start_date: new Date().toISOString().split('T')[0],
      end_date: '',
      payment_terms_days: 30,
      auto_create_payable: false
    });
  };

  // Handle modal close
  const handleModalClose = () => {
    setShowModal(false);
    setEditingExpense(null);
    resetForm();
    setError(null);
  };

  // Format frequency for display
  const formatFrequency = (frequency: RecurrenceFrequency) => {
    return frequency.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Check if expense is due soon (next 7 days)
  const isDueSoon = (nextDueDate: string) => {
    const due = new Date(nextDueDate);
    const today = new Date();
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7 && diffDays >= 0;
  };

  // Check if expense is overdue
  const isOverdue = (nextDueDate: string) => {
    const due = new Date(nextDueDate);
    const today = new Date();
    return due < today;
  };

  // Handle approve action
  const handleApprove = (expense: RecurringExpense) => {
    setSelectedExpense(expense);
    setConfirmAction('approve');
    setShowConfirmModal(true);
  };

  // Handle reject action
  const handleReject = (expense: RecurringExpense) => {
    setSelectedExpense(expense);
    setConfirmAction('reject');
    setShowConfirmModal(true);
  };

  // Handle confirm action
  const handleConfirmAction = async () => {
    if (!selectedExpense || !confirmAction || !currentOrganization || !user) return;

    try {
      setSubmitting(true);
      setError(null);

      if (confirmAction === 'approve') {
        // Generate unique source_id with timestamp to prevent duplicates
        const timestamp = Date.now();
        const uniqueSourceId = `${selectedExpense.id}-${timestamp}`;

        // Generate reference number with timestamp
        const referenceNumber = `REC-${selectedExpense.name.replace(/\s+/g, '-').toUpperCase()}-${timestamp}`;

        // Calculate due date
        const today = new Date();
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + selectedExpense.payment_terms_days);

        // Create payable data for Enhanced Payables (pending approval)
        const payableData: CreateEnhancedPayableRequest = {
          source_type: EnhancedPayableSourceType.RECURRING_EXPENSE,
          source_id: uniqueSourceId,
          supplier_id: selectedExpense.supplier_id || undefined,
          employee_id: selectedExpense.employee_id || undefined,
          reference_number: referenceNumber,
          invoice_date: today.toISOString().split('T')[0],
          due_date: dueDate.toISOString().split('T')[0],
          amount: selectedExpense.amount,
          vat_amount: selectedExpense.vat_amount,
          withholding_tax_rate: selectedExpense.withholding_tax_rate,
          withholding_tax_amount: selectedExpense.withholding_tax_rate > 0
            ? (selectedExpense.amount * selectedExpense.withholding_tax_rate) / 100
            : 0,
          currency: 'PHP',
          category: selectedExpense.category || '',
          notes: `Created from recurring expense: ${selectedExpense.name}`,
          expense_type_id: selectedExpense.expense_type_id || undefined,
          department: selectedExpense.department || '',
          project_code: selectedExpense.project_code || ''
        };

        const result = await createEnhancedPayable(currentOrganization.id, payableData, user.id);

        if (!result.success) {
          setError(result.error || 'Failed to create payable');
          return;
        }
      } else if (confirmAction === 'reject') {
        // For now, just close the modal - you can add rejection logic here
        console.log('Rejected recurring expense:', selectedExpense.name);
      }

      setShowConfirmModal(false);
      setSelectedExpense(null);
      setConfirmAction(null);

      // Refresh the data
      await loadData();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Recurring Expenses</h1>
            <p className="text-gray-600 dark:text-gray-400">Manage recurring expense templates and schedules</p>
          </div>
          <Button onClick={() => setShowModal(true)} className="bg-primary">
            <HiPlus className="mr-2 h-4 w-4" />
            Add Recurring Expense
          </Button>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Select
            value={filters.frequency || ''}
            onChange={(e) => setFilters({ ...filters, frequency: e.target.value as RecurrenceFrequency || undefined })}
          >
            <option value="">All Frequencies</option>
            {Object.values(RecurrenceFrequency).map(frequency => (
              <option key={frequency} value={frequency}>
                {formatFrequency(frequency)}
              </option>
            ))}
          </Select>

          <Select
            value={filters.is_active?.toString() || ''}
            onChange={(e) => setFilters({ ...filters, is_active: e.target.value ? e.target.value === 'true' : undefined })}
          >
            <option value="">All Status</option>
            <option value="true">Active</option>
            <option value="false">Inactive</option>
          </Select>

          <Select
            value={filters.due_soon?.toString() || ''}
            onChange={(e) => setFilters({ ...filters, due_soon: e.target.value ? e.target.value === 'true' : undefined })}
          >
            <option value="">All Due Dates</option>
            <option value="true">Due Soon (30 days)</option>
          </Select>

          <TextInput
            placeholder="Search expenses..."
            value={filters.search || ''}
            onChange={(e) => setFilters({ ...filters, search: e.target.value || undefined })}
          />
        </div>

        {/* Error Alert */}
        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}

        {/* Recurring Expenses Table */}
        <div className="overflow-x-auto">
          <Table>
            <Table.Head>
              <Table.HeadCell>Name</Table.HeadCell>
              <Table.HeadCell>Amount</Table.HeadCell>
              <Table.HeadCell>Frequency</Table.HeadCell>
              <Table.HeadCell>Next Due</Table.HeadCell>
              <Table.HeadCell>Payee</Table.HeadCell>
              <Table.HeadCell>Status</Table.HeadCell>
              <Table.HeadCell>Actions</Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y">
              {paginatedRecurringExpenses.map((expense) => (
                <Table.Row key={expense.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                  <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                    <div>
                      <div className="font-semibold">{expense.name}</div>
                      {expense.description && (
                        <div className="text-sm text-gray-500">{expense.description}</div>
                      )}
                      {expense.expense_type && (
                        <Badge color="gray" size="sm" className="mt-1">
                          {expense.expense_type.name}
                        </Badge>
                      )}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div className="font-semibold">₱{expense.amount.toLocaleString()}</div>
                    {expense.vat_amount > 0 && (
                      <div className="text-xs text-gray-500">VAT: ₱{expense.vat_amount.toLocaleString()}</div>
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    <Badge color="blue">{formatFrequency(expense.frequency)}</Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <div className={`font-medium ${
                      isOverdue(expense.next_due_date) ? 'text-red-600' :
                      isDueSoon(expense.next_due_date) ? 'text-yellow-600' :
                      'text-gray-900 dark:text-white'
                    }`}>
                      {formatDate(expense.next_due_date)}
                    </div>
                    {isOverdue(expense.next_due_date) && (
                      <Badge color="failure" size="sm">Overdue</Badge>
                    )}
                    {isDueSoon(expense.next_due_date) && !isOverdue(expense.next_due_date) && (
                      <Badge color="warning" size="sm">Due Soon</Badge>
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    {expense.supplier ? (
                      <div>
                        <div className="font-medium">{expense.supplier.name}</div>
                        <div className="text-sm text-gray-500">Supplier</div>
                      </div>
                    ) : expense.employee ? (
                      <div>
                        <div className="font-medium">
                          {expense.employee.first_name} {expense.employee.last_name}
                        </div>
                        <div className="text-sm text-gray-500">Employee</div>
                      </div>
                    ) : (
                      <span className="text-gray-500">Not specified</span>
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    <div className="flex flex-col space-y-1">
                      <Badge color={expense.is_active ? 'green' : 'red'}>
                        {expense.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                      {expense.auto_create_payable && (
                        <Badge color="purple" size="sm">Auto-create</Badge>
                      )}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="xs"
                        color="success"
                        onClick={() => handleApprove(expense)}
                        disabled={!expense.is_active || submitting}
                        title="Send to Enhanced Payables"
                      >
                        <HiCheck className="h-3 w-3" />
                      </Button>
                      <Button
                        size="xs"
                        color="failure"
                        onClick={() => handleReject(expense)}
                        disabled={submitting}
                        title="Reject"
                      >
                        <HiX className="h-3 w-3" />
                      </Button>
                      <Button
                        size="xs"
                        color="gray"
                        onClick={() => handleEdit(expense)}
                        title="Edit"
                      >
                        <HiPencil className="h-3 w-3" />
                      </Button>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>

        {recurringExpenses.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No recurring expenses found. Create your first recurring expense template to get started.
          </div>
        )}

        {/* Pagination */}
        {recurringExpenses.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalCount}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="recurring expenses"
          />
        )}
      </Card>

      {/* Create/Edit Modal */}
      <Modal show={showModal} onClose={handleModalClose} size="xl">
        <Modal.Header>
          {editingExpense ? 'Edit Recurring Expense' : 'Create Recurring Expense'}
        </Modal.Header>
        <Modal.Body>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Name *</label>
                <TextInput
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                  placeholder="e.g., Office Rent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Expense Type</label>
                <Select
                  value={formData.expense_type_id}
                  onChange={(e) => setFormData({ ...formData, expense_type_id: e.target.value })}
                >
                  <option value="">Select expense type</option>
                  {expenseTypes.map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name} ({type.code})
                    </option>
                  ))}
                </Select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Description</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Optional description"
                rows={3}
              />
            </div>

            {/* Payee Selection - Supplier OR Employee */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Supplier</label>
                <Select
                  value={formData.supplier_id}
                  onChange={(e) => setFormData({
                    ...formData,
                    supplier_id: e.target.value,
                    employee_id: '' // Clear employee when supplier is selected
                  })}
                >
                  <option value="">Select supplier</option>
                  {suppliers.map(supplier => (
                    <option key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </option>
                  ))}
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Employee</label>
                <Select
                  value={formData.employee_id}
                  onChange={(e) => setFormData({
                    ...formData,
                    employee_id: e.target.value,
                    supplier_id: '' // Clear supplier when employee is selected
                  })}
                >
                  <option value="">Select employee</option>
                  {employees.map(employee => (
                    <option key={employee.id} value={employee.id}>
                      {employee.first_name} {employee.last_name}
                    </option>
                  ))}
                </Select>
              </div>
            </div>

            <div className="text-sm text-gray-500 mb-4">
              <strong>Note:</strong> Please select either a supplier OR an employee (not both).
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Amount (₱) *</label>
                <TextInput
                  type="number"
                  value={formData.amount}
                  onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
                  required
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">VAT Amount (₱)</label>
                <TextInput
                  type="number"
                  value={formData.vat_amount}
                  onChange={(e) => setFormData({ ...formData, vat_amount: parseFloat(e.target.value) || 0 })}
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Withholding Tax (%)</label>
                <TextInput
                  type="number"
                  value={formData.withholding_tax_rate}
                  onChange={(e) => setFormData({ ...formData, withholding_tax_rate: parseFloat(e.target.value) || 0 })}
                  min="0"
                  max="100"
                  step="0.01"
                  placeholder="0.00"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Frequency *</label>
                <Select
                  value={formData.frequency}
                  onChange={(e) => setFormData({ ...formData, frequency: e.target.value as RecurrenceFrequency })}
                  required
                >
                  {Object.values(RecurrenceFrequency).map(frequency => (
                    <option key={frequency} value={frequency}>
                      {formatFrequency(frequency)}
                    </option>
                  ))}
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Payment Terms (Days)</label>
                <TextInput
                  type="number"
                  value={formData.payment_terms_days}
                  onChange={(e) => setFormData({ ...formData, payment_terms_days: parseInt(e.target.value) || 30 })}
                  min="0"
                  placeholder="30"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Start Date *</label>
                <TextInput
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">End Date (Optional)</label>
                <TextInput
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Checkbox
                checked={formData.auto_create_payable}
                onChange={(e) => setFormData({ ...formData, auto_create_payable: e.target.checked })}
              />
              <label className="text-sm font-medium">Auto-create payables when due</label>
            </div>

            {error && (
              <Alert color="failure">
                {error}
              </Alert>
            )}
          </form>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={handleSubmit} disabled={submitting} className="bg-primary">
            {submitting ? <Spinner size="sm" className="mr-2" /> : null}
            {editingExpense ? 'Update' : 'Create'}
          </Button>
          <Button color="gray" onClick={handleModalClose}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Confirmation Modal */}
      <Modal show={showConfirmModal} onClose={() => setShowConfirmModal(false)}>
        <Modal.Header>
          {confirmAction === 'approve' ? 'Send to Enhanced Payables' : 'Reject Recurring Expense'}
        </Modal.Header>
        <Modal.Body>
          {selectedExpense && (
            <div className="space-y-4">
              <p className="text-sm text-gray-700">
                {confirmAction === 'approve'
                  ? `Are you sure you want to create a payable from "${selectedExpense.name}"? This will create a payable in Enhanced Payables for approval.`
                  : `Are you sure you want to reject "${selectedExpense.name}"?`
                }
              </p>

              {confirmAction === 'approve' && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Payable Details:</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <div><strong>Amount:</strong> ₱{selectedExpense.amount.toLocaleString()}</div>
                    <div><strong>Frequency:</strong> {formatFrequency(selectedExpense.frequency)}</div>
                    <div><strong>Payee:</strong> {
                      selectedExpense.supplier ? selectedExpense.supplier.name :
                      selectedExpense.employee ? `${selectedExpense.employee.first_name} ${selectedExpense.employee.last_name}` :
                      'Not specified'
                    }</div>
                    <div className="mt-2 text-xs text-blue-700 bg-blue-100 p-2 rounded">
                      <strong>Workflow:</strong> This will create a payable in Enhanced Payables (pending approval) → Approve/Reject → Traditional Accounts Payable
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            color={confirmAction === 'approve' ? 'success' : 'failure'}
            onClick={handleConfirmAction}
            disabled={submitting}
          >
            {submitting ? <Spinner size="sm" className="mr-2" /> : null}
            {confirmAction === 'approve' ? 'Send to Enhanced Payables' : 'Reject'}
          </Button>
          <Button color="gray" onClick={() => setShowConfirmModal(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default RecurringExpenses;
