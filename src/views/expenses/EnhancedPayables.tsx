import React, { useState, useEffect } from 'react';
import { Card, But<PERSON>, Table, Badge, Spinner, Alert, Modal, TextInput, Select, Textarea } from 'flowbite-react';
import { HiPlus, HiPencil, <PERSON><PERSON>ye, <PERSON>Check, HiX, HiExclamation } from 'react-icons/hi';
import Pagination from '../../components/common/Pagination';
import { useAuth } from '../../context/AuthContext';
import { useOrganization } from '../../context/OrganizationContext';
import {
  createEnhancedPayable,
  getExpenseTypes,
  getEnhancedPayables,
  approvePayable,
  rejectPayable,
  getPayableDetails
} from '../../services/operationalExpenses';
import { getSuppliers } from '../../services/supplier';
import { getEmployees } from '../../services/employee';
import {
  EnhancedPayable,
  CreateEnhancedPayableRequest,
  EnhancedPayableFilters,
  ExpenseType,
  EnhancedPayableSourceType,
  PayableStatus,
  ApprovalStatus
} from '../../types/operationalExpenses.types';

const EnhancedPayables: React.FC = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  // State management
  const [payables, setPayables] = useState<EnhancedPayable[]>([]);
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedPayable, setSelectedPayable] = useState<EnhancedPayable | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [filters, setFilters] = useState<EnhancedPayableFilters>({});

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Form state
  const [formData, setFormData] = useState<CreateEnhancedPayableRequest>({
    source_type: EnhancedPayableSourceType.MANUAL_ENTRY,
    source_id: '',
    supplier_id: '',
    employee_id: '',
    reference_number: '',
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: '',
    amount: 0,
    vat_amount: 0,
    withholding_tax_rate: 0,
    withholding_tax_amount: 0,
    currency: 'PHP',
    category: '',
    invoice_url: '',
    notes: '',
    expense_type_id: '',
    department: '',
    project_code: ''
  });

  // Load data
  const loadData = async () => {
    if (!currentOrganization) return;

    try {
      setLoading(true);

      // Load all required data in parallel
      const [payablesResult, typesResult, suppliersResult, employeesResult] = await Promise.all([
        getEnhancedPayables(currentOrganization.id, filters),
        getExpenseTypes(currentOrganization.id, { is_active: true }),
        getSuppliers(currentOrganization.id),
        getEmployees(currentOrganization.id)
      ]);

      if (payablesResult.success && payablesResult.data) {
        setPayables(payablesResult.data);
        setTotalCount(payablesResult.data.length);
      } else {
        setError(payablesResult.error || 'Failed to load payables');
      }

      if (typesResult.success && typesResult.data) {
        setExpenseTypes(typesResult.data);
      }

      if (suppliersResult.suppliers) {
        setSuppliers(suppliersResult.suppliers);
      }

      if (employeesResult.employees) {
        setEmployees(employeesResult.employees);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [currentOrganization, filters]);

  // Calculate pagination
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedPayables = payables.slice(startIndex, endIndex);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentOrganization || !user) return;

    try {
      setSubmitting(true);
      setError(null);

      // Generate source_id for manual entries
      const sourceId = formData.source_type === EnhancedPayableSourceType.MANUAL_ENTRY
        ? `manual-${Date.now()}`
        : formData.source_id;

      // Calculate due date if not provided
      const dueDate = formData.due_date || (() => {
        const date = new Date(formData.invoice_date);
        date.setDate(date.getDate() + 30); // Default 30 days
        return date.toISOString().split('T')[0];
      })();

      // Calculate withholding tax amount
      const withholdingTaxAmount = formData.withholding_tax_rate > 0
        ? (formData.amount * formData.withholding_tax_rate) / 100
        : 0;

      const cleanFormData = {
        ...formData,
        source_id: sourceId,
        due_date: dueDate,
        withholding_tax_amount: withholdingTaxAmount,
        supplier_id: formData.supplier_id || null,
        employee_id: formData.employee_id || null,
        expense_type_id: formData.expense_type_id || undefined
      };

      const result = await createEnhancedPayable(currentOrganization.id, cleanFormData, user.id);

      if (result.success) {
        setShowModal(false);
        resetForm();
        loadData();
      } else {
        setError(result.error || 'Failed to create payable');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      source_type: EnhancedPayableSourceType.MANUAL_ENTRY,
      source_id: '',
      supplier_id: '',
      employee_id: '',
      reference_number: '',
      invoice_date: new Date().toISOString().split('T')[0],
      due_date: '',
      amount: 0,
      vat_amount: 0,
      withholding_tax_rate: 0,
      withholding_tax_amount: 0,
      currency: 'PHP',
      category: '',
      invoice_url: '',
      notes: '',
      expense_type_id: '',
      department: '',
      project_code: ''
    });
  };

  // Handle modal close
  const handleModalClose = () => {
    setShowModal(false);
    resetForm();
    setError(null);
  };

  // Format source type for display
  const formatSourceType = (sourceType: EnhancedPayableSourceType) => {
    return sourceType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Get status badge color
  const getStatusBadgeColor = (status: PayableStatus) => {
    switch (status) {
      case PayableStatus.PAID: return 'green';
      case PayableStatus.PARTIALLY_PAID: return 'yellow';
      case PayableStatus.OPEN: return 'blue';
      case PayableStatus.DRAFT: return 'gray';
      case PayableStatus.CANCELLED: return 'red';
      default: return 'gray';
    }
  };

  // Get approval status badge color
  const getApprovalBadgeColor = (status: ApprovalStatus) => {
    switch (status) {
      case ApprovalStatus.APPROVED: return 'green';
      case ApprovalStatus.PENDING: return 'yellow';
      case ApprovalStatus.REJECTED: return 'red';
      case ApprovalStatus.REQUIRES_HIGHER_APPROVAL: return 'purple';
      default: return 'gray';
    }
  };

  // Check if payable is overdue
  const isOverdue = (dueDate: string, status: PayableStatus) => {
    if (status === PayableStatus.PAID) return false;
    const due = new Date(dueDate);
    const today = new Date();
    return due < today;
  };

  // Action handlers
  const handleViewDetails = async (payable: EnhancedPayable) => {
    setSelectedPayable(payable);
    setShowDetailsModal(true);
  };

  const handleApprove = async (payable: EnhancedPayable) => {
    if (!user) return;

    try {
      setSubmitting(true);
      const result = await approvePayable(payable.id, user.id);

      if (result.success) {
        loadData(); // Refresh the list
      } else {
        setError(result.error || 'Failed to approve payable');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  const handleReject = (payable: EnhancedPayable) => {
    setSelectedPayable(payable);
    setShowRejectModal(true);
  };

  const handleRejectConfirm = async () => {
    if (!selectedPayable || !user || !rejectionReason.trim()) return;

    try {
      setSubmitting(true);
      const result = await rejectPayable(selectedPayable.id, user.id, rejectionReason);

      if (result.success) {
        setShowRejectModal(false);
        setRejectionReason('');
        setSelectedPayable(null);
        loadData(); // Refresh the list
      } else {
        setError(result.error || 'Failed to reject payable');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Enhanced Payables</h1>
            <p className="text-gray-600 dark:text-gray-400">Manage operational expenses and payables</p>
          </div>
          <Button onClick={() => setShowModal(true)} className="bg-primary">
            <HiPlus className="mr-2 h-4 w-4" />
            Create Payable
          </Button>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mb-6">
          <Select
            value={filters.status || ''}
            onChange={(e) => setFilters({ ...filters, status: e.target.value as PayableStatus || undefined })}
          >
            <option value="">All Status</option>
            {Object.values(PayableStatus).map(status => (
              <option key={status} value={status}>
                {status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </option>
            ))}
          </Select>

          <Select
            value={filters.approval_status || ''}
            onChange={(e) => setFilters({ ...filters, approval_status: e.target.value as ApprovalStatus || undefined })}
          >
            <option value="">All Approval Status</option>
            {Object.values(ApprovalStatus).map(status => (
              <option key={status} value={status}>
                {status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </option>
            ))}
          </Select>

          <Select
            value={filters.source_type || ''}
            onChange={(e) => setFilters({ ...filters, source_type: e.target.value as EnhancedPayableSourceType || undefined })}
          >
            <option value="">All Source Types</option>
            {Object.values(EnhancedPayableSourceType)
              .filter(type => type !== EnhancedPayableSourceType.RECURRING_EXPENSE)
              .map(type => (
                <option key={type} value={type}>
                  {formatSourceType(type)}
                </option>
              ))}
          </Select>

          <div>
            <label className="block text-sm font-medium mb-1">From Date</label>
            <TextInput
              type="date"
              value={filters.date_from || ''}
              onChange={(e) => setFilters({ ...filters, date_from: e.target.value || undefined })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">To Date</label>
            <TextInput
              type="date"
              value={filters.date_to || ''}
              onChange={(e) => setFilters({ ...filters, date_to: e.target.value || undefined })}
            />
          </div>

          <TextInput
            placeholder="Search payables..."
            value={filters.search || ''}
            onChange={(e) => setFilters({ ...filters, search: e.target.value || undefined })}
          />
        </div>

        {/* Error Alert */}
        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}

        {/* Payables Table */}
        <div className="overflow-x-auto">
          <Table>
            <Table.Head>
              <Table.HeadCell>Reference</Table.HeadCell>
              <Table.HeadCell>Payee</Table.HeadCell>
              <Table.HeadCell>Amount</Table.HeadCell>
              <Table.HeadCell>Due Date</Table.HeadCell>
              <Table.HeadCell>Status</Table.HeadCell>
              <Table.HeadCell>Approval</Table.HeadCell>
              <Table.HeadCell>Actions</Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y">
              {paginatedPayables.map((payable) => (
                <Table.Row key={payable.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                  <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                    <div>
                      <div className="font-semibold">{payable.reference_number}</div>
                      <div className="text-sm text-gray-500">
                        {formatSourceType(payable.source_type)}
                      </div>
                      {payable.expense_type && (
                        <Badge color="gray" size="sm" className="mt-1">
                          {payable.expense_type.name}
                        </Badge>
                      )}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    {payable.supplier ? (
                      <div>
                        <div className="font-medium">{payable.supplier.name}</div>
                        <div className="text-sm text-gray-500">Supplier</div>
                      </div>
                    ) : payable.employee ? (
                      <div>
                        <div className="font-medium">
                          {payable.employee.first_name} {payable.employee.last_name}
                        </div>
                        <div className="text-sm text-gray-500">Employee</div>
                      </div>
                    ) : (
                      <span className="text-gray-500">Not specified</span>
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    <div className="font-semibold">₱{payable.amount.toLocaleString()}</div>
                    <div className="text-sm text-gray-500">
                      Balance: ₱{payable.balance.toLocaleString()}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div className={`font-medium ${
                      isOverdue(payable.due_date, payable.status) ? 'text-red-600' : 'text-gray-900 dark:text-white'
                    }`}>
                      {formatDate(payable.due_date)}
                    </div>
                    {isOverdue(payable.due_date, payable.status) && (
                      <Badge color="failure" size="sm">Overdue</Badge>
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    <Badge color={getStatusBadgeColor(payable.status)}>
                      {payable.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <Badge color={getApprovalBadgeColor(payable.approval_status)}>
                      {payable.approval_status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="xs"
                        color="gray"
                        title="View Details"
                        onClick={() => handleViewDetails(payable)}
                      >
                        <HiEye className="h-3 w-3" />
                      </Button>
                      {payable.approval_status === ApprovalStatus.PENDING && (
                        <>
                          <Button
                            size="xs"
                            color="success"
                            title="Approve"
                            onClick={() => handleApprove(payable)}
                            disabled={submitting}
                          >
                            <HiCheck className="h-3 w-3" />
                          </Button>
                          <Button
                            size="xs"
                            color="failure"
                            title="Reject"
                            onClick={() => handleReject(payable)}
                            disabled={submitting}
                          >
                            <HiX className="h-3 w-3" />
                          </Button>
                        </>
                      )}
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>

        {payables.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No payables found. Create your first payable to get started.
          </div>
        )}

        {/* Pagination */}
        {payables.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalCount}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="payables"
          />
        )}
      </Card>

      {/* Create Modal */}
      <Modal show={showModal} onClose={handleModalClose} size="xl">
        <Modal.Header>
          Create New Payable
        </Modal.Header>
        <Modal.Body>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Source Type *</label>
                <Select
                  value={formData.source_type}
                  onChange={(e) => setFormData({ ...formData, source_type: e.target.value as EnhancedPayableSourceType })}
                  required
                >
                  {Object.values(EnhancedPayableSourceType).map(type => (
                    <option key={type} value={type}>
                      {formatSourceType(type)}
                    </option>
                  ))}
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Reference Number *</label>
                <TextInput
                  value={formData.reference_number}
                  onChange={(e) => setFormData({ ...formData, reference_number: e.target.value })}
                  required
                  placeholder="e.g., INV-2024-001"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Expense Type</label>
                <Select
                  value={formData.expense_type_id}
                  onChange={(e) => setFormData({ ...formData, expense_type_id: e.target.value })}
                >
                  <option value="">Select expense type</option>
                  {expenseTypes.map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name} ({type.code})
                    </option>
                  ))}
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Department</label>
                <TextInput
                  value={formData.department}
                  onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                  placeholder="e.g., IT, HR, Finance"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Supplier</label>
                <Select
                  value={formData.supplier_id}
                  onChange={(e) => setFormData({ ...formData, supplier_id: e.target.value, employee_id: '' })}
                >
                  <option value="">Select supplier</option>
                  {suppliers.map(supplier => (
                    <option key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </option>
                  ))}
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Employee</label>
                <Select
                  value={formData.employee_id}
                  onChange={(e) => setFormData({ ...formData, employee_id: e.target.value, supplier_id: '' })}
                >
                  <option value="">Select employee</option>
                  {employees.map(employee => (
                    <option key={employee.id} value={employee.id}>
                      {employee.first_name} {employee.last_name}
                    </option>
                  ))}
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Amount (₱) *</label>
                <TextInput
                  type="number"
                  value={formData.amount}
                  onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
                  required
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">VAT Amount (₱)</label>
                <TextInput
                  type="number"
                  value={formData.vat_amount}
                  onChange={(e) => setFormData({ ...formData, vat_amount: parseFloat(e.target.value) || 0 })}
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Withholding Tax (%)</label>
                <TextInput
                  type="number"
                  value={formData.withholding_tax_rate}
                  onChange={(e) => setFormData({ ...formData, withholding_tax_rate: parseFloat(e.target.value) || 0 })}
                  min="0"
                  max="100"
                  step="0.01"
                  placeholder="0.00"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Invoice Date *</label>
                <TextInput
                  type="date"
                  value={formData.invoice_date}
                  onChange={(e) => setFormData({ ...formData, invoice_date: e.target.value })}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Due Date</label>
                <TextInput
                  type="date"
                  value={formData.due_date}
                  onChange={(e) => setFormData({ ...formData, due_date: e.target.value })}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Notes</label>
              <Textarea
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="Optional notes"
                rows={3}
              />
            </div>

            {error && (
              <Alert color="failure">
                {error}
              </Alert>
            )}
          </form>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={handleSubmit} disabled={submitting} className="bg-primary">
            {submitting ? <Spinner size="sm" className="mr-2" /> : null}
            Create Payable
          </Button>
          <Button color="gray" onClick={handleModalClose}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Details Modal */}
      <Modal show={showDetailsModal} onClose={() => setShowDetailsModal(false)} size="xl">
        <Modal.Header>
          Payable Details
        </Modal.Header>
        <Modal.Body>
          {selectedPayable && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Reference Number</label>
                  <p className="text-sm text-gray-900">{selectedPayable.reference_number}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Source Type</label>
                  <p className="text-sm text-gray-900">{formatSourceType(selectedPayable.source_type)}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Amount</label>
                  <p className="text-sm text-gray-900">₱{selectedPayable.amount.toLocaleString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Balance</label>
                  <p className="text-sm text-gray-900">₱{selectedPayable.balance.toLocaleString()}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <Badge color={getStatusBadgeColor(selectedPayable.status)}>
                    {selectedPayable.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Badge>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Approval Status</label>
                  <Badge color={getApprovalBadgeColor(selectedPayable.approval_status)}>
                    {selectedPayable.approval_status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Badge>
                </div>
              </div>

              {selectedPayable.notes && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Notes</label>
                  <p className="text-sm text-gray-900">{selectedPayable.notes}</p>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowDetailsModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Reject Modal */}
      <Modal show={showRejectModal} onClose={() => setShowRejectModal(false)}>
        <Modal.Header>
          Reject Payable
        </Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p className="text-sm text-gray-700">
              Are you sure you want to reject this payable? Please provide a reason for rejection.
            </p>
            <div>
              <label className="block text-sm font-medium mb-2">Rejection Reason *</label>
              <Textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Please provide a reason for rejection..."
                rows={3}
                required
              />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="failure"
            onClick={handleRejectConfirm}
            disabled={submitting || !rejectionReason.trim()}
          >
            {submitting ? <Spinner size="sm" className="mr-2" /> : null}
            Reject Payable
          </Button>
          <Button color="gray" onClick={() => setShowRejectModal(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default EnhancedPayables;
