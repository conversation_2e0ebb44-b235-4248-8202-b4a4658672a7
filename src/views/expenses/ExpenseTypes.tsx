import React, { useState, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Table, Badge, Spinner, Alert, Modal, TextInput, Select, Textarea, Checkbox } from 'flowbite-react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>cil, Hi<PERSON><PERSON>, HiEye } from 'react-icons/hi';
import Pagination from '../../components/common/Pagination';
import { useAuth } from '../../context/AuthContext';
import { useOrganization } from '../../context/OrganizationContext';
import {
  getExpenseTypes,
  createExpenseType,
  updateExpenseType,
  deleteExpenseType
} from '../../services/operationalExpenses';
import {
  ExpenseType,
  CreateExpenseTypeRequest,
  ExpenseCategory,
  RecurrenceFrequency,
  ExpenseTypeFilters
} from '../../types/operationalExpenses.types';
import ExpenseTypeCollections from '../../components/expenses/ExpenseTypeCollections';

const ExpenseTypes: React.FC = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  // State management
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [editingExpenseType, setEditingExpenseType] = useState<ExpenseType | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [filters, setFilters] = useState<ExpenseTypeFilters>({});

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Form state
  const [formData, setFormData] = useState<CreateExpenseTypeRequest>({
    name: '',
    code: '',
    description: '',
    category: ExpenseCategory.OPERATIONAL,
    requires_approval: true,
    approval_limit: 0,
    default_account_code: '',
    is_recurring_type: false,
    default_frequency: undefined
  });

  // Load expense types
  const loadExpenseTypes = async () => {
    if (!currentOrganization) return;

    try {
      setLoading(true);
      const result = await getExpenseTypes(currentOrganization.id, filters);

      if (result.success && result.data) {
        setExpenseTypes(result.data);
        setTotalCount(result.data.length);
      } else {
        setError(result.error || 'Failed to load expense types');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadExpenseTypes();
  }, [currentOrganization, filters]);

  // Calculate pagination
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedExpenseTypes = expenseTypes.slice(startIndex, endIndex);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentOrganization || !user) return;

    try {
      setSubmitting(true);
      setError(null);

      let result;
      if (editingExpenseType) {
        result = await updateExpenseType(editingExpenseType.id, formData);
      } else {
        result = await createExpenseType(currentOrganization.id, formData, user.id);
      }

      if (result.success) {
        setShowModal(false);
        setEditingExpenseType(null);
        resetForm();
        loadExpenseTypes();
      } else {
        setError(result.error || 'Failed to save expense type');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle edit
  const handleEdit = (expenseType: ExpenseType) => {
    setEditingExpenseType(expenseType);
    setFormData({
      name: expenseType.name,
      code: expenseType.code,
      description: expenseType.description || '',
      category: expenseType.category,
      requires_approval: expenseType.requires_approval,
      approval_limit: expenseType.approval_limit,
      default_account_code: expenseType.default_account_code || '',
      is_recurring_type: expenseType.is_recurring_type,
      default_frequency: expenseType.default_frequency
    });
    setShowModal(true);
  };

  // Handle delete
  const handleDelete = async (expenseType: ExpenseType) => {
    if (!confirm(`Are you sure you want to delete "${expenseType.name}"?`)) return;

    try {
      const result = await deleteExpenseType(expenseType.id);
      if (result.success) {
        loadExpenseTypes();
      } else {
        setError(result.error || 'Failed to delete expense type');
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      description: '',
      category: ExpenseCategory.OPERATIONAL,
      requires_approval: true,
      approval_limit: 0,
      default_account_code: '',
      is_recurring_type: false,
      default_frequency: undefined
    });
  };

  // Handle modal close
  const handleModalClose = () => {
    setShowModal(false);
    setEditingExpenseType(null);
    resetForm();
    setError(null);
  };

  // Format category for display
  const formatCategory = (category: ExpenseCategory) => {
    return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Format frequency for display
  const formatFrequency = (frequency?: RecurrenceFrequency) => {
    if (!frequency) return '-';
    return frequency.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Expense Types</h1>
            <p className="text-gray-600 dark:text-gray-400">Manage expense categories and approval workflows</p>
          </div>
          <div className="flex gap-2">
            <ExpenseTypeCollections
              onExpenseTypesAdded={loadExpenseTypes}
              className="flex-1"
            />
            <Button onClick={() => setShowModal(true)} className="bg-primary">
              <HiPlus className="mr-2 h-4 w-4" />
              Add Custom Type
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Select
            value={filters.category || ''}
            onChange={(e) => setFilters({ ...filters, category: e.target.value as ExpenseCategory || undefined })}
          >
            <option value="">All Categories</option>
            {Object.values(ExpenseCategory).map(category => (
              <option key={category} value={category}>
                {formatCategory(category)}
              </option>
            ))}
          </Select>

          <Select
            value={filters.is_active?.toString() || ''}
            onChange={(e) => setFilters({ ...filters, is_active: e.target.value ? e.target.value === 'true' : undefined })}
          >
            <option value="">All Status</option>
            <option value="true">Active</option>
            <option value="false">Inactive</option>
          </Select>

          <TextInput
            placeholder="Search expense types..."
            value={filters.search || ''}
            onChange={(e) => setFilters({ ...filters, search: e.target.value || undefined })}
          />
        </div>

        {/* Error Alert */}
        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}

        {/* Expense Types Table */}
        <div className="overflow-x-auto">
          <Table>
            <Table.Head>
              <Table.HeadCell>Name</Table.HeadCell>
              <Table.HeadCell>Code</Table.HeadCell>
              <Table.HeadCell>Category</Table.HeadCell>
              <Table.HeadCell>Approval Limit</Table.HeadCell>
              <Table.HeadCell>Recurring</Table.HeadCell>
              <Table.HeadCell>Status</Table.HeadCell>
              <Table.HeadCell>Actions</Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y">
              {paginatedExpenseTypes.map((expenseType) => (
                <Table.Row key={expenseType.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                  <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                    <div>
                      <div className="font-semibold">{expenseType.name}</div>
                      {expenseType.description && (
                        <div className="text-sm text-gray-500">{expenseType.description}</div>
                      )}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <Badge color="gray">{expenseType.code}</Badge>
                  </Table.Cell>
                  <Table.Cell>{formatCategory(expenseType.category)}</Table.Cell>
                  <Table.Cell>
                    {expenseType.requires_approval ? (
                      <span>₱{expenseType.approval_limit.toLocaleString()}</span>
                    ) : (
                      <span className="text-gray-500">No approval required</span>
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    {expenseType.is_recurring_type ? (
                      <div>
                        <Badge color="blue">Recurring</Badge>
                        <div className="text-xs text-gray-500 mt-1">
                          {formatFrequency(expenseType.default_frequency)}
                        </div>
                      </div>
                    ) : (
                      <Badge color="gray">One-time</Badge>
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    <Badge color={expenseType.is_active ? 'green' : 'red'}>
                      {expenseType.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="xs"
                        color="gray"
                        onClick={() => handleEdit(expenseType)}
                      >
                        <HiPencil className="h-3 w-3" />
                      </Button>
                      <Button
                        size="xs"
                        color="failure"
                        onClick={() => handleDelete(expenseType)}
                        disabled={!expenseType.is_active}
                      >
                        <HiTrash className="h-3 w-3" />
                      </Button>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>

        {expenseTypes.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No expense types found. Create your first expense type to get started.
          </div>
        )}

        {/* Pagination */}
        {expenseTypes.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalCount}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="expense types"
          />
        )}
      </Card>

      {/* Create/Edit Modal */}
      <Modal show={showModal} onClose={handleModalClose} size="lg">
        <Modal.Header>
          {editingExpenseType ? 'Edit Expense Type' : 'Create Expense Type'}
        </Modal.Header>
        <Modal.Body>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Name *</label>
                <TextInput
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                  placeholder="e.g., Office Rent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Code *</label>
                <TextInput
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                  required
                  placeholder="e.g., RENT"
                  maxLength={10}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Description</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Optional description"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Category *</label>
                <Select
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value as ExpenseCategory })}
                  required
                >
                  {Object.values(ExpenseCategory).map(category => (
                    <option key={category} value={category}>
                      {formatCategory(category)}
                    </option>
                  ))}
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Account Code</label>
                <TextInput
                  value={formData.default_account_code}
                  onChange={(e) => setFormData({ ...formData, default_account_code: e.target.value })}
                  placeholder="e.g., 5100"
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Checkbox
                checked={formData.requires_approval}
                onChange={(e) => setFormData({ ...formData, requires_approval: e.target.checked })}
              />
              <label className="text-sm font-medium">Requires Approval</label>
            </div>

            {formData.requires_approval && (
              <div>
                <label className="block text-sm font-medium mb-2">Approval Limit (₱)</label>
                <TextInput
                  type="number"
                  value={formData.approval_limit}
                  onChange={(e) => setFormData({ ...formData, approval_limit: parseFloat(e.target.value) || 0 })}
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                />
              </div>
            )}

            <div className="flex items-center space-x-4">
              <Checkbox
                checked={formData.is_recurring_type}
                onChange={(e) => setFormData({ ...formData, is_recurring_type: e.target.checked })}
              />
              <label className="text-sm font-medium">Can be Recurring</label>
            </div>

            {formData.is_recurring_type && (
              <div>
                <label className="block text-sm font-medium mb-2">Default Frequency</label>
                <Select
                  value={formData.default_frequency || ''}
                  onChange={(e) => setFormData({ ...formData, default_frequency: e.target.value as RecurrenceFrequency || undefined })}
                >
                  <option value="">Select frequency</option>
                  {Object.values(RecurrenceFrequency).map(frequency => (
                    <option key={frequency} value={frequency}>
                      {formatFrequency(frequency)}
                    </option>
                  ))}
                </Select>
              </div>
            )}

            {error && (
              <Alert color="failure">
                {error}
              </Alert>
            )}
          </form>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={handleSubmit} disabled={submitting} className="bg-primary">
            {submitting ? <Spinner size="sm" className="mr-2" /> : null}
            {editingExpenseType ? 'Update' : 'Create'}
          </Button>
          <Button color="gray" onClick={handleModalClose}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default ExpenseTypes;
