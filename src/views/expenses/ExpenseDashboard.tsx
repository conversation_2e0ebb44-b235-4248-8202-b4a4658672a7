import React, { useState, useEffect } from 'react';
import { <PERSON>, Badge, Spinner, <PERSON><PERSON>, <PERSON><PERSON>, Progress } from 'flowbite-react';
import { 
  HiOutlineExclamation, 
  HiOutlineClock, 
  HiOutlineRefresh, 
  HiOutlineDocumentText,
  HiOutlineTrendingUp,
  HiOutlineUsers,
  HiOutlineCalendar
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getExpenseDashboardData, getDueRecurringExpenses } from '../../services/operationalExpenses';
import { ExpenseDashboardData, RecurringExpense } from '../../types/operationalExpenses.types';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

const ExpenseDashboard: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();
  
  // State management
  const [dashboardData, setDashboardData] = useState<ExpenseDashboardData | null>(null);
  const [dueRecurringExpenses, setDueRecurringExpenses] = useState<RecurringExpense[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load dashboard data
  const loadDashboardData = async () => {
    if (!currentOrganization) return;

    try {
      setLoading(true);
      setError(null);
      
      const [dashboardResult, recurringResult] = await Promise.all([
        getExpenseDashboardData(currentOrganization.id),
        getDueRecurringExpenses(currentOrganization.id)
      ]);
      
      if (dashboardResult.success && dashboardResult.data) {
        setDashboardData(dashboardResult.data);
      } else {
        setError(dashboardResult.error || 'Failed to load dashboard data');
      }

      if (recurringResult.success && recurringResult.data) {
        setDueRecurringExpenses(recurringResult.data);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, [currentOrganization]);

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Check if expense is due soon (next 7 days)
  const isDueSoon = (nextDueDate: string) => {
    const due = new Date(nextDueDate);
    const today = new Date();
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7 && diffDays >= 0;
  };

  // Check if expense is overdue
  const isOverdue = (nextDueDate: string) => {
    const due = new Date(nextDueDate);
    const today = new Date();
    return due < today;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert color="failure" className="mb-4">
        {error}
      </Alert>
    );
  }

  if (!dashboardData) {
    return (
      <Alert color="info" className="mb-4">
        No dashboard data available.
      </Alert>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Expense Dashboard</h1>
          <p className="text-gray-600 dark:text-gray-400">Overview of operational expenses and pending actions</p>
        </div>
        <Button onClick={loadDashboardData} color="gray">
          <HiOutlineRefresh className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Pending Approvals */}
        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg">
                <HiOutlineExclamation className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Approvals</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardData.total_pending_approvals}
              </p>
            </div>
          </div>
        </Card>

        {/* Overdue Payments */}
        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-lg">
                <HiOutlineClock className="w-6 h-6 text-red-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Overdue Payments</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardData.total_overdue_payments}
              </p>
            </div>
          </div>
        </Card>

        {/* Upcoming Recurring */}
        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg">
                <HiOutlineCalendar className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Due Soon (30 days)</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardData.upcoming_recurring_expenses}
              </p>
            </div>
          </div>
        </Card>

        {/* Total Categories */}
        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg">
                <HiOutlineDocumentText className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Active Categories</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardData.expense_by_category.length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Expense by Category */}
        <Card>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Expenses by Category (Last 6 Months)
            </h3>
            <HiOutlineTrendingUp className="w-5 h-5 text-gray-400" />
          </div>
          
          {dashboardData.expense_by_category.length > 0 ? (
            <div className="space-y-4">
              {dashboardData.expense_by_category
                .sort((a, b) => b.amount - a.amount)
                .slice(0, 8) // Show top 8 categories
                .map((category, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {category.category}
                      </span>
                      <div className="text-right">
                        <span className="text-sm font-semibold text-gray-900 dark:text-white">
                          {formatWithCurrency(category.amount)}
                        </span>
                        <span className="text-xs text-gray-500 ml-2">
                          {category.percentage.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                    <Progress
                      progress={category.percentage}
                      color="blue"
                      size="sm"
                    />
                  </div>
                ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No expense data available for the last 6 months.
            </div>
          )}
        </Card>

        {/* Due Recurring Expenses */}
        <Card>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Upcoming Recurring Expenses
            </h3>
            <HiOutlineCalendar className="w-5 h-5 text-gray-400" />
          </div>
          
          {dueRecurringExpenses.length > 0 ? (
            <div className="space-y-3">
              {dueRecurringExpenses.slice(0, 10).map((expense) => (
                <div key={expense.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {expense.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {expense.supplier?.name || 'No supplier'}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-gray-900 dark:text-white">
                      {formatWithCurrency(expense.amount)}
                    </div>
                    <div className={`text-sm ${
                      isOverdue(expense.next_due_date) ? 'text-red-600' :
                      isDueSoon(expense.next_due_date) ? 'text-yellow-600' :
                      'text-gray-500'
                    }`}>
                      {formatDate(expense.next_due_date)}
                    </div>
                  </div>
                  <div className="ml-3">
                    {isOverdue(expense.next_due_date) && (
                      <Badge color="failure" size="sm">Overdue</Badge>
                    )}
                    {isDueSoon(expense.next_due_date) && !isOverdue(expense.next_due_date) && (
                      <Badge color="warning" size="sm">Due Soon</Badge>
                    )}
                    {!isDueSoon(expense.next_due_date) && !isOverdue(expense.next_due_date) && (
                      <Badge color="gray" size="sm">Upcoming</Badge>
                    )}
                  </div>
                </div>
              ))}
              
              {dueRecurringExpenses.length > 10 && (
                <div className="text-center text-sm text-gray-500 pt-2">
                  And {dueRecurringExpenses.length - 10} more...
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No recurring expenses due in the next 30 days.
            </div>
          )}
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="mt-8">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button color="blue" className="justify-center">
            <HiOutlineDocumentText className="mr-2 h-4 w-4" />
            Create New Expense
          </Button>
          <Button color="yellow" className="justify-center">
            <HiOutlineExclamation className="mr-2 h-4 w-4" />
            Review Pending Approvals
          </Button>
          <Button color="green" className="justify-center">
            <HiOutlineCalendar className="mr-2 h-4 w-4" />
            Process Due Expenses
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default ExpenseDashboard;
