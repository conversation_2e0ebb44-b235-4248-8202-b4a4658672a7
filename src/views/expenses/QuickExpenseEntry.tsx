import React, { useState, useEffect } from 'react';
import { Card, Button, TextInput, Select, Textarea, Alert, Spinner, Badge } from 'flowbite-react';
import { HiPlus, HiSave, HiX, HiUpload } from 'react-icons/hi';
import { useAuth } from '../../context/AuthContext';
import { useOrganization } from '../../context/OrganizationContext';
import { createEnhancedPayable, getExpenseTypes } from '../../services/operationalExpenses';
import { getSuppliers } from '../../services/supplier';
import { createPayment } from '../../services/payables';
import {
  CreateEnhancedPayableRequest,
  ExpenseType,
  EnhancedPayableSourceType
} from '../../types/operationalExpenses.types';
import EnhancedNumberInput from '../../components/common/EnhancedNumberInput';

interface QuickExpenseEntryProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  prefilledData?: Partial<CreateEnhancedPayableRequest>;
}

const QuickExpenseEntry: React.FC<QuickExpenseEntryProps> = ({
  onSuccess,
  onCancel,
  prefilledData
}) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  // State management
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<CreateEnhancedPayableRequest>({
    source_type: EnhancedPayableSourceType.MANUAL_ENTRY,
    source_id: '',
    supplier_id: '',
    employee_id: '',
    reference_number: '',
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: '',
    amount: 0,
    vat_amount: 0,
    withholding_tax_rate: 0,
    withholding_tax_amount: 0,
    currency: 'PHP',
    category: '',
    invoice_url: '',
    notes: '',
    expense_type_id: '',
    department: '',
    project_code: '',
    ...prefilledData
  });

  // Payment state for already-paid expenses
  const [paymentStatus, setPaymentStatus] = useState<'unpaid' | 'paid'>('unpaid');
  const [paymentData, setPaymentData] = useState({
    payment_date: new Date().toISOString().split('T')[0],
    payment_method: 'cash' as const,
    reference_number: '',
    remarks: ''
  });

  // Load reference data
  const loadReferenceData = async () => {
    if (!currentOrganization) return;

    try {
      setLoading(true);

      const [typesResult, suppliersResult] = await Promise.all([
        getExpenseTypes(currentOrganization.id, { is_active: true }),
        getSuppliers(currentOrganization.id)
      ]);

      if (typesResult.success && typesResult.data) {
        setExpenseTypes(typesResult.data);
      }

      if (suppliersResult.suppliers) {
        setSuppliers(suppliersResult.suppliers);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadReferenceData();
  }, [currentOrganization]);

  // Auto-generate reference number
  useEffect(() => {
    if (!formData.reference_number && formData.expense_type_id) {
      const expenseType = expenseTypes.find(t => t.id === formData.expense_type_id);
      if (expenseType) {
        const timestamp = Date.now().toString().slice(-6);
        setFormData(prev => ({
          ...prev,
          reference_number: `${expenseType.code}-${timestamp}`
        }));
      }
    }
  }, [formData.expense_type_id, expenseTypes]);

  // Auto-calculate due date based on supplier payment terms
  useEffect(() => {
    if (formData.supplier_id && formData.invoice_date && !formData.due_date) {
      const supplier = suppliers.find(s => s.id === formData.supplier_id);
      const paymentTerms = supplier?.payment_terms_days || 30;

      const invoiceDate = new Date(formData.invoice_date);
      const dueDate = new Date(invoiceDate);
      dueDate.setDate(dueDate.getDate() + paymentTerms);

      setFormData(prev => ({
        ...prev,
        due_date: dueDate.toISOString().split('T')[0]
      }));
    }
  }, [formData.supplier_id, formData.invoice_date, suppliers]);

  // Auto-calculate withholding tax amount
  useEffect(() => {
    if (formData.withholding_tax_rate > 0 && formData.amount > 0) {
      const withholdingAmount = (formData.amount * formData.withholding_tax_rate) / 100;
      setFormData(prev => ({
        ...prev,
        withholding_tax_amount: withholdingAmount
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        withholding_tax_amount: 0
      }));
    }
  }, [formData.amount, formData.withholding_tax_rate]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentOrganization || !user) return;

    try {
      setSubmitting(true);
      setError(null);
      setSuccess(null);

      // Validation
      if (!formData.reference_number) {
        setError('Reference number is required');
        return;
      }
      if (!formData.amount || formData.amount <= 0) {
        setError('Amount must be greater than 0');
        return;
      }
      if (!formData.supplier_id && !formData.employee_id) {
        setError('Please select either a supplier or employee');
        return;
      }

      // Generate source_id for manual entries
      const sourceId = `manual-${Date.now()}`;

      // Calculate due date if not provided
      const dueDate = formData.due_date || (() => {
        const date = new Date(formData.invoice_date);
        date.setDate(date.getDate() + 30); // Default 30 days
        return date.toISOString().split('T')[0];
      })();

      const cleanFormData = {
        ...formData,
        source_id: sourceId,
        due_date: dueDate,
        supplier_id: formData.supplier_id || null,
        employee_id: formData.employee_id || null,
        expense_type_id: formData.expense_type_id || undefined
      };

      const result = await createEnhancedPayable(currentOrganization.id, cleanFormData, user.id);

      if (result.success && result.data) {
        // If expense is already paid, create payment record
        if (paymentStatus === 'paid') {
          const paymentResult = await createPayment(
            currentOrganization.id,
            {
              payable_id: result.data.id,
              payment_date: paymentData.payment_date,
              amount_paid: formData.amount,
              payment_method: paymentData.payment_method as any,
              reference_number: paymentData.reference_number,
              attachment_url: '',
              remarks: paymentData.remarks || 'Payment recorded during expense entry'
            },
            user.id
          );

          if (paymentResult.success) {
            setSuccess('Expense created and payment recorded successfully!');
          } else {
            setSuccess('Expense created but failed to record payment. Please add payment manually.');
          }
        } else {
          setSuccess('Expense created successfully!');
        }

        // Reset form for next entry
        setFormData({
          source_type: EnhancedPayableSourceType.MANUAL_ENTRY,
          source_id: '',
          supplier_id: formData.supplier_id, // Keep supplier selected
          employee_id: '',
          reference_number: '',
          invoice_date: new Date().toISOString().split('T')[0],
          due_date: '',
          amount: 0,
          vat_amount: 0,
          withholding_tax_rate: formData.withholding_tax_rate, // Keep tax rate
          withholding_tax_amount: 0,
          currency: 'PHP',
          category: '',
          invoice_url: '',
          notes: '',
          expense_type_id: formData.expense_type_id, // Keep expense type
          department: formData.department, // Keep department
          project_code: '',
          ...prefilledData
        });

        // Reset payment data
        setPaymentStatus('unpaid');
        setPaymentData({
          payment_date: new Date().toISOString().split('T')[0],
          payment_method: 'cash',
          reference_number: '',
          remarks: ''
        });

        if (onSuccess) {
          onSuccess();
        }
      } else {
        setError(result.error || 'Failed to create expense');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle expense type change
  const handleExpenseTypeChange = (expenseTypeId: string) => {
    const expenseType = expenseTypes.find(t => t.id === expenseTypeId);
    setFormData(prev => ({
      ...prev,
      expense_type_id: expenseTypeId,
      category: expenseType?.category || '',
      withholding_tax_rate: expenseType?.category === 'professional_services' ? 10 : 0 // Auto-set WHT for professional services
    }));
  };

  // Handle supplier change
  const handleSupplierChange = (supplierId: string) => {
    const supplier = suppliers.find(s => s.id === supplierId);
    setFormData(prev => ({
      ...prev,
      supplier_id: supplierId,
      employee_id: '', // Clear employee when supplier is selected
      withholding_tax_rate: supplier?.default_withholding_tax_rate || prev.withholding_tax_rate
    }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <Card>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">Quick Expense Entry</h2>
        {onCancel && (
          <Button color="gray" size="sm" onClick={onCancel}>
            <HiX className="mr-1 h-4 w-4" />
            Cancel
          </Button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Expense Type and Reference */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Expense Type *</label>
            <Select
              value={formData.expense_type_id}
              onChange={(e) => handleExpenseTypeChange(e.target.value)}
              required
            >
              <option value="">Select expense type</option>
              {expenseTypes.map(type => (
                <option key={type.id} value={type.id}>
                  {type.name} ({type.code})
                </option>
              ))}
            </Select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Reference Number *</label>
            <TextInput
              value={formData.reference_number}
              onChange={(e) => setFormData({ ...formData, reference_number: e.target.value })}
              required
              placeholder="Auto-generated or enter manually"
            />
          </div>
        </div>

        {/* Supplier and Amount */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Supplier *</label>
            <Select
              value={formData.supplier_id}
              onChange={(e) => handleSupplierChange(e.target.value)}
              required
            >
              <option value="">Select supplier</option>
              {suppliers.map(supplier => (
                <option key={supplier.id} value={supplier.id}>
                  {supplier.name}
                </option>
              ))}
            </Select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Amount (₱) *</label>
            <EnhancedNumberInput
              value={formData.amount}
              onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
              required
              min="0"
              step="0.01"
              placeholder="0.00"
            />
          </div>
        </div>

        {/* Payment Status */}
        <div className="border-t pt-4">
          <label className="block text-sm font-medium mb-3">Payment Status *</label>
          <div className="grid grid-cols-2 gap-4">
            <div
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                paymentStatus === 'unpaid'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setPaymentStatus('unpaid')}
            >
              <div className="flex items-center">
                <input
                  type="radio"
                  name="paymentStatus"
                  value="unpaid"
                  checked={paymentStatus === 'unpaid'}
                  onChange={() => setPaymentStatus('unpaid')}
                  className="mr-3"
                />
                <div>
                  <h4 className="font-medium">Not Yet Paid</h4>
                  <p className="text-sm text-gray-600">Create payable for future payment</p>
                </div>
              </div>
            </div>
            <div
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                paymentStatus === 'paid'
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setPaymentStatus('paid')}
            >
              <div className="flex items-center">
                <input
                  type="radio"
                  name="paymentStatus"
                  value="paid"
                  checked={paymentStatus === 'paid'}
                  onChange={() => setPaymentStatus('paid')}
                  className="mr-3"
                />
                <div>
                  <h4 className="font-medium">Already Paid</h4>
                  <p className="text-sm text-gray-600">Record expense and payment together</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Details (only show if already paid) */}
        {paymentStatus === 'paid' && (
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <h4 className="font-medium mb-3 text-green-800">Payment Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Payment Date *</label>
                <TextInput
                  type="date"
                  value={paymentData.payment_date}
                  onChange={(e) => setPaymentData({ ...paymentData, payment_date: e.target.value })}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Payment Method *</label>
                <Select
                  value={paymentData.payment_method}
                  onChange={(e) => setPaymentData({ ...paymentData, payment_method: e.target.value as any })}
                  required
                >
                  <option value="cash">Cash</option>
                  <option value="check">Check</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="gcash">GCash</option>
                  <option value="paymaya">PayMaya</option>
                  <option value="other">Other</option>
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Payment Reference</label>
                <TextInput
                  type="text"
                  value={paymentData.reference_number}
                  onChange={(e) => setPaymentData({ ...paymentData, reference_number: e.target.value })}
                  placeholder="Check number, transaction ID, etc."
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Payment Remarks</label>
                <TextInput
                  type="text"
                  value={paymentData.remarks}
                  onChange={(e) => setPaymentData({ ...paymentData, remarks: e.target.value })}
                  placeholder="Additional payment notes"
                />
              </div>
            </div>
          </div>
        )}

        {/* Dates */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Invoice Date *</label>
            <TextInput
              type="date"
              value={formData.invoice_date}
              onChange={(e) => setFormData({ ...formData, invoice_date: e.target.value })}
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Due Date</label>
            <TextInput
              type="date"
              value={formData.due_date}
              onChange={(e) => setFormData({ ...formData, due_date: e.target.value })}
              placeholder="Auto-calculated from payment terms"
            />
          </div>
        </div>

        {/* Tax Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">VAT Amount (₱)</label>
            <EnhancedNumberInput
              value={formData.vat_amount}
              onChange={(e) => setFormData({ ...formData, vat_amount: parseFloat(e.target.value) || 0 })}
              min="0"
              step="0.01"
              placeholder="0.00"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Withholding Tax (%)</label>
            <EnhancedNumberInput
              value={formData.withholding_tax_rate}
              onChange={(e) => setFormData({ ...formData, withholding_tax_rate: parseFloat(e.target.value) || 0 })}
              min="0"
              max="100"
              step="0.01"
              placeholder="0.00"
            />
            {formData.withholding_tax_amount > 0 && (
              <div className="text-sm text-gray-500 mt-1">
                WHT Amount: ₱{formData.withholding_tax_amount.toLocaleString()}
              </div>
            )}
          </div>
        </div>

        {/* Department and Notes */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Department</label>
            <TextInput
              value={formData.department}
              onChange={(e) => setFormData({ ...formData, department: e.target.value })}
              placeholder="e.g., IT, HR, Finance"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Project Code</label>
            <TextInput
              value={formData.project_code}
              onChange={(e) => setFormData({ ...formData, project_code: e.target.value })}
              placeholder="Optional project code"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Notes</label>
          <Textarea
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            placeholder="Optional notes or description"
            rows={3}
          />
        </div>

        {/* Alerts */}
        {error && (
          <Alert color="failure">
            {error}
          </Alert>
        )}

        {success && (
          <Alert color="success">
            {success}
          </Alert>
        )}

        {/* Submit Button */}
        <div className="flex justify-end space-x-2">
          <Button
            type="submit"
            disabled={submitting}
            className="bg-primary"
          >
            {submitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Creating...
              </>
            ) : (
              <>
                <HiSave className="mr-2 h-4 w-4" />
                Create Expense
              </>
            )}
          </Button>
        </div>
      </form>

      {/* Quick Stats */}
      {formData.amount > 0 && (
        <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h3 className="text-sm font-medium mb-2">Summary</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Gross Amount:</span>
              <div className="font-semibold">₱{formData.amount.toLocaleString()}</div>
            </div>
            <div>
              <span className="text-gray-500">VAT:</span>
              <div className="font-semibold">₱{formData.vat_amount.toLocaleString()}</div>
            </div>
            <div>
              <span className="text-gray-500">Withholding Tax:</span>
              <div className="font-semibold">₱{formData.withholding_tax_amount.toLocaleString()}</div>
            </div>
            <div>
              <span className="text-gray-500">Net Payable:</span>
              <div className="font-semibold text-blue-600">
                ₱{(formData.amount - formData.withholding_tax_amount).toLocaleString()}
              </div>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default QuickExpenseEntry;
