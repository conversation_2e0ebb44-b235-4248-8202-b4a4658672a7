import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Spinner,
  Alert,
  TextInput,
  Select,
  Label,
  Checkbox,
  Tooltip,
  Modal,
  Dropdown
} from 'flowbite-react';
import {
  HiOutlineInformationCircle,
  HiOutlinePlus,
  HiOutlineTrash,
  HiOutlineExclamationCircle,
  HiOutlineArrowLeft,
  HiOutlineClock,
  HiOutlineCalendar,
  HiOutlineUser,
  HiOutlineMoon,
  HiOutlineSun,
  HiOutlineCheck,
  HiOutlineTemplate,
  HiOutlineDocumentDuplicate,
  HiOutlineSave,
  HiOutlineClipboardList,
  HiOutlineAdjustments,
  HiOutlineKey
} from 'react-icons/hi';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { format, parseISO, isValid, differenceInMinutes } from 'date-fns';
import { useOrganization } from '../../context/OrganizationContext';
import { getEmployees, Employee } from '../../services/employee';
import { TimeEntryStatus } from '../../types/payroll';
import PageTitle from '../../components/shared/PageTitle';
import EmployeeSearchSelect from '../../components/payroll/EmployeeSearchSelect';

interface TimeEntry {
  id?: string;
  organization_id: string;
  employee_id: string;
  date: string;
  shift_group?: string; // Used to identify related split shifts
  time_in?: string;
  time_out?: string;
  break_start?: string;
  break_end?: string;
  status: TimeEntryStatus;
  regular_hours: number;
  overtime_hours: number;
  night_diff_hours: number;
  exclude_lunch_break?: boolean;
  is_rest_day: boolean;
  is_holiday: boolean;
  holiday_type?: string;
  created_at?: string;
  updated_at?: string;
}

import { createTimeEntries } from '../../services/timeEntry';

// Function to calculate night differential hours (10:00 PM to 6:00 AM)
const calculateNightDifferentialHours = (timeIn: Date, timeOut: Date): number => {
  // Ensure timeOut is after timeIn (handle overnight shifts)
  let adjustedTimeOut = new Date(timeOut);
  if (adjustedTimeOut < timeIn) {
    adjustedTimeOut.setDate(adjustedTimeOut.getDate() + 1);
  }

  // Create night shift start and end times for the day of timeIn
  const nightShiftStart = new Date(timeIn);
  nightShiftStart.setHours(22, 0, 0, 0); // 10:00 PM

  // Create night shift end for the next day
  const nightShiftEnd = new Date(timeIn);
  nightShiftEnd.setHours(6, 0, 0, 0); // 6:00 AM
  if (nightShiftEnd < nightShiftStart) {
    nightShiftEnd.setDate(nightShiftEnd.getDate() + 1);
  }

  // Create night shift times for the next day
  const nextDayNightShiftStart = new Date(nightShiftStart);
  nextDayNightShiftStart.setDate(nextDayNightShiftStart.getDate() + 1);

  const nextDayNightShiftEnd = new Date(nightShiftEnd);
  nextDayNightShiftEnd.setDate(nextDayNightShiftEnd.getDate() + 1);

  // Handle cases where the shift spans multiple days
  let nightDiffMinutes = 0;

  // Check if the shift overlaps with the night shift on the first day
  if (timeIn <= nightShiftEnd && adjustedTimeOut >= nightShiftStart) {
    const overlapStart = timeIn > nightShiftStart ? timeIn : nightShiftStart;
    const overlapEnd = adjustedTimeOut < nightShiftEnd ? adjustedTimeOut : nightShiftEnd;

    if (overlapEnd > overlapStart) {
      nightDiffMinutes += differenceInMinutes(overlapEnd, overlapStart);
    }
  }

  // Check if the shift overlaps with the night shift on the next day
  if (timeIn <= nextDayNightShiftEnd && adjustedTimeOut >= nextDayNightShiftStart) {
    const overlapStart = timeIn > nextDayNightShiftStart ? timeIn : nextDayNightShiftStart;
    const overlapEnd = adjustedTimeOut < nextDayNightShiftEnd ? adjustedTimeOut : nextDayNightShiftEnd;

    if (overlapEnd > overlapStart) {
      nightDiffMinutes += differenceInMinutes(overlapEnd, overlapStart);
    }
  }

  // Convert minutes to hours (rounded to nearest 0.5)
  return Math.round((nightDiffMinutes / 60) * 2) / 2;
};

// Sortable Time Entry component for drag and drop
interface SortableTimeEntryProps {
  entry: Partial<TimeEntry>;
  index: number;
  updateTimeEntry: (index: number, field: string, value: any) => void;
  removeTimeEntryRow: (index: number) => void;
  employees: Employee[];
  isLoading: boolean;
}

const SortableTimeEntry: React.FC<SortableTimeEntryProps> = ({
  entry,
  index,
  updateTimeEntry,
  removeTimeEntryRow,
  employees,
  isLoading
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: `entry-${index}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <Card className="overflow-hidden" ref={setNodeRef} style={style}>
      <div className="bg-gray-50 px-4 py-2 border-b flex justify-between items-center">
        <div className="flex items-center">
          <span
            className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mr-2 cursor-move"
            {...attributes}
            {...listeners}
          >
            #{index + 1}
          </span>
          <h3 className="text-sm font-medium text-gray-700">Time Entry</h3>
        </div>
        <Button
          color="light"
          size="xs"
          onClick={() => removeTimeEntryRow(index)}
          disabled={false}
          className="text-gray-500 hover:text-red-500"
        >
          <HiOutlineTrash className="h-4 w-4" />
        </Button>
      </div>

      <div className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <div className="mb-1 flex items-center">
              <HiOutlineUser className="h-4 w-4 text-gray-500 mr-1" />
              <Label htmlFor={`employeeId-${index}`} value="Employee" className="text-sm" />
            </div>
            <EmployeeSearchSelect
              employees={employees}
              selectedEmployeeId={entry.employee_id}
              onSelect={(employeeId) => updateTimeEntry(index, 'employee_id', employeeId)}
              required
              isLoading={isLoading}
              placeholder="Search for an employee..."
            />
          </div>

          <div>
            <div className="mb-1 flex items-center">
              <HiOutlineCalendar className="h-4 w-4 text-gray-500 mr-1" />
              <Label htmlFor={`date-${index}`} value="Date" className="text-sm" />
            </div>
            <DatePicker
              id={`date-${index}`}
              selected={entry.date ? new Date(entry.date) : null}
              onChange={(date: Date) => updateTimeEntry(index, 'date', date.toISOString().split('T')[0])}
              dateFormat="MMM d, yyyy"
              className="w-full rounded-lg border border-gray-300 p-2 text-sm"
              placeholderText="Select date"
              required
            />
          </div>

          <div>
            <div className="mb-1 flex items-center">
              <Label htmlFor={`status-${index}`} value="Status" className="text-sm" />
            </div>
            <div className="flex space-x-2">
              <div
                className={`flex-1 text-center py-1.5 px-2 rounded-md cursor-pointer text-sm border ${
                  entry.status === TimeEntryStatus.PRESENT
                    ? 'bg-green-100 border-green-200 text-green-800 font-medium'
                    : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => updateTimeEntry(index, 'status', TimeEntryStatus.PRESENT)}
              >
                Present
              </div>
              <div
                className={`flex-1 text-center py-1.5 px-2 rounded-md cursor-pointer text-sm border ${
                  entry.status === TimeEntryStatus.ABSENT
                    ? 'bg-red-100 border-red-200 text-red-800 font-medium'
                    : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => updateTimeEntry(index, 'status', TimeEntryStatus.ABSENT)}
              >
                Absent
              </div>
              <div
                className={`flex-1 text-center py-1.5 px-2 rounded-md cursor-pointer text-sm border ${
                  entry.status === TimeEntryStatus.LEAVE
                    ? 'bg-blue-100 border-blue-200 text-blue-800 font-medium'
                    : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => updateTimeEntry(index, 'status', TimeEntryStatus.LEAVE)}
              >
                Leave
              </div>
            </div>
          </div>
        </div>

        {entry.status === TimeEntryStatus.PRESENT && (
          <>
            <div className="bg-gray-50 p-3 rounded-lg mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="mb-1 flex items-center">
                    <HiOutlineClock className="h-4 w-4 text-gray-500 mr-1" />
                    <Label htmlFor={`timeIn-${index}`} value="Time In" className="text-sm" />
                  </div>
                  <DatePicker
                    id={`timeIn-${index}`}
                    selected={entry.time_in ? new Date(entry.time_in) : null}
                    onChange={(date: Date) => updateTimeEntry(index, 'time_in', date.toISOString())}
                    showTimeSelect
                    showTimeSelectOnly
                    timeIntervals={15}
                    timeCaption="Time"
                    dateFormat="h:mm aa"
                    className="w-full rounded-lg border border-gray-300 p-2 text-sm"
                    placeholderText="Select time in"
                    required
                  />
                </div>
                <div>
                  <div className="mb-1 flex items-center">
                    <HiOutlineClock className="h-4 w-4 text-gray-500 mr-1" />
                    <Label htmlFor={`timeOut-${index}`} value="Time Out" className="text-sm" />
                  </div>
                  <DatePicker
                    id={`timeOut-${index}`}
                    selected={entry.time_out ? new Date(entry.time_out) : null}
                    onChange={(date: Date) => updateTimeEntry(index, 'time_out', date.toISOString())}
                    showTimeSelect
                    showTimeSelectOnly
                    timeIntervals={15}
                    timeCaption="Time"
                    dateFormat="h:mm aa"
                    className="w-full rounded-lg border border-gray-300 p-2 text-sm"
                    placeholderText="Select time out"
                    required
                  />
                </div>
              </div>

              <div className="mt-3 flex items-center">
                <Checkbox
                  id={`excludeLunch-${index}`}
                  checked={entry.exclude_lunch_break}
                  onChange={(e) => updateTimeEntry(index, 'exclude_lunch_break', e.target.checked)}
                />
                <Label htmlFor={`excludeLunch-${index}`} className="ml-2 text-sm text-gray-700">
                  Exclude lunch break (1 hour)
                </Label>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-2 mb-3">
              <div className="bg-blue-50 rounded-lg p-2 border border-blue-100">
                <div className="text-xs text-blue-700 mb-1">Regular Hours</div>
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-blue-500 mr-1"></div>
                  <span className="text-blue-800 font-medium">{entry.regular_hours || 0}</span>
                </div>
              </div>
              <div className="bg-orange-50 rounded-lg p-2 border border-orange-100">
                <div className="text-xs text-orange-700 mb-1">Overtime</div>
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-orange-500 mr-1"></div>
                  <span className="text-orange-800 font-medium">{entry.overtime_hours || 0}</span>
                </div>
              </div>
              <div className="bg-indigo-50 rounded-lg p-2 border border-indigo-100">
                <div className="text-xs text-indigo-700 mb-1">Night Diff</div>
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-indigo-500 mr-1"></div>
                  <span className="text-indigo-800 font-medium">{entry.night_diff_hours || 0}</span>
                  <HiOutlineMoon className="ml-1 h-3 w-3 text-indigo-600" />
                </div>
              </div>
            </div>

            <div className="text-xs text-gray-500 mb-3">
              Hours are calculated automatically. You can manually adjust if needed.
            </div>
          </>
        )}

        <div className="flex flex-wrap gap-2 mt-3">
          <div
            className={`flex items-center px-3 py-1.5 rounded-full text-xs cursor-pointer ${
              entry.is_rest_day
                ? 'bg-purple-100 text-purple-800 border border-purple-200'
                : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
            }`}
            onClick={() => updateTimeEntry(index, 'is_rest_day', !entry.is_rest_day)}
          >
            <span className={`w-2 h-2 rounded-full mr-1 ${entry.is_rest_day ? 'bg-purple-500' : 'bg-gray-400'}`}></span>
            Rest Day
          </div>
          <div
            className={`flex items-center px-3 py-1.5 rounded-full text-xs cursor-pointer ${
              entry.is_holiday
                ? 'bg-blue-100 text-blue-800 border border-blue-200'
                : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
            }`}
            onClick={() => updateTimeEntry(index, 'is_holiday', !entry.is_holiday)}
          >
            <span className={`w-2 h-2 rounded-full mr-1 ${entry.is_holiday ? 'bg-blue-500' : 'bg-gray-400'}`}></span>
            Holiday
          </div>
        </div>
      </div>
    </Card>
  );
};

const AddTimeEntry: React.FC = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<boolean>(false);

  // State for time entries
  const [timeEntries, setTimeEntries] = useState<Partial<TimeEntry>[]>([
    {
      date: new Date().toISOString().split('T')[0],
      status: TimeEntryStatus.PRESENT,
      is_rest_day: false,
      is_holiday: false,
      exclude_lunch_break: true,
      regular_hours: 0,
      overtime_hours: 0,
      night_diff_hours: 0
    }
  ]);

  // Template functionality
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [templateName, setTemplateName] = useState('');
  const [templates, setTemplates] = useState<{name: string, entries: Partial<TimeEntry>[]}[]>([]);
  const [showKeyboardShortcutsModal, setShowKeyboardShortcutsModal] = useState(false);

  // Drag and drop functionality
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Reference for keyboard shortcuts
  const formRef = useRef<HTMLFormElement>(null);

  // Fetch employees
  const fetchEmployees = async () => {
    if (!currentOrganization) return;

    try {
      const { employees, error } = await getEmployees(currentOrganization.id);

      if (error) {
        setError(error);
      } else {
        setEmployees(employees);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Load employees when the component mounts
  useEffect(() => {
    fetchEmployees();

    // Load templates from localStorage
    const savedTemplates = localStorage.getItem('timeEntryTemplates');
    if (savedTemplates) {
      try {
        setTemplates(JSON.parse(savedTemplates));
      } catch (err) {
        console.error('Error loading templates:', err);
      }
    } else {
      // Add default split shift template if no templates exist
      const defaultTemplates = [
        {
          name: "Split Shift Template",
          entries: [
            {
              status: TimeEntryStatus.PRESENT,
              is_rest_day: false,
              is_holiday: false,
              exclude_lunch_break: false,
              regular_hours: 0,
              overtime_hours: 0,
              night_diff_hours: 0
            },
            {
              status: TimeEntryStatus.PRESENT,
              is_rest_day: false,
              is_holiday: false,
              exclude_lunch_break: false,
              regular_hours: 0,
              overtime_hours: 0,
              night_diff_hours: 0
            }
          ]
        }
      ];
      setTemplates(defaultTemplates);
      localStorage.setItem('timeEntryTemplates', JSON.stringify(defaultTemplates));
    }

    // Set up keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only process if not in an input field
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement || e.target instanceof HTMLSelectElement) {
        return;
      }

      // Ctrl/Cmd + Shift + K - Show keyboard shortcuts
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'K') {
        e.preventDefault();
        setShowKeyboardShortcutsModal(true);
      }

      // Ctrl/Cmd + Shift + T - Show templates
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
        e.preventDefault();
        setShowTemplateModal(true);
      }

      // Ctrl/Cmd + Shift + S - Save form
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'S') {
        e.preventDefault();
        formRef.current?.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
      }

      // Ctrl/Cmd + Shift + A - Add new entry
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
        e.preventDefault();
        addTimeEntryRow();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentOrganization]);

  // Save template
  const saveTemplate = () => {
    if (!templateName.trim()) {
      return;
    }

    const newTemplates = [
      ...templates,
      {
        name: templateName,
        entries: timeEntries.map(entry => ({
          ...entry,
          // Remove specific data that shouldn't be in a template
          employee_id: undefined,
          date: undefined
        }))
      }
    ];

    setTemplates(newTemplates);
    localStorage.setItem('timeEntryTemplates', JSON.stringify(newTemplates));
    setShowTemplateModal(false);
    setTemplateName('');
  };

  // Apply template
  const applyTemplate = (templateIndex: number) => {
    const template = templates[templateIndex];
    if (!template) return;

    // Create new entries based on the template
    const newEntries = template.entries.map(entry => ({
      ...entry,
      date: new Date().toISOString().split('T')[0],
      employee_id: undefined
    }));

    setTimeEntries(newEntries);
    setShowTemplateModal(false);
  };

  // Handle drag end for reordering
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setTimeEntries((items) => {
        const oldIndex = items.findIndex((_, i) => `entry-${i}` === active.id);
        const newIndex = items.findIndex((_, i) => `entry-${i}` === over.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  // Add a new time entry row
  const addTimeEntryRow = () => {
    setTimeEntries([
      ...timeEntries,
      {
        date: new Date().toISOString().split('T')[0],
        status: TimeEntryStatus.PRESENT,
        is_rest_day: false,
        is_holiday: false,
        exclude_lunch_break: true,
        regular_hours: 0,
        overtime_hours: 0,
        night_diff_hours: 0
      }
    ]);
  };

  // Remove a time entry row
  const removeTimeEntryRow = (index: number) => {
    if (timeEntries.length === 1) {
      // Don't remove the last row, just reset it
      setTimeEntries([
        {
          date: new Date().toISOString().split('T')[0],
          status: TimeEntryStatus.PRESENT,
          is_rest_day: false,
          is_holiday: false,
          exclude_lunch_break: true,
          regular_hours: 0,
          overtime_hours: 0,
          night_diff_hours: 0
        }
      ]);
    } else {
      setTimeEntries(timeEntries.filter((_, i) => i !== index));
    }
  };

  // Update a time entry field
  const updateTimeEntry = (index: number, field: string, value: any) => {
    const updatedEntries = [...timeEntries];
    updatedEntries[index] = {
      ...updatedEntries[index],
      [field]: value
    };

    // If time_in or time_out changes, recalculate hours
    if (field === 'time_in' || field === 'time_out' || field === 'exclude_lunch_break') {
      const entry = updatedEntries[index];

      if (entry.time_in && entry.time_out) {
        const timeIn = new Date(entry.time_in);
        const timeOut = new Date(entry.time_out);

        if (isValid(timeIn) && isValid(timeOut)) {
          // Handle shifts that span across midnight
          let totalMinutes = 0;

          // If timeOut is earlier than timeIn, it likely means the shift spans to the next day
          if (timeOut < timeIn) {
            // Create a new timeOut date on the next day
            const nextDayTimeOut = new Date(timeOut);
            nextDayTimeOut.setDate(nextDayTimeOut.getDate() + 1);
            totalMinutes = differenceInMinutes(nextDayTimeOut, timeIn);
          } else {
            totalMinutes = differenceInMinutes(timeOut, timeIn);
          }

          // Subtract lunch break if applicable (1 hour = 60 minutes)
          if (entry.exclude_lunch_break && totalMinutes > 300) { // Only subtract if shift is > 5 hours
            totalMinutes -= 60;
          }

          // Calculate regular and overtime hours
          const totalHours = totalMinutes / 60;
          const regularHours = Math.min(8, totalHours);
          const overtimeHours = Math.max(0, totalHours - 8);

          // Round to nearest 0.5
          updatedEntries[index].regular_hours = Math.round(regularHours * 2) / 2;
          updatedEntries[index].overtime_hours = Math.round(overtimeHours * 2) / 2;

          // For night differential calculation, we need to adjust timeOut if it's across midnight
          let adjustedTimeOut = timeOut;
          if (timeOut < timeIn) {
            adjustedTimeOut = new Date(timeOut);
            adjustedTimeOut.setDate(adjustedTimeOut.getDate() + 1);
          }

          // Calculate night differential hours
          updatedEntries[index].night_diff_hours = calculateNightDifferentialHours(timeIn, adjustedTimeOut);
        }
      }
    }

    setTimeEntries(updatedEntries);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization) return;

    // Validate entries
    const invalidEntries = timeEntries.filter(entry =>
      !entry.employee_id || !entry.date ||
      (entry.status === TimeEntryStatus.PRESENT && (!entry.time_in || !entry.time_out))
    );

    if (invalidEntries.length > 0) {
      setSubmitError('Please fill in all required fields for all time entries.');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Convert timeEntries to the format expected by the API
      const entriesToSave = timeEntries.map(entry => ({
        employee_id: entry.employee_id,
        date: entry.date,
        time_in: entry.time_in,
        time_out: entry.time_out,
        status: entry.status,
        regular_hours: entry.regular_hours,
        overtime_hours: entry.overtime_hours,
        night_diff_hours: entry.night_diff_hours,
        exclude_lunch_break: entry.exclude_lunch_break,
        is_rest_day: entry.is_rest_day,
        is_holiday: entry.is_holiday,
        holiday_type: entry.holiday_type
      }));

      const { entries, error } = await createTimeEntries(currentOrganization.id, entriesToSave);

      if (error) {
        setSubmitError(error);
      } else {
        setSubmitSuccess(true);
        // Reset form after successful submission
        setTimeout(() => {
          navigate('/payroll/time-entries');
        }, 2000);
      }
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while saving time entries');
    } finally {
      setIsSubmitting(false);
    }
  };

  // If loading, show a spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <Button color="light" size="sm" onClick={() => navigate('/payroll/time-entries')} className="mr-3">
            <HiOutlineArrowLeft className="mr-1 h-4 w-4" />
            Back
          </Button>
          <PageTitle title="Add Time Entries" />
        </div>
      </div>

      {error && (
        <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
          <span className="font-medium">Error!</span> {error}
        </Alert>
      )}

      {submitSuccess && (
        <Alert color="success" icon={HiOutlineCheck} className="mb-4">
          Time entries saved successfully! Redirecting...
        </Alert>
      )}

      {submitError && (
        <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
          {submitError}
        </Alert>
      )}

      <Card className="mb-4">
        <div className="p-4 bg-blue-50 border-b border-blue-100 rounded-t-lg">
          <h3 className="text-blue-800 font-medium">Quick Tips</h3>
          <ul className="mt-2 text-sm text-blue-700 space-y-1">
            <li className="flex items-start">
              <HiOutlineCheck className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" />
              <span>Add multiple time entries at once for bulk processing</span>
            </li>
            <li className="flex items-start">
              <HiOutlineCheck className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" />
              <span>Hours are automatically calculated based on time in/out</span>
            </li>
            <li className="flex items-start">
              <HiOutlineCheck className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" />
              <span>Night differential (10PM-6AM) is calculated automatically</span>
            </li>
            <li className="flex items-start">
              <HiOutlineCheck className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" />
              <span><strong>For split shifts</strong> (e.g., 7am-10am, then 3pm-7pm), create separate entries for each shift segment</span>
            </li>
          </ul>
        </div>
      </Card>

      <div className="flex justify-between mb-4">
        <div className="flex space-x-2">
          <Button size="sm" color="light" onClick={() => setShowTemplateModal(true)}>
            <HiOutlineTemplate className="mr-1 h-4 w-4" />
            Templates
          </Button>
          <Button size="sm" color="light" onClick={() => setShowKeyboardShortcutsModal(true)}>
            <HiOutlineKey className="mr-1 h-4 w-4" />
            Shortcuts
          </Button>
        </div>
      </div>

      <form ref={formRef} onSubmit={handleSubmit}>
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={timeEntries.map((_, index) => `entry-${index}`)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-4">
              {timeEntries.map((entry, index) => (
                <SortableTimeEntry
                  key={`entry-${index}`}
                  entry={entry}
                  index={index}
                  updateTimeEntry={updateTimeEntry}
                  removeTimeEntryRow={removeTimeEntryRow}
                  employees={employees}
                  isLoading={isLoading}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>

        <div className="flex justify-center">
          <Button color="light" size="sm" onClick={addTimeEntryRow} className="mt-4">
            <HiOutlinePlus className="mr-1 h-4 w-4" />
            Add Another Entry
          </Button>
        </div>

        <div className="flex justify-between mt-6">
          <Button color="light" size="sm" onClick={() => navigate('/payroll/time-entries')}>
            Cancel
          </Button>
          <Button color="primary" type="submit" isProcessing={isSubmitting}>
            <HiOutlineSave className="mr-1 h-4 w-4" />
            Save Time Entries
          </Button>
        </div>
      </form>

      {/* Template Modal */}
      <Modal
        show={showTemplateModal}
        onClose={() => setShowTemplateModal(false)}
        size="md"
      >
        <Modal.Header>Time Entry Templates</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            {templates.length > 0 ? (
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-gray-700">Saved Templates</h3>
                <div className="space-y-2">
                  {templates.map((template, index) => (
                    <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                      <div>
                        <div className="font-medium text-gray-800">{template.name}</div>
                        <div className="text-xs text-gray-500">{template.entries.length} entries</div>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="xs" color="light" onClick={() => applyTemplate(index)}>
                          <HiOutlineDocumentDuplicate className="h-4 w-4" />
                        </Button>
                        <Button
                          size="xs"
                          color="failure"
                          onClick={() => {
                            const newTemplates = templates.filter((_, i) => i !== index);
                            setTemplates(newTemplates);
                            localStorage.setItem('timeEntryTemplates', JSON.stringify(newTemplates));
                          }}
                        >
                          <HiOutlineTrash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                <HiOutlineTemplate className="mx-auto h-10 w-10 mb-2" />
                <p>No templates saved yet</p>
              </div>
            )}

            <div className="border-t pt-4 mt-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Save Current Entries as Template</h3>
              <div className="flex space-x-2">
                <TextInput
                  type="text"
                  placeholder="Template name"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  className="flex-1"
                />
                <Button
                  color="blue"
                  onClick={saveTemplate}
                  disabled={!templateName.trim() || timeEntries.length === 0}
                >
                  Save
                </Button>
              </div>
            </div>
          </div>
        </Modal.Body>
      </Modal>

      {/* Keyboard Shortcuts Modal */}
      <Modal
        show={showKeyboardShortcutsModal}
        onClose={() => setShowKeyboardShortcutsModal(false)}
        size="md"
      >
        <Modal.Header>Keyboard Shortcuts</Modal.Header>
        <Modal.Body>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-2 border-b">
              <span className="text-gray-700">Add new entry</span>
              <div className="flex space-x-1">
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Ctrl</kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Shift</kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">A</kbd>
              </div>
            </div>
            <div className="flex justify-between items-center p-2 border-b">
              <span className="text-gray-700">Save entries</span>
              <div className="flex space-x-1">
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Ctrl</kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Shift</kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">S</kbd>
              </div>
            </div>
            <div className="flex justify-between items-center p-2 border-b">
              <span className="text-gray-700">Show templates</span>
              <div className="flex space-x-1">
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Ctrl</kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Shift</kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">T</kbd>
              </div>
            </div>
            <div className="flex justify-between items-center p-2">
              <span className="text-gray-700">Show this help</span>
              <div className="flex space-x-1">
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Ctrl</kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Shift</kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">K</kbd>
              </div>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default AddTimeEntry;
