import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  Table,
  Spinner,
  Badge,
  Modal,
  Alert,
  Select,
  TextInput,
  Label,
  Checkbox,
  Tooltip
} from 'flowbite-react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import {
  HiOutlinePlus,
  HiOutlineCalendar,
  HiOutlineClock,
  HiOutlineExclamationCircle,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineCheck,
  HiOutlineX,
  HiOutlineRefresh,
  HiOutlineInformationCircle,
  HiOutlineMoon,
  HiOutlineSearch,
  HiOutlineAdjustments,
  HiOutlineDocumentDuplicate
} from 'react-icons/hi';

import {
  HiCalendarDays as HiOutlineCalendarDays
} from 'react-icons/hi2';
import { format, parseISO, isValid, differenceInHours, differenceInMinutes, setHours, setMinutes, isBefore, isAfter } from 'date-fns';
import { useOrganization } from '../../context/OrganizationContext';
import { getEmployees } from '../../services/employee';
import { TimeEntryStatus } from '../../types/payroll';
import { Employee } from '../../services/employee';
import PageTitle from '../../components/shared/PageTitle';
import Pagination from '../../components/common/Pagination';

// Define TimeEntry interface
interface TimeEntry {
  id: string;
  organization_id: string;
  employee_id: string;
  date: string;
  shift_group?: string; // Used to identify related split shifts
  time_in?: string;
  time_out?: string;
  break_start?: string;
  break_end?: string;
  status: string;
  regular_hours: number;
  overtime_hours: number;
  night_diff_hours: number;
  exclude_lunch_break: boolean;
  is_rest_day: boolean;
  is_holiday: boolean;
  holiday_type?: string;
  created_at: string;
  updated_at: string;
}

import { getTimeEntries, createTimeEntry as saveTimeEntry, deleteTimeEntry, updateTimeEntry } from '../../services/timeEntry';

// Function to calculate night differential hours (10:00 PM to 6:00 AM)
const calculateNightDifferentialHours = (timeIn: Date, timeOut: Date): number => {
  // Create night shift start and end times for the same day as timeIn
  const nightShiftStart = new Date(timeIn);
  nightShiftStart.setHours(22, 0, 0, 0); // 10:00 PM

  const nightShiftEnd = new Date(timeIn);
  nightShiftEnd.setHours(6, 0, 0, 0); // 6:00 AM

  // If nightShiftEnd is before nightShiftStart, it means it's for the next day
  if (nightShiftEnd < nightShiftStart) {
    nightShiftEnd.setDate(nightShiftEnd.getDate() + 1);
  }

  // Create night shift start and end times for the next day
  const nextDayNightShiftStart = new Date(nightShiftStart);
  nextDayNightShiftStart.setDate(nextDayNightShiftStart.getDate() + 1);

  const nextDayNightShiftEnd = new Date(nightShiftEnd);
  nextDayNightShiftEnd.setDate(nextDayNightShiftEnd.getDate() + 1);

  // Handle cases where the shift spans multiple days
  let nightDiffMinutes = 0;

  // Check if the shift overlaps with the night shift on the first day
  if (timeIn < nightShiftEnd && timeOut > nightShiftStart) {
    const overlapStart = timeIn > nightShiftStart ? timeIn : nightShiftStart;
    const overlapEnd = timeOut < nightShiftEnd ? timeOut : nightShiftEnd;
    nightDiffMinutes += differenceInMinutes(overlapEnd, overlapStart);
  }

  // Check if the shift overlaps with the night shift on the next day
  if (timeIn < nextDayNightShiftEnd && timeOut > nextDayNightShiftStart) {
    const overlapStart = timeIn > nextDayNightShiftStart ? timeIn : nextDayNightShiftStart;
    const overlapEnd = timeOut < nextDayNightShiftEnd ? timeOut : nextDayNightShiftEnd;
    nightDiffMinutes += differenceInMinutes(overlapEnd, overlapStart);
  }

  // Convert minutes to hours (rounded to nearest 0.5)
  return Math.round((nightDiffMinutes / 60) * 2) / 2;
};

const TimeEntries: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [currentEntry, setCurrentEntry] = useState<Partial<TimeEntry> | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [filterStartDate, setFilterStartDate] = useState<Date | null>(null);
  const [filterEndDate, setFilterEndDate] = useState<Date | null>(null);
  const [filterEmployeeId, setFilterEmployeeId] = useState<string>('');

  // Batch actions
  const [selectedEntries, setSelectedEntries] = useState<string[]>([]);
  const [showBatchActionModal, setShowBatchActionModal] = useState<boolean>(false);
  const [batchAction, setBatchAction] = useState<string>('');
  const [batchActionValue, setBatchActionValue] = useState<any>(null);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Fetch time entries
  const fetchTimeEntries = async () => {
    if (!currentOrganization) return;

    setIsLoading(true);
    setError(null);

    try {
      // Log the date filter values for debugging
      if (filterStartDate && filterEndDate) {
        console.log('Date Filter:', {
          startDate: filterStartDate.toISOString().split('T')[0],
          endDate: filterEndDate.toISOString().split('T')[0],
          isSameDay: filterStartDate.toISOString().split('T')[0] === filterEndDate.toISOString().split('T')[0]
        });
      }

      const { entries, count, error } = await getTimeEntries(currentOrganization.id, {
        limit: itemsPerPage,
        offset: (currentPage - 1) * itemsPerPage,
        startDate: filterStartDate || undefined,
        endDate: filterEndDate || undefined,
        employeeId: filterEmployeeId || undefined
      });

      if (error) {
        setError(error);
      } else {
        // Log the returned entries for debugging
        console.log(`Fetched ${entries.length} entries:`, entries.map(e => ({
          id: e.id,
          date: e.date,
          employee: e.employee_id
        })));

        setTimeEntries(entries);
        setTotalCount(count);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch employees
  const fetchEmployees = async () => {
    if (!currentOrganization) return;

    try {
      const { employees, error } = await getEmployees(currentOrganization.id);

      if (error) {
        setError(error);
      } else {
        setEmployees(employees);
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Load data when the component mounts or when filters/page changes
  useEffect(() => {
    // Log filter values for debugging
    if (filterStartDate || filterEndDate) {
      console.log('Filter values changed:', {
        startDate: filterStartDate ? filterStartDate.toISOString() : null,
        endDate: filterEndDate ? filterEndDate.toISOString() : null,
        startDateStr: filterStartDate ? filterStartDate.toISOString().split('T')[0] : null,
        endDateStr: filterEndDate ? filterEndDate.toISOString().split('T')[0] : null,
        isSameDay: filterStartDate && filterEndDate ?
          filterStartDate.toISOString().split('T')[0] === filterEndDate.toISOString().split('T')[0] : false
      });
    }

    fetchTimeEntries();
  }, [currentOrganization, currentPage, filterStartDate, filterEndDate, filterEmployeeId]);

  // Load employees when the component mounts
  useEffect(() => {
    fetchEmployees();
  }, [currentOrganization]);

  // Set up keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only process if not in an input field
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement || e.target instanceof HTMLSelectElement) {
        return;
      }

      // Ctrl/Cmd + A - Add new time entry
      if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
        e.preventDefault();
        window.location.href = '/payroll/add-time-entry';
      }

      // Ctrl/Cmd + B - Toggle batch action modal
      if ((e.ctrlKey || e.metaKey) && e.key === 'b' && selectedEntries.length > 0) {
        e.preventDefault();
        setShowBatchActionModal(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedEntries]);

  // Handle adding/editing a time entry
  const handleSaveTimeEntry = async () => {
    if (!currentOrganization || !currentEntry) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { entry, error } = await saveTimeEntry(currentOrganization.id, currentEntry);

      if (error) {
        setSubmitError(error);
      } else {
        setShowEditModal(false);
        fetchTimeEntries();
      }
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting a time entry
  const handleDeleteTimeEntry = async () => {
    if (!currentOrganization || !currentEntry?.id) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { success, error } = await deleteTimeEntry(currentOrganization.id, currentEntry.id);

      if (error) {
        setSubmitError(error);
      } else if (success) {
        setShowDeleteModal(false);
        fetchTimeEntries();
      }
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle filter reset
  const handleResetFilters = () => {
    setFilterStartDate(null);
    setFilterEndDate(null);
    setFilterEmployeeId('');
    setCurrentPage(1);
  };

  // Handle batch selection
  const toggleEntrySelection = (entryId: string) => {
    setSelectedEntries(prev =>
      prev.includes(entryId)
        ? prev.filter(id => id !== entryId)
        : [...prev, entryId]
    );
  };

  // Select all entries on current page
  const selectAllEntries = () => {
    if (selectedEntries.length === timeEntries.length) {
      // If all are selected, deselect all
      setSelectedEntries([]);
    } else {
      // Otherwise, select all
      setSelectedEntries(timeEntries.map(entry => entry.id));
    }
  };

  // Apply batch action
  const applyBatchAction = async () => {
    if (!currentOrganization || selectedEntries.length === 0 || !batchAction) {
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Create a copy of the selected entries
      const entriesToUpdate = timeEntries
        .filter(entry => selectedEntries.includes(entry.id))
        .map(entry => ({
          ...entry,
          [batchAction]: batchActionValue
        }));

      // Update each entry
      for (const entry of entriesToUpdate) {
        await updateTimeEntry(currentOrganization.id, entry);
      }

      // Refresh the list
      fetchTimeEntries();
      setSelectedEntries([]);
      setShowBatchActionModal(false);
      setBatchAction('');
      setBatchActionValue(null);
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while updating entries');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render status badge
  const renderStatusBadge = (status: TimeEntryStatus) => {
    switch (status) {
      case TimeEntryStatus.PRESENT:
        return <Badge color="success">Present</Badge>;
      case TimeEntryStatus.ABSENT:
        return <Badge color="failure">Absent</Badge>;
      case TimeEntryStatus.LEAVE:
        return <Badge color="info">Leave</Badge>;
      case TimeEntryStatus.HOLIDAY:
        return <Badge color="purple">Holiday</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  // Get employee name by ID
  const getEmployeeName = (employeeId: string): string => {
    const employee = employees.find(emp => emp.id === employeeId);
    return employee ? `${employee.first_name} ${employee.last_name}` : 'Unknown Employee';
  };

  // Format time
  const formatTime = (timeString?: string): string => {
    if (!timeString) return 'N/A';

    try {
      const date = parseISO(timeString);
      if (!isValid(date)) return 'Invalid Time';

      return format(date, 'h:mm a');
    } catch (err) {
      return 'Invalid Time';
    }
  };

  // If loading, show a spinner
  if (isLoading && timeEntries.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <PageTitle title="Time & Attendance" />
        <Button color="primary" as="a" href="/payroll/add-time-entry">
          <HiOutlinePlus className="mr-2 h-5 w-5" />
          Add Time Entry
        </Button>
      </div>

      {error && (
        <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
          <span className="font-medium">Error!</span> {error}
        </Alert>
      )}

      <Card className="mb-6">
        <div className="p-2">
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-700 mb-1">
                <Label htmlFor="filterStartDate" value="Date Range" />
              </div>
              <div className="flex items-center space-x-2">
                <DatePicker
                  id="filterStartDate"
                  selected={filterStartDate}
                  onChange={(date: Date) => {
                    if (date) {
                      const newDate = new Date(date);
                      newDate.setHours(12, 0, 0, 0);
                      setFilterStartDate(newDate);
                    } else {
                      setFilterStartDate(null);
                    }
                  }}
                  dateFormat="MMM d, yyyy"
                  className="w-full rounded-lg border border-gray-300 p-2 text-sm"
                  placeholderText="Start date"
                />
                <span className="text-gray-500">to</span>
                <DatePicker
                  id="filterEndDate"
                  selected={filterEndDate}
                  onChange={(date: Date) => {
                    if (date) {
                      const newDate = new Date(date);
                      newDate.setHours(12, 0, 0, 0);
                      setFilterEndDate(newDate);
                    } else {
                      setFilterEndDate(null);
                    }
                  }}
                  dateFormat="MMM d, yyyy"
                  className="w-full rounded-lg border border-gray-300 p-2 text-sm"
                  placeholderText="End date"
                  minDate={filterStartDate || undefined}
                />
              </div>
            </div>
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-700 mb-1">
                <Label htmlFor="filterEmployee" value="Employee" />
              </div>
              <Select
                id="filterEmployee"
                value={filterEmployeeId}
                onChange={(e) => setFilterEmployeeId(e.target.value)}
                className="text-sm"
              >
                <option value="">All Employees</option>
                {employees.map((employee) => (
                  <option key={employee.id} value={employee.id}>
                    {employee.first_name} {employee.last_name}
                  </option>
                ))}
              </Select>
            </div>
            <div className="flex items-end space-x-2">
              <Button size="sm" color="light" onClick={handleResetFilters} className="whitespace-nowrap">
                <HiOutlineRefresh className="mr-1 h-4 w-4" />
                Reset
              </Button>
              <Button size="sm" color="blue" onClick={() => fetchTimeEntries()} className="whitespace-nowrap">
                <HiOutlineSearch className="mr-1 h-4 w-4" />
                Apply Filters
              </Button>
            </div>
          </div>

          {/* Quick date filters */}
          <div className="flex flex-wrap gap-2 mb-2">
            <Button size="xs" color="light" onClick={() => {
              const today = new Date();
              today.setHours(12, 0, 0, 0);
              setFilterStartDate(today);
              setFilterEndDate(today);
              setTimeout(() => fetchTimeEntries(), 0);
            }}>
              Today
            </Button>
            <Button size="xs" color="light" onClick={() => {
              const yesterday = new Date();
              yesterday.setDate(yesterday.getDate() - 1);
              yesterday.setHours(12, 0, 0, 0);
              setFilterStartDate(yesterday);
              setFilterEndDate(yesterday);
              setTimeout(() => fetchTimeEntries(), 0);
            }}>
              Yesterday
            </Button>
            <Button size="xs" color="light" onClick={() => {
              const today = new Date();
              today.setHours(12, 0, 0, 0);

              const startOfWeek = new Date(today);
              startOfWeek.setDate(today.getDate() - today.getDay()); // Sunday

              setFilterStartDate(startOfWeek);
              setFilterEndDate(today);
              setTimeout(() => fetchTimeEntries(), 0);
            }}>
              This Week
            </Button>
            <Button size="xs" color="light" onClick={() => {
              const today = new Date();
              today.setHours(12, 0, 0, 0);

              const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
              startOfMonth.setHours(12, 0, 0, 0);

              setFilterStartDate(startOfMonth);
              setFilterEndDate(today);
              setTimeout(() => fetchTimeEntries(), 0);
            }}>
              This Month
            </Button>
          </div>
        </div>
      </Card>

      <Card>
        <div className="p-4">
          {/* Summary stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
              <div className="text-sm text-blue-700 font-medium">Total Entries</div>
              <div className="text-2xl font-bold text-blue-800">{totalCount}</div>
            </div>
            <div className="bg-green-50 rounded-lg p-3 border border-green-100">
              <div className="text-sm text-green-700 font-medium">Present</div>
              <div className="text-2xl font-bold text-green-800">
                {timeEntries.filter(e => e.status === TimeEntryStatus.PRESENT).length}
              </div>
            </div>
            <div className="bg-red-50 rounded-lg p-3 border border-red-100">
              <div className="text-sm text-red-700 font-medium">Absent</div>
              <div className="text-2xl font-bold text-red-800">
                {timeEntries.filter(e => e.status === TimeEntryStatus.ABSENT).length}
              </div>
            </div>
            <div className="bg-purple-50 rounded-lg p-3 border border-purple-100">
              <div className="text-sm text-purple-700 font-medium">On Leave</div>
              <div className="text-2xl font-bold text-purple-800">
                {timeEntries.filter(e => e.status === TimeEntryStatus.LEAVE).length}
              </div>
            </div>
          </div>

          {selectedEntries.length > 0 && (
            <div className="bg-blue-50 p-3 mb-4 rounded-lg border border-blue-100 flex justify-between items-center">
              <div className="flex items-center">
                <span className="font-medium text-blue-800">{selectedEntries.length} entries selected</span>
                <Button
                  size="xs"
                  color="light"
                  onClick={() => setSelectedEntries([])}
                  className="ml-2"
                >
                  Clear
                </Button>
              </div>
              <Button
                size="sm"
                color="blue"
                onClick={() => setShowBatchActionModal(true)}
              >
                <HiOutlineAdjustments className="mr-1 h-4 w-4" />
                Batch Actions
              </Button>
            </div>
          )}

          <div className="overflow-x-auto">
            <Table striped className="text-sm">
              <Table.Head className="bg-gray-50">
                <Table.HeadCell className="py-2 w-10">
                  <Checkbox
                    checked={timeEntries.length > 0 && selectedEntries.length === timeEntries.length}
                    onChange={selectAllEntries}
                  />
                </Table.HeadCell>
                <Table.HeadCell className="py-2">Date</Table.HeadCell>
                <Table.HeadCell className="py-2">Employee</Table.HeadCell>
                <Table.HeadCell className="py-2">Status</Table.HeadCell>
                <Table.HeadCell className="py-2">Time</Table.HeadCell>
                <Table.HeadCell className="py-2">Hours</Table.HeadCell>
                <Table.HeadCell className="py-2">Actions</Table.HeadCell>
              </Table.Head>
              <Table.Body>
                {timeEntries.length > 0 ? (
                  timeEntries.map((entry) => (
                    <Table.Row
                      key={entry.id}
                      className={`hover:bg-gray-50 ${selectedEntries.includes(entry.id) ? 'bg-blue-50' : ''}`}
                    >
                      <Table.Cell className="py-2">
                        <Checkbox
                          checked={selectedEntries.includes(entry.id)}
                          onChange={() => toggleEntrySelection(entry.id)}
                        />
                      </Table.Cell>
                      <Table.Cell className="py-2 whitespace-nowrap">
                        <div className="font-medium">
                          {entry.date
                            ? format(new Date(entry.date + 'T00:00:00'), 'MMM d, yyyy')
                            : 'Unknown Date'}
                        </div>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {entry.is_rest_day && (
                            <Badge color="purple" size="xs">Rest Day</Badge>
                          )}
                          {entry.is_holiday && (
                            <Badge color="info" size="xs">Holiday</Badge>
                          )}
                          {/* Check if this is a split shift by looking at the shift_group */}
                          {entry.shift_group && timeEntries.filter(e =>
                            e.shift_group === entry.shift_group
                          ).length > 1 && (
                            <Badge color="dark" size="xs">Split Shift</Badge>
                          )}
                        </div>
                      </Table.Cell>
                      <Table.Cell className="py-2">
                        <div className="font-medium">{getEmployeeName(entry.employee_id)}</div>
                        <div className="text-xs text-gray-500">
                          {employees.find(e => e.id === entry.employee_id)?.position?.title || ''}
                        </div>
                      </Table.Cell>
                      <Table.Cell className="py-2">
                        {renderStatusBadge(entry.status as TimeEntryStatus)}
                      </Table.Cell>
                      <Table.Cell className="py-2">
                        {entry.status === TimeEntryStatus.PRESENT ? (
                          <div>
                            <div className="flex items-center">
                              <HiOutlineClock className="mr-1 h-3 w-3 text-gray-500" />
                              <span>{entry.time_in ? formatTime(entry.time_in) : 'N/A'}</span>
                            </div>
                            <div className="flex items-center mt-1">
                              <HiOutlineClock className="mr-1 h-3 w-3 text-gray-500" />
                              <span>{entry.time_out ? formatTime(entry.time_out) : 'N/A'}</span>
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-500">N/A</span>
                        )}
                      </Table.Cell>
                      <Table.Cell className="py-2">
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-blue-500 mr-1"></div>
                          <span>{entry.regular_hours} reg</span>
                        </div>
                        {entry.overtime_hours > 0 && (
                          <div className="flex items-center mt-1">
                            <div className="w-3 h-3 rounded-full bg-orange-500 mr-1"></div>
                            <span>{entry.overtime_hours} OT</span>
                          </div>
                        )}
                        {entry.night_diff_hours > 0 && (
                          <div className="flex items-center mt-1">
                            <div className="w-3 h-3 rounded-full bg-indigo-500 mr-1"></div>
                            <span>{entry.night_diff_hours} night</span>
                            <HiOutlineMoon className="ml-1 h-3 w-3 text-indigo-600" />
                          </div>
                        )}
                      </Table.Cell>
                      <Table.Cell className="py-2">
                        <div className="flex space-x-1">
                          <Tooltip content="Edit">
                            <Button
                              size="xs"
                              color="light"
                              as="a"
                              href={`/payroll/edit-time-entry/${entry.id}`}
                            >
                              <HiOutlinePencil className="h-3 w-3" />
                            </Button>
                          </Tooltip>
                          <Tooltip content="Delete">
                            <Button
                              size="xs"
                              color="light"
                              onClick={() => {
                                setCurrentEntry(entry);
                                setShowDeleteModal(true);
                              }}
                            >
                              <HiOutlineTrash className="h-3 w-3 text-red-500" />
                            </Button>
                          </Tooltip>
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  ))
                ) : (
                  <Table.Row>
                    <Table.Cell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center text-gray-500">
                        <HiOutlineCalendarDays className="h-12 w-12 mb-2" />
                        <p className="text-lg font-medium">No time entries found</p>
                        <p className="text-sm">Try adjusting your filters or add a new time entry</p>
                        <Button
                          color="blue"
                          size="sm"
                          className="mt-4"
                          as="a"
                          href="/payroll/add-time-entry"
                        >
                          <HiOutlinePlus className="mr-2 h-4 w-4" />
                          Add Time Entry
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                )}
              </Table.Body>
            </Table>
          </div>

          {totalCount > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(totalCount / itemsPerPage)}
              itemsPerPage={itemsPerPage}
              totalItems={totalCount}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={(newItemsPerPage) => {
                setItemsPerPage(newItemsPerPage);
                setCurrentPage(1); // Reset to first page when changing items per page
              }}
              itemName="entries"
            />
          )}
        </div>
      </Card>



      {/* Delete Confirmation Modal */}
      <div className={`${showDeleteModal ? 'fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50' : 'hidden'}`}>
        <Modal
          show={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          size="md"
          className="mx-auto"
          popup={false}
        >
        <Modal.Header>Delete Time Entry</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>
              Are you sure you want to delete this time entry for{' '}
              <span className="font-medium">
                {currentEntry?.employee_id ? getEmployeeName(currentEntry.employee_id) : 'Unknown Employee'}
              </span>{' '}
              on{' '}
              <span className="font-medium">
                {currentEntry?.date ? format(new Date(currentEntry.date + 'T00:00:00'), 'MMMM d, yyyy') : 'Unknown Date'}
              </span>?
            </p>
            {submitError && (
              <Alert color="failure" icon={HiOutlineExclamationCircle}>
                {submitError}
              </Alert>
            )}
            <div className="flex justify-end space-x-2">
              <Button
                color="gray"
                onClick={() => setShowDeleteModal(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                color="failure"
                onClick={handleDeleteTimeEntry}
                isProcessing={isSubmitting}
              >
                Delete
              </Button>
            </div>
          </div>
        </Modal.Body>
        </Modal>
      </div>

      {/* Batch Action Modal */}
      <Modal
        show={showBatchActionModal}
        onClose={() => setShowBatchActionModal(false)}
        size="md"
      >
        <Modal.Header>Batch Update {selectedEntries.length} Entries</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <div>
              <Label htmlFor="batchAction" value="Select Field to Update" className="mb-2 block" />
              <Select
                id="batchAction"
                value={batchAction}
                onChange={(e) => {
                  setBatchAction(e.target.value);
                  setBatchActionValue(null);
                }}
                required
              >
                <option value="">Select a field...</option>
                <option value="status">Status</option>
                <option value="is_rest_day">Rest Day</option>
                <option value="is_holiday">Holiday</option>
                <option value="exclude_lunch_break">Exclude Lunch Break</option>
              </Select>
            </div>

            {batchAction === 'status' && (
              <div>
                <Label htmlFor="statusValue" value="New Status" className="mb-2 block" />
                <Select
                  id="statusValue"
                  value={batchActionValue || ''}
                  onChange={(e) => setBatchActionValue(e.target.value)}
                  required
                >
                  <option value={TimeEntryStatus.PRESENT}>Present</option>
                  <option value={TimeEntryStatus.ABSENT}>Absent</option>
                  <option value={TimeEntryStatus.LEAVE}>Leave</option>
                  <option value={TimeEntryStatus.HOLIDAY}>Holiday</option>
                </Select>
              </div>
            )}

            {(batchAction === 'is_rest_day' || batchAction === 'is_holiday' || batchAction === 'exclude_lunch_break') && (
              <div>
                <Label htmlFor="boolValue" value={`Set ${batchAction.replace('_', ' ').replace('is', '').trim()} to:`} className="mb-2 block" />
                <div className="flex space-x-4">
                  <div className="flex items-center">
                    <input
                      id="boolTrue"
                      type="radio"
                      name="boolValue"
                      value="true"
                      checked={batchActionValue === true}
                      onChange={() => setBatchActionValue(true)}
                      className="h-4 w-4 text-blue-600"
                    />
                    <Label htmlFor="boolTrue" className="ml-2">Yes</Label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="boolFalse"
                      type="radio"
                      name="boolValue"
                      value="false"
                      checked={batchActionValue === false}
                      onChange={() => setBatchActionValue(false)}
                      className="h-4 w-4 text-blue-600"
                    />
                    <Label htmlFor="boolFalse" className="ml-2">No</Label>
                  </div>
                </div>
              </div>
            )}

            {submitError && (
              <Alert color="failure" icon={HiOutlineExclamationCircle}>
                {submitError}
              </Alert>
            )}

            <div className="flex justify-end space-x-2 pt-2">
              <Button
                color="gray"
                onClick={() => setShowBatchActionModal(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                color="blue"
                onClick={applyBatchAction}
                isProcessing={isSubmitting}
                disabled={!batchAction || batchActionValue === null}
              >
                Apply to {selectedEntries.length} Entries
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default TimeEntries;
