import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON>ton,
  Card,
  Table,
  Spinner,
  Alert,
  Modal,
  Select,
  Tabs
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineExclamationCircle,
  HiOutlineDocumentDownload,
  HiOutlineMailOpen,
  HiOutlinePrinter,
  HiOutlineEye
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getPayrollPeriodById } from '../../services/payroll';
import { generatePayslipHTML } from '../../services/payslipGeneration';
import { 
  PayrollPeriodWithDetails, 
  PayrollItemWithDetails,
  PayslipTemplate 
} from '../../types/payroll';
import PageTitle from '../../components/shared/PageTitle';
import { formatCurrency } from '../../utils/formatters';

const PayrollPayslips: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const [payrollPeriod, setPayrollPeriod] = useState<PayrollPeriodWithDetails | null>(null);
  const [selectedItem, setSelectedItem] = useState<PayrollItemWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showPayslipModal, setShowPayslipModal] = useState<boolean>(false);
  const [payslipHtml, setPayslipHtml] = useState<string>('');
  const [isGeneratingPayslip, setIsGeneratingPayslip] = useState<boolean>(false);
  const [payslipError, setPayslipError] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<PayslipTemplate>(PayslipTemplate.DEFAULT);

  // Fetch payroll period details
  const fetchPayrollPeriod = async () => {
    if (!currentOrganization || !id) return;

    setIsLoading(true);
    setError(null);

    try {
      const { period, error } = await getPayrollPeriodById(currentOrganization.id, id, true);

      if (error) {
        setError(error);
      } else if (period) {
        setPayrollPeriod(period);
      } else {
        setError('Payroll period not found');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Load payroll period when the component mounts
  useEffect(() => {
    fetchPayrollPeriod();
  }, [currentOrganization, id]);

  // Generate payslip for an employee
  const handleGeneratePayslip = async (item: PayrollItemWithDetails) => {
    if (!currentOrganization) return;

    setSelectedItem(item);
    setIsGeneratingPayslip(true);
    setPayslipError(null);
    setShowPayslipModal(true);

    try {
      const { html, error } = await generatePayslipHTML(
        currentOrganization.id,
        item.id,
        selectedTemplate
      );

      if (error) {
        setPayslipError(error);
      } else if (html) {
        setPayslipHtml(html);
      }
    } catch (err: any) {
      setPayslipError(err.message);
    } finally {
      setIsGeneratingPayslip(false);
    }
  };

  // Print payslip
  const handlePrintPayslip = () => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(payslipHtml);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    }
  };

  // Download payslip as HTML
  const handleDownloadPayslip = () => {
    const blob = new Blob([payslipHtml], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `payslip-${selectedItem?.employee?.last_name}-${payrollPeriod?.name.replace(/\s+/g, '-')}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // If loading, show a spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamationCircle}>
          <span className="font-medium">Error!</span> {error}
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/payroll')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Payroll Periods
          </Button>
        </div>
      </div>
    );
  }

  // If payroll period not found, show message
  if (!payrollPeriod) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamationCircle}>
          <span className="font-medium">Error!</span> Payroll period not found
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/payroll')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Payroll Periods
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button color="gray" className="mr-4" onClick={() => navigate(`/payroll/periods/${id}`)}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back
          </Button>
          <PageTitle title={`Payslips: ${payrollPeriod.name}`} />
        </div>
        <div className="flex space-x-2">
          <div className="w-48">
            <Select
              value={selectedTemplate}
              onChange={(e) => setSelectedTemplate(e.target.value as PayslipTemplate)}
            >
              <option value={PayslipTemplate.DEFAULT}>Default Template</option>
              <option value={PayslipTemplate.COMPACT}>Compact Template</option>
              <option value={PayslipTemplate.DETAILED}>Detailed Template</option>
            </Select>
          </div>
          <Button color="primary">
            <HiOutlineMailOpen className="mr-2 h-5 w-5" />
            Email All Payslips
          </Button>
        </div>
      </div>

      <Card>
        <div className="overflow-x-auto">
          <Table striped>
            <Table.Head>
              <Table.HeadCell>Employee</Table.HeadCell>
              <Table.HeadCell>Employee ID</Table.HeadCell>
              <Table.HeadCell>Basic Pay</Table.HeadCell>
              <Table.HeadCell>Gross Pay</Table.HeadCell>
              <Table.HeadCell>Deductions</Table.HeadCell>
              <Table.HeadCell>Net Pay</Table.HeadCell>
              <Table.HeadCell>Actions</Table.HeadCell>
            </Table.Head>
            <Table.Body>
              {payrollPeriod.payroll_items && payrollPeriod.payroll_items.length > 0 ? (
                payrollPeriod.payroll_items.map((item) => (
                  <Table.Row key={item.id}>
                    <Table.Cell className="font-medium">
                      {item.employee ? (
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                            {item.employee.profile_image_url ? (
                              <img
                                src={item.employee.profile_image_url}
                                alt={`${item.employee.first_name} ${item.employee.last_name}`}
                                className="w-8 h-8 rounded-full object-cover"
                              />
                            ) : (
                              <span className="text-xs font-semibold">
                                {item.employee.first_name?.[0]}{item.employee.last_name?.[0]}
                              </span>
                            )}
                          </div>
                          <div>
                            <p className="font-medium">
                              {item.employee.first_name} {item.employee.last_name}
                            </p>
                          </div>
                        </div>
                      ) : (
                        'Unknown Employee'
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {item.employee?.employee_number || 'N/A'}
                    </Table.Cell>
                    <Table.Cell>{formatCurrency(Number(item.basic_pay))}</Table.Cell>
                    <Table.Cell>{formatCurrency(Number(item.gross_pay))}</Table.Cell>
                    <Table.Cell>{formatCurrency(Number(item.total_deductions))}</Table.Cell>
                    <Table.Cell className="font-medium text-green-600">
                      {formatCurrency(Number(item.net_pay))}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex space-x-2">
                        <Button
                          size="xs"
                          color="info"
                          onClick={() => handleGeneratePayslip(item)}
                        >
                          <HiOutlineEye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="xs"
                          color="success"
                          onClick={() => {
                            handleGeneratePayslip(item).then(() => {
                              if (!payslipError) {
                                handlePrintPayslip();
                              }
                            });
                          }}
                        >
                          <HiOutlinePrinter className="h-4 w-4" />
                        </Button>
                        <Button
                          size="xs"
                          color="light"
                          onClick={() => {
                            handleGeneratePayslip(item).then(() => {
                              if (!payslipError) {
                                handleDownloadPayslip();
                              }
                            });
                          }}
                        >
                          <HiOutlineDocumentDownload className="h-4 w-4" />
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center py-4">
                    No payroll items found for this period.
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table>
        </div>
      </Card>

      {/* Payslip Modal */}
      <Modal
        show={showPayslipModal}
        onClose={() => setShowPayslipModal(false)}
        size="xl"
      >
        <Modal.Header>
          Payslip: {selectedItem?.employee?.first_name} {selectedItem?.employee?.last_name}
        </Modal.Header>
        <Modal.Body>
          {isGeneratingPayslip ? (
            <div className="flex justify-center items-center h-64">
              <Spinner size="xl" />
            </div>
          ) : payslipError ? (
            <Alert color="failure" icon={HiOutlineExclamationCircle}>
              <span className="font-medium">Error!</span> {payslipError}
            </Alert>
          ) : (
            <div className="flex flex-col space-y-4">
              <div className="flex justify-end space-x-2">
                <Button color="light" onClick={handleDownloadPayslip}>
                  <HiOutlineDocumentDownload className="mr-2 h-5 w-5" />
                  Download
                </Button>
                <Button color="primary" onClick={handlePrintPayslip}>
                  <HiOutlinePrinter className="mr-2 h-5 w-5" />
                  Print
                </Button>
              </div>
              <div className="border rounded-lg p-4 bg-white">
                <iframe
                  srcDoc={payslipHtml}
                  title="Payslip Preview"
                  className="w-full h-[600px]"
                  frameBorder="0"
                />
              </div>
            </div>
          )}
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default PayrollPayslips;
