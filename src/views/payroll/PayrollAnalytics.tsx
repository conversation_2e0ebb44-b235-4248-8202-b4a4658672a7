import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  Card,
  Spinner,
  Alert,
  Label,
  Select
} from 'flowbite-react';
import {
  HiOutlineDocumentReport,
  HiOutlineExclamationCircle,
  HiOutlineChartPie,
  HiOutlineChartBar,
  HiOutlineCurrencyDollar,
  HiOutlineUsers,
  HiOutlineCalendar
} from 'react-icons/hi';
import { format, subMonths, startOfYear, endOfYear } from 'date-fns';
import { useOrganization } from '../../context/OrganizationContext';
import { getPayrollSummaryReport } from '../../services/payrollReporting';
import { PayrollSummary } from '../../types/payroll';
import PageTitle from '../../components/shared/PageTitle';
import { formatCurrency } from '../../utils/formatters';
import ReactApexChart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';

const PayrollAnalytics: React.FC = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const [timeRange, setTimeRange] = useState<string>('year');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [summaryData, setSummaryData] = useState<PayrollSummary[]>([]);

  // Derived metrics
  const totalGrossPay = summaryData.reduce((sum, period) => sum + period.totalGrossPay, 0);
  const totalNetPay = summaryData.reduce((sum, period) => sum + period.totalNetPay, 0);
  const totalDeductions = summaryData.reduce((sum, period) => sum + period.totalDeductions, 0);
  const totalAllowances = summaryData.reduce((sum, period) => sum + period.totalAllowances, 0);
  const averageEmployees = summaryData.length > 0
    ? Math.round(summaryData.reduce((sum, period) => sum + period.totalEmployees, 0) / summaryData.length)
    : 0;
  const averageNetPay = averageEmployees > 0
    ? totalNetPay / (averageEmployees * summaryData.length)
    : 0;

  // Load payroll summary data
  useEffect(() => {
    if (!currentOrganization) return;

    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Determine date range based on selected time range
        let startDate, endDate;
        const now = new Date();

        switch (timeRange) {
          case 'quarter':
            startDate = subMonths(now, 3);
            endDate = now;
            break;
          case 'half_year':
            startDate = subMonths(now, 6);
            endDate = now;
            break;
          case 'year':
          default:
            startDate = startOfYear(now);
            endDate = endOfYear(now);
            break;
        }

        const { data, error } = await getPayrollSummaryReport({
          organizationId: currentOrganization.id,
          startDate,
          endDate
        });

        if (error) {
          throw new Error(error);
        }

        if (data) {
          // Sort by date
          const sortedData = [...data].sort((a, b) =>
            new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
          );
          setSummaryData(sortedData);
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentOrganization, timeRange]);

  // Prepare chart data
  const prepareChartData = () => {
    // Payroll distribution chart
    const payrollDistributionOptions: ApexOptions = {
      chart: {
        type: 'donut',
      },
      labels: ['Net Pay', 'Deductions', 'Allowances'],
      colors: ['#10B981', '#EF4444', '#3B82F6'],
      legend: {
        position: 'bottom'
      },
      plotOptions: {
        pie: {
          donut: {
            labels: {
              show: true,
              total: {
                show: true,
                formatter: function (w) {
                  const total = w.globals.seriesTotals.reduce((a: number, b: number) => a + b, 0);
                  return total > 0 ? formatCurrency(total) : formatCurrency(0);
                }
              }
            }
          }
        }
      },
      dataLabels: {
        formatter: function (val: number) {
          return isNaN(val) ? '0%' : val.toFixed(1) + '%';
        }
      },
      tooltip: {
        y: {
          formatter: function (val: number) {
            return formatCurrency(val);
          }
        }
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            width: 300
          },
          legend: {
            position: 'bottom'
          }
        }
      }]
    };

    const payrollDistributionSeries = [
      totalNetPay || 0,
      totalDeductions || 0,
      totalAllowances || 0
    ];

    // Payroll trend chart
    const payrollTrendOptions: ApexOptions = {
      chart: {
        type: 'area',
        height: 350,
        toolbar: {
          show: false
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: 'smooth'
      },
      xaxis: {
        type: 'category',
        categories: summaryData.map(period => format(new Date(period.startDate), 'MMM yyyy'))
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return formatCurrency(val);
          }
        }
      },
      colors: ['#10B981', '#EF4444', '#3B82F6'],
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.9,
          stops: [0, 90, 100]
        }
      }
    };

    const payrollTrendSeries = [
      {
        name: 'Gross Pay',
        data: summaryData.map(period => period.totalGrossPay)
      },
      {
        name: 'Deductions',
        data: summaryData.map(period => period.totalDeductions)
      },
      {
        name: 'Net Pay',
        data: summaryData.map(period => period.totalNetPay)
      }
    ];

    // Employee count chart
    const employeeCountOptions: ApexOptions = {
      chart: {
        type: 'bar',
        height: 350,
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
        },
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        type: 'category',
        categories: summaryData.map(period => format(new Date(period.startDate), 'MMM yyyy'))
      },
      yaxis: {
        title: {
          text: 'Employees'
        }
      },
      fill: {
        opacity: 1
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + " employees";
          }
        }
      },
      colors: ['#6366F1']
    };

    const employeeCountSeries = [
      {
        name: 'Employees',
        data: summaryData.map(period => period.totalEmployees)
      }
    ];

    return {
      payrollDistribution: {
        options: payrollDistributionOptions,
        series: payrollDistributionSeries
      },
      payrollTrend: {
        options: payrollTrendOptions,
        series: payrollTrendSeries
      },
      employeeCount: {
        options: employeeCountOptions,
        series: employeeCountSeries
      }
    };
  };

  const chartData = prepareChartData();

  return (
    <div className="container mx-auto px-4 py-8">
      <PageTitle
        title="Payroll Analytics"
        subtitle="Visualize and analyze payroll data"
      />

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4">
          <Label htmlFor="time-range" value="Time Range" />
          <Select
            id="time-range"
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="w-40"
          >
            <option value="quarter">Last Quarter</option>
            <option value="half_year">Last 6 Months</option>
            <option value="year">This Year</option>
          </Select>
        </div>

        <Button
          color="light"
          onClick={() => navigate('/payroll/reports')}
        >
          <HiOutlineDocumentReport className="mr-2 h-5 w-5" />
          View Detailed Reports
        </Button>
      </div>

      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <Spinner size="xl" />
        </div>
      )}

      {error && (
        <Alert color="failure" icon={HiOutlineExclamationCircle}>
          <span className="font-medium">Error!</span> {error}
        </Alert>
      )}

      {!isLoading && !error && summaryData.length === 0 && (
        <Alert color="info" icon={HiOutlineExclamationCircle}>
          <span className="font-medium">No data available.</span> There are no payroll periods in the selected time range.
        </Alert>
      )}

      {!isLoading && !error && summaryData.length > 0 && (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <div className="flex items-center">
                <div className="inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-green-100 text-green-600">
                  <HiOutlineCurrencyDollar className="h-6 w-6" />
                </div>
                <div className="ml-4">
                  <h5 className="text-xl font-bold leading-none text-gray-900">
                    {formatCurrency(totalNetPay)}
                  </h5>
                  <p className="text-sm font-normal text-gray-500">
                    Total Net Pay
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-blue-100 text-blue-600">
                  <HiOutlineChartBar className="h-6 w-6" />
                </div>
                <div className="ml-4">
                  <h5 className="text-xl font-bold leading-none text-gray-900">
                    {formatCurrency(totalGrossPay)}
                  </h5>
                  <p className="text-sm font-normal text-gray-500">
                    Total Gross Pay
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-red-100 text-red-600">
                  <HiOutlineChartPie className="h-6 w-6" />
                </div>
                <div className="ml-4">
                  <h5 className="text-xl font-bold leading-none text-gray-900">
                    {formatCurrency(totalDeductions)}
                  </h5>
                  <p className="text-sm font-normal text-gray-500">
                    Total Deductions
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-purple-100 text-purple-600">
                  <HiOutlineUsers className="h-6 w-6" />
                </div>
                <div className="ml-4">
                  <h5 className="text-xl font-bold leading-none text-gray-900">
                    {averageEmployees}
                  </h5>
                  <p className="text-sm font-normal text-gray-500">
                    Average Employees
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <Card>
              <h5 className="text-lg font-bold leading-none text-gray-900 mb-4">
                Payroll Distribution
              </h5>
              <ReactApexChart
                options={chartData.payrollDistribution.options}
                series={chartData.payrollDistribution.series}
                type="donut"
                height={350}
              />
            </Card>

            <Card>
              <h5 className="text-lg font-bold leading-none text-gray-900 mb-4">
                Employee Count Trend
              </h5>
              <ReactApexChart
                options={chartData.employeeCount.options}
                series={chartData.employeeCount.series}
                type="bar"
                height={350}
              />
            </Card>
          </div>

          <Card>
            <h5 className="text-lg font-bold leading-none text-gray-900 mb-4">
              Payroll Trend
            </h5>
            <ReactApexChart
              options={chartData.payrollTrend.options}
              series={chartData.payrollTrend.series}
              type="area"
              height={350}
            />
          </Card>
        </>
      )}
    </div>
  );
};

export default PayrollAnalytics;
