import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useOrganization } from '../../context/OrganizationContext';
import { format } from 'date-fns';
import {
  getPayrollSummaryReport,
  getEmployeeEarningsReport,
  getGovernmentContributionsReport,
  getBIRWithholdingTaxReport
} from '../../services/payrollReporting';
import { PayrollReportType, PayrollReportFormat } from '../../types/payroll';
import { formatCurrency } from '../../utils/formatters';

const PayrollReportsEnhanced: React.FC = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const [activeReport, setActiveReport] = useState<string>('summary');
  const [startDate, setStartDate] = useState<Date>(new Date(new Date().getFullYear(), 0, 1));
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [reportFormat, setReportFormat] = useState<string>('html');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<any>(null);

  // Generate report
  const generateReport = async (reportType: string) => {
    if (!currentOrganization) return;

    setIsLoading(true);
    setError(null);
    setReportData(null);
    setActiveReport(reportType);

    try {
      const options = {
        organizationId: currentOrganization.id,
        startDate,
        endDate,
        reportType: reportType as PayrollReportType,
        format: reportFormat as PayrollReportFormat
      };

      let result;
      switch (reportType) {
        case 'summary':
          result = await getPayrollSummaryReport(options);
          break;
        case 'employee_earnings':
          result = await getEmployeeEarningsReport(options);
          break;
        case 'government_contributions':
          result = await getGovernmentContributionsReport(options);
          break;
        case 'bir_withholding_tax':
          result = await getBIRWithholdingTaxReport(options);
          break;
        default:
          throw new Error('Invalid report type');
      }

      if (result.error) {
        setError(result.error);
      } else {
        setReportData(result.data);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle date change
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>, isStart: boolean) => {
    const date = new Date(e.target.value);
    if (isStart) {
      setStartDate(date);
    } else {
      setEndDate(date);
    }
  };

  // Format date for input
  const formatDateForInput = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  // Render summary report
  const renderSummaryReport = () => {
    if (!reportData || reportData.length === 0) {
      return (
        <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400">
          <p className="text-yellow-700">No data available for the selected period.</p>
        </div>
      );
    }

    const totalGrossPay = reportData.reduce((sum: number, period: any) => sum + period.totalGrossPay, 0);
    const totalNetPay = reportData.reduce((sum: number, period: any) => sum + period.totalNetPay, 0);
    const totalDeductions = reportData.reduce((sum: number, period: any) => sum + period.totalDeductions, 0);
    const totalAllowances = reportData.reduce((sum: number, period: any) => sum + period.totalAllowances, 0);

    return (
      <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm text-blue-700">Total Gross Pay</p>
            <p className="text-xl font-bold text-blue-700">{formatCurrency(totalGrossPay)}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm text-green-700">Total Net Pay</p>
            <p className="text-xl font-bold text-green-700">{formatCurrency(totalNetPay)}</p>
          </div>
          <div className="bg-red-50 p-4 rounded-lg">
            <p className="text-sm text-red-700">Total Deductions</p>
            <p className="text-xl font-bold text-red-700">{formatCurrency(totalDeductions)}</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <p className="text-sm text-purple-700">Total Allowances</p>
            <p className="text-xl font-bold text-purple-700">{formatCurrency(totalAllowances)}</p>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-500">
            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3">Period</th>
                <th scope="col" className="px-6 py-3">Date Range</th>
                <th scope="col" className="px-6 py-3">Employees</th>
                <th scope="col" className="px-6 py-3">Gross Pay</th>
                <th scope="col" className="px-6 py-3">Deductions</th>
                <th scope="col" className="px-6 py-3">Net Pay</th>
                <th scope="col" className="px-6 py-3">Status</th>
              </tr>
            </thead>
            <tbody>
              {reportData.map((period: any) => (
                <tr key={period.periodId} className="bg-white border-b hover:bg-gray-50">
                  <td className="px-6 py-4 font-medium text-gray-900">
                    {period.periodName}
                    {period.isThirteenthMonth && ' (13th Month)'}
                  </td>
                  <td className="px-6 py-4">
                    {format(new Date(period.startDate), 'MMM d, yyyy')} - {format(new Date(period.endDate), 'MMM d, yyyy')}
                  </td>
                  <td className="px-6 py-4">{period.totalEmployees}</td>
                  <td className="px-6 py-4">{formatCurrency(period.totalGrossPay)}</td>
                  <td className="px-6 py-4">{formatCurrency(period.totalDeductions)}</td>
                  <td className="px-6 py-4 font-medium text-green-600">{formatCurrency(period.totalNetPay)}</td>
                  <td className="px-6 py-4">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      period.status === 'paid' ? 'bg-green-100 text-green-800' :
                      period.status === 'approved' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {period.status.charAt(0).toUpperCase() + period.status.slice(1)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr className="font-semibold text-gray-900">
                <th scope="row" className="px-6 py-3 text-base" colSpan={3}>Totals</th>
                <td className="px-6 py-3">{formatCurrency(totalGrossPay)}</td>
                <td className="px-6 py-3">{formatCurrency(totalDeductions)}</td>
                <td className="px-6 py-3 text-green-600">{formatCurrency(totalNetPay)}</td>
                <td className="px-6 py-3"></td>
              </tr>
            </tfoot>
          </table>
        </div>
      </>
    );
  };

  // Render government contributions report
  const renderGovernmentContributionsReport = () => {
    if (!reportData || !reportData.items || reportData.items.length === 0) {
      return (
        <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400">
          <p className="text-yellow-700">No data available for the selected period.</p>
        </div>
      );
    }

    const { items, summary } = reportData;

    return (
      <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm text-blue-700">Total SSS</p>
            <p className="text-xl font-bold text-blue-700">{formatCurrency(summary.totalSSS)}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm text-green-700">Total PhilHealth</p>
            <p className="text-xl font-bold text-green-700">{formatCurrency(summary.totalPhilHealth)}</p>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <p className="text-sm text-yellow-700">Total Pag-IBIG</p>
            <p className="text-xl font-bold text-yellow-700">{formatCurrency(summary.totalPagibig)}</p>
          </div>
          <div className="bg-red-50 p-4 rounded-lg">
            <p className="text-sm text-red-700">Total Withholding Tax</p>
            <p className="text-xl font-bold text-red-700">{formatCurrency(summary.totalWithholdingTax)}</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <p className="text-sm text-purple-700">Total Contributions</p>
            <p className="text-xl font-bold text-purple-700">
              {formatCurrency(summary.totalContributions + summary.totalWithholdingTax)}
            </p>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-500">
            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3">Employee</th>
                <th scope="col" className="px-6 py-3">SSS</th>
                <th scope="col" className="px-6 py-3">PhilHealth</th>
                <th scope="col" className="px-6 py-3">Pag-IBIG</th>
                <th scope="col" className="px-6 py-3">Withholding Tax</th>
                <th scope="col" className="px-6 py-3">Total</th>
              </tr>
            </thead>
            <tbody>
              {items.map((item: any) => {
                const sss = Number(item.sss_contribution || 0);
                const philhealth = Number(item.philhealth_contribution || 0);
                const pagibig = Number(item.pagibig_contribution || 0);
                const tax = Number(item.withholding_tax || 0);
                const total = sss + philhealth + pagibig + tax;

                return (
                  <tr key={item.id} className="bg-white border-b hover:bg-gray-50">
                    <td className="px-6 py-4 font-medium text-gray-900">
                      {item.employee.first_name} {item.employee.last_name}
                      <div className="text-xs text-gray-500">
                        {item.employee.employee_number || 'No ID'}
                      </div>
                    </td>
                    <td className="px-6 py-4">{formatCurrency(sss)}</td>
                    <td className="px-6 py-4">{formatCurrency(philhealth)}</td>
                    <td className="px-6 py-4">{formatCurrency(pagibig)}</td>
                    <td className="px-6 py-4">{formatCurrency(tax)}</td>
                    <td className="px-6 py-4 font-medium">{formatCurrency(total)}</td>
                  </tr>
                );
              })}
            </tbody>
            <tfoot>
              <tr className="font-semibold text-gray-900">
                <th scope="row" className="px-6 py-3 text-base">Totals</th>
                <td className="px-6 py-3">{formatCurrency(summary.totalSSS)}</td>
                <td className="px-6 py-3">{formatCurrency(summary.totalPhilHealth)}</td>
                <td className="px-6 py-3">{formatCurrency(summary.totalPagibig)}</td>
                <td className="px-6 py-3">{formatCurrency(summary.totalWithholdingTax)}</td>
                <td className="px-6 py-3">{formatCurrency(summary.totalContributions + summary.totalWithholdingTax)}</td>
              </tr>
            </tfoot>
          </table>
        </div>
      </>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-4">
        <h1 className="text-2xl font-bold">Payroll Reports</h1>
        <p className="text-gray-500">Generate and view payroll reports</p>
      </div>

      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Report Filters</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              className="w-full rounded-lg border border-gray-300 p-2.5"
              value={formatDateForInput(startDate)}
              onChange={(e) => handleDateChange(e, true)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              className="w-full rounded-lg border border-gray-300 p-2.5"
              value={formatDateForInput(endDate)}
              onChange={(e) => handleDateChange(e, false)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Report Format
            </label>
            <select
              className="w-full rounded-lg border border-gray-300 p-2.5"
              value={reportFormat}
              onChange={(e) => setReportFormat(e.target.value)}
            >
              <option value="html">HTML</option>
              <option value="pdf">PDF</option>
              <option value="csv">CSV</option>
              <option value="excel">Excel</option>
            </select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div
          className={`border rounded-lg p-4 hover:bg-gray-50 cursor-pointer ${activeReport === 'summary' ? 'bg-blue-50 border-blue-300' : ''}`}
          onClick={() => generateReport('summary')}
        >
          <h3 className="text-lg font-medium mb-2">Summary Report</h3>
          <p className="text-gray-600 mb-4">
            Generate a summary report of payroll periods showing total amounts for each period.
          </p>
          <button
            className={`px-4 py-2 rounded ${activeReport === 'summary' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'} hover:bg-blue-700 hover:text-white`}
            onClick={(e) => { e.stopPropagation(); generateReport('summary'); }}
            disabled={isLoading}
          >
            {isLoading && activeReport === 'summary' ? 'Generating...' : 'Generate Report'}
          </button>
        </div>

        <div
          className={`border rounded-lg p-4 hover:bg-gray-50 cursor-pointer ${activeReport === 'government_contributions' ? 'bg-blue-50 border-blue-300' : ''}`}
          onClick={() => generateReport('government_contributions')}
        >
          <h3 className="text-lg font-medium mb-2">Government Contributions</h3>
          <p className="text-gray-600 mb-4">
            Generate a report of government contributions (SSS, PhilHealth, Pag-IBIG, and Withholding Tax).
          </p>
          <div className="flex space-x-2">
            <button
              className={`px-4 py-2 rounded ${activeReport === 'government_contributions' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'} hover:bg-blue-700 hover:text-white`}
              onClick={(e) => { e.stopPropagation(); generateReport('government_contributions'); }}
              disabled={isLoading}
            >
              {isLoading && activeReport === 'government_contributions' ? 'Generating...' : 'Generate Report'}
            </button>
            <button
              className="px-4 py-2 rounded bg-green-600 text-white hover:bg-green-700"
              onClick={(e) => { e.stopPropagation(); navigate('/payroll/reports/government-contributions'); }}
            >
              Enhanced View
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="p-4 mb-6 bg-red-50 border-l-4 border-red-400">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {isLoading && (
        <div className="flex justify-center items-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {!isLoading && reportData && (
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">
              {activeReport === 'summary' ? 'Payroll Summary Report' :
               activeReport === 'government_contributions' ? 'Government Contributions Report' :
               'Report Results'}
            </h2>
            <button
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 flex items-center"
              onClick={() => window.print()}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              Print/Download
            </button>
          </div>

          {activeReport === 'summary' && renderSummaryReport()}
          {activeReport === 'government_contributions' && renderGovernmentContributionsReport()}
        </div>
      )}
    </div>
  );
};

export default PayrollReportsEnhanced;
