import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Spinner,
  Alert,
  TextInput,
  Select,
  Label,
  Checkbox,
  Tooltip,
} from 'flowbite-react';
import {
  HiOutlineInformationCircle,
  HiOutlineArrowLeft,
  HiOutlineExclamationCircle,
  HiOutlineMoon,
} from 'react-icons/hi';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { format, parseISO, isValid, differenceInMinutes } from 'date-fns';
import { useOrganization } from '../../context/OrganizationContext';
import { getEmployees, Employee } from '../../services/employee';
import { TimeEntryStatus } from '../../types/payroll';
import PageTitle from '../../components/shared/PageTitle';
import EmployeeSearchSelect from '../../components/payroll/EmployeeSearchSelect';

interface TimeEntry {
  id: string;
  organization_id: string;
  employee_id: string;
  date: string;
  time_in?: string;
  time_out?: string;
  break_start?: string;
  break_end?: string;
  status: TimeEntryStatus;
  regular_hours: number;
  overtime_hours: number;
  night_diff_hours: number;
  exclude_lunch_break: boolean;
  is_rest_day: boolean;
  is_holiday: boolean;
  holiday_type?: string;
  created_at: string;
  updated_at: string;
}

import { getTimeEntryById, updateTimeEntry } from '../../services/timeEntry';

// Function to calculate night differential hours (10:00 PM to 6:00 AM)
const calculateNightDifferentialHours = (timeIn: Date, timeOut: Date): number => {
  // Ensure timeOut is after timeIn (handle overnight shifts)
  let adjustedTimeOut = new Date(timeOut);
  if (adjustedTimeOut < timeIn) {
    adjustedTimeOut.setDate(adjustedTimeOut.getDate() + 1);
  }

  // Create night shift start and end times for the day of timeIn
  const nightShiftStart = new Date(timeIn);
  nightShiftStart.setHours(22, 0, 0, 0); // 10:00 PM

  // Create night shift end for the next day
  const nightShiftEnd = new Date(timeIn);
  nightShiftEnd.setHours(6, 0, 0, 0); // 6:00 AM
  if (nightShiftEnd < nightShiftStart) {
    nightShiftEnd.setDate(nightShiftEnd.getDate() + 1);
  }

  // Create night shift times for the next day
  const nextDayNightShiftStart = new Date(nightShiftStart);
  nextDayNightShiftStart.setDate(nextDayNightShiftStart.getDate() + 1);

  const nextDayNightShiftEnd = new Date(nightShiftEnd);
  nextDayNightShiftEnd.setDate(nextDayNightShiftEnd.getDate() + 1);

  // Handle cases where the shift spans multiple days
  let nightDiffMinutes = 0;

  // Check if the shift overlaps with the night shift on the first day
  if (timeIn <= nightShiftEnd && adjustedTimeOut >= nightShiftStart) {
    const overlapStart = timeIn > nightShiftStart ? timeIn : nightShiftStart;
    const overlapEnd = adjustedTimeOut < nightShiftEnd ? adjustedTimeOut : nightShiftEnd;

    if (overlapEnd > overlapStart) {
      nightDiffMinutes += differenceInMinutes(overlapEnd, overlapStart);
    }
  }

  // Check if the shift overlaps with the night shift on the next day
  if (timeIn <= nextDayNightShiftEnd && adjustedTimeOut >= nextDayNightShiftStart) {
    const overlapStart = timeIn > nextDayNightShiftStart ? timeIn : nextDayNightShiftStart;
    const overlapEnd = adjustedTimeOut < nextDayNightShiftEnd ? adjustedTimeOut : nextDayNightShiftEnd;

    if (overlapEnd > overlapStart) {
      nightDiffMinutes += differenceInMinutes(overlapEnd, overlapStart);
    }
  }

  // Convert minutes to hours (rounded to nearest 0.5)
  return Math.round((nightDiffMinutes / 60) * 2) / 2;
};

const EditTimeEntry: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [timeEntry, setTimeEntry] = useState<Partial<TimeEntry> | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<boolean>(false);

  // Fetch time entry
  const fetchTimeEntry = async () => {
    if (!currentOrganization || !id) return;

    try {
      const { entry, error } = await getTimeEntryById(currentOrganization.id, id);

      if (error) {
        setError(error);
      } else if (entry) {
        // Add exclude_lunch_break property if not present
        // This is a UI-only field, not stored in the database
        setTimeEntry({
          ...entry,
          exclude_lunch_break: entry.exclude_lunch_break !== undefined ? entry.exclude_lunch_break : true
        });
      } else {
        setError('Time entry not found');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch employees
  const fetchEmployees = async () => {
    if (!currentOrganization) return;

    try {
      const { employees, error } = await getEmployees(currentOrganization.id);

      if (error) {
        setError(error);
      } else {
        setEmployees(employees);
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Load data when the component mounts
  useEffect(() => {
    fetchTimeEntry();
    fetchEmployees();
  }, [currentOrganization, id]);

  // Update a time entry field
  const updateTimeEntryField = (field: string, value: any) => {
    if (!timeEntry) return;

    const updatedEntry = {
      ...timeEntry,
      [field]: value
    };

    // If time_in or time_out changes, recalculate hours
    if (field === 'time_in' || field === 'time_out' || field === 'exclude_lunch_break') {
      if (updatedEntry.time_in && updatedEntry.time_out) {
        const timeIn = new Date(updatedEntry.time_in);
        const timeOut = new Date(updatedEntry.time_out);

        if (isValid(timeIn) && isValid(timeOut)) {
          // Handle shifts that span across midnight
          let totalMinutes = 0;

          // If timeOut is earlier than timeIn, it likely means the shift spans to the next day
          if (timeOut < timeIn) {
            // Create a new timeOut date on the next day
            const nextDayTimeOut = new Date(timeOut);
            nextDayTimeOut.setDate(nextDayTimeOut.getDate() + 1);
            totalMinutes = differenceInMinutes(nextDayTimeOut, timeIn);
          } else {
            totalMinutes = differenceInMinutes(timeOut, timeIn);
          }

          // Subtract lunch break if applicable (1 hour = 60 minutes)
          if (updatedEntry.exclude_lunch_break && totalMinutes > 300) { // Only subtract if shift is > 5 hours
            totalMinutes -= 60;
          }

          // Calculate regular and overtime hours
          const totalHours = totalMinutes / 60;
          const regularHours = Math.min(8, totalHours);
          const overtimeHours = Math.max(0, totalHours - 8);

          // Round to nearest 0.5
          updatedEntry.regular_hours = Math.round(regularHours * 2) / 2;
          updatedEntry.overtime_hours = Math.round(overtimeHours * 2) / 2;

          // For night differential calculation, we need to adjust timeOut if it's across midnight
          let adjustedTimeOut = timeOut;
          if (timeOut < timeIn) {
            adjustedTimeOut = new Date(timeOut);
            adjustedTimeOut.setDate(adjustedTimeOut.getDate() + 1);
          }

          // Calculate night differential hours
          updatedEntry.night_diff_hours = calculateNightDifferentialHours(timeIn, adjustedTimeOut);
        }
      }
    }

    setTimeEntry(updatedEntry);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization || !timeEntry || !id) return;

    // Validate entry
    if (!timeEntry.employee_id || !timeEntry.date ||
        (timeEntry.status === TimeEntryStatus.PRESENT && (!timeEntry.time_in || !timeEntry.time_out))) {
      setSubmitError('Please fill in all required fields.');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Prepare the data for the API
      const entryToUpdate = {
        employee_id: timeEntry.employee_id,
        date: timeEntry.date,
        time_in: timeEntry.time_in,
        time_out: timeEntry.time_out,
        status: timeEntry.status,
        regular_hours: timeEntry.regular_hours,
        overtime_hours: timeEntry.overtime_hours,
        night_diff_hours: timeEntry.night_diff_hours,
        exclude_lunch_break: timeEntry.exclude_lunch_break,
        is_rest_day: timeEntry.is_rest_day,
        is_holiday: timeEntry.is_holiday,
        holiday_type: timeEntry.holiday_type
      };

      const { entry, error } = await updateTimeEntry(currentOrganization.id, id, entryToUpdate);

      if (error) {
        // Check if it's a duplicate key error or an existing entry error
        if (error.includes('duplicate key value') || error.includes('already exists for this employee')) {
          setSubmitError('An entry already exists for this employee on this date. The existing entry has been updated.');

          // Still show success and redirect after a delay
          setSubmitSuccess(true);
          setTimeout(() => {
            navigate('/payroll/time-entries');
          }, 3000);
        } else {
          setSubmitError(error);
        }
      } else {
        setSubmitSuccess(true);
        // Redirect after successful submission
        setTimeout(() => {
          navigate('/payroll/time-entries');
        }, 2000);
      }
    } catch (err: any) {
      setSubmitError(err.message || 'An error occurred while saving the time entry');
    } finally {
      setIsSubmitting(false);
    }
  };

  // If loading, show a spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
          <span className="font-medium">Error!</span> {error}
        </Alert>
        <Button color="light" onClick={() => navigate('/payroll/time-entries')}>
          <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
          Back to Time Entries
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button color="light" onClick={() => navigate('/payroll/time-entries')} className="mr-4">
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Time Entries
          </Button>
          <PageTitle title="Edit Time Entry" />
        </div>
      </div>

      {submitSuccess && (
        <Alert color="success" className="mb-4">
          Time entry updated successfully! Redirecting...
        </Alert>
      )}

      <Card>
        <form onSubmit={handleSubmit}>
          {submitError && (
            <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
              {submitError}
            </Alert>
          )}

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="employeeId" value="Employee" />
                </div>
                <EmployeeSearchSelect
                  employees={employees}
                  selectedEmployeeId={timeEntry?.employee_id}
                  onSelect={(employeeId) => updateTimeEntryField('employee_id', employeeId)}
                  required
                  isLoading={isLoading}
                  placeholder="Search for an employee..."
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="date" value="Date" />
                </div>
                <DatePicker
                  id="date"
                  selected={timeEntry?.date ? new Date(timeEntry.date) : null}
                  onChange={(date: Date) => updateTimeEntryField('date', date.toISOString().split('T')[0])}
                  dateFormat="MMMM d, yyyy"
                  className="w-full rounded-lg border border-gray-300 p-2.5"
                  placeholderText="Select date"
                  required
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="status" value="Status" />
                </div>
                <Select
                  id="status"
                  value={timeEntry?.status || TimeEntryStatus.PRESENT}
                  onChange={(e) => updateTimeEntryField('status', e.target.value as TimeEntryStatus)}
                  required
                >
                  <option value={TimeEntryStatus.PRESENT}>Present</option>
                  <option value={TimeEntryStatus.ABSENT}>Absent</option>
                  <option value={TimeEntryStatus.LEAVE}>Leave</option>
                  <option value={TimeEntryStatus.HOLIDAY}>Holiday</option>
                </Select>
              </div>
            </div>

            {timeEntry?.status === TimeEntryStatus.PRESENT && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="mb-2 block">
                      <Label htmlFor="timeIn" value="Time In" />
                    </div>
                    <DatePicker
                      id="timeIn"
                      selected={timeEntry?.time_in ? new Date(timeEntry.time_in) : null}
                      onChange={(date: Date) => updateTimeEntryField('time_in', date.toISOString())}
                      showTimeSelect
                      showTimeSelectOnly
                      timeIntervals={15}
                      timeCaption="Time"
                      dateFormat="h:mm aa"
                      className="w-full rounded-lg border border-gray-300 p-2.5"
                      placeholderText="Select time in"
                      required
                    />
                  </div>
                  <div>
                    <div className="mb-2 block">
                      <Label htmlFor="timeOut" value="Time Out" />
                    </div>
                    <DatePicker
                      id="timeOut"
                      selected={timeEntry?.time_out ? new Date(timeEntry.time_out) : null}
                      onChange={(date: Date) => updateTimeEntryField('time_out', date.toISOString())}
                      showTimeSelect
                      showTimeSelectOnly
                      timeIntervals={15}
                      timeCaption="Time"
                      dateFormat="h:mm aa"
                      className="w-full rounded-lg border border-gray-300 p-2.5"
                      placeholderText="Select time out"
                      required
                    />
                  </div>
                </div>

                <div className="mt-4">
                  <div className="flex items-center mb-4">
                    <Checkbox
                      id="excludeLunch"
                      checked={timeEntry?.exclude_lunch_break}
                      onChange={(e) => updateTimeEntryField('exclude_lunch_break', e.target.checked)}
                    />
                    <Label htmlFor="excludeLunch" className="ml-2">
                      Exclude lunch break (1 hour)
                    </Label>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div>
                    <div className="mb-2 flex items-center">
                      <Label htmlFor="regularHours" value="Regular Hours" />
                    </div>
                    <TextInput
                      id="regularHours"
                      type="number"
                      step="0.5"
                      min="0"
                      value={timeEntry?.regular_hours || 0}
                      onChange={(e) => updateTimeEntryField('regular_hours', parseFloat(e.target.value))}
                      required
                    />
                  </div>
                  <div>
                    <div className="mb-2 flex items-center">
                      <Label htmlFor="overtimeHours" value="Overtime Hours" />
                    </div>
                    <TextInput
                      id="overtimeHours"
                      type="number"
                      step="0.5"
                      min="0"
                      value={timeEntry?.overtime_hours || 0}
                      onChange={(e) => updateTimeEntryField('overtime_hours', parseFloat(e.target.value))}
                      required
                    />
                  </div>
                  <div>
                    <div className="mb-2 flex items-center">
                      <Label htmlFor="nightDiffHours" value="Night Differential Hours" />
                      <Tooltip content="Hours worked between 10:00 PM and 6:00 AM. For shifts that span across midnight (e.g., 5 PM to 1 AM), the system will automatically calculate the night differential hours.">
                        <HiOutlineInformationCircle className="ml-1 h-4 w-4 text-gray-500" />
                      </Tooltip>
                    </div>
                    <div className="flex items-center">
                      <TextInput
                        id="nightDiffHours"
                        type="number"
                        step="0.5"
                        min="0"
                        value={timeEntry?.night_diff_hours || 0}
                        onChange={(e) => updateTimeEntryField('night_diff_hours', parseFloat(e.target.value))}
                        className="flex-1"
                        required
                      />
                      <HiOutlineMoon className="ml-2 h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                </div>
              </>
            )}

            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <Checkbox
                  id="restDay"
                  checked={timeEntry?.is_rest_day}
                  onChange={(e) => updateTimeEntryField('is_rest_day', e.target.checked)}
                />
                <Label htmlFor="restDay" className="ml-2">
                  Rest Day
                </Label>
              </div>
              <div className="flex items-center">
                <Checkbox
                  id="holiday"
                  checked={timeEntry?.is_holiday}
                  onChange={(e) => updateTimeEntryField('is_holiday', e.target.checked)}
                />
                <Label htmlFor="holiday" className="ml-2">
                  Holiday
                </Label>
              </div>
            </div>

            {timeEntry?.is_holiday && (
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="holidayType" value="Holiday Type" />
                </div>
                <Select
                  id="holidayType"
                  value={timeEntry?.holiday_type || 'regular'}
                  onChange={(e) => updateTimeEntryField('holiday_type', e.target.value)}
                >
                  <option value="regular">Regular Holiday</option>
                  <option value="special">Special Holiday</option>
                </Select>
              </div>
            )}
          </div>

          <div className="flex justify-between mt-6">
            <Button color="light" onClick={() => navigate('/payroll/time-entries')}>
              Cancel
            </Button>
            <Button color="primary" type="submit" isProcessing={isSubmitting}>
              Save Changes
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default EditTimeEntry;
