import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Spinner,
  <PERSON><PERSON>,
  <PERSON>,
  Badge
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineExclamationCircle,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { 
  getEmployeesWithContributionPreferences,
  deleteEmployeeContributionPreferences,
  EmployeeContributionPreference
} from '../../services/employeeContributionPreferences';
import PageTitle from '../../components/shared/PageTitle';
import { formatCurrency } from '../../utils/formatters';

interface EmployeeWithPreferences extends EmployeeContributionPreference {
  employee: {
    id: string;
    first_name: string;
    middle_name?: string | null;
    last_name: string;
    employee_number?: string | null;
  };
}

const EmployeeContributionPreferences: React.FC = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();

  // State
  const [employees, setEmployees] = useState<EmployeeWithPreferences[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Load employees with contribution preferences
  useEffect(() => {
    const loadEmployees = async () => {
      if (!currentOrganization) return;

      try {
        setLoading(true);
        const { employees: employeeData, error } = await getEmployeesWithContributionPreferences(
          currentOrganization.id
        );

        if (error) {
          setError(error);
        } else if (employeeData) {
          setEmployees(employeeData);
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    loadEmployees();
  }, [currentOrganization]);

  // Handle delete preferences
  const handleDeletePreferences = async (employeeId: string, preferenceId: string) => {
    if (!currentOrganization) return;

    setDeletingId(preferenceId);

    try {
      const { success, error } = await deleteEmployeeContributionPreferences(
        currentOrganization.id,
        employeeId
      );

      if (error) {
        setError(error);
      } else if (success) {
        // Remove from local state
        setEmployees(prev => prev.filter(emp => emp.id !== preferenceId));
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setDeletingId(null);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <Spinner size="xl" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button color="gray" className="mr-4" onClick={() => navigate('/payroll/settings')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Payroll Settings
          </Button>
          <PageTitle title="Employee Contribution Preferences" />
        </div>
      </div>

      {error && (
        <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
          {error}
        </Alert>
      )}

      <Card>
        {employees.length === 0 ? (
          <div className="text-center py-8">
            <HiOutlineInformationCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Custom Preferences</h3>
            <p className="text-gray-600 mb-4">
              No employees have custom contribution preferences set. Employees will use standard Philippine law calculations.
            </p>
            <p className="text-sm text-gray-500">
              To set custom preferences, edit an employee's contributions in any payroll period and enable "Save as employee-specific contribution preferences".
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table striped>
              <Table.Head>
                <Table.HeadCell>Employee</Table.HeadCell>
                <Table.HeadCell>SSS</Table.HeadCell>
                <Table.HeadCell>PhilHealth</Table.HeadCell>
                <Table.HeadCell>Pag-IBIG</Table.HeadCell>
                <Table.HeadCell>Withholding Tax</Table.HeadCell>
                <Table.HeadCell>Notes</Table.HeadCell>
                <Table.HeadCell>Actions</Table.HeadCell>
              </Table.Head>
              <Table.Body>
                {employees.map((employee) => (
                  <Table.Row key={employee.id}>
                    <Table.Cell className="font-medium">
                      <div>
                        <div className="font-semibold">
                          {employee.employee.first_name} {employee.employee.last_name}
                        </div>
                        {employee.employee.employee_number && (
                          <div className="text-sm text-gray-500">
                            #{employee.employee.employee_number}
                          </div>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {employee.sss_contribution_override ? (
                        <Badge color="warning">
                          {formatCurrency(employee.sss_contribution_override)}
                        </Badge>
                      ) : (
                        <span className="text-gray-500">Standard</span>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {employee.philhealth_contribution_override ? (
                        <Badge color="warning">
                          {formatCurrency(employee.philhealth_contribution_override)}
                        </Badge>
                      ) : (
                        <span className="text-gray-500">Standard</span>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {employee.pagibig_contribution_override ? (
                        <Badge color="warning">
                          {formatCurrency(employee.pagibig_contribution_override)}
                        </Badge>
                      ) : (
                        <span className="text-gray-500">Standard</span>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {employee.withholding_tax_override ? (
                        <Badge color="warning">
                          {formatCurrency(employee.withholding_tax_override)}
                        </Badge>
                      ) : (
                        <span className="text-gray-500">Standard</span>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="max-w-xs truncate" title={employee.notes || ''}>
                        {employee.notes || '-'}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex space-x-2">
                        <Button
                          size="xs"
                          color="failure"
                          onClick={() => handleDeletePreferences(employee.employee_id, employee.id)}
                          disabled={deletingId === employee.id}
                          isProcessing={deletingId === employee.id}
                        >
                          <HiOutlineTrash className="h-3 w-3" />
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}
      </Card>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-sm font-medium text-blue-800 mb-2">How Employee Preferences Work</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Each employee can have personalized government contribution amounts</li>
          <li>• These preferences automatically apply to all future payroll periods</li>
          <li>• You can set preferences by editing any payroll item and enabling "Save as employee-specific contribution preferences"</li>
          <li>• Employees without custom preferences use standard Philippine law calculations</li>
          <li>• You can always recalculate any payroll item to reset to legal amounts</li>
        </ul>
      </div>
    </div>
  );
};

export default EmployeeContributionPreferences;
