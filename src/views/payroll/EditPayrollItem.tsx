import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON>ton,
  Card,
  Spinner,
  Alert,
  Label,
  TextInput,
  Checkbox,
  Modal
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineExclamationCircle,
  HiOutlineSave,
  HiOutlineRefresh,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import {
  getPayrollItemById,
  updatePayrollItem,
  recalculateGovernmentContributions
} from '../../services/payroll';
import {
  saveEmployeeContributionPreferences,
  getEmployeeContributionPreferences
} from '../../services/employeeContributionPreferences';
import { PayrollItemWithDetails, PayrollPeriodStatus } from '../../types/payroll';
import PageTitle from '../../components/shared/PageTitle';
import { formatCurrency } from '../../utils/formatters';

const EditPayrollItem: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();

  // State
  const [payrollItem, setPayrollItem] = useState<PayrollItemWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [showRecalculateModal, setShowRecalculateModal] = useState(false);
  const [isRecalculating, setIsRecalculating] = useState(false);

  // Form state
  const [sssContribution, setSssContribution] = useState<number>(0);
  const [philhealthContribution, setPhilhealthContribution] = useState<number>(0);
  const [pagibigContribution, setPagibigContribution] = useState<number>(0);
  const [withholdingTax, setWithholdingTax] = useState<number>(0);
  const [useManualOverrides, setUseManualOverrides] = useState(false);

  // Load payroll item
  useEffect(() => {
    const loadPayrollItem = async () => {
      if (!currentOrganization || !id) return;

      try {
        setLoading(true);
        const { item, error } = await getPayrollItemById(currentOrganization.id, id);

        if (error) {
          setError(error);
        } else if (item) {
          setPayrollItem(item);

          // Initialize form values
          setSssContribution(item.sss_contribution || 0);
          setPhilhealthContribution(item.philhealth_contribution || 0);
          setPagibigContribution(item.pagibig_contribution || 0);
          setWithholdingTax(item.withholding_tax || 0);
          setUseManualOverrides(item.contributions_manually_edited || false);

          // Load existing employee preferences to show if they exist
          if (item.employee?.id) {
            const { preferences } = await getEmployeeContributionPreferences(
              currentOrganization.id,
              item.employee.id
            );

            // If preferences exist and current values match, show that manual overrides are being used
            if (preferences) {
              const hasMatchingPreferences =
                (preferences.sss_contribution_override === item.sss_contribution ||
                 preferences.philhealth_contribution_override === item.philhealth_contribution ||
                 preferences.pagibig_contribution_override === item.pagibig_contribution ||
                 preferences.withholding_tax_override === item.withholding_tax);

              if (hasMatchingPreferences) {
                setUseManualOverrides(true);
              }
            }
          }
        } else {
          setError('Payroll item not found');
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    loadPayrollItem();
  }, [currentOrganization, id]);

  // Check if editing is allowed
  const canEdit = payrollItem?.payroll_period?.status === PayrollPeriodStatus.DRAFT ||
                  payrollItem?.payroll_period?.status === PayrollPeriodStatus.PROCESSING;

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization || !id || !payrollItem || !user) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Calculate new totals
      const governmentDeductions = sssContribution + philhealthContribution + pagibigContribution + withholdingTax;
      const otherDeductions = payrollItem.total_deductions -
        (payrollItem.sss_contribution + payrollItem.philhealth_contribution +
         payrollItem.pagibig_contribution + payrollItem.withholding_tax);
      const newTotalDeductions = governmentDeductions + otherDeductions;
      const newNetPay = payrollItem.gross_pay - newTotalDeductions;

      // Calculate taxable income
      const newTaxableIncome = payrollItem.gross_pay - sssContribution - philhealthContribution - pagibigContribution;

      const updateData = {
        sss_contribution: sssContribution,
        philhealth_contribution: philhealthContribution,
        pagibig_contribution: pagibigContribution,
        withholding_tax: withholdingTax,
        taxable_income: newTaxableIncome,
        total_deductions: newTotalDeductions,
        net_pay: newNetPay,
        contributions_manually_edited: useManualOverrides,
        // Store override values if manual overrides are enabled
        sss_contribution_override: useManualOverrides ? sssContribution : null,
        philhealth_contribution_override: useManualOverrides ? philhealthContribution : null,
        pagibig_contribution_override: useManualOverrides ? pagibigContribution : null,
        withholding_tax_override: useManualOverrides ? withholdingTax : null,
      };

      const { item, error } = await updatePayrollItem(currentOrganization.id, id, updateData);

      if (error) {
        setSubmitError(error);
      } else if (item) {
        // Save employee preferences if manual overrides are enabled
        if (useManualOverrides && payrollItem.employee?.id) {
          const { error: preferencesError } = await saveEmployeeContributionPreferences(
            currentOrganization.id,
            payrollItem.employee.id,
            {
              sss_contribution_override: sssContribution,
              philhealth_contribution_override: philhealthContribution,
              pagibig_contribution_override: pagibigContribution,
              withholding_tax_override: withholdingTax,
              notes: `Updated on ${new Date().toLocaleDateString()} via payroll item edit`
            },
            user.id
          );

          if (preferencesError) {
            console.warn('Failed to save employee preferences:', preferencesError);
            // Don't fail the whole operation, just warn
          }
        }

        setSubmitSuccess(true);
        // Redirect after successful submission
        setTimeout(() => {
          navigate(`/payroll/items/${id}`);
        }, 2000);
      }
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle recalculation
  const handleRecalculate = async () => {
    if (!currentOrganization || !id) return;

    setIsRecalculating(true);
    setSubmitError(null);

    try {
      const { item, error } = await recalculateGovernmentContributions(currentOrganization.id, id);

      if (error) {
        setSubmitError(error);
      } else if (item) {
        // Update form values with recalculated amounts
        setSssContribution(item.sss_contribution || 0);
        setPhilhealthContribution(item.philhealth_contribution || 0);
        setPagibigContribution(item.pagibig_contribution || 0);
        setWithholdingTax(item.withholding_tax || 0);
        setUseManualOverrides(false);

        // Update the payroll item state
        setPayrollItem(prev => prev ? { ...prev, ...item } : null);

        setShowRecalculateModal(false);
        setSubmitSuccess(true);
        setTimeout(() => setSubmitSuccess(false), 3000);
      }
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsRecalculating(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <Spinner size="xl" />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamationCircle}>
          <span className="font-medium">Error!</span> {error}
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/payroll')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Payroll
          </Button>
        </div>
      </div>
    );
  }

  if (!payrollItem) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="warning" icon={HiOutlineInformationCircle}>
          Payroll item not found.
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button
            color="gray"
            className="mr-4"
            onClick={() => navigate(`/payroll/items/${id}`)}
          >
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back
          </Button>
          <PageTitle
            title="Edit Government Contributions"
            subtitle={`${payrollItem.employee?.first_name} ${payrollItem.employee?.last_name} - ${payrollItem.payroll_period?.name}`}
          />
        </div>
        <div className="flex space-x-2">
          <Button
            color="light"
            onClick={() => setShowRecalculateModal(true)}
            disabled={!canEdit}
          >
            <HiOutlineRefresh className="mr-2 h-5 w-5" />
            Recalculate
          </Button>
        </div>
      </div>

      {!canEdit && (
        <Alert color="warning" className="mb-4" icon={HiOutlineInformationCircle}>
          This payroll period has been approved or paid. Government contributions cannot be edited.
        </Alert>
      )}

      {submitSuccess && (
        <Alert color="success" className="mb-4">
          Government contributions updated successfully! Redirecting...
        </Alert>
      )}

      <Card>
        <form onSubmit={handleSubmit}>
          {submitError && (
            <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
              {submitError}
            </Alert>
          )}

          {/* Employee Info */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Employee Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Basic Pay:</span> {formatCurrency(payrollItem.basic_pay)}
              </div>
              <div>
                <span className="font-medium">Gross Pay:</span> {formatCurrency(payrollItem.gross_pay)}
              </div>
              <div>
                <span className="font-medium">Current Net Pay:</span> {formatCurrency(payrollItem.net_pay)}
              </div>
              <div>
                <span className="font-medium">Total Deductions:</span> {formatCurrency(payrollItem.total_deductions)}
              </div>
            </div>
          </div>

          {/* Manual Override Toggle */}
          <div className="mb-6">
            <div className="flex items-center gap-2">
              <Checkbox
                id="useManualOverrides"
                checked={useManualOverrides}
                onChange={(e) => setUseManualOverrides(e.target.checked)}
                disabled={!canEdit}
              />
              <Label htmlFor="useManualOverrides">
                Save as employee-specific contribution preferences
              </Label>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              When enabled, these values will be saved as <strong>{payrollItem?.employee?.first_name} {payrollItem?.employee?.last_name}'s</strong> personal contribution preferences and automatically applied to all future payroll periods for this employee only.
            </p>
          </div>

          {/* Government Contributions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* SSS Contribution */}
            <div>
              <div className="mb-2 block">
                <Label htmlFor="sssContribution" value="SSS Contribution" />
              </div>
              <TextInput
                id="sssContribution"
                type="number"
                step="0.01"
                min="0"
                value={sssContribution}
                onChange={(e) => setSssContribution(parseFloat(e.target.value) || 0)}
                disabled={!canEdit}
                placeholder="0.00"
              />
              <p className="text-xs text-gray-500 mt-1">Employee share only</p>
            </div>

            {/* PhilHealth Contribution */}
            <div>
              <div className="mb-2 block">
                <Label htmlFor="philhealthContribution" value="PhilHealth Contribution" />
              </div>
              <TextInput
                id="philhealthContribution"
                type="number"
                step="0.01"
                min="0"
                value={philhealthContribution}
                onChange={(e) => setPhilhealthContribution(parseFloat(e.target.value) || 0)}
                disabled={!canEdit}
                placeholder="0.00"
              />
              <p className="text-xs text-gray-500 mt-1">Employee share only</p>
            </div>

            {/* Pag-IBIG Contribution */}
            <div>
              <div className="mb-2 block">
                <Label htmlFor="pagibigContribution" value="Pag-IBIG Contribution" />
              </div>
              <TextInput
                id="pagibigContribution"
                type="number"
                step="0.01"
                min="0"
                value={pagibigContribution}
                onChange={(e) => setPagibigContribution(parseFloat(e.target.value) || 0)}
                disabled={!canEdit}
                placeholder="0.00"
              />
              <p className="text-xs text-gray-500 mt-1">Employee share only</p>
            </div>

            {/* Withholding Tax */}
            <div>
              <div className="mb-2 block">
                <Label htmlFor="withholdingTax" value="Withholding Tax" />
              </div>
              <TextInput
                id="withholdingTax"
                type="number"
                step="0.01"
                min="0"
                value={withholdingTax}
                onChange={(e) => setWithholdingTax(parseFloat(e.target.value) || 0)}
                disabled={!canEdit}
                placeholder="0.00"
              />
              <p className="text-xs text-gray-500 mt-1">Based on taxable income</p>
            </div>
          </div>

          {/* Calculation Summary */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Calculation Summary</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Total Government Deductions:</span>
                {formatCurrency(sssContribution + philhealthContribution + pagibigContribution + withholdingTax)}
              </div>
              <div>
                <span className="font-medium">Taxable Income:</span>
                {formatCurrency(payrollItem.gross_pay - sssContribution - philhealthContribution - pagibigContribution)}
              </div>
              <div>
                <span className="font-medium">New Total Deductions:</span>
                {formatCurrency(
                  (sssContribution + philhealthContribution + pagibigContribution + withholdingTax) +
                  (payrollItem.total_deductions - (payrollItem.sss_contribution + payrollItem.philhealth_contribution + payrollItem.pagibig_contribution + payrollItem.withholding_tax))
                )}
              </div>
              <div>
                <span className="font-medium">New Net Pay:</span>
                {formatCurrency(
                  payrollItem.gross_pay -
                  ((sssContribution + philhealthContribution + pagibigContribution + withholdingTax) +
                  (payrollItem.total_deductions - (payrollItem.sss_contribution + payrollItem.philhealth_contribution + payrollItem.pagibig_contribution + payrollItem.withholding_tax)))
                )}
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              color="gray"
              onClick={() => navigate(`/payroll/items/${id}`)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            {canEdit && (
              <Button
                type="submit"
                color="primary"
                isProcessing={isSubmitting}
                disabled={isSubmitting}
              >
                <HiOutlineSave className="mr-2 h-5 w-5" />
                Save Changes
              </Button>
            )}
          </div>
        </form>
      </Card>

      {/* Recalculate Confirmation Modal */}
      <Modal
        show={showRecalculateModal}
        onClose={() => setShowRecalculateModal(false)}
        size="md"
      >
        <Modal.Header>Recalculate Government Contributions</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>
              This will recalculate all government contributions based on current Philippine law and the employee's basic pay.
            </p>
            <div className="p-4 bg-yellow-50 rounded-lg">
              <h4 className="font-semibold text-yellow-800 mb-2">This will:</h4>
              <ul className="list-disc list-inside text-sm text-yellow-700 space-y-1">
                <li>Reset SSS, PhilHealth, and Pag-IBIG contributions to legal amounts</li>
                <li>Recalculate withholding tax based on taxable income</li>
                <li>Clear any manual overrides</li>
                <li>Update net pay accordingly</li>
              </ul>
            </div>
            <p className="text-sm text-gray-600">
              Are you sure you want to proceed? This action cannot be undone.
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="primary"
            onClick={handleRecalculate}
            isProcessing={isRecalculating}
            disabled={isRecalculating}
          >
            <HiOutlineRefresh className="mr-2 h-5 w-5" />
            Recalculate
          </Button>
          <Button
            color="gray"
            onClick={() => setShowRecalculateModal(false)}
            disabled={isRecalculating}
          >
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default EditPayrollItem;
