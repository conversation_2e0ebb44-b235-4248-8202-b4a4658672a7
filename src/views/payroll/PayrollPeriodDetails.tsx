import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  Card,
  Table,
  Spinner,
  Badge,
  Ta<PERSON>,
  <PERSON>ert,
  Modal
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineCurrencyDollar,
  HiOutlineDocumentText,
  HiOutlineExclamationCircle,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineUsers,
  HiOutlineCalendar,
  HiOutlineCheck,
  HiOutlineX
} from 'react-icons/hi';
import { format } from 'date-fns';
import { useOrganization } from '../../context/OrganizationContext';
import { getPayrollPeriodById, updatePayrollPeriod, deletePayrollPeriod } from '../../services/payroll';
import { PayrollPeriodWithDetails, PayrollPeriodStatus, PayrollItemStatus } from '../../types/payroll';
import { hasPayrollPayablesBeenCreated } from '../../services/payrollPayables';
import SendPayrollToPayableButton from '../../components/payroll/SendPayrollToPayableButton';
import PageTitle from '../../components/shared/PageTitle';
import { formatCurrency } from '../../utils/formatters';

const PayrollPeriodDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const [payrollPeriod, setPayrollPeriod] = useState<PayrollPeriodWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [payablesCreated, setPayablesCreated] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [showApproveModal, setShowApproveModal] = useState<boolean>(false);

  // Fetch payroll period details
  const fetchPayrollPeriod = async () => {
    if (!currentOrganization || !id) return;

    setIsLoading(true);
    setError(null);

    try {
      // Always include items when fetching the payroll period
      const { period, error } = await getPayrollPeriodById(currentOrganization.id, id, true);

      if (error) {
        setError(error);
        console.error('Error fetching payroll period:', error);
      } else if (period) {
        console.log('Fetched payroll period:', period);
        console.log('Payroll items:', period.payroll_items?.length || 0);
        setPayrollPeriod(period);

        // Check if payables have been created for approved periods
        if (period.status === PayrollPeriodStatus.APPROVED) {
          const payablesStatus = await hasPayrollPayablesBeenCreated(currentOrganization.id, id);
          setPayablesCreated(payablesStatus);
        }
      } else {
        setError('Payroll period not found');
      }
    } catch (err: any) {
      console.error('Error in fetchPayrollPeriod:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Load payroll period when the component mounts
  useEffect(() => {
    fetchPayrollPeriod();
  }, [currentOrganization, id]);

  // Handle deleting a payroll period
  const handleDeletePayrollPeriod = async () => {
    if (!currentOrganization || !id) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { success, error } = await deletePayrollPeriod(currentOrganization.id, id);

      if (error) {
        setSubmitError(error);
      } else if (success) {
        navigate('/payroll');
      }
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle approving a payroll period
  const handleApprovePayrollPeriod = async () => {
    if (!currentOrganization || !id || !payrollPeriod) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { period, error } = await updatePayrollPeriod(currentOrganization.id, id, {
        status: PayrollPeriodStatus.APPROVED
      });

      if (error) {
        setSubmitError(error);
      } else if (period) {
        setShowApproveModal(false);
        fetchPayrollPeriod();
      }
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render status badge
  const renderStatusBadge = (status: PayrollPeriodStatus) => {
    switch (status) {
      case PayrollPeriodStatus.DRAFT:
        return <Badge color="gray">Draft</Badge>;
      case PayrollPeriodStatus.PROCESSING:
        return <Badge color="blue">Processing</Badge>;
      case PayrollPeriodStatus.APPROVED:
        return <Badge color="green">Approved</Badge>;
      case PayrollPeriodStatus.PAID:
        return <Badge color="purple">Paid</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  // Render payroll item status badge
  const renderPayrollItemStatusBadge = (status: PayrollItemStatus) => {
    switch (status) {
      case PayrollItemStatus.DRAFT:
        return <Badge color="gray">Draft</Badge>;
      case PayrollItemStatus.CALCULATED:
        return <Badge color="blue">Calculated</Badge>;
      case PayrollItemStatus.APPROVED:
        return <Badge color="green">Approved</Badge>;
      case PayrollItemStatus.PAID:
        return <Badge color="purple">Paid</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  // Calculate totals
  const calculateTotals = () => {
    if (!payrollPeriod?.payroll_items || payrollPeriod.payroll_items.length === 0) {
      return {
        totalBasicPay: 0,
        totalGrossPay: 0,
        totalNetPay: 0,
        totalDeductions: 0,
        totalAllowances: 0,
        totalEmployees: 0
      };
    }

    return payrollPeriod.payroll_items.reduce(
      (acc, item) => {
        return {
          totalBasicPay: acc.totalBasicPay + Number(item.basic_pay),
          totalGrossPay: acc.totalGrossPay + Number(item.gross_pay),
          totalNetPay: acc.totalNetPay + Number(item.net_pay),
          totalDeductions: acc.totalDeductions + Number(item.total_deductions),
          totalAllowances: acc.totalAllowances + Number(item.total_allowances),
          totalEmployees: acc.totalEmployees + 1
        };
      },
      {
        totalBasicPay: 0,
        totalGrossPay: 0,
        totalNetPay: 0,
        totalDeductions: 0,
        totalAllowances: 0,
        totalEmployees: 0
      }
    );
  };

  const totals = calculateTotals();

  // If loading, show a spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamationCircle}>
          <span className="font-medium">Error!</span> {error}
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/payroll')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Payroll Periods
          </Button>
        </div>
      </div>
    );
  }

  // If payroll period not found, show message
  if (!payrollPeriod) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamationCircle}>
          <span className="font-medium">Error!</span> Payroll period not found
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/payroll')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Payroll Periods
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button color="gray" className="mr-4" onClick={() => navigate('/payroll')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back
          </Button>
          <PageTitle title={`Payroll Period: ${payrollPeriod.name}`} />
        </div>
        <div className="flex space-x-2">
          {payrollPeriod.status === PayrollPeriodStatus.DRAFT && (
            <>
              <Link to={`/payroll/periods/${payrollPeriod.id}/edit`}>
                <Button color="warning">
                  <HiOutlinePencil className="mr-2 h-5 w-5" />
                  Edit
                </Button>
              </Link>
              <Button color="failure" onClick={() => setShowDeleteModal(true)}>
                <HiOutlineTrash className="mr-2 h-5 w-5" />
                Delete
              </Button>
            </>
          )}
          {payrollPeriod.status === PayrollPeriodStatus.PROCESSING && (
            <Button color="success" onClick={() => setShowApproveModal(true)}>
              <HiOutlineCheck className="mr-2 h-5 w-5" />
              Approve
            </Button>
          )}
          {payrollPeriod.status === PayrollPeriodStatus.DRAFT && (
            <Link to={`/payroll/periods/${payrollPeriod.id}/process`}>
              <Button color="primary">
                <HiOutlineCurrencyDollar className="mr-2 h-5 w-5" />
                Process Payroll
              </Button>
            </Link>
          )}
          {payrollPeriod.status !== PayrollPeriodStatus.DRAFT && (
            <Link to={`/payroll/periods/${payrollPeriod.id}/payslips`}>
              <Button color="light">
                <HiOutlineDocumentText className="mr-2 h-5 w-5" />
                View Payslips
              </Button>
            </Link>
          )}
          {payrollPeriod.status === PayrollPeriodStatus.APPROVED && (
            <SendPayrollToPayableButton
              payrollPeriodId={payrollPeriod.id}
              payrollPeriodName={payrollPeriod.name}
              payrollStatus={payrollPeriod.status as PayrollPeriodStatus}
              totalNetPay={totals.totalNetPay}
              totalEmployees={totals.totalEmployees}
              payablesCreated={payablesCreated}
              onSuccess={() => {
                setPayablesCreated(true);
                fetchPayrollPeriod();
              }}
            />
          )}
        </div>
      </div>

      <div className="mb-6">
        <div className="border-b border-gray-200">
          <ul className="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500">
            <li className="mr-2">
              <button
                className={`inline-flex items-center p-4 border-b-2 rounded-t-lg ${
                  activeTab === 'overview'
                    ? 'text-blue-600 border-blue-600'
                    : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('overview')}
              >
                <HiOutlineCalendar className="w-4 h-4 mr-2" />
                Overview
              </button>
            </li>
            <li className="mr-2">
              <button
                className={`inline-flex items-center p-4 border-b-2 rounded-t-lg ${
                  activeTab === 'employees'
                    ? 'text-blue-600 border-blue-600'
                    : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('employees')}
              >
                <HiOutlineUsers className="w-4 h-4 mr-2" />
                Employees
              </button>
            </li>
          </ul>
        </div>
      </div>

      {activeTab === 'overview' && (
        <Card>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Period Information</h3>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500">Period Name</p>
                  <p className="font-medium">{payrollPeriod.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Start Date</p>
                  <p>{format(new Date(payrollPeriod.start_date), 'MMMM d, yyyy')}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">End Date</p>
                  <p>{format(new Date(payrollPeriod.end_date), 'MMMM d, yyyy')}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Payment Date</p>
                  <p>{format(new Date(payrollPeriod.payment_date), 'MMMM d, yyyy')}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <div>{renderStatusBadge(payrollPeriod.status as PayrollPeriodStatus)}</div>
                </div>
                <div>
                  <p className="text-sm text-gray-500">13th Month Pay</p>
                  <div>
                    {payrollPeriod.is_thirteenth_month ? (
                      <Badge color="success" icon={HiOutlineCheck}>Yes</Badge>
                    ) : (
                      <Badge color="gray" icon={HiOutlineX}>No</Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-4">Summary</h3>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500">Total Employees</p>
                  <p className="font-medium">{totals.totalEmployees}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Basic Pay</p>
                  <p className="font-medium">{formatCurrency(totals.totalBasicPay)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Allowances</p>
                  <p className="font-medium">{formatCurrency(totals.totalAllowances)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Deductions</p>
                  <p className="font-medium">{formatCurrency(totals.totalDeductions)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Gross Pay</p>
                  <p className="font-medium">{formatCurrency(totals.totalGrossPay)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Net Pay</p>
                  <p className="font-medium text-lg text-green-600">{formatCurrency(totals.totalNetPay)}</p>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      {activeTab === 'employees' && (
        <Card>
          <div className="overflow-x-auto">
            <Table striped>
              <Table.Head>
                <Table.HeadCell>Employee</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>Basic Pay</Table.HeadCell>
                <Table.HeadCell>Allowances</Table.HeadCell>
                <Table.HeadCell>Deductions</Table.HeadCell>
                <Table.HeadCell>Gross Pay</Table.HeadCell>
                <Table.HeadCell>Net Pay</Table.HeadCell>
                <Table.HeadCell>Actions</Table.HeadCell>
              </Table.Head>
              <Table.Body>
                {payrollPeriod.payroll_items && payrollPeriod.payroll_items.length > 0 ? (
                  payrollPeriod.payroll_items.map((item) => (
                    <Table.Row key={item.id}>
                      <Table.Cell className="font-medium">
                        {item.employee ? (
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                              {item.employee.profile_image_url ? (
                                <img
                                  src={item.employee.profile_image_url}
                                  alt={`${item.employee.first_name} ${item.employee.last_name}`}
                                  className="w-8 h-8 rounded-full object-cover"
                                />
                              ) : (
                                <span className="text-xs font-semibold">
                                  {item.employee.first_name?.[0]}{item.employee.last_name?.[0]}
                                </span>
                              )}
                            </div>
                            <div>
                              <p className="font-medium">
                                {item.employee.first_name} {item.employee.last_name}
                              </p>
                              {item.employee.employee_number && (
                                <p className="text-xs text-gray-500">
                                  #{item.employee.employee_number}
                                </p>
                              )}
                            </div>
                          </div>
                        ) : (
                          'Unknown Employee'
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        {renderPayrollItemStatusBadge(item.status as PayrollItemStatus)}
                      </Table.Cell>
                      <Table.Cell>{formatCurrency(Number(item.basic_pay))}</Table.Cell>
                      <Table.Cell>{formatCurrency(Number(item.total_allowances))}</Table.Cell>
                      <Table.Cell>{formatCurrency(Number(item.total_deductions))}</Table.Cell>
                      <Table.Cell>{formatCurrency(Number(item.gross_pay))}</Table.Cell>
                      <Table.Cell className="font-medium text-green-600">
                        {formatCurrency(Number(item.net_pay))}
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex space-x-2">
                          <Link to={`/payroll/items/${item.id}`}>
                            <Button size="xs" color="info">
                              Details
                            </Button>
                          </Link>
                          {(payrollPeriod.status === PayrollPeriodStatus.PROCESSING || payrollPeriod.status === PayrollPeriodStatus.DRAFT) && (
                            <Link to={`/payroll/items/${item.id}/edit`}>
                              <Button size="xs" color="warning">
                                Edit
                              </Button>
                            </Link>
                          )}
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  ))
                ) : (
                  <Table.Row>
                    <Table.Cell colSpan={8} className="text-center py-4">
                      No payroll items found for this period.
                      {payrollPeriod.status === PayrollPeriodStatus.DRAFT && (
                        <div className="mt-2">
                          <Link to={`/payroll/periods/${payrollPeriod.id}/process`}>
                            <Button size="xs" color="primary">
                              Process Payroll
                            </Button>
                          </Link>
                        </div>
                      )}
                    </Table.Cell>
                  </Table.Row>
                )}
              </Table.Body>
            </Table>
          </div>
        </Card>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        show={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        size="md"
      >
        <Modal.Header>Delete Payroll Period</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>Are you sure you want to delete this payroll period? This action cannot be undone.</p>
            {submitError && (
              <Alert color="failure" icon={HiOutlineExclamationCircle}>
                {submitError}
              </Alert>
            )}
            <div className="flex justify-end space-x-2">
              <Button
                color="gray"
                onClick={() => setShowDeleteModal(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                color="failure"
                onClick={handleDeletePayrollPeriod}
                isProcessing={isSubmitting}
              >
                Delete
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>

      {/* Approve Confirmation Modal */}
      <Modal
        show={showApproveModal}
        onClose={() => setShowApproveModal(false)}
        size="md"
      >
        <Modal.Header>Approve Payroll Period</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>
              Are you sure you want to approve this payroll period? This will finalize all payroll
              calculations and prepare the payslips for payment.
            </p>
            {submitError && (
              <Alert color="failure" icon={HiOutlineExclamationCircle}>
                {submitError}
              </Alert>
            )}
            <div className="flex justify-end space-x-2">
              <Button
                color="gray"
                onClick={() => setShowApproveModal(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                color="success"
                onClick={handleApprovePayrollPeriod}
                isProcessing={isSubmitting}
              >
                Approve
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default PayrollPeriodDetails;
