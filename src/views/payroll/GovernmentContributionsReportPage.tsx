import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  Card,
  Spinner,
  Alert,
  Label,
  Select,
  TextInput
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineExclamationCircle,
  HiOutlineDocumentReport,
  HiOutlineOfficeBuilding
} from 'react-icons/hi';
import { format } from 'date-fns';
import { useOrganization } from '../../context/OrganizationContext';
import { getGovernmentContributionsReport } from '../../services/payrollReporting';
import { 
  batchProcessGovernmentReports, 
  getOrganizationGovernmentIds,
  validateGovernmentContributionData
} from '../../services/governmentReporting';
import PageTitle from '../../components/shared/PageTitle';
import EnhancedGovernmentContributionsReport from '../../components/payroll/EnhancedGovernmentContributionsReport';

const GovernmentContributionsReportPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const [month, setMonth] = useState<number>(new Date().getMonth() + 1);
  const [year, setYear] = useState<number>(new Date().getFullYear());
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isBatchProcessing, setIsBatchProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<any>(null);
  const [organizationDetails, setOrganizationDetails] = useState<any>({
    sssNumber: '',
    philHealthNumber: '',
    pagibigNumber: '',
    address: ''
  });
  const [validationResults, setValidationResults] = useState<any>({
    sss: { valid: true, missingEmployees: [] },
    philhealth: { valid: true, missingEmployees: [] },
    pagibig: { valid: true, missingEmployees: [] }
  });
  
  // Get month name
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  
  // Load report data
  useEffect(() => {
    if (!currentOrganization) return;
    
    fetchReportData();
    fetchOrganizationDetails();
  }, [currentOrganization, month, year]);
  
  // Fetch report data
  const fetchReportData = async () => {
    if (!currentOrganization) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Create date range for the specified month
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0); // Last day of the month
      
      const { data, error } = await getGovernmentContributionsReport({
        organizationId: currentOrganization.id,
        startDate,
        endDate
      });
      
      if (error) {
        throw new Error(error);
      }
      
      setReportData(data);
      
      // Validate data for each report type
      const sssValidation = await validateGovernmentContributionData(
        currentOrganization.id,
        'sss'
      );
      
      const philhealthValidation = await validateGovernmentContributionData(
        currentOrganization.id,
        'philhealth'
      );
      
      const pagibigValidation = await validateGovernmentContributionData(
        currentOrganization.id,
        'pagibig'
      );
      
      setValidationResults({
        sss: sssValidation,
        philhealth: philhealthValidation,
        pagibig: pagibigValidation
      });
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fetch organization details
  const fetchOrganizationDetails = async () => {
    if (!currentOrganization) return;
    
    try {
      // Get organization address
      const { data: orgData, error: orgError } = await getOrganizationGovernmentIds(
        currentOrganization.id
      );
      
      if (orgError) {
        throw new Error(orgError);
      }
      
      if (orgData) {
        setOrganizationDetails({
          ...organizationDetails,
          ...orgData,
          address: currentOrganization.address || '123 Main St, Manila, Philippines' // Fallback address
        });
      }
    } catch (err: any) {
      console.error('Error fetching organization details:', err);
    }
  };
  
  // Handle batch processing
  const handleBatchProcess = async () => {
    if (!currentOrganization) return;
    
    setIsBatchProcessing(true);
    
    try {
      const result = await batchProcessGovernmentReports({
        organizationId: currentOrganization.id,
        month,
        year,
        reportTypes: ['sss', 'philhealth', 'pagibig'],
        format: 'pdf'
      });
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to process reports');
      }
      
      // Show success message
      alert('Reports processed successfully!');
      
      // In a real implementation, you would provide links to download the reports
      console.log('Generated reports:', result.reports);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsBatchProcessing(false);
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button color="gray" className="mr-4" onClick={() => navigate('/payroll/reports')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Reports
          </Button>
          <PageTitle title="Government Contributions Reports" />
        </div>
      </div>
      
      {error && (
        <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
          <span className="font-medium">Error!</span> {error}
        </Alert>
      )}
      
      {!validationResults.sss.valid || !validationResults.philhealth.valid || !validationResults.pagibig.valid ? (
        <Alert color="warning" icon={HiOutlineExclamationCircle} className="mb-4">
          <span className="font-medium">Warning!</span> Some employees are missing government ID numbers.
          Please update employee records for accurate reporting.
        </Alert>
      ) : null}
      
      {isLoading ? (
        <div className="flex justify-center items-center p-12">
          <Spinner size="xl" />
          <span className="ml-2">Loading report data...</span>
        </div>
      ) : reportData ? (
        <EnhancedGovernmentContributionsReport
          data={reportData}
          organizationName={currentOrganization?.name || ''}
          organizationDetails={organizationDetails}
          isLoading={isBatchProcessing}
          onBatchProcess={handleBatchProcess}
        />
      ) : (
        <Card>
          <div className="p-6 text-center">
            <HiOutlineDocumentReport className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900">No Report Data</h3>
            <p className="mt-1 text-sm text-gray-500">
              There is no government contribution data available for {monthNames[month - 1]} {year}.
            </p>
            <div className="mt-6">
              <Button color="primary" onClick={fetchReportData}>
                Refresh Data
              </Button>
            </div>
          </div>
        </Card>
      )}
      
      {/* Organization Details Section */}
      <Card className="mt-6">
        <h3 className="text-lg font-medium mb-4 flex items-center">
          <HiOutlineOfficeBuilding className="mr-2 h-5 w-5" />
          Organization Government IDs
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="sss-number" value="SSS Employer Number" />
            <TextInput
              id="sss-number"
              value={organizationDetails.sssNumber}
              onChange={(e) => setOrganizationDetails({
                ...organizationDetails,
                sssNumber: e.target.value
              })}
              placeholder="Enter SSS employer number"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="philhealth-number" value="PhilHealth Employer Number" />
            <TextInput
              id="philhealth-number"
              value={organizationDetails.philHealthNumber}
              onChange={(e) => setOrganizationDetails({
                ...organizationDetails,
                philHealthNumber: e.target.value
              })}
              placeholder="Enter PhilHealth employer number"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="pagibig-number" value="Pag-IBIG Employer Number" />
            <TextInput
              id="pagibig-number"
              value={organizationDetails.pagibigNumber}
              onChange={(e) => setOrganizationDetails({
                ...organizationDetails,
                pagibigNumber: e.target.value
              })}
              placeholder="Enter Pag-IBIG employer number"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="address" value="Employer Address" />
            <TextInput
              id="address"
              value={organizationDetails.address}
              onChange={(e) => setOrganizationDetails({
                ...organizationDetails,
                address: e.target.value
              })}
              placeholder="Enter employer address"
              className="mt-1"
            />
          </div>
        </div>
        
        <div className="mt-4">
          <Button color="primary">
            Save Organization Details
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default GovernmentContributionsReportPage;
