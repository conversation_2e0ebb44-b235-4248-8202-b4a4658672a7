import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useOrganization } from '../../context/OrganizationContext';
import { format, subMonths, startOfYear, endOfYear } from 'date-fns';
import { getPayrollSummaryReport } from '../../services/payrollReporting';
import { formatCurrency } from '../../utils/formatters';

const PayrollAnalyticsEnhanced: React.FC = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const [timeRange, setTimeRange] = useState<string>('year');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [summaryData, setSummaryData] = useState<any[]>([]);

  // Load payroll summary data
  useEffect(() => {
    const fetchData = async () => {
      if (!currentOrganization) return;

      setIsLoading(true);
      setError(null);

      try {
        // Determine date range based on selected time range
        let startDate, endDate;
        const now = new Date();

        switch (timeRange) {
          case 'quarter':
            startDate = subMonths(now, 3);
            endDate = now;
            break;
          case 'half_year':
            startDate = subMonths(now, 6);
            endDate = now;
            break;
          case 'year':
          default:
            startDate = startOfYear(now);
            endDate = endOfYear(now);
            break;
        }

        const { data, error } = await getPayrollSummaryReport({
          organizationId: currentOrganization.id,
          startDate,
          endDate
        });

        if (error) {
          throw new Error(error);
        }

        if (data) {
          // Sort by date
          const sortedData = [...data].sort((a, b) =>
            new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
          );
          setSummaryData(sortedData);
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentOrganization, timeRange]);

  // Calculate summary metrics
  const totalGrossPay = summaryData.reduce((sum, period) => sum + period.totalGrossPay, 0);
  const totalNetPay = summaryData.reduce((sum, period) => sum + period.totalNetPay, 0);
  const totalDeductions = summaryData.reduce((sum, period) => sum + period.totalDeductions, 0);
  const totalAllowances = summaryData.reduce((sum, period) => sum + period.totalAllowances, 0);
  const averageEmployees = summaryData.length > 0
    ? Math.round(summaryData.reduce((sum, period) => sum + period.totalEmployees, 0) / summaryData.length)
    : 0;
  const averageNetPay = averageEmployees > 0 && summaryData.length > 0
    ? totalNetPay / (averageEmployees * summaryData.length)
    : 0;

  // Group data by month for trend analysis
  const monthlyData = summaryData.reduce((acc: any, period) => {
    const month = format(new Date(period.startDate), 'MMM yyyy');
    if (!acc[month]) {
      acc[month] = {
        month,
        grossPay: 0,
        netPay: 0,
        deductions: 0,
        allowances: 0,
        employees: 0,
        count: 0
      };
    }

    acc[month].grossPay += period.totalGrossPay;
    acc[month].netPay += period.totalNetPay;
    acc[month].deductions += period.totalDeductions;
    acc[month].allowances += period.totalAllowances;
    acc[month].employees += period.totalEmployees;
    acc[month].count += 1;

    return acc;
  }, {});

  // Convert to array and calculate averages
  const monthlyTrends = Object.values(monthlyData).map((item: any) => ({
    ...item,
    employees: Math.round(item.employees / item.count)
  }));

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-4 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Payroll Analytics</h1>
          <p className="text-gray-500">Visualize and analyze payroll data</p>
        </div>
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">Time Range:</label>
          <select
            className="rounded-lg border border-gray-300 p-2"
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <option value="quarter">Last Quarter</option>
            <option value="half_year">Last 6 Months</option>
            <option value="year">This Year</option>
          </select>
          <button
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            onClick={() => navigate('/payroll/reports')}
          >
            View Reports
          </button>
        </div>
      </div>

      {isLoading && (
        <div className="flex justify-center items-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {error && (
        <div className="p-4 mb-6 bg-red-50 border-l-4 border-red-400">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {!isLoading && !error && summaryData.length === 0 && (
        <div className="p-4 mb-6 bg-yellow-50 border-l-4 border-yellow-400">
          <p className="text-yellow-700">No payroll data available for the selected time range.</p>
        </div>
      )}

      {!isLoading && !error && summaryData.length > 0 && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-green-100 text-green-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h5 className="text-xl font-bold leading-none text-gray-900">
                    {formatCurrency(totalNetPay)}
                  </h5>
                  <p className="text-sm font-normal text-gray-500">
                    Total Net Pay
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-blue-100 text-blue-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h5 className="text-xl font-bold leading-none text-gray-900">
                    {formatCurrency(totalGrossPay)}
                  </h5>
                  <p className="text-sm font-normal text-gray-500">
                    Total Gross Pay
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-red-100 text-red-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h5 className="text-xl font-bold leading-none text-gray-900">
                    {formatCurrency(totalDeductions)}
                  </h5>
                  <p className="text-sm font-normal text-gray-500">
                    Total Deductions
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-purple-100 text-purple-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h5 className="text-xl font-bold leading-none text-gray-900">
                    {averageEmployees}
                  </h5>
                  <p className="text-sm font-normal text-gray-500">
                    Average Employees
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Payroll Distribution</h2>
              <div className="h-64">
                <div className="relative h-full">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-full max-w-xs">
                      <div className="mb-4">
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium text-green-700">Net Pay</span>
                          <span className="text-sm font-medium text-green-700">
                            {totalGrossPay > 0 ? Math.round((totalNetPay / totalGrossPay) * 100) : 0}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-green-600 h-2.5 rounded-full"
                            style={{ width: `${totalGrossPay > 0 ? Math.round((totalNetPay / totalGrossPay) * 100) : 0}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="mb-4">
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium text-red-700">Deductions</span>
                          <span className="text-sm font-medium text-red-700">
                            {totalGrossPay > 0 ? Math.round((totalDeductions / totalGrossPay) * 100) : 0}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-red-600 h-2.5 rounded-full"
                            style={{ width: `${totalGrossPay > 0 ? Math.round((totalDeductions / totalGrossPay) * 100) : 0}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="mb-4">
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium text-blue-700">Allowances</span>
                          <span className="text-sm font-medium text-blue-700">
                            {totalGrossPay > 0 ? Math.round((totalAllowances / totalGrossPay) * 100) : 0}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-blue-600 h-2.5 rounded-full"
                            style={{ width: `${totalGrossPay > 0 ? Math.round((totalAllowances / totalGrossPay) * 100) : 0}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Employee Count Trend</h2>
              <div className="h-64 flex items-center justify-center">
                {monthlyTrends.length > 0 ? (
                  <div className="w-full h-full flex items-end justify-between">
                    {monthlyTrends.map((item, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <div
                          className="bg-indigo-600 w-12 rounded-t-lg"
                          style={{
                            height: `${Math.max(20, (item.employees / Math.max(...monthlyTrends.map(i => i.employees))) * 200)}px`
                          }}
                        ></div>
                        <div className="text-xs mt-2 text-gray-600">{item.month}</div>
                        <div className="text-xs font-semibold">{item.employees}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No trend data available</p>
                )}
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Payroll Trend</h2>
            <div className="h-64">
              {monthlyTrends.length > 0 ? (
                <div className="w-full h-full">
                  <table className="w-full text-sm text-left text-gray-500">
                    <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3">Month</th>
                        <th scope="col" className="px-6 py-3">Employees</th>
                        <th scope="col" className="px-6 py-3">Gross Pay</th>
                        <th scope="col" className="px-6 py-3">Deductions</th>
                        <th scope="col" className="px-6 py-3">Net Pay</th>
                        <th scope="col" className="px-6 py-3">Avg. Per Employee</th>
                      </tr>
                    </thead>
                    <tbody>
                      {monthlyTrends.map((item, index) => (
                        <tr key={index} className="bg-white border-b">
                          <td className="px-6 py-4 font-medium text-gray-900">{item.month}</td>
                          <td className="px-6 py-4">{item.employees}</td>
                          <td className="px-6 py-4">{formatCurrency(item.grossPay)}</td>
                          <td className="px-6 py-4">{formatCurrency(item.deductions)}</td>
                          <td className="px-6 py-4 font-medium text-green-600">{formatCurrency(item.netPay)}</td>
                          <td className="px-6 py-4">
                            {formatCurrency(item.employees > 0 ? item.netPay / item.employees : 0)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500">No trend data available</p>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default PayrollAnalyticsEnhanced;
