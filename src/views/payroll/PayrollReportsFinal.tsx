import React from 'react';

const PayrollReportsFinal: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-4">
        <h1 className="text-2xl font-bold">Payroll Reports</h1>
        <p className="text-gray-500">Generate and view payroll reports</p>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Available Reports</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="border rounded-lg p-4 hover:bg-gray-50">
            <h3 className="text-lg font-medium mb-2">Summary Report</h3>
            <p className="text-gray-600 mb-4">
              Generate a summary report of payroll periods showing total amounts for each period.
            </p>
            <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
              Generate Report
            </button>
          </div>
          
          <div className="border rounded-lg p-4 hover:bg-gray-50">
            <h3 className="text-lg font-medium mb-2">Employee Earnings</h3>
            <p className="text-gray-600 mb-4">
              Generate a detailed report of employee earnings across payroll periods.
            </p>
            <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
              Generate Report
            </button>
          </div>
          
          <div className="border rounded-lg p-4 hover:bg-gray-50">
            <h3 className="text-lg font-medium mb-2">Government Contributions</h3>
            <p className="text-gray-600 mb-4">
              Generate a report of government contributions (SSS, PhilHealth, Pag-IBIG, and Withholding Tax).
            </p>
            <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
              Generate Report
            </button>
          </div>
          
          <div className="border rounded-lg p-4 hover:bg-gray-50">
            <h3 className="text-lg font-medium mb-2">BIR Withholding Tax</h3>
            <p className="text-gray-600 mb-4">
              Generate a BIR withholding tax report (2316) for employees.
            </p>
            <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
              Generate Report
            </button>
          </div>
        </div>
        
        <div className="border-t pt-4">
          <h3 className="text-lg font-medium mb-4">Report Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                className="w-full rounded-lg border border-gray-300 p-2.5"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                className="w-full rounded-lg border border-gray-300 p-2.5"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Report Format
              </label>
              <select className="w-full rounded-lg border border-gray-300 p-2.5">
                <option value="html">HTML</option>
                <option value="pdf">PDF</option>
                <option value="csv">CSV</option>
                <option value="excel">Excel</option>
              </select>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-8 bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Sample Report Preview</h2>
        
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-500">
            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3">Period</th>
                <th scope="col" className="px-6 py-3">Date Range</th>
                <th scope="col" className="px-6 py-3">Employees</th>
                <th scope="col" className="px-6 py-3">Gross Pay</th>
                <th scope="col" className="px-6 py-3">Deductions</th>
                <th scope="col" className="px-6 py-3">Net Pay</th>
                <th scope="col" className="px-6 py-3">Status</th>
              </tr>
            </thead>
            <tbody>
              <tr className="bg-white border-b">
                <td className="px-6 py-4 font-medium text-gray-900">
                  January 2023
                </td>
                <td className="px-6 py-4">
                  Jan 1, 2023 - Jan 15, 2023
                </td>
                <td className="px-6 py-4">24</td>
                <td className="px-6 py-4">₱240,000.00</td>
                <td className="px-6 py-4">₱48,000.00</td>
                <td className="px-6 py-4 font-medium text-green-600">₱192,000.00</td>
                <td className="px-6 py-4">
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                    Paid
                  </span>
                </td>
              </tr>
              <tr className="bg-white border-b">
                <td className="px-6 py-4 font-medium text-gray-900">
                  January 2023
                </td>
                <td className="px-6 py-4">
                  Jan 16, 2023 - Jan 31, 2023
                </td>
                <td className="px-6 py-4">24</td>
                <td className="px-6 py-4">₱240,000.00</td>
                <td className="px-6 py-4">₱48,000.00</td>
                <td className="px-6 py-4 font-medium text-green-600">₱192,000.00</td>
                <td className="px-6 py-4">
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                    Paid
                  </span>
                </td>
              </tr>
              <tr className="bg-white border-b">
                <td className="px-6 py-4 font-medium text-gray-900">
                  February 2023
                </td>
                <td className="px-6 py-4">
                  Feb 1, 2023 - Feb 15, 2023
                </td>
                <td className="px-6 py-4">25</td>
                <td className="px-6 py-4">₱250,000.00</td>
                <td className="px-6 py-4">₱50,000.00</td>
                <td className="px-6 py-4 font-medium text-green-600">₱200,000.00</td>
                <td className="px-6 py-4">
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                    Approved
                  </span>
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr className="font-semibold text-gray-900">
                <th scope="row" className="px-6 py-3 text-base" colSpan={3}>Totals</th>
                <td className="px-6 py-3">₱730,000.00</td>
                <td className="px-6 py-3">₱146,000.00</td>
                <td className="px-6 py-3 text-green-600">₱584,000.00</td>
                <td className="px-6 py-3"></td>
              </tr>
            </tfoot>
          </table>
        </div>
        
        <div className="mt-4 flex justify-end">
          <button className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
            Print/Download
          </button>
        </div>
      </div>
    </div>
  );
};

export default PayrollReportsFinal;
