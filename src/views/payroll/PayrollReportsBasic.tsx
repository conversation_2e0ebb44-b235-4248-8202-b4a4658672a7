import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, Spinner } from 'flowbite-react';
import { HiOutlineDocumentReport } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import PageTitle from '../../components/shared/PageTitle';

const PayrollReportsBasic: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const [isLoading, setIsLoading] = useState(false);

  const handleGenerateReport = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <PageTitle
        title="Payroll Reports"
        subtitle="Generate and view payroll reports"
      />

      <Card>
        <h3 className="text-lg font-medium mb-4">Payroll Reports</h3>
        <p className="text-gray-500 mb-4">
          Generate various payroll reports including summary reports, employee earnings, and government contributions.
        </p>
        
        <div className="mt-4">
          <Button
            color="primary"
            onClick={handleGenerateReport}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Generating...
              </>
            ) : (
              <>
                <HiOutlineDocumentReport className="mr-2 h-5 w-5" />
                Generate Report
              </>
            )}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default PayrollReportsBasic;
