import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Spinner,
  Alert,
  TextInput,
  Dropdown,
  Select,
  Modal,
  Label,
  Badge
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineSearch,
  HiOutlineDotsVertical,
  HiOutlineExclamation,
  HiOutlineCurrencyDollar,
  HiOutlineCalendar
} from 'react-icons/hi';
import { format } from 'date-fns';
import { useOrganization } from '../../context/OrganizationContext';
import { getEmployees, Employee, EmployeeWithDetails } from '../../services/employee';
import { supabase } from '../../lib/supabase';
import PageTitle from '../../components/shared/PageTitle';
import EmployeeSearchSelect from '../../components/payroll/EmployeeSearchSelect';
import Pagination from '../../components/common/Pagination';

// Interface for employee salary
interface EmployeeSalary {
  id: string;
  employee_id: string;
  basic_salary: number;
  daily_rate?: number;
  rate_type: 'monthly' | 'daily';
  effective_date: string;
  end_date?: string;
  currency: string;
  pay_frequency: string;
  created_at: string;
  updated_at: string;
}

// Interface for employee salary with employee details
interface EmployeeSalaryWithDetails extends EmployeeSalary {
  employee?: {
    id: string;
    first_name: string;
    middle_name?: string;
    last_name: string;
    employee_number?: string;
  };
}

const EmployeeSalaries: React.FC = () => {
  const { currentOrganization, loading: isOrgLoading } = useOrganization();
  const [salaries, setSalaries] = useState<EmployeeSalaryWithDetails[]>([]);
  const [employees, setEmployees] = useState<EmployeeWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [selectedSalaryId, setSelectedSalaryId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Form state for add/edit
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string>('');
  const [basicSalary, setBasicSalary] = useState<string>('');
  const [dailyRate, setDailyRate] = useState<string>('');
  const [rateType, setRateType] = useState<'monthly' | 'daily'>('monthly');
  const [effectiveDate, setEffectiveDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState<string>('');
  const [currency, setCurrency] = useState<string>('PHP');
  const [payFrequency, setPayFrequency] = useState<string>('monthly');

  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Fetch employee salaries
  const fetchEmployeeSalaries = async () => {
    if (!currentOrganization) return;

    setIsLoading(true);
    setError(null);

    try {
      console.log('Fetching employee salaries for organization:', currentOrganization.id);

      // Create a query builder
      let query = supabase
        .from('employee_salary')
        .select(`
          *,
          employee:employee_id (
            id,
            first_name,
            middle_name,
            last_name,
            employee_number
          )
        `, { count: 'exact' });

      // Try to filter by organization_id if the column exists
      if (currentOrganization) {
        try {
          // First check if any records exist
          const { data: checkData, error: checkError } = await supabase
            .from('employee_salary')
            .select('id')
            .limit(1);

          console.log('Checking if employee_salary table has records:', checkData, checkError);

          // Try to apply organization filter
          query = query.eq('organization_id', currentOrganization.id);
        } catch (err) {
          console.log('Organization ID filter not applied:', err);

          // If organization_id filter fails, try to join with employees table
          query = query.select(`
            *,
            employee:employee_id (
              id,
              first_name,
              middle_name,
              last_name,
              employee_number,
              organization_id
            )
          `, { count: 'exact' })
          .filter('employee.organization_id', 'eq', currentOrganization.id);
        }
      }

      // Apply sorting and pagination
      query = query
        .order('created_at', { ascending: false })
        .range((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage - 1);

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching employee salaries:', error);
        throw new Error(error.message);
      }

      console.log('Fetched employee salaries:', data);
      setSalaries(data || []);
      setTotalCount(count || 0);
    } catch (err: any) {
      console.error('Error in fetchEmployeeSalaries:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch employees
  const fetchEmployees = async () => {
    if (!currentOrganization) {
      console.error('No current organization - waiting for organization context to load');
      return;
    }

    try {
      console.log('Fetching employees for organization:', currentOrganization.id, currentOrganization.name);

      // First try a simple query to check if we can access the employees table
      const { count, error: countError } = await supabase
        .from('employees')
        .select('*', { count: 'exact', head: true })
        .eq('organization_id', currentOrganization.id);

      console.log('Employee count check:', count, countError);

      if (countError) {
        console.error('Error checking employee count:', countError);
        throw new Error(countError.message);
      }

      if (count === 0) {
        console.warn('No employees found for organization:', currentOrganization.id);
        setEmployees([]);
        return;
      }

      // Directly fetch employees from the database with all required fields
      const { data: directEmployees, error: directError } = await supabase
        .from('employees')
        .select(`
          id,
          first_name,
          last_name,
          middle_name,
          employee_number,
          position_id,
          position:position_id (
            id,
            title
          )
        `)
        .eq('organization_id', currentOrganization.id)
        .eq('is_active', true);

      console.log('Direct employee query result:', directEmployees?.length, directError);

      if (directError) {
        console.error('Error fetching employees directly:', directError);
        throw new Error(directError.message);
      }

      if (directEmployees && directEmployees.length > 0) {
        console.log('Setting employees from direct query:', directEmployees.length);
        setEmployees(directEmployees);
        return;
      }

      // If we get here, try a simpler query without the position join
      const { data: simpleEmployees, error: simpleError } = await supabase
        .from('employees')
        .select('id, first_name, last_name, middle_name, employee_number')
        .eq('organization_id', currentOrganization.id)
        .eq('is_active', true);

      console.log('Simple employee query result:', simpleEmployees?.length, simpleError);

      if (simpleError) {
        console.error('Error fetching employees with simple query:', simpleError);
      } else if (simpleEmployees && simpleEmployees.length > 0) {
        console.log('Setting employees from simple query:', simpleEmployees.length);
        setEmployees(simpleEmployees);
        return;
      }

      // Last resort: fallback to service function
      const { employees, error } = await getEmployees(currentOrganization.id, {
        isActive: true
      });

      if (error) {
        console.error('Error from getEmployees:', error);
        throw new Error(error);
      }

      console.log('Fetched employees from service:', employees?.length);

      if (employees && employees.length > 0) {
        setEmployees(employees);
      } else {
        console.warn('No employees found from any method for organization:', currentOrganization.id);
        setEmployees([]);
      }
    } catch (err: any) {
      console.error('Error fetching employees:', err);
      // Even if there's an error, we should set an empty array to avoid undefined errors
      setEmployees([]);
    }
  };

  // Load data when component mounts or when page changes
  useEffect(() => {
    fetchEmployeeSalaries();
  }, [currentOrganization, currentPage, itemsPerPage]);

  // Load employees when component mounts
  useEffect(() => {
    if (currentOrganization) {
      console.log('Organization changed, fetching employees for:', currentOrganization.name);
      fetchEmployees();
    }
  }, [currentOrganization]);

  // Load employees when modal is opened
  useEffect(() => {
    if (showAddModal && currentOrganization) {
      console.log('Add modal opened, fetching employees...');
      fetchEmployees();
    }
  }, [showAddModal, currentOrganization]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchEmployeeSalaries();
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Reset form
  const resetForm = () => {
    setSelectedEmployeeId('');
    setBasicSalary('');
    setDailyRate('');
    setRateType('monthly');
    setEffectiveDate(new Date().toISOString().split('T')[0]);
    setEndDate('');
    setCurrency('PHP');
    setPayFrequency('monthly');
    setSubmitError(null);
  };

  // Handle add salary modal open
  const handleAddSalaryClick = async () => {
    resetForm();

    // Check if we have a current organization
    if (!currentOrganization) {
      console.error('Cannot add salary: No organization selected');
      return;
    }

    // Set the modal to open first (the useEffect will trigger employee fetch)
    setShowAddModal(true);

    console.log('Opening add salary modal, current employees count:', employees.length);
  };

  // Handle edit salary modal open
  const handleEditSalaryClick = (salary: EmployeeSalaryWithDetails) => {
    setSelectedSalaryId(salary.id);
    setSelectedEmployeeId(salary.employee_id);
    setBasicSalary(salary.basic_salary.toString());
    setDailyRate(salary.daily_rate?.toString() || '');
    setRateType(salary.rate_type);
    setEffectiveDate(salary.effective_date);
    setEndDate(salary.end_date || '');
    setCurrency(salary.currency);
    setPayFrequency(salary.pay_frequency);
    setShowEditModal(true);
  };

  // Handle delete salary modal open
  const handleDeleteSalaryClick = (salaryId: string) => {
    setSelectedSalaryId(salaryId);
    setShowDeleteModal(true);
  };

  // Handle create salary
  const handleCreateSalary = async () => {
    if (!currentOrganization) return;

    // Validate based on rate type
    if (!selectedEmployeeId || !effectiveDate) {
      setSubmitError('Please fill in all required fields');
      return;
    }

    if (rateType === 'monthly' && !basicSalary) {
      setSubmitError('Please enter the monthly rate');
      return;
    }

    if (rateType === 'daily' && !dailyRate) {
      setSubmitError('Please enter the daily rate');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Calculate values based on rate type
      let calculatedBasicSalary = 0;
      let calculatedDailyRate = 0;

      if (rateType === 'monthly') {
        calculatedBasicSalary = parseFloat(basicSalary);
        // Calculate daily rate: monthly rate / 22 days
        calculatedDailyRate = calculatedBasicSalary / 22;
      } else {
        calculatedDailyRate = parseFloat(dailyRate);
        // Calculate monthly rate: daily rate * 22 days
        calculatedBasicSalary = calculatedDailyRate * 22;
      }

      // First, check if the employee_salary table has the organization_id column
      const { data: tableInfo, error: tableError } = await supabase
        .from('employee_salary')
        .select('*')
        .limit(1);

      console.log('Table info check:', tableInfo, tableError);

      // Prepare the data to insert
      const salaryData: any = {
        employee_id: selectedEmployeeId,
        basic_salary: calculatedBasicSalary,
        daily_rate: calculatedDailyRate,
        rate_type: rateType,
        effective_date: effectiveDate,
        end_date: endDate || null,
        currency,
        pay_frequency: payFrequency
      };

      // Add organization_id if the column exists
      if (currentOrganization) {
        salaryData.organization_id = currentOrganization.id;
      }

      console.log('Inserting salary data:', salaryData);

      const { data, error } = await supabase
        .from('employee_salary')
        .insert(salaryData)
        .select();

      if (error) {
        throw new Error(error.message);
      }

      setShowAddModal(false);
      fetchEmployeeSalaries();
      resetForm();
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle update salary
  const handleUpdateSalary = async () => {
    if (!currentOrganization || !selectedSalaryId) return;

    // Validate based on rate type
    if (!selectedEmployeeId || !effectiveDate) {
      setSubmitError('Please fill in all required fields');
      return;
    }

    if (rateType === 'monthly' && !basicSalary) {
      setSubmitError('Please enter the monthly rate');
      return;
    }

    if (rateType === 'daily' && !dailyRate) {
      setSubmitError('Please enter the daily rate');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Calculate values based on rate type
      let calculatedBasicSalary = 0;
      let calculatedDailyRate = 0;

      if (rateType === 'monthly') {
        calculatedBasicSalary = parseFloat(basicSalary);
        // Calculate daily rate: monthly rate / 22 days
        calculatedDailyRate = calculatedBasicSalary / 22;
      } else {
        calculatedDailyRate = parseFloat(dailyRate);
        // Calculate monthly rate: daily rate * 22 days
        calculatedBasicSalary = calculatedDailyRate * 22;
      }

      // Prepare the data to update
      const salaryData: any = {
        employee_id: selectedEmployeeId,
        basic_salary: calculatedBasicSalary,
        daily_rate: calculatedDailyRate,
        rate_type: rateType,
        effective_date: effectiveDate,
        end_date: endDate || null,
        currency,
        pay_frequency: payFrequency
      };

      // Add organization_id if the column exists
      if (currentOrganization) {
        salaryData.organization_id = currentOrganization.id;
      }

      console.log('Updating salary data:', salaryData);

      // Create a query builder
      let query = supabase
        .from('employee_salary')
        .update(salaryData)
        .eq('id', selectedSalaryId);

      // Add organization_id filter if it exists
      if (currentOrganization) {
        try {
          query = query.eq('organization_id', currentOrganization.id);
        } catch (err) {
          console.log('Organization ID filter not applied:', err);
        }
      }

      const { data, error } = await query.select();

      if (error) {
        throw new Error(error.message);
      }

      setShowEditModal(false);
      fetchEmployeeSalaries();
      resetForm();
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete salary
  const handleDeleteSalary = async () => {
    if (!currentOrganization || !selectedSalaryId) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Create a query builder
      let query = supabase
        .from('employee_salary')
        .delete()
        .eq('id', selectedSalaryId);

      // Add organization_id filter if it exists
      if (currentOrganization) {
        try {
          query = query.eq('organization_id', currentOrganization.id);
        } catch (err) {
          console.log('Organization ID filter not applied:', err);
        }
      }

      const { error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      setShowDeleteModal(false);
      fetchEmployeeSalaries();
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render salary form
  const renderSalaryForm = () => (
    <div className="space-y-4">
      {submitError && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {submitError}
        </Alert>
      )}

      <div>
        <div className="mb-2 block">
          <Label htmlFor="employeeId" value="Employee *" />
        </div>
        <EmployeeSearchSelect
          employees={employees}
          selectedEmployeeId={selectedEmployeeId}
          onSelect={setSelectedEmployeeId}
          required
          isLoading={false}
          placeholder="Search for an employee..."
        />
      </div>

      <div>
        <div className="mb-2 block">
          <Label htmlFor="rateType" value="Rate Type *" />
        </div>
        <Select
          id="rateType"
          value={rateType}
          onChange={(e) => setRateType(e.target.value as 'monthly' | 'daily')}
          required
        >
          <option value="monthly">Monthly Rate</option>
          <option value="daily">Daily Rate</option>
        </Select>
      </div>

      {rateType === 'monthly' ? (
        <div>
          <div className="mb-2 block">
            <Label htmlFor="basicSalary" value="Monthly Rate *" />
          </div>
          <TextInput
            id="basicSalary"
            type="number"
            step="0.01"
            min="0"
            value={basicSalary}
            onChange={(e) => setBasicSalary(e.target.value)}
            required
            icon={HiOutlineCurrencyDollar}
          />
        </div>
      ) : (
        <div>
          <div className="mb-2 block">
            <Label htmlFor="dailyRate" value="Daily Rate *" />
          </div>
          <TextInput
            id="dailyRate"
            type="number"
            step="0.01"
            min="0"
            value={dailyRate}
            onChange={(e) => setDailyRate(e.target.value)}
            required
            icon={HiOutlineCurrencyDollar}
          />
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="effectiveDate" value="Effective Date *" />
          </div>
          <TextInput
            id="effectiveDate"
            type="date"
            value={effectiveDate}
            onChange={(e) => setEffectiveDate(e.target.value)}
            required
            icon={HiOutlineCalendar}
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="endDate" value="End Date" />
          </div>
          <TextInput
            id="endDate"
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            icon={HiOutlineCalendar}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="currency" value="Currency" />
          </div>
          <Select
            id="currency"
            value={currency}
            onChange={(e) => setCurrency(e.target.value)}
          >
            <option value="PHP">PHP - Philippine Peso</option>
            <option value="USD">USD - US Dollar</option>
          </Select>
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="payFrequency" value="Pay Frequency" />
          </div>
          <Select
            id="payFrequency"
            value={payFrequency}
            onChange={(e) => setPayFrequency(e.target.value)}
          >
            <option value="monthly">Monthly</option>
            <option value="semi_monthly">Semi-Monthly</option>
            <option value="weekly">Weekly</option>
            <option value="daily">Daily</option>
          </Select>
        </div>
      </div>
    </div>
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <PageTitle title="Employee Salaries" />
        <Button
          color="primary"
          onClick={handleAddSalaryClick}
          disabled={isOrgLoading || !currentOrganization}
        >
          <HiOutlinePlus className="mr-2 h-5 w-5" />
          Add Salary
        </Button>
      </div>

      {isOrgLoading && (
        <Card>
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
            <p className="ml-4">Loading organization data...</p>
          </div>
        </Card>
      )}

      {!isOrgLoading && !currentOrganization && (
        <Card>
          <div className="p-8 text-center">
            <HiOutlineExclamation className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Organization Selected</h3>
            <p className="text-gray-500">Please select an organization to manage employee salaries.</p>
          </div>
        </Card>
      )}

      {!isOrgLoading && currentOrganization && (
        <>
          {error && (
        <Alert color="failure" className="mb-4">
          <HiOutlineExclamation className="h-4 w-4 mr-2" />
          {error}
        </Alert>
      )}

      <Card>
        <div className="mb-4">
          <form onSubmit={handleSearch}>
            <TextInput
              type="text"
              placeholder="Search employee salaries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={HiOutlineSearch}
              rightIcon={() => (
                <Button type="submit" size="xs" color="light">
                  Search
                </Button>
              )}
            />
          </form>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : salaries.length === 0 ? (
          <div className="text-center p-8">
            <p className="text-gray-500">No employee salaries found.</p>
            <Button color="primary" className="mt-4" onClick={handleAddSalaryClick}>
              <HiOutlinePlus className="mr-2 h-5 w-5" />
              Add Salary
            </Button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table striped>
              <Table.Head>
                <Table.HeadCell>Employee</Table.HeadCell>
                <Table.HeadCell>Rate Type</Table.HeadCell>
                <Table.HeadCell>Monthly Rate</Table.HeadCell>
                <Table.HeadCell>Daily Rate</Table.HeadCell>
                <Table.HeadCell>Effective Date</Table.HeadCell>
                <Table.HeadCell>End Date</Table.HeadCell>
                <Table.HeadCell>Pay Frequency</Table.HeadCell>
                <Table.HeadCell>Actions</Table.HeadCell>
              </Table.Head>
              <Table.Body>
                {salaries.map((salary) => (
                  <Table.Row key={salary.id}>
                    <Table.Cell className="font-medium">
                      {salary.employee ? (
                        <div>
                          <div>{`${salary.employee.first_name} ${salary.employee.middle_name ? salary.employee.middle_name + ' ' : ''}${salary.employee.last_name}`}</div>
                          {salary.employee.employee_number && (
                            <div className="text-xs text-gray-500">
                              ID: {salary.employee.employee_number}
                            </div>
                          )}
                        </div>
                      ) : (
                        'Unknown Employee'
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <Badge color={salary.rate_type === 'daily' ? 'success' : 'info'}>
                        {salary.rate_type === 'daily' ? 'Daily' : 'Monthly'}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      {salary.currency} {(salary.basic_salary || salary.monthly_rate || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </Table.Cell>
                    <Table.Cell>
                      {salary.currency} {(salary.daily_rate || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </Table.Cell>
                    <Table.Cell>
                      {format(new Date(salary.effective_date), 'MMM d, yyyy')}
                    </Table.Cell>
                    <Table.Cell>
                      {salary.end_date ? format(new Date(salary.end_date), 'MMM d, yyyy') : '-'}
                    </Table.Cell>
                    <Table.Cell>
                      {salary.pay_frequency === 'monthly' ? 'Monthly' :
                       salary.pay_frequency === 'semi_monthly' ? 'Semi-Monthly' :
                       salary.pay_frequency === 'weekly' ? 'Weekly' :
                       salary.pay_frequency === 'daily' ? 'Daily' : salary.pay_frequency}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex space-x-2">
                        <Button size="xs" color="light" onClick={() => handleEditSalaryClick(salary)}>
                          <HiOutlinePencil className="mr-1 h-4 w-4" />
                          Edit
                        </Button>
                        <Button size="xs" color="failure" onClick={() => handleDeleteSalaryClick(salary.id)}>
                          <HiOutlineTrash className="mr-1 h-4 w-4" />
                          Delete
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / itemsPerPage)}
          itemsPerPage={itemsPerPage}
          totalItems={totalCount}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1); // Reset to first page when changing items per page
          }}
          itemName="salaries"
        />
      </Card>

      {/* Add Salary Modal */}
      <Modal
        show={showAddModal}
        onClose={() => setShowAddModal(false)}
        size="md"
      >
        <Modal.Header>Add Employee Salary</Modal.Header>
        <Modal.Body>
          {employees.length === 0 ? (
            <div className="p-4 text-center">
              <Spinner size="xl" className="mx-auto mb-4" />
              <p>Loading employees...</p>
            </div>
          ) : (
            renderSalaryForm()
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button color="primary" onClick={handleCreateSalary} disabled={isSubmitting || employees.length === 0}>
            {isSubmitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Saving...
              </>
            ) : (
              'Save'
            )}
          </Button>
          <Button color="gray" onClick={() => setShowAddModal(false)} disabled={isSubmitting}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Edit Salary Modal */}
      <Modal
        show={showEditModal}
        onClose={() => setShowEditModal(false)}
        size="md"
      >
        <Modal.Header>Edit Employee Salary</Modal.Header>
        <Modal.Body>
          {renderSalaryForm()}
        </Modal.Body>
        <Modal.Footer>
          <Button color="primary" onClick={handleUpdateSalary} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Updating...
              </>
            ) : (
              'Update'
            )}
          </Button>
          <Button color="gray" onClick={() => setShowEditModal(false)} disabled={isSubmitting}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        show={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        size="md"
      >
        <Modal.Header>Delete Employee Salary</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>Are you sure you want to delete this salary record? This action cannot be undone.</p>
            {submitError && (
              <Alert color="failure">
                <HiOutlineExclamation className="h-4 w-4 mr-2" />
                {submitError}
              </Alert>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="failure" onClick={handleDeleteSalary} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
          <Button color="gray" onClick={() => setShowDeleteModal(false)} disabled={isSubmitting}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
        </>
      )}
    </div>
  );
};

export default EmployeeSalaries;
