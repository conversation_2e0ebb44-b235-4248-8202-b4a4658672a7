import React, { useState } from 'react';
import { <PERSON>, But<PERSON> } from 'flowbite-react';
import { useOrganization } from '../../context/OrganizationContext';
import PageTitle from '../../components/shared/PageTitle';

const PayrollReportsSimple: React.FC = () => {
  const { currentOrganization } = useOrganization();

  return (
    <div className="container mx-auto px-4 py-8">
      <PageTitle
        title="Payroll Reports"
        subtitle="Generate and view payroll reports"
      />

      <Card>
        <div className="p-4">
          <h3 className="text-lg font-medium mb-4">Simple Test Component</h3>
          <p>This is a simplified version of the PayrollReports component to test for errors.</p>
          <Button color="primary" className="mt-4">
            Test Button
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default PayrollReportsSimple;
