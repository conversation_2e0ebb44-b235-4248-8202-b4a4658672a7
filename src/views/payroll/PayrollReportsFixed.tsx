import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Tabs,
  Label,
  Select,
  Spinner,
  <PERSON><PERSON>,
  Badge
} from 'flowbite-react';
import {
  HiOutlineDocumentReport,
  HiOutlineDocumentDownload,
  HiOutlineExclamationCircle,
  HiOutlineSearch,
  HiOutlineX,
  HiOutlineUser
} from 'react-icons/hi';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { format } from 'date-fns';
import { useOrganization } from '../../context/OrganizationContext';
import { getEmployees, Employee } from '../../services/employee';
import {
  getPayrollSummaryReport,
  getEmployeeEarningsReport,
  getGovernmentContributionsReport,
  getBIRWithholdingTaxReport
} from '../../services/payrollReporting';
import { PayrollReportType, PayrollReportFormat } from '../../types/payroll';
import PageTitle from '../../components/shared/PageTitle';
import { formatCurrency } from '../../utils/formatters';

const PayrollReportsFixed: React.FC = () => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const [activeTab, setActiveTab] = useState<string>('summary');
  const [startDate, setStartDate] = useState<Date | null>(new Date(new Date().getFullYear(), 0, 1));
  const [endDate, setEndDate] = useState<Date | null>(new Date());
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  const [reportFormat, setReportFormat] = useState<PayrollReportFormat>(PayrollReportFormat.HTML);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<any>(null);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState<boolean>(false);

  // Load employees
  React.useEffect(() => {
    const fetchEmployees = async () => {
      if (!currentOrganization) return;

      setIsLoadingEmployees(true);
      try {
        const { employees, error } = await getEmployees(currentOrganization.id);
        if (error) {
          console.error('Error fetching employees:', error);
        } else if (employees) {
          setEmployees(employees);
        }
      } catch (err) {
        console.error('Error in fetchEmployees:', err);
      } finally {
        setIsLoadingEmployees(false);
      }
    };

    fetchEmployees();
  }, [currentOrganization]);

  // Handle employee selection
  const handleEmployeeSelect = (employeeId: string) => {
    if (selectedEmployees.includes(employeeId)) {
      setSelectedEmployees(selectedEmployees.filter(id => id !== employeeId));
    } else {
      setSelectedEmployees([...selectedEmployees, employeeId]);
    }
  };

  // Clear all employee selections
  const clearAllEmployeeSelections = () => {
    setSelectedEmployees([]);
  };

  // Handle report generation
  const handleGenerateReport = async () => {
    if (!currentOrganization) return;

    setIsLoading(true);
    setError(null);
    setReportData(null);

    try {
      const options = {
        organizationId: currentOrganization.id,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
        employeeIds: selectedEmployees.length > 0 ? selectedEmployees : undefined,
        format: reportFormat
      };

      let result;

      switch (activeTab) {
        case 'summary':
          result = await getPayrollSummaryReport(options);
          break;
        case 'employee_earnings':
          result = await getEmployeeEarningsReport(options);
          break;
        case 'government_contributions':
          result = await getGovernmentContributionsReport(options);
          break;
        case 'bir_withholding_tax':
          result = await getBIRWithholdingTaxReport(options);
          break;
        default:
          result = { error: 'Invalid report type' };
      }

      if (result.error) {
        setError(result.error);
      } else {
        setReportData(result.data);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Render summary report
  const renderSummaryReport = () => {
    if (!reportData) return null;

    return (
      <div className="mt-6">
        <h3 className="text-lg font-medium mb-4">Payroll Summary Report</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-500">
            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3">Period</th>
                <th scope="col" className="px-6 py-3">Date Range</th>
                <th scope="col" className="px-6 py-3">Employees</th>
                <th scope="col" className="px-6 py-3">Gross Pay</th>
                <th scope="col" className="px-6 py-3">Deductions</th>
                <th scope="col" className="px-6 py-3">Net Pay</th>
                <th scope="col" className="px-6 py-3">Status</th>
              </tr>
            </thead>
            <tbody>
              {reportData.map((period: any) => (
                <tr key={period.periodId} className="bg-white border-b hover:bg-gray-50">
                  <td className="px-6 py-4 font-medium text-gray-900">
                    {period.periodName}
                    {period.isThirteenthMonth && ' (13th Month)'}
                  </td>
                  <td className="px-6 py-4">
                    {format(new Date(period.startDate), 'MMM d, yyyy')} - {format(new Date(period.endDate), 'MMM d, yyyy')}
                  </td>
                  <td className="px-6 py-4">{period.totalEmployees}</td>
                  <td className="px-6 py-4">{formatCurrency(period.totalGrossPay)}</td>
                  <td className="px-6 py-4">{formatCurrency(period.totalDeductions)}</td>
                  <td className="px-6 py-4 font-medium text-green-600">{formatCurrency(period.totalNetPay)}</td>
                  <td className="px-6 py-4">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      period.status === 'paid' ? 'bg-green-100 text-green-800' :
                      period.status === 'approved' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {period.status.charAt(0).toUpperCase() + period.status.slice(1)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr className="font-semibold text-gray-900">
                <th scope="row" className="px-6 py-3 text-base" colSpan={3}>Totals</th>
                <td className="px-6 py-3">{formatCurrency(reportData.reduce((sum: number, period: any) => sum + period.totalGrossPay, 0))}</td>
                <td className="px-6 py-3">{formatCurrency(reportData.reduce((sum: number, period: any) => sum + period.totalDeductions, 0))}</td>
                <td className="px-6 py-3 text-green-600">{formatCurrency(reportData.reduce((sum: number, period: any) => sum + period.totalNetPay, 0))}</td>
                <td className="px-6 py-3"></td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <PageTitle
        title="Payroll Reports"
        subtitle="Generate and view payroll reports"
      />

      <Card>
        <Tabs.Group
          aria-label="Report tabs"
          style={{ underline: true }}
          onActiveTabChange={(tab) => setActiveTab(Object.values(PayrollReportType)[tab])}
        >
          <Tabs.Item
            active
            title="Summary Report"
            icon={HiOutlineDocumentReport}
          >
            <p className="text-gray-500 mb-4">
              Generate a summary report of payroll periods showing total amounts for each period.
            </p>
          </Tabs.Item>
          <Tabs.Item
            title="Employee Earnings"
            icon={HiOutlineDocumentReport}
          >
            <p className="text-gray-500 mb-4">
              Generate a detailed report of employee earnings across payroll periods.
            </p>
          </Tabs.Item>
          <Tabs.Item
            title="Government Contributions"
            icon={HiOutlineDocumentReport}
          >
            <p className="text-gray-500 mb-4">
              Generate a report of government contributions (SSS, PhilHealth, Pag-IBIG, and Withholding Tax).
            </p>
          </Tabs.Item>
          <Tabs.Item
            title="BIR Withholding Tax"
            icon={HiOutlineDocumentReport}
          >
            <p className="text-gray-500 mb-4">
              Generate a BIR withholding tax report (2316) for employees.
            </p>
          </Tabs.Item>
        </Tabs.Group>

        <div className="mt-4">
          <h3 className="text-lg font-medium mb-4">Report Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="start-date" value="Start Date" />
              <DatePicker
                id="start-date"
                selected={startDate}
                onChange={(date) => setStartDate(date)}
                className="w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900"
                dateFormat="MMMM d, yyyy"
              />
            </div>
            <div>
              <Label htmlFor="end-date" value="End Date" />
              <DatePicker
                id="end-date"
                selected={endDate}
                onChange={(date) => setEndDate(date)}
                className="w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900"
                dateFormat="MMMM d, yyyy"
              />
            </div>
            <div>
              <Label htmlFor="report-format" value="Report Format" />
              <Select
                id="report-format"
                value={reportFormat}
                onChange={(e) => setReportFormat(e.target.value as PayrollReportFormat)}
              >
                <option value={PayrollReportFormat.HTML}>HTML</option>
                <option value={PayrollReportFormat.PDF}>PDF</option>
                <option value={PayrollReportFormat.CSV}>CSV</option>
                <option value={PayrollReportFormat.EXCEL}>Excel</option>
              </Select>
            </div>
            <div className="flex items-end">
              <Button
                color="primary"
                onClick={handleGenerateReport}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    <HiOutlineDocumentReport className="mr-2 h-5 w-5" />
                    Generate Report
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {activeTab !== 'summary' && (
          <div className="mt-4">
            <Label htmlFor="employee-select" value="Select Employees (Optional)" />
            <div className="mt-1">
              {isLoadingEmployees ? (
                <div className="flex items-center">
                  <Spinner size="sm" className="mr-2" />
                  <span>Loading employees...</span>
                </div>
              ) : (
                <div>
                  <div className="relative">
                    <div className="flex items-center border rounded-lg p-2 bg-gray-50">
                      <HiOutlineSearch className="h-5 w-5 text-gray-400 mr-2" />
                      <input
                        type="text"
                        placeholder="Search employees..."
                        className="bg-transparent border-none focus:ring-0 w-full"
                        disabled={employees.length === 0}
                      />
                    </div>
                  </div>

                  {selectedEmployees.length > 0 && (
                    <div className="mt-2">
                      <div className="flex flex-wrap gap-2 mb-2">
                        {selectedEmployees.map(id => {
                          const employee = employees.find(e => e.id === id);
                          return (
                            <Badge
                              key={id}
                              color="info"
                              className="flex items-center"
                            >
                              {employee ? `${employee.first_name} ${employee.last_name}` : id}
                              <button
                                onClick={() => handleEmployeeSelect(id)}
                                className="ml-1 p-1"
                              >
                                <HiOutlineX className="h-3 w-3" />
                              </button>
                            </Badge>
                          );
                        })}
                      </div>
                      <Button
                        size="xs"
                        color="light"
                        onClick={clearAllEmployeeSelections}
                      >
                        Clear All
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
            <p className="mt-1 text-sm text-gray-500">
              Leave empty to include all employees in the report.
            </p>
          </div>
        )}

        {error && (
          <Alert color="failure" icon={HiOutlineExclamationCircle} className="mt-4">
            <span className="font-medium">Error!</span> {error}
          </Alert>
        )}

        {reportData && (
          <div className="mt-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-medium">Report Results</h3>
              <Button color="light" onClick={() => window.print()}>
                <HiOutlineDocumentDownload className="mr-2 h-5 w-5" />
                Print/Download
              </Button>
            </div>

            {activeTab === 'summary' && renderSummaryReport()}
            {activeTab === 'employee_earnings' && reportData && (
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">Employee Earnings Report</h3>
                <p>Employee earnings report will be implemented here.</p>
              </div>
            )}
            {activeTab === 'government_contributions' && reportData && (
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">Government Contributions Report</h3>
                <p>Government contributions report will be implemented here.</p>
              </div>
            )}
            {activeTab === 'bir_withholding_tax' && reportData && (
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">BIR Withholding Tax Report</h3>
                <p>BIR withholding tax report will be implemented here.</p>
              </div>
            )}
          </div>
        )}
      </Card>
    </div>
  );
};

export default PayrollReportsFixed;
