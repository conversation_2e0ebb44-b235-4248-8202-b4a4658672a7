import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Ta<PERSON>,
  Spinner,
  Alert,
  Label,
  TextInput,
  Select,
  ToggleSwitch
} from 'flowbite-react';
import {
  HiOutlineCog,
  HiOutlineCalendar,
  HiOutlineCurrencyDollar,
  HiOutlineDocumentText,
  HiOutlineExclamationCircle,
  HiOutlineSave,
  HiOutlineCloudUpload,
  HiOutlineEye,
  HiOutlineDownload,
  HiOutlineDocumentDuplicate,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getPayrollSettings, createPayrollSettings } from '../../services/payroll';
import { PayrollSetting, PaySchedule } from '../../types/payroll';
import PageTitle from '../../components/shared/PageTitle';
import PayslipTemplatePreview from '../../components/payroll/PayslipTemplatePreview';

const PayrollSettings: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const [settings, setSettings] = useState<PayrollSetting | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('general');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<boolean>(false);

  // Form state
  const [paySchedule, setPaySchedule] = useState<PaySchedule>(PaySchedule.SEMI_MONTHLY);
  const [semiMonthlyDays, setSemiMonthlyDays] = useState<number[]>([15, 30]);
  const [sssEmployerRate, setSssEmployerRate] = useState<number>(8.5);
  const [philhealthEmployerRate, setPhilhealthEmployerRate] = useState<number>(2.0);
  const [pagibigEmployerRate, setPagibigEmployerRate] = useState<number>(2.0);
  const [taxTableVersion, setTaxTableVersion] = useState<string>('2023');
  const [payBasedOnTimeEntries, setPayBasedOnTimeEntries] = useState<boolean>(true);
  const [autoCalculate13thMonth, setAutoCalculate13thMonth] = useState<boolean>(true);
  const [includeAllowancesIn13thMonth, setIncludeAllowancesIn13thMonth] = useState<boolean>(false);
  const [showTemplatePreview, setShowTemplatePreview] = useState<boolean>(false);

  // Fetch payroll settings
  const fetchPayrollSettings = async () => {
    if (!currentOrganization) return;

    setIsLoading(true);
    setError(null);

    try {
      const { settings, error } = await getPayrollSettings(currentOrganization.id);

      if (error) {
        setError(error);
      } else if (settings) {
        setSettings(settings);

        // Initialize form state with settings
        setPaySchedule(settings.pay_schedule as PaySchedule);
        setSemiMonthlyDays(settings.semi_monthly_days as number[]);
        setPayBasedOnTimeEntries(settings.pay_based_on_time_entries ?? true);
        setSssEmployerRate(settings.sss_employer_contribution_rate);
        setPhilhealthEmployerRate(settings.philhealth_employer_contribution_rate);
        setPagibigEmployerRate(settings.pagibig_employer_contribution_rate);
        setTaxTableVersion(settings.tax_table_version);

        // These would be additional fields in a real implementation
        setAutoCalculate13thMonth(true);
        setIncludeAllowancesIn13thMonth(false);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Load settings when the component mounts
  useEffect(() => {
    fetchPayrollSettings();
  }, [currentOrganization]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization) return;

    setIsSubmitting(true);
    setSubmitError(null);
    setSubmitSuccess(false);

    try {
      const settingsData = {
        pay_schedule: paySchedule,
        semi_monthly_days: semiMonthlyDays,
        pay_based_on_time_entries: payBasedOnTimeEntries,
        sss_employer_contribution_rate: sssEmployerRate,
        philhealth_employer_contribution_rate: philhealthEmployerRate,
        pagibig_employer_contribution_rate: pagibigEmployerRate,
        tax_table_version: taxTableVersion
      };

      const { settings: updatedSettings, error } = settings
        ? await createPayrollSettings(currentOrganization.id, settingsData)
        : await createPayrollSettings(currentOrganization.id, settingsData);

      if (error) {
        setSubmitError(error);
      } else {
        setSettings(updatedSettings || null);
        setSubmitSuccess(true);

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSubmitSuccess(false);
        }, 3000);
      }
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // If loading, show a spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <PageTitle title="Payroll Settings" />
      </div>

      {error && (
        <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
          <span className="font-medium">Error!</span> {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <ul className="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500">
              <li className="mr-2">
                <button
                  type="button"
                  className={`inline-flex items-center p-4 border-b-2 rounded-t-lg ${
                    activeTab === 'general'
                      ? 'text-blue-600 border-blue-600'
                      : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                  }`}
                  onClick={() => setActiveTab('general')}
                >
                  <HiOutlineCog className="w-4 h-4 mr-2" />
                  General Settings
                </button>
              </li>
              <li className="mr-2">
                <button
                  type="button"
                  className={`inline-flex items-center p-4 border-b-2 rounded-t-lg ${
                    activeTab === 'contributions'
                      ? 'text-blue-600 border-blue-600'
                      : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                  }`}
                  onClick={() => setActiveTab('contributions')}
                >
                  <HiOutlineCurrencyDollar className="w-4 h-4 mr-2" />
                  Government Contributions
                </button>
              </li>
              <li className="mr-2">
                <button
                  type="button"
                  className={`inline-flex items-center p-4 border-b-2 rounded-t-lg ${
                    activeTab === 'templates'
                      ? 'text-blue-600 border-blue-600'
                      : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                  }`}
                  onClick={() => setActiveTab('templates')}
                >
                  <HiOutlineDocumentText className="w-4 h-4 mr-2" />
                  Payslip Templates
                </button>
              </li>
            </ul>
          </div>
        </div>

        {activeTab === 'general' && (
          <Card>
            <h3 className="text-lg font-medium mb-4">Pay Schedule</h3>

            <div className="mb-4">
              <div className="mb-2 block">
                <Label htmlFor="paySchedule" value="Pay Schedule" />
              </div>
              <Select
                id="paySchedule"
                value={paySchedule}
                onChange={(e) => setPaySchedule(e.target.value as PaySchedule)}
                required
              >
                <option value={PaySchedule.MONTHLY}>Monthly</option>
                <option value={PaySchedule.SEMI_MONTHLY}>Semi-Monthly</option>
                <option value={PaySchedule.WEEKLY}>Weekly</option>
              </Select>
              <p className="mt-1 text-sm text-gray-500">
                Select how often employees are paid.
              </p>
            </div>

            {paySchedule === PaySchedule.SEMI_MONTHLY && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="firstPayDay" value="First Pay Day" />
                  </div>
                  <TextInput
                    id="firstPayDay"
                    type="number"
                    min={1}
                    max={31}
                    value={semiMonthlyDays[0]}
                    onChange={(e) => setSemiMonthlyDays([parseInt(e.target.value), semiMonthlyDays[1]])}
                    required
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Day of the month for the first pay period.
                  </p>
                </div>
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="secondPayDay" value="Second Pay Day" />
                  </div>
                  <TextInput
                    id="secondPayDay"
                    type="number"
                    min={1}
                    max={31}
                    value={semiMonthlyDays[1]}
                    onChange={(e) => setSemiMonthlyDays([semiMonthlyDays[0], parseInt(e.target.value)])}
                    required
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Day of the month for the second pay period.
                  </p>
                </div>
              </div>
            )}

            <h3 className="text-lg font-medium mb-4 mt-6">Payroll Calculation</h3>

            <div className="mb-4">
              <div className="flex items-center gap-2">
                <ToggleSwitch
                  checked={payBasedOnTimeEntries}
                  onChange={setPayBasedOnTimeEntries}
                  label="Calculate pay based on actual time entries"
                />
              </div>
              <p className="mt-1 text-sm text-gray-500">
                When enabled, payroll is calculated based on actual time entries. When disabled, uses scheduled period length.
                <br />
                <strong>Recommended:</strong> Enable this for accurate payroll based on actual work days.
              </p>
            </div>

            <h3 className="text-lg font-medium mb-4 mt-6">13th Month Pay</h3>

            <div className="mb-4">
              <div className="flex items-center gap-2">
                <ToggleSwitch
                  checked={autoCalculate13thMonth}
                  onChange={setAutoCalculate13thMonth}
                  label="Automatically calculate 13th month pay"
                />
              </div>
              <p className="mt-1 text-sm text-gray-500">
                When enabled, the system will automatically calculate 13th month pay based on basic salary.
              </p>
            </div>

            <div className="mb-4">
              <div className="flex items-center gap-2">
                <ToggleSwitch
                  checked={includeAllowancesIn13thMonth}
                  onChange={setIncludeAllowancesIn13thMonth}
                  label="Include allowances in 13th month pay calculation"
                />
              </div>
              <p className="mt-1 text-sm text-gray-500">
                When enabled, allowances will be included in the 13th month pay calculation.
              </p>
            </div>
          </Card>
        )}

        {activeTab === 'contributions' && (
          <Card>
            <h3 className="text-lg font-medium mb-4">Employer Contribution Rates</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="sssEmployerRate" value="SSS Employer Rate (%)" />
                </div>
                <TextInput
                  id="sssEmployerRate"
                  type="number"
                  step="0.01"
                  min={0}
                  max={100}
                  value={sssEmployerRate}
                  onChange={(e) => setSssEmployerRate(parseFloat(e.target.value))}
                  required
                />
                <p className="mt-1 text-sm text-gray-500">
                  Default: 8.5%
                </p>
              </div>
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="philhealthEmployerRate" value="PhilHealth Employer Rate (%)" />
                </div>
                <TextInput
                  id="philhealthEmployerRate"
                  type="number"
                  step="0.01"
                  min={0}
                  max={100}
                  value={philhealthEmployerRate}
                  onChange={(e) => setPhilhealthEmployerRate(parseFloat(e.target.value))}
                  required
                />
                <p className="mt-1 text-sm text-gray-500">
                  Default: 2.0%
                </p>
              </div>
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="pagibigEmployerRate" value="Pag-IBIG Employer Rate (%)" />
                </div>
                <TextInput
                  id="pagibigEmployerRate"
                  type="number"
                  step="0.01"
                  min={0}
                  max={100}
                  value={pagibigEmployerRate}
                  onChange={(e) => setPagibigEmployerRate(parseFloat(e.target.value))}
                  required
                />
                <p className="mt-1 text-sm text-gray-500">
                  Default: 2.0%
                </p>
              </div>
            </div>

            <h3 className="text-lg font-medium mb-4 mt-6">Tax Settings</h3>

            <div className="mb-4">
              <div className="mb-2 block">
                <Label htmlFor="taxTableVersion" value="Tax Table Version" />
              </div>
              <Select
                id="taxTableVersion"
                value={taxTableVersion}
                onChange={(e) => setTaxTableVersion(e.target.value)}
                required
              >
                <option value="2023">2023 (Current)</option>
                <option value="2022">2022</option>
                <option value="2021">2021</option>
              </Select>
              <p className="mt-1 text-sm text-gray-500">
                Select which tax table version to use for withholding tax calculations.
              </p>
            </div>
          </Card>
        )}

        {activeTab === 'templates' && (
          <div className="space-y-6">
            {/* Company Branding */}
            <Card>
              <h3 className="text-lg font-medium mb-4">Company Branding</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="companyLogo" value="Company Logo" />
                  </div>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <HiOutlineCloudUpload className="mx-auto h-12 w-12 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-500">
                      Click to upload logo or drag and drop
                    </p>
                    <p className="text-xs text-gray-400">
                      PNG, JPG up to 2MB (Recommended: 200x80px)
                    </p>
                  </div>
                </div>

                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="brandColor" value="Brand Color" />
                  </div>
                  <div className="flex gap-2">
                    <TextInput
                      id="brandColor"
                      type="color"
                      value="#3B82F6"
                      className="w-16"
                    />
                    <TextInput
                      value="#3B82F6"
                      placeholder="Hex color code"
                      className="flex-1"
                    />
                  </div>
                  <p className="mt-1 text-sm text-gray-500">
                    Primary color for headers and accents
                  </p>
                </div>
              </div>
            </Card>

            {/* Template Layout */}
            <Card>
              <h3 className="text-lg font-medium mb-4">Template Layout</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="templateStyle" value="Template Style" />
                  </div>
                  <Select id="templateStyle" value="modern">
                    <option value="modern">Modern (Recommended)</option>
                    <option value="classic">Classic</option>
                    <option value="minimal">Minimal</option>
                    <option value="detailed">Detailed</option>
                  </Select>
                </div>

                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="paperSize" value="Paper Size" />
                  </div>
                  <Select id="paperSize" value="a4">
                    <option value="a4">A4 (210 × 297 mm)</option>
                    <option value="letter">Letter (8.5 × 11 in)</option>
                    <option value="legal">Legal (8.5 × 14 in)</option>
                  </Select>
                </div>
              </div>

              <div className="mb-4">
                <div className="flex items-center gap-2">
                  <ToggleSwitch
                    checked={true}
                    label="Show company address on payslip"
                  />
                </div>
              </div>

              <div className="mb-4">
                <div className="flex items-center gap-2">
                  <ToggleSwitch
                    checked={true}
                    label="Include QR code for verification"
                  />
                </div>
              </div>
            </Card>

            {/* Field Configuration */}
            <Card>
              <h3 className="text-lg font-medium mb-4">Field Configuration</h3>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Employee Information</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {[
                      'Employee Name',
                      'Employee ID',
                      'Position',
                      'Department',
                      'Hire Date',
                      'Employee Photo'
                    ].map((field) => (
                      <div key={field} className="flex items-center gap-2">
                        <ToggleSwitch
                          checked={!['Employee Photo'].includes(field)}
                          size="sm"
                        />
                        <span className="text-sm">{field}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Earnings</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {[
                      'Basic Pay',
                      'Overtime Pay',
                      'Holiday Pay',
                      'Night Differential',
                      'Allowances',
                      'Bonuses'
                    ].map((field) => (
                      <div key={field} className="flex items-center gap-2">
                        <ToggleSwitch
                          checked={true}
                          size="sm"
                        />
                        <span className="text-sm">{field}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Deductions</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {[
                      'SSS Contribution',
                      'PhilHealth',
                      'Pag-IBIG',
                      'Withholding Tax',
                      'Loans',
                      'Other Deductions'
                    ].map((field) => (
                      <div key={field} className="flex items-center gap-2">
                        <ToggleSwitch
                          checked={true}
                          size="sm"
                        />
                        <span className="text-sm">{field}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </Card>

            {/* Language & Localization */}
            <Card>
              <h3 className="text-lg font-medium mb-4">Language & Localization</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="primaryLanguage" value="Primary Language" />
                  </div>
                  <Select id="primaryLanguage" value="en">
                    <option value="en">English</option>
                    <option value="fil">Filipino</option>
                    <option value="ceb">Cebuano</option>
                  </Select>
                </div>

                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="dateFormat" value="Date Format" />
                  </div>
                  <Select id="dateFormat" value="mm/dd/yyyy">
                    <option value="mm/dd/yyyy">MM/DD/YYYY (US)</option>
                    <option value="dd/mm/yyyy">DD/MM/YYYY (PH)</option>
                    <option value="yyyy-mm-dd">YYYY-MM-DD (ISO)</option>
                  </Select>
                </div>
              </div>

              <div className="mt-4">
                <div className="flex items-center gap-2">
                  <ToggleSwitch
                    checked={false}
                    label="Enable bilingual payslips (English + Filipino)"
                  />
                </div>
              </div>
            </Card>

            {/* Preview & Actions */}
            <Card>
              <h3 className="text-lg font-medium mb-4">Preview & Actions</h3>

              <div className="flex flex-wrap gap-3">
                <Button
                  color="gray"
                  size="sm"
                  onClick={() => setShowTemplatePreview(true)}
                >
                  <HiOutlineEye className="mr-2 h-4 w-4" />
                  Preview Template
                </Button>
                <Button color="blue" size="sm">
                  <HiOutlineDownload className="mr-2 h-4 w-4" />
                  Download Sample
                </Button>
                <Button color="green" size="sm">
                  <HiOutlineDocumentDuplicate className="mr-2 h-4 w-4" />
                  Duplicate Template
                </Button>
                <Button color="purple" size="sm">
                  <HiOutlineCog className="mr-2 h-4 w-4" />
                  Advanced Settings
                </Button>
              </div>

              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <div className="flex items-start gap-3">
                  <HiOutlineInformationCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900">Template Tips</h4>
                    <ul className="mt-1 text-sm text-blue-700 space-y-1">
                      <li>• Use your company logo for professional appearance</li>
                      <li>• Include all legally required information for compliance</li>
                      <li>• Test print templates before mass distribution</li>
                      <li>• Consider employee feedback on readability</li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        <div className="mt-6">
          {submitError && (
            <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
              <span className="font-medium">Error!</span> {submitError}
            </Alert>
          )}

          {submitSuccess && (
            <Alert color="success" icon={HiOutlineSave} className="mb-4">
              <span className="font-medium">Success!</span> Payroll settings have been saved.
            </Alert>
          )}

          <div className="flex justify-end">
            <Button
              type="submit"
              color="primary"
              isProcessing={isSubmitting}
              disabled={isSubmitting}
            >
              <HiOutlineSave className="mr-2 h-5 w-5" />
              Save Settings
            </Button>
          </div>
        </div>
      </form>

      {/* Payslip Template Preview Modal */}
      <PayslipTemplatePreview
        isOpen={showTemplatePreview}
        onClose={() => setShowTemplatePreview(false)}
      />
    </div>
  );
};

export default PayrollSettings;
