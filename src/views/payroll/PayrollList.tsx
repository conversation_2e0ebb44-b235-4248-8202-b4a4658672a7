import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Table,
  Spinner,
  Badge,
  Modal,
  Dropdown
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlineCalendar,
  HiOutlineCurrencyDollar,
  HiOutlineDocumentText,
  HiOutlineCheck,
  HiOutlineX,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineEye,
  HiOutlineGift
} from 'react-icons/hi';
import { format } from 'date-fns';
import { useOrganization } from '../../context/OrganizationContext';
import { getPayrollPeriods, createPayrollPeriod, deletePayrollPeriod } from '../../services/payroll';
import { PayrollPeriod, PayrollPeriodStatus } from '../../types/payroll';
import { hasPayrollPayablesBeenCreated } from '../../services/payrollPayables';
import SendPayrollToPayableButton from '../../components/payroll/SendPayrollToPayableButton';
import CreatePayrollPeriodForm from '../../components/payroll/CreatePayrollPeriodForm';
import ThirteenthMonthPayModal from '../../components/payroll/ThirteenthMonthPayModal';
import PageTitle from '../../components/shared/PageTitle';
import EmptyState from '../../components/shared/EmptyState';
import Pagination from '../../components/common/Pagination';

const PayrollList: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const navigate = useNavigate();
  const [payrollPeriods, setPayrollPeriods] = useState<PayrollPeriod[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [payablesStatus, setPayablesStatus] = useState<Record<string, boolean>>({});
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [show13thMonthModal, setShow13thMonthModal] = useState<boolean>(false);
  const [selectedPeriodId, setSelectedPeriodId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Fetch payroll periods
  const fetchPayrollPeriods = async () => {
    if (!currentOrganization) return;

    setIsLoading(true);
    setError(null);

    try {
      const { periods, count, error } = await getPayrollPeriods(currentOrganization.id, {
        limit: itemsPerPage,
        offset: (currentPage - 1) * itemsPerPage,
      });

      if (error) {
        setError(error);
      } else {
        setPayrollPeriods(periods);
        setTotalCount(count);

        // Check payables status for approved periods
        const statusChecks: Record<string, boolean> = {};
        for (const period of periods) {
          if (period.status === PayrollPeriodStatus.APPROVED) {
            const hasPayables = await hasPayrollPayablesBeenCreated(currentOrganization.id, period.id);
            statusChecks[period.id] = hasPayables;
          }
        }
        setPayablesStatus(statusChecks);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Load payroll periods when the component mounts or when the page changes
  useEffect(() => {
    fetchPayrollPeriods();
  }, [currentOrganization, currentPage, itemsPerPage]);

  // Handle creating a new payroll period
  const handleCreatePayrollPeriod = async (periodData: Omit<PayrollPeriod, 'id' | 'organization_id' | 'created_at' | 'updated_at'>) => {
    if (!currentOrganization) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { period, error } = await createPayrollPeriod(currentOrganization.id, periodData);

      if (error) {
        setSubmitError(error);
      } else {
        setShowCreateModal(false);
        fetchPayrollPeriods();
      }
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting a payroll period
  const handleDeletePayrollPeriod = async () => {
    if (!currentOrganization || !selectedPeriodId) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { success, error } = await deletePayrollPeriod(currentOrganization.id, selectedPeriodId);

      if (error) {
        setSubmitError(error);
      } else if (success) {
        setShowDeleteModal(false);
        fetchPayrollPeriods();
      }
    } catch (err: any) {
      setSubmitError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render status badge
  const renderStatusBadge = (status: PayrollPeriodStatus) => {
    switch (status) {
      case PayrollPeriodStatus.DRAFT:
        return <Badge color="gray">Draft</Badge>;
      case PayrollPeriodStatus.PROCESSING:
        return <Badge color="blue">Processing</Badge>;
      case PayrollPeriodStatus.APPROVED:
        return <Badge color="green">Approved</Badge>;
      case PayrollPeriodStatus.PAID:
        return <Badge color="purple">Paid</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  // If loading, show a spinner
  if (isLoading && payrollPeriods.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <PageTitle title="Payroll Periods" />
        <div className="flex space-x-2">
          <Dropdown
            label="Actions"
            color="light"
            dismissOnClick={true}
          >
            <Dropdown.Item
              icon={HiOutlineGift}
              onClick={() => setShow13thMonthModal(true)}
            >
              Process 13th Month Pay
            </Dropdown.Item>
          </Dropdown>
          <Button color="primary" onClick={() => setShowCreateModal(true)}>
            <HiOutlinePlus className="mr-2 h-5 w-5" />
            Create Payroll Period
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {payrollPeriods.length === 0 ? (
        <EmptyState
          title="No payroll periods found"
          description="Create your first payroll period to start processing payroll."
          icon={<HiOutlineCalendar className="h-12 w-12 text-gray-400" />}
          actionLabel="Create Payroll Period"
          onAction={() => setShowCreateModal(true)}
        />
      ) : (
        <Card>
          <div className="overflow-x-auto">
            <Table striped>
              <Table.Head>
                <Table.HeadCell>Period Name</Table.HeadCell>
                <Table.HeadCell>Start Date</Table.HeadCell>
                <Table.HeadCell>End Date</Table.HeadCell>
                <Table.HeadCell>Payment Date</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>13th Month</Table.HeadCell>
                <Table.HeadCell>Actions</Table.HeadCell>
              </Table.Head>
              <Table.Body>
                {payrollPeriods.map((period) => (
                  <Table.Row key={period.id}>
                    <Table.Cell className="font-medium">
                      {period.name}
                    </Table.Cell>
                    <Table.Cell>
                      {format(new Date(period.start_date), 'MMM d, yyyy')}
                    </Table.Cell>
                    <Table.Cell>
                      {format(new Date(period.end_date), 'MMM d, yyyy')}
                    </Table.Cell>
                    <Table.Cell>
                      {format(new Date(period.payment_date), 'MMM d, yyyy')}
                    </Table.Cell>
                    <Table.Cell>
                      {renderStatusBadge(period.status as PayrollPeriodStatus)}
                    </Table.Cell>
                    <Table.Cell>
                      {period.is_thirteenth_month ? (
                        <Badge color="success" icon={HiOutlineCheck}>Yes</Badge>
                      ) : (
                        <Badge color="gray" icon={HiOutlineX}>No</Badge>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex space-x-2">
                        <Link to={`/payroll/periods/${period.id}`}>
                          <Button size="xs" color="info">
                            <HiOutlineEye className="mr-1 h-4 w-4" />
                            View
                          </Button>
                        </Link>
                        {period.status === PayrollPeriodStatus.DRAFT && (
                          <>
                            <Link to={`/payroll/periods/${period.id}/edit`}>
                              <Button size="xs" color="warning">
                                <HiOutlinePencil className="mr-1 h-4 w-4" />
                                Edit
                              </Button>
                            </Link>
                            <Button
                              size="xs"
                              color="failure"
                              onClick={() => {
                                setSelectedPeriodId(period.id);
                                setShowDeleteModal(true);
                              }}
                            >
                              <HiOutlineTrash className="mr-1 h-4 w-4" />
                              Delete
                            </Button>
                          </>
                        )}
                        {period.status === PayrollPeriodStatus.DRAFT && (
                          <Link to={`/payroll/periods/${period.id}/process`}>
                            <Button size="xs" color="success">
                              <HiOutlineCurrencyDollar className="mr-1 h-4 w-4" />
                              Process
                            </Button>
                          </Link>
                        )}
                        {period.status !== PayrollPeriodStatus.DRAFT && (
                          <Link to={`/payroll/periods/${period.id}/payslips`}>
                            <Button size="xs" color="light">
                              <HiOutlineDocumentText className="mr-1 h-4 w-4" />
                              Payslips
                            </Button>
                          </Link>
                        )}
                        {period.status === PayrollPeriodStatus.APPROVED && (
                          <SendPayrollToPayableButton
                            payrollPeriodId={period.id}
                            payrollPeriodName={period.name}
                            payrollStatus={period.status as PayrollPeriodStatus}
                            totalNetPay={0} // We don't have totals in list view
                            totalEmployees={0} // We don't have totals in list view
                            payablesCreated={payablesStatus[period.id] || false}
                            onSuccess={() => {
                              setPayablesStatus(prev => ({ ...prev, [period.id]: true }));
                              fetchPayrollPeriods();
                            }}
                            className="text-xs"
                          />
                        )}
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil(totalCount / itemsPerPage)}
            itemsPerPage={itemsPerPage}
            totalItems={totalCount}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="payroll periods"
          />
        </Card>
      )}

      {/* Create Payroll Period Modal */}
      <Modal
        show={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        size="lg"
      >
        <Modal.Header>Create Payroll Period</Modal.Header>
        <Modal.Body>
          <CreatePayrollPeriodForm
            onSubmit={handleCreatePayrollPeriod}
            isSubmitting={isSubmitting}
            error={submitError}
            onCancel={() => setShowCreateModal(false)}
          />
        </Modal.Body>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        show={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        size="md"
      >
        <Modal.Header>Delete Payroll Period</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>Are you sure you want to delete this payroll period? This action cannot be undone.</p>
            {submitError && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <p>{submitError}</p>
              </div>
            )}
            <div className="flex justify-end space-x-2">
              <Button
                color="gray"
                onClick={() => setShowDeleteModal(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                color="failure"
                onClick={handleDeletePayrollPeriod}
                isProcessing={isSubmitting}
              >
                Delete
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>

      {/* 13th Month Pay Modal */}
      <ThirteenthMonthPayModal
        show={show13thMonthModal}
        onClose={() => setShow13thMonthModal(false)}
        onSuccess={(periodId) => {
          setShow13thMonthModal(false);
          navigate(`/payroll/periods/${periodId}`);
          fetchPayrollPeriods();
        }}
      />
    </div>
  );
};

export default PayrollList;
