import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Spinner,
  <PERSON><PERSON>,
  Ta<PERSON>
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineExclamationCircle,
  HiOutlineUser,
  HiOutlineCurrencyDollar,
  HiOutlineDocumentText,
  HiOutlinePencil
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getPayrollItemForPayslip } from '../../services/payslipGeneration';
import { PayrollItemWithDetails, DeductionType, PayrollPeriodStatus } from '../../types/payroll';
import PageTitle from '../../components/shared/PageTitle';
import { formatCurrency } from '../../utils/formatters';
import { format } from 'date-fns';

const PayrollItemDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const [payrollItem, setPayrollItem] = useState<PayrollItemWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Fetch payroll item details
  const fetchPayrollItem = async () => {
    if (!currentOrganization || !id) return;

    setIsLoading(true);
    setError(null);

    try {
      const { item, error } = await getPayrollItemForPayslip(currentOrganization.id, id);

      if (error) {
        setError(error);
      } else if (item) {
        setPayrollItem(item);
      } else {
        setError('Payroll item not found');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Load payroll item when the component mounts
  useEffect(() => {
    fetchPayrollItem();
  }, [currentOrganization, id]);

  // Group deductions by type
  const groupDeductionsByType = () => {
    if (!payrollItem?.deductions) return {};

    return payrollItem.deductions.reduce((acc, deduction) => {
      const type = deduction.type as DeductionType;
      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(deduction);
      return acc;
    }, {} as Record<string, any[]>);
  };

  const deductionsByType = groupDeductionsByType();

  // If loading, show a spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamationCircle}>
          <span className="font-medium">Error!</span> {error}
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate(-1)}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back
          </Button>
        </div>
      </div>
    );
  }

  // If payroll item not found, show message
  if (!payrollItem) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamationCircle}>
          <span className="font-medium">Error!</span> Payroll item not found
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate(-1)}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button
            color="gray"
            className="mr-4"
            onClick={() => navigate(`/payroll/periods/${payrollItem.payroll_period.id}`)}
          >
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back
          </Button>
          <PageTitle
            title={`Payslip: ${payrollItem.employee?.first_name} ${payrollItem.employee?.last_name}`}
            subtitle={`Payroll Period: ${payrollItem.payroll_period?.name}`}
          />
        </div>
        <div className="flex space-x-2">
          {(payrollItem.payroll_period?.status === PayrollPeriodStatus.DRAFT ||
            payrollItem.payroll_period?.status === PayrollPeriodStatus.PROCESSING) && (
            <Button
              color="warning"
              onClick={() => navigate(`/payroll/items/${payrollItem.id}/edit`)}
            >
              <HiOutlinePencil className="mr-2 h-5 w-5" />
              Edit Contributions
            </Button>
          )}
          <Button
            color="primary"
            onClick={() => navigate(`/payroll/periods/${payrollItem.payroll_period.id}/payslips`)}
          >
            <HiOutlineDocumentText className="mr-2 h-5 w-5" />
            View Payslip
          </Button>
        </div>
      </div>

      <div className="mb-6">
        <div className="border-b border-gray-200">
          <ul className="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500">
            <li className="mr-2">
              <button
                className={`inline-flex items-center p-4 border-b-2 rounded-t-lg ${
                  activeTab === 'overview'
                    ? 'text-blue-600 border-blue-600'
                    : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('overview')}
              >
                <HiOutlineCurrencyDollar className="w-4 h-4 mr-2" />
                Overview
              </button>
            </li>
            <li className="mr-2">
              <button
                className={`inline-flex items-center p-4 border-b-2 rounded-t-lg ${
                  activeTab === 'details'
                    ? 'text-blue-600 border-blue-600'
                    : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('details')}
              >
                <HiOutlineDocumentText className="w-4 h-4 mr-2" />
                Calculation Details
              </button>
            </li>
          </ul>
        </div>
      </div>

      {activeTab === 'overview' && (
        <>
          <Card>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-4">Employee Information</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500">Name</p>
                    <p className="font-medium">
                      {payrollItem.employee?.first_name} {payrollItem.employee?.last_name}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Employee ID</p>
                    <p>{payrollItem.employee?.employee_number || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Position</p>
                    <p>{payrollItem.employee?.position?.title || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Department</p>
                    <p>{payrollItem.employee?.department?.name || 'N/A'}</p>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-4">Payroll Information</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500">Period</p>
                    <p>{payrollItem.payroll_period?.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Pay Date</p>
                    <p>{format(new Date(payrollItem.payroll_period?.payment_date), 'MMMM d, yyyy')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <p>{payrollItem.status}</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <Card>
              <h3 className="text-lg font-medium mb-4">Earnings</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <p className="text-sm text-gray-500">Basic Pay</p>
                  <p className="font-medium">{formatCurrency(Number(payrollItem.basic_pay))}</p>
                </div>

                {payrollItem.allowances && payrollItem.allowances.length > 0 && (
                  <>
                    <div className="border-t border-gray-200 pt-2 mt-2">
                      <p className="text-sm font-medium">Allowances</p>
                    </div>
                    {payrollItem.allowances.map((allowance, index) => (
                      <div key={index} className="flex justify-between">
                        <p className="text-sm text-gray-500">{allowance.name}</p>
                        <p>{formatCurrency(Number(allowance.amount))}</p>
                      </div>
                    ))}
                  </>
                )}

                <div className="border-t border-gray-200 pt-2 mt-2">
                  <div className="flex justify-between font-medium">
                    <p>Total Gross Pay</p>
                    <p>{formatCurrency(Number(payrollItem.gross_pay))}</p>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <h3 className="text-lg font-medium mb-4">Deductions</h3>
              <div className="space-y-3">
                {deductionsByType['government'] && (
                  <>
                    <div className="border-b border-gray-200 pb-1">
                      <p className="text-sm font-medium">Government Contributions</p>
                    </div>
                    {deductionsByType['government'].map((deduction, index) => (
                      <div key={index} className="flex justify-between">
                        <p className="text-sm text-gray-500">{deduction.name}</p>
                        <p>{formatCurrency(Number(deduction.amount))}</p>
                      </div>
                    ))}
                  </>
                )}

                {deductionsByType['tax'] && (
                  <>
                    <div className="border-b border-gray-200 pb-1 mt-3">
                      <p className="text-sm font-medium">Taxes</p>
                    </div>
                    {deductionsByType['tax'].map((deduction, index) => (
                      <div key={index} className="flex justify-between">
                        <p className="text-sm text-gray-500">{deduction.name}</p>
                        <p>{formatCurrency(Number(deduction.amount))}</p>
                      </div>
                    ))}
                  </>
                )}

                {deductionsByType['other'] && (
                  <>
                    <div className="border-b border-gray-200 pb-1 mt-3">
                      <p className="text-sm font-medium">Other Deductions</p>
                    </div>
                    {deductionsByType['other'].map((deduction, index) => (
                      <div key={index} className="flex justify-between">
                        <p className="text-sm text-gray-500">{deduction.name}</p>
                        <p>{formatCurrency(Number(deduction.amount))}</p>
                      </div>
                    ))}
                  </>
                )}

                <div className="border-t border-gray-200 pt-2 mt-2">
                  <div className="flex justify-between font-medium">
                    <p>Total Deductions</p>
                    <p>{formatCurrency(Number(payrollItem.total_deductions))}</p>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          <Card className="mt-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-medium">Net Pay</h3>
                <p className="text-sm text-gray-500">Total amount to be paid</p>
              </div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(Number(payrollItem.net_pay))}
              </div>
            </div>
          </Card>
        </>
      )}

      {activeTab === 'details' && (
        <div className="space-y-6">
          <Card>
            <h3 className="text-lg font-medium mb-4">Calculation Details</h3>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium">Taxable Income</h4>
                <p className="text-sm text-gray-500">
                  Gross pay minus tax-exempt deductions
                </p>
                <p className="font-medium mt-1">{formatCurrency(Number(payrollItem.taxable_income))}</p>
              </div>

              <div>
                <h4 className="font-medium">Government Contributions</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                  <div className="border rounded p-3">
                    <p className="text-sm font-medium">SSS</p>
                    <p className="text-sm text-gray-500">Employee Share</p>
                    <p className="font-medium">{formatCurrency(Number(payrollItem.sss_contribution))}</p>
                  </div>
                  <div className="border rounded p-3">
                    <p className="text-sm font-medium">PhilHealth</p>
                    <p className="text-sm text-gray-500">Employee Share</p>
                    <p className="font-medium">{formatCurrency(Number(payrollItem.philhealth_contribution))}</p>
                  </div>
                  <div className="border rounded p-3">
                    <p className="text-sm font-medium">Pag-IBIG</p>
                    <p className="text-sm text-gray-500">Employee Share</p>
                    <p className="font-medium">{formatCurrency(Number(payrollItem.pagibig_contribution))}</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium">Withholding Tax</h4>
                <p className="text-sm text-gray-500">
                  Based on taxable income after deductions
                </p>
                <p className="font-medium mt-1">{formatCurrency(Number(payrollItem.withholding_tax))}</p>
              </div>
            </div>
          </Card>

          <Card>
            <h3 className="text-lg font-medium mb-4">Government Contribution Formulas</h3>

            {/* SSS Contribution */}
            <div className="mb-6">
              <h4 className="font-medium text-blue-600">SSS Contribution</h4>
              <div className="bg-gray-50 p-3 rounded-md my-2">
                <div className="grid grid-cols-2 gap-2 mb-2">
                  <div>
                    <span className="text-sm text-gray-500">Monthly Salary:</span>
                    <span className="ml-2 font-mono">
                      {formatCurrency(Number(payrollItem.basic_pay) * (payrollItem.payroll_period.name.includes('Semi-Monthly') ? 2 : 1))}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Employee Share:</span>
                    <span className="ml-2 font-mono">
                      {formatCurrency(Number(payrollItem.sss_contribution))}
                    </span>
                  </div>
                </div>
                <div className="text-sm text-gray-600 mt-2">
                  <p className="font-medium">Formula:</p>
                  <p>Based on SSS Contribution Table (2023)</p>
                  <ul className="list-disc list-inside mt-1 ml-2 space-y-1">
                    <li>Employee pays a fixed amount based on salary bracket</li>
                    <li>Brackets range from ₱135 (for salary up to ₱3,249.99) to ₱1,125 (for salary ₱24,750 and above)</li>
                    <li>Employer pays 1.89 times the employee contribution</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* PhilHealth Contribution */}
            <div className="mb-6">
              <h4 className="font-medium text-blue-600">PhilHealth Contribution</h4>
              <div className="bg-gray-50 p-3 rounded-md my-2">
                <div className="grid grid-cols-2 gap-2 mb-2">
                  <div>
                    <span className="text-sm text-gray-500">Monthly Salary:</span>
                    <span className="ml-2 font-mono">
                      {formatCurrency(Number(payrollItem.basic_pay) * (payrollItem.payroll_period.name.includes('Semi-Monthly') ? 2 : 1))}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Employee Share:</span>
                    <span className="ml-2 font-mono">
                      {formatCurrency(Number(payrollItem.philhealth_contribution))}
                    </span>
                  </div>
                </div>
                <div className="text-sm text-gray-600 mt-2">
                  <p className="font-medium">Formula: 2% of monthly salary (employee share)</p>
                  <p className="font-mono mt-1">
                    {formatCurrency(Number(payrollItem.basic_pay) * (payrollItem.payroll_period.name.includes('Semi-Monthly') ? 2 : 1))} × 2% =
                    {formatCurrency(Number(payrollItem.basic_pay) * (payrollItem.payroll_period.name.includes('Semi-Monthly') ? 2 : 1) * 0.02)}
                  </p>
                  <p className="font-medium mt-2">Notes:</p>
                  <ul className="list-disc list-inside mt-1 ml-2 space-y-1">
                    <li>Total PhilHealth contribution is 4% of monthly salary</li>
                    <li>Split equally between employee (2%) and employer (2%)</li>
                    <li>Minimum monthly salary for calculation: ₱10,000</li>
                    <li>Maximum monthly salary for calculation: ₱80,000</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Pag-IBIG Contribution */}
            <div className="mb-6">
              <h4 className="font-medium text-blue-600">Pag-IBIG Contribution</h4>
              <div className="bg-gray-50 p-3 rounded-md my-2">
                <div className="grid grid-cols-2 gap-2 mb-2">
                  <div>
                    <span className="text-sm text-gray-500">Monthly Salary:</span>
                    <span className="ml-2 font-mono">
                      {formatCurrency(Number(payrollItem.basic_pay) * (payrollItem.payroll_period.name.includes('Semi-Monthly') ? 2 : 1))}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Employee Share:</span>
                    <span className="ml-2 font-mono">
                      {formatCurrency(Number(payrollItem.pagibig_contribution))}
                    </span>
                  </div>
                </div>
                <div className="text-sm text-gray-600 mt-2">
                  <p className="font-medium">Formula:</p>
                  <ul className="list-disc list-inside mt-1 ml-2 space-y-1">
                    <li>For monthly salary up to ₱1,500: 1% employee contribution</li>
                    <li>For monthly salary over ₱1,500: 2% employee contribution</li>
                  </ul>
                  <p className="font-mono mt-2">
                    {Number(payrollItem.basic_pay) * (payrollItem.payroll_period.name.includes('Semi-Monthly') ? 2 : 1) <= 1500
                      ? `${formatCurrency(Number(payrollItem.basic_pay) * (payrollItem.payroll_period.name.includes('Semi-Monthly') ? 2 : 1))} × 1% = ${formatCurrency(Number(payrollItem.basic_pay) * (payrollItem.payroll_period.name.includes('Semi-Monthly') ? 2 : 1) * 0.01)}`
                      : `${formatCurrency(Number(payrollItem.basic_pay) * (payrollItem.payroll_period.name.includes('Semi-Monthly') ? 2 : 1))} × 2% = ${formatCurrency(Number(payrollItem.basic_pay) * (payrollItem.payroll_period.name.includes('Semi-Monthly') ? 2 : 1) * 0.02)}`
                    }
                  </p>
                  <p className="font-medium mt-2">Notes:</p>
                  <ul className="list-disc list-inside mt-1 ml-2 space-y-1">
                    <li>Employer always contributes 2% of monthly salary</li>
                    <li>Maximum monthly contribution: ₱100 for employee, ₱100 for employer</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Withholding Tax */}
            {Number(payrollItem.withholding_tax) > 0 && (
              <div>
                <h4 className="font-medium text-blue-600">Withholding Tax</h4>
                <div className="bg-gray-50 p-3 rounded-md my-2">
                  <div className="grid grid-cols-2 gap-2 mb-2">
                    <div>
                      <span className="text-sm text-gray-500">Taxable Income:</span>
                      <span className="ml-2 font-mono">
                        {formatCurrency(Number(payrollItem.taxable_income))}
                      </span>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">Withholding Tax:</span>
                      <span className="ml-2 font-mono">
                        {formatCurrency(Number(payrollItem.withholding_tax))}
                      </span>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600 mt-2">
                    <p className="font-medium">Formula: Based on BIR Withholding Tax Table (2023)</p>
                    <p className="font-medium mt-2">Tax Brackets:</p>
                    <ul className="list-disc list-inside mt-1 ml-2 space-y-1">
                      <li>₱0 to ₱20,833: 0%</li>
                      <li>₱20,834 to ₱33,332: 15% of excess over ₱20,833</li>
                      <li>₱33,333 to ₱66,666: ₱1,875 + 20% of excess over ₱33,333</li>
                      <li>₱66,667 to ₱166,666: ₱8,541.80 + 25% of excess over ₱66,667</li>
                      <li>₱166,667 to ₱666,666: ₱33,541.80 + 30% of excess over ₱166,667</li>
                      <li>Over ₱666,667: ₱183,541.80 + 35% of excess over ₱666,667</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </Card>
        </div>
      )}
    </div>
  );
};

export default PayrollItemDetails;
