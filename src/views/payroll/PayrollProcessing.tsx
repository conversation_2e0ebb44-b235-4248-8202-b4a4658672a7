import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON>ton,
  Card,
  Table,
  Spinner,
  Alert,
  Checkbox,
  Progress,
  Modal,
  Badge
} from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlineExclamationCircle,
  HiOutlineRefresh,
  HiOutlineCheck,
  HiOutlineX,
  HiOutlineCalculator
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getPayrollPeriodById, updatePayrollPeriod } from '../../services/payroll';
import { getEmployees } from '../../services/employee';
import { processPayroll } from '../../services/payrollProcessing';
import { PayrollPeriodWithDetails, PayrollPeriodStatus } from '../../types/payroll';
import { Employee } from '../../services/employee';
import PageTitle from '../../components/shared/PageTitle';
import { formatCurrency } from '../../utils/formatters';

const PayrollProcessing: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const [payrollPeriod, setPayrollPeriod] = useState<PayrollPeriodWithDetails | null>(null);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [processError, setProcessError] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);

  // Fetch payroll period details
  const fetchPayrollPeriod = async () => {
    if (!currentOrganization || !id) return;

    setIsLoading(true);
    setError(null);

    try {
      const { period, error } = await getPayrollPeriodById(currentOrganization.id, id, true);

      if (error) {
        setError(error);
      } else if (period) {
        setPayrollPeriod(period);

        // If the period is not in draft status, redirect to details page
        if (period.status !== PayrollPeriodStatus.DRAFT) {
          navigate(`/payroll/periods/${id}`);
        }
      } else {
        setError('Payroll period not found');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch employees
  const fetchEmployees = async () => {
    if (!currentOrganization) return;

    try {
      const { employees, error } = await getEmployees(currentOrganization.id);

      if (error) {
        setError(error);
      } else {
        setEmployees(employees);

        // Select all employees by default
        setSelectedEmployees(employees.map(emp => emp.id));
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Load data when the component mounts
  useEffect(() => {
    fetchPayrollPeriod();
    fetchEmployees();
  }, [currentOrganization, id]);

  // Handle select all employees
  const handleSelectAll = () => {
    if (selectedEmployees.length === employees.length) {
      setSelectedEmployees([]);
    } else {
      setSelectedEmployees(employees.map(emp => emp.id));
    }
  };

  // Handle individual employee selection
  const handleSelectEmployee = (employeeId: string) => {
    if (selectedEmployees.includes(employeeId)) {
      setSelectedEmployees(selectedEmployees.filter(id => id !== employeeId));
    } else {
      setSelectedEmployees([...selectedEmployees, employeeId]);
    }
  };

  // Handle process payroll
  const handleProcessPayroll = async () => {
    if (!currentOrganization || !id || selectedEmployees.length === 0) return;

    setIsProcessing(true);
    setProcessError(null);
    setProgress(0);

    try {
      // Process each employee with progress updates
      const totalEmployees = selectedEmployees.length;
      setProgress(10); // Show initial progress

      // Process all selected employees
      const { success, processedItems, error } = await processPayroll(
        currentOrganization.id,
        id,
        selectedEmployees,
        { reprocess: true } // Reprocess to ensure we have the latest data
      );

      if (!success) {
        setProcessError(error || 'Failed to process payroll');
        return;
      }

      // Update progress based on processed items
      const newProgress = Math.round((processedItems / totalEmployees) * 100);
      setProgress(Math.max(newProgress, 100)); // Ensure we show 100% at the end

      // Fetch the updated payroll period to ensure we have the latest data
      const { period } = await getPayrollPeriodById(currentOrganization.id, id, true);

      if (period) {
        setPayrollPeriod(period);
      }

      // Show success modal
      setShowSuccessModal(true);
    } catch (err: any) {
      console.error('Error processing payroll:', err);
      setProcessError(err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  // If loading, show a spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamationCircle}>
          <span className="font-medium">Error!</span> {error}
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/payroll')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Payroll Periods
          </Button>
        </div>
      </div>
    );
  }

  // If payroll period not found, show message
  if (!payrollPeriod) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamationCircle}>
          <span className="font-medium">Error!</span> Payroll period not found
        </Alert>
        <div className="mt-4">
          <Button color="gray" onClick={() => navigate('/payroll')}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back to Payroll Periods
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button color="gray" className="mr-4" onClick={() => navigate(`/payroll/periods/${id}`)}>
            <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
            Back
          </Button>
          <PageTitle title={`Process Payroll: ${payrollPeriod.name}`} />
        </div>
        <div>
          <Button
            color="primary"
            onClick={() => setShowConfirmModal(true)}
            disabled={selectedEmployees.length === 0 || isProcessing}
          >
            <HiOutlineCalculator className="mr-2 h-5 w-5" />
            Process Payroll
          </Button>
        </div>
      </div>

      {processError && (
        <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
          <span className="font-medium">Error!</span> {processError}
        </Alert>
      )}

      {isProcessing && (
        <Card className="mb-4">
          <h3 className="text-lg font-medium mb-2">Processing Payroll...</h3>
          <Progress
            progress={progress}
            size="lg"
            color="blue"
            labelText
            labelPosition="inside"
          />
          <p className="text-sm text-gray-500 mt-2">
            Processing {selectedEmployees.length} employees...
          </p>
        </Card>
      )}

      <Card>
        <div className="mb-4">
          <h3 className="text-lg font-medium">Select Employees to Process</h3>
          <p className="text-sm text-gray-500">
            Select the employees you want to include in this payroll run.
          </p>
        </div>

        <div className="overflow-x-auto">
          <Table striped>
            <Table.Head>
              <Table.HeadCell className="w-10">
                <Checkbox
                  checked={selectedEmployees.length === employees.length && employees.length > 0}
                  onChange={handleSelectAll}
                />
              </Table.HeadCell>
              <Table.HeadCell>Employee</Table.HeadCell>
              <Table.HeadCell>Employee ID</Table.HeadCell>
              <Table.HeadCell>Department</Table.HeadCell>
              <Table.HeadCell>Position</Table.HeadCell>
              <Table.HeadCell>Status</Table.HeadCell>
            </Table.Head>
            <Table.Body>
              {employees.length > 0 ? (
                employees.map((employee) => (
                  <Table.Row key={employee.id}>
                    <Table.Cell>
                      <Checkbox
                        checked={selectedEmployees.includes(employee.id)}
                        onChange={() => handleSelectEmployee(employee.id)}
                      />
                    </Table.Cell>
                    <Table.Cell className="font-medium">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                          {employee.profile_image_url ? (
                            <img
                              src={employee.profile_image_url}
                              alt={`${employee.first_name} ${employee.last_name}`}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                          ) : (
                            <span className="text-xs font-semibold">
                              {employee.first_name?.[0]}{employee.last_name?.[0]}
                            </span>
                          )}
                        </div>
                        <div>
                          <p className="font-medium">
                            {employee.first_name} {employee.last_name}
                          </p>
                        </div>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {employee.employee_number || 'N/A'}
                    </Table.Cell>
                    <Table.Cell>
                      {employee.department?.name || 'Not assigned'}
                    </Table.Cell>
                    <Table.Cell>
                      {employee.position?.title || 'Not assigned'}
                    </Table.Cell>
                    <Table.Cell>
                      {employee.is_active ? (
                        <Badge color="success" icon={HiOutlineCheck}>Active</Badge>
                      ) : (
                        <Badge color="gray" icon={HiOutlineX}>Inactive</Badge>
                      )}
                    </Table.Cell>
                  </Table.Row>
                ))
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={6} className="text-center py-4">
                    No employees found.
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table>
        </div>

        <div className="flex justify-between items-center mt-4">
          <div>
            <p className="text-sm text-gray-500">
              {selectedEmployees.length} of {employees.length} employees selected
            </p>
          </div>
          <div className="flex space-x-2">
            <Button
              color="gray"
              onClick={() => navigate(`/payroll/periods/${id}`)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onClick={() => setShowConfirmModal(true)}
              disabled={selectedEmployees.length === 0 || isProcessing}
            >
              <HiOutlineCalculator className="mr-2 h-5 w-5" />
              Process Payroll
            </Button>
          </div>
        </div>
      </Card>

      {/* Confirm Process Modal */}
      <Modal
        show={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        size="md"
      >
        <Modal.Header>Confirm Process Payroll</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>
              Are you sure you want to process payroll for {selectedEmployees.length} employees?
              This will calculate their pay for the period {payrollPeriod.name}.
            </p>
            <div className="flex justify-end space-x-2">
              <Button
                color="gray"
                onClick={() => setShowConfirmModal(false)}
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button
                color="primary"
                onClick={() => {
                  setShowConfirmModal(false);
                  handleProcessPayroll();
                }}
                isProcessing={isProcessing}
              >
                Process Payroll
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>

      {/* Success Modal */}
      <Modal
        show={showSuccessModal}
        onClose={() => {
          setShowSuccessModal(false);
          navigate(`/payroll/periods/${id}`);
        }}
        size="md"
      >
        <Modal.Header>Payroll Processing Complete</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="bg-green-100 rounded-full p-3">
                <HiOutlineCheck className="h-10 w-10 text-green-600" />
              </div>
            </div>
            <p className="text-center">
              Payroll has been successfully processed for {selectedEmployees.length} employees.
            </p>
            <div className="flex justify-center">
              <Button
                color="primary"
                onClick={() => {
                  setShowSuccessModal(false);
                  navigate(`/payroll/periods/${id}`);
                }}
              >
                View Payroll Details
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default PayrollProcessing;
