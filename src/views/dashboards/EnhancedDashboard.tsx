import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON>, Spinner, Badge, <PERSON>ton, Tabs } from 'flowbite-react';
import { useOrganization } from 'src/context/OrganizationContext';
import { HiOutlineCog, HiOutlineExclamationCircle, HiOutlineRefresh } from 'react-icons/hi';
import { getProducts, Product } from '../../services/product';
import { getCustomers, Customer } from '../../services/customer';
import { getEmployees } from '../../services/employee';
import { getTimeEntries } from '../../services/timeEntry';
import { formatCurrency } from '../../utils/formatters';
import SalesOverview from '../../components/dashboard/enhanced/SalesOverview';
import InventoryStatus from '../../components/dashboard/enhanced/InventoryStatus';
import RecentActivity from '../../components/dashboard/enhanced/RecentActivity';
import TopProducts from '../../components/dashboard/enhanced/TopProducts';
import EmployeeStats from '../../components/dashboard/enhanced/EmployeeStats';

const EnhancedDashboard: React.FC = () => {
  const { currentOrganization, loading: orgLoading } = useOrganization();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState<{
    products: Product[];
    lowStockItems: Product[];
    customers: Customer[];
    recentCustomers: Customer[];
    employeeCount: number;
    timeEntries: any[];
    payrollPeriods: any[];
  }>({
    products: [],
    lowStockItems: [],
    customers: [],
    recentCustomers: [],
    employeeCount: 0,
    timeEntries: [],
    payrollPeriods: []
  });
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Fetch dashboard data
  useEffect(() => {
    if (!currentOrganization) return;

    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch products
        const { products, error: productsError } = await getProducts(
          currentOrganization.id,
          { limit: 100 }
        );

        if (productsError) {
          throw new Error(productsError);
        }

        // Identify low stock items
        const lowStockItems = products.filter(
          product => product.stock_quantity <= product.min_stock_level
        );

        // Fetch customers
        const { customers, error: customersError } = await getCustomers(
          currentOrganization.id,
          { limit: 50 }
        );

        if (customersError) {
          throw new Error(customersError);
        }

        // Get recent customers (last 5)
        const recentCustomers = [...customers]
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, 5);

        // Fetch employee count
        const { count: employeeCount, error: employeesError } = await getEmployees(
          currentOrganization.id,
          { limit: 1 }
        );

        if (employeesError) {
          throw new Error(employeesError);
        }

        // Fetch time entries
        const { entries: timeEntries, error: timeEntriesError } = await getTimeEntries(
          currentOrganization.id,
          { limit: 10 }
        );

        if (timeEntriesError) {
          throw new Error(timeEntriesError);
        }

        // Mock payroll periods
        const mockPayrollPeriods = [
          {
            id: 1,
            name: 'January 2023',
            start_date: '2023-01-01',
            end_date: '2023-01-15',
            status: 'completed'
          },
          {
            id: 2,
            name: 'February 2023',
            start_date: '2023-02-01',
            end_date: '2023-02-15',
            status: 'completed'
          },
          {
            id: 3,
            name: 'March 2023',
            start_date: '2023-03-01',
            end_date: '2023-03-15',
            status: 'processing'
          }
        ];

        // Set dashboard data
        setDashboardData({
          products,
          lowStockItems,
          customers,
          recentCustomers,
          employeeCount,
          timeEntries: timeEntries || [],
          payrollPeriods: mockPayrollPeriods || []
        });
      } catch (err: any) {
        console.error('Error fetching dashboard data:', err);
        setError(err.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [currentOrganization]);

  // Handle refresh
  const handleRefresh = () => {
    if (!currentOrganization) return;

    // Re-fetch dashboard data
    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch products
        const { products, error: productsError } = await getProducts(
          currentOrganization.id,
          { limit: 100 }
        );

        if (productsError) {
          throw new Error(productsError);
        }

        // Identify low stock items
        const lowStockItems = products.filter(
          product => product.stock_quantity <= product.min_stock_level
        );

        // Fetch customers
        const { customers, error: customersError } = await getCustomers(
          currentOrganization.id,
          { limit: 50 }
        );

        if (customersError) {
          throw new Error(customersError);
        }

        // Get recent customers (last 5)
        const recentCustomers = [...customers]
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, 5);

        // Fetch employee count
        const { count: employeeCount, error: employeesError } = await getEmployees(
          currentOrganization.id,
          { limit: 1 }
        );

        if (employeesError) {
          throw new Error(employeesError);
        }

        // Fetch time entries
        const { entries: timeEntries, error: timeEntriesError } = await getTimeEntries(
          currentOrganization.id,
          { limit: 10 }
        );

        if (timeEntriesError) {
          throw new Error(timeEntriesError);
        }

        // Mock payroll periods
        const mockPayrollPeriods = [
          {
            id: 1,
            name: 'January 2023',
            start_date: '2023-01-01',
            end_date: '2023-01-15',
            status: 'completed'
          },
          {
            id: 2,
            name: 'February 2023',
            start_date: '2023-02-01',
            end_date: '2023-02-15',
            status: 'completed'
          },
          {
            id: 3,
            name: 'March 2023',
            start_date: '2023-03-01',
            end_date: '2023-03-15',
            status: 'processing'
          }
        ];

        // Set dashboard data
        setDashboardData({
          products,
          lowStockItems,
          customers,
          recentCustomers,
          employeeCount,
          timeEntries: timeEntries || [],
          payrollPeriods: mockPayrollPeriods || []
        });
      } catch (err: any) {
        console.error('Error fetching dashboard data:', err);
        setError(err.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  };

  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Dashboard Header */}
      <div className="col-span-12 mb-4">
        <Card>
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold mb-2">Business Dashboard</h2>
              <p className="text-sm text-gray-500">
                {currentOrganization ? currentOrganization.name : 'Loading organization...'}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button color="light" onClick={handleRefresh} disabled={loading}>
                <HiOutlineRefresh className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
                <span className="ml-1">Refresh</span>
              </Button>
              {currentOrganization && (
                <Link to="/organization/settings">
                  <Button color="light">
                    <HiOutlineCog className="h-5 w-5 mr-1" />
                    <span>Settings</span>
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </Card>
      </div>

      {!currentOrganization && !orgLoading ? (
        <div className="col-span-12">
          <Alert color="warning">
            <h3 className="font-medium">No Organization Selected</h3>
            <p>
              Please select or create an organization to start using the POS system.
            </p>
            <div className="mt-4">
              <Link to="/organization/create">
                <Button color="primary">
                  Create Organization
                </Button>
              </Link>
            </div>
          </Alert>
        </div>
      ) : loading ? (
        <div className="col-span-12 flex justify-center items-center p-12">
          <Spinner size="xl" />
          <span className="ml-2">Loading dashboard data...</span>
        </div>
      ) : error ? (
        <div className="col-span-12">
          <Alert color="failure" icon={HiOutlineExclamationCircle}>
            <h3 className="font-medium">Error Loading Dashboard</h3>
            <p>{error}</p>
            <div className="mt-4">
              <Button color="failure" onClick={handleRefresh}>
                <HiOutlineRefresh className="mr-2 h-5 w-5" />
                Retry
              </Button>
            </div>
          </Alert>
        </div>
      ) : (
        <>
          <div className="col-span-12">
            <Tabs.Group
              aria-label="Dashboard tabs"
              style={{ underline: true }}
              onActiveTabChange={(tab) => setActiveTab(tab === 0 ? 'overview' : tab === 1 ? 'inventory' : 'employees')}
            >
              <Tabs.Item title="Business Overview" active={activeTab === 'overview'}>
                <div className="grid grid-cols-12 gap-6 mt-4">
                  <div className="lg:col-span-8 col-span-12">
                    <SalesOverview customers={dashboardData.customers} />
                  </div>
                  <div className="lg:col-span-4 col-span-12">
                    <RecentActivity
                      timeEntries={dashboardData.timeEntries}
                      customers={dashboardData.recentCustomers}
                    />
                  </div>
                  <div className="lg:col-span-12 col-span-12">
                    <TopProducts products={dashboardData.products} />
                  </div>
                </div>
              </Tabs.Item>
              <Tabs.Item title="Inventory Status" active={activeTab === 'inventory'}>
                <div className="grid grid-cols-12 gap-6 mt-4">
                  <div className="col-span-12">
                    <InventoryStatus
                      products={dashboardData.products}
                      lowStockItems={dashboardData.lowStockItems}
                    />
                  </div>
                </div>
              </Tabs.Item>
              <Tabs.Item title="Employee Stats" active={activeTab === 'employees'}>
                <div className="grid grid-cols-12 gap-6 mt-4">
                  <div className="col-span-12">
                    <EmployeeStats
                      employeeCount={dashboardData.employeeCount}
                      timeEntries={dashboardData.timeEntries}
                      payrollPeriods={dashboardData.payrollPeriods}
                    />
                  </div>
                </div>
              </Tabs.Item>
            </Tabs.Group>
          </div>
        </>
      )}
    </div>
  );
};

export default EnhancedDashboard;
