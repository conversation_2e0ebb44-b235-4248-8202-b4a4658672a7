import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON>, Spinner, But<PERSON> } from 'flowbite-react';
import { useOrganization } from 'src/context/OrganizationContext';
import { HiOutlineCog, HiOutlineExclamationCircle, HiOutlineRefresh } from 'react-icons/hi';
import { getProducts, Product } from '../../services/product';
import { getCustomers, Customer } from '../../services/customer';
import { getEmployees } from '../../services/employee';
import { formatCurrency } from '../../utils/formatters';

const SimpleDashboard: React.FC = () => {
  const { currentOrganization, loading: orgLoading } = useOrganization();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState<{
    products: Product[];
    lowStockItems: Product[];
    customers: Customer[];
    employeeCount: number;
  }>({
    products: [],
    lowStockItems: [],
    customers: [],
    employeeCount: 0
  });

  // Fetch dashboard data
  useEffect(() => {
    if (!currentOrganization) return;

    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch products
        const { products, error: productsError } = await getProducts(
          currentOrganization.id,
          { limit: 100 }
        );

        if (productsError) {
          throw new Error(productsError);
        }

        // Identify low stock items
        const lowStockItems = products.filter(
          product => product.stock_quantity <= product.min_stock_level
        );

        // Fetch customers
        const { customers, error: customersError } = await getCustomers(
          currentOrganization.id,
          { limit: 50 }
        );

        if (customersError) {
          throw new Error(customersError);
        }

        // Fetch employee count
        const { count: employeeCount, error: employeesError } = await getEmployees(
          currentOrganization.id,
          { limit: 1 }
        );

        if (employeesError) {
          throw new Error(employeesError);
        }

        // Set dashboard data
        setDashboardData({
          products,
          lowStockItems,
          customers,
          employeeCount
        });
      } catch (err: any) {
        console.error('Error fetching dashboard data:', err);
        setError(err.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [currentOrganization]);

  // Handle refresh
  const handleRefresh = () => {
    if (!currentOrganization) return;

    // Re-fetch dashboard data
    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch products
        const { products, error: productsError } = await getProducts(
          currentOrganization.id,
          { limit: 100 }
        );

        if (productsError) {
          throw new Error(productsError);
        }

        // Identify low stock items
        const lowStockItems = products.filter(
          product => product.stock_quantity <= product.min_stock_level
        );

        // Fetch customers
        const { customers, error: customersError } = await getCustomers(
          currentOrganization.id,
          { limit: 50 }
        );

        if (customersError) {
          throw new Error(customersError);
        }

        // Fetch employee count
        const { count: employeeCount, error: employeesError } = await getEmployees(
          currentOrganization.id,
          { limit: 1 }
        );

        if (employeesError) {
          throw new Error(employeesError);
        }

        // Set dashboard data
        setDashboardData({
          products,
          lowStockItems,
          customers,
          employeeCount
        });
      } catch (err: any) {
        console.error('Error fetching dashboard data:', err);
        setError(err.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  };

  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Dashboard Header */}
      <div className="col-span-12 mb-4">
        <Card>
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold mb-2">Business Dashboard</h2>
              <p className="text-sm text-gray-500">
                {currentOrganization ? currentOrganization.name : 'Loading organization...'}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button color="light" onClick={handleRefresh} disabled={loading}>
                <HiOutlineRefresh className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
                <span className="ml-1">Refresh</span>
              </Button>
              {currentOrganization && (
                <Link to="/organization/settings">
                  <Button color="light">
                    <HiOutlineCog className="h-5 w-5 mr-1" />
                    <span>Settings</span>
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </Card>
      </div>

      {!currentOrganization && !orgLoading ? (
        <div className="col-span-12">
          <Alert color="warning">
            <h3 className="font-medium">No Organization Selected</h3>
            <p>
              Please select or create an organization to start using the POS system.
            </p>
            <div className="mt-4">
              <Link to="/organization/create">
                <Button color="primary">
                  Create Organization
                </Button>
              </Link>
            </div>
          </Alert>
        </div>
      ) : loading ? (
        <div className="col-span-12 flex justify-center items-center p-12">
          <Spinner size="xl" />
          <span className="ml-2">Loading dashboard data...</span>
        </div>
      ) : error ? (
        <div className="col-span-12">
          <Alert color="failure" icon={HiOutlineExclamationCircle}>
            <h3 className="font-medium">Error Loading Dashboard</h3>
            <p>{error}</p>
            <div className="mt-4">
              <Button color="failure" onClick={handleRefresh}>
                <HiOutlineRefresh className="mr-2 h-5 w-5" />
                Retry
              </Button>
            </div>
          </Alert>
        </div>
      ) : (
        <>
          <div className="col-span-12">
            <Card>
              <h3 className="text-xl font-semibold mb-4">Business Overview</h3>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm text-blue-700">Total Products</p>
                  <p className="text-xl font-bold text-blue-700">{dashboardData.products.length}</p>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <p className="text-sm text-red-700">Low Stock Items</p>
                  <p className="text-xl font-bold text-red-700">{dashboardData.lowStockItems.length}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-sm text-green-700">Total Customers</p>
                  <p className="text-xl font-bold text-green-700">{dashboardData.customers.length}</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <p className="text-sm text-purple-700">Total Employees</p>
                  <p className="text-xl font-bold text-purple-700">{dashboardData.employeeCount}</p>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="text-lg font-medium mb-4">Inventory Value</h4>
                <p className="text-2xl font-bold">
                  {formatCurrency(
                    dashboardData.products.reduce((sum, product) => {
                      const stockQuantity = product.stock_quantity || 0;
                      const unitPrice = product.unit_price || 0;
                      return sum + (stockQuantity * unitPrice);
                    }, 0)
                  )}
                </p>
                <p className="text-sm text-gray-500">Total value of inventory</p>
              </div>
            </Card>
          </div>
        </>
      )}
    </div>
  );
};

export default SimpleDashboard;
