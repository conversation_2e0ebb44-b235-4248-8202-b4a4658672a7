import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON>, Spinner, <PERSON>ton, Tabs } from 'flowbite-react';
import { useOrganization } from 'src/context/OrganizationContext';
import '../../styles/dashboard.css';
import {
  HiOutlineCog,
  HiOutlineExclamationCircle,
  HiOutlineRefresh,
  HiOutlineExclamation,
  HiOutlineDocumentReport,
  HiOutlineUser
} from 'react-icons/hi';
import { getProducts, Product } from '../../services/product';
import { getCustomers, Customer } from '../../services/customer';
import { getEmployees } from '../../services/employee';
import { getTimeEntries } from '../../services/timeEntry';
import { formatCurrency } from '../../utils/formatters';
import SalesOverview from '../../components/dashboard/enhanced/SalesOverview';
import TopProducts from '../../components/dashboard/enhanced/TopProducts';
import InventoryStatus from '../../components/dashboard/enhanced/InventoryStatus';
import RecentActivity from '../../components/dashboard/enhanced/RecentActivity';
import EmployeeStats from '../../components/dashboard/enhanced/EmployeeStats';
import SalesSummary from '../../components/dashboard/SalesSummary';

const BasicEnhancedDashboard: React.FC = () => {
  const { currentOrganization, loading: orgLoading } = useOrganization();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState<{
    products: Product[];
    lowStockItems: Product[];
    customers: Customer[];
    recentCustomers: Customer[];
    employeeCount: number;
    timeEntries: any[];
  }>({
    products: [],
    lowStockItems: [],
    customers: [],
    recentCustomers: [],
    employeeCount: 0,
    timeEntries: []
  });
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Fetch dashboard data
  useEffect(() => {
    if (!currentOrganization) return;

    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch products
        const { products, error: productsError } = await getProducts(
          currentOrganization.id,
          { limit: 100 }
        );

        if (productsError) {
          throw new Error(productsError);
        }

        // Identify low stock items
        const lowStockItems = products.filter(
          product => product.stock_quantity <= product.min_stock_level
        );

        // Fetch customers
        const { customers, error: customersError } = await getCustomers(
          currentOrganization.id,
          { limit: 50 }
        );

        if (customersError) {
          throw new Error(customersError);
        }

        // Get recent customers (last 5)
        const recentCustomers = [...customers]
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, 5);

        // Fetch employee count
        const { count: employeeCount, error: employeesError } = await getEmployees(
          currentOrganization.id,
          { limit: 1 }
        );

        if (employeesError) {
          throw new Error(employeesError);
        }

        // Fetch time entries
        const { entries: timeEntries, error: timeEntriesError } = await getTimeEntries(
          currentOrganization.id,
          { limit: 10 }
        );

        if (timeEntriesError) {
          throw new Error(timeEntriesError);
        }

        // Set dashboard data
        setDashboardData({
          products,
          lowStockItems,
          customers,
          recentCustomers,
          employeeCount,
          timeEntries: timeEntries || []
        });
      } catch (err: any) {
        console.error('Error fetching dashboard data:', err);
        setError(err.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [currentOrganization]);

  // Handle refresh
  const handleRefresh = () => {
    if (!currentOrganization) return;

    // Re-fetch dashboard data
    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch products
        const { products, error: productsError } = await getProducts(
          currentOrganization.id,
          { limit: 100 }
        );

        if (productsError) {
          throw new Error(productsError);
        }

        // Identify low stock items
        const lowStockItems = products.filter(
          product => product.stock_quantity <= product.min_stock_level
        );

        // Fetch customers
        const { customers, error: customersError } = await getCustomers(
          currentOrganization.id,
          { limit: 50 }
        );

        if (customersError) {
          throw new Error(customersError);
        }

        // Get recent customers (last 5)
        const recentCustomers = [...customers]
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, 5);

        // Fetch employee count
        const { count: employeeCount, error: employeesError } = await getEmployees(
          currentOrganization.id,
          { limit: 1 }
        );

        if (employeesError) {
          throw new Error(employeesError);
        }

        // Fetch time entries
        const { entries: timeEntries, error: timeEntriesError } = await getTimeEntries(
          currentOrganization.id,
          { limit: 10 }
        );

        if (timeEntriesError) {
          throw new Error(timeEntriesError);
        }

        // Set dashboard data
        setDashboardData({
          products,
          lowStockItems,
          customers,
          recentCustomers,
          employeeCount,
          timeEntries: timeEntries || []
        });
      } catch (err: any) {
        console.error('Error fetching dashboard data:', err);
        setError(err.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  };

  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Dashboard Header */}
      <div className="col-span-12 mb-4">
        <Card>
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold mb-2">Business Dashboard</h2>
              <p className="text-sm text-gray-500">
                {currentOrganization ? currentOrganization.name : 'Loading organization...'}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button color="light" onClick={handleRefresh} disabled={loading}>
                <HiOutlineRefresh className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
                <span className="ml-1">Refresh</span>
              </Button>
              {currentOrganization && (
                <Link to="/organization/settings">
                  <Button color="light">
                    <HiOutlineCog className="h-5 w-5 mr-1" />
                    <span>Settings</span>
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </Card>
      </div>

      {!currentOrganization && !orgLoading ? (
        <div className="col-span-12">
          <Alert color="warning">
            <h3 className="font-medium">No Organization Selected</h3>
            <p>
              Please select or create an organization to start using the POS system.
            </p>
            <div className="mt-4">
              <Link to="/organization/create">
                <Button color="primary">
                  Create Organization
                </Button>
              </Link>
            </div>
          </Alert>
        </div>
      ) : loading ? (
        <div className="col-span-12 flex justify-center items-center p-12">
          <Spinner size="xl" />
          <span className="ml-2">Loading dashboard data...</span>
        </div>
      ) : error ? (
        <div className="col-span-12">
          <Alert color="failure" icon={HiOutlineExclamationCircle}>
            <h3 className="font-medium">Error Loading Dashboard</h3>
            <p>{error}</p>
            <div className="mt-4">
              <Button color="failure" onClick={handleRefresh}>
                <HiOutlineRefresh className="mr-2 h-5 w-5" />
                Retry
              </Button>
            </div>
          </Alert>
        </div>
      ) : (
        <>
          <div className="col-span-12">
            <Tabs
              aria-label="Dashboard tabs"
              onActiveTabChange={(tab) => setActiveTab(tab === 0 ? 'overview' : tab === 1 ? 'sales' : tab === 2 ? 'inventory' : 'employees')}
              className="dashboard-tabs focus:outline-none"
            >
              <Tabs.Item title="Business Overview" active={activeTab === 'overview'}>
                <div className="grid grid-cols-12 gap-6 mt-4">


                  {/* Key Metrics Cards */}
                  <div className="col-span-12">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
                      {/* Total Products */}
                      <Card className="p-4">
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-blue-100 mr-4">
                            <HiOutlineDocumentReport className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <h5 className="text-gray-500 text-sm">Total Products</h5>
                            <p className="text-2xl font-bold">{dashboardData.products.length}</p>
                          </div>
                        </div>
                      </Card>

                      {/* Low Stock Items */}
                      <Card className="p-4">
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-orange-100 mr-4">
                            <HiOutlineExclamation className="h-6 w-6 text-orange-600" />
                          </div>
                          <div>
                            <h5 className="text-gray-500 text-sm">Low Stock</h5>
                            <p className="text-2xl font-bold text-orange-600">{dashboardData.lowStockItems.length}</p>
                          </div>
                        </div>
                      </Card>

                      {/* Total Customers */}
                      <Card className="p-4">
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-green-100 mr-4">
                            <HiOutlineUser className="h-6 w-6 text-green-600" />
                          </div>
                          <div>
                            <h5 className="text-gray-500 text-sm">Customers</h5>
                            <p className="text-2xl font-bold">{dashboardData.customers.length}</p>
                          </div>
                        </div>
                      </Card>

                      {/* Active Employees */}
                      <Card className="p-4">
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-purple-100 mr-4">
                            <HiOutlineUser className="h-6 w-6 text-purple-600" />
                          </div>
                          <div>
                            <h5 className="text-gray-500 text-sm">Employees</h5>
                            <p className="text-2xl font-bold">{dashboardData.employeeCount}</p>
                          </div>
                        </div>
                      </Card>

                      {/* Recent Customers */}
                      <Card className="p-4">
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-yellow-100 mr-4">
                            <HiOutlineUser className="h-6 w-6 text-yellow-600" />
                          </div>
                          <div>
                            <h5 className="text-gray-500 text-sm">Recent Customers</h5>
                            <p className="text-2xl font-bold text-yellow-600">{dashboardData.recentCustomers.length}</p>
                          </div>
                        </div>
                      </Card>

                      {/* Time Entries */}
                      <Card className="p-4">
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-indigo-100 mr-4">
                            <HiOutlineDocumentReport className="h-6 w-6 text-indigo-600" />
                          </div>
                          <div>
                            <h5 className="text-gray-500 text-sm">Time Entries</h5>
                            <p className="text-2xl font-bold text-indigo-600">{dashboardData.timeEntries.length}</p>
                          </div>
                        </div>
                      </Card>
                    </div>
                  </div>

                  <div className="lg:col-span-8 col-span-12">
                    <SalesOverview customers={dashboardData.customers} />
                  </div>
                  <div className="lg:col-span-4 col-span-12">
                    <RecentActivity
                      timeEntries={dashboardData.timeEntries}
                      customers={dashboardData.recentCustomers}
                    />
                  </div>
                  <div className="lg:col-span-12 col-span-12">
                    <TopProducts products={dashboardData.products} />
                  </div>
                </div>
              </Tabs.Item>
              <Tabs.Item title="Sales Summary" active={activeTab === 'sales'}>
                <div className="mt-4">
                  <SalesSummary showHeader={false} />
                </div>
              </Tabs.Item>
              <Tabs.Item title="Inventory Status" active={activeTab === 'inventory'}>
                <div className="grid grid-cols-12 gap-6 mt-4">

                  <div className="col-span-12">
                    <InventoryStatus
                      products={dashboardData.products}
                      lowStockItems={dashboardData.lowStockItems}
                    />
                  </div>
                  <div className="col-span-12 mt-4">
                    <Card>
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold">Float Inventory Management</h3>
                        <div className="flex space-x-2">
                          <Link to="/inventory/float">
                            <Button color="warning" size="sm">
                              <HiOutlineExclamation className="mr-2 h-5 w-5" />
                              View Float Inventory
                            </Button>
                          </Link>
                          <Link to="/inventory/float/report">
                            <Button color="light" size="sm">
                              <HiOutlineDocumentReport className="mr-2 h-5 w-5" />
                              View Reports
                            </Button>
                          </Link>
                        </div>
                      </div>
                      <p className="text-gray-600">
                        Float inventory occurs when items are sold without sufficient stock.
                        Monitor and resolve float inventory to improve operational efficiency.
                      </p>
                    </Card>
                  </div>
                </div>
              </Tabs.Item>
              <Tabs.Item title="Employee Stats" active={activeTab === 'employees'}>
                <div className="grid grid-cols-12 gap-6 mt-4">
                  <div className="col-span-12">
                    <EmployeeStats
                      employeeCount={dashboardData.employeeCount}
                      timeEntries={dashboardData.timeEntries}
                      payrollPeriods={[
                        {
                          id: 1,
                          name: 'January 2023',
                          start_date: '2023-01-01',
                          end_date: '2023-01-15',
                          status: 'completed'
                        },
                        {
                          id: 2,
                          name: 'February 2023',
                          start_date: '2023-02-01',
                          end_date: '2023-02-15',
                          status: 'completed'
                        }
                      ]}
                    />
                  </div>
                </div>
              </Tabs.Item>
            </Tabs>
          </div>
        </>
      )}
    </div>
  );
};

export default BasicEnhancedDashboard;
