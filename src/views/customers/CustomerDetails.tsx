import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ner,
  <PERSON><PERSON>,
  <PERSON>ge,
  Modal
} from 'flowbite-react';
import {
  HiOutlineExclamation,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineArrowLeft,
  HiOutlineMail,
  HiOutlinePhone,
  HiOutlineLocationMarker,
  HiOutlineOfficeBuilding,
  HiOutlineGlobe,
  HiOutlineIdentification,
  HiOutlineDocumentText,
  HiOutlineTag
} from 'react-icons/hi';
import { getCustomerById, deleteCustomer, Customer } from '../../services/customer';
import { useOrganization } from '../../context/OrganizationContext';
import { getCustomerTags, addTagToCustomer, removeTagFromCustomer } from '../../services/customerTagService';
import { Tag } from '../../types/tagging.types';
import TagSelector from '../../components/tags/TagSelector';
import TagList from '../../components/tags/TagList';
import CustomerLoyaltyProfile from '../../components/customers/CustomerLoyaltyProfile';

const CustomerDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { currentOrganization } = useOrganization();
  const navigate = useNavigate();

  const [customer, setCustomer] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Tags state
  const [customerTags, setCustomerTags] = useState<Tag[]>([]);
  const [isLoadingTags, setIsLoadingTags] = useState(false);
  const [tagsError, setTagsError] = useState<string | null>(null);

  // Delete confirmation modal
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomer = async () => {
      if (!currentOrganization || !id) return;

      setIsLoading(true);
      setError(null);

      try {
        const { customer: customerData, error: fetchError } = await getCustomerById(
          currentOrganization.id,
          id
        );

        if (fetchError) {
          setError(fetchError);
        } else if (customerData) {
          setCustomer(customerData);
        } else {
          setError('Customer not found');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the customer');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomer();
  }, [currentOrganization, id]);

  // Fetch customer tags
  useEffect(() => {
    const fetchCustomerTags = async () => {
      if (!id) return;

      setIsLoadingTags(true);
      setTagsError(null);

      try {
        const { tags, error } = await getCustomerTags(id);

        if (error) {
          setTagsError(error);
        } else {
          setCustomerTags(tags);
        }
      } catch (err: any) {
        setTagsError(err.message || 'An error occurred while fetching tags');
      } finally {
        setIsLoadingTags(false);
      }
    };

    fetchCustomerTags();
  }, [id]);

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!currentOrganization || !customer) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const { success, error: deleteErr } = await deleteCustomer(
        currentOrganization.id,
        customer.id
      );

      if (deleteErr) {
        setDeleteError(deleteErr);
      } else if (success) {
        setShowDeleteConfirm(false);
        // Navigate to the customer list page
        navigate('/customers');
      }
    } catch (err: any) {
      setDeleteError(err.message || 'An error occurred while deleting the customer');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle tag changes
  const handleTagsChange = async (newTags: Tag[]) => {
    if (!id) return;

    // Find tags that were added
    const addedTags = newTags.filter(
      newTag => !customerTags.some(existingTag => existingTag.id === newTag.id)
    );

    // Find tags that were removed
    const removedTags = customerTags.filter(
      existingTag => !newTags.some(newTag => newTag.id === existingTag.id)
    );

    // Update the state with the new tags immediately for better UX
    setCustomerTags(newTags);

    // Add new tags
    for (const tag of addedTags) {
      try {
        await addTagToCustomer(id, tag.id);
      } catch (err) {
        console.error(`Failed to add tag ${tag.name}:`, err);
        // If there's an error, revert the tag addition in the UI
        setCustomerTags(prevTags => prevTags.filter(t => t.id !== tag.id));
      }
    }

    // Remove deleted tags
    for (const tag of removedTags) {
      try {
        await removeTagFromCustomer(id, tag.id);
      } catch (err) {
        console.error(`Failed to remove tag ${tag.name}:`, err);
        // If there's an error, revert the tag removal in the UI
        setCustomerTags(prevTags => [...prevTags, tag]);
      }
    }
  };

  if (!currentOrganization) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">No Organization Selected</h3>
            <p>
              Please select an organization to view customer details.
            </p>
          </Alert>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        </Card>
      </div>
    );
  }

  if (error || !customer) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">Error</h3>
            <p>{error || 'Customer not found'}</p>
            <div className="mt-4">
              <Link to="/customers">
                <Button color="gray" size="sm">
                  <HiOutlineArrowLeft className="mr-2 h-4 w-4" />
                  Back to Customers
                </Button>
              </Link>
            </div>
          </Alert>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        {/* Header with actions */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div className="flex items-center">
            <h1 className="text-2xl font-bold">{customer.name}</h1>
          </div>

          <div className="flex gap-2">
            <Link to="/customers">
              <Button color="gray">
                <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
                Back
              </Button>
            </Link>
            <Link to={`/customers/edit/${customer.id}`}>
              <Button color="primary">
                <HiOutlinePencil className="mr-2 h-5 w-5" />
                Edit
              </Button>
            </Link>
            <Button color="failure" onClick={handleDeleteClick}>
              <HiOutlineTrash className="mr-2 h-5 w-5" />
              Delete
            </Button>
          </div>
        </div>

        {/* Customer information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Basic Information */}
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold mb-4">Contact Information</h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <HiOutlineMail className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p>{customer.email || 'Not provided'}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <HiOutlinePhone className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p>{customer.phone || 'Not provided'}</p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold mb-4">Additional Information</h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <HiOutlineIdentification className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Tax ID</p>
                    <p>{customer.tax_id || 'Not provided'}</p>
                  </div>
                </div>

                {customer.notes && (
                  <div className="flex items-start">
                    <HiOutlineDocumentText className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Notes</p>
                      <p className="whitespace-pre-line">{customer.notes}</p>
                    </div>
                  </div>
                )}

                {/* Tags */}
                <div className="flex items-start mt-4">
                  <HiOutlineTag className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                  <div className="w-full">
                    <p className="text-sm text-gray-500 mb-2">Tags</p>
                    {isLoadingTags ? (
                      <div className="flex items-center">
                        <Spinner size="sm" className="mr-2" />
                        <span className="text-gray-500">Loading tags...</span>
                      </div>
                    ) : tagsError ? (
                      <Alert color="failure" className="mb-4">
                        {tagsError}
                      </Alert>
                    ) : (
                      <div>
                        <TagSelector
                          entityType="customer"
                          entityId={customer.id}
                          selectedTags={customerTags}
                          onTagsChange={handleTagsChange}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Address Information */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Address Information</h2>
            <div className="space-y-4">
              {customer.address && (
                <div className="flex items-start">
                  <HiOutlineLocationMarker className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Street Address</p>
                    <p>{customer.address}</p>
                  </div>
                </div>
              )}

              {(customer.city || customer.state) && (
                <div className="flex items-start">
                  <HiOutlineOfficeBuilding className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">City/State</p>
                    <p>
                      {customer.city && customer.state
                        ? `${customer.city}, ${customer.state}`
                        : customer.city || customer.state}
                    </p>
                  </div>
                </div>
              )}

              {(customer.postal_code || customer.country) && (
                <div className="flex items-start">
                  <HiOutlineGlobe className="mt-1 mr-3 h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">
                      {customer.postal_code && customer.country
                        ? 'Postal Code/Country'
                        : customer.postal_code ? 'Postal Code' : 'Country'}
                    </p>
                    <p>
                      {customer.postal_code && customer.country
                        ? `${customer.postal_code}, ${customer.country}`
                        : customer.postal_code || customer.country}
                    </p>
                  </div>
                </div>
              )}

              {!customer.address && !customer.city && !customer.state &&
               !customer.postal_code && !customer.country && (
                <p className="text-gray-500">No address information provided</p>
              )}
            </div>
          </div>
        </div>

        {/* Loyalty Profile */}
        <div className="mt-8">
          <CustomerLoyaltyProfile customerId={customer.id} />
        </div>

        {/* System Information */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h2 className="text-sm font-medium text-gray-500 mb-2">System Information</h2>
          <div className="flex flex-wrap gap-4">
            <div>
              <p className="text-xs text-gray-500">Customer ID</p>
              <p className="text-sm">{customer.id}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Created</p>
              <p className="text-sm">{new Date(customer.created_at).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Last Updated</p>
              <p className="text-sm">{new Date(customer.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)}>
        <Modal.Header>Confirm Deletion</Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-red-500" />
            <h3 className="mb-5 text-lg font-normal text-gray-500">
              Are you sure you want to delete {customer.name}?
            </h3>
            {deleteError && (
              <Alert color="failure" className="mb-4">
                {deleteError}
              </Alert>
            )}
            <div className="flex justify-center gap-4">
              <Button color="failure" onClick={confirmDelete} disabled={isDeleting}>
                {isDeleting ? <Spinner size="sm" /> : 'Yes, delete'}
              </Button>
              <Button color="gray" onClick={() => setShowDeleteConfirm(false)}>
                No, cancel
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default CustomerDetails;
