import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Card,
  Table,
  But<PERSON>,
  Spinner,
  Alert,
  TextInput,
  Dropdown,
  Modal
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineSearch,
  HiOutlineDotsVertical,
  HiOutlineExclamation,
  HiOutlineMail,
  HiOutlinePhone,
  HiOutlineEye,
  HiOutlineDownload,
  HiOutlineUpload
} from 'react-icons/hi';
import { getCustomers, Customer, deleteCustomer } from '../../services/customer';
import { useOrganization } from '../../context/OrganizationContext';
import Pagination from '../../components/common/Pagination';
import { exportCustomers } from '../../utils/excelExport';
import GenericImport from '../../components/common/GenericImport';
import {
  previewCustomerImport,
  importCustomers,
  downloadCustomerImportTemplate,
  CustomerImportResult
} from '../../services/customerImport';

const CustomerList = () => {
  const { currentOrganization } = useOrganization();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [customersPerPage, setCustomersPerPage] = useState(10);

  // Delete confirmation modal
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Import modal
  const [showImportModal, setShowImportModal] = useState(false);

  const fetchCustomers = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { customers: customerData, count, error: fetchError } = await getCustomers(
        currentOrganization.id,
        {
          searchQuery: searchQuery || undefined,
          limit: customersPerPage,
          offset: (currentPage - 1) * customersPerPage
        }
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setCustomers(customerData);
        setTotalCustomers(count);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching customers');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCustomers();
  }, [currentOrganization, currentPage, searchQuery, customersPerPage]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
    fetchCustomers();
  };

  const handleDeleteClick = (customer: Customer) => {
    setCustomerToDelete(customer);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!currentOrganization || !customerToDelete) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const { success, error: deleteErr } = await deleteCustomer(
        currentOrganization.id,
        customerToDelete.id
      );

      if (deleteErr) {
        setDeleteError(deleteErr);
      } else if (success) {
        setShowDeleteConfirm(false);
        setCustomerToDelete(null);
        fetchCustomers(); // Refresh the list
      }
    } catch (err: any) {
      setDeleteError(err.message || 'An error occurred while deleting the customer');
    } finally {
      setIsDeleting(false);
    }
  };

  // Export customers to Excel
  const handleExportCustomers = () => {
    if (customers.length === 0) {
      setError('No customers to export');
      return;
    }
    exportCustomers(customers);
  };

  // Handle import completion
  const handleImportComplete = (result: CustomerImportResult) => {
    if (result.successCount > 0) {
      // Refresh the customer list to show imported customers
      fetchCustomers();
    }
    setShowImportModal(false);
  };

  const totalPages = Math.ceil(totalCustomers / customersPerPage);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Customers</h1>
            <p className="text-gray-500">
              Manage your customer database. You can add, edit, and delete customer information.
            </p>
          </div>

          <div className="flex gap-2">
            <Button color="light" onClick={handleExportCustomers}>
              <HiOutlineDownload className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button color="light" onClick={() => setShowImportModal(true)}>
              <HiOutlineUpload className="mr-2 h-4 w-4" />
              Import
            </Button>
            <Link to="/customers/create">
              <Button color="primary">
                <HiOutlinePlus className="mr-2 h-5 w-5" />
                Add Customer
              </Button>
            </Link>
          </div>
        </div>

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="mb-6">
          <div className="flex gap-2">
            <TextInput
              id="search"
              type="text"
              placeholder="Search customers by name, email, or phone"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
              icon={HiOutlineSearch}
            />
            <Button type="submit">
              Search
            </Button>
          </div>
        </form>

        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}

        {deleteError && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {deleteError}
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : customers.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 mb-4">No customers found</p>
            <Link to="/customers/create">
              <Button color="primary" size="sm">
                <HiOutlinePlus className="mr-2 h-4 w-4" />
                Add Your First Customer
              </Button>
            </Link>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <Table hoverable>
                <Table.Head>
                  <Table.HeadCell>Name</Table.HeadCell>
                  <Table.HeadCell>Contact</Table.HeadCell>
                  <Table.HeadCell>Location</Table.HeadCell>
                  <Table.HeadCell>
                    <span className="sr-only">Actions</span>
                  </Table.HeadCell>
                </Table.Head>
                <Table.Body className="divide-y">
                  {customers.map((customer) => (
                    <Table.Row key={customer.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                      <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                        <Link
                          to={`/customers/details/${customer.id}`}
                          className="hover:text-primary hover:underline"
                        >
                          {customer.name}
                        </Link>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex flex-col">
                          {customer.email && (
                            <div className="flex items-center text-sm">
                              <HiOutlineMail className="mr-1 h-4 w-4 text-gray-500" />
                              <span>{customer.email}</span>
                            </div>
                          )}
                          {customer.phone && (
                            <div className="flex items-center text-sm mt-1">
                              <HiOutlinePhone className="mr-1 h-4 w-4 text-gray-500" />
                              <span>{customer.phone}</span>
                            </div>
                          )}
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        {customer.city && customer.state ? (
                          <span>{customer.city}, {customer.state}</span>
                        ) : customer.city ? (
                          <span>{customer.city}</span>
                        ) : customer.state ? (
                          <span>{customer.state}</span>
                        ) : (
                          <span className="text-gray-400">Not specified</span>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex items-center">
                          <Dropdown
                            label=""
                            dismissOnClick={true}
                            renderTrigger={() => (
                              <Button color="gray" size="xs">
                                <HiOutlineDotsVertical className="h-4 w-4" />
                              </Button>
                            )}
                          >
                            <Dropdown.Item as={Link} to={`/customers/details/${customer.id}`} className="text-blue-600 hover:bg-blue-50">
                              <HiOutlineEye className="mr-2 h-4 w-4" />
                              View Details
                            </Dropdown.Item>
                            <Dropdown.Item as={Link} to={`/customers/edit/${customer.id}`} className="text-primary hover:bg-primary/10">
                              <HiOutlinePencil className="mr-2 h-4 w-4" />
                              Edit
                            </Dropdown.Item>
                            <Dropdown.Item onClick={() => handleDeleteClick(customer)} className="text-red-600 hover:bg-red-50">
                              <HiOutlineTrash className="mr-2 h-4 w-4" />
                              Delete
                            </Dropdown.Item>
                          </Dropdown>
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </div>

            {/* Pagination */}
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={customersPerPage}
              totalItems={totalCustomers}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={(newItemsPerPage) => {
                setCustomersPerPage(newItemsPerPage);
                setCurrentPage(1); // Reset to first page when changing items per page
              }}
              itemName="customers"
            />
          </>
        )}
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)}>
        <Modal.Header>Confirm Deletion</Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-red-500" />
            <h3 className="mb-5 text-lg font-normal text-gray-500">
              Are you sure you want to delete {customerToDelete?.name}?
            </h3>
            <div className="flex justify-center gap-4">
              <Button color="failure" onClick={confirmDelete} disabled={isDeleting}>
                {isDeleting ? <Spinner size="sm" /> : 'Yes, delete'}
              </Button>
              <Button color="gray" onClick={() => setShowDeleteConfirm(false)}>
                No, cancel
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>

      {/* Customer Import Modal */}
      <GenericImport
        show={showImportModal}
        onClose={() => setShowImportModal(false)}
        onImportComplete={handleImportComplete}
        title="Import Customers"
        entityName="customers"
        previewFunction={previewCustomerImport}
        importFunction={importCustomers}
        downloadTemplateFunction={downloadCustomerImportTemplate}
      />
    </div>
  );
};

export default CustomerList;
