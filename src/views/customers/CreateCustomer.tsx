import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Alert } from 'flowbite-react';
import { HiOutlineExclamation } from 'react-icons/hi';
import CustomerForm from '../../components/customers/CustomerForm';
import { createCustomer, Customer } from '../../services/customer';
import { useOrganization } from '../../context/OrganizationContext';

const CreateCustomer = () => {
  const { currentOrganization } = useOrganization();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (customerData: Partial<Customer>) => {
    if (!currentOrganization) {
      setError('No organization selected');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const { customer, error: createError } = await createCustomer(
        currentOrganization.id,
        customerData as Omit<Customer, 'id' | 'organization_id' | 'created_at' | 'updated_at'>
      );

      if (createError) {
        setError(createError);
      } else if (customer) {
        // Navigate to the customer list page
        navigate('/customers');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while creating the customer');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!currentOrganization) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">No Organization Selected</h3>
            <p>
              Please select an organization before creating a customer.
            </p>
          </Alert>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Create New Customer</h1>
        </div>

        <CustomerForm
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          error={error || undefined}
        />
      </Card>
    </div>
  );
};

export default CreateCustomer;
