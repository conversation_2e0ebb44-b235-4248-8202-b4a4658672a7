import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { <PERSON>, Alert, Spinner } from 'flowbite-react';
import { HiOutlineExclamation } from 'react-icons/hi';
import CustomerForm from '../../components/customers/CustomerForm';
import { getCustomerById, updateCustomer, Customer } from '../../services/customer';
import { useOrganization } from '../../context/OrganizationContext';

const EditCustomer = () => {
  const { id } = useParams<{ id: string }>();
  const { currentOrganization } = useOrganization();
  const navigate = useNavigate();
  
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomer = async () => {
      if (!currentOrganization || !id) return;

      setIsLoading(true);
      setError(null);

      try {
        const { customer: customerData, error: fetchError } = await getCustomerById(
          currentOrganization.id,
          id
        );

        if (fetchError) {
          setError(fetchError);
        } else if (customerData) {
          setCustomer(customerData);
        } else {
          setError('Customer not found');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching the customer');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomer();
  }, [currentOrganization, id]);

  const handleSubmit = async (customerData: Partial<Customer>) => {
    if (!currentOrganization || !id) {
      setError('No organization selected or customer ID missing');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const { customer: updatedCustomer, error: updateError } = await updateCustomer(
        currentOrganization.id,
        id,
        customerData
      );

      if (updateError) {
        setError(updateError);
      } else if (updatedCustomer) {
        // Navigate to the customer list page
        navigate('/customers');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while updating the customer');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!currentOrganization) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">No Organization Selected</h3>
            <p>
              Please select an organization before editing a customer.
            </p>
          </Alert>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        </Card>
      </div>
    );
  }

  if (error && !customer) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <Alert color="failure" icon={HiOutlineExclamation}>
            <h3 className="text-lg font-medium">Error</h3>
            <p>{error}</p>
          </Alert>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Edit Customer</h1>
        </div>

        {customer && (
          <CustomerForm
            initialData={customer}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            error={error || undefined}
          />
        )}
      </Card>
    </div>
  );
};

export default EditCustomer;
