import React, { useState } from 'react';
import { Alert } from 'flowbite-react';
import { HiOutlineCheck, HiOutlineInformationCircle } from 'react-icons/hi';
import TimeClock from '../../components/faceRecognition/TimeClock';

const TimeClockPage: React.FC = () => {
  console.log('🔥 TimeClockPage component mounting...');

  const [recentActivity, setRecentActivity] = useState<{
    employeeId: string;
    type: 'clock_in' | 'clock_out';
    timestamp: Date;
  } | null>(null);

  const handleTimeEntryCreated = (employeeId: string, type: 'clock_in' | 'clock_out') => {
    console.log(`✅ Time entry created: ${type} for employee ${employeeId}`);

    // Update recent activity
    setRecentActivity({
      employeeId,
      type,
      timestamp: new Date(),
    });

    // Clear recent activity after 10 seconds
    setTimeout(() => {
      setRecentActivity(null);
    }, 10000);

    // You can add additional logic here, such as:
    // - Showing a success notification
    // - Updating a dashboard
    // - Sending notifications to managers
    // - Logging the event
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Recent Activity Banner */}
      {recentActivity && (
        <div className="fixed top-4 right-4 z-50 max-w-sm">
          <Alert color="success" className="shadow-lg">
            <HiOutlineCheck className="h-4 w-4" />
            <div>
              <div className="font-medium">Time Entry Recorded</div>
              <div className="text-sm">
                Employee {recentActivity.employeeId} successfully {' '}
                {recentActivity.type === 'clock_in' ? 'clocked in' : 'clocked out'} at{' '}
                {recentActivity.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </Alert>
        </div>
      )}

      {/* Help Banner */}
      <div className="bg-blue-50 border-b border-blue-200 p-4">
        <div className="max-w-4xl mx-auto">
          <Alert color="info" className="border-0 bg-transparent p-0">
            <HiOutlineInformationCircle className="h-5 w-5" />
            <div className="ml-3">
              <div className="font-medium text-blue-800">Face Recognition Time Clock</div>
              <div className="text-sm text-blue-700">
                Look at the camera to automatically clock in/out, or use the PIN option for manual entry.
                Make sure your face is well-lit and clearly visible to the camera.
              </div>
            </div>
          </Alert>
        </div>
      </div>

      {/* Main Time Clock */}
      <TimeClock onTimeEntryCreated={handleTimeEntryCreated} />
    </div>
  );
};

export default TimeClockPage;
