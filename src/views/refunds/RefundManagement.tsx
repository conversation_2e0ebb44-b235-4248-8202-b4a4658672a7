import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Badge, Spinner, Alert, Modal, TextInput, Select, Tabs } from 'flowbite-react';
import { HiPlus, HiEye, HiCheck, HiX, HiRefresh, HiDocumentText, HiOutlinePrinter, HiOutlineDocumentDuplicate, HiOutlineDownload } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { RefundService } from '../../services/refund';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import RefundProcessor from '../../components/refund/RefundProcessor';
import CreditMemoGenerator from '../../components/refund/CreditMemoGenerator';
import ReceiptAnnotation from '../../components/refund/ReceiptAnnotation';
import RefundAnalytics from '../../components/refund/RefundAnalytics';
import Pagination from '../../components/common/Pagination';
import { exportRefunds } from '../../utils/excelExport';

import {
  RefundWithDetails,
  RefundFilters,
  RefundStatus,
  RefundType,
  RefundMethod,
  RefundSummary
} from '../../types/refund.types';

const RefundManagement: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const formatCurrency = useCurrencyFormatter();

  // State management
  const [refunds, setRefunds] = useState<RefundWithDetails[]>([]);
  const [summary, setSummary] = useState<RefundSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('list');

  // Modal states
  const [showCreditMemoModal, setShowCreditMemoModal] = useState(false);
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [selectedRefundForMemo, setSelectedRefundForMemo] = useState<RefundWithDetails | null>(null);
  const [selectedSaleForReceipt, setSelectedSaleForReceipt] = useState<string | null>(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Filters
  const [filters, setFilters] = useState<RefundFilters>({});

  // Modals
  const [showProcessorModal, setShowProcessorModal] = useState(false);
  const [selectedRefund, setSelectedRefund] = useState<RefundWithDetails | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Load refunds
  const loadRefunds = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    try {
      const result = await RefundService.getRefunds(currentOrganization.id, {
        limit: itemsPerPage,
        offset: (currentPage - 1) * itemsPerPage,
        filters,
        sortBy: 'created_at',
        sortOrder: 'desc'
      });

      if (result.success) {
        setRefunds(result.data || []);
        setTotalCount(result.total_count || 0);
      } else {
        setError(result.error || 'Failed to load refunds');
      }
    } catch (err) {
      setError('Failed to load refunds');
    } finally {
      setLoading(false);
    }
  };

  // Load summary
  const loadSummary = async () => {
    if (!currentOrganization) return;

    try {
      const summaryData = await RefundService.getRefundSummary(currentOrganization.id);
      setSummary(summaryData);
    } catch (err) {
      console.error('Failed to load refund summary:', err);
    }
  };

  // Process refund approval
  const processRefund = async (refundId: string, approved: boolean, notes?: string) => {
    if (!currentOrganization || !user) return;

    setLoading(true);
    try {
      const result = await RefundService.processRefund({
        refund_id: refundId,
        approved,
        approval_notes: notes,
        processed_by: user.id
      });

      if (result.success) {
        await loadRefunds();
        await loadSummary();
        setSelectedRefund(null);
        setShowDetailsModal(false);
      } else {
        setError(result.error || 'Failed to process refund');
      }
    } catch (err) {
      setError('Failed to process refund');
    } finally {
      setLoading(false);
    }
  };

  // New action handlers
  const handleGenerateCreditMemo = (refund: RefundWithDetails) => {
    console.log('Credit memo for refund:', refund);
    setSelectedRefundForMemo(refund);
    setShowCreditMemoModal(true);
  };

  const handleViewAnnotatedReceipt = (refund: RefundWithDetails) => {
    console.log('Refund object:', refund);
    console.log('Original sale ID:', refund.original_sale_id);
    console.log('Original sale object:', refund.original_sale);

    // Try to get the sale ID from either the direct field or the nested object
    const saleId = refund.original_sale_id || refund.original_sale?.id;

    if (!saleId) {
      console.error('No sale ID found in refund object');
      return;
    }

    setSelectedSaleForReceipt(saleId);
    setShowReceiptModal(true);
  };

  // Effects
  useEffect(() => {
    loadRefunds();
  }, [currentOrganization, currentPage, itemsPerPage, filters]);

  useEffect(() => {
    loadSummary();
  }, [currentOrganization]);

  // Status badge color
  const getStatusColor = (status: RefundStatus) => {
    switch (status) {
      case RefundStatus.PENDING: return 'warning';
      case RefundStatus.APPROVED: return 'info';
      case RefundStatus.PROCESSED: return 'success';
      case RefundStatus.REJECTED: return 'failure';
      case RefundStatus.CANCELLED: return 'gray';
      default: return 'gray';
    }
  };

  // Type badge color
  const getTypeColor = (type: RefundType) => {
    switch (type) {
      case RefundType.FULL: return 'blue';
      case RefundType.PARTIAL: return 'purple';
      case RefundType.EXCHANGE: return 'green';
      case RefundType.STORE_CREDIT: return 'yellow';
      default: return 'gray';
    }
  };

  const totalPages = Math.ceil(totalCount / itemsPerPage);

  // Export refunds to Excel
  const handleExportRefunds = () => {
    if (refunds.length === 0) {
      setError('No refunds to export');
      return;
    }
    exportRefunds(refunds);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Refund Management</h1>
          <p className="text-gray-600">Process and manage customer refunds</p>
        </div>
        <div className="flex gap-3">
          <Button onClick={handleExportRefunds} color="light">
            <HiOutlineDownload className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={loadRefunds} disabled={loading} color="light">
            <HiRefresh className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={() => setShowProcessorModal(true)}>
            <HiPlus className="h-4 w-4 mr-2" />
            New Refund
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Alert color="failure" onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 mr-4">
                <HiDocumentText className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h5 className="text-gray-500 text-sm">Total Refunds</h5>
                <p className="text-2xl font-bold">{summary.total_refunds}</p>
                <p className="text-sm text-gray-600">{formatCurrency(summary.total_amount)}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 mr-4">
                <HiDocumentText className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <h5 className="text-gray-500 text-sm">Pending Approval</h5>
                <p className="text-2xl font-bold text-yellow-600">{summary.pending_approvals}</p>
                <p className="text-sm text-gray-600">{formatCurrency(summary.pending_amount)}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 mr-4">
                <HiCheck className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h5 className="text-gray-500 text-sm">Processed Today</h5>
                <p className="text-2xl font-bold text-green-600">{summary.processed_today}</p>
                <p className="text-sm text-gray-600">{formatCurrency(summary.processed_amount_today)}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 mr-4">
                <HiDocumentText className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h5 className="text-gray-500 text-sm">Avg Refund</h5>
                <p className="text-2xl font-bold text-purple-600">
                  {formatCurrency(summary.total_refunds > 0 ? summary.total_amount / summary.total_refunds : 0)}
                </p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Card>
        <Tabs
          aria-label="Refund tabs"
          onActiveTabChange={(tab) => setActiveTab(tab === 0 ? 'list' : 'analytics')}
        >
          <Tabs.Item title="Refund List" active={activeTab === 'list'}>
            {/* Filters */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
              <Select
                value={filters.status || ''}
                onChange={(e) => setFilters({ ...filters, status: e.target.value as RefundStatus || undefined })}
              >
                <option value="">All Statuses</option>
                <option value={RefundStatus.PENDING}>Pending</option>
                <option value={RefundStatus.APPROVED}>Approved</option>
                <option value={RefundStatus.PROCESSED}>Processed</option>
                <option value={RefundStatus.REJECTED}>Rejected</option>
                <option value={RefundStatus.CANCELLED}>Cancelled</option>
              </Select>

              <Select
                value={filters.refund_type || ''}
                onChange={(e) => setFilters({ ...filters, refund_type: e.target.value as RefundType || undefined })}
              >
                <option value="">All Types</option>
                <option value={RefundType.FULL}>Full Refund</option>
                <option value={RefundType.PARTIAL}>Partial Refund</option>
                <option value={RefundType.EXCHANGE}>Exchange</option>
                <option value={RefundType.STORE_CREDIT}>Store Credit</option>
              </Select>

              <Select
                value={filters.refund_method || ''}
                onChange={(e) => setFilters({ ...filters, refund_method: e.target.value as RefundMethod || undefined })}
              >
                <option value="">All Methods</option>
                <option value={RefundMethod.CASH}>Cash</option>
                <option value={RefundMethod.CARD}>Card</option>
                <option value={RefundMethod.STORE_CREDIT}>Store Credit</option>
                <option value={RefundMethod.ORIGINAL_PAYMENT}>Original Payment</option>
              </Select>

              <TextInput
                type="date"
                value={filters.date_from || ''}
                onChange={(e) => setFilters({ ...filters, date_from: e.target.value || undefined })}
                placeholder="From Date"
              />

              <TextInput
                placeholder="Search refunds..."
                value={filters.search || ''}
                onChange={(e) => setFilters({ ...filters, search: e.target.value || undefined })}
              />
            </div>

            {/* Refunds Table */}
            {loading ? (
              <div className="flex justify-center py-8">
                <Spinner size="lg" />
              </div>
            ) : (
              <>
                <Table>
                  <Table.Head>
                    <Table.HeadCell>Refund #</Table.HeadCell>
                    <Table.HeadCell>Original Sale</Table.HeadCell>
                    <Table.HeadCell>Customer</Table.HeadCell>
                    <Table.HeadCell>Type</Table.HeadCell>
                    <Table.HeadCell>Amount</Table.HeadCell>
                    <Table.HeadCell>Status</Table.HeadCell>
                    <Table.HeadCell>Date</Table.HeadCell>
                    <Table.HeadCell>Actions</Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {refunds.map((refund) => (
                      <Table.Row key={refund.id}>
                        <Table.Cell className="font-medium">
                          {refund.refund_number}
                        </Table.Cell>
                        <Table.Cell>
                          {refund.original_sale?.invoice_number || 'N/A'}
                        </Table.Cell>
                        <Table.Cell>
                          {refund.original_sale?.customer?.name || 'Walk-in Customer'}
                        </Table.Cell>
                        <Table.Cell>
                          <Badge color={getTypeColor(refund.refund_type as RefundType)}>
                            {refund.refund_type.replace('_', ' ')}
                          </Badge>
                        </Table.Cell>
                        <Table.Cell className="font-medium">
                          {formatCurrency(refund.total_amount)}
                        </Table.Cell>
                        <Table.Cell>
                          <Badge color={getStatusColor(refund.status as RefundStatus)}>
                            {refund.status}
                          </Badge>
                        </Table.Cell>
                        <Table.Cell>
                          {new Date(refund.created_at).toLocaleDateString()}
                        </Table.Cell>
                        <Table.Cell>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              color="light"
                              onClick={() => {
                                setSelectedRefund(refund);
                                setShowDetailsModal(true);
                              }}
                            >
                              <HiEye className="h-4 w-4" />
                            </Button>
                            {refund.status === 'pending' && (
                              <>
                                <Button
                                  size="sm"
                                  color="success"
                                  onClick={() => processRefund(refund.id, true)}
                                >
                                  <HiCheck className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  color="failure"
                                  onClick={() => processRefund(refund.id, false)}
                                >
                                  <HiX className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                            {refund.status === 'processed' && (
                              <>
                                <Button
                                  size="sm"
                                  color="info"
                                  onClick={() => handleGenerateCreditMemo(refund)}
                                  title="Generate Credit Memo"
                                >
                                  <HiOutlinePrinter className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  color="purple"
                                  onClick={() => handleViewAnnotatedReceipt(refund)}
                                  title="View Annotated Receipt"
                                >
                                  <HiOutlineDocumentDuplicate className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>

                {refunds.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No refunds found. Create your first refund to get started.
                  </div>
                )}

                {/* Pagination */}
                {refunds.length > 0 && (
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    itemsPerPage={itemsPerPage}
                    totalItems={totalCount}
                    onPageChange={setCurrentPage}
                    onItemsPerPageChange={(newItemsPerPage) => {
                      setItemsPerPage(newItemsPerPage);
                      setCurrentPage(1);
                    }}
                    itemName="refunds"
                  />
                )}
              </>
            )}
          </Tabs.Item>

          <Tabs.Item title="Analytics" active={activeTab === 'analytics'}>
            <RefundAnalytics />
          </Tabs.Item>
        </Tabs>
      </Card>

      {/* New Refund Modal */}
      <Modal show={showProcessorModal} onClose={() => setShowProcessorModal(false)} size="7xl">
        <Modal.Header>Process New Refund</Modal.Header>
        <Modal.Body>
          <RefundProcessor
            onRefundCreated={(refund) => {
              setShowProcessorModal(false);
              loadRefunds();
              loadSummary();
            }}
            onClose={() => setShowProcessorModal(false)}
          />
        </Modal.Body>
      </Modal>

      {/* Refund Details Modal */}
      <Modal show={showDetailsModal} onClose={() => setShowDetailsModal(false)} size="4xl">
        <Modal.Header>Refund Details</Modal.Header>
        <Modal.Body>
          {selectedRefund && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Refund Number</label>
                  <p className="text-sm">{selectedRefund.refund_number}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <Badge color={getStatusColor(selectedRefund.status as RefundStatus)}>
                    {selectedRefund.status}
                  </Badge>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Type</label>
                  <Badge color={getTypeColor(selectedRefund.refund_type as RefundType)}>
                    {selectedRefund.refund_type.replace('_', ' ')}
                  </Badge>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Amount</label>
                  <p className="text-sm font-medium">{formatCurrency(selectedRefund.total_amount)}</p>
                </div>
              </div>

              {selectedRefund.reason_notes && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Reason Notes</label>
                  <p className="text-sm">{selectedRefund.reason_notes}</p>
                </div>
              )}

              {selectedRefund.refund_items && selectedRefund.refund_items.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Refunded Items</label>
                  <Table>
                    <Table.Head>
                      <Table.HeadCell>Product</Table.HeadCell>
                      <Table.HeadCell>Quantity</Table.HeadCell>
                      <Table.HeadCell>Unit Price</Table.HeadCell>
                      <Table.HeadCell>Total</Table.HeadCell>
                    </Table.Head>
                    <Table.Body>
                      {selectedRefund.refund_items.map((item) => (
                        <Table.Row key={item.id}>
                          <Table.Cell>{item.product?.name || 'Unknown Product'}</Table.Cell>
                          <Table.Cell>{item.quantity}</Table.Cell>
                          <Table.Cell>{formatCurrency(item.unit_price)}</Table.Cell>
                          <Table.Cell>{formatCurrency(item.total_price)}</Table.Cell>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowDetailsModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Credit Memo Modal */}
      <Modal
        show={showCreditMemoModal}
        onClose={() => {
          setShowCreditMemoModal(false);
          setSelectedRefundForMemo(null);
        }}
        size="6xl"
      >
        <Modal.Header>Credit Memo Generator</Modal.Header>
        <Modal.Body>
          {selectedRefundForMemo && (
            <CreditMemoGenerator
              refund={selectedRefundForMemo}
              onClose={() => {
                setShowCreditMemoModal(false);
                setSelectedRefundForMemo(null);
              }}
            />
          )}
        </Modal.Body>
      </Modal>

      {/* Annotated Receipt Modal */}
      <Modal
        show={showReceiptModal}
        onClose={() => {
          setShowReceiptModal(false);
          setSelectedSaleForReceipt(null);
        }}
        size="5xl"
      >
        <Modal.Header>Annotated Receipt</Modal.Header>
        <Modal.Body>
          {selectedSaleForReceipt && (
            <ReceiptAnnotation
              saleId={selectedSaleForReceipt}
              onClose={() => {
                setShowReceiptModal(false);
                setSelectedSaleForReceipt(null);
              }}
            />
          )}
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default RefundManagement;
