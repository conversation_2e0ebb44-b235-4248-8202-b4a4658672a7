.page-wrapper {
    margin-left: 256px;
    transition: .2s ease-in;
    margin-right: 0px;

}

.simplebar-scrollbar:before {
    background: var(--color-border) !important;
  }
  [data-simplebar], .simplebar-offset, .simplebar-content,.simplebar-wrapper,.simplebar-mask,.simplebar-content-wrapper{
    outline:none !important;
    border:none !important
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

@media screen and (max-width:1280px) {
    .page-wrapper{
        margin-left: 0px;
    }
}

[data-sidebar-type="mini-sidebar"] .page-wrapper-sub {
    margin-left: 15px;
}
