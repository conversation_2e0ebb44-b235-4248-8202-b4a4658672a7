/* Custom styles for React Select to match Flowbite UI */

.react-select-container {
  font-family: inherit;
}

.react-select__control {
  min-height: 38px !important;
  border-radius: 0.5rem !important;
  border: 1px solid rgb(209, 213, 219) !important;
}

.react-select__control:hover {
  border-color: rgb(156, 163, 175) !important;
}

.react-select__control--is-focused {
  border-color: rgb(59, 130, 246) !important;
  box-shadow: 0 0 0 1px rgb(59, 130, 246) !important;
}

.react-select__menu {
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.react-select__option {
  padding: 0.5rem 1rem !important;
  cursor: pointer !important;
}

.react-select__option--is-selected {
  background-color: rgb(59, 130, 246) !important;
  color: white !important;
}

.react-select__option--is-focused:not(.react-select__option--is-selected) {
  background-color: rgb(243, 244, 246) !important;
}

.react-select__indicator {
  color: rgb(107, 114, 128) !important;
}

.react-select__indicator-separator {
  background-color: rgb(209, 213, 219) !important;
}

.react-select__placeholder {
  color: rgb(156, 163, 175) !important;
}

.react-select__single-value {
  color: rgb(17, 24, 39) !important;
}

.react-select__clear-indicator:hover {
  color: rgb(239, 68, 68) !important;
}

/* Make sure the dropdown appears above other elements */
.react-select__menu {
  z-index: 99999 !important;
}
