import { Router<PERSON>rovider } from "react-router";
import { Flowbite, ThemeModeScript, Alert } from 'flowbite-react';
import customTheme from './utils/theme/custom-theme';
import router from "./routes/Router";
import { AuthProvider } from "./context/AuthContext";
import { OrganizationProvider } from "./context/OrganizationContext";
import { OrganizationSettingsProvider } from "./context/OrganizationSettingsContext";
import { PermissionProvider } from "./context/PermissionContext";
import { UomProvider } from "./context/UomContext";
import { ProductUomProvider } from "./context/ProductUomContext";
import { LoyaltyProvider } from "./context/LoyaltyContext";
import { ChatNotificationProvider } from "./context/ChatNotificationContext";
// ConditionalChatProvider removed - now loaded only on chat routes
import { useEffect, useState } from "react";
import { checkSupabaseConnection } from "./utils/checkSupabaseConnection";
import { Toaster } from 'react-hot-toast';

function App() {
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'error'>('checking');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const checkConnection = async () => {
      try {
        const isConnected = await checkSupabaseConnection();
        if (isConnected) {
          setConnectionStatus('connected');
        } else {
          setConnectionStatus('error');
          setErrorMessage('Could not connect to Supabase. Please check your configuration.');
        }
      } catch (error) {
        console.error('Connection check error:', error);
        setConnectionStatus('error');
        setErrorMessage('An error occurred while checking the Supabase connection.');
      }
    };

    checkConnection();
  }, []);

  return (
    <>
      <ThemeModeScript />
      <Toaster position="top-right" toastOptions={{
        duration: 3000,
        style: {
          background: '#363636',
          color: '#fff',
        },
      }} />
      <Flowbite theme={{ theme: customTheme }}>
        {connectionStatus === 'checking' && (
          <div className="flex justify-center items-center h-screen">
            <p>Connecting to AIRyx server...</p>
          </div>
        )}

        {connectionStatus === 'error' && (
          <div className="flex justify-center items-center h-screen">
            <div className="w-full max-w-md p-4">
              <Alert color="failure">
                <h3 className="text-lg font-medium">Connection Error</h3>
                <p>{errorMessage}</p>
                <p className="mt-2">Please check your AIRyx server connection.</p>
              </Alert>
            </div>
          </div>
        )}

        {connectionStatus === 'connected' && (
          <AuthProvider>
            <OrganizationProvider>
              <OrganizationSettingsProvider>
                <ChatNotificationProvider>
                  <UomProvider>
                    <ProductUomProvider>
                      <LoyaltyProvider>
                        <PermissionProvider>
                          <RouterProvider router={router} />
                        </PermissionProvider>
                      </LoyaltyProvider>
                    </ProductUomProvider>
                  </UomProvider>
                </ChatNotificationProvider>
              </OrganizationSettingsProvider>
            </OrganizationProvider>
          </AuthProvider>
        )}
      </Flowbite>
    </>
  );
}

export default App;
