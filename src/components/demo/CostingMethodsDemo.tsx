import React, { useState } from 'react';
import { Card, Button, Select, Badge } from 'flowbite-react';
import { 
  CostingService, 
  CostingMethodType, 
  CostingMethodFactory,
  SaleItem 
} from '../../services/costingMethods';
import { useOrganization } from '../../context/OrganizationContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

// =====================================================
// COSTING METHODS DEMO COMPONENT
// =====================================================

const CostingMethodsDemo: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();
  
  const [selectedMethod, setSelectedMethod] = useState<CostingMethodType>(CostingMethodType.FIFO);
  const [isCalculating, setIsCalculating] = useState(false);
  const [results, setResults] = useState<any[]>([]);
  const [comparisonResults, setComparisonResults] = useState<any[]>([]);

  // Sample sale items for demonstration
  const sampleSaleItems: SaleItem[] = [
    {
      id: 'demo-sale-1',
      productId: 'demo-product-1',
      quantity: 10,
      baseQuantity: 10,
      saleDate: new Date('2024-01-15T10:00:00Z')
    },
    {
      id: 'demo-sale-2',
      productId: 'demo-product-1',
      quantity: 5,
      baseQuantity: 5,
      saleDate: new Date('2024-01-15T14:00:00Z')
    },
    {
      id: 'demo-sale-3',
      productId: 'demo-product-2',
      quantity: 8,
      baseQuantity: 8,
      saleDate: new Date('2024-01-15T16:00:00Z')
    }
  ];

  // Calculate COGS with selected method
  const calculateWithMethod = async () => {
    if (!currentOrganization) return;

    setIsCalculating(true);
    try {
      const result = await CostingService.calculateCOGS(
        currentOrganization.id,
        sampleSaleItems,
        selectedMethod
      );

      setResults([{
        method: selectedMethod,
        result,
        timestamp: new Date()
      }]);

    } catch (error) {
      console.error('Error calculating COGS:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  // Compare all methods
  const compareAllMethods = async () => {
    if (!currentOrganization) return;

    setIsCalculating(true);
    const methods = [
      CostingMethodType.SIMPLE,
      CostingMethodType.FIFO,
      CostingMethodType.LIFO,
      CostingMethodType.WEIGHTED_AVERAGE
    ];

    const comparisonData = [];

    try {
      for (const method of methods) {
        const result = await CostingService.calculateCOGS(
          currentOrganization.id,
          sampleSaleItems,
          method
        );

        comparisonData.push({
          method,
          result,
          methodInfo: CostingMethodFactory.getAvailableMethods().find(m => m.value === method)
        });
      }

      setComparisonResults(comparisonData);

    } catch (error) {
      console.error('Error comparing methods:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const getMethodBadgeColor = (method: CostingMethodType) => {
    switch (method) {
      case CostingMethodType.SIMPLE: return 'gray';
      case CostingMethodType.FIFO: return 'blue';
      case CostingMethodType.LIFO: return 'purple';
      case CostingMethodType.WEIGHTED_AVERAGE: return 'green';
      default: return 'gray';
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          🎯 Advanced Costing Methods Demo
        </h1>
        <p className="text-gray-600">
          Test and compare different COGS calculation methods
        </p>
      </div>

      {/* Controls */}
      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-gray-700">
              Select Costing Method:
            </label>
            <Select
              value={selectedMethod}
              onChange={(e) => setSelectedMethod(e.target.value as CostingMethodType)}
              className="w-64"
            >
              {CostingMethodFactory.getAvailableMethods().map(method => (
                <option key={method.value} value={method.value}>
                  {method.label}
                </option>
              ))}
            </Select>
          </div>
          
          <div className="flex gap-3">
            <Button 
              onClick={calculateWithMethod}
              disabled={isCalculating || !currentOrganization}
              color="blue"
            >
              {isCalculating ? 'Calculating...' : 'Calculate COGS'}
            </Button>
            <Button 
              onClick={compareAllMethods}
              disabled={isCalculating || !currentOrganization}
              color="purple"
            >
              {isCalculating ? 'Comparing...' : 'Compare All Methods'}
            </Button>
          </div>
        </div>
      </Card>

      {/* Sample Data Info */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">📦 Sample Sale Items</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {sampleSaleItems.map((item, index) => (
            <div key={item.id} className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm font-medium">Sale Item {index + 1}</div>
              <div className="text-xs text-gray-600 mt-1">
                Product: {item.productId}<br/>
                Quantity: {item.quantity}<br/>
                Date: {item.saleDate.toLocaleDateString()}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Single Method Results */}
      {results.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">📊 Calculation Results</h3>
          {results.map((result, index) => (
            <div key={index} className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <Badge color={getMethodBadgeColor(result.method)} size="lg">
                  {CostingMethodFactory.getAvailableMethods()
                    .find(m => m.value === result.method)?.label}
                </Badge>
                <span className="text-sm text-gray-500">
                  {result.timestamp.toLocaleTimeString()}
                </span>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <div className="text-sm text-gray-600">Total COGS</div>
                  <div className="text-xl font-bold text-blue-600">
                    {formatWithCurrency(result.result.totalCost)}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Average Cost</div>
                  <div className="text-lg font-semibold">
                    {formatWithCurrency(result.result.averageCost)}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Layers Used</div>
                  <div className="text-lg font-semibold">
                    {result.result.layersUsed.length}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Method</div>
                  <div className="text-lg font-semibold capitalize">
                    {result.result.method}
                  </div>
                </div>
              </div>

              {/* Layer Details */}
              {result.result.layersUsed.length > 0 && (
                <div className="mt-4">
                  <div className="text-sm font-medium text-gray-700 mb-2">
                    Cost Layers:
                  </div>
                  <div className="space-y-1">
                    {result.result.layersUsed.map((layer: any, layerIndex: number) => (
                      <div key={layerIndex} className="text-xs bg-white p-2 rounded">
                        Layer {layerIndex + 1}: {layer.quantity} units @ {formatWithCurrency(layer.unitCost)} = {formatWithCurrency(layer.totalCost)}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </Card>
      )}

      {/* Comparison Results */}
      {comparisonResults.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">🔍 Method Comparison</h3>
          
          {/* Summary Table */}
          <div className="overflow-x-auto mb-6">
            <table className="w-full text-sm">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left">Method</th>
                  <th className="px-4 py-2 text-right">Total COGS</th>
                  <th className="px-4 py-2 text-right">Avg Cost</th>
                  <th className="px-4 py-2 text-right">Layers</th>
                  <th className="px-4 py-2 text-center">Difference</th>
                </tr>
              </thead>
              <tbody>
                {comparisonResults.map((item, index) => {
                  const baseline = comparisonResults[0]?.result.totalCost || 0;
                  const difference = item.result.totalCost - baseline;
                  const percentage = baseline > 0 ? (difference / baseline) * 100 : 0;
                  
                  return (
                    <tr key={index} className="border-t">
                      <td className="px-4 py-2">
                        <Badge color={getMethodBadgeColor(item.method)}>
                          {item.methodInfo?.label}
                        </Badge>
                      </td>
                      <td className="px-4 py-2 text-right font-semibold">
                        {formatWithCurrency(item.result.totalCost)}
                      </td>
                      <td className="px-4 py-2 text-right">
                        {formatWithCurrency(item.result.averageCost)}
                      </td>
                      <td className="px-4 py-2 text-right">
                        {item.result.layersUsed.length}
                      </td>
                      <td className="px-4 py-2 text-center">
                        {index === 0 ? (
                          <span className="text-gray-500">Baseline</span>
                        ) : (
                          <span className={difference >= 0 ? 'text-red-600' : 'text-green-600'}>
                            {difference >= 0 ? '+' : ''}{formatWithCurrency(difference)}
                            <br/>
                            <span className="text-xs">
                              ({percentage >= 0 ? '+' : ''}{percentage.toFixed(1)}%)
                            </span>
                          </span>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Method Descriptions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {comparisonResults.map((item, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Badge color={getMethodBadgeColor(item.method)}>
                    {item.methodInfo?.label}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">
                  {item.methodInfo?.description}
                </p>
                <div className="mt-2 text-xs text-gray-500">
                  Total: {formatWithCurrency(item.result.totalCost)} | 
                  Layers: {item.result.layersUsed.length}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Instructions */}
      <Card className="p-6 bg-blue-50">
        <h3 className="text-lg font-semibold mb-3">💡 How to Use</h3>
        <div className="space-y-2 text-sm text-gray-700">
          <p><strong>1. Single Method Test:</strong> Select a costing method and click "Calculate COGS" to see results for that method.</p>
          <p><strong>2. Compare All Methods:</strong> Click "Compare All Methods" to see how different methods affect COGS calculation.</p>
          <p><strong>3. Switch Methods:</strong> Use the dropdown in the Sales Summary to change the active costing method.</p>
          <p><strong>4. Real Data:</strong> This demo uses sample data. In production, it uses actual inventory transactions.</p>
        </div>
      </Card>
    </div>
  );
};

export default CostingMethodsDemo;
