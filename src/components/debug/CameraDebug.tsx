import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'flowbite-react';

const CameraDebug: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  
  const [status, setStatus] = useState<string>('Not started');
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `${timestamp}: ${message}`]);
    console.log(message);
  };

  const testCamera = async () => {
    setError(null);
    setLogs([]);
    setStatus('Testing...');
    
    try {
      addLog('🚀 Starting camera test...');
      
      // Check browser support
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia not supported');
      }
      addLog('✅ getUserMedia API available');
      
      // Test camera permissions
      if (navigator.permissions) {
        try {
          const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });
          addLog(`📷 Camera permission status: ${permission.state}`);
        } catch (e) {
          addLog('⚠️ Could not check camera permissions');
        }
      }
      
      // Try to get camera stream
      addLog('📷 Requesting camera access...');
      const constraints: MediaStreamConstraints = {
        video: {
          facingMode: 'user',
          width: { ideal: 640, min: 320 },
          height: { ideal: 480, min: 240 }
        },
        audio: false
      };
      
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      addLog('✅ Camera stream obtained');
      
      streamRef.current = stream;
      
      // Check stream details
      const tracks = stream.getTracks();
      addLog(`📹 Stream has ${tracks.length} tracks:`);
      tracks.forEach((track, i) => {
        addLog(`  Track ${i}: ${track.kind}, enabled: ${track.enabled}, state: ${track.readyState}`);
      });
      
      // Assign to video element
      if (videoRef.current) {
        addLog('🎥 Assigning stream to video element...');
        const video = videoRef.current;
        
        video.srcObject = stream;
        video.muted = true;
        video.playsInline = true;
        video.autoplay = true;
        
        video.onloadedmetadata = () => {
          addLog(`📹 Video metadata loaded: ${video.videoWidth}x${video.videoHeight}`);
        };
        
        video.oncanplay = () => {
          addLog('📹 Video can play');
        };
        
        video.onplaying = () => {
          addLog('📹 Video playing');
          setStatus('Success - Video playing');
        };
        
        video.onerror = (e) => {
          addLog(`❌ Video error: ${e}`);
        };
        
        try {
          await video.play();
          addLog('✅ Video play() successful');
        } catch (playError: any) {
          addLog(`⚠️ Video play() failed: ${playError.message}`);
          if (playError.name === 'NotAllowedError') {
            setError('Video autoplay blocked - click the video to start');
          }
        }
      }
      
    } catch (err: any) {
      addLog(`❌ Camera test failed: ${err.message}`);
      setError(err.message);
      setStatus('Failed');
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
      addLog('🛑 Camera stopped');
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    setStatus('Stopped');
  };

  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <Card>
        <h1 className="text-2xl font-bold mb-4">Camera Debug Tool</h1>
        
        <div className="mb-4">
          <p className="text-lg">Status: <span className="font-mono">{status}</span></p>
        </div>
        
        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}
        
        <div className="mb-6">
          <div className="aspect-video bg-black rounded-lg overflow-hidden mb-4">
            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              autoPlay
              muted
              playsInline
              onClick={() => videoRef.current?.play()}
            />
          </div>
        </div>
        
        <div className="flex gap-4 mb-6">
          <Button onClick={testCamera} disabled={status === 'Testing...'}>
            Test Camera
          </Button>
          <Button color="gray" onClick={stopCamera}>
            Stop Camera
          </Button>
        </div>
        
        <div className="bg-gray-100 p-4 rounded-lg max-h-60 overflow-y-auto">
          <h3 className="font-bold mb-2">Debug Logs:</h3>
          {logs.length === 0 ? (
            <p className="text-gray-500 italic">No logs yet - click "Test Camera" to start</p>
          ) : (
            <div className="font-mono text-sm">
              {logs.map((log, i) => (
                <div key={i} className="mb-1">{log}</div>
              ))}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default CameraDebug;
