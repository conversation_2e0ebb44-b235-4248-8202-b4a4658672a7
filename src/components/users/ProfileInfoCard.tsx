import { useState, useRef } from 'react';
import {
  Card,
  Avatar,
  Badge,
  Button,
  Tooltip,
  Spinner
} from 'flowbite-react';
import {
  HiOutlineCamera,
  HiOutlineMail,
  HiOutlinePhone,
  HiOutlineOfficeBuilding,
  HiOutlineLocationMarker,
  HiOutlinePencil
} from 'react-icons/hi';

interface ProfileInfoCardProps {
  profile: {
    firstName: string;
    lastName: string;
    email: string;
    role: string;
    avatarUrl: string;
    jobTitle: string;
    department: string;
    phoneNumber: string;
    location: string;
    bio: string;
    joinDate: string;
  };
  onEditClick: () => void;
  onAvatarUpload: (file: File) => Promise<void>;
}

const ProfileInfoCard: React.FC<ProfileInfoCardProps> = ({
  profile,
  onEditClick,
  onAvatarUpload
}) => {
  const [isHoveringAvatar, setIsHoveringAvatar] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleAvatarClick = () => {
    if (!isUploading) {
      fileInputRef.current?.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setIsUploading(true);
      try {
        await onAvatarUpload(e.target.files[0]);
      } finally {
        setIsUploading(false);
      }
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return 'purple';
      case 'admin':
        return 'red';
      case 'manager':
        return 'green';
      case 'employee':
        return 'blue';
      case 'cashier':
        return 'yellow';
      case 'inventory_manager':
        return 'indigo';
      default:
        return 'gray';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  return (
    <Card>
      <div className="flex flex-col items-center pb-6">
        <div
          className={`relative cursor-pointer group ${isUploading ? 'cursor-wait' : ''}`}
          onMouseEnter={() => !isUploading && setIsHoveringAvatar(true)}
          onMouseLeave={() => !isUploading && setIsHoveringAvatar(false)}
          onClick={handleAvatarClick}
        >
          <Avatar
            img={profile.avatarUrl || undefined}
            size="xl"
            rounded
            className={`mb-3 ring-2 ${isUploading ? 'opacity-60 ring-blue-300 dark:ring-blue-700' : 'ring-gray-200 dark:ring-gray-700'}`}
            status="online"
            statusPosition="bottom-right"
          />

          {isUploading ? (
            <div className="absolute inset-0 bg-black bg-opacity-30 rounded-full flex items-center justify-center">
              <div className="text-center">
                <Spinner size="md" className="mx-auto" />
                <span className="text-xs text-white mt-1 block">Uploading...</span>
              </div>
            </div>
          ) : (
            <div
              className={`absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center transition-opacity duration-200 ${
                isHoveringAvatar ? 'opacity-100' : 'opacity-0'
              }`}
            >
              <HiOutlineCamera className="h-8 w-8 text-white" />
            </div>
          )}

          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleFileChange}
            disabled={isUploading}
          />
        </div>

        <h5 className="mb-1 text-xl font-medium text-gray-900 dark:text-white">
          {profile.firstName} {profile.lastName}
        </h5>

        <div className="mb-3">
          <Badge color={getRoleBadgeColor(profile.role)} size="sm">
            {profile.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Badge>
        </div>

        {profile.jobTitle && (
          <span className="text-sm text-gray-500 dark:text-gray-400 mb-3">
            {profile.jobTitle}
            {profile.department && ` • ${profile.department}`}
          </span>
        )}

        <div className="space-y-3 w-full">
          <div className="flex items-center text-sm">
            <HiOutlineMail className="mr-2 h-4 w-4 text-gray-500" />
            <span className="text-gray-700 dark:text-gray-300 truncate">{profile.email}</span>
          </div>

          {profile.phoneNumber && (
            <div className="flex items-center text-sm">
              <HiOutlinePhone className="mr-2 h-4 w-4 text-gray-500" />
              <span className="text-gray-700 dark:text-gray-300">{profile.phoneNumber}</span>
            </div>
          )}

          {profile.location && (
            <div className="flex items-center text-sm">
              <HiOutlineLocationMarker className="mr-2 h-4 w-4 text-gray-500" />
              <span className="text-gray-700 dark:text-gray-300">{profile.location}</span>
            </div>
          )}
        </div>

        <div className="mt-6 w-full border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-500 dark:text-gray-400">Member since</span>
            <span className="font-medium">{formatDate(profile.joinDate)}</span>
          </div>
        </div>

        <div className="mt-6 w-full">
          <Button
            color="light"
            className="w-full"
            onClick={onEditClick}
          >
            <HiOutlinePencil className="mr-2 h-4 w-4" />
            Edit Profile
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ProfileInfoCard;
