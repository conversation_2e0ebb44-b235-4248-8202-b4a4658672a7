import { useState } from 'react';
import { 
  Button, 
  TextInput, 
  Label, 
  Alert, 
  Spinner, 
  ToggleSwitch,
  Card
} from 'flowbite-react';
import { 
  HiOutlineLockClosed, 
  HiOutlineShieldCheck, 
  HiOutlineDeviceMobile,
  HiOutlineExclamation,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import { Link } from 'react-router-dom';

interface SecuritySettingsProps {
  userId?: string;
}

const SecuritySettings: React.FC<SecuritySettingsProps> = ({ userId }) => {
  // State for password change
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
  const [changingPassword, setChangingPassword] = useState(false);
  
  // State for 2FA (mock for now)
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [showTwoFactorSetup, setShowTwoFactorSetup] = useState(false);
  
  // State for session management (mock for now)
  const [activeSessions, setActiveSessions] = useState([
    { id: '1', device: 'Chrome on macOS', lastActive: new Date().toISOString(), current: true },
    { id: '2', device: 'Safari on iPhone', lastActive: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), current: false }
  ]);
  
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset states
    setPasswordError(null);
    setPasswordSuccess(null);
    
    // Validate passwords
    if (newPassword !== confirmPassword) {
      setPasswordError('New passwords do not match');
      return;
    }
    
    if (newPassword.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return;
    }
    
    setChangingPassword(true);
    
    // This would be replaced with actual API call
    setTimeout(() => {
      setChangingPassword(false);
      setPasswordSuccess('Password changed successfully');
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    }, 1500);
  };
  
  const handleToggleTwoFactor = () => {
    if (!twoFactorEnabled) {
      // Show setup UI
      setShowTwoFactorSetup(true);
    } else {
      // This would be replaced with actual API call to disable 2FA
      setTimeout(() => {
        setTwoFactorEnabled(false);
      }, 500);
    }
  };
  
  const handleSetupTwoFactor = () => {
    // This would be replaced with actual API call
    setTimeout(() => {
      setTwoFactorEnabled(true);
      setShowTwoFactorSetup(false);
    }, 1500);
  };
  
  const handleRevokeSession = (sessionId: string) => {
    // This would be replaced with actual API call
    setActiveSessions(activeSessions.filter(session => session.id !== sessionId));
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };
  
  return (
    <div className="space-y-8">
      {/* Password Change Section */}
      <div>
        <h3 className="text-lg font-medium mb-4 flex items-center">
          <HiOutlineLockClosed className="mr-2 h-5 w-5 text-gray-500" />
          Password
        </h3>
        
        {passwordError && (
          <Alert color="failure" className="mb-4">
            {passwordError}
          </Alert>
        )}
        
        {passwordSuccess && (
          <Alert color="success" className="mb-4">
            {passwordSuccess}
          </Alert>
        )}
        
        <form onSubmit={handlePasswordChange} className="space-y-4">
          <div>
            <Label htmlFor="currentPassword" value="Current Password" />
            <TextInput
              id="currentPassword"
              type="password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="newPassword" value="New Password" />
            <TextInput
              id="newPassword"
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="confirmPassword" value="Confirm New Password" />
            <TextInput
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
          </div>
          
          <Button type="submit" disabled={changingPassword}>
            {changingPassword ? <Spinner size="sm" className="mr-2" /> : null}
            Change Password
          </Button>
        </form>
      </div>
      
      {/* Two-Factor Authentication Section */}
      <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium mb-4 flex items-center">
          <HiOutlineShieldCheck className="mr-2 h-5 w-5 text-gray-500" />
          Two-Factor Authentication
        </h3>
        
        <div className="flex items-center justify-between mb-4">
          <div>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              Add an extra layer of security to your account
            </p>
            <p className="text-xs text-gray-500 mt-1">
              We'll ask for a verification code in addition to your password when you sign in
            </p>
          </div>
          <ToggleSwitch
            checked={twoFactorEnabled}
            onChange={handleToggleTwoFactor}
            label=""
          />
        </div>
        
        {showTwoFactorSetup && (
          <Card className="mb-4">
            <div className="text-center">
              <h4 className="text-md font-medium mb-2">Set up Two-Factor Authentication</h4>
              <p className="text-sm text-gray-500 mb-4">
                Scan this QR code with your authenticator app
              </p>
              
              <div className="bg-gray-100 dark:bg-gray-700 p-4 inline-block rounded-lg mb-4">
                <div className="w-40 h-40 bg-gray-300 dark:bg-gray-600 rounded-md mx-auto flex items-center justify-center">
                  <HiOutlineDeviceMobile className="h-16 w-16 text-gray-500" />
                </div>
              </div>
              
              <div className="mb-4">
                <Label htmlFor="verificationCode" value="Verification Code" />
                <TextInput
                  id="verificationCode"
                  type="text"
                  placeholder="Enter 6-digit code"
                  className="max-w-xs mx-auto"
                />
              </div>
              
              <div className="flex justify-center space-x-3">
                <Button color="gray" onClick={() => setShowTwoFactorSetup(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSetupTwoFactor}>
                  Verify and Enable
                </Button>
              </div>
            </div>
          </Card>
        )}
      </div>
      
      {/* Active Sessions Section */}
      <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium mb-4 flex items-center">
          <HiOutlineDeviceMobile className="mr-2 h-5 w-5 text-gray-500" />
          Active Sessions
        </h3>
        
        <div className="space-y-4">
          {activeSessions.map((session) => (
            <div 
              key={session.id} 
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <div>
                <div className="flex items-center">
                  <span className="font-medium">{session.device}</span>
                  {session.current && (
                    <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                      Current
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-500">
                  Last active: {formatDate(session.lastActive)}
                </p>
              </div>
              
              {!session.current && (
                <Button 
                  color="failure" 
                  size="xs"
                  onClick={() => handleRevokeSession(session.id)}
                >
                  Revoke
                </Button>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SecuritySettings;
