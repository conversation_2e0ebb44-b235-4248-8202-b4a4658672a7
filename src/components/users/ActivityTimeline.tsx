import React from 'react';
import { 
  HiO<PERSON><PERSON>Login, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>ogo<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>User, 
  HiO<PERSON>lineLockClosed,
  HiOutlineShoppingCart,
  HiOutlineDocumentText,
  HiOutlineOfficeBuilding,
  HiOutlineDesktopComputer
} from 'react-icons/hi';

interface Activity {
  id: string;
  type: string;
  timestamp: string;
  details: string;
  metadata?: Record<string, any>;
}

interface ActivityTimelineProps {
  activities: Activity[];
}

const ActivityTimeline: React.FC<ActivityTimelineProps> = ({ activities }) => {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'login':
        return <HiOutlineLogin className="h-5 w-5 text-blue-500" />;
      case 'logout':
        return <HiOutlineLogout className="h-5 w-5 text-gray-500" />;
      case 'profile_update':
        return <HiOutlineUser className="h-5 w-5 text-green-500" />;
      case 'password_change':
        return <HiOutlineLockClosed className="h-5 w-5 text-yellow-500" />;
      case 'purchase':
        return <HiOutlineShoppingCart className="h-5 w-5 text-purple-500" />;
      case 'document':
        return <HiOutlineDocumentText className="h-5 w-5 text-red-500" />;
      case 'organization':
        return <HiOutlineOfficeBuilding className="h-5 w-5 text-indigo-500" />;
      default:
        return <HiOutlineDesktopComputer className="h-5 w-5 text-gray-500" />;
    }
  };
  
  const getActivityTitle = (type: string) => {
    switch (type) {
      case 'login':
        return 'Logged in';
      case 'logout':
        return 'Logged out';
      case 'profile_update':
        return 'Updated profile';
      case 'password_change':
        return 'Changed password';
      case 'purchase':
        return 'Made a purchase';
      case 'document':
        return 'Document action';
      case 'organization':
        return 'Organization update';
      default:
        return 'Activity';
    }
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      // Today - show time
      return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays === 1) {
      // Yesterday
      return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays < 7) {
      // Within a week
      return `${diffDays} days ago`;
    } else {
      // More than a week
      return date.toLocaleDateString([], { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };
  
  // Sort activities by timestamp (newest first)
  const sortedActivities = [...activities].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );
  
  return (
    <div className="flow-root">
      {sortedActivities.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No activity recorded yet</p>
        </div>
      ) : (
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {sortedActivities.map((activity) => (
            <li key={activity.id} className="py-4">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 mt-1">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {getActivityTitle(activity.type)}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {activity.details}
                  </p>
                </div>
                <div className="flex-shrink-0 text-sm text-gray-500 dark:text-gray-400">
                  {formatDate(activity.timestamp)}
                </div>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default ActivityTimeline;
