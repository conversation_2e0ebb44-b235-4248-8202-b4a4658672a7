import { useState, useRef, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON> 
} from 'flowbite-react';
import { 
  HiOutlineCamera, 
  HiOutlineUpload, 
  HiOutlineX, 
  HiOutlineCheck,
  HiOutlineExclamation
} from 'react-icons/hi';

interface ProfileImageUploaderProps {
  show: boolean;
  onClose: () => void;
  onUpload: (file: File) => Promise<void>;
  currentImageUrl?: string;
}

const ProfileImageUploader: React.FC<ProfileImageUploaderProps> = ({
  show,
  onClose,
  onUpload,
  currentImageUrl
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  useEffect(() => {
    // Create preview when file is selected
    if (!selectedFile) {
      setPreview(null);
      return;
    }
    
    const objectUrl = URL.createObjectURL(selectedFile);
    setPreview(objectUrl);
    
    // Free memory when component unmounts
    return () => URL.revokeObjectURL(objectUrl);
  }, [selectedFile]);
  
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      setSelectedFile(null);
      return;
    }
    
    const file = e.target.files[0];
    validateAndSetFile(file);
  };
  
  const validateAndSetFile = (file: File) => {
    setError(null);
    
    // Check file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('Image size should be less than 5MB');
      return;
    }
    
    setSelectedFile(file);
  };
  
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };
  
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      validateAndSetFile(file);
    }
  };
  
  const handleUpload = async () => {
    if (!selectedFile) return;
    
    setIsUploading(true);
    setError(null);
    
    try {
      await onUpload(selectedFile);
      handleClose();
    } catch (err: any) {
      setError(err.message || 'Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };
  
  const handleClose = () => {
    setSelectedFile(null);
    setPreview(null);
    setError(null);
    onClose();
  };
  
  return (
    <Modal show={show} onClose={handleClose} size="md">
      <Modal.Header>
        Upload Profile Picture
      </Modal.Header>
      <Modal.Body>
        {error && (
          <Alert color="failure" className="mb-4">
            <div className="flex items-center">
              <HiOutlineExclamation className="mr-2 h-5 w-5" />
              <span>{error}</span>
            </div>
          </Alert>
        )}
        
        <div 
          className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
            isDragging 
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20' 
              : 'border-gray-300 hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500'
          }`}
          onClick={() => fileInputRef.current?.click()}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {preview ? (
            <div className="flex flex-col items-center">
              <div className="relative w-40 h-40 mb-4">
                <img 
                  src={preview} 
                  alt="Preview" 
                  className="w-full h-full object-cover rounded-full"
                />
                <button
                  type="button"
                  className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedFile(null);
                  }}
                >
                  <HiOutlineX className="h-4 w-4" />
                </button>
              </div>
              <span className="text-sm text-gray-500">
                {selectedFile?.name}
              </span>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              {currentImageUrl ? (
                <img 
                  src={currentImageUrl} 
                  alt="Current" 
                  className="w-32 h-32 object-cover rounded-full mb-4 opacity-50"
                />
              ) : (
                <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                  <HiOutlineCamera className="h-10 w-10 text-gray-500" />
                </div>
              )}
              <HiOutlineUpload className="h-8 w-8 text-gray-400 mb-2" />
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                Click to upload or drag and drop
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                SVG, PNG, JPG or GIF (Max. 5MB)
              </p>
            </div>
          )}
          
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept="image/*"
            onChange={handleFileSelect}
          />
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="flex justify-end w-full space-x-3">
          <Button color="gray" onClick={handleClose}>
            Cancel
          </Button>
          <Button 
            color="primary" 
            onClick={handleUpload} 
            disabled={!selectedFile || isUploading}
          >
            {isUploading ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Uploading...
              </>
            ) : (
              <>
                <HiOutlineCheck className="mr-2 h-5 w-5" />
                Upload
              </>
            )}
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default ProfileImageUploader;
