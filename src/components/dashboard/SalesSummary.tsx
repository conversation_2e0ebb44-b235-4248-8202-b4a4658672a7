import React, { useState, useEffect } from 'react';
import { Card, Select, But<PERSON>, Spinner, Alert } from 'flowbite-react';
import { HiOutlineRefresh, HiOutlineTrendingUp, HiOutlineTrendingDown, HiInformationCircle } from 'react-icons/hi';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { useOrganization } from '../../context/OrganizationContext';
import { getSales, Sale } from '../../services/sale';
import { RefundService } from '../../services/refund';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { format, subDays, endOfDay, parseISO, startOfToday, endOfToday } from 'date-fns';
import { supabase } from '../../lib/supabase';
import { CostingService, CostingMethodType, CostingMethodFactory } from '../../services/costingMethods';
import MetricsExplanationModal from './MetricsExplanationModal';

interface SalesSummaryData {
  grossSales: number;
  refunds: number;
  discounts: number;
  netSales: number;
  totalAmount: number;
  costOfGoods: number;
  grossProfit: number;
  salesCount: number;
  hourlyData: Array<{
    hour: string;
    grossSales: number;
    netSales: number;
    totalAmount: number;
    discounts: number;
    refunds: number;
    costOfGoods: number;
    grossProfit: number;
    salesCount: number;
  }>;
  dailyData: Array<{
    date: string;
    grossSales: number;
    netSales: number;
    totalAmount: number;
    discounts: number;
    refunds: number;
    costOfGoods: number;
    grossProfit: number;
    salesCount: number;
  }>;
}

interface SalesSummaryProps {
  className?: string;
  showHeader?: boolean; // Option to hide header when used in dashboard
}

const SalesSummary: React.FC<SalesSummaryProps> = ({ className = '', showHeader = true }) => {
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState('today');
  const [salesData, setSalesData] = useState<SalesSummaryData>({
    grossSales: 0,
    refunds: 0,
    discounts: 0,
    netSales: 0,
    totalAmount: 0,
    costOfGoods: 0,
    grossProfit: 0,
    salesCount: 0,
    hourlyData: [],
    dailyData: []
  });

  // Costing method state
  const [costingMethod, setCostingMethod] = useState<CostingMethodType>(CostingMethodType.FIFO);
  const [isCalculatingCOGS, setIsCalculatingCOGS] = useState(false);

  // Modal state
  const [showMetricsModal, setShowMetricsModal] = useState(false);

  // Calculate date range
  const getDateRange = (range: string) => {
    const now = new Date();
    let startDate: Date;
    let endDate: Date;

    switch (range) {
      case 'today':
        startDate = startOfToday();
        endDate = endOfToday();
        break;
      case '7days':
        startDate = subDays(now, 7);
        endDate = endOfDay(now);
        break;
      case '30days':
        startDate = subDays(now, 30);
        endDate = endOfDay(now);
        break;
      case '90days':
        startDate = subDays(now, 90);
        endDate = endOfDay(now);
        break;
      default:
        startDate = startOfToday();
        endDate = endOfToday();
    }

    return { startDate, endDate };
  };

  // Fetch sales data
  const fetchSalesData = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { startDate, endDate } = getDateRange(dateRange);

      // Fetch sales data
      const { sales, error: salesError } = await getSales(currentOrganization.id, {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        limit: 1000,
        sortBy: 'sale_date',
        sortOrder: 'desc'
      });

      if (salesError) {
        throw new Error(salesError);
      }

      // Fetch refund data with date filtering
      const refundSummary = await RefundService.getRefundSummary(
        currentOrganization.id,
        startDate.toISOString(),
        endDate.toISOString()
      );

      // Fetch detailed refund data for chart distribution
      const refundListResponse = await RefundService.getRefunds(currentOrganization.id, {
        filters: {
          date_from: startDate.toISOString(),
          date_to: endDate.toISOString()
        },
        limit: 1000
      });

      const refundData = refundListResponse.success ? refundListResponse.data : [];

      // Process sales data with refund information
      const processedData = await processSalesData(sales, startDate, endDate, refundSummary, refundData);
      setSalesData(processedData);

    } catch (err: any) {
      console.error('Error fetching sales data:', err);
      setError(err.message || 'Failed to fetch sales data');
    } finally {
      setLoading(false);
    }
  };

  // Calculate Cost of Goods Sold (COGS) using advanced costing methods
  const calculateCOGS = async (sales: Sale[]): Promise<number> => {
    if (!sales.length || !currentOrganization?.id) return 0;

    try {
      setIsCalculatingCOGS(true);

      // Get all sale items for the sales
      const saleIds = sales.map(sale => sale.id).filter((id): id is string => Boolean(id));

      if (saleIds.length === 0) return 0;

      const { data: saleItems, error } = await supabase
        .from('sale_items')
        .select(`
          id,
          quantity,
          base_quantity,
          product_id,
          sale_id,
          sales!inner(sale_date)
        `)
        .in('sale_id', saleIds);

      if (error || !saleItems) {
        console.warn('Could not fetch sale items for COGS calculation:', error);
        return 0;
      }

      // Transform sale items to the format expected by costing service
      // NOTE: We use actual quantity sold (not base_quantity) because:
      // - Sales are always in pieces (pcs)
      // - UOM is only used for purchases, not sales
      // - Using base_quantity would inflate COGS incorrectly
      const costingSaleItems = saleItems.map(item => ({
        id: item.id,
        productId: item.product_id,
        quantity: item.quantity, // Use actual sale quantity (always in pcs)
        baseQuantity: item.quantity, // Same as quantity for sales
        saleDate: parseISO(item.sales.sale_date)
      }));

      // Calculate COGS using the selected costing method
      const result = await CostingService.calculateCOGS(
        currentOrganization.id,
        costingSaleItems,
        costingMethod
      );

      console.log(`COGS calculation result:`, {
        method: result.method,
        totalCost: result.totalCost,
        layersUsed: result.layersUsed.length
      });

      return result.totalCost;
    } catch (err) {
      console.error('Error calculating COGS:', err);
      return 0;
    } finally {
      setIsCalculatingCOGS(false);
    }
  };

  // Process sales data for charts and metrics
  const processSalesData = async (sales: Sale[], startDate: Date, endDate: Date, refundSummary: any, refundData: any[] = []): Promise<SalesSummaryData> => {
    // Filter only completed sales (exclude drafts, cancelled, refunded)
    const completedSales = sales.filter(sale => sale.status === 'completed');

    // Calculate totals with correct formulas
    // Gross Sales = Subtotal (before any deductions)
    const grossSales = completedSales.reduce((sum, sale) => sum + sale.subtotal, 0);

    // Total Discounts = Regular discounts + Loyalty points discounts
    const discounts = completedSales.reduce((sum, sale) =>
      sum + (sale.discount_amount || 0) + (sale.loyalty_points_discount || 0), 0);

    // Net Sales = Gross Sales - Discounts (before tax)
    const netSales = grossSales - discounts;

    // Total Amount = Final amount received (includes tax)
    const totalAmount = completedSales.reduce((sum, sale) => sum + sale.total_amount, 0);

    // Refunds = Total refunded amount
    const refunds = refundSummary?.total_amount || 0;

    // Calculate actual Cost of Goods Sold using product cost_price
    const costOfGoods = await calculateCOGS(completedSales);

    // Calculate gross profit (Net Sales - COGS)
    const grossProfit = netSales - costOfGoods;

    // Generate hourly data for today view
    let hourlyData: Array<{
      hour: string;
      grossSales: number;
      netSales: number;
      totalAmount: number;
      discounts: number;
      refunds: number;
      costOfGoods: number;
      grossProfit: number;
      salesCount: number;
    }> = [];

    // Generate daily data for multi-day views
    let dailyData: Array<{
      date: string;
      grossSales: number;
      netSales: number;
      totalAmount: number;
      discounts: number;
      refunds: number;
      costOfGoods: number;
      grossProfit: number;
      salesCount: number;
    }> = [];

    if (dateRange === 'today') {
      // Generate hourly data (1 AM to 12 midnight) - all 24 hours
      const hours = Array.from({ length: 24 }, (_, i) => i); // 0 to 23 (standard hour format)

      hourlyData = await Promise.all(hours.map(async hour => {
        const hourSales = completedSales.filter(sale => {
          const saleHour = parseISO(sale.sale_date).getHours(); // Keep 0-23 format
          return saleHour === hour;
        });

        // Calculate hourly metrics with correct formulas
        const hourGrossSales = hourSales.reduce((sum, sale) => sum + sale.subtotal, 0);
        const hourDiscounts = hourSales.reduce((sum, sale) =>
          sum + (sale.discount_amount || 0) + (sale.loyalty_points_discount || 0), 0);
        const hourNetSales = hourGrossSales - hourDiscounts;
        const hourTotalAmount = hourSales.reduce((sum, sale) => sum + sale.total_amount, 0);

        // Calculate actual refunds for this hour
        const hourRefunds = refundData
          .filter(refund => {
            const refundHour = parseISO(refund.created_at).getHours();
            return refundHour === hour;
          })
          .reduce((sum, refund) => sum + Number(refund.total_amount), 0);

        // Format hour display: 1 AM to 12 midnight
        let hourDisplay: string;
        if (hour === 0) {
          hourDisplay = '12 AM';
        } else if (hour < 12) {
          hourDisplay = `${hour} AM`;
        } else if (hour === 12) {
          hourDisplay = '12 PM';
        } else {
          hourDisplay = `${hour - 12} PM`;
        }

        // Calculate COGS for this hour (simplified - using cost_price from sales items)
        const hourCOGS = await calculateCOGS(hourSales);
        const hourGrossProfit = hourNetSales - hourCOGS;

        return {
          hour: hourDisplay,
          grossSales: hourGrossSales,
          netSales: hourNetSales,
          totalAmount: hourTotalAmount,
          discounts: hourDiscounts,
          refunds: hourRefunds,
          costOfGoods: hourCOGS,
          grossProfit: hourGrossProfit,
          salesCount: hourSales.length
        };
      }));
    } else {
      // Generate daily data for multi-day periods
      const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const dates = Array.from({ length: daysDiff + 1 }, (_, i) => {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        return date;
      });

      dailyData = await Promise.all(dates.map(async date => {
        const dateStr = format(date, 'yyyy-MM-dd');
        const daySales = completedSales.filter(sale =>
          format(parseISO(sale.sale_date), 'yyyy-MM-dd') === dateStr
        );

        // Calculate daily metrics with correct formulas
        const dayGrossSales = daySales.reduce((sum, sale) => sum + sale.subtotal, 0);
        const dayDiscounts = daySales.reduce((sum, sale) =>
          sum + (sale.discount_amount || 0) + (sale.loyalty_points_discount || 0), 0);
        const dayNetSales = dayGrossSales - dayDiscounts;
        const dayTotalAmount = daySales.reduce((sum, sale) => sum + sale.total_amount, 0);

        // Calculate actual refunds for this day
        const dayRefunds = refundData
          .filter(refund => {
            const refundDate = format(parseISO(refund.created_at), 'yyyy-MM-dd');
            return refundDate === dateStr;
          })
          .reduce((sum, refund) => sum + Number(refund.total_amount), 0);

        // Calculate COGS for this day
        const dayCOGS = await calculateCOGS(daySales);
        const dayGrossProfit = dayNetSales - dayCOGS;

        return {
          date: format(date, 'MMM dd'),
          grossSales: dayGrossSales,
          netSales: dayNetSales,
          totalAmount: dayTotalAmount,
          discounts: dayDiscounts,
          refunds: dayRefunds,
          costOfGoods: dayCOGS,
          grossProfit: dayGrossProfit,
          salesCount: daySales.length
        };
      }));
    }

    return {
      grossSales,
      refunds,
      discounts,
      netSales,
      totalAmount,
      costOfGoods,
      grossProfit,
      salesCount: completedSales.length,
      hourlyData,
      dailyData
    };
  };

  // Chart configuration for sales trend
  const salesChartOptions: ApexOptions = {
    chart: {
      type: 'area',
      height: 300,
      toolbar: { show: false },
      zoom: { enabled: false }
    },
    colors: ['#10B981', '#3B82F6', '#F59E0B', '#8B5CF6', '#EF4444'],
    dataLabels: { enabled: false },
    stroke: {
      curve: 'smooth',
      width: 2
    },
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.3,
        opacityTo: 0.1,
        stops: [0, 90, 100]
      }
    },
    grid: {
      borderColor: '#E5E7EB',
      strokeDashArray: 3
    },
    xaxis: {
      categories: dateRange === 'today'
        ? salesData.hourlyData.map(d => d.hour)
        : salesData.dailyData.map(d => d.date),
      labels: {
        style: { colors: '#6B7280', fontSize: '12px' },
        rotate: dateRange === 'today' ? -45 : 0
      }
    },
    yaxis: {
      labels: {
        style: { colors: '#6B7280', fontSize: '12px' },
        formatter: (value) => formatWithCurrency(value, false) // Don't include currency symbol in chart
      }
    },
    tooltip: {
      theme: 'light',
      y: {
        formatter: (value) => formatWithCurrency(value)
      }
    },
    legend: {
      position: 'top',
      horizontalAlign: 'right'
    }
  };

  const salesChartSeries = [
    {
      name: 'Gross Sales',
      data: dateRange === 'today'
        ? salesData.hourlyData.map(d => d.grossSales)
        : salesData.dailyData.map(d => d.grossSales)
    },
    {
      name: 'Net Sales',
      data: dateRange === 'today'
        ? salesData.hourlyData.map(d => d.netSales)
        : salesData.dailyData.map(d => d.netSales)
    },
    {
      name: 'Discounts',
      data: dateRange === 'today'
        ? salesData.hourlyData.map(d => d.discounts)
        : salesData.dailyData.map(d => d.discounts)
    },
    {
      name: 'Cost of Goods',
      data: dateRange === 'today'
        ? salesData.hourlyData.map(d => d.costOfGoods)
        : salesData.dailyData.map(d => d.costOfGoods)
    },
    {
      name: 'Refunds',
      data: dateRange === 'today'
        ? salesData.hourlyData.map(d => d.refunds)
        : salesData.dailyData.map(d => d.refunds)
    }
  ];

  // Load data on component mount and when dependencies change
  useEffect(() => {
    fetchSalesData();
  }, [currentOrganization, dateRange]);

  // Recalculate when costing method changes
  useEffect(() => {
    if (salesData.salesCount > 0) {
      fetchSalesData(); // Recalculate with new costing method
    }
  }, [costingMethod]);

  // Calculate percentage changes (simplified)
  const getPercentageChange = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  // Mock previous period data for percentage calculations
  const previousGrossSales = salesData.grossSales * 0.85;
  const previousNetSales = salesData.netSales * 0.88;
  const previousDiscounts = salesData.discounts * 1.15;
  const previousRefunds = salesData.refunds * 0.75; // Assume refunds decreased
  const previousGrossProfit = salesData.grossProfit * 0.82;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      {showHeader && (
        <div className="flex justify-between items-center">
          <div>
            <div className="flex items-center gap-3">
              <h2 className="text-2xl font-bold text-gray-900">Sales Summary</h2>
              <button
                onClick={() => setShowMetricsModal(true)}
                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                title="View metrics explanation"
              >
                <HiInformationCircle className="h-5 w-5" />
              </button>
            </div>
            <p className="text-gray-600">Track your sales performance and trends</p>
          </div>
          <div className="flex items-center space-x-3">
            <Select
              value={costingMethod}
              onChange={(e) => setCostingMethod(e.target.value as CostingMethodType)}
              className="w-48"
              title="Costing Method"
            >
              {CostingMethodFactory.getAvailableMethods().map(method => (
                <option key={method.value} value={method.value}>
                  {method.label}
                </option>
              ))}
            </Select>
            <Select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="w-40"
            >
              <option value="today">Today</option>
              <option value="7days">Last 7 days</option>
              <option value="30days">Last 30 days</option>
              <option value="90days">Last 90 days</option>
            </Select>
            <Button color="light" onClick={fetchSalesData} disabled={loading || isCalculatingCOGS}>
              <HiOutlineRefresh className={`h-4 w-4 ${loading || isCalculatingCOGS ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      )}

      {/* Controls for dashboard view */}
      {!showHeader && (
        <div className="flex justify-between items-center">
          <button
            onClick={() => setShowMetricsModal(true)}
            className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-blue-600 transition-colors"
            title="View metrics explanation"
          >
            <HiInformationCircle className="h-4 w-4" />
            <span>How are these calculated?</span>
          </button>
          <div className="flex items-center space-x-3">
            <Select
              value={costingMethod}
              onChange={(e) => setCostingMethod(e.target.value as CostingMethodType)}
              className="w-48"
              title="Costing Method"
            >
              {CostingMethodFactory.getAvailableMethods().map(method => (
                <option key={method.value} value={method.value}>
                  {method.label}
                </option>
              ))}
            </Select>
            <Select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="w-40"
            >
              <option value="today">Today</option>
              <option value="7days">Last 7 days</option>
              <option value="30days">Last 30 days</option>
              <option value="90days">Last 90 days</option>
            </Select>
            <Button color="light" onClick={fetchSalesData} disabled={loading || isCalculatingCOGS}>
              <HiOutlineRefresh className={`h-4 w-4 ${loading || isCalculatingCOGS ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      )}

      {error && (
        <Alert color="failure">
          <span className="font-medium">Error:</span> {error}
        </Alert>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Spinner size="xl" />
          <span className="ml-3">Loading sales data...</span>
        </div>
      ) : (
        <>
          {/* Metrics Cards */}
          {/*
            Metrics Explanation:
            - Gross Sales: Subtotal before any deductions
            - Discounts: Regular discounts + loyalty points discounts
            - Net Sales: Gross Sales - Discounts (before tax)
            - Total Amount: Final amount received (Net Sales + Tax)
            - Gross Profit: Net Sales × 40% (estimated margin)
          */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-7 gap-4">
            <Card className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-600">Gross Sales</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatWithCurrency(salesData.grossSales)}
                  </p>
                  <div className="flex items-center mt-1">
                    {getPercentageChange(salesData.grossSales, previousGrossSales) >= 0 ? (
                      <HiOutlineTrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    ) : (
                      <HiOutlineTrendingDown className="h-4 w-4 text-red-500 mr-1" />
                    )}
                    <span className={`text-sm ${
                      getPercentageChange(salesData.grossSales, previousGrossSales) >= 0
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}>
                      {Math.abs(getPercentageChange(salesData.grossSales, previousGrossSales)).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-600">Refunds</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatWithCurrency(salesData.refunds)}
                  </p>
                  <div className="flex items-center mt-1">
                    {getPercentageChange(salesData.refunds, previousRefunds) >= 0 ? (
                      <HiOutlineTrendingUp className="h-4 w-4 text-red-500 mr-1" />
                    ) : (
                      <HiOutlineTrendingDown className="h-4 w-4 text-green-500 mr-1" />
                    )}
                    <span className={`text-sm ${
                      getPercentageChange(salesData.refunds, previousRefunds) >= 0
                        ? 'text-red-600'
                        : 'text-green-600'
                    }`}>
                      {Math.abs(getPercentageChange(salesData.refunds, previousRefunds)).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-600">Discounts</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatWithCurrency(salesData.discounts)}
                  </p>
                  <div className="flex items-center mt-1">
                    {getPercentageChange(salesData.discounts, previousDiscounts) >= 0 ? (
                      <HiOutlineTrendingUp className="h-4 w-4 text-red-500 mr-1" />
                    ) : (
                      <HiOutlineTrendingDown className="h-4 w-4 text-green-500 mr-1" />
                    )}
                    <span className={`text-sm ${
                      getPercentageChange(salesData.discounts, previousDiscounts) >= 0
                        ? 'text-red-600'
                        : 'text-green-600'
                    }`}>
                      {Math.abs(getPercentageChange(salesData.discounts, previousDiscounts)).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-600">Net Sales</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatWithCurrency(salesData.netSales)}
                  </p>
                  <div className="flex items-center mt-1">
                    {getPercentageChange(salesData.netSales, previousNetSales) >= 0 ? (
                      <HiOutlineTrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    ) : (
                      <HiOutlineTrendingDown className="h-4 w-4 text-red-500 mr-1" />
                    )}
                    <span className={`text-sm ${
                      getPercentageChange(salesData.netSales, previousNetSales) >= 0
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}>
                      {Math.abs(getPercentageChange(salesData.netSales, previousNetSales)).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-600">Total Amount</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatWithCurrency(salesData.totalAmount)}
                  </p>
                  <div className="flex items-center mt-1">
                    <span className="text-sm text-gray-500">
                      Final received
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-600">Cost of Goods</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatWithCurrency(salesData.costOfGoods)}
                  </p>
                  <div className="flex items-center mt-1">
                    <span className="text-sm text-gray-500">
                      {CostingMethodFactory.getAvailableMethods()
                        .find(m => m.value === costingMethod)?.label || 'Simple Cost'}
                    </span>
                    {isCalculatingCOGS && (
                      <div className="ml-2">
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-600">Gross Profit</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatWithCurrency(salesData.grossProfit)}
                  </p>
                  <div className="flex items-center mt-1">
                    {getPercentageChange(salesData.grossProfit, previousGrossProfit) >= 0 ? (
                      <HiOutlineTrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    ) : (
                      <HiOutlineTrendingDown className="h-4 w-4 text-red-500 mr-1" />
                    )}
                    <span className={`text-sm ${
                      getPercentageChange(salesData.grossProfit, previousGrossProfit) >= 0
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}>
                      {Math.abs(getPercentageChange(salesData.grossProfit, previousGrossProfit)).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Sales Trend Chart */}
          <Card className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                {dateRange === 'today' ? 'Today\'s Sales (Hourly)' : 'Sales Trend'}
              </h3>
              <div className="flex items-center space-x-4 text-sm">
                <span className="text-gray-600">
                  Total Sales: <strong>{salesData.salesCount}</strong>
                </span>
              </div>
            </div>
            <Chart
              options={salesChartOptions}
              series={salesChartSeries}
              type="area"
              height={300}
            />
          </Card>

          {/* Export Table */}
          <Card className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                {dateRange === 'today' ? 'Hourly Breakdown' : 'Daily Breakdown'}
              </h3>
              <Button color="light" size="sm">
                Export Data
              </Button>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left">
                <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                  <tr>
                    <th className="px-6 py-3">
                      {dateRange === 'today' ? 'Hour' : 'Date'}
                    </th>
                    <th className="px-6 py-3">Gross Sales</th>
                    <th className="px-6 py-3">Discounts</th>
                    <th className="px-6 py-3">Net Sales</th>
                    <th className="px-6 py-3">Total Amount</th>
                    <th className="px-6 py-3">Cost of Goods</th>
                    <th className="px-6 py-3">Gross Profit</th>
                  </tr>
                </thead>
                <tbody>
                  {(dateRange === 'today' ? salesData.hourlyData : salesData.dailyData)
                    .map((item, index) => {
                      // Use actual calculated COGS and gross profit
                      const costOfGoods = item.costOfGoods;
                      const grossProfit = item.grossProfit;

                      return (
                        <tr key={index} className="bg-white border-b hover:bg-gray-50">
                          <td className="px-6 py-4 font-medium text-gray-900">
                            {dateRange === 'today' ? (item as any).hour : (item as any).date}
                          </td>
                          <td className="px-6 py-4">
                            {formatWithCurrency(item.grossSales)}
                          </td>
                          <td className="px-6 py-4 text-red-600">
                            {formatWithCurrency(item.discounts)}
                          </td>
                          <td className="px-6 py-4 font-semibold">
                            {formatWithCurrency(item.netSales)}
                          </td>
                          <td className="px-6 py-4 text-blue-600 font-semibold">
                            {formatWithCurrency(item.totalAmount)}
                          </td>
                          <td className="px-6 py-4">
                            {formatWithCurrency(costOfGoods)}
                          </td>
                          <td className="px-6 py-4 text-green-600 font-semibold">
                            {formatWithCurrency(grossProfit)}
                          </td>
                        </tr>
                      );
                    })}
                </tbody>
              </table>
            </div>
          </Card>
        </>
      )}

      {/* Metrics Explanation Modal */}
      <MetricsExplanationModal
        isOpen={showMetricsModal}
        onClose={() => setShowMetricsModal(false)}
        salesData={salesData}
        costingMethod={CostingMethodFactory.getAvailableMethods()
          .find(m => m.value === costingMethod)?.label || 'Simple Cost'}
        dateRange={dateRange}
      />
    </div>
  );
};

export default SalesSummary;
