import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card } from 'flowbite-react';
import { Hi<PERSON>, HiCalculator, HiCurrencyDollar, HiTrendingUp } from 'react-icons/hi';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

// =====================================================
// METRICS EXPLANATION MODAL
// =====================================================

interface MetricsExplanationModalProps {
  isOpen: boolean;
  onClose: () => void;
  salesData: {
    grossSales: number;
    refunds: number;
    discounts: number;
    netSales: number;
    totalAmount: number;
    costOfGoods: number;
    grossProfit: number;
    salesCount: number;
  };
  costingMethod: string;
  sampleCalculations?: {
    sampleSales: Array<{
      id: string;
      subtotal: number;
      discount_amount: number;
      loyalty_points_discount: number;
      tax_amount: number;
      total_amount: number;
    }>;
    sampleRefunds: Array<{
      total_amount: number;
    }>;
  };
  dateRange?: string;
}

const MetricsExplanationModal: React.FC<MetricsExplanationModalProps> = ({
  isOpen,
  onClose,
  salesData,
  costingMethod,
  sampleCalculations,
  dateRange
}) => {
  const formatWithCurrency = useCurrencyFormatter();

  // Sample data for demonstration if no real data provided
  const defaultSampleSales = [
    { id: '1', subtotal: 150.00, discount_amount: 10.00, loyalty_points_discount: 5.00, tax_amount: 16.20, total_amount: 151.20 },
    { id: '2', subtotal: 200.00, discount_amount: 0.00, loyalty_points_discount: 0.00, tax_amount: 24.00, total_amount: 224.00 },
    { id: '3', subtotal: 138.00, discount_amount: 8.00, loyalty_points_discount: 0.00, tax_amount: 15.60, total_amount: 145.60 }
  ];

  const defaultSampleRefunds = [
    { total_amount: 75.50 },
    { total_amount: 985.50 }
  ];

  const sampleSales = sampleCalculations?.sampleSales || defaultSampleSales;
  const sampleRefunds = sampleCalculations?.sampleRefunds || defaultSampleRefunds;

  const getDateRangeLabel = (range?: string) => {
    switch (range) {
      case 'today': return 'Today';
      case '7days': return 'Last 7 Days';
      case '30days': return 'Last 30 Days';
      case '90days': return 'Last 90 Days';
      default: return 'Current Period';
    }
  };

  const metrics = [
    {
      name: 'Gross Sales',
      icon: <HiCurrencyDollar className="h-5 w-5 text-green-600" />,
      value: salesData.grossSales,
      color: 'green',
      formula: 'Sum of all sale subtotals (before any deductions)',
      calculation: 'Subtotal₁ + Subtotal₂ + Subtotal₃ + ...',
      example: {
        breakdown: sampleSales.map((sale, i) => `Sale ${i + 1}: ${formatWithCurrency(sale.subtotal)}`),
        total: sampleSales.reduce((sum, sale) => sum + sale.subtotal, 0)
      },
      description: 'The total revenue from all sales before any discounts, taxes, or deductions are applied.'
    },
    {
      name: 'Refunds',
      icon: <HiTrendingUp className="h-5 w-5 text-red-600 rotate-180" />,
      value: salesData.refunds,
      color: 'red',
      formula: 'Sum of all refunded amounts',
      calculation: 'Refund₁ + Refund₂ + Refund₃ + ...',
      example: {
        breakdown: sampleRefunds.map((refund, i) => `Refund ${i + 1}: ${formatWithCurrency(refund.total_amount)}`),
        total: sampleRefunds.reduce((sum, refund) => sum + refund.total_amount, 0)
      },
      description: 'Total amount refunded to customers for returned or cancelled orders.'
    },
    {
      name: 'Discounts',
      icon: <HiTrendingUp className="h-5 w-5 text-orange-600 rotate-180" />,
      value: salesData.discounts,
      color: 'orange',
      formula: 'Sum of all discount types applied to sales',
      calculation: '(Regular Discounts + Loyalty Points Discounts) for all sales',
      example: {
        breakdown: sampleSales.map((sale, i) => {
          const totalDiscount = (sale.discount_amount || 0) + (sale.loyalty_points_discount || 0);
          return `Sale ${i + 1}: ${formatWithCurrency(sale.discount_amount || 0)} + ${formatWithCurrency(sale.loyalty_points_discount || 0)} = ${formatWithCurrency(totalDiscount)}`;
        }),
        total: sampleSales.reduce((sum, sale) => sum + (sale.discount_amount || 0) + (sale.loyalty_points_discount || 0), 0)
      },
      description: 'Total value of all discounts given, including regular discounts and loyalty point redemptions.'
    },
    {
      name: 'Net Sales',
      icon: <HiCalculator className="h-5 w-5 text-blue-600" />,
      value: salesData.netSales,
      color: 'blue',
      formula: 'Gross Sales - Total Discounts',
      calculation: 'Gross Sales - Discounts',
      example: {
        breakdown: [
          `Gross Sales: ${formatWithCurrency(sampleSales.reduce((sum, sale) => sum + sale.subtotal, 0))}`,
          `Total Discounts: ${formatWithCurrency(sampleSales.reduce((sum, sale) => sum + (sale.discount_amount || 0) + (sale.loyalty_points_discount || 0), 0))}`,
          `Net Sales = Gross Sales - Discounts`
        ],
        total: sampleSales.reduce((sum, sale) => sum + sale.subtotal, 0) - sampleSales.reduce((sum, sale) => sum + (sale.discount_amount || 0) + (sale.loyalty_points_discount || 0), 0)
      },
      description: 'Revenue after discounts but before taxes. This represents the actual selling price of goods.'
    },
    {
      name: 'Total Amount',
      icon: <HiCurrencyDollar className="h-5 w-5 text-purple-600" />,
      value: salesData.totalAmount,
      color: 'purple',
      formula: 'Sum of all final amounts received from customers',
      calculation: 'Total Amount₁ + Total Amount₂ + Total Amount₃ + ...',
      example: {
        breakdown: sampleSales.map((sale, i) => `Sale ${i + 1}: ${formatWithCurrency(sale.total_amount)}`),
        total: sampleSales.reduce((sum, sale) => sum + sale.total_amount, 0)
      },
      description: 'The final amount received from customers, including taxes. This is the actual cash collected.'
    },
    {
      name: 'Cost of Goods',
      icon: <HiCalculator className="h-5 w-5 text-indigo-600" />,
      value: salesData.costOfGoods,
      color: 'indigo',
      formula: `Calculated using ${costingMethod} costing method`,
      calculation: getCostingMethodFormula(costingMethod),
      example: {
        breakdown: [
          `Method: ${costingMethod}`,
          `Data Source: sale_items.quantity (actual pieces sold)`,
          `NOT base_quantity (UOM conversion not used)`,
          `Reason: Sales are always in pieces, UOM only for purchases`,
          `Calculated based on inventory layers and purchase history`
        ],
        total: salesData.costOfGoods
      },
      description: 'The actual cost of products sold using sale_items.quantity (pieces sold), calculated with advanced costing methods. UOM conversions are NOT used since sales are always in pieces.'
    },
    {
      name: 'Gross Profit',
      icon: <HiTrendingUp className="h-5 w-5 text-emerald-600" />,
      value: salesData.grossProfit,
      color: 'emerald',
      formula: 'Net Sales - Cost of Goods Sold',
      calculation: 'Net Sales - COGS',
      example: {
        breakdown: [
          `Net Sales: ${formatWithCurrency(salesData.netSales)}`,
          `Cost of Goods: ${formatWithCurrency(salesData.costOfGoods)}`,
          `Gross Profit = Net Sales - COGS`
        ],
        total: salesData.netSales - salesData.costOfGoods
      },
      description: 'The profit remaining after deducting the cost of goods sold from net sales. This shows the profitability of your products.'
    }
  ];

  function getCostingMethodFormula(method: string): string {
    switch (method.toLowerCase()) {
      case 'fifo':
      case 'fifo (first in, first out)':
        return 'Uses oldest inventory costs first (First In, First Out)';
      case 'lifo':
      case 'lifo (last in, first out)':
        return 'Uses newest inventory costs first (Last In, First Out)';
      case 'weighted_average':
      case 'weighted average':
        return 'Uses average cost of all available inventory';
      case 'simple':
      case 'simple cost':
        return 'Uses product cost_price × quantity sold';
      default:
        return 'Advanced costing method based on inventory transactions';
    }
  }

  const getColorClasses = (color: string) => {
    const colorMap: { [key: string]: string } = {
      green: 'bg-green-50 border-green-200 text-green-800',
      red: 'bg-red-50 border-red-200 text-red-800',
      orange: 'bg-orange-50 border-orange-200 text-orange-800',
      blue: 'bg-blue-50 border-blue-200 text-blue-800',
      purple: 'bg-purple-50 border-purple-200 text-purple-800',
      indigo: 'bg-indigo-50 border-indigo-200 text-indigo-800',
      emerald: 'bg-emerald-50 border-emerald-200 text-emerald-800'
    };
    return colorMap[color] || 'bg-gray-50 border-gray-200 text-gray-800';
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="7xl">
      <Modal.Header className="border-b border-gray-200">
        <div className="flex items-center gap-3">
          <HiCalculator className="h-6 w-6 text-blue-600" />
          <span className="text-xl font-semibold">Sales Metrics Explained</span>
        </div>
      </Modal.Header>

      <Modal.Body className="p-0">
        <div className="max-h-[80vh] overflow-y-auto p-6">
          {/* Introduction */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              📊 Understanding Your Sales Metrics
            </h3>
            <p className="text-blue-800 text-sm mb-3">
              This guide explains how each metric is calculated, shows the formulas used,
              and provides sample calculations based on your actual data structure.
            </p>
            <div className="text-sm text-blue-700">
              <strong>Current Period:</strong> {getDateRangeLabel(dateRange)} |
              <strong> Sales Count:</strong> {salesData.salesCount} transactions
            </div>
          </div>

          {/* Calculation Flow */}
          <div className="mb-6 p-4 bg-green-50 rounded-lg border border-green-200">
            <h3 className="text-lg font-semibold text-green-900 mb-3">
              🔄 Calculation Flow
            </h3>
            <div className="flex flex-wrap items-center gap-2 text-sm">
              <Badge color="green">Gross Sales</Badge>
              <span>→</span>
              <Badge color="orange">- Discounts</Badge>
              <span>=</span>
              <Badge color="blue">Net Sales</Badge>
              <span>→</span>
              <Badge color="indigo">- Cost of Goods</Badge>
              <span>=</span>
              <Badge color="emerald">Gross Profit</Badge>
            </div>
            <div className="mt-2 text-xs text-green-700">
              Total Amount = Net Sales + Taxes | Refunds = Separate tracking
            </div>
          </div>

          {/* Metrics Grid */}
          <div className="space-y-6">
            {metrics.map((metric, index) => (
              <Card key={index} className={`border-2 ${getColorClasses(metric.color)}`}>
                <div className="p-6">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {metric.icon}
                      <h4 className="text-lg font-semibold">{metric.name}</h4>
                      <Badge color={metric.color as any} size="sm">
                        {formatWithCurrency(metric.value)}
                      </Badge>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-sm mb-4 opacity-90">
                    {metric.description}
                  </p>

                  {/* Formula and Calculation */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Formula */}
                    <div>
                      <h5 className="font-semibold mb-2 flex items-center gap-2">
                        📐 Formula
                      </h5>
                      <div className="bg-white bg-opacity-50 p-3 rounded border">
                        <code className="text-sm font-mono">{metric.formula}</code>
                      </div>
                      <div className="mt-2 bg-white bg-opacity-50 p-3 rounded border">
                        <code className="text-sm font-mono">{metric.calculation}</code>
                      </div>
                    </div>

                    {/* Sample Calculation */}
                    <div>
                      <h5 className="font-semibold mb-2 flex items-center gap-2">
                        🧮 Sample Calculation
                      </h5>
                      <div className="bg-white bg-opacity-50 p-3 rounded border space-y-1">
                        {metric.example.breakdown.map((line, i) => (
                          <div key={i} className="text-sm font-mono">
                            {line}
                          </div>
                        ))}
                        <div className="border-t pt-2 mt-2">
                          <div className="text-sm font-mono font-semibold">
                            Result: {formatWithCurrency(metric.example.total)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Current Period Summary */}
          <div className="mt-8 p-4 bg-purple-50 rounded-lg border border-purple-200">
            <h3 className="text-lg font-semibold text-purple-900 mb-3">
              📈 Your Current Period Results
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {formatWithCurrency(salesData.grossSales)}
                </div>
                <div className="text-sm text-purple-700">Gross Sales</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {formatWithCurrency(salesData.netSales)}
                </div>
                <div className="text-sm text-blue-700">Net Sales</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-600">
                  {formatWithCurrency(salesData.costOfGoods)}
                </div>
                <div className="text-sm text-indigo-700">Cost of Goods</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-600">
                  {formatWithCurrency(salesData.grossProfit)}
                </div>
                <div className="text-sm text-emerald-700">Gross Profit</div>
              </div>
            </div>
            <div className="mt-4 text-center">
              <div className="text-sm text-purple-700">
                <strong>Profit Margin:</strong> {
                  salesData.netSales > 0
                    ? ((salesData.grossProfit / salesData.netSales) * 100).toFixed(1)
                    : '0'
                }% |
                <strong> Costing Method:</strong> {costingMethod}
              </div>
            </div>
          </div>

          {/* Percentage Calculations */}
          <div className="mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <h3 className="text-lg font-semibold text-yellow-900 mb-3">
              📊 Percentage Calculations Explained
            </h3>
            <p className="text-yellow-800 text-sm mb-4">
              The percentage changes shown below each metric compare the current period with the previous period using this formula:
            </p>

            <div className="bg-white bg-opacity-50 p-4 rounded border mb-4">
              <h4 className="font-semibold mb-2">📐 Percentage Change Formula:</h4>
              <code className="text-sm font-mono block mb-2">
                Percentage Change = ((Current Value - Previous Value) / Previous Value) × 100
              </code>

              <div className="mt-3 space-y-2 text-sm">
                <div><strong>🟢 Positive %:</strong> Indicates growth/increase from previous period</div>
                <div><strong>🔴 Negative %:</strong> Indicates decline/decrease from previous period</div>
                <div><strong>⚠️ Special Cases:</strong></div>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li><strong>Refunds:</strong> Lower refunds = Good (green), Higher refunds = Bad (red)</li>
                  <li><strong>Discounts:</strong> Higher discounts = Caution (red), Lower discounts = Good (green)</li>
                  <li><strong>Sales/Profit:</strong> Higher = Good (green), Lower = Bad (red)</li>
                </ul>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white bg-opacity-50 p-3 rounded border">
                <h5 className="font-semibold mb-2">🧮 Example Calculation:</h5>
                <div className="text-sm font-mono space-y-1">
                  <div>Current Gross Sales: {formatWithCurrency(salesData.grossSales)}</div>
                  <div>Previous Gross Sales: {formatWithCurrency(salesData.grossSales * 0.85)}</div>
                  <div>Calculation: (({formatWithCurrency(salesData.grossSales)} - {formatWithCurrency(salesData.grossSales * 0.85)}) / {formatWithCurrency(salesData.grossSales * 0.85)}) × 100</div>
                  <div className="font-semibold">Result: {((salesData.grossSales - (salesData.grossSales * 0.85)) / (salesData.grossSales * 0.85) * 100).toFixed(1)}%</div>
                </div>
              </div>

              <div className="bg-white bg-opacity-50 p-3 rounded border">
                <h5 className="font-semibold mb-2">📈 Interpretation Guide:</h5>
                <div className="text-sm space-y-1">
                  <div><strong>0% to 5%:</strong> Stable performance</div>
                  <div><strong>5% to 15%:</strong> Moderate growth/decline</div>
                  <div><strong>15% to 30%:</strong> Significant change</div>
                  <div><strong>30%+:</strong> Major change (investigate)</div>
                  <div className="mt-2 text-xs text-gray-600">
                    <em>Note: Percentages are calculated against simulated previous period data for demonstration.</em>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              💡 Additional Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700">
              <div>
                <h4 className="font-semibold mb-2">Calculation Order:</h4>
                <ol className="list-decimal list-inside space-y-1">
                  <li>Gross Sales (base revenue)</li>
                  <li>Discounts (deductions)</li>
                  <li>Net Sales (after discounts)</li>
                  <li>Cost of Goods (using costing method)</li>
                  <li>Gross Profit (Net Sales - COGS)</li>
                </ol>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Key Relationships:</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li>Net Sales = Gross Sales - Discounts</li>
                  <li>Gross Profit = Net Sales - COGS</li>
                  <li>Total Amount includes taxes</li>
                  <li>COGS varies by costing method</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">COGS Data Source:</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li><strong>Table:</strong> sale_items</li>
                  <li><strong>Quantity:</strong> quantity field (pcs sold)</li>
                  <li><strong>NOT base_quantity</strong> (UOM conversion)</li>
                  <li><strong>Reason:</strong> Sales are always in pieces</li>
                  <li><strong>UOM:</strong> Only used for purchases</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer className="border-t border-gray-200">
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            Close
          </button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default MetricsExplanationModal;
