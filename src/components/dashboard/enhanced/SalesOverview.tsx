import React from 'react';
import { Card } from 'flowbite-react';
import { Customer } from '../../../services/customer';

interface SalesOverviewProps {
  customers: Customer[];
}

const SalesOverview: React.FC<SalesOverviewProps> = ({ customers }) => {
  return (
    <Card>
      <h3 className="text-xl font-semibold mb-4">Sales Overview</h3>
      <p className="text-gray-500">Total Customers: {customers.length}</p>
    </Card>
  );
};

export default SalesOverview;
