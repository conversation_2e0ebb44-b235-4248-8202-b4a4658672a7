import React from 'react';
import { Card, Badge } from 'flowbite-react';
import { Customer } from '../../../services/customer';
import { Link } from 'react-router-dom';

interface RecentActivityProps {
  timeEntries: any[];
  customers: Customer[];
}

const RecentActivity: React.FC<RecentActivityProps> = ({ timeEntries, customers }) => {
  return (
    <Card className="h-full">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold">Recent Activity</h3>
        <Badge color="gray" size="sm">
          Today
        </Badge>
      </div>

      <div className="space-y-4">
        <p className="text-gray-500">Time Entries: {timeEntries.length}</p>
        <p className="text-gray-500">Recent Customers: {customers.length}</p>
      </div>

      <div className="mt-4 text-center">
        <Link to="/reports">
          <span className="text-sm text-blue-600 hover:underline">
            View All Activity
          </span>
        </Link>
      </div>
    </Card>
  );
};

export default RecentActivity;
