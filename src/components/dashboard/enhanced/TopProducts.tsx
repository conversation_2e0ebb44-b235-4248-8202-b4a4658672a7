import React from 'react';
import { <PERSON>, But<PERSON> } from 'flowbite-react';
import { Product } from '../../../services/product';
import { Link } from 'react-router-dom';

interface TopProductsProps {
  products: Product[];
}

const TopProducts: React.FC<TopProductsProps> = ({ products }) => {
  return (
    <Card>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold">Top Products</h3>
        <Link to="/products">
          <Button color="light" size="sm">
            View All Products
          </Button>
        </Link>
      </div>
      <p className="text-gray-500">Total Products: {products.length}</p>
    </Card>
  );
};

export default TopProducts;
