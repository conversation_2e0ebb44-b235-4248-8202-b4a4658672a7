import React from 'react';
import { Card, Button } from 'flowbite-react';
import { Product } from '../../../services/product';
import { formatCurrency } from '../../../utils/formatters';
import { Link } from 'react-router-dom';

interface InventoryStatusProps {
  products: Product[];
  lowStockItems: Product[];
}

const InventoryStatus: React.FC<InventoryStatusProps> = ({ products, lowStockItems }) => {
  // Calculate inventory metrics
  const totalProducts = products.length;
  const totalInventoryValue = products.reduce((sum, product) => {
    const stockQuantity = product.stock_quantity || 0;
    const unitPrice = product.unit_price || 0;
    return sum + (stockQuantity * unitPrice);
  }, 0);

  return (
    <Card>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold">Inventory Summary</h3>
        <Link to="/inventory">
          <Button color="light" size="sm">
            View All Inventory
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <p className="text-sm text-blue-700">Total Products</p>
          <p className="text-xl font-bold text-blue-700">{totalProducts}</p>
          <p className="text-xs text-blue-600 mt-1">In inventory</p>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <p className="text-sm text-yellow-700">Low Stock Items</p>
          <p className="text-xl font-bold text-yellow-700">{lowStockItems.length}</p>
          <p className="text-xs text-yellow-600 mt-1">Need attention</p>
        </div>
      </div>

      <div className="mt-6">
        <h4 className="text-lg font-medium mb-2">Inventory Value</h4>
        <p className="text-2xl font-bold">{formatCurrency(totalInventoryValue)}</p>
        <p className="text-sm text-gray-500">Total value of inventory</p>
      </div>
    </Card>
  );
};

export default InventoryStatus;
