import React from 'react';
import { <PERSON>, But<PERSON> } from 'flowbite-react';
import { formatCurrency } from '../../../utils/formatters';
import { Link } from 'react-router-dom';
import { HiOutlineUserGroup, HiOutlineClock, HiOutlineCash, HiOutlineCalendar } from 'react-icons/hi';

interface EmployeeStatsProps {
  employeeCount: number;
  timeEntries: any[];
  payrollPeriods: any[];
}

const EmployeeStats: React.FC<EmployeeStatsProps> = ({
  employeeCount,
  timeEntries,
  payrollPeriods
}) => {
  // Calculate total hours worked from time entries
  const totalHoursWorked = timeEntries.reduce((sum, entry) => {
    return sum + (entry.regular_hours || 0) + (entry.overtime_hours || 0);
  }, 0);

  // Calculate average hours per employee
  const avgHoursPerEmployee = employeeCount > 0 ?
    (totalHoursWorked / employeeCount).toFixed(1) : '0';

  // Generate mock payroll data
  const totalPayroll = 150000; // Mock total payroll amount
  const avgSalary = employeeCount > 0 ?
    (totalPayroll / employeeCount).toFixed(2) : '0';

  return (
    <Card>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold">Employee Overview</h3>
        <Link to="/employees">
          <Button color="light" size="sm">
            View All Employees
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg flex items-center">
          <div className="p-3 bg-blue-100 rounded-full mr-3">
            <HiOutlineUserGroup className="h-6 w-6 text-blue-700" />
          </div>
          <div>
            <p className="text-sm text-blue-700">Total Employees</p>
            <p className="text-xl font-bold text-blue-700">{employeeCount}</p>
          </div>
        </div>

        <div className="bg-green-50 p-4 rounded-lg flex items-center">
          <div className="p-3 bg-green-100 rounded-full mr-3">
            <HiOutlineClock className="h-6 w-6 text-green-700" />
          </div>
          <div>
            <p className="text-sm text-green-700">Avg Hours/Employee</p>
            <p className="text-xl font-bold text-green-700">{avgHoursPerEmployee}</p>
          </div>
        </div>

        <div className="bg-purple-50 p-4 rounded-lg flex items-center">
          <div className="p-3 bg-purple-100 rounded-full mr-3">
            <HiOutlineCash className="h-6 w-6 text-purple-700" />
          </div>
          <div>
            <p className="text-sm text-purple-700">Avg Salary</p>
            <p className="text-xl font-bold text-purple-700">{formatCurrency(Number(avgSalary))}</p>
          </div>
        </div>

        <div className="bg-yellow-50 p-4 rounded-lg flex items-center">
          <div className="p-3 bg-yellow-100 rounded-full mr-3">
            <HiOutlineCalendar className="h-6 w-6 text-yellow-700" />
          </div>
          <div>
            <p className="text-sm text-yellow-700">Payroll Periods</p>
            <p className="text-xl font-bold text-yellow-700">{payrollPeriods.length}</p>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default EmployeeStats;
