import React, { useState, useEffect } from 'react';
import { Card, Button, TextInput, Select, Table, Badge, Alert, Modal, Textarea, Checkbox } from 'flowbite-react';
import { HiSearch, HiPlus, HiTrash, HiExclamation, <PERSON><PERSON>heck, HiX } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';
import { RefundService } from '../../services/refund';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import EnhancedNumberInput from '../common/EnhancedNumberInput';
import {
  RefundType,
  RefundReason,
  RefundMethod,
  ItemCondition,
  CreateRefundRequest,
  CreateRefundItemRequest,
  RefundValidation
} from '../../types/refund.types';

interface RefundProcessorProps {
  onRefundCreated?: (refund: any) => void;
  onClose?: () => void;
}

interface SelectedItem {
  sale_item_id: string;
  product_id: string;
  product_name: string;
  sku: string;
  original_quantity: number;
  refund_quantity: number;
  unit_price: number;
  condition: ItemCondition;
  restore_inventory: boolean;
  notes?: string;
}

const RefundProcessor: React.FC<RefundProcessorProps> = ({ onRefundCreated, onClose }) => {
  const { currentOrganization } = useOrganization();
  const { settings, refreshSettings } = useOrganizationSettings();
  const formatCurrency = useCurrencyFormatter();

  // Search and sale selection
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [selectedSale, setSelectedSale] = useState<any>(null);
  const [validation, setValidation] = useState<RefundValidation | null>(null);

  // Refund form data
  const [refundType, setRefundType] = useState<RefundType>(RefundType.PARTIAL);
  const [reason, setReason] = useState<RefundReason>(RefundReason.CUSTOMER_CHANGED_MIND);
  const [reasonNotes, setReasonNotes] = useState('');
  const [refundMethod, setRefundMethod] = useState<RefundMethod>(RefundMethod.ORIGINAL_PAYMENT);
  const [selectedItems, setSelectedItems] = useState<SelectedItem[]>([]);
  const [restockingFee, setRestockingFee] = useState(0);

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  // Force refresh settings when component mounts
  useEffect(() => {
    refreshSettings();
  }, []);

  // Handle refund type changes
  useEffect(() => {
    if (selectedSale && validation?.can_refund) {
      if (refundType === RefundType.FULL || refundType === RefundType.EXCHANGE || refundType === RefundType.STORE_CREDIT) {
        // Auto-select all items for full refund, exchange, or store credit
        const items: SelectedItem[] = validation.eligible_items.map(item => {
          const saleItem = selectedSale.sale_items.find((si: any) => si.id === item.sale_item_id);
          const product = saleItem?.product;

          return {
            sale_item_id: item.sale_item_id,
            product_id: item.product_id,
            product_name: product?.name || 'Unknown Product',
            sku: product?.sku || '',
            original_quantity: saleItem?.quantity || 0,
            refund_quantity: item.max_quantity,
            unit_price: item.unit_price,
            condition: ItemCondition.NEW,
            restore_inventory: true
          };
        });
        setSelectedItems(items);
      } else if (refundType === RefundType.PARTIAL) {
        // For partial refunds, clear selection so user can choose
        setSelectedItems([]);
      }
    }
  }, [refundType, selectedSale, validation]);

  // Search for original sales
  const searchSales = async () => {
    if (!searchTerm.trim() || !currentOrganization) return;

    setLoading(true);
    try {
      const result = await RefundService.searchOriginalSale(currentOrganization.id, searchTerm);
      if (result.success) {
        setSearchResults(result.data || []);
      } else {
        setError(result.error || 'Failed to search sales');
      }
    } catch (err) {
      setError('Failed to search sales');
    } finally {
      setLoading(false);
    }
  };

  // Select a sale for refund
  const selectSale = async (sale: any) => {
    if (!currentOrganization) return;

    setLoading(true);
    try {
      const validationResult = await RefundService.validateRefund(sale.id, currentOrganization.id);
      setValidation(validationResult);

      if (validationResult.can_refund) {
        setSelectedSale(sale);
        setSearchResults([]);
        setSearchTerm('');

        // Initialize selected items based on refund type
        if (refundType === RefundType.FULL || refundType === RefundType.EXCHANGE || refundType === RefundType.STORE_CREDIT) {
          const items: SelectedItem[] = validationResult.eligible_items.map(item => {
            const saleItem = sale.sale_items.find((si: any) => si.id === item.sale_item_id);
            const product = saleItem?.product;

            return {
              sale_item_id: item.sale_item_id,
              product_id: item.product_id,
              product_name: product?.name || 'Unknown Product',
              sku: product?.sku || '',
              original_quantity: saleItem?.quantity || 0,
              refund_quantity: item.max_quantity,
              unit_price: item.unit_price,
              condition: ItemCondition.NEW,
              restore_inventory: true
            };
          });
          setSelectedItems(items);
        } else {
          // For partial refunds, start with empty selection
          setSelectedItems([]);
        }
      } else {
        setError(validationResult.reasons.join(', '));
      }
    } catch (err) {
      setError('Failed to validate refund');
    } finally {
      setLoading(false);
    }
  };

  // Add item to refund
  const addItemToRefund = (eligibleItem: any) => {
    const saleItem = selectedSale.sale_items.find((si: any) => si.id === eligibleItem.sale_item_id);
    const product = saleItem?.product;

    const newItem: SelectedItem = {
      sale_item_id: eligibleItem.sale_item_id,
      product_id: eligibleItem.product_id,
      product_name: product?.name || 'Unknown Product',
      sku: product?.sku || '',
      original_quantity: saleItem?.quantity || 0,
      refund_quantity: 1,
      unit_price: eligibleItem.unit_price,
      condition: ItemCondition.NEW,
      restore_inventory: true
    };

    setSelectedItems([...selectedItems, newItem]);
  };

  // Remove item from refund
  const removeItemFromRefund = (index: number) => {
    setSelectedItems(selectedItems.filter((_, i) => i !== index));
  };

  // Update item quantity
  const updateItemQuantity = (index: number, quantity: number) => {
    const updatedItems = [...selectedItems];
    const eligibleItem = validation?.eligible_items.find(
      item => item.sale_item_id === updatedItems[index].sale_item_id
    );

    if (eligibleItem && quantity <= eligibleItem.max_quantity && quantity > 0) {
      updatedItems[index].refund_quantity = quantity;
      setSelectedItems(updatedItems);
    }
  };

  // Update item condition
  const updateItemCondition = (index: number, condition: ItemCondition) => {
    const updatedItems = [...selectedItems];
    updatedItems[index].condition = condition;
    updatedItems[index].restore_inventory = condition === ItemCondition.NEW;
    setSelectedItems(updatedItems);
  };

  // Calculate totals
  const calculateTotals = () => {
    const subtotal = selectedItems.reduce((sum, item) => sum + (item.refund_quantity * item.unit_price), 0);
    const total = subtotal - restockingFee;

    return { subtotal, total };
  };

  // Process refund
  const processRefund = async () => {
    if (!currentOrganization || !selectedSale || selectedItems.length === 0) return;

    setLoading(true);
    try {
      const refundItems: CreateRefundItemRequest[] = selectedItems.map(item => ({
        sale_item_id: item.sale_item_id,
        product_id: item.product_id,
        quantity: item.refund_quantity,
        unit_price: item.unit_price,
        condition: item.condition,
        restore_inventory: item.restore_inventory,
        notes: item.notes
      }));

      const request: CreateRefundRequest = {
        original_sale_id: selectedSale.id,
        refund_type: refundType,
        reason,
        reason_notes: reasonNotes,
        refund_method: refundMethod,
        customer_id: selectedSale.customer?.id,
        items: refundItems,
        restocking_fee: restockingFee,
        requires_approval: calculateTotals().total > 1000 // Business rule
      };

      const result = await RefundService.createRefund(request, currentOrganization.id);

      if (result.success) {
        onRefundCreated?.(result.data);
        resetForm();
        setShowConfirmModal(false);
      } else {
        setError(result.error || 'Failed to create refund');
      }
    } catch (err) {
      setError('Failed to process refund');
    } finally {
      setLoading(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setSelectedSale(null);
    setValidation(null);
    setSelectedItems([]);
    setSearchTerm('');
    setSearchResults([]);
    setReasonNotes('');
    setRestockingFee(0);
    setError(null);
  };

  const { subtotal, total } = calculateTotals();

  return (
    <div className="space-y-6">
      {/* Search for Original Sale */}
      <Card>
        <h3 className="text-lg font-semibold mb-4">Find Original Sale</h3>
        <div className="flex gap-4">
          <TextInput
            placeholder="Enter invoice number..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && searchSales()}
            className="flex-1"
          />
          <Button onClick={searchSales} disabled={loading}>
            <HiSearch className="h-4 w-4 mr-2" />
            Search
          </Button>
        </div>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className="mt-4">
            <h4 className="font-medium mb-2">Search Results</h4>
            <div className="space-y-2">
              {searchResults.map((sale) => (
                <div
                  key={sale.id}
                  className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                  onClick={() => selectSale(sale)}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Invoice: {sale.invoice_number}</p>
                      <p className="text-sm text-gray-600">
                        {new Date(sale.sale_date).toLocaleDateString()} - {sale.customer?.name || 'Walk-in Customer'}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(sale.total_amount)}</p>
                      <p className="text-sm text-gray-600">{sale.sale_items?.length || 0} items</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* Error Display */}
      {error && (
        <Alert color="failure" onDismiss={() => setError(null)}>
          <HiExclamation className="h-4 w-4" />
          {error}
        </Alert>
      )}

      {/* Selected Sale and Refund Form */}
      {selectedSale && validation?.can_refund && (
        <>
          {/* Sale Information */}
          <Card>
            <h3 className="text-lg font-semibold mb-4">Original Sale Information</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Invoice Number</label>
                <p className="text-sm">{selectedSale.invoice_number}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Sale Date</label>
                <p className="text-sm">{new Date(selectedSale.sale_date).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Customer</label>
                <p className="text-sm">{selectedSale.customer?.name || 'Walk-in Customer'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Total Amount</label>
                <p className="text-sm font-medium">{formatCurrency(selectedSale.total_amount)}</p>
              </div>
            </div>
          </Card>

          {/* Refund Configuration */}
          <Card>
            <h3 className="text-lg font-semibold mb-4">Refund Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Refund Type</label>
                <Select
                  value={refundType}
                  onChange={(e) => setRefundType(e.target.value as RefundType)}
                >
                  <option value={RefundType.FULL}>Full Refund</option>
                  <option value={RefundType.PARTIAL}>Partial Refund</option>
                  <option value={RefundType.EXCHANGE}>Exchange</option>
                  <option value={RefundType.STORE_CREDIT}>Store Credit</option>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Reason</label>
                <Select
                  value={reason}
                  onChange={(e) => setReason(e.target.value as RefundReason)}
                >
                  <option value={RefundReason.DEFECTIVE}>Defective</option>
                  <option value={RefundReason.WRONG_ITEM}>Wrong Item</option>
                  <option value={RefundReason.CUSTOMER_CHANGED_MIND}>Customer Changed Mind</option>
                  <option value={RefundReason.DAMAGED_IN_TRANSIT}>Damaged in Transit</option>
                  <option value={RefundReason.NOT_AS_DESCRIBED}>Not as Described</option>
                  <option value={RefundReason.DUPLICATE_ORDER}>Duplicate Order</option>
                  <option value={RefundReason.OTHER}>Other</option>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Refund Method</label>
                <Select
                  value={refundMethod}
                  onChange={(e) => setRefundMethod(e.target.value as RefundMethod)}
                >
                  <option value={RefundMethod.ORIGINAL_PAYMENT}>Original Payment Method</option>
                  <option value={RefundMethod.CASH}>Cash</option>
                  <option value={RefundMethod.CARD}>Card</option>
                  <option value={RefundMethod.STORE_CREDIT}>Store Credit</option>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Restocking Fee</label>
                <EnhancedNumberInput
                  min="0"
                  step="0.01"
                  value={restockingFee}
                  onChange={(e) => setRestockingFee(parseFloat(e.target.value) || 0)}
                  onBlur={(e) => {
                    // If field is empty on blur, reset to 0
                    if (e.target.value === '' || parseFloat(e.target.value) < 0) {
                      setRestockingFee(0);
                    }
                  }}
                  autoSelect={true}
                  preventScrollChange={true}
                />
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium mb-1">Reason Notes</label>
              <Textarea
                value={reasonNotes}
                onChange={(e) => setReasonNotes(e.target.value)}
                placeholder="Additional details about the refund reason..."
                rows={3}
              />
            </div>
          </Card>

          {/* Selected Items Display */}
          {selectedItems.length > 0 && (
            <Card>
              <h3 className="text-lg font-semibold mb-4">Items to Refund</h3>
              <Table>
                <Table.Head>
                  <Table.HeadCell>Product</Table.HeadCell>
                  <Table.HeadCell>Quantity</Table.HeadCell>
                  <Table.HeadCell>Unit Price</Table.HeadCell>
                  <Table.HeadCell>Condition</Table.HeadCell>
                  <Table.HeadCell>Restore Inventory</Table.HeadCell>
                  <Table.HeadCell>Total</Table.HeadCell>
                  {refundType === RefundType.PARTIAL && <Table.HeadCell>Actions</Table.HeadCell>}
                </Table.Head>
                <Table.Body>
                  {selectedItems.map((item, index) => {
                    const eligibleItem = validation?.eligible_items.find(
                      ei => ei.sale_item_id === item.sale_item_id
                    );

                    return (
                      <Table.Row key={item.sale_item_id}>
                        <Table.Cell>
                          <div>
                            <p className="font-medium">{item.product_name}</p>
                            <p className="text-sm text-gray-600">SKU: {item.sku}</p>
                          </div>
                        </Table.Cell>
                        <Table.Cell>
                          {refundType === RefundType.PARTIAL ? (
                            <div>
                              <EnhancedNumberInput
                                min="0.01"
                                step="0.01"
                                max={eligibleItem?.max_quantity || 1}
                                value={item.refund_quantity}
                                onChange={(e) => updateItemQuantity(index, parseFloat(e.target.value) || 0.01)}
                                onBlur={(e) => {
                                  // If field is empty on blur, reset to 0.01
                                  if (e.target.value === '' || parseFloat(e.target.value) <= 0) {
                                    updateItemQuantity(index, 0.01);
                                  }
                                }}
                                className="w-20"
                                autoSelect={true}
                                preventScrollChange={true}
                              />
                              <p className="text-xs text-gray-500">Max: {eligibleItem?.max_quantity}</p>
                            </div>
                          ) : (
                            <span className="font-medium">{item.refund_quantity}</span>
                          )}
                        </Table.Cell>
                        <Table.Cell>{formatCurrency(item.unit_price)}</Table.Cell>
                        <Table.Cell>
                          {refundType === RefundType.PARTIAL ? (
                            <Select
                              value={item.condition}
                              onChange={(e) => updateItemCondition(index, e.target.value as ItemCondition)}
                              className="w-32"
                            >
                              <option value={ItemCondition.NEW}>New</option>
                              <option value={ItemCondition.USED}>Used</option>
                              <option value={ItemCondition.DAMAGED}>Damaged</option>
                              <option value={ItemCondition.DEFECTIVE}>Defective</option>
                            </Select>
                          ) : (
                            <span className="capitalize">{item.condition}</span>
                          )}
                        </Table.Cell>
                        <Table.Cell>
                          {refundType === RefundType.PARTIAL ? (
                            <Checkbox
                              checked={item.restore_inventory}
                              onChange={(e) => {
                                const updatedItems = [...selectedItems];
                                updatedItems[index].restore_inventory = e.target.checked;
                                setSelectedItems(updatedItems);
                              }}
                            />
                          ) : (
                            <span>{item.restore_inventory ? 'Yes' : 'No'}</span>
                          )}
                        </Table.Cell>
                        <Table.Cell className="font-medium">
                          {formatCurrency(item.refund_quantity * item.unit_price)}
                        </Table.Cell>
                        {refundType === RefundType.PARTIAL && (
                          <Table.Cell>
                            <Button
                              size="sm"
                              color="failure"
                              onClick={() => removeItemFromRefund(index)}
                            >
                              <HiTrash className="h-4 w-4" />
                            </Button>
                          </Table.Cell>
                        )}
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            </Card>
          )}

          {/* Item Selection for Partial Refunds */}
          {refundType === RefundType.PARTIAL && (
            <Card>
              <h3 className="text-lg font-semibold mb-4">Select Items to Refund</h3>

              {/* Available Items */}
              <div className="mb-6">
                <h4 className="font-medium mb-2">Available Items</h4>
                <div className="space-y-2">
                  {validation.eligible_items
                    .filter(item => !selectedItems.some(si => si.sale_item_id === item.sale_item_id))
                    .map((item) => {
                      const saleItem = selectedSale.sale_items.find((si: any) => si.id === item.sale_item_id);
                      const product = saleItem?.product;

                      return (
                        <div key={item.sale_item_id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex-1">
                            <p className="font-medium">{product?.name}</p>
                            <p className="text-sm text-gray-600">SKU: {product?.sku}</p>
                            <p className="text-sm text-gray-600">
                              Available: {item.max_quantity} @ {formatCurrency(item.unit_price)} each
                            </p>
                          </div>
                          <Button
                            size="sm"
                            onClick={() => addItemToRefund(item)}
                          >
                            <HiPlus className="h-4 w-4 mr-1" />
                            Add
                          </Button>
                        </div>
                      );
                    })}
                </div>
              </div>


            </Card>
          )}

          {/* Refund Summary */}
          {(selectedItems.length > 0 || refundType !== RefundType.PARTIAL) && (
            <Card>
              <h3 className="text-lg font-semibold mb-4">Refund Summary</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(subtotal)}</span>
                </div>
                {restockingFee > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>Restocking Fee:</span>
                    <span>-{formatCurrency(restockingFee)}</span>
                  </div>
                )}
                <div className="flex justify-between text-lg font-bold border-t pt-2">
                  <span>Total Refund:</span>
                  <span>{formatCurrency(total)}</span>
                </div>
              </div>

              {total > 1000 && (
                <Alert color="warning" className="mt-4">
                  <HiExclamation className="h-4 w-4" />
                  This refund requires manager approval due to the amount exceeding {formatCurrency(1000)}.
                </Alert>
              )}

              <div className="flex gap-4 mt-6">
                <Button
                  onClick={() => setShowConfirmModal(true)}
                  disabled={loading || (refundType === RefundType.PARTIAL && selectedItems.length === 0)}
                  className="flex-1"
                >
                  Process Refund
                </Button>
                <Button
                  color="gray"
                  onClick={resetForm}
                  disabled={loading}
                >
                  Cancel
                </Button>
              </div>
            </Card>
          )}
        </>
      )}

      {/* Confirmation Modal */}
      <Modal show={showConfirmModal} onClose={() => setShowConfirmModal(false)}>
        <Modal.Header>Confirm Refund</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>Are you sure you want to process this refund?</p>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Refund Details</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Invoice:</span>
                  <span>{selectedSale?.invoice_number}</span>
                </div>
                <div className="flex justify-between">
                  <span>Type:</span>
                  <span className="capitalize">{refundType.replace('_', ' ')}</span>
                </div>
                <div className="flex justify-between">
                  <span>Reason:</span>
                  <span className="capitalize">{reason.replace('_', ' ')}</span>
                </div>
                <div className="flex justify-between">
                  <span>Method:</span>
                  <span className="capitalize">{refundMethod.replace('_', ' ')}</span>
                </div>
                <div className="flex justify-between font-medium">
                  <span>Total Amount:</span>
                  <span>{formatCurrency(total)}</span>
                </div>
              </div>
            </div>

            {/* Items to be refunded */}
            {selectedItems.length > 0 && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Items to Refund</h4>
                <div className="space-y-2">
                  {selectedItems.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <div>
                        <span className="font-medium">{item.product_name}</span>
                        <span className="text-gray-600 ml-2">x{item.refund_quantity}</span>
                      </div>
                      <span>{formatCurrency(item.refund_quantity * item.unit_price)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {total > 1000 && (
              <Alert color="warning">
                <HiExclamation className="h-4 w-4" />
                This refund will be pending manager approval.
              </Alert>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={processRefund} disabled={loading}>
            {loading ? 'Processing...' : 'Confirm Refund'}
          </Button>
          <Button color="gray" onClick={() => setShowConfirmModal(false)} disabled={loading}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default RefundProcessor;
