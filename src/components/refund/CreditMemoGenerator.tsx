import React, { useState, useRef } from 'react';
import { <PERSON>, Button, Badge, Alert } from 'flowbite-react';
import { HiOutlinePrinter, HiOutlineDownload, HiOutlineEye } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { RefundWithDetails } from '../../types/refund.types';
import { format } from 'date-fns';

interface CreditMemoGeneratorProps {
  refund: RefundWithDetails;
  onClose?: () => void;
}

const CreditMemoGenerator: React.FC<CreditMemoGeneratorProps> = ({
  refund,
  onClose
}) => {
  const { currentOrganization } = useOrganization();
  const formatCurrency = useCurrencyFormatter();
  const [showPreview, setShowPreview] = useState(false);
  const printRef = useRef<HTMLDivElement>(null);

  const handlePrint = () => {
    const printContent = printRef.current;
    if (printContent) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>Credit Memo - ${refund.refund_number}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .print-content { max-width: 800px; margin: 0 auto; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .text-right { text-align: right; }
                .text-center { text-align: center; }
                .font-bold { font-weight: bold; }
                .text-lg { font-size: 1.125rem; }
                .text-xl { font-size: 1.25rem; }
                .text-3xl { font-size: 1.875rem; }
                .mb-2 { margin-bottom: 0.5rem; }
                .mb-4 { margin-bottom: 1rem; }
                .mb-6 { margin-bottom: 1.5rem; }
                .mb-8 { margin-bottom: 2rem; }
                .pt-2 { padding-top: 0.5rem; }
                .pt-6 { padding-top: 1.5rem; }
                .pb-6 { padding-bottom: 1.5rem; }
                .border-t { border-top: 1px solid #ddd; }
                .border-b-2 { border-bottom: 2px solid #ddd; }
                .bg-gray-50 { background-color: #f9fafb; }
                .p-4 { padding: 1rem; }
                .rounded { border-radius: 0.25rem; }
                .grid { display: grid; }
                .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
                .gap-8 { gap: 2rem; }
                .flex { display: flex; }
                .justify-between { justify-content: space-between; }
                .justify-end { justify-content: flex-end; }
                .items-start { align-items: flex-start; }
                .space-y-2 > * + * { margin-top: 0.5rem; }
                .w-64 { width: 16rem; }
                .text-gray-600 { color: #4b5563; }
                .text-gray-900 { color: #111827; }
                .text-red-600 { color: #dc2626; }
              </style>
            </head>
            <body>
              <div class="print-content">
                ${printContent.innerHTML}
              </div>
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
      }
    }
  };

  const generateCreditMemoNumber = () => {
    // Generate credit memo number based on refund number
    return `CM-${refund.refund_number.replace('REF-', '')}`;
  };

  const CreditMemoDocument = () => (
    <div ref={printRef} className="bg-white p-8 max-w-4xl mx-auto">
      {/* Header */}
      <div className="border-b-2 border-gray-300 pb-6 mb-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">CREDIT MEMO</h1>
            <p className="text-lg text-gray-600 mt-2">
              Credit Memo #: {generateCreditMemoNumber()}
            </p>
          </div>
          <div className="text-right">
            <h2 className="text-xl font-bold text-gray-900">
              {currentOrganization?.name || 'Your Business'}
            </h2>
            {currentOrganization?.address && (
              <p className="text-gray-600">{currentOrganization.address}</p>
            )}
            {currentOrganization?.phone && (
              <p className="text-gray-600">Phone: {currentOrganization.phone}</p>
            )}
            {currentOrganization?.email && (
              <p className="text-gray-600">Email: {currentOrganization.email}</p>
            )}
            {currentOrganization?.website && (
              <p className="text-gray-600">{currentOrganization.website}</p>
            )}
          </div>
        </div>
      </div>

      {/* Credit Memo Details */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="text-lg font-semibold mb-4">Credit To:</h3>
          <div className="bg-gray-50 p-4 rounded">
            <p className="font-medium">
              {refund.original_sale?.customer?.name || 'Walk-in Customer'}
            </p>
            {refund.original_sale?.customer?.email && (
              <p className="text-gray-600">{refund.original_sale.customer.email}</p>
            )}
            {refund.original_sale?.customer?.phone && (
              <p className="text-gray-600">{refund.original_sale.customer.phone}</p>
            )}
          </div>
        </div>
        
        <div>
          <h3 className="text-lg font-semibold mb-4">Credit Details:</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Date Issued:</span>
              <span className="font-medium">
                {format(new Date(refund.created_at), 'MMM dd, yyyy')}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Original Invoice:</span>
              <span className="font-medium">
                {refund.original_sale?.invoice_number}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Refund Type:</span>
              <span className="font-medium capitalize">{refund.refund_type}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Reason:</span>
              <span className="font-medium capitalize">
                {refund.reason.replace(/_/g, ' ')}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Refunded Items:</h3>
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 px-4 py-2 text-left">Item</th>
              <th className="border border-gray-300 px-4 py-2 text-center">Qty</th>
              <th className="border border-gray-300 px-4 py-2 text-right">Unit Price</th>
              <th className="border border-gray-300 px-4 py-2 text-center">Condition</th>
              <th className="border border-gray-300 px-4 py-2 text-right">Total</th>
            </tr>
          </thead>
          <tbody>
            {refund.refund_items?.map((item, index) => (
              <tr key={index}>
                <td className="border border-gray-300 px-4 py-2">
                  <div>
                    <p className="font-medium">{item.product?.name}</p>
                    <p className="text-sm text-gray-600">SKU: {item.product?.sku}</p>
                  </div>
                </td>
                <td className="border border-gray-300 px-4 py-2 text-center">
                  {item.quantity}
                </td>
                <td className="border border-gray-300 px-4 py-2 text-right">
                  {formatCurrency(item.unit_price)}
                </td>
                <td className="border border-gray-300 px-4 py-2 text-center">
                  <Badge color={
                    item.condition === 'new' ? 'success' :
                    item.condition === 'used' ? 'warning' :
                    item.condition === 'damaged' ? 'failure' : 'gray'
                  }>
                    {item.condition}
                  </Badge>
                </td>
                <td className="border border-gray-300 px-4 py-2 text-right">
                  {formatCurrency(item.total_price)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-64">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>{formatCurrency(refund.subtotal)}</span>
            </div>
            {refund.restocking_fee > 0 && (
              <div className="flex justify-between text-red-600">
                <span>Restocking Fee:</span>
                <span>-{formatCurrency(refund.restocking_fee)}</span>
              </div>
            )}
            <div className="border-t pt-2 flex justify-between font-bold text-lg">
              <span>Total Credit:</span>
              <span>{formatCurrency(refund.total_amount)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t pt-6 text-center text-gray-600">
        <p className="mb-2">
          <strong>Refund Method:</strong> {refund.refund_method.replace(/_/g, ' ').toUpperCase()}
        </p>
        {refund.reason_notes && (
          <p className="mb-2">
            <strong>Notes:</strong> {refund.reason_notes}
          </p>
        )}
        <p className="text-sm">
          This credit memo was generated on {format(new Date(), 'MMM dd, yyyy HH:mm')}
        </p>
        <p className="text-sm mt-2">
          Thank you for your business!
        </p>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <Card className="p-4">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">Credit Memo</h3>
            <p className="text-gray-600">
              Generate and manage credit memo for refund {refund.refund_number}
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              color="light"
              onClick={() => setShowPreview(!showPreview)}
            >
              <HiOutlineEye className="mr-2 h-4 w-4" />
              {showPreview ? 'Hide' : 'Preview'}
            </Button>
            <Button
              color="info"
              onClick={handlePrint}
            >
              <HiOutlinePrinter className="mr-2 h-4 w-4" />
              Print
            </Button>
          </div>
        </div>
      </Card>

      {/* Preview */}
      {showPreview && (
        <Card className="p-0">
          <div className="max-h-96 overflow-y-auto">
            <CreditMemoDocument />
          </div>
        </Card>
      )}

      {/* Hidden print component */}
      <div className="hidden">
        <CreditMemoDocument />
      </div>
    </div>
  );
};

export default CreditMemoGenerator;
