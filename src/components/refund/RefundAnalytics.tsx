import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Badge } from 'flowbite-react';
import { HiOutlineRefresh, HiOutlineTrendingUp, HiOutlineTrendingDown } from 'react-icons/hi';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { useOrganization } from '../../context/OrganizationContext';
import { RefundService } from '../../services/refund';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';

interface RefundAnalyticsData {
  totalRefunds: number;
  totalAmount: number;
  averageRefundAmount: number;
  refundRate: number;
  byReason: { [key: string]: { count: number; amount: number } };
  byMethod: { [key: string]: { count: number; amount: number } };
  byCondition: { [key: string]: { count: number; amount: number } };
  dailyTrend: Array<{ date: string; count: number; amount: number }>;
  topRefundedProducts: Array<{ product_name: string; count: number; amount: number }>;
}

const RefundAnalytics: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const formatCurrency = useCurrencyFormatter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState('30days');
  const [analyticsData, setAnalyticsData] = useState<RefundAnalyticsData>({
    totalRefunds: 0,
    totalAmount: 0,
    averageRefundAmount: 0,
    refundRate: 0,
    byReason: {},
    byMethod: {},
    byCondition: {},
    dailyTrend: [],
    topRefundedProducts: []
  });

  useEffect(() => {
    if (currentOrganization) {
      fetchAnalyticsData();
    }
  }, [currentOrganization, dateRange]);

  const getDateRange = () => {
    const endDate = endOfDay(new Date());
    let startDate: Date;

    switch (dateRange) {
      case 'today':
        startDate = startOfDay(new Date());
        break;
      case '7days':
        startDate = startOfDay(subDays(new Date(), 7));
        break;
      case '30days':
        startDate = startOfDay(subDays(new Date(), 30));
        break;
      case '90days':
        startDate = startOfDay(subDays(new Date(), 90));
        break;
      default:
        startDate = startOfDay(subDays(new Date(), 30));
    }

    return { startDate, endDate };
  };

  const fetchAnalyticsData = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { startDate, endDate } = getDateRange();

      // Fetch refunds data
      const refundResponse = await RefundService.getRefunds(currentOrganization.id, {
        filters: {
          date_from: startDate.toISOString(),
          date_to: endDate.toISOString()
        },
        limit: 1000
      });

      if (!refundResponse.success || !refundResponse.data) {
        throw new Error('Failed to fetch refund data');
      }

      const refunds = refundResponse.data;
      const processedRefunds = refunds.filter(r => r.status === 'processed');

      // Calculate analytics
      const totalRefunds = processedRefunds.length;
      const totalAmount = processedRefunds.reduce((sum, r) => sum + Number(r.total_amount), 0);
      const averageRefundAmount = totalRefunds > 0 ? totalAmount / totalRefunds : 0;

      // Group by reason
      const byReason: { [key: string]: { count: number; amount: number } } = {};
      processedRefunds.forEach(refund => {
        const reason = refund.reason.replace(/_/g, ' ');
        if (!byReason[reason]) {
          byReason[reason] = { count: 0, amount: 0 };
        }
        byReason[reason].count++;
        byReason[reason].amount += Number(refund.total_amount);
      });

      // Group by method
      const byMethod: { [key: string]: { count: number; amount: number } } = {};
      processedRefunds.forEach(refund => {
        const method = refund.refund_method.replace(/_/g, ' ');
        if (!byMethod[method]) {
          byMethod[method] = { count: 0, amount: 0 };
        }
        byMethod[method].count++;
        byMethod[method].amount += Number(refund.total_amount);
      });

      // Group by condition (from refund items)
      const byCondition: { [key: string]: { count: number; amount: number } } = {};
      processedRefunds.forEach(refund => {
        refund.refund_items?.forEach(item => {
          const condition = item.condition;
          if (!byCondition[condition]) {
            byCondition[condition] = { count: 0, amount: 0 };
          }
          byCondition[condition].count++;
          byCondition[condition].amount += Number(item.total_price);
        });
      });

      // Daily trend
      const dailyTrend: Array<{ date: string; count: number; amount: number }> = [];
      const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      
      for (let i = 0; i < days; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        const dateStr = format(date, 'yyyy-MM-dd');
        
        const dayRefunds = processedRefunds.filter(r => 
          format(new Date(r.created_at), 'yyyy-MM-dd') === dateStr
        );
        
        dailyTrend.push({
          date: format(date, 'MMM dd'),
          count: dayRefunds.length,
          amount: dayRefunds.reduce((sum, r) => sum + Number(r.total_amount), 0)
        });
      }

      // Top refunded products
      const productRefunds: { [key: string]: { count: number; amount: number; name: string } } = {};
      processedRefunds.forEach(refund => {
        refund.refund_items?.forEach(item => {
          const productId = item.product_id;
          const productName = item.product?.name || 'Unknown Product';
          if (!productRefunds[productId]) {
            productRefunds[productId] = { count: 0, amount: 0, name: productName };
          }
          productRefunds[productId].count += item.quantity;
          productRefunds[productId].amount += Number(item.total_price);
        });
      });

      const topRefundedProducts = Object.values(productRefunds)
        .sort((a, b) => b.amount - a.amount)
        .slice(0, 10)
        .map(p => ({
          product_name: p.name,
          count: p.count,
          amount: p.amount
        }));

      setAnalyticsData({
        totalRefunds,
        totalAmount,
        averageRefundAmount,
        refundRate: 0, // Would need sales data to calculate
        byReason,
        byMethod,
        byCondition,
        dailyTrend,
        topRefundedProducts
      });

    } catch (err: any) {
      setError(err.message || 'Failed to fetch analytics data');
    } finally {
      setLoading(false);
    }
  };

  // Chart configurations
  const trendChartOptions: ApexOptions = {
    chart: {
      type: 'area',
      height: 300,
      toolbar: { show: false }
    },
    dataLabels: { enabled: false },
    stroke: { curve: 'smooth', width: 2 },
    xaxis: {
      categories: analyticsData.dailyTrend.map(d => d.date),
      labels: { style: { fontSize: '12px' } }
    },
    yaxis: [
      {
        title: { text: 'Refund Count' },
        labels: { style: { fontSize: '12px' } }
      },
      {
        opposite: true,
        title: { text: 'Amount' },
        labels: { 
          style: { fontSize: '12px' },
          formatter: (value) => formatCurrency(value)
        }
      }
    ],
    colors: ['#3B82F6', '#EF4444'],
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.3
      }
    }
  };

  const trendChartSeries = [
    {
      name: 'Refund Count',
      data: analyticsData.dailyTrend.map(d => d.count),
      yAxisIndex: 0
    },
    {
      name: 'Refund Amount',
      data: analyticsData.dailyTrend.map(d => d.amount),
      yAxisIndex: 1
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Refund Analytics</h2>
          <p className="text-gray-600">Analyze refund patterns and trends</p>
        </div>
        <div className="flex items-center space-x-3">
          <Select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="w-40"
          >
            <option value="today">Today</option>
            <option value="7days">Last 7 days</option>
            <option value="30days">Last 30 days</option>
            <option value="90days">Last 90 days</option>
          </Select>
          <Button color="light" onClick={fetchAnalyticsData} disabled={loading}>
            <HiOutlineRefresh className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {error && (
        <Alert color="failure">
          <h3 className="font-medium">Error</h3>
          <p>{error}</p>
        </Alert>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-600">Total Refunds</p>
              <p className="text-2xl font-bold text-gray-900">
                {analyticsData.totalRefunds}
              </p>
            </div>
            <HiOutlineTrendingUp className="h-8 w-8 text-blue-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-600">Total Amount</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(analyticsData.totalAmount)}
              </p>
            </div>
            <HiOutlineTrendingDown className="h-8 w-8 text-red-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-600">Average Refund</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(analyticsData.averageRefundAmount)}
              </p>
            </div>
            <HiOutlineTrendingUp className="h-8 w-8 text-yellow-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-600">Refund Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {analyticsData.refundRate.toFixed(1)}%
              </p>
            </div>
            <HiOutlineTrendingDown className="h-8 w-8 text-green-500" />
          </div>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Trend Chart */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Refund Trend</h3>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Spinner size="xl" />
            </div>
          ) : (
            <Chart
              options={trendChartOptions}
              series={trendChartSeries}
              type="area"
              height={300}
            />
          )}
        </Card>

        {/* Top Products */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Most Refunded Products</h3>
          <div className="space-y-3">
            {analyticsData.topRefundedProducts.slice(0, 5).map((product, index) => (
              <div key={index} className="flex justify-between items-center">
                <div className="flex-1">
                  <p className="font-medium truncate">{product.product_name}</p>
                  <p className="text-sm text-gray-600">{product.count} items</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-red-600">
                    {formatCurrency(product.amount)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Breakdown Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* By Reason */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Refunds by Reason</h3>
          <div className="space-y-2">
            {Object.entries(analyticsData.byReason).map(([reason, data]) => (
              <div key={reason} className="flex justify-between items-center">
                <div>
                  <p className="font-medium capitalize">{reason}</p>
                  <p className="text-sm text-gray-600">{data.count} refunds</p>
                </div>
                <p className="font-medium">{formatCurrency(data.amount)}</p>
              </div>
            ))}
          </div>
        </Card>

        {/* By Method */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Refunds by Method</h3>
          <div className="space-y-2">
            {Object.entries(analyticsData.byMethod).map(([method, data]) => (
              <div key={method} className="flex justify-between items-center">
                <div>
                  <p className="font-medium capitalize">{method}</p>
                  <p className="text-sm text-gray-600">{data.count} refunds</p>
                </div>
                <p className="font-medium">{formatCurrency(data.amount)}</p>
              </div>
            ))}
          </div>
        </Card>

        {/* By Condition */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Items by Condition</h3>
          <div className="space-y-2">
            {Object.entries(analyticsData.byCondition).map(([condition, data]) => (
              <div key={condition} className="flex justify-between items-center">
                <div>
                  <Badge color={
                    condition === 'new' ? 'success' :
                    condition === 'used' ? 'warning' :
                    condition === 'damaged' ? 'failure' : 'gray'
                  }>
                    {condition}
                  </Badge>
                  <p className="text-sm text-gray-600 mt-1">{data.count} items</p>
                </div>
                <p className="font-medium">{formatCurrency(data.amount)}</p>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default RefundAnalytics;
