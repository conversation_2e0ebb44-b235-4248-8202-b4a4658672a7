import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>, Badge, Al<PERSON>, Spin<PERSON> } from 'flowbite-react';
import { HiOutlinePrinter, HiOutlineEye, HiOutlineRefresh } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { RefundService } from '../../services/refund';
import { getSaleById } from '../../services/sale';
import { format } from 'date-fns';

interface ReceiptAnnotationProps {
  saleId: string;
  onClose?: () => void;
}

interface AnnotatedReceiptData {
  sale: any;
  refunds: any[];
  totalRefunded: number;
  refundPercentage: number;
}

const ReceiptAnnotation: React.FC<ReceiptAnnotationProps> = ({
  saleId,
  onClose
}) => {
  const { currentOrganization } = useOrganization();
  const formatCurrency = useCurrencyFormatter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [receiptData, setReceiptData] = useState<AnnotatedReceiptData | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const printRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchReceiptData();
  }, [saleId]);

  const fetchReceiptData = async () => {
    if (!currentOrganization || !saleId) {
      setError('Missing organization or sale ID');
      setLoading(false);
      return;
    }

    console.log('Fetching receipt data for sale ID:', saleId);
    setLoading(true);
    setError(null);

    try {
      // Fetch sale details
      const { sale, error: saleError } = await getSaleById(currentOrganization.id, saleId);
      if (saleError || !sale) {
        throw new Error(saleError || 'Sale not found');
      }

      // Fetch refunds for this sale
      const refundResponse = await RefundService.getRefunds(currentOrganization.id, {
        filters: {
          // We'll need to add a filter by original_sale_id
        },
        limit: 100
      });

      const refunds = refundResponse.success 
        ? refundResponse.data?.filter(r => r.original_sale_id === saleId) || []
        : [];

      const totalRefunded = refunds
        .filter(r => r.status === 'processed')
        .reduce((sum, r) => sum + Number(r.total_amount), 0);

      const refundPercentage = sale.total_amount > 0 
        ? (totalRefunded / sale.total_amount) * 100 
        : 0;

      setReceiptData({
        sale,
        refunds,
        totalRefunded,
        refundPercentage
      });

    } catch (err: any) {
      setError(err.message || 'Failed to fetch receipt data');
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    const printContent = printRef.current;
    if (printContent) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>Receipt - ${receiptData?.sale.invoice_number}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .receipt { max-width: 400px; margin: 0 auto; font-size: 12px; }
                .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px; }
                .item-row { display: flex; justify-content: space-between; margin: 2px 0; }
                .total-row { border-top: 1px solid #000; padding-top: 5px; margin-top: 10px; font-weight: bold; }
                .refund-section { border-top: 2px dashed #000; margin-top: 15px; padding-top: 10px; }
                .refund-item { color: #dc2626; }
                .text-center { text-align: center; }
                .font-bold { font-weight: bold; }
                .text-red { color: #dc2626; }
                .text-green { color: #059669; }
                .border-dashed { border-style: dashed; }
              </style>
            </head>
            <body>
              ${printContent.innerHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
      }
    }
  };

  const getRefundStatus = () => {
    if (!receiptData) return null;
    
    if (receiptData.refundPercentage >= 100) {
      return { label: 'FULLY REFUNDED', color: 'failure' };
    } else if (receiptData.refundPercentage > 0) {
      return { 
        label: `PARTIALLY REFUNDED (${receiptData.refundPercentage.toFixed(1)}%)`, 
        color: 'warning' 
      };
    }
    return null;
  };

  const AnnotatedReceiptDocument = () => {
    if (!receiptData) return null;

    const { sale, refunds, totalRefunded } = receiptData;
    const refundStatus = getRefundStatus();

    return (
      <div ref={printRef} className="receipt bg-white p-6 max-w-md mx-auto border">
        {/* Header */}
        <div className="header text-center border-b-2 border-black pb-4 mb-4">
          <h2 className="text-lg font-bold">{currentOrganization?.name || 'Your Business'}</h2>
          {currentOrganization?.address && (
            <p className="text-sm">{currentOrganization.address}</p>
          )}
          {currentOrganization?.phone && (
            <p className="text-sm">Phone: {currentOrganization.phone}</p>
          )}
          {currentOrganization?.email && (
            <p className="text-sm">Email: {currentOrganization.email}</p>
          )}
          {currentOrganization?.website && (
            <p className="text-sm">{currentOrganization.website}</p>
          )}
        </div>

        {/* Receipt Info */}
        <div className="mb-4">
          <div className="flex justify-between">
            <span>Receipt #:</span>
            <span className="font-bold">{sale.invoice_number}</span>
          </div>
          <div className="flex justify-between">
            <span>Date:</span>
            <span>{format(new Date(sale.sale_date), 'MMM dd, yyyy HH:mm')}</span>
          </div>
          {sale.customer && (
            <div className="flex justify-between">
              <span>Customer:</span>
              <span>{sale.customer.name}</span>
            </div>
          )}
        </div>

        {/* Refund Status Badge */}
        {refundStatus && (
          <div className="mb-4 text-center">
            <Badge color={refundStatus.color as any} size="lg">
              {refundStatus.label}
            </Badge>
          </div>
        )}

        {/* Items */}
        <div className="mb-4">
          <h3 className="font-bold mb-2">Items:</h3>
          {sale.items?.map((item: any, index: number) => (
            <div key={index} className="item-row flex justify-between mb-1">
              <div className="flex-1">
                <div className="font-medium">{item.product?.name}</div>
                <div className="text-sm text-gray-600">
                  {item.quantity} x {formatCurrency(item.unit_price)}
                </div>
              </div>
              <div className="text-right">
                {formatCurrency(item.total_amount)}
              </div>
            </div>
          ))}
        </div>

        {/* Totals */}
        <div className="border-t border-black pt-2">
          <div className="flex justify-between">
            <span>Subtotal:</span>
            <span>{formatCurrency(sale.subtotal)}</span>
          </div>
          <div className="flex justify-between">
            <span>Tax:</span>
            <span>{formatCurrency(sale.tax_amount)}</span>
          </div>
          <div className="flex justify-between">
            <span>Discount:</span>
            <span>-{formatCurrency(sale.discount_amount)}</span>
          </div>
          <div className="flex justify-between font-bold text-lg border-t pt-1 mt-1">
            <span>Total:</span>
            <span>{formatCurrency(sale.total_amount)}</span>
          </div>
        </div>

        {/* Payment Info */}
        <div className="mt-4">
          <div className="flex justify-between">
            <span>Payment Method:</span>
            <span className="capitalize">{sale.payment_method || 'Cash'}</span>
          </div>
          {sale.cash_tendered && (
            <>
              <div className="flex justify-between">
                <span>Cash Tendered:</span>
                <span>{formatCurrency(sale.cash_tendered)}</span>
              </div>
              <div className="flex justify-between">
                <span>Change:</span>
                <span>{formatCurrency(sale.change_amount || 0)}</span>
              </div>
            </>
          )}
        </div>

        {/* Refund Section */}
        {refunds.length > 0 && (
          <div className="refund-section border-t-2 border-dashed border-black mt-6 pt-4">
            <h3 className="font-bold mb-2 text-red-600">REFUND HISTORY:</h3>
            {refunds.map((refund, index) => (
              <div key={index} className="mb-3 text-sm">
                <div className="flex justify-between font-medium">
                  <span>Refund #{refund.refund_number}</span>
                  <span className="text-red-600">-{formatCurrency(refund.total_amount)}</span>
                </div>
                <div className="text-gray-600">
                  <div>Date: {format(new Date(refund.created_at), 'MMM dd, yyyy')}</div>
                  <div>Reason: {refund.reason.replace(/_/g, ' ')}</div>
                  <div>Method: {refund.refund_method.replace(/_/g, ' ')}</div>
                </div>
              </div>
            ))}
            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between font-bold text-red-600">
                <span>Total Refunded:</span>
                <span>-{formatCurrency(totalRefunded)}</span>
              </div>
              <div className="flex justify-between font-bold">
                <span>Net Amount:</span>
                <span>{formatCurrency(sale.total_amount - totalRefunded)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="text-center mt-6 text-sm text-gray-600">
          <p>Thank you for your business!</p>
          <p className="mt-2">
            Receipt reprinted on {format(new Date(), 'MMM dd, yyyy HH:mm')}
          </p>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card className="p-8">
        <div className="flex justify-center items-center">
          <Spinner size="xl" />
          <span className="ml-3">Loading receipt data...</span>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <Alert color="failure">
          <h3 className="font-medium">Error Loading Receipt</h3>
          <p>{error}</p>
          <div className="mt-4">
            <Button size="sm" onClick={fetchReceiptData}>
              <HiOutlineRefresh className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </Alert>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <Card className="p-4">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">Annotated Receipt</h3>
            <p className="text-gray-600">
              Receipt with refund annotations for {receiptData?.sale.invoice_number}
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              color="light"
              onClick={() => setShowPreview(!showPreview)}
            >
              <HiOutlineEye className="mr-2 h-4 w-4" />
              {showPreview ? 'Hide' : 'Preview'}
            </Button>
            <Button
              color="info"
              onClick={handlePrint}
            >
              <HiOutlinePrinter className="mr-2 h-4 w-4" />
              Print Receipt
            </Button>
          </div>
        </div>
      </Card>

      {/* Preview */}
      {showPreview && (
        <Card className="p-0">
          <div className="max-h-96 overflow-y-auto">
            <AnnotatedReceiptDocument />
          </div>
        </Card>
      )}

      {/* Hidden print component */}
      <div className="hidden">
        <AnnotatedReceiptDocument />
      </div>
    </div>
  );
};

export default ReceiptAnnotation;
