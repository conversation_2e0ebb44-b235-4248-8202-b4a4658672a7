import { useEffect, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { checkUserProfile, checkUserOrganizations, createUserProfile, createUserOrganization } from '../services/userProfile';
import { Spinner } from 'flowbite-react';
import { supabase } from '../lib/supabase';

const OnboardingCheck = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        console.log('Checking onboarding status for user:', user.id);

        // First check the database for onboarding completion
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('onboarding_completed, onboarding_completed_at')
          .eq('id', user.id)
          .maybeSingle();

        if (profileError) {
          console.error('Error checking profile onboarding status:', profileError);
          // If there's an error accessing the profile, redirect to onboarding
          navigate('/onboarding');
          return;
        }

        // If user has a profile and onboarding is marked as completed
        if (profile && profile.onboarding_completed) {
          console.log('User has completed onboarding in database');

          // Also set localStorage for faster future checks
          localStorage.setItem(`onboarding_completed_${user.id}`, 'true');

          // Clean up any leftover onboarding data
          localStorage.removeItem(`onboarding_data_${user.id}`);

          setLoading(false);
          return;
        }

        // If no profile or onboarding not completed, check localStorage as fallback
        const localOnboardingCompleted = localStorage.getItem(`onboarding_completed_${user.id}`);

        if (localOnboardingCompleted && profile) {
          console.log('User has completed onboarding in localStorage but not in database, updating database');

          // Update the database to match localStorage
          const { error: updateError } = await supabase
            .rpc('mark_onboarding_completed', { user_id: user.id });

          if (updateError) {
            console.error('Error updating onboarding status in database:', updateError);
          }

          setLoading(false);
          return;
        }

        // If neither database nor localStorage indicates completion, redirect to onboarding
        console.log('User has not completed onboarding, redirecting');
        localStorage.removeItem(`onboarding_data_${user.id}`);
        navigate('/onboarding');
        return;

      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // If there's an error, redirect to onboarding to be safe
        navigate('/onboarding');
        return;
      }

      setLoading(false);
    };

    checkOnboardingStatus();
  }, [user, navigate]);

  if (loading) {
    return (
      <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50">
        <div className="text-center">
          <Spinner size="xl" />
          <p className="mt-2 text-white">Checking your account setup...</p>
        </div>
      </div>
    );
  }

  return null;
};

export default OnboardingCheck;
