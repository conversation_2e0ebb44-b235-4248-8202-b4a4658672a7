import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Spin<PERSON>,
  Table,
  Badge,
  Checkbox,
  Tabs
} from 'flowbite-react';
import {
  HiOutlineUpload,
  HiOutlineDownload,
  HiOutlineX,
  HiOutlineCheck,
  HiOutlineExclamation,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';

export interface ImportResult {
  success: boolean;
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: string[];
  warnings: string[];
  createdItems: any[];
}

export interface ImportPreview {
  isValid: boolean;
  headers: string[];
  sampleData: any[];
  errors: string[];
  warnings: string[];
  totalRows: number;
}

interface GenericImportProps {
  show: boolean;
  onClose: () => void;
  onImportComplete: (result: ImportResult) => void;
  title: string;
  entityName: string; // e.g., "customers", "suppliers", "employees"
  previewFunction: (csvText: string) => ImportPreview;
  importFunction: (organizationId: string, csvText: string, userId: string, skipDuplicates: boolean) => Promise<ImportResult>;
  downloadTemplateFunction: () => void;
}

const GenericImport: React.FC<GenericImportProps> = ({
  show,
  onClose,
  onImportComplete,
  title,
  entityName,
  previewFunction,
  importFunction,
  downloadTemplateFunction
}) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State
  const [step, setStep] = useState<'upload' | 'preview' | 'importing' | 'complete'>('upload');
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [csvText, setCsvText] = useState<string>('');
  const [preview, setPreview] = useState<ImportPreview | null>(null);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [skipDuplicates, setSkipDuplicates] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [importing, setImporting] = useState(false);

  // Reset state when modal opens/closes
  React.useEffect(() => {
    if (show) {
      setStep('upload');
      setCsvFile(null);
      setCsvText('');
      setPreview(null);
      setImportResult(null);
      setError(null);
      setImporting(false);
    }
  }, [show]);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      setError('Please select a CSV file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size must be less than 5MB');
      return;
    }

    setCsvFile(file);
    setError(null);

    // Read file content
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      setCsvText(text);
      
      // Generate preview
      const previewResult = previewFunction(text);
      setPreview(previewResult);
      setStep('preview');
    };
    reader.onerror = () => {
      setError('Failed to read file');
    };
    reader.readAsText(file);
  };

  // Handle import
  const handleImport = async () => {
    if (!currentOrganization || !user || !csvText) return;

    setImporting(true);
    setStep('importing');
    setError(null);

    try {
      const result = await importFunction(
        currentOrganization.id,
        csvText,
        user.id,
        skipDuplicates
      );

      setImportResult(result);
      setStep('complete');
      onImportComplete(result);
    } catch (err: any) {
      setError(err.message || 'Import failed');
      setStep('preview');
    } finally {
      setImporting(false);
    }
  };

  // Handle close
  const handleClose = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onClose();
  };

  // Render upload step
  const renderUploadStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <HiOutlineUpload className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-lg font-medium text-gray-900">{title}</h3>
        <p className="mt-1 text-sm text-gray-500">
          Upload a CSV file to bulk import {entityName} into your system
        </p>
      </div>

      {/* Template Download */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <HiOutlineInformationCircle className="h-5 w-5 text-blue-400 mt-0.5" />
          <div className="ml-3 flex-1">
            <h4 className="text-sm font-medium text-blue-800">Need a template?</h4>
            <p className="mt-1 text-sm text-blue-700">
              Download our CSV template with sample data to get started quickly.
            </p>
            <Button
              size="xs"
              color="light"
              className="mt-2"
              onClick={downloadTemplateFunction}
            >
              <HiOutlineDownload className="mr-1 h-4 w-4" />
              Download Template
            </Button>
          </div>
        </div>
      </div>

      {/* File Upload */}
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
        <div className="text-center">
          <input
            ref={fileInputRef}
            type="file"
            accept=".csv"
            onChange={handleFileSelect}
            className="hidden"
          />
          <Button
            color="primary"
            onClick={() => fileInputRef.current?.click()}
          >
            <HiOutlineUpload className="mr-2 h-5 w-5" />
            Choose CSV File
          </Button>
          <p className="mt-2 text-sm text-gray-500">
            or drag and drop your CSV file here
          </p>
        </div>
      </div>

      {/* Requirements */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-900 mb-2">CSV Requirements:</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• First row must contain column headers</li>
          <li>• Maximum file size: 5MB</li>
          <li>• Use comma (,) as field separator</li>
          <li>• Enclose text fields in quotes if they contain commas</li>
        </ul>
      </div>

      {error && (
        <Alert color="failure">
          {error}
        </Alert>
      )}
    </div>
  );

  // Render preview step
  const renderPreviewStep = () => {
    if (!preview) return null;

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Import Preview</h3>
            <p className="text-sm text-gray-500">
              Review your data before importing {preview.totalRows} {entityName}
            </p>
          </div>
          <Badge color={preview.isValid ? 'success' : 'failure'}>
            {preview.isValid ? 'Valid' : 'Has Errors'}
          </Badge>
        </div>

        {/* Validation Results */}
        {(preview.errors.length > 0 || preview.warnings.length > 0) && (
          <div className="space-y-3">
            {preview.errors.length > 0 && (
              <Alert color="failure">
                <div className="font-medium">Errors found ({preview.errors.length}):</div>
                <ul className="mt-2 text-sm list-disc list-inside">
                  {preview.errors.slice(0, 5).map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                  {preview.errors.length > 5 && (
                    <li>... and {preview.errors.length - 5} more errors</li>
                  )}
                </ul>
              </Alert>
            )}

            {preview.warnings.length > 0 && (
              <Alert color="warning">
                <div className="font-medium">Warnings ({preview.warnings.length}):</div>
                <ul className="mt-2 text-sm list-disc list-inside">
                  {preview.warnings.slice(0, 3).map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                  {preview.warnings.length > 3 && (
                    <li>... and {preview.warnings.length - 3} more warnings</li>
                  )}
                </ul>
              </Alert>
            )}
          </div>
        )}

        {/* Sample Data Preview */}
        {preview.sampleData.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">
              Sample Data (first 5 rows):
            </h4>
            <div className="overflow-x-auto">
              <Table>
                <Table.Head>
                  {preview.headers.map((header) => (
                    <Table.HeadCell key={header}>{header}</Table.HeadCell>
                  ))}
                </Table.Head>
                <Table.Body>
                  {preview.sampleData.map((row, index) => (
                    <Table.Row key={index}>
                      {preview.headers.map((header) => (
                        <Table.Cell key={header}>
                          {String(row[header] || '')}
                        </Table.Cell>
                      ))}
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </div>
          </div>
        )}

        {/* Import Options */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Import Options:</h4>
          <div className="flex items-center">
            <Checkbox
              id="skipDuplicates"
              checked={skipDuplicates}
              onChange={(e) => setSkipDuplicates(e.target.checked)}
            />
            <label htmlFor="skipDuplicates" className="ml-2 text-sm text-gray-700">
              Skip duplicate entries (recommended)
            </label>
          </div>
        </div>

        {error && (
          <Alert color="failure">
            {error}
          </Alert>
        )}
      </div>
    );
  };

  // Render importing step
  const renderImportingStep = () => (
    <div className="space-y-6 text-center">
      <Spinner size="xl" />
      <div>
        <h3 className="text-lg font-medium text-gray-900">Importing {entityName}...</h3>
        <p className="text-sm text-gray-500">
          Please wait while we process your file. This may take a few moments.
        </p>
      </div>
    </div>
  );

  // Render complete step
  const renderCompleteStep = () => {
    if (!importResult) return null;

    return (
      <div className="space-y-6">
        <div className="text-center">
          {importResult.success ? (
            <HiOutlineCheck className="mx-auto h-12 w-12 text-green-500" />
          ) : (
            <HiOutlineExclamation className="mx-auto h-12 w-12 text-red-500" />
          )}
          <h3 className="mt-2 text-lg font-medium text-gray-900">
            Import {importResult.success ? 'Completed' : 'Completed with Errors'}
          </h3>
        </div>

        {/* Results Summary */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{importResult.successCount}</div>
            <div className="text-sm text-green-700">Imported</div>
          </div>
          <div className="text-center p-4 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{importResult.errorCount}</div>
            <div className="text-sm text-red-700">Errors</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-600">{importResult.totalRows}</div>
            <div className="text-sm text-gray-700">Total</div>
          </div>
        </div>

        {/* Errors and Warnings */}
        {(importResult.errors.length > 0 || importResult.warnings.length > 0) && (
          <Tabs aria-label="Import results">
            {importResult.errors.length > 0 && (
              <Tabs.Item title={`Errors (${importResult.errors.length})`}>
                <div className="max-h-60 overflow-y-auto">
                  <ul className="text-sm text-red-600 space-y-1">
                    {importResult.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              </Tabs.Item>
            )}
            {importResult.warnings.length > 0 && (
              <Tabs.Item title={`Warnings (${importResult.warnings.length})`}>
                <div className="max-h-60 overflow-y-auto">
                  <ul className="text-sm text-yellow-600 space-y-1">
                    {importResult.warnings.map((warning, index) => (
                      <li key={index}>• {warning}</li>
                    ))}
                  </ul>
                </div>
              </Tabs.Item>
            )}
          </Tabs>
        )}
      </div>
    );
  };

  return (
    <Modal show={show} onClose={handleClose} size="4xl">
      <Modal.Header>
        <div className="flex items-center gap-2">
          <HiOutlineUpload className="h-6 w-6" />
          {title}
        </div>
      </Modal.Header>
      <Modal.Body>
        {step === 'upload' && renderUploadStep()}
        {step === 'preview' && renderPreviewStep()}
        {step === 'importing' && renderImportingStep()}
        {step === 'complete' && renderCompleteStep()}
      </Modal.Body>
      <Modal.Footer>
        <div className="flex justify-between w-full">
          <div>
            {step === 'preview' && (
              <Button
                color="light"
                onClick={() => setStep('upload')}
              >
                Back
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            <Button color="gray" onClick={handleClose}>
              {step === 'complete' ? 'Close' : 'Cancel'}
            </Button>
            {step === 'preview' && preview?.isValid && (
              <Button
                color="primary"
                onClick={handleImport}
                disabled={importing}
              >
                {importing ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Importing...
                  </>
                ) : (
                  `Import ${entityName}`
                )}
              </Button>
            )}
          </div>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default GenericImport;
