import React from 'react';
import { <PERSON><PERSON>, Select } from 'flowbite-react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
  itemName?: string; // e.g., "products", "customers", "receipts"
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  itemsPerPage,
  totalItems,
  onPageChange,
  onItemsPerPageChange,
  itemName = 'items'
}) => {
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;

  if (totalItems === 0) {
    return null;
  }

  return (
    <div className="flex flex-col md:flex-row items-center justify-between mt-4">
      <div className="flex items-center gap-2 mb-2 md:mb-0">
        <span className="text-sm text-gray-500">Items per page:</span>
        <Select
          id="itemsPerPage"
          value={itemsPerPage}
          onChange={(e) => onItemsPerPageChange(parseInt(e.target.value))}
          className="w-20"
          size="sm"
        >
          <option value="5">5</option>
          <option value="10">10</option>
          <option value="25">25</option>
          <option value="50">50</option>
          <option value="100">100</option>
        </Select>
        <span className="text-sm text-gray-500">
          Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, totalItems)} of {totalItems} {itemName}
        </span>
      </div>
      
      {totalPages > 1 && (
        <div className="flex gap-2 items-center flex-wrap">
          <Button
            color="gray"
            onClick={() => onPageChange(1)}
            disabled={currentPage === 1}
            size="xs"
          >
            First
          </Button>
          <Button
            color="gray"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            size="sm"
          >
            Previous
          </Button>
          
          {/* Page number buttons */}
          <div className="flex gap-1">
            {[...Array(totalPages)].map((_, index) => {
              // Show current page, first, last, and nearby pages
              if (
                index === 0 ||
                index === totalPages - 1 ||
                (index >= currentPage - 2 && index <= currentPage + 2)
              ) {
                return (
                  <Button
                    key={index}
                    color={currentPage === index + 1 ? "blue" : "gray"}
                    onClick={() => onPageChange(index + 1)}
                    size="xs"
                    className="min-w-[32px]"
                  >
                    {index + 1}
                  </Button>
                );
              } else if (
                index === currentPage - 3 ||
                index === currentPage + 3
              ) {
                return <span key={index} className="text-gray-400">...</span>;
              }
              return null;
            })}
          </div>
          
          <Button
            color="gray"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            size="sm"
          >
            Next
          </Button>
          <Button
            color="gray"
            onClick={() => onPageChange(totalPages)}
            disabled={currentPage === totalPages}
            size="xs"
          >
            Last
          </Button>
        </div>
      )}
    </div>
  );
};

export default Pagination;
