import React, { useState } from 'react';
import { TextInput, Label, Tooltip } from 'flowbite-react';
import { HiOutlineInformationCircle } from 'react-icons/hi';

interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  /**
   * The label for the input
   */
  label: string;
  
  /**
   * The name of the input
   */
  name: string;
  
  /**
   * Error messages to display
   */
  errors?: string[];
  
  /**
   * Whether the input is required
   */
  required?: boolean;
  
  /**
   * Tooltip text to explain the field
   */
  tooltip?: string;
  
  /**
   * Additional class names for the container
   */
  containerClassName?: string;
  
  /**
   * Additional class names for the input
   */
  inputClassName?: string;
  
  /**
   * Additional class names for the label
   */
  labelClassName?: string;
}

/**
 * Enhanced form input component with better error display and tooltips
 */
const FormInput: React.FC<FormInputProps> = ({
  label,
  name,
  errors,
  required,
  tooltip,
  containerClassName = '',
  inputClassName = '',
  labelClassName = '',
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  
  const hasErrors = errors && errors.length > 0;
  
  return (
    <div className={`mb-4 ${containerClassName}`}>
      <div className="flex items-center mb-1">
        <Label
          htmlFor={name}
          value={label}
          className={`text-sm font-medium ${labelClassName}`}
        />
        {required && <span className="text-red-500 ml-1">*</span>}
        
        {tooltip && (
          <Tooltip content={tooltip}>
            <HiOutlineInformationCircle className="ml-1 h-4 w-4 text-gray-400 hover:text-gray-600" />
          </Tooltip>
        )}
      </div>
      
      <TextInput
        id={name}
        name={name}
        className={inputClassName}
        color={hasErrors ? 'failure' : isFocused ? 'info' : undefined}
        onFocus={(e) => {
          setIsFocused(true);
          if (props.onFocus) props.onFocus(e);
        }}
        onBlur={(e) => {
          setIsFocused(false);
          if (props.onBlur) props.onBlur(e);
        }}
        required={required}
        helperText={
          hasErrors ? (
            <ul className="mt-1 text-sm text-red-600 list-disc list-inside">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          ) : null
        }
        {...props}
      />
    </div>
  );
};

export default FormInput;
