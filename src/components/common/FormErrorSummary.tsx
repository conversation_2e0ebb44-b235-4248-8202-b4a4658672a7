import React from 'react';
import { Alert } from 'flowbite-react';
import { HiOutlineExclamation } from 'react-icons/hi';

interface FormErrorSummaryProps<T> {
  /**
   * Field-level validation errors
   */
  errors: {
    [K in keyof T]?: string[];
  };
  
  /**
   * Form-level validation errors
   */
  formErrors: string[];
  
  /**
   * Field labels for better error messages
   */
  fieldLabels: {
    [K in keyof T]?: string;
  };
  
  /**
   * Whether to show the summary
   */
  show: boolean;
  
  /**
   * Additional class names
   */
  className?: string;
}

/**
 * Component to display a summary of form errors
 */
function FormErrorSummary<T extends Record<string, any>>({
  errors,
  formErrors,
  fieldLabels,
  show,
  className = ''
}: FormErrorSummaryProps<T>) {
  if (!show) return null;
  
  // Count total errors
  const fieldErrorCount = Object.values(errors).reduce(
    (count, fieldErrors) => count + (fieldErrors?.length || 0),
    0
  );
  const totalErrorCount = fieldErrorCount + formErrors.length;
  
  if (totalErrorCount === 0) return null;
  
  return (
    <Alert color="failure" icon={HiOutlineExclamation} className={`mb-4 ${className}`}>
      <h4 className="text-lg font-medium mb-2">
        Please fix the following {totalErrorCount === 1 ? 'error' : 'errors'}:
      </h4>
      
      <ul className="list-disc list-inside space-y-1">
        {/* Form-level errors */}
        {formErrors.map((error, index) => (
          <li key={`form-${index}`}>{error}</li>
        ))}
        
        {/* Field-level errors */}
        {Object.entries(errors).map(([field, fieldErrors]) => {
          if (!fieldErrors || fieldErrors.length === 0) return null;
          
          const fieldLabel = fieldLabels[field as keyof T] || field;
          
          return fieldErrors.map((error, index) => (
            <li key={`${field}-${index}`}>
              <strong>{fieldLabel}:</strong> {error}
            </li>
          ));
        })}
      </ul>
    </Alert>
  );
}

export default FormErrorSummary;
