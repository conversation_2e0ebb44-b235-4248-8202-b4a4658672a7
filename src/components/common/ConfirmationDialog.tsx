import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON> } from 'flowbite-react';
import { HiOutlineExclamation } from 'react-icons/hi';

export interface ConfirmationDialogProps {
  /**
   * Whether the dialog is open
   */
  show: boolean;
  
  /**
   * The title of the dialog
   */
  title: string;
  
  /**
   * The message to display in the dialog
   */
  message: string;
  
  /**
   * The text for the confirm button
   * @default "Confirm"
   */
  confirmText?: string;
  
  /**
   * The text for the cancel button
   * @default "Cancel"
   */
  cancelText?: string;
  
  /**
   * The color of the confirm button
   * @default "failure"
   */
  confirmColor?: 'failure' | 'success' | 'warning' | 'primary';
  
  /**
   * Whether the confirm button is in a loading state
   * @default false
   */
  isLoading?: boolean;
  
  /**
   * Callback when the dialog is closed without confirming
   */
  onClose: () => void;
  
  /**
   * Callback when the confirm button is clicked
   */
  onConfirm: () => void;
  
  /**
   * Additional content to display in the dialog
   */
  children?: React.ReactNode;
}

/**
 * A reusable confirmation dialog component
 */
const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  show,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmColor = 'failure',
  isLoading = false,
  onClose,
  onConfirm,
  children
}) => {
  return (
    <Modal show={show} onClose={onClose} size="md">
      <Modal.Header>{title}</Modal.Header>
      <Modal.Body>
        <div className="text-center">
          <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200" />
          <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
            {message}
          </h3>
          
          {children && <div className="mb-5">{children}</div>}
          
          <div className="flex justify-center gap-4">
            <Button
              color={confirmColor}
              onClick={onConfirm}
              disabled={isLoading}
            >
              {isLoading ? <Spinner size="sm" className="mr-2" /> : null}
              {confirmText}
            </Button>
            <Button color="gray" onClick={onClose}>
              {cancelText}
            </Button>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ConfirmationDialog;
