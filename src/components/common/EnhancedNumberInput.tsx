import React from 'react';
import { TextInput } from 'flowbite-react';

interface EnhancedNumberInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'onFocus' | 'onWheel'> {
  /**
   * The current value of the input
   */
  value: number | string;
  
  /**
   * Callback when the value changes
   */
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  
  /**
   * Callback when the input loses focus (optional)
   */
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  
  /**
   * Whether to auto-select all text when focused (default: true)
   */
  autoSelect?: boolean;
  
  /**
   * Whether to prevent scroll wheel from changing the value (default: true)
   */
  preventScrollChange?: boolean;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Flowbite sizing
   */
  sizing?: 'sm' | 'md' | 'lg';
  
  /**
   * Minimum value
   */
  min?: number | string;
  
  /**
   * Maximum value
   */
  max?: number | string;
  
  /**
   * Step value
   */
  step?: number | string;
  
  /**
   * Whether the input is required
   */
  required?: boolean;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
}

/**
 * Enhanced number input component with auto-select and scroll prevention
 */
const EnhancedNumberInput: React.FC<EnhancedNumberInputProps> = ({
  value,
  onChange,
  onBlur,
  autoSelect = true,
  preventScrollChange = true,
  className = '',
  sizing = 'md',
  min,
  max,
  step,
  required,
  placeholder,
  ...props
}) => {
  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    if (autoSelect) {
      // Select all text when focused for easy editing
      e.target.select();
    }
  };

  const handleWheel = (e: React.WheelEvent<HTMLInputElement>) => {
    if (preventScrollChange) {
      // Prevent scroll wheel from changing the number value
      e.currentTarget.blur();
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Call the provided onBlur handler if it exists
    if (onBlur) {
      onBlur(e);
    }
  };

  return (
    <TextInput
      type="number"
      value={value}
      onChange={onChange}
      onFocus={handleFocus}
      onWheel={handleWheel}
      onBlur={handleBlur}
      className={className}
      sizing={sizing}
      min={min}
      max={max}
      step={step}
      required={required}
      placeholder={placeholder}
      {...props}
    />
  );
};

export default EnhancedNumberInput;
