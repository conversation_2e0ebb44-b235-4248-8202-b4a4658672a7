import React from 'react';
import { <PERSON><PERSON>, Toolt<PERSON> } from 'flowbite-react';
import { HiOutlineCheck, HiOutlineClock } from 'react-icons/hi';

interface AutoSaveIndicatorProps {
  /**
   * Whether the data is currently being saved
   */
  isSaving: boolean;
  
  /**
   * Whether the data has been modified since the last save
   */
  isDirty: boolean;
  
  /**
   * The formatted relative time of the last save
   */
  lastSavedFormatted: string;
  
  /**
   * Additional class names to apply to the indicator
   */
  className?: string;
}

/**
 * A component to display the auto-save status
 */
const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({
  isSaving,
  isDirty,
  lastSavedFormatted,
  className = ''
}) => {
  return (
    <div className={`flex items-center text-sm text-gray-500 ${className}`}>
      {isSaving ? (
        <>
          <Spinner size="sm" className="mr-2" />
          <span>Saving...</span>
        </>
      ) : isDirty ? (
        <>
          <HiOutlineClock className="mr-2 h-4 w-4 text-yellow-500" />
          <span>Unsaved changes</span>
        </>
      ) : (
        <Tooltip content={`Last saved: ${lastSavedFormatted}`}>
          <div className="flex items-center">
            <HiOutlineCheck className="mr-2 h-4 w-4 text-green-500" />
            <span>Saved</span>
          </div>
        </Tooltip>
      )}
    </div>
  );
};

export default AutoSaveIndicator;
