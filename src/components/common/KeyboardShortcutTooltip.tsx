import React from 'react';
import { KeyboardShortcutTooltipProps, formatKeyboardShortcut } from '../../hooks/useKeyboardShortcut';

/**
 * A component to display a keyboard shortcut in a tooltip-like format
 */
const KeyboardShortcutTooltip: React.FC<KeyboardShortcutTooltipProps> = ({ 
  shortcut, 
  className = '' 
}) => {
  return (
    <span 
      className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 ${className}`}
      aria-label={`Keyboard shortcut: ${formatKeyboardShortcut(shortcut)}`}
    >
      {formatKeyboardShortcut(shortcut)}
    </span>
  );
};

export default KeyboardShortcutTooltip;
