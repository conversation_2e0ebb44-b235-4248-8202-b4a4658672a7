import React, { useState, useEffect } from 'react';
import { TextInput, Button } from 'flowbite-react';
import { HiOutlineSearch, HiOutlineX } from 'react-icons/hi';
import { useChatContext } from '../../context/ChatContext';

const ChatSearch: React.FC = () => {
  const { searchQuery, searchMessages, clearSearch } = useChatContext();
  const [query, setQuery] = useState(searchQuery);
  const [isSearching, setIsSearching] = useState(false);

  // Update local state when context changes
  useEffect(() => {
    setQuery(searchQuery);
  }, [searchQuery]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!query.trim()) {
      clearSearch();
      return;
    }
    
    setIsSearching(true);
    try {
      await searchMessages(query);
    } finally {
      setIsSearching(false);
    }
  };

  const handleClear = () => {
    setQuery('');
    clearSearch();
  };

  return (
    <div className="mb-4">
      <form onSubmit={handleSearch} className="flex items-center">
        <div className="relative w-full">
          <TextInput
            type="text"
            placeholder="Search messages..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            icon={HiOutlineSearch}
            className="w-full"
          />
          {query && (
            <button
              type="button"
              className="absolute inset-y-0 right-10 flex items-center pr-3"
              onClick={handleClear}
            >
              <HiOutlineX className="h-4 w-4 text-gray-500 hover:text-gray-700" />
            </button>
          )}
        </div>
        <Button
          type="submit"
          color="blue"
          size="sm"
          className="ml-2"
          disabled={isSearching}
        >
          {isSearching ? 'Searching...' : 'Search'}
        </Button>
      </form>
      
      {searchQuery && (
        <div className="mt-2 text-sm text-gray-500 flex justify-between items-center">
          <span>Showing results for: "{searchQuery}"</span>
          <Button
            color="light"
            size="xs"
            onClick={handleClear}
          >
            Clear
          </Button>
        </div>
      )}
    </div>
  );
};

export default ChatSearch;
