import React, { useState, useEffect } from 'react';
import { Modal, Button, TextInput, Spinner, Avatar } from 'flowbite-react';
import { HiOutlineSearch, HiOutlineX } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useChatContext } from '../../context/ChatContext';
import { getOrganizationMembers } from '../../services/userManagement';
import { useAuth } from '../../context/AuthContext';
import defaultAvatar from '../../assets/images/svgs/icon-account.svg';

interface NewChatModalProps {
  show: boolean;
  onClose: () => void;
  onChatCreated: (conversationId: string) => void;
}

interface Member {
  id: string;
  user_id: string;
  first_name: string | null;
  last_name: string | null;
  avatar_url: string | null;
}

const NewChatModal: React.FC<NewChatModalProps> = ({ show, onClose, onChatCreated }) => {
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const { startOneOnOneChat } = useChatContext();
  const [members, setMembers] = useState<Member[]>([]);
  const [filteredMembers, setFilteredMembers] = useState<Member[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [creatingChat, setCreatingChat] = useState(false);

  // Fetch organization members when modal is shown
  useEffect(() => {
    if (show && currentOrganization) {
      fetchMembers();
    }
  }, [show, currentOrganization]);

  // Filter members based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredMembers(members);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = members.filter(member =>
      (member.first_name?.toLowerCase().includes(query) ||
       member.last_name?.toLowerCase().includes(query))
    );

    setFilteredMembers(filtered);
  }, [searchQuery, members]);

  const fetchMembers = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { members: orgMembers, error: membersError } = await getOrganizationMembers(currentOrganization.id);

      if (membersError) {
        throw new Error(membersError);
      }

      // Format members and exclude current user
      const formattedMembers = orgMembers
        .filter(member => member.user_id !== user?.id)
        .map(member => ({
          id: member.id,
          user_id: member.user_id,
          first_name: member.profile?.first_name || null,
          last_name: member.profile?.last_name || null,
          avatar_url: member.profile?.avatar_url || null
        }));

      setMembers(formattedMembers);
      setFilteredMembers(formattedMembers);
    } catch (err: any) {
      console.error('Error fetching members:', err);
      setError(err.message || 'Failed to load organization members');
    } finally {
      setLoading(false);
    }
  };

  const handleStartChat = async (userId: string) => {
    if (!currentOrganization) return;

    setCreatingChat(true);
    try {
      const conversationId = await startOneOnOneChat(userId);
      onChatCreated(conversationId);
    } catch (err: any) {
      console.error('Error creating chat:', err);
      setError(err.message || 'Failed to create chat');
    } finally {
      setCreatingChat(false);
    }
  };

  const handleClose = () => {
    setSearchQuery('');
    setError(null);
    onClose();
  };

  return (
    <Modal show={show} onClose={handleClose} size="md">
      <Modal.Header>
        New Chat
      </Modal.Header>
      <Modal.Body>
        <div className="mb-4">
          <TextInput
            id="search-members"
            type="text"
            icon={HiOutlineSearch}
            placeholder="Search members..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 text-red-800 rounded-lg">
            <p>{error}</p>
            <Button color="light" size="xs" onClick={fetchMembers} className="mt-2">
              Retry
            </Button>
          </div>
        )}

        <div className="max-h-60 overflow-y-auto">
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <Spinner size="lg" />
            </div>
          ) : filteredMembers.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              {searchQuery ? 'No members found matching your search' : 'No members available'}
            </div>
          ) : (
            <ul className="space-y-2">
              {filteredMembers.map((member) => (
                <li
                  key={member.id}
                  className="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-colors"
                  onClick={() => handleStartChat(member.user_id)}
                >
                  <div className="flex items-center">
                    <Avatar
                      img={member.avatar_url || undefined}
                      rounded
                      size="md"
                      placeholderInitials={
                        member.first_name && member.last_name
                          ? `${member.first_name[0]}${member.last_name[0]}`
                          : member.first_name
                            ? member.first_name[0]
                            : member.last_name
                              ? member.last_name[0]
                              : ''
                      }
                    />
                    <div className="ml-3">
                      <p className="font-medium">
                        {member.first_name} {member.last_name}
                      </p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button color="gray" onClick={handleClose}>
          Cancel
        </Button>
      </Modal.Footer>

      {/* Loading overlay when creating chat */}
      {creatingChat && (
        <div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50 flex justify-center items-center rounded-lg">
          <Spinner size="xl" />
        </div>
      )}
    </Modal>
  );
};

export default NewChatModal;
