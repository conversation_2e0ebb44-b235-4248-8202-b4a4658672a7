import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'flowbite-react';
import { HiOutlinePaperClip, HiOutlineX } from 'react-icons/hi';
import { ALLOWED_FILE_TYPES, MAX_FILE_SIZE, isValidFile } from '../../services/fileUpload';

interface FileUploadButtonProps {
  onFileSelected: (file: File | null) => void;
  disabled?: boolean;
  selectedFile?: File | null;
}

const FileUploadButton: React.FC<FileUploadButtonProps> = ({
  onFileSelected,
  disabled = false,
  selectedFile: externalSelectedFile = null
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [internalSelectedFile, setInternalSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Use either the external selectedFile (if provided) or the internal state
  const selectedFile = externalSelectedFile !== undefined ? externalSelectedFile : internalSelectedFile;

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    const validation = isValidFile(file);

    if (!validation.valid) {
      setError(validation.error || 'Invalid file');
      setInternalSelectedFile(null);
      onFileSelected(null);
      // Reset the input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }

    setError(null);
    setInternalSelectedFile(file);
    onFileSelected(file);
  };

  const handleClearFile = () => {
    setInternalSelectedFile(null);
    onFileSelected(null);
    setError(null);
    // Reset the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const allowedExtensions = Object.values(ALLOWED_FILE_TYPES)
    .map(type => `.${type.ext}`)
    .join(', ');

  return (
    <div className="relative">
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
        accept={Object.keys(ALLOWED_FILE_TYPES).join(',')}
        disabled={disabled}
      />

      {selectedFile ? (
        <div className="flex items-center bg-blue-50 dark:bg-blue-900 p-2 rounded-lg">
          <span className="text-sm truncate max-w-[150px]">{selectedFile.name}</span>
          <Button
            color="light"
            size="xs"
            className="ml-2 p-1"
            onClick={handleClearFile}
          >
            <HiOutlineX className="w-4 h-4" />
          </Button>
        </div>
      ) : (
        <Tooltip content={`Attach a file (Max: ${MAX_FILE_SIZE / (1024 * 1024)}MB)\nAllowed: ${allowedExtensions}`}>
          <Button
            color="light"
            size="sm"
            onClick={handleClick}
            disabled={disabled}
            className="p-2"
          >
            <HiOutlinePaperClip className="w-5 h-5" />
          </Button>
        </Tooltip>
      )}

      {error && (
        <div className="absolute bottom-full left-0 mb-1 text-xs text-red-500 bg-red-50 dark:bg-red-900 p-1 rounded">
          {error}
        </div>
      )}
    </div>
  );
};

export default FileUploadButton;
