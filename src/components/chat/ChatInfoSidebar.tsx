import React, { useState } from 'react';
import { Card, Avatar, <PERSON><PERSON>, <PERSON><PERSON>, Modal } from 'flowbite-react';
import { HiOutlineUserGroup, HiOutlineX, HiOutlineUserAdd, HiOutlineLogout, HiOutlineTrash } from 'react-icons/hi';
import { useAuth } from '../../context/AuthContext';
import { useChatContext } from '../../context/ChatContext';
import { ConversationWithParticipants } from '../../services/chat';
import { formatDate } from '../../utils/formatters';
import { useNavigate } from 'react-router-dom';
import defaultAvatar from '../../assets/images/svgs/icon-account.svg';

interface ChatInfoSidebarProps {
  conversation: ConversationWithParticipants;
  onClose: () => void;
}

const ChatInfoSidebar: React.FC<ChatInfoSidebarProps> = ({ conversation, onClose }) => {
  const { user } = useAuth();
  const { deleteConversation, leaveConversation } = useChatContext();
  const navigate = useNavigate();
  const [showLeaveModal, setShowLeaveModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [leavingGroup, setLeavingGroup] = useState(false);
  const [deletingGroup, setDeletingGroup] = useState(false);

  // Check if current user is admin or owner
  const isAdmin = conversation.participants.some(
    p => p.user_id === user?.id && p.is_admin
  ) || conversation.created_by === user?.id; // Also consider the creator as admin

  // Debug log to check admin status
  console.log('User ID:', user?.id);
  console.log('Created by:', conversation.created_by);
  console.log('Is admin:', isAdmin);
  console.log('Participants:', conversation.participants);

  // Get conversation display name
  const getConversationName = () => {
    if (conversation.is_group) {
      return conversation.name || 'Unnamed Group';
    }

    // For one-on-one chats, show the other person's name
    const otherParticipants = conversation.participants.filter(
      p => p.profiles && p.user_id !== user?.id
    );

    if (otherParticipants.length > 0) {
      const profile = otherParticipants[0].profiles;
      return profile?.first_name && profile?.last_name
        ? `${profile.first_name} ${profile.last_name}`
        : 'Unknown User';
    }

    return 'Chat';
  };

  // Get avatar for conversation
  const getConversationAvatar = () => {
    if (conversation.is_group) {
      return null; // Use default group avatar
    }

    // For one-on-one chats, show the other person's avatar
    const otherParticipants = conversation.participants.filter(
      p => p.profiles && p.user_id !== user?.id
    );

    if (otherParticipants.length > 0) {
      return otherParticipants[0].profiles?.avatar_url;
    }

    return null;
  };

  const handleLeaveGroup = async () => {
    if (!conversation) return;

    setLeavingGroup(true);

    try {
      const success = await leaveConversation(conversation.id);

      if (success) {
        setShowLeaveModal(false);
        onClose();
        navigate('/chat');
      }
    } catch (error) {
      console.error('Error leaving group:', error);
    } finally {
      setLeavingGroup(false);
    }
  };

  const handleDeleteGroup = async () => {
    if (!conversation) return;

    setDeletingGroup(true);

    try {
      const success = await deleteConversation(conversation.id);

      if (success) {
        setShowDeleteModal(false);
        onClose();
        navigate('/chat');
      }
    } catch (error) {
      console.error('Error deleting group:', error);
    } finally {
      setDeletingGroup(false);
    }
  };

  return (
    <div className="fixed top-0 right-0 h-full w-80 bg-white dark:bg-gray-800 shadow-lg z-50 overflow-y-auto">
      <div className="p-4 border-b flex justify-between items-center">
        <h3 className="font-medium">Chat Info</h3>
        <Button color="light" size="sm" onClick={onClose}>
          <HiOutlineX className="w-5 h-5" />
        </Button>
      </div>

      <div className="p-4">
        <div className="flex flex-col items-center mb-6">
          {conversation.is_group ? (
            <div className="w-20 h-20 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-3">
              <HiOutlineUserGroup className="w-10 h-10 text-blue-600 dark:text-blue-300" />
            </div>
          ) : (
            <Avatar
              img={getConversationAvatar() || undefined}
              rounded
              size="xl"
              className="mb-3"
              placeholderInitials={getConversationName().substring(0, 2)}
            />
          )}
          <h2 className="text-xl font-semibold">{getConversationName()}</h2>
          {conversation.is_group && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {conversation.participants.length} participants
            </p>
          )}
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Created {formatDate(conversation.created_at)}
          </p>
        </div>

        {conversation.is_group && (
          <>
            <div className="mb-4">
              <h4 className="font-medium mb-2">Participants</h4>
              <ul className="space-y-2">
                {conversation.participants.map((participant) => (
                  <li key={participant.id} className="flex items-center">
                    <Avatar
                      img={participant.profiles?.avatar_url || undefined}
                      rounded
                      size="xs"
                      className="mr-2"
                      placeholderInitials={
                        participant.profiles?.first_name && participant.profiles?.last_name
                          ? `${participant.profiles.first_name[0]}${participant.profiles.last_name[0]}`
                          : participant.profiles?.first_name
                            ? participant.profiles.first_name[0]
                            : participant.profiles?.last_name
                              ? participant.profiles.last_name[0]
                              : ''
                      }
                    />
                    <span className="text-sm">
                      {participant.profiles?.first_name} {participant.profiles?.last_name}
                      {participant.user_id === user?.id && ' (You)'}
                    </span>
                    {participant.is_admin && (
                      <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded">
                        Admin
                      </span>
                    )}
                  </li>
                ))}
              </ul>
            </div>

            <div className="space-y-2">
              {isAdmin && (
                <>
                  <Button
                    color="light"
                    className="w-full flex items-center justify-center"
                    disabled
                  >
                    <HiOutlineUserAdd className="mr-2" />
                    Add Participants
                  </Button>

                  <Button
                    color="failure"
                    className="w-full flex items-center justify-center"
                    onClick={() => setShowDeleteModal(true)}
                  >
                    <HiOutlineTrash className="mr-2" />
                    Delete Group
                  </Button>
                </>
              )}

              <Button
                color="failure"
                className="w-full flex items-center justify-center"
                onClick={() => setShowLeaveModal(true)}
              >
                <HiOutlineLogout className="mr-2" />
                Leave Group
              </Button>
            </div>
          </>
        )}
      </div>

      {/* Leave Group Modal */}
      <Modal show={showLeaveModal} onClose={() => setShowLeaveModal(false)} size="sm">
        <Modal.Header>
          Leave Group
        </Modal.Header>
        <Modal.Body>
          <p>Are you sure you want to leave this group? You won't receive any new messages from this conversation.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowLeaveModal(false)}>
            Cancel
          </Button>
          <Button
            color="failure"
            onClick={handleLeaveGroup}
            disabled={leavingGroup}
          >
            {leavingGroup ? <Spinner size="sm" className="mr-2" /> : null}
            Leave Group
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Group Modal */}
      <Modal show={showDeleteModal} onClose={() => setShowDeleteModal(false)} size="sm">
        <Modal.Header>
          Delete Group
        </Modal.Header>
        <Modal.Body>
          <p className="text-red-600 font-medium">Warning: This action cannot be undone.</p>
          <p className="mt-2">Are you sure you want to delete this group? All messages and data will be permanently removed.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button
            color="failure"
            onClick={handleDeleteGroup}
            disabled={deletingGroup}
          >
            {deletingGroup ? <Spinner size="sm" className="mr-2" /> : null}
            Delete Group
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default ChatInfoSidebar;
