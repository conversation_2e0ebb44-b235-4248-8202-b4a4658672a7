import React from 'react';
import { Avatar } from 'flowbite-react';
import { useAuth } from '../../context/AuthContext';
import { MessageWithSender } from '../../services/chat';
import MessageActions from './MessageActions';
import MessageAttachment from './MessageAttachment';
import defaultAvatar from '../../assets/images/svgs/icon-account.svg';

interface ChatMessageProps {
  message: MessageWithSender;
  showAvatar?: boolean;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message, showAvatar = true }) => {
  const { user } = useAuth();
  const isCurrentUser = message.sender_id === user?.id;



  // Format timestamp
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Get initials for avatar
  const getInitials = () => {
    if (message.sender?.first_name && message.sender?.last_name) {
      return `${message.sender.first_name[0]}${message.sender.last_name[0]}`;
    } else if (message.sender?.first_name) {
      return message.sender.first_name[0];
    } else if (message.sender?.last_name) {
      return message.sender.last_name[0];
    }

    // If no name is available, return empty string (Flowbite will handle this case)
    return '';
  };

  return (
    <div
      className={`flex items-end ${isCurrentUser ? 'justify-end' : 'justify-start'} ${showAvatar ? 'mb-3' : 'mb-1'} group`}
      data-message-id={message.id}
    >
      {/* Only show avatar for other users' messages and when showAvatar is true */}
      {!isCurrentUser && showAvatar && (
        <Avatar
          img={message.sender?.avatar_url || undefined}
          rounded
          size="xs"
          placeholderInitials={getInitials()}
          className="mr-2 self-end flex-shrink-0 ring-1 ring-gray-200 dark:ring-gray-600"
        />
      )}

      <div
        className={`relative max-w-[75%] px-3 py-2 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md ${
          isCurrentUser
            ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
            : 'bg-white dark:bg-gray-700 dark:text-white border border-gray-200 dark:border-gray-600'
        }`}
        style={{
          borderBottomRightRadius: isCurrentUser && !showAvatar ? '6px' : '12px',
          borderBottomLeftRadius: !isCurrentUser && !showAvatar ? '6px' : '12px',
        }}
      >
        {/* Only show name for other users' messages and when it's the first in a sequence */}
        {!isCurrentUser && showAvatar && (
          <div className="text-xs font-medium mb-1 text-gray-600 dark:text-gray-300">
            {message.sender?.first_name} {message.sender?.last_name}
          </div>
        )}

        <div className="text-sm leading-relaxed">
          {message.content}
        </div>

        {message.attachment_url && (
          <div className="mt-1">
            <MessageAttachment
              type={message.attachment_type || ''}
              url={message.attachment_url}
              name={message.attachment_name || 'Attachment'}
              size={message.attachment_size || 0}
            />
          </div>
        )}

        <div
          className={`text-xs mt-1 flex items-center justify-between opacity-75 ${
            isCurrentUser ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
          }`}
        >
          <div className="flex items-center space-x-1">
            <span>{formatTime(message.created_at)}</span>
            {message.created_at !== message.updated_at && (
              <span className="italic">(edited)</span>
            )}
          </div>

          {/* Read receipt indicator for current user's messages */}
          {isCurrentUser && (
            <div className="flex items-center ml-1" title={message.is_read ? "Read" : "Delivered"}>
              {message.is_read ? (
                <div className="flex items-center text-blue-200">
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <svg className="w-3 h-3 -ml-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              ) : (
                <svg className="w-3 h-3 text-blue-200/60" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          )}
        </div>

        <MessageActions message={message} />
      </div>

      {/* Spacer for alignment when no avatar - matches avatar size (xs = 24px) + margin (mr-2 = 8px) = 32px total */}
      {!isCurrentUser && !showAvatar && (
        <div className="w-6 mr-2 flex-shrink-0" />
      )}
    </div>
  );
};

export default ChatMessage;
