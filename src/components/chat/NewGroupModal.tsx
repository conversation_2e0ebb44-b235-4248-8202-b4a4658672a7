import React, { useState, useEffect } from 'react';
import { Modal, Button, TextInput, Spinner, Avatar, Checkbox, Label } from 'flowbite-react';
import { HiOutlineSearch, HiOutlineX, HiOutlineUserGroup } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useChatContext } from '../../context/ChatContext';
import { getOrganizationMembers } from '../../services/userManagement';
import { useAuth } from '../../context/AuthContext';
import defaultAvatar from '../../assets/images/svgs/icon-account.svg';

interface NewGroupModalProps {
  show: boolean;
  onClose: () => void;
  onGroupCreated: (conversationId: string) => void;
}

interface Member {
  id: string;
  user_id: string;
  first_name: string | null;
  last_name: string | null;
  avatar_url: string | null;
  selected: boolean;
}

const NewGroupModal: React.FC<NewGroupModalProps> = ({ show, onClose, onGroupCreated }) => {
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const { startGroupChat } = useChatContext();
  const [members, setMembers] = useState<Member[]>([]);
  const [filteredMembers, setFilteredMembers] = useState<Member[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [groupName, setGroupName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [creatingGroup, setCreatingGroup] = useState(false);
  const [step, setStep] = useState<'select-members' | 'set-name'>('select-members');

  // Fetch organization members when modal is shown
  useEffect(() => {
    if (show && currentOrganization) {
      fetchMembers();
      setStep('select-members');
      setGroupName('');
    }
  }, [show, currentOrganization]);

  // Filter members based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredMembers(members);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = members.filter(member =>
      (member.first_name?.toLowerCase().includes(query) ||
       member.last_name?.toLowerCase().includes(query))
    );

    setFilteredMembers(filtered);
  }, [searchQuery, members]);

  const fetchMembers = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { members: orgMembers, error: membersError } = await getOrganizationMembers(currentOrganization.id);

      if (membersError) {
        throw new Error(membersError);
      }

      // Format members and exclude current user
      const formattedMembers = orgMembers
        .filter(member => member.user_id !== user?.id)
        .map(member => ({
          id: member.id,
          user_id: member.user_id,
          first_name: member.profile?.first_name || null,
          last_name: member.profile?.last_name || null,
          avatar_url: member.profile?.avatar_url || null,
          selected: false
        }));

      setMembers(formattedMembers);
      setFilteredMembers(formattedMembers);
    } catch (err: any) {
      console.error('Error fetching members:', err);
      setError(err.message || 'Failed to load organization members');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleMember = (userId: string) => {
    const updatedMembers = members.map(member =>
      member.user_id === userId
        ? { ...member, selected: !member.selected }
        : member
    );

    setMembers(updatedMembers);
    setFilteredMembers(
      filteredMembers.map(member =>
        member.user_id === userId
          ? { ...member, selected: !member.selected }
          : member
      )
    );
  };

  const handleNextStep = () => {
    const selectedCount = members.filter(m => m.selected).length;
    if (selectedCount === 0) {
      setError('Please select at least one member for the group');
      return;
    }

    setError(null);
    setStep('set-name');
  };

  const handleCreateGroup = async () => {
    if (!currentOrganization) return;

    if (!groupName.trim()) {
      setError('Please enter a group name');
      return;
    }

    const selectedUserIds = members
      .filter(member => member.selected)
      .map(member => member.user_id);

    if (selectedUserIds.length === 0) {
      setError('Please select at least one member for the group');
      return;
    }

    setCreatingGroup(true);
    setError(null);

    try {
      const conversationId = await startGroupChat(
        currentOrganization.id,
        groupName.trim(),
        selectedUserIds
      );
      onGroupCreated(conversationId);
    } catch (err: any) {
      console.error('Error creating group:', err);
      setError(err.message || 'Failed to create group');
      setCreatingGroup(false);
    }
  };

  const handleBack = () => {
    setStep('select-members');
    setError(null);
  };

  const handleClose = () => {
    setSearchQuery('');
    setGroupName('');
    setError(null);
    onClose();
  };

  const selectedCount = members.filter(m => m.selected).length;

  return (
    <Modal show={show} onClose={handleClose} size="md">
      <Modal.Header>
        {step === 'select-members' ? 'New Group Chat' : 'Create Group'}
      </Modal.Header>
      <Modal.Body>
        {step === 'select-members' ? (
          <>
            <div className="mb-4">
              <TextInput
                id="search-members"
                type="text"
                icon={HiOutlineSearch}
                placeholder="Search members..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-100 text-red-800 rounded-lg">
                <p>{error}</p>
              </div>
            )}

            <div className="mb-2 flex justify-between items-center">
              <p className="text-sm text-gray-500">
                Select members to add to the group
              </p>
              <span className="text-sm font-medium">
                {selectedCount} selected
              </span>
            </div>

            <div className="max-h-60 overflow-y-auto">
              {loading ? (
                <div className="flex justify-center items-center h-40">
                  <Spinner size="lg" />
                </div>
              ) : filteredMembers.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  {searchQuery ? 'No members found matching your search' : 'No members available'}
                </div>
              ) : (
                <ul className="space-y-2">
                  {filteredMembers.map((member) => (
                    <li
                      key={member.id}
                      className="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-colors"
                      onClick={() => handleToggleMember(member.user_id)}
                    >
                      <div className="flex items-center">
                        <Checkbox
                          id={`member-${member.id}`}
                          checked={member.selected}
                          onChange={() => {}}
                          className="mr-3"
                        />
                        <Avatar
                          img={member.avatar_url || undefined}
                          rounded
                          size="md"
                          placeholderInitials={
                            member.first_name && member.last_name
                              ? `${member.first_name[0]}${member.last_name[0]}`
                              : member.first_name
                                ? member.first_name[0]
                                : member.last_name
                                  ? member.last_name[0]
                                  : ''
                          }
                        />
                        <div className="ml-3">
                          <p className="font-medium">
                            {member.first_name} {member.last_name}
                          </p>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </>
        ) : (
          <>
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <HiOutlineUserGroup className="w-8 h-8 text-blue-600 dark:text-blue-300" />
              </div>
            </div>

            <div className="mb-4">
              <Label htmlFor="group-name" value="Group Name" className="mb-2 block" />
              <TextInput
                id="group-name"
                type="text"
                placeholder="Enter group name"
                value={groupName}
                onChange={(e) => setGroupName(e.target.value)}
                required
              />
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-100 text-red-800 rounded-lg">
                <p>{error}</p>
              </div>
            )}

            <div className="mb-4">
              <p className="text-sm font-medium mb-2">Selected Members ({selectedCount})</p>
              <div className="flex flex-wrap gap-2">
                {members.filter(m => m.selected).map(member => (
                  <div
                    key={member.id}
                    className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1"
                  >
                    <span className="text-sm">
                      {member.first_name} {member.last_name}
                    </span>
                    <button
                      type="button"
                      className="ml-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                      onClick={() => handleToggleMember(member.user_id)}
                    >
                      <HiOutlineX className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </Modal.Body>
      <Modal.Footer>
        {step === 'select-members' ? (
          <>
            <Button color="gray" onClick={handleClose}>
              Cancel
            </Button>
            <Button color="primary" onClick={handleNextStep} disabled={selectedCount === 0}>
              Next
            </Button>
          </>
        ) : (
          <>
            <Button color="gray" onClick={handleBack}>
              Back
            </Button>
            <Button
              color="primary"
              onClick={handleCreateGroup}
              disabled={!groupName.trim() || creatingGroup}
            >
              {creatingGroup ? <Spinner size="sm" className="mr-2" /> : null}
              Create Group
            </Button>
          </>
        )}
      </Modal.Footer>

      {/* Loading overlay when creating group */}
      {creatingGroup && (
        <div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50 flex justify-center items-center rounded-lg">
          <Spinner size="xl" />
        </div>
      )}
    </Modal>
  );
};

export default NewGroupModal;
