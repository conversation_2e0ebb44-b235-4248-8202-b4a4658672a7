import React, { useState } from 'react';
import { Button, Dropdown, Modal, TextInput } from 'flowbite-react';
import { HiDotsVertical, HiPencil, HiTrash } from 'react-icons/hi';
import { useChatContext } from '../../context/ChatContext';
import { useAuth } from '../../context/AuthContext';
import { MessageWithSender } from '../../services/chat';

interface MessageActionsProps {
  message: MessageWithSender;
}

const MessageActions: React.FC<MessageActionsProps> = ({ message }) => {
  const { user } = useAuth();
  const { deleteUserMessage, editUserMessage } = useChatContext();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editedContent, setEditedContent] = useState(message.content);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Only show actions for the user's own messages
  if (message.sender_id !== user?.id) {
    return null;
  }

  const handleDelete = async () => {
    setIsSubmitting(true);
    try {
      await deleteUserMessage(message.id);
      setShowDeleteModal(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = async () => {
    if (!editedContent.trim()) return;

    setIsSubmitting(true);
    try {
      await editUserMessage(message.id, editedContent);
      setShowEditModal(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="absolute top-0 right-0 p-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <Dropdown
          label={<HiDotsVertical className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white" />}
          arrowIcon={false}
          inline
        >
          <Dropdown.Item icon={HiPencil} onClick={() => setShowEditModal(true)}>
            Edit
          </Dropdown.Item>
          <Dropdown.Item icon={HiTrash} onClick={() => setShowDeleteModal(true)}>
            Delete
          </Dropdown.Item>
        </Dropdown>
      </div>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onClose={() => setShowDeleteModal(false)} size="sm">
        <Modal.Header>Delete Message</Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <p className="mb-4">Are you sure you want to delete this message?</p>
            <p className="text-sm text-gray-500 italic">"{message.content}"</p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button color="failure" onClick={handleDelete} disabled={isSubmitting}>
            {isSubmitting ? 'Deleting...' : 'Delete'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Edit Modal */}
      <Modal show={showEditModal} onClose={() => setShowEditModal(false)}>
        <Modal.Header>Edit Message</Modal.Header>
        <Modal.Body>
          <TextInput
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            placeholder="Edit your message"
            className="mb-4"
          />
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
          <Button color="blue" onClick={handleEdit} disabled={isSubmitting || !editedContent.trim()}>
            {isSubmitting ? 'Saving...' : 'Save'}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default MessageActions;
