import React from 'react';
import { HiOutlineDocumentText, HiOutlineDownload, HiOutlineArchive, HiOutlineVolumeUp, HiOutlineFilm } from 'react-icons/hi';

interface MessageAttachmentProps {
  type: string | null;
  url: string | null;
  name: string | null;
  size: number | null;
  darkMode?: boolean;
}

const formatFileSize = (bytes: number | null): string => {
  if (!bytes) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`;
};

const MessageAttachment: React.FC<MessageAttachmentProps> = ({ 
  type, 
  url, 
  name, 
  size,
  darkMode = false
}) => {
  if (!url || !type) return null;
  
  const fileName = name || url.split('/').pop() || 'file';
  const fileSize = formatFileSize(size);
  
  // Render image attachment
  if (type === 'image') {
    return (
      <div className="mt-2 w-full max-w-sm">
        <a href={url} target="_blank" rel="noopener noreferrer" className="block">
          <img
            src={url}
            alt={fileName}
            className="rounded-lg max-h-60 w-full object-cover hover:object-contain transition-all duration-200 cursor-pointer"
          />
        </a>
        <div className="flex items-center justify-between mt-1 text-xs text-gray-500 dark:text-gray-400">
          <span className="truncate flex-1 mr-2">{fileName}</span>
          <span className="flex-shrink-0">{fileSize}</span>
        </div>
      </div>
    );
  }
  
  // Render document attachment
  const getIcon = () => {
    switch (type) {
      case 'document':
        return <HiOutlineDocumentText className="w-5 h-5" />;
      case 'archive':
        return <HiOutlineArchive className="w-5 h-5" />;
      case 'audio':
        return <HiOutlineVolumeUp className="w-5 h-5" />;
      case 'video':
        return <HiOutlineFilm className="w-5 h-5" />;
      default:
        return <HiOutlineDocumentText className="w-5 h-5" />;
    }
  };
  
  return (
    <div className="mt-2">
      <a 
        href={url} 
        target="_blank" 
        rel="noopener noreferrer" 
        className={`flex items-center p-3 rounded-lg ${
          darkMode 
            ? 'bg-gray-800 hover:bg-gray-700' 
            : 'bg-gray-100 hover:bg-gray-200'
        } transition-colors`}
      >
        <div className={`mr-3 p-2 rounded-full ${
          darkMode ? 'bg-gray-700' : 'bg-white'
        }`}>
          {getIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">{fileName}</p>
          <p className="text-xs text-gray-500 dark:text-gray-400">{fileSize}</p>
        </div>
        <HiOutlineDownload className="w-5 h-5 ml-2" />
      </a>
    </div>
  );
};

export default MessageAttachment;
