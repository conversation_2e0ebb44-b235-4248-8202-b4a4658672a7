import { useState, useEffect } from 'react';
import { Modal, Button, TextInput, Table, Alert, Spinner } from 'flowbite-react';
import { HiOutlineSearch, HiOutlineExclamation } from 'react-icons/hi';
import { Product } from '../../services/product';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

interface ProductSearchModalProps {
  show: boolean;
  onClose: () => void;
  onSelect: (product: Product) => void;
  onSearch: (query: string) => Promise<{ products: Product[], count: number }>;
  isLoading?: boolean;
  error?: string | null;
}

const ProductSearchModal = ({
  show,
  onClose,
  onSelect,
  onSearch,
  isLoading = false,
  error = null,
}: ProductSearchModalProps) => {
  const formatCurrency = useCurrencyFormatter();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [searching, setSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  // Clear search results when modal is closed
  useEffect(() => {
    if (!show) {
      setSearchQuery('');
      setSearchResults([]);
      setTotalCount(0);
      setSearchError(null);
    }
  }, [show]);

  // Handle search submit
  const handleSearch = async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }
    
    if (!searchQuery.trim()) {
      return;
    }
    
    setSearching(true);
    setSearchError(null);
    
    try {
      const { products, count } = await onSearch(searchQuery);
      setSearchResults(products);
      setTotalCount(count);
    } catch (err: any) {
      console.error('Error in handleSearch:', err);
      setSearchError(err.message || 'An error occurred while searching');
    } finally {
      setSearching(false);
    }
  };

  // Auto-search when query changes (with debounce)
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchQuery.trim().length >= 2) {
        handleSearch();
      }
    }, 500);
    
    return () => clearTimeout(timer);
  }, [searchQuery]);

  return (
    <Modal show={show} onClose={onClose} size="xl">
      <Modal.Header>
        Search Products
      </Modal.Header>
      <Modal.Body>
        {error && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {error}
          </Alert>
        )}
        
        {searchError && (
          <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
            {searchError}
          </Alert>
        )}
        
        <form onSubmit={handleSearch} className="mb-4">
          <div className="flex gap-2">
            <TextInput
              id="product-search"
              type="text"
              placeholder="Search products by name, SKU, or barcode"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
              icon={HiOutlineSearch}
              autoFocus
            />
            <Button type="submit" disabled={searching || isLoading}>
              {searching ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Searching...
                </>
              ) : (
                'Search'
              )}
            </Button>
          </div>
        </form>
        
        {isLoading || searching ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : searchResults.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500">
              {searchQuery.trim() ? 'No products found matching your search' : 'Enter a search term to find products'}
            </p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <Table hoverable>
                <Table.Head>
                  <Table.HeadCell>Product</Table.HeadCell>
                  <Table.HeadCell>SKU</Table.HeadCell>
                  <Table.HeadCell>Price</Table.HeadCell>
                  <Table.HeadCell>
                    <span className="sr-only">Actions</span>
                  </Table.HeadCell>
                </Table.Head>
                <Table.Body className="divide-y">
                  {searchResults.map((product) => (
                    <Table.Row key={product.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                      <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                        <div className="flex items-center gap-3">
                          {product.image_url ? (
                            <img
                              src={product.image_url}
                              alt={product.name}
                              className="h-10 w-10 rounded-md object-cover"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center">
                              <span className="text-gray-500 text-xs">No image</span>
                            </div>
                          )}
                          <div>
                            <p className="font-medium">{product.name}</p>
                            <p className="text-xs text-gray-500">
                              {product.category?.name || 'Uncategorized'}
                            </p>
                          </div>
                        </div>
                      </Table.Cell>
                      <Table.Cell>{product.sku || '-'}</Table.Cell>
                      <Table.Cell>{formatCurrency(product.unit_price)}</Table.Cell>
                      <Table.Cell>
                        <Button
                          color="primary"
                          size="xs"
                          onClick={() => onSelect(product)}
                        >
                          Select
                        </Button>
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </div>
            
            <div className="mt-4 text-sm text-gray-500">
              Showing {searchResults.length} of {totalCount} results
            </div>
          </>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button color="gray" onClick={onClose}>
          Cancel
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ProductSearchModal;
