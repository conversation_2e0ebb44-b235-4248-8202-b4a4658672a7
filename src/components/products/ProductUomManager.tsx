import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Spinner,
  <PERSON><PERSON>,
  <PERSON>,
  Modal
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineExclamation
} from 'react-icons/hi';
import { ProductUom, UnitOfMeasurement } from '../../types/uom.types';
import { getProductUoms, createProductUom, updateProductUom, deleteProductUom } from '../../services/productUom';
import ProductUomForm from '../uom/ProductUomForm';
import UomGuide from '../uom/UomGuide';
import { useOrganization } from '../../context/OrganizationContext';
import { getProductById } from '../../services/product';

interface ProductUomManagerProps {
  productId: string;
}

const ProductUomManager: React.FC<ProductUomManagerProps> = ({ productId }) => {
  const { currentOrganization } = useOrganization();
  const [productUoms, setProductUoms] = useState<(ProductUom & { uom: UnitOfMeasurement })[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [productName, setProductName] = useState<string>('');

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedUom, setSelectedUom] = useState<(ProductUom & { uom: UnitOfMeasurement }) | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  const fetchProductUoms = async () => {
    if (!productId || !currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { productUoms: uomData, error: fetchError } = await getProductUoms(
        productId,
        currentOrganization.id
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setProductUoms(uomData);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching product UoMs');
    } finally {
      setLoading(false);
    }
  };

  const fetchProductDetails = async () => {
    if (!productId || !currentOrganization) return;

    try {
      const { product } = await getProductById(currentOrganization.id, productId);
      if (product) {
        setProductName(product.name);
      }
    } catch (err) {
      console.error('Error fetching product details:', err);
    }
  };

  useEffect(() => {
    fetchProductUoms();
    fetchProductDetails();
  }, [productId, currentOrganization]);

  const handleAddClick = () => {
    setSelectedUom(null);
    setFormError(null);
    setShowAddModal(true);
  };

  const handleEditClick = (uom: ProductUom & { uom: UnitOfMeasurement }) => {
    setSelectedUom(uom);
    setFormError(null);
    setShowEditModal(true);
  };

  const handleDeleteClick = (uom: ProductUom & { uom: UnitOfMeasurement }) => {
    setSelectedUom(uom);
    setShowDeleteConfirm(true);
  };

  const handleAddSubmit = async (uomData: Partial<ProductUom>) => {
    if (!productId || !currentOrganization) return;

    setIsSubmitting(true);
    setFormError(null);

    try {
      const { productUom, error: createError } = await createProductUom(
        {
          product_id: productId,
          uom_id: uomData.uom_id!,
          conversion_factor: uomData.conversion_factor!,
          is_default: uomData.is_default || false,
          is_purchasing_unit: uomData.is_purchasing_unit || false,
          is_selling_unit: uomData.is_selling_unit || false
        },
        currentOrganization.id
      );

      if (createError) {
        setFormError(createError);
      } else {
        setShowAddModal(false);
        fetchProductUoms();
      }
    } catch (err: any) {
      setFormError(err.message || 'An error occurred while creating the product UoM');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditSubmit = async (uomData: Partial<ProductUom>) => {
    if (!selectedUom || !currentOrganization) return;

    setIsSubmitting(true);
    setFormError(null);

    try {
      const { productUom, error: updateError } = await updateProductUom(
        selectedUom.id,
        {
          conversion_factor: uomData.conversion_factor,
          is_default: uomData.is_default,
          is_purchasing_unit: uomData.is_purchasing_unit,
          is_selling_unit: uomData.is_selling_unit
        },
        currentOrganization.id
      );

      if (updateError) {
        setFormError(updateError);
      } else {
        setShowEditModal(false);
        fetchProductUoms();
      }
    } catch (err: any) {
      setFormError(err.message || 'An error occurred while updating the product UoM');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedUom || !currentOrganization) return;

    setIsSubmitting(true);
    setFormError(null);

    try {
      const { success, error: deleteError } = await deleteProductUom(
        selectedUom.id,
        currentOrganization.id
      );

      if (deleteError) {
        setFormError(deleteError);
      } else if (success) {
        setShowDeleteConfirm(false);
        setSelectedUom(null);
        fetchProductUoms();
      }
    } catch (err: any) {
      setFormError(err.message || 'An error occurred while deleting the product UoM');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Units of Measurement</h2>
        <Button color="primary" size="sm" onClick={handleAddClick}>
          <HiOutlinePlus className="mr-2 h-4 w-4" />
          Add Unit
        </Button>
      </div>

      {/* Show UoM Guide */}
      <UomGuide productId={productId} productName={productName} />

      {error && (
        <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
          {error}
        </Alert>
      )}

      {loading ? (
        <div className="flex justify-center items-center p-8">
          <Spinner size="xl" />
        </div>
      ) : productUoms.length === 0 ? (
        <div className="p-8 text-center">
          <p className="text-gray-500 mb-4">No units of measurement defined for this product</p>
          <Button color="primary" size="sm" onClick={handleAddClick}>
            <HiOutlinePlus className="mr-2 h-4 w-4" />
            Add Your First Unit
          </Button>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <Table hoverable>
            <Table.Head>
              <Table.HeadCell>Unit</Table.HeadCell>
              <Table.HeadCell>Conversion Factor</Table.HeadCell>
              <Table.HeadCell>Default</Table.HeadCell>
              <Table.HeadCell>Purchasing</Table.HeadCell>
              <Table.HeadCell>Selling</Table.HeadCell>
              <Table.HeadCell>
                <span className="sr-only">Actions</span>
              </Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y">
              {productUoms.map((item) => (
                <Table.Row key={item.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                  <Table.Cell className="font-medium">
                    {item.uom.name} ({item.uom.code})
                  </Table.Cell>
                  <Table.Cell>
                    {item.conversion_factor}
                  </Table.Cell>
                  <Table.Cell>
                    {item.is_default ? (
                      <Badge color="success">Default</Badge>
                    ) : (
                      <span className="text-gray-400">No</span>
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    {item.is_purchasing_unit ? (
                      <Badge color="info">Yes</Badge>
                    ) : (
                      <span className="text-gray-400">No</span>
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    {item.is_selling_unit ? (
                      <Badge color="info">Yes</Badge>
                    ) : (
                      <span className="text-gray-400">No</span>
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    <div className="flex items-center space-x-2">
                      <Button color="light" size="xs" onClick={() => handleEditClick(item)}>
                        <HiOutlinePencil className="h-4 w-4" />
                      </Button>
                      <Button color="failure" size="xs" onClick={() => handleDeleteClick(item)}>
                        <HiOutlineTrash className="h-4 w-4" />
                      </Button>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
      )}

      {/* Add Modal */}
      <Modal show={showAddModal} onClose={() => setShowAddModal(false)}>
        <Modal.Header>Add Unit of Measurement</Modal.Header>
        <Modal.Body>
          <ProductUomForm
            productId={productId}
            onSubmit={handleAddSubmit}
            isSubmitting={isSubmitting}
            error={formError || undefined}
          />
        </Modal.Body>
      </Modal>

      {/* Edit Modal */}
      <Modal show={showEditModal} onClose={() => setShowEditModal(false)}>
        <Modal.Header>Edit Unit of Measurement</Modal.Header>
        <Modal.Body>
          <ProductUomForm
            productId={productId}
            initialData={selectedUom || undefined}
            onSubmit={handleEditSubmit}
            isSubmitting={isSubmitting}
            error={formError || undefined}
          />
        </Modal.Body>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)}>
        <Modal.Header>Confirm Deletion</Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-red-500" />
            <h3 className="mb-5 text-lg font-normal text-gray-500">
              Are you sure you want to delete {selectedUom?.uom.name} ({selectedUom?.uom.code})?
            </h3>
            {formError && (
              <Alert color="failure" className="mb-4">
                {formError}
              </Alert>
            )}
            <div className="flex justify-center gap-4">
              <Button color="failure" onClick={handleDeleteConfirm} disabled={isSubmitting}>
                {isSubmitting ? <Spinner size="sm" /> : 'Yes, delete'}
              </Button>
              <Button color="gray" onClick={() => setShowDeleteConfirm(false)}>
                No, cancel
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default ProductUomManager;
