import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Button,
  TextInput,
  Textarea,
  Label,
  Select,
  Spinner,
  Alert,
  ToggleSwitch,
  FileInput
} from 'flowbite-react';
import { Product, Category, getCategories } from '../../services/product';
import { useOrganization } from '../../context/OrganizationContext';
import { supabase } from '../../lib/supabase';
import { HiOutlineExclamation } from 'react-icons/hi';

interface ProductFormProps {
  initialData?: Partial<Product>;
  onSubmit: (productData: Partial<Product>) => Promise<void>;
  isSubmitting: boolean;
  error?: string;
}

const ProductForm: React.FC<ProductFormProps> = ({
  initialData,
  onSubmit,
  isSubmitting,
  error
}) => {
  const { currentOrganization } = useOrganization();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(initialData?.image_url || null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // Form state
  const [formData, setFormData] = useState<Partial<Product>>(() => {
    // Create initial form data
    const baseData = {
      name: '',
      description: '',
      category_id: null,
      sku: '',
      barcode: '',
      unit_price: 0,
      cost_price: 0,

      stock_quantity: 0,
      min_stock_level: 0,
      is_active: true
    };

    // If we have initial data, merge it
    if (initialData) {
      const mergedData = { ...baseData, ...initialData };

      // If the product has a category property but no category_id, extract the ID
      if ((initialData as any).category && !initialData.category_id) {
        mergedData.category_id = (initialData as any).category?.id || null;
      }

      return mergedData;
    }

    return baseData;
  });

  useEffect(() => {
    const fetchCategories = async () => {
      if (!currentOrganization) return;

      setLoadingCategories(true);
      try {
        const { categories, error } = await getCategories(currentOrganization.id);
        if (error) {
          console.error('Error fetching categories:', error);
        } else {
          setCategories(categories);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, [currentOrganization]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    // Handle numeric inputs with proper validation
    if (type === 'number') {
      let numericValue: number | null = null;

      if (value !== '' && value !== null) {
        const parsed = parseFloat(value);
        if (!isNaN(parsed) && parsed >= 0) {
          numericValue = parsed;
        }
      }

      setFormData({
        ...formData,
        [name]: numericValue
      });
    } else if (name === 'category_id') {
      // Special handling for category_id - convert empty string to null
      setFormData({
        ...formData,
        [name]: value === '' ? null : value
      });
    } else {
      // Handle text inputs
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleToggleChange = (checked: boolean) => {
    setFormData({
      ...formData,
      is_active: checked
    });
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);

      // Create a preview
      const reader = new FileReader();
      reader.onload = (event) => {
        setImagePreview(event.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async (): Promise<string | null> => {
    if (!imageFile || !currentOrganization) return null;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const fileExt = imageFile.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `products/${currentOrganization.id}/${fileName}`;

      const { data, error } = await supabase.storage
        .from('product-images')
        .upload(filePath, imageFile, {
          cacheControl: '3600',
          upsert: false,
          onUploadProgress: (progress) => {
            setUploadProgress(Math.round((progress.loaded / progress.total) * 100));
          }
        });

      if (error) {
        console.error('Error uploading image:', error);
        return null;
      }

      // Get the public URL
      const { data: urlData } = supabase.storage
        .from('product-images')
        .getPublicUrl(filePath);

      return urlData.publicUrl;
    } catch (err) {
      console.error('Error uploading image:', err);
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name || formData.name.trim() === '') {
      return; // Form validation will handle this
    }

    if (formData.unit_price === null || formData.unit_price === undefined || formData.unit_price < 0) {
      return; // Form validation will handle this
    }

    let productData = { ...formData };

    // Ensure numeric fields are properly set
    productData.unit_price = productData.unit_price || 0;
    productData.cost_price = productData.cost_price || 0;
    // stock_quantity is managed through inventory transactions, not manual updates
    productData.min_stock_level = productData.min_stock_level || 0;

    // Convert empty strings to null for optional fields
    if (productData.sku === '') {
      productData.sku = null;
    }
    if (productData.barcode === '') {
      productData.barcode = null;
    }

    // Upload image if there's a new one
    if (imageFile) {
      const imageUrl = await uploadImage();
      if (imageUrl) {
        productData.image_url = imageUrl;
      }
    }

    await onSubmit(productData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {error}
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-6">
          {/* Basic Information */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="name" value="Product Name *" />
            </div>
            <TextInput
              id="name"
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              required
            />
          </div>

          <div>
            <div className="mb-2 block">
              <Label htmlFor="description" value="Description" />
            </div>
            <Textarea
              id="description"
              name="description"
              value={formData.description || ''}
              onChange={handleChange}
              rows={4}
            />
          </div>

          <div>
            <div className="mb-2 block">
              <Label htmlFor="category_id" value="Category" />
            </div>
            <Select
              id="category_id"
              name="category_id"
              value={formData.category_id || ''}
              onChange={handleChange}
              disabled={loadingCategories}
            >
              <option value="">Select a category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </Select>
          </div>

          {/* Product Identifiers */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="mb-2 block">
                <Label htmlFor="sku" value="SKU (Optional)" />
              </div>
              <TextInput
                id="sku"
                name="sku"
                value={formData.sku || ''}
                onChange={handleChange}
                placeholder="Enter SKU if available"
              />
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="barcode" value="Barcode (Optional)" />
              </div>
              <TextInput
                id="barcode"
                name="barcode"
                value={formData.barcode || ''}
                onChange={handleChange}
                placeholder="Enter barcode if available"
              />
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="mb-2 block">
                <Label htmlFor="unit_price" value="Selling Price *" />
              </div>
              <TextInput
                id="unit_price"
                name="unit_price"
                type="number"
                step="0.01"
                min="0"
                value={formData.unit_price || ''}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="cost_price" value="Cost Price" />
              </div>
              <TextInput
                id="cost_price"
                name="cost_price"
                type="number"
                step="0.01"
                min="0"
                value={formData.cost_price || ''}
                onChange={handleChange}
              />
            </div>
          </div>



          {/* Inventory - Only show for existing products */}
          {initialData?.id && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="stock_quantity" value="Current Stock" />
                  <p className="text-xs text-gray-500 mt-1">
                    Stock quantity is managed through inventory transactions. Use "Adjust Inventory" to change stock levels.
                  </p>
                </div>
                <div className="relative">
                  <TextInput
                    id="stock_quantity"
                    name="stock_quantity"
                    type="number"
                    step="1"
                    min="0"
                    value={formData.stock_quantity || ''}
                    disabled
                    className="bg-gray-50"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <Link
                      to={`/inventory/adjust/${initialData?.id}`}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      Adjust Stock
                    </Link>
                  </div>
                </div>
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="min_stock_level" value="Minimum Stock Level" />
                </div>
                <TextInput
                  id="min_stock_level"
                  name="min_stock_level"
                  type="number"
                  step="1"
                  min="0"
                  value={formData.min_stock_level || ''}
                  onChange={handleChange}
                />
              </div>
            </div>
          )}

          {/* Image Upload */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="image" value="Product Image" />
            </div>
            <FileInput
              id="image"
              accept="image/*"
              onChange={handleImageChange}
              helperText="Upload a product image (max 5MB)"
            />

            {imagePreview && (
              <div className="mt-2">
                <img
                  src={imagePreview}
                  alt="Product preview"
                  className="h-32 w-32 object-cover rounded-md border border-gray-200"
                />
              </div>
            )}

            {isUploading && (
              <div className="mt-2">
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-500 mt-1">Uploading: {uploadProgress}%</p>
              </div>
            )}
          </div>

          {/* Status */}
          <div className="flex items-center gap-2">
            <ToggleSwitch
              checked={formData.is_active || false}
              onChange={handleToggleChange}
              label="Active Product"
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button type="submit" disabled={isSubmitting || isUploading}>
          {isSubmitting ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Product'
          )}
        </Button>
      </div>
    </form>
  );
};

export default ProductForm;
