import React, { useState, useEffect } from 'react';
import { Button } from 'flowbite-react';
import { HiOutlineX, HiOutlineSearch } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { Product } from '../../services/product';
import { productCache } from '../../services/productCache';
import AsyncSelect from 'react-select/async';
import { GroupBase, StylesConfig } from 'react-select';

interface EnhancedProductSearchSelectorProps {
  value: string;
  onChange: (productId: string, product: Product | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  pageSize?: number;
  instanceId?: string; // Preserved for compatibility
}

interface ProductOption {
  value: string;
  label: string;
  product: Product;
}

const EnhancedProductSearchSelector: React.FC<EnhancedProductSearchSelectorProps> = ({
  value,
  onChange,
  placeholder = 'Search for a product...',
  className = '',
  disabled = false,
  required = false,
  pageSize = 10,
  instanceId,
}) => {
  const { currentOrganization } = useOrganization();
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  
  // Format product as option for react-select
  const formatProductAsOption = (product: Product): ProductOption => {
    // Build a descriptive label that includes the most important product details
    let label = product.name;
    
    // Add SKU if available (important for product identification)
    if (product.sku) {
      label += ` (SKU: ${product.sku})`;
    }
    
    // Add barcode if available and different from SKU
    if (product.barcode && product.barcode !== product.sku) {
      label += ` | Barcode: ${product.barcode}`;
    }
    
    return {
      value: product.id,
      label,
      product
    };
  };
  
  // The loadOptions function for AsyncSelect
  const loadOptions = async (inputValue: string): Promise<ProductOption[]> => {
    if (!currentOrganization) return [];
    
    try {
      const { products } = await productCache.searchProducts(
        currentOrganization.id,
        inputValue,
        1, // Page
        pageSize
      );
      
      // Format products as options for react-select
      const newOptions = products.map(formatProductAsOption);
      
      return newOptions;
    } catch (error) {
      return [];
    }
  };
  
  // Fetch the selected product when the component mounts or value changes
  useEffect(() => {
    const fetchSelectedProduct = async () => {
      if (!value || !currentOrganization) return;
      
      // If we already have the selected product, no need to fetch
      if (selectedProduct && selectedProduct.id === value) return;
      
      setIsLoading(true);
      try {
        const product = await productCache.getProduct(currentOrganization.id, value);
        if (product) {
          setSelectedProduct(product);
        }
      } catch (error) {
        // Silently handle error
      } finally {
        setIsLoading(false);
      }
    };

    fetchSelectedProduct();
  }, [value, currentOrganization, selectedProduct]);

  // Handle selection change
  const handleSelectChange = (option: ProductOption | null) => {
    if (option) {
      setSelectedProduct(option.product);
      onChange(option.value, option.product);
    } else {
      setSelectedProduct(null);
      onChange('', null);
    }
  };

  // Handle clearing the selection
  const handleClearSelection = () => {
    setSelectedProduct(null);
    onChange('', null);
  };

  // Custom styles for the AsyncSelect component
  const customStyles: StylesConfig<ProductOption, false, GroupBase<ProductOption>> = {
    control: (provided, state) => ({
      ...provided,
      minHeight: '38px',
      paddingLeft: '30px', // Accommodate the search icon
      borderRadius: '0.5rem',
      boxShadow: state.isFocused ? '0 0 0 1px rgb(59, 130, 246)' : 'none',
      borderColor: state.isFocused ? 'rgb(59, 130, 246)' : 'rgb(209, 213, 219)',
      '&:hover': {
        borderColor: state.isFocused ? 'rgb(59, 130, 246)' : 'rgb(156, 163, 175)'
      }
    }),
    menu: (provided) => ({
      ...provided,
      borderRadius: '0.5rem',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      zIndex: 99999
    }),
    menuList: (provided) => ({
      ...provided,
      padding: '5px',
      maxHeight: '300px'
    }),
    option: (provided, { isSelected, isFocused }) => ({
      ...provided,
      backgroundColor: isSelected 
        ? 'rgb(59, 130, 246)' 
        : isFocused 
          ? 'rgb(243, 244, 246)' 
          : 'white',
      color: isSelected ? 'white' : 'rgb(17, 24, 39)',
      padding: '8px 12px',
      cursor: 'pointer',
      '&:active': {
        backgroundColor: isSelected ? 'rgb(59, 130, 246)' : 'rgb(229, 231, 235)'
      }
    }),
    input: (provided) => ({
      ...provided,
      color: 'rgb(17, 24, 39)'
    }),
    singleValue: (provided) => ({
      ...provided,
      color: 'rgb(17, 24, 39)'
    }),
    placeholder: (provided) => ({
      ...provided,
      color: 'rgb(156, 163, 175)'
    }),
    indicatorSeparator: (provided) => ({
      ...provided,
      backgroundColor: 'rgb(209, 213, 219)'
    }),
    dropdownIndicator: (provided) => ({
      ...provided,
      color: 'rgb(107, 114, 128)',
      '&:hover': {
        color: 'rgb(75, 85, 99)'
      }
    }),
    clearIndicator: (provided) => ({
      ...provided,
      color: 'rgb(107, 114, 128)',
      '&:hover': {
        color: 'rgb(239, 68, 68)'
      }
    }),
    loadingIndicator: (provided) => ({
      ...provided, 
      color: 'rgb(107, 114, 128)'
    })
  };

  return (
    <div className={`relative ${className}`}>
      {selectedProduct ? (
        <div className="flex items-center border rounded-lg p-2 bg-white">
          <div className="flex-grow">
            <div className="font-medium">{selectedProduct.name}</div>
            <div className="text-xs text-gray-500">
              {selectedProduct.sku && `SKU: ${selectedProduct.sku}`}
              {selectedProduct.barcode && ` | Barcode: ${selectedProduct.barcode}`}
            </div>
          </div>
          <Button
            color="light"
            size="xs"
            onClick={handleClearSelection}
            disabled={disabled}
            title="Clear selection"
          >
            <HiOutlineX className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-10">
            <HiOutlineSearch className="h-5 w-5 text-gray-400" />
          </div>
          
          <AsyncSelect<ProductOption>
            loadOptions={loadOptions}
            onChange={handleSelectChange}
            placeholder={placeholder}
            isDisabled={disabled}
            className="react-select-container"
            classNamePrefix="react-select"
            isLoading={isLoading}
            isClearable={true}
            required={required}
            menuPlacement="auto"
            menuPosition="fixed"
            menuPortalTarget={document.body}
            instanceId={instanceId}
            formatOptionLabel={(option: ProductOption) => (
              <div className="flex flex-col">
                <div className="font-medium">{option.product.name}</div>
                <div className="text-xs text-gray-500">
                  {option.product.sku && `SKU: ${option.product.sku}`}
                  {option.product.barcode && option.product.barcode !== option.product.sku && 
                    ` | Barcode: ${option.product.barcode}`}
                </div>
              </div>
            )}
            styles={customStyles}
            noOptionsMessage={({ inputValue }) => 
              inputValue.trim() ? "No products found" : "Type to search for products"
            }
            cacheOptions
            defaultOptions
          />
        </div>
      )}
    </div>
  );
};

export default EnhancedProductSearchSelector;
