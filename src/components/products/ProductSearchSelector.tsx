import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { TextIn<PERSON>, Spinner, But<PERSON> } from 'flowbite-react';
import { HiOutlineSearch, HiOutlineX } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { Product, getProducts } from '../../services/product';
import { useDebounce } from '../../hooks/useDebounce';

interface ProductSearchSelectorProps {
  value: string;
  onChange: (productId: string, product: Product | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
}

const ProductSearchSelector: React.FC<ProductSearchSelectorProps> = ({
  value,
  onChange,
  placeholder = 'Search for a product...',
  className = '',
  disabled = false,
  required = false,
}) => {
  const { currentOrganization } = useOrganization();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });

  // Fetch the selected product when the component mounts or value changes
  useEffect(() => {
    const fetchSelectedProduct = async () => {
      if (!value || !currentOrganization) return;

      // If we already have the product in our list, use it
      const existingProduct = products.find(p => p.id === value);
      if (existingProduct) {
        setSelectedProduct(existingProduct);
        return;
      }

      // Otherwise, fetch it
      setLoading(true);
      try {
        const { products: fetchedProducts } = await getProducts(currentOrganization.id, {
          searchQuery: '',
          limit: 1,
        });

        const product = fetchedProducts.find(p => p.id === value);
        if (product) {
          setSelectedProduct(product);
        }
      } catch (err) {
        console.error('Error fetching selected product:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchSelectedProduct();
  }, [value, currentOrganization]);

  // Search for products when the debounced query changes
  useEffect(() => {
    const searchProducts = async () => {
      if (!currentOrganization) return;
      if (!debouncedSearchQuery.trim() && !isOpen) return;

      setLoading(true);
      setError(null);

      try {
        const { products: fetchedProducts, error: fetchError } = await getProducts(
          currentOrganization.id,
          {
            searchQuery: debouncedSearchQuery,
            limit: 10,
            isActive: true,
          }
        );

        if (fetchError) {
          setError(fetchError);
        } else {
          setProducts(fetchedProducts);
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred while searching for products');
      } finally {
        setLoading(false);
      }
    };

    searchProducts();
  }, [debouncedSearchQuery, currentOrganization, isOpen]);

  // Close the dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if the click is outside both the input container and the dropdown
      const isClickInsideInput = dropdownRef.current && dropdownRef.current.contains(event.target as Node);

      // Get the dropdown element from the DOM
      const dropdownElement = document.querySelector('.product-search-dropdown');
      const isClickInsideDropdown = dropdownElement && dropdownElement.contains(event.target as Node);

      if (!isClickInsideInput && !isClickInsideDropdown) {
        setIsOpen(false);
      }
    };

    // Update dropdown position on window resize
    const updateDropdownPosition = () => {
      if (isOpen && inputRef.current) {
        const rect = inputRef.current.getBoundingClientRect();
        setDropdownPosition({
          top: rect.bottom + window.scrollY,
          left: rect.left + window.scrollX,
          width: rect.width
        });
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('resize', updateDropdownPosition);
    window.addEventListener('scroll', updateDropdownPosition);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', updateDropdownPosition);
      window.removeEventListener('scroll', updateDropdownPosition);
    };
  }, [isOpen]);

  // Handle product selection
  const handleSelectProduct = (product: Product) => {
    setSelectedProduct(product);
    setSearchQuery('');
    setIsOpen(false);
    onChange(product.id, product);
  };

  // Handle clearing the selection
  const handleClearSelection = () => {
    setSelectedProduct(null);
    setSearchQuery('');
    onChange('', null);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Handle input focus
  const handleFocus = () => {
    setIsOpen(true);

    // Calculate the position for the dropdown
    if (inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      });
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setIsOpen(true);

    // Calculate the position for the dropdown
    if (inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      });
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {selectedProduct ? (
        <div className="flex items-center border rounded-lg p-2 bg-white">
          <div className="flex-grow">
            <div className="font-medium">{selectedProduct.name}</div>
            <div className="text-xs text-gray-500">
              {selectedProduct.sku && `SKU: ${selectedProduct.sku}`}
              {selectedProduct.barcode && ` | Barcode: ${selectedProduct.barcode}`}
            </div>
          </div>
          <Button
            color="light"
            size="xs"
            onClick={handleClearSelection}
            disabled={disabled}
            title="Clear selection"
          >
            <HiOutlineX className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <HiOutlineSearch className="h-5 w-5 text-gray-400" />
          </div>
          <TextInput
            ref={inputRef}
            type="text"
            value={searchQuery}
            onChange={handleInputChange}
            onFocus={handleFocus}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            className="pl-10"
          />
          {loading && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <Spinner size="sm" />
            </div>
          )}
        </div>
      )}

      {isOpen && !selectedProduct && createPortal(
        <div
          className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto product-search-dropdown"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`
          }}
        >
          {error && (
            <div className="p-3 text-sm text-red-500">{error}</div>
          )}

          {!loading && products.length === 0 && !error && (
            <div className="p-3 text-sm text-gray-500">
              {debouncedSearchQuery.trim()
                ? 'No products found. Try a different search term.'
                : 'Type to search for products.'}
            </div>
          )}

          {products.map(product => (
            <div
              key={product.id}
              className="p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
              onClick={() => handleSelectProduct(product)}
            >
              <div className="font-medium">{product.name}</div>
              <div className="text-xs text-gray-500">
                {product.sku && `SKU: ${product.sku}`}
                {product.barcode && ` | Barcode: ${product.barcode}`}
              </div>
            </div>
          ))}
        </div>,
        document.body
      )}
    </div>
  );
};

export default ProductSearchSelector;
