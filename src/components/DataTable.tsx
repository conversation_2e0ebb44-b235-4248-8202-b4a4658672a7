import React, { useState } from 'react';
import { Table, Pagination } from 'flowbite-react';

interface Column {
  name: string;
  selector: (row: any) => React.ReactNode;
  sortable?: boolean;
  sortField?: string;
}

interface DataTableProps {
  columns: Column[];
  data: any[];
  pagination?: boolean;
  pageSize?: number;
}

export const DataTable: React.FC<DataTableProps> = ({
  columns,
  data,
  pagination = false,
  pageSize = 10
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Handle sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Sort data if needed
  const sortedData = React.useMemo(() => {
    if (!sortField) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (aValue === bValue) return 0;
      
      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return sortDirection === 'asc'
        ? aValue - bValue
        : bValue - aValue;
    });
  }, [data, sortField, sortDirection]);

  // Paginate data if needed
  const paginatedData = React.useMemo(() => {
    if (!pagination) return sortedData;
    
    const startIndex = (currentPage - 1) * pageSize;
    return sortedData.slice(startIndex, startIndex + pageSize);
  }, [sortedData, pagination, currentPage, pageSize]);

  // Calculate total pages
  const totalPages = Math.ceil(data.length / pageSize);

  // Handle page change
  const onPageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div>
      <div className="overflow-x-auto">
        <Table>
          <Table.Head>
            {columns.map((column, index) => (
              <Table.HeadCell 
                key={index}
                onClick={() => column.sortable && column.sortField && handleSort(column.sortField)}
                className={column.sortable ? 'cursor-pointer' : ''}
              >
                {column.name}
                {column.sortable && column.sortField === sortField && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </Table.HeadCell>
            ))}
          </Table.Head>
          <Table.Body className="divide-y">
            {paginatedData.map((row, rowIndex) => (
              <Table.Row key={rowIndex} className="bg-white">
                {columns.map((column, colIndex) => (
                  <Table.Cell key={colIndex}>
                    {column.selector(row)}
                  </Table.Cell>
                ))}
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>

      {pagination && totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
            showIcons
          />
        </div>
      )}
    </div>
  );
};
