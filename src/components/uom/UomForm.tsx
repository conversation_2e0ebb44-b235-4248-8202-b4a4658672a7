import { useState, useEffect } from 'react';
import {
  Button,
  TextInput,
  Textarea,
  Label,
  Alert,
  Spinner,
  ToggleSwitch
} from 'flowbite-react';
import { UnitOfMeasurement } from '../../types/uom.types';
import { HiOutlineExclamation } from 'react-icons/hi';

interface UomFormProps {
  initialData?: Partial<UnitOfMeasurement>;
  onSubmit: (uomData: Partial<UnitOfMeasurement>) => Promise<void>;
  isSubmitting: boolean;
  error?: string;
}

const UomForm: React.FC<UomFormProps> = ({
  initialData,
  onSubmit,
  isSubmitting,
  error
}) => {
  // Form state
  const [formData, setFormData] = useState<Partial<UnitOfMeasurement>>(() => {
    // Create initial form data
    return {
      code: '',
      name: '',
      description: '',
      is_active: true,
      ...initialData
    };
  });

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData
      }));
    }
  }, [initialData]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle toggle switch changes
  const handleToggleChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      is_active: checked
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {error}
        </Alert>
      )}

      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Code */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="code" value="Code *" />
            </div>
            <TextInput
              id="code"
              name="code"
              value={formData.code || ''}
              onChange={handleChange}
              required
              placeholder="e.g., pcs, box, kg"
            />
            <p className="mt-1 text-sm text-gray-500">
              Short code used in reports and labels
            </p>
          </div>

          {/* Name */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="name" value="Name *" />
            </div>
            <TextInput
              id="name"
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              required
              placeholder="e.g., Pieces, Box, Kilogram"
            />
            <p className="mt-1 text-sm text-gray-500">
              Full name displayed in forms
            </p>
          </div>
        </div>

        {/* Description */}
        <div>
          <div className="mb-2 block">
            <Label htmlFor="description" value="Description" />
          </div>
          <Textarea
            id="description"
            name="description"
            value={formData.description || ''}
            onChange={handleChange}
            rows={3}
            placeholder="Optional description of this unit of measurement"
          />
        </div>

        {/* Active Status */}
        <div className="flex items-center gap-2">
          <ToggleSwitch
            checked={formData.is_active || false}
            onChange={handleToggleChange}
            label="Active"
          />
          <p className="text-sm text-gray-500">
            Inactive units won't appear in selection dropdowns
          </p>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" color="primary" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Unit of Measurement'
          )}
        </Button>
      </div>
    </form>
  );
};

export default UomForm;
