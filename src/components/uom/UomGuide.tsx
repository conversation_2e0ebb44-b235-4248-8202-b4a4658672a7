import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'flowbite-react';
import { HiOutlineInformationCircle, HiOutlineLightBulb } from 'react-icons/hi';
import { Link } from 'react-router-dom';

interface UomGuideProps {
  productId: string;
  productName?: string;
}

const UomGuide: React.FC<UomGuideProps> = ({ productId, productName }) => {
  return (
    <Card className="mb-4">
      <div className="flex flex-col gap-4">
        <div className="flex items-start">
          <HiOutlineInformationCircle className="mr-3 h-6 w-6 text-blue-500 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Units of Measurement Guide
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Units of Measurement (UoM) allow you to track inventory in different units (e.g., pieces, boxes, kilograms).
              {productName && ` You need to set up UoMs for ${productName} before you can use it in transactions.`}
            </p>
          </div>
        </div>

        <Alert color="info" icon={HiOutlineLightBulb}>
          <div className="font-medium">Why set up Units of Measurement?</div>
          <ul className="mt-1.5 list-disc list-inside text-sm">
            <li>Purchase products in one unit (e.g., boxes) and sell in another (e.g., pieces)</li>
            <li>Track inventory in multiple units with automatic conversion</li>
            <li>Generate reports with quantities in your preferred units</li>
          </ul>
        </Alert>

        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <h4 className="font-medium mb-2 text-gray-900 dark:text-white">How to add Units of Measurement:</h4>
          <ol className="list-decimal list-inside text-sm text-gray-500 dark:text-gray-400 space-y-2">
            <li>Click the <strong>"Add Unit"</strong> button above</li>
            <li>Select a unit from the dropdown (e.g., Pieces, Box, Kilogram)</li>
            <li>Set the <strong>conversion factor</strong> (e.g., 1 box = 12 pieces, so conversion factor is 12)</li>
            <li>Check the appropriate options:
              <ul className="list-disc list-inside ml-5 mt-1">
                <li><strong>Default Unit:</strong> The primary unit for this product</li>
                <li><strong>Purchasing Unit:</strong> Can be used when purchasing this product</li>
                <li><strong>Selling Unit:</strong> Can be used when selling this product</li>
              </ul>
            </li>
            <li>Click <strong>"Save Unit"</strong> to add it</li>
          </ol>
        </div>

        <div className="flex flex-wrap gap-2">
          <Link to={`/settings/uom`}>
            <Button color="light" size="sm">
              Manage Global Units
            </Button>
          </Link>
          <Link to={`/products/${productId}`}>
            <Button color="light" size="sm">
              Back to Product Details
            </Button>
          </Link>
        </div>
      </div>
    </Card>
  );
};

export default UomGuide;
