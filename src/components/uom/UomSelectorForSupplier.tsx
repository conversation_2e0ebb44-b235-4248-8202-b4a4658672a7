import { useState, useEffect } from 'react';
import { Select, Spinner } from 'flowbite-react';
import { getUnitsOfMeasurement } from '../../services/uom';
import { UnitOfMeasurement } from '../../types/uom.types';
import { useOrganization } from '../../context/OrganizationContext';

interface UomSelectorForSupplierProps {
  value: string;
  onChange: (uomId: string, uomName?: string) => void;
  disabled?: boolean;
  className?: string;
}

const UomSelectorForSupplier: React.FC<UomSelectorForSupplierProps> = ({
  value,
  onChange,
  disabled = false,
  className = ''
}) => {
  const { currentOrganization } = useOrganization();
  const [uoms, setUoms] = useState<UnitOfMeasurement[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Keep track of whether we've already auto-selected a UoM
  const [hasAutoSelected, setHasAutoSelected] = useState(false);
  // Keep track of whether we've already fetched UoMs
  const [hasFetchedUoms, setHasFetchedUoms] = useState(false);

  // Fetch UoMs only once when the component mounts
  useEffect(() => {
    // Skip if we've already fetched UoMs or if there's no organization
    if (hasFetchedUoms || !currentOrganization) {
      return;
    }

    const fetchUoms = async () => {
      setLoading(true);
      setError(null);

      console.log('Fetching all UoMs for organization:', currentOrganization.id);

      try {
        const { uoms: organizationUoms, error: fetchError } = await getUnitsOfMeasurement(
          currentOrganization.id
        );

        console.log('Fetched organization UoMs:', organizationUoms);

        if (fetchError) {
          console.error('Error fetching UoMs:', fetchError);
          setError(fetchError);
        } else if (!organizationUoms || organizationUoms.length === 0) {
          console.warn('No UoMs found for organization');
          setError('No units of measurement found for this organization');
        } else {
          setUoms(organizationUoms);
          setHasFetchedUoms(true);

          // Only auto-select if no value is provided and we're not disabled
          if (!value && !disabled && organizationUoms.length > 0 && !hasAutoSelected) {
            console.log('Auto-selecting UoM because no value is provided');
            setHasAutoSelected(true);

            // Try to find the 'pcs' UoM first
            const pcsUom = organizationUoms.find(u => u.code === 'pcs');
            if (pcsUom) {
              onChange(pcsUom.id, `${pcsUom.name} (${pcsUom.code})`);
            } else {
              const firstUom = organizationUoms[0];
              onChange(firstUom.id, `${firstUom.name} (${firstUom.code})`);
            }
          }
        }
      } catch (err: any) {
        console.error('Exception in fetchUoms:', err);
        setError(err.message || 'Failed to load units of measurement');
      } finally {
        setLoading(false);
      }
    };

    fetchUoms();
  }, [currentOrganization, value, disabled, hasAutoSelected, hasFetchedUoms, onChange]);

  // Handle value changes from parent
  useEffect(() => {
    if (value && uoms.length > 0) {
      const selectedUom = uoms.find(u => u.id === value);
      if (!selectedUom) {
        console.warn('Selected UoM not found in available UoMs:', value);
      } else {
        console.log('UoM value from parent is valid:', {
          id: value,
          name: `${selectedUom.name} (${selectedUom.code})`
        });
      }
    }
  }, [value, uoms]);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedUomId = e.target.value;
    const selectedUom = uoms.find(u => u.id === selectedUomId);
    console.log('UoM manually changed to:', {
      id: selectedUomId,
      name: selectedUom ? `${selectedUom.name} (${selectedUom.code})` : undefined
    });
    onChange(selectedUomId, selectedUom ? `${selectedUom.name} (${selectedUom.code})` : undefined);
  };

  // Add a separate effect to handle value changes from parent
  useEffect(() => {
    if (value && uoms.length > 0) {
      const selectedUom = uoms.find(u => u.id === value);
      if (!selectedUom) {
        console.warn('Selected UoM not found in available UoMs:', value);
      } else {
        console.log('UoM value updated from parent:', {
          id: value,
          name: selectedUom ? `${selectedUom.name} (${selectedUom.code})` : undefined
        });
      }
    }
  }, [value, uoms]);

  if (loading) {
    return (
      <div className="flex items-center">
        <Spinner size="sm" className="mr-2" />
        <span className="text-sm text-gray-500">Loading...</span>
      </div>
    );
  }

  if (error) {
    return <div className="text-sm text-red-500">{error}</div>;
  }

  // If no UoMs are available, show a select with a default option
  if (uoms.length === 0) {
    return (
      <div>
        <Select
          value=""
          disabled={true}
          className={className}
        >
          <option value="">No units available</option>
        </Select>
        <div className="mt-1 text-xs text-red-500">
          This organization has no units of measurement defined. Please add UoMs in the settings first.
        </div>
      </div>
    );
  }

  return (
    <Select
      value={value}
      onChange={handleChange}
      disabled={disabled || loading}
      className={className}
    >
      <option value="">Select a unit</option>
      {uoms.map(uom => (
        <option key={uom.id} value={uom.id}>
          {uom.name} ({uom.code})
        </option>
      ))}
    </Select>
  );
};

export default UomSelectorForSupplier;
