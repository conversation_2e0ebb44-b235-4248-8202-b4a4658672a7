import { useState, useEffect } from 'react';
import {
  Button,
  TextInput,
  Label,
  Alert,
  Spinner,
  ToggleSwitch,
  Select
} from 'flowbite-react';
import { ProductUom, UnitOfMeasurement } from '../../types/uom.types';
import { HiOutlineExclamation } from 'react-icons/hi';
import { getUnitsOfMeasurement } from '../../services/uom';
import { useOrganization } from '../../context/OrganizationContext';

interface ProductUomFormProps {
  productId: string;
  initialData?: Partial<ProductUom>;
  onSubmit: (uomData: Partial<ProductUom>) => Promise<void>;
  isSubmitting: boolean;
  error?: string;
}

const ProductUomForm: React.FC<ProductUomFormProps> = ({
  productId,
  initialData,
  onSubmit,
  isSubmitting,
  error
}) => {
  const { currentOrganization } = useOrganization();
  const [uoms, setUoms] = useState<UnitOfMeasurement[]>([]);
  const [loadingUoms, setLoadingUoms] = useState(false);
  const [uomError, setUomError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<Partial<ProductUom>>(() => {
    // Create initial form data
    return {
      product_id: productId,
      uom_id: '',
      conversion_factor: 1,
      is_default: false,
      is_purchasing_unit: false,
      is_selling_unit: false,
      ...initialData
    };
  });

  // Fetch UoMs when component mounts
  useEffect(() => {
    const fetchUoms = async () => {
      if (!currentOrganization) return;

      setLoadingUoms(true);
      setUomError(null);

      try {
        const { uoms: uomData, error: fetchError } = await getUnitsOfMeasurement(
          currentOrganization.id,
          { isActive: true }
        );

        if (fetchError) {
          setUomError(fetchError);
        } else {
          setUoms(uomData);
        }
      } catch (err: any) {
        setUomError(err.message || 'Failed to load units of measurement');
      } finally {
        setLoadingUoms(false);
      }
    };

    fetchUoms();
  }, [currentOrganization]);

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData
      }));
    }
  }, [initialData]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'number') {
      setFormData(prev => ({
        ...prev,
        [name]: value === '' ? '' : parseFloat(value)
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle toggle switch changes
  const handleToggleChange = (name: string) => (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {error}
        </Alert>
      )}

      {uomError && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {uomError}
        </Alert>
      )}

      <div className="space-y-6">
        {/* Unit of Measurement */}
        <div>
          <div className="mb-2 block">
            <Label htmlFor="uom_id" value="Unit of Measurement *" />
          </div>
          <Select
            id="uom_id"
            name="uom_id"
            value={formData.uom_id || ''}
            onChange={handleChange}
            required
            disabled={loadingUoms || !!initialData?.id}
          >
            <option value="">Select a unit of measurement</option>
            {uoms.map(uom => (
              <option key={uom.id} value={uom.id}>
                {uom.name} ({uom.code})
              </option>
            ))}
          </Select>
          {loadingUoms && (
            <div className="mt-2 flex items-center">
              <Spinner size="sm" className="mr-2" />
              <span className="text-sm text-gray-500">Loading units of measurement...</span>
            </div>
          )}
        </div>

        {/* Conversion Factor */}
        <div>
          <div className="mb-2 block">
            <Label htmlFor="conversion_factor" value="Conversion Factor *" />
          </div>
          <TextInput
            id="conversion_factor"
            name="conversion_factor"
            type="number"
            step="0.00001"
            min="0.00001"
            value={formData.conversion_factor || ''}
            onChange={handleChange}
            required
          />
          <p className="mt-1 text-sm text-gray-500">
            Multiply by this factor to convert to the base unit (e.g., 12 for a dozen)
          </p>
        </div>

        {/* Flags */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <ToggleSwitch
              checked={formData.is_default || false}
              onChange={handleToggleChange('is_default')}
              label="Default Unit"
            />
            <p className="text-sm text-gray-500">
              This is the primary unit for this product
            </p>
          </div>

          <div className="flex items-center gap-2">
            <ToggleSwitch
              checked={formData.is_purchasing_unit || false}
              onChange={handleToggleChange('is_purchasing_unit')}
              label="Purchasing Unit"
            />
            <p className="text-sm text-gray-500">
              This unit can be used for purchasing
            </p>
          </div>

          <div className="flex items-center gap-2">
            <ToggleSwitch
              checked={formData.is_selling_unit || false}
              onChange={handleToggleChange('is_selling_unit')}
              label="Selling Unit"
            />
            <p className="text-sm text-gray-500">
              This unit can be used for selling
            </p>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" color="primary" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Unit'
          )}
        </Button>
      </div>
    </form>
  );
};

export default ProductUomForm;
