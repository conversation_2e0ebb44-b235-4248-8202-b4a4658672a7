import { useState, useEffect } from 'react';
import { Select, Spinner } from 'flowbite-react';
import { getProductUoms } from '../../services/productUom';
import { ProductUom, UnitOfMeasurement } from '../../types/uom.types';
import { useOrganization } from '../../context/OrganizationContext';
import { useUom } from '../../context/UomContext';
import { useProductUomCache } from '../../context/ProductUomContext';
import { supabase } from '../../lib/supabase';

interface UomSelectorProps {
  productId: string;
  value: string;
  onChange: (uomId: string) => void;
  onUomChange?: (uom: ProductUom & { uom: UnitOfMeasurement }) => void;
  filter?: 'all' | 'purchasing' | 'selling';
  disabled?: boolean;
  className?: string;
  // New prop to accept pre-loaded UoMs
  preloadedUoms?: (ProductUom & { uom: UnitOfMeasurement })[];
}

const UomSelector: React.FC<UomSelectorProps> = ({
  productId,
  value,
  onChange,
  onUomChange,
  filter = 'all',
  disabled = false,
  className = '',
  preloadedUoms = []
}) => {
  const { currentOrganization } = useOrganization();
  const { uoms: globalUoms } = useUom();
  const productUomCache = useProductUomCache();
  const [uoms, setUoms] = useState<(ProductUom & { uom: UnitOfMeasurement })[]>(preloadedUoms);
  const [loading, setLoading] = useState(preloadedUoms.length === 0);
  const [error, setError] = useState<string | null>(null);
  const [defaultUom, setDefaultUom] = useState<(ProductUom & { uom: UnitOfMeasurement }) | null>(null);
  const [showDefaultMessage, setShowDefaultMessage] = useState(false);

  // Main effect to fetch UoMs for the product or use preloaded UoMs
  useEffect(() => {
    // Don't run effect if we don't have required dependencies
    if (!productId || !currentOrganization) {
      return;
    }

    // Function to filter UoMs based on the filter type
    const filterUomsByType = (allUoms) => {
      if (!allUoms || allUoms.length === 0) return [];
      
      let filteredUoms = allUoms;
      
      if (filter === 'purchasing') {
        const purchasingUoms = allUoms.filter(u => u.is_purchasing_unit);
        filteredUoms = purchasingUoms.length > 0 ? purchasingUoms : allUoms;
      } else if (filter === 'selling') {
        const sellingUoms = allUoms.filter(u => u.is_selling_unit);
        filteredUoms = sellingUoms.length > 0 ? sellingUoms : allUoms;
      }
      
      return filteredUoms;
    };

    // Function to find default UoM from a list
    const findDefaultUomInList = (uomList) => {
      if (!uomList || uomList.length === 0) return null;
      return uomList.find(u => u.is_default) || uomList[0];
    };

    // If we have preloaded UoMs, use them
    if (preloadedUoms && preloadedUoms.length > 0) {
      const filteredUoms = filterUomsByType(preloadedUoms);
      const foundDefaultUom = findDefaultUomInList(filteredUoms);
      
      setUoms(filteredUoms);
      if (foundDefaultUom) setDefaultUom(foundDefaultUom);
      setLoading(false);
      return;
    }

    // Only fetch if we don't have preloaded UoMs
    const fetchUoms = async () => {
      // Check if we have this product's UoMs in the cache
      const cachedUoms = productUomCache.getFromCache(productId);
      if (cachedUoms && cachedUoms.length > 0) {
        const filteredUoms = filterUomsByType(cachedUoms);
        const foundDefaultUom = findDefaultUomInList(filteredUoms);
        
        setUoms(filteredUoms);
        if (foundDefaultUom) setDefaultUom(foundDefaultUom);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);
      setShowDefaultMessage(false);

      try {
        const { productUoms, error: fetchError } = await getProductUoms(
          productId,
          currentOrganization.id
        );

        if (fetchError) {
          setError(fetchError);
          setShowDefaultMessage(true);
          findDefaultPcsUom();
        } else if (!productUoms || productUoms.length === 0) {
          setError('No units of measurement found for this product');
          setShowDefaultMessage(true);
          findDefaultPcsUom();
        } else {
          // Add to cache for future use
          productUomCache.addToCache(productId, productUoms);

          // Use our helper functions for consistency
          const filteredUoms = filterUomsByType(productUoms);
          const foundDefaultUom = findDefaultUomInList(filteredUoms);

          setUoms(filteredUoms);
          
          if (foundDefaultUom) {
            setDefaultUom(foundDefaultUom);
          } else if (filteredUoms.length > 0) {
            setDefaultUom(filteredUoms[0]);
          } else {
            // Try to find the default "pcs" UoM as a fallback
            findDefaultPcsUom();
          }
        }
      } catch (err: any) {
        setError(err.message || 'Failed to load units of measurement');
        setShowDefaultMessage(true);
        findDefaultPcsUom();
      } finally {
        setLoading(false);
      }
    };

    fetchUoms();
    // Using a JSON.stringify for preloadedUoms to avoid reference equality issues
    // productUomCache is intentionally omitted to prevent infinite loops
  }, [productId, currentOrganization?.id, filter, JSON.stringify(preloadedUoms?.map(u => u.id))]);

  // Function to find the default "pcs" UoM as a last resort
  const findDefaultPcsUom = async () => {
    if (!currentOrganization || !productId) return;

    try {
      // First, check if there are any UoMs defined for this product
      const { data: productUomsData, error: productUomsError } = await supabase
        .from('product_uoms')
        .select(`
          *,
          uom:uom_id (*)
        `)
        .eq('product_id', productId)
        .eq('organization_id', currentOrganization.id);

      if (!productUomsError && productUomsData && productUomsData.length > 0) {
        // Use the first UoM found
        const firstUom = productUomsData[0] as ProductUom & { uom: UnitOfMeasurement };
        const allUoms = productUomsData as (ProductUom & { uom: UnitOfMeasurement })[];
        
        // Batch all state updates together to avoid multiple renders
        const updates = () => {
          setDefaultUom(firstUom);
          setShowDefaultMessage(false); // Don't show the default message since we found a real UoM
          setUoms(allUoms);
          setError(null); // Clear any error since we found UoMs
        };
        
        // Use a single update to avoid multiple rerenders
        updates();
        return;
      }

      // Use the cached UoMs from context if available
      if (globalUoms && globalUoms.length > 0) {
        // Try to find 'pcs' UoM in the cached UoMs
        const pcsUom = globalUoms.find(u => u.code === 'pcs');

        if (pcsUom) {
          // Create a default UoM object
          const pcsDefaultUom = {
            id: `temp-${Date.now()}`, // Temporary ID
            uom_id: pcsUom.id,
            uom: pcsUom,
            product_id: productId,
            organization_id: currentOrganization.id,
            conversion_factor: 1,
            is_default: true,
            is_purchasing_unit: true,
            is_selling_unit: true,
            created_by: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          } as ProductUom & { uom: UnitOfMeasurement };

          // Batch state updates
          const updates = () => {
            setDefaultUom(pcsDefaultUom);
            setShowDefaultMessage(true);
          };
          
          updates();

          // Try to create this UoM for the product
          try {
            await supabase
              .from('product_uoms')
              .insert({
                product_id: productId,
                uom_id: pcsUom.id,
                organization_id: currentOrganization.id,
                conversion_factor: 1,
                is_default: true,
                is_purchasing_unit: true,
                is_selling_unit: true
              });
          } catch (err) {
            // Silent error handling in production
          }

          return;
        }

        // If no 'pcs' UoM found, use the first UoM in the list
        const firstUom = globalUoms[0];

        // Create a default UoM object
        const defaultUom = {
          id: `temp-${Date.now()}`, // Temporary ID
          uom_id: firstUom.id,
          uom: firstUom,
          product_id: productId,
          organization_id: currentOrganization.id,
          conversion_factor: 1,
          is_default: true,
          is_purchasing_unit: true,
          is_selling_unit: true,
          created_by: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        } as ProductUom & { uom: UnitOfMeasurement };

        // Batch state updates
        const updates = () => {
          setDefaultUom(defaultUom);
          setShowDefaultMessage(true);
        };
        
        updates();

        return;
      }

      // Fallback to database queries if context doesn't have UoMs
      // If no UoMs found for the product, find the 'pieces' UoM in this organization
      const { data: piecesUom, error: piecesError } = await supabase
        .from('units_of_measurement')
        .select('*')
        .eq('organization_id', currentOrganization.id)
        .eq('code', 'pcs')
        .single();

      if (piecesError) {
        // If no 'pcs' UoM found, try to find any UoM
        const { data: anyUom, error: anyUomError } = await supabase
          .from('units_of_measurement')
          .select('*')
          .eq('organization_id', currentOrganization.id)
          .limit(1);

        if (!anyUomError && anyUom && anyUom.length > 0) {
          // Create a default UoM object
          const altDefaultUom = {
            id: `temp-${Date.now()}`, // Temporary ID
            uom_id: anyUom[0].id,
            uom: anyUom[0],
            product_id: productId,
            organization_id: currentOrganization.id,
            conversion_factor: 1,
            is_default: true,
            is_purchasing_unit: true,
            is_selling_unit: true,
            created_by: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          } as ProductUom & { uom: UnitOfMeasurement };

          // Set the default UoM
          setDefaultUom(altDefaultUom);
          setShowDefaultMessage(true);
        }
      } else if (piecesUom) {
        // Create a default UoM object
        const pcsDefaultUom = {
          id: `temp-${Date.now()}`, // Temporary ID
          uom_id: piecesUom.id,
          uom: piecesUom,
          product_id: productId,
          organization_id: currentOrganization.id,
          conversion_factor: 1,
          is_default: true,
          is_purchasing_unit: true,
          is_selling_unit: true,
          created_by: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        } as ProductUom & { uom: UnitOfMeasurement };

        // Set the default UoM
        setDefaultUom(pcsDefaultUom);
        setShowDefaultMessage(true);

        // Try to create this UoM for the product
        try {
          await supabase
            .from('product_uoms')
            .insert({
              product_id: productId,
              uom_id: piecesUom.id,
              organization_id: currentOrganization.id,
              conversion_factor: 1,
              is_default: true,
              is_purchasing_unit: true,
              is_selling_unit: true
            });
        } catch (err) {
          // Silent error handling in production
        }
      }
    } catch (err) {
      // Silent error handling in production
    }
  };

  // Effect to update the selected UoM when the default UoM changes
  useEffect(() => {
    // Only auto-select the default UoM if no value is selected yet and defaultUom has changed
    if (defaultUom && !value && defaultUom.uom_id) {
      // Create a copy to avoid mutating the original object
      const uomToUse = { ...defaultUom };
      
      // Make sure conversion_factor is set
      if (uomToUse.conversion_factor === undefined || uomToUse.conversion_factor === null) {
        uomToUse.conversion_factor = 1;
      }

      // Call the appropriate callback (outside of render)
      const selectDefaultUom = () => {
        if (onUomChange) {
          onUomChange(uomToUse);
        } else {
          onChange(uomToUse.uom_id);
        }
      };
      
      // Use a timeout to break potential cycle
      const timerId = setTimeout(selectDefaultUom, 0);
      return () => clearTimeout(timerId);
    }
  }, [defaultUom?.uom_id, value]);

  // Handle select change
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedUomId = e.target.value;

    // If onUomChange is provided, find the selected UoM and call the callback
    if (onUomChange) {
      const selectedUom = uoms.find(u => u.uom_id === selectedUomId);

      if (selectedUom) {
        // Make sure conversion_factor is set and is a number
        if (selectedUom.conversion_factor === undefined || selectedUom.conversion_factor === null) {
          selectedUom.conversion_factor = 1;
        } else {
          // Ensure it's a number
          selectedUom.conversion_factor = Number(selectedUom.conversion_factor);
        }

        // Call onUomChange with the selected UoM
        onUomChange(selectedUom);
      }
    } else {
      // If no onUomChange is provided, call onChange
      onChange(selectedUomId);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className="flex items-center h-9">
        <Spinner size="sm" className="mr-1" />
        <span className="text-xs text-gray-500">Loading...</span>
      </div>
    );
  }

  // Render error state with default message
  if (error && showDefaultMessage) {
    return (
      <div className="flex flex-col">
        <Select
          value={defaultUom?.uom_id || ""}
          disabled={!defaultUom} // Only disable if no default UoM
          onChange={handleChange}
          className={`${className} text-sm`}
        >
          {defaultUom ? (
            <option value={defaultUom.uom_id}>
              {defaultUom.uom.name} ({defaultUom.uom.code})
            </option>
          ) : (
            <option value="">No units available</option>
          )}
        </Select>
      </div>
    );
  }

  // Render error state without default message
  if (error) {
    return (
      <div className="flex flex-col">
        <Select
          value=""
          disabled={true}
          className={`${className} text-sm`}
        >
          <option value="">No units available</option>
        </Select>
      </div>
    );
  }

  // Render normal select
  // Find the currently selected UoM
  const selectedUom = value ? uoms.find(u => u.uom_id === value) : defaultUom;

  return (
    <div className="flex flex-col">
      <Select
        value={value || defaultUom?.uom_id || ''}
        onChange={handleChange}
        disabled={loading || (uoms.length === 0 && disabled)} // Only disable if loading or no UoMs available
        className={`${className} text-sm`}
      >
        {uoms.length === 0 ? (
          <option value="">Select a unit</option>
        ) : (
          <>
            <option value="">Select a unit</option>
            {uoms.map(item => (
              <option key={item.uom_id} value={item.uom_id}>
                {item.uom.name} ({item.uom.code}) - CF: {Number(item.conversion_factor) || 1}
              </option>
            ))}
          </>
        )}
      </Select>
      {/* Hidden input to track conversion factor */}
      {selectedUom && (
        <input
          type="hidden"
          value={Number(selectedUom.conversion_factor) || 1}
          data-uom-id={selectedUom.uom_id}
          data-conversion-factor={Number(selectedUom.conversion_factor) || 1}
        />
      )}
    </div>
  );
};

export default UomSelector;
