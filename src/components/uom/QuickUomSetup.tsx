import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Checkbox, Spinner, Table } from 'flowbite-react';
import { HiOutlineCheck, HiOutlineExclamation } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getProductUoms, updateProductUom } from '../../services/productUom';
import { ProductUom, UnitOfMeasurement } from '../../types/uom.types';

interface QuickUomSetupProps {
  productId: string;
  onComplete?: () => void;
}

const QuickUomSetup: React.FC<QuickUomSetupProps> = ({ productId, onComplete }) => {
  const { currentOrganization } = useOrganization();
  const [uoms, setUoms] = useState<(ProductUom & { uom: UnitOfMeasurement })[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Track which UoMs should be purchasing units
  const [purchasingUoms, setPurchasingUoms] = useState<Record<string, boolean>>({});
  
  useEffect(() => {
    const fetchUoms = async () => {
      if (!productId || !currentOrganization) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const { productUoms, error: fetchError } = await getProductUoms(
          productId,
          currentOrganization.id
        );
        
        if (fetchError) {
          setError(fetchError);
        } else {
          setUoms(productUoms);
          
          // Initialize the purchasing UoMs state
          const initialPurchasingState: Record<string, boolean> = {};
          productUoms.forEach(uom => {
            initialPurchasingState[uom.id] = uom.is_purchasing_unit;
          });
          setPurchasingUoms(initialPurchasingState);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to load units of measurement');
      } finally {
        setLoading(false);
      }
    };
    
    fetchUoms();
  }, [productId, currentOrganization]);
  
  const handleTogglePurchasing = (uomId: string) => {
    setPurchasingUoms(prev => ({
      ...prev,
      [uomId]: !prev[uomId]
    }));
  };
  
  const handleSaveChanges = async () => {
    if (!currentOrganization) return;
    
    setSaving(true);
    setError(null);
    setSuccess(null);
    
    try {
      // Update each UoM that has changed
      const updatePromises = uoms.map(async uom => {
        const shouldBePurchasing = purchasingUoms[uom.id];
        
        // Only update if the value has changed
        if (shouldBePurchasing !== uom.is_purchasing_unit) {
          return updateProductUom(
            uom.id,
            { is_purchasing_unit: shouldBePurchasing },
            currentOrganization.id
          );
        }
        return null;
      });
      
      // Wait for all updates to complete
      await Promise.all(updatePromises.filter(Boolean));
      
      setSuccess('Units of measurement updated successfully');
      
      // Call the onComplete callback if provided
      if (onComplete) {
        onComplete();
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while updating units of measurement');
    } finally {
      setSaving(false);
    }
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Spinner size="xl" />
      </div>
    );
  }
  
  if (uoms.length === 0) {
    return (
      <Alert color="warning" icon={HiOutlineExclamation}>
        <h3 className="font-medium">No units of measurement found</h3>
        <p>
          This product doesn't have any units of measurement defined. Please add units of measurement first.
        </p>
      </Alert>
    );
  }
  
  return (
    <Card>
      <h3 className="text-lg font-medium mb-4">Quick UoM Setup for Purchasing</h3>
      
      {error && (
        <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert color="success" icon={HiOutlineCheck} className="mb-4">
          {success}
        </Alert>
      )}
      
      <p className="text-sm text-gray-500 mb-4">
        Check the units of measurement that should be available for purchasing this product:
      </p>
      
      <Table>
        <Table.Head>
          <Table.HeadCell>Unit</Table.HeadCell>
          <Table.HeadCell>Conversion Factor</Table.HeadCell>
          <Table.HeadCell>Default</Table.HeadCell>
          <Table.HeadCell>Use for Purchasing</Table.HeadCell>
        </Table.Head>
        <Table.Body className="divide-y">
          {uoms.map(uom => (
            <Table.Row key={uom.id}>
              <Table.Cell>
                {uom.uom.name} ({uom.uom.code})
              </Table.Cell>
              <Table.Cell>{uom.conversion_factor}</Table.Cell>
              <Table.Cell>
                {uom.is_default ? (
                  <span className="text-green-500">Yes</span>
                ) : (
                  <span className="text-gray-400">No</span>
                )}
              </Table.Cell>
              <Table.Cell>
                <Checkbox
                  checked={purchasingUoms[uom.id] || false}
                  onChange={() => handleTogglePurchasing(uom.id)}
                />
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
      
      <div className="flex justify-end mt-4">
        <Button
          color="primary"
          onClick={handleSaveChanges}
          disabled={saving}
        >
          {saving ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </div>
    </Card>
  );
};

export default QuickUomSetup;
