import React, { useState } from 'react';
import { Button, Alert } from 'flowbite-react';
import { HiOutlineRefresh, HiOutlineExclamation } from 'react-icons/hi';
import { cleanupAuthState } from '../../utils/authCleanup';

interface AuthCleanupButtonProps {
  variant?: 'button' | 'alert';
  className?: string;
}

const AuthCleanupButton: React.FC<AuthCleanupButtonProps> = ({ 
  variant = 'button',
  className = '' 
}) => {
  const [isCleaningUp, setIsCleaningUp] = useState(false);

  const handleCleanup = async () => {
    if (isCleaningUp) return;
    
    setIsCleaningUp(true);
    
    try {
      await cleanupAuthState();
    } catch (error) {
      console.error('Error during cleanup:', error);
      // Even if cleanup fails, redirect to login
      window.location.href = '/auth/login';
    }
  };

  if (variant === 'alert') {
    return (
      <Alert color="warning" className={className}>
        <div className="flex items-center">
          <HiOutlineExclamation className="mr-2 h-5 w-5" />
          <div className="flex-1">
            <h3 className="text-lg font-medium">Authentication Issue Detected</h3>
            <p className="mt-1 text-sm">
              There seems to be an issue with your authentication session. 
              Click the button below to fix this issue.
            </p>
          </div>
          <Button
            color="warning"
            size="sm"
            onClick={handleCleanup}
            disabled={isCleaningUp}
            className="ml-4"
          >
            <HiOutlineRefresh className={`mr-2 h-4 w-4 ${isCleaningUp ? 'animate-spin' : ''}`} />
            {isCleaningUp ? 'Fixing...' : 'Fix Now'}
          </Button>
        </div>
      </Alert>
    );
  }

  return (
    <Button
      color="gray"
      size="sm"
      onClick={handleCleanup}
      disabled={isCleaningUp}
      className={className}
    >
      <HiOutlineRefresh className={`mr-2 h-4 w-4 ${isCleaningUp ? 'animate-spin' : ''}`} />
      {isCleaningUp ? 'Cleaning up...' : 'Fix Auth Issues'}
    </Button>
  );
};

export default AuthCleanupButton;
