import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge, Spinner } from 'flowbite-react';
import {
  HiOutlineCamera,
  HiOutlineStop,
  HiOutlinePlay,
  HiOutlineEye,
  HiOutlineEyeOff,
} from 'react-icons/hi';
import {
  initializeFaceAPI,
  getCameraStream,
  analyzeFace,
  drawFaceAnalysisOverlay,
  checkBrowserSupport,
  FaceAnalysis,
} from '../../utils/faceRecognition';

interface LiveFaceAnalysisProps {
  onClose?: () => void;
  showControls?: boolean;
}

const LiveFaceAnalysis: React.FC<LiveFaceAnalysisProps> = ({
  onClose,
  showControls = true,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const analysisIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const [isInitialized, setIsInitialized] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<FaceAnalysis | null>(null);
  const [error, setError] = useState<string>('');
  const [showOverlay, setShowOverlay] = useState(true);
  const [showLandmarks, setShowLandmarks] = useState(true);

  useEffect(() => {
    initializeCamera();
    return () => {
      cleanup();
    };
  }, []);

  const initializeCamera = async () => {
    try {
      setError('');
      
      // Check browser support
      const browserSupport = checkBrowserSupport();
      if (!browserSupport.supported) {
        throw new Error(`Browser not supported: ${browserSupport.missing.join(', ')}`);
      }

      // Initialize face-api
      const modelsLoaded = await initializeFaceAPI();
      if (!modelsLoaded) {
        throw new Error('Failed to load face recognition models');
      }

      // Get camera stream
      const stream = await getCameraStream();
      if (!stream) {
        throw new Error('Failed to access camera');
      }

      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.onloadedmetadata = () => {
          if (videoRef.current) {
            videoRef.current.play().then(() => {
              setIsInitialized(true);
              // Auto-start analysis
              setTimeout(() => {
                startAnalysis();
              }, 1000);
            });
          }
        };
      }
    } catch (err) {
      console.error('Camera initialization failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to initialize camera');
    }
  };

  const startAnalysis = () => {
    if (!videoRef.current || !canvasRef.current) return;

    setIsAnalyzing(true);
    analysisIntervalRef.current = setInterval(async () => {
      if (videoRef.current && canvasRef.current) {
        try {
          const analysis = await analyzeFace(videoRef.current);
          setCurrentAnalysis(analysis);

          // Draw overlay if enabled
          if (showOverlay && analysis) {
            // Set canvas size to match video
            const video = videoRef.current;
            const canvas = canvasRef.current;
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            drawFaceAnalysisOverlay(canvas, analysis, {
              showLandmarks,
              showBoundingBox: true,
              showAnalysisText: true,
            });
          } else if (!showOverlay) {
            // Clear canvas if overlay is disabled
            const ctx = canvasRef.current.getContext('2d');
            if (ctx) {
              ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
            }
          }
        } catch (err) {
          console.error('Face analysis error:', err);
        }
      }
    }, 500); // Analyze every 500ms for smooth updates
  };

  const stopAnalysis = () => {
    if (analysisIntervalRef.current) {
      clearInterval(analysisIntervalRef.current);
      analysisIntervalRef.current = null;
    }
    setIsAnalyzing(false);
    setCurrentAnalysis(null);
    
    // Clear canvas
    if (canvasRef.current) {
      const ctx = canvasRef.current.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
      }
    }
  };

  const cleanup = () => {
    stopAnalysis();
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
  };

  const formatExpression = (expression: string) => {
    return expression.charAt(0).toUpperCase() + expression.slice(1);
  };

  const getExpressionEmoji = (expression: string) => {
    const emojiMap: { [key: string]: string } = {
      happy: '😊',
      sad: '😢',
      angry: '😠',
      fearful: '😨',
      disgusted: '🤢',
      surprised: '😲',
      neutral: '😐'
    };
    return emojiMap[expression] || '😐';
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Live Face Analysis
        </h3>
        {onClose && (
          <Button size="sm" color="gray" onClick={onClose}>
            Close
          </Button>
        )}
      </div>

      {error && (
        <Alert color="failure" className="mb-4">
          <span className="font-medium">Error:</span> {error}
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Video Feed */}
        <div className="lg:col-span-2">
          <div className="relative bg-gray-900 rounded-lg overflow-hidden">
            {!isInitialized && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
                <div className="text-center text-white">
                  <Spinner size="lg" className="mb-4" />
                  <p>Initializing camera...</p>
                </div>
              </div>
            )}
            
            <video
              ref={videoRef}
              className="w-full h-auto"
              autoPlay
              muted
              playsInline
              style={{ maxHeight: '400px' }}
            />
            
            <canvas
              ref={canvasRef}
              className="absolute top-0 left-0 w-full h-full pointer-events-none"
              style={{ maxHeight: '400px' }}
            />
          </div>

          {/* Controls */}
          {showControls && isInitialized && (
            <div className="flex items-center justify-center gap-4 mt-4">
              {!isAnalyzing ? (
                <Button onClick={startAnalysis} color="blue">
                  <HiOutlinePlay className="mr-2 h-4 w-4" />
                  Start Analysis
                </Button>
              ) : (
                <Button onClick={stopAnalysis} color="red">
                  <HiOutlineStop className="mr-2 h-4 w-4" />
                  Stop Analysis
                </Button>
              )}
              
              <Button
                onClick={() => setShowOverlay(!showOverlay)}
                color={showOverlay ? 'green' : 'gray'}
                size="sm"
              >
                {showOverlay ? <HiOutlineEye className="mr-2 h-4 w-4" /> : <HiOutlineEyeOff className="mr-2 h-4 w-4" />}
                Overlay
              </Button>
              
              <Button
                onClick={() => setShowLandmarks(!showLandmarks)}
                color={showLandmarks ? 'green' : 'gray'}
                size="sm"
              >
                Landmarks
              </Button>
            </div>
          )}
        </div>

        {/* Analysis Results */}
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-900 dark:text-white">
            Analysis Results
          </h4>
          
          {!currentAnalysis ? (
            <div className="text-center text-gray-500 py-8">
              {isAnalyzing ? (
                <>
                  <Spinner className="mb-2" />
                  <p>Analyzing...</p>
                </>
              ) : (
                <p>No face detected</p>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {/* Gender */}
              {currentAnalysis.gender && (
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Gender:</span>
                    <Badge color="blue">
                      {Math.round((currentAnalysis.genderProbability || 0) * 100)}% {currentAnalysis.gender.toUpperCase()}
                    </Badge>
                  </div>
                </div>
              )}

              {/* Expression */}
              {currentAnalysis.dominantExpression && (
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Expression:</span>
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{getExpressionEmoji(currentAnalysis.dominantExpression)}</span>
                      <Badge color="green">
                        {Math.round((currentAnalysis.expressionProbability || 0) * 100)}% {formatExpression(currentAnalysis.dominantExpression)}
                      </Badge>
                    </div>
                  </div>
                </div>
              )}

              {/* Age */}
              {currentAnalysis.age && (
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Age:</span>
                    <Badge color="purple">
                      {currentAnalysis.age} years
                    </Badge>
                  </div>
                </div>
              )}

              {/* Face Orientation */}
              {currentAnalysis.faceOrientation && (
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="space-y-2">
                    <span className="font-medium">Face Orientation:</span>
                    <div className="grid grid-cols-3 gap-2 text-sm">
                      <div className="text-center">
                        <div className="font-medium">Roll</div>
                        <Badge color="gray">{currentAnalysis.faceOrientation.roll}°</Badge>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">Pitch</div>
                        <Badge color="gray">{currentAnalysis.faceOrientation.pitch}°</Badge>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">Yaw</div>
                        <Badge color="gray">{currentAnalysis.faceOrientation.yaw}°</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Detection Confidence */}
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Detection:</span>
                  <Badge color="indigo">
                    {Math.round(currentAnalysis.detection.score * 100)}% confidence
                  </Badge>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default LiveFaceAnalysis;
