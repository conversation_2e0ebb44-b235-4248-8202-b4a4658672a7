import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge } from 'flowbite-react';
import {
  HiOutlineCamera,
  HiOutlineCheck,
  HiOutlineX,
  HiOutlineExclamation,
} from 'react-icons/hi';
import {
  initializeFaceAPI,
  getCameraStream,
  checkBrowserSupport,
  analyzeFace,
  drawFaceAnalysisOverlay,
  FaceAnalysis,
} from '../../utils/faceRecognition';
import * as faceapi from '@vladmandic/face-api';

const FaceRecognitionDiagnostic: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [diagnostics, setDiagnostics] = useState({
    browserSupport: { supported: false, missing: [] as string[] },
    modelsLoaded: false,
    cameraAccess: false,
    faceDetection: false,
  });
  const [isRunning, setIsRunning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [detectedFaces, setDetectedFaces] = useState(0);
  const [showDebugCanvas, setShowDebugCanvas] = useState(false);
  const [continuousDetection, setContinuousDetection] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<FaceAnalysis | null>(null);
  const [showLiveAnalysis, setShowLiveAnalysis] = useState(true);
  const detectionIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const runDiagnostics = async () => {
    setIsRunning(true);
    setError(null);
    
    try {
      // 1. Check browser support
      console.log('🔍 Checking browser support...');
      const browserSupport = checkBrowserSupport();
      setDiagnostics(prev => ({ ...prev, browserSupport }));
      
      if (!browserSupport.supported) {
        throw new Error(`Browser not supported: ${browserSupport.missing.join(', ')}`);
      }
      console.log('✅ Browser support OK');

      // 2. Load face-api models
      console.log('🔍 Loading face-api models...');
      const modelsLoaded = await initializeFaceAPI();
      setDiagnostics(prev => ({ ...prev, modelsLoaded }));
      
      if (!modelsLoaded) {
        throw new Error('Failed to load face-api models. Check if models are in /public/models/');
      }
      console.log('✅ Face-api models loaded');

      // 3. Test camera access
      console.log('🔍 Testing camera access...');
      const stream = await getCameraStream();
      
      if (!stream) {
        throw new Error('Failed to access camera. Check permissions and ensure camera is not in use.');
      }
      
      setDiagnostics(prev => ({ ...prev, cameraAccess: true }));
      console.log('✅ Camera access OK');

      // 4. Setup video and test face detection
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.onloadedmetadata = async () => {
          if (videoRef.current) {
            await videoRef.current.play();
            
            // Test face detection after a short delay
            setTimeout(async () => {
              await testFaceDetection();
            }, 2000);
          }
        };
      }

    } catch (err: any) {
      console.error('❌ Diagnostic failed:', err);
      setError(err.message);
    } finally {
      setIsRunning(false);
    }
  };

  const testFaceDetection = async () => {
    if (!videoRef.current) return;

    try {
      console.log('🔍 Testing face detection...');

      // Test with different detection options
      console.log('Testing with TinyFaceDetector (default)...');
      let detections = await faceapi
        .detectAllFaces(videoRef.current, new faceapi.TinyFaceDetectorOptions())
        .withFaceLandmarks();

      console.log(`TinyFaceDetector found ${detections.length} faces`);

      if (detections.length === 0) {
        console.log('Testing with SSD MobileNet (more sensitive)...');
        detections = await faceapi
          .detectAllFaces(videoRef.current, new faceapi.SsdMobilenetv1Options())
          .withFaceLandmarks();

        console.log(`SSD MobileNet found ${detections.length} faces`);
      }

      if (detections.length === 0) {
        console.log('Testing with MTCNN (most sensitive)...');
        detections = await faceapi
          .detectAllFaces(videoRef.current, new faceapi.MtcnnOptions())
          .withFaceLandmarks();

        console.log(`MTCNN found ${detections.length} faces`);
      }

      setDetectedFaces(detections.length);

      if (detections.length > 0) {
        setDiagnostics(prev => ({ ...prev, faceDetection: true }));
        console.log(`✅ Face detection OK - detected ${detections.length} face(s)`);

        // Log detection details
        detections.forEach((detection, index) => {
          console.log(`Face ${index + 1}:`, {
            confidence: detection.detection.score,
            box: detection.detection.box,
            landmarks: detection.landmarks.positions.length
          });
        });
      } else {
        console.log('⚠️ No faces detected with any detector - trying different approaches...');

        // Try with lower confidence threshold
        console.log('Testing with lower confidence threshold...');
        const lowConfidenceDetections = await faceapi
          .detectAllFaces(videoRef.current, new faceapi.TinyFaceDetectorOptions({ scoreThreshold: 0.3 }));

        console.log(`Low confidence detection found ${lowConfidenceDetections.length} faces`);

        if (lowConfidenceDetections.length > 0) {
          setDetectedFaces(lowConfidenceDetections.length);
          setDiagnostics(prev => ({ ...prev, faceDetection: true }));
          console.log('✅ Face detection OK with lower threshold');
        }
      }

      // Draw debug visualization if enabled
      if (showDebugCanvas && canvasRef.current && videoRef.current) {
        drawDebugVisualization(detections);
      }
    } catch (err: any) {
      console.error('❌ Face detection failed:', err);
      setError(`Face detection failed: ${err.message}`);
    }
  };

  const drawDebugVisualization = (detections: any[]) => {
    if (!canvasRef.current || !videoRef.current) return;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw detection boxes
    detections.forEach((detection, index) => {
      const box = detection.detection.box;

      // Draw bounding box
      ctx.strokeStyle = '#00ff00';
      ctx.lineWidth = 2;
      ctx.strokeRect(box.x, box.y, box.width, box.height);

      // Draw confidence score
      ctx.fillStyle = '#00ff00';
      ctx.font = '16px Arial';
      ctx.fillText(
        `Face ${index + 1}: ${(detection.detection.score * 100).toFixed(1)}%`,
        box.x,
        box.y - 5
      );

      // Draw landmarks if available
      if (detection.landmarks) {
        ctx.fillStyle = '#ff0000';
        detection.landmarks.positions.forEach((point: any) => {
          ctx.beginPath();
          ctx.arc(point.x, point.y, 2, 0, 2 * Math.PI);
          ctx.fill();
        });
      }
    });
  };

  const startContinuousDetection = () => {
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
    }

    setContinuousDetection(true);
    detectionIntervalRef.current = setInterval(async () => {
      await testFaceDetection();

      // Perform live face analysis if enabled
      if (showLiveAnalysis && videoRef.current) {
        try {
          const analysis = await analyzeFace(videoRef.current);
          setCurrentAnalysis(analysis);

          // Draw analysis overlay if enabled and analysis available
          if (analysis && showDebugCanvas && canvasRef.current) {
            drawFaceAnalysisOverlay(canvasRef.current, analysis, {
              showLandmarks: true,
              showBoundingBox: true,
              showAnalysisText: true,
            });
          }
        } catch (analysisErr) {
          console.error('Face analysis error:', analysisErr);
          setCurrentAnalysis(null);
        }
      }
    }, 1000); // Run detection every second
  };

  const stopContinuousDetection = () => {
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      detectionIntervalRef.current = null;
    }
    setContinuousDetection(false);
    setCurrentAnalysis(null);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (detectionIntervalRef.current) {
        clearInterval(detectionIntervalRef.current);
      }
    };
  }, []);

  const getDiagnosticIcon = (status: boolean) => {
    return status ? (
      <HiOutlineCheck className="h-5 w-5 text-green-500" />
    ) : (
      <HiOutlineX className="h-5 w-5 text-red-500" />
    );
  };

  const getDiagnosticBadge = (status: boolean) => {
    return status ? (
      <Badge color="success">Pass</Badge>
    ) : (
      <Badge color="failure">Fail</Badge>
    );
  };

  return (
    <Card className="max-w-4xl mx-auto">
      <div className="p-6">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Face Recognition Diagnostics</h2>
          <p className="text-gray-600">Test your system's facial recognition capabilities</p>
        </div>

        {/* Diagnostic Results */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">Browser Support</h3>
              {getDiagnosticIcon(diagnostics.browserSupport.supported)}
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">
                {diagnostics.browserSupport.supported 
                  ? 'All required features available'
                  : `Missing: ${diagnostics.browserSupport.missing.join(', ')}`
                }
              </span>
              {getDiagnosticBadge(diagnostics.browserSupport.supported)}
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">Face-API Models</h3>
              {getDiagnosticIcon(diagnostics.modelsLoaded)}
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">
                {diagnostics.modelsLoaded ? 'Models loaded successfully' : 'Models not loaded'}
              </span>
              {getDiagnosticBadge(diagnostics.modelsLoaded)}
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">Camera Access</h3>
              {getDiagnosticIcon(diagnostics.cameraAccess)}
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">
                {diagnostics.cameraAccess ? 'Camera accessible' : 'Camera not accessible'}
              </span>
              {getDiagnosticBadge(diagnostics.cameraAccess)}
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">Face Detection</h3>
              {getDiagnosticIcon(diagnostics.faceDetection)}
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">
                {diagnostics.faceDetection 
                  ? `${detectedFaces} face(s) detected`
                  : 'No faces detected'
                }
              </span>
              {getDiagnosticBadge(diagnostics.faceDetection)}
            </div>
          </div>
        </div>

        {/* Camera Feed */}
        {diagnostics.cameraAccess && (
          <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-medium">Camera Feed</h3>
              <div className="flex items-center gap-4">
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={showDebugCanvas}
                    onChange={(e) => setShowDebugCanvas(e.target.checked)}
                    className="rounded"
                  />
                  Debug Overlay
                </label>
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={continuousDetection}
                    onChange={(e) => e.target.checked ? startContinuousDetection() : stopContinuousDetection()}
                    className="rounded"
                  />
                  Live Detection
                </label>
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={showLiveAnalysis}
                    onChange={(e) => setShowLiveAnalysis(e.target.checked)}
                    className="rounded"
                  />
                  Live Analysis
                </label>
              </div>
            </div>
            <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
              <video
                ref={videoRef}
                className="w-full h-full object-cover"
                autoPlay
                muted
                playsInline
              />
              {showDebugCanvas && (
                <canvas
                  ref={canvasRef}
                  className="absolute top-0 left-0 w-full h-full pointer-events-none"
                />
              )}
            </div>
            {detectedFaces > 0 && (
              <div className="mt-2 text-center text-sm text-green-600">
                ✅ {detectedFaces} face(s) detected
              </div>
            )}

            {/* Live Face Analysis Results */}
            {showLiveAnalysis && currentAnalysis && continuousDetection && (
              <div className="mt-4 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Live Face Analysis</h4>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  {/* Gender */}
                  {currentAnalysis.gender && (
                    <div className="text-center">
                      <div className="font-medium text-gray-600 dark:text-gray-300">Gender</div>
                      <Badge color="blue" size="sm">
                        {Math.round((currentAnalysis.genderProbability || 0) * 100)}% {currentAnalysis.gender.toUpperCase()}
                      </Badge>
                    </div>
                  )}

                  {/* Expression */}
                  {currentAnalysis.dominantExpression && (
                    <div className="text-center">
                      <div className="font-medium text-gray-600 dark:text-gray-300">Expression</div>
                      <Badge color="green" size="sm">
                        {Math.round((currentAnalysis.expressionProbability || 0) * 100)}% {currentAnalysis.dominantExpression.toUpperCase()}
                      </Badge>
                    </div>
                  )}

                  {/* Age */}
                  {currentAnalysis.age && (
                    <div className="text-center">
                      <div className="font-medium text-gray-600 dark:text-gray-300">Age</div>
                      <Badge color="purple" size="sm">
                        {currentAnalysis.age} years
                      </Badge>
                    </div>
                  )}

                  {/* Face Orientation */}
                  {currentAnalysis.faceOrientation && (
                    <div className="text-center">
                      <div className="font-medium text-gray-600 dark:text-gray-300">Orientation</div>
                      <div className="text-xs space-y-1">
                        <div>Roll: {currentAnalysis.faceOrientation.roll}°</div>
                        <div>Pitch: {currentAnalysis.faceOrientation.pitch}°</div>
                        <div>Yaw: {currentAnalysis.faceOrientation.yaw}°</div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Detection Confidence */}
                <div className="mt-3 text-center">
                  <Badge color="indigo" size="sm">
                    Detection: {Math.round(currentAnalysis.detection.score * 100)}% confidence
                  </Badge>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Error */}
        {error && (
          <Alert color="failure" className="mb-4">
            <HiOutlineExclamation className="h-4 w-4" />
            <span>{error}</span>
          </Alert>
        )}

        {/* Controls */}
        <div className="flex justify-center gap-4">
          <Button
            color="primary"
            onClick={runDiagnostics}
            disabled={isRunning}
          >
            <HiOutlineCamera className="mr-2 h-4 w-4" />
            {isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'}
          </Button>

          {diagnostics.faceDetection && (
            <Button
              color="light"
              onClick={testFaceDetection}
            >
              Test Face Detection Again
            </Button>
          )}
        </div>

        {/* Instructions */}
        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Troubleshooting Tips:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Ensure you're using HTTPS or localhost</li>
            <li>• Allow camera permissions when prompted</li>
            <li>• Close other apps using the camera</li>
            <li>• Ensure good lighting on your face</li>
            <li>• Sit 2-3 feet from the camera</li>
            <li>• Look directly at the camera</li>
            <li>• Try enabling "Live Detection" for continuous testing</li>
            <li>• Enable "Live Analysis" to see gender, age, expression, and orientation</li>
            <li>• Enable "Debug Overlay" to see detection boxes and landmarks</li>
            <li>• Check browser console for detailed logs</li>
          </ul>
        </div>

        {/* Debug Info */}
        {diagnostics.cameraAccess && (
          <div className="mt-4 bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Debug Information:</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <div>Video dimensions: {videoRef.current?.videoWidth || 'N/A'} x {videoRef.current?.videoHeight || 'N/A'}</div>
              <div>Video ready state: {videoRef.current?.readyState || 'N/A'}</div>
              <div>Continuous detection: {continuousDetection ? 'Active' : 'Inactive'}</div>
              <div>Last detection: {detectedFaces} face(s)</div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default FaceRecognitionDiagnostic;
