import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ge, Spinner, Progress } from 'flowbite-react';
import {
  HiOutlineCamera,
  HiOutlineCheck,
  HiOutlineRefresh,
  HiOutlineExclamation,
  HiOutlinePlay,
} from 'react-icons/hi';
import {
  initializeFaceAPI,
  getCameraStream,
  captureFaceFromVideo,
  validateEnrollmentQuality,
  checkBrowserSupport,
  analyzeFace,
  drawFaceAnalysisOverlay,
  FaceAnalysis,
} from '../../utils/faceRecognition';
import { enrollEmployeeFace } from '../../services/faceRecognition';
import { useOrganization } from '../../context/OrganizationContext';

interface FaceEnrollmentProps {
  employeeId: string;
  employeeName: string;
  onEnrollmentComplete: (success: boolean) => void;
  onCancel: () => void;
}

interface EnrollmentStep {
  id: number;
  name: string;
  instruction: string;
  completed: boolean;
  quality: number;
  descriptor?: Float32Array;
}

const FaceEnrollment: React.FC<FaceEnrollmentProps> = ({
  employeeId,
  employeeName,
  onEnrollmentComplete,
  onCancel,
}) => {
  const { currentOrganization } = useOrganization();
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const debugCanvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const detectionIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const videoRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // State
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [isCapturing, setIsCapturing] = useState(false);
  const [isEnrolling, setIsEnrolling] = useState(false);
  const [showDebugOverlay, setShowDebugOverlay] = useState(false);
  const [liveDetection, setLiveDetection] = useState(false);
  const [currentDetection, setCurrentDetection] = useState<{
    detected: boolean;
    quality: number;
    method?: string;
  }>({ detected: false, quality: 0 });
  const [currentAnalysis, setCurrentAnalysis] = useState<FaceAnalysis | null>(null);
  const [showLiveAnalysis, setShowLiveAnalysis] = useState(true);
  const [enrollmentSteps, setEnrollmentSteps] = useState<EnrollmentStep[]>([
    {
      id: 1,
      name: 'Front View',
      instruction: 'Look directly at the camera',
      completed: false,
      quality: 0,
    },
    {
      id: 2,
      name: 'Slight Left',
      instruction: 'Turn your head slightly to the left',
      completed: false,
      quality: 0,
    },
    {
      id: 3,
      name: 'Slight Right',
      instruction: 'Turn your head slightly to the right',
      completed: false,
      quality: 0,
    },
  ]);

  // Check browser support on mount
  useEffect(() => {
    const support = checkBrowserSupport();
    if (!support.supported) {
      setError(`Browser not supported. Missing: ${support.missing.join(', ')}`);
      return;
    }
    // Don't auto-initialize - wait for user to start
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      if (detectionIntervalRef.current) {
        clearInterval(detectionIntervalRef.current);
      }
      if (videoRefreshIntervalRef.current) {
        clearInterval(videoRefreshIntervalRef.current);
      }
    };
  }, []);

  const startEnrollment = async () => {
    setIsInitializing(true);
    setHasStarted(true);
    setError(null);

    try {
      console.log('🚀 Starting face enrollment...');

      // Initialize face-api.js
      console.log('📦 Loading face recognition models...');
      const faceApiReady = await initializeFaceAPI();
      if (!faceApiReady) {
        throw new Error('Failed to load face recognition models');
      }
      console.log('✅ Face recognition models loaded');

      // Get camera stream
      console.log('📷 Accessing camera...');
      const stream = await getCameraStream();
      if (!stream) {
        throw new Error('Failed to access camera');
      }
      console.log('✅ Camera access granted');

      streamRef.current = stream;

      // Setup video element with enhanced loading
      if (videoRef.current) {
        console.log('🎥 Setting up video element...');
        const video = videoRef.current;

        // Clear any existing stream and handlers
        if (video.srcObject) {
          console.log('🧹 Clearing existing video stream...');
          video.srcObject = null;
        }
        video.onloadedmetadata = null;
        video.oncanplay = null;
        video.onloadeddata = null;
        video.onplaying = null;

        // Set video properties before assigning stream
        video.muted = true;
        video.playsInline = true;
        video.autoplay = true;
        video.controls = false;

        // Force video element refresh
        video.style.display = 'block';
        video.style.backgroundColor = '#000';

        console.log('🔗 Assigning stream to video element...');
        video.srcObject = stream;

        // Force immediate load
        video.load();

        // Enhanced event handling with forced video refresh
        const handleVideoReady = () => {
          console.log('📹 Video ready event fired');
          console.log('📹 Video dimensions:', video.videoWidth, 'x', video.videoHeight);
          console.log('📹 Video ready state:', video.readyState);
          console.log('📹 Video current time:', video.currentTime);
          console.log('📹 Video paused:', video.paused);

          // Force video refresh if dimensions are 0 but stream exists
          if ((video.videoWidth === 0 || video.videoHeight === 0) && streamRef.current) {
            console.log('🔄 Forcing video refresh due to 0 dimensions...');
            const currentStream = streamRef.current;
            video.srcObject = null;
            setTimeout(() => {
              video.srcObject = currentStream;
              video.load();
              setTimeout(() => {
                video.play().catch(console.error);
              }, 200);
            }, 100);
            return;
          }

          if (video.videoWidth > 0 && video.videoHeight > 0) {
            console.log('✅ Video has valid dimensions, attempting to play...');
            video.play().then(() => {
              console.log('✅ Video playing successfully');
              setIsInitialized(true);
              // Auto-enable live detection for immediate feedback
              setTimeout(() => {
                setLiveDetection(true);
                startLiveDetection();
              }, 1000);

              // Start video monitoring for black screen issues
              startVideoMonitoring();
            }).catch((err) => {
              console.error('❌ Video play failed:', err);
              setIsInitialized(true); // Still initialize for manual play
            });
          } else {
            console.warn('⚠️ Video ready but dimensions are 0, will retry...');
            // Set initialized anyway so user can see the interface
            setIsInitialized(true);
          }
        };

        // Multiple event listeners
        video.addEventListener('loadedmetadata', () => {
          console.log('📹 loadedmetadata event');
          handleVideoReady();
        });

        video.addEventListener('canplay', () => {
          console.log('📹 canplay event');
          handleVideoReady();
        });

        video.addEventListener('loadeddata', () => {
          console.log('📹 loadeddata event');
          handleVideoReady();
        });

        video.addEventListener('playing', () => {
          console.log('📹 playing event - video is now playing');
          setIsInitialized(true);
        });

        // Force load
        video.load();

        // Immediate play attempt
        setTimeout(() => {
          video.play().then(() => {
            console.log('✅ Initial play successful');
            setIsInitialized(true);
            setTimeout(() => {
              setLiveDetection(true);
              startLiveDetection();
            }, 1000);
          }).catch((err) => {
            console.warn('❌ Initial play failed:', err);
            setIsInitialized(true); // Still show interface
          });
        }, 500);

        // Aggressive video refresh attempts
        const refreshAttempts = [2000, 4000, 6000];
        refreshAttempts.forEach((delay, index) => {
          setTimeout(() => {
            if (video.videoWidth === 0 && streamRef.current) {
              console.log(`🔄 Refresh attempt ${index + 1} - forcing video reload`);
              const currentStream = streamRef.current;
              video.srcObject = null;
              setTimeout(() => {
                video.srcObject = currentStream;
                video.load();
                setTimeout(() => {
                  video.play().then(() => {
                    console.log(`✅ Refresh attempt ${index + 1} successful`);
                    if (!isInitialized) {
                      setIsInitialized(true);
                      setTimeout(() => {
                        setLiveDetection(true);
                        startLiveDetection();
                      }, 1000);
                    }
                  }).catch(console.error);
                }, 200);
              }, 100);
            }
          }, delay);
        });

        // Final fallback
        setTimeout(() => {
          console.log('⏰ Final timeout check');
          console.log('📹 Final video state:', {
            readyState: video.readyState,
            videoWidth: video.videoWidth,
            videoHeight: video.videoHeight,
            paused: video.paused,
            srcObject: !!video.srcObject,
            currentTime: video.currentTime,
            networkState: video.networkState
          });

          if (!isInitialized) {
            console.log('⏰ Forcing initialization after timeout');
            setIsInitialized(true);
          }
        }, 8000);
      }
    } catch (err: any) {
      console.error('❌ Initialization failed:', err);
      setError(err.message);
    } finally {
      setIsInitializing(false);
    }
  };

  const captureCurrentStep = async () => {
    if (!videoRef.current || !canvasRef.current || !currentOrganization) {
      return;
    }

    // Check if video has valid dimensions
    if (videoRef.current.videoWidth === 0 || videoRef.current.videoHeight === 0) {
      setError('Video not ready - please wait for camera to load properly');
      return;
    }

    setIsCapturing(true);
    setError(null);

    try {
      const captureResult = await captureFaceFromVideo(videoRef.current, canvasRef.current);

      if (!captureResult.descriptor) {
        setError(`No face detected. ${captureResult.detectionMethod ? `Tried: ${captureResult.detectionMethod}` : 'Please ensure you are facing the camera clearly.'}`);
        return;
      }

      const qualityValidation = validateEnrollmentQuality(captureResult.quality);
      if (!qualityValidation.isValid) {
        setError(`${qualityValidation.message} (Quality: ${(captureResult.quality * 100).toFixed(1)}%)`);
        return;
      }

      console.log(`✅ Enrollment step ${currentStep + 1} captured successfully:`, {
        quality: captureResult.quality,
        method: captureResult.detectionMethod,
        level: qualityValidation.level
      });

      // Update current step
      const updatedSteps = [...enrollmentSteps];
      updatedSteps[currentStep] = {
        ...updatedSteps[currentStep],
        completed: true,
        quality: captureResult.quality,
        descriptor: captureResult.descriptor,
      };
      setEnrollmentSteps(updatedSteps);

      // Move to next step or complete enrollment
      if (currentStep < enrollmentSteps.length - 1) {
        setCurrentStep(currentStep + 1);
      } else {
        await completeEnrollment(updatedSteps);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsCapturing(false);
    }
  };

  const completeEnrollment = async (steps: EnrollmentStep[]) => {
    if (!currentOrganization) return;

    setIsEnrolling(true);
    setError(null);

    try {
      // Use the best quality descriptor
      const bestStep = steps.reduce((best, current) => 
        current.quality > best.quality ? current : best
      );

      if (!bestStep.descriptor) {
        throw new Error('No valid face descriptor found');
      }

      const result = await enrollEmployeeFace(
        currentOrganization.id,
        employeeId,
        bestStep.descriptor,
        bestStep.quality
      );

      if (!result.success) {
        throw new Error(result.error || 'Enrollment failed');
      }

      onEnrollmentComplete(true);
    } catch (err: any) {
      setError(err.message);
      onEnrollmentComplete(false);
    } finally {
      setIsEnrolling(false);
    }
  };

  const retryCurrentStep = () => {
    const updatedSteps = [...enrollmentSteps];
    updatedSteps[currentStep] = {
      ...updatedSteps[currentStep],
      completed: false,
      quality: 0,
      descriptor: undefined,
    };
    setEnrollmentSteps(updatedSteps);
    setError(null);
  };

  const resetEnrollment = () => {
    setCurrentStep(0);
    setEnrollmentSteps(steps => 
      steps.map(step => ({
        ...step,
        completed: false,
        quality: 0,
        descriptor: undefined,
      }))
    );
    setError(null);
  };

  const getOverallProgress = () => {
    const completedSteps = enrollmentSteps.filter(step => step.completed).length;
    return (completedSteps / enrollmentSteps.length) * 100;
  };

  const isAllStepsCompleted = enrollmentSteps.every(step => step.completed);

  // Live detection functions
  const startLiveDetection = () => {
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
    }

    setLiveDetection(true);
    detectionIntervalRef.current = setInterval(async () => {
      if (videoRef.current && canvasRef.current) {
        // Check if video has valid dimensions before attempting detection
        if (videoRef.current.videoWidth === 0 || videoRef.current.videoHeight === 0) {
          console.warn('⚠️ Skipping live detection - video dimensions are 0');
          setCurrentDetection({ detected: false, quality: 0 });
          return;
        }

        try {
          const captureResult = await captureFaceFromVideo(videoRef.current, canvasRef.current);
          setCurrentDetection({
            detected: !!captureResult.descriptor,
            quality: captureResult.quality,
            method: captureResult.detectionMethod,
          });

          // Perform live face analysis and draw overlay
          if (showLiveAnalysis && debugCanvasRef.current) {
            try {
              const analysis = await analyzeFace(videoRef.current);
              setCurrentAnalysis(analysis);

              // Draw analysis overlay directly on video
              if (analysis) {
                // Set canvas size to match video
                const video = videoRef.current;
                const canvas = debugCanvasRef.current;
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;

                drawFaceAnalysisOverlay(canvas, analysis, {
                  showLandmarks: true,
                  showBoundingBox: true,
                  showAnalysisText: true,
                });
              }
            } catch (analysisErr) {
              console.error('Face analysis error:', analysisErr);
              setCurrentAnalysis(null);
            }
          }
          // Draw basic debug overlay if live analysis is disabled but debug is enabled
          else if (showDebugOverlay && debugCanvasRef.current) {
            drawDebugOverlay(captureResult);
          }
        } catch (err) {
          console.error('Live detection error:', err);
          setCurrentDetection({ detected: false, quality: 0 });
          setCurrentAnalysis(null);
        }
      }
    }, 1000);
  };

  const stopLiveDetection = () => {
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      detectionIntervalRef.current = null;
    }
    setLiveDetection(false);
    setCurrentDetection({ detected: false, quality: 0 });
  };

  // Video monitoring to automatically fix black screen issues
  const startVideoMonitoring = () => {
    if (videoRefreshIntervalRef.current) {
      clearInterval(videoRefreshIntervalRef.current);
    }

    videoRefreshIntervalRef.current = setInterval(() => {
      const video = videoRef.current;
      if (video && streamRef.current) {
        // Check if video is black (dimensions are 0 but stream exists)
        if (video.videoWidth === 0 || video.videoHeight === 0) {
          console.log('🔍 Video monitoring: Detected black screen, attempting auto-refresh');

          // Auto-refresh the video
          const currentStream = streamRef.current;
          video.srcObject = null;
          setTimeout(() => {
            video.srcObject = currentStream;
            video.load();
            setTimeout(() => {
              video.play().catch(console.error);
            }, 200);
          }, 100);
        }
      }
    }, 3000); // Check every 3 seconds
  };

  const stopVideoMonitoring = () => {
    if (videoRefreshIntervalRef.current) {
      clearInterval(videoRefreshIntervalRef.current);
      videoRefreshIntervalRef.current = null;
    }
  };

  const drawDebugOverlay = (captureResult: any) => {
    if (!debugCanvasRef.current || !videoRef.current) return;

    const canvas = debugCanvasRef.current;
    const video = videoRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (captureResult.descriptor) {
      // Draw a simple indicator that face is detected
      ctx.strokeStyle = '#00ff00';
      ctx.lineWidth = 3;
      ctx.strokeRect(50, 50, canvas.width - 100, canvas.height - 100);

      // Draw quality info
      ctx.fillStyle = '#00ff00';
      ctx.font = '20px Arial';
      ctx.fillText(
        `Face Detected: ${(captureResult.quality * 100).toFixed(1)}%`,
        60,
        40
      );

      if (captureResult.detectionMethod) {
        ctx.fillText(
          `Method: ${captureResult.detectionMethod}`,
          60,
          canvas.height - 20
        );
      }
    } else {
      // Draw red indicator for no face
      ctx.strokeStyle = '#ff0000';
      ctx.lineWidth = 3;
      ctx.strokeRect(50, 50, canvas.width - 100, canvas.height - 100);

      ctx.fillStyle = '#ff0000';
      ctx.font = '20px Arial';
      ctx.fillText('No Face Detected', 60, 40);
    }
  };

  // Show start screen if not started yet
  if (!hasStarted) {
    return (
      <Card className="max-w-2xl mx-auto">
        <div className="text-center py-12">
          <div className="mb-6">
            <HiOutlineCamera className="mx-auto h-16 w-16 text-blue-500 mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Face Enrollment</h2>
            <p className="text-gray-600">Set up facial recognition for {employeeName}</p>
          </div>

          <div className="mb-8">
            <div className="bg-blue-50 rounded-lg p-6 mb-6">
              <h3 className="font-medium text-blue-900 mb-3">What to expect:</h3>
              <div className="text-sm text-blue-800 space-y-2 text-left">
                <div className="flex items-center gap-2">
                  <span className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium">1</span>
                  <span>Camera will activate and show live detection</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium">2</span>
                  <span>Capture 3 photos: front view, slight left, slight right</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-medium">3</span>
                  <span>System will save your facial recognition profile</span>
                </div>
              </div>
            </div>
          </div>

          {error && (
            <Alert color="failure" className="mb-6">
              <HiOutlineExclamation className="h-4 w-4" />
              <span>{error}</span>
            </Alert>
          )}

          <div className="flex justify-center gap-4">
            <Button color="gray" onClick={onCancel}>
              Cancel
            </Button>
            <Button
              color="primary"
              size="lg"
              onClick={startEnrollment}
              className="px-8"
            >
              <HiOutlineCamera className="mr-2 h-5 w-5" />
              Start Enrollment
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  // Show loading screen during initialization
  if (isInitializing) {
    return (
      <Card className="max-w-2xl mx-auto">
        <div className="text-center py-12">
          <Spinner size="xl" />
          <h3 className="mt-4 text-lg font-medium">Setting up camera...</h3>
          <p className="text-gray-500">Loading face recognition models and accessing camera</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <div className="p-6">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Face Enrollment</h2>
          <p className="text-gray-600">Setting up facial recognition for {employeeName}</p>
        </div>

        {/* Progress */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>{Math.round(getOverallProgress())}% Complete</span>
          </div>
          <Progress progress={getOverallProgress()} color="blue" />
        </div>

        {/* Steps */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          {enrollmentSteps.map((step, index) => (
            <div
              key={step.id}
              className={`p-4 rounded-lg border-2 ${
                index === currentStep
                  ? 'border-blue-500 bg-blue-50'
                  : step.completed
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">{step.name}</h4>
                {step.completed ? (
                  <HiOutlineCheck className="h-5 w-5 text-green-500" />
                ) : index === currentStep ? (
                  <HiOutlineCamera className="h-5 w-5 text-blue-500" />
                ) : (
                  <div className="h-5 w-5 rounded-full border-2 border-gray-300" />
                )}
              </div>
              <p className="text-sm text-gray-600">{step.instruction}</p>
              {step.completed && (
                <Badge color="success" size="sm" className="mt-2">
                  Quality: {Math.round(step.quality * 100)}%
                </Badge>
              )}
            </div>
          ))}
        </div>

        {/* Camera Feed */}
        {(isInitialized || streamRef.current) && (
          <div className="mb-6">
            {/* Camera Header */}
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-medium">Camera Feed</h3>
              <div className="flex items-center gap-4">
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={showDebugOverlay}
                    onChange={(e) => setShowDebugOverlay(e.target.checked)}
                    className="rounded"
                  />
                  Debug Overlay
                </label>
                <span className="text-sm text-green-600">
                  ✅ Live Detection Active
                </span>
              </div>
            </div>

            <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
              <video
                ref={videoRef}
                className="w-full h-full object-cover"
                autoPlay
                muted
                playsInline
                controls={false}
                style={{ backgroundColor: '#000' }}
              />
              <canvas ref={canvasRef} className="hidden" />
              {(showDebugOverlay || showLiveAnalysis) && (
                <canvas
                  ref={debugCanvasRef}
                  className="absolute top-0 left-0 w-full h-full pointer-events-none"
                />
              )}

              {/* Always show activation button if stream exists but video not playing properly */}
              {streamRef.current && (
                !videoRef.current?.currentTime ||
                videoRef.current?.videoWidth === 0 ||
                videoRef.current?.paused
              ) && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
                  <div className="text-center">
                    <Button
                      color="primary"
                      size="lg"
                      onClick={() => {
                        console.log('🎬 Force video display');
                        const video = videoRef.current;
                        if (video && streamRef.current) {
                          // Complete reset
                          video.pause();
                          video.srcObject = null;
                          video.removeAttribute('src');
                          video.load();

                          // Force new assignment
                          setTimeout(() => {
                            video.srcObject = streamRef.current;
                            video.muted = true;
                            video.playsInline = true;
                            video.autoplay = true;
                            video.load();

                            setTimeout(() => {
                              video.play().then(() => {
                                console.log('✅ Video now playing');
                                setLiveDetection(true);
                                startLiveDetection();
                              }).catch(err => {
                                console.error('❌ Play failed:', err);
                                // Try one more time
                                setTimeout(() => video.play(), 1000);
                              });
                            }, 500);
                          }, 200);
                        }
                      }}
                      className="mb-2"
                    >
                      <HiOutlinePlay className="mr-2 h-6 w-6" />
                      Activate Camera Feed
                    </Button>
                    <p className="text-white text-sm">Camera ready - click to display video</p>
                  </div>
                </div>
              )}
            </div>

            {/* Live Detection Status */}
            {liveDetection && (
              <div className="mt-2 text-center text-sm">
                {currentDetection.detected ? (
                  <span className="text-green-600">
                    ✅ Face detected - Quality: {(currentDetection.quality * 100).toFixed(1)}%
                    {currentDetection.method && ` (${currentDetection.method})`}
                  </span>
                ) : (
                  <span className="text-red-600">❌ No face detected</span>
                )}
              </div>
            )}



            {/* Current instruction */}
            {!isAllStepsCompleted && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white px-4 py-2 rounded-lg">
                {enrollmentSteps[currentStep]?.instruction}
              </div>
            )}
          </div>
        )}

        {/* Error */}
        {error && (
          <Alert color="failure" className="mb-4">
            <HiOutlineExclamation className="h-4 w-4" />
            <span>{error}</span>
          </Alert>
        )}

        {/* Live Analysis Controls */}
        {isInitialized && (
          <div className="flex justify-center items-center gap-6 mb-4 bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
            <label className="flex items-center gap-2 text-sm font-medium">
              <input
                type="checkbox"
                checked={showLiveAnalysis}
                onChange={(e) => setShowLiveAnalysis(e.target.checked)}
                className="rounded text-blue-600"
              />
              <span className="text-blue-700 dark:text-blue-300">🤖 Live Analysis Overlay</span>
            </label>
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={liveDetection}
                onChange={(e) => e.target.checked ? startLiveDetection() : stopLiveDetection()}
                className="rounded text-green-600"
              />
              <span className="text-green-700 dark:text-green-300">🔄 Live Detection</span>
            </label>
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={showDebugOverlay}
                onChange={(e) => setShowDebugOverlay(e.target.checked)}
                className="rounded text-gray-600"
              />
              <span className="text-gray-700 dark:text-gray-300">🔧 Debug Mode</span>
            </label>
          </div>
        )}

        {/* Controls */}
        <div className="flex justify-between items-center">
          <Button color="gray" onClick={onCancel}>
            Cancel
          </Button>

          <div className="flex gap-2">

            {!isAllStepsCompleted && (
              <>
                <Button
                  color="light"
                  onClick={retryCurrentStep}
                  disabled={!enrollmentSteps[currentStep]?.completed}
                >
                  <HiOutlineRefresh className="mr-2 h-4 w-4" />
                  Retry
                </Button>
                <Button
                  color="primary"
                  onClick={captureCurrentStep}
                  disabled={isCapturing || (!isInitialized && !streamRef.current)}
                >
                  {isCapturing ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Capturing...
                    </>
                  ) : (
                    <>
                      <HiOutlineCamera className="mr-2 h-4 w-4" />
                      Capture
                    </>
                  )}
                </Button>
              </>
            )}

            {isAllStepsCompleted && (
              <>
                <Button color="light" onClick={resetEnrollment}>
                  Start Over
                </Button>
                <Button
                  color="success"
                  onClick={() => completeEnrollment(enrollmentSteps)}
                  disabled={isEnrolling}
                >
                  {isEnrolling ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Enrolling...
                    </>
                  ) : (
                    <>
                      <HiOutlineCheck className="mr-2 h-4 w-4" />
                      Complete Enrollment
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Tips */}
        <div className="mt-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Enrollment Tips:</h4>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Ensure good lighting on your face</li>
            <li>• Look directly at the camera for each step</li>
            <li>• Keep your face centered in the camera view</li>
            <li>• Remove glasses or hats if possible</li>
            <li>• Enable "Live Analysis Overlay" to see real-time gender, age, expression, and orientation directly on video</li>
            <li>• Enable "Live Detection" to see continuous face detection feedback</li>
            <li>• The overlay shows analysis data just like in professional face recognition systems</li>
            <li>• Multiple detection methods will be tried automatically</li>
            <li>• The live analysis helps ensure optimal face positioning</li>
          </ul>
        </div>
      </div>
    </Card>
  );
};

export default FaceEnrollment;
