import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge, Spinner } from 'flowbite-react';
import {
  HiOut<PERSON>Key,
  HiOutlineExclamation,
  HiOutlineCheck,
  HiOutlineRefresh,
  HiOutlineUserCircle,
} from 'react-icons/hi';
import {
  initializeFaceAPI,
  captureFaceFromVideo,
  findBestMatch,
  checkBrowserSupport,
  analyzeFace,
  drawFaceAnalysisOverlay,
  FaceAnalysis,
} from '../../utils/faceRecognition';
import {
  getOrganizationFaceDescriptors,
  logRecognitionAttempt,
} from '../../services/faceRecognition';
import { getEmployeeById } from '../../services/employee';
import {
  createFaceTimeEntry,
} from '../../services/faceTimeEntry';
import { createTimeEntry } from '../../services/faceRecognition';
import { supabase } from '../../lib/supabase';
import { useOrganization } from '../../context/OrganizationContext';
import PinFallback from './PinFallback';

interface TimeClockProps {
  onTimeEntryCreated?: (employeeId: string, type: 'clock_in' | 'clock_out') => void;
  onCancel?: () => void;
}

interface RecognitionResult {
  employeeId: string;
  confidence: number;
  employeeName?: string;
  timestamp?: Date;
}

const TimeClock: React.FC<TimeClockProps> = ({ onTimeEntryCreated, onCancel }) => {
  console.log('🚀 TimeClock component rendering...');
  console.log('📍 Component props:', { onTimeEntryCreated });
  
  const { currentOrganization } = useOrganization();
  console.log('🏢 Current organization:', currentOrganization);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const debugCanvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const recognitionIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const videoRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastRecognitionAttempt = useRef<number>(0);

  // State
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [recognitionResult, setRecognitionResult] = useState<RecognitionResult | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showPinFallback, setShowPinFallback] = useState(false);
  const [isProcessingTimeEntry, setIsProcessingTimeEntry] = useState(false);
  const [lastTimeEntry, setLastTimeEntry] = useState<{
    type: 'clock_in' | 'clock_out';
    time: string;
    employeeName?: string;
    employeeId?: string;
    timestamp?: Date;
  } | null>(null);
  const [userCooldowns, setUserCooldowns] = useState<Record<string, Date>>({});

  // Helper function to check if a user is in cooldown
  const isUserInCooldown = (employeeId: string): boolean => {
    const cooldownEnd = userCooldowns[employeeId];
    return cooldownEnd ? new Date() < cooldownEnd : false;
  };

  // Helper function to get remaining cooldown time in minutes
  const getRemainingCooldownMinutes = (employeeId: string): number => {
    const cooldownEnd = userCooldowns[employeeId];
    if (!cooldownEnd || new Date() >= cooldownEnd) return 0;
    return Math.ceil((cooldownEnd.getTime() - new Date().getTime()) / (1000 * 60));
  };

  // Helper function to set user cooldown
  const setUserCooldown = (employeeId: string, minutes: number = 5) => {
    const cooldownEnd = new Date();
    cooldownEnd.setMinutes(cooldownEnd.getMinutes() + minutes);
    setUserCooldowns(prev => ({
      ...prev,
      [employeeId]: cooldownEnd
    }));
  };
  const [showLiveAnalysis, setShowLiveAnalysis] = useState(true);
  const [currentAnalysis, setCurrentAnalysis] = useState<FaceAnalysis | null>(null);
  const [liveDetection, setLiveDetection] = useState(false);
  const [currentDetection, setCurrentDetection] = useState<{
    detected: boolean;
    quality: number;
    method?: string;
  }>({ detected: false, quality: 0 });

  // Confirmation modal state
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    employeeId: string;
    employeeName: string;
    action: 'clock_in' | 'clock_out';
    existingEntry?: {
      time_in: string;
      date: string;
    };
  } | null>(null);

  // Update current time every second and check cooldowns
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());

      // Clear expired cooldowns
      const now = new Date();
      setUserCooldowns(prev => {
        const updated = { ...prev };
        Object.keys(updated).forEach(userId => {
          if (updated[userId] <= now) {
            delete updated[userId];
          }
        });
        return updated;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Initialize system on mount with proper ref checking
  useEffect(() => {
    console.log('🔧 TimeClock component mounted, starting initialization...');

    const support = checkBrowserSupport();
    console.log('🔍 Browser support check:', support);

    if (!support.supported) {
      console.error('❌ Browser not supported:', support.missing);
      setError(`Browser not supported. Missing: ${support.missing.join(', ')}`);
      return;
    }

    console.log('✅ Browser support confirmed, starting initialization...');

    // Small delay to ensure DOM is ready, then initialize
    setTimeout(() => {
      if (videoRef.current) {
        console.log('✅ Video element found, starting initialization...');
        initializeSystem();
      } else {
        console.error('❌ Video element not found after DOM ready');
        setError('Video element not available. Please refresh the page.');
      }
    }, 100);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      if (recognitionIntervalRef.current) {
        clearInterval(recognitionIntervalRef.current);
      }
      if (videoRefreshIntervalRef.current) {
        clearInterval(videoRefreshIntervalRef.current);
      }
    };
  }, []);

  const initializeSystem = async () => {
    setIsInitializing(true);
    setError(null);

    try {
      console.log('🚀 Starting time clock initialization...');

      // Check if video element is available
      if (!videoRef.current) {
        throw new Error('Video element not available');
      }
      console.log('✅ Video element is available');

      // Check browser support first
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia not supported');
      }
      console.log('✅ getUserMedia API available');

      // Stop existing streams first
      if (streamRef.current) {
        console.log('🛑 Stopping existing camera stream...');
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }

      // Step 1: Get camera stream (exact same as working debug tool)
      console.log('📷 Requesting camera access...');
      const constraints: MediaStreamConstraints = {
        video: {
          facingMode: 'user',
          width: { ideal: 640, min: 320 },
          height: { ideal: 480, min: 240 }
        },
        audio: false
      };
      
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log('✅ Camera stream obtained');
      
      streamRef.current = stream;
      
      // Log stream details (like debug tool)
      const tracks = stream.getTracks();
      console.log(`📹 Stream has ${tracks.length} tracks:`);
      tracks.forEach((track, i) => {
        console.log(`  Track ${i}: ${track.kind}, enabled: ${track.enabled}, state: ${track.readyState}`);
      });

      // Step 2: Setup video element with enhanced initialization
      if (videoRef.current) {
        console.log('🎥 Assigning stream to video element...');
        const video = videoRef.current;

        // Clear any existing source first
        video.srcObject = null;
        video.load();

        // Wait a moment then assign the new stream
        await new Promise(resolve => setTimeout(resolve, 100));

        video.srcObject = stream;
        video.muted = true;
        video.playsInline = true;
        video.autoplay = true;

        // Add additional properties for better compatibility
        video.setAttribute('webkit-playsinline', 'true');
        video.setAttribute('playsinline', 'true');

        let initializationComplete = false;

        const handleInitialization = () => {
          if (!initializationComplete && video.videoWidth > 0 && video.videoHeight > 0) {
            initializationComplete = true;
            console.log(`✅ Video fully initialized: ${video.videoWidth}x${video.videoHeight}`);
            setIsInitialized(true);

            // Start face detection after video is ready
            setTimeout(() => {
              setLiveDetection(true);
              startLiveDetection();
            }, 1000);

            // Start monitoring
            startVideoMonitoring();
          }
        };

        video.onloadedmetadata = () => {
          console.log(`📹 Video metadata loaded: ${video.videoWidth}x${video.videoHeight}`);
          handleInitialization();
        };

        video.oncanplay = () => {
          console.log('📹 Video can play');
          handleInitialization();
        };

        video.onplaying = () => {
          console.log('📹 Video playing');
          handleInitialization();
        };

        video.onloadeddata = () => {
          console.log('📹 Video data loaded');
          handleInitialization();
        };

        video.onerror = (e) => {
          console.error(`❌ Video error:`, e);
          setError('Video playback error');
        };

        try {
          await video.play();
          console.log('✅ Video play() successful');

          // Give it a moment to fully initialize
          setTimeout(() => {
            handleInitialization();
          }, 500);

        } catch (playError: any) {
          console.log(`⚠️ Video play() failed: ${playError.message}`);
          if (playError.name === 'NotAllowedError') {
            setError('Video autoplay blocked - click the video to start');
          } else {
            setError(`Video play error: ${playError.message}`);
          }

          // Still try to initialize for user interaction
          setTimeout(() => {
            handleInitialization();
          }, 500);
        }
      } else {
        console.error('❌ Video element not found');
        setError('Video element not available');
      }

      // Step 3: Load face-api.js models after video is working
      console.log('📦 Loading face recognition models...');
      try {
        const faceApiReady = await initializeFaceAPI();
        if (faceApiReady) {
          console.log('✅ Face recognition models loaded successfully');
        } else {
          console.warn('⚠️ Face recognition models failed to load - camera will work but no face detection');
        }
      } catch (modelError: any) {
        console.warn('⚠️ Face-api.js initialization failed:', modelError);
        // Don't fail the whole initialization - camera can still work
      }

    } catch (err: any) {
      console.error('❌ Initialization failed:', err);
      
      // Provide specific error messages (keeping the good error handling)
      let errorMessage = 'Failed to initialize camera';
      
      if (err.name === 'NotAllowedError') {
        errorMessage = 'Camera permission denied. Please allow camera access and refresh the page.';
      } else if (err.name === 'NotFoundError') {
        errorMessage = 'No camera found. Please connect a camera and refresh the page.';
      } else if (err.name === 'NotSupportedError') {
        errorMessage = 'Camera not supported by this browser.';
      } else if (err.name === 'NotReadableError') {
        errorMessage = 'Camera is already in use by another application.';
      } else if (err.message === 'getUserMedia not supported') {
        errorMessage = 'Your browser does not support camera access.';
      } else {
        errorMessage = `Camera error: ${err.message || err}`;
      }
      
      setError(errorMessage);
    } finally {
      setIsInitializing(false);
    }
  };

  const startContinuousRecognition = () => {
    if (recognitionIntervalRef.current) {
      clearInterval(recognitionIntervalRef.current);
    }

    recognitionIntervalRef.current = setInterval(async () => {
      if (!isRecognizing && !showPinFallback && !isProcessingTimeEntry) {
        await performFaceRecognition();
      }
    }, 3000); // Check every 3 seconds for time clock
  };

  const startLiveDetection = () => {
    if (recognitionIntervalRef.current) {
      clearInterval(recognitionIntervalRef.current);
    }

    setLiveDetection(true);
    recognitionIntervalRef.current = setInterval(async () => {
      if (videoRef.current && canvasRef.current) {
        // Check if video has valid dimensions before attempting detection
        if (videoRef.current.videoWidth === 0 || videoRef.current.videoHeight === 0) {
          console.warn('⚠️ Skipping live detection - video dimensions are 0');
          setCurrentDetection({ detected: false, quality: 0 });
          return;
        }

        try {
          const captureResult = await captureFaceFromVideo(videoRef.current, canvasRef.current);
          setCurrentDetection({
            detected: !!captureResult.descriptor,
            quality: captureResult.quality,
            method: captureResult.detectionMethod,
          });

          // Perform live face analysis and draw overlay
          if (showLiveAnalysis && debugCanvasRef.current) {
            try {
              const analysis = await analyzeFace(videoRef.current);
              setCurrentAnalysis(analysis);

              // Draw analysis overlay
              if (analysis) {
                const video = videoRef.current;
                const canvas = debugCanvasRef.current;
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;

                drawFaceAnalysisOverlay(canvas, analysis, {
                  showLandmarks: true,
                  showBoundingBox: true,
                  showAnalysisText: true,
                });
              }
            } catch (analysisErr) {
              console.error('Face analysis error:', analysisErr);
              setCurrentAnalysis(null);
            }
          }

          // Perform actual recognition if face detected with good quality and not in cooldown
          if (captureResult.descriptor &&
              captureResult.quality > 0.6 &&
              !isRecognizing &&
              !isProcessingTimeEntry) {
              // Note: User-specific cooldown will be checked during recognition
            await performFaceRecognition();
          }
        } catch (err) {
          console.error('Live detection error:', err);
          setCurrentDetection({ detected: false, quality: 0 });
          setCurrentAnalysis(null);
        }
      }
    }, 1500); // More frequent for live feedback
  };

  // Enhanced video diagnostics based on face-api.js demo patterns
  const getVideoStatus = () => {
    const video = videoRef.current;
    const stream = streamRef.current;
    
    if (!video || !stream) {
      return {
        healthy: false,
        issues: ['No video element or stream'],
        details: { video: !!video, stream: !!stream }
      };
    }

    const status = {
      videoWidth: video.videoWidth,
      videoHeight: video.videoHeight,
      readyState: video.readyState,
      paused: video.paused,
      currentTime: video.currentTime,
      streamActive: stream.active,
      streamTracks: stream.getTracks().map(t => ({ 
        kind: t.kind, 
        enabled: t.enabled, 
        readyState: t.readyState,
        label: t.label
      })),
      networkState: video.networkState,
      error: video.error,
    };

    const issues = [];
    
    // Check video dimensions (critical for face detection)
    if (status.videoWidth === 0 || status.videoHeight === 0) {
      issues.push('Invalid video dimensions');
    }
    
    // Check ready state (should be 4 for optimal performance)
    if (status.readyState < 2) {
      issues.push('Video not ready for playback');
    }
    
    // Check if paused when it should be playing
    if (status.paused) {
      issues.push('Video is paused');
    }
    
    // Check stream health
    if (!status.streamActive) {
      issues.push('Stream is not active');
    }
    
    // Check for disabled tracks
    const disabledTracks = status.streamTracks.filter(t => !t.enabled);
    if (disabledTracks.length > 0) {
      issues.push(`${disabledTracks.length} disabled tracks`);
    }
    
    // Check for ended tracks
    const endedTracks = status.streamTracks.filter(t => t.readyState === 'ended');
    if (endedTracks.length > 0) {
      issues.push(`${endedTracks.length} ended tracks`);
    }

    return {
      healthy: issues.length === 0,
      issues,
      details: status
    };
  };

  // Enhanced video monitoring with comprehensive diagnostics
  const startVideoMonitoring = () => {
    if (videoRefreshIntervalRef.current) {
      clearInterval(videoRefreshIntervalRef.current);
    }

    let consecutiveIssues = 0;
    const maxConsecutiveAttempts = 3;
    let lastHealthyCheck = Date.now();

    videoRefreshIntervalRef.current = setInterval(() => {
      const status = getVideoStatus();
      
      if (!status.healthy) {
        consecutiveIssues++;
        console.log(`🔍 Video monitoring: Issues detected (attempt ${consecutiveIssues}/${maxConsecutiveAttempts})`, {
          issues: status.issues,
          details: status.details
        });

        if (consecutiveIssues <= maxConsecutiveAttempts) {
          // Progressive escalation based on face-api.js demo patterns
          if (consecutiveIssues === 1) {
            // Level 1: Simple play attempt
            console.log('🔄 Level 1 fix: Simple play attempt');
            const video = videoRef.current;
            if (video) {
              video.play().catch(err => {
                console.warn('Level 1 play attempt failed:', err);
              });
            }
          } else if (consecutiveIssues === 2) {
            // Level 2: Stream reassignment (face-api.js pattern)
            console.log('🔄 Level 2 fix: Stream reassignment');
            const video = videoRef.current;
            const stream = streamRef.current;
            if (video && stream) {
              video.srcObject = null;
              setTimeout(() => {
                video.srcObject = stream;
                video.load();
                setTimeout(() => {
                  video.play().catch(err => {
                    console.warn('Level 2 play attempt failed:', err);
                  });
                }, 200);
              }, 100);
            }
          } else if (consecutiveIssues === 3) {
            // Level 3: Complete force refresh
            console.log('🔄 Level 3 fix: Complete force refresh');
            forceVideoRefresh();
          }
        } else {
          console.warn('⚠️ Max consecutive issue attempts reached, stopping auto-fix');
          console.warn('💡 Manual intervention may be required - user should try "Fix Video Display" button');
        }
        
        lastHealthyCheck = Date.now();
      } else {
        // Video is healthy
        if (consecutiveIssues > 0) {
          console.log(`✅ Video monitoring: Video recovered after ${consecutiveIssues} attempts`);
          console.log('📊 Current video status:', status.details);
          consecutiveIssues = 0;
        }
        lastHealthyCheck = Date.now();
      }

      // Check for prolonged issues (longer than 30 seconds)
      const timeSinceHealthy = Date.now() - lastHealthyCheck;
      if (timeSinceHealthy > 30000) {
        console.warn('⚠️ Video has been unhealthy for >30 seconds - may need manual intervention');
      }
    }, 1500); // Check more frequently for responsive fixes
  };

  const stopLiveDetection = () => {
    if (recognitionIntervalRef.current) {
      clearInterval(recognitionIntervalRef.current);
      recognitionIntervalRef.current = null;
    }
    setLiveDetection(false);
    setCurrentDetection({ detected: false, quality: 0 });
  };

  const performFaceRecognition = async () => {
    if (!videoRef.current || !canvasRef.current || !currentOrganization) {
      return;
    }

    // Note: Cooldown check will be done per user during recognition

    // Prevent too frequent recognition attempts
    const now = Date.now();
    if (now - lastRecognitionAttempt.current < 2000) {
      return;
    }
    lastRecognitionAttempt.current = now;

    // Check if video has valid dimensions
    if (videoRef.current.videoWidth === 0 || videoRef.current.videoHeight === 0) {
      console.warn('⚠️ Video not ready for recognition - dimensions are 0');
      return;
    }

    setIsRecognizing(true);
    setError(null);

    try {
      console.log('🔍 Starting face recognition...');

      // Use enhanced face capture with multiple detection methods
      const captureResult = await captureFaceFromVideo(videoRef.current, canvasRef.current);

      if (!captureResult.descriptor) {
        console.log('❌ No face detected in current frame');
        return;
      }

      console.log(`📸 Face captured for recognition:`, {
        quality: captureResult.quality,
        method: captureResult.detectionMethod
      });

      // Get all enrolled face descriptors
      const { descriptors, error: descriptorsError } = await getOrganizationFaceDescriptors(
        currentOrganization.id
      );

      if (descriptorsError) {
        console.error('❌ Error getting face descriptors:', descriptorsError);
        setError(descriptorsError);
        return;
      }

      if (descriptors.length === 0) {
        console.warn('⚠️ No enrolled employees found');
        setError('No enrolled employees found. Please enroll employees first.');
        return;
      }

      console.log(`📋 Found ${descriptors.length} enrolled employees`);

      // Find best match using enhanced matching
      const knownDescriptors = descriptors.map(d => ({
        id: d.employeeId,
        descriptor: d.descriptor,
        threshold: d.threshold || 0.6,
      }));

      const match = findBestMatch(captureResult.descriptor, knownDescriptors);

      if (match && match.confidence > 0.7) { // Higher threshold for time clock
        console.log(`✅ Employee recognized: ${match.id} with ${(match.confidence * 100).toFixed(1)}% confidence`);

        // Log recognition attempt
        await logRecognitionAttempt(
          currentOrganization.id,
          match.id,
          'verification',
          'facial_recognition',
          true,
          match.confidence
        );

        setRecognitionResult({
          employeeId: match.id,
          confidence: match.confidence,
          employeeName: `Employee ${match.id}`, // Will be updated after time entry creation
          timestamp: new Date(),
        });

        // Auto-process time entry after a short delay
        setTimeout(() => {
          processTimeEntry(match.id, match.confidence);
        }, 2000);
      } else {
        console.log(`❌ Recognition failed - ${match ? `low confidence: ${(match.confidence * 100).toFixed(1)}%` : 'no match found'}`);

        // Log failed attempt if we had a low-confidence match
        if (match) {
          await logRecognitionAttempt(
            currentOrganization.id,
            match.id,
            'verification',
            'facial_recognition',
            false,
            match.confidence
          );
        }
      }
    } catch (err: any) {
      console.error('Recognition error:', err);
      setError(err.message);
    } finally {
      setIsRecognizing(false);
    }
  };

  // Check for existing open time entry
  const checkExistingTimeEntry = async (employeeId: string) => {
    try {
      const today = new Date().toISOString().split('T')[0];

      const { data, error } = await supabase
        .from('time_entries')
        .select('*')
        .eq('employee_id', employeeId)
        .eq('organization_id', currentOrganization!.id)
        .eq('date', today)
        .eq('status', 'present')
        .is('time_out', null)
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error checking existing time entry:', error);
        return null;
      }

      return data && data.length > 0 ? data[0] : null;
    } catch (err) {
      console.error('Error checking existing time entry:', err);
      return null;
    }
  };

  // Show confirmation modal before processing time entry
  const showTimeEntryConfirmation = async (employeeId: string, employeeName: string) => {
    const existingEntry = await checkExistingTimeEntry(employeeId);

    if (existingEntry) {
      // Employee has open entry - ask to clock out
      setPendingAction({
        employeeId,
        employeeName,
        action: 'clock_out',
        existingEntry: {
          time_in: existingEntry.time_in,
          date: existingEntry.date,
        },
      });
    } else {
      // No open entry - ask to clock in
      setPendingAction({
        employeeId,
        employeeName,
        action: 'clock_in',
      });
    }

    setShowConfirmation(true);
  };

  // Process confirmed time entry with specific action
  const processConfirmedTimeEntry = async (forceAction?: 'clock_in' | 'clock_out') => {
    if (!pendingAction) return;

    setIsProcessingTimeEntry(true);
    setError(null);
    setShowConfirmation(false);

    try {
      let result;

      if (forceAction === 'clock_in' && pendingAction.existingEntry) {
        // Force clock in - create new entry without closing the existing one
        result = await createTimeEntry(
          currentOrganization!.id,
          pendingAction.employeeId,
          'clock_in',
          'facial_recognition',
          0.95
        );
      } else {
        // Normal flow - let the system decide based on existing entries
        result = await createFaceTimeEntry(
          currentOrganization!.id,
          pendingAction.employeeId,
          'facial_recognition',
          0.95
        );
      }

      if (result.success) {
        const entryType = forceAction || result.action || pendingAction.action;

        // Set 5-minute cooldown for this user
        setUserCooldown(pendingAction.employeeId, 5);

        // Update UI with success message
        setLastTimeEntry({
          type: entryType,
          time: new Date().toLocaleTimeString(),
          employeeName: pendingAction.employeeName,
          employeeId: pendingAction.employeeId,
          timestamp: new Date(),
        });

        // Clear recognition result and success message after delay
        setTimeout(() => {
          setRecognitionResult(null);
          setLastTimeEntry(null);
        }, 10000);

        onTimeEntryCreated?.(pendingAction.employeeId, entryType);
      } else {
        setError(result.error || 'Failed to create time entry');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsProcessingTimeEntry(false);
      setPendingAction(null);
    }
  };

  // Cancel time entry
  const cancelTimeEntry = () => {
    setShowConfirmation(false);
    setPendingAction(null);
    setRecognitionResult(null);
    // Close the entire modal when user cancels
    onCancel?.();
  };

  const processTimeEntry = async (employeeId: string, confidence: number) => {
    if (!currentOrganization) return;

    try {
      console.log(`⏰ Processing time entry for employee: ${employeeId}`);

      // Check if this user is in cooldown
      if (isUserInCooldown(employeeId)) {
        const remainingMinutes = getRemainingCooldownMinutes(employeeId);
        console.log(`⏰ User ${employeeId} is in cooldown: ${remainingMinutes} minutes remaining`);
        setError(`Please wait ${remainingMinutes} minutes before your next entry.`);
        return;
      }

      // Get employee details for better UI feedback
      const { employee } = await getEmployeeById(currentOrganization.id, employeeId);
      const employeeName = employee
        ? `${employee.first_name} ${employee.last_name}`.trim()
        : `Employee ${employeeId}`;

      console.log(`👤 Employee name: ${employeeName}`);

      // Show confirmation instead of directly creating entry
      await showTimeEntryConfirmation(employeeId, employeeName);

    } catch (err: any) {
      console.error('❌ Time entry processing failed:', err);
      setError(err.message);
    }
  };

  const handlePinSuccess = async (employeeId: string) => {
    if (!currentOrganization) return;

    setShowPinFallback(false);

    try {
      // Get employee details for confirmation
      const { employee } = await getEmployeeById(currentOrganization.id, employeeId);
      const employeeName = employee
        ? `${employee.first_name} ${employee.last_name}`.trim()
        : `Employee ${employeeId}`;

      // Show confirmation for PIN entries too
      await showTimeEntryConfirmation(employeeId, employeeName);

    } catch (err: any) {
      setError(err.message);
    }
  };  // Enhanced force video refresh based on face-api.js demo best practices
  const forceVideoRefresh = async () => {
    const video = videoRef.current;
    if (!video || !streamRef.current) {
      console.warn('⚠️ Cannot refresh - video element or stream not available');
      return;
    }

    console.log('🔄 Starting comprehensive video refresh...');
    
    // Get initial diagnostic data
    const initialStatus = getVideoStatus();
    console.log('📊 Initial video status:', initialStatus);
    
    setError(null);

    try {
      // Step 1: Complete video element reset with event cleanup (face-api.js pattern)
      console.log('🔄 Step 1: Complete element reset and cleanup');
      
      // Remove all event listeners to prevent conflicts
      video.onloadedmetadata = null;
      video.oncanplay = null;
      video.onloadeddata = null;
      video.onplaying = null;
      video.onerror = null;
      
      // Pause and clear source
      video.pause();
      video.srcObject = null;
      video.removeAttribute('src');
      video.load();
      
      // Force DOM re-render (face-api.js technique)
      video.style.display = 'none';
      await new Promise(resolve => setTimeout(resolve, 100));
      video.style.display = 'block';
      
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Step 2: Reapply enhanced video properties (based on face-api.js webcam demo)
      console.log('🔄 Step 2: Enhanced video property setup');
      
      // Set all critical properties from face-api.js demo
      video.muted = true;
      video.playsInline = true;
      video.autoplay = true;
      video.controls = false;
      video.style.backgroundColor = '#000';
      
      // Additional properties for better browser compatibility
      video.setAttribute('webkit-playsinline', 'true');
      video.setAttribute('playsinline', 'true');
      
      // Step 3: Enhanced stream assignment with fresh properties
      console.log('🔄 Step 3: Enhanced stream assignment');
      
      const currentStream = streamRef.current;
      
      // Verify stream is still active before reassignment
      if (!currentStream.active) {
        throw new Error('Stream is no longer active - camera may have been disconnected');
      }
      
      // Check track health
      const videoTracks = currentStream.getVideoTracks();
      if (videoTracks.length === 0) {
        throw new Error('No video tracks available in stream');
      }
      
      const primaryTrack = videoTracks[0];
      if (primaryTrack.readyState === 'ended') {
        throw new Error('Video track has ended - camera may have been disconnected');
      }
      
      // Assign stream and load (face-api.js pattern)
      video.srcObject = currentStream;
      video.load();
      
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Step 4: Enhanced play attempts with proper error handling
      console.log('🔄 Step 4: Enhanced play attempts');
      
      let playSuccess = false;
      const maxPlayAttempts = 5;
      
      for (let attempt = 1; attempt <= maxPlayAttempts; attempt++) {
        try {
          await video.play();
          console.log(`✅ Video play successful on attempt ${attempt}`);
          playSuccess = true;
          break;
        } catch (playErr: any) {
          console.warn(`❌ Play attempt ${attempt} failed:`, playErr.message || playErr);
          
          if (playErr.name === 'NotAllowedError') {
            throw new Error('Video play blocked by browser - user interaction may be required');
          }
          
          if (attempt < maxPlayAttempts) {
            // Progressive delay increase for subsequent attempts
            const delay = 200 * attempt;
            await new Promise(resolve => setTimeout(resolve, delay));
            
            // Force reload between attempts for better success rate
            video.load();
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }
      }
      
      if (!playSuccess) {
        throw new Error(`Failed to start video playback after ${maxPlayAttempts} attempts`);
      }
      
      // Step 5: Enhanced dimension validation with timeout protection
      console.log('🔄 Step 5: Enhanced dimension validation');
      
      const dimensionTimeout = 5000; // 5 second timeout
      const checkInterval = 200;
      const maxChecks = dimensionTimeout / checkInterval;
      let dimensionChecks = 0;
      let validDimensions = false;
      
      while (dimensionChecks < maxChecks && !validDimensions) {
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        dimensionChecks++;
        
        const width = video.videoWidth;
        const height = video.videoHeight;
        
        console.log(`📐 Dimension check ${dimensionChecks}/${maxChecks}: ${width}x${height} (ready: ${video.readyState})`);
        
        if (width > 0 && height > 0) {
          validDimensions = true;
          console.log(`✅ Valid dimensions achieved: ${width}x${height}`);
          break;
        }
        
        // If we're halfway through checks and still no dimensions, try a reload
        if (dimensionChecks === Math.floor(maxChecks / 2)) {
          console.log('🔄 Midway reload attempt for dimension fix');
          video.load();
          await video.play().catch(console.warn);
        }
      }
      
      // Final status check
      const finalStatus = getVideoStatus();
      console.log('📊 Final video status:', finalStatus);
      
      if (finalStatus.healthy) {
        console.log('✅ Video refresh successful - all systems healthy');
        
        // Re-enable detection with delay for stability
        if (!liveDetection) {
          setTimeout(() => {
            setLiveDetection(true);
            startLiveDetection();
          }, 1000);
        }
        
        // Restart monitoring
        startVideoMonitoring();
        
        // Clear any existing errors
        setError(null);
      } else {
        console.warn('⚠️ Video refresh completed but issues remain:', finalStatus.issues);
        setError(`Video display issues persist: ${finalStatus.issues.join(', ')}. Camera may need to be restarted.`);
      }
      
    } catch (err: any) {
      console.error('❌ Enhanced video refresh failed:', err);
      const errorMessage = err.message || 'Unknown error during video refresh';
      setError(`Video refresh failed: ${errorMessage}`);
      
      // Additional error context for debugging
      console.error('📊 Error context:', {
        streamActive: streamRef.current?.active,
        videoElement: !!video,
        streamTracks: streamRef.current?.getTracks().map(t => ({ 
          kind: t.kind, 
          readyState: t.readyState,
          enabled: t.enabled 
        }))
      });
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: true,
      hour: 'numeric',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const toggleLiveAnalysis = () => {
    setShowLiveAnalysis(!showLiveAnalysis);
    if (!showLiveAnalysis && debugCanvasRef.current) {
      // Clear canvas when disabling
      const ctx = debugCanvasRef.current.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, debugCanvasRef.current.width, debugCanvasRef.current.height);
      }
    }
  };

  if (showPinFallback) {
    console.log('📌 Rendering PIN fallback interface...');
    return (
      <PinFallback
        onSuccess={handlePinSuccess}
        onCancel={() => {
          setShowPinFallback(false);
          // Also close the entire modal if user cancels PIN
          onCancel?.();
        }}
      />
    );
  }

  console.log('🎬 Rendering main TimeClock interface...', {
    isInitialized,
    isInitializing,
    error,
    hasStream: !!streamRef.current,
    currentOrganization: !!currentOrganization
  });

  return (
    <div className="bg-white">
      {/* Compact Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Time Clock</h1>
            <div className="text-sm opacity-90">
              {formatDate(currentTime)}
            </div>
          </div>
          <div className="text-right">
            <div className="text-3xl font-mono font-bold">
              {formatTime(currentTime)}
            </div>
            <div className="text-sm opacity-90">
              Current Time
            </div>
          </div>
        </div>

        {/* Status in header - Show cooldown for any user */}
        {Object.keys(userCooldowns).length > 0 && (
          <div className="mt-3 text-center">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-yellow-100 text-yellow-800">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
              Some users in cooldown period
            </div>
          </div>
        )}
      </div>

      <div className="p-4">{/* Content wrapper with reduced padding */}
        {/* Two-column layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Camera */}
          <div className="space-y-4">
            {/* Camera Feed */}
            <div className="relative">
              <div className="aspect-video bg-black rounded-lg overflow-hidden relative">{/* Remove height limit for better camera view */}
              {/* Always render video element for ref attachment */}
              <video
                ref={videoRef}
                className={`w-full h-full object-cover cursor-pointer ${
                  isInitialized ? 'block' : 'hidden'
                }`}
                autoPlay
                muted
                playsInline
                onClick={async () => {
                  // Handle click to start video if autoplay failed
                  if (videoRef.current && streamRef.current) {
                    try {
                      await videoRef.current.play();
                      console.log('✅ Video started via user click');
                    } catch (err) {
                      console.warn('❌ Manual video start failed:', err);
                    }
                  }
                }}
              />
              <canvas ref={canvasRef} className="hidden" />

              {/* Loading overlay */}
              {isInitializing && (
                <div className="absolute inset-0 bg-gray-200 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <Spinner size="xl" />
                    <p className="mt-4 text-gray-600">Initializing camera...</p>
                  </div>
                </div>
              )}

              {/* Not available overlay */}
              {!isInitializing && !isInitialized && (
                <div className="absolute inset-0 bg-gray-200 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <HiOutlineExclamation className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Camera not available</p>
                  </div>
                </div>
              )}

              {/* Simple Status Indicator */}
              {isInitialized && isRecognizing && (
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
                  <Badge color="info">
                    <Spinner size="sm" className="mr-2" />
                    Recognizing face...
                  </Badge>
                </div>
              )}

              {/* Cooldown Indicator - Show if any user is in cooldown */}
              {Object.keys(userCooldowns).length > 0 && (
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
                  <Badge color="warning">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
                      Cooldown Active
                    </div>
                  </Badge>
                </div>
              )}

            {/* Simple Instructions */}
            {isInitialized && (
              <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white px-4 py-2 rounded-lg text-center">
                <p className="text-sm font-medium">
                  {Object.keys(userCooldowns).length > 0
                    ? "Some users in cooldown period"
                    : "Look at the camera to clock in/out"
                  }
                </p>
                <p className="text-xs opacity-75">
                  {Object.keys(userCooldowns).length > 0
                    ? "Wait for cooldown to expire or use PIN"
                    : "Your face will be automatically recognized"
                  }
                </p>
              </div>
              )}
              </div>
            </div>

            {/* Camera Controls */}
            <div className="flex justify-center gap-3">
              <Button
                color="light"
                size="sm"
                onClick={() => setShowPinFallback(true)}
                disabled={isProcessingTimeEntry}
              >
                <HiOutlineKey className="mr-2 h-4 w-4" />
                Use PIN Instead
              </Button>

              {!isInitialized && (
                <Button
                  color="primary"
                  size="sm"
                  onClick={() => {
                    if (!videoRef.current) {
                      setError('Video element not available. Please refresh the page.');
                      return;
                    }
                    initializeSystem();
                  }}
                  disabled={isInitializing}
                >
                  <HiOutlineRefresh className="mr-2 h-4 w-4" />
                  {isInitializing ? 'Starting Camera...' : 'Start Camera'}
                </Button>
              )}
            </div>

            {/* Error */}
            {error && (
              <Alert color="failure">
                <HiOutlineExclamation className="h-4 w-4" />
                <div>
                  <div className="font-medium text-sm">Camera Error</div>
                  <div className="text-xs mt-1">{error}</div>
                  {streamRef.current && (
                    <div className="text-xs mt-1 opacity-75">
                      Camera stream is active but video display failed. Try clicking "Fix Video" or "Restart Camera".
                    </div>
                  )}
                </div>
              </Alert>
            )}
          </div>

          {/* Right Column - Recognition Data & Info */}
          <div className="space-y-4">
            {/* Success Message */}
            {lastTimeEntry && (
              <Alert color="success">
                <div className="flex items-center">
                  <HiOutlineCheck className="h-5 w-5 mr-3" />
                  <div>
                    <div className="font-bold text-lg">
                      {lastTimeEntry.type === 'clock_in' ? '✅ Clocked In' : '✅ Clocked Out'}
                    </div>
                    <div className="text-sm mt-1">
                      {lastTimeEntry.employeeName && `${lastTimeEntry.employeeName} • `}
                      {lastTimeEntry.time}
                    </div>
                  </div>
                </div>
              </Alert>
            )}

            {/* Recognition Result */}
            {recognitionResult && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <HiOutlineUserCircle className="h-6 w-6 mr-2 text-blue-600" />
                    <div className="font-medium text-blue-900">Face Recognized</div>
                  </div>
                  {isProcessingTimeEntry && (
                    <div className="flex items-center">
                      <Spinner size="sm" className="mr-2" />
                      <span className="text-sm text-blue-700">Processing...</span>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <div>
                    <div className="text-sm text-gray-600">Employee</div>
                    <div className="font-medium">
                      {recognitionResult.employeeName || `Employee ${recognitionResult.employeeId}`}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">Confidence</div>
                    <div className="font-medium">
                      {Math.round(recognitionResult.confidence * 100)}%
                    </div>
                  </div>

                  {recognitionResult.timestamp && (
                    <div>
                      <div className="text-sm text-gray-600">Recognized At</div>
                      <div className="font-medium">
                        {recognitionResult.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Instructions */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Instructions</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p>• Position your face clearly in the camera</p>
                <p>• Look directly at the camera</p>
                <p>• Ensure good lighting</p>
                <p>• Wait for automatic recognition</p>
              </div>
            </div>

            {/* Status Info */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Status</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Camera:</span>
                  <span className={isInitialized ? 'text-green-600' : 'text-red-600'}>
                    {isInitialized ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Recognition:</span>
                  <span className={isRecognizing ? 'text-blue-600' : 'text-gray-500'}>
                    {isRecognizing ? 'Scanning...' : 'Standby'}
                  </span>
                </div>
                {Object.keys(userCooldowns).length > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Cooldowns:</span>
                    <span className="text-yellow-600">
                      {Object.keys(userCooldowns).length} user(s)
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>{/* Close content wrapper */}

      {/* Confirmation Modal */}
      {showConfirmation && pendingAction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <div className="p-6">
              <div className="text-center mb-6">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                  <HiOutlineUserCircle className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {pendingAction.employeeName}
                </h3>
                <p className="text-sm text-gray-500">
                  {pendingAction.existingEntry
                    ? `You have an open time entry from ${
                        new Date(pendingAction.existingEntry.time_in).toLocaleTimeString()
                      }`
                    : 'Ready to clock in'
                  }
                </p>
              </div>

              <div className="text-center mb-6">
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  What would you like to do?
                </div>
                <div className="text-sm text-gray-500">
                  {new Date().toLocaleTimeString()}
                </div>
              </div>

              {pendingAction.existingEntry ? (
                // Employee has open entry - show both options
                <div className="space-y-3">
                  <Button
                    color="success"
                    className="w-full"
                    onClick={() => processConfirmedTimeEntry('clock_out')}
                    disabled={isProcessingTimeEntry}
                  >
                    {isProcessingTimeEntry ? (
                      <>
                        <Spinner size="sm" className="mr-2" />
                        Processing...
                      </>
                    ) : (
                      <>
                        🕐 Clock Out
                        <span className="block text-xs opacity-75">
                          End your current shift
                        </span>
                      </>
                    )}
                  </Button>

                  <Button
                    color="warning"
                    className="w-full"
                    onClick={() => processConfirmedTimeEntry('clock_in')}
                    disabled={isProcessingTimeEntry}
                  >
                    {isProcessingTimeEntry ? (
                      <>
                        <Spinner size="sm" className="mr-2" />
                        Processing...
                      </>
                    ) : (
                      <>
                        🕘 Clock In Again
                        <span className="block text-xs opacity-75">
                          Start new shift (previous entry will remain open)
                        </span>
                      </>
                    )}
                  </Button>

                  <Button
                    color="gray"
                    className="w-full"
                    onClick={cancelTimeEntry}
                    disabled={isProcessingTimeEntry}
                  >
                    Cancel
                  </Button>
                </div>
              ) : (
                // No open entry - simple clock in
                <div className="flex gap-3">
                  <Button
                    color="gray"
                    className="flex-1"
                    onClick={cancelTimeEntry}
                    disabled={isProcessingTimeEntry}
                  >
                    Cancel
                  </Button>
                  <Button
                    color="primary"
                    className="flex-1"
                    onClick={() => processConfirmedTimeEntry()}
                    disabled={isProcessingTimeEntry}
                  >
                    {isProcessingTimeEntry ? (
                      <>
                        <Spinner size="sm" className="mr-2" />
                        Processing...
                      </>
                    ) : (
                      '🕘 Clock In'
                    )}
                  </Button>
                </div>
              )}
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default TimeClock;
