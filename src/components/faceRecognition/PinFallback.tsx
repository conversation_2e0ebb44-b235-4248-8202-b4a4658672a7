import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, TextInput } from 'flowbite-react';
import {
  HiOut<PERSON>Key,
  HiOutlineBackspace,
  HiOutlineX,
  HiOutlineExclamation,
  HiOutlineLockClosed,
} from 'react-icons/hi';
import { verifyEmployeePin, logRecognitionAttempt } from '../../services/faceRecognition';
import { createFaceTimeEntry } from '../../services/faceTimeEntry';
import { useOrganization } from '../../context/OrganizationContext';

interface PinFallbackProps {
  onSuccess: (employeeId: string) => void;
  onCancel: () => void;
}

const PinFallback: React.FC<PinFallbackProps> = ({ onSuccess, onCancel }) => {
  const { currentOrganization } = useOrganization();
  const [employeeId, setEmployeeId] = useState('');
  const [pin, setPin] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isLocked, setIsLocked] = useState(false);
  const [step, setStep] = useState<'employee_id' | 'pin'>('employee_id');
  
  const employeeIdRef = useRef<HTMLInputElement>(null);
  const pinRef = useRef<HTMLInputElement>(null);

  // Focus appropriate input when step changes
  useEffect(() => {
    if (step === 'employee_id' && employeeIdRef.current) {
      employeeIdRef.current.focus();
    } else if (step === 'pin' && pinRef.current) {
      pinRef.current.focus();
    }
  }, [step]);

  const handleEmployeeIdSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!employeeId.trim()) {
      setError('Please enter your employee ID');
      return;
    }

    setError(null);
    setStep('pin');
  };

  const handlePinSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!pin.trim()) {
      setError('Please enter your PIN');
      return;
    }

    if (!currentOrganization) {
      setError('Organization not found');
      return;
    }

    setIsVerifying(true);
    setError(null);

    try {
      const result = await verifyEmployeePin(
        currentOrganization.id,
        employeeId,
        pin
      );

      if (result.success) {
        // Log successful PIN verification
        await logRecognitionAttempt(
          currentOrganization.id,
          employeeId,
          'verification',
          'pin',
          true
        );

        onSuccess(employeeId);
      } else {
        // Log failed PIN verification
        await logRecognitionAttempt(
          currentOrganization.id,
          employeeId,
          'verification',
          'pin',
          false,
          undefined,
          result.error
        );

        if (result.locked) {
          setIsLocked(true);
          setError('Account is temporarily locked due to too many failed attempts. Please try again later.');
        } else {
          setError(result.error || 'Invalid PIN');
        }
      }
    } catch (err: any) {
      setError(err.message || 'Verification failed');
    } finally {
      setIsVerifying(false);
    }
  };

  const handlePinDigit = (digit: string) => {
    if (pin.length < 6) { // Assuming max 6-digit PIN
      setPin(prev => prev + digit);
    }
  };

  const handlePinBackspace = () => {
    setPin(prev => prev.slice(0, -1));
  };

  const handlePinClear = () => {
    setPin('');
  };

  const goBackToEmployeeId = () => {
    setStep('employee_id');
    setPin('');
    setError(null);
  };

  const renderPinPad = () => {
    const digits = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
    
    return (
      <div className="grid grid-cols-3 gap-3 max-w-xs mx-auto">
        {digits.map(digit => (
          <Button
            key={digit}
            color="light"
            size="lg"
            className="aspect-square text-xl font-bold"
            onClick={() => handlePinDigit(digit)}
            disabled={isVerifying}
          >
            {digit}
          </Button>
        ))}
        
        {/* Backspace button */}
        <Button
          color="light"
          size="lg"
          className="aspect-square"
          onClick={handlePinBackspace}
          disabled={isVerifying || pin.length === 0}
        >
          <HiOutlineBackspace className="h-6 w-6" />
        </Button>
        
        {/* Clear button */}
        <Button
          color="light"
          size="lg"
          className="aspect-square"
          onClick={handlePinClear}
          disabled={isVerifying || pin.length === 0}
        >
          <HiOutlineX className="h-6 w-6" />
        </Button>
      </div>
    );
  };

  return (
    <div className="bg-white">
      {/* Compact Header */}
      <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <HiOutlineKey className="h-6 w-6 mr-3" />
            <div>
              <h2 className="text-xl font-bold">PIN Authentication</h2>
              <p className="text-sm opacity-90">
                {step === 'employee_id'
                  ? 'Enter your employee ID to continue'
                  : 'Enter your PIN to clock in/out'
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">{/* Content wrapper */}

          {/* Employee ID Step */}
          {step === 'employee_id' && (
            <form onSubmit={handleEmployeeIdSubmit} className="space-y-4">
              <div>
                <label htmlFor="employeeId" className="block text-sm font-medium text-gray-700 mb-2">
                  Employee ID
                </label>
                <TextInput
                  ref={employeeIdRef}
                  id="employeeId"
                  type="text"
                  value={employeeId}
                  onChange={(e) => setEmployeeId(e.target.value)}
                  placeholder="Enter your employee ID"
                  disabled={isVerifying}
                  autoComplete="username"
                />
              </div>

              <Button
                type="submit"
                color="primary"
                className="w-full"
                disabled={isVerifying || !employeeId.trim()}
              >
                Continue
              </Button>
            </form>
          )}

          {/* PIN Step */}
          {step === 'pin' && (
            <div className="space-y-6">
              {/* Employee ID Display */}
              <div className="text-center">
                <p className="text-sm text-gray-600">Employee ID:</p>
                <p className="text-lg font-medium text-gray-900">{employeeId}</p>
                <Button
                  color="light"
                  size="xs"
                  onClick={goBackToEmployeeId}
                  disabled={isVerifying}
                >
                  Change
                </Button>
              </div>

              {/* PIN Display */}
              <div className="text-center">
                <div className="flex justify-center space-x-2 mb-4">
                  {Array.from({ length: 6 }, (_, i) => (
                    <div
                      key={i}
                      className={`w-4 h-4 rounded-full border-2 ${
                        i < pin.length
                          ? 'bg-blue-600 border-blue-600'
                          : 'border-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>

              {/* PIN Pad */}
              {renderPinPad()}

              {/* Submit Button */}
              <Button
                color="primary"
                className="w-full"
                onClick={handlePinSubmit}
                disabled={isVerifying || pin.length === 0}
              >
                {isVerifying ? 'Verifying...' : 'Verify PIN'}
              </Button>
            </div>
          )}

          {/* Error */}
          {error && (
            <Alert color={isLocked ? 'warning' : 'failure'} className="mt-4">
              <div className="flex items-center">
                {isLocked ? (
                  <HiOutlineLockClosed className="h-4 w-4 mr-2" />
                ) : (
                  <HiOutlineExclamation className="h-4 w-4 mr-2" />
                )}
                <span>{error}</span>
              </div>
            </Alert>
          )}

        {/* Cancel Button */}
        <div className="mt-6 text-center">
          <Button
            color="light"
            onClick={onCancel}
            disabled={isVerifying}
          >
            Back to Camera
          </Button>
        </div>
      </div>{/* Close content wrapper */}
    </div>
  );
};

export default PinFallback;
