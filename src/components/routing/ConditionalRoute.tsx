import React from 'react';
import { Navigate } from 'react-router-dom';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';

interface ConditionalRouteProps {
  element: React.ReactNode;
  requiredFeature: 'chat' | string;
  fallbackPath?: string;
}

/**
 * A component that conditionally renders a route based on feature flags in organization settings.
 * If the required feature is disabled, it redirects to the fallback path.
 */
const ConditionalRoute: React.FC<ConditionalRouteProps> = ({ 
  element, 
  requiredFeature, 
  fallbackPath = '/' 
}) => {
  const { settings } = useOrganizationSettings();
  
  // Check if the required feature is enabled
  let isFeatureEnabled = true;
  
  if (requiredFeature === 'chat') {
    isFeatureEnabled = settings.chat_enabled !== false; // Default to true if not set
  }
  // Add more feature checks here as needed
  
  // If the feature is enabled, render the element
  if (isFeatureEnabled) {
    return <>{element}</>;
  }
  
  // Otherwise, redirect to the fallback path
  return <Navigate to={fallbackPath} replace />;
};

export default ConditionalRoute;
