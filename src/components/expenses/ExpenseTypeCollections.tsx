import React, { useState } from 'react';
import { <PERSON>, Button, Badge, Modal, Alert, Progress } from 'flowbite-react';
import { 
  HiOutlineCollection,
  HiOutlineCheck,
  HiOutlinePlus,
  HiOutlineX,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import { useAuth } from '../../context/AuthContext';
import { useOrganization } from '../../context/OrganizationContext';
import { createExpenseType } from '../../services/operationalExpenses';
import { BUSINESS_TYPE_TEMPLATES, BusinessTypeTemplate, ExpenseTypeTemplate } from '../../data/expenseTypeTemplates';

interface ExpenseTypeCollectionsProps {
  onExpenseTypesAdded?: () => void;
  className?: string;
}

const ExpenseTypeCollections: React.FC<ExpenseTypeCollectionsProps> = ({
  onExpenseTypesAdded,
  className = ''
}) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  // State
  const [showModal, setShowModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<BusinessTypeTemplate | null>(null);
  const [selectedExpenseTypes, setSelectedExpenseTypes] = useState<Set<string>>(new Set());
  const [isCreating, setIsCreating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Handle template selection
  const handleTemplateSelect = (template: BusinessTypeTemplate) => {
    setSelectedTemplate(template);
    // Pre-select all expense types
    const allCodes = new Set(template.expense_types.map(et => et.code));
    setSelectedExpenseTypes(allCodes);
  };

  // Toggle expense type selection
  const toggleExpenseType = (code: string) => {
    const newSelection = new Set(selectedExpenseTypes);
    if (newSelection.has(code)) {
      newSelection.delete(code);
    } else {
      newSelection.add(code);
    }
    setSelectedExpenseTypes(newSelection);
  };

  // Create selected expense types
  const handleCreateExpenseTypes = async () => {
    if (!currentOrganization || !user || !selectedTemplate) return;

    setIsCreating(true);
    setError(null);
    setProgress(0);

    try {
      const selectedTypes = selectedTemplate.expense_types.filter(et =>
        selectedExpenseTypes.has(et.code)
      );

      let created = 0;
      let skipped = 0;
      const total = selectedTypes.length;

      for (const expenseType of selectedTypes) {
        try {
          const result = await createExpenseType(currentOrganization.id, {
            code: expenseType.code,
            name: expenseType.name,
            category: expenseType.category as any, // Template uses string, API expects enum
            description: expenseType.description,
            is_recurring_type: expenseType.is_recurring,
            requires_approval: expenseType.requires_approval,
            approval_limit: expenseType.approval_limit
          }, user.id);

          if (result.success) {
            created++;
          } else {
            // If error contains "duplicate" or "already exists", count as skipped
            if (result.error?.toLowerCase().includes('duplicate') ||
                result.error?.toLowerCase().includes('already exists') ||
                result.error?.toLowerCase().includes('unique constraint')) {
              skipped++;
            }
          }
        } catch (err: any) {
          console.error(`Error creating expense type ${expenseType.code}:`, err);
          // Check if it's a duplicate error
          if (err.message?.toLowerCase().includes('duplicate') ||
              err.message?.toLowerCase().includes('already exists') ||
              err.message?.toLowerCase().includes('unique constraint')) {
            skipped++;
          }
        }

        setProgress(Math.round(((created + skipped) / total) * 100));
      }

      // Create success message with summary
      let message = '';
      if (created > 0 && skipped > 0) {
        message = `Created ${created} new expense types, skipped ${skipped} existing ones.`;
      } else if (created > 0) {
        message = `Successfully created ${created} expense types!`;
      } else if (skipped > 0) {
        message = `All ${skipped} expense types already exist - no new ones created.`;
      } else {
        setError('Failed to create expense types. Please try again.');
        return;
      }

      setSuccess(message);
      onExpenseTypesAdded?.();

      // Close modal after success
      setTimeout(() => {
        setShowModal(false);
        setSelectedTemplate(null);
        setSelectedExpenseTypes(new Set());
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsCreating(false);
      setProgress(0);
    }
  };

  return (
    <>
      <div className={className}>
        <Button
          color="light"
          onClick={() => setShowModal(true)}
          className="w-full"
        >
          <HiOutlineCollection className="mr-2 h-5 w-5" />
          Browse Expense Type Collections
        </Button>
      </div>

      {/* Collections Modal */}
      <Modal
        show={showModal}
        onClose={() => {
          setShowModal(false);
          setSelectedTemplate(null);
          setSelectedExpenseTypes(new Set());
          setError(null);
          setSuccess(null);
        }}
        size="6xl"
      >
        <Modal.Header>
          <div className="flex items-center gap-2">
            <HiOutlineCollection className="h-6 w-6" />
            Expense Type Collections
          </div>
        </Modal.Header>
        <Modal.Body>
          {!selectedTemplate ? (
            // Template Selection View
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">Choose Your Business Type</h3>
                <p className="text-gray-600">
                  Select a business type to see pre-configured expense categories that match your needs.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {BUSINESS_TYPE_TEMPLATES.map((template) => (
                  <Card
                    key={template.id}
                    className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 border-transparent hover:border-blue-200"
                    onClick={() => handleTemplateSelect(template)}
                  >
                    <div className="text-center">
                      <div className="text-4xl mb-3">{template.icon}</div>
                      <h4 className="text-lg font-semibold mb-2">{template.name}</h4>
                      <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                      <Badge color={template.color} className="mb-2">
                        {template.expense_types.length} expense types
                      </Badge>
                    </div>
                  </Card>
                ))}
              </div>

              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div className="flex items-start gap-2">
                  <HiOutlineInformationCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">What are expense type collections?</p>
                    <p>
                      These are pre-configured expense categories tailored for different business types. 
                      You can select which ones to add to your organization and customize them later.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            // Expense Type Selection View
            <div className="space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Button
                    color="light"
                    size="sm"
                    onClick={() => setSelectedTemplate(null)}
                  >
                    ← Back
                  </Button>
                  <div>
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <span className="text-2xl">{selectedTemplate.icon}</span>
                      {selectedTemplate.name}
                    </h3>
                    <p className="text-sm text-gray-600">{selectedTemplate.description}</p>
                  </div>
                </div>
                <Badge color={selectedTemplate.color}>
                  {selectedExpenseTypes.size} of {selectedTemplate.expense_types.length} selected
                </Badge>
              </div>

              {/* Progress Bar */}
              {isCreating && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Creating expense types...</span>
                    <span>{progress}%</span>
                  </div>
                  <Progress progress={progress} color="blue" />
                </div>
              )}

              {/* Alerts */}
              {error && (
                <Alert color="failure" onDismiss={() => setError(null)}>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert color="success">
                  {success}
                </Alert>
              )}

              {/* Expense Types Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-96 overflow-y-auto">
                {selectedTemplate.expense_types.map((expenseType) => (
                  <div
                    key={expenseType.code}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      selectedExpenseTypes.has(expenseType.code)
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => toggleExpenseType(expenseType.code)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-lg">{expenseType.icon}</span>
                          <h5 className="font-medium">{expenseType.name}</h5>
                          {selectedExpenseTypes.has(expenseType.code) && (
                            <HiOutlineCheck className="h-4 w-4 text-blue-600" />
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{expenseType.description}</p>
                        <div className="flex flex-wrap gap-1">
                          <Badge color={expenseType.color} size="xs">
                            {expenseType.category.replace('_', ' ')}
                          </Badge>
                          {expenseType.is_recurring && (
                            <Badge color="green" size="xs">Recurring</Badge>
                          )}
                          {expenseType.requires_approval && (
                            <Badge color="yellow" size="xs">Needs Approval</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          {selectedTemplate && (
            <div className="flex justify-between w-full">
              <div className="flex items-center gap-2">
                <Button
                  color="light"
                  onClick={() => {
                    const allCodes = new Set(selectedTemplate.expense_types.map(et => et.code));
                    setSelectedExpenseTypes(allCodes);
                  }}
                  size="sm"
                >
                  Select All
                </Button>
                <Button
                  color="light"
                  onClick={() => setSelectedExpenseTypes(new Set())}
                  size="sm"
                >
                  Clear All
                </Button>
              </div>
              <div className="flex gap-2">
                <Button
                  color="light"
                  onClick={() => {
                    setShowModal(false);
                    setSelectedTemplate(null);
                    setSelectedExpenseTypes(new Set());
                  }}
                  disabled={isCreating}
                >
                  <HiOutlineX className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button
                  color="primary"
                  onClick={handleCreateExpenseTypes}
                  disabled={selectedExpenseTypes.size === 0 || isCreating}
                >
                  <HiOutlinePlus className="mr-2 h-4 w-4" />
                  {isCreating ? 'Creating...' : `Add ${selectedExpenseTypes.size} Expense Types`}
                </Button>
              </div>
            </div>
          )}
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ExpenseTypeCollections;
