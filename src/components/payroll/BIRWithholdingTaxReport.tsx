import React from 'react';
import { Card, Table } from 'flowbite-react';
import { format } from 'date-fns';
import { formatCurrency } from '../../utils/formatters';

interface BIRWithholdingTaxReportProps {
  data: any[];
  year: number;
}

const BIRWithholdingTaxReport: React.FC<BIRWithholdingTaxReportProps> = ({ data, year }) => {
  // Calculate totals
  const totalBasicPay = data.reduce((sum, employee) => sum + employee.totalBasicPay, 0);
  const totalGrossPay = data.reduce((sum, employee) => sum + employee.totalGrossPay, 0);
  const totalNetPay = data.reduce((sum, employee) => sum + employee.totalNetPay, 0);
  const totalWithholdingTax = data.reduce((sum, employee) => sum + employee.totalWithholdingTax, 0);
  const totalSSS = data.reduce((sum, employee) => sum + employee.totalSSS, 0);
  const totalPhilHealth = data.reduce((sum, employee) => sum + employee.totalPhilHealth, 0);
  const totalPagibig = data.reduce((sum, employee) => sum + employee.totalPagibig, 0);
  const totalDeductions = data.reduce((sum, employee) => sum + employee.totalDeductions, 0);
  const totalTaxableIncome = data.reduce((sum, employee) => sum + employee.totalTaxableIncome, 0);

  return (
    <div className="space-y-6">
      <Card>
        <div className="text-center mb-6">
          <h2 className="text-xl font-bold">ANNUAL WITHHOLDING TAX REPORT</h2>
          <h3 className="text-lg">For Calendar Year {year}</h3>
          <p className="text-sm text-gray-500 mt-2">
            Equivalent to BIR Form 2316: Certificate of Compensation Payment / Tax Withheld
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm text-blue-700">Total Taxable Income</p>
            <p className="text-xl font-bold text-blue-700">{formatCurrency(totalTaxableIncome)}</p>
          </div>
          <div className="bg-red-50 p-4 rounded-lg">
            <p className="text-sm text-red-700">Total Withholding Tax</p>
            <p className="text-xl font-bold text-red-700">{formatCurrency(totalWithholdingTax)}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm text-green-700">Total Gross Compensation</p>
            <p className="text-xl font-bold text-green-700">{formatCurrency(totalGrossPay)}</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <p className="text-sm text-purple-700">Total Employees</p>
            <p className="text-xl font-bold text-purple-700">{data.length}</p>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <Table.Head>
              <Table.HeadCell>Employee</Table.HeadCell>
              <Table.HeadCell>TIN</Table.HeadCell>
              <Table.HeadCell>Gross Compensation</Table.HeadCell>
              <Table.HeadCell>Non-Taxable</Table.HeadCell>
              <Table.HeadCell>Taxable Income</Table.HeadCell>
              <Table.HeadCell>Withholding Tax</Table.HeadCell>
              <Table.HeadCell>Tax Rate</Table.HeadCell>
            </Table.Head>
            <Table.Body>
              {data.map((employee) => {
                // Calculate tax rate
                const taxRate = employee.totalWithholdingTax > 0 && employee.totalTaxableIncome > 0
                  ? (employee.totalWithholdingTax / employee.totalTaxableIncome * 100).toFixed(2)
                  : '0.00';
                
                // Calculate non-taxable amount
                const nonTaxable = employee.totalGrossPay - employee.totalTaxableIncome;
                
                return (
                  <Table.Row key={employee.employee.id} className="bg-white">
                    <Table.Cell className="font-medium">
                      {employee.employee.first_name} {employee.employee.last_name}
                      <div className="text-xs text-gray-500">
                        {employee.employee.employee_number || 'No ID'}
                      </div>
                    </Table.Cell>
                    <Table.Cell>{employee.employee.tin_number || 'N/A'}</Table.Cell>
                    <Table.Cell>{formatCurrency(employee.totalGrossPay)}</Table.Cell>
                    <Table.Cell>{formatCurrency(nonTaxable)}</Table.Cell>
                    <Table.Cell>{formatCurrency(employee.totalTaxableIncome)}</Table.Cell>
                    <Table.Cell>{formatCurrency(employee.totalWithholdingTax)}</Table.Cell>
                    <Table.Cell>{taxRate}%</Table.Cell>
                  </Table.Row>
                );
              })}
            </Table.Body>
            <Table.Footer>
              <Table.Row className="font-semibold text-gray-900">
                <Table.Cell colSpan={2}>Totals</Table.Cell>
                <Table.Cell>{formatCurrency(totalGrossPay)}</Table.Cell>
                <Table.Cell>{formatCurrency(totalGrossPay - totalTaxableIncome)}</Table.Cell>
                <Table.Cell>{formatCurrency(totalTaxableIncome)}</Table.Cell>
                <Table.Cell>{formatCurrency(totalWithholdingTax)}</Table.Cell>
                <Table.Cell>
                  {totalWithholdingTax > 0 && totalTaxableIncome > 0
                    ? (totalWithholdingTax / totalTaxableIncome * 100).toFixed(2)
                    : '0.00'}%
                </Table.Cell>
              </Table.Row>
            </Table.Footer>
          </Table>
        </div>
      </Card>

      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
        <div className="flex">
          <div className="ml-3">
            <p className="text-sm text-yellow-700">
              <span className="font-bold">Note:</span> This report is for reference only and may not exactly match the official BIR Form 2316. 
              Please consult with a tax professional before filing official tax documents.
            </p>
          </div>
        </div>
      </div>

      <h3 className="text-lg font-medium mt-8 mb-4">Detailed Employee Tax Information</h3>
      {data.map((employee) => (
        <Card key={`detail-${employee.employee.id}`} className="mb-4">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h4 className="text-lg font-medium">
                {employee.employee.first_name} {employee.employee.last_name}
              </h4>
              <p className="text-sm text-gray-500">
                TIN: {employee.employee.tin_number || 'N/A'} | 
                Employee ID: {employee.employee.employee_number || 'N/A'}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Annual Withholding Tax</p>
              <p className="text-lg font-medium text-red-600">
                {formatCurrency(employee.totalWithholdingTax)}
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            <div className="border rounded p-3">
              <p className="text-sm font-medium">Gross Compensation</p>
              <p className="text-lg">{formatCurrency(employee.totalGrossPay)}</p>
            </div>
            <div className="border rounded p-3">
              <p className="text-sm font-medium">Taxable Income</p>
              <p className="text-lg">{formatCurrency(employee.totalTaxableIncome)}</p>
            </div>
            <div className="border rounded p-3">
              <p className="text-sm font-medium">Tax Rate</p>
              <p className="text-lg">
                {employee.totalWithholdingTax > 0 && employee.totalTaxableIncome > 0
                  ? (employee.totalWithholdingTax / employee.totalTaxableIncome * 100).toFixed(2)
                  : '0.00'}%
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="border rounded p-3">
              <p className="text-sm font-medium">SSS Contributions</p>
              <p className="text-lg">{formatCurrency(employee.totalSSS)}</p>
            </div>
            <div className="border rounded p-3">
              <p className="text-sm font-medium">PhilHealth Contributions</p>
              <p className="text-lg">{formatCurrency(employee.totalPhilHealth)}</p>
            </div>
            <div className="border rounded p-3">
              <p className="text-sm font-medium">Pag-IBIG Contributions</p>
              <p className="text-lg">{formatCurrency(employee.totalPagibig)}</p>
            </div>
          </div>
          
          <div className="overflow-x-auto mt-4">
            <Table>
              <Table.Head>
                <Table.HeadCell>Period</Table.HeadCell>
                <Table.HeadCell>Gross Pay</Table.HeadCell>
                <Table.HeadCell>Taxable Income</Table.HeadCell>
                <Table.HeadCell>Withholding Tax</Table.HeadCell>
              </Table.Head>
              <Table.Body>
                {employee.periods.map((period: any, index: number) => (
                  <Table.Row key={index} className="bg-white">
                    <Table.Cell className="font-medium">
                      {period.periodName}
                      <div className="text-xs text-gray-500">
                        {format(new Date(period.startDate), 'MMM d')} - {format(new Date(period.endDate), 'MMM d, yyyy')}
                      </div>
                    </Table.Cell>
                    <Table.Cell>{formatCurrency(period.grossPay)}</Table.Cell>
                    <Table.Cell>{formatCurrency(period.taxableIncome || 0)}</Table.Cell>
                    <Table.Cell>{formatCurrency(period.withholdingTax)}</Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
              <Table.Footer>
                <Table.Row className="font-semibold text-gray-900">
                  <Table.Cell>Totals</Table.Cell>
                  <Table.Cell>{formatCurrency(employee.totalGrossPay)}</Table.Cell>
                  <Table.Cell>{formatCurrency(employee.totalTaxableIncome)}</Table.Cell>
                  <Table.Cell>{formatCurrency(employee.totalWithholdingTax)}</Table.Cell>
                </Table.Row>
              </Table.Footer>
            </Table>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default BIRWithholdingTaxReport;
