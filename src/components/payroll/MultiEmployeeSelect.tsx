import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Badge } from 'flowbite-react';
import { HiOutlineSearch, HiOutlineX, HiOutlineUser } from 'react-icons/hi';
import { getEmployees, Employee } from '../../services/employee';

interface MultiEmployeeSelectProps {
  organizationId: string;
  selectedEmployeeIds: string[];
  onSelect: (employeeIds: string[]) => void;
  placeholder?: string;
}

const MultiEmployeeSelect: React.FC<MultiEmployeeSelectProps> = ({
  organizationId,
  selectedEmployeeIds,
  onSelect,
  placeholder = 'Search and select employees...'
}) => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Load employees
  useEffect(() => {
    const fetchEmployees = async () => {
      if (!organizationId) return;
      
      setIsLoading(true);
      try {
        const { employees, error } = await getEmployees(organizationId);
        if (error) {
          console.error('Error fetching employees:', error);
        } else if (employees) {
          setEmployees(employees);
          setFilteredEmployees(employees);
        }
      } catch (err) {
        console.error('Error in fetchEmployees:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEmployees();
  }, [organizationId]);

  // Filter employees based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredEmployees(employees);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = employees.filter(
      emp =>
        emp.first_name.toLowerCase().includes(query) ||
        emp.last_name.toLowerCase().includes(query) ||
        `${emp.first_name} ${emp.last_name}`.toLowerCase().includes(query) ||
        (emp.employee_number && emp.employee_number.toLowerCase().includes(query))
    );

    setFilteredEmployees(filtered);
  }, [searchQuery, employees]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Toggle employee selection
  const toggleEmployeeSelection = (employeeId: string) => {
    const newSelection = selectedEmployeeIds.includes(employeeId)
      ? selectedEmployeeIds.filter(id => id !== employeeId)
      : [...selectedEmployeeIds, employeeId];
    
    onSelect(newSelection);
  };

  // Get selected employee names
  const selectedEmployeeNames = employees
    .filter(emp => selectedEmployeeIds.includes(emp.id))
    .map(emp => `${emp.first_name} ${emp.last_name}`);

  // Clear all selections
  const clearAllSelections = () => {
    onSelect([]);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="relative">
        <TextInput
          type="text"
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setIsDropdownOpen(true);
          }}
          onClick={() => setIsDropdownOpen(true)}
          placeholder={selectedEmployeeIds.length > 0 ? `${selectedEmployeeIds.length} employees selected` : placeholder}
          icon={isLoading ? Spinner : HiOutlineSearch}
          disabled={isLoading}
        />
      </div>

      {selectedEmployeeIds.length > 0 && (
        <div className="mt-2 flex flex-wrap gap-2">
          {selectedEmployeeNames.map((name, index) => (
            <Badge 
              key={index} 
              color="info" 
              className="flex items-center"
            >
              {name}
              <button
                onClick={() => toggleEmployeeSelection(selectedEmployeeIds[index])}
                className="ml-1 p-1"
              >
                <HiOutlineX className="h-3 w-3" />
              </button>
            </Badge>
          ))}
          
          <Button 
            size="xs" 
            color="light" 
            onClick={clearAllSelections}
            className="mt-1"
          >
            Clear All
          </Button>
        </div>
      )}

      {isDropdownOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 flex justify-center">
              <Spinner size="md" />
            </div>
          ) : employees.length === 0 ? (
            <div className="px-4 py-2 text-gray-500">No employees available. Please add employees first.</div>
          ) : filteredEmployees.length > 0 ? (
            <ul className="py-1">
              {filteredEmployees.map((employee) => (
                <li
                  key={employee.id}
                  className={`px-4 py-2 cursor-pointer hover:bg-gray-100 ${
                    selectedEmployeeIds.includes(employee.id) ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => toggleEmployeeSelection(employee.id)}
                >
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                      {employee.profile_image_url ? (
                        <img
                          src={employee.profile_image_url}
                          alt={`${employee.first_name} ${employee.last_name}`}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                      ) : (
                        <HiOutlineUser className="h-4 w-4 text-gray-500" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium">
                        {employee.first_name} {employee.last_name}
                      </div>
                      {employee.employee_number && (
                        <div className="text-xs text-gray-500">
                          ID: {employee.employee_number}
                        </div>
                      )}
                      {employee.position && (
                        <div className="text-xs text-gray-500">
                          {employee.position.title}
                        </div>
                      )}
                    </div>
                    {selectedEmployeeIds.includes(employee.id) && (
                      <div className="ml-auto">
                        <div className="h-5 w-5 bg-blue-500 rounded-full flex items-center justify-center">
                          <HiOutlineX className="h-3 w-3 text-white" />
                        </div>
                      </div>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="px-4 py-2 text-gray-500">No employees found matching "{searchQuery}"</div>
          )}
        </div>
      )}
    </div>
  );
};

export default MultiEmployeeSelect;
