import React, { useState } from 'react';
import { <PERSON><PERSON>, Button, Label, Select, Spinner, Alert } from 'flowbite-react';
import { HiOutlineExclamationCircle } from 'react-icons/hi';
import { process13thMonthPay } from '../../services/payrollProcessing';
import { useOrganization } from '../../contexts/OrganizationContext';

interface ThirteenthMonthPayModalProps {
  show: boolean;
  onClose: () => void;
  onSuccess: (periodId: string) => void;
}

const ThirteenthMonthPayModal: React.FC<ThirteenthMonthPayModalProps> = ({
  show,
  onClose,
  onSuccess
}) => {
  const { organization } = useOrganization();
  const [year, setYear] = useState<number>(new Date().getFullYear());
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Generate year options (current year and 2 years back)
  const currentYear = new Date().getFullYear();
  const yearOptions = [
    { value: currentYear, label: currentYear.toString() },
    { value: currentYear - 1, label: (currentYear - 1).toString() },
    { value: currentYear - 2, label: (currentYear - 2).toString() }
  ];
  
  const handleProcess = async () => {
    if (!organization?.id) return;
    
    setIsProcessing(true);
    setError(null);
    
    try {
      const result = await process13thMonthPay(organization.id, year);
      
      if (result.success && result.periodId) {
        onSuccess(result.periodId);
      } else {
        setError(result.error || 'Failed to process 13th month pay');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while processing 13th month pay');
    } finally {
      setIsProcessing(false);
    }
  };
  
  return (
    <Modal show={show} onClose={onClose} size="md">
      <Modal.Header>Process 13th Month Pay</Modal.Header>
      <Modal.Body>
        <div className="space-y-4">
          <p className="text-gray-500">
            This will calculate and process 13th month pay for all active employees based on their
            basic pay for the selected year. The 13th month pay is calculated as 1/12 of the total
            basic pay received during the year, or prorated based on months worked.
          </p>
          
          {error && (
            <Alert color="failure" icon={HiOutlineExclamationCircle}>
              {error}
            </Alert>
          )}
          
          <div>
            <div className="mb-2 block">
              <Label htmlFor="year" value="Year" />
            </div>
            <Select
              id="year"
              value={year}
              onChange={(e) => setYear(parseInt(e.target.value))}
              disabled={isProcessing}
            >
              {yearOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>
          </div>
          
          <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
            <h4 className="text-yellow-800 font-medium text-sm">Important Notes:</h4>
            <ul className="mt-1 text-sm text-yellow-700 list-disc list-inside">
              <li>13th month pay is tax-exempt up to ₱90,000</li>
              <li>All active employees will be included in the calculation</li>
              <li>The payment will be processed as a separate payroll period</li>
              <li>This action cannot be undone once the period is approved</li>
            </ul>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button
          color="primary"
          onClick={handleProcess}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Processing...
            </>
          ) : (
            'Process 13th Month Pay'
          )}
        </Button>
        <Button color="gray" onClick={onClose} disabled={isProcessing}>
          Cancel
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ThirteenthMonthPayModal;
