import { useState, useEffect, useRef } from 'react';
import { <PERSON>In<PERSON>, <PERSON><PERSON>, Spinner } from 'flowbite-react';
import { HiOutlineSearch, HiOutlineX } from 'react-icons/hi';
import { Employee } from '../../services/employee';

interface EmployeeSearchSelectProps {
  employees: Employee[];
  selectedEmployeeId: string | undefined;
  onSelect: (employeeId: string) => void;
  required?: boolean;
  isLoading?: boolean;
  className?: string;
  placeholder?: string;
}

const EmployeeSearchSelect: React.FC<EmployeeSearchSelectProps> = ({
  employees,
  selectedEmployeeId,
  onSelect,
  required = false,
  isLoading = false,
  className = '',
  placeholder = 'Search and select an employee...'
}) => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get the selected employee name
  const selectedEmployee = employees.find(emp => emp.id === selectedEmployeeId);
  const displayValue = selectedEmployee
    ? `${selectedEmployee.first_name} ${selectedEmployee.last_name}`
    : searchQuery;

  // Filter employees based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredEmployees(employees);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = employees.filter(
      emp =>
        emp.first_name.toLowerCase().includes(query) ||
        emp.last_name.toLowerCase().includes(query) ||
        `${emp.first_name} ${emp.last_name}`.toLowerCase().includes(query)
    );

    setFilteredEmployees(filtered);
  }, [searchQuery, employees]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle employee selection
  const handleSelectEmployee = (employeeId: string) => {
    onSelect(employeeId);
    setIsDropdownOpen(false);
    setSearchQuery('');
  };

  // Clear selection
  const handleClearSelection = () => {
    onSelect('');
    setSearchQuery('');
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div className="relative">
        <TextInput
          type="text"
          value={displayValue}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            if (selectedEmployeeId) {
              onSelect(''); // Clear selection when user starts typing
            }
            setIsDropdownOpen(true);
          }}
          onClick={() => setIsDropdownOpen(true)}
          placeholder={placeholder}
          required={required}
          icon={HiOutlineSearch}
          rightIcon={selectedEmployeeId ? () => (
            <Button
              size="xs"
              color="light"
              onClick={handleClearSelection}
              className="absolute right-1 top-1/2 transform -translate-y-1/2"
            >
              <HiOutlineX className="h-4 w-4" />
            </Button>
          ) : undefined}
          disabled={isLoading}
        />

        {isLoading && (
          <div className="absolute right-10 top-1/2 transform -translate-y-1/2">
            <Spinner size="sm" />
          </div>
        )}
      </div>

      {isDropdownOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {employees.length === 0 ? (
            <div className="px-4 py-2 text-gray-500">No employees available. Please add employees first.</div>
          ) : filteredEmployees.length > 0 ? (
            <ul className="py-1">
              {filteredEmployees.map((employee) => (
                <li
                  key={employee.id}
                  className={`px-4 py-2 cursor-pointer hover:bg-gray-100 ${
                    employee.id === selectedEmployeeId ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => handleSelectEmployee(employee.id)}
                >
                  <div className="font-medium">{employee.first_name} {employee.last_name}</div>
                  {employee.position && (
                    <div className="text-sm text-gray-500">{employee.position.title}</div>
                  )}
                </li>
              ))}
            </ul>
          ) : (
            <div className="px-4 py-2 text-gray-500">No employees found matching "{searchQuery}"</div>
          )}
        </div>
      )}
    </div>
  );
};

export default EmployeeSearchSelect;
