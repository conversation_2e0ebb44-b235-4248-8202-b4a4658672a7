import React, { useState } from 'react';
import { Card, Table, Button, Badge, Alert, Select, Label } from 'flowbite-react';
import { format } from 'date-fns';
import { formatCurrency } from '../../utils/formatters';
import { HiOutlineDocumentDownload, HiOutlineExclamationCircle } from 'react-icons/hi';

interface PagibigContributionReportProps {
  data: {
    items: any[];
    summary: {
      totalPagibig: number;
      employeeCount: number;
    };
  };
  month: number;
  year: number;
  employerName: string;
  employerPagibig: string;
  employerAddress: string;
}

const PagibigContributionReport: React.FC<PagibigContributionReportProps> = ({ 
  data, 
  month, 
  year, 
  employerName, 
  employerPagibig, 
  employerAddress 
}) => {
  const [reportType, setReportType] = useState<string>('mcrf');
  const [showValidationErrors, setShowValidationErrors] = useState<boolean>(false);
  
  // Group by employee
  const employeeMap = new Map();
  
  data.items.forEach((item: any) => {
    const employeeId = item.employee_id;
    const employee = item.employee;
    
    if (!employeeMap.has(employeeId)) {
      employeeMap.set(employeeId, {
        id: employeeId,
        firstName: employee.first_name,
        middleName: employee.middle_name || '',
        lastName: employee.last_name,
        pagibigNumber: employee.pagibig_number || '',
        totalPagibig: 0,
        employeeShare: 0,
        employerShare: 0,
        periods: []
      });
    }
    
    const employeeData = employeeMap.get(employeeId);
    const pagibigContribution = Number(item.pagibig_contribution || 0);
    
    // Calculate employer share (typically 2% of monthly salary, same as employee)
    // This is an approximation - in a real system, you'd get the actual employer contribution
    const employerShare = pagibigContribution; // Assuming equal 2% employee, 2% employer
    
    employeeData.totalPagibig += pagibigContribution;
    employeeData.employeeShare += pagibigContribution;
    employeeData.employerShare += employerShare;
    
    employeeData.periods.push({
      periodId: item.payroll_period.id,
      periodName: item.payroll_period.name,
      startDate: item.payroll_period.start_date,
      endDate: item.payroll_period.end_date,
      pagibigContribution: pagibigContribution
    });
  });
  
  // Convert map to array
  const employees = Array.from(employeeMap.values());
  
  // Calculate totals
  const totalEmployeeShare = employees.reduce((sum, emp) => sum + emp.employeeShare, 0);
  const totalEmployerShare = employees.reduce((sum, emp) => sum + emp.employerShare, 0);
  const grandTotal = totalEmployeeShare + totalEmployerShare;
  
  // Validation for Pag-IBIG numbers
  const employeesWithoutPagibig = employees.filter(emp => !emp.pagibigNumber);
  const hasValidationErrors = employeesWithoutPagibig.length > 0;
  
  // Get month name
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  const monthName = monthNames[month - 1];
  
  // Handle export
  const handleExport = () => {
    setShowValidationErrors(true);
    
    if (hasValidationErrors) {
      return;
    }
    
    // In a real implementation, this would generate and download the file
    alert('Export functionality would generate an official Pag-IBIG MCRF form here');
  };
  
  return (
    <div className="space-y-6">
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Pag-IBIG Contribution Report (MCRF)</h3>
          <div className="flex items-center space-x-4">
            <div>
              <Label htmlFor="report-type" value="Report Type" />
              <Select
                id="report-type"
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
                className="w-40"
              >
                <option value="mcrf">MCRF (Monthly)</option>
                <option value="mcrf_adj">MCRF-Adjustment</option>
              </Select>
            </div>
            <Button color="light" onClick={handleExport}>
              <HiOutlineDocumentDownload className="mr-2 h-5 w-5" />
              Export for Pag-IBIG
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-sm font-medium">Employer Name</p>
            <p className="text-lg">{employerName}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Pag-IBIG Employer Number</p>
            <p className="text-lg">{employerPagibig}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Employer Address</p>
            <p className="text-lg">{employerAddress}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Applicable Month/Year</p>
            <p className="text-lg">{monthName} {year}</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm text-blue-700">Total Employee Share</p>
            <p className="text-xl font-bold text-blue-700">{formatCurrency(totalEmployeeShare)}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm text-green-700">Total Employer Share</p>
            <p className="text-xl font-bold text-green-700">{formatCurrency(totalEmployerShare)}</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <p className="text-sm text-purple-700">Grand Total</p>
            <p className="text-xl font-bold text-purple-700">{formatCurrency(grandTotal)}</p>
          </div>
        </div>
        
        {showValidationErrors && hasValidationErrors && (
          <Alert color="failure" icon={HiOutlineExclamationCircle} className="mb-4">
            <span className="font-medium">Validation Error!</span> {employeesWithoutPagibig.length} employee(s) missing Pag-IBIG numbers.
            Please update employee records before exporting.
          </Alert>
        )}
      </Card>
      
      <div className="overflow-x-auto">
        <Table>
          <Table.Head>
            <Table.HeadCell className="w-10">No.</Table.HeadCell>
            <Table.HeadCell>Pag-IBIG MID Number</Table.HeadCell>
            <Table.HeadCell>Employee Name</Table.HeadCell>
            <Table.HeadCell>Employee Share</Table.HeadCell>
            <Table.HeadCell>Employer Share</Table.HeadCell>
            <Table.HeadCell>Total</Table.HeadCell>
            <Table.HeadCell>Remarks</Table.HeadCell>
          </Table.Head>
          <Table.Body>
            {employees.map((employee, index) => {
              const hasError = !employee.pagibigNumber;
              const total = employee.employeeShare + employee.employerShare;
              
              return (
                <Table.Row key={employee.id} className={`${hasError ? 'bg-red-50' : 'bg-white'}`}>
                  <Table.Cell>{index + 1}</Table.Cell>
                  <Table.Cell>
                    {employee.pagibigNumber || (
                      <Badge color="failure">Missing</Badge>
                    )}
                  </Table.Cell>
                  <Table.Cell className="font-medium">
                    {`${employee.lastName}, ${employee.firstName} ${employee.middleName.charAt(0)}`.trim()}
                  </Table.Cell>
                  <Table.Cell>{formatCurrency(employee.employeeShare)}</Table.Cell>
                  <Table.Cell>{formatCurrency(employee.employerShare)}</Table.Cell>
                  <Table.Cell className="font-medium">{formatCurrency(total)}</Table.Cell>
                  <Table.Cell>
                    {hasError && (
                      <span className="text-red-600 text-xs">Missing Pag-IBIG number</span>
                    )}
                  </Table.Cell>
                </Table.Row>
              );
            })}
          </Table.Body>
          <Table.Footer>
            <Table.Row className="font-semibold text-gray-900">
              <Table.Cell colSpan={3}>Totals</Table.Cell>
              <Table.Cell>{formatCurrency(totalEmployeeShare)}</Table.Cell>
              <Table.Cell>{formatCurrency(totalEmployerShare)}</Table.Cell>
              <Table.Cell>{formatCurrency(grandTotal)}</Table.Cell>
              <Table.Cell></Table.Cell>
            </Table.Row>
          </Table.Footer>
        </Table>
      </div>
      
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-medium mb-2">Remittance Instructions</h4>
        <p className="text-sm text-gray-600 mb-2">
          This report should be submitted to Pag-IBIG along with payment on or before the 15th day of the month following the applicable month.
        </p>
        <p className="text-sm text-gray-600">
          Payment can be made through Pag-IBIG accredited banks, GCash, or the Pag-IBIG Fund Electronic Collection System.
        </p>
      </div>
    </div>
  );
};

export default PagibigContributionReport;
