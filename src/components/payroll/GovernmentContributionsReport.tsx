import React from 'react';
import { Card, Table, Badge } from 'flowbite-react';
import { format } from 'date-fns';
import { formatCurrency } from '../../utils/formatters';

interface GovernmentContributionsReportProps {
  data: {
    items: any[];
    summary: {
      totalSSS: number;
      totalPhilHealth: number;
      totalPagibig: number;
      totalWithholdingTax: number;
      totalContributions: number;
      employeeCount: number;
    };
  };
}

const GovernmentContributionsReport: React.FC<GovernmentContributionsReportProps> = ({ data }) => {
  // Group by employee
  const employeeMap = new Map();
  
  data.items.forEach((item: any) => {
    const employeeId = item.employee_id;
    const employeeName = `${item.employee.first_name} ${item.employee.last_name}`;
    
    if (!employeeMap.has(employeeId)) {
      employeeMap.set(employeeId, {
        id: employeeId,
        name: employeeName,
        employeeNumber: item.employee.employee_number,
        tinNumber: item.employee.tin_number,
        sssNumber: item.employee.sss_number,
        philhealthNumber: item.employee.philhealth_number,
        pagibigNumber: item.employee.pagibig_number,
        totalSSS: 0,
        totalPhilHealth: 0,
        totalPagibig: 0,
        totalWithholdingTax: 0,
        periods: []
      });
    }
    
    const employeeData = employeeMap.get(employeeId);
    
    employeeData.totalSSS += Number(item.sss_contribution || 0);
    employeeData.totalPhilHealth += Number(item.philhealth_contribution || 0);
    employeeData.totalPagibig += Number(item.pagibig_contribution || 0);
    employeeData.totalWithholdingTax += Number(item.withholding_tax || 0);
    
    employeeData.periods.push({
      periodId: item.payroll_period.id,
      periodName: item.payroll_period.name,
      startDate: item.payroll_period.start_date,
      endDate: item.payroll_period.end_date,
      sssContribution: Number(item.sss_contribution || 0),
      philhealthContribution: Number(item.philhealth_contribution || 0),
      pagibigContribution: Number(item.pagibig_contribution || 0),
      withholdingTax: Number(item.withholding_tax || 0)
    });
  });
  
  // Convert map to array
  const employees = Array.from(employeeMap.values());

  return (
    <div className="space-y-6">
      <Card>
        <h3 className="text-lg font-medium mb-4">Government Contributions Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm text-blue-700">Total SSS</p>
            <p className="text-xl font-bold text-blue-700">{formatCurrency(data.summary.totalSSS)}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm text-green-700">Total PhilHealth</p>
            <p className="text-xl font-bold text-green-700">{formatCurrency(data.summary.totalPhilHealth)}</p>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <p className="text-sm text-yellow-700">Total Pag-IBIG</p>
            <p className="text-xl font-bold text-yellow-700">{formatCurrency(data.summary.totalPagibig)}</p>
          </div>
          <div className="bg-red-50 p-4 rounded-lg">
            <p className="text-sm text-red-700">Total Withholding Tax</p>
            <p className="text-xl font-bold text-red-700">{formatCurrency(data.summary.totalWithholdingTax)}</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <p className="text-sm text-purple-700">Total Contributions</p>
            <p className="text-xl font-bold text-purple-700">
              {formatCurrency(data.summary.totalSSS + data.summary.totalPhilHealth + data.summary.totalPagibig)}
            </p>
          </div>
        </div>
        <div className="mt-4 text-sm text-gray-500">
          <p>Total Employees: <span className="font-medium">{data.summary.employeeCount}</span></p>
          <p>Total Records: <span className="font-medium">{data.items.length}</span></p>
        </div>
      </Card>

      <div className="overflow-x-auto">
        <Table>
          <Table.Head>
            <Table.HeadCell>Employee</Table.HeadCell>
            <Table.HeadCell>ID Numbers</Table.HeadCell>
            <Table.HeadCell>SSS</Table.HeadCell>
            <Table.HeadCell>PhilHealth</Table.HeadCell>
            <Table.HeadCell>Pag-IBIG</Table.HeadCell>
            <Table.HeadCell>Withholding Tax</Table.HeadCell>
            <Table.HeadCell>Total</Table.HeadCell>
          </Table.Head>
          <Table.Body>
            {employees.map((employee) => (
              <Table.Row key={employee.id} className="bg-white">
                <Table.Cell className="font-medium">
                  {employee.name}
                  <div className="text-xs text-gray-500">
                    {employee.employeeNumber || 'No ID'}
                  </div>
                </Table.Cell>
                <Table.Cell>
                  <div className="space-y-1">
                    <div className="text-xs">
                      <span className="font-medium">TIN:</span> {employee.tinNumber || 'N/A'}
                    </div>
                    <div className="text-xs">
                      <span className="font-medium">SSS:</span> {employee.sssNumber || 'N/A'}
                    </div>
                    <div className="text-xs">
                      <span className="font-medium">PhilHealth:</span> {employee.philhealthNumber || 'N/A'}
                    </div>
                    <div className="text-xs">
                      <span className="font-medium">Pag-IBIG:</span> {employee.pagibigNumber || 'N/A'}
                    </div>
                  </div>
                </Table.Cell>
                <Table.Cell>{formatCurrency(employee.totalSSS)}</Table.Cell>
                <Table.Cell>{formatCurrency(employee.totalPhilHealth)}</Table.Cell>
                <Table.Cell>{formatCurrency(employee.totalPagibig)}</Table.Cell>
                <Table.Cell>{formatCurrency(employee.totalWithholdingTax)}</Table.Cell>
                <Table.Cell className="font-medium">
                  {formatCurrency(
                    employee.totalSSS +
                    employee.totalPhilHealth +
                    employee.totalPagibig +
                    employee.totalWithholdingTax
                  )}
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>

      <h3 className="text-lg font-medium mt-8 mb-4">Detailed Contribution Records</h3>
      {employees.map((employee) => (
        <Card key={`detail-${employee.id}`} className="mb-4">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h4 className="text-lg font-medium">{employee.name}</h4>
              <p className="text-sm text-gray-500">Employee ID: {employee.employeeNumber || 'N/A'}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Total Contributions</p>
              <p className="text-lg font-medium text-purple-600">
                {formatCurrency(
                  employee.totalSSS +
                  employee.totalPhilHealth +
                  employee.totalPagibig
                )}
              </p>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <Table>
              <Table.Head>
                <Table.HeadCell>Period</Table.HeadCell>
                <Table.HeadCell>SSS</Table.HeadCell>
                <Table.HeadCell>PhilHealth</Table.HeadCell>
                <Table.HeadCell>Pag-IBIG</Table.HeadCell>
                <Table.HeadCell>Withholding Tax</Table.HeadCell>
                <Table.HeadCell>Total</Table.HeadCell>
              </Table.Head>
              <Table.Body>
                {employee.periods.map((period: any, index: number) => (
                  <Table.Row key={index} className="bg-white">
                    <Table.Cell className="font-medium">
                      {period.periodName}
                      <div className="text-xs text-gray-500">
                        {format(new Date(period.startDate), 'MMM d')} - {format(new Date(period.endDate), 'MMM d, yyyy')}
                      </div>
                    </Table.Cell>
                    <Table.Cell>{formatCurrency(period.sssContribution)}</Table.Cell>
                    <Table.Cell>{formatCurrency(period.philhealthContribution)}</Table.Cell>
                    <Table.Cell>{formatCurrency(period.pagibigContribution)}</Table.Cell>
                    <Table.Cell>{formatCurrency(period.withholdingTax)}</Table.Cell>
                    <Table.Cell className="font-medium">
                      {formatCurrency(
                        period.sssContribution +
                        period.philhealthContribution +
                        period.pagibigContribution +
                        period.withholdingTax
                      )}
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
              <Table.Footer>
                <Table.Row className="font-semibold text-gray-900">
                  <Table.Cell>Totals</Table.Cell>
                  <Table.Cell>{formatCurrency(employee.totalSSS)}</Table.Cell>
                  <Table.Cell>{formatCurrency(employee.totalPhilHealth)}</Table.Cell>
                  <Table.Cell>{formatCurrency(employee.totalPagibig)}</Table.Cell>
                  <Table.Cell>{formatCurrency(employee.totalWithholdingTax)}</Table.Cell>
                  <Table.Cell>
                    {formatCurrency(
                      employee.totalSSS +
                      employee.totalPhilHealth +
                      employee.totalPagibig +
                      employee.totalWithholdingTax
                    )}
                  </Table.Cell>
                </Table.Row>
              </Table.Footer>
            </Table>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default GovernmentContributionsReport;
