import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'flowbite-react';
import { HiOutlineX, HiOutlineDownload, HiOutlinePrinter } from 'react-icons/hi';
import PayslipTemplate from './PayslipTemplate';
import { useOrganization } from '../../context/OrganizationContext';
import { getEmployees, EmployeeWithDetails } from '../../services/employee';
import { getDepartments, Department, createDepartment } from '../../services/department';
import { getJobPositions, JobPosition, createJobPosition } from '../../services/jobPosition';

interface PayslipTemplatePreviewProps {
  isOpen: boolean;
  onClose: () => void;
}

const PayslipTemplatePreview: React.FC<PayslipTemplatePreviewProps> = ({
  isOpen,
  onClose
}) => {
  const { currentOrganization } = useOrganization();
  const [employee, setEmployee] = useState<EmployeeWithDetails | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [positions, setPositions] = useState<JobPosition[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Function to ensure basic organizational structure exists
  const ensureBasicStructure = async (organizationId: string) => {
    try {
      // Check if departments exist, if not create basic ones
      const { departments } = await getDepartments(organizationId, { limit: 1 });
      if (departments.length === 0) {
        await createDepartment(organizationId, {
          name: 'Sales',
          description: 'Sales Department',
          is_active: true,
          manager_id: null
        });
      }

      // Check if positions exist, if not create basic ones
      const { positions } = await getJobPositions(organizationId, { limit: 1 });
      if (positions.length === 0) {
        await createJobPosition(organizationId, {
          title: 'Sales Associate',
          description: 'Sales Associate Position',
          department_id: null,
          is_active: true
        });
      }
    } catch (error) {
      console.error('Error ensuring basic structure:', error);
    }
  };

  // Fetch data for preview
  useEffect(() => {
    const fetchData = async () => {
      if (!currentOrganization || !isOpen) return;

      setIsLoading(true);
      try {
        // First ensure basic organizational structure exists
        await ensureBasicStructure(currentOrganization.id);

        // Fetch employees, departments, and positions in parallel
        const [employeesResult, departmentsResult, positionsResult] = await Promise.all([
          getEmployees(currentOrganization.id, { limit: 10 }),
          getDepartments(currentOrganization.id, { limit: 5 }),
          getJobPositions(currentOrganization.id, { limit: 5 })
        ]);

        setDepartments(departmentsResult.departments);
        setPositions(positionsResult.positions);

        if (employeesResult.employees.length > 0) {
          // Try to find an employee with position and department
          let selectedEmployee = employeesResult.employees.find(emp => emp.position && emp.department);

          // If no employee has both position and department, use the first one
          if (!selectedEmployee) {
            selectedEmployee = employeesResult.employees[0];
          }

          setEmployee(selectedEmployee);
        }
      } catch (error) {
        console.error('Error fetching data for preview:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentOrganization, isOpen]);

  // Use real employee data or fallback to sample data
  const getPositionName = () => {
    // First priority: Employee has linked position data
    if (employee?.position?.title) {
      return employee.position.title;
    }

    // Second priority: Employee has position_id, try to find it in fetched positions
    if (employee?.position_id && positions.length > 0) {
      const position = positions.find(p => p.id === employee.position_id);
      if (position) {
        return position.title;
      } else {
        return 'Position Not Found';
      }
    }

    // Third priority: Use first available position from organization
    if (positions.length > 0) {
      return positions[0].title;
    }

    // Final fallback: Default position
    return 'Sales Associate';
  };

  const getDepartmentName = () => {
    // First priority: Employee has linked department data
    if (employee?.department?.name) {
      return employee.department.name;
    }

    // Second priority: Employee has department_id, try to find it in fetched departments
    if (employee?.department_id && departments.length > 0) {
      const department = departments.find(d => d.id === employee.department_id);
      if (department) {
        return department.name;
      } else {
        return 'Department Not Found';
      }
    }

    // Third priority: Use first available department from organization
    if (departments.length > 0) {
      return departments[0].name;
    }

    // Final fallback: Default department
    return 'Sales';
  };

  // Ensure we always have valid position and department values
  const positionName = getPositionName();
  const departmentName = getDepartmentName();

  // Ensure no null/undefined values are passed to template
  const employeeData = {
    name: employee ? `${employee.first_name} ${employee.last_name}` : 'Raulito Cadahing Borreros',
    employee_id: employee?.employee_number || employee?.id || '1',
    position: positionName || 'Sales Associate', // Double fallback
    department: departmentName || 'Sales' // Double fallback
  };



  const sampleData = {
    employee: employeeData,
    payPeriod: {
      start_date: '2025-05-15',
      end_date: '2025-05-30',
      payment_date: '2025-06-03'
    },
    earnings: {
      basic_pay: 9900.00,
      overtime_pay: 1200.00,
      holiday_pay: 500.00,
      night_differential: 300.00,
      allowances: 2000.00
    },
    deductions: {
      sss_contribution: 495.00,
      philhealth_contribution: 275.00,
      pagibig_contribution: 100.00,
      withholding_tax: 850.00,
      loans: 1000.00,
      other_deductions: 200.00
    },
    company: {
      name: 'Rapverse',
      address: '123 Business Street, Makati City, Philippines',
      brand_color: '#3B82F6'
    },
    template: {
      style: 'modern' as const,
      show_company_address: true,
      include_qr_code: true,
      paper_size: 'a4' as const
    }
  };

  const handleDownload = () => {
    // In a real implementation, this would generate a PDF
    console.log('Downloading payslip template...');
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="4xl">
      <Modal.Header>
        <div className="flex items-center justify-between w-full">
          <h3 className="text-lg font-semibold text-gray-900">
            Payslip Template Preview
          </h3>
          <div className="flex gap-2">
            <Button color="gray" size="sm" onClick={handlePrint}>
              <HiOutlinePrinter className="mr-2 h-4 w-4" />
              Print
            </Button>
            <Button color="blue" size="sm" onClick={handleDownload}>
              <HiOutlineDownload className="mr-2 h-4 w-4" />
              Download PDF
            </Button>
          </div>
        </div>
      </Modal.Header>

      <Modal.Body className="p-0">
        <div className="max-h-[70vh] overflow-y-auto p-6 bg-gray-50">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Spinner size="xl" />
              <span className="ml-3 text-gray-600">Loading employee data...</span>
            </div>
          ) : (
            <PayslipTemplate {...sampleData} />
          )}
        </div>
      </Modal.Body>

      <Modal.Footer>
        <div className="flex justify-between w-full">
          <div className="text-sm text-gray-500">
            {employee
              ? `Preview using real employee data: ${employee.first_name} ${employee.last_name}`
              : 'This is a preview using sample data. Actual payslips will use real employee information.'
            }
          </div>
          <Button color="gray" onClick={onClose}>
            Close
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default PayslipTemplatePreview;
