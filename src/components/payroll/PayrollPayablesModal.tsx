import React from 'react';
import { Modal, Button, Checkbox, Label, Alert } from 'flowbite-react';
import {
  HiOutlinePaperAirplane,
  HiOutlineExclamation,
  HiOutlineCheckCircle,
  HiOutlineUsers,
  HiOutlineOfficeBuilding,
  HiOutlineCurrencyDollar
} from 'react-icons/hi';
import { PayrollPayableOptions } from '../../services/payrollPayables';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

interface PayrollPayablesModalProps {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  payrollPeriodName: string;
  totalNetPay: number;
  totalEmployees: number;
  options: PayrollPayableOptions;
  onOptionsChange: (options: PayrollPayableOptions) => void;
  loading: boolean;
  result?: {
    success: boolean;
    message: string;
    summary?: any;
  } | null;
}

const PayrollPayablesModal: React.FC<PayrollPayablesModalProps> = ({
  show,
  onClose,
  onConfirm,
  payrollPeriodName,
  totalNetPay,
  totalEmployees,
  options,
  onOptionsChange,
  loading,
  result
}) => {
  const formatWithCurrency = useCurrencyFormatter();

  // Estimated government contributions (rough calculation for display)
  const estimatedGovernmentContributions = totalNetPay * 0.15; // Rough estimate

  return (
    <Modal show={show} onClose={onClose} size="lg">
      <Modal.Header>
        <div className="flex items-center gap-2">
          <HiOutlinePaperAirplane className="h-5 w-5 text-blue-600" />
          Create Payables from Payroll
        </div>
      </Modal.Header>

      <Modal.Body>
        <div className="space-y-6">
          {!result ? (
            <>
              {/* Payroll Period Summary */}
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-800 mb-2">Payroll Period: {payrollPeriodName}</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2 text-blue-700">
                    <HiOutlineUsers className="h-4 w-4" />
                    <span>{totalEmployees} employees</span>
                  </div>
                  <div className="flex items-center gap-2 text-blue-700">
                    <HiOutlineCurrencyDollar className="h-4 w-4" />
                    <span>Total Net Pay: {formatWithCurrency(totalNetPay)}</span>
                  </div>
                </div>
              </div>

              {/* Warning */}
              <div className="flex items-start gap-3 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <HiOutlineExclamation className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-yellow-800">Confirm Action</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    This will create payables for the approved payroll period. 
                    Choose which types of payables to create:
                  </p>
                </div>
              </div>

              {/* Payable Options */}
              <div className="space-y-4">
                {/* Employee Salary Payables */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Checkbox
                      checked={options.createEmployeePayables}
                      onChange={(e) => onOptionsChange({
                        ...options,
                        createEmployeePayables: e.target.checked
                      })}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <HiOutlineUsers className="h-4 w-4 text-green-600" />
                        <Label className="font-medium text-gray-900">Employee Salary Payables</Label>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        Create individual payables for each employee's net pay
                      </p>
                      <div className="text-sm font-medium text-green-600">
                        {totalEmployees} payables • {formatWithCurrency(totalNetPay)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Government Remittances */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Checkbox
                      checked={options.createGovernmentPayables}
                      onChange={(e) => onOptionsChange({
                        ...options,
                        createGovernmentPayables: e.target.checked
                      })}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <HiOutlineOfficeBuilding className="h-4 w-4 text-blue-600" />
                        <Label className="font-medium text-gray-900">Government Remittances</Label>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        Create payables for government contributions and withholding tax
                      </p>
                      <div className="text-xs text-gray-500 space-y-1">
                        <div>• SSS Contributions (Employee + Employer)</div>
                        <div>• PhilHealth Contributions (Employee + Employer)</div>
                        <div>• Pag-IBIG Contributions (Employee + Employer)</div>
                        <div>• BIR Withholding Tax</div>
                      </div>
                      <div className="text-sm font-medium text-blue-600 mt-2">
                        ~4 payables • {formatWithCurrency(estimatedGovernmentContributions)} (estimated)
                      </div>
                    </div>
                  </div>
                </div>

                {/* Third-party Deductions */}
                <div className="border border-gray-200 rounded-lg p-4 opacity-50">
                  <div className="flex items-start gap-3">
                    <Checkbox
                      checked={options.createThirdPartyPayables}
                      onChange={(e) => onOptionsChange({
                        ...options,
                        createThirdPartyPayables: e.target.checked
                      })}
                      disabled={true}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <HiOutlineOfficeBuilding className="h-4 w-4 text-gray-400" />
                        <Label className="font-medium text-gray-500">Third-party Deductions</Label>
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Coming Soon</span>
                      </div>
                      <p className="text-sm text-gray-500 mb-2">
                        Create payables for loan deductions and other third-party payments
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Summary */}
              {(options.createEmployeePayables || options.createGovernmentPayables) && (
                <div className="bg-gray-50 p-4 rounded-lg border">
                  <h5 className="font-medium text-gray-900 mb-2">Summary</h5>
                  <div className="text-sm text-gray-600 space-y-1">
                    {options.createEmployeePayables && (
                      <div>• {totalEmployees} employee salary payables</div>
                    )}
                    {options.createGovernmentPayables && (
                      <div>• ~4 government remittance payables</div>
                    )}
                    <div className="font-medium text-gray-900 mt-2">
                      Total estimated: {formatWithCurrency(
                        (options.createEmployeePayables ? totalNetPay : 0) +
                        (options.createGovernmentPayables ? estimatedGovernmentContributions : 0)
                      )}
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            /* Result Display */
            <div className="space-y-4">
              {result.success ? (
                <div className="flex items-start gap-3 p-4 bg-green-50 rounded-lg border border-green-200">
                  <HiOutlineCheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-green-800">Success!</h4>
                    <p className="text-sm text-green-700 mt-1">
                      {result.message}
                    </p>
                    {result.summary && (
                      <div className="mt-3 text-sm text-green-700 space-y-1">
                        {result.summary.employeePayables > 0 && (
                          <div>• {result.summary.employeePayables} employee salary payables</div>
                        )}
                        {result.summary.governmentPayables > 0 && (
                          <div>• {result.summary.governmentPayables} government remittance payables</div>
                        )}
                        {result.summary.thirdPartyPayables > 0 && (
                          <div>• {result.summary.thirdPartyPayables} third-party payables</div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <Alert color="failure">
                  <div className="flex items-center gap-2">
                    <HiOutlineExclamation className="h-4 w-4" />
                    <span className="font-medium">Error</span>
                  </div>
                  <div className="mt-2 text-sm">
                    {result.message}
                  </div>
                </Alert>
              )}
            </div>
          )}
        </div>
      </Modal.Body>

      <Modal.Footer>
        <div className="flex justify-between w-full">
          <Button color="gray" onClick={onClose} disabled={loading}>
            {result?.success ? 'Close' : 'Cancel'}
          </Button>
          {!result && (
            <Button 
              color="primary" 
              onClick={onConfirm}
              disabled={loading || (!options.createEmployeePayables && !options.createGovernmentPayables)}
              isProcessing={loading}
            >
              {loading ? 'Creating Payables...' : 'Create Payables'}
            </Button>
          )}
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default PayrollPayablesModal;
