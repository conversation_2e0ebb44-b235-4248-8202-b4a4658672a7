import React, { useState } from 'react';
import { <PERSON>, Ta<PERSON>, Button, Alert, Select, Label, Spinner } from 'flowbite-react';
import { format } from 'date-fns';
import { formatCurrency } from '../../utils/formatters';
import { HiOutlineDocumentDownload, HiOutlineExclamationCircle, HiOutlineDocumentReport } from 'react-icons/hi';
import SSSContributionReport from './SSSContributionReport';
import PhilHealthContributionReport from './PhilHealthContributionReport';
import PagibigContributionReport from './PagibigContributionReport';

interface EnhancedGovernmentContributionsReportProps {
  data: {
    items: any[];
    summary: {
      totalSSS: number;
      totalPhilHealth: number;
      totalPagibig: number;
      totalWithholdingTax: number;
      totalContributions: number;
      employeeCount: number;
    };
  };
  organizationName: string;
  organizationDetails: {
    sssNumber: string;
    philHealthNumber: string;
    pagibigNumber: string;
    address: string;
  };
  isLoading?: boolean;
  onBatchProcess?: () => void;
}

const EnhancedGovernmentContributionsReport: React.FC<EnhancedGovernmentContributionsReportProps> = ({ 
  data, 
  organizationName, 
  organizationDetails,
  isLoading = false,
  onBatchProcess
}) => {
  const [activeTab, setActiveTab] = useState<number>(0);
  const [month, setMonth] = useState<number>(new Date().getMonth() + 1);
  const [year, setYear] = useState<number>(new Date().getFullYear());
  
  // Get month name
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  
  // Handle batch processing
  const handleBatchProcess = () => {
    if (onBatchProcess) {
      onBatchProcess();
    } else {
      alert('Batch processing would generate all reports at once');
    }
  };
  
  return (
    <div className="space-y-6">
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Government Contributions Reports</h3>
          <div className="flex items-center space-x-4">
            <div>
              <Label htmlFor="month-select" value="Month" />
              <Select
                id="month-select"
                value={month}
                onChange={(e) => setMonth(parseInt(e.target.value))}
                className="w-40"
              >
                {monthNames.map((name, index) => (
                  <option key={index} value={index + 1}>{name}</option>
                ))}
              </Select>
            </div>
            <div>
              <Label htmlFor="year-select" value="Year" />
              <Select
                id="year-select"
                value={year}
                onChange={(e) => setYear(parseInt(e.target.value))}
                className="w-32"
              >
                {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map((yearOption) => (
                  <option key={yearOption} value={yearOption}>{yearOption}</option>
                ))}
              </Select>
            </div>
            <Button color="primary" onClick={handleBatchProcess} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <HiOutlineDocumentReport className="mr-2 h-5 w-5" />
                  Batch Process All
                </>
              )}
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm text-blue-700">Total SSS</p>
            <p className="text-xl font-bold text-blue-700">{formatCurrency(data.summary.totalSSS)}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm text-green-700">Total PhilHealth</p>
            <p className="text-xl font-bold text-green-700">{formatCurrency(data.summary.totalPhilHealth)}</p>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <p className="text-sm text-yellow-700">Total Pag-IBIG</p>
            <p className="text-xl font-bold text-yellow-700">{formatCurrency(data.summary.totalPagibig)}</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <p className="text-sm text-purple-700">Total Contributions</p>
            <p className="text-xl font-bold text-purple-700">
              {formatCurrency(data.summary.totalSSS + data.summary.totalPhilHealth + data.summary.totalPagibig)}
            </p>
          </div>
        </div>
        
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <h4 className="font-medium mb-2">Compliance Status</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <div className={`h-3 w-3 rounded-full mr-2 ${data.summary.totalSSS > 0 ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm">SSS Contributions: {data.summary.totalSSS > 0 ? 'Ready to submit' : 'No data'}</span>
            </div>
            <div className="flex items-center">
              <div className={`h-3 w-3 rounded-full mr-2 ${data.summary.totalPhilHealth > 0 ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm">PhilHealth Contributions: {data.summary.totalPhilHealth > 0 ? 'Ready to submit' : 'No data'}</span>
            </div>
            <div className="flex items-center">
              <div className={`h-3 w-3 rounded-full mr-2 ${data.summary.totalPagibig > 0 ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm">Pag-IBIG Contributions: {data.summary.totalPagibig > 0 ? 'Ready to submit' : 'No data'}</span>
            </div>
          </div>
        </div>
      </Card>
      
      <Tabs.Group
        aria-label="Government contribution reports"
        style="underline"
        onActiveTabChange={setActiveTab}
      >
        <Tabs.Item
          active
          title="SSS (R-3)"
          icon={HiOutlineDocumentReport}
        >
          <SSSContributionReport 
            data={{
              items: data.items,
              summary: {
                totalSSS: data.summary.totalSSS,
                employeeCount: data.summary.employeeCount
              }
            }}
            month={month}
            year={year}
            employerName={organizationName}
            employerSSS={organizationDetails.sssNumber}
            employerAddress={organizationDetails.address}
          />
        </Tabs.Item>
        <Tabs.Item
          title="PhilHealth (RF-1)"
          icon={HiOutlineDocumentReport}
        >
          <PhilHealthContributionReport 
            data={{
              items: data.items,
              summary: {
                totalPhilHealth: data.summary.totalPhilHealth,
                employeeCount: data.summary.employeeCount
              }
            }}
            month={month}
            year={year}
            employerName={organizationName}
            employerPhilHealth={organizationDetails.philHealthNumber}
            employerAddress={organizationDetails.address}
          />
        </Tabs.Item>
        <Tabs.Item
          title="Pag-IBIG (MCRF)"
          icon={HiOutlineDocumentReport}
        >
          <PagibigContributionReport 
            data={{
              items: data.items,
              summary: {
                totalPagibig: data.summary.totalPagibig,
                employeeCount: data.summary.employeeCount
              }
            }}
            month={month}
            year={year}
            employerName={organizationName}
            employerPagibig={organizationDetails.pagibigNumber}
            employerAddress={organizationDetails.address}
          />
        </Tabs.Item>
      </Tabs.Group>
      
      <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Remittance Deadlines</h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc pl-5 space-y-1">
                <li>SSS: 10th day (10+ employees) or 15th day (less than 10 employees) of the following month</li>
                <li>PhilHealth: 10th day of the following month</li>
                <li>Pag-IBIG: 15th day of the following month</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedGovernmentContributionsReport;
