import React, { useState } from 'react';
import { Button, Label, TextInput, Checkbox, Alert } from 'flowbite-react';
import { HiOutlineExclamationCircle } from 'react-icons/hi';
import { PayrollPeriod, PayrollPeriodStatus } from '../../types/payroll';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface CreatePayrollPeriodFormProps {
  onSubmit: (data: Omit<PayrollPeriod, 'id' | 'organization_id' | 'created_at' | 'updated_at'>) => Promise<void>;
  isSubmitting: boolean;
  error?: string | null;
  onCancel: () => void;
}

const CreatePayrollPeriodForm: React.FC<CreatePayrollPeriodFormProps> = ({
  onSubmit,
  isSubmitting,
  error,
  onCancel,
}) => {
  const [name, setName] = useState<string>('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [paymentDate, setPaymentDate] = useState<Date | null>(null);
  const [isThirteenthMonth, setIsThirteenthMonth] = useState<boolean>(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!name.trim()) {
      errors.name = 'Period name is required';
    }

    if (!startDate) {
      errors.startDate = 'Start date is required';
    }

    if (!endDate) {
      errors.endDate = 'End date is required';
    } else if (startDate && endDate < startDate) {
      errors.endDate = 'End date must be after start date';
    }

    if (!paymentDate) {
      errors.paymentDate = 'Payment date is required';
    } else if (endDate && paymentDate < endDate) {
      errors.paymentDate = 'Payment date must be on or after end date';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!startDate || !endDate || !paymentDate) {
      return;
    }

    const periodData: Omit<PayrollPeriod, 'id' | 'organization_id' | 'created_at' | 'updated_at'> = {
      name,
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0],
      payment_date: paymentDate.toISOString().split('T')[0],
      status: PayrollPeriodStatus.DRAFT,
      is_thirteenth_month: isThirteenthMonth,
    };

    await onSubmit(periodData);
  };

  // Generate a period name suggestion based on selected dates
  const suggestPeriodName = () => {
    if (startDate && endDate) {
      const startMonth = startDate.toLocaleString('default', { month: 'long' });
      const startDay = startDate.getDate();
      const endMonth = endDate.toLocaleString('default', { month: 'long' });
      const endDay = endDate.getDate();
      const year = startDate.getFullYear();

      if (startMonth === endMonth) {
        setName(`${startMonth} ${startDay}-${endDay}, ${year}`);
      } else {
        setName(`${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`);
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <Alert color="failure" icon={HiOutlineExclamationCircle}>
          {error}
        </Alert>
      )}

      <div>
        <div className="mb-2 block">
          <Label htmlFor="name" value="Period Name" />
        </div>
        <TextInput
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="e.g., January 1-15, 2023"
          color={formErrors.name ? 'failure' : undefined}
          helperText={formErrors.name}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="startDate" value="Start Date" />
          </div>
          <DatePicker
            id="startDate"
            selected={startDate}
            onChange={(date: Date) => {
              setStartDate(date);
              if (date && (!endDate || endDate < date)) {
                // Set end date to 15 days after start date by default
                const newEndDate = new Date(date);
                newEndDate.setDate(date.getDate() + 14);
                setEndDate(newEndDate);
                
                // Set payment date to 5 days after end date by default
                const newPaymentDate = new Date(newEndDate);
                newPaymentDate.setDate(newEndDate.getDate() + 5);
                setPaymentDate(newPaymentDate);
              }
              // Suggest a period name after dates are selected
              setTimeout(suggestPeriodName, 100);
            }}
            dateFormat="MMMM d, yyyy"
            className={`w-full rounded-lg ${
              formErrors.startDate ? 'border-red-500' : 'border-gray-300'
            } p-2.5`}
            placeholderText="Select start date"
          />
          {formErrors.startDate && (
            <p className="mt-1 text-sm text-red-500">{formErrors.startDate}</p>
          )}
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="endDate" value="End Date" />
          </div>
          <DatePicker
            id="endDate"
            selected={endDate}
            onChange={(date: Date) => {
              setEndDate(date);
              // Suggest a period name after dates are selected
              setTimeout(suggestPeriodName, 100);
              
              // Update payment date if needed
              if (date && (!paymentDate || paymentDate < date)) {
                const newPaymentDate = new Date(date);
                newPaymentDate.setDate(date.getDate() + 5);
                setPaymentDate(newPaymentDate);
              }
            }}
            dateFormat="MMMM d, yyyy"
            className={`w-full rounded-lg ${
              formErrors.endDate ? 'border-red-500' : 'border-gray-300'
            } p-2.5`}
            placeholderText="Select end date"
            minDate={startDate || undefined}
          />
          {formErrors.endDate && (
            <p className="mt-1 text-sm text-red-500">{formErrors.endDate}</p>
          )}
        </div>
      </div>

      <div>
        <div className="mb-2 block">
          <Label htmlFor="paymentDate" value="Payment Date" />
        </div>
        <DatePicker
          id="paymentDate"
          selected={paymentDate}
          onChange={(date: Date) => setPaymentDate(date)}
          dateFormat="MMMM d, yyyy"
          className={`w-full rounded-lg ${
            formErrors.paymentDate ? 'border-red-500' : 'border-gray-300'
          } p-2.5`}
          placeholderText="Select payment date"
          minDate={endDate || undefined}
        />
        {formErrors.paymentDate && (
          <p className="mt-1 text-sm text-red-500">{formErrors.paymentDate}</p>
        )}
      </div>

      <div className="flex items-center gap-2">
        <Checkbox
          id="isThirteenthMonth"
          checked={isThirteenthMonth}
          onChange={(e) => setIsThirteenthMonth(e.target.checked)}
        />
        <Label htmlFor="isThirteenthMonth">
          This is a 13th month pay period
        </Label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button color="gray" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          type="submit"
          color="primary"
          isProcessing={isSubmitting}
          disabled={isSubmitting}
        >
          Create Payroll Period
        </Button>
      </div>
    </form>
  );
};

export default CreatePayrollPeriodForm;
