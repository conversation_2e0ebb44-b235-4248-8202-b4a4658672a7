import React from 'react';
import { formatCurrency } from '../../utils/formatters';

interface PayslipTemplateProps {
  employee: {
    name: string;
    employee_id: string;
    position?: string;
    department?: string;
  };
  payPeriod: {
    start_date: string;
    end_date: string;
    payment_date: string;
  };
  earnings: {
    basic_pay: number;
    overtime_pay?: number;
    holiday_pay?: number;
    night_differential?: number;
    allowances?: number;
  };
  deductions: {
    sss_contribution?: number;
    philhealth_contribution?: number;
    pagibig_contribution?: number;
    withholding_tax?: number;
    loans?: number;
    other_deductions?: number;
  };
  company: {
    name: string;
    address?: string;
    logo?: string;
    brand_color?: string;
  };
  template: {
    style: 'modern' | 'classic' | 'minimal' | 'detailed';
    show_company_address: boolean;
    include_qr_code: boolean;
    paper_size: 'a4' | 'letter' | 'legal';
  };
}

const PayslipTemplate: React.FC<PayslipTemplateProps> = ({
  employee,
  payPeriod,
  earnings,
  deductions,
  company,
  template
}) => {


  const totalEarnings = Object.values(earnings).reduce((sum, val) => sum + (val || 0), 0);
  const totalDeductions = Object.values(deductions).reduce((sum, val) => sum + (val || 0), 0);
  const netPay = totalEarnings - totalDeductions;

  const brandColor = company.brand_color || '#3B82F6';

  return (
    <div className="bg-white p-8 shadow-lg max-w-2xl mx-auto" style={{ fontFamily: 'Arial, sans-serif' }}>
      {/* Header */}
      <div className="border-b-2 pb-4 mb-6" style={{ borderColor: brandColor }}>
        <div className="flex justify-between items-start">
          <div>
            {company.logo && (
              <img src={company.logo} alt="Company Logo" className="h-16 mb-2" />
            )}
            <h1 className="text-2xl font-bold" style={{ color: brandColor }}>
              {company.name}
            </h1>
            {template.show_company_address && company.address && (
              <p className="text-sm text-gray-600 mt-1">{company.address}</p>
            )}
          </div>
          <div className="text-right">
            <h2 className="text-xl font-semibold text-gray-800">PAYSLIP</h2>
            <p className="text-sm text-gray-600">
              Pay Period: {new Date(payPeriod.start_date).toLocaleDateString()} to{' '}
              {new Date(payPeriod.end_date).toLocaleDateString()}
            </p>
            <p className="text-sm text-gray-600">
              Payment Date: {new Date(payPeriod.payment_date).toLocaleDateString()}
            </p>
          </div>
        </div>
      </div>

      {/* Employee Information */}
      <div className="grid grid-cols-2 gap-8 mb-6">
        <div>
          <h3 className="font-semibold text-gray-800 mb-2">Employee Information</h3>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Name:</span>
              <span className="font-medium">{employee.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Employee ID:</span>
              <span className="font-medium">{employee.employee_id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Position:</span>
              <span className="font-medium">
                {(employee.position && employee.position !== 'N/A' && employee.position !== 'null' && employee.position !== 'undefined')
                  ? employee.position
                  : 'Sales Associate'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Department:</span>
              <span className="font-medium">
                {(employee.department && employee.department !== 'N/A' && employee.department !== 'null' && employee.department !== 'undefined')
                  ? employee.department
                  : 'Sales'}
              </span>
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-semibold text-gray-800 mb-2">Payroll Information</h3>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Payroll Period:</span>
              <span className="font-medium">
                {new Date(payPeriod.start_date).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric'
                })} - {new Date(payPeriod.end_date).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric'
                })}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Payslip #:</span>
              <span className="font-medium">PS{Date.now().toString().slice(-6)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Earnings and Deductions */}
      <div className="grid grid-cols-2 gap-8 mb-6">
        {/* Earnings */}
        <div>
          <h3 className="font-semibold text-gray-800 mb-3 pb-1 border-b" style={{ borderColor: brandColor }}>
            Earnings
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Basic Pay</span>
              <span className="font-medium">{formatCurrency(earnings.basic_pay)}</span>
            </div>
            {earnings.overtime_pay && earnings.overtime_pay > 0 && (
              <div className="flex justify-between">
                <span>Overtime Pay</span>
                <span className="font-medium">{formatCurrency(earnings.overtime_pay)}</span>
              </div>
            )}
            {earnings.holiday_pay && earnings.holiday_pay > 0 && (
              <div className="flex justify-between">
                <span>Holiday Pay</span>
                <span className="font-medium">{formatCurrency(earnings.holiday_pay)}</span>
              </div>
            )}
            {earnings.night_differential && earnings.night_differential > 0 && (
              <div className="flex justify-between">
                <span>Night Differential</span>
                <span className="font-medium">{formatCurrency(earnings.night_differential)}</span>
              </div>
            )}
            {earnings.allowances && earnings.allowances > 0 && (
              <div className="flex justify-between">
                <span>Allowances</span>
                <span className="font-medium">{formatCurrency(earnings.allowances)}</span>
              </div>
            )}
            <div className="border-t pt-2 mt-2 flex justify-between font-semibold">
              <span>Total Earnings</span>
              <span>{formatCurrency(totalEarnings)}</span>
            </div>
          </div>
        </div>

        {/* Deductions */}
        <div>
          <h3 className="font-semibold text-gray-800 mb-3 pb-1 border-b" style={{ borderColor: brandColor }}>
            Deductions
          </h3>
          <div className="space-y-2 text-sm">
            {deductions.sss_contribution && deductions.sss_contribution > 0 && (
              <div className="flex justify-between">
                <span>SSS Contribution</span>
                <span className="font-medium">{formatCurrency(deductions.sss_contribution)}</span>
              </div>
            )}
            {deductions.philhealth_contribution && deductions.philhealth_contribution > 0 && (
              <div className="flex justify-between">
                <span>PhilHealth</span>
                <span className="font-medium">{formatCurrency(deductions.philhealth_contribution)}</span>
              </div>
            )}
            {deductions.pagibig_contribution && deductions.pagibig_contribution > 0 && (
              <div className="flex justify-between">
                <span>Pag-IBIG</span>
                <span className="font-medium">{formatCurrency(deductions.pagibig_contribution)}</span>
              </div>
            )}
            {deductions.withholding_tax && deductions.withholding_tax > 0 && (
              <div className="flex justify-between">
                <span>Withholding Tax</span>
                <span className="font-medium">{formatCurrency(deductions.withholding_tax)}</span>
              </div>
            )}
            {deductions.loans && deductions.loans > 0 && (
              <div className="flex justify-between">
                <span>Loans</span>
                <span className="font-medium">{formatCurrency(deductions.loans)}</span>
              </div>
            )}
            {deductions.other_deductions && deductions.other_deductions > 0 && (
              <div className="flex justify-between">
                <span>Other Deductions</span>
                <span className="font-medium">{formatCurrency(deductions.other_deductions)}</span>
              </div>
            )}
            <div className="border-t pt-2 mt-2 flex justify-between font-semibold">
              <span>Total Deductions</span>
              <span>{formatCurrency(totalDeductions)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Net Pay */}
      <div className="border-t-2 pt-4" style={{ borderColor: brandColor }}>
        <div className="flex justify-between items-center">
          <span className="text-lg font-semibold text-gray-800">Net Pay</span>
          <span className="text-2xl font-bold" style={{ color: brandColor }}>
            {formatCurrency(netPay)}
          </span>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 pt-4 border-t text-xs text-gray-500">
        <div className="flex justify-between items-center">
          <div>
            <p>This is a computer-generated payslip.</p>
            <p>For questions, contact HR Department.</p>
          </div>
          {template.include_qr_code && (
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-200 border border-gray-300 flex items-center justify-center text-xs">
                QR Code
              </div>
              <p className="mt-1">Scan to verify</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PayslipTemplate;
