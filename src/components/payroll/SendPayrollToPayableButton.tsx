import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Mo<PERSON> } from 'flowbite-react';
import {
  HiOutlinePaperAirplane,
  HiOutlineExclamation,
  HiOutlineCheckCircle
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { createPayablesFromPayroll, PayrollPayableOptions } from '../../services/payrollPayables';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { PayrollPeriodStatus } from '../../types/payroll';
import PayrollPayablesModal from './PayrollPayablesModal';

interface SendPayrollToPayableButtonProps {
  payrollPeriodId: string;
  payrollPeriodName: string;
  payrollStatus: PayrollPeriodStatus;
  totalNetPay: number;
  totalEmployees: number;
  payablesCreated?: boolean;
  onSuccess?: () => void;
  className?: string;
}

const SendPayrollToPayableButton: React.FC<SendPayrollToPayableButtonProps> = ({
  payrollPeriodId,
  payrollPeriodName,
  payrollStatus,
  totalNetPay,
  totalEmployees,
  payablesCreated = false,
  onSuccess,
  className = ''
}) => {
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const formatWithCurrency = useCurrencyFormatter();

  // State
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    summary?: any;
  } | null>(null);

  // Default options
  const [options, setOptions] = useState<PayrollPayableOptions>({
    createEmployeePayables: true,
    createGovernmentPayables: true,
    createThirdPartyPayables: false,
    groupByEmployee: true
  });

  // Check if button should be enabled
  const isEnabled = payrollStatus === PayrollPeriodStatus.APPROVED &&
                   !payablesCreated &&
                   currentOrganization &&
                   user;

  // Handle send to payables
  const handleSendToPayables = async () => {
    if (!currentOrganization || !user) return;

    setLoading(true);
    setResult(null);

    try {
      const { success, summary, error } = await createPayablesFromPayroll(
        currentOrganization.id,
        payrollPeriodId,
        user.id,
        options
      );

      if (error) {
        setResult({
          success: false,
          message: error
        });
      } else if (success && summary) {
        const totalPayables = summary.employeePayables + summary.governmentPayables + summary.thirdPartyPayables;
        setResult({
          success: true,
          message: `Successfully created ${totalPayables} payables totaling ${formatWithCurrency(summary.totalAmount)}`,
          summary
        });

        // Call success callback
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (err: any) {
      setResult({
        success: false,
        message: err.message || 'Failed to create payables'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle modal close
  const handleCloseModal = () => {
    setShowModal(false);
    setResult(null);
  };

  // If payables already created, show status badge
  if (payablesCreated) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
          <HiOutlineCheckCircle className="mr-1 h-4 w-4" />
          Sent to Payables
        </div>
      </div>
    );
  }

  return (
    <>
      <Button
        color={isEnabled ? 'primary' : 'gray'}
        size="sm"
        disabled={!isEnabled || loading}
        onClick={() => setShowModal(true)}
        className={className}
      >
        <HiOutlinePaperAirplane className="mr-2 h-4 w-4" />
        {loading ? 'Creating...' : 'Send to Payables'}
      </Button>

      <PayrollPayablesModal
        show={showModal}
        onClose={handleCloseModal}
        onConfirm={handleSendToPayables}
        payrollPeriodName={payrollPeriodName}
        totalNetPay={totalNetPay}
        totalEmployees={totalEmployees}
        options={options}
        onOptionsChange={setOptions}
        loading={loading}
        result={result}
      />
    </>
  );
};

export default SendPayrollToPayableButton;
