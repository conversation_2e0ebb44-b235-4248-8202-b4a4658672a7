import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Spinner, 
  Alert, 
  TextInput,
  Checkbox,
  Badge
} from 'flowbite-react';
import { 
  HiOutlineTag, 
  HiOutlinePlus, 
  HiOutlineX,
  HiOutlineSearch
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getOrganizationTags } from '../../services/tagService';
import { Tag } from '../../types/tagging.types';

interface BulkTagManagerProps {
  show: boolean;
  onClose: () => void;
  selectedIds: string[];
  entityType: 'product' | 'customer' | 'supplier';
  onApplyTags: (tagIds: string[], action: 'add' | 'remove') => Promise<void>;
}

const BulkTagManager: React.FC<BulkTagManagerProps> = ({
  show,
  onClose,
  selectedIds,
  entityType,
  onApplyTags
}) => {
  const { currentOrganization } = useOrganization();
  
  // State for tags
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [action, setAction] = useState<'add' | 'remove'>('add');
  
  // Fetch available tags when the component mounts
  useEffect(() => {
    if (!currentOrganization || !show) return;
    
    const fetchTags = async () => {
      setIsLoading(true);
      try {
        const { success, tags } = await getOrganizationTags(currentOrganization.id);
        if (success && tags) {
          setAvailableTags(tags);
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTags();
  }, [currentOrganization, show]);
  
  // Reset state when modal is opened
  useEffect(() => {
    if (show) {
      setSelectedTags([]);
      setSearchTerm('');
      setError(null);
      setAction('add');
    }
  }, [show]);
  
  // Filter tags based on search term
  const filteredTags = availableTags.filter(tag => 
    searchTerm === '' || 
    tag.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (tag.description && tag.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );
  
  // Toggle tag selection
  const toggleTagSelection = (tagId: string) => {
    setSelectedTags(prev => 
      prev.includes(tagId) 
        ? prev.filter(id => id !== tagId) 
        : [...prev, tagId]
    );
  };
  
  // Apply tags to selected entities
  const handleApplyTags = async () => {
    if (selectedTags.length === 0) {
      setError('Please select at least one tag');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      await onApplyTags(selectedTags, action);
      onClose();
    } catch (err: any) {
      setError(err.message || 'Failed to apply tags');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Modal show={show} onClose={onClose} size="md">
      <Modal.Header>
        Manage Tags for {selectedIds.length} {entityType}{selectedIds.length > 1 ? 's' : ''}
      </Modal.Header>
      <Modal.Body>
        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}
        
        <div className="mb-4">
          <div className="flex space-x-4 mb-4">
            <div className="flex items-center">
              <input
                type="radio"
                id="add-tags"
                name="tag-action"
                value="add"
                checked={action === 'add'}
                onChange={() => setAction('add')}
                className="mr-2"
              />
              <label htmlFor="add-tags">Add tags</label>
            </div>
            <div className="flex items-center">
              <input
                type="radio"
                id="remove-tags"
                name="tag-action"
                value="remove"
                checked={action === 'remove'}
                onChange={() => setAction('remove')}
                className="mr-2"
              />
              <label htmlFor="remove-tags">Remove tags</label>
            </div>
          </div>
          
          <TextInput
            type="text"
            placeholder="Search tags..."
            icon={HiOutlineSearch}
            onChange={(e) => setSearchTerm(e.target.value)}
            value={searchTerm}
            className="mb-4"
          />
          
          {isLoading ? (
            <div className="flex justify-center items-center p-4">
              <Spinner size="md" />
            </div>
          ) : filteredTags.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              <p>No matching tags found</p>
            </div>
          ) : (
            <div className="max-h-60 overflow-y-auto border rounded-lg p-2">
              {filteredTags.map(tag => (
                <div 
                  key={tag.id} 
                  className="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded cursor-pointer"
                  onClick={() => toggleTagSelection(tag.id)}
                >
                  <Checkbox
                    checked={selectedTags.includes(tag.id)}
                    onChange={() => {}}
                    className="mr-3"
                  />
                  <div 
                    className="w-3 h-3 rounded-full mr-2" 
                    style={{ backgroundColor: tag.color || '#3b82f6' }} 
                  />
                  <div className="flex-1">
                    <p className="font-medium">{tag.name}</p>
                    {tag.description && (
                      <p className="text-xs text-gray-500 truncate">{tag.description}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        <div className="mt-4">
          <p className="text-sm text-gray-500 mb-2">Selected tags ({selectedTags.length}):</p>
          <div className="flex flex-wrap gap-2">
            {selectedTags.length === 0 ? (
              <p className="text-sm text-gray-400 italic">No tags selected</p>
            ) : (
              selectedTags.map(tagId => {
                const tag = availableTags.find(t => t.id === tagId);
                return tag ? (
                  <Badge 
                    key={tag.id} 
                    color="light" 
                    className="flex items-center"
                    style={{ borderLeft: `3px solid ${tag.color || '#3b82f6'}` }}
                  >
                    {tag.name}
                    <button 
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleTagSelection(tag.id);
                      }}
                      className="ml-2 text-gray-500 hover:text-gray-700"
                    >
                      <HiOutlineX className="h-3 w-3" />
                    </button>
                  </Badge>
                ) : null;
              })
            )}
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="flex justify-end gap-2">
          <Button color="gray" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleApplyTags} 
            disabled={isSubmitting || selectedTags.length === 0}
          >
            {isSubmitting ? <Spinner size="sm" className="mr-2" /> : null}
            {action === 'add' ? 'Add' : 'Remove'} Tags
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default BulkTagManager;
