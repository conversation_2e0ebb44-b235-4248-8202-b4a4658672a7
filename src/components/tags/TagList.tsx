import React from 'react';
import { Badge, Tooltip } from 'flowbite-react';
import { Tag } from '../../types/tagging.types';

interface TagListProps {
  tags: Tag[];
  maxDisplay?: number;
  showTooltip?: boolean;
  className?: string;
  onClick?: (tag: Tag) => void;
}

const TagList: React.FC<TagListProps> = ({
  tags,
  maxDisplay = 3,
  showTooltip = true,
  className = '',
  onClick,
}) => {
  if (!tags || tags.length === 0) {
    return null;
  }

  // Get badge color based on tag color or default to blue
  const getBadgeColor = (color: string | null) => {
    if (!color) return 'blue';
    
    // Map common color names to Flowbite colors
    const colorMap: Record<string, string> = {
      red: 'red',
      green: 'green',
      blue: 'blue',
      yellow: 'yellow',
      purple: 'purple',
      pink: 'pink',
      indigo: 'indigo',
      gray: 'gray',
    };
    
    // Check if the color is in our map
    const lowerColor = color.toLowerCase();
    for (const [key, value] of Object.entries(colorMap)) {
      if (lowerColor.includes(key)) {
        return value;
      }
    }
    
    // Default to blue if no match
    return 'blue';
  };

  // Display tags up to maxDisplay, with a +X more indicator if needed
  const displayTags = tags.slice(0, maxDisplay);
  const remainingCount = tags.length - maxDisplay;

  return (
    <div className={`flex flex-wrap gap-1.5 ${className}`}>
      {displayTags.map((tag) => (
        <Badge
          key={tag.id}
          color={getBadgeColor(tag.color)}
          className={`text-xs ${onClick ? 'cursor-pointer' : ''}`}
          onClick={onClick ? () => onClick(tag) : undefined}
        >
          {tag.name}
        </Badge>
      ))}
      
      {remainingCount > 0 && (
        showTooltip ? (
          <Tooltip
            content={
              <div className="py-1">
                {tags.slice(maxDisplay).map((tag) => (
                  <div key={tag.id} className="px-2 py-0.5 text-sm">
                    {tag.name}
                  </div>
                ))}
              </div>
            }
          >
            <Badge color="gray" className="text-xs cursor-help">
              +{remainingCount} more
            </Badge>
          </Tooltip>
        ) : (
          <Badge color="gray" className="text-xs">
            +{remainingCount} more
          </Badge>
        )
      )}
    </div>
  );
};

export default TagList;
