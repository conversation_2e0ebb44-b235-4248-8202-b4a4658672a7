import React, { useState, useEffect, useRef } from 'react';
import { Button, TextInput, Badge } from 'flowbite-react';
import { HiOutlineTag, HiOutlineX, HiOutlineSearch, HiOutlineFilter } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getOrganizationTags } from '../../services/tagService';
import { Tag } from '../../types/tagging.types';

interface TagFilterProps {
  selectedTags: string[];
  onTagsChange: (tagIds: string[]) => void;
}

const TagFilter: React.FC<TagFilterProps> = ({ selectedTags, onTagsChange }) => {
  const { currentOrganization } = useOrganization();
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // Fetch available tags when the component mounts
  useEffect(() => {
    if (!currentOrganization) return;
    
    const fetchTags = async () => {
      setIsLoading(true);
      try {
        const { success, tags } = await getOrganizationTags(currentOrganization.id);
        if (success && tags) {
          setAvailableTags(tags);
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTags();
  }, [currentOrganization]);
  
  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Filter tags based on search term
  const filteredTags = availableTags.filter(tag => 
    searchTerm === '' || 
    tag.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (tag.description && tag.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );
  
  // Add a tag to the filter
  const handleAddTag = (tag: Tag) => {
    if (!selectedTags.includes(tag.id)) {
      onTagsChange([...selectedTags, tag.id]);
    }
    setSearchTerm('');
  };
  
  // Remove a tag from the filter
  const handleRemoveTag = (tagId: string) => {
    onTagsChange(selectedTags.filter(id => id !== tagId));
  };
  
  // Clear all selected tags
  const handleClearTags = () => {
    onTagsChange([]);
  };
  
  return (
    <div className="flex flex-col">
      <div className="flex items-center mb-2">
        <div className="relative" ref={dropdownRef}>
          <Button
            size="xs"
            color="light"
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="flex items-center text-xs"
          >
            <HiOutlineFilter className="mr-1 h-4 w-4" />
            Filter by Tag
          </Button>
          
          {isDropdownOpen && (
            <div className="absolute z-10 mt-2 w-64 bg-white rounded-lg shadow-lg dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <div className="p-3">
                <TextInput
                  type="text"
                  placeholder="Search tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="mb-2"
                  icon={HiOutlineSearch}
                />
                
                <div className="max-h-40 overflow-y-auto">
                  {isLoading ? (
                    <div className="text-center py-2 text-sm text-gray-500">
                      Loading tags...
                    </div>
                  ) : filteredTags.length > 0 ? (
                    <div className="py-1">
                      {filteredTags.map(tag => (
                        <button
                          key={tag.id}
                          onClick={() => {
                            handleAddTag(tag);
                          }}
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center ${
                            selectedTags.includes(tag.id) ? 'bg-blue-50 dark:bg-blue-900' : ''
                          }`}
                        >
                          <div 
                            className="w-3 h-3 rounded-full mr-2" 
                            style={{ backgroundColor: tag.color || '#3b82f6' }} 
                          />
                          <span>{tag.name}</span>
                        </button>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-2 text-sm text-gray-500">
                      No matching tags found
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
        
        {selectedTags.length > 0 && (
          <Button
            size="xs"
            color="light"
            onClick={handleClearTags}
            className="ml-2 text-xs"
          >
            Clear Filters
          </Button>
        )}
      </div>
      
      {selectedTags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-3">
          {selectedTags.map(tagId => {
            const tag = availableTags.find(t => t.id === tagId);
            return tag ? (
              <Badge 
                key={tag.id} 
                color="light" 
                className="flex items-center"
                style={{ borderLeft: `3px solid ${tag.color || '#3b82f6'}` }}
              >
                {tag.name}
                <button 
                  onClick={() => handleRemoveTag(tag.id)}
                  className="ml-2 text-gray-500 hover:text-gray-700"
                >
                  <HiOutlineX className="h-3 w-3" />
                </button>
              </Badge>
            ) : null;
          })}
        </div>
      )}
    </div>
  );
};

export default TagFilter;
