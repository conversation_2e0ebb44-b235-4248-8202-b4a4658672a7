import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  TextInput,
  Label,
  Alert,
  Spinner,
  Badge
} from 'flowbite-react';
import {
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineTag,
  HiOutlineExclamation,
  HiOutlineEye,
  HiOutlineSearch
} from 'react-icons/hi';
import { Link } from 'react-router-dom';
import { useOrganization } from '../../context/OrganizationContext';
import {
  getOrganizationTags,
  createTag,
  updateTag,
  deleteTag,
  getTagsWithCount
} from '../../services/tagService';
import { Tag, TagWithCount } from '../../types/tagging.types';
import Pagination from '../common/Pagination';

interface TagManagerProps {
  className?: string;
}

const TagManager: React.FC<TagManagerProps> = ({ className = '' }) => {
  const { currentOrganization } = useOrganization();

  // State for tags
  const [tags, setTags] = useState<TagWithCount[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State for tag form
  const [showTagModal, setShowTagModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [tagName, setTagName] = useState('');
  const [tagDescription, setTagDescription] = useState('');
  const [tagColor, setTagColor] = useState('#3b82f6'); // Default blue

  // State for delete confirmation
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [tagToDelete, setTagToDelete] = useState<Tag | null>(null);

  // Fetch tags when the component mounts or organization changes
  useEffect(() => {
    if (!currentOrganization) return;

    const fetchTags = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const { success, tags, error } = await getTagsWithCount(currentOrganization.id);

        if (success && tags) {
          setTags(tags);
        } else if (error) {
          setError(error);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch tags');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTags();
  }, [currentOrganization]);

  // Open the tag modal for creating a new tag
  const handleAddTag = () => {
    setEditingTag(null);
    setTagName('');
    setTagDescription('');
    setTagColor('#3b82f6');
    setShowTagModal(true);
  };

  // Open the tag modal for editing an existing tag
  const handleEditTag = (tag: Tag) => {
    setEditingTag(tag);
    setTagName(tag.name);
    setTagDescription(tag.description || '');
    setTagColor(tag.color || '#3b82f6');
    setShowTagModal(true);
  };

  // Open the delete confirmation modal
  const handleDeleteClick = (tag: Tag) => {
    setTagToDelete(tag);
    setShowDeleteModal(true);
  };

  // Submit the tag form (create or update)
  const handleSubmitTag = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization) return;

    setIsSubmitting(true);
    setError(null);

    try {
      if (editingTag) {
        // Update existing tag
        const { success, tag, error } = await updateTag(editingTag.id, {
          name: tagName,
          description: tagDescription || null,
          color: tagColor || null
        });

        if (success && tag) {
          setTags(prevTags =>
            prevTags.map(t =>
              t.id === tag.id ? { ...t, ...tag } : t
            )
          );
          setShowTagModal(false);
        } else if (error) {
          setError(error);
        }
      } else {
        // Create new tag
        const { success, tag, error } = await createTag({
          organization_id: currentOrganization.id,
          name: tagName,
          description: tagDescription || null,
          color: tagColor || null
        });

        if (success && tag) {
          setTags(prevTags => [...prevTags, { ...tag, usage_count: 0 }]);
          setShowTagModal(false);
        } else if (error) {
          setError(error);
        }
      }
    } catch (err: any) {
      setError(err.message || 'Failed to save tag');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Confirm tag deletion
  const handleConfirmDelete = async () => {
    if (!tagToDelete) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const { success, error } = await deleteTag(tagToDelete.id);

      if (success) {
        setTags(prevTags => prevTags.filter(t => t.id !== tagToDelete.id));
        setShowDeleteModal(false);
      } else if (error) {
        setError(error);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to delete tag');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter tags based on search term
  const filteredTags = tags.filter(tag =>
    searchTerm === '' ||
    tag.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (tag.description && tag.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Pagination logic
  const totalPages = Math.ceil(filteredTags.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentTags = filteredTags.slice(indexOfFirstItem, indexOfLastItem);

  // Predefined colors for the color picker
  const predefinedColors = [
    '#ef4444', // red
    '#f97316', // orange
    '#f59e0b', // amber
    '#eab308', // yellow
    '#84cc16', // lime
    '#22c55e', // green
    '#10b981', // emerald
    '#14b8a6', // teal
    '#06b6d4', // cyan
    '#0ea5e9', // sky
    '#3b82f6', // blue
    '#6366f1', // indigo
    '#8b5cf6', // violet
    '#a855f7', // purple
    '#d946ef', // fuchsia
    '#ec4899', // pink
    '#f43f5e', // rose
    '#6b7280', // gray
  ];

  return (
    <div className={className}>
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold flex items-center">
            <HiOutlineTag className="mr-2 h-5 w-5" />
            Tag Management
          </h2>
          <Button size="sm" onClick={handleAddTag}>
            <HiOutlinePlus className="mr-2 h-4 w-4" />
            Add Tag
          </Button>
        </div>

        <div className="mb-4">
          <TextInput
            type="text"
            placeholder="Search tags..."
            icon={HiOutlineSearch}
            onChange={(e) => setSearchTerm(e.target.value)}
            value={searchTerm}
          />
        </div>

        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}

        {isLoading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="xl" />
          </div>
        ) : tags.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <HiOutlineTag className="mx-auto h-12 w-12 mb-4 text-gray-400" />
            <p className="text-lg font-medium">No tags found</p>
            <p className="mt-1">Create your first tag to start organizing your data</p>
            <Button color="light" className="mt-4" onClick={handleAddTag}>
              <HiOutlinePlus className="mr-2 h-4 w-4" />
              Add Tag
            </Button>
          </div>
        ) : filteredTags.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <HiOutlineSearch className="mx-auto h-12 w-12 mb-4 text-gray-400" />
            <p className="text-lg font-medium">No matching tags found</p>
            <p className="mt-1">Try a different search term or create a new tag</p>
            <Button color="light" className="mt-4" onClick={() => setSearchTerm('')}>
              Clear Search
            </Button>
          </div>
        ) : (
          <Table>
            <Table.Head>
              <Table.HeadCell>Name</Table.HeadCell>
              <Table.HeadCell>Description</Table.HeadCell>
              <Table.HeadCell>Usage</Table.HeadCell>
              <Table.HeadCell>
                <span className="sr-only">Actions</span>
              </Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y">
              {currentTags.map(tag => (
                <Table.Row key={tag.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                  <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: tag.color || '#3b82f6' }}
                      />
                      {tag.usage_count > 0 ? (
                        <Link
                          to={`/settings/tags/${tag.id}/items`}
                          className="text-blue-600 hover:underline dark:text-blue-500"
                        >
                          {tag.name}
                        </Link>
                      ) : (
                        tag.name
                      )}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    {tag.description || <span className="text-gray-400 italic">No description</span>}
                  </Table.Cell>
                  <Table.Cell>
                    <Badge color="gray" className="font-normal">
                      {tag.usage_count} {tag.usage_count === 1 ? 'item' : 'items'}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <div className="flex space-x-2">
                      {tag.usage_count > 0 && (
                        <Link to={`/settings/tags/${tag.id}/items`}>
                          <Button color="info" size="xs">
                            <HiOutlineEye className="h-4 w-4 mr-1" />
                            View Items
                          </Button>
                        </Link>
                      )}
                      <Button color="light" size="xs" onClick={() => handleEditTag(tag)}>
                        <HiOutlinePencil className="h-4 w-4" />
                      </Button>
                      <Button color="failure" size="xs" onClick={() => handleDeleteClick(tag)}>
                        <HiOutlineTrash className="h-4 w-4" />
                      </Button>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        )}

        {/* Pagination */}
        {filteredTags.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={filteredTags.length}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            itemName="tags"
          />
        )}
      </Card>

      {/* Tag Form Modal */}
      <Modal show={showTagModal} onClose={() => setShowTagModal(false)}>
        <Modal.Header>
          {editingTag ? 'Edit Tag' : 'Create New Tag'}
        </Modal.Header>
        <Modal.Body>
          <form onSubmit={handleSubmitTag} className="space-y-4">
            <div>
              <Label htmlFor="tagName" value="Tag Name" />
              <TextInput
                id="tagName"
                value={tagName}
                onChange={(e) => setTagName(e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="tagDescription" value="Description (Optional)" />
              <TextInput
                id="tagDescription"
                value={tagDescription}
                onChange={(e) => setTagDescription(e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="tagColor" value="Color" />
              <div className="flex items-center space-x-2">
                <input
                  type="color"
                  id="tagColor"
                  value={tagColor}
                  onChange={(e) => setTagColor(e.target.value)}
                  className="w-10 h-10 border-0 p-0"
                />
                <div className="flex flex-wrap gap-2 mt-2">
                  {predefinedColors.map(color => (
                    <button
                      key={color}
                      type="button"
                      className={`w-6 h-6 rounded-full ${tagColor === color ? 'ring-2 ring-offset-2 ring-blue-500' : ''}`}
                      style={{ backgroundColor: color }}
                      onClick={() => setTagColor(color)}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button color="gray" onClick={() => setShowTagModal(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? <Spinner size="sm" className="mr-2" /> : null}
                {editingTag ? 'Update Tag' : 'Create Tag'}
              </Button>
            </div>
          </form>
        </Modal.Body>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onClose={() => setShowDeleteModal(false)} size="md">
        <Modal.Header>
          Confirm Deletion
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-red-500" />
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              Are you sure you want to delete the tag "{tagToDelete?.name}"?
            </h3>
            {tagToDelete && (tagToDelete as TagWithCount).usage_count > 0 && (
              <Alert color="warning" className="mb-4">
                This tag is used by {(tagToDelete as TagWithCount).usage_count} items.
                Deleting it will remove the tag from all items.
              </Alert>
            )}
            <div className="flex justify-center gap-4">
              <Button color="gray" onClick={() => setShowDeleteModal(false)}>
                Cancel
              </Button>
              <Button color="failure" onClick={handleConfirmDelete} disabled={isSubmitting}>
                {isSubmitting ? <Spinner size="sm" className="mr-2" /> : null}
                Delete Tag
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default TagManager;
