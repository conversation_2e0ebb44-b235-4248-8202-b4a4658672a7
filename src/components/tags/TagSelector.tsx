import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Button, TextInput } from 'flowbite-react';
import { HiOutlineTag, HiOutlinePlus, HiOutlineX } from 'react-icons/hi';
import { Tag, TaggableEntityType } from '../../types/tagging.types';
import { getOrganizationTags, addTagToEntity, removeTagFromEntity } from '../../services/tagService';
import { useOrganization } from '../../context/OrganizationContext';

interface TagSelectorProps {
  entityType: TaggableEntityType;
  entityId: string;
  selectedTags: Tag[];
  onTagsChange?: (tags: Tag[]) => void;
  readOnly?: boolean;
  className?: string;
}

const TagSelector: React.FC<TagSelectorProps> = ({
  entityType,
  entityId,
  selectedTags,
  onTagsChange,
  readOnly = false,
  className = '',
}) => {
  const { currentOrganization } = useOrganization();
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Fetch available tags when the component mounts
  useEffect(() => {
    if (!currentOrganization) return;

    const fetchTags = async () => {
      setIsLoading(true);
      try {
        const { success, tags } = await getOrganizationTags(currentOrganization.id);
        if (success && tags) {
          setAvailableTags(tags);
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTags();
  }, [currentOrganization]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Filter tags based on search term and already selected tags
  const filteredTags = availableTags.filter(tag =>
    tag.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    !selectedTags.some(selectedTag => selectedTag.id === tag.id)
  );

  // Add a tag to the entity
  const handleAddTag = async (tag: Tag) => {
    if (!currentOrganization || readOnly) return;

    try {
      const { success } = await addTagToEntity({
        tag_id: tag.id,
        entity_type: entityType,
        entity_id: entityId,
      });

      if (success) {
        const updatedTags = [...selectedTags, tag];
        if (onTagsChange) {
          onTagsChange(updatedTags);
        }
      }
    } catch (error) {
      console.error('Error adding tag:', error);
    }
  };

  // Remove a tag from the entity
  const handleRemoveTag = async (tagId: string) => {
    if (!currentOrganization || readOnly) return;

    try {
      const { success } = await removeTagFromEntity({
        tag_id: tagId,
        entity_type: entityType,
        entity_id: entityId,
      });

      if (success) {
        const updatedTags = selectedTags.filter(tag => tag.id !== tagId);
        if (onTagsChange) {
          onTagsChange(updatedTags);
        }
      }
    } catch (error) {
      console.error('Error removing tag:', error);
    }
  };

  // Get badge color based on tag color or default to blue
  const getBadgeColor = (color: string | null) => {
    if (!color) return 'blue';

    // Map common color names to Flowbite colors
    const colorMap: Record<string, string> = {
      red: 'red',
      green: 'green',
      blue: 'blue',
      yellow: 'yellow',
      purple: 'purple',
      pink: 'pink',
      indigo: 'indigo',
      gray: 'gray',
    };

    // Check if the color is in our map
    const lowerColor = color.toLowerCase();
    for (const [key, value] of Object.entries(colorMap)) {
      if (lowerColor.includes(key)) {
        return value;
      }
    }

    // Default to blue if no match
    return 'blue';
  };

  return (
    <div className={`flex flex-col space-y-2 ${className}`}>
      {/* Display selected tags */}
      <div className="flex flex-wrap gap-2">
        {selectedTags.map(tag => (
          <Badge
            key={tag.id}
            color={getBadgeColor(tag.color)}
            className="flex items-center"
          >
            {tag.name}
            {!readOnly && (
              <button
                onClick={() => handleRemoveTag(tag.id)}
                className="ml-1 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
                aria-label={`Remove ${tag.name} tag`}
              >
                <HiOutlineX className="h-3 w-3" />
              </button>
            )}
          </Badge>
        ))}
      </div>

      {/* Tag selector dropdown */}
      {!readOnly && (
        <div className="relative" ref={dropdownRef}>
          <Button
            size="xs"
            color="light"
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="flex items-center text-xs"
          >
            <HiOutlineTag className="mr-1 h-4 w-4" />
            Add Tags
          </Button>

          {isDropdownOpen && (
            <div className="absolute z-10 mt-2 w-64 bg-white rounded-lg shadow-lg dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <div className="p-3">
                <TextInput
                  type="text"
                  placeholder="Search tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="mb-2"
                />

                <div className="max-h-40 overflow-y-auto">
                  {isLoading ? (
                    <div className="text-center py-2 text-sm text-gray-500">
                      Loading tags...
                    </div>
                  ) : filteredTags.length > 0 ? (
                    <div className="py-1">
                      {filteredTags.map(tag => (
                        <button
                          key={tag.id}
                          onClick={() => {
                            handleAddTag(tag);
                            setSearchTerm('');
                          }}
                          className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 flex items-center"
                        >
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: tag.color || '#3b82f6' }}
                          />
                          <span>{tag.name}</span>
                        </button>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-2 text-sm text-gray-500">
                      No matching tags found
                    </div>
                  )}
                </div>

                <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    size="xs"
                    color="light"
                    className="w-full text-xs"
                    onClick={() => {
                      // This would open a modal to create a new tag
                      console.log('Create new tag');
                      // Close the dropdown
                      setIsDropdownOpen(false);
                      // Navigate to tag management page
                      window.open('/settings/tags', '_blank');
                    }}
                  >
                    <HiOutlinePlus className="mr-1 h-3 w-3" />
                    Create New Tag
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TagSelector;
