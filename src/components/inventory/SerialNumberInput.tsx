import { useState } from 'react';
import { Button, TextInput, Badge, Modal, Alert } from 'flowbite-react';
import { HiOutlinePlus, HiOutlineTrash, HiOutlineExclamation } from 'react-icons/hi';

interface SerialNumberInputProps {
  serialNumbers: string[];
  onChange: (serialNumbers: string[]) => void;
  quantity: number;
}

const SerialNumberInput: React.FC<SerialNumberInputProps> = ({
  serialNumbers,
  onChange,
  quantity
}) => {
  const [showModal, setShowModal] = useState(false);
  const [newSerialNumber, setNewSerialNumber] = useState('');
  const [error, setError] = useState<string | null>(null);

  const handleAddSerialNumber = () => {
    if (!newSerialNumber.trim()) {
      setError('Serial number cannot be empty');
      return;
    }

    if (serialNumbers.includes(newSerialNumber)) {
      setError('Serial number already exists');
      return;
    }

    if (serialNumbers.length >= quantity) {
      setError(`Cannot add more than ${quantity} serial numbers`);
      return;
    }

    onChange([...serialNumbers, newSerialNumber]);
    setNewSerialNumber('');
    setError(null);
  };

  const handleRemoveSerialNumber = (index: number) => {
    const newSerialNumbers = [...serialNumbers];
    newSerialNumbers.splice(index, 1);
    onChange(newSerialNumbers);
  };

  const handleBulkImport = (text: string) => {
    const lines = text.split('\n').filter(line => line.trim());
    const uniqueLines = [...new Set(lines)];
    
    // Check if adding these would exceed the quantity
    if (uniqueLines.length > quantity) {
      setError(`Cannot add more than ${quantity} serial numbers`);
      return;
    }

    // Check for duplicates with existing serial numbers
    const existingSet = new Set(serialNumbers);
    const duplicates = uniqueLines.filter(sn => existingSet.has(sn));
    
    if (duplicates.length > 0) {
      setError(`Duplicate serial numbers found: ${duplicates.join(', ')}`);
      return;
    }

    onChange([...serialNumbers, ...uniqueLines]);
    setShowModal(false);
    setError(null);
  };

  return (
    <div>
      <div className="flex items-center gap-2 mb-2">
        <Button 
          size="xs" 
          color="primary" 
          onClick={() => setShowModal(true)}
          disabled={serialNumbers.length >= quantity}
        >
          <HiOutlinePlus className="mr-1 h-4 w-4" />
          Manage Serial Numbers
        </Button>
        <span className="text-sm text-gray-500">
          {serialNumbers.length} of {quantity} entered
        </span>
      </div>

      {serialNumbers.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {serialNumbers.map((sn, index) => (
            <Badge 
              key={index} 
              color="info"
              className="flex items-center"
            >
              {sn}
              <button
                onClick={() => handleRemoveSerialNumber(index)}
                className="ml-2 text-xs"
              >
                <HiOutlineTrash className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}

      <Modal show={showModal} onClose={() => setShowModal(false)}>
        <Modal.Header>Manage Serial Numbers</Modal.Header>
        <Modal.Body>
          {error && (
            <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
              {error}
            </Alert>
          )}

          <div className="mb-4">
            <h3 className="text-sm font-medium mb-2">Add Individual Serial Number</h3>
            <div className="flex gap-2">
              <TextInput
                type="text"
                value={newSerialNumber}
                onChange={(e) => setNewSerialNumber(e.target.value)}
                placeholder="Enter serial number"
                className="flex-1"
              />
              <Button 
                color="primary" 
                onClick={handleAddSerialNumber}
                disabled={serialNumbers.length >= quantity}
              >
                <HiOutlinePlus className="h-5 w-5" />
              </Button>
            </div>
          </div>

          <div className="mb-4">
            <h3 className="text-sm font-medium mb-2">Current Serial Numbers ({serialNumbers.length} of {quantity})</h3>
            <div className="max-h-40 overflow-y-auto p-2 bg-gray-50 rounded">
              {serialNumbers.length === 0 ? (
                <p className="text-gray-500 text-center">No serial numbers added yet</p>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {serialNumbers.map((sn, index) => (
                    <Badge 
                      key={index} 
                      color="info"
                      className="flex items-center"
                    >
                      {sn}
                      <button
                        onClick={() => handleRemoveSerialNumber(index)}
                        className="ml-2 text-xs"
                      >
                        <HiOutlineTrash className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">Bulk Import</h3>
            <p className="text-xs text-gray-500 mb-2">
              Enter one serial number per line. Maximum {quantity} serial numbers.
            </p>
            <textarea
              className="w-full p-2 border border-gray-300 rounded"
              rows={5}
              placeholder="SN001&#10;SN002&#10;SN003"
              onChange={(e) => {
                // Clear error when user starts typing
                if (error) setError(null);
              }}
            />
            <div className="mt-2">
              <Button 
                size="sm"
                onClick={(e) => {
                  const textarea = e.currentTarget.previousSibling as HTMLTextAreaElement;
                  handleBulkImport(textarea.value);
                }}
              >
                Import
              </Button>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowModal(false)}>Close</Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default SerialNumberInput;
