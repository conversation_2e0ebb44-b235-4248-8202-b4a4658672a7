import { useState, useEffect } from 'react';
import {
  Card,
  <PERSON>ton,
  Spinner,
  Alert,
  TextInput,
  Textarea,
  Label,
  Select,
  Checkbox,
  Radio,
  Badge
} from 'flowbite-react';
import {
  HiOutlineExclamation,
  HiOutlineCheck,
  HiOutlineX,
  HiOutlineClipboardCheck,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import {
  getQcChecklistTemplateById,
  getQcChecklistTemplatesForProduct,
  QcChecklistTemplateWithItems
} from '../../services/qcChecklist';

interface QcInspectionFormProps {
  productId: string;
  onComplete: (results: any) => void;
  onCancel: () => void;
}

const QcInspectionForm: React.FC<QcInspectionFormProps> = ({
  productId,
  onComplete,
  onCancel
}) => {
  const { currentOrganization } = useOrganization();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [templates, setTemplates] = useState<any[]>([]);
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');
  const [selectedTemplate, setSelectedTemplate] = useState<QcChecklistTemplateWithItems | null>(null);
  const [results, setResults] = useState<any[]>([]);
  const [notes, setNotes] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (currentOrganization && productId) {
      fetchTemplates();
    }
  }, [currentOrganization, productId]);

  useEffect(() => {
    if (selectedTemplateId) {
      fetchTemplateDetails();
    } else {
      setSelectedTemplate(null);
      setResults([]);
    }
  }, [selectedTemplateId]);

  const fetchTemplates = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const { templates: templateList, error: templatesError } = await getQcChecklistTemplatesForProduct(
        currentOrganization.id,
        productId
      );

      if (templatesError) {
        setError(templatesError);
      } else {
        setTemplates(templateList);
        
        // If there's only one template, select it automatically
        if (templateList.length === 1) {
          setSelectedTemplateId(templateList[0].id);
        }
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching QC checklist templates');
    } finally {
      setLoading(false);
    }
  };

  const fetchTemplateDetails = async () => {
    if (!currentOrganization || !selectedTemplateId) return;

    setLoading(true);
    setError(null);

    try {
      const { template, error: templateError } = await getQcChecklistTemplateById(
        currentOrganization.id,
        selectedTemplateId
      );

      if (templateError) {
        setError(templateError);
      } else if (template) {
        setSelectedTemplate(template);
        
        // Initialize results
        const initialResults = template.items.map(item => ({
          checklist_item_id: item.id,
          result_value: item.item_type === 'boolean' ? 'false' : '',
          numeric_value: null,
          is_passed: false,
          notes: ''
        }));
        
        setResults(initialResults);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching QC checklist template details');
    } finally {
      setLoading(false);
    }
  };

  const handleResultChange = (index: number, field: string, value: any) => {
    const newResults = [...results];
    newResults[index] = { ...newResults[index], [field]: value };
    
    // If this is a numeric value, update the numeric_value field
    if (field === 'result_value' && selectedTemplate?.items[index].item_type === 'numeric') {
      newResults[index].numeric_value = value ? parseFloat(value) : null;
    }
    
    // Evaluate if the result passes based on criteria
    const item = selectedTemplate?.items[index];
    if (item) {
      let isPassed = true;
      
      if (item.pass_criteria) {
        try {
          const criteria = JSON.parse(item.pass_criteria);
          
          switch (item.item_type) {
            case 'boolean':
              isPassed = value === (criteria.expected || 'true');
              break;
              
            case 'numeric':
              const numValue = parseFloat(value);
              if (criteria.min !== undefined && numValue < criteria.min) {
                isPassed = false;
              }
              if (criteria.max !== undefined && numValue > criteria.max) {
                isPassed = false;
              }
              break;
              
            case 'select':
              if (criteria.passing_values) {
                isPassed = criteria.passing_values.includes(value);
              }
              break;
              
            case 'text':
              if (criteria.pattern) {
                const regex = new RegExp(criteria.pattern);
                isPassed = regex.test(value);
              }
              break;
          }
        } catch (err) {
          console.error('Error parsing pass criteria:', err);
        }
      }
      
      newResults[index].is_passed = isPassed;
    }
    
    setResults(newResults);
  };

  const handleSubmit = () => {
    if (!selectedTemplate) return;
    
    // Validate that all required items have values
    const requiredItems = selectedTemplate.items.filter(item => item.is_required);
    const missingRequired = requiredItems.find((item, index) => {
      const result = results[index];
      return !result.result_value && result.result_value !== false;
    });
    
    if (missingRequired) {
      setError(`Please complete all required fields: ${missingRequired.name}`);
      return;
    }
    
    // Calculate overall pass/fail status
    const failedItems = results.filter(result => !result.is_passed);
    const status = failedItems.length > 0 ? 'failed' : 'passed';
    
    // Prepare the inspection data
    const inspectionData = {
      template_id: selectedTemplate.id,
      status,
      notes,
      results
    };
    
    onComplete(inspectionData);
  };

  if (loading && !selectedTemplate) {
    return (
      <div className="flex justify-center items-center py-8">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <Card>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h2 className="text-xl font-bold flex items-center">
            <HiOutlineClipboardCheck className="mr-2 h-6 w-6" />
            Quality Control Inspection
          </h2>
        </div>
      </div>

      {error && (
        <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
          {error}
        </Alert>
      )}

      <div className="space-y-6">
        {templates.length === 0 ? (
          <Alert color="info" icon={HiOutlineInformationCircle}>
            No QC checklist templates are assigned to this product.
          </Alert>
        ) : (
          <>
            <div>
              <Label htmlFor="templateSelect" value="Select Checklist Template" />
              <Select
                id="templateSelect"
                value={selectedTemplateId}
                onChange={(e) => setSelectedTemplateId(e.target.value)}
                required
              >
                <option value="">Select a template...</option>
                {templates.map((template) => (
                  <option key={template.id} value={template.id}>
                    {template.name}
                  </option>
                ))}
              </Select>
            </div>

            {selectedTemplate && (
              <>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-medium mb-2">{selectedTemplate.name}</h3>
                  {selectedTemplate.description && (
                    <p className="text-gray-700 mb-4">{selectedTemplate.description}</p>
                  )}

                  <div className="space-y-6">
                    {selectedTemplate.items.map((item, index) => (
                      <div key={item.id} className="p-4 bg-white rounded-lg shadow-sm">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium">
                              {item.name}
                              {item.is_required && <span className="text-red-500 ml-1">*</span>}
                            </h4>
                            {item.description && (
                              <p className="text-sm text-gray-500">{item.description}</p>
                            )}
                          </div>
                          {results[index] && (
                            <Badge
                              color={results[index].is_passed ? 'success' : 'failure'}
                              icon={results[index].is_passed ? HiOutlineCheck : HiOutlineX}
                            >
                              {results[index].is_passed ? 'Pass' : 'Fail'}
                            </Badge>
                          )}
                        </div>

                        <div className="mt-3">
                          {item.item_type === 'boolean' && (
                            <div className="flex gap-4">
                              <div className="flex items-center">
                                <Radio
                                  id={`${item.id}-true`}
                                  name={`item-${item.id}`}
                                  value="true"
                                  checked={results[index]?.result_value === 'true'}
                                  onChange={() => handleResultChange(index, 'result_value', 'true')}
                                />
                                <Label htmlFor={`${item.id}-true`} className="ml-2">
                                  Yes
                                </Label>
                              </div>
                              <div className="flex items-center">
                                <Radio
                                  id={`${item.id}-false`}
                                  name={`item-${item.id}`}
                                  value="false"
                                  checked={results[index]?.result_value === 'false'}
                                  onChange={() => handleResultChange(index, 'result_value', 'false')}
                                />
                                <Label htmlFor={`${item.id}-false`} className="ml-2">
                                  No
                                </Label>
                              </div>
                            </div>
                          )}

                          {item.item_type === 'numeric' && (
                            <div className="flex items-center gap-2">
                              <TextInput
                                type="number"
                                value={results[index]?.result_value || ''}
                                onChange={(e) => handleResultChange(index, 'result_value', e.target.value)}
                                required={item.is_required}
                                className="flex-1"
                              />
                              {item.unit && (
                                <span className="text-gray-500">{item.unit}</span>
                              )}
                              {item.min_value !== null && item.max_value !== null && (
                                <span className="text-xs text-gray-500">
                                  ({item.min_value} - {item.max_value})
                                </span>
                              )}
                            </div>
                          )}

                          {item.item_type === 'text' && (
                            <TextInput
                              type="text"
                              value={results[index]?.result_value || ''}
                              onChange={(e) => handleResultChange(index, 'result_value', e.target.value)}
                              required={item.is_required}
                            />
                          )}

                          {item.item_type === 'select' && item.options && (
                            <Select
                              value={results[index]?.result_value || ''}
                              onChange={(e) => handleResultChange(index, 'result_value', e.target.value)}
                              required={item.is_required}
                            >
                              <option value="">Select an option...</option>
                              {JSON.parse(item.options).map((option: string) => (
                                <option key={option} value={option}>
                                  {option}
                                </option>
                              ))}
                            </Select>
                          )}

                          <div className="mt-2">
                            <Label htmlFor={`notes-${item.id}`} value="Notes" className="text-sm" />
                            <TextInput
                              id={`notes-${item.id}`}
                              type="text"
                              value={results[index]?.notes || ''}
                              onChange={(e) => handleResultChange(index, 'notes', e.target.value)}
                              placeholder="Add any observations or notes..."
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label htmlFor="inspectionNotes" value="Overall Inspection Notes" />
                  <Textarea
                    id="inspectionNotes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={3}
                    placeholder="Add any general notes about this inspection..."
                  />
                </div>
              </>
            )}
          </>
        )}
      </div>

      <div className="flex justify-end gap-4 mt-6">
        <Button color="gray" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          color="primary"
          onClick={handleSubmit}
          disabled={isSubmitting || !selectedTemplate || templates.length === 0}
        >
          {isSubmitting ? <Spinner size="sm" className="mr-2" /> : null}
          Complete Inspection
        </Button>
      </div>
    </Card>
  );
};

export default QcInspectionForm;
