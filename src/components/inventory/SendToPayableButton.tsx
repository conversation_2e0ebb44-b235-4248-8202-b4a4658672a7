import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Mo<PERSON> } from 'flowbite-react';
import {
  HiOutlinePaperAirplane,
  HiOutlineExclamation,
  HiOutlineCheckCircle
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { createPayableFromReceipt } from '../../services/payables';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

interface SendToPayableButtonProps {
  receiptId: string;
  receiptNumber: string;
  receiptStatus: string;
  onSuccess?: () => void;
  className?: string;
}

const SendToPayableButton: React.FC<SendToPayableButtonProps> = ({
  receiptId,
  receiptNumber,
  receiptStatus,
  onSuccess,
  className = ''
}) => {
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const formatWithCurrency = useCurrencyFormatter();

  // State
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    payable?: any;
  } | null>(null);

  // Check if button should be enabled
  const isEnabled = receiptStatus === 'completed' && currentOrganization && user;

  // Handle send to payable
  const handleSendToPayable = async () => {
    if (!currentOrganization || !user) return;

    setLoading(true);
    setResult(null);

    try {
      const { success, payable, error } = await createPayableFromReceipt(
        currentOrganization.id,
        receiptId,
        user.id
      );

      if (error) {
        setResult({
          success: false,
          message: error
        });
      } else if (success && payable) {
        setResult({
          success: true,
          message: `Payable created successfully with reference: ${payable.reference_number}`,
          payable: payable
        });

        // Call success callback
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (err: any) {
      setResult({
        success: false,
        message: err.message || 'Failed to create payable'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle modal close
  const handleCloseModal = () => {
    setShowModal(false);
    setResult(null);
  };

  return (
    <>
      <Button
        color={isEnabled ? 'primary' : 'gray'}
        size="sm"
        disabled={!isEnabled || loading}
        onClick={() => setShowModal(true)}
        className={className}
      >
        <HiOutlinePaperAirplane className="mr-2 h-4 w-4" />
        {loading ? 'Sending...' : 'Send to Payable'}
      </Button>

      <Modal show={showModal} onClose={handleCloseModal} size="md">
        <Modal.Header>
          <div className="flex items-center gap-2">
            <HiOutlinePaperAirplane className="h-5 w-5 text-blue-600" />
            Send Receipt to Payable
          </div>
        </Modal.Header>

        <Modal.Body>
          {!result ? (
            <div className="space-y-4">
              <div className="flex items-start gap-3 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <HiOutlineExclamation className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-yellow-800">Confirm Action</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    This will manually create a payable from receipt <strong>{receiptNumber}</strong>.
                  </p>
                  <p className="text-sm text-yellow-700 mt-2">
                    The payable will be created with:
                  </p>
                  <ul className="text-sm text-yellow-700 mt-1 ml-4 list-disc">
                    <li>Amount calculated from receipt items</li>
                    <li>VAT based on business settings</li>
                    <li>Due date based on supplier payment terms</li>
                    <li>Reference number: INV-{receiptNumber}</li>
                  </ul>
                </div>
              </div>

              {receiptStatus !== 'completed' && (
                <Alert color="warning">
                  Receipt must be completed before creating a payable.
                </Alert>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {result.success ? (
                <div className="flex items-start gap-3 p-4 bg-green-50 rounded-lg border border-green-200">
                  <HiOutlineCheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-green-800">Success!</h4>
                    <p className="text-sm text-green-700 mt-1">
                      {result.message}
                    </p>
                    {result.payable && (
                      <div className="mt-3 text-sm text-green-700">
                        <p><strong>Amount:</strong> {formatWithCurrency(result.payable.amount)}</p>
                        <p><strong>VAT:</strong> {formatWithCurrency(result.payable.vat_amount)}</p>
                        <p><strong>Due Date:</strong> {new Date(result.payable.due_date).toLocaleDateString()}</p>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <Alert color="failure">
                  <div className="flex items-start gap-2">
                    <HiOutlineExclamation className="h-5 w-5 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium">Error Creating Payable</h4>
                      <p className="text-sm mt-1">{result.message}</p>
                    </div>
                  </div>
                </Alert>
              )}
            </div>
          )}
        </Modal.Body>

        <Modal.Footer>
          <div className="flex justify-end gap-2 w-full">
            {!result ? (
              <>
                <Button
                  color="light"
                  onClick={handleCloseModal}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  color="primary"
                  onClick={handleSendToPayable}
                  disabled={loading || receiptStatus !== 'completed'}
                >
                  <HiOutlinePaperAirplane className="mr-2 h-4 w-4" />
                  {loading ? 'Sending to Enhanced Payables...' : 'Send to Enhanced Payables'}
                </Button>
              </>
            ) : (
              <Button
                color={result.success ? 'success' : 'light'}
                onClick={handleCloseModal}
              >
                {result.success ? 'Sent to Enhanced Payables!' : 'Close'}
              </Button>
            )}
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default SendToPayableButton;
