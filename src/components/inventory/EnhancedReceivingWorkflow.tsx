// Enhanced Receiving Workflow Component
// Comprehensive component for handling partial, over, damaged, and rejected items

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Badge,
  Table,
  TextInput,
  Textarea,
  Select,
  Alert,
  Modal,
  Spinner,
  Progress,
  Tabs
} from 'flowbite-react';
import {
  HiOutlineCheck,
  HiOutlineX,
  HiOutlineExclamation,
  HiOutlineClipboardCheck,
  HiOutlineEye,
  HiOutlineTruck,
  HiOutlineDocumentReport,
  HiOutlineChartBar
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import EnhancedNumberInput from '../common/EnhancedNumberInput';
import {
  getEnhancedReceiptItems,
  calculateReceivingMetrics,
  processBatchReceiving,
  validateReceivingActions,
  calculatePayableAmount,
  getReceivingStatusSummary
} from '../../services/receiving';
import {
  EnhancedReceiptItem,
  ReceivingFormItem,
  ReceivingAction,
  ReceivingMetrics,
  PayableCalculation,
  ReceivingStatusSummary
} from '../../types/receiving.types';

interface EnhancedReceivingWorkflowProps {
  receiptId: string;
  onComplete?: (payableAmount: number) => void;
  onError?: (error: string) => void;
}

const EnhancedReceivingWorkflow: React.FC<EnhancedReceivingWorkflowProps> = ({
  receiptId,
  onComplete,
  onError
}) => {
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();

  // State management
  const [items, setItems] = useState<ReceivingFormItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Metrics and calculations
  const [metrics, setMetrics] = useState<ReceivingMetrics | null>(null);
  const [payableCalc, setPayableCalc] = useState<PayableCalculation | null>(null);
  const [statusSummary, setStatusSummary] = useState<ReceivingStatusSummary | null>(null);

  // UI state
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [showBatchModal, setShowBatchModal] = useState(false);
  const [showDiscrepancyModal, setShowDiscrepancyModal] = useState(false);
  const [activeTab, setActiveTab] = useState('receiving');

  // Form state
  const [batchAction, setBatchAction] = useState<'receive' | 'accept' | 'reject'>('receive');
  const [batchNotes, setBatchNotes] = useState('');

  useEffect(() => {
    if (currentOrganization && receiptId) {
      loadReceivingData();
    }
  }, [currentOrganization, receiptId]);

  const loadReceivingData = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      // Load items
      const { items: receiptItems, error: itemsError } = await getEnhancedReceiptItems(
        currentOrganization.id,
        receiptId
      );

      if (itemsError) {
        setError(itemsError);
        return;
      }

      // Convert to form items
      const formItems: ReceivingFormItem[] = receiptItems.map(item => ({
        ...item,
        isSelected: false,
        isEditing: false,
        hasChanges: false,
        validationErrors: [],
        temp_received_quantity: item.received_quantity,
        temp_accepted_quantity: item.accepted_quantity,
        temp_rejected_quantity: item.rejected_quantity
      }));

      setItems(formItems);

      // Load metrics
      const { metrics: receivingMetrics } = await calculateReceivingMetrics(
        currentOrganization.id,
        receiptId
      );
      setMetrics(receivingMetrics);

      // Load payable calculation
      const payableCalculation = await calculatePayableAmount(
        currentOrganization.id,
        receiptId
      );
      setPayableCalc(payableCalculation);

      // Load status summary
      const { summary } = await getReceivingStatusSummary(
        currentOrganization.id,
        receiptId
      );
      setStatusSummary(summary);

    } catch (err: any) {
      setError(err.message || 'Failed to load receiving data');
      onError?.(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleItemQuantityChange = (itemId: string, field: string, value: number) => {
    setItems(prev => prev.map(item => {
      if (item.id === itemId) {
        const updated = {
          ...item,
          [field]: value,
          hasChanges: true
        };

        // Validate the change
        const errors = validateItemQuantities(updated);
        updated.validationErrors = errors;

        return updated;
      }
      return item;
    }));
  };

  const validateItemQuantities = (item: ReceivingFormItem): string[] => {
    const errors: string[] = [];
    const expectedQty = item.expected_quantity || item.quantity;
    const receivedQty = item.temp_received_quantity || 0;
    const acceptedQty = item.temp_accepted_quantity || 0;
    const rejectedQty = item.temp_rejected_quantity || 0;

    // Basic validations
    if (receivedQty < 0) errors.push('Received quantity cannot be negative');
    if (acceptedQty < 0) errors.push('Accepted quantity cannot be negative');
    if (rejectedQty < 0) errors.push('Rejected quantity cannot be negative');

    // Logical validations
    if (acceptedQty + rejectedQty > receivedQty) {
      errors.push('Accepted + Rejected cannot exceed Received quantity');
    }

    // Over-receiving warning
    if (receivedQty > expectedQty) {
      errors.push(`Over-received by ${receivedQty - expectedQty} units - requires approval`);
    }

    return errors;
  };

  const handleBatchAction = async () => {
    if (!currentOrganization || !user || selectedItems.size === 0) return;

    setProcessing(true);
    try {
      const actions: ReceivingAction[] = Array.from(selectedItems).map(itemId => {
        const item = items.find(i => i.id === itemId);
        if (!item) throw new Error(`Item ${itemId} not found`);

        return {
          type: batchAction,
          item_id: itemId,
          quantity: getActionQuantity(item, batchAction),
          notes: batchNotes || undefined
        };
      });

      // Validate actions
      const validation = await validateReceivingActions(currentOrganization.id, actions);
      if (!validation.is_valid) {
        setError(validation.errors.join('; '));
        return;
      }

      // Process batch
      const result = await processBatchReceiving(
        currentOrganization.id,
        {
          items: actions,
          notes: batchNotes,
          auto_create_discrepancies: true,
          notify_supplier: false
        },
        user.id
      );

      if (result.success) {
        // Reload data
        await loadReceivingData();
        setShowBatchModal(false);
        setSelectedItems(new Set());
        setBatchNotes('');

        if (onComplete) {
          onComplete(result.payable_amount);
        }
      } else {
        setError(result.error || 'Failed to process batch action');
      }

    } catch (err: any) {
      setError(err.message || 'Failed to process batch action');
    } finally {
      setProcessing(false);
    }
  };

  const getActionQuantity = (item: ReceivingFormItem, action: string): number => {
    switch (action) {
      case 'receive':
        return item.temp_received_quantity || 0;
      case 'accept':
        return item.temp_accepted_quantity || 0;
      case 'reject':
        return item.temp_rejected_quantity || 0;
      default:
        return 0;
    }
  };

  const getReceivingStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'gray', label: 'Pending' },
      partial: { color: 'warning', label: 'Partial' },
      complete: { color: 'success', label: 'Complete' },
      over_received: { color: 'purple', label: 'Over-Received' },
      rejected: { color: 'failure', label: 'Rejected' },
      mixed: { color: 'indigo', label: 'Mixed' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge color={config.color}>{config.label}</Badge>;
  };

  // Use the organization's currency formatter
  const formatWithCurrency = useCurrencyFormatter();

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center p-8">
          <Spinner size="xl" />
          <span className="ml-3">Loading receiving data...</span>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert color="failure" icon={HiOutlineExclamation}>
          <h3 className="text-lg font-medium">Error Loading Receiving Data</h3>
          <p>{error}</p>
          <div className="mt-4">
            <Button size="sm" onClick={loadReceivingData}>
              Retry
            </Button>
          </div>
        </Alert>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Receiving Metrics Dashboard */}
      {metrics && (
        <Card>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Receiving Progress</h3>
            <div className="flex gap-2">
              <Button
                size="sm"
                color="primary"
                onClick={() => setShowBatchModal(true)}
                disabled={selectedItems.size === 0}
              >
                <HiOutlineClipboardCheck className="mr-2 h-4 w-4" />
                Batch Action ({selectedItems.size})
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{metrics.total_items}</div>
              <div className="text-sm text-gray-500">Total Items</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{metrics.complete_items}</div>
              <div className="text-sm text-gray-500">Complete</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{metrics.pending_items}</div>
              <div className="text-sm text-gray-500">Pending</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{metrics.rejected_items}</div>
              <div className="text-sm text-gray-500">Rejected</div>
            </div>
          </div>

          <div className="mb-4">
            <div className="flex justify-between text-sm mb-1">
              <span>Completion Progress</span>
              <span>{metrics.completion_percentage.toFixed(1)}%</span>
            </div>
            <Progress progress={metrics.completion_percentage} color="blue" />
          </div>

          {payableCalc && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Payable Calculation</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Expected:</span>
                  <div className="font-medium">{formatCurrency(payableCalc.total_amount)}</div>
                </div>
                <div>
                  <span className="text-gray-500">Accepted:</span>
                  <div className="font-medium text-green-600">{formatCurrency(payableCalc.accepted_amount)}</div>
                </div>
                <div>
                  <span className="text-gray-500">Rejected:</span>
                  <div className="font-medium text-red-600">{formatCurrency(payableCalc.rejected_amount)}</div>
                </div>
                <div>
                  <span className="text-gray-500">Payable:</span>
                  <div className="font-bold text-blue-600">{formatCurrency(payableCalc.payable_amount)}</div>
                </div>
              </div>
            </div>
          )}
        </Card>
      )}

      {/* Main Receiving Interface */}
      <Card>
        <Tabs aria-label="Receiving workflow tabs" variant="underline">
          <Tabs.Item active title="Item Receiving" icon={HiOutlineTruck}>
            <div className="overflow-x-auto">
              <Table hoverable>
                <Table.Head>
                  <Table.HeadCell>
                    <input
                      type="checkbox"
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedItems(new Set(items.map(i => i.id)));
                        } else {
                          setSelectedItems(new Set());
                        }
                      }}
                      checked={selectedItems.size === items.length && items.length > 0}
                    />
                  </Table.HeadCell>
                  <Table.HeadCell>Product</Table.HeadCell>
                  <Table.HeadCell>Expected</Table.HeadCell>
                  <Table.HeadCell>Received</Table.HeadCell>
                  <Table.HeadCell>Accepted</Table.HeadCell>
                  <Table.HeadCell>Rejected</Table.HeadCell>
                  <Table.HeadCell>Status</Table.HeadCell>
                  <Table.HeadCell>Actions</Table.HeadCell>
                </Table.Head>
                <Table.Body className="divide-y">
                  {items.map((item) => (
                    <Table.Row
                      key={item.id}
                      className={`bg-white dark:border-gray-700 dark:bg-gray-800 ${
                        item.validationErrors?.length ? 'bg-red-50' : ''
                      }`}
                    >
                      <Table.Cell>
                        <input
                          type="checkbox"
                          checked={selectedItems.has(item.id)}
                          onChange={(e) => {
                            const newSelected = new Set(selectedItems);
                            if (e.target.checked) {
                              newSelected.add(item.id);
                            } else {
                              newSelected.delete(item.id);
                            }
                            setSelectedItems(newSelected);
                          }}
                        />
                      </Table.Cell>

                      <Table.Cell>
                        <div>
                          <div className="font-medium">{item.product?.name || 'Unknown Product'}</div>
                          {item.product?.sku && (
                            <div className="text-xs text-gray-500">SKU: {item.product.sku}</div>
                          )}
                          <div className="text-xs text-gray-500">
                            {formatWithCurrency(item.unit_cost)} per {item.uom?.name || 'unit'}
                          </div>
                        </div>
                      </Table.Cell>

                      <Table.Cell>
                        <div className="text-center">
                          <div className="font-medium">{item.expected_quantity || item.quantity}</div>
                          <div className="text-xs text-gray-500">{item.uom?.name}</div>
                        </div>
                      </Table.Cell>

                      <Table.Cell>
                        <div className="flex flex-col gap-1">
                          <EnhancedNumberInput
                            value={item.temp_received_quantity || 0}
                            onChange={(e) => handleItemQuantityChange(
                              item.id,
                              'temp_received_quantity',
                              parseFloat(e.target.value) || 0
                            )}
                            onBlur={(e) => {
                              // If field is empty on blur, reset to 0
                              if (e.target.value === '' || parseFloat(e.target.value) < 0) {
                                handleItemQuantityChange(item.id, 'temp_received_quantity', 0);
                              }
                            }}
                            min="0"
                            step="0.01"
                            sizing="sm"
                            className={item.validationErrors?.length ? 'border-red-500' : ''}
                            autoSelect={true}
                            preventScrollChange={true}
                          />
                          {item.over_received_quantity > 0 && (
                            <Badge color="purple" size="xs">
                              +{item.over_received_quantity} over
                            </Badge>
                          )}
                        </div>
                      </Table.Cell>

                      <Table.Cell>
                        <EnhancedNumberInput
                          value={item.temp_accepted_quantity || 0}
                          onChange={(e) => handleItemQuantityChange(
                            item.id,
                            'temp_accepted_quantity',
                            parseFloat(e.target.value) || 0
                          )}
                          onBlur={(e) => {
                            // If field is empty on blur, reset to 0
                            if (e.target.value === '' || parseFloat(e.target.value) < 0) {
                              handleItemQuantityChange(item.id, 'temp_accepted_quantity', 0);
                            }
                          }}
                          min="0"
                          step="0.01"
                          sizing="sm"
                          className={item.validationErrors?.length ? 'border-red-500' : ''}
                          autoSelect={true}
                          preventScrollChange={true}
                        />
                      </Table.Cell>

                      <Table.Cell>
                        <div className="flex flex-col gap-1">
                          <EnhancedNumberInput
                            value={item.temp_rejected_quantity || 0}
                            onChange={(e) => handleItemQuantityChange(
                              item.id,
                              'temp_rejected_quantity',
                              parseFloat(e.target.value) || 0
                            )}
                            onBlur={(e) => {
                              // If field is empty on blur, reset to 0
                              if (e.target.value === '' || parseFloat(e.target.value) < 0) {
                                handleItemQuantityChange(item.id, 'temp_rejected_quantity', 0);
                              }
                            }}
                            min="0"
                            step="0.01"
                            sizing="sm"
                            className={item.validationErrors?.length ? 'border-red-500' : ''}
                            autoSelect={true}
                            preventScrollChange={true}
                          />
                          {item.rejected_quantity > 0 && item.rejection_reason && (
                            <div className="text-xs text-red-600">
                              {item.rejection_reason}
                            </div>
                          )}
                        </div>
                      </Table.Cell>

                      <Table.Cell>
                        {getReceivingStatusBadge(item.receiving_status)}
                        {item.validationErrors?.length > 0 && (
                          <div className="mt-1">
                            {item.validationErrors.map((error, idx) => (
                              <div key={idx} className="text-xs text-red-600">
                                {error}
                              </div>
                            ))}
                          </div>
                        )}
                      </Table.Cell>

                      <Table.Cell>
                        <div className="flex gap-1">
                          <Button
                            size="xs"
                            color="gray"
                            onClick={() => {
                              // TODO: Open item details modal
                            }}
                          >
                            <HiOutlineEye className="h-3 w-3" />
                          </Button>

                          {item.over_received_quantity > 0 && !item.over_received_approved && (
                            <Button
                              size="xs"
                              color="purple"
                              onClick={() => {
                                // TODO: Approve over-received quantity
                              }}
                            >
                              Approve
                            </Button>
                          )}

                          {item.rejected_quantity > 0 && (
                            <Button
                              size="xs"
                              color="failure"
                              onClick={() => {
                                // TODO: Open rejection details modal
                              }}
                            >
                              Details
                            </Button>
                          )}
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </div>
          </Tabs.Item>

          <Tabs.Item title="Quality Control" icon={HiOutlineClipboardCheck}>
            <div className="text-center py-8 text-gray-500">
              Quality control interface will be implemented here
            </div>
          </Tabs.Item>

          <Tabs.Item title="Discrepancies" icon={HiOutlineDocumentReport}>
            <div className="text-center py-8 text-gray-500">
              Discrepancies management will be implemented here
            </div>
          </Tabs.Item>

          <Tabs.Item title="Analytics" icon={HiOutlineChartBar}>
            <div className="text-center py-8 text-gray-500">
              Receiving analytics will be implemented here
            </div>
          </Tabs.Item>
        </Tabs>
      </Card>

      {/* Batch Action Modal */}
      <Modal show={showBatchModal} onClose={() => setShowBatchModal(false)}>
        <Modal.Header>Batch Receiving Action</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Action Type</label>
              <Select
                value={batchAction}
                onChange={(e) => setBatchAction(e.target.value as any)}
              >
                <option value="receive">Mark as Received</option>
                <option value="accept">Mark as Accepted</option>
                <option value="reject">Mark as Rejected</option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Notes</label>
              <Textarea
                value={batchNotes}
                onChange={(e) => setBatchNotes(e.target.value)}
                placeholder="Optional notes for this batch action..."
                rows={3}
              />
            </div>

            <div className="bg-gray-50 p-3 rounded">
              <p className="text-sm text-gray-600">
                This action will be applied to {selectedItems.size} selected items.
              </p>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="gray"
            onClick={() => setShowBatchModal(false)}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={handleBatchAction}
            disabled={processing}
          >
            {processing ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Processing...
              </>
            ) : (
              `Apply ${batchAction} to ${selectedItems.size} items`
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default EnhancedReceivingWorkflow;
