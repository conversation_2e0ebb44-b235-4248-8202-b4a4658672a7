import React, { ReactNode } from 'react';
import CardBox from './CardBox';
import { Button } from 'flowbite-react';
import { HiOutlinePlus } from 'react-icons/hi';

interface EmptyStateProps {
  title: string;
  description?: string;
  icon?: ReactNode;
  actions?: ReactNode;
  actionLabel?: string;
  onAction?: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon,
  actions,
  actionLabel,
  onAction
}) => {
  return (
    <CardBox className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <div className="text-gray-400 dark:text-gray-500 mb-4">
        {icon || <HiOutlinePlus className="h-12 w-12" />}
      </div>
      <h3 className="text-lg font-medium mb-2">{title}</h3>
      {description && (
        <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md">
          {description}
        </p>
      )}
      {actions && (
        <div className="mt-2">
          {actions}
        </div>
      )}
      {!actions && actionLabel && onAction && (
        <div className="mt-2">
          <Button color="primary" onClick={onAction}>
            {actionLabel}
          </Button>
        </div>
      )}
    </CardBox>
  );
};

export default EmptyState;
