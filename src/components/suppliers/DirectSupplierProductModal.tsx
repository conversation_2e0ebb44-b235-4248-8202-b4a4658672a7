import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON> } from 'flowbite-react';
import { Product } from '../../services/product';
import { SupplierProductInput, addOrUpdateSupplierProduct } from '../../services/supplierProduct';
import DirectSupplierProductForm from './DirectSupplierProductForm';
import { useOrganization } from '../../context/OrganizationContext';

interface DirectSupplierProductModalProps {
  show: boolean;
  onClose: () => void;
  onSuccess: () => void;
  supplierId: string;
  product: Product;
}

const DirectSupplierProductModal = ({
  show,
  onClose,
  onSuccess,
  supplierId,
  product,
}: DirectSupplierProductModalProps) => {
  const { currentOrganization } = useOrganization();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (data: SupplierProductInput) => {
    if (!currentOrganization) {
      setError('No organization selected');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      console.log('Submitting supplier product data:', data);

      // Use the addOrUpdateSupplierProduct function to save the data
      const { success, error: submitError } = await addOrUpdateSupplierProduct(
        supplierId,
        product.id,
        data.unit_price || 0,
        currentOrganization.id,
        data.uom_id || undefined,
        data.uom_name || undefined,
        data.conversion_factor
      );

      if (submitError) {
        setError(submitError);
      } else if (success) {
        onSuccess();
        onClose();
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while saving the product');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal show={show} onClose={onClose} size="lg">
      <Modal.Header>
        Add Product to Supplier
      </Modal.Header>
      <Modal.Body>
        <DirectSupplierProductForm
          product={product}
          supplierId={supplierId}
          onSubmit={handleSubmit}
          onCancel={onClose}
          isSubmitting={isSubmitting}
          error={error}
        />
      </Modal.Body>
    </Modal>
  );
};

export default DirectSupplierProductModal;
