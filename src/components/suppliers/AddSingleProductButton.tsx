import { useState } from 'react';
import { Button } from 'flowbite-react';
import { HiOutlinePlus } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { Product } from '../../services/product';
import { getProducts } from '../../services/product';
import ProductSearchModal from '../products/ProductSearchModal';
import SimpleSupplierProductModal from './SimpleSupplierProductModal';

interface AddSingleProductButtonProps {
  supplierId: string;
  onSuccess: () => void;
  buttonText?: string;
  buttonSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  buttonColor?: 'primary' | 'secondary' | 'success' | 'warning' | 'failure' | 'info' | 'light' | 'dark' | 'gray';
}

const AddSingleProductButton = ({
  supplierId,
  onSuccess,
  buttonText = 'Add Product',
  buttonSize = 'md',
  buttonColor = 'primary'
}: AddSingleProductButtonProps) => {
  const { currentOrganization } = useOrganization();
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [showProductModal, setShowProductModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle product selection from search
  const handleProductSelect = async (product: Product) => {
    console.log('Selected product:', product);
    setSelectedProduct(product);
    setShowSearchModal(false);
    setShowProductModal(true);
  };

  // Handle search for products
  const handleSearch = async (query: string) => {
    if (!currentOrganization) return { products: [], count: 0 };
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { products, count, error } = await getProducts(
        currentOrganization.id,
        {
          searchQuery: query,
          limit: 10,
          offset: 0
        }
      );
      
      if (error) {
        console.error('Error searching products:', error);
        setError(error);
        return { products: [], count: 0 };
      }
      
      return { products, count };
    } catch (err: any) {
      console.error('Exception searching products:', err);
      setError(err.message || 'An error occurred while searching products');
      return { products: [], count: 0 };
    } finally {
      setIsLoading(false);
    }
  };

  // Handle success from the supplier product modal
  const handleSupplierProductSuccess = () => {
    setShowProductModal(false);
    setSelectedProduct(null);
    onSuccess();
  };

  return (
    <>
      <Button
        size={buttonSize}
        color={buttonColor}
        onClick={() => setShowSearchModal(true)}
      >
        <HiOutlinePlus className="mr-2 h-4 w-4" />
        {buttonText}
      </Button>

      {/* Product Search Modal */}
      <ProductSearchModal
        show={showSearchModal}
        onClose={() => setShowSearchModal(false)}
        onSelect={handleProductSelect}
        onSearch={handleSearch}
        isLoading={isLoading}
        error={error}
      />

      {/* Supplier Product Modal */}
      {selectedProduct && (
        <SimpleSupplierProductModal
          show={showProductModal}
          onClose={() => setShowProductModal(false)}
          onSuccess={handleSupplierProductSuccess}
          supplierId={supplierId}
          product={selectedProduct}
        />
      )}
    </>
  );
};

export default AddSingleProductButton;
