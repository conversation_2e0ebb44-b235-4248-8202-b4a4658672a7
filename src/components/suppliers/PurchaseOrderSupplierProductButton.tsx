import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Badge } from 'flowbite-react';
import { HiOutlineCog, HiOutlinePencil } from 'react-icons/hi';
import { Product as FullProduct } from '../../services/product';
import SimpleSupplierProductModal from './SimpleSupplierProductModal';
import { supabase } from '../../lib/supabase';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

// Simplified Product type for use in this component
type SimpleProduct = {
  id: string;
  name: string;
  organization_id?: string;
};

interface PurchaseOrderSupplierProductButtonProps {
  supplierId: string;
  product: SimpleProduct | FullProduct;
  onSuccess: (unitPrice: number, uomId: string | null, conversionFactor?: number) => void;
  buttonSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  buttonColor?: 'primary' | 'secondary' | 'success' | 'warning' | 'failure' | 'info' | 'light' | 'dark' | 'gray';
  currentUnitPrice?: number;
  currentUomId?: string;
}

const PurchaseOrderSupplierProductButton = ({
  supplierId,
  product,
  onSuccess,
  buttonSize = 'xs',
  buttonColor = 'light',
  currentUnitPrice,
  currentUomId
}: PurchaseOrderSupplierProductButtonProps) => {
  const [showModal, setShowModal] = useState(false);
  const [supplierProduct, setSupplierProduct] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const formatCurrency = useCurrencyFormatter();

  // Fetch the supplier product when the component mounts
  useEffect(() => {
    const fetchSupplierProduct = async () => {
      if (!supplierId || !product.id) return;

      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('supplier_products')
          .select(`
            *,
            uom:uom_id (
              id,
              code,
              name
            )
          `)
          .eq('supplier_id', supplierId)
          .eq('product_id', product.id)
          .maybeSingle();

        if (error) {
          console.error('Error fetching supplier product:', error);
        } else if (data) {
          // Store the fetched supplier product
          setSupplierProduct(data);
        }
      } catch (err) {
        console.error('Exception fetching supplier product:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchSupplierProduct();
  }, [supplierId, product.id]);

  // Handle success from the supplier product modal
  const handleSupplierProductSuccess = () => {
    // Fetch the latest supplier product data to get the updated price
    const fetchLatestData = async () => {
      try {
        // First, get the product's default UoM
        const { data: defaultUom, error: defaultUomError } = await supabase
          .from('product_uoms')
          .select(`
            uom_id,
            conversion_factor,
            is_default,
            is_purchasing_unit,
            uom:uom_id (
              id,
              code,
              name
            )
          `)
          .eq('product_id', product.id)
          .eq('is_default', true)
          .maybeSingle();

        if (defaultUomError) {
          console.error('Error fetching product default UoM:', defaultUomError);
        }

        // Now fetch the supplier product
        const { data, error } = await supabase
          .from('supplier_products')
          .select(`
            *,
            uom:uom_id (
              id,
              code,
              name
            )
          `)
          .eq('supplier_id', supplierId)
          .eq('product_id', product.id)
          .maybeSingle();

        if (error) {
          console.error('Error fetching updated supplier product:', error);
        } else if (data) {
          console.log('Fetched updated supplier product:', data);

          // If we have a default UoM, ensure the supplier product uses it
          if (defaultUom && defaultUom.uom_id) {
            let needsUpdate = false;
            const updateData: any = {};

            // Always use the default UoM from the product
            if (data.uom_id !== defaultUom.uom_id) {
              console.log(`Updating supplier product UoM to default: ${defaultUom.uom_id}`);
              updateData.uom_id = defaultUom.uom_id;
              updateData.conversion_factor = defaultUom.conversion_factor;

              if (defaultUom.uom) {
                updateData.uom_name = `${defaultUom.uom.name} (${defaultUom.uom.code})`;
              }

              if (data.unit_price) {
                updateData.base_price = data.unit_price * defaultUom.conversion_factor;
              }

              needsUpdate = true;
            }

            // Update the supplier product if needed
            if (needsUpdate) {
              console.log('Updating supplier product with default UoM data:', updateData);

              const { error: updateError } = await supabase
                .from('supplier_products')
                .update(updateData)
                .eq('id', data.id);

              if (updateError) {
                console.error('Error updating supplier product with default UoM:', updateError);
              } else {
                console.log('Updated supplier product with default UoM successfully');
                // Update the data object with the new values
                Object.assign(data, updateData);
              }
            }
          }

          // Call the onSuccess callback with the updated price, UoM, and conversion factor
          // Note: We need to handle the case where unit_price is explicitly set to 0
          const unitPrice = data.unit_price !== null && data.unit_price !== undefined ? data.unit_price : 0;
          onSuccess(unitPrice, data.uom_id, data.conversion_factor || 1);
        } else {
          console.warn('No supplier product found after update');
          // If no data found, just close the modal
          setShowModal(false);
        }
      } catch (err) {
        console.error('Exception fetching updated supplier product:', err);
        setShowModal(false);
      }
    };

    fetchLatestData();
    setShowModal(false);

    // Also update our local state with the latest supplier product
    const refreshSupplierProduct = async () => {
      try {
        const { data, error } = await supabase
          .from('supplier_products')
          .select(`
            *,
            uom:uom_id (
              id,
              code,
              name
            )
          `)
          .eq('supplier_id', supplierId)
          .eq('product_id', product.id)
          .maybeSingle();

        if (error) {
          console.error('Error refreshing supplier product:', error);
        } else if (data) {
          // Update the supplier product state with the refreshed data
          setSupplierProduct(data);
        } else {
          setSupplierProduct(null);
        }
      } catch (err) {
        console.error('Exception refreshing supplier product:', err);
      }
    };

    refreshSupplierProduct();
  };

  return (
    <>
      <div className="flex items-center gap-2">
        {supplierProduct ? (
          // Show edit button for existing supplier product
          <Tooltip content={`Edit supplier product: ${formatCurrency(supplierProduct.unit_price || 0)} per ${supplierProduct.uom?.code || 'unit'}`}>
            <Button
              size={buttonSize}
              color="primary"
              type="button"
              onClick={async (e) => {
                // Prevent the event from propagating to parent forms
                e.preventDefault();
                e.stopPropagation();
                setShowModal(true);
              }}
            >
              <HiOutlinePencil className="h-4 w-4" />
            </Button>
          </Tooltip>
        ) : (
          // Show configure button for new supplier product
          <Tooltip content="Configure supplier price and UoM">
            <Button
              size={buttonSize}
              color={buttonColor}
              type="button"
              onClick={async (e) => {
                // Prevent the event from propagating to parent forms
                e.preventDefault();
                e.stopPropagation();
                setShowModal(true);
              }}
            >
              <HiOutlineCog className="h-4 w-4" />
            </Button>
          </Tooltip>
        )}
      </div>

      {/* Supplier Product Modal */}
      <SimpleSupplierProductModal
        show={showModal}
        onClose={() => setShowModal(false)}
        onSuccess={handleSupplierProductSuccess}
        supplierId={supplierId}
        product={product}
        currentUnitPrice={currentUnitPrice}
        currentUomId={currentUomId}
      />
    </>
  );
};

export default PurchaseOrderSupplierProductButton;
