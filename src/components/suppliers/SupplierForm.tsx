import { useState, useEffect } from 'react';
import {
  Button,
  TextInput,
  Textarea,
  Label,
  Select,
  Spinner,
  Alert,
} from 'flowbite-react';
import { Supplier } from '../../services/supplier';
import { useOrganization } from '../../context/OrganizationContext';
import { HiOutlineExclamation } from 'react-icons/hi';
import { countries } from '../../utils/countryList';

interface SupplierFormProps {
  initialData?: Partial<Supplier>;
  onSubmit: (supplierData: Partial<Supplier>) => Promise<void>;
  isSubmitting: boolean;
  error?: string;
  viewMode?: boolean;
  onCancel?: () => void;
}

const SupplierForm = ({ initialData, onSubmit, isSubmitting, error, viewMode = false, onCancel }: SupplierFormProps) => {
  const { currentOrganization } = useOrganization();
  const [formData, setFormData] = useState<Partial<Supplier>>({
    name: '',
    contact_person: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    country: '',
    tax_id: '',
    notes: '',
    ...initialData,
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      setFormData({
        name: '',
        contact_person: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        state: '',
        postal_code: '',
        country: '',
        tax_id: '',
        notes: '',
        ...initialData,
      });
    }
  }, [initialData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear validation error when field is changed
    if (validationErrors[name]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Required fields
    if (!formData.name?.trim()) {
      errors.name = 'Supplier name is required';
    }

    // Email validation
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);
  };

  // Display field value or placeholder for view mode
  const displayValue = (value: string | null | undefined, placeholder: string = '-') => {
    return value ? value : <span className="text-gray-400">{placeholder}</span>;
  };

  // Render form in view mode (read-only)
  if (viewMode) {
    return (
      <div className="space-y-4">
        {/* Name */}
        <div>
          <h3 className="text-sm font-medium text-gray-500">Supplier Name</h3>
          <p className="mt-1 text-base">{displayValue(formData.name)}</p>
        </div>

        {/* Contact Person */}
        <div>
          <h3 className="text-sm font-medium text-gray-500">Contact Person</h3>
          <p className="mt-1 text-base">{displayValue(formData.contact_person)}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Email */}
          <div>
            <h3 className="text-sm font-medium text-gray-500">Email</h3>
            <p className="mt-1 text-base">{displayValue(formData.email)}</p>
          </div>

          {/* Phone */}
          <div>
            <h3 className="text-sm font-medium text-gray-500">Phone</h3>
            <p className="mt-1 text-base">{displayValue(formData.phone)}</p>
          </div>
        </div>

        {/* Address */}
        <div>
          <h3 className="text-sm font-medium text-gray-500">Address</h3>
          <p className="mt-1 text-base">{displayValue(formData.address)}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* City */}
          <div>
            <h3 className="text-sm font-medium text-gray-500">City</h3>
            <p className="mt-1 text-base">{displayValue(formData.city)}</p>
          </div>

          {/* State/Province */}
          <div>
            <h3 className="text-sm font-medium text-gray-500">State/Province</h3>
            <p className="mt-1 text-base">{displayValue(formData.state)}</p>
          </div>

          {/* Postal Code */}
          <div>
            <h3 className="text-sm font-medium text-gray-500">Postal Code</h3>
            <p className="mt-1 text-base">{displayValue(formData.postal_code)}</p>
          </div>
        </div>

        {/* Country */}
        <div>
          <h3 className="text-sm font-medium text-gray-500">Country</h3>
          <p className="mt-1 text-base">{displayValue(formData.country)}</p>
        </div>

        {/* Tax ID */}
        <div>
          <h3 className="text-sm font-medium text-gray-500">Tax ID</h3>
          <p className="mt-1 text-base">{displayValue(formData.tax_id)}</p>
        </div>

        {/* Notes */}
        <div>
          <h3 className="text-sm font-medium text-gray-500">Notes</h3>
          <p className="mt-1 text-base whitespace-pre-line">{displayValue(formData.notes)}</p>
        </div>
      </div>
    );
  }

  // Render editable form
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {error}
        </Alert>
      )}

      {/* Name */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="name" value="Supplier Name *" />
        </div>
        <TextInput
          id="name"
          name="name"
          value={formData.name || ''}
          onChange={handleChange}
          placeholder="Enter supplier name"
          required
          color={validationErrors.name ? 'failure' : undefined}
          helperText={validationErrors.name}
        />
      </div>

      {/* Contact Person */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="contact_person" value="Contact Person" />
        </div>
        <TextInput
          id="contact_person"
          name="contact_person"
          value={formData.contact_person || ''}
          onChange={handleChange}
          placeholder="Enter contact person name"
        />
      </div>

      {/* Email */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="email" value="Email" />
        </div>
        <TextInput
          id="email"
          name="email"
          type="email"
          value={formData.email || ''}
          onChange={handleChange}
          placeholder="Enter email address"
          color={validationErrors.email ? 'failure' : undefined}
          helperText={validationErrors.email}
        />
      </div>

      {/* Phone */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="phone" value="Phone" />
        </div>
        <TextInput
          id="phone"
          name="phone"
          value={formData.phone || ''}
          onChange={handleChange}
          placeholder="Enter phone number"
        />
      </div>

      {/* Address */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="address" value="Address" />
        </div>
        <TextInput
          id="address"
          name="address"
          value={formData.address || ''}
          onChange={handleChange}
          placeholder="Enter street address"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* City */}
        <div>
          <div className="mb-2 block">
            <Label htmlFor="city" value="City" />
          </div>
          <TextInput
            id="city"
            name="city"
            value={formData.city || ''}
            onChange={handleChange}
            placeholder="Enter city"
          />
        </div>

        {/* State/Province */}
        <div>
          <div className="mb-2 block">
            <Label htmlFor="state" value="State/Province" />
          </div>
          <TextInput
            id="state"
            name="state"
            value={formData.state || ''}
            onChange={handleChange}
            placeholder="Enter state or province"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Postal Code */}
        <div>
          <div className="mb-2 block">
            <Label htmlFor="postal_code" value="Postal Code" />
          </div>
          <TextInput
            id="postal_code"
            name="postal_code"
            value={formData.postal_code || ''}
            onChange={handleChange}
            placeholder="Enter postal code"
          />
        </div>

        {/* Country */}
        <div>
          <div className="mb-2 block">
            <Label htmlFor="country" value="Country" />
          </div>
          <Select
            id="country"
            name="country"
            value={formData.country || ''}
            onChange={handleChange}
          >
            <option value="">Select a country</option>
            {countries.map((country) => (
              <option key={country.code} value={country.name}>
                {country.name}
              </option>
            ))}
          </Select>
        </div>
      </div>

      {/* Tax ID */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="tax_id" value="Tax ID" />
        </div>
        <TextInput
          id="tax_id"
          name="tax_id"
          value={formData.tax_id || ''}
          onChange={handleChange}
          placeholder="Enter tax ID"
        />
      </div>

      {/* Notes */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="notes" value="Notes" />
        </div>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes || ''}
          onChange={handleChange}
          placeholder="Enter additional notes"
          rows={3}
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        {onCancel && (
          <Button color="gray" onClick={onCancel} type="button">
            Cancel
          </Button>
        )}
        <Button color="primary" type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Supplier'
          )}
        </Button>
      </div>
    </form>
  );
};

export default SupplierForm;
