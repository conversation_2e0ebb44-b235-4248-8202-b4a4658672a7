import { useState, useEffect } from 'react';
import { Button, TextInput, Label, Alert, Spinner, Checkbox, Textarea } from 'flowbite-react';
import { HiOutlineExclamation, HiOutlineInformationCircle } from 'react-icons/hi';
import { Product } from '../../services/product';
import { SupplierProductInput } from '../../services/supplierProduct';
import { useOrganization } from '../../context/OrganizationContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import UomSelectorForSupplier from '../uom/UomSelectorForSupplier';
import { getProductConversionFactors } from '../../services/productUom';
import { Tooltip } from 'flowbite-react';

interface DirectSupplierProductFormProps {
  product: Product;
  supplierId: string;
  onSubmit: (data: SupplierProductInput) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
  error?: string | null;
}

const DirectSupplierProductForm = ({
  product,
  supplierId,
  onSubmit,
  onCancel,
  isSubmitting = false,
  error = null,
}: DirectSupplierProductFormProps) => {
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  // Form state
  const [formData, setFormData] = useState({
    supplier_id: supplierId,
    product_id: product.id,
    unit_price: product.unit_price || 0,
    minimum_order_quantity: null as number | null,
    lead_time_days: null as number | null,
    is_preferred: false,
    notes: '',
    uom_id: null as string | null,
    uom_name: null as string | null,
    conversion_factor: 1,
    available_conversion_factors: {} as Record<string, number>,
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Fetch conversion factors when the component mounts
  useEffect(() => {
    const fetchConversionFactors = async () => {
      if (!currentOrganization || !product.id) return;

      try {
        console.log('Fetching conversion factors for product:', product.id);
        const { conversionFactors, error } = await getProductConversionFactors(
          product.id,
          currentOrganization.id
        );

        if (error) {
          console.error('Error fetching conversion factors:', error);
        } else {
          console.log('Fetched conversion factors:', conversionFactors);
          setFormData(prev => ({
            ...prev,
            available_conversion_factors: conversionFactors
          }));
        }
      } catch (err) {
        console.error('Error fetching conversion factors:', err);
      }
    };

    fetchConversionFactors();
  }, [product.id, currentOrganization]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setFormData(prev => ({
        ...prev,
        [name]: checkbox.checked
      }));
    } else if (type === 'number') {
      setFormData(prev => ({
        ...prev,
        [name]: value === '' ? null : parseFloat(value)
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear validation error when field is changed
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Validate the form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Validate unit price if provided
    if (formData.unit_price !== null && formData.unit_price !== undefined) {
      if (formData.unit_price < 0) {
        errors.unit_price = 'Unit price cannot be negative';
      }
    }

    // Validate minimum order quantity if provided
    if (formData.minimum_order_quantity !== null && formData.minimum_order_quantity !== undefined && formData.minimum_order_quantity < 0) {
      errors.minimum_order_quantity = 'Minimum order quantity cannot be negative';
    }

    // Validate lead time days if provided
    if (formData.lead_time_days !== null && formData.lead_time_days !== undefined && formData.lead_time_days < 0) {
      errors.lead_time_days = 'Lead time days cannot be negative';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Log the form data for debugging
    console.log('Submitting direct supplier product form with data:', formData);

    // Calculate the base price
    const basePrice = formData.unit_price !== undefined &&
                      formData.unit_price !== null &&
                      formData.conversion_factor ?
                      formData.unit_price * formData.conversion_factor : null;

    console.log('Calculated base price:', {
      unit_price: formData.unit_price,
      conversion_factor: formData.conversion_factor,
      base_price: basePrice
    });

    // Prepare the data for submission
    const supplierProductData: SupplierProductInput = {
      supplier_id: supplierId,
      product_id: product.id,
      unit_price: formData.unit_price,
      minimum_order_quantity: formData.minimum_order_quantity,
      lead_time_days: formData.lead_time_days,
      is_preferred: formData.is_preferred,
      notes: formData.notes || null,
      // Explicitly set UoM fields
      uom_id: formData.uom_id,
      uom_name: formData.uom_name,
      conversion_factor: formData.conversion_factor,
      // Include the organization_id
      organization_id: currentOrganization?.id,
      // Explicitly calculate base price
      base_price: basePrice
    };

    // Log the final data being sent to the server
    console.log('Final supplier product data being submitted:', supplierProductData);

    await onSubmit(supplierProductData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {error}
        </Alert>
      )}

      {submitError && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {submitError}
        </Alert>
      )}

      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
        <div className="flex items-center gap-3 mb-2">
          {product.image_url ? (
            <img
              src={product.image_url}
              alt={product.name}
              className="h-12 w-12 rounded-md object-cover"
            />
          ) : (
            <div className="h-12 w-12 rounded-md bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500 text-xs">No image</span>
            </div>
          )}
          <div>
            <h3 className="font-medium text-lg">{product.name}</h3>
            <div className="text-sm text-gray-500">
              <span className="mr-3">SKU: {product.sku || 'N/A'}</span>
              <span>Current Price: {formatWithCurrency(product.unit_price)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Unit of Measurement */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="uom_id" value="Unit of Measurement" />
        </div>
        <UomSelectorForSupplier
          value={formData.uom_id || ''}
          onChange={(uomId, uomName) => {
            // Get the conversion factor for this UoM from product_uoms
            let conversionFactor = formData.available_conversion_factors?.[uomId];

            // If no conversion factor found in product_uoms, use the default (1)
            if (!conversionFactor) {
              conversionFactor = 1;
            }

            // Log the conversion factor for debugging
            console.log('Using conversion factor for UoM:', {
              uomId,
              conversionFactor,
              availableFactors: formData.available_conversion_factors
            });

            // Store the original conversion factor from product_uoms
            const originalConversionFactor = conversionFactor;

            console.log('UoM changed:', {
              from: formData.uom_id,
              to: uomId,
              conversionFactor,
              uomName,
              originalConversionFactor
            });

            setFormData(prev => ({
              ...prev,
              uom_id: uomId,
              uom_name: uomName,
              conversion_factor: originalConversionFactor
            }));
          }}
        />
      </div>

      {/* Conversion Factor */}
      <div>
        <div className="mb-2 flex items-center">
          <Label htmlFor="conversion_factor" value="Conversion Factor" />
          <Tooltip content="This is the factor used to convert between units. For example, if this UoM is 'case' and each case contains 12 pieces, the conversion factor would be 12.">
            <HiOutlineInformationCircle className="ml-2 h-5 w-5 text-gray-400" />
          </Tooltip>
        </div>
        <TextInput
          id="conversion_factor"
          name="conversion_factor"
          type="number"
          step="0.01"
          min="0.01"
          value={formData.conversion_factor || 1}
          onChange={(e) => {
            const value = parseFloat(e.target.value);
            if (!isNaN(value) && value > 0) {
              console.log('Conversion factor changed to:', value);
              setFormData(prev => ({
                ...prev,
                conversion_factor: value
              }));
            }
          }}
          helperText="Factor to convert between this unit and the base unit"
        />
      </div>

      {/* Unit Price */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="unit_price" value="Unit Price from Supplier" />
        </div>
        <TextInput
          id="unit_price"
          name="unit_price"
          type="number"
          step="0.01"
          value={formData.unit_price === null ? '' : formData.unit_price}
          onChange={handleChange}
          placeholder="Enter unit price"
          color={validationErrors.unit_price ? 'failure' : undefined}
          helperText={validationErrors.unit_price || "Enter the supplier's price for this product"}
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button color="gray" onClick={onCancel} type="button">
          Cancel
        </Button>
        <Button color="primary" type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Product'
          )}
        </Button>
      </div>
    </form>
  );
};

export default DirectSupplierProductForm;
