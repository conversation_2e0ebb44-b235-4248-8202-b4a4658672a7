import { useState, useEffect } from 'react';
import { Button, TextInput, Label, Alert, Spinner, Checkbox, Textarea, Tooltip, Badge } from 'flowbite-react';
import { HiOutlineExclamation, HiOutlineInformationCircle } from 'react-icons/hi';
import { Product } from '../../services/product';
import { SupplierProductInput } from '../../services/supplierProduct';
import { useOrganization } from '../../context/OrganizationContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import UomSelectorForSupplier from '../uom/UomSelectorForSupplier';
import { getProductConversionFactors } from '../../services/productUom';
import { supabase } from '../../lib/supabase';

interface SimpleSupplierProductFormProps {
  product: Product;
  supplierId: string;
  onSubmit: (data: SupplierProductInput) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
  error?: string | null;
  currentUnitPrice?: number;
  currentUomId?: string;
  existingSupplierProduct?: any;
}

const SimpleSupplierProductForm = ({
  product,
  supplierId,
  onSubmit,
  onCancel,
  isSubmitting = false,
  error = null,
  currentUnitPrice,
  currentUomId,
  existingSupplierProduct
}: SimpleSupplierProductFormProps) => {
  const { currentOrganization } = useOrganization();
  const formatCurrency = useCurrencyFormatter();

  // Form state
  const [formData, setFormData] = useState({
    supplier_id: supplierId,
    product_id: product.id,
    unit_price: existingSupplierProduct?.unit_price !== undefined
      ? existingSupplierProduct.unit_price
      : (currentUnitPrice !== undefined ? currentUnitPrice : (product.unit_price || 0)),
    minimum_order_quantity: existingSupplierProduct?.minimum_order_quantity || null as number | null,
    lead_time_days: existingSupplierProduct?.lead_time_days || null as number | null,
    is_preferred: existingSupplierProduct?.is_preferred || false,
    notes: existingSupplierProduct?.notes || '',
    uom_id: existingSupplierProduct?.uom_id || currentUomId || null as string | null,
    uom_name: existingSupplierProduct?.uom_name || null as string | null,
    conversion_factor: existingSupplierProduct?.conversion_factor || 1,
    available_conversion_factors: {} as Record<string, number>,
  });

  // Log the initial form data with current UoM and price
  console.log('Initializing form with current values:', {
    currentUnitPrice,
    currentUomId,
    formData
  });

  // Check if this product already exists for this supplier
  useEffect(() => {
    const checkExistingProduct = async () => {
      if (!currentOrganization || !product.id) return;

      try {
        console.log('Checking if product already exists for supplier:', {
          supplierId,
          productId: product.id
        });

        const { data, error } = await supabase
          .from('supplier_products')
          .select(`
            *,
            uom:uom_id (
              id,
              code,
              name
            )
          `)
          .eq('supplier_id', supplierId)
          .eq('product_id', product.id)
          .maybeSingle();

        console.log('Existing product check result:', { data, error });

        if (data) {
          console.log('Found existing supplier product, initializing form with:', data);
          setFormData(prev => ({
            ...prev,
            unit_price: data.unit_price || product.unit_price || 0,
            minimum_order_quantity: data.minimum_order_quantity,
            lead_time_days: data.lead_time_days,
            is_preferred: data.is_preferred || false,
            notes: data.notes || '',
            uom_id: data.uom_id,
            uom_name: data.uom_name,
            conversion_factor: data.conversion_factor || 1
          }));
        }
      } catch (err) {
        console.error('Error checking for existing supplier product:', err);
      }
    };

    checkExistingProduct();
  }, [supplierId, product.id, currentOrganization]);

  // Fetch the product's UoMs when the component mounts
  useEffect(() => {
    const fetchProductUoms = async () => {
      if (!currentOrganization || !product.id) return;

      try {
        console.log('Fetching UoMs for product:', product.id);

        // Get all the product's UoMs
        const { data: productUoms, error: productUomsError } = await supabase
          .from('product_uoms')
          .select(`
            uom_id,
            conversion_factor,
            is_default,
            uom:uom_id (
              id,
              code,
              name
            )
          `)
          .eq('product_id', product.id);

        if (productUomsError) {
          console.error('Error fetching product UoMs:', productUomsError);
        } else if (!productUoms || productUoms.length === 0) {
          console.warn('No UoMs found for product:', product.id);
        } else {
          console.log('Found UoMs for product:', productUoms);

          // Find the default UoM
          const defaultUom = productUoms.find(u => u.is_default);

          // Create a map of UoM IDs to conversion factors
          const conversionFactors: Record<string, number> = {};
          productUoms.forEach(uom => {
            conversionFactors[uom.uom_id] = uom.conversion_factor || 1;
          });

          // Update the form data with the default UoM and conversion factors
          setFormData(prev => {
            const newFormData = {
              ...prev,
              available_conversion_factors: conversionFactors
            };

            // If we have a current UoM ID, use it
            if (prev.uom_id) {
              newFormData.conversion_factor = conversionFactors[prev.uom_id] || 1;
            }
            // Otherwise, use the default UoM if available
            else if (defaultUom) {
              newFormData.uom_id = defaultUom.uom_id;
              newFormData.conversion_factor = defaultUom.conversion_factor || 1;

              // Set the UoM name if available
              if (defaultUom.uom) {
                newFormData.uom_name = `${defaultUom.uom.name} (${defaultUom.uom.code})`;
              }
            }

            return newFormData;
          });
        }
      } catch (err) {
        console.error('Error fetching product UoMs:', err);
      }
    };

    fetchProductUoms();
  }, [product.id, currentOrganization]);

  // Handle form submission
  const handleSubmit = async (e: React.MouseEvent | React.FormEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Prepare form data for submission

    // Ensure unit_price is a number
    const unitPrice = formData.unit_price === null || formData.unit_price === undefined ?
                      product.unit_price || 0 :
                      formData.unit_price;

    // Calculate the base price
    const basePrice = unitPrice * formData.conversion_factor;

    // Calculate the base price (unit_price * conversion_factor)

    // Prepare the data for submission with explicit values
    const supplierProductData: SupplierProductInput = {
      supplier_id: supplierId,
      product_id: product.id,
      unit_price: unitPrice,
      minimum_order_quantity: formData.minimum_order_quantity,
      lead_time_days: formData.lead_time_days,
      is_preferred: formData.is_preferred,
      notes: formData.notes || null,
      uom_id: formData.uom_id,
      uom_name: formData.uom_name,
      conversion_factor: formData.conversion_factor,
      organization_id: currentOrganization?.id,
      base_price: basePrice
    };

    // Submit the supplier product data

    await onSubmit(supplierProductData);
  };

  return (
    <div className="space-y-4">
      {error && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {error}
        </Alert>
      )}

      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
        <div className="flex items-center gap-3 mb-2">
          {product.image_url ? (
            <img
              src={product.image_url}
              alt={product.name}
              className="h-12 w-12 rounded-md object-cover"
            />
          ) : (
            <div className="h-12 w-12 rounded-md bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500 text-xs">No image</span>
            </div>
          )}
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-lg">{product.name}</h3>
              {existingSupplierProduct && (
                <Badge color="success">
                  Already in supplier catalog
                </Badge>
              )}
            </div>
            <div className="text-sm text-gray-500">
              <span className="mr-3">SKU: {product.sku || 'N/A'}</span>
              <span>Current Price: {formatCurrency(product.unit_price)}</span>
              {existingSupplierProduct && (
                <span className="ml-3">
                  Supplier Price: {formatCurrency(existingSupplierProduct.unit_price || 0)}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Unit of Measurement */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="uom_id" value="Unit of Measurement" />
        </div>
        <div className="flex items-center">
          <UomSelectorForSupplier
            value={formData.uom_id || ''}
            onChange={(uomId, uomName) => {
              setFormData(prev => {
                // Get the conversion factor for this UoM
                const conversionFactor = prev.available_conversion_factors?.[uomId] || 1;

                return {
                  ...prev,
                  uom_id: uomId,
                  uom_name: uomName,
                  conversion_factor: conversionFactor
                };
              });
            }}
            className="w-full"
          />
          <Tooltip content="Select the unit of measurement for this supplier product">
            <HiOutlineInformationCircle className="ml-2 h-5 w-5 text-gray-400" />
          </Tooltip>
        </div>
        <div className="flex justify-between mt-1">
          <p className="text-sm text-gray-500">
            Choose the unit of measurement that this supplier uses for this product
          </p>
          {formData.conversion_factor && formData.conversion_factor !== 1 && (
            <p className="text-sm font-medium">
              Conversion Factor: {formData.conversion_factor}
            </p>
          )}
        </div>
      </div>

      {/* Unit Price */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="unit_price" value="Unit Price from Supplier" />
          <span className="text-xs text-gray-500 ml-2">(Use 0 for products not supplied by this supplier)</span>
        </div>
        <TextInput
          id="unit_price"
          name="unit_price"
          type="number"
          min="0"
          step="0.01"
          value={formData.unit_price === null ? '' : formData.unit_price}
          onChange={(e) => {
            const value = e.target.value === '' ? null : parseFloat(e.target.value);
            setFormData(prev => ({
              ...prev,
              unit_price: value as number | null
            }));
          }}
          placeholder="Enter unit price (0 for products not supplied)"
        />
      </div>

      {/* Minimum Order Quantity */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="minimum_order_quantity" value="Minimum Order Quantity" />
        </div>
        <TextInput
          id="minimum_order_quantity"
          name="minimum_order_quantity"
          type="number"
          step="1"
          min="0"
          value={formData.minimum_order_quantity === null ? '' : formData.minimum_order_quantity}
          onChange={(e) => {
            const value = e.target.value === '' ? null : parseInt(e.target.value);
            setFormData(prev => ({
              ...prev,
              minimum_order_quantity: value as number | null
            }));
          }}
          placeholder="Enter minimum order quantity"
        />
      </div>

      {/* Lead Time Days */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="lead_time_days" value="Lead Time (Days)" />
        </div>
        <TextInput
          id="lead_time_days"
          name="lead_time_days"
          type="number"
          step="1"
          min="0"
          value={formData.lead_time_days === null ? '' : formData.lead_time_days}
          onChange={(e) => {
            const value = e.target.value === '' ? null : parseInt(e.target.value);
            setFormData(prev => ({
              ...prev,
              lead_time_days: value as number | null
            }));
          }}
          placeholder="Enter lead time in days"
        />
      </div>

      {/* Is Preferred */}
      <div className="flex items-center gap-2">
        <Checkbox
          id="is_preferred"
          checked={formData.is_preferred}
          onChange={(e) => {
            setFormData(prev => ({
              ...prev,
              is_preferred: e.target.checked
            }));
          }}
        />
        <Label htmlFor="is_preferred">
          Preferred supplier for this product
        </Label>
      </div>

      {/* Notes */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="notes" value="Notes" />
        </div>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes || ''}
          onChange={(e) => {
            setFormData(prev => ({
              ...prev,
              notes: e.target.value
            }));
          }}
          placeholder="Enter any notes about this supplier product"
          rows={3}
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button color="gray" onClick={onCancel} type="button">
          Cancel
        </Button>
        <Button
          color="primary"
          type="button"
          disabled={isSubmitting}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleSubmit(e as any);
          }}
        >
          {isSubmitting ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            existingSupplierProduct ? 'Update Product' : 'Save Product'
          )}
        </Button>
      </div>
    </div>
  );
};

export default SimpleSupplierProductForm;
