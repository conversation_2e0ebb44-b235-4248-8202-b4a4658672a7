import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Spinner,
  Alert,
  TextInput,
  Pagination,
  Badge,
  Modal,
  Tooltip,
} from 'flowbite-react';
import {
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineSearch,
  HiOutlineExclamation,
  HiOutlineStar,
  HiOutlineInformationCircle,
} from 'react-icons/hi';
import {
  getSupplierProducts,
  SupplierProduct,
  updateSupplierProduct,
  removeSupplierProduct,
} from '../../services/supplierProduct';
import { supabase } from '../../lib/supabase';
import { useOrganization } from '../../context/OrganizationContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import SimpleSupplierProductForm from './SimpleSupplierProductForm';

interface SupplierProductsListProps {
  supplierId: string;
  onProductCountChange?: (count: number) => void;
  refreshTrigger?: number;
}

const SupplierProductsList = ({
  supplierId,
  onProductCountChange,
  refreshTrigger = 0,
}: SupplierProductsListProps) => {
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  // State for supplier products
  const [supplierProducts, setSupplierProducts] = useState<SupplierProduct[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // State for search
  const [searchQuery, setSearchQuery] = useState('');
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // State for edit modal
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentProduct, setCurrentProduct] = useState<SupplierProduct | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // State for delete confirmation
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [productToDelete, setProductToDelete] = useState<SupplierProduct | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Fetch supplier products when organization, page, search, or refreshTrigger changes
  useEffect(() => {
    fetchSupplierProducts();
  }, [currentOrganization, currentPage, supplierId, refreshTrigger]);

  // Handle search with debounce
  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      fetchSupplierProducts();
    }, 300);

    setSearchTimeout(timeout);

    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchQuery]);

  // Update product count when total count changes
  useEffect(() => {
    if (onProductCountChange) {
      onProductCountChange(totalCount);
    }
  }, [totalCount, onProductCountChange]);

  const fetchSupplierProducts = async () => {
    if (!currentOrganization || !supplierId) return;

    setLoading(true);
    setError(null);

    try {
      const { supplierProducts: productsData, count, error: fetchError } = await getSupplierProducts(
        currentOrganization.id,
        supplierId,
        {
          searchQuery,
          limit: pageSize,
          offset: (currentPage - 1) * pageSize,
          sortBy: 'product.name',
          sortOrder: 'asc',
        }
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setSupplierProducts(productsData);
        setTotalCount(count);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching supplier products');
    } finally {
      setLoading(false);
    }
  };

  // Open edit modal
  const openEditModal = async (product: SupplierProduct) => {
    console.log('Opening edit modal for supplier product:', product);
    setSubmitError(null);

    // Fetch the latest data for this product to ensure we have the most up-to-date information
    try {
      const { data, error } = await supabase
        .from('supplier_products')
        .select(`
          *,
          product:product_id (*),
          uom:uom_id (
            id,
            code,
            name
          )
        `)
        .eq('id', product.id)
        .single();

      if (error) {
        console.error('Error fetching supplier product for editing:', error);
        setCurrentProduct(product);
      } else if (data) {
        console.log('Fetched latest supplier product data for editing:', data);
        setCurrentProduct(data as SupplierProduct);
      } else {
        console.warn('No data found for supplier product:', product.id);
        setCurrentProduct(product);
      }
    } catch (err) {
      console.error('Exception fetching supplier product for editing:', err);
      setCurrentProduct(product);
    }

    setShowEditModal(true);
  };

  // Open delete confirmation
  const openDeleteConfirm = (product: SupplierProduct) => {
    setProductToDelete(product);
    setDeleteError(null);
    setShowDeleteConfirm(true);
  };

  // Handle update supplier product
  const handleUpdateProduct = async (data: any) => {
    if (!currentProduct) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      console.log('Updating supplier product with data:', data);

      // Ensure unit_price is a number
      const unitPrice = data.unit_price === null || data.unit_price === undefined ?
                      (currentProduct.product?.unit_price || 0) :
                      data.unit_price;

      // Calculate base price
      const basePrice = unitPrice * data.conversion_factor;

      console.log('Calculated base price:', {
        unit_price: unitPrice,
        conversion_factor: data.conversion_factor,
        base_price: basePrice
      });

      // Prepare the data with explicit values
      const updateData = {
        unit_price: unitPrice,
        minimum_order_quantity: data.minimum_order_quantity,
        lead_time_days: data.lead_time_days,
        is_preferred: data.is_preferred,
        notes: data.notes,
        uom_id: data.uom_id,
        uom_name: data.uom_name,
        conversion_factor: data.conversion_factor,
        organization_id: currentOrganization?.id,
        base_price: basePrice
      };

      console.log('Final data being updated directly in database:', updateData);

      // Update directly in the database
      const { data: result, error } = await supabase
        .from('supplier_products')
        .update(updateData)
        .eq('id', currentProduct.id)
        .select('*')
        .single();

      if (error) {
        console.error('Error updating supplier product:', error);
        setSubmitError(error.message);
      } else {
        console.log('Supplier product updated successfully:', result);
        setShowEditModal(false);
        fetchSupplierProducts();
      }
    } catch (err: any) {
      console.error('Exception updating supplier product:', err);
      setSubmitError(err.message || 'An error occurred while updating the product');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete supplier product
  const handleDeleteProduct = async () => {
    if (!productToDelete) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      console.log('Deleting supplier product:', productToDelete.id);

      // Delete directly from the database
      const { error } = await supabase
        .from('supplier_products')
        .delete()
        .eq('id', productToDelete.id);

      if (error) {
        console.error('Error deleting supplier product:', error);
        setDeleteError(error.message);
      } else {
        console.log('Supplier product deleted successfully');
        setShowDeleteConfirm(false);

        // If we deleted the last item on the page and it's not the first page,
        // go to the previous page
        if (supplierProducts.length === 1 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        } else {
          // Otherwise, refresh the current page
          fetchSupplierProducts();
        }
      }
    } catch (err: any) {
      console.error('Exception deleting supplier product:', err);
      setDeleteError(err.message || 'An error occurred while removing the product');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle page change
  const onPageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle search submit
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchSupplierProducts();
  };

  // Navigate to product details
  const navigateToProductDetails = (productId: string) => {
    navigate(`/products/details/${productId}`);
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <form onSubmit={handleSearch} className="mb-4">
        <div className="flex gap-2">
          <TextInput
            id="search"
            type="text"
            placeholder="Search products by name, SKU, or barcode"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1"
            icon={HiOutlineSearch}
          />
          <Button type="submit">
            Search
          </Button>
        </div>
      </form>

      {error && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {error}
        </Alert>
      )}

      {loading ? (
        <div className="flex justify-center items-center p-8">
          <Spinner size="xl" />
        </div>
      ) : supplierProducts.length === 0 ? (
        <div className="p-8 text-center">
          <p className="text-gray-500">No products found for this supplier</p>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <Table hoverable>
              <Table.Head>
                <Table.HeadCell>Product</Table.HeadCell>
                <Table.HeadCell>SKU</Table.HeadCell>
                <Table.HeadCell>Unit</Table.HeadCell>
                <Table.HeadCell>Supplier Price</Table.HeadCell>
                <Table.HeadCell>
                  <div className="flex items-center">
                    Total Price
                    <Tooltip content="Price per base unit (Supplier Price × Conversion Factor)">
                      <HiOutlineInformationCircle className="ml-1 h-4 w-4 text-gray-400" />
                    </Tooltip>
                  </div>
                </Table.HeadCell>
                <Table.HeadCell>Min. Order</Table.HeadCell>
                <Table.HeadCell>Lead Time</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>
                  <span className="sr-only">Actions</span>
                </Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {supplierProducts.map((supplierProduct) => (
                  <Table.Row key={supplierProduct.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                    <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                      <div className="flex items-center gap-3">
                        {supplierProduct.product?.image_url ? (
                          <img
                            src={supplierProduct.product.image_url}
                            alt={supplierProduct.product?.name}
                            className="h-10 w-10 rounded-md object-cover"
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center">
                            <span className="text-gray-500 text-xs">No image</span>
                          </div>
                        )}
                        <div>
                          <button
                            onClick={() => supplierProduct.product?.id && navigateToProductDetails(supplierProduct.product.id)}
                            className="font-medium text-left hover:text-blue-600 hover:underline cursor-pointer focus:outline-none"
                          >
                            {supplierProduct.product?.name}
                          </button>
                          <p className="text-xs text-gray-500">
                            {supplierProduct.product?.category?.name || 'Uncategorized'}
                          </p>
                        </div>
                      </div>
                    </Table.Cell>
                    <Table.Cell>{supplierProduct.product?.sku || '-'}</Table.Cell>
                    <Table.Cell>
                      {supplierProduct.uom ? (
                        <Badge color="info">
                          {supplierProduct.uom.name} ({supplierProduct.uom.code})
                        </Badge>
                      ) : supplierProduct.uom_name ? (
                        <Badge color="info">
                          {supplierProduct.uom_name}
                        </Badge>
                      ) : (
                        <Badge color="gray">Default Unit</Badge>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {supplierProduct.unit_price !== null
                        ? formatWithCurrency(supplierProduct.unit_price)
                        : formatWithCurrency(supplierProduct.product?.unit_price || 0)}
                    </Table.Cell>
                    <Table.Cell>
                      {supplierProduct.unit_price !== null && supplierProduct.conversion_factor ? (
                        <Tooltip
                          content={`Calculation: ${formatWithCurrency(supplierProduct.unit_price)} × ${supplierProduct.conversion_factor} = ${formatWithCurrency(supplierProduct.unit_price * supplierProduct.conversion_factor)}`}
                        >
                          <div className="cursor-help">
                            {formatWithCurrency(supplierProduct.unit_price * supplierProduct.conversion_factor)}
                            <div className="text-xs text-gray-500">
                              per base unit
                            </div>
                          </div>
                        </Tooltip>
                      ) : supplierProduct.unit_price !== null ? (
                        <div>
                          {formatWithCurrency(supplierProduct.unit_price)}
                          <div className="text-xs text-gray-500">
                            (no conversion)
                          </div>
                        </div>
                      ) : (
                        <div>
                          {formatWithCurrency(supplierProduct.product?.unit_price || 0)}
                          <div className="text-xs text-gray-500">
                            (default price)
                          </div>
                        </div>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {supplierProduct.minimum_order_quantity !== null
                        ? supplierProduct.minimum_order_quantity
                        : '-'}
                    </Table.Cell>
                    <Table.Cell>
                      {supplierProduct.lead_time_days !== null
                        ? `${supplierProduct.lead_time_days} days`
                        : '-'}
                    </Table.Cell>
                    <Table.Cell>
                      {supplierProduct.is_preferred && (
                        <Badge color="success" icon={HiOutlineStar}>
                          Preferred
                        </Badge>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center space-x-2">
                        <Button
                          color="light"
                          size="xs"
                          onClick={() => openEditModal(supplierProduct)}
                          title="Edit Product"
                        >
                          <HiOutlinePencil className="h-4 w-4" />
                        </Button>
                        <Button
                          color="light"
                          size="xs"
                          onClick={() => openDeleteConfirm(supplierProduct)}
                          title="Remove Product"
                        >
                          <HiOutlineTrash className="h-4 w-4" />
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={onPageChange}
                showIcons
              />
            </div>
          )}
        </>
      )}

      {/* Edit Modal */}
      <Modal show={showEditModal} onClose={() => setShowEditModal(false)} size="lg">
        <Modal.Header>
          Edit Supplier Product
        </Modal.Header>
        <Modal.Body>
          {currentProduct && currentProduct.product && (
            <SimpleSupplierProductForm
              product={currentProduct.product}
              supplierId={supplierId}
              onSubmit={handleUpdateProduct}
              onCancel={() => setShowEditModal(false)}
              isSubmitting={isSubmitting}
              error={submitError || undefined}
            />
          )}
        </Modal.Body>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)} size="md">
        <Modal.Header>Confirm Removal</Modal.Header>
        <Modal.Body>
          {deleteError && (
            <Alert color="failure" icon={HiOutlineExclamation} className="mb-4">
              {deleteError}
            </Alert>
          )}
          <p className="text-gray-500">
            Are you sure you want to remove <span className="font-semibold">{productToDelete?.product?.name}</span> from this supplier?
          </p>
          <p className="text-gray-500 mt-2">
            This action cannot be undone. This will permanently remove the product from this supplier.
          </p>
          <div className="flex justify-end space-x-3 mt-4">
            <Button
              color="gray"
              onClick={() => setShowDeleteConfirm(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              color="failure"
              onClick={handleDeleteProduct}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Removing...
                </>
              ) : (
                'Remove Product'
              )}
            </Button>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default SupplierProductsList;
