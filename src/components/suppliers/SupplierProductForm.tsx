import { useState, useEffect } from 'react';
import {
  Button,
  TextInput,
  Textarea,
  Label,
  Checkbox,
  Spinner,
  Alert,
  Tooltip,
} from 'flowbite-react';
import { HiOutlineExclamation, HiOutlineInformationCircle } from 'react-icons/hi';
import { Product } from '../../services/product';
import { SupplierProduct, SupplierProductInput, SupplierProductFormData } from '../../services/supplierProduct';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import UomSelectorForSupplier from '../uom/UomSelectorForSupplier';
import { useOrganization } from '../../context/OrganizationContext';
import { getProductConversionFactors } from '../../services/productUom';

interface SupplierProductFormProps {
  product: Product;
  supplierProduct?: SupplierProduct;
  supplierId: string;
  onSubmit: (data: SupplierProductInput) => Promise<void>;
  onCancel: () => void;
  isSubmitting: boolean;
  error?: string;
}

const SupplierProductForm = ({
  product,
  supplierProduct,
  supplierId,
  onSubmit,
  onCancel,
  isSubmitting,
  error,
}: SupplierProductFormProps) => {
  const formatWithCurrency = useCurrencyFormatter();
  const { currentOrganization } = useOrganization();
  const [formData, setFormData] = useState<Partial<SupplierProductFormData>>({
    supplier_id: supplierId,
    product_id: product.id,
    unit_price: supplierProduct?.unit_price || null,
    minimum_order_quantity: supplierProduct?.minimum_order_quantity || null,
    lead_time_days: supplierProduct?.lead_time_days || null,
    is_preferred: supplierProduct?.is_preferred || false,
    notes: supplierProduct?.notes || '',
    uom_id: supplierProduct?.uom_id || null,
    uom_name: supplierProduct?.uom ? `${supplierProduct.uom.name} (${supplierProduct.uom.code})` : undefined,
    conversion_factor: supplierProduct?.conversion_factor || 1,
    available_conversion_factors: {}
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Fetch conversion factors for the product
  useEffect(() => {
    const fetchConversionFactors = async () => {
      if (!currentOrganization || !product.id) return;

      try {
        const { conversionFactors, error } = await getProductConversionFactors(
          product.id,
          currentOrganization.id
        );

        if (error) {
          console.error('Error fetching conversion factors:', error);
          return;
        }

        console.log('Fetched conversion factors:', conversionFactors);

        // Update form data with available conversion factors
        setFormData(prev => ({
          ...prev,
          available_conversion_factors: conversionFactors
        }));

        // If we have a UoM selected and a conversion factor for it, update the form
        // Only update if the conversion factor is still the default (1)
        if (formData.uom_id &&
            conversionFactors[formData.uom_id] &&
            (!formData.conversion_factor || formData.conversion_factor === 1)) {
          setFormData(prev => ({
            ...prev,
            conversion_factor: conversionFactors[formData.uom_id]
          }));
        }
      } catch (err) {
        console.error('Error in fetchConversionFactors:', err);
      }
    };

    fetchConversionFactors();
  }, [currentOrganization, product.id]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setFormData((prev) => ({
        ...prev,
        [name]: value === '' ? null : parseFloat(value)
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }

    // Clear validation error when field is changed
    if (validationErrors[name]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Validate unit price if provided
    if (formData.unit_price !== null && formData.unit_price !== undefined) {
      if (formData.unit_price < 0) {
        errors.unit_price = 'Unit price cannot be negative';
      }
      // Remove the validation that supplier price can't be higher than selling price
      // This allows setting any price for the supplier
    }

    // Validate minimum order quantity if provided
    if (formData.minimum_order_quantity !== null && formData.minimum_order_quantity !== undefined && formData.minimum_order_quantity < 0) {
      errors.minimum_order_quantity = 'Minimum order quantity cannot be negative';
    }

    // Validate lead time days if provided
    if (formData.lead_time_days !== null && formData.lead_time_days !== undefined && formData.lead_time_days < 0) {
      errors.lead_time_days = 'Lead time days cannot be negative';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Log the form data for debugging
    console.log('Submitting supplier product form with data:', formData);

    // Calculate the base price
    const basePrice = formData.unit_price !== undefined &&
                      formData.unit_price !== null &&
                      formData.conversion_factor ?
                      formData.unit_price * formData.conversion_factor : null;

    console.log('Calculated base price:', {
      unit_price: formData.unit_price,
      conversion_factor: formData.conversion_factor,
      base_price: basePrice
    });

    // Ensure all required fields are present (excluding UI-only fields)
    const supplierProductData: SupplierProductInput = {
      supplier_id: supplierId,
      product_id: product.id,
      unit_price: formData.unit_price !== undefined ? formData.unit_price : null,
      minimum_order_quantity: formData.minimum_order_quantity !== undefined ? formData.minimum_order_quantity : null,
      lead_time_days: formData.lead_time_days !== undefined ? formData.lead_time_days : null,
      is_preferred: formData.is_preferred !== undefined ? formData.is_preferred : false,
      notes: formData.notes || null,
      // Explicitly set UoM fields
      uom_id: formData.uom_id || null,
      uom_name: formData.uom_name || null, // Include UoM name
      conversion_factor: formData.conversion_factor || 1,
      // Include the organization_id
      organization_id: currentOrganization?.id,
      // Explicitly calculate base price
      base_price: basePrice
    };

    // Log the final data being sent to the server
    console.log('Final supplier product data being submitted:', supplierProductData);

    await onSubmit(supplierProductData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {error}
        </Alert>
      )}

      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
        <div className="flex items-center gap-3 mb-2">
          {product.image_url ? (
            <img
              src={product.image_url}
              alt={product.name}
              className="h-12 w-12 rounded-md object-cover"
            />
          ) : (
            <div className="h-12 w-12 rounded-md bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500 text-xs">No image</span>
            </div>
          )}
          <div>
            <h3 className="font-medium text-lg">{product.name}</h3>
            <div className="text-sm text-gray-500">
              <span className="mr-3">SKU: {product.sku || 'N/A'}</span>
              <span>Current Price: {formatWithCurrency(product.unit_price)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Unit of Measurement */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="uom_id" value="Unit of Measurement" />
        </div>
        <UomSelectorForSupplier
          value={formData.uom_id || ''}
          onChange={(uomId, uomName) => {
            // Get the conversion factor for this UoM from product_uoms
            let conversionFactor = formData.available_conversion_factors?.[uomId];

            // If no conversion factor found in product_uoms, use the default (1)
            if (!conversionFactor) {
              conversionFactor = 1;
            }

            // Log the conversion factor for debugging
            console.log('Using conversion factor for UoM:', {
              uomId,
              conversionFactor,
              availableFactors: formData.available_conversion_factors
            });

            // Store the original conversion factor from product_uoms
            const originalConversionFactor = conversionFactor;

            console.log('UoM changed:', {
              from: formData.uom_id,
              to: uomId,
              conversionFactor,
              uomName,
              originalConversionFactor
            });

            // Only update the conversion factor if:
            // 1. It's the first time setting a UoM (formData.uom_id is null)
            // 2. The UoM has changed AND the user hasn't manually modified the conversion factor
            const shouldUpdateConversionFactor =
              formData.uom_id === null ||
              (formData.uom_id !== uomId &&
               formData.conversion_factor === formData.available_conversion_factors?.[formData.uom_id]);

            console.log('Should update conversion factor?', shouldUpdateConversionFactor);

            setFormData(prev => ({
              ...prev,
              uom_id: uomId,
              uom_name: uomName,
              conversion_factor: shouldUpdateConversionFactor ? originalConversionFactor : prev.conversion_factor
            }));
          }}
        />
      </div>

      {/* Conversion Factor */}
      <div>
        <div className="mb-2 flex items-center">
          <Label htmlFor="conversion_factor" value="Conversion Factor" />
          <Tooltip content="This is the factor used to convert between units. For example, if this UoM is 'case' and each case contains 12 pieces, the conversion factor would be 12.">
            <HiOutlineInformationCircle className="ml-2 h-5 w-5 text-gray-400" />
          </Tooltip>
        </div>
        <TextInput
          id="conversion_factor"
          name="conversion_factor"
          type="number"
          step="0.01"
          min="0.01"
          value={formData.conversion_factor || 1}
          onChange={(e) => {
            const value = parseFloat(e.target.value);
            if (!isNaN(value) && value > 0) {
              console.log('Conversion factor changed to:', value);
              setFormData(prev => ({
                ...prev,
                conversion_factor: value
              }));
            }
          }}
          helperText="Factor to convert between this unit and the base unit"
        />
      </div>

      {/* Unit Price */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="unit_price" value="Unit Price from Supplier" />
        </div>
        <TextInput
          id="unit_price"
          name="unit_price"
          type="number"
          step="0.01"
          value={formData.unit_price === null ? '' : formData.unit_price}
          onChange={handleChange}
          placeholder="Enter unit price"
          color={validationErrors.unit_price ? 'failure' : undefined}
          helperText={validationErrors.unit_price || "Leave blank to use the product's default price"}
        />
      </div>

      {/* Minimum Order Quantity */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="minimum_order_quantity" value="Minimum Order Quantity" />
        </div>
        <TextInput
          id="minimum_order_quantity"
          name="minimum_order_quantity"
          type="number"
          min="0"
          step="1"
          value={formData.minimum_order_quantity === null ? '' : formData.minimum_order_quantity}
          onChange={handleChange}
          placeholder="Enter minimum order quantity"
          color={validationErrors.minimum_order_quantity ? 'failure' : undefined}
          helperText={validationErrors.minimum_order_quantity}
        />
      </div>

      {/* Lead Time Days */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="lead_time_days" value="Lead Time (Days)" />
        </div>
        <TextInput
          id="lead_time_days"
          name="lead_time_days"
          type="number"
          min="0"
          step="1"
          value={formData.lead_time_days === null ? '' : formData.lead_time_days}
          onChange={handleChange}
          placeholder="Enter lead time in days"
          color={validationErrors.lead_time_days ? 'failure' : undefined}
          helperText={validationErrors.lead_time_days}
        />
      </div>

      {/* Preferred Supplier */}
      <div className="flex items-center gap-2">
        <Checkbox
          id="is_preferred"
          name="is_preferred"
          checked={formData.is_preferred === true}
          onChange={handleChange}
        />
        <Label htmlFor="is_preferred" className="flex">
          Preferred supplier for this product
        </Label>
      </div>

      {/* Notes */}
      <div>
        <div className="mb-2 block">
          <Label htmlFor="notes" value="Notes" />
        </div>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes || ''}
          onChange={handleChange}
          placeholder="Enter additional notes about this supplier-product relationship"
          rows={3}
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button color="gray" onClick={onCancel} type="button">
          Cancel
        </Button>
        <Button color="primary" type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Product'
          )}
        </Button>
      </div>
    </form>
  );
};

export default SupplierProductForm;
