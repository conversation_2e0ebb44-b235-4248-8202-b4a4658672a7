import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'flowbite-react';
import { Product } from '../../services/product';
import { SupplierProductInput } from '../../services/supplierProduct';
import SimpleSupplierProductForm from './SimpleSupplierProductForm';
import { useOrganization } from '../../context/OrganizationContext';
import { supabase } from '../../lib/supabase';

interface SimpleSupplierProductModalProps {
  show: boolean;
  onClose: () => void;
  onSuccess: () => void;
  supplierId: string;
  product: Product;
  currentUnitPrice?: number;
  currentUomId?: string;
}

const SimpleSupplierProductModal = ({
  show,
  onClose,
  onSuccess,
  supplierId,
  product,
  currentUnitPrice,
  currentUomId,
}: SimpleSupplierProductModalProps) => {
  const { currentOrganization } = useOrganization();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [existingSupplierProduct, setExistingSupplierProduct] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch the existing supplier product when the modal is shown
  useEffect(() => {
    if (show && supplierId && product.id) {
      const fetchSupplierProduct = async () => {
        setIsLoading(true);
        try {
          const { data, error } = await supabase
            .from('supplier_products')
            .select(`
              *,
              uom:uom_id (
                id,
                code,
                name
              )
            `)
            .eq('supplier_id', supplierId)
            .eq('product_id', product.id)
            .maybeSingle();

          if (error) {
            console.error('Error fetching supplier product in modal:', error);
          } else if (data) {
            // Store the fetched supplier product
            setExistingSupplierProduct(data);
          } else {
            setExistingSupplierProduct(null);
          }
        } catch (err) {
          console.error('Exception fetching supplier product in modal:', err);
        } finally {
          setIsLoading(false);
        }
      };

      fetchSupplierProduct();
    }
  }, [show, supplierId, product.id]);

  const handleSubmit = async (data: SupplierProductInput) => {
    if (!currentOrganization) {
      setError('No organization selected');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Prepare supplier product data for submission

      // Ensure unit_price is a number
      const unitPrice = data.unit_price === null || data.unit_price === undefined ?
                      product.unit_price || 0 :
                      data.unit_price;

      // Calculate base price
      const basePrice = unitPrice * (data.conversion_factor || 1);

      // Calculate the base price (unit_price * conversion_factor)

      // Prepare the data with explicit values
      const insertData = {
        supplier_id: supplierId,
        product_id: product.id,
        unit_price: unitPrice,
        minimum_order_quantity: data.minimum_order_quantity,
        lead_time_days: data.lead_time_days,
        is_preferred: data.is_preferred,
        notes: data.notes,
        uom_id: data.uom_id,
        uom_name: data.uom_name,
        conversion_factor: data.conversion_factor || 1,
        organization_id: currentOrganization.id,
        base_price: basePrice
      };

      // Check if the supplier product already exists
      const { data: existingData, error: checkError } = await supabase
        .from('supplier_products')
        .select('id')
        .eq('supplier_id', supplierId)
        .eq('product_id', product.id)
        .maybeSingle();

      // Determine whether to update or insert

      let result;
      let submitError;

      if (existingData) {
        // Update existing record
        const { data, error } = await supabase
          .from('supplier_products')
          .update(insertData)
          .eq('id', existingData.id)
          .select('*')
          .single();

        result = data;
        submitError = error;
      } else {
        // Insert new record
        const { data, error } = await supabase
          .from('supplier_products')
          .insert(insertData)
          .select('*')
          .single();

        result = data;
        submitError = error;
      }

      if (submitError) {
        console.error('Error saving supplier product:', submitError);
        setError(submitError.message);
      } else {
        // Supplier product saved successfully
        onSuccess();
        onClose();
      }
    } catch (err: any) {
      console.error('Exception saving supplier product:', err);
      setError(err.message || 'An error occurred while saving the product');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal show={show} onClose={onClose} size="lg">
      <Modal.Header>
        {existingSupplierProduct ? 'Edit Supplier Product' : 'Add Product to Supplier'}
      </Modal.Header>
      <Modal.Body>
        {isLoading ? (
          <div className="flex justify-center py-4">
            <Spinner size="xl" />
          </div>
        ) : (
          <SimpleSupplierProductForm
            product={product}
            supplierId={supplierId}
            onSubmit={handleSubmit}
            onCancel={onClose}
            isSubmitting={isSubmitting}
            error={error}
            currentUnitPrice={currentUnitPrice}
            currentUomId={currentUomId}
            existingSupplierProduct={existingSupplierProduct}
          />
        )}
      </Modal.Body>
    </Modal>
  );
};

export default SimpleSupplierProductModal;
