import { useState } from 'react';
import { Button } from 'flowbite-react';
import { HiOutlinePlus } from 'react-icons/hi';
import { Product } from '../../services/product';
import SimpleSupplierProductModal from './SimpleSupplierProductModal';

interface AddProductToSupplierButtonProps {
  supplierId: string;
  product: Product;
  onSuccess: () => void;
  buttonText?: string;
  buttonSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  buttonColor?: 'primary' | 'secondary' | 'success' | 'warning' | 'failure' | 'info' | 'light' | 'dark' | 'gray';
}

const AddProductToSupplierButton = ({
  supplierId,
  product,
  onSuccess,
  buttonText = 'Add Product',
  buttonSize = 'md',
  buttonColor = 'primary'
}: AddProductToSupplierButtonProps) => {
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <Button
        size={buttonSize}
        color={buttonColor}
        onClick={() => setShowModal(true)}
      >
        <HiOutlinePlus className="mr-2 h-4 w-4" />
        {buttonText}
      </Button>

      <SimpleSupplierProductModal
        show={showModal}
        onClose={() => setShowModal(false)}
        onSuccess={onSuccess}
        supplierId={supplierId}
        product={product}
      />
    </>
  );
};

export default AddProductToSupplierButton;
