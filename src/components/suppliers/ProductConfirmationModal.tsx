import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Badge,
} from 'flowbite-react';
import {
  HiOutlineExclamation,
  HiOutlineTrash,
  HiOutlineArrowLeft,
  HiOutlineArrowRight
} from 'react-icons/hi';
import { Product } from '../../services/product';
import { ProductUom, UnitOfMeasurement } from '../../types/uom.types';
import { SupplierProductInput, SupplierProductFormData, addSupplierProducts } from '../../services/supplierProduct';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import SimpleSupplierProductModal from './SimpleSupplierProductModal';
import { useOrganization } from '../../context/OrganizationContext';

// Extend Product type to include UoMs
type ProductWithUoms = Product & {
  uoms?: (ProductUom & { uom: UnitOfMeasurement })[];
};

interface ProductConfirmationModalProps {
  show: boolean;
  onClose: () => void;
  products: ProductWithUoms[];
  supplierId: string;
  onSuccess: () => void;
}

const ProductConfirmationModal = ({
  show,
  onClose,
  products,
  supplierId,
  onSuccess,
}: ProductConfirmationModalProps) => {
  const formatWithCurrency = useCurrencyFormatter();
  const { currentOrganization } = useOrganization();
  const [activeTab, setActiveTab] = useState<number>(0);
  const [productForms, setProductForms] = useState<Record<string, SupplierProductFormData>>({});
  const [currentProductIndex, setCurrentProductIndex] = useState<number>(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [removedProducts, setRemovedProducts] = useState<Record<string, boolean>>({});
  const [showSimpleModal, setShowSimpleModal] = useState(false);

  // Get current product
  const currentProduct = products[currentProductIndex];

  // Count of products not removed
  const activeProductCount = products.filter(p => !removedProducts[p.id]).length;

  // Handle form submission for a product
  const handleProductFormSubmit = async (data: SupplierProductInput) => {
    console.log('Product form submitted with data:', data);

    // Save the form data (including any UI-only fields)
    setProductForms(prev => ({
      ...prev,
      [data.product_id]: {
        ...data,
        // Add any UI-specific fields needed
        available_conversion_factors: {}
      } as SupplierProductFormData
    }));

    // Move to the next product or submit all if this is the last one
    if (currentProductIndex < products.length - 1) {
      // Find next product that hasn't been removed
      let nextIndex = currentProductIndex + 1;
      while (nextIndex < products.length && removedProducts[products[nextIndex].id]) {
        nextIndex++;
      }

      if (nextIndex < products.length) {
        setCurrentProductIndex(nextIndex);
      } else {
        // If all remaining products are removed, submit what we have
        await handleSubmitAll();
      }
    } else {
      // This is the last product, submit all
      await handleSubmitAll();
    }
  };

  // Handle removing a product from the list
  const handleRemoveProduct = (productId: string) => {
    setRemovedProducts(prev => ({
      ...prev,
      [productId]: true
    }));

    // If we're removing the current product, move to the next one
    if (currentProduct && currentProduct.id === productId) {
      // Find next product that hasn't been removed
      let nextIndex = currentProductIndex + 1;
      while (nextIndex < products.length &&
             (removedProducts[products[nextIndex].id] || products[nextIndex].id === productId)) {
        nextIndex++;
      }

      if (nextIndex < products.length) {
        setCurrentProductIndex(nextIndex);
      } else {
        // Try to find a previous product that hasn't been removed
        let prevIndex = currentProductIndex - 1;
        while (prevIndex >= 0 &&
               (removedProducts[products[prevIndex].id] || products[prevIndex].id === productId)) {
          prevIndex--;
        }

        if (prevIndex >= 0) {
          setCurrentProductIndex(prevIndex);
        }
      }
    }
  };

  // Handle submitting all products
  const handleSubmitAll = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Filter out removed products and prepare data for submission
      const productsToAdd = products
        .filter(p => !removedProducts[p.id])
        .map(p => {
          // Use form data if available, otherwise create default
          const formData = productForms[p.id];

          if (formData) {
            // Keep the UoM name for the database
            const { available_conversion_factors, ...cleanData } = formData;

            // Ensure organization_id is set
            if (!cleanData.organization_id && currentOrganization) {
              cleanData.organization_id = currentOrganization.id;
            }

            // Calculate base_price if both unit_price and conversion_factor are available
            if (cleanData.unit_price !== undefined && cleanData.unit_price !== null &&
                cleanData.conversion_factor !== undefined && cleanData.conversion_factor !== null) {
              cleanData.base_price = cleanData.unit_price * cleanData.conversion_factor;
            }

            console.log('Prepared product data:', cleanData);

            return cleanData;
          } else {
            // Create default data with UoM fields
            return {
              supplier_id: supplierId,
              product_id: p.id,
              unit_price: null,
              minimum_order_quantity: null,
              lead_time_days: null,
              is_preferred: false,
              notes: null,
              organization_id: currentOrganization?.id,
              // Default UoM fields
              uom_id: null,
              uom_name: null,
              conversion_factor: 1,
              base_price: null
            };
          }
        });

      if (productsToAdd.length === 0) {
        onClose();
        return;
      }

      console.log('Submitting products to add:', productsToAdd);

      const { success, error, data } = await addSupplierProducts(supplierId, productsToAdd);

      if (error) {
        setError(error);
      } else if (success) {
        console.log('Products added successfully:', data);
        onSuccess();
        onClose();
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while adding products');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    // Reset state
    setProductForms({});
    setCurrentProductIndex(0);
    setRemovedProducts({});
    setError(null);
    onClose();
  };

  return (
    <Modal show={show} onClose={handleCancel} size="xl">
      <Modal.Header>
        Add Products to Supplier
      </Modal.Header>
      <Modal.Body>
        <div className="space-y-4">
          {error && (
            <Alert color="failure" icon={HiOutlineExclamation}>
              {error}
            </Alert>
          )}

          <div>
            {/* Custom Tabs */}
            <div className="mb-4 border-b border-gray-200">
              <ul className="flex flex-wrap -mb-px text-sm font-medium text-center">
                <li className="mr-2" role="presentation">
                  <button
                    className={`inline-block p-4 border-b-2 rounded-t-lg ${
                      activeTab === 0
                        ? 'border-blue-600 text-blue-600'
                        : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                    }`}
                    type="button"
                    onClick={() => setActiveTab(0)}
                  >
                    Product Details
                  </button>
                </li>
                <li className="mr-2" role="presentation">
                  <button
                    className={`inline-block p-4 border-b-2 rounded-t-lg ${
                      activeTab === 1
                        ? 'border-blue-600 text-blue-600'
                        : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                    }`}
                    type="button"
                    onClick={() => setActiveTab(1)}
                  >
                    Review All Products
                  </button>
                </li>
              </ul>
            </div>

            {/* Tab Content */}
            <div className="mt-4">
              {/* Product Details Tab */}
              {activeTab === 0 && (
                <>
                  {activeProductCount === 0 ? (
                    <div className="p-4 text-center">
                      <p className="text-gray-500">No products selected</p>
                    </div>
                  ) : currentProduct && !removedProducts[currentProduct.id] ? (
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-medium">
                          Product {currentProductIndex + 1} of {activeProductCount}
                        </h3>
                        <div className="flex items-center space-x-2">
                          <Button
                            color="light"
                            size="xs"
                            onClick={() => {
                              // Find previous product that hasn't been removed
                              let prevIndex = currentProductIndex - 1;
                              while (prevIndex >= 0 && removedProducts[products[prevIndex].id]) {
                                prevIndex--;
                              }
                              if (prevIndex >= 0) {
                                setCurrentProductIndex(prevIndex);
                              }
                            }}
                            disabled={currentProductIndex === 0 ||
                              !products.slice(0, currentProductIndex).some(p => !removedProducts[p.id])}
                          >
                            <HiOutlineArrowLeft className="mr-1 h-4 w-4" />
                            Previous
                          </Button>
                          <Button
                            color="light"
                            size="xs"
                            onClick={() => {
                              // Find next product that hasn't been removed
                              let nextIndex = currentProductIndex + 1;
                              while (nextIndex < products.length && removedProducts[products[nextIndex].id]) {
                                nextIndex++;
                              }
                              if (nextIndex < products.length) {
                                setCurrentProductIndex(nextIndex);
                              }
                            }}
                            disabled={currentProductIndex >= products.length - 1 ||
                              !products.slice(currentProductIndex + 1).some(p => !removedProducts[p.id])}
                          >
                            Next
                            <HiOutlineArrowRight className="ml-1 h-4 w-4" />
                          </Button>
                          <Button
                            color="light"
                            size="xs"
                            onClick={() => handleRemoveProduct(currentProduct.id)}
                          >
                            <HiOutlineTrash className="mr-1 h-4 w-4" />
                            Remove
                          </Button>
                        </div>
                      </div>

                      <div className="p-4 text-center">
                        <p className="text-gray-500 mb-4">Click the button below to add this product to the supplier.</p>
                        <Button
                          color="primary"
                          onClick={() => {
                            // Show the SimpleSupplierProductModal
                            setShowSimpleModal(true);
                          }}
                        >
                          Configure Product
                        </Button>
                      </div>

                      {/* Use SimpleSupplierProductModal for direct database access */}
                      {showSimpleModal && (
                        <SimpleSupplierProductModal
                          show={showSimpleModal}
                          onClose={() => setShowSimpleModal(false)}
                          onSuccess={() => {
                            setShowSimpleModal(false);
                            // Mark this product as configured
                            setProductForms(prev => ({
                              ...prev,
                              [currentProduct.id]: {
                                supplier_id: supplierId,
                                product_id: currentProduct.id,
                                unit_price: 0,
                                organization_id: currentOrganization?.id
                              } as SupplierProductFormData
                            }));

                            // Move to the next product
                            let nextIndex = currentProductIndex + 1;
                            while (nextIndex < products.length && removedProducts[products[nextIndex].id]) {
                              nextIndex++;
                            }

                            if (nextIndex < products.length) {
                              setCurrentProductIndex(nextIndex);
                            } else {
                              // If no more products, switch to review tab
                              setActiveTab(1);
                            }
                          }}
                          supplierId={supplierId}
                          product={currentProduct}
                        />
                      )}
                    </div>
                  ) : (
                    <div className="p-4 text-center">
                      <p className="text-gray-500">No product selected</p>
                    </div>
                  )}
                </>
              )}

              {/* Review All Products Tab */}
              {activeTab === 1 && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">
                      Selected Products ({activeProductCount})
                    </h3>
                  </div>

                  {activeProductCount === 0 ? (
                    <div className="p-4 text-center">
                      <p className="text-gray-500">No products selected</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <Table>
                        <Table.Head>
                          <Table.HeadCell>Product</Table.HeadCell>
                          <Table.HeadCell>SKU</Table.HeadCell>
                          <Table.HeadCell>Unit</Table.HeadCell>
                          <Table.HeadCell>Price</Table.HeadCell>
                          <Table.HeadCell>Status</Table.HeadCell>
                          <Table.HeadCell>
                            <span className="sr-only">Actions</span>
                          </Table.HeadCell>
                        </Table.Head>
                        <Table.Body className="divide-y">
                          {products.map((product, index) => {
                            if (removedProducts[product.id]) return null;

                            const formData = productForms[product.id];
                            const isConfigured = !!formData;

                            return (
                              <Table.Row key={product.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                                  <div className="flex items-center gap-3">
                                    {product.image_url ? (
                                      <img
                                        src={product.image_url}
                                        alt={product.name}
                                        className="h-10 w-10 rounded-md object-cover"
                                      />
                                    ) : (
                                      <div className="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center">
                                        <span className="text-gray-500 text-xs">No image</span>
                                      </div>
                                    )}
                                    <div>
                                      <p className="font-medium">{product.name}</p>
                                      <p className="text-xs text-gray-500">
                                        {product.category?.name || 'Uncategorized'}
                                      </p>
                                    </div>
                                  </div>
                                </Table.Cell>
                                <Table.Cell>{product.sku || '-'}</Table.Cell>
                                <Table.Cell>
                                  {formData?.uom_id ? (
                                    <Badge color="info">
                                      {formData.uom_name || 'Custom Unit'}
                                    </Badge>
                                  ) : (
                                    <Badge color="gray">Default Unit</Badge>
                                  )}
                                </Table.Cell>
                                <Table.Cell>
                                  {formData?.unit_price !== null && formData?.unit_price !== undefined ? (
                                    <div>
                                      <div>{formatWithCurrency(formData.unit_price)}</div>
                                      {formData.conversion_factor && formData.conversion_factor !== 1 && (
                                        <div className="text-xs text-gray-500">
                                          Base: {formatWithCurrency(formData.unit_price * (formData.conversion_factor || 1))} per base unit
                                        </div>
                                      )}
                                    </div>
                                  ) : (
                                    formatWithCurrency(product.unit_price)
                                  )}
                                </Table.Cell>
                                <Table.Cell>
                                  {isConfigured ? (
                                    <Badge color="success">Configured</Badge>
                                  ) : (
                                    <Badge color="gray">Not Configured</Badge>
                                  )}
                                </Table.Cell>
                                <Table.Cell>
                                  <div className="flex items-center space-x-2">
                                    <Button
                                      color="light"
                                      size="xs"
                                      onClick={() => {
                                        setCurrentProductIndex(index);
                                        setActiveTab(0);
                                      }}
                                    >
                                      {isConfigured ? 'Edit' : 'Configure'}
                                    </Button>
                                    <Button
                                      color="light"
                                      size="xs"
                                      onClick={() => handleRemoveProduct(product.id)}
                                    >
                                      <HiOutlineTrash className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </Table.Cell>
                              </Table.Row>
                            );
                          })}
                        </Table.Body>
                      </Table>
                    </div>
                  )}

                  {activeProductCount > 0 && (
                    <div className="flex justify-end space-x-3 pt-4">
                      <Button color="gray" onClick={handleCancel} type="button">
                        Cancel
                      </Button>
                      <Button
                        color="primary"
                        onClick={handleSubmitAll}
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <Spinner size="sm" className="mr-2" />
                            Saving...
                          </>
                        ) : (
                          'Save All Products'
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ProductConfirmationModal;
