import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Spinner,
  Alert,
  TextInput,
  Pagination,
  Table,
  Checkbox,
  Badge,
} from 'flowbite-react';
import { HiOutlineSearch, HiOutlineExclamation } from 'react-icons/hi';
import { getAvailableProducts } from '../../services/supplierProduct';
import { Product } from '../../services/product';
import { useOrganization } from '../../context/OrganizationContext';
import { formatCurrency } from '../../utils/formatters';

interface ProductSelectionModalProps {
  show: boolean;
  onClose: () => void;
  supplierId: string;
  onProductsSelected: (products: Product[]) => void;
}

const ProductSelectionModal = ({
  show,
  onClose,
  supplierId,
  onProductsSelected,
}: ProductSelectionModalProps) => {
  const { currentOrganization } = useOrganization();
  
  // State for products
  const [products, setProducts] = useState<Product[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  
  // State for search
  const [searchQuery, setSearchQuery] = useState('');
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  
  // State for selected products
  const [selectedProducts, setSelectedProducts] = useState<Record<string, Product>>({});
  const selectedCount = Object.keys(selectedProducts).length;

  // Fetch products when organization, page, or search changes
  useEffect(() => {
    if (show) {
      fetchProducts();
    }
  }, [currentOrganization, currentPage, show]);

  // Handle search with debounce
  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      if (show) {
        fetchProducts();
      }
    }, 300);

    setSearchTimeout(timeout);

    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchQuery]);

  const fetchProducts = async () => {
    if (!currentOrganization || !supplierId) return;

    setLoading(true);
    setError(null);

    try {
      const { products: productData, count, error: fetchError } = await getAvailableProducts(
        currentOrganization.id,
        supplierId,
        {
          searchQuery,
          limit: pageSize,
          offset: (currentPage - 1) * pageSize,
        }
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setProducts(productData);
        setTotalCount(count);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching products');
    } finally {
      setLoading(false);
    }
  };

  // Handle product selection
  const toggleProductSelection = (product: Product) => {
    setSelectedProducts((prev) => {
      const newSelected = { ...prev };
      if (newSelected[product.id]) {
        delete newSelected[product.id];
      } else {
        newSelected[product.id] = product;
      }
      return newSelected;
    });
  };

  // Handle page change
  const onPageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle search submit
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchProducts();
  };

  // Handle confirm selection
  const handleConfirmSelection = () => {
    onProductsSelected(Object.values(selectedProducts));
    onClose();
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <Modal show={show} onClose={onClose} size="xl">
      <Modal.Header>
        Select Products for Supplier
      </Modal.Header>
      <Modal.Body>
        <div className="space-y-4">
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="mb-4">
            <div className="flex gap-2">
              <TextInput
                id="search"
                type="text"
                placeholder="Search products by name, SKU, or barcode"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1"
                icon={HiOutlineSearch}
              />
              <Button type="submit">
                Search
              </Button>
            </div>
          </form>

          {/* Selected Products Counter */}
          <div className="flex justify-between items-center">
            <div>
              <Badge color="info" className="text-sm">
                {selectedCount} {selectedCount === 1 ? 'product' : 'products'} selected
              </Badge>
            </div>
          </div>

          {error && (
            <Alert color="failure" icon={HiOutlineExclamation}>
              {error}
            </Alert>
          )}

          {loading ? (
            <div className="flex justify-center items-center p-8">
              <Spinner size="xl" />
            </div>
          ) : products.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-gray-500">No products found</p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <Table hoverable>
                  <Table.Head>
                    <Table.HeadCell className="w-10">
                      <span className="sr-only">Select</span>
                    </Table.HeadCell>
                    <Table.HeadCell>Product</Table.HeadCell>
                    <Table.HeadCell>SKU</Table.HeadCell>
                    <Table.HeadCell>Category</Table.HeadCell>
                    <Table.HeadCell>Price</Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {products.map((product) => (
                      <Table.Row 
                        key={product.id} 
                        className={`bg-white dark:border-gray-700 dark:bg-gray-800 ${
                          selectedProducts[product.id] ? 'bg-blue-50 dark:bg-blue-900' : ''
                        }`}
                      >
                        <Table.Cell>
                          <Checkbox
                            checked={!!selectedProducts[product.id]}
                            onChange={() => toggleProductSelection(product)}
                          />
                        </Table.Cell>
                        <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                          <div className="flex items-center gap-3">
                            {product.image_url ? (
                              <img
                                src={product.image_url}
                                alt={product.name}
                                className="h-10 w-10 rounded-md object-cover"
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center">
                                <span className="text-gray-500 text-xs">No image</span>
                              </div>
                            )}
                            <div>
                              <p className="font-medium">{product.name}</p>
                            </div>
                          </div>
                        </Table.Cell>
                        <Table.Cell>{product.sku || '-'}</Table.Cell>
                        <Table.Cell>
                          {(product as any).category?.name || 'Uncategorized'}
                        </Table.Cell>
                        <Table.Cell>{formatCurrency(product.unit_price)}</Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={onPageChange}
                    showIcons
                  />
                </div>
              )}
            </>
          )}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="flex justify-between w-full">
          <div>
            <Badge color="info" className="text-sm">
              {selectedCount} {selectedCount === 1 ? 'product' : 'products'} selected
            </Badge>
          </div>
          <div className="flex space-x-2">
            <Button color="gray" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              color="primary" 
              onClick={handleConfirmSelection}
              disabled={selectedCount === 0}
            >
              {selectedCount === 0 ? 'Select Products' : `Add ${selectedCount} Products`}
            </Button>
          </div>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default ProductSelectionModal;
