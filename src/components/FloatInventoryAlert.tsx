import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'flowbite-react';
import { HiOutlineExclamation, HiOutlineExternalLink } from 'react-icons/hi';
import { useOrganization } from '../context/OrganizationContext';
import { getFloatInventoryReport, FloatInventoryReport } from '../services/floatInventory';
import { formatQuantityWithSeparators } from '../utils/formatters';

interface FloatInventoryAlertProps {
  threshold?: number; // Days threshold for alerts
  maxItems?: number; // Maximum number of items to show
  showOnEmpty?: boolean; // Whether to show the component when there are no alerts
}

const FloatInventoryAlert: React.FC<FloatInventoryAlertProps> = ({
  threshold = 7,
  maxItems = 5,
  showOnEmpty = false
}) => {
  const { currentOrganization } = useOrganization();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [alertItems, setAlertItems] = useState<FloatInventoryReport[]>([]);

  useEffect(() => {
    const loadAlertData = async () => {
      if (!currentOrganization?.id) return;

      setLoading(true);
      setError(null);

      try {
        // Get unresolved float inventory items
        const { report, error: reportError } = await getFloatInventoryReport(
          currentOrganization.id,
          { resolved: false }
        );

        if (reportError) throw new Error(reportError);

        // Filter for items that exceed the threshold
        const alertableItems = report.filter(
          item => item.days_unresolved && item.days_unresolved >= threshold
        );

        setAlertItems(alertableItems);
      } catch (err: any) {
        setError(err.message || 'Failed to load alert data');
      } finally {
        setLoading(false);
      }
    };

    loadAlertData();

    // Set up interval to refresh data every 30 minutes
    const intervalId = setInterval(loadAlertData, 30 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, [currentOrganization, threshold]);

  // Don't render anything if there are no alerts and showOnEmpty is false
  if (!loading && alertItems.length === 0 && !showOnEmpty) {
    return null;
  }

  return (
    <Alert
      color="warning"
      icon={HiOutlineExclamation}
      className="mb-4"
    >
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-medium mb-2">
            Float Inventory Alerts
            {alertItems.length > 0 && (
              <Badge color="failure" className="ml-2">
                {alertItems.length}
              </Badge>
            )}
          </h3>

          {loading ? (
            <div className="flex items-center space-x-2">
              <Spinner size="sm" />
              <span>Loading alerts...</span>
            </div>
          ) : error ? (
            <p className="text-red-600">{error}</p>
          ) : alertItems.length === 0 ? (
            <p>No float inventory items require attention.</p>
          ) : (
            <>
              <p className="mb-2">
                The following items have been unresolved for {threshold}+ days:
              </p>
              <ul className="list-disc pl-5 space-y-1 mb-2">
                {alertItems.slice(0, maxItems).map(item => (
                  <li key={item.id}>
                    <Link
                      to={`/inventory/float/${item.id}`}
                      className="text-blue-600 hover:underline"
                    >
                      {item.product_name}
                    </Link>
                    <span> - {formatQuantityWithSeparators(item.quantity)} units - Unresolved for </span>
                    <span className="font-semibold text-red-600">
                      {item.days_unresolved} days
                    </span>
                  </li>
                ))}
                {alertItems.length > maxItems && (
                  <li>And {alertItems.length - maxItems} more items...</li>
                )}
              </ul>
            </>
          )}
        </div>

        <Button
          size="xs"
          color="warning"
          as={Link}
          to="/inventory/float/report"
        >
          <HiOutlineExternalLink className="mr-1 h-4 w-4" />
          View Report
        </Button>
      </div>
    </Alert>
  );
};

export default FloatInventoryAlert;
