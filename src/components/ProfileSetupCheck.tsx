import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Button, Modal, Label, TextInput, Spinner } from 'flowbite-react';
import { useAuth } from '../context/AuthContext';
import { checkUserProfile, checkUserOrganizations, createUserProfile, createUserOrganization } from '../services/userProfile';

const ProfileSetupCheck = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [profileStatus, setProfileStatus] = useState<{
    hasProfile: boolean;
    hasOrganization: boolean;
    error?: string;
  }>({
    hasProfile: false,
    hasOrganization: false
  });

  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showOrgModal, setShowOrgModal] = useState(false);

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [organizationName, setOrganizationName] = useState('');

  const [creatingProfile, setCreatingProfile] = useState(false);
  const [creatingOrg, setCreatingOrg] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const checkUserSetup = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      setLoading(true);

      try {
        // Check if there's pending profile setup data from registration
        const pendingSetupData = localStorage.getItem('pendingProfileSetup');
        if (pendingSetupData) {
          const setupData = JSON.parse(pendingSetupData);

          // Set the form values from the stored data
          setFirstName(setupData.firstName || '');
          setLastName(setupData.lastName || '');
          setOrganizationName(setupData.organizationName || '');

          console.log('Found pending profile setup data:', setupData);
        } else {
          // Try to get user metadata from Supabase
          if (user.user_metadata) {
            setFirstName(user.user_metadata.first_name || '');
            setLastName(user.user_metadata.last_name || '');
            setOrganizationName(user.user_metadata.organization_name || '');
          }
        }

        // Check if user has a profile
        const profileCheck = await checkUserProfile(user.id);

        // Check if user has organizations
        const orgCheck = await checkUserOrganizations(user.id);

        setProfileStatus({
          hasProfile: profileCheck.hasProfile,
          hasOrganization: orgCheck.hasOrganizations,
          error: profileCheck.error || orgCheck.error
        });

        // Only show the profile setup modals after login (we're already in a protected route)
        // If user doesn't have a profile, show the profile setup modal
        if (!profileCheck.hasProfile) {
          setShowProfileModal(true);
        }
        // If user has a profile but no organization, show the organization setup modal
        else if (!orgCheck.hasOrganizations) {
          setShowOrgModal(true);
        } else {
          // If everything is set up, clear the pending setup data
          localStorage.removeItem('pendingProfileSetup');
        }
      } catch (err: any) {
        console.error('Error checking user setup:', err);
        setProfileStatus({
          hasProfile: false,
          hasOrganization: false,
          error: err.message
        });
      } finally {
        setLoading(false);
      }
    };

    // Since this component is only rendered after login (in FullLayout),
    // we're already in a post-login context, so the check is appropriate here
    checkUserSetup();
  }, [user]);

  const handleCreateProfile = async () => {
    if (!user) return;

    setError(null);
    setSuccess(null);
    setCreatingProfile(true);

    try {
      if (!firstName.trim() || !lastName.trim()) {
        setError('First name and last name are required');
        return;
      }

      const result = await createUserProfile(user.id, firstName, lastName);

      if (result.success) {
        setSuccess('Profile created successfully');
        setProfileStatus(prev => ({ ...prev, hasProfile: true }));

        console.log('Profile created successfully:', result.profile);

        // Close the profile modal and open the organization modal if needed
        setTimeout(() => {
          setShowProfileModal(false);
          setShowOrgModal(true);
        }, 1500);
      } else {
        console.error('Failed to create profile:', result.error);
        setError(result.error || 'Failed to create profile');
      }
    } catch (err: any) {
      console.error('Error creating profile:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setCreatingProfile(false);
    }
  };

  const handleCreateOrganization = async () => {
    if (!user) return;

    setError(null);
    setSuccess(null);
    setCreatingOrg(true);

    try {
      if (!organizationName.trim()) {
        setError('Organization name is required');
        return;
      }

      const result = await createUserOrganization(user.id, organizationName);

      if (result.success) {
        setSuccess('Organization created successfully');
        setProfileStatus(prev => ({ ...prev, hasOrganization: true }));

        console.log('Organization created successfully:', result.organization);

        // Clear the pending setup data
        localStorage.removeItem('pendingProfileSetup');

        // Close the organization modal
        setTimeout(() => {
          setShowOrgModal(false);
          // Reload the page to refresh the organization context
          window.location.reload();
        }, 1500);
      } else {
        console.error('Failed to create organization:', result.error);
        setError(result.error || 'Failed to create organization');
      }
    } catch (err: any) {
      console.error('Error creating organization:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setCreatingOrg(false);
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50">
        <div className="text-center">
          <Spinner size="xl" />
          <p className="mt-2 text-white">Checking your profile setup...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <>
      {/* Profile Setup Modal */}
      <Modal
        show={showProfileModal}
        onClose={() => {}}
        dismissible={false}
      >
        <Modal.Header>
          Complete Your Profile
        </Modal.Header>
        <Modal.Body>
          <div className="space-y-6">
            <p className="text-base leading-relaxed text-gray-500 dark:text-gray-400">
              Please provide your name to complete your profile setup.
            </p>

            {error && (
              <Alert color="failure">
                {error}
              </Alert>
            )}

            {success && (
              <Alert color="success">
                {success}
              </Alert>
            )}

            <div>
              <div className="mb-2 block">
                <Label htmlFor="firstName" value="First Name" />
              </div>
              <TextInput
                id="firstName"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                required
              />
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="lastName" value="Last Name" />
              </div>
              <TextInput
                id="lastName"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                required
              />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="primary"
            onClick={handleCreateProfile}
            disabled={creatingProfile}
          >
            {creatingProfile ? 'Creating...' : 'Create Profile'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Organization Setup Modal */}
      <Modal
        show={showOrgModal}
        onClose={() => {}}
        dismissible={false}
      >
        <Modal.Header>
          Create Your Organization
        </Modal.Header>
        <Modal.Body>
          <div className="space-y-6">
            <p className="text-base leading-relaxed text-gray-500 dark:text-gray-400">
              Please create an organization to start using the POS system. Each organization is a separate tenant with isolated data.
            </p>

            {error && (
              <Alert color="failure">
                {error}
              </Alert>
            )}

            {success && (
              <Alert color="success">
                {success}
              </Alert>
            )}

            <div>
              <div className="mb-2 block">
                <Label htmlFor="organizationName" value="Organization Name" />
              </div>
              <TextInput
                id="organizationName"
                value={organizationName}
                onChange={(e) => setOrganizationName(e.target.value)}
                required
              />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="primary"
            onClick={handleCreateOrganization}
            disabled={creatingOrg}
          >
            {creatingOrg ? 'Creating...' : 'Create Organization'}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ProfileSetupCheck;
