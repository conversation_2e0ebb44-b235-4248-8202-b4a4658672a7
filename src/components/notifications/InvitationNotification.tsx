import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'flowbite-react';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../context/AuthContext';
import { HiOutlineMailOpen } from 'react-icons/hi';

interface Invitation {
  id: string;
  organization_id: string;
  organization_name: string;
  role: string;
  token: string;
}

const InvitationNotification = () => {
  const { user } = useAuth();
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [dismissed, setDismissed] = useState<Record<string, boolean>>({});

  useEffect(() => {
    // Load dismissed invitations from localStorage
    const savedDismissed = JSON.parse(localStorage.getItem('dismissedInvitations') || '{}');
    setDismissed(savedDismissed);

    const fetchInvitations = async () => {
      if (!user || !user.email) return;

      setLoading(true);
      setError(null);

      try {
        // Get invitations for the user's email that haven't been accepted or rejected yet
        const { data, error } = await supabase
          .from('invitations')
          .select('id, organization_id, token, role, organizations(name)')
          .eq('email', user.email)
          .is('accepted_at', null)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching invitations:', error);
          setError('Failed to load invitations');
          return;
        }

        // Format the invitations
        const formattedInvitations = data.map(inv => ({
          id: inv.id,
          organization_id: inv.organization_id,
          organization_name: inv.organizations?.name || 'Unknown Organization',
          role: inv.role,
          token: inv.token
        }));

        setInvitations(formattedInvitations);
      } catch (err: any) {
        console.error('Error in fetchInvitations:', err);
        setError(err.message || 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchInvitations();

    // Set up a subscription to listen for new invitations
    const channel = supabase
      .channel('invitation_notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'invitations',
          filter: `email=eq.${user?.email}`
        },
        () => {
          fetchInvitations();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user]);

  const handleDismiss = (id: string) => {
    setDismissed(prev => ({ ...prev, [id]: true }));

    // Store the dismissed state in localStorage to persist across sessions
    const dismissedInvitations = JSON.parse(localStorage.getItem('dismissedInvitations') || '{}');
    dismissedInvitations[id] = true;
    localStorage.setItem('dismissedInvitations', JSON.stringify(dismissedInvitations));
  };

  const handleReject = async (id: string) => {
    try {
      setLoading(true);

      // Mark the invitation as rejected by setting accepted_at to a past date
      // This is a workaround since there's no rejected column
      const { error } = await supabase
        .from('invitations')
        .update({
          accepted_at: '1970-01-01T00:00:00.000Z' // Use epoch time to indicate rejection
        })
        .eq('id', id);

      if (error) {
        console.error('Error rejecting invitation:', error);
        setError('Failed to reject invitation');
        return;
      }

      // Remove from the local state
      handleDismiss(id);

      // Remove from the invitations list
      setInvitations(prev => prev.filter(inv => inv.id !== id));
    } catch (err: any) {
      console.error('Error in handleReject:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return null; // Don't show anything while loading
  }

  if (error) {
    return null; // Don't show errors to the user
  }

  // Filter out dismissed invitations
  const activeInvitations = invitations.filter(inv => !dismissed[inv.id]);

  if (activeInvitations.length === 0) {
    return null; // Don't show anything if there are no invitations
  }

  return (
    <div className="mb-4">
      {activeInvitations.map(invitation => (
        <Alert
          key={invitation.id}
          color="info"
          icon={HiOutlineMailOpen}
          onDismiss={() => handleDismiss(invitation.id)}
          className="mb-2"
        >
          <div className="flex flex-col sm:flex-row sm:items-center justify-between">
            <div>
              <span className="font-medium">
                You've been invited to join {invitation.organization_name}
              </span>
              <p className="mt-1 text-sm">
                You've been invited to join as a <strong>{invitation.role}</strong>
              </p>
            </div>
            <div className="mt-2 sm:mt-0 flex space-x-2">
              <Link to={`/auth/accept-invitation?token=${invitation.token}`}>
                <Button size="xs" color="info">
                  Accept Invitation
                </Button>
              </Link>
              <Button
                size="xs"
                color="light"
                onClick={(e) => {
                  e.preventDefault();
                  handleReject(invitation.id);
                }}
                disabled={loading}
              >
                Reject
              </Button>
            </div>
          </div>
        </Alert>
      ))}
    </div>
  );
};

export default InvitationNotification;
