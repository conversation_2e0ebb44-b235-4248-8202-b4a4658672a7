import React, { ReactNode } from 'react';
import { ChatProvider } from '../../context/ChatContext';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';

interface ConditionalChatProviderProps {
  children: ReactNode;
}

/**
 * A wrapper component that conditionally renders the ChatProvider
 * based on the organization settings.
 */
const ConditionalChatProvider: React.FC<ConditionalChatProviderProps> = ({ children }) => {
  const { settings } = useOrganizationSettings();
  
  // Check if chat is enabled in the organization settings
  const isChatEnabled = settings.chat_enabled !== false; // Default to true if not set
  
  // If chat is disabled, just render the children without the ChatProvider
  if (!isChatEnabled) {
    return <>{children}</>;
  }
  
  // If chat is enabled, wrap children with the ChatProvider
  return <ChatProvider>{children}</ChatProvider>;
};

export default ConditionalChatProvider;
