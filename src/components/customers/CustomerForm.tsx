import { useState, useEffect } from 'react';
import {
  Button,
  TextInput,
  Textarea,
  Label,
  Alert,
  Spinner,
  ToggleSwitch
} from 'flowbite-react';
import { Customer } from '../../services/customer';
import { HiOutlineExclamation } from 'react-icons/hi';

interface CustomerFormProps {
  initialData?: Partial<Customer>;
  onSubmit: (customerData: Partial<Customer>) => Promise<void>;
  isSubmitting: boolean;
  error?: string;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  initialData,
  onSubmit,
  isSubmitting,
  error
}) => {
  // Form state
  const [formData, setFormData] = useState<Partial<Customer>>(() => {
    // Create initial form data
    return {
      name: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      state: '',
      postal_code: '',
      country: '',
      tax_id: '',
      notes: '',
      ...initialData
    };
  });

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData
      }));
    }
  }, [initialData]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle toggle switch changes
  const handleToggleChange = (checked: boolean, name: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {error}
        </Alert>
      )}

      {/* Basic Information */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">Basic Information</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Name */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="name" value="Name *" />
            </div>
            <TextInput
              id="name"
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              required
            />
          </div>

          {/* Email */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="email" value="Email" />
            </div>
            <TextInput
              id="email"
              name="email"
              type="email"
              value={formData.email || ''}
              onChange={handleChange}
            />
          </div>
        </div>

        {/* Phone */}
        <div>
          <div className="mb-2 block">
            <Label htmlFor="phone" value="Phone" />
          </div>
          <TextInput
            id="phone"
            name="phone"
            value={formData.phone || ''}
            onChange={handleChange}
          />
        </div>
      </div>

      {/* Address Information */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">Address Information</h3>

        {/* Address */}
        <div>
          <div className="mb-2 block">
            <Label htmlFor="address" value="Address" />
          </div>
          <TextInput
            id="address"
            name="address"
            value={formData.address || ''}
            onChange={handleChange}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* City */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="city" value="City" />
            </div>
            <TextInput
              id="city"
              name="city"
              value={formData.city || ''}
              onChange={handleChange}
            />
          </div>

          {/* State/Province */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="state" value="State/Province" />
            </div>
            <TextInput
              id="state"
              name="state"
              value={formData.state || ''}
              onChange={handleChange}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Postal Code */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="postal_code" value="Postal Code" />
            </div>
            <TextInput
              id="postal_code"
              name="postal_code"
              value={formData.postal_code || ''}
              onChange={handleChange}
            />
          </div>

          {/* Country */}
          <div>
            <div className="mb-2 block">
              <Label htmlFor="country" value="Country" />
            </div>
            <TextInput
              id="country"
              name="country"
              value={formData.country || ''}
              onChange={handleChange}
            />
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">Additional Information</h3>

        {/* Tax ID */}
        <div>
          <div className="mb-2 block">
            <Label htmlFor="tax_id" value="Tax ID" />
          </div>
          <TextInput
            id="tax_id"
            name="tax_id"
            value={formData.tax_id || ''}
            onChange={handleChange}
          />
        </div>

        {/* Loyalty Eligibility */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label htmlFor="loyalty_eligible" value="Loyalty Program Eligible" className="mb-0" />
            <ToggleSwitch
              id="loyalty_eligible"
              checked={formData.loyalty_eligible || false}
              onChange={(checked) => handleToggleChange(checked, 'loyalty_eligible')}
              label=""
            />
          </div>
          <p className="text-sm text-gray-500">
            Enable this to allow the customer to earn and redeem loyalty points
          </p>
        </div>

        {/* Notes */}
        <div>
          <div className="mb-2 block">
            <Label htmlFor="notes" value="Notes" />
          </div>
          <Textarea
            id="notes"
            name="notes"
            rows={4}
            value={formData.notes || ''}
            onChange={handleChange}
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" color="primary" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Customer'
          )}
        </Button>
      </div>
    </form>
  );
};

export default CustomerForm;
