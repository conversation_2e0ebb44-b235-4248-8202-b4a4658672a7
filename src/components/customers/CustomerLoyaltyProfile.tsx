import React, { useState, useEffect } from 'react';
import {
  Card,
  Badge,
  But<PERSON>,
  Spinner,
  Alert,
  TextInput,
  Label,
  Modal,
  Tabs
} from 'flowbite-react';
import {
  HiOutlineGift,
  HiOutlinePlus,
  HiOutlineMinus,
  HiOutlineRefresh,
  HiOutlineChartPie
} from 'react-icons/hi';
import { useLoyalty } from '../../context/LoyaltyContext';
import { useOrganization } from '../../context/OrganizationContext';
import { useAuth } from '../../context/AuthContext';
import {
  CustomerLoyaltyProfile as CustomerLoyaltyProfileType,
  LoyaltyTransaction,
  getCustomerLoyaltyTransactions,
  adjustCustomerLoyaltyPoints
} from '../../services/loyalty';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { formatDate } from '../../utils/formatters';
import { supabase } from '../../lib/supabase';

interface CustomerLoyaltyProfileProps {
  customerId: string;
}

const CustomerLoyaltyProfile: React.FC<CustomerLoyaltyProfileProps> = ({ customerId }) => {
  const { settings, getCustomerProfile, refreshSettings } = useLoyalty();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();
  const formatWithCurrency = useCurrencyFormatter();

  const [profile, setProfile] = useState<CustomerLoyaltyProfileType | null>(null);
  const [transactions, setTransactions] = useState<LoyaltyTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAdjustModal, setShowAdjustModal] = useState(false);
  const [adjustPoints, setAdjustPoints] = useState<number>(0);
  const [adjustNotes, setAdjustNotes] = useState<string>('');
  const [isAdjusting, setIsAdjusting] = useState(false);
  const [adjustError, setAdjustError] = useState<string | null>(null);
  const [customerEligible, setCustomerEligible] = useState<boolean | null>(null);

  // Fetch customer loyalty profile
  useEffect(() => {
    const fetchProfile = async () => {
      if (!currentOrganization || !customerId) return;

      setIsLoading(true);
      try {
        // Check customer eligibility first
        const { data: customerData, error: customerError } = await supabase
          .from('customers')
          .select('loyalty_eligible')
          .eq('id', customerId)
          .single();

        if (customerError) {
          throw new Error(customerError.message);
        }

        setCustomerEligible(customerData.loyalty_eligible);

        // If customer is not eligible, don't fetch profile
        if (customerData.loyalty_eligible === false) {
          setIsLoading(false);
          return;
        }

        const customerProfile = await getCustomerProfile(customerId);
        setProfile(customerProfile);

        // Fetch transactions
        const { transactions: loyaltyTransactions } = await getCustomerLoyaltyTransactions(
          currentOrganization.id,
          customerId
        );

        setTransactions(loyaltyTransactions);
      } catch (err: any) {
        console.error('Error fetching loyalty profile:', err);
        setError(err.message || 'Failed to load loyalty profile');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, [currentOrganization, customerId, getCustomerProfile]);

  // Handle adjust points
  const handleAdjustPoints = async () => {
    if (!currentOrganization || !customerId || !user) {
      setAdjustError('Missing required information');
      return;
    }

    if (adjustPoints === 0) {
      setAdjustError('Please enter a non-zero point value');
      return;
    }

    if (!adjustNotes.trim()) {
      setAdjustError('Please provide a reason for the adjustment');
      return;
    }

    setIsAdjusting(true);
    setAdjustError(null);

    try {
      const { success, error: adjustmentError } = await adjustCustomerLoyaltyPoints(
        currentOrganization.id,
        customerId,
        user.id,
        adjustPoints,
        adjustNotes
      );

      if (adjustmentError) {
        setAdjustError(adjustmentError);
      } else {
        // Refresh profile and transactions
        const customerProfile = await getCustomerProfile(customerId);
        setProfile(customerProfile);

        const { transactions: loyaltyTransactions } = await getCustomerLoyaltyTransactions(
          currentOrganization.id,
          customerId
        );

        setTransactions(loyaltyTransactions);

        // Close modal and reset form
        setShowAdjustModal(false);
        setAdjustPoints(0);
        setAdjustNotes('');
      }
    } catch (err: any) {
      console.error('Error adjusting points:', err);
      setAdjustError(err.message || 'Failed to adjust points');
    } finally {
      setIsAdjusting(false);
    }
  };

  // If loyalty program is disabled
  if (!settings?.is_enabled) {
    return (
      <Card>
        <div className="flex items-center mb-4">
          <HiOutlineGift className="mr-2 h-6 w-6 text-gray-400" />
          <h2 className="text-xl font-bold">Loyalty Program</h2>
        </div>

        <Alert color="info">
          The loyalty program is currently disabled. Enable it in organization settings.
        </Alert>
      </Card>
    );
  }

  // If customer is not eligible for loyalty program
  if (customerEligible === false) {
    return (
      <Card>
        <div className="flex items-center mb-4">
          <HiOutlineGift className="mr-2 h-6 w-6 text-gray-400" />
          <h2 className="text-xl font-bold">Loyalty Program</h2>
        </div>

        <Alert color="warning">
          This customer is not eligible for the loyalty program. You can enable eligibility in the customer edit form.
        </Alert>
      </Card>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <div className="flex items-center mb-4">
          <HiOutlineGift className="mr-2 h-6 w-6 text-blue-500" />
          <h2 className="text-xl font-bold">Loyalty Program</h2>
        </div>

        <div className="flex justify-center py-8">
          <Spinner size="lg" />
        </div>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card>
        <div className="flex items-center mb-4">
          <HiOutlineGift className="mr-2 h-6 w-6 text-blue-500" />
          <h2 className="text-xl font-bold">Loyalty Program</h2>
        </div>

        <Alert color="failure">
          {error}
        </Alert>
      </Card>
    );
  }

  // No profile yet
  if (!profile) {
    return (
      <Card>
        <div className="flex items-center mb-4">
          <HiOutlineGift className="mr-2 h-6 w-6 text-blue-500" />
          <h2 className="text-xl font-bold">Loyalty Program</h2>
        </div>

        <Alert color="info">
          This customer doesn't have a loyalty profile yet. They'll earn points with their first purchase.
        </Alert>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <HiOutlineGift className="mr-2 h-6 w-6 text-blue-500" />
            <h2 className="text-xl font-bold">Loyalty Program</h2>
          </div>

          <Button
            color="light"
            size="xs"
            onClick={() => setShowAdjustModal(true)}
          >
            Adjust Points
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Available Points</div>
            <div className="text-2xl font-bold text-blue-600">{profile.current_points_balance}</div>
            <div className="text-xs text-gray-500 mt-1">
              Value: {formatWithCurrency(profile.current_points_balance * (settings?.points_redemption_rate || 0))}
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Lifetime Earned</div>
            <div className="text-2xl font-bold text-green-600">{profile.lifetime_points_earned}</div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Lifetime Redeemed</div>
            <div className="text-2xl font-bold text-purple-600">{profile.lifetime_points_redeemed}</div>
          </div>
        </div>

        <Tabs aria-label="Loyalty tabs">
          <Tabs.Item title="Transactions" icon={HiOutlineRefresh}>
            {transactions.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No loyalty transactions yet
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left text-gray-500">
                  <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                    <tr>
                      <th className="px-4 py-2">Date</th>
                      <th className="px-4 py-2">Type</th>
                      <th className="px-4 py-2">Points</th>
                      <th className="px-4 py-2">Notes</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transactions.map(transaction => (
                      <tr key={transaction.id} className="border-b">
                        <td className="px-4 py-2">{formatDate(transaction.created_at || '')}</td>
                        <td className="px-4 py-2">
                          <Badge
                            color={
                              transaction.transaction_type === 'earn' ? 'success' :
                              transaction.transaction_type === 'redeem' ? 'purple' :
                              transaction.transaction_type === 'expire' ? 'warning' :
                              'info'
                            }
                          >
                            {transaction.transaction_type}
                          </Badge>
                        </td>
                        <td className="px-4 py-2 font-medium">
                          {transaction.points > 0 ? '+' : ''}{transaction.points}
                        </td>
                        <td className="px-4 py-2">{transaction.notes}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </Tabs.Item>

          <Tabs.Item title="Statistics" icon={HiOutlineChartPie}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-sm text-gray-500 mb-1">Points Earned This Year</div>
                <div className="text-xl font-bold">
                  {transactions
                    .filter(t =>
                      t.transaction_type === 'earn' &&
                      new Date(t.created_at || '').getFullYear() === new Date().getFullYear()
                    )
                    .reduce((sum, t) => sum + Math.abs(t.points), 0)
                  }
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-sm text-gray-500 mb-1">Points Redeemed This Year</div>
                <div className="text-xl font-bold">
                  {transactions
                    .filter(t =>
                      t.transaction_type === 'redeem' &&
                      new Date(t.created_at || '').getFullYear() === new Date().getFullYear()
                    )
                    .reduce((sum, t) => sum + Math.abs(t.points), 0)
                  }
                </div>
              </div>
            </div>
          </Tabs.Item>
        </Tabs>
      </Card>

      {/* Adjust Points Modal */}
      <Modal show={showAdjustModal} onClose={() => setShowAdjustModal(false)}>
        <Modal.Header>Adjust Loyalty Points</Modal.Header>
        <Modal.Body>
          {adjustError && (
            <Alert color="failure" className="mb-4">
              {adjustError}
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <Label htmlFor="adjustPoints">Points</Label>
              <div className="flex items-center mt-1">
                <Button
                  color="light"
                  size="sm"
                  onClick={() => setAdjustPoints(prev => prev - 100)}
                >
                  <HiOutlineMinus className="h-4 w-4" />
                </Button>
                <TextInput
                  id="adjustPoints"
                  type="number"
                  value={adjustPoints}
                  onChange={(e) => setAdjustPoints(parseInt(e.target.value) || 0)}
                  className="mx-2"
                />
                <Button
                  color="light"
                  size="sm"
                  onClick={() => setAdjustPoints(prev => prev + 100)}
                >
                  <HiOutlinePlus className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Use positive values to add points, negative to deduct points
              </p>
            </div>

            <div>
              <Label htmlFor="adjustNotes">Reason for Adjustment</Label>
              <TextInput
                id="adjustNotes"
                type="text"
                value={adjustNotes}
                onChange={(e) => setAdjustNotes(e.target.value)}
                placeholder="e.g., Customer service compensation"
              />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="primary"
            onClick={handleAdjustPoints}
            disabled={isAdjusting}
          >
            {isAdjusting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Saving...
              </>
            ) : (
              'Save Adjustment'
            )}
          </Button>
          <Button
            color="gray"
            onClick={() => setShowAdjustModal(false)}
          >
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default CustomerLoyaltyProfile;
