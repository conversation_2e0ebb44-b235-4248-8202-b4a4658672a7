import React, { useState, useEffect } from 'react';
import {
  <PERSON>dal,
  Button,
  TextInput,
  Spinner,
  Alert,
  Table,
  Badge,
  Card
} from 'flowbite-react';
import {
  HiOutlineUser,
  HiOutlineSearch,
  HiOutlineExclamation,
  HiOutlinePlus,
  HiOutlineGift,
  HiOutlineMail,
  HiOutlinePhone
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getCustomers, Customer } from '../../services/customer';
import { useLoyalty } from '../../context/LoyaltyContext';
import { getBatchCustomerLoyaltyPoints } from '../../services/loyalty';
import Pagination from '../common/Pagination';

interface CustomerSelectorProps {
  onSelectCustomer: (customer: Customer | null) => void;
  selectedCustomerId?: string | null;
  onCreateCustomer?: () => void;
}

const CustomerSelector: React.FC<CustomerSelectorProps> = ({
  onSelectCustomer,
  selectedCustomerId,
  onCreateCustomer
}) => {
  const { currentOrganization } = useOrganization();
  const { settings: loyaltySettings } = useLoyalty();
  const [isOpen, setIsOpen] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [customerLoyaltyPoints, setCustomerLoyaltyPoints] = useState<{[key: string]: number}>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);

  // Sync selectedCustomer with selectedCustomerId prop
  useEffect(() => {
    if (!selectedCustomerId) {
      setSelectedCustomer(null);
    } else if (customers.length > 0) {
      const selected = customers.find(c => c.id === selectedCustomerId);
      if (selected) {
        setSelectedCustomer(selected);
      }
    }
  }, [selectedCustomerId, customers]);

  // Fetch customers
  useEffect(() => {
    const fetchCustomers = async () => {
      if (!currentOrganization || !isOpen) return;

      setLoading(true);
      setError(null);

      try {
        const { customers: customerData, error: fetchError } = await getCustomers(
          currentOrganization.id,
          { limit: 100 }
        );

        if (fetchError) {
          setError(fetchError);
        } else {
          setCustomers(customerData);
          setFilteredCustomers(customerData);

          // Fetch loyalty points for eligible customers
          if (loyaltySettings?.is_enabled) {
            const loyaltyCustomers = customerData.filter(c => c.loyalty_eligible);
            fetchLoyaltyPoints(loyaltyCustomers);
          }
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch customers');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomers();
  }, [currentOrganization, isOpen, loyaltySettings?.is_enabled]);

  // Fetch loyalty points for customers
  const fetchLoyaltyPoints = async (loyaltyCustomers: Customer[]) => {
    if (!currentOrganization || loyaltyCustomers.length === 0) {
      return;
    }

    try {
      // Use the loyalty service to get points for all customers
      const { pointsMap, error } = await getBatchCustomerLoyaltyPoints(
        currentOrganization.id,
        loyaltyCustomers.map(c => c.id)
      );

      if (error) {
        console.error('Error fetching loyalty points:', error);
        return;
      }

      setCustomerLoyaltyPoints(pointsMap);
    } catch (err) {
      console.error('Error fetching loyalty points:', err);
    }
  };

  // Filter customers based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredCustomers(customers);
      return;
    }

    const filtered = customers.filter(
      customer =>
        (customer.name && customer.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (customer.phone && customer.phone.includes(searchTerm))
    );

    setFilteredCustomers(filtered);
  }, [customers, searchTerm]);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen]);

  const handleOpen = () => {
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleSelectCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    onSelectCustomer(customer);
    setIsOpen(false);
  };

  const handleClearCustomer = () => {
    setSelectedCustomer(null);
    onSelectCustomer(null);
  };

  const getCustomerDisplayName = (customer: Customer | null) => {
    if (!customer) return 'Select Customer';
    return customer.name || 'Unnamed Customer';
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentCustomers = filteredCustomers.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <>
      <div className="flex items-center gap-2">
        <Button
          color="light"
          className="w-full flex items-center justify-between"
          onClick={handleOpen}
          data-testid="customer-selector-button"
        >
          <div className="flex items-center">
            <HiOutlineUser className="mr-2 h-5 w-5" />
            {selectedCustomer ? getCustomerDisplayName(selectedCustomer) : 'Select Customer'}
          </div>
          {selectedCustomer && (
            <Button
              size="xs"
              color="gray"
              onClick={(e) => {
                e.stopPropagation();
                handleClearCustomer();
              }}
            >
              Clear
            </Button>
          )}
        </Button>
      </div>

      <Modal show={isOpen} onClose={handleClose} size="lg">
        <Modal.Header>Select Customer</Modal.Header>
        <Modal.Body>
          {error && (
            <Alert color="failure" className="mb-4">
              <HiOutlineExclamation className="mr-2 h-5 w-5" />
              {error}
            </Alert>
          )}

          <div className="mb-4">
            <TextInput
              id="customer-search"
              type="text"
              placeholder="Search by name, email, or phone"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              icon={HiOutlineSearch}
            />
          </div>

          {loading ? (
            <div className="flex justify-center items-center p-8">
              <Spinner size="xl" />
            </div>
          ) : filteredCustomers.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No customers found</p>
              <Button
                size="sm"
                onClick={onCreateCustomer}
              >
                <HiOutlinePlus className="mr-2 h-4 w-4" />
                Add New Customer
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="grid grid-cols-1 gap-2 max-h-[60vh] overflow-y-auto">
                {currentCustomers.map((customer) => (
                <div
                  key={customer.id}
                  className="cursor-pointer hover:bg-blue-50 transition-colors p-2.5 rounded-md border border-gray-200 hover:border-blue-300 hover:shadow-sm"
                  onClick={() => handleSelectCustomer(customer)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <HiOutlineUser className="h-4 w-4 text-blue-500 flex-shrink-0" />
                        <h5 className="text-sm font-semibold text-gray-900 truncate">
                          {customer.name || 'Unnamed Customer'}
                        </h5>
                        {customer.loyalty_eligible && (
                          <Badge color="purple" size="sm" className="text-xs">Loyalty</Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        {customer.email && (
                          <div className="flex items-center min-w-0">
                            <HiOutlineMail className="mr-1 h-3 w-3 text-gray-400 flex-shrink-0" />
                            <span className="truncate">{customer.email}</span>
                          </div>
                        )}

                        {customer.phone && (
                          <div className="flex items-center flex-shrink-0">
                            <HiOutlinePhone className="mr-1 h-3 w-3 text-gray-400" />
                            <span>{customer.phone}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {customer.loyalty_eligible && loyaltySettings?.is_enabled && (
                      <div className="flex items-center ml-3 flex-shrink-0">
                        <div className="flex items-center bg-purple-100 px-2 py-1 rounded-full">
                          <HiOutlineGift className="mr-1 h-3 w-3 text-purple-600" />
                          <span className="text-xs font-semibold text-purple-700">
                            {customerLoyaltyPoints[customer.id] || 0}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              </div>

              {/* Pagination */}
              {filteredCustomers.length > 0 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  itemsPerPage={itemsPerPage}
                  totalItems={filteredCustomers.length}
                  onPageChange={setCurrentPage}
                  onItemsPerPageChange={(newItemsPerPage) => {
                    setItemsPerPage(newItemsPerPage);
                    setCurrentPage(1); // Reset to first page when changing items per page
                  }}
                  itemName="customers"
                />
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <div className="flex justify-between w-full">
            <Button color="light" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              color="blue"
              onClick={() => {
                setIsOpen(false);
                if (onCreateCustomer) {
                  onCreateCustomer();
                }
              }}
            >
              <HiOutlinePlus className="mr-2 h-4 w-4" />
              New Customer
            </Button>
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default CustomerSelector;
