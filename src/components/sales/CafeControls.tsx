import React, { useState } from 'react';
import {
  Button,
  Modal,
  TextInput,
  Label,
  Select,
  ToggleSwitch
} from 'flowbite-react';
import {
  HiOutlineBeaker,
  HiOutlineClock,
  HiOutlineClipboard
} from 'react-icons/hi';

interface CafeControlsProps {
  onToggleTakeaway: (isTakeaway: boolean) => void;
  isTakeaway: boolean;
}

const CafeControls: React.FC<CafeControlsProps> = ({
  onToggleTakeaway,
  isTakeaway
}) => {
  const [isOrderTypeModalOpen, setIsOrderTypeModalOpen] = useState(false);

  const handleToggleTakeaway = () => {
    onToggleTakeaway(!isTakeaway);
  };

  return (
    <div className="mb-4">
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center">
            <HiOutlineBeaker className="mr-2 h-5 w-5 text-gray-600" />
            <span className="text-sm font-medium">Order Type</span>
          </div>
          <div className="flex items-center">
            <span className="text-sm mr-2">{isTakeaway ? 'Takeaway' : 'Dine In'}</span>
            <ToggleSwitch
              checked={isTakeaway}
              onChange={handleToggleTakeaway}
              label=""
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <Button color="light" size="sm">
            <HiOutlineClock className="mr-2 h-4 w-4" />
            Quick Items
          </Button>
          <Button color="light" size="sm">
            <HiOutlineClipboard className="mr-2 h-4 w-4" />
            Order History
          </Button>
        </div>
      </div>

      {/* Quick Items Modal would go here */}
    </div>
  );
};

export default CafeControls;
