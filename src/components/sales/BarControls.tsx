import React, { useState } from 'react';
import {
  Button,
  Modal,
  TextInput,
  Label,
  Select,
  ToggleSwitch
} from 'flowbite-react';
import {
  HiOutlineBookOpen,
  HiOutlineTag,
  HiOutlineClipboard
} from 'react-icons/hi';

interface BarControlsProps {
  onToggleTabMode: (isTabMode: boolean) => void;
  isTabMode: boolean;
}

const BarControls: React.FC<BarControlsProps> = ({
  onToggleTabMode,
  isTabMode
}) => {
  const [isOrderTypeModalOpen, setIsOrderTypeModalOpen] = useState(false);

  const handleToggleTabMode = () => {
    onToggleTabMode(!isTabMode);
  };

  return (
    <div className="mb-4">
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center">
            <HiOutlineBookOpen className="mr-2 h-5 w-5 text-gray-600" />
            <span className="text-sm font-medium">Order Type</span>
          </div>
          <div className="flex items-center">
            <span className="text-sm mr-2">{isTabMode ? 'Running Tab' : 'Direct Payment'}</span>
            <ToggleSwitch
              checked={isTabMode}
              onChange={handleToggleTabMode}
              label=""
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <Button color="light" size="sm">
            <HiOutlineTag className="mr-2 h-4 w-4" />
            Happy Hour
          </Button>
          <Button color="light" size="sm">
            <HiOutlineClipboard className="mr-2 h-4 w-4" />
            Order History
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BarControls;
