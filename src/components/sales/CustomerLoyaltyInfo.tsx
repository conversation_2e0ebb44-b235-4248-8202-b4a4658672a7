import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Spinner } from 'flowbite-react';
import { HiOutlineGift } from 'react-icons/hi';
import { Customer } from '../../services/customer';
import { useLoyalty } from '../../context/LoyaltyContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

interface CustomerLoyaltyInfoProps {
  customer: Customer | null;
}

const CustomerLoyaltyInfo: React.FC<CustomerLoyaltyInfoProps> = ({ customer }) => {
  const { settings, getCustomerProfile } = useLoyalty();
  const formatWithCurrency = useCurrencyFormatter();
  
  const [isLoading, setIsLoading] = useState(false);
  const [loyaltyProfile, setLoyaltyProfile] = useState<any>(null);
  
  useEffect(() => {
    const fetchLoyaltyProfile = async () => {
      if (!customer || !settings?.is_enabled || customer.loyalty_eligible === false) {
        setLoyaltyProfile(null);
        return;
      }
      
      setIsLoading(true);
      try {
        const profile = await getCustomerProfile(customer.id);
        setLoyaltyProfile(profile);
      } catch (err) {
        console.error('Error fetching loyalty profile:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchLoyaltyProfile();
  }, [customer, settings, getCustomerProfile]);
  
  if (!customer || !settings?.is_enabled || customer.loyalty_eligible === false) {
    return null;
  }
  
  if (isLoading) {
    return (
      <div className="flex items-center text-sm text-gray-500 mt-1">
        <HiOutlineGift className="mr-1 h-4 w-4 text-blue-500" />
        <Spinner size="sm" className="mr-1" />
        Loading loyalty info...
      </div>
    );
  }
  
  if (!loyaltyProfile) {
    return (
      <div className="flex items-center text-sm text-gray-500 mt-1">
        <HiOutlineGift className="mr-1 h-4 w-4 text-blue-500" />
        No loyalty profile yet
      </div>
    );
  }
  
  const pointsValue = loyaltyProfile.current_points_balance * (settings?.points_redemption_rate || 0);
  
  return (
    <div className="mt-2 p-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <HiOutlineGift className="mr-2 h-5 w-5 text-blue-500" />
          <span className="text-gray-700 font-medium">Loyalty Points:</span>
        </div>
        <div className="flex items-center space-x-2">
          <Badge color="blue" size="lg" className="font-bold">
            {loyaltyProfile.current_points_balance} points
          </Badge>
          <span className="text-xs text-gray-600 font-medium">
            ≈ {formatWithCurrency(pointsValue)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default CustomerLoyaltyInfo;
