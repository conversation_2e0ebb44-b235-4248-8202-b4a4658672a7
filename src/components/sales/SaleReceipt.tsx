import React from 'react';
import { <PERSON>, Badge } from 'flowbite-react';
import {
  HiOutlineReceiptTax,
  HiOutlineUser,
  HiOutlineCalendar,
  HiOutlineGift
} from 'react-icons/hi';
import { Sale, SaleItem } from '../../services/sale';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import { formatDateTime } from '../../utils/formatters';
import { useOrganization } from '../../context/OrganizationContext';

interface SaleReceiptProps {
  sale: Sale;
  showPrintButton?: boolean;
  onPrint?: () => void;
}

const SaleReceipt: React.FC<SaleReceiptProps> = ({
  sale,
  showPrintButton = false,
  onPrint
}) => {
  const formatWithCurrency = useCurrencyFormatter();
  const { currentOrganization } = useOrganization();

  return (
    <Card className="print:shadow-none print:border-none">
      {/* Business Header */}
      <div className="text-center border-b border-gray-200 pb-4 mb-4">
        <h1 className="text-xl font-bold text-gray-900">
          {currentOrganization?.name || 'Your Business'}
        </h1>
        {currentOrganization?.address && (
          <p className="text-sm text-gray-600 mt-1">{currentOrganization.address}</p>
        )}
        <div className="flex justify-center space-x-4 text-sm text-gray-600 mt-1">
          {currentOrganization?.phone && (
            <span>Phone: {currentOrganization.phone}</span>
          )}
          {currentOrganization?.email && (
            <span>Email: {currentOrganization.email}</span>
          )}
        </div>
        {currentOrganization?.website && (
          <p className="text-sm text-gray-600 mt-1">{currentOrganization.website}</p>
        )}
      </div>

      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <HiOutlineReceiptTax className="mr-2 h-6 w-6 text-blue-500" />
          <h2 className="text-xl font-bold">Receipt</h2>
        </div>

        <div>
          <Badge color="blue">
            {sale.invoice_number}
          </Badge>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex items-center mb-2">
          <HiOutlineCalendar className="mr-2 h-5 w-5 text-gray-500" />
          <span>{formatDateTime(sale.sale_date)}</span>
        </div>

        {sale.customer && (
          <div className="flex items-center mb-2">
            <HiOutlineUser className="mr-2 h-5 w-5 text-gray-500" />
            <span>{sale.customer.name}</span>
          </div>
        )}

        {sale.cashier && (
          <div className="flex items-center">
            <HiOutlineUser className="mr-2 h-5 w-5 text-blue-500" />
            <div>
              <span className="font-medium">
                Cashier: {`${sale.cashier.first_name || ''} ${sale.cashier.last_name || ''}`.trim() || 'Unknown User'}
              </span>
            </div>
          </div>
        )}
      </div>

      <div className="border-t border-b border-gray-200 py-4 mb-4">
        <table className="w-full text-sm">
          <thead className="text-left">
            <tr className="border-b border-gray-200">
              <th className="pb-2">Item</th>
              <th className="pb-2 text-right">Qty</th>
              <th className="pb-2 text-right">Price</th>
              <th className="pb-2 text-right">Total</th>
            </tr>
          </thead>
          <tbody>
            {sale.items?.map((item, index) => (
              <tr key={item.id || index} className="border-b border-gray-100">
                <td className="py-2">{item.product?.name || 'Product'}</td>
                <td className="py-2 text-right">{item.quantity} pcs</td>
                <td className="py-2 text-right">{formatWithCurrency(item.unit_price)}</td>
                <td className="py-2 text-right">{formatWithCurrency(item.unit_price * item.quantity)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="space-y-2 mb-4">
        <div className="flex justify-between text-sm">
          <span>Subtotal</span>
          <span>{formatWithCurrency(sale.subtotal)}</span>
        </div>

        {sale.discount_amount > 0 && (
		<div className="flex justify-between text-sm">
			<span>Discount</span>
			<span className="text-red-500">-{formatWithCurrency(sale.discount_amount)}</span>
		</div>
		)}

		{sale.loyalty_points_discount > 0 && sale.loyalty_points_used > 0 && (
		<div className="flex justify-between text-sm">
			<div className="flex items-center">
			<span className="mr-1">Loyalty Points</span>
			<Badge color="purple" className="text-xs">
				{sale.loyalty_points_used} points
			</Badge>
			</div>
			<span className="text-purple-500">-{formatWithCurrency(sale.loyalty_points_discount)}</span>
		</div>
		)}

        <div className="flex justify-between text-sm">
          <span>Tax</span>
          <span>{formatWithCurrency(sale.tax_amount)}</span>
        </div>

        <div className="flex justify-between font-bold text-lg pt-2 border-t border-gray-200">
          <span>Total</span>
          <span>{formatWithCurrency(sale.total_amount)}</span>
        </div>
      </div>

     {sale.loyalty_points_earned > 0 && (
		<div className="bg-blue-50 p-3 rounded-lg mb-4 flex items-center">
			<HiOutlineGift className="mr-2 h-5 w-5 text-blue-500" />
			<div>
			<p className="font-medium">Points Earned</p>
			<p className="text-sm text-blue-600">{sale.loyalty_points_earned} points added to your account</p>
			</div>
		</div>
		)}

      
      {sale.payment_method === 'cash' && sale.cash_tendered && (
        <div className="space-y-2 mb-4 border-t border-gray-200 pt-4">
          <div className="flex justify-between text-sm">
            <span>Cash Tendered:</span>
            <span>{formatWithCurrency(sale.cash_tendered)}</span>
          </div>
          {sale.change_amount && sale.change_amount > 0 && (
            <div className="flex justify-between text-sm font-medium">
              <span>Change:</span>
              <span>{formatWithCurrency(sale.change_amount)}</span>
            </div>
          )}
        </div>
      )}

      <div className="text-center text-sm text-gray-500">
        <p>Thank you for your business!</p>
        <p className="mt-1">Payment Method: {sale.payment_method}</p>

        {sale.notes && (
          <p className="mt-2 italic">{sale.notes}</p>
        )}
      </div>

      {showPrintButton && onPrint && (
        <div className="mt-4 print:hidden">
          <button
            onClick={onPrint}
            className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
          >
            Print Receipt
          </button>
        </div>
      )}
    </Card>
  );
};

export default SaleReceipt;
