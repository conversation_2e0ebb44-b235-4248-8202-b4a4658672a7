import React, { useState, useEffect } from 'react';
import {
  Modal,
  Button,
  TextInput,
  Label
} from 'flowbite-react';
import {
  HiOutlineTicket,
  HiOutlineTag,
  HiOutlineCash,
  HiOutlineTrash
} from 'react-icons/hi';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import EnhancedNumberInput from '../common/EnhancedNumberInput';

interface DiscountSelectorProps {
  subtotal: number;
  onApplyDiscount: (amount: number, isPercentage: boolean) => void;
  currentDiscount?: {
    amount: number;
    isPercentage: boolean;
  };
}

const DiscountSelector: React.FC<DiscountSelectorProps> = ({
  subtotal,
  onApplyDiscount,
  currentDiscount
}) => {
  const { settings } = useOrganizationSettings();
  const formatWithCurrency = useCurrencyFormatter();
  const [isOpen, setIsOpen] = useState(false);
  const [discountType, setDiscountType] = useState<'percentage' | 'fixed'>(
    currentDiscount?.isPercentage ? 'percentage' : 'fixed'
  );
  const [percentageDiscount, setPercentageDiscount] = useState(
    currentDiscount?.isPercentage ? currentDiscount.amount : 0
  );
  const [fixedDiscount, setFixedDiscount] = useState(
    !currentDiscount?.isPercentage ? currentDiscount?.amount || 0 : 0
  );

  // Sync internal state with currentDiscount prop
  useEffect(() => {
    if (!currentDiscount || currentDiscount.amount === 0) {
      // Reset to defaults when discount is cleared
      setDiscountType('fixed');
      setPercentageDiscount(0);
      setFixedDiscount(0);
    } else {
      // Update state when discount changes
      setDiscountType(currentDiscount.isPercentage ? 'percentage' : 'fixed');
      if (currentDiscount.isPercentage) {
        setPercentageDiscount(currentDiscount.amount);
        setFixedDiscount(0);
      } else {
        setFixedDiscount(currentDiscount.amount);
        setPercentageDiscount(0);
      }
    }
  }, [currentDiscount]);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen]);

  const handleOpen = () => {
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleApplyDiscount = () => {
    if (discountType === 'percentage') {
      onApplyDiscount(percentageDiscount, true);
    } else {
      onApplyDiscount(fixedDiscount, false);
    }
    setIsOpen(false);
  };

  const handleRemoveDiscount = () => {
    onApplyDiscount(0, false);
    setIsOpen(false);
  };

  const calculateDiscountAmount = () => {
    if (discountType === 'percentage') {
      return (subtotal * percentageDiscount) / 100;
    } else {
      return fixedDiscount;
    }
  };

  const hasDiscount = currentDiscount && currentDiscount.amount > 0;

  return (
    <>
      <Button
        color={hasDiscount ? 'success' : 'light'}
        size="sm"
        onClick={handleOpen}
        className="flex items-center"
        data-testid="discount-selector-button"
      >
        <HiOutlineTicket className="mr-2 h-4 w-4" />
        {hasDiscount
          ? `Discount: ${currentDiscount.isPercentage
              ? `${currentDiscount.amount}%`
              : formatWithCurrency(currentDiscount.amount)}`
          : 'Add Discount'}
      </Button>

      <Modal show={isOpen} onClose={handleClose} size="md">
        <Modal.Header>Apply Discount</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            {/* Tab Headers */}
            <div className="flex border-b border-gray-200">
              <button
                type="button"
                className={`flex items-center px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                  discountType === 'percentage'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setDiscountType('percentage')}
              >
                <HiOutlineTag className="mr-2 h-4 w-4" />
                Percentage
              </button>
              <button
                type="button"
                className={`flex items-center px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                  discountType === 'fixed'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setDiscountType('fixed')}
              >
                <HiOutlineCash className="mr-2 h-4 w-4" />
                Fixed Amount
              </button>
            </div>

            {/* Tab Content */}
            {discountType === 'percentage' && (
              <div className="space-y-4 py-2">
                <div>
                  <Label htmlFor="percentageDiscount">Discount Percentage</Label>
                  <div className="flex items-center mt-1">
                    <EnhancedNumberInput
                      id="percentageDiscount"
                      min={0}
                      max={100}
                      value={percentageDiscount}
                      onChange={(e) => setPercentageDiscount(Number(e.target.value))}
                      className="flex-1"
                    />
                    <span className="ml-2 text-lg">%</span>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Subtotal:</span>
                    <span>{formatWithCurrency(subtotal)}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Discount ({percentageDiscount}%):</span>
                    <span className="text-red-500">-{formatWithCurrency((subtotal * percentageDiscount) / 100)}</span>
                  </div>
                  <div className="flex justify-between font-bold pt-2 border-t border-gray-200 mt-2">
                    <span>New Subtotal:</span>
                    <span>{formatWithCurrency(subtotal - (subtotal * percentageDiscount) / 100)}</span>
                  </div>
                </div>

                {/* Quick percentage buttons */}
                <div className="grid grid-cols-4 gap-2 mt-2">
                  {[5, 10, 15, 20].map((percent) => (
                    <Button
                      key={percent}
                      size="xs"
                      color={percentageDiscount === percent ? "blue" : "light"}
                      onClick={() => setPercentageDiscount(percent)}
                    >
                      {percent}%
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {discountType === 'fixed' && (
              <div className="space-y-4 py-2">
                <div>
                  <Label htmlFor="fixedDiscount">Discount Amount</Label>
                  <div className="flex items-center mt-1">
                    <span className="mr-2 text-lg">{settings?.currency || '$'}</span>
                    <EnhancedNumberInput
                      id="fixedDiscount"
                      min={0}
                      max={subtotal}
                      value={fixedDiscount}
                      onChange={(e) => setFixedDiscount(Number(e.target.value))}
                      className="flex-1"
                    />
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Subtotal:</span>
                    <span>{formatWithCurrency(subtotal)}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Discount:</span>
                    <span className="text-red-500">-{formatWithCurrency(fixedDiscount)}</span>
                  </div>
                  <div className="flex justify-between font-bold pt-2 border-t border-gray-200 mt-2">
                    <span>New Subtotal:</span>
                    <span>{formatWithCurrency(subtotal - fixedDiscount)}</span>
                  </div>
                </div>

                {/* Quick amount buttons */}
                <div className="grid grid-cols-3 gap-2 mt-2">
                  {[5, 10, 20].map((amount) => (
                    <Button
                      key={amount}
                      size="xs"
                      color={fixedDiscount === amount ? "blue" : "light"}
                      onClick={() => setFixedDiscount(amount)}
                    >
                      {formatWithCurrency(amount)}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <div className="flex justify-between w-full">
            {hasDiscount && (
              <Button
                color="failure"
                onClick={handleRemoveDiscount}
                className="flex items-center"
              >
                <HiOutlineTrash className="mr-2 h-4 w-4" />
                Remove Discount
              </Button>
            )}
            <div className="flex gap-2 ml-auto">
              <Button color="gray" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                color="success"
                onClick={handleApplyDiscount}
                className="flex items-center"
              >
                <HiOutlineTicket className="mr-2 h-4 w-4" />
                Apply Discount
              </Button>
            </div>
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default DiscountSelector;
