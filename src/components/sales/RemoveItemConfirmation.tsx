import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Button } from 'flowbite-react';
import { HiOutlineExclamation, HiOutlineTrash } from 'react-icons/hi';

interface RemoveItemConfirmationProps {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  itemName: string;
}

const RemoveItemConfirmation: React.FC<RemoveItemConfirmationProps> = ({
  show,
  onClose,
  onConfirm,
  itemName
}) => {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  // Handle ESC key to close modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && show) {
        onClose();
      }
    };

    if (show) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [show, onClose]);

  return (
    <Modal show={show} onClose={onClose} size="sm">
      <Modal.Header>
        <div className="flex items-center">
          <HiOutlineExclamation className="mr-2 h-6 w-6 text-red-500" />
          Confirm Item Removal
        </div>
      </Modal.Header>
      <Modal.Body>
        <div className="text-center">
          <HiOutlineExclamation className="mx-auto mb-4 h-14 w-14 text-gray-400" />
          <p className="text-gray-700 text-lg">
            Are you sure you want to remove <span className="font-medium">{itemName}</span> from the cart?
          </p>
          <p className="text-gray-500 text-sm mt-2">
            This action cannot be undone.
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="flex justify-center gap-4 w-full">
          <Button color="gray" onClick={onClose}>
            Cancel
          </Button>
          <Button
            color="failure"
            onClick={handleConfirm}
            className="flex items-center"
          >
            <HiOutlineTrash className="mr-2 h-4 w-4" />
            Remove Item
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default RemoveItemConfirmation;
