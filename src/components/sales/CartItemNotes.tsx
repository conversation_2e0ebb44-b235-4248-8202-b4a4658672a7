import React, { useState } from 'react';
import { Modal, Button, Textarea } from 'flowbite-react';
import { HiOutlineDocumentText } from 'react-icons/hi';

interface CartItemNotesProps {
  itemId: string;
  initialNotes?: string;
  onSave: (itemId: string, notes: string) => void;
}

const CartItemNotes: React.FC<CartItemNotesProps> = ({ 
  itemId, 
  initialNotes = '', 
  onSave 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [notes, setNotes] = useState(initialNotes);
  
  const handleOpen = () => {
    setIsOpen(true);
  };
  
  const handleClose = () => {
    setIsOpen(false);
  };
  
  const handleSave = () => {
    onSave(itemId, notes);
    setIsOpen(false);
  };
  
  return (
    <>
      <Button 
        size="xs" 
        color="light" 
        onClick={handleOpen}
        className="text-xs"
      >
        <HiOutlineDocumentText className="mr-1 h-3 w-3" />
        {initialNotes ? 'Edit Notes' : 'Add Notes'}
      </Button>
      
      <Modal show={isOpen} onClose={handleClose} size="md">
        <Modal.Header>Add Special Instructions</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p className="text-sm text-gray-500">
              Add any special instructions or notes for this item.
            </p>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="E.g., No onions, extra sauce, etc."
              rows={4}
            />
          </div>
        </Modal.Body>
        <Modal.Footer>
          <div className="flex justify-end gap-2">
            <Button color="gray" onClick={handleClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Notes
            </Button>
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default CartItemNotes;
