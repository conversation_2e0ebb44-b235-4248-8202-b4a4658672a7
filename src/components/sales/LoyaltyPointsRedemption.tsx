import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  TextInput,
  Label,
  Alert,
  Spinner,
  Badge
} from 'flowbite-react';
import {
  HiOutlineGift,
  HiOutlineInformationCircle,
  HiOutlineChevronDown,
  HiOutlineChevronUp
} from 'react-icons/hi';
import { useLoyalty } from '../../context/LoyaltyContext';
import { Customer } from '../../services/customer';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

interface LoyaltyPointsRedemptionProps {
  customer: Customer | null;
  onApplyPoints: (points: number, discount: number) => void;
  disabled?: boolean;
  totalAmount: number;
}

const LoyaltyPointsRedemption: React.FC<LoyaltyPointsRedemptionProps> = ({
  customer,
  onApplyPoints,
  disabled = false,
  totalAmount
}) => {
  const { settings, getCustomerProfile, calculateDiscount, calculatePoints } = useLoyalty();
  const formatWithCurrency = useCurrencyFormatter();

  const [customerProfile, setCustomerProfile] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pointsToRedeem, setPointsToRedeem] = useState<number>(0);
  const [potentialDiscount, setPotentialDiscount] = useState<number>(0);
  const [potentialPoints, setPotentialPoints] = useState<number>(0);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  // Fetch customer loyalty profile when customer changes
  useEffect(() => {
    const fetchCustomerProfile = async () => {
      if (!customer) {
        setCustomerProfile(null);
        return;
      }

      setIsLoading(true);
      try {
        const profile = await getCustomerProfile(customer.id);
        setCustomerProfile(profile);
        // Reset points to redeem when customer changes
        setPointsToRedeem(0);
        setPotentialDiscount(0);
      } catch (err) {
        console.error('Error fetching customer profile:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomerProfile();
  }, [customer, getCustomerProfile]);

  // Calculate potential points to be earned
  useEffect(() => {
    const calculatePotentialPoints = async () => {
      if (!customer || !settings?.is_enabled || totalAmount <= 0) {
        setPotentialPoints(0);
        return;
      }

      try {
        const points = await calculatePoints(totalAmount, customer.id);
        setPotentialPoints(points);
      } catch (err) {
        console.error('Error calculating potential points:', err);
        setPotentialPoints(0);
      }
    };

    calculatePotentialPoints();
  }, [customer, totalAmount, calculatePoints, settings]);

  // Handle points input change
  const handlePointsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (isNaN(value) || value < 0) {
      setPointsToRedeem(0);
      setPotentialDiscount(0);
      return;
    }

    // Don't allow more points than the customer has
    if (customerProfile && value > customerProfile.current_points_balance) {
      setPointsToRedeem(customerProfile.current_points_balance);
    } else {
      setPointsToRedeem(value);
    }

    // Calculate potential discount
    calculatePotentialDiscount(value);
  };

  // Calculate potential discount for entered points
  const calculatePotentialDiscount = async (points: number) => {
    if (!customer || points <= 0) {
      setPotentialDiscount(0);
      return;
    }

    try {
      const { discount, error: discountError } = await calculateDiscount(points, customer.id);

      if (discountError) {
        setError(discountError);
        setPotentialDiscount(0);
      } else {
        setError(null);
        setPotentialDiscount(discount);
      }
    } catch (err: any) {
      console.error('Error calculating discount:', err);
      setError(err.message || 'Error calculating discount');
      setPotentialDiscount(0);
    }
  };

  // Handle apply points button click
  const handleApplyPoints = () => {
    if (pointsToRedeem > 0 && potentialDiscount > 0) {
      onApplyPoints(pointsToRedeem, potentialDiscount);
    }
  };

  // Handle max points button click
  const handleUseMaxPoints = async () => {
    if (!customer || !customerProfile) return;

    const maxPoints = customerProfile.current_points_balance;
    setPointsToRedeem(maxPoints);

    try {
      const { discount, error: discountError } = await calculateDiscount(maxPoints, customer.id);

      if (discountError) {
        setError(discountError);
        setPotentialDiscount(0);
      } else {
        setError(null);
        setPotentialDiscount(discount);
      }
    } catch (err: any) {
      console.error('Error calculating max discount:', err);
      setError(err.message || 'Error calculating discount');
      setPotentialDiscount(0);
    }
  };

  // If loyalty program is disabled, no customer is selected, or customer is not eligible
  if (!settings?.is_enabled || !customer || customer.loyalty_eligible === false) {
    return null;
  }

  return (
    <Card className="mb-4">
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center">
          <HiOutlineGift className="mr-2 h-5 w-5 text-blue-500" />
          <h3 className="text-lg font-medium">Loyalty Points</h3>
        </div>

        <div className="flex items-center">
          {customerProfile && (
            <Badge color="info" className="text-xs mr-2">
              {customerProfile.current_points_balance} points
            </Badge>
          )}
          {isExpanded ? (
            <HiOutlineChevronUp className="h-5 w-5 text-gray-500" />
          ) : (
            <HiOutlineChevronDown className="h-5 w-5 text-gray-500" />
          )}
        </div>
      </div>

      {isExpanded && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          {isLoading ? (
            <div className="flex justify-center py-2">
              <Spinner size="sm" />
            </div>
          ) : !customerProfile ? (
            <p className="text-sm text-gray-500">
              This customer doesn't have a loyalty profile yet. They'll earn points with this purchase.
            </p>
          ) : (
            <>
              {error && (
                <Alert color="failure" className="mb-2 text-sm">
                  {error}
                </Alert>
              )}

              <div className="space-y-3">
                {potentialPoints > 0 && (
                  <div className="text-sm text-green-600 flex items-center">
                    <HiOutlineInformationCircle className="mr-1 h-4 w-4" />
                    Customer will earn {potentialPoints} points with this purchase
                  </div>
                )}

                <div>
                  <div className="flex items-center justify-between mb-1">
                    <Label htmlFor="pointsToRedeem" className="text-sm mb-0">Points to Redeem</Label>
                    <Button
                      size="xs"
                      color="light"
                      onClick={handleUseMaxPoints}
                      disabled={disabled || !customerProfile || customerProfile.current_points_balance <= 0}
                    >
                      Use Max
                    </Button>
                  </div>
                  <TextInput
                    id="pointsToRedeem"
                    type="number"
                    min="0"
                    max={customerProfile?.current_points_balance || 0}
                    value={pointsToRedeem}
                    onChange={handlePointsChange}
                    disabled={disabled || !customerProfile || customerProfile.current_points_balance <= 0}
                    className="mb-2"
                  />

                  {potentialDiscount > 0 && (
                    <div className="text-sm text-blue-600 mb-2">
                      Discount value: {formatWithCurrency(potentialDiscount)}
                    </div>
                  )}

                  <Button
                    color="primary"
                    size="sm"
                    className="w-full"
                    onClick={handleApplyPoints}
                    disabled={
                      disabled ||
                      !customerProfile ||
                      pointsToRedeem <= 0 ||
                      potentialDiscount <= 0 ||
                      pointsToRedeem > customerProfile.current_points_balance
                    }
                  >
                    Apply Points
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </Card>
  );
};

export default LoyaltyPointsRedemption;
