import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  But<PERSON>,
  Spinner,
  Table,
  Tabs,
  Badge,
  TextInput,
  Label,
  Select
} from 'flowbite-react';
import {
  HiOutlineCalendar,
  HiOutlineSearch,
  HiOutlineEye,
  HiOutlineDocumentReport,
  HiOutlineRefresh,
  HiOutlineFilter
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getSales } from '../../services/sale';
import { formatDate, formatDateTime } from '../../utils/formatters';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';
import Pagination from '../common/Pagination';

interface SalesHistoryProps {
  show: boolean;
  onClose: () => void;
  onViewReceipt: (saleId: string) => void;
}

const SalesHistory: React.FC<SalesHistoryProps> = ({
  show,
  onClose,
  onViewReceipt
}) => {
  const { currentOrganization } = useOrganization();
  const formatWithCurrency = useCurrencyFormatter();

  const [sales, setSales] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [dateFilter, setDateFilter] = useState<'today' | 'yesterday' | 'week' | 'month' | 'custom'>('today');
  const [startDate, setStartDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Fetch sales data
  const fetchSales = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      // Calculate date range based on filter
      let calculatedStartDate = new Date();
      const calculatedEndDate = new Date();

      switch (dateFilter) {
        case 'today':
          calculatedStartDate = new Date();
          calculatedStartDate.setHours(0, 0, 0, 0);
          break;

        case 'yesterday':
          calculatedStartDate = new Date();
          calculatedStartDate.setDate(calculatedStartDate.getDate() - 1);
          calculatedStartDate.setHours(0, 0, 0, 0);
          calculatedEndDate.setDate(calculatedEndDate.getDate() - 1);
          calculatedEndDate.setHours(23, 59, 59, 999);
          break;

        case 'week':
          calculatedStartDate = new Date();
          calculatedStartDate.setDate(calculatedStartDate.getDate() - 7);
          calculatedStartDate.setHours(0, 0, 0, 0);
          break;

        case 'month':
          calculatedStartDate = new Date();
          calculatedStartDate.setMonth(calculatedStartDate.getMonth() - 1);
          calculatedStartDate.setHours(0, 0, 0, 0);
          break;

        case 'custom':
          calculatedStartDate = new Date(startDate);
          calculatedStartDate.setHours(0, 0, 0, 0);
          const customEndDate = new Date(endDate);
          customEndDate.setHours(23, 59, 59, 999);
          break;
      }

      // Get sales for the selected date range
      const { sales: salesData, error: salesError } = await getSales(currentOrganization.id, {
        startDate: calculatedStartDate.toISOString(),
        endDate: dateFilter === 'custom' ? new Date(endDate + 'T23:59:59').toISOString() : calculatedEndDate.toISOString(),
        limit: 100,
        sortBy: 'created_at',
        sortOrder: 'desc'
      });

      if (salesError) {
        throw new Error(salesError);
      }

      setSales(salesData);
    } catch (err: any) {
      console.error('Error fetching sales:', err);
      setError(err.message || 'Failed to fetch sales');
    } finally {
      setLoading(false);
    }
  };

  // Fetch sales when modal is opened or filter changes
  useEffect(() => {
    if (show) {
      fetchSales();
    }
  }, [show, dateFilter, startDate, endDate, currentOrganization]);

  // Filter sales by search term
  const filteredSales = sales.filter(sale => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      (sale.invoice_number && sale.invoice_number.toLowerCase().includes(searchLower)) ||
      (sale.customer && sale.customer.name && sale.customer.name.toLowerCase().includes(searchLower))
    );
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredSales.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentSales = filteredSales.slice(indexOfFirstItem, indexOfLastItem);

  // Calculate totals
  const totalSales = filteredSales.length;
  const totalAmount = filteredSales.reduce((sum, sale) => sum + sale.total_amount, 0);

  // Handle ESC key press
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && show) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscKey);
    return () => {
      window.removeEventListener('keydown', handleEscKey);
    };
  }, [show, onClose]);

  return (
    <Modal show={show} onClose={onClose} size="7xl">
      <Modal.Header>
        <div className="flex items-center">
          <HiOutlineDocumentReport className="mr-2 h-5 w-5 text-blue-500" />
          Sales History
          <span className="ml-2 text-xs bg-gray-200 px-1.5 py-0.5 rounded">F8</span>
        </div>
      </Modal.Header>
      <Modal.Body>
        <div className="space-y-4">
          {/* Filters */}
          <div className="flex flex-wrap gap-3 items-end">
            <div className="flex-1">
              <Label htmlFor="search" value="Search" />
              <TextInput
                id="search"
                type="text"
                icon={HiOutlineSearch}
                placeholder="Search by invoice # or customer"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="dateFilter" value="Date Range" />
              <Select
                id="dateFilter"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value as any)}
              >
                <option value="today">Today</option>
                <option value="yesterday">Yesterday</option>
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
                <option value="custom">Custom Range</option>
              </Select>
            </div>

            {dateFilter === 'custom' && (
              <>
                <div>
                  <Label htmlFor="startDate" value="Start Date" />
                  <TextInput
                    id="startDate"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="endDate" value="End Date" />
                  <TextInput
                    id="endDate"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
              </>
            )}

            <Button color="light" onClick={fetchSales}>
              <HiOutlineRefresh className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>

          {/* Summary */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-100">
              <div className="text-sm text-blue-700">Total Sales</div>
              <div className="text-xl font-bold">{totalSales}</div>
            </div>
            <div className="bg-green-50 p-3 rounded-lg border border-green-100">
              <div className="text-sm text-green-700">Total Amount</div>
              <div className="text-xl font-bold">{formatWithCurrency(totalAmount)}</div>
            </div>
          </div>

          {/* Sales Table */}
          {loading ? (
            <div className="flex justify-center py-8">
              <Spinner size="xl" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              {error}
            </div>
          ) : filteredSales.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No sales found for the selected period
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table hoverable>
                <Table.Head>
                  <Table.HeadCell>Invoice #</Table.HeadCell>
                  <Table.HeadCell>Date</Table.HeadCell>
                  <Table.HeadCell>Customer</Table.HeadCell>
                  <Table.HeadCell>Cashier</Table.HeadCell>
                  <Table.HeadCell>Payment</Table.HeadCell>
                  <Table.HeadCell>Amount</Table.HeadCell>
                  <Table.HeadCell>Actions</Table.HeadCell>
                </Table.Head>
                <Table.Body className="divide-y">
                  {currentSales.map((sale) => (
                    <Table.Row key={sale.id} className="bg-white">
                      <Table.Cell className="font-medium">
                        {sale.invoice_number}
                      </Table.Cell>
                      <Table.Cell>
                        {formatDateTime(sale.created_at)}
                      </Table.Cell>
                      <Table.Cell>
                        {sale.customer ? sale.customer.name : 'Walk-in Customer'}
                      </Table.Cell>
                      <Table.Cell>
                        {sale.cashier ? (
                          <div className="text-sm">
                            <div className="font-medium">
                              {`${sale.cashier.first_name || ''} ${sale.cashier.last_name || ''}`.trim() || 'Unknown User'}
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">Unknown</span>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        <Badge color={sale.payment_method === 'cash' ? 'success' : 'info'}>
                          {sale.payment_method === 'cash' ? 'Cash' : 'Card'}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell className="font-medium">
                        {formatWithCurrency(sale.total_amount)}
                      </Table.Cell>
                      <Table.Cell>
                        <Button
                          size="xs"
                          color="light"
                          onClick={() => onViewReceipt(sale.id)}
                        >
                          <HiOutlineEye className="mr-1 h-3 w-3" />
                          View
                        </Button>
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>

              {/* Pagination */}
              {filteredSales.length > 0 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  itemsPerPage={itemsPerPage}
                  totalItems={filteredSales.length}
                  onPageChange={setCurrentPage}
                  onItemsPerPageChange={(newItemsPerPage) => {
                    setItemsPerPage(newItemsPerPage);
                    setCurrentPage(1); // Reset to first page when changing items per page
                  }}
                  itemName="sales"
                />
              )}
            </div>
          )}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="flex justify-between w-full items-center">
          <p className="text-sm text-gray-500">
            Press <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">ESC</kbd> to close
          </p>
          <Button color="gray" onClick={onClose}>
            Close
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default SalesHistory;
