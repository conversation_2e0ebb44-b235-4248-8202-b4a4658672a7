import React, { useState } from 'react';
import {
  Button,
  Modal,
  TextInput,
  Label,
  Select
} from 'flowbite-react';
import {
  HiOutlineHome,
  HiOutlineUserGroup,
  HiOutlineClipboard
} from 'react-icons/hi';

interface Table {
  id: string;
  name: string;
  seats: number;
  status: 'available' | 'occupied' | 'reserved';
}

interface RestaurantControlsProps {
  onSelectTable: (table: Table | null) => void;
  selectedTable: Table | null;
}

const RestaurantControls: React.FC<RestaurantControlsProps> = ({
  onSelectTable,
  selectedTable
}) => {
  const [isTableModalOpen, setIsTableModalOpen] = useState(false);

  // Mock tables data (in a real app, this would come from the database)
  const tables: Table[] = [
    { id: '1', name: 'Table 1', seats: 2, status: 'available' },
    { id: '2', name: 'Table 2', seats: 4, status: 'occupied' },
    { id: '3', name: 'Table 3', seats: 4, status: 'available' },
    { id: '4', name: 'Table 4', seats: 6, status: 'reserved' },
    { id: '5', name: 'Table 5', seats: 2, status: 'available' },
    { id: '6', name: 'Table 6', seats: 8, status: 'available' },
    { id: '7', name: 'Bar 1', seats: 1, status: 'occupied' },
    { id: '8', name: 'Bar 2', seats: 1, status: 'available' },
    { id: '9', name: 'Bar 3', seats: 1, status: 'available' },
    { id: '10', name: 'Bar 4', seats: 1, status: 'available' },
  ];

  const handleSelectTable = (table: Table) => {
    onSelectTable(table);
    setIsTableModalOpen(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'occupied':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'reserved':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="mb-4">
      <div className="flex flex-col gap-2">
        <Button
          color="light"
          className="w-full flex items-center justify-between"
          onClick={() => setIsTableModalOpen(true)}
        >
          <div className="flex items-center">
            <HiOutlineHome className="mr-2 h-5 w-5" />
            {selectedTable ? selectedTable.name : 'Select Table'}
          </div>
          {selectedTable && (
            <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(selectedTable.status)}`}>
              {selectedTable.status.charAt(0).toUpperCase() + selectedTable.status.slice(1)}
            </span>
          )}
        </Button>

        <div className="grid grid-cols-2 gap-2">
          <Button color="light" size="sm">
            <HiOutlineUserGroup className="mr-2 h-4 w-4" />
            Split Bill
          </Button>
          <Button color="light" size="sm">
            <HiOutlineClipboard className="mr-2 h-4 w-4" />
            Order History
          </Button>
        </div>
      </div>

      {/* Table Selection Modal */}
      <Modal show={isTableModalOpen} onClose={() => setIsTableModalOpen(false)} size="lg">
        <Modal.Header>Select Table</Modal.Header>
        <Modal.Body>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {tables.map(table => (
              <div
                key={table.id}
                className={`border rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow ${
                  table.status !== 'available' ? 'opacity-50' : ''
                }`}
                onClick={() => table.status === 'available' && handleSelectTable(table)}
              >
                <div className="flex justify-between items-start">
                  <h3 className="font-medium">{table.name}</h3>
                  <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(table.status)}`}>
                    {table.status.charAt(0).toUpperCase() + table.status.slice(1)}
                  </span>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {table.seats} {table.seats === 1 ? 'seat' : 'seats'}
                </p>
              </div>
            ))}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={() => setIsTableModalOpen(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default RestaurantControls;
