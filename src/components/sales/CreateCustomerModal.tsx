import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Button,
  TextInput,
  Label,
  Alert,
  Spinner,
  ToggleSwitch
} from 'flowbite-react';
import { HiOutlineUser, HiOutlineMail, HiOutlinePhone, HiOutlineGift } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { createCustomer } from '../../services/customer';

interface CreateCustomerModalProps {
  show: boolean;
  onClose: () => void;
  onCustomerCreated: (customerId: string) => void;
}

const CreateCustomerModal: React.FC<CreateCustomerModalProps> = ({
  show,
  onClose,
  onCustomerCreated
}) => {
  const { currentOrganization } = useOrganization();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    country: '',
    loyalty_eligible: true
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle toggle switch changes
  const handleToggleChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      loyalty_eligible: checked
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentOrganization) {
      setError('No organization selected');
      return;
    }

    if (!formData.name.trim()) {
      setError('Customer name is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const { customer, error: createError } = await createCustomer(
        currentOrganization.id,
        {
          name: formData.name,
          email: formData.email || null,
          phone: formData.phone || null,
          address: formData.address || null,
          city: formData.city || null,
          state: formData.state || null,
          postal_code: formData.postal_code || null,
          country: formData.country || null,
          loyalty_eligible: formData.loyalty_eligible
        }
      );

      if (createError) {
        throw new Error(createError);
      }

      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        state: '',
        postal_code: '',
        country: '',
        loyalty_eligible: true
      });

      // Notify parent component if callback exists
      if (typeof onCustomerCreated === 'function') {
        console.log('Calling onCustomerCreated with customer ID:', customer.id);
        onCustomerCreated(customer.id);
      } else {
        console.error('onCustomerCreated is not a function:', onCustomerCreated);
      }

      // Close modal
      onClose();
    } catch (err: any) {
      console.error('Error creating customer:', err);
      setError(err.message || 'Failed to create customer');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal show={show} onClose={onClose} size="lg">
      <Modal.Header>Create New Customer</Modal.Header>
      <Modal.Body>
        {error && (
          <Alert color="failure" className="mb-4">
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>

            {/* Name */}
            <div>
              <div className="mb-2 flex items-center">
                <HiOutlineUser className="mr-2 h-5 w-5 text-gray-500" />
                <Label htmlFor="name" value="Customer Name *" />
              </div>
              <TextInput
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>

            {/* Email */}
            <div>
              <div className="mb-2 flex items-center">
                <HiOutlineMail className="mr-2 h-5 w-5 text-gray-500" />
                <Label htmlFor="email" value="Email" />
              </div>
              <TextInput
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
              />
            </div>

            {/* Phone */}
            <div>
              <div className="mb-2 flex items-center">
                <HiOutlinePhone className="mr-2 h-5 w-5 text-gray-500" />
                <Label htmlFor="phone" value="Phone" />
              </div>
              <TextInput
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
              />
            </div>
          </div>

          {/* Loyalty Program */}
          <div className="pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <HiOutlineGift className="mr-2 h-5 w-5 text-blue-500" />
                <Label htmlFor="loyalty_eligible" value="Loyalty Program Eligible" className="mb-0" />
              </div>
              <ToggleSwitch
                id="loyalty_eligible"
                checked={formData.loyalty_eligible}
                onChange={handleToggleChange}
                label=""
              />
            </div>
            <p className="text-sm text-gray-500">
              Enable this to allow the customer to earn and redeem loyalty points
            </p>
          </div>
        </form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          color="primary"
          onClick={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Creating...
            </>
          ) : (
            'Create Customer'
          )}
        </Button>
        <Button
          color="gray"
          onClick={onClose}
        >
          Cancel
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CreateCustomerModal;
