import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Label, 
  TextInput, 
  Spinner, 
  Alert,
  ToggleSwitch
} from 'flowbite-react';
import { 
  HiOutlineReceiptTax
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';
import { updateOrganizationSettings } from '../../services/organization';

interface TaxSettingsProps {
  isOwner: boolean;
}

const TaxSettings: React.FC<TaxSettingsProps> = ({ isOwner }) => {
  const { currentOrganization } = useOrganization();
  const { settings, refreshSettings } = useOrganizationSettings();
  
  // Default tax settings for the Philippines
  const defaultTaxSettings = {
    vat_rate: 12, // 12% VAT in the Philippines
    vat_inclusive: true, // Default to VAT-inclusive pricing
    tax_enabled: true // Enable tax by default
  };
  
  // State for tax settings
  const [taxSettings, setTaxSettings] = useState(defaultTaxSettings);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Initialize tax settings from organization settings
  useEffect(() => {
    if (settings && settings.tax_settings) {
      setTaxSettings({
        ...defaultTaxSettings,
        ...settings.tax_settings
      });
    }
  }, [settings]);
  
  // Handle tax rate change
  const handleTaxRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 0 && value <= 100) {
      setTaxSettings(prev => ({
        ...prev,
        vat_rate: value
      }));
    }
  };
  
  // Handle tax inclusive toggle
  const handleTaxInclusiveToggle = () => {
    setTaxSettings(prev => ({
      ...prev,
      vat_inclusive: !prev.vat_inclusive
    }));
  };
  
  // Handle tax enabled toggle
  const handleTaxEnabledToggle = () => {
    setTaxSettings(prev => ({
      ...prev,
      tax_enabled: !prev.tax_enabled
    }));
  };
  
  // Save tax settings
  const handleSave = async () => {
    if (!currentOrganization) return;
    
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      // Update settings with the new tax settings
      const updatedSettings = {
        ...settings,
        tax_settings: taxSettings
      };
      
      await updateOrganizationSettings(currentOrganization.id, updatedSettings);
      await refreshSettings();
      setSuccess('Tax settings updated successfully');
    } catch (err: any) {
      setError(err.message || 'Failed to update tax settings');
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!isOwner) {
    return null;
  }
  
  return (
    <Card>
      <div className="flex items-center mb-4">
        <HiOutlineReceiptTax className="mr-2 h-6 w-6 text-blue-500" />
        <h2 className="text-xl font-bold">Tax Settings</h2>
      </div>
      
      {error && (
        <Alert color="failure" className="mb-4">
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert color="success" className="mb-4">
          {success}
        </Alert>
      )}
      
      <div className="space-y-4 mb-4">
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label htmlFor="taxEnabled" className="mb-0">Enable Tax Calculation</Label>
            <ToggleSwitch
              id="taxEnabled"
              checked={taxSettings.tax_enabled}
              onChange={handleTaxEnabledToggle}
              label=""
            />
          </div>
          <p className="text-sm text-gray-500">
            Enable or disable tax calculation for all transactions
          </p>
        </div>
        
        {taxSettings.tax_enabled && (
          <>
            <div>
              <Label htmlFor="vatRate" value="VAT Rate (%)" />
              <div className="flex items-center mt-1">
                <TextInput
                  id="vatRate"
                  type="number"
                  min={0}
                  max={100}
                  step={0.01}
                  value={taxSettings.vat_rate}
                  onChange={handleTaxRateChange}
                  className="flex-1"
                />
                <span className="ml-2">%</span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Standard VAT rate in the Philippines is 12%
              </p>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label htmlFor="vatInclusive" className="mb-0">VAT-Inclusive Pricing</Label>
                <ToggleSwitch
                  id="vatInclusive"
                  checked={taxSettings.vat_inclusive}
                  onChange={handleTaxInclusiveToggle}
                  label=""
                />
              </div>
              <p className="text-sm text-gray-500">
                When enabled, product prices are treated as VAT-inclusive. When disabled, VAT is added on top of the product price.
              </p>
            </div>
          </>
        )}
      </div>
      
      <Button 
        color="blue" 
        onClick={handleSave}
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Spinner size="sm" className="mr-2" />
            Saving...
          </>
        ) : (
          'Save Changes'
        )}
      </Button>
    </Card>
  );
};

export default TaxSettings;
