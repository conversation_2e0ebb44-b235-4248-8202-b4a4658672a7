import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner, Label, ToggleSwitch } from 'flowbite-react';
import { HiOutlineChatAlt2 } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';
import { updateOrganizationSettings } from '../../services/organization';

interface ChatSettingsProps {
  isOwner: boolean;
}

const ChatSettings: React.FC<ChatSettingsProps> = ({ isOwner }) => {
  const { currentOrganization } = useOrganization();
  const { settings, refreshSettings } = useOrganizationSettings();
  const [chatSettings, setChatSettings] = useState({
    chat_enabled: true
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Initialize chat settings from organization settings
  useEffect(() => {
    if (settings) {
      setChatSettings({
        chat_enabled: settings.chat_enabled !== false // Default to true if not set
      });
    }
  }, [settings]);

  // Handle chat enabled toggle
  const handleChatEnabledToggle = () => {
    setChatSettings(prev => ({
      ...prev,
      chat_enabled: !prev.chat_enabled
    }));
  };

  // Save chat settings
  const handleSave = async () => {
    if (!currentOrganization) {
      setError('No organization selected');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Update organization settings with chat settings
      const updatedSettings = {
        ...settings,
        chat_enabled: chatSettings.chat_enabled
      };

      await updateOrganizationSettings(currentOrganization.id, updatedSettings);
      await refreshSettings();
      setSuccess('Chat settings updated successfully');
    } catch (err: any) {
      console.error('Error updating chat settings:', err);
      setError(err.message || 'Failed to update chat settings');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <div className="flex items-center mb-4">
        <HiOutlineChatAlt2 className="mr-2 h-6 w-6 text-blue-500" />
        <h2 className="text-xl font-bold">Chat Settings</h2>
      </div>
      
      {error && (
        <Alert color="failure" className="mb-4">
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert color="success" className="mb-4">
          {success}
        </Alert>
      )}
      
      <div className="space-y-4 mb-4">
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label htmlFor="chatEnabled" className="mb-0">Enable Chat Functionality</Label>
            <ToggleSwitch
              id="chatEnabled"
              checked={chatSettings.chat_enabled}
              onChange={handleChatEnabledToggle}
              label=""
              disabled={!isOwner}
            />
          </div>
          <p className="text-sm text-gray-500">
            Enable or disable the chat functionality for all users in this organization. 
            When disabled, the chat feature will not be available and no chat-related data will be loaded.
          </p>
          {!chatSettings.chat_enabled && (
            <Alert color="warning" className="mt-2">
              <p className="text-sm">
                <strong>Note:</strong> Disabling chat will hide the chat menu item and prevent users from accessing chat functionality.
                Existing chat data will be preserved but not accessible until chat is enabled again.
              </p>
            </Alert>
          )}
        </div>
      </div>
      
      <Button 
        color="blue" 
        onClick={handleSave}
        disabled={isLoading || !isOwner}
      >
        {isLoading ? (
          <>
            <Spinner size="sm" className="mr-2" />
            Saving...
          </>
        ) : (
          'Save Changes'
        )}
      </Button>
    </Card>
  );
};

export default ChatSettings;
