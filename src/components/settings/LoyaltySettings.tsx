import React, { useState, useEffect } from 'react';
import {
  Card,
  Label,
  TextInput,
  ToggleSwitch,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'flowbite-react';
import { HiOutlineGift, HiOutlineInformationCircle } from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useLoyalty } from '../../context/LoyaltyContext';
import { updateLoyaltyProgramSettings } from '../../services/loyalty';
import { useCurrencyFormatter } from '../../utils/currencyFormatter';

interface LoyaltySettingsProps {
  isOwner: boolean;
}

const LoyaltySettings: React.FC<LoyaltySettingsProps> = ({ isOwner }) => {
  const { currentOrganization } = useOrganization();
  const { settings, refreshSettings } = useLoyalty();
  const formatWithCurrency = useCurrencyFormatter();

  // Default loyalty settings
  const defaultLoyaltySettings = {
    is_enabled: false,
    points_earning_rate: 1.0,
    points_redemption_rate: 0.01,
    minimum_points_for_redemption: 100,
    points_expiration_months: null as number | null
  };

  // State for loyalty settings
  const [loyaltySettings, setLoyaltySettings] = useState(defaultLoyaltySettings);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Initialize loyalty settings from context
  useEffect(() => {
    if (settings) {
      setLoyaltySettings({
        is_enabled: settings.is_enabled,
        points_earning_rate: settings.points_earning_rate,
        points_redemption_rate: settings.points_redemption_rate,
        minimum_points_for_redemption: settings.minimum_points_for_redemption,
        points_expiration_months: settings.points_expiration_months
      });
    }
  }, [settings]);

  // Handle earning rate change
  const handleEarningRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 0) {
      setLoyaltySettings(prev => ({
        ...prev,
        points_earning_rate: value
      }));
    }
  };

  // Handle redemption rate change
  const handleRedemptionRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 0) {
      setLoyaltySettings(prev => ({
        ...prev,
        points_redemption_rate: value
      }));
    }
  };

  // Handle minimum points change
  const handleMinimumPointsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= 0) {
      setLoyaltySettings(prev => ({
        ...prev,
        minimum_points_for_redemption: value
      }));
    }
  };

  // Handle expiration months change
  const handleExpirationMonthsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value === '' ? null : parseInt(e.target.value);
    if (value === null || (!isNaN(value) && value >= 0)) {
      setLoyaltySettings(prev => ({
        ...prev,
        points_expiration_months: value
      }));
    }
  };

  // Handle loyalty program toggle
  const handleLoyaltyEnabledToggle = () => {
    setLoyaltySettings(prev => ({
      ...prev,
      is_enabled: !prev.is_enabled
    }));
  };

  // Save loyalty settings
  const handleSave = async () => {
    if (!currentOrganization) {
      setError('No organization selected');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await updateLoyaltyProgramSettings(currentOrganization.id, loyaltySettings);
      await refreshSettings();
      setSuccess('Loyalty program settings updated successfully');
    } catch (err: any) {
      console.error('Error updating loyalty settings:', err);
      setError(err.message || 'Failed to update loyalty settings');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate example values
  const examplePurchaseAmount = 100;
  const examplePointsEarned = Math.floor(examplePurchaseAmount * loyaltySettings.points_earning_rate);
  const exampleRedemptionAmount = Math.floor(1000 * loyaltySettings.points_redemption_rate);

  return (
    <Card>
      <div className="flex items-center mb-4">
        <HiOutlineGift className="mr-2 h-6 w-6 text-blue-500" />
        <h2 className="text-xl font-bold">Loyalty Program Settings</h2>
      </div>

      {error && (
        <Alert color="failure" className="mb-4">
          {error}
        </Alert>
      )}

      {success && (
        <Alert color="success" className="mb-4">
          {success}
        </Alert>
      )}

      <div className="space-y-4 mb-4">
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label htmlFor="loyaltyEnabled" className="mb-0">Enable Loyalty Program</Label>
            <ToggleSwitch
              id="loyaltyEnabled"
              checked={loyaltySettings.is_enabled}
              onChange={handleLoyaltyEnabledToggle}
              disabled={!isOwner}
              label=""
            />
          </div>
          <p className="text-sm text-gray-500">
            Enable or disable the customer loyalty points program
          </p>
        </div>

        {loyaltySettings.is_enabled && (
          <>
            <div>
              <div className="flex items-center mb-2">
                <Label htmlFor="earningRate" className="mb-0">Points Earning Rate</Label>
                <Tooltip content="Number of points earned per currency unit spent">
                  <HiOutlineInformationCircle className="ml-2 h-4 w-4 text-gray-400" />
                </Tooltip>
              </div>
              <TextInput
                id="earningRate"
                type="number"
                step="0.001"
                min="0"
                value={loyaltySettings.points_earning_rate}
                onChange={handleEarningRateChange}
                disabled={!isOwner}
                helperText={`Example: ${examplePointsEarned} points earned for a ${formatWithCurrency(examplePurchaseAmount)} purchase`}
              />
            </div>

            <div>
              <div className="flex items-center mb-2">
                <Label htmlFor="redemptionRate" className="mb-0">Points Redemption Rate</Label>
                <Tooltip content="Currency value of each point when redeemed">
                  <HiOutlineInformationCircle className="ml-2 h-4 w-4 text-gray-400" />
                </Tooltip>
              </div>
              <TextInput
                id="redemptionRate"
                type="number"
                step="0.001"
                min="0"
                value={loyaltySettings.points_redemption_rate}
                onChange={handleRedemptionRateChange}
                disabled={!isOwner}
                helperText={`Example: 1,000 points = ${formatWithCurrency(exampleRedemptionAmount)} discount`}
              />
            </div>

            <div>
              <div className="flex items-center mb-2">
                <Label htmlFor="minimumPoints" className="mb-0">Minimum Points for Redemption</Label>
                <Tooltip content="Minimum number of points required to redeem">
                  <HiOutlineInformationCircle className="ml-2 h-4 w-4 text-gray-400" />
                </Tooltip>
              </div>
              <TextInput
                id="minimumPoints"
                type="number"
                min="0"
                value={loyaltySettings.minimum_points_for_redemption}
                onChange={handleMinimumPointsChange}
                disabled={!isOwner}
              />
            </div>

            <div>
              <div className="flex items-center mb-2">
                <Label htmlFor="expirationMonths" className="mb-0">Points Expiration (Months)</Label>
                <Tooltip content="Number of months after which points expire (leave empty for no expiration)">
                  <HiOutlineInformationCircle className="ml-2 h-4 w-4 text-gray-400" />
                </Tooltip>
              </div>
              <TextInput
                id="expirationMonths"
                type="number"
                min="0"
                value={loyaltySettings.points_expiration_months === null ? '' : loyaltySettings.points_expiration_months}
                onChange={handleExpirationMonthsChange}
                disabled={!isOwner}
                placeholder="Never expire"
              />
            </div>
          </>
        )}
      </div>

      {isOwner && (
        <Button
          color="primary"
          onClick={handleSave}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      )}
    </Card>
  );
};

export default LoyaltySettings;
