import React, { useState, useEffect } from 'react';
import { Select, Label, Alert, Spinner } from 'flowbite-react';
import { HiOutlineCurrencyDollar } from 'react-icons/hi';

// =====================================================
// CURRENCY DATA
// =====================================================

interface Currency {
  code: string;
  name: string;
  symbol: string;
  locale: string;
  flag?: string;
}

// Comprehensive currency list with Philippines at the top
const currencies: Currency[] = [
  // Philippines first (as requested)
  {
    code: 'PHP',
    name: 'Philippine Peso',
    symbol: '₱',
    locale: 'en-PH',
    flag: '🇵🇭'
  },
  
  // Major currencies
  {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    locale: 'en-US',
    flag: '🇺🇸'
  },
  {
    code: 'EUR',
    name: 'Euro',
    symbol: '€',
    locale: 'en-EU',
    flag: '🇪🇺'
  },
  {
    code: 'GBP',
    name: 'British Pound',
    symbol: '£',
    locale: 'en-GB',
    flag: '🇬🇧'
  },
  {
    code: 'JPY',
    name: 'Japanese Yen',
    symbol: '¥',
    locale: 'ja-JP',
    flag: '🇯🇵'
  },
  
  // Asian currencies
  {
    code: 'SGD',
    name: 'Singapore Dollar',
    symbol: 'S$',
    locale: 'en-SG',
    flag: '🇸🇬'
  },
  {
    code: 'HKD',
    name: 'Hong Kong Dollar',
    symbol: 'HK$',
    locale: 'en-HK',
    flag: '🇭🇰'
  },
  {
    code: 'MYR',
    name: 'Malaysian Ringgit',
    symbol: 'RM',
    locale: 'ms-MY',
    flag: '🇲🇾'
  },
  {
    code: 'THB',
    name: 'Thai Baht',
    symbol: '฿',
    locale: 'th-TH',
    flag: '🇹🇭'
  },
  {
    code: 'IDR',
    name: 'Indonesian Rupiah',
    symbol: 'Rp',
    locale: 'id-ID',
    flag: '🇮🇩'
  },
  {
    code: 'VND',
    name: 'Vietnamese Dong',
    symbol: '₫',
    locale: 'vi-VN',
    flag: '🇻🇳'
  },
  {
    code: 'KRW',
    name: 'South Korean Won',
    symbol: '₩',
    locale: 'ko-KR',
    flag: '🇰🇷'
  },
  {
    code: 'CNY',
    name: 'Chinese Yuan',
    symbol: '¥',
    locale: 'zh-CN',
    flag: '🇨🇳'
  },
  {
    code: 'INR',
    name: 'Indian Rupee',
    symbol: '₹',
    locale: 'en-IN',
    flag: '🇮🇳'
  },
  
  // Other major currencies
  {
    code: 'AUD',
    name: 'Australian Dollar',
    symbol: 'A$',
    locale: 'en-AU',
    flag: '🇦🇺'
  },
  {
    code: 'CAD',
    name: 'Canadian Dollar',
    symbol: 'C$',
    locale: 'en-CA',
    flag: '🇨🇦'
  },
  {
    code: 'CHF',
    name: 'Swiss Franc',
    symbol: 'Fr',
    locale: 'de-CH',
    flag: '🇨🇭'
  },
  {
    code: 'NZD',
    name: 'New Zealand Dollar',
    symbol: 'NZ$',
    locale: 'en-NZ',
    flag: '🇳🇿'
  },
  {
    code: 'SEK',
    name: 'Swedish Krona',
    symbol: 'kr',
    locale: 'sv-SE',
    flag: '🇸🇪'
  },
  {
    code: 'NOK',
    name: 'Norwegian Krone',
    symbol: 'kr',
    locale: 'nb-NO',
    flag: '🇳🇴'
  },
  {
    code: 'DKK',
    name: 'Danish Krone',
    symbol: 'kr',
    locale: 'da-DK',
    flag: '🇩🇰'
  },
  
  // Middle East & Africa
  {
    code: 'AED',
    name: 'UAE Dirham',
    symbol: 'د.إ',
    locale: 'ar-AE',
    flag: '🇦🇪'
  },
  {
    code: 'SAR',
    name: 'Saudi Riyal',
    symbol: '﷼',
    locale: 'ar-SA',
    flag: '🇸🇦'
  },
  {
    code: 'ZAR',
    name: 'South African Rand',
    symbol: 'R',
    locale: 'en-ZA',
    flag: '🇿🇦'
  },
  
  // Latin America
  {
    code: 'BRL',
    name: 'Brazilian Real',
    symbol: 'R$',
    locale: 'pt-BR',
    flag: '🇧🇷'
  },
  {
    code: 'MXN',
    name: 'Mexican Peso',
    symbol: '$',
    locale: 'es-MX',
    flag: '🇲🇽'
  },
  {
    code: 'ARS',
    name: 'Argentine Peso',
    symbol: '$',
    locale: 'es-AR',
    flag: '🇦🇷'
  }
];

// =====================================================
// CURRENCY SELECTOR COMPONENT
// =====================================================

interface CurrencySelectorProps {
  value: string;
  onChange: (currency: Currency) => void;
  disabled?: boolean;
  error?: string;
  showPreview?: boolean;
}

const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  value,
  onChange,
  disabled = false,
  error,
  showPreview = true
}) => {
  const [selectedCurrency, setSelectedCurrency] = useState<Currency | null>(null);

  // Find the selected currency object
  useEffect(() => {
    const currency = currencies.find(c => c.code === value);
    setSelectedCurrency(currency || currencies[0]); // Default to PHP if not found
  }, [value]);

  const handleCurrencyChange = (currencyCode: string) => {
    const currency = currencies.find(c => c.code === currencyCode);
    if (currency) {
      setSelectedCurrency(currency);
      onChange(currency);
    }
  };

  const formatSampleAmount = (amount: number): string => {
    if (!selectedCurrency) return `${amount}`;
    
    try {
      return new Intl.NumberFormat(selectedCurrency.locale, {
        style: 'currency',
        currency: selectedCurrency.code,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    } catch (error) {
      // Fallback if locale is not supported
      return `${selectedCurrency.symbol} ${amount.toFixed(2)}`;
    }
  };

  return (
    <div className="space-y-3">
      {/* Currency Selector */}
      <div>
        <div className="flex items-center gap-2 mb-2">
          <HiOutlineCurrencyDollar className="h-4 w-4 text-gray-500" />
          <Label htmlFor="currency" value="Currency" />
        </div>
        
        <Select
          id="currency"
          value={value}
          onChange={(e) => handleCurrencyChange(e.target.value)}
          disabled={disabled}
          className={error ? 'border-red-500' : ''}
        >
          {currencies.map((currency) => (
            <option key={currency.code} value={currency.code}>
              {currency.flag} {currency.code} - {currency.name} ({currency.symbol})
            </option>
          ))}
        </Select>
        
        {error && (
          <Alert color="failure" className="mt-2">
            {error}
          </Alert>
        )}
      </div>

      {/* Currency Preview */}
      {showPreview && selectedCurrency && (
        <div className="p-3 bg-gray-50 rounded-lg border">
          <div className="text-sm text-gray-600 mb-2">
            <strong>Preview:</strong>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-gray-500">Currency Code:</div>
              <div className="font-medium">{selectedCurrency.code}</div>
            </div>
            <div>
              <div className="text-gray-500">Symbol:</div>
              <div className="font-medium text-lg">{selectedCurrency.symbol}</div>
            </div>
            <div>
              <div className="text-gray-500">Sample Amount:</div>
              <div className="font-medium">{formatSampleAmount(1234.56)}</div>
            </div>
            <div>
              <div className="text-gray-500">Locale:</div>
              <div className="font-medium">{selectedCurrency.locale}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CurrencySelector;
export { currencies, type Currency };
