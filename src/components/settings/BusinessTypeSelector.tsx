import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Label,
  Select,
  Spinner,
  Alert
} from 'flowbite-react';
import {
  HiOutlineShoppingBag,
  HiOutlineOfficeBuilding,
  HiOutlineBeaker,
  HiOutlineBookOpen
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';
import { updateOrganizationSettings } from '../../services/organization';

export type BusinessType = 'retail' | 'restaurant' | 'cafe' | 'bar';

interface BusinessTypeOption {
  value: BusinessType;
  label: string;
  icon: React.ReactNode;
  description: string;
}

const businessTypeOptions: BusinessTypeOption[] = [
  {
    value: 'retail',
    label: 'Retail Store',
    icon: <HiOutlineShoppingBag className="h-8 w-8 text-blue-500" />,
    description: 'General merchandise, clothing, electronics, etc.'
  },
  {
    value: 'restaurant',
    label: 'Restaurant',
    icon: <HiOutlineOfficeBuilding className="h-8 w-8 text-red-500" />,
    description: 'Full-service restaurant with table service'
  },
  {
    value: 'cafe',
    label: 'Cafe',
    icon: <HiOutlineBeaker className="h-8 w-8 text-green-500" />,
    description: 'Coffee shop, bakery, or casual dining'
  },
  {
    value: 'bar',
    label: 'Bar',
    icon: <HiOutlineBookOpen className="h-8 w-8 text-purple-500" />,
    description: 'Bar, pub, or nightclub'
  }
];

const BusinessTypeSelector: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const { settings, refreshSettings } = useOrganizationSettings();
  const [selectedType, setSelectedType] = useState<BusinessType>('retail');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Initialize selected type from settings
  useEffect(() => {
    if (settings && settings.business_type) {
      setSelectedType(settings.business_type as BusinessType);
    }
  }, [settings]);

  const handleSave = async () => {
    if (!currentOrganization) return;

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Define default tax settings based on business type for Philippines
      const taxSettings = {
        // VAT rate in the Philippines is 12%
        vat_rate: 12,
        // Default to VAT-inclusive pricing (common in the Philippines)
        vat_inclusive: true,
        // Enable tax by default
        tax_enabled: true
      };

      // Update settings with the new business type and tax settings
      const updatedSettings = {
        ...settings,
        business_type: selectedType,
        tax_settings: {
          ...(settings.tax_settings || {}),
          ...taxSettings
        }
      };

      await updateOrganizationSettings(currentOrganization.id, updatedSettings);
      await refreshSettings();
      setSuccess('Business type updated successfully');
    } catch (err: any) {
      setError(err.message || 'Failed to update business type');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <h2 className="text-xl font-bold mb-4">Business Type</h2>

      {error && (
        <Alert color="failure" className="mb-4">
          {error}
        </Alert>
      )}

      {success && (
        <Alert color="success" className="mb-4">
          {success}
        </Alert>
      )}

      <div className="mb-4">
        <Label htmlFor="businessType" value="Select your business type" />
        <Select
          id="businessType"
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value as BusinessType)}
          className="mt-2"
        >
          {businessTypeOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        {businessTypeOptions.map((option) => (
          <div
            key={option.value}
            className={`p-4 border rounded-lg cursor-pointer ${
              selectedType === option.value
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedType(option.value)}
          >
            <div className="flex items-start">
              <div className="mr-3">
                {option.icon}
              </div>
              <div>
                <h3 className="font-medium">{option.label}</h3>
                <p className="text-sm text-gray-500">{option.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <Button
        color="blue"
        onClick={handleSave}
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Spinner size="sm" className="mr-2" />
            Saving...
          </>
        ) : (
          'Save Changes'
        )}
      </Button>
    </Card>
  );
};

export default BusinessTypeSelector;
