import { Dropdown, <PERSON><PERSON>, Spinner } from 'flowbite-react';
import { useOrganization } from '../../context/OrganizationContext';
import { Link } from 'react-router-dom';
import { HiOutlineOfficeBuilding, HiPlus, HiSelector } from 'react-icons/hi';

const OrganizationSelector = () => {
  const { organizations, currentOrganization, setCurrentOrganization, loading } = useOrganization();

  if (loading) {
    return (
      <div className="flex items-center bg-primary text-white px-3 py-1.5 rounded-md">
        <Spinner size="sm" className="mr-2" />
        <span className="text-sm font-medium">Loading...</span>
      </div>
    );
  }

  if (!organizations || organizations.length === 0) {
    return (
      <Link to="/organization/create">
        <div className="flex items-center bg-primary text-white px-3 py-1.5 rounded-md hover:bg-primary-700 transition-colors">
          <HiPlus className="mr-2 h-4 w-4" />
          <span className="font-medium">Create Organization</span>
        </div>
      </Link>
    );
  }

  return (
    <div className="flex items-center">
      <Dropdown
        label={
          <div className="flex items-center bg-primary text-white px-3 py-1.5 rounded-md hover:bg-primary-700 transition-colors">
            <HiOutlineOfficeBuilding className="mr-2 h-4 w-4" />
            <span className="max-w-[150px] truncate font-medium">
              {currentOrganization?.name || 'Select Organization'}
            </span>
            <HiSelector className="ml-2 h-4 w-4" />
          </div>
        }
        color="light"
        size="sm"
      >
        <Dropdown.Header>
          <span className="block text-sm font-medium">Your Organizations</span>
        </Dropdown.Header>
        {organizations.map((org) => (
          <Dropdown.Item
            key={org.id}
            onClick={() => {
              // Set the organization in context and localStorage
              setCurrentOrganization(org);
              // Force a page reload to ensure all components update
              window.location.reload();
            }}
            className={currentOrganization?.id === org.id ? 'bg-gray-100 dark:bg-gray-700' : ''}
          >
            <div className="flex items-center">
              <HiOutlineOfficeBuilding className="mr-2 h-4 w-4" />
              <span>{org.name}</span>
            </div>
          </Dropdown.Item>
        ))}
        <Dropdown.Divider />
        <Link to="/organization/create">
          <Dropdown.Item>
            <div className="flex items-center">
              <HiPlus className="mr-2 h-4 w-4" />
              <span>Create New Organization</span>
            </div>
          </Dropdown.Item>
        </Link>
      </Dropdown>
    </div>
  );
};

export default OrganizationSelector;
