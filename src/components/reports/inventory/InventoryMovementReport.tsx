import React, { useState } from 'react';
import { Table, Button, TextInput, Dropdown, Badge } from 'flowbite-react';
import { formatCurrency } from '../../../utils/formatters';
import { HiOutlineSearch, HiOutlineDownload, HiOutlineArrowUp, HiOutlineArrowDown } from 'react-icons/hi';

interface InventoryMovementReportProps {
  products: any[];
  transactions?: any[];
}

const InventoryMovementReport: React.FC<InventoryMovementReportProps> = ({
  products = [],
  transactions = []
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string>('movement');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [timeRange, setTimeRange] = useState<'7days' | '30days' | '90days' | 'all'>('30days');

  // Add safety logging
  React.useEffect(() => {
    console.log('InventoryMovementReport received:', {
      productsCount: products?.length || 0,
      transactionsCount: transactions?.length || 0,
      sampleProduct: products?.[0] ? {
        name: products[0].name,
        sku: products[0].sku,
        stock_quantity: products[0].stock_quantity
      } : 'No products',
      sampleTransaction: transactions?.[0] ? {
        product_id: transactions[0].product_id,
        quantity: transactions[0].quantity,
        transaction_type: transactions[0].transaction_type,
        created_at: transactions[0].created_at
      } : 'No transactions'
    });
  }, [products, transactions]);

  // Calculate date range for filtering transactions
  const getDateRange = () => {
    const endDate = new Date();
    const startDate = new Date();

    switch (timeRange) {
      case '7days':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90days':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case 'all':
      default:
        startDate.setFullYear(startDate.getFullYear() - 10); // Effectively all
        break;
    }

    return { startDate, endDate };
  };

  // Process real transaction data if available, otherwise use mock data
  const productsWithMovement = (products || []).map(product => {
    if (transactions && transactions.length > 0) {
      // Filter transactions for this product within the selected time range
      const { startDate, endDate } = getDateRange();
      const productTransactions = transactions.filter(t =>
        t.product_id === product.id &&
        new Date(t.created_at) >= startDate &&
        new Date(t.created_at) <= endDate
      );

      // Calculate inflow (positive quantities) and outflow (negative quantities)
      const inflow = productTransactions
        .filter(t => t.quantity > 0)
        .reduce((sum, t) => sum + t.quantity, 0);

      const outflow = productTransactions
        .filter(t => t.quantity < 0)
        .reduce((sum, t) => sum + Math.abs(t.quantity), 0);

      const movement = inflow - outflow;

      return {
        ...product,
        inflow: inflow || 0,
        outflow: outflow || 0,
        movement: movement || 0,
        previous_stock: (product.stock_quantity || 0) - (movement || 0),
        transaction_count: productTransactions.length
      };
    } else {
      // Fallback to mock data if no transactions
      const inflow = Math.floor(Math.random() * 50);
      const outflow = Math.floor(Math.random() * 30);
      const movement = inflow - outflow;

      return {
        ...product,
        inflow,
        outflow,
        movement,
        previous_stock: (product.stock_quantity || 0) - movement,
        transaction_count: 0
      };
    }
  });

  // Filter products based on search term
  const filteredProducts = productsWithMovement.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.sku?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    let aValue, bValue;

    if (sortField === 'name') {
      aValue = a.name.toLowerCase();
      bValue = b.name.toLowerCase();
    } else if (sortField === 'sku') {
      aValue = a.sku?.toLowerCase() || '';
      bValue = b.sku?.toLowerCase() || '';
    } else if (sortField === 'current_stock') {
      aValue = a.stock_quantity;
      bValue = b.stock_quantity;
    } else if (sortField === 'previous_stock') {
      aValue = a.previous_stock;
      bValue = b.previous_stock;
    } else if (sortField === 'inflow') {
      aValue = a.inflow;
      bValue = b.inflow;
    } else if (sortField === 'outflow') {
      aValue = a.outflow;
      bValue = b.outflow;
    } else { // movement
      aValue = a.movement;
      bValue = b.movement;
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const exportCSV = () => {
    // Create CSV content
    const headers = ['Name', 'SKU', 'Previous Stock', 'Inflow', 'Outflow', 'Current Stock', 'Net Movement'];
    const rows = sortedProducts.map(product => [
      product.name,
      product.sku || 'N/A',
      product.previous_stock,
      product.inflow,
      product.outflow,
      product.stock_quantity,
      product.movement
    ]);

    // Convert to CSV
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'inventory_movement_report.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4">
        <div className="w-full md:w-auto">
          <TextInput
            id="search"
            type="text"
            icon={HiOutlineSearch}
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex gap-2 flex-wrap">
          <Dropdown label="Time Range" color="info" size="sm">
            <Dropdown.Item onClick={() => setTimeRange('7days')}>
              Last 7 Days {timeRange === '7days' && '✓'}
            </Dropdown.Item>
            <Dropdown.Item onClick={() => setTimeRange('30days')}>
              Last 30 Days {timeRange === '30days' && '✓'}
            </Dropdown.Item>
            <Dropdown.Item onClick={() => setTimeRange('90days')}>
              Last 90 Days {timeRange === '90days' && '✓'}
            </Dropdown.Item>
            <Dropdown.Item onClick={() => setTimeRange('all')}>
              All Time {timeRange === 'all' && '✓'}
            </Dropdown.Item>
          </Dropdown>

          <Dropdown label="Sort By" color="light" size="sm">
            <Dropdown.Item onClick={() => handleSort('name')}>Name</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('sku')}>SKU</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('previous_stock')}>Previous Stock</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('inflow')}>Inflow</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('outflow')}>Outflow</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('current_stock')}>Current Stock</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('movement')}>Net Movement</Dropdown.Item>
          </Dropdown>

          <Button color="light" size="sm" onClick={exportCSV}>
            <HiOutlineDownload className="mr-2 h-5 w-5" />
            Export
          </Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table striped>
          <Table.Head>
            <Table.HeadCell onClick={() => handleSort('name')} className="cursor-pointer">
              Product Name {sortField === 'name' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('sku')} className="cursor-pointer">
              SKU {sortField === 'sku' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('previous_stock')} className="cursor-pointer">
              Previous Stock {sortField === 'previous_stock' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('inflow')} className="cursor-pointer">
              Inflow {sortField === 'inflow' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('outflow')} className="cursor-pointer">
              Outflow {sortField === 'outflow' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('current_stock')} className="cursor-pointer">
              Current Stock {sortField === 'current_stock' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('movement')} className="cursor-pointer">
              Net Movement {sortField === 'movement' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
          </Table.Head>
          <Table.Body className="divide-y">
            {sortedProducts.map((product) => (
              <Table.Row key={product.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                  {product.name}
                </Table.Cell>
                <Table.Cell>{product.sku || 'N/A'}</Table.Cell>
                <Table.Cell>{product.previous_stock}</Table.Cell>
                <Table.Cell className="text-green-600">{product.inflow}</Table.Cell>
                <Table.Cell className="text-red-600">{product.outflow}</Table.Cell>
                <Table.Cell>{product.stock_quantity}</Table.Cell>
                <Table.Cell>
                  <div className="flex items-center">
                    {product.movement > 0 ? (
                      <>
                        <HiOutlineArrowUp className="text-green-600 mr-1" />
                        <span className="text-green-600">+{product.movement}</span>
                      </>
                    ) : product.movement < 0 ? (
                      <>
                        <HiOutlineArrowDown className="text-red-600 mr-1" />
                        <span className="text-red-600">{product.movement}</span>
                      </>
                    ) : (
                      <span>0</span>
                    )}
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>

      <div className="mt-4 text-sm text-gray-500">
        <p>
          {transactions && transactions.length > 0
            ? `Note: This report shows inventory movement for the selected time period (${timeRange === '7days' ? 'Last 7 Days' : timeRange === '30days' ? 'Last 30 Days' : timeRange === '90days' ? 'Last 90 Days' : 'All Time'}).`
            : 'Note: No transaction data available. Showing simulated data for demonstration purposes.'}
        </p>
      </div>
    </div>
  );
};

export default InventoryMovementReport;
