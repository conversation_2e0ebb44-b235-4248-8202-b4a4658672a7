import React, { useState } from 'react';
import { Table, Button, TextInput, Dropdown, Badge } from 'flowbite-react';
import { formatCurrency } from '../../../utils/formatters';
import { HiOutlineSearch, HiOutlineDownload, HiOutlineExclamation } from 'react-icons/hi';
import { Link } from 'react-router-dom';

interface LowStockReportProps {
  products: any[];
}

const LowStockReport: React.FC<LowStockReportProps> = ({ products = [] }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string>('stock_level');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Add safety logging
  React.useEffect(() => {
    console.log('LowStockReport received products:', {
      count: products?.length || 0,
      sampleProduct: products?.[0] ? {
        name: products[0].name,
        sku: products[0].sku,
        stock_quantity: products[0].stock_quantity,
        min_stock_level: products[0].min_stock_level,
      } : 'No products'
    });
  }, [products]);

  // Filter products based on search term (with null checks)
  const filteredProducts = (products || []).filter(product =>
    product && product.name && product.name.toLowerCase().includes((searchTerm || '').toLowerCase()) ||
    product && product.sku && product.sku.toLowerCase().includes((searchTerm || '').toLowerCase())
  );

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    let aValue, bValue;

    if (sortField === 'name') {
      aValue = a.name.toLowerCase();
      bValue = b.name.toLowerCase();
    } else if (sortField === 'sku') {
      aValue = a.sku?.toLowerCase() || '';
      bValue = b.sku?.toLowerCase() || '';
    } else if (sortField === 'stock_level') {
      aValue = a.stock_quantity;
      bValue = b.stock_quantity;
    } else if (sortField === 'min_level') {
      aValue = a.min_stock_level;
      bValue = b.min_stock_level;
    } else if (sortField === 'reorder_qty') {
      aValue = a.reorder_quantity || 0;
      bValue = b.reorder_quantity || 0;
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const exportCSV = () => {
    // Create CSV content
    const headers = ['Name', 'SKU', 'Current Stock', 'Min Stock Level', 'Reorder Quantity', 'Status'];
    const rows = sortedProducts.map(product => {
      const stockStatus = product.stock_quantity === 0 ? 'Out of Stock' : 'Low Stock';
      return [
        product.name,
        product.sku || 'N/A',
        product.stock_quantity,
        product.min_stock_level,
        product.reorder_quantity || 'Not set',
        stockStatus
      ];
    });

    // Convert to CSV
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'low_stock_report.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4">
        <div className="w-full md:w-auto">
          <TextInput
            id="search"
            type="text"
            icon={HiOutlineSearch}
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          <Dropdown label="Sort By" color="light" size="sm">
            <Dropdown.Item onClick={() => handleSort('name')}>Name</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('sku')}>SKU</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('stock_level')}>Current Stock</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('min_level')}>Min Stock Level</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('reorder_qty')}>Reorder Quantity</Dropdown.Item>
          </Dropdown>

          <Button color="light" size="sm" onClick={exportCSV}>
            <HiOutlineDownload className="mr-2 h-5 w-5" />
            Export
          </Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table striped>
          <Table.Head>
            <Table.HeadCell onClick={() => handleSort('name')} className="cursor-pointer">
              Product Name {sortField === 'name' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('sku')} className="cursor-pointer">
              SKU {sortField === 'sku' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('stock_level')} className="cursor-pointer">
              Current Stock {sortField === 'stock_level' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('min_level')} className="cursor-pointer">
              Min Stock Level {sortField === 'min_level' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('reorder_qty')} className="cursor-pointer">
              Reorder Quantity {sortField === 'reorder_qty' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell>Status</Table.HeadCell>
            <Table.HeadCell>Actions</Table.HeadCell>
          </Table.Head>
          <Table.Body className="divide-y">
            {sortedProducts.map((product) => (
              <Table.Row key={product.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                  {product.name}
                </Table.Cell>
                <Table.Cell>{product.sku || 'N/A'}</Table.Cell>
                <Table.Cell className="text-red-600 font-medium">{product.stock_quantity}</Table.Cell>
                <Table.Cell>{product.min_stock_level}</Table.Cell>
                <Table.Cell>{product.reorder_quantity || 'Not set'}</Table.Cell>
                <Table.Cell>
                  {product.stock_quantity === 0 ? (
                    <Badge color="failure" icon={HiOutlineExclamation}>
                      Out of Stock
                    </Badge>
                  ) : (
                    <Badge color="warning" icon={HiOutlineExclamation}>
                      Low Stock
                    </Badge>
                  )}
                </Table.Cell>
                <Table.Cell>
                  <div className="flex gap-2">
                    <Link to={`/products/details/${product.id}`}>
                      <Button size="xs">View</Button>
                    </Link>
                    <Link to="/purchases/requests/create">
                      <Button size="xs" color="warning">Order</Button>
                    </Link>
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>

      {sortedProducts.length === 0 && (
        <div className="text-center py-4">
          <p className="text-gray-500">No low stock items found.</p>
        </div>
      )}
    </div>
  );
};

export default LowStockReport;
