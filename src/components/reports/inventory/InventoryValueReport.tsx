import React, { useState } from 'react';
import { Table, Button, TextInput, Dropdown } from 'flowbite-react';
import { formatCurrency } from '../../../utils/formatters';
import { HiOutlineSearch, HiOutlineDownload } from 'react-icons/hi';

interface InventoryValueReportProps {
  products: any[];
}

const InventoryValueReport: React.FC<InventoryValueReportProps> = ({ products = [] }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string>('value');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Add safety logging
  React.useEffect(() => {
    console.log('InventoryValueReport received products:', {
      count: products?.length || 0,
      sampleProduct: products?.[0] ? {
        name: products[0].name,
        sku: products[0].sku,
        stock_quantity: products[0].stock_quantity,
        selling_price: products[0].selling_price
      } : 'No products'
    });
  }, [products]);

  // Filter products based on search term (with null check)
  const filteredProducts = (products || []).filter(product =>
    product && product.name && product.name.toLowerCase().includes((searchTerm || '').toLowerCase()) ||
    product && product.sku && product.sku.toLowerCase().includes((searchTerm || '').toLowerCase())
  );

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    let aValue, bValue;

    if (sortField === 'name') {
      aValue = a.name.toLowerCase();
      bValue = b.name.toLowerCase();
    } else if (sortField === 'sku') {
      aValue = a.sku?.toLowerCase() || '';
      bValue = b.sku?.toLowerCase() || '';
    } else if (sortField === 'quantity') {
      aValue = a.stock_quantity;
      bValue = b.stock_quantity;
    } else if (sortField === 'price') {
      aValue = a.selling_price;
      bValue = b.selling_price;
    } else { // value
      aValue = a.stock_quantity * a.selling_price;
      bValue = b.stock_quantity * b.selling_price;
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Calculate total inventory value
  const totalValue = sortedProducts.reduce((sum, product) =>
    sum + (product.stock_quantity * product.selling_price), 0
  );

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const exportCSV = () => {
    // Create CSV content
    const headers = ['Name', 'SKU', 'Quantity', 'Unit Price', 'Total Value'];
    const rows = sortedProducts.map(product => [
      product.name,
      product.sku || 'N/A',
      product.stock_quantity || 0,
      product.unit_price || 0,
      (product.stock_quantity || 0) * (product.unit_price || 0)
    ]);

    // Add total row
    rows.push(['', '', '', 'TOTAL', totalValue]);

    // Convert to CSV
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'inventory_value_report.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4">
        <div className="w-full md:w-auto">
          <TextInput
            id="search"
            type="text"
            icon={HiOutlineSearch}
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          <Dropdown label="Sort By" color="light" size="sm">
            <Dropdown.Item onClick={() => handleSort('name')}>Name</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('sku')}>SKU</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('quantity')}>Quantity</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('price')}>Unit Price</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('value')}>Total Value</Dropdown.Item>
          </Dropdown>

          <Button color="light" size="sm" onClick={exportCSV}>
            <HiOutlineDownload className="mr-2 h-5 w-5" />
            Export
          </Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table striped>
          <Table.Head>
            <Table.HeadCell onClick={() => handleSort('name')} className="cursor-pointer">
              Product Name {sortField === 'name' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('sku')} className="cursor-pointer">
              SKU {sortField === 'sku' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('quantity')} className="cursor-pointer">
              Quantity {sortField === 'quantity' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('price')} className="cursor-pointer">
              Unit Price {sortField === 'price' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('value')} className="cursor-pointer">
              Total Value {sortField === 'value' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
          </Table.Head>
          <Table.Body className="divide-y">
            {sortedProducts.map((product) => (
              <Table.Row key={product.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                  {product.name}
                </Table.Cell>
                <Table.Cell>{product.sku || 'N/A'}</Table.Cell>
                <Table.Cell>{product.stock_quantity || 0}</Table.Cell>
                <Table.Cell>{formatCurrency(product.unit_price || 0)}</Table.Cell>
                <Table.Cell className="font-medium">
                  {formatCurrency((product.stock_quantity || 0) * (product.unit_price || 0))}
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
          <Table.Footer>
            <Table.Row className="font-semibold text-gray-900 dark:text-white">
              <Table.Cell colSpan={4} className="text-right">
                Total Inventory Value:
              </Table.Cell>
              <Table.Cell>{formatCurrency(totalValue)}</Table.Cell>
            </Table.Row>
          </Table.Footer>
        </Table>
      </div>
    </div>
  );
};

export default InventoryValueReport;
