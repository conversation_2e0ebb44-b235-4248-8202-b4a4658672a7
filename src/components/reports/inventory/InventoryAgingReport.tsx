import React, { useState } from 'react';
import { Table, Button, TextInput, Dropdown, Badge } from 'flowbite-react';
import { formatCurrency } from '../../../utils/formatters';
import { HiOutlineSearch, HiOutlineDownload, HiOutlineClock } from 'react-icons/hi';

interface InventoryAgingReportProps {
  products: any[];
  transactions?: any[];
}

const InventoryAgingReport: React.FC<InventoryAgingReportProps> = ({
  products = [],
  transactions = []
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string>('days_in_inventory');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Add safety logging
  React.useEffect(() => {
    console.log('InventoryAgingReport received:', {
      productsCount: products?.length || 0,
      transactionsCount: transactions?.length || 0,
      sampleProduct: products?.[0] ? {
        name: products[0].name,
        sku: products[0].sku,
        stock_quantity: products[0].stock_quantity
      } : 'No products',
      sampleTransaction: transactions?.[0] ? {
        product_id: transactions[0].product_id,
        quantity: transactions[0].quantity,
        transaction_type: transactions[0].transaction_type,
        created_at: transactions[0].created_at
      } : 'No transactions'
    });
  }, [products, transactions]);

  // Calculate aging data based on real transactions if available
  const productsWithAging = (products || []).map(product => {
    // Find the most recent transaction for this product
    let lastMovementDate = null;
    let daysInInventory = 0;

    if (transactions && transactions.length > 0) {
      // Filter transactions for this product
      const productTransactions = transactions.filter(t => t.product_id === product.id);

      if (productTransactions.length > 0) {
        // Sort transactions by date (newest first)
        productTransactions.sort((a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );

        // Get the most recent transaction date
        lastMovementDate = new Date(productTransactions[0].created_at);

        // Calculate days in inventory
        const today = new Date();
        const timeDiff = today.getTime() - lastMovementDate.getTime();
        daysInInventory = Math.floor(timeDiff / (1000 * 3600 * 24));
      }
    }

    // If no transactions found, generate random data
    if (!lastMovementDate) {
      daysInInventory = Math.floor(Math.random() * 365);
      lastMovementDate = new Date(Date.now() - (daysInInventory * 24 * 60 * 60 * 1000));
    }

    // Determine aging category
    let agingCategory = '';
    if (daysInInventory <= 30) {
      agingCategory = '0-30 days';
    } else if (daysInInventory <= 60) {
      agingCategory = '31-60 days';
    } else if (daysInInventory <= 90) {
      agingCategory = '61-90 days';
    } else if (daysInInventory <= 180) {
      agingCategory = '91-180 days';
    } else {
      agingCategory = '180+ days';
    }

    return {
      ...product,
      days_in_inventory: daysInInventory,
      aging_category: agingCategory,
      last_movement_date: lastMovementDate.toISOString().split('T')[0],
      has_real_data: lastMovementDate !== null
    };
  });

  // Filter products based on search term
  const filteredProducts = productsWithAging.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.sku?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    let aValue, bValue;

    if (sortField === 'name') {
      aValue = a.name.toLowerCase();
      bValue = b.name.toLowerCase();
    } else if (sortField === 'sku') {
      aValue = a.sku?.toLowerCase() || '';
      bValue = b.sku?.toLowerCase() || '';
    } else if (sortField === 'stock_quantity') {
      aValue = a.stock_quantity;
      bValue = b.stock_quantity;
    } else if (sortField === 'days_in_inventory') {
      aValue = a.days_in_inventory;
      bValue = b.days_in_inventory;
    } else if (sortField === 'value') {
      aValue = a.stock_quantity * a.selling_price;
      bValue = b.stock_quantity * b.selling_price;
    } else { // last_movement
      aValue = new Date(a.last_movement_date).getTime();
      bValue = new Date(b.last_movement_date).getTime();
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Calculate aging statistics
  const agingCategories = {
    '0-30 days': { count: 0, value: 0 },
    '31-60 days': { count: 0, value: 0 },
    '61-90 days': { count: 0, value: 0 },
    '91-180 days': { count: 0, value: 0 },
    '180+ days': { count: 0, value: 0 }
  };

  sortedProducts.forEach(product => {
    const category = product.aging_category;
    const value = product.stock_quantity * product.selling_price;

    agingCategories[category].count++;
    agingCategories[category].value += value;
  });

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const exportCSV = () => {
    // Create CSV content
    const headers = ['Name', 'SKU', 'Quantity', 'Days in Inventory', 'Last Movement', 'Aging Category', 'Value'];
    const rows = sortedProducts.map(product => [
      product.name,
      product.sku || 'N/A',
      product.stock_quantity,
      product.days_in_inventory,
      product.last_movement_date,
      product.aging_category,
      product.stock_quantity * product.selling_price
    ]);

    // Convert to CSV
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'inventory_aging_report.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getBadgeColor = (category: string) => {
    switch (category) {
      case '0-30 days':
        return 'success';
      case '31-60 days':
        return 'info';
      case '61-90 days':
        return 'warning';
      case '91-180 days':
        return 'purple';
      case '180+ days':
        return 'failure';
      default:
        return 'gray';
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        {Object.entries(agingCategories).map(([category, data]) => (
          <div key={category} className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <h5 className="text-sm font-medium">{category}</h5>
              <Badge color={getBadgeColor(category)}>{data.count}</Badge>
            </div>
            <p className="text-lg font-bold">{formatCurrency(data.value)}</p>
          </div>
        ))}
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4">
        <div className="w-full md:w-auto">
          <TextInput
            id="search"
            type="text"
            icon={HiOutlineSearch}
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          <Dropdown label="Sort By" color="light" size="sm">
            <Dropdown.Item onClick={() => handleSort('name')}>Name</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('sku')}>SKU</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('stock_quantity')}>Quantity</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('days_in_inventory')}>Days in Inventory</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('last_movement')}>Last Movement</Dropdown.Item>
            <Dropdown.Item onClick={() => handleSort('value')}>Value</Dropdown.Item>
          </Dropdown>

          <Button color="light" size="sm" onClick={exportCSV}>
            <HiOutlineDownload className="mr-2 h-5 w-5" />
            Export
          </Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table striped>
          <Table.Head>
            <Table.HeadCell onClick={() => handleSort('name')} className="cursor-pointer">
              Product Name {sortField === 'name' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('sku')} className="cursor-pointer">
              SKU {sortField === 'sku' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('stock_quantity')} className="cursor-pointer">
              Quantity {sortField === 'stock_quantity' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('days_in_inventory')} className="cursor-pointer">
              Days in Inventory {sortField === 'days_in_inventory' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('last_movement')} className="cursor-pointer">
              Last Movement {sortField === 'last_movement' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
            <Table.HeadCell>Aging Category</Table.HeadCell>
            <Table.HeadCell onClick={() => handleSort('value')} className="cursor-pointer">
              Value {sortField === 'value' && (sortDirection === 'asc' ? '↑' : '↓')}
            </Table.HeadCell>
          </Table.Head>
          <Table.Body className="divide-y">
            {sortedProducts.map((product) => (
              <Table.Row key={product.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                  {product.name}
                </Table.Cell>
                <Table.Cell>{product.sku || 'N/A'}</Table.Cell>
                <Table.Cell>{product.stock_quantity}</Table.Cell>
                <Table.Cell>{product.days_in_inventory}</Table.Cell>
                <Table.Cell>{product.last_movement_date}</Table.Cell>
                <Table.Cell>
                  <Badge color={getBadgeColor(product.aging_category)} icon={HiOutlineClock}>
                    {product.aging_category}
                  </Badge>
                </Table.Cell>
                <Table.Cell className="font-medium">
                  {formatCurrency((product.stock_quantity || 0) * (product.unit_price || 0))}
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>

      <div className="mt-4 text-sm text-gray-500">
        <p>
          {transactions && transactions.length > 0
            ? 'Note: This report shows inventory aging based on the last transaction date for each product.'
            : 'Note: No transaction data available. Showing simulated aging data for demonstration purposes.'}
        </p>
      </div>
    </div>
  );
};

export default InventoryAgingReport;
