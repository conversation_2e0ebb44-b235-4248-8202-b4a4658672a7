import { useState, useEffect } from 'react';
import {
  Button,
  TextInput,
  Textarea,
  Label,
  Select,
  Spinner,
  Alert,
  ToggleSwitch,
  FileInput,
  Card,
  Tabs
} from 'flowbite-react';
import { HiOutlineExclamation, HiOutlineUser, HiOutlineOfficeBuilding, HiOutlineIdentification, HiOutlineInformationCircle } from 'react-icons/hi';
import { Employee, EmployeeWithDetails, EmployeeGovernmentId } from '../../services/employee';
import { Department, getDepartments } from '../../services/department';
import { JobPosition, getJobPositions } from '../../services/jobPosition';
import { EmploymentType, getEmploymentTypes } from '../../services/employmentType';
import { getOrganizationMembers, OrganizationMember } from '../../services/userManagement';
import { useOrganization } from '../../context/OrganizationContext';
import { supabase } from '../../lib/supabase';

interface EmployeeFormProps {
  initialData?: Partial<EmployeeWithDetails>;
  onSubmit: (employeeData: Partial<Employee>, governmentIds: Partial<EmployeeGovernmentId>) => Promise<void>;
  isSubmitting: boolean;
  error?: string;
}

const EmployeeForm: React.FC<EmployeeFormProps> = ({
  initialData,
  onSubmit,
  isSubmitting,
  error,
}) => {
  const { currentOrganization } = useOrganization();
  const [activeTab, setActiveTab] = useState<string>('personal');

  // Personal Information
  const [firstName, setFirstName] = useState<string>(initialData?.first_name || '');
  const [middleName, setMiddleName] = useState<string>(initialData?.middle_name || '');
  const [lastName, setLastName] = useState<string>(initialData?.last_name || '');
  const [email, setEmail] = useState<string>(initialData?.email || '');
  const [phone, setPhone] = useState<string>(initialData?.phone || '');
  const [dateOfBirth, setDateOfBirth] = useState<string>(
    initialData?.date_of_birth ? new Date(initialData.date_of_birth).toISOString().split('T')[0] : ''
  );
  const [gender, setGender] = useState<string>(initialData?.gender || '');
  const [maritalStatus, setMaritalStatus] = useState<string>(initialData?.marital_status || '');
  const [nationality, setNationality] = useState<string>(initialData?.nationality || '');

  // Address Information
  const [address, setAddress] = useState<string>(initialData?.address || '');
  const [city, setCity] = useState<string>(initialData?.city || '');
  const [state, setState] = useState<string>(initialData?.state || '');
  const [postalCode, setPostalCode] = useState<string>(initialData?.postal_code || '');
  const [country, setCountry] = useState<string>(initialData?.country || '');

  // Emergency Contact
  const [emergencyContactName, setEmergencyContactName] = useState<string>(initialData?.emergency_contact_name || '');
  const [emergencyContactPhone, setEmergencyContactPhone] = useState<string>(initialData?.emergency_contact_phone || '');
  const [emergencyContactRelationship, setEmergencyContactRelationship] = useState<string>(
    initialData?.emergency_contact_relationship || ''
  );

  // Employment Information
  const [employeeNumber, setEmployeeNumber] = useState<string>(initialData?.employee_number || '');
  const [departmentId, setDepartmentId] = useState<string>(initialData?.department_id || '');
  const [positionId, setPositionId] = useState<string>(initialData?.position_id || '');
  const [employmentTypeId, setEmploymentTypeId] = useState<string>(initialData?.employment_type_id || '');
  const [userId, setUserId] = useState<string>(initialData?.user_id || '');
  const [hireDate, setHireDate] = useState<string>(
    initialData?.hire_date ? new Date(initialData.hire_date).toISOString().split('T')[0] : ''
  );
  const [endDate, setEndDate] = useState<string>(
    initialData?.end_date ? new Date(initialData.end_date).toISOString().split('T')[0] : ''
  );
  const [status, setStatus] = useState<string>(initialData?.status || 'active');
  const [isActive, setIsActive] = useState<boolean>(initialData?.is_active !== false);
  const [notes, setNotes] = useState<string>(initialData?.notes || '');

  // Government IDs
  const [sssNumber, setSssNumber] = useState<string>('');
  const [philhealthNumber, setPhilhealthNumber] = useState<string>('');
  const [pagibigNumber, setPagibigNumber] = useState<string>('');
  const [tinNumber, setTinNumber] = useState<string>('');

  // Initialize government IDs from initialData
  useEffect(() => {
    if (Array.isArray(initialData?.government_ids) && initialData?.government_ids.length > 0) {
      const govIds = initialData.government_ids[0];
      if (govIds.sss_number) setSssNumber(govIds.sss_number);
      if (govIds.philhealth_number) setPhilhealthNumber(govIds.philhealth_number);
      if (govIds.pagibig_number) setPagibigNumber(govIds.pagibig_number);
      if (govIds.tin_number) setTinNumber(govIds.tin_number);
    }
  }, [initialData]);



  // Profile Image
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [profileImageUrl, setProfileImageUrl] = useState<string>(initialData?.profile_image_url || '');
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadError, setUploadError] = useState<string | null>(null);

  // Reference Data
  const [departments, setDepartments] = useState<Department[]>([]);
  const [positions, setPositions] = useState<JobPosition[]>([]);
  const [employmentTypes, setEmploymentTypes] = useState<EmploymentType[]>([]);
  const [organizationMembers, setOrganizationMembers] = useState<OrganizationMember[]>([]);
  const [loadingReferenceData, setLoadingReferenceData] = useState<boolean>(false);

  // Load reference data
  useEffect(() => {
    if (currentOrganization) {
      fetchReferenceData();
    }
  }, [currentOrganization]);

  const fetchReferenceData = async () => {
    if (!currentOrganization) return;

    setLoadingReferenceData(true);

    try {
      // Fetch departments
      const { departments: deptData } = await getDepartments(currentOrganization.id);
      setDepartments(deptData);

      // Fetch positions
      const { positions: posData } = await getJobPositions(currentOrganization.id);
      setPositions(posData);

      // Fetch employment types
      const { employmentTypes: etData } = await getEmploymentTypes(currentOrganization.id);
      setEmploymentTypes(etData);

      // Fetch organization members (users)
      const { members, error } = await getOrganizationMembers(currentOrganization.id);
      if (!error && members) {
        setOrganizationMembers(members);
      }
    } catch (error) {
      console.error('Error fetching reference data:', error);
    } finally {
      setLoadingReferenceData(false);
    }
  };

  const handleProfileImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setProfileImage(e.target.files[0]);
    }
  };

  const uploadProfileImage = async (): Promise<string | null> => {
    if (!profileImage || !currentOrganization) return null;

    setUploadProgress(0);
    setUploadError(null);

    try {
      const fileExt = profileImage.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `${currentOrganization.id}/employee-images/${fileName}`;

      const { error: uploadError, data } = await supabase.storage
        .from('employee-images')
        .upload(filePath, profileImage, {
          cacheControl: '3600',
          upsert: false,
        });

      if (uploadError) {
        setUploadError(uploadError.message);
        return null;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('employee-images')
        .getPublicUrl(filePath);

      return urlData.publicUrl;
    } catch (error: any) {
      setUploadError(error.message);
      return null;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    let imageUrl = profileImageUrl;

    // Only upload profile image if the employee is not linked to a user account
    if (profileImage && !userId) {
      const uploadedUrl = await uploadProfileImage();
      if (uploadedUrl) {
        imageUrl = uploadedUrl;
      }
    }

    const employeeData: Partial<Employee> = {
      first_name: firstName,
      middle_name: middleName || null,
      last_name: lastName,
      email: email || null,
      phone: phone || null,
      date_of_birth: dateOfBirth || null,
      gender: gender || null,
      marital_status: maritalStatus || null,
      nationality: nationality || null,
      address: address || null,
      city: city || null,
      state: state || null,
      postal_code: postalCode || null,
      country: country || null,
      emergency_contact_name: emergencyContactName || null,
      emergency_contact_phone: emergencyContactPhone || null,
      emergency_contact_relationship: emergencyContactRelationship || null,
      employee_number: employeeNumber || null,
      department_id: departmentId || null,
      position_id: positionId || null,
      employment_type_id: employmentTypeId || null,
      user_id: userId || null,
      hire_date: hireDate || null,
      end_date: endDate || null,
      status: status || null,
      is_active: isActive,
      profile_image_url: imageUrl || null,
      notes: notes || null,
    };

    // Clean up government IDs
    const governmentIds: Partial<EmployeeGovernmentId> = {};

    // Include all values, even empty strings
    // This ensures that if a field is cleared, it will be updated to null in the database
    governmentIds.sss_number = sssNumber || null;
    governmentIds.philhealth_number = philhealthNumber || null;
    governmentIds.pagibig_number = pagibigNumber || null;
    governmentIds.tin_number = tinNumber || null;

    await onSubmit(employeeData, governmentIds);
  };

  return (
    <form onSubmit={handleSubmit}>
      {error && (
        <Alert color="failure" className="mb-4">
          <HiOutlineExclamation className="h-4 w-4 mr-2" />
          {error}
        </Alert>
      )}

      <div className="space-y-6">
        <Card>
          <h3 className="text-lg font-medium mb-4 flex items-center">
            <HiOutlineUser className="mr-2 h-5 w-5 text-gray-600" />
            Personal Information
          </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="firstName" value="First Name *" />
                </div>
                <TextInput
                  id="firstName"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  required
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="middleName" value="Middle Name" />
                </div>
                <TextInput
                  id="middleName"
                  value={middleName}
                  onChange={(e) => setMiddleName(e.target.value)}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="lastName" value="Last Name *" />
                </div>
                <TextInput
                  id="lastName"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="email" value="Email" />
                </div>
                <TextInput
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="phone" value="Phone" />
                </div>
                <TextInput
                  id="phone"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="dateOfBirth" value="Date of Birth" />
                </div>
                <TextInput
                  id="dateOfBirth"
                  type="date"
                  value={dateOfBirth}
                  onChange={(e) => setDateOfBirth(e.target.value)}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="gender" value="Gender" />
                </div>
                <Select
                  id="gender"
                  value={gender}
                  onChange={(e) => setGender(e.target.value)}
                >
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                  <option value="prefer_not_to_say">Prefer not to say</option>
                </Select>
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="maritalStatus" value="Marital Status" />
                </div>
                <Select
                  id="maritalStatus"
                  value={maritalStatus}
                  onChange={(e) => setMaritalStatus(e.target.value)}
                >
                  <option value="">Select Marital Status</option>
                  <option value="single">Single</option>
                  <option value="married">Married</option>
                  <option value="divorced">Divorced</option>
                  <option value="widowed">Widowed</option>
                  <option value="separated">Separated</option>
                </Select>
              </div>
            </div>

            <div className="mb-4">
              <div className="mb-2 block">
                <Label htmlFor="nationality" value="Nationality" />
              </div>
              <TextInput
                id="nationality"
                value={nationality}
                onChange={(e) => setNationality(e.target.value)}
              />
            </div>

            <h4 className="text-md font-medium mb-2">Address</h4>

            <div className="mb-4">
              <div className="mb-2 block">
                <Label htmlFor="address" value="Street Address" />
              </div>
              <TextInput
                id="address"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="city" value="City" />
                </div>
                <TextInput
                  id="city"
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="state" value="State/Province" />
                </div>
                <TextInput
                  id="state"
                  value={state}
                  onChange={(e) => setState(e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="postalCode" value="Postal Code" />
                </div>
                <TextInput
                  id="postalCode"
                  value={postalCode}
                  onChange={(e) => setPostalCode(e.target.value)}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="country" value="Country" />
                </div>
                <TextInput
                  id="country"
                  value={country}
                  onChange={(e) => setCountry(e.target.value)}
                />
              </div>
            </div>

            <h4 className="text-md font-medium mb-2">Emergency Contact</h4>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="emergencyContactName" value="Contact Name" />
                </div>
                <TextInput
                  id="emergencyContactName"
                  value={emergencyContactName}
                  onChange={(e) => setEmergencyContactName(e.target.value)}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="emergencyContactPhone" value="Contact Phone" />
                </div>
                <TextInput
                  id="emergencyContactPhone"
                  value={emergencyContactPhone}
                  onChange={(e) => setEmergencyContactPhone(e.target.value)}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="emergencyContactRelationship" value="Relationship" />
                </div>
                <TextInput
                  id="emergencyContactRelationship"
                  value={emergencyContactRelationship}
                  onChange={(e) => setEmergencyContactRelationship(e.target.value)}
                />
              </div>
            </div>
          </Card>

        <Card>
          <h3 className="text-lg font-medium mb-4 flex items-center">
            <HiOutlineOfficeBuilding className="mr-2 h-5 w-5 text-gray-600" />
            Employment Details
          </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="employeeNumber" value="Employee Number" />
                </div>
                <TextInput
                  id="employeeNumber"
                  value={employeeNumber}
                  onChange={(e) => setEmployeeNumber(e.target.value)}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="status" value="Employment Status" />
                </div>
                <Select
                  id="status"
                  value={status}
                  onChange={(e) => setStatus(e.target.value)}
                >
                  <option value="active">Active</option>
                  <option value="on_leave">On Leave</option>
                  <option value="terminated">Terminated</option>
                  <option value="resigned">Resigned</option>
                  <option value="retired">Retired</option>
                </Select>
              </div>
            </div>

            <div>
              <div className="mb-2 block">
                <Label
                  htmlFor="userId"
                  value="Link to User Account"
                  className="flex items-center"
                >
                  <span>Link to User Account</span>
                  <HiOutlineInformationCircle
                    className="ml-1 text-gray-500"
                    title="Link this employee to a user account if they need access to the system"
                  />
                </Label>
              </div>
              <Select
                id="userId"
                name="userId"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
              >
                <option value="">Not linked to any user</option>
                {organizationMembers.map(member => (
                  <option key={member.user_id} value={member.user_id}>
                    {member.profile?.email || 'No email'} ({member.profile?.first_name} {member.profile?.last_name})
                  </option>
                ))}
              </Select>
              <p className="mt-1 text-sm text-gray-500">
                Link this employee to a user account if they need access to the system
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="departmentId" value="Department" />
                </div>
                <Select
                  id="departmentId"
                  value={departmentId}
                  onChange={(e) => setDepartmentId(e.target.value)}
                >
                  <option value="">Select Department</option>
                  {departments.map((dept) => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name}
                    </option>
                  ))}
                </Select>
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="positionId" value="Position" />
                </div>
                <Select
                  id="positionId"
                  value={positionId}
                  onChange={(e) => setPositionId(e.target.value)}
                >
                  <option value="">Select Position</option>
                  {positions.map((pos) => (
                    <option key={pos.id} value={pos.id}>
                      {pos.title}
                    </option>
                  ))}
                </Select>
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="employmentTypeId" value="Employment Type" />
                </div>
                <Select
                  id="employmentTypeId"
                  value={employmentTypeId}
                  onChange={(e) => setEmploymentTypeId(e.target.value)}
                >
                  <option value="">Select Employment Type</option>
                  {employmentTypes.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="hireDate" value="Hire Date" />
                </div>
                <TextInput
                  id="hireDate"
                  type="date"
                  value={hireDate}
                  onChange={(e) => setHireDate(e.target.value)}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="endDate" value="End Date" />
                </div>
                <TextInput
                  id="endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </div>

            <div className="mb-4">
              <div className="flex items-center gap-2">
                <ToggleSwitch
                  checked={isActive}
                  onChange={setIsActive}
                  label="Active Employee"
                />
              </div>
            </div>

            <div className="mb-4">
              <div className="mb-2 block">
                <Label htmlFor="notes" value="Notes" />
              </div>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={4}
              />
            </div>

            <div className="mb-4">
              <div className="mb-2 block">
                <Label htmlFor="profileImage" value="Profile Image" />
              </div>

              {userId ? (
                <div>
                  <p className="text-sm text-gray-500 mb-2">
                    This employee is linked to a user account. The profile image will be automatically used from the user account.
                  </p>
                  {profileImageUrl && (
                    <div className="mt-2">
                      <div className="w-20 h-20 overflow-hidden rounded-full border border-gray-200">
                        <img
                          src={profileImageUrl}
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <>
                  <FileInput
                    id="profileImage"
                    onChange={handleProfileImageChange}
                    helperText="Upload a profile image (optional)"
                  />
                  {profileImageUrl && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-500 mb-1">Current Image:</p>
                      <div className="w-20 h-20 overflow-hidden rounded-full border border-gray-200">
                        <img
                          src={profileImageUrl}
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  )}
                  {uploadError && (
                    <p className="text-sm text-red-500 mt-1">{uploadError}</p>
                  )}
                </>
              )}
            </div>
          </Card>

        <Card>
          <h3 className="text-lg font-medium mb-4 flex items-center">
            <HiOutlineIdentification className="mr-2 h-5 w-5 text-gray-600" />
            Government IDs
          </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="sssNumber" value="SSS Number" />
                </div>
                <TextInput
                  id="sssNumber"
                  value={sssNumber}
                  onChange={(e) => setSssNumber(e.target.value)}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="philhealthNumber" value="PhilHealth Number" />
                </div>
                <TextInput
                  id="philhealthNumber"
                  value={philhealthNumber}
                  onChange={(e) => setPhilhealthNumber(e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="mb-2 block">
                  <Label htmlFor="pagibigNumber" value="Pag-IBIG Number" />
                </div>
                <TextInput
                  id="pagibigNumber"
                  value={pagibigNumber}
                  onChange={(e) => setPagibigNumber(e.target.value)}
                />
              </div>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="tinNumber" value="TIN Number" />
                </div>
                <TextInput
                  id="tinNumber"
                  value={tinNumber}
                  onChange={(e) => setTinNumber(e.target.value)}
                />
              </div>
            </div>
          </Card>
        </div>

      <div className="mt-6 flex justify-end">
        <Button type="submit" color="primary" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Employee'
          )}
        </Button>
      </div>
    </form>
  );
};

export default EmployeeForm;
