import { useState, useEffect } from 'react';
import {
  Button,
  TextInput,
  Textarea,
  Label,
  Select,
  Spinner,
  Alert,
  ToggleSwitch,
  FileInput
} from 'flowbite-react';
import { HiOutlineExclamation, HiOutlineInformationCircle } from 'react-icons/hi';
import { Employee, EmployeeWithDetails, EmployeeGovernmentId } from '../../services/employee';
import { Department, getDepartments } from '../../services/department';
import { JobPosition, getJobPositions } from '../../services/jobPosition';
import { EmploymentType, getEmploymentTypes } from '../../services/employmentType';
import { getOrganizationMembers, OrganizationMember } from '../../services/userManagement';
import { useOrganization } from '../../context/OrganizationContext';
import { supabase } from '../../lib/supabase';

interface SimpleEmployeeFormProps {
  initialData?: Partial<EmployeeWithDetails>;
  onSubmit: (employeeData: Partial<Employee>, governmentIds: Partial<EmployeeGovernmentId>) => Promise<void>;
  isSubmitting: boolean;
  error?: string;
  onCancel?: () => void;
}

const SimpleEmployeeForm: React.FC<SimpleEmployeeFormProps> = ({
  initialData,
  onSubmit,
  isSubmitting,
  error,
  onCancel
}) => {
  const { currentOrganization } = useOrganization();

  // Form state
  const [formData, setFormData] = useState<Partial<Employee>>(() => {
    // Create a clean version of initialData with only the fields we need
    // This ensures we're not including nested objects like department, position, etc.
    const cleanData: Partial<Employee> = {
      first_name: initialData?.first_name || '',
      middle_name: initialData?.middle_name || '',
      last_name: initialData?.last_name || '',
      email: initialData?.email || '',
      phone: initialData?.phone || '',
      employee_number: initialData?.employee_number || '',
      department_id: initialData?.department_id || '',
      position_id: initialData?.position_id || '',
      employment_type_id: initialData?.employment_type_id || '',
      status: initialData?.status || 'active',
      is_active: initialData?.is_active !== false,
      user_id: initialData?.user_id || null,
      date_of_birth: initialData?.date_of_birth || null,
      gender: initialData?.gender || null,
      marital_status: initialData?.marital_status || null,
      nationality: initialData?.nationality || null,
      address: initialData?.address || null,
      city: initialData?.city || null,
      state: initialData?.state || null,
      postal_code: initialData?.postal_code || null,
      country: initialData?.country || null,
      emergency_contact_name: initialData?.emergency_contact_name || null,
      emergency_contact_phone: initialData?.emergency_contact_phone || null,
      emergency_contact_relationship: initialData?.emergency_contact_relationship || null,
      hire_date: initialData?.hire_date || null,
      end_date: initialData?.end_date || null,
      notes: initialData?.notes || null,
      profile_image_url: initialData?.profile_image_url || null,
    };

    return cleanData;
  });

  // Government IDs state
  const [governmentIds, setGovernmentIds] = useState<Partial<EmployeeGovernmentId>>(() => {
    // Create a clean version of government IDs
    const cleanGovIds: Partial<EmployeeGovernmentId> = {
      sss_number: '',
      philhealth_number: '',
      pagibig_number: '',
      tin_number: ''
    };

    // Check if government_ids is an array and has items
    if (Array.isArray(initialData?.government_ids) && initialData?.government_ids.length > 0) {
      const govIds = initialData.government_ids[0];
      if (govIds.sss_number) cleanGovIds.sss_number = govIds.sss_number;
      if (govIds.philhealth_number) cleanGovIds.philhealth_number = govIds.philhealth_number;
      if (govIds.pagibig_number) cleanGovIds.pagibig_number = govIds.pagibig_number;
      if (govIds.tin_number) cleanGovIds.tin_number = govIds.tin_number;
    }

    return cleanGovIds;
  });



  // Reference Data
  const [departments, setDepartments] = useState<Department[]>([]);
  const [positions, setPositions] = useState<JobPosition[]>([]);
  const [employmentTypes, setEmploymentTypes] = useState<EmploymentType[]>([]);
  const [organizationMembers, setOrganizationMembers] = useState<OrganizationMember[]>([]);
  const [loadingReferenceData, setLoadingReferenceData] = useState<boolean>(false);

  // Load reference data
  useEffect(() => {
    if (currentOrganization) {
      fetchReferenceData();
    }
  }, [currentOrganization]);

  const fetchReferenceData = async () => {
    if (!currentOrganization) return;

    setLoadingReferenceData(true);

    try {
      // Fetch departments
      const { departments: deptData } = await getDepartments(currentOrganization.id);
      setDepartments(deptData);

      // Fetch positions
      const { positions: posData } = await getJobPositions(currentOrganization.id);
      setPositions(posData);

      // Fetch employment types
      const { employmentTypes: etData } = await getEmploymentTypes(currentOrganization.id);
      setEmploymentTypes(etData);

      // Fetch organization members (users)
      const { members, error } = await getOrganizationMembers(currentOrganization.id);
      if (!error && members) {
        setOrganizationMembers(members);
      }
    } catch (error) {
      console.error('Error fetching reference data:', error);
    } finally {
      setLoadingReferenceData(false);
    }
  };

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // For UUID fields, convert empty string to null
    if (['department_id', 'position_id', 'employment_type_id'].includes(name)) {
      setFormData(prev => ({
        ...prev,
        [name]: value === '' ? null : value
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle government ID changes
  const handleGovIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setGovernmentIds(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle toggle switch
  const handleToggleChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      is_active: checked
    }));
  };

  // Handle profile image change
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const handleProfileImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setProfileImage(e.target.files[0]);
    }
  };

  // Upload profile image
  const uploadProfileImage = async (): Promise<string | null> => {
    if (!profileImage || !currentOrganization) return null;

    try {
      setUploadError(null);

      // Generate a unique file name
      const fileExt = profileImage.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `${currentOrganization.id}/employee-images/${fileName}`;

      // Upload the file
      const { error: uploadError } = await supabase.storage
        .from('employee-images')
        .upload(filePath, profileImage);

      if (uploadError) {
        setUploadError(uploadError.message);
        return null;
      }

      // Get the public URL
      const { data: urlData } = await supabase.storage
        .from('employee-images')
        .getPublicUrl(filePath);

      return urlData.publicUrl;
    } catch (error: any) {
      setUploadError(error.message);
      return null;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Ensure UUID fields are not empty strings
    const cleanedFormData = { ...formData };

    // Convert empty strings to null for UUID fields
    ['department_id', 'position_id', 'employment_type_id', 'user_id'].forEach(field => {
      if (cleanedFormData[field as keyof typeof cleanedFormData] === '') {
        cleanedFormData[field as keyof typeof cleanedFormData] = null;
      }
    });

    // Only upload profile image if the employee is not linked to a user account
    if (profileImage && !cleanedFormData.user_id) {
      const uploadedUrl = await uploadProfileImage();
      if (uploadedUrl) {
        cleanedFormData.profile_image_url = uploadedUrl;
      }
    }

    // Clean up government IDs
    const cleanedGovIds: Partial<EmployeeGovernmentId> = {};

    // Include all values, even empty strings
    // This ensures that if a field is cleared, it will be updated to null in the database
    cleanedGovIds.sss_number = governmentIds.sss_number || null;
    cleanedGovIds.philhealth_number = governmentIds.philhealth_number || null;
    cleanedGovIds.pagibig_number = governmentIds.pagibig_number || null;
    cleanedGovIds.tin_number = governmentIds.tin_number || null;

    await onSubmit(cleanedFormData, cleanedGovIds);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert color="failure" icon={HiOutlineExclamation}>
          {error}
        </Alert>
      )}

      <div className="space-y-6">
        <h3 className="text-lg font-medium">Basic Information</h3>

        <div className="mb-4">
          <div className="mb-2 block">
            <Label htmlFor="profileImage" value="Profile Image" />
          </div>

          {formData.user_id ? (
            <div>
              <p className="text-sm text-gray-500 mb-2">
                This employee is linked to a user account. The profile image will be automatically used from the user account.
              </p>
              {formData.profile_image_url && (
                <div className="mt-2">
                  <div className="w-20 h-20 overflow-hidden rounded-full border border-gray-200">
                    <img
                      src={formData.profile_image_url}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              )}
            </div>
          ) : (
            <>
              <FileInput
                id="profileImage"
                onChange={handleProfileImageChange}
                helperText="Upload a profile image (optional)"
              />
              {formData.profile_image_url && (
                <div className="mt-2">
                  <p className="text-sm text-gray-500 mb-1">Current Image:</p>
                  <div className="w-20 h-20 overflow-hidden rounded-full border border-gray-200">
                    <img
                      src={formData.profile_image_url}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              )}
              {uploadError && (
                <p className="text-sm text-red-500 mt-1">{uploadError}</p>
              )}
            </>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="mb-2 block">
              <Label htmlFor="first_name" value="First Name *" />
            </div>
            <TextInput
              id="first_name"
              name="first_name"
              value={formData.first_name || ''}
              onChange={handleChange}
              required
            />
          </div>

          <div>
            <div className="mb-2 block">
              <Label htmlFor="middle_name" value="Middle Name" />
            </div>
            <TextInput
              id="middle_name"
              name="middle_name"
              value={formData.middle_name || ''}
              onChange={handleChange}
            />
          </div>

          <div>
            <div className="mb-2 block">
              <Label htmlFor="last_name" value="Last Name *" />
            </div>
            <TextInput
              id="last_name"
              name="last_name"
              value={formData.last_name || ''}
              onChange={handleChange}
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="mb-2 block">
              <Label htmlFor="email" value="Email" />
            </div>
            <TextInput
              id="email"
              name="email"
              type="email"
              value={formData.email || ''}
              onChange={handleChange}
            />
          </div>

          <div>
            <div className="mb-2 block">
              <Label htmlFor="phone" value="Phone" />
            </div>
            <TextInput
              id="phone"
              name="phone"
              value={formData.phone || ''}
              onChange={handleChange}
            />
          </div>
        </div>
      </div>

      <div className="space-y-6">
        <h3 className="text-lg font-medium">Employment Details</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="mb-2 block">
              <Label htmlFor="employee_number" value="Employee Number" />
            </div>
            <TextInput
              id="employee_number"
              name="employee_number"
              value={formData.employee_number || ''}
              onChange={handleChange}
            />
          </div>

          <div>
            <div className="mb-2 block">
              <Label htmlFor="status" value="Status" />
            </div>
            <Select
              id="status"
              name="status"
              value={formData.status || 'active'}
              onChange={handleChange}
            >
              <option value="active">Active</option>
              <option value="on_leave">On Leave</option>
              <option value="terminated">Terminated</option>
              <option value="resigned">Resigned</option>
              <option value="retired">Retired</option>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="mb-2 block">
              <Label htmlFor="department_id" value="Department" />
            </div>
            <Select
              id="department_id"
              name="department_id"
              value={formData.department_id || ''}
              onChange={handleChange}
            >
              <option value="">Select Department</option>
              {departments.map(dept => (
                <option key={dept.id} value={dept.id}>{dept.name}</option>
              ))}
            </Select>
          </div>

          <div>
            <div className="mb-2 block">
              <Label htmlFor="position_id" value="Position" />
            </div>
            <Select
              id="position_id"
              name="position_id"
              value={formData.position_id || ''}
              onChange={handleChange}
            >
              <option value="">Select Position</option>
              {positions.map(pos => (
                <option key={pos.id} value={pos.id}>{pos.title}</option>
              ))}
            </Select>
          </div>

          <div>
            <div className="mb-2 block">
              <Label htmlFor="employment_type_id" value="Employment Type" />
            </div>
            <Select
              id="employment_type_id"
              name="employment_type_id"
              value={formData.employment_type_id || ''}
              onChange={handleChange}
            >
              <option value="">Select Employment Type</option>
              {employmentTypes.map(type => (
                <option key={type.id} value={type.id}>{type.name}</option>
              ))}
            </Select>
          </div>
        </div>

        <div>
          <div className="mb-2 block">
            <Label
              htmlFor="user_id"
              value="Link to User Account"
              className="flex items-center"
            >
              <span>Link to User Account</span>
              <HiOutlineInformationCircle
                className="ml-1 text-gray-500"
                title="Link this employee to a user account if they need access to the system"
              />
            </Label>
          </div>
          <Select
            id="user_id"
            name="user_id"
            value={formData.user_id || ''}
            onChange={handleChange}
          >
            <option value="">Not linked to any user</option>
            {organizationMembers.map(member => (
              <option key={member.id} value={member.id}>
                {member.email} ({member.first_name} {member.last_name})
              </option>
            ))}
          </Select>
          <p className="mt-1 text-sm text-gray-500">
            Link this employee to a user account if they need access to the system
          </p>
        </div>

        <div className="flex items-center gap-2">
          <ToggleSwitch
            checked={formData.is_active !== false}
            onChange={handleToggleChange}
            label="Active Employee"
          />
        </div>
      </div>

      <div className="space-y-6">
        <h3 className="text-lg font-medium">Government IDs</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="mb-2 block">
              <Label htmlFor="sss_number" value="SSS Number" />
            </div>
            <TextInput
              id="sss_number"
              name="sss_number"
              value={governmentIds.sss_number || ''}
              onChange={handleGovIdChange}
            />
          </div>

          <div>
            <div className="mb-2 block">
              <Label htmlFor="philhealth_number" value="PhilHealth Number" />
            </div>
            <TextInput
              id="philhealth_number"
              name="philhealth_number"
              value={governmentIds.philhealth_number || ''}
              onChange={handleGovIdChange}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="mb-2 block">
              <Label htmlFor="pagibig_number" value="Pag-IBIG Number" />
            </div>
            <TextInput
              id="pagibig_number"
              name="pagibig_number"
              value={governmentIds.pagibig_number || ''}
              onChange={handleGovIdChange}
            />
          </div>

          <div>
            <div className="mb-2 block">
              <Label htmlFor="tin_number" value="TIN Number" />
            </div>
            <TextInput
              id="tin_number"
              name="tin_number"
              value={governmentIds.tin_number || ''}
              onChange={handleGovIdChange}
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button color="gray" onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit" color="primary" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            'Save Employee'
          )}
        </Button>
      </div>
    </form>
  );
};

export default SimpleEmployeeForm;
