import React, { useState, useEffect, useRef } from 'react';
import { TextInput, Spinner } from 'flowbite-react';
import { HiOutlineSearch, HiOutlineUser } from 'react-icons/hi';
import { EmployeeWithDetails } from '../../services/employee';

interface EmployeeSearchSelectProps {
  employees: EmployeeWithDetails[];
  selectedEmployeeId: string;
  onSelect: (employeeId: string) => void;
  required?: boolean;
  isLoading?: boolean;
  placeholder?: string;
}

const EmployeeSearchSelect: React.FC<EmployeeSearchSelectProps> = ({
  employees,
  selectedEmployeeId,
  onSelect,
  required = false,
  isLoading = false,
  placeholder = 'Search for an employee...'
}) => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
  const [filteredEmployees, setFilteredEmployees] = useState<EmployeeWithDetails[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Find the selected employee
  const selectedEmployee = employees && employees.length > 0 ?
    employees.find(emp => emp.id === selectedEmployeeId) : undefined;

  // Filter employees based on search query
  useEffect(() => {
    if (!employees || employees.length === 0) {
      setFilteredEmployees([]);
      return;
    }

    if (searchQuery.trim() === '') {
      setFilteredEmployees(employees);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = employees.filter(
        emp => {
          // Safely access properties with null checks
          const firstName = emp.first_name ? emp.first_name.toLowerCase() : '';
          const lastName = emp.last_name ? emp.last_name.toLowerCase() : '';
          const employeeNumber = emp.employee_number ? emp.employee_number.toLowerCase() : '';

          return firstName.includes(query) ||
                 lastName.includes(query) ||
                 employeeNumber.includes(query);
        }
      );
      setFilteredEmployees(filtered);
    }
  }, [searchQuery, employees]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle employee selection
  const handleSelectEmployee = (employeeId: string) => {
    onSelect(employeeId);
    setIsDropdownOpen(false);
    setSearchQuery('');
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <TextInput
        type="text"
        value={selectedEmployee
          ? `${selectedEmployee.first_name || ''} ${selectedEmployee.last_name || ''}${selectedEmployee.employee_number ? ` (${selectedEmployee.employee_number})` : ''}`
          : searchQuery}
        onChange={(e) => {
          setSearchQuery(e.target.value);
          if (selectedEmployeeId) {
            onSelect(''); // Clear selection when user starts typing
          }
          setIsDropdownOpen(true);
        }}
        onFocus={() => {
          setIsDropdownOpen(true);
        }}
        placeholder={placeholder}
        required={required}
        icon={isLoading ? Spinner : HiOutlineSearch}
        rightIcon={HiOutlineUser}
        onClick={() => {
          setIsDropdownOpen(true);
        }}
      />

      {isDropdownOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {filteredEmployees.length === 0 ? (
            <div className="p-3 text-sm text-gray-500">No employees found</div>
          ) : (
            <ul>
              {filteredEmployees.map((employee) => (
                <li
                  key={employee.id}
                  className={`p-2 hover:bg-gray-100 cursor-pointer ${
                    employee.id === selectedEmployeeId ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => handleSelectEmployee(employee.id)}
                >
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                      {employee.profile_image_url ? (
                        <img
                          src={employee.profile_image_url}
                          alt={`${employee.first_name} ${employee.last_name}`}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                      ) : (
                        <HiOutlineUser className="h-4 w-4 text-gray-500" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium">
                        {employee.first_name} {employee.last_name}
                      </div>
                      {employee.employee_number && (
                        <div className="text-xs text-gray-500">
                          ID: {employee.employee_number}
                        </div>
                      )}
                      {employee.position && (
                        <div className="text-xs text-gray-500">
                          {employee.position.title}
                        </div>
                      )}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export default EmployeeSearchSelect;
