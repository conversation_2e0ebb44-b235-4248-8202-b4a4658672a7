import React, { useState, useEffect } from 'react';
import { Card, Badge, Button, Modal } from 'flowbite-react';
import { 
  HiOutlineEye, 
  HiOutlinePaperClip,
  HiOutlineCalendar,
  HiOutlineCash,
  HiOutlineDocumentText,
  HiOutlineX
} from 'react-icons/hi';
import { PayablePaymentWithDetails, PaymentMethod } from '../../types/payables.types';
import { getAttachments } from '../../services/attachments';
import { useOrganization } from '../../context/OrganizationContext';
import { PayableAttachment } from '../../types/attachments.types';
import AttachmentGallery from '../attachments/AttachmentGallery';

interface PaymentHistoryCardProps {
  payment: PayablePaymentWithDetails;
  formatWithCurrency: (amount: number) => string;
  formatDate: (date: string) => string;
  formatDateTime: (date: string) => string;
  getPaymentMethodLabel: (method: PaymentMethod) => string;
  isLast: boolean;
}

const PaymentHistoryCard: React.FC<PaymentHistoryCardProps> = ({
  payment,
  formatWithCurrency,
  formatDate,
  formatDateTime,
  getPaymentMethodLabel,
  isLast
}) => {
  const { currentOrganization } = useOrganization();
  const [attachments, setAttachments] = useState<PayableAttachment[]>([]);
  const [attachmentCount, setAttachmentCount] = useState(0);
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [loadingAttachments, setLoadingAttachments] = useState(true);

  // Load attachment count
  useEffect(() => {
    const loadAttachmentCount = async () => {
      if (!currentOrganization) return;

      try {
        const result = await getAttachments(
          currentOrganization.id,
          'payment',
          payment.id
        );

        if (result.success && result.attachments) {
          setAttachments(result.attachments);
          setAttachmentCount(result.attachments.length);
        }
      } catch (error) {
        console.error('Error loading attachments:', error);
      } finally {
        setLoadingAttachments(false);
      }
    };

    loadAttachmentCount();
  }, [currentOrganization, payment.id]);

  // Get payment method icon
  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.CASH:
        return <HiOutlineCash className="w-4 h-4" />;
      case PaymentMethod.CHECK:
      case PaymentMethod.BANK_TRANSFER:
      case PaymentMethod.CREDIT_CARD:
        return <HiOutlineDocumentText className="w-4 h-4" />;
      default:
        return <HiOutlineCash className="w-4 h-4" />;
    }
  };

  // Get payment method color
  const getPaymentMethodColor = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.CASH:
        return 'green';
      case PaymentMethod.CHECK:
        return 'blue';
      case PaymentMethod.BANK_TRANSFER:
        return 'purple';
      case PaymentMethod.CREDIT_CARD:
        return 'orange';
      case PaymentMethod.GCASH:
      case PaymentMethod.PAYMAYA:
        return 'cyan';
      default:
        return 'gray';
    }
  };

  return (
    <>
      <div className={`relative ${!isLast ? 'pb-4' : ''}`}>
        {/* Timeline connector */}
        {!isLast && (
          <div className="absolute left-6 top-12 w-0.5 h-full bg-gray-200"></div>
        )}
        
        <div className="flex items-start space-x-4">
          {/* Timeline dot */}
          <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center border-4 border-white shadow-sm">
            <HiOutlineCash className="w-5 h-5 text-green-600" />
          </div>

          {/* Payment card */}
          <div className="flex-1 min-w-0">
            <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <h6 className="text-lg font-semibold text-gray-900">
                    {formatWithCurrency(payment.amount_paid)}
                  </h6>
                  <Badge 
                    color={getPaymentMethodColor(payment.payment_method as PaymentMethod)}
                    className="flex items-center gap-1"
                  >
                    {getPaymentMethodIcon(payment.payment_method as PaymentMethod)}
                    {getPaymentMethodLabel(payment.payment_method as PaymentMethod)}
                  </Badge>
                </div>
                
                <div className="flex items-center space-x-2">
                  {/* Attachment indicator */}
                  {attachmentCount > 0 && (
                    <Button
                      size="xs"
                      color="light"
                      onClick={() => setShowAttachmentsModal(true)}
                      className="flex items-center gap-1"
                    >
                      <HiOutlinePaperClip className="w-3 h-3" />
                      {attachmentCount}
                    </Button>
                  )}
                  
                  <div className="flex items-center text-sm text-gray-500">
                    <HiOutlineCalendar className="w-4 h-4 mr-1" />
                    {formatDate(payment.payment_date)}
                  </div>
                </div>
              </div>

              {/* Payment details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                {payment.reference_number && (
                  <div>
                    <span className="text-gray-500">Reference:</span>
                    <p className="font-medium text-gray-900">{payment.reference_number}</p>
                  </div>
                )}
                
                {payment.remarks && (
                  <div className="md:col-span-2">
                    <span className="text-gray-500">Remarks:</span>
                    <p className="text-gray-900">{payment.remarks}</p>
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="mt-3 pt-3 border-t border-gray-100 flex items-center justify-between text-xs text-gray-500">
                <span>Created {formatDateTime(payment.created_at)}</span>
                {attachmentCount > 0 && (
                  <Button
                    size="xs"
                    color="light"
                    onClick={() => setShowAttachmentsModal(true)}
                  >
                    <HiOutlineEye className="w-3 h-3 mr-1" />
                    View Attachments
                  </Button>
                )}
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* Attachments Modal */}
      <Modal
        show={showAttachmentsModal}
        onClose={() => setShowAttachmentsModal(false)}
        size="4xl"
      >
        <Modal.Header>
          <div className="flex items-center gap-2">
            <HiOutlinePaperClip className="w-5 h-5" />
            Payment Attachments
            <Badge color="gray">{attachmentCount}</Badge>
          </div>
        </Modal.Header>
        <Modal.Body>
          <AttachmentGallery
            attachableType="payment"
            attachableId={payment.id}
            editable={true}
            showUpload={true}
            maxFiles={10}
          />
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="light"
            onClick={() => setShowAttachmentsModal(false)}
          >
            <HiOutlineX className="mr-2 h-4 w-4" />
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default PaymentHistoryCard;
