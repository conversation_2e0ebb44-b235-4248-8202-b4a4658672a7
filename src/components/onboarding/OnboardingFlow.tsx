import React, { useState, useEffect } from 'react';
import { Card, Button, Progress } from 'flowbite-react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../../lib/supabase';
import OrganizationStep from './steps/OrganizationStep';
import BusinessTypeStep from './steps/BusinessTypeStep';
import DiscoveryStep from './steps/DiscoveryStep';
import PersonalDetailsStep from './steps/PersonalDetailsStep';
import CompletionStep from './steps/CompletionStep';

export interface OnboardingData {
  organizationName: string;
  businessType: string;
  posType: string;
  discoverySource: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  address?: string;
}

const OnboardingFlow: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    organizationName: '',
    businessType: '',
    posType: '',
    discoverySource: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    address: '',
  });

  const totalSteps = 5;
  const progressPercentage = (currentStep / totalSteps) * 100;

  useEffect(() => {
    // Check if user is already onboarded
    const checkOnboardingStatus = async () => {
      if (!user) return;

      try {
        // Check database first for onboarding completion
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('onboarding_completed')
          .eq('id', user.id)
          .maybeSingle();

        if (!profileError && profile && profile.onboarding_completed) {
          console.log('User already completed onboarding, redirecting to dashboard');
          navigate('/');
          return;
        }

        // Fallback to localStorage check
        const onboardingCompleted = localStorage.getItem(`onboarding_completed_${user.id}`);
        if (onboardingCompleted) {
          console.log('User completed onboarding in localStorage, redirecting to dashboard');
          navigate('/');
          return;
        }

        // Pre-fill data from user metadata if available
        if (user.user_metadata) {
          setOnboardingData(prev => ({
            ...prev,
            firstName: user.user_metadata.first_name || '',
            lastName: user.user_metadata.last_name || '',
            organizationName: user.user_metadata.organization_name || '',
          }));
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // Continue with onboarding if there's an error
      }
    };

    checkOnboardingStatus();
  }, [user, navigate]);

  const updateOnboardingData = (data: Partial<OnboardingData>) => {
    setOnboardingData(prev => ({ ...prev, ...data }));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const completeOnboarding = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Import the services we need
      const { checkUserProfile, createUserProfile, checkUserOrganizations, createUserOrganization } = await import('../../services/userProfile');

      console.log('Checking existing profile and organization for user:', user.id);

      // Check if profile already exists
      const profileCheck = await checkUserProfile(user.id);
      console.log('Profile check result:', profileCheck);

      // Check if organization already exists
      const orgCheck = await checkUserOrganizations(user.id);
      console.log('Organization check result:', orgCheck);

      // Only create profile if it doesn't exist
      if (!profileCheck.hasProfile) {
        console.log('Creating profile...');
        const profileResult = await createUserProfile(user.id, onboardingData.firstName, onboardingData.lastName);
        console.log('Profile creation result:', profileResult);

        if (!profileResult.success) {
          throw new Error(`Profile creation failed: ${profileResult.error}`);
        }
      } else {
        console.log('Profile already exists, skipping creation');
      }

      // Only create organization if it doesn't exist
      if (!orgCheck.hasOrganizations) {
        console.log('Creating organization...');
        const orgResult = await createUserOrganization(user.id, onboardingData.organizationName);
        console.log('Organization creation result:', orgResult);

        if (!orgResult.success) {
          throw new Error(`Organization creation failed: ${orgResult.error}`);
        }
      } else {
        console.log('Organization already exists, skipping creation');
      }

      // Mark onboarding as completed in database
      console.log('Marking onboarding as completed for user:', user.id);
      const { error: onboardingError } = await supabase
        .rpc('mark_onboarding_completed', { user_id: user.id });

      if (onboardingError) {
        console.error('Error marking onboarding as completed:', onboardingError);
        // Continue anyway, but log the error
      }

      // Also mark in localStorage for faster future checks
      localStorage.setItem(`onboarding_completed_${user.id}`, 'true');

      // Clean up any temporary data
      localStorage.removeItem(`onboarding_data_${user.id}`);

      // Force reload to refresh all contexts
      window.location.href = '/';
    } catch (error) {
      console.error('Error completing onboarding:', error);
      alert(`Setup failed: ${error.message}. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <OrganizationStep
            data={onboardingData}
            updateData={updateOnboardingData}
            onNext={nextStep}
          />
        );
      case 2:
        return (
          <BusinessTypeStep
            data={onboardingData}
            updateData={updateOnboardingData}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 3:
        return (
          <DiscoveryStep
            data={onboardingData}
            updateData={updateOnboardingData}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 4:
        return (
          <PersonalDetailsStep
            data={onboardingData}
            updateData={updateOnboardingData}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 5:
        return (
          <CompletionStep
            data={onboardingData}
            onComplete={completeOnboarding}
            onPrev={prevStep}
            isLoading={isLoading}
          />
        );
      default:
        return null;
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Welcome to AIRyx
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Let's set up your business in just a few steps
          </p>
        </div>

        <Card className="mb-6">
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Step {currentStep} of {totalSteps}
              </span>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {Math.round(progressPercentage)}%
              </span>
            </div>
            <Progress progress={progressPercentage} color="blue" />
          </div>

          {renderStep()}
        </Card>

        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          <p>Need help? Contact our support team</p>
        </div>
      </div>
    </div>
  );
};

export default OnboardingFlow;
