import React, { useState } from 'react';
import { Button, Label, TextInput, Alert } from 'flowbite-react';
import { HiOutlineUser } from 'react-icons/hi';
import { OnboardingData } from '../OnboardingFlow';

interface PersonalDetailsStepProps {
  data: OnboardingData;
  updateData: (data: Partial<OnboardingData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

const PersonalDetailsStep: React.FC<PersonalDetailsStepProps> = ({
  data,
  updateData,
  onNext,
  onPrev,
}) => {
  const [error, setError] = useState<string | null>(null);

  const handleNext = () => {
    setError(null);
    
    if (!data.firstName.trim()) {
      setError('First name is required');
      return;
    }

    if (!data.lastName.trim()) {
      setError('Last name is required');
      return;
    }

    if (data.firstName.trim().length < 2) {
      setError('First name must be at least 2 characters long');
      return;
    }

    if (data.lastName.trim().length < 2) {
      setError('Last name must be at least 2 characters long');
      return;
    }

    onNext();
  };

  const handleInputChange = (field: keyof OnboardingData, value: string) => {
    updateData({ [field]: value });
    if (error) setError(null);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4">
          <HiOutlineUser className="w-8 h-8 text-blue-600 dark:text-blue-400" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Tell us about yourself
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          We need your personal details to complete your profile
        </p>
      </div>

      {error && (
        <Alert color="failure">
          {error}
        </Alert>
      )}

      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="mb-2 block">
              <Label htmlFor="firstName" value="First Name *" />
            </div>
            <TextInput
              id="firstName"
              type="text"
              placeholder="Enter your first name"
              value={data.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              required
            />
          </div>

          <div>
            <div className="mb-2 block">
              <Label htmlFor="lastName" value="Last Name *" />
            </div>
            <TextInput
              id="lastName"
              type="text"
              placeholder="Enter your last name"
              value={data.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              required
            />
          </div>
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="phoneNumber" value="Phone Number (Optional)" />
          </div>
          <TextInput
            id="phoneNumber"
            type="tel"
            placeholder="e.g., +63 ************"
            value={data.phoneNumber || ''}
            onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
          />
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            We may use this to contact you about your account
          </p>
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="address" value="Business Address (Optional)" />
          </div>
          <TextInput
            id="address"
            type="text"
            placeholder="Enter your business address"
            value={data.address || ''}
            onChange={(e) => handleInputChange('address', e.target.value)}
          />
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            This helps us provide location-specific features
          </p>
        </div>
      </div>

      <div className="flex justify-between">
        <Button color="gray" onClick={onPrev}>
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!data.firstName.trim() || !data.lastName.trim()}
        >
          Continue
        </Button>
      </div>
    </div>
  );
};

export default PersonalDetailsStep;
