import React, { useState } from 'react';
import { Button, Label, TextInput, Alert } from 'flowbite-react';
import { HiOutlineOfficeBuilding } from 'react-icons/hi';
import { OnboardingData } from '../OnboardingFlow';

interface OrganizationStepProps {
  data: OnboardingData;
  updateData: (data: Partial<OnboardingData>) => void;
  onNext: () => void;
}

const OrganizationStep: React.FC<OrganizationStepProps> = ({
  data,
  updateData,
  onNext,
}) => {
  const [error, setError] = useState<string | null>(null);

  const handleNext = () => {
    setError(null);
    
    if (!data.organizationName.trim()) {
      setError('Organization name is required');
      return;
    }

    if (data.organizationName.trim().length < 2) {
      setError('Organization name must be at least 2 characters long');
      return;
    }

    onNext();
  };

  const handleInputChange = (value: string) => {
    updateData({ organizationName: value });
    if (error) setError(null);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4">
          <HiOutlineOfficeBuilding className="w-8 h-8 text-blue-600 dark:text-blue-400" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          What's your organization name?
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          This will be the name of your business or company in the system
        </p>
      </div>

      {error && (
        <Alert color="failure">
          {error}
        </Alert>
      )}

      <div className="space-y-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="organizationName" value="Organization/Company Name" />
          </div>
          <TextInput
            id="organizationName"
            type="text"
            placeholder="Enter your organization name"
            value={data.organizationName}
            onChange={(e) => handleInputChange(e.target.value)}
            required
            autoFocus
          />
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Examples: "ABC Store", "Maria's Restaurant", "Tech Solutions Inc."
          </p>
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          onClick={handleNext}
          disabled={!data.organizationName.trim()}
          className="px-8"
        >
          Continue
        </Button>
      </div>
    </div>
  );
};

export default OrganizationStep;
