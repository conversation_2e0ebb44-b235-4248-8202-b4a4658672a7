import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'flowbite-react';
import { HiOutlineGlobeAlt, HiOutlineUsers, HiOutlineNewspaper, HiOutlinePhone, HiOutlineSearch, HiOutlineHeart } from 'react-icons/hi';
import { OnboardingData } from '../OnboardingFlow';

interface DiscoveryStepProps {
  data: OnboardingData;
  updateData: (data: Partial<OnboardingData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

interface DiscoverySource {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
}

const discoverySources: DiscoverySource[] = [
  {
    id: 'google_search',
    name: 'Google Search',
    description: 'Found us through search results',
    icon: HiOutlineSearch,
  },
  {
    id: 'facebook',
    name: 'Facebook',
    description: 'Social media or Facebook ads',
    icon: HiOutlineGlobeAlt,
  },
  {
    id: 'friend_referral',
    name: 'Friend/Family Referral',
    description: 'Recommended by someone I know',
    icon: HiOutlineUsers,
  },
  {
    id: 'business_referral',
    name: 'Business Partner Referral',
    description: 'Recommended by another business',
    icon: HiOutlineUsers,
  },
  {
    id: 'online_ad',
    name: 'Online Advertisement',
    description: 'Saw an ad online (not Facebook)',
    icon: HiOutlineGlobeAlt,
  },
  {
    id: 'news_article',
    name: 'News/Article',
    description: 'Read about us in news or blog',
    icon: HiOutlineNewspaper,
  },
  {
    id: 'sales_call',
    name: 'Sales Call/Email',
    description: 'Contacted by our sales team',
    icon: HiOutlinePhone,
  },
  {
    id: 'existing_customer',
    name: 'Existing Customer',
    description: 'Already using AIRyx products',
    icon: HiOutlineHeart,
  },
  {
    id: 'other',
    name: 'Other',
    description: 'Different source',
    icon: HiOutlineGlobeAlt,
  },
];

const DiscoveryStep: React.FC<DiscoveryStepProps> = ({
  data,
  updateData,
  onNext,
  onPrev,
}) => {
  const [error, setError] = useState<string | null>(null);

  const handleSourceSelect = (source: DiscoverySource) => {
    updateData({ discoverySource: source.id });
    setError(null);
  };

  const handleNext = () => {
    if (!data.discoverySource) {
      setError('Please select how you found out about us');
      return;
    }
    onNext();
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          How did you find out about AIRyx?
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          This helps us understand how to better serve our customers
        </p>
      </div>

      {error && (
        <Alert color="failure">
          {error}
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {discoverySources.map((source) => {
          const Icon = source.icon;
          const isSelected = data.discoverySource === source.id;
          
          return (
            <div
              key={source.id}
              onClick={() => handleSourceSelect(source)}
              className={`
                p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                ${isSelected
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }
              `}
            >
              <div className="flex items-start space-x-3">
                <div className={`
                  p-2 rounded-lg
                  ${isSelected
                    ? 'bg-blue-100 dark:bg-blue-800'
                    : 'bg-gray-100 dark:bg-gray-800'
                  }
                `}>
                  <Icon className={`
                    w-5 h-5
                    ${isSelected
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-gray-600 dark:text-gray-400'
                    }
                  `} />
                </div>
                
                <div className="flex-1">
                  <h3 className={`
                    font-medium
                    ${isSelected
                      ? 'text-blue-900 dark:text-blue-100'
                      : 'text-gray-900 dark:text-white'
                    }
                  `}>
                    {source.name}
                  </h3>
                  <p className={`
                    text-sm
                    ${isSelected
                      ? 'text-blue-700 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-400'
                    }
                  `}>
                    {source.description}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="flex justify-between">
        <Button
          color="gray"
          onClick={onPrev}
        >
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!data.discoverySource}
        >
          Continue
        </Button>
      </div>
    </div>
  );
};

export default DiscoveryStep;
