import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'flowbite-react';
import { HiOutlineShoppingBag, HiOutlineHome, HiOutlineSparkles, HiOutlineHeart, HiOutlineCog } from 'react-icons/hi';
import { OnboardingData } from '../OnboardingFlow';

interface BusinessTypeStepProps {
  data: OnboardingData;
  updateData: (data: Partial<OnboardingData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

interface BusinessType {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  posType: 'retail' | 'cafe' | 'restaurant' | 'general';
  popular?: boolean;
}

const businessTypes: BusinessType[] = [
  {
    id: 'retail_store',
    name: 'Retail Store',
    description: 'Clothing, electronics, general merchandise',
    icon: HiOutlineShoppingBag,
    posType: 'retail',
    popular: true,
  },
  {
    id: 'sari_sari_store',
    name: 'Sari-Sari Store',
    description: 'Neighborhood convenience store',
    icon: HiOutlineHome,
    posType: 'retail',
    popular: true,
  },
  {
    id: 'restaurant',
    name: 'Restaurant',
    description: 'Full-service dining establishment',
    icon: HiOutlineSparkles,
    posType: 'restaurant',
    popular: true,
  },
  {
    id: 'cafe_coffee_shop',
    name: 'Café/Coffee Shop',
    description: 'Coffee, pastries, light meals',
    icon: HiOutlineHeart,
    posType: 'cafe',
    popular: true,
  },
  {
    id: 'bakery',
    name: 'Bakery',
    description: 'Bread, cakes, pastries',
    icon: HiOutlineHeart,
    posType: 'retail',
  },
  {
    id: 'pharmacy',
    name: 'Pharmacy',
    description: 'Medicines and health products',
    icon: HiOutlineHeart,
    posType: 'retail',
  },
  {
    id: 'beauty_salon',
    name: 'Beauty Salon/Barbershop',
    description: 'Hair, beauty, and grooming services',
    icon: HiOutlineSparkles,
    posType: 'general',
  },
  {
    id: 'auto_parts',
    name: 'Auto Parts Store',
    description: 'Car parts and accessories',
    icon: HiOutlineCog,
    posType: 'retail',
  },
  {
    id: 'hardware',
    name: 'Hardware Store',
    description: 'Tools, construction materials',
    icon: HiOutlineCog,
    posType: 'retail',
  },
  {
    id: 'grocery',
    name: 'Grocery Store',
    description: 'Food and household items',
    icon: HiOutlineShoppingBag,
    posType: 'retail',
  },
  {
    id: 'fast_food',
    name: 'Fast Food',
    description: 'Quick service restaurant',
    icon: HiOutlineSparkles,
    posType: 'restaurant',
  },
  {
    id: 'other',
    name: 'Other',
    description: 'Different type of business',
    icon: HiOutlineCog,
    posType: 'general',
  },
];

const BusinessTypeStep: React.FC<BusinessTypeStepProps> = ({
  data,
  updateData,
  onNext,
  onPrev,
}) => {
  const [error, setError] = useState<string | null>(null);

  const handleBusinessTypeSelect = (businessType: BusinessType) => {
    updateData({
      businessType: businessType.id,
      posType: businessType.posType,
    });
    setError(null);
  };

  const handleNext = () => {
    if (!data.businessType) {
      setError('Please select your business type');
      return;
    }
    onNext();
  };

  const selectedBusinessType = businessTypes.find(bt => bt.id === data.businessType);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          What type of business do you have?
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          This helps us configure the right POS system for your needs
        </p>
      </div>

      {error && (
        <Alert color="failure">
          {error}
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {businessTypes.map((businessType) => {
          const Icon = businessType.icon;
          const isSelected = data.businessType === businessType.id;

          return (
            <div
              key={businessType.id}
              onClick={() => handleBusinessTypeSelect(businessType)}
              className={`
                relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                ${isSelected
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }
                ${businessType.popular ? 'ring-2 ring-orange-200 dark:ring-orange-800' : ''}
              `}
            >
              {businessType.popular && (
                <div className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                  Popular
                </div>
              )}

              <div className="flex items-start space-x-3">
                <div className={`
                  p-2 rounded-lg
                  ${isSelected
                    ? 'bg-blue-100 dark:bg-blue-800'
                    : 'bg-gray-100 dark:bg-gray-800'
                  }
                `}>
                  <Icon className={`
                    w-6 h-6
                    ${isSelected
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-gray-600 dark:text-gray-400'
                    }
                  `} />
                </div>

                <div className="flex-1">
                  <h3 className={`
                    font-semibold
                    ${isSelected
                      ? 'text-blue-900 dark:text-blue-100'
                      : 'text-gray-900 dark:text-white'
                    }
                  `}>
                    {businessType.name}
                  </h3>
                  <p className={`
                    text-sm
                    ${isSelected
                      ? 'text-blue-700 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-400'
                    }
                  `}>
                    {businessType.description}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {selectedBusinessType && (
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            <strong>Selected:</strong> {selectedBusinessType.name} -
            We'll configure a {selectedBusinessType.posType} POS system for you.
          </p>
        </div>
      )}

      <div className="flex justify-between">
        <Button
          color="gray"
          onClick={onPrev}
        >
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!data.businessType}
        >
          Continue
        </Button>
      </div>
    </div>
  );
};

export default BusinessTypeStep;
