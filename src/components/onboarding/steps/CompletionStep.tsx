import React from 'react';
import { <PERSON><PERSON>, Spin<PERSON> } from 'flowbite-react';
import { HiOutlineCheckCircle, HiOutlineOfficeBuilding, HiOutlineShoppingBag, HiOutlineUser } from 'react-icons/hi';
import { OnboardingData } from '../OnboardingFlow';

interface CompletionStepProps {
  data: OnboardingData;
  onComplete: () => void;
  onPrev: () => void;
  isLoading: boolean;
}

const getBusinessTypeDisplay = (businessType: string) => {
  const businessTypes: { [key: string]: string } = {
    'retail_store': 'Retail Store',
    'sari_sari_store': 'Sari-Sari Store',
    'restaurant': 'Restaurant',
    'cafe_coffee_shop': 'Café/Coffee Shop',
    'bakery': 'Bakery',
    'pharmacy': 'Pharmacy',
    'beauty_salon': 'Beauty Salon/Barbershop',
    'auto_parts': 'Auto Parts Store',
    'hardware': 'Hardware Store',
    'grocery': 'Grocery Store',
    'fast_food': 'Fast Food',
    'other': 'Other',
  };
  return businessTypes[businessType] || businessType;
};

const getPosTypeDisplay = (posType: string) => {
  const posTypes: { [key: string]: string } = {
    'retail': 'Retail POS',
    'cafe': 'Café POS',
    'restaurant': 'Restaurant POS',
    'general': 'General POS',
  };
  return posTypes[posType] || posType;
};

const getDiscoverySourceDisplay = (source: string) => {
  const sources: { [key: string]: string } = {
    'google_search': 'Google Search',
    'facebook': 'Facebook',
    'friend_referral': 'Friend/Family Referral',
    'business_referral': 'Business Partner',
    'online_ad': 'Online Advertisement',
    'news_article': 'News/Article',
    'sales_call': 'Sales Call/Email',
    'existing_customer': 'Existing Customer',
    'other': 'Other',
  };
  return sources[source] || source;
};

const CompletionStep: React.FC<CompletionStepProps> = ({
  data,
  onComplete,
  onPrev,
  isLoading,
}) => {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4">
          <HiOutlineCheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Almost done!
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Please review your information before we set up your account
        </p>
      </div>

      <div className="space-y-4">
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex items-center space-x-3 mb-3">
            <HiOutlineOfficeBuilding className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <h3 className="font-semibold text-gray-900 dark:text-white">Organization</h3>
          </div>
          <p className="text-gray-700 dark:text-gray-300 ml-8">{data.organizationName}</p>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex items-center space-x-3 mb-3">
            <HiOutlineShoppingBag className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <h3 className="font-semibold text-gray-900 dark:text-white">Business Type</h3>
          </div>
          <p className="text-gray-700 dark:text-gray-300 ml-8">
            {getBusinessTypeDisplay(data.businessType)}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 ml-8">
            We'll set up a {getPosTypeDisplay(data.posType)} for you
          </p>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex items-center space-x-3 mb-3">
            <HiOutlineUser className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <h3 className="font-semibold text-gray-900 dark:text-white">Personal Details</h3>
          </div>
          <div className="ml-8 space-y-1">
            <p className="text-gray-700 dark:text-gray-300">
              {data.firstName} {data.lastName}
            </p>
            {data.phoneNumber && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Phone: {data.phoneNumber}
              </p>
            )}
            {data.address && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Address: {data.address}
              </p>
            )}
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 dark:text-white mb-2">How you found us</h3>
          <p className="text-gray-700 dark:text-gray-300">
            {getDiscoverySourceDisplay(data.discoverySource)}
          </p>
        </div>
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
          What happens next?
        </h3>
        <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
          <li>• We'll create your organization and profile</li>
          <li>• Your {getPosTypeDisplay(data.posType)} will be configured</li>
          <li>• You'll be redirected to your dashboard</li>
          <li>• You can start adding products and making sales</li>
        </ul>
      </div>

      <div className="flex justify-between">
        <Button color="gray" onClick={onPrev} disabled={isLoading}>
          Back
        </Button>
        <Button onClick={onComplete} disabled={isLoading}>
          {isLoading ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Setting up your account...
            </>
          ) : (
            'Complete Setup'
          )}
        </Button>
      </div>
    </div>
  );
};

export default CompletionStep;
