import React, { useState, useEffect } from 'react';
import { Card, Button, Badge, Modal, TextInput, Alert, Spinner } from 'flowbite-react';
import { 
  HiOutlineEye, 
  HiOutlineTrash, 
  HiOutlineStar, 
  HiStar,
  HiOutlinePencil,
  HiOutlineDownload,
  HiOutlineDocumentText
} from 'react-icons/hi';
import { useAuth } from '../../context/AuthContext';
import { useOrganization } from '../../context/OrganizationContext';
import {
  getAttachments,
  deleteAttachment,
  setPrimaryAttachment,
  updateAttachmentDescription
} from '../../services/attachments';
import {
  AttachmentGalleryProps,
  PayableAttachment
} from '../../types/attachments.types';
import MultipleFileUpload from './MultipleFileUpload';

const AttachmentGallery: React.FC<AttachmentGalleryProps> = ({
  attachableType,
  attachableId,
  editable = true,
  showUpload = true,
  maxFiles = 10,
  className = ''
}) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  // State
  const [attachments, setAttachments] = useState<PayableAttachment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAttachment, setSelectedAttachment] = useState<PayableAttachment | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [editingDescription, setEditingDescription] = useState<string | null>(null);
  const [newDescription, setNewDescription] = useState('');

  // Load attachments
  const loadAttachments = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    setError(null);

    try {
      const result = await getAttachments(
        currentOrganization.id,
        attachableType,
        attachableId
      );

      if (result.success) {
        setAttachments(result.attachments || []);
      } else {
        setError(result.error || 'Failed to load attachments');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Load attachments on mount
  useEffect(() => {
    loadAttachments();
  }, [currentOrganization, attachableType, attachableId]);

  // Handle upload complete
  const handleUploadComplete = (newAttachments: PayableAttachment[]) => {
    setAttachments(prev => [...prev, ...newAttachments]);
  };

  // Handle delete attachment
  const handleDelete = async (attachmentId: string) => {
    if (!user) return;

    try {
      const result = await deleteAttachment(attachmentId, user.id);
      if (result.success) {
        setAttachments(prev => prev.filter(a => a.id !== attachmentId));
      } else {
        setError(result.error || 'Failed to delete attachment');
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Handle set primary
  const handleSetPrimary = async (attachmentId: string) => {
    try {
      const result = await setPrimaryAttachment(attachmentId);
      if (result.success) {
        setAttachments(prev => prev.map(a => ({
          ...a,
          is_primary: a.id === attachmentId
        })));
      } else {
        setError(result.error || 'Failed to set primary attachment');
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Handle update description
  const handleUpdateDescription = async (attachmentId: string) => {
    if (!user) return;

    try {
      const result = await updateAttachmentDescription(
        attachmentId,
        newDescription,
        user.id
      );

      if (result.success) {
        setAttachments(prev => prev.map(a => 
          a.id === attachmentId 
            ? { ...a, description: newDescription }
            : a
        ));
        setEditingDescription(null);
        setNewDescription('');
      } else {
        setError(result.error || 'Failed to update description');
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Get file type icon
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return null; // Will show image preview
    }
    return <HiOutlineDocumentText className="h-8 w-8 text-gray-400" />;
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Error Alert */}
      {error && (
        <Alert color="failure" onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Upload Section */}
      {showUpload && editable && (
        <Card>
          <h4 className="text-lg font-semibold mb-3">Upload Attachments</h4>
          <MultipleFileUpload
            attachableType={attachableType}
            attachableId={attachableId}
            onUploadComplete={handleUploadComplete}
            onUploadError={setError}
            maxFiles={maxFiles}
          />
        </Card>
      )}

      {/* Attachments Grid */}
      <Card>
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-semibold">
            Attachments ({attachments.length})
          </h4>
        </div>

        {attachments.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <HiOutlineDocumentText className="h-12 w-12 mx-auto mb-2 text-gray-300" />
            <p>No attachments yet</p>
            {showUpload && editable && (
              <p className="text-sm">Upload some files to get started</p>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="border rounded-lg p-3 hover:shadow-md transition-shadow"
              >
                {/* File Preview */}
                <div className="aspect-square mb-3 bg-gray-50 rounded-lg flex items-center justify-center overflow-hidden">
                  {attachment.file_type.startsWith('image/') ? (
                    <img
                      src={attachment.file_url}
                      alt={attachment.file_name}
                      className="w-full h-full object-cover cursor-pointer"
                      onClick={() => {
                        setSelectedAttachment(attachment);
                        setShowModal(true);
                      }}
                    />
                  ) : (
                    <div className="text-center">
                      {getFileIcon(attachment.file_type)}
                      <p className="text-xs text-gray-500 mt-1">
                        {attachment.file_extension.toUpperCase()}
                      </p>
                    </div>
                  )}
                </div>

                {/* File Info */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <h5 className="text-sm font-medium truncate flex-1">
                      {attachment.file_name}
                    </h5>
                    {attachment.is_primary && (
                      <Badge color="yellow" size="xs">
                        <HiStar className="h-3 w-3 mr-1" />
                        Primary
                      </Badge>
                    )}
                  </div>

                  <p className="text-xs text-gray-500">
                    {formatFileSize(attachment.file_size)}
                  </p>

                  {/* Description */}
                  {editingDescription === attachment.id ? (
                    <div className="space-y-2">
                      <TextInput
                        size="sm"
                        value={newDescription}
                        onChange={(e) => setNewDescription(e.target.value)}
                        placeholder="Add description..."
                      />
                      <div className="flex gap-1">
                        <Button
                          size="xs"
                          onClick={() => handleUpdateDescription(attachment.id)}
                        >
                          Save
                        </Button>
                        <Button
                          size="xs"
                          color="light"
                          onClick={() => {
                            setEditingDescription(null);
                            setNewDescription('');
                          }}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-xs text-gray-600">
                      {attachment.description || 'No description'}
                    </p>
                  )}

                  {/* Actions */}
                  {editable && (
                    <div className="flex gap-1 pt-2">
                      <Button
                        size="xs"
                        color="light"
                        onClick={() => {
                          setSelectedAttachment(attachment);
                          setShowModal(true);
                        }}
                      >
                        <HiOutlineEye className="h-3 w-3" />
                      </Button>
                      
                      {!attachment.is_primary && (
                        <Button
                          size="xs"
                          color="light"
                          onClick={() => handleSetPrimary(attachment.id)}
                          title="Set as primary"
                        >
                          <HiOutlineStar className="h-3 w-3" />
                        </Button>
                      )}

                      <Button
                        size="xs"
                        color="light"
                        onClick={() => {
                          setEditingDescription(attachment.id);
                          setNewDescription(attachment.description || '');
                        }}
                        title="Edit description"
                      >
                        <HiOutlinePencil className="h-3 w-3" />
                      </Button>

                      {attachment.uploaded_by === user?.id && (
                        <Button
                          size="xs"
                          color="failure"
                          onClick={() => handleDelete(attachment.id)}
                          title="Delete"
                        >
                          <HiOutlineTrash className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Preview Modal */}
      <Modal
        show={showModal}
        onClose={() => {
          setShowModal(false);
          setSelectedAttachment(null);
        }}
        size="4xl"
      >
        <Modal.Header>
          {selectedAttachment?.file_name}
        </Modal.Header>
        <Modal.Body>
          {selectedAttachment && (
            <div className="space-y-4">
              {selectedAttachment.file_type.startsWith('image/') ? (
                <img
                  src={selectedAttachment.file_url}
                  alt={selectedAttachment.file_name}
                  className="w-full h-auto max-h-96 object-contain mx-auto"
                />
              ) : (
                <div className="text-center py-8">
                  <HiOutlineDocumentText className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                  <p className="text-lg font-medium">{selectedAttachment.file_name}</p>
                  <p className="text-gray-500">
                    {selectedAttachment.file_extension.toUpperCase()} • {formatFileSize(selectedAttachment.file_size)}
                  </p>
                </div>
              )}

              {selectedAttachment.description && (
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-gray-600">{selectedAttachment.description}</p>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="primary"
            onClick={() => window.open(selectedAttachment?.file_url, '_blank')}
          >
            <HiOutlineDownload className="mr-2 h-4 w-4" />
            Download
          </Button>
          <Button
            color="light"
            onClick={() => {
              setShowModal(false);
              setSelectedAttachment(null);
            }}
          >
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default AttachmentGallery;
