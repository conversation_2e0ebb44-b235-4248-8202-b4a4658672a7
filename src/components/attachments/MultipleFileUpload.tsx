import React, { useState, useRef } from 'react';
import { Button, Progress, Alert } from 'flowbite-react';
import { HiOutlineUpload, HiOutlineX, HiOutlinePhotograph } from 'react-icons/hi';
import { useAuth } from '../../context/AuthContext';
import { useOrganization } from '../../context/OrganizationContext';
import { uploadAttachment } from '../../services/attachments';
import {
  MultipleFileUploadProps,
  FileUploadProgress,
  PayableAttachment
} from '../../types/attachments.types';

const MultipleFileUpload: React.FC<MultipleFileUploadProps> = ({
  attachableType,
  attachableId,
  onUploadComplete,
  onUploadError,
  maxFiles = 10,
  disabled = false,
  className = ''
}) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State
  const [uploadQueue, setUploadQueue] = useState<FileUploadProgress[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    // Check max files limit
    if (uploadQueue.length + files.length > maxFiles) {
      onUploadError?.(`Maximum ${maxFiles} files allowed`);
      return;
    }

    // Add files to upload queue
    const newUploads: FileUploadProgress[] = files.map(file => ({
      file,
      progress: 0,
      status: 'pending'
    }));

    setUploadQueue(prev => [...prev, ...newUploads]);

    // Start upload process
    uploadFiles([...uploadQueue, ...newUploads]);

    // Clear input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Upload files
  const uploadFiles = async (uploads: FileUploadProgress[]) => {
    if (!currentOrganization || !user) return;

    setIsUploading(true);
    const completedAttachments: PayableAttachment[] = [];

    for (let i = 0; i < uploads.length; i++) {
      const upload = uploads[i];
      if (upload.status !== 'pending') continue;

      try {
        // Update status to uploading
        setUploadQueue(prev => prev.map((item, index) => 
          index === i ? { ...item, status: 'uploading', progress: 0 } : item
        ));

        // Simulate progress (since Supabase doesn't provide upload progress)
        const progressInterval = setInterval(() => {
          setUploadQueue(prev => prev.map((item, index) => 
            index === i && item.status === 'uploading' 
              ? { ...item, progress: Math.min(item.progress + 10, 90) } 
              : item
          ));
        }, 100);

        // Upload file
        const result = await uploadAttachment(
          currentOrganization.id,
          attachableType,
          attachableId,
          upload.file,
          user.id,
          '', // No description by default
          false // Not primary by default
        );

        clearInterval(progressInterval);

        if (result.success && result.attachment) {
          // Update status to completed
          setUploadQueue(prev => prev.map((item, index) => 
            index === i ? { 
              ...item, 
              status: 'completed', 
              progress: 100,
              attachment: result.attachment 
            } : item
          ));

          completedAttachments.push(result.attachment);
        } else {
          // Update status to error
          setUploadQueue(prev => prev.map((item, index) => 
            index === i ? { 
              ...item, 
              status: 'error', 
              error: result.error || 'Upload failed' 
            } : item
          ));

          onUploadError?.(result.error || 'Upload failed');
        }
      } catch (error: any) {
        setUploadQueue(prev => prev.map((item, index) => 
          index === i ? { 
            ...item, 
            status: 'error', 
            error: error.message 
          } : item
        ));

        onUploadError?.(error.message);
      }
    }

    setIsUploading(false);

    // Call completion callback
    if (completedAttachments.length > 0) {
      onUploadComplete?.(completedAttachments);
    }

    // Clear completed uploads after a delay
    setTimeout(() => {
      setUploadQueue(prev => prev.filter(item => item.status !== 'completed'));
    }, 3000);
  };

  // Remove file from queue
  const removeFromQueue = (index: number) => {
    setUploadQueue(prev => prev.filter((_, i) => i !== index));
  };

  // Get status color
  const getStatusColor = (status: FileUploadProgress['status']) => {
    switch (status) {
      case 'pending': return 'gray';
      case 'uploading': return 'blue';
      case 'completed': return 'green';
      case 'error': return 'red';
      default: return 'gray';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Button */}
      <div className="flex items-center gap-2">
        <Button
          color="light"
          size="sm"
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled || isUploading || uploadQueue.length >= maxFiles}
        >
          <HiOutlineUpload className="mr-2 h-4 w-4" />
          Upload Images
        </Button>
        <span className="text-sm text-gray-500">
          {uploadQueue.length}/{maxFiles} files
        </span>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*,.pdf"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Upload Queue */}
      {uploadQueue.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Upload Progress</h4>
          {uploadQueue.map((upload, index) => (
            <div key={index} className="border rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <HiOutlinePhotograph className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium truncate max-w-xs">
                    {upload.file.name}
                  </span>
                  <span className="text-xs text-gray-500">
                    ({(upload.file.size / 1024 / 1024).toFixed(1)} MB)
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-xs px-2 py-1 rounded-full bg-${getStatusColor(upload.status)}-100 text-${getStatusColor(upload.status)}-800`}>
                    {upload.status}
                  </span>
                  {upload.status === 'pending' && (
                    <Button
                      color="light"
                      size="xs"
                      onClick={() => removeFromQueue(index)}
                    >
                      <HiOutlineX className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Progress Bar */}
              {upload.status === 'uploading' && (
                <Progress
                  progress={upload.progress}
                  color="blue"
                  size="sm"
                />
              )}

              {/* Error Message */}
              {upload.status === 'error' && upload.error && (
                <Alert color="failure" className="mt-2">
                  <span className="text-sm">{upload.error}</span>
                </Alert>
              )}

              {/* Success Message */}
              {upload.status === 'completed' && (
                <Alert color="success" className="mt-2">
                  <span className="text-sm">Upload completed successfully!</span>
                </Alert>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Instructions */}
      <div className="text-xs text-gray-500">
        <p>• Supported formats: JPEG, PNG, GIF, WebP, PDF</p>
        <p>• Maximum file size: 10MB per file</p>
        <p>• Maximum {maxFiles} files total</p>
      </div>
    </div>
  );
};

export default MultipleFileUpload;
