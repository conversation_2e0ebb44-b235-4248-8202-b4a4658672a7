// =====================================================
// ADVANCED COSTING METHODS - COMPREHENSIVE TESTING
// =====================================================

import { 
  CostingService, 
  CostingMethodType, 
  CostingMethodFactory,
  SimpleCostingMethod,
  FIFOCostingMethod,
  LIFOCostingMethod,
  WeightedAverageCostingMethod,
  SaleItem
} from '../services/costingMethods';

// =====================================================
// TEST DATA SETUP
// =====================================================

const TEST_ORGANIZATION_ID = 'test-org-123';

// Sample sale items for testing
const sampleSaleItems: SaleItem[] = [
  {
    id: 'sale-item-1',
    productId: 'product-widget-a',
    quantity: 10,
    baseQuantity: 10,
    saleDate: new Date('2024-01-15T10:00:00Z')
  },
  {
    id: 'sale-item-2',
    productId: 'product-widget-a',
    quantity: 5,
    baseQuantity: 5,
    saleDate: new Date('2024-01-15T14:00:00Z')
  },
  {
    id: 'sale-item-3',
    productId: 'product-widget-b',
    quantity: 8,
    baseQuantity: 8,
    saleDate: new Date('2024-01-15T16:00:00Z')
  }
];

// =====================================================
// TESTING SCENARIOS
// =====================================================

/**
 * Test Scenario 1: Simple Costing Method
 * 
 * Expected Behavior:
 * - Uses product.cost_price for all calculations
 * - No inventory layer tracking
 * - Fast calculation
 * - Suitable for free tier users
 */
export const testSimpleCostingMethod = async () => {
  console.log('\n🧪 Testing Simple Costing Method');
  console.log('=====================================');
  
  const method = new SimpleCostingMethod(TEST_ORGANIZATION_ID);
  
  try {
    const result = await method.calculateCOGS(sampleSaleItems);
    
    console.log('✅ Simple Costing Results:');
    console.log(`   Total Cost: $${result.totalCost.toFixed(2)}`);
    console.log(`   Average Cost: $${result.averageCost.toFixed(2)}`);
    console.log(`   Method: ${result.method}`);
    console.log(`   Layers Used: ${result.layersUsed.length}`);
    
    return result;
  } catch (error) {
    console.error('❌ Simple Costing Error:', error);
    throw error;
  }
};

/**
 * Test Scenario 2: FIFO Costing Method
 * 
 * Expected Behavior:
 * - Uses oldest inventory costs first
 * - Tracks inventory layers by purchase date
 * - More accurate for businesses with price inflation
 * - Requires inventory transaction history
 */
export const testFIFOCostingMethod = async () => {
  console.log('\n🧪 Testing FIFO Costing Method');
  console.log('=====================================');
  
  const method = new FIFOCostingMethod(TEST_ORGANIZATION_ID);
  
  try {
    const result = await method.calculateCOGS(sampleSaleItems);
    
    console.log('✅ FIFO Costing Results:');
    console.log(`   Total Cost: $${result.totalCost.toFixed(2)}`);
    console.log(`   Average Cost: $${result.averageCost.toFixed(2)}`);
    console.log(`   Method: ${result.method}`);
    console.log(`   Layers Used: ${result.layersUsed.length}`);
    
    // Show layer details
    result.layersUsed.forEach((layer, index) => {
      console.log(`   Layer ${index + 1}: ${layer.quantity} units @ $${layer.unitCost} = $${layer.totalCost.toFixed(2)}`);
    });
    
    return result;
  } catch (error) {
    console.error('❌ FIFO Costing Error:', error);
    throw error;
  }
};

/**
 * Test Scenario 3: LIFO Costing Method
 * 
 * Expected Behavior:
 * - Uses newest inventory costs first
 * - Better matches current market prices
 * - May result in higher COGS during inflation
 * - Requires inventory transaction history
 */
export const testLIFOCostingMethod = async () => {
  console.log('\n🧪 Testing LIFO Costing Method');
  console.log('=====================================');
  
  const method = new LIFOCostingMethod(TEST_ORGANIZATION_ID);
  
  try {
    const result = await method.calculateCOGS(sampleSaleItems);
    
    console.log('✅ LIFO Costing Results:');
    console.log(`   Total Cost: $${result.totalCost.toFixed(2)}`);
    console.log(`   Average Cost: $${result.averageCost.toFixed(2)}`);
    console.log(`   Method: ${result.method}`);
    console.log(`   Layers Used: ${result.layersUsed.length}`);
    
    // Show layer details
    result.layersUsed.forEach((layer, index) => {
      console.log(`   Layer ${index + 1}: ${layer.quantity} units @ $${layer.unitCost} = $${layer.totalCost.toFixed(2)}`);
    });
    
    return result;
  } catch (error) {
    console.error('❌ LIFO Costing Error:', error);
    throw error;
  }
};

/**
 * Test Scenario 4: Weighted Average Costing Method
 * 
 * Expected Behavior:
 * - Uses average cost of all available inventory
 * - Smooths out price fluctuations
 * - Good for businesses with stable pricing
 * - Single average cost for all units
 */
export const testWeightedAverageCostingMethod = async () => {
  console.log('\n🧪 Testing Weighted Average Costing Method');
  console.log('=====================================');
  
  const method = new WeightedAverageCostingMethod(TEST_ORGANIZATION_ID);
  
  try {
    const result = await method.calculateCOGS(sampleSaleItems);
    
    console.log('✅ Weighted Average Costing Results:');
    console.log(`   Total Cost: $${result.totalCost.toFixed(2)}`);
    console.log(`   Average Cost: $${result.averageCost.toFixed(2)}`);
    console.log(`   Method: ${result.method}`);
    console.log(`   Layers Used: ${result.layersUsed.length}`);
    
    // Show average calculation
    if (result.layersUsed.length > 0) {
      const avgLayer = result.layersUsed[0];
      console.log(`   Weighted Avg: ${avgLayer.quantity} units @ $${avgLayer.unitCost} = $${avgLayer.totalCost.toFixed(2)}`);
    }
    
    return result;
  } catch (error) {
    console.error('❌ Weighted Average Costing Error:', error);
    throw error;
  }
};

/**
 * Test Scenario 5: Costing Service Integration
 * 
 * Tests the main CostingService with method switching
 */
export const testCostingServiceIntegration = async () => {
  console.log('\n🧪 Testing Costing Service Integration');
  console.log('=====================================');
  
  const methods = [
    CostingMethodType.SIMPLE,
    CostingMethodType.FIFO,
    CostingMethodType.LIFO,
    CostingMethodType.WEIGHTED_AVERAGE
  ];
  
  const results = [];
  
  for (const method of methods) {
    try {
      console.log(`\n📊 Testing ${method.toUpperCase()} method...`);
      
      const result = await CostingService.calculateCOGS(
        TEST_ORGANIZATION_ID,
        sampleSaleItems,
        method
      );
      
      console.log(`   ✅ ${method}: $${result.totalCost.toFixed(2)}`);
      results.push({ method, result });
      
    } catch (error) {
      console.error(`   ❌ ${method} failed:`, error);
    }
  }
  
  return results;
};

/**
 * Test Scenario 6: Method Comparison
 * 
 * Compares all methods side by side
 */
export const testMethodComparison = async () => {
  console.log('\n🧪 Method Comparison Analysis');
  console.log('=====================================');
  
  const results = await testCostingServiceIntegration();
  
  console.log('\n📊 COMPARISON RESULTS:');
  console.log('Method                | Total COGS | Avg Cost | Layers');
  console.log('---------------------|------------|----------|-------');
  
  results.forEach(({ method, result }) => {
    const methodName = method.padEnd(20);
    const totalCost = `$${result.totalCost.toFixed(2)}`.padEnd(10);
    const avgCost = `$${result.averageCost.toFixed(2)}`.padEnd(8);
    const layers = result.layersUsed.length.toString();
    
    console.log(`${methodName}| ${totalCost}| ${avgCost}| ${layers}`);
  });
  
  // Calculate differences
  if (results.length >= 2) {
    const simple = results.find(r => r.method === CostingMethodType.SIMPLE);
    const fifo = results.find(r => r.method === CostingMethodType.FIFO);
    
    if (simple && fifo) {
      const difference = Math.abs(simple.result.totalCost - fifo.result.totalCost);
      const percentage = simple.result.totalCost > 0 ? (difference / simple.result.totalCost) * 100 : 0;
      
      console.log(`\n💡 Simple vs FIFO difference: $${difference.toFixed(2)} (${percentage.toFixed(1)}%)`);
    }
  }
  
  return results;
};

// =====================================================
// MAIN TEST RUNNER
// =====================================================

export const runAllCostingTests = async () => {
  console.log('🚀 Starting Advanced Costing Methods Testing');
  console.log('==============================================');
  
  try {
    // Test individual methods
    await testSimpleCostingMethod();
    await testFIFOCostingMethod();
    await testLIFOCostingMethod();
    await testWeightedAverageCostingMethod();
    
    // Test service integration
    await testCostingServiceIntegration();
    
    // Compare methods
    await testMethodComparison();
    
    console.log('\n✅ All costing tests completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Testing failed:', error);
    throw error;
  }
};

// Export for use in other files
export default {
  runAllCostingTests,
  testSimpleCostingMethod,
  testFIFOCostingMethod,
  testLIFOCostingMethod,
  testWeightedAverageCostingMethod,
  testCostingServiceIntegration,
  testMethodComparison
};
