import { supabase } from '../lib/supabase';
import {
  PayrollPeriod,
  PayrollPeriodWithDetails,
  PayrollItem,
  PayrollItemWithDetails,
  PayrollDeduction,
  PayrollAllowance,
  PayrollSetting,
  TimeEntry,
  EmployeeSalary,
  EmployeeAllowance,
  EmployeeDeduction,
  PayrollCalculationResult,
  PayrollCalculationInput,
  ProcessPayrollOptions,
  PayrollReportOptions,
  PayrollSummary,
  PayrollPeriodStatus,
  PayrollItemStatus,
  DeductionType,
  AllowanceType,
  RateType
} from '../types/payroll';
import { Employee } from './employee';

/**
 * Get all payroll periods for an organization
 */
export const getPayrollPeriods = async (
  organizationId: string,
  options?: {
    limit?: number;
    offset?: number;
    status?: PayrollPeriodStatus;
    startDate?: Date;
    endDate?: Date;
  }
): Promise<{
  periods: PayrollPeriod[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('payroll_periods')
      .select('*', { count: 'exact' })
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });

    // Apply filters if provided
    if (options?.status) {
      query = query.eq('status', options.status);
    }

    if (options?.startDate) {
      query = query.gte('start_date', options.startDate.toISOString().split('T')[0]);
    }

    if (options?.endDate) {
      query = query.lte('end_date', options.endDate.toISOString().split('T')[0]);
    }

    // Apply pagination if provided
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching payroll periods:', error);
      return {
        periods: [],
        count: 0,
        error: error.message,
      };
    }

    return {
      periods: data as PayrollPeriod[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error in getPayrollPeriods:', error);
    return {
      periods: [],
      count: 0,
      error: error.message,
    };
  }
};

/**
 * Get a single payroll period by ID with its payroll items
 */
export const getPayrollPeriodById = async (
  organizationId: string,
  periodId: string,
  includeItems: boolean = false
): Promise<{
  period?: PayrollPeriodWithDetails;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('payroll_periods')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('id', periodId)
      .single();

    if (error) {
      console.error('Error fetching payroll period:', error);
      return {
        error: error.message,
      };
    }

    const period = data as PayrollPeriodWithDetails;

    if (includeItems) {
      const { data: itemsData, error: itemsError } = await supabase
        .from('payroll_items')
        .select(`
          *,
          employee:employee_id (
            id,
            first_name,
            middle_name,
            last_name,
            employee_number,
            profile_image_url
          )
        `)
        .eq('organization_id', organizationId)
        .eq('payroll_period_id', periodId);

      if (itemsError) {
        console.error('Error fetching payroll items:', itemsError);
        return {
          period,
          error: itemsError.message,
        };
      }

      period.payroll_items = itemsData as PayrollItemWithDetails[];
    }

    return {
      period,
    };
  } catch (error: any) {
    console.error('Error in getPayrollPeriodById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new payroll period
 */
export const createPayrollPeriod = async (
  organizationId: string,
  periodData: Omit<PayrollPeriod, 'id' | 'organization_id' | 'created_at' | 'updated_at'>
): Promise<{
  period?: PayrollPeriod;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('payroll_periods')
      .insert({
        organization_id: organizationId,
        ...periodData,
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error creating payroll period:', error);
      return {
        error: error.message,
      };
    }

    return {
      period: data as PayrollPeriod,
    };
  } catch (error: any) {
    console.error('Error in createPayrollPeriod:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update a payroll period
 */
export const updatePayrollPeriod = async (
  organizationId: string,
  periodId: string,
  periodData: Partial<PayrollPeriod>
): Promise<{
  period?: PayrollPeriod;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('payroll_periods')
      .update(periodData)
      .eq('organization_id', organizationId)
      .eq('id', periodId)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating payroll period:', error);
      return {
        error: error.message,
      };
    }

    return {
      period: data as PayrollPeriod,
    };
  } catch (error: any) {
    console.error('Error in updatePayrollPeriod:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Delete a payroll period
 */
export const deletePayrollPeriod = async (
  organizationId: string,
  periodId: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('payroll_periods')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', periodId);

    if (error) {
      console.error('Error deleting payroll period:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deletePayrollPeriod:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Get a single payroll item by ID
 */
export const getPayrollItemById = async (
  organizationId: string,
  itemId: string
): Promise<{
  item?: PayrollItemWithDetails;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('payroll_items')
      .select(`
        *,
        employee:employee_id (
          id,
          first_name,
          middle_name,
          last_name,
          employee_number,
          profile_image_url
        ),
        payroll_period:payroll_period_id (
          id,
          name,
          start_date,
          end_date,
          payment_date,
          status
        )
      `)
      .eq('organization_id', organizationId)
      .eq('id', itemId)
      .single();

    if (error) {
      console.error('Error fetching payroll item:', error);
      return {
        error: error.message,
      };
    }

    return {
      item: data as PayrollItemWithDetails,
    };
  } catch (error: any) {
    console.error('Error in getPayrollItemById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update a payroll item with government contribution overrides
 */
export const updatePayrollItem = async (
  organizationId: string,
  itemId: string,
  itemData: {
    sss_contribution_override?: number;
    philhealth_contribution_override?: number;
    pagibig_contribution_override?: number;
    withholding_tax_override?: number;
    contributions_manually_edited?: boolean;
    sss_contribution?: number;
    philhealth_contribution?: number;
    pagibig_contribution?: number;
    withholding_tax?: number;
    taxable_income?: number;
    net_pay?: number;
    total_deductions?: number;
  }
): Promise<{
  item?: PayrollItem;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('payroll_items')
      .update(itemData)
      .eq('organization_id', organizationId)
      .eq('id', itemId)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating payroll item:', error);
      return {
        error: error.message,
      };
    }

    return {
      item: data as PayrollItem,
    };
  } catch (error: any) {
    console.error('Error in updatePayrollItem:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Recalculate government contributions for a payroll item based on Philippine law
 */
export const recalculateGovernmentContributions = async (
  organizationId: string,
  itemId: string
): Promise<{
  item?: PayrollItem;
  error?: string;
}> => {
  try {
    // First get the current payroll item
    const { item, error: fetchError } = await getPayrollItemById(organizationId, itemId);

    if (fetchError || !item) {
      return { error: fetchError || 'Payroll item not found' };
    }

    // Import calculation functions
    const {
      calculateSSSContribution,
      calculatePhilHealthContribution,
      calculatePagIbigContribution,
      calculateWithholdingTax
    } = await import('./payrollCalculation');

    // Get the employee's salary information to determine the correct monthly salary
    const { data: salaryData } = await supabase
      .from('employee_salary')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('employee_id', item.employee_id)
      .single();

    if (!salaryData) {
      return { error: 'Employee salary not found' };
    }

    // Calculate monthly salary using the same logic as original payroll processing
    let monthlySalary = 0;
    if (salaryData.rate_type === 'daily') {
      // If daily rate is the primary rate, convert to monthly
      monthlySalary = Number(salaryData.daily_rate) * 22; // 22 working days
    } else {
      // If monthly rate is the primary rate
      monthlySalary = Number(salaryData.basic_salary);
    }

    // Debug logging
    console.log('Recalculation Debug:', {
      employeeId: item.employee_id,
      rateType: salaryData.rate_type,
      dailyRate: salaryData.daily_rate,
      basicSalary: salaryData.basic_salary,
      calculatedMonthlySalary: monthlySalary,
      originalBasicPay: item.basic_pay
    });

    // Calculate government contributions
    const sssContribution = calculateSSSContribution(monthlySalary);
    const philhealthContribution = calculatePhilHealthContribution(monthlySalary);
    const pagibigContribution = calculatePagIbigContribution(monthlySalary);

    // Calculate taxable income (gross pay minus tax-exempt deductions)
    const taxableIncome = item.gross_pay - sssContribution.employeeShare - philhealthContribution.employeeShare - pagibigContribution.employeeShare;

    // Calculate withholding tax
    const withholdingTax = calculateWithholdingTax(taxableIncome);

    // Calculate new total deductions
    const governmentDeductions = sssContribution.employeeShare + philhealthContribution.employeeShare + pagibigContribution.employeeShare + withholdingTax;
    const otherDeductions = item.total_deductions - (item.sss_contribution + item.philhealth_contribution + item.pagibig_contribution + item.withholding_tax);
    const newTotalDeductions = governmentDeductions + otherDeductions;

    // Calculate new net pay
    const newNetPay = item.gross_pay - newTotalDeductions;

    // Update the payroll item with recalculated values
    const updateData = {
      sss_contribution: sssContribution.employeeShare,
      philhealth_contribution: philhealthContribution.employeeShare,
      pagibig_contribution: pagibigContribution.employeeShare,
      withholding_tax: withholdingTax,
      taxable_income: taxableIncome,
      total_deductions: newTotalDeductions,
      net_pay: newNetPay,
      contributions_manually_edited: false, // Reset manual edit flag
      // Clear override values
      sss_contribution_override: null,
      philhealth_contribution_override: null,
      pagibig_contribution_override: null,
      withholding_tax_override: null,
    };

    return await updatePayrollItem(organizationId, itemId, updateData);
  } catch (error: any) {
    console.error('Error in recalculateGovernmentContributions:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Get payroll settings for an organization
 */
export const getPayrollSettings = async (
  organizationId: string
): Promise<{
  settings?: PayrollSetting;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('payroll_settings')
      .select('*')
      .eq('organization_id', organizationId)
      .single();

    if (error) {
      // If no settings found, create default settings
      if (error.code === 'PGRST116') {
        return createPayrollSettings(organizationId, {
          pay_schedule: 'semi-monthly',
          semi_monthly_days: [15, 30],
          pay_based_on_time_entries: true,
          sss_employer_contribution_rate: 8.5,
          philhealth_employer_contribution_rate: 2.0,
          pagibig_employer_contribution_rate: 2.0,
          tax_table_version: '2023'
        });
      }

      console.error('Error fetching payroll settings:', error);
      return {
        error: error.message,
      };
    }

    return {
      settings: data as PayrollSetting,
    };
  } catch (error: any) {
    console.error('Error in getPayrollSettings:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create payroll settings for an organization
 */
export const createPayrollSettings = async (
  organizationId: string,
  settingsData: Omit<PayrollSetting, 'id' | 'organization_id' | 'created_at' | 'updated_at'>
): Promise<{
  settings?: PayrollSetting;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('payroll_settings')
      .insert({
        organization_id: organizationId,
        ...settingsData,
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error creating payroll settings:', error);
      return {
        error: error.message,
      };
    }

    return {
      settings: data as PayrollSetting,
    };
  } catch (error: any) {
    console.error('Error in createPayrollSettings:', error);
    return {
      error: error.message,
    };
  }
};
