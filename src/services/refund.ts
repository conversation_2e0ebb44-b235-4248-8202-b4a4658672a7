import { supabase } from '../lib/supabase';
import { createInventoryTransaction } from './inventoryTransaction';
import {
  Refund,
  RefundInsert,
  RefundUpdate,
  RefundWithDetails,
  RefundItem,
  RefundItemInsert,
  CreateRefundRequest,
  ProcessRefundRequest,
  RefundResponse,
  RefundListResponse,
  RefundFilters,
  RefundListOptions,
  RefundValidation,
  RefundSummary,
  RefundStatus,
  RefundType,
  RefundMethod
} from '../types/refund.types';

export class RefundService {
  /**
   * Validate if a sale can be refunded
   */
  static async validateRefund(saleId: string, organizationId: string): Promise<RefundValidation> {
    try {
      // Get original sale with items (without refunds for now since refund tables don't exist)
      const { data: sale, error: saleError } = await supabase
        .from('sales')
        .select(`
          *,
          customer:customers (
            id,
            name,
            email,
            phone
          ),
          sale_items (
            id,
            product_id,
            quantity,
            unit_price,
            total_amount,
            product:products (
              id,
              name,
              sku
            )
          )
        `)
        .eq('id', saleId)
        .eq('organization_id', organizationId)
        .single();

      if (saleError || !sale) {
        return {
          can_refund: false,
          reasons: [saleError ? `Database error: ${saleError.message}` : 'Sale not found'],
          max_refund_amount: 0,
          eligible_items: []
        };
      }

      // Check if sale is too old (configurable policy)
      const saleDate = new Date(sale.sale_date);
      const daysSinceSale = Math.floor((Date.now() - saleDate.getTime()) / (1000 * 60 * 60 * 24));
      const maxRefundDays = 30; // Configurable business rule

      if (daysSinceSale > maxRefundDays) {
        return {
          can_refund: false,
          reasons: [`Sale is older than ${maxRefundDays} days`],
          max_refund_amount: 0,
          eligible_items: []
        };
      }

      // Get existing refunds for this sale to calculate remaining quantities
      const { data: existingRefunds } = await supabase
        .from('refunds')
        .select(`
          id,
          status,
          refund_items (
            sale_item_id,
            quantity
          )
        `)
        .eq('original_sale_id', saleId)
        .neq('status', 'cancelled')
        .neq('status', 'rejected');

      // Calculate already refunded quantities
      const refundedQuantities: { [saleItemId: string]: number } = {};
      existingRefunds?.forEach(refund => {
        refund.refund_items?.forEach(item => {
          refundedQuantities[item.sale_item_id] =
            (refundedQuantities[item.sale_item_id] || 0) + item.quantity;
        });
      });

      // Calculate eligible items with remaining quantities
      const eligibleItems = sale.sale_items
        ?.map(item => {
          const refundedQty = refundedQuantities[item.id] || 0;
          const remainingQty = item.quantity - refundedQty;

          return {
            sale_item_id: item.id,
            product_id: item.product_id,
            max_quantity: remainingQty,
            unit_price: item.unit_price
          };
        })
        .filter(item => item.max_quantity > 0) || [];

      const maxRefundAmount = eligibleItems.reduce(
        (sum, item) => sum + (item.max_quantity * item.unit_price),
        0
      );

      return {
        can_refund: eligibleItems.length > 0,
        reasons: eligibleItems.length === 0 ? ['All items have already been refunded'] : [],
        max_refund_amount: maxRefundAmount,
        eligible_items: eligibleItems
      };

    } catch (error) {
      console.error('Error validating refund:', error);
      return {
        can_refund: false,
        reasons: ['Error validating refund'],
        max_refund_amount: 0,
        eligible_items: []
      };
    }
  }

  /**
   * Create a new refund
   */
  static async createRefund(request: CreateRefundRequest, organizationId: string): Promise<RefundResponse> {
    try {
      // Validate the refund first
      const validation = await this.validateRefund(request.original_sale_id, organizationId);
      if (!validation.can_refund) {
        return {
          success: false,
          error: `Cannot process refund: ${validation.reasons.join(', ')}`
        };
      }

      // Generate refund number
      const { data: refundNumber, error: numberError } = await supabase.rpc('generate_refund_number', {
        org_id: organizationId
      });

      if (numberError) {
        console.error('Error generating refund number:', numberError);
        return {
          success: false,
          error: `Failed to generate refund number: ${numberError.message}`
        };
      }

      if (!refundNumber) {
        return {
          success: false,
          error: 'Failed to generate refund number - no number returned'
        };
      }

      // Calculate totals - NO TAX ON REFUNDS
      const subtotal = request.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);

      // No tax calculation for refunds
      const taxAmount = 0;
      const totalAmount = subtotal - (request.restocking_fee || 0);

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return {
          success: false,
          error: 'User not authenticated'
        };
      }

      // Create refund record
      const refundData: RefundInsert = {
        organization_id: organizationId,
        refund_number: refundNumber,
        original_sale_id: request.original_sale_id,
        customer_id: request.customer_id,
        refund_type: request.refund_type,
        reason: request.reason,
        reason_notes: request.reason_notes,
        subtotal,
        tax_amount: taxAmount,
        total_amount: totalAmount,
        restocking_fee: request.restocking_fee || 0,
        refund_method: request.refund_method,
        requires_approval: request.requires_approval || totalAmount > 1000, // Business rule
        status: (request.requires_approval || totalAmount > 1000) ? 'pending' : 'approved',
        created_by: user.id
      };

      const { data: refund, error: refundError } = await supabase
        .from('refunds')
        .insert(refundData)
        .select()
        .single();

      if (refundError || !refund) {
        return {
          success: false,
          error: 'Failed to create refund'
        };
      }

      // Create refund items
      const refundItems: RefundItemInsert[] = request.items.map(item => ({
        refund_id: refund.id,
        sale_item_id: item.sale_item_id,
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.quantity * item.unit_price,
        condition: item.condition,
        restore_inventory: item.restore_inventory,
        notes: item.notes
      }));

      const { error: itemsError } = await supabase
        .from('refund_items')
        .insert(refundItems);

      if (itemsError) {
        // Rollback refund creation
        await supabase.from('refunds').delete().eq('id', refund.id);
        return {
          success: false,
          error: 'Failed to create refund items'
        };
      }

      // If auto-approved, process immediately
      if (refund.status === 'approved') {
        await this.processRefund({
          refund_id: refund.id,
          approved: true,
          processed_by: user.id
        });
      }

      // Get complete refund data
      const completeRefund = await this.getRefundById(refund.id);

      return {
        success: true,
        data: completeRefund.data
      };

    } catch (error) {
      console.error('Error creating refund:', error);
      return {
        success: false,
        error: 'Failed to create refund'
      };
    }
  }

  /**
   * Process a refund (approve/reject and execute)
   */
  static async processRefund(request: ProcessRefundRequest): Promise<RefundResponse> {
    try {
      const { data: refund, error: refundError } = await supabase
        .from('refunds')
        .select(`
          *,
          refund_items (
            *,
            products (id, name, sku)
          )
        `)
        .eq('id', request.refund_id)
        .single();

      if (refundError || !refund) {
        return {
          success: false,
          error: 'Refund not found'
        };
      }

      if (refund.status !== 'pending' && refund.status !== 'approved') {
        return {
          success: false,
          error: 'Refund cannot be processed in current status'
        };
      }

      // Update refund status
      const updateData: RefundUpdate = {
        status: request.approved ? 'processed' : 'rejected',
        approved_by: request.processed_by,
        approved_at: new Date().toISOString(),
        approval_notes: request.approval_notes,
        processed_by: request.processed_by,
        processed_at: new Date().toISOString()
      };

      const { error: updateError } = await supabase
        .from('refunds')
        .update(updateData)
        .eq('id', request.refund_id);

      if (updateError) {
        return {
          success: false,
          error: 'Failed to update refund status'
        };
      }

      // If approved, restore inventory and handle payment
      if (request.approved) {
        // Get current user for inventory tracking
        const { data: { user } } = await supabase.auth.getUser();
        const userId = user?.id || request.processed_by;

        await this.restoreInventory(refund.refund_items, request.refund_id, userId);

        // Handle store credit creation if needed
        if (refund.refund_method === 'store_credit') {
          await this.createStoreCredit(refund);
        }

        // Update original sale status based on refund type and amount
        await this.updateOriginalSaleStatus(refund.original_sale_id, refund.refund_type, refund.total_amount);
      }

      // Get updated refund data
      const updatedRefund = await this.getRefundById(request.refund_id);

      return {
        success: true,
        data: updatedRefund.data
      };

    } catch (error) {
      console.error('Error processing refund:', error);
      return {
        success: false,
        error: 'Failed to process refund'
      };
    }
  }

  /**
   * Get refund by ID with full details
   */
  static async getRefundById(refundId: string): Promise<RefundResponse> {
    try {
      const { data, error } = await supabase
        .from('refunds')
        .select(`
          *,
          original_sale:sales (
            id,
            invoice_number,
            total_amount,
            sale_date,
            customer:customers (
              id,
              name,
              email,
              phone
            )
          ),
          refund_items (
            *,
            product:products (
              id,
              name,
              sku,
              unit_price,
              image_url
            ),
            original_sale_item:sale_items (
              id,
              quantity,
              unit_price,
              total_amount
            )
          )
        `)
        .eq('id', refundId)
        .single();

      if (error || !data) {
        return {
          success: false,
          error: 'Refund not found'
        };
      }

      return {
        success: true,
        data: data as RefundWithDetails
      };

    } catch (error) {
      console.error('Error getting refund:', error);
      return {
        success: false,
        error: 'Failed to get refund'
      };
    }
  }

  /**
   * Process inventory for refunded items based on condition
   */
  private static async restoreInventory(refundItems: any[], refundId: string, userId: string): Promise<void> {
    for (const item of refundItems) {
      try {
        // Get the product to find its default UoM
        const { data: product, error: productError } = await supabase
          .from('products')
          .select(`
            id,
            name,
            organization_id,
            stock_quantity,
            product_uoms (
              id,
              uom_id,
              is_default,
              conversion_factor
            )
          `)
          .eq('id', item.product_id)
          .single();

        if (productError || !product) {
          console.error('Error fetching product for refund inventory:', productError);
          continue;
        }

        // Get the default UoM for the product
        const defaultUom = product.product_uoms?.find((pu: any) => pu.is_default);
        const uomId = defaultUom?.uom_id;

        if (!uomId) {
          console.error('No default unit of measure found for product:', item.product_id);
          continue;
        }

        // Determine transaction type and quantity based on condition and restore_inventory flag
        let transactionType = 'adjustment';
        let adjustmentQuantity = 0;
        let notes = '';

        switch (item.condition) {
          case 'new':
            if (item.restore_inventory) {
              transactionType = 'return';
              adjustmentQuantity = item.quantity; // Positive to add back to inventory
              notes = `Refund - Item returned in new condition, restored to inventory (Refund: ${refundId})`;
            } else {
              transactionType = 'adjustment';
              adjustmentQuantity = 0;
              notes = `Refund - Item in new condition but not restored to inventory (Refund: ${refundId})`;
            }
            break;
          case 'used':
            if (item.restore_inventory) {
              transactionType = 'return';
              adjustmentQuantity = item.quantity; // Positive to add back to inventory
              notes = `Refund - Used item returned, restored to inventory (Refund: ${refundId})`;
            } else {
              transactionType = 'adjustment';
              adjustmentQuantity = 0;
              notes = `Refund - Used item returned but not restored to inventory (Refund: ${refundId})`;
            }
            break;
          case 'damaged':
            transactionType = 'adjustment';
            adjustmentQuantity = 0;
            notes = `Refund - Damaged item, sent for disposal/repair, not restored to inventory (Refund: ${refundId})`;
            break;
          case 'defective':
            transactionType = 'adjustment';
            adjustmentQuantity = 0;
            notes = `Refund - Defective item, sent for warranty/disposal, not restored to inventory (Refund: ${refundId})`;
            break;
          default:
            transactionType = 'adjustment';
            adjustmentQuantity = 0;
            notes = `Refund - Unknown condition, not restored to inventory (Refund: ${refundId})`;
        }

        // Create the inventory transaction using the same service as adjustments
        const { transaction, error: transactionError } = await createInventoryTransaction(
          product.organization_id,
          userId,
          {
            productId: item.product_id,
            transactionType: transactionType,
            quantity: adjustmentQuantity, // This will be positive for returns, 0 for adjustments
            uomId: uomId,
            referenceId: refundId,
            referenceType: 'refund',
            notes: notes,
          }
        );

        if (transactionError) {
          console.error('Error creating refund inventory transaction:', transactionError);
        } else {
          console.log(`Created refund inventory transaction: ${adjustmentQuantity} units of product ${item.product_id}, condition: ${item.condition}, restore: ${item.restore_inventory}`);
        }

      } catch (error) {
        console.error('Error processing refund inventory for item:', item.product_id, error);
      }
    }
  }

  /**
   * Create store credit for refund
   */
  private static async createStoreCredit(refund: any): Promise<void> {
    const { data: creditNumber } = await supabase.rpc('generate_credit_number', {
      org_id: refund.organization_id
    });

    await supabase.from('store_credits').insert({
      organization_id: refund.organization_id,
      customer_id: refund.customer_id,
      refund_id: refund.id,
      credit_number: creditNumber,
      original_amount: refund.total_amount,
      remaining_balance: refund.total_amount,
      created_by: refund.processed_by
    });
  }

  /**
   * Update original sale status based on refund type and amount
   */
  private static async updateOriginalSaleStatus(saleId: string, refundType: string, refundAmount: number): Promise<void> {
    try {
      // Get original sale details
      const { data: sale, error: saleError } = await supabase
        .from('sales')
        .select('id, total_amount, status')
        .eq('id', saleId)
        .single();

      if (saleError || !sale) {
        console.error('Error fetching original sale for status update:', saleError);
        return;
      }

      // Get total refunded amount for this sale
      const { data: refunds, error: refundsError } = await supabase
        .from('refunds')
        .select('total_amount')
        .eq('original_sale_id', saleId)
        .eq('status', 'processed');

      if (refundsError) {
        console.error('Error fetching refunds for sale status update:', refundsError);
        return;
      }

      const totalRefunded = refunds?.reduce((sum, r) => sum + Number(r.total_amount), 0) || 0;
      const refundPercentage = (totalRefunded / sale.total_amount) * 100;

      // Determine new status based on refund percentage
      let newStatus = sale.status;
      if (refundType === 'full' || refundPercentage >= 100) {
        newStatus = 'refunded';
      } else if (refundPercentage > 0) {
        // For partial refunds, we keep status as 'completed' but could add a flag
        // This maintains the original sale integrity while tracking refund status
        newStatus = 'completed';
      }

      // Update sale status if it changed
      if (newStatus !== sale.status) {
        const { error: updateError } = await supabase
          .from('sales')
          .update({
            status: newStatus,
            updated_at: new Date().toISOString()
          })
          .eq('id', saleId);

        if (updateError) {
          console.error('Error updating sale status:', updateError);
        } else {
          console.log(`Updated sale ${saleId} status to ${newStatus} (${refundPercentage.toFixed(1)}% refunded)`);
        }
      }

    } catch (error) {
      console.error('Error updating original sale status:', error);
    }
  }

  /**
   * Get list of refunds with filtering and pagination
   */
  static async getRefunds(organizationId: string, options: RefundListOptions = {}): Promise<RefundListResponse> {
    try {

      let query = supabase
        .from('refunds')
        .select(`
          *,
          original_sale:sales (
            id,
            invoice_number,
            total_amount,
            sale_date,
            customer:customers (
              id,
              name,
              email,
              phone
            )
          ),
          refund_items (
            *,
            product:products (
              id,
              name,
              sku,
              unit_price
            )
          )
        `, { count: 'exact' })
        .eq('organization_id', organizationId);

      // Apply filters
      if (options.filters) {
        const { status, refund_type, date_from, date_to, customer_id } = options.filters;

        if (status) {
          query = query.eq('status', status);
        }

        if (refund_type) {
          query = query.eq('refund_type', refund_type);
        }

        if (date_from) {
          query = query.gte('created_at', date_from);
        }

        if (date_to) {
          query = query.lte('created_at', date_to);
        }

        if (customer_id) {
          query = query.eq('customer_id', customer_id);
        }
      }

      // Apply sorting
      const sortBy = options.sort_by || 'created_at';
      const sortOrder = options.sort_order || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          success: false,
          error: error.message
        };
      }

      return {
        success: true,
        data: data || [],
        total_count: count || 0
      };

    } catch (error) {
      console.error('Error getting refunds:', error);
      return {
        success: false,
        error: 'Failed to fetch refunds'
      };
    }
  }

  /**
   * Get refund summary and analytics
   */
  static async getRefundSummary(organizationId: string, dateFrom?: string, dateTo?: string): Promise<RefundSummary> {
    try {

      let query = supabase
        .from('refunds')
        .select('*')
        .eq('organization_id', organizationId);

      if (dateFrom) {
        query = query.gte('created_at', dateFrom);
      }

      if (dateTo) {
        // Add one day to include the entire end date
        const endDate = new Date(dateTo);
        endDate.setDate(endDate.getDate() + 1);
        query = query.lt('created_at', endDate.toISOString());
      }

      const { data: refunds, error } = await query;



      if (error || !refunds) {
        console.error('❌ Refund query error:', error);
        throw new Error('Failed to fetch refund data');
      }

      // Calculate summary statistics
      const summary: RefundSummary = {
        total_refunds: refunds.length,
        total_amount: refunds.reduce((sum, r) => sum + Number(r.total_amount), 0),
        pending_approvals: refunds.filter(r => r.status === 'pending').length,
        pending_amount: refunds.filter(r => r.status === 'pending').reduce((sum, r) => sum + Number(r.total_amount), 0),
        processed_today: 0,
        processed_amount_today: 0,
        by_reason: {},
        by_method: {}
      };

      // Calculate today's processed refunds
      const today = new Date().toISOString().split('T')[0];
      const processedToday = refunds.filter(r =>
        r.status === 'processed' &&
        r.processed_at &&
        r.processed_at.startsWith(today)
      );

      summary.processed_today = processedToday.length;
      summary.processed_amount_today = processedToday.reduce((sum, r) => sum + Number(r.total_amount), 0);

      // Group by reason
      refunds.forEach(refund => {
        const reason = refund.reason;
        if (!summary.by_reason[reason]) {
          summary.by_reason[reason] = { count: 0, amount: 0 };
        }
        summary.by_reason[reason].count++;
        summary.by_reason[reason].amount += Number(refund.total_amount);
      });

      // Group by method
      refunds.forEach(refund => {
        const method = refund.refund_method;
        if (!summary.by_method[method]) {
          summary.by_method[method] = { count: 0, amount: 0 };
        }
        summary.by_method[method].count++;
        summary.by_method[method].amount += Number(refund.total_amount);
      });

      return summary;

    } catch (error) {
      console.error('Error getting refund summary:', error);
      return {
        total_refunds: 0,
        total_amount: 0,
        pending_approvals: 0,
        pending_amount: 0,
        processed_today: 0,
        processed_amount_today: 0,
        by_reason: {},
        by_method: {}
      };
    }
  }

  /**
   * Search for original sale by receipt number or customer
   */
  static async searchOriginalSale(organizationId: string, searchTerm: string) {
    try {
      const { data, error } = await supabase
        .from('sales')
        .select(`
          *,
          customer:customers (
            id,
            name,
            email,
            phone
          ),
          sale_items (
            *,
            product:products (
              id,
              name,
              sku,
              unit_price,
              image_url
            )
          )
        `)
        .eq('organization_id', organizationId)
        .or(`invoice_number.ilike.%${searchTerm}%`)
        .order('sale_date', { ascending: false })
        .limit(10);

      if (error) {
        return {
          success: false,
          error: 'Failed to search sales'
        };
      }

      return {
        success: true,
        data: data || []
      };

    } catch (error) {
      console.error('Error searching original sale:', error);
      return {
        success: false,
        error: 'Failed to search sales'
      };
    }
  }
}
