// Service layer for Enhanced Payables System - Operational Expenses
// Phase 1: Foundation services for expense management

import { supabase } from '../lib/supabase';
import {
  ExpenseType,
  CreateExpenseTypeRequest,
  ExpenseTypeFilters,
  RecurringExpense,
  CreateRecurringExpenseRequest,
  RecurringExpenseFilters,
  EnhancedPayable,
  CreateEnhancedPayableRequest,
  EnhancedPayableFilters,
  VendorContact,
  CreateVendorContactRequest,
  ApprovalWorkflow,
  OperationalExpenseResponse,
  ExpenseTypesResponse,
  RecurringExpensesResponse,
  EnhancedPayablesResponse,
  ExpenseDashboardData,
  ExpenseCategory,
  RecurrenceFrequency,
  EnhancedPayableSourceType,
  PayableStatus,
  ApprovalStatus
} from '../types/operationalExpenses.types';
import { PayrollPayableOptions } from './payrollPayables';
import { PayrollPeriodStatus } from '../types/payroll';
import { getOrganizationSettings } from './organization';

// =====================================================
// EXPENSE TYPES MANAGEMENT
// =====================================================

/**
 * Get all expense types for an organization
 */
export const getExpenseTypes = async (
  organizationId: string,
  filters?: ExpenseTypeFilters
): Promise<ExpenseTypesResponse> => {
  try {
    let query = supabase
      .from('expense_types')
      .select('*')
      .eq('organization_id', organizationId)
      .order('name');

    // Apply filters
    if (filters?.category) {
      query = query.eq('category', filters.category);
    }
    if (filters?.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active);
    }
    if (filters?.is_recurring_type !== undefined) {
      query = query.eq('is_recurring_type', filters.is_recurring_type);
    }
    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,code.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching expense types:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data: data || [] };
  } catch (err: any) {
    console.error('Error in getExpenseTypes:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Create a new expense type
 */
export const createExpenseType = async (
  organizationId: string,
  expenseTypeData: CreateExpenseTypeRequest,
  userId: string
): Promise<OperationalExpenseResponse<ExpenseType>> => {
  try {
    const { data, error } = await supabase
      .from('expense_types')
      .insert({
        organization_id: organizationId,
        ...expenseTypeData,
        created_by: userId
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating expense type:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data, message: 'Expense type created successfully' };
  } catch (err: any) {
    console.error('Error in createExpenseType:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Update an expense type
 */
export const updateExpenseType = async (
  expenseTypeId: string,
  updates: Partial<CreateExpenseTypeRequest>
): Promise<OperationalExpenseResponse<ExpenseType>> => {
  try {
    const { data, error } = await supabase
      .from('expense_types')
      .update(updates)
      .eq('id', expenseTypeId)
      .select()
      .single();

    if (error) {
      console.error('Error updating expense type:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data, message: 'Expense type updated successfully' };
  } catch (err: any) {
    console.error('Error in updateExpenseType:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Delete an expense type (soft delete by setting is_active = false)
 */
export const deleteExpenseType = async (
  expenseTypeId: string
): Promise<OperationalExpenseResponse<void>> => {
  try {
    const { error } = await supabase
      .from('expense_types')
      .update({ is_active: false })
      .eq('id', expenseTypeId);

    if (error) {
      console.error('Error deleting expense type:', error);
      return { success: false, error: error.message };
    }

    return { success: true, message: 'Expense type deleted successfully' };
  } catch (err: any) {
    console.error('Error in deleteExpenseType:', err);
    return { success: false, error: err.message };
  }
};

// =====================================================
// RECURRING EXPENSES MANAGEMENT
// =====================================================

/**
 * Get all recurring expenses for an organization
 */
export const getRecurringExpenses = async (
  organizationId: string,
  filters?: RecurringExpenseFilters
): Promise<RecurringExpensesResponse> => {
  try {
    let query = supabase
      .from('recurring_expenses')
      .select(`
        *,
        expense_type:expense_types(*),
        supplier:suppliers(id, name, contact_person, email, phone),
        employee:employees(id, first_name, last_name, employee_number)
      `)
      .eq('organization_id', organizationId)
      .order('next_due_date');

    // Apply filters
    if (filters?.expense_type_id) {
      query = query.eq('expense_type_id', filters.expense_type_id);
    }
    if (filters?.supplier_id) {
      query = query.eq('supplier_id', filters.supplier_id);
    }
    if (filters?.frequency) {
      query = query.eq('frequency', filters.frequency);
    }
    if (filters?.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active);
    }
    if (filters?.due_soon) {
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      query = query.lte('next_due_date', thirtyDaysFromNow.toISOString().split('T')[0]);
    }
    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching recurring expenses:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data: data || [] };
  } catch (err: any) {
    console.error('Error in getRecurringExpenses:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Create a new recurring expense
 */
export const createRecurringExpense = async (
  organizationId: string,
  recurringExpenseData: CreateRecurringExpenseRequest,
  userId: string
): Promise<OperationalExpenseResponse<RecurringExpense>> => {
  try {
    // Calculate next due date based on start date and frequency
    const startDate = new Date(recurringExpenseData.start_date);
    const nextDueDate = calculateNextDueDate(startDate, recurringExpenseData.frequency);

    const { data, error } = await supabase
      .from('recurring_expenses')
      .insert({
        organization_id: organizationId,
        ...recurringExpenseData,
        next_due_date: nextDueDate.toISOString().split('T')[0],
        created_by: userId
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating recurring expense:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data, message: 'Recurring expense created successfully' };
  } catch (err: any) {
    console.error('Error in createRecurringExpense:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Update a recurring expense
 */
export const updateRecurringExpense = async (
  recurringExpenseId: string,
  updates: Partial<CreateRecurringExpenseRequest>
): Promise<OperationalExpenseResponse<RecurringExpense>> => {
  try {
    const { data, error } = await supabase
      .from('recurring_expenses')
      .update(updates)
      .eq('id', recurringExpenseId)
      .select()
      .single();

    if (error) {
      console.error('Error updating recurring expense:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data, message: 'Recurring expense updated successfully' };
  } catch (err: any) {
    console.error('Error in updateRecurringExpense:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Create Enhanced Payable from inventory receipt (requires approval)
 */
export const createEnhancedPayableFromReceipt = async (
  organizationId: string,
  receiptId: string,
  userId: string
): Promise<OperationalExpenseResponse<EnhancedPayable>> => {
  try {
    // Check if receipt exists and is completed
    const { data: receipt, error: receiptError } = await supabase
      .from('inventory_receipts')
      .select(`
        id, receipt_number, status, receipt_date, purchase_order_id, organization_id,
        purchase_order:purchase_orders(
          id, order_number, supplier_id,
          supplier:suppliers(id, name, payment_terms_days)
        )
      `)
      .eq('organization_id', organizationId)
      .eq('id', receiptId)
      .single();

    if (receiptError || !receipt) {
      return { success: false, error: 'Receipt not found' };
    }

    if (receipt.status !== 'completed') {
      return { success: false, error: 'Receipt must be completed before creating payable' };
    }

    if (!receipt.purchase_order?.supplier_id) {
      return { success: false, error: 'Receipt must have a valid supplier' };
    }

    // Check if payable already exists for this receipt
    const { data: existingPayable } = await supabase
      .from('payables')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('source_type', 'purchase_receipt')
      .eq('source_id', receiptId)
      .single();

    if (existingPayable) {
      return { success: false, error: 'Payable already exists for this receipt' };
    }

    // Get receipt items with QC status
    const { data: receiptItems, error: itemsError } = await supabase
      .from('inventory_receipt_items')
      .select(`
        id, product_id, quantity, unit_cost, qc_status,
        product:products(id, name, sku)
      `)
      .eq('inventory_receipt_id', receiptId);

    if (itemsError || !receiptItems) {
      return { success: false, error: 'Failed to load receipt items' };
    }

    // Calculate total amount (exclude failed QC items)
    const passedItems = receiptItems.filter(item => item.qc_status !== 'failed');
    const receiptTotal = passedItems.reduce((total, item) => {
      return total + (item.quantity * item.unit_cost);
    }, 0);

    if (receiptTotal <= 0) {
      return { success: false, error: 'No valid items to create payable for' };
    }

    // Calculate due date
    const paymentTermsDays = receipt.purchase_order.supplier.payment_terms_days || 30;
    const dueDate = new Date(receipt.receipt_date);
    dueDate.setDate(dueDate.getDate() + paymentTermsDays);

    // Generate unique source_id and reference number
    const timestamp = Date.now();
    const uniqueSourceId = `${receiptId}-${timestamp}`;
    const referenceNumber = `INV-${receipt.receipt_number}-${timestamp}`;

    // Create Enhanced Payable (pending approval)
    const payableData: CreateEnhancedPayableRequest = {
      source_type: EnhancedPayableSourceType.PURCHASE_RECEIPT,
      source_id: uniqueSourceId,
      supplier_id: receipt.purchase_order.supplier_id,
      reference_number: referenceNumber,
      invoice_date: receipt.receipt_date,
      due_date: dueDate.toISOString().split('T')[0],
      amount: receiptTotal,
      vat_amount: 0, // Will be calculated based on business settings
      withholding_tax_rate: 0,
      withholding_tax_amount: 0,
      currency: 'PHP',
      category: 'inventory',
      notes: `Created from inventory receipt: ${receipt.receipt_number} (PO: ${receipt.purchase_order.order_number}). Amount calculated based on QC status - failed items excluded.`,
      department: '',
      project_code: ''
    };

    const result = await createEnhancedPayable(organizationId, payableData, userId);
    return result;
  } catch (err: any) {
    console.error('Error in createEnhancedPayableFromReceipt:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Create payable from recurring expense (goes directly to traditional payables, already approved)
 */
export const createPayableFromRecurringExpense = async (
  organizationId: string,
  recurringExpenseId: string,
  userId: string
): Promise<OperationalExpenseResponse<EnhancedPayable>> => {
  try {
    // Get recurring expense details
    const { data: recurringExpense, error: fetchError } = await supabase
      .from('recurring_expenses')
      .select('*')
      .eq('id', recurringExpenseId)
      .eq('organization_id', organizationId)
      .single();

    if (fetchError || !recurringExpense) {
      return { success: false, error: 'Recurring expense not found' };
    }

    // Calculate due date
    const invoiceDate = new Date().toISOString().split('T')[0];
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + recurringExpense.payment_terms_days);

    // Generate unique source_id with timestamp to prevent duplicates
    const timestamp = Date.now();
    const uniqueSourceId = `${recurringExpenseId}-${timestamp}`;

    // Generate reference number
    const referenceNumber = `REC-${recurringExpense.name.substring(0, 3).toUpperCase()}-${timestamp}`;

    // Calculate balance (amount minus any withholding tax)
    const withholdingTaxAmount = (recurringExpense.amount * recurringExpense.withholding_tax_rate) / 100;
    const balance = recurringExpense.amount - withholdingTaxAmount;

    // Create payable directly in traditional payables table (already approved)
    const { data, error } = await supabase
      .from('payables')
      .insert({
        organization_id: organizationId,
        source_type: EnhancedPayableSourceType.RECURRING_EXPENSE,
        source_id: uniqueSourceId,
        supplier_id: recurringExpense.supplier_id,
        employee_id: recurringExpense.employee_id,
        reference_number: referenceNumber,
        invoice_date: invoiceDate,
        due_date: dueDate.toISOString().split('T')[0],
        amount: recurringExpense.amount,
        vat_amount: recurringExpense.vat_amount,
        withholding_tax_rate: recurringExpense.withholding_tax_rate,
        withholding_tax_amount: withholdingTaxAmount,
        currency: recurringExpense.currency || 'PHP',
        expense_type_id: recurringExpense.expense_type_id,
        recurring_expense_id: recurringExpenseId,
        notes: `Created from recurring expense: ${recurringExpense.name}`,
        balance,
        status: PayableStatus.OPEN,
        approval_status: ApprovalStatus.APPROVED, // Already approved
        approved_by: userId,
        approved_at: new Date().toISOString(),
        created_by: userId
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating payable from recurring expense:', error);
      return { success: false, error: error.message };
    }

    // Update the recurring expense's next due date
    const nextDueDate = calculateNextDueDate(new Date(), recurringExpense.frequency);
    await supabase
      .from('recurring_expenses')
      .update({
        next_due_date: nextDueDate.toISOString().split('T')[0],
        last_generated_date: invoiceDate,
        updated_at: new Date().toISOString()
      })
      .eq('id', recurringExpenseId);

    return { success: true, data, message: 'Payable created successfully and approved' };
  } catch (err: any) {
    console.error('Error in createPayableFromRecurringExpense:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Create Enhanced Payables from payroll (requires approval)
 * Gets organization ID from payroll period to ensure security
 */
export const createEnhancedPayablesFromPayroll = async (
  _organizationId: string, // Keep for compatibility but use payroll period's org ID
  payrollPeriodId: string,
  userId: string,
  options: PayrollPayableOptions
): Promise<OperationalExpenseResponse<any>> => {
  try {
    // Get payroll period details first to get the correct organization ID
    const { data: payrollPeriod, error: periodError } = await supabase
      .from('payroll_periods')
      .select('*')
      .eq('id', payrollPeriodId)
      .single();

    if (periodError || !payrollPeriod) {
      return { success: false, error: 'Payroll period not found' };
    }

    // Use the organization ID from the payroll period to ensure security
    const organizationId = payrollPeriod.organization_id;

    // Get organization settings for currency
    const orgSettings = await getOrganizationSettings(organizationId);
    const currency = orgSettings?.settings?.currency || 'PHP';

    if (payrollPeriod.status !== PayrollPeriodStatus.APPROVED) {
      return { success: false, error: 'Payroll period must be approved before creating payables' };
    }

    // Get payroll items with organization filter for security
    const { data: payrollItems, error: itemsError } = await supabase
      .from('payroll_items')
      .select(`
        *,
        employee:employees(id, first_name, last_name, employee_number)
      `)
      .eq('payroll_period_id', payrollPeriodId)
      .eq('organization_id', organizationId);

    if (itemsError || !payrollItems) {
      return { success: false, error: 'Failed to load payroll items' };
    }

    const createdPayables = [];
    let summary = {
      employeePayables: 0,
      governmentPayables: 0,
      thirdPartyPayables: 0,
      totalAmount: 0
    };

    // Create payables for each payroll item
    for (const item of payrollItems) {
      if (options.createEmployeePayables && item.net_pay > 0) {
        // Create employee payable
        const timestamp = Date.now() + Math.random(); // Ensure uniqueness
        const referenceNumber = `PAY-${item.employee.employee_number}-${payrollPeriod.period_start}-${timestamp}`;

        const employeePayableData: CreateEnhancedPayableRequest = {
          source_type: EnhancedPayableSourceType.PAYROLL,
          source_id: payrollPeriodId, // Use payroll_period_id for trigger validation
          employee_id: item.employee_id,
          reference_number: referenceNumber,
          invoice_date: payrollPeriod.period_end,
          due_date: payrollPeriod.pay_date,
          amount: item.net_pay,
          vat_amount: 0,
          withholding_tax_rate: 0,
          withholding_tax_amount: 0,
          currency,
          category: 'payroll',
          notes: `Employee salary for ${payrollPeriod.period_start} to ${payrollPeriod.period_end}`,
          department: '',
          project_code: ''
        };

        const result = await createEnhancedPayable(organizationId, employeePayableData, userId);
        if (result.success) {
          createdPayables.push(result.data);
          summary.employeePayables++;
          summary.totalAmount += item.net_pay;
        }
      }

      // Create government contribution payables if enabled
      if (options.createGovernmentPayables) {
        const contributions = [
          { name: 'SSS', amount: item.sss_contribution },
          { name: 'PhilHealth', amount: item.philhealth_contribution },
          { name: 'Pag-IBIG', amount: item.pagibig_contribution },
          { name: 'BIR', amount: item.withholding_tax }
        ];

        for (const contrib of contributions) {
          if (contrib.amount > 0) {
            const timestamp = Date.now() + Math.random();
            const referenceNumber = `${contrib.name}-${item.employee.employee_number}-${payrollPeriod.period_start}-${timestamp}`;

            const govPayableData: CreateEnhancedPayableRequest = {
              source_type: EnhancedPayableSourceType.PAYROLL,
              source_id: payrollPeriodId, // Use payroll_period_id for trigger validation
              reference_number: referenceNumber,
              invoice_date: payrollPeriod.period_end,
              due_date: payrollPeriod.pay_date,
              amount: contrib.amount,
              vat_amount: 0,
              withholding_tax_rate: 0,
              withholding_tax_amount: 0,
              currency,
              category: 'government_contribution',
              notes: `${contrib.name} contribution for ${item.employee.first_name} ${item.employee.last_name} (${payrollPeriod.period_start} to ${payrollPeriod.period_end})`,
              department: '',
              project_code: ''
            };

            const result = await createEnhancedPayable(organizationId, govPayableData, userId);
            if (result.success) {
              createdPayables.push(result.data);
              summary.governmentPayables++;
              summary.totalAmount += contrib.amount;
            }
          }
        }
      }
    }

    // Mark payroll period as having payables created
    if (createdPayables.length > 0) {
      await supabase
        .from('payroll_periods')
        .update({
          payables_created: true,
          payables_created_at: new Date().toISOString(),
          payables_created_by: userId
        })
        .eq('id', payrollPeriodId)
        .eq('organization_id', organizationId);
    }

    return {
      success: true,
      data: createdPayables,
      summary,
      message: `Created ${createdPayables.length} Enhanced Payables for approval`
    };
  } catch (err: any) {
    console.error('Error in createEnhancedPayablesFromPayroll:', err);
    return { success: false, error: err.message };
  }
};

// =====================================================
// ENHANCED PAYABLES MANAGEMENT
// =====================================================

/**
 * Get enhanced payables with operational expense features
 */
export const getEnhancedPayables = async (
  organizationId: string,
  filters?: EnhancedPayableFilters
): Promise<EnhancedPayablesResponse> => {
  try {
    let query = supabase
      .from('payables')
      .select(`
        *,
        expense_type:expense_types(*),
        recurring_expense:recurring_expenses(*),
        supplier:suppliers(id, name, contact_person, email, phone),
        employee:employees(id, first_name, last_name, employee_number),
        payments:payable_payments(*)
      `)
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.approval_status) {
      query = query.eq('approval_status', filters.approval_status);
    }
    if (filters?.source_type) {
      query = query.eq('source_type', filters.source_type);
    }
    if (filters?.expense_type_id) {
      query = query.eq('expense_type_id', filters.expense_type_id);
    }
    if (filters?.supplier_id) {
      query = query.eq('supplier_id', filters.supplier_id);
    }
    if (filters?.department) {
      query = query.eq('department', filters.department);
    }
    if (filters?.project_code) {
      query = query.eq('project_code', filters.project_code);
    }
    if (filters?.date_from) {
      query = query.gte('invoice_date', filters.date_from);
    }
    if (filters?.date_to) {
      query = query.lte('invoice_date', filters.date_to);
    }
    if (filters?.amount_from) {
      query = query.gte('amount', filters.amount_from);
    }
    if (filters?.amount_to) {
      query = query.lte('amount', filters.amount_to);
    }
    if (filters?.overdue) {
      const today = new Date().toISOString().split('T')[0];
      query = query.lt('due_date', today).in('status', ['open', 'partially_paid']);
    }
    if (filters?.search) {
      query = query.or(`reference_number.ilike.%${filters.search}%,notes.ilike.%${filters.search}%`);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching enhanced payables:', error);
      return { success: false, error: error.message };
    }

    // Calculate total_paid for each payable
    const enhancedData = (data || []).map(payable => ({
      ...payable,
      total_paid: payable.payments?.reduce((sum: number, payment: any) => sum + payment.amount_paid, 0) || 0
    }));

    return { success: true, data: enhancedData };
  } catch (err: any) {
    console.error('Error in getEnhancedPayables:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Create an enhanced payable with operational expense features
 */
export const createEnhancedPayable = async (
  organizationId: string,
  payableData: CreateEnhancedPayableRequest,
  userId: string
): Promise<OperationalExpenseResponse<EnhancedPayable>> => {
  try {
    // Calculate balance (amount minus any withholding tax)
    const balance = payableData.amount - (payableData.withholding_tax_amount || 0);

    const { data, error } = await supabase
      .from('payables')
      .insert({
        organization_id: organizationId,
        ...payableData,
        balance,
        status: PayableStatus.OPEN,
        approval_status: ApprovalStatus.PENDING,
        created_by: userId
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating enhanced payable:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data, message: 'Payable created successfully' };
  } catch (err: any) {
    console.error('Error in createEnhancedPayable:', err);
    return { success: false, error: err.message };
  }
};

// =====================================================
// VENDOR CONTACTS MANAGEMENT
// =====================================================

/**
 * Get vendor contacts for a supplier
 */
export const getVendorContacts = async (
  supplierId: string
): Promise<OperationalExpenseResponse<VendorContact[]>> => {
  try {
    const { data, error } = await supabase
      .from('vendor_contacts')
      .select('*')
      .eq('supplier_id', supplierId)
      .eq('is_active', true)
      .order('contact_type');

    if (error) {
      console.error('Error fetching vendor contacts:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data: data || [] };
  } catch (err: any) {
    console.error('Error in getVendorContacts:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Create a new vendor contact
 */
export const createVendorContact = async (
  contactData: CreateVendorContactRequest
): Promise<OperationalExpenseResponse<VendorContact>> => {
  try {
    const { data, error } = await supabase
      .from('vendor_contacts')
      .insert(contactData)
      .select()
      .single();

    if (error) {
      console.error('Error creating vendor contact:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data, message: 'Vendor contact created successfully' };
  } catch (err: any) {
    console.error('Error in createVendorContact:', err);
    return { success: false, error: err.message };
  }
};

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Calculate next due date based on frequency
 */
export const calculateNextDueDate = (currentDate: Date, frequency: RecurrenceFrequency): Date => {
  const nextDate = new Date(currentDate);

  switch (frequency) {
    case RecurrenceFrequency.WEEKLY:
      nextDate.setDate(nextDate.getDate() + 7);
      break;
    case RecurrenceFrequency.BI_WEEKLY:
      nextDate.setDate(nextDate.getDate() + 14);
      break;
    case RecurrenceFrequency.MONTHLY:
      nextDate.setMonth(nextDate.getMonth() + 1);
      break;
    case RecurrenceFrequency.QUARTERLY:
      nextDate.setMonth(nextDate.getMonth() + 3);
      break;
    case RecurrenceFrequency.SEMI_ANNUAL:
      nextDate.setMonth(nextDate.getMonth() + 6);
      break;
    case RecurrenceFrequency.ANNUAL:
      nextDate.setFullYear(nextDate.getFullYear() + 1);
      break;
    default:
      nextDate.setMonth(nextDate.getMonth() + 1); // Default to monthly
  }

  return nextDate;
};

/**
 * Approve a payable
 */
export const approvePayable = async (
  payableId: string,
  userId: string
): Promise<OperationalExpenseResponse<EnhancedPayable>> => {
  try {
    const { data, error } = await supabase
      .from('payables')
      .update({
        approval_status: ApprovalStatus.APPROVED,
        approved_by: userId,
        approved_at: new Date().toISOString()
      })
      .eq('id', payableId)
      .select()
      .single();

    if (error) {
      console.error('Error approving payable:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data, message: 'Payable approved successfully' };
  } catch (err: any) {
    console.error('Error in approvePayable:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Reject a payable
 */
export const rejectPayable = async (
  payableId: string,
  userId: string,
  rejectionReason: string
): Promise<OperationalExpenseResponse<EnhancedPayable>> => {
  try {
    const { data, error } = await supabase
      .from('payables')
      .update({
        approval_status: ApprovalStatus.REJECTED,
        approved_by: userId,
        approved_at: new Date().toISOString(),
        rejection_reason: rejectionReason
      })
      .eq('id', payableId)
      .select()
      .single();

    if (error) {
      console.error('Error rejecting payable:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data, message: 'Payable rejected successfully' };
  } catch (err: any) {
    console.error('Error in rejectPayable:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Get payable details by ID
 */
export const getPayableDetails = async (
  payableId: string
): Promise<OperationalExpenseResponse<EnhancedPayable>> => {
  try {
    const { data, error } = await supabase
      .from('payables')
      .select(`
        *,
        expense_type:expense_types(*),
        recurring_expense:recurring_expenses(*),
        supplier:suppliers(id, name, contact_person, email, phone),
        employee:employees(id, first_name, last_name, employee_number),
        payments:payable_payments(*)
      `)
      .eq('id', payableId)
      .single();

    if (error) {
      console.error('Error fetching payable details:', error);
      return { success: false, error: error.message };
    }

    // Calculate total_paid
    const total_paid = data.payments?.reduce((sum: number, payment: any) => sum + payment.amount_paid, 0) || 0;

    return {
      success: true,
      data: { ...data, total_paid }
    };
  } catch (err: any) {
    console.error('Error in getPayableDetails:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Get due recurring expenses (next 30 days)
 */
export const getDueRecurringExpenses = async (
  organizationId: string
): Promise<RecurringExpensesResponse> => {
  const filters: RecurringExpenseFilters = {
    is_active: true,
    due_soon: true
  };

  return getRecurringExpenses(organizationId, filters);
};

/**
 * Process due recurring expenses and create payables automatically
 */
export const processRecurringExpenses = async (
  organizationId: string
): Promise<OperationalExpenseResponse<{ created: number; errors: string[] }>> => {
  try {
    // Get all active recurring expenses
    const recurringResult = await getRecurringExpenses(organizationId, {
      is_active: true,
      auto_create_payable: true
    });

    if (!recurringResult.success || !recurringResult.data) {
      return { success: false, error: 'Failed to fetch recurring expenses' };
    }

    const recurringExpenses = recurringResult.data;
    const today = new Date();
    const created: string[] = [];
    const errors: string[] = [];

    for (const expense of recurringExpenses) {
      try {
        // Check if this expense is due for creation
        const nextDueDate = calculateRecurringDueDate(expense.start_date, expense.frequency, expense.last_generated_date);

        if (nextDueDate && nextDueDate <= today) {
          // Generate period key for unique source_id
          const periodKey = `${nextDueDate.getFullYear()}-${(nextDueDate.getMonth() + 1).toString().padStart(2, '0')}`;
          const uniqueSourceId = `${expense.id}-${periodKey}`;

          // Check if payable already exists for this period
          const existingPayableResult = await supabase
            .from('payables')
            .select('id')
            .eq('organization_id', organizationId)
            .eq('source_type', EnhancedPayableSourceType.RECURRING_EXPENSE)
            .eq('source_id', uniqueSourceId)
            .single();

          if (existingPayableResult.data) {
            // Payable already exists for this period, skip
            continue;
          }

          // Create payable from recurring expense
          const payableData: CreateEnhancedPayableRequest = {
            source_type: EnhancedPayableSourceType.RECURRING_EXPENSE,
            source_id: uniqueSourceId,
            supplier_id: expense.supplier_id || undefined,
            employee_id: expense.employee_id || undefined,
            reference_number: `REC-${expense.name.replace(/\s+/g, '-').toUpperCase()}-${nextDueDate.getFullYear()}${(nextDueDate.getMonth() + 1).toString().padStart(2, '0')}`,
            invoice_date: nextDueDate.toISOString().split('T')[0],
            due_date: (() => {
              const dueDate = new Date(nextDueDate);
              dueDate.setDate(dueDate.getDate() + expense.payment_terms_days);
              return dueDate.toISOString().split('T')[0];
            })(),
            amount: expense.amount,
            vat_amount: expense.vat_amount,
            withholding_tax_rate: expense.withholding_tax_rate,
            withholding_tax_amount: expense.withholding_tax_rate > 0
              ? (expense.amount * expense.withholding_tax_rate) / 100
              : 0,
            currency: 'PHP',
            category: expense.category || '',
            notes: `Auto-generated from recurring expense: ${expense.name} for period ${periodKey}`,
            expense_type_id: expense.expense_type_id || undefined,
            department: expense.department || '',
            project_code: expense.project_code || ''
          };

          // Create the payable
          const createResult = await createEnhancedPayable(organizationId, payableData, 'system');

          if (createResult.success) {
            // Update the recurring expense's last_generated_date
            await supabase
              .from('recurring_expenses')
              .update({
                last_generated_date: nextDueDate.toISOString().split('T')[0],
                updated_at: new Date().toISOString()
              })
              .eq('id', expense.id);

            created.push(`${expense.name} - ₱${expense.amount.toLocaleString()}`);
          } else {
            errors.push(`Failed to create payable for ${expense.name}: ${createResult.error}`);
          }
        }
      } catch (err: any) {
        errors.push(`Error processing ${expense.name}: ${err.message}`);
      }
    }

    return {
      success: true,
      data: { created: created.length, errors },
      message: `Processed ${created.length} recurring expenses. ${errors.length} errors.`
    };
  } catch (err: any) {
    console.error('Error in processRecurringExpenses:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Calculate next due date for recurring expense processing
 */
const calculateRecurringDueDate = (
  startDate: string,
  frequency: RecurrenceFrequency,
  lastGenerated?: string | null
): Date | null => {
  const start = new Date(startDate);
  const lastGen = lastGenerated ? new Date(lastGenerated) : null;
  const baseDate = lastGen || start;
  const today = new Date();

  let nextDate = new Date(baseDate);

  switch (frequency) {
    case RecurrenceFrequency.DAILY:
      nextDate.setDate(nextDate.getDate() + 1);
      break;
    case RecurrenceFrequency.WEEKLY:
      nextDate.setDate(nextDate.getDate() + 7);
      break;
    case RecurrenceFrequency.MONTHLY:
      nextDate.setMonth(nextDate.getMonth() + 1);
      break;
    case RecurrenceFrequency.QUARTERLY:
      nextDate.setMonth(nextDate.getMonth() + 3);
      break;
    case RecurrenceFrequency.YEARLY:
      nextDate.setFullYear(nextDate.getFullYear() + 1);
      break;
    default:
      return null;
  }

  // If this is the first generation and start date is in the future, use start date
  if (!lastGenerated && start > today) {
    return start;
  }

  return nextDate;
};

/**
 * Get expense dashboard data
 */
export const getExpenseDashboardData = async (
  organizationId: string
): Promise<OperationalExpenseResponse<ExpenseDashboardData>> => {
  try {
    // Get pending approvals count
    const { data: pendingApprovals, error: pendingError } = await supabase
      .from('payables')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('approval_status', 'pending');

    // Get overdue payments count
    const today = new Date().toISOString().split('T')[0];
    const { data: overduePayments, error: overdueError } = await supabase
      .from('payables')
      .select('id')
      .eq('organization_id', organizationId)
      .in('status', ['open', 'partially_paid'])
      .lt('due_date', today);

    // Get upcoming recurring expenses (next 30 days)
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    const { data: upcomingRecurring, error: recurringError } = await supabase
      .from('recurring_expenses')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('is_active', true)
      .lte('next_due_date', thirtyDaysFromNow.toISOString().split('T')[0]);

    // Get expense by category (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    const { data: expensesByCategory, error: categoryError } = await supabase
      .from('payables')
      .select(`
        category,
        amount,
        expense_type:expense_types(category)
      `)
      .eq('organization_id', organizationId)
      .gte('created_at', sixMonthsAgo.toISOString());

    // Process category data
    const categoryMap = new Map<string, number>();
    let totalAmount = 0;

    if (expensesByCategory) {
      expensesByCategory.forEach(expense => {
        const category = expense.expense_type?.category || expense.category || 'uncategorized';
        const amount = expense.amount || 0;
        categoryMap.set(category, (categoryMap.get(category) || 0) + amount);
        totalAmount += amount;
      });
    }

    const expense_by_category = Array.from(categoryMap.entries()).map(([category, amount]) => ({
      category: category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      amount,
      percentage: totalAmount > 0 ? (amount / totalAmount) * 100 : 0
    }));

    const dashboardData: ExpenseDashboardData = {
      total_pending_approvals: pendingApprovals?.length || 0,
      total_overdue_payments: overduePayments?.length || 0,
      upcoming_recurring_expenses: upcomingRecurring?.length || 0,
      monthly_expense_trend: [], // Would need more complex query for trends
      expense_by_category,
      vendor_performance: [] // Would need more complex query for vendor performance
    };

    return { success: true, data: dashboardData };
  } catch (err: any) {
    console.error('Error in getExpenseDashboardData:', err);
    return { success: false, error: err.message };
  }
};
