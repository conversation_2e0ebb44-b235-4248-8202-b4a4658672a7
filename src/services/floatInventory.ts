import { supabase } from '../lib/supabase';

/**
 * Get or create the 'pcs' UoM for an organization
 * This ensures we always have a valid UoM for float inventory
 *
 * This function will:
 * 1. Try to find an existing 'pcs' UoM
 * 2. If not found, create a new 'pcs' UoM
 * 3. If creation fails, try to find any UoM and use that
 * 4. If all else fails, create a temporary UoM ID that can be used
 *
 * This ensures we ALWAYS have a valid UoM ID to use for float inventory
 */
export const getOrCreatePcsUom = async (organizationId: string): Promise<string> => {
  try {
    // Step 1: Try to get the existing 'pcs' UoM
    const { data: existingUom, error: getError } = await supabase
      .from('units_of_measure')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('code', 'pcs')
      .single();

    if (!getError && existingUom) {
      console.log('Found existing pcs UoM:', existingUom.id);
      return existingUom.id;
    }

    // Step 2: If not found, create a new 'pcs' UoM
    const { data: newUom, error: createError } = await supabase
      .from('units_of_measure')
      .insert({
        organization_id: organizationId,
        code: 'pcs',
        name: 'Pieces',
        is_base_unit: true,
        conversion_factor: 1
      })
      .select('id')
      .single();

    if (!createError && newUom) {
      console.log('Created new pcs UoM:', newUom.id);
      return newUom.id;
    }

    console.warn('Error creating pcs UoM:', createError);

    // Step 3: If creation fails, try to find any UoM
    const { data: anyUom, error: anyUomError } = await supabase
      .from('units_of_measure')
      .select('id')
      .eq('organization_id', organizationId)
      .limit(1)
      .single();

    if (!anyUomError && anyUom) {
      console.warn('Using fallback UoM:', anyUom.id);
      return anyUom.id;
    }

    console.warn('Could not find any UoM for organization:', organizationId);

    // Step 4: Last resort - create a temporary UoM ID
    // This is a UUID that can be used temporarily
    // It won't be a valid reference, but it will prevent null errors
    const tempUomId = crypto.randomUUID();
    console.warn('Using temporary UoM ID:', tempUomId);

    // Try to create a basic UoM with this ID
    try {
      await supabase
        .from('units_of_measure')
        .insert({
          id: tempUomId,
          organization_id: organizationId,
          code: 'pcs',
          name: 'Pieces',
          is_base_unit: true,
          conversion_factor: 1
        });

      console.log('Created emergency pcs UoM with custom ID:', tempUomId);
    } catch (err) {
      console.error('Failed to create emergency UoM, but will use the ID anyway:', err);
    }

    return tempUomId;
  } catch (err) {
    console.error('Error in getOrCreatePcsUom:', err);
    // Even in case of catastrophic failure, return a valid UUID
    // This ensures we never return null
    const emergencyUomId = crypto.randomUUID();
    console.warn('Using emergency UoM ID due to error:', emergencyUomId);
    return emergencyUomId;
  }
};

export type FloatInventory = {
  id: string;
  organization_id: string;
  product_id: string;
  quantity: number;
  sale_id: string;
  sale_item_id: string;
  resolved: boolean;
  created_at: string;
  resolved_at: string | null;
  product?: any;
  sale?: any;
};

/**
 * Get all float inventory items for an organization
 */
export const getFloatInventory = async (
  organizationId: string,
  options?: {
    resolved?: boolean;
    productId?: string;
    limit?: number;
    offset?: number;
  }
): Promise<{
  floatItems: FloatInventory[];
  count: number;
  error?: string;
}> => {
  try {
    const {
      resolved,
      productId,
      limit = 50,
      offset = 0,
    } = options || {};

    let query = supabase
      .from('float_inventory')
      .select(`
        *,
        product:product_id(*),
        sale:sale_id(id, invoice_number, sale_date)
      `, { count: 'exact' })
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters if provided
    if (resolved !== undefined) {
      query = query.eq('resolved', resolved);
    }

    if (productId) {
      query = query.eq('product_id', productId);
    }

    const { data, error, count } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return {
      floatItems: data || [],
      count: count || 0,
    };
  } catch (err: any) {
    console.error('Error fetching float inventory:', err);
    return {
      floatItems: [],
      count: 0,
      error: err.message,
    };
  }
};

/**
 * Get float inventory summary by product
 */
export const getFloatInventorySummary = async (
  organizationId: string
): Promise<{
  summary: {
    product_id: string;
    product_name: string;
    total_float_quantity: number;
    unresolved_float_quantity: number;
    oldest_float_date: string;
  }[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('get_float_inventory_summary', {
      p_organization_id: organizationId,
    });

    if (error) {
      throw new Error(error.message);
    }

    return {
      summary: data || [],
    };
  } catch (err: any) {
    console.error('Error fetching float inventory summary:', err);
    return {
      summary: [],
      error: err.message,
    };
  }
};

/**
 * Manually resolve float inventory
 */
export const resolveFloatInventory = async (
  floatId: string,
  userId: string,
  notes: string = 'Manually resolved'
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // Get the float inventory item
    const { data: floatItem, error: fetchError } = await supabase
      .from('float_inventory')
      .select('*')
      .eq('id', floatId)
      .single();

    if (fetchError || !floatItem) {
      throw new Error(fetchError?.message || 'Float inventory item not found');
    }

    // First, check if the product has a UoM associated with it
    let validUomId: string | null = null;

    try {
      console.log('Checking product UoMs for product:', floatItem.product_id);

      // Step 1: Check if the product has any UoMs associated with it
      console.log('Checking product UoMs with product_id:', floatItem.product_id, 'and organization_id:', floatItem.organization_id);
      const { data: productUoms, error: productUomError } = await supabase
        .from('product_uoms')
        .select(`
          id,
          uom_id,
          uom:units_of_measurement!uom_id (id, code, name)
        `)
        .eq('product_id', floatItem.product_id)
        .eq('organization_id', floatItem.organization_id);

      if (productUomError) {
        console.error('Error fetching product UoMs:', productUomError);
      }

      console.log('Product UoMs found:', productUoms?.length || 0);

      // If product has UoMs, try to find a 'pcs' UoM first
      if (productUoms && productUoms.length > 0) {
        // Look for 'pcs' UoM
        const pcsProductUom = productUoms.find(pu => pu.uom?.code === 'pcs');

        if (pcsProductUom && pcsProductUom.uom_id) {
          validUomId = pcsProductUom.uom_id;
          console.log('Found pcs UoM for product:', validUomId);
        } else {
          // If no 'pcs' UoM, use the first UoM
          validUomId = productUoms[0].uom_id;
          console.log('Using first product UoM:', validUomId);
        }
      }
      // If product has no UoMs, check for organization UoMs
      else {
        console.log('No product UoMs found, checking organization UoMs');

        // Step 2: Check if the organization has a 'pcs' UoM
        console.log('Checking for pcs UoM in organization:', floatItem.organization_id);
        const { data: pcsUoms, error: pcsError } = await supabase
          .from('units_of_measurement')
          .select('id, code, name')
          .eq('organization_id', floatItem.organization_id)
          .eq('code', 'pcs')
          .limit(1);

        if (!pcsError && pcsUoms && pcsUoms.length > 0) {
          validUomId = pcsUoms[0].id;
          console.log('Found organization pcs UoM:', validUomId);

          // Create a product UoM association
          try {
            console.log('Creating product UoM association');
            await supabase
              .from('product_uoms')
              .insert({
                product_id: floatItem.product_id,
                uom_id: validUomId,
                organization_id: floatItem.organization_id,
                conversion_factor: 1,
                is_default: true,
                is_purchasing_unit: true,
                is_selling_unit: true
              });
            console.log('Product UoM association created successfully');
          } catch (assocErr) {
            console.warn('Could not create product UoM association:', assocErr);
            // Continue anyway since we have a valid UoM ID
          }
        } else {
          console.log('No organization pcs UoM found, checking any organization UoM');

          // Step 3: Check if the organization has any UoM
          console.log('Checking for any UoM in organization:', floatItem.organization_id);
          const { data: anyUoms, error: anyError } = await supabase
            .from('units_of_measurement')
            .select('id, code, name')
            .eq('organization_id', floatItem.organization_id)
            .limit(1);

          if (!anyError && anyUoms && anyUoms.length > 0) {
            validUomId = anyUoms[0].id;
            console.log('Using organization UoM:', anyUoms[0].code, validUomId);

            // Create a product UoM association
            try {
              console.log('Creating product UoM association');
              await supabase
                .from('product_uoms')
                .insert({
                  product_id: floatItem.product_id,
                  uom_id: validUomId,
                  organization_id: floatItem.organization_id,
                  conversion_factor: 1,
                  is_default: true,
                  is_purchasing_unit: true,
                  is_selling_unit: true
                });
              console.log('Product UoM association created successfully');
            } catch (assocErr) {
              console.warn('Could not create product UoM association:', assocErr);
              // Continue anyway since we have a valid UoM ID
            }
          } else {
            console.log('No organization UoMs found, creating pcs UoM');

            // Step 4: Create a 'pcs' UoM for the organization
            try {
              console.log('Creating new pcs UoM in units_of_measurement table');
              const { data: newUom, error: createError } = await supabase
                .from('units_of_measurement')
                .insert({
                  organization_id: floatItem.organization_id,
                  code: 'pcs',
                  name: 'Pieces',
                  description: 'Individual units or items',
                  is_active: true
                })
                .select('id')
                .single();

              if (createError) {
                console.error('Error creating pcs UoM:', createError);
                throw new Error(`Failed to create UoM: ${createError.message}`);
              }

              if (!newUom) {
                console.error('No UoM returned after creation');
                throw new Error('No UoM returned after creation');
              }

              validUomId = newUom.id;
              console.log('Created new pcs UoM:', validUomId);

              // Create a product UoM association
              try {
                console.log('Creating product UoM association');
                await supabase
                  .from('product_uoms')
                  .insert({
                    product_id: floatItem.product_id,
                    uom_id: validUomId,
                    organization_id: floatItem.organization_id,
                    conversion_factor: 1,
                    is_default: true,
                    is_purchasing_unit: true,
                    is_selling_unit: true
                  });
                console.log('Product UoM association created successfully');
              } catch (assocErr) {
                console.warn('Could not create product UoM association:', assocErr);
                // Continue anyway since we have a valid UoM ID
              }
            } catch (createErr) {
              console.error('Error creating UoM:', createErr);

              // Step 5: Last resort - find any UoM in the system
              console.log('Trying to find any UoM in the system');
              const { data: systemUoms, error: systemError } = await supabase
                .from('units_of_measurement')
                .select('id')
                .limit(1);

              if (!systemError && systemUoms && systemUoms.length > 0) {
                validUomId = systemUoms[0].id;
                console.log('Using system UoM as last resort:', validUomId);
              } else {
                throw new Error('Could not find or create any UoM in the system');
              }
            }
          }
        }
      }
    } catch (err: any) {
      console.error('Error in UoM handling:', err);
      throw new Error(`UoM error: ${err.message}`);
    }

    if (!validUomId) {
      console.error('No valid UoM ID found after all attempts');
      throw new Error('Could not find or create a valid UoM');
    }

    console.log('Final valid UoM ID to use:', validUomId);

    // Initialize transaction ID variable
    let transactionId: string | null = null;

    // First, check if the inventory_transactions table exists
    console.log('Checking if inventory_transactions table exists');
    try {
      // Check if the inventory_transactions table has a conversion_factor column
      const { data: tableInfo, error: tableError } = await supabase
        .from('inventory_transactions')
        .select('*')
        .limit(1);

      if (tableError) {
        console.error('Error checking inventory_transactions table:', tableError);
        console.warn('Inventory transactions table might not exist, skipping transaction creation');
        // Don't throw an error, just continue to mark the float as resolved
        return;
      }

      // Determine if conversion_factor column exists
      const hasConversionFactor = tableInfo && tableInfo.length > 0 && 'conversion_factor' in tableInfo[0];

      // Create the transaction data with or without conversion_factor based on schema
      const transactionData = {
        organization_id: floatItem.organization_id,
        product_id: floatItem.product_id,
        transaction_type: 'adjustment',
        quantity: floatItem.quantity,
        notes: `Manual resolution of float inventory: ${notes}`,
        created_by: userId,
        uom_id: validUomId, // Use the valid UoM ID we found or created
        ...(hasConversionFactor ? { conversion_factor: 1 } : {}) // Only include if column exists
      };

      console.log('Creating inventory transaction with data:', transactionData);

      // Create an adjustment transaction to resolve the float
      const { data: createdTransaction, error: transactionError } = await supabase
        .from('inventory_transactions')
        .insert(transactionData)
        .select('id')
        .single();

      if (transactionError) {
        console.error('Error creating inventory transaction:', transactionError);
        throw new Error(`Error creating inventory transaction: ${transactionError.message}`);
      }

      console.log('Inventory transaction created successfully');

      // Store the transaction ID for later use
      transactionId = createdTransaction?.id || null;
    } catch (err: any) {
      console.error('Error in inventory transaction creation:', err);

      // Mark the float as resolved anyway to prevent further errors
      console.log('Marking float as resolved despite transaction error');
      // First check if the float_inventory table has a notes column
      const { data: floatColumns, error: columnsError } = await supabase
        .from('float_inventory')
        .select('*')
        .limit(1);

      const hasNotesColumn = floatColumns && floatColumns.length > 0 && 'notes' in floatColumns[0];

      // Check for additional reporting columns
      const hasResolutionType = floatColumns && floatColumns.length > 0 && 'resolution_type' in floatColumns[0];
      const hasResolutionNotes = floatColumns && floatColumns.length > 0 && 'resolution_notes' in floatColumns[0];
      const hasResolvedBy = floatColumns && floatColumns.length > 0 && 'resolved_by' in floatColumns[0];

      // Update with all available reporting columns
      const { error: updateError } = await supabase
        .from('float_inventory')
        .update({
          resolved: true,
          resolved_at: new Date().toISOString(),
          ...(hasNotesColumn ? { notes: `Manually resolved with error: ${err.message}` } : {}),
          ...(hasResolutionType ? { resolution_type: 'manual' } : {}),
          ...(hasResolutionNotes ? { resolution_notes: `Manual resolution attempted but failed: ${err.message}` } : {}),
          ...(hasResolvedBy ? { resolved_by: userId } : {})
        })
        .eq('id', floatId);

      if (updateError) {
        console.error('Error updating float inventory:', updateError);
        throw new Error(`Error updating float inventory: ${updateError.message}`);
      }

      return { success: true, warning: `Float marked as resolved, but transaction failed: ${err.message}` };
    }

    // Check if the float_inventory table has additional reporting columns
    const { data: floatColumns, error: columnsError } = await supabase
      .from('float_inventory')
      .select('*')
      .limit(1);

    const hasNotesColumn = floatColumns && floatColumns.length > 0 && 'notes' in floatColumns[0];
    const hasResolutionType = floatColumns && floatColumns.length > 0 && 'resolution_type' in floatColumns[0];
    const hasResolutionNotes = floatColumns && floatColumns.length > 0 && 'resolution_notes' in floatColumns[0];
    const hasResolutionTransactionId = floatColumns && floatColumns.length > 0 && 'resolution_transaction_id' in floatColumns[0];
    const hasResolvedBy = floatColumns && floatColumns.length > 0 && 'resolved_by' in floatColumns[0];

    console.log('Column availability:', {
      hasNotesColumn,
      hasResolutionType,
      hasResolutionNotes,
      hasResolutionTransactionId,
      hasResolvedBy
    });

    // Mark the float as resolved with all available reporting columns
    const { error: updateError } = await supabase
      .from('float_inventory')
      .update({
        resolved: true,
        resolved_at: new Date().toISOString(),
        ...(hasNotesColumn ? { notes: `Manually resolved: ${notes}` } : {}),
        ...(hasResolutionType ? { resolution_type: 'manual' } : {}),
        ...(hasResolutionNotes ? { resolution_notes: notes } : {}),
        ...(hasResolutionTransactionId ? { resolution_transaction_id: transactionId } : {}),
        ...(hasResolvedBy ? { resolved_by: userId } : {})
      })
      .eq('id', floatId);

    if (updateError) {
      throw new Error(`Error updating float inventory: ${updateError.message}`);
    }

    return { success: true };
  } catch (err: any) {
    console.error('Error resolving float inventory:', err);
    return {
      success: false,
      error: err.message,
    };
  }
};

/**
 * Get organization inventory settings
 */
export const getInventorySettings = async (
  organizationId: string
): Promise<{
  settings: FloatInventorySettings;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('organization_settings')
      .select('inventory_settings')
      .eq('organization_id', organizationId)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Default settings if none exist
    const defaultSettings = {
      allow_negative_inventory: true,
      warn_on_low_inventory: true,
      auto_create_purchase_requests: true,
    };

    return {
      settings: data?.inventory_settings || defaultSettings,
    };
  } catch (err: any) {
    console.error('Error fetching inventory settings:', err);
    return {
      settings: {
        allow_negative_inventory: true,
        warn_on_low_inventory: true,
        auto_create_purchase_requests: true,
      },
      error: err.message,
    };
  }
};

/**
 * Update organization inventory settings
 */
export type FloatInventorySettings = {
  allow_negative_inventory: boolean;
  warn_on_low_inventory: boolean;
  auto_create_purchase_requests: boolean;
};

export type FloatInventoryReport = {
  id: string;
  product_id: string;
  product_name: string;
  product_sku: string;
  quantity: number;
  sale_id: string;
  sale_number: string;
  sale_date: string;
  customer_name: string;
  resolved: boolean;
  created_at: string;
  resolved_at: string | null;
  resolution_type: string | null;
  resolution_notes: string | null;
  days_to_resolve: number | null;
  days_unresolved: number | null;
};

export type FloatInventoryDetail = {
  id: string;
  product_id: string;
  product_name: string;
  product_sku: string;
  quantity: number;
  sale_id: string;
  sale_number: string;
  sale_date: string;
  customer_id: string | null;
  customer_name: string | null;
  resolved: boolean;
  created_at: string;
  resolved_at: string | null;
  resolution_type: string | null;
  resolution_notes: string | null;
  resolved_by_id: string | null;
  resolved_by_name: string | null;
  days_to_resolve: number | null;
  days_unresolved: number | null;
};

export type FloatInventorySummaryByProduct = {
  product_id: string;
  product_name: string;
  product_sku: string;
  total_float_quantity: number;
  unresolved_float_quantity: number;
  resolved_float_quantity: number;
  resolution_rate: number;
  avg_days_to_resolve: number | null;
  oldest_unresolved_date: string | null;
  oldest_unresolved_days: number | null;
};

export type FloatInventorySummaryByDate = {
  date_group: string;
  total_float_quantity: number;
  resolved_float_quantity: number;
  unresolved_float_quantity: number;
  resolution_rate: number;
};

export const updateInventorySettings = async (
  organizationId: string,
  settings: FloatInventorySettings
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('organization_settings')
      .update({
        inventory_settings: settings,
      })
      .eq('organization_id', organizationId);

    if (error) {
      throw new Error(error.message);
    }

    return { success: true };
  } catch (err: any) {
    console.error('Error updating inventory settings:', err);
    return {
      success: false,
      error: err.message,
    };
  }
};

/**
 * Get a single float inventory item by ID with detailed information
 */
export const getFloatInventoryById = async (
  organizationId: string,
  floatId: string
): Promise<{
  floatItem: FloatInventoryDetail | null;
  error?: string;
}> => {
  try {
    // First check if the resolved_by column exists by checking the table structure
    let hasResolvedByColumn = false;
    try {
      const { data: tableCheck } = await supabase
        .from('float_inventory')
        .select('resolved_by')
        .limit(1);
      hasResolvedByColumn = true;
    } catch (columnError: any) {
      console.log('resolved_by column not available:', columnError.message);
      hasResolvedByColumn = false;
    }

    let data, error;

    if (hasResolvedByColumn) {
      // Try with resolved_by column and relationship
      const result = await supabase
        .from('float_inventory')
        .select(`
          id,
          product_id,
          quantity,
          sale_id,
          resolved,
          created_at,
          resolved_at,
          resolution_type,
          resolution_notes,
          notes,
          resolved_by,
          product:product_id(name, sku),
          sale:sale_id(
            id,
            invoice_number,
            sale_date,
            customer_id,
            customer:customer_id(id, name)
          )
        `)
        .eq('organization_id', organizationId)
        .eq('id', floatId)
        .single();

      data = result.data;
      error = result.error;
    } else {
      // Fallback query without resolved_by column
      console.log('Using fallback query without resolved_by column');
      const result = await supabase
        .from('float_inventory')
        .select(`
          id,
          product_id,
          quantity,
          sale_id,
          resolved,
          created_at,
          resolved_at,
          resolution_type,
          resolution_notes,
          notes,
          product:product_id(name, sku),
          sale:sale_id(
            id,
            invoice_number,
            sale_date,
            customer_id,
            customer:customer_id(id, name)
          )
        `)
        .eq('organization_id', organizationId)
        .eq('id', floatId)
        .single();

      data = result.data;
      error = result.error;
    }

    if (error) {
      throw new Error(error.message);
    }

    if (!data) {
      return {
        floatItem: null,
        error: 'Float inventory item not found',
      };
    }

    // Transform the data to match the expected format
    const createdAt = new Date(data.created_at);
    const resolvedAt = data.resolved_at ? new Date(data.resolved_at) : null;
    const now = new Date();

    const floatItem: FloatInventoryDetail = {
      id: data.id,
      product_id: data.product_id,
      product_name: data.product?.name || 'Unknown Product',
      product_sku: data.product?.sku || '',
      quantity: data.quantity,
      sale_id: data.sale_id,
      sale_number: data.sale?.invoice_number || 'N/A',
      sale_date: data.sale?.sale_date || data.created_at,
      customer_id: data.sale?.customer_id || null,
      customer_name: data.sale?.customer?.name || null,
      resolved: data.resolved,
      created_at: data.created_at,
      resolved_at: data.resolved_at,
      resolution_type: data.resolution_type,
      resolution_notes: data.resolution_notes || data.notes,
      resolved_by_id: (hasResolvedByColumn && data.resolved_by) ? data.resolved_by : null,
      resolved_by_name: (hasResolvedByColumn && data.resolved_by) ? 'User' : null,
      days_to_resolve: data.resolved && resolvedAt
        ? Math.floor((resolvedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24))
        : null,
      days_unresolved: !data.resolved
        ? Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24))
        : null,
    };

    return {
      floatItem,
    };
  } catch (err: any) {
    console.error('Error fetching float inventory item:', err);
    return {
      floatItem: null,
      error: err.message,
    };
  }
};

/**
 * Get detailed float inventory report
 */
export const getFloatInventoryReport = async (
  organizationId: string,
  options?: {
    startDate?: string;
    endDate?: string;
    productId?: string;
    resolved?: boolean;
  }
): Promise<{
  report: FloatInventoryReport[];
  error?: string;
}> => {
  try {
    const { startDate, endDate, productId, resolved } = options || {};

    // Try the database function first
    const { data, error } = await supabase.rpc('get_float_inventory_report', {
      p_organization_id: organizationId,
      p_start_date: startDate || null,
      p_end_date: endDate || null,
      p_product_id: productId || null,
      p_resolved: resolved === undefined ? null : resolved
    });

    if (error) {
      // If the database function fails, fall back to a direct query
      console.warn('Database function failed, falling back to direct query:', error.message);

      let query = supabase
        .from('float_inventory')
        .select(`
          id,
          product_id,
          quantity,
          sale_id,
          resolved,
          created_at,
          resolved_at,
          resolution_type,
          resolution_notes,
          notes,
          product:product_id(name, sku),
          sale:sale_id(id, invoice_number, sale_date, customer:customer_id(name))
        `)
        .eq('organization_id', organizationId)
        .order('created_at', { ascending: false });

      // Apply filters
      if (startDate) query = query.gte('created_at', startDate);
      if (endDate) query = query.lte('created_at', endDate);
      if (productId) query = query.eq('product_id', productId);
      if (resolved !== undefined) query = query.eq('resolved', resolved);

      const { data: fallbackData, error: fallbackError } = await query;

      if (fallbackError) {
        throw new Error(fallbackError.message);
      }

      // Transform the data to match the expected format
      const transformedData = (fallbackData || []).map((item: any) => {
        const createdAt = new Date(item.created_at);
        const resolvedAt = item.resolved_at ? new Date(item.resolved_at) : null;
        const now = new Date();

        return {
          id: item.id,
          product_id: item.product_id,
          product_name: item.product?.name || 'Unknown Product',
          product_sku: item.product?.sku || '',
          quantity: item.quantity,
          sale_id: item.sale_id,
          sale_number: item.sale?.invoice_number || 'N/A',
          sale_date: item.sale?.sale_date || item.created_at,
          customer_name: item.sale?.customer?.name || 'Walk-in Customer',
          resolved: item.resolved,
          created_at: item.created_at,
          resolved_at: item.resolved_at,
          resolution_type: item.resolution_type,
          resolution_notes: item.resolution_notes || item.notes,
          days_to_resolve: item.resolved && resolvedAt
            ? Math.floor((resolvedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24))
            : null,
          days_unresolved: !item.resolved
            ? Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24))
            : null,
        };
      });

      return {
        report: transformedData,
      };
    }

    return {
      report: data || [],
    };
  } catch (err: any) {
    console.error('Error fetching float inventory report:', err);
    return {
      report: [],
      error: err.message,
    };
  }
};

/**
 * Get float inventory summary by product
 */
export const getFloatInventorySummaryByProduct = async (
  organizationId: string,
  options?: {
    startDate?: string;
    endDate?: string;
  }
): Promise<{
  summary: FloatInventorySummaryByProduct[];
  error?: string;
}> => {
  try {
    const { startDate, endDate } = options || {};

    const { data, error } = await supabase.rpc('get_float_inventory_summary_by_product', {
      p_organization_id: organizationId,
      p_start_date: startDate || null,
      p_end_date: endDate || null
    });

    if (error) {
      console.warn('Database function failed, returning empty summary:', error.message);
      return {
        summary: [],
        error: `Database function not available: ${error.message}`,
      };
    }

    return {
      summary: data || [],
    };
  } catch (err: any) {
    console.error('Error fetching float inventory summary by product:', err);
    return {
      summary: [],
      error: err.message,
    };
  }
};

/**
 * Get float inventory summary by date
 */
export const getFloatInventorySummaryByDate = async (
  organizationId: string,
  options?: {
    startDate?: string;
    endDate?: string;
  }
): Promise<{
  summary: FloatInventorySummaryByDate[];
  error?: string;
}> => {
  try {
    const { startDate, endDate } = options || {};

    const { data, error } = await supabase.rpc('get_float_inventory_summary_by_date', {
      p_organization_id: organizationId,
      p_start_date: startDate || null,
      p_end_date: endDate || null
    });

    if (error) {
      console.warn('Database function failed, returning empty summary:', error.message);
      return {
        summary: [],
        error: `Database function not available: ${error.message}`,
      };
    }

    return {
      summary: data || [],
    };
  } catch (err: any) {
    console.error('Error fetching float inventory summary by date:', err);
    return {
      summary: [],
      error: err.message,
    };
  }
};
