import { supabase } from '../lib/supabase';
import { getGovernmentContributionsReport } from './payrollReporting';
import { PayrollReportOptions } from '../types/payroll';

/**
 * Interface for batch processing options
 */
export interface BatchProcessingOptions {
  organizationId: string;
  month: number;
  year: number;
  reportTypes: ('sss' | 'philhealth' | 'pagibig')[];
  format: 'csv' | 'pdf' | 'excel';
}

/**
 * Interface for batch processing result
 */
export interface BatchProcessingResult {
  success: boolean;
  reports: {
    type: string;
    url?: string;
    error?: string;
  }[];
  error?: string;
}

/**
 * Process government contribution reports in batch
 * @param options Batch processing options
 * @returns Batch processing result
 */
export const batchProcessGovernmentReports = async (
  options: BatchProcessingOptions
): Promise<BatchProcessingResult> => {
  try {
    const { organizationId, month, year, reportTypes, format } = options;
    
    // Create date range for the specified month
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0); // Last day of the month
    
    // Get organization details
    const { data: orgData, error: orgError } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', organizationId)
      .single();
    
    if (orgError) {
      throw new Error(`Error fetching organization details: ${orgError.message}`);
    }
    
    // Get organization government IDs
    const { data: orgGovData, error: orgGovError } = await supabase
      .from('organization_government_ids')
      .select('*')
      .eq('organization_id', organizationId)
      .single();
    
    if (orgGovError && orgGovError.code !== 'PGRST116') { // Ignore not found error
      throw new Error(`Error fetching organization government IDs: ${orgGovError.message}`);
    }
    
    // Fetch contribution data
    const { data, error } = await getGovernmentContributionsReport({
      organizationId,
      startDate,
      endDate
    });
    
    if (error) {
      throw new Error(`Error fetching government contributions data: ${error}`);
    }
    
    if (!data || !data.items || data.items.length === 0) {
      return {
        success: false,
        reports: [],
        error: 'No contribution data found for the specified period'
      };
    }
    
    // Process each report type
    const reports = await Promise.all(
      reportTypes.map(async (type) => {
        try {
          // In a real implementation, this would generate the actual report files
          // For now, we'll just simulate the process
          
          // Validate required data
          if (type === 'sss' && (!orgGovData?.sss_number)) {
            return {
              type,
              error: 'Missing SSS employer number'
            };
          }
          
          if (type === 'philhealth' && (!orgGovData?.philhealth_number)) {
            return {
              type,
              error: 'Missing PhilHealth employer number'
            };
          }
          
          if (type === 'pagibig' && (!orgGovData?.pagibig_number)) {
            return {
              type,
              error: 'Missing Pag-IBIG employer number'
            };
          }
          
          // Simulate file generation
          const fileName = `${type}_report_${year}_${month.toString().padStart(2, '0')}.${format}`;
          
          // In a real implementation, we would:
          // 1. Generate the report file
          // 2. Upload it to storage
          // 3. Return the URL
          
          // For now, just return a mock URL
          return {
            type,
            url: `/reports/${fileName}`
          };
        } catch (err: any) {
          return {
            type,
            error: `Error processing ${type} report: ${err.message}`
          };
        }
      })
    );
    
    // Check if all reports were successful
    const success = reports.every(report => !report.error);
    
    return {
      success,
      reports
    };
  } catch (err: any) {
    console.error('Error in batch processing government reports:', err);
    return {
      success: false,
      reports: [],
      error: err.message
    };
  }
};

/**
 * Validate government contribution data
 * @param organizationId Organization ID
 * @param reportType Report type
 * @returns Validation result
 */
export const validateGovernmentContributionData = async (
  organizationId: string,
  reportType: 'sss' | 'philhealth' | 'pagibig'
): Promise<{
  valid: boolean;
  missingEmployees?: any[];
  missingOrganizationData?: string[];
  error?: string;
}> => {
  try {
    // Get employees with missing government IDs
    let query = supabase
      .from('employees')
      .select('id, first_name, last_name, employee_number');
    
    if (reportType === 'sss') {
      query = query.is('sss_number', null);
    } else if (reportType === 'philhealth') {
      query = query.is('philhealth_number', null);
    } else if (reportType === 'pagibig') {
      query = query.is('pagibig_number', null);
    }
    
    query = query.eq('organization_id', organizationId);
    
    const { data: missingEmployees, error: empError } = await query;
    
    if (empError) {
      throw new Error(`Error validating employee data: ${empError.message}`);
    }
    
    // Get organization government IDs
    const { data: orgGovData, error: orgGovError } = await supabase
      .from('organization_government_ids')
      .select('*')
      .eq('organization_id', organizationId)
      .single();
    
    if (orgGovError && orgGovError.code !== 'PGRST116') { // Ignore not found error
      throw new Error(`Error fetching organization government IDs: ${orgGovError.message}`);
    }
    
    // Check for missing organization data
    const missingOrganizationData: string[] = [];
    
    if (reportType === 'sss' && (!orgGovData?.sss_number)) {
      missingOrganizationData.push('SSS Employer Number');
    }
    
    if (reportType === 'philhealth' && (!orgGovData?.philhealth_number)) {
      missingOrganizationData.push('PhilHealth Employer Number');
    }
    
    if (reportType === 'pagibig' && (!orgGovData?.pagibig_number)) {
      missingOrganizationData.push('Pag-IBIG Employer Number');
    }
    
    return {
      valid: missingEmployees?.length === 0 && missingOrganizationData.length === 0,
      missingEmployees,
      missingOrganizationData
    };
  } catch (err: any) {
    console.error('Error validating government contribution data:', err);
    return {
      valid: false,
      error: err.message
    };
  }
};

/**
 * Get organization government IDs
 * @param organizationId Organization ID
 * @returns Organization government IDs
 */
export const getOrganizationGovernmentIds = async (
  organizationId: string
): Promise<{
  data?: {
    sssNumber: string;
    philHealthNumber: string;
    pagibigNumber: string;
    tinNumber: string;
  };
  error?: string;
}> => {
  try {
    // Get organization government IDs
    const { data, error } = await supabase
      .from('organization_government_ids')
      .select('*')
      .eq('organization_id', organizationId)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') { // Not found
        return {
          data: {
            sssNumber: '',
            philHealthNumber: '',
            pagibigNumber: '',
            tinNumber: ''
          }
        };
      }
      throw new Error(error.message);
    }
    
    return {
      data: {
        sssNumber: data.sss_number || '',
        philHealthNumber: data.philhealth_number || '',
        pagibigNumber: data.pagibig_number || '',
        tinNumber: data.tin_number || ''
      }
    };
  } catch (err: any) {
    console.error('Error getting organization government IDs:', err);
    return {
      error: err.message
    };
  }
};
