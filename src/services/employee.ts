import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';

export type Employee = Database['public']['Tables']['employees']['Row'];
export type Department = Database['public']['Tables']['departments']['Row'];
export type JobPosition = Database['public']['Tables']['job_positions']['Row'];
export type EmploymentType = Database['public']['Tables']['employment_types']['Row'];
export type EmployeeGovernmentId = Database['public']['Tables']['employee_government_ids']['Row'];
export type EmployeeSalary = Database['public']['Tables']['employee_salary']['Row'];
export type EmployeeDocument = Database['public']['Tables']['employee_documents']['Row'];
export type EmployeeDependent = Database['public']['Tables']['employee_dependents']['Row'];

export interface EmployeeWithDetails extends Employee {
  department?: Department;
  position?: JobPosition;
  employment_type?: EmploymentType;
  government_ids?: EmployeeGovernmentId[];
}

/**
 * Get all employees for an organization
 */
export const getEmployees = async (
  organizationId: string,
  options?: {
    searchQuery?: string;
    departmentId?: string;
    positionId?: string;
    employmentTypeId?: string;
    status?: string;
    isActive?: boolean;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  },
): Promise<{
  employees: EmployeeWithDetails[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('employees')
      .select(
        `
        *,
        department:department_id (
          id,
          name,
          description
        ),
        position:position_id (
          id,
          title,
          description
        ),
        employment_type:employment_type_id (
          id,
          name,
          description
        ),
        government_ids:employee_government_ids (*)
      `,
        { count: 'exact' },
      )
      .eq('organization_id', organizationId);

    // Apply filters
    if (options?.departmentId) {
      query = query.eq('department_id', options.departmentId);
    }

    if (options?.positionId) {
      query = query.eq('position_id', options.positionId);
    }

    if (options?.employmentTypeId) {
      query = query.eq('employment_type_id', options.employmentTypeId);
    }

    if (options?.status) {
      query = query.eq('status', options.status);
    }

    if (options?.isActive !== undefined) {
      query = query.eq('is_active', options.isActive);
    }

    if (options?.searchQuery) {
      query = query.or(
        `first_name.ilike.%${options.searchQuery}%,last_name.ilike.%${options.searchQuery}%,email.ilike.%${options.searchQuery}%,employee_number.ilike.%${options.searchQuery}%`,
      );
    }

    // Apply sorting
    if (options?.sortBy) {
      query = query.order(options.sortBy, { ascending: options.sortOrder === 'asc' });
    } else {
      query = query.order('last_name', { ascending: true });
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching employees:', error);
      return {
        employees: [],
        count: 0,
        error: error.message,
      };
    }

    // For employees linked to user accounts, fetch their profile images
    const employeesWithUserImages = await Promise.all(
      data.map(async (employee) => {
        if (employee.user_id) {
          try {
            const { data: profileData, error: profileError } = await supabase
              .from('profiles')
              .select('avatar_url')
              .eq('id', employee.user_id)
              .single();

            if (!profileError && profileData && profileData.avatar_url) {
              // Use the user's profile image
              return {
                ...employee,
                profile_image_url: profileData.avatar_url
              };
            }
          } catch (profileError) {
            console.error('Error fetching user profile:', profileError);
            // Continue without updating the profile image
          }
        }
        return employee;
      })
    );

    return {
      employees: employeesWithUserImages as EmployeeWithDetails[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error in getEmployees:', error);
    return {
      employees: [],
      count: 0,
      error: error.message,
    };
  }
};

/**
 * Get a single employee by ID
 */
export const getEmployeeById = async (
  organizationId: string,
  employeeId: string,
): Promise<{
  employee?: EmployeeWithDetails;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('employees')
      .select(
        `
        *,
        department:department_id (
          id,
          name,
          description
        ),
        position:position_id (
          id,
          title,
          description
        ),
        employment_type:employment_type_id (
          id,
          name,
          description
        ),
        government_ids:employee_government_ids (*)
      `,
      )
      .eq('organization_id', organizationId)
      .eq('id', employeeId)
      .single();

    if (error) {
      console.error('Error fetching employee:', error);
      return {
        error: error.message,
      };
    }

    // If the employee is linked to a user account, fetch the user's profile image
    if (data.user_id) {
      try {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('avatar_url')
          .eq('id', data.user_id)
          .single();

        if (!profileError && profileData && profileData.avatar_url) {
          // Use the user's profile image
          data.profile_image_url = profileData.avatar_url;
        }
      } catch (profileError) {
        console.error('Error fetching user profile:', profileError);
        // Continue without updating the profile image
      }
    }

    return {
      employee: data as EmployeeWithDetails,
    };
  } catch (error: any) {
    console.error('Error in getEmployeeById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new employee
 */
export const createEmployee = async (
  organizationId: string,
  employee: Omit<Employee, 'id' | 'organization_id' | 'created_at' | 'updated_at'>,
  governmentIds?: Omit<EmployeeGovernmentId, 'id' | 'employee_id' | 'created_at' | 'updated_at'>,
): Promise<{
  employee?: EmployeeWithDetails;
  error?: string;
}> => {
  try {
    // Clean up the employee data to ensure UUID fields are not empty strings
    const cleanedEmployee = { ...employee };

    // Convert empty strings to null for UUID fields
    if (cleanedEmployee.department_id === '') cleanedEmployee.department_id = null;
    if (cleanedEmployee.position_id === '') cleanedEmployee.position_id = null;
    if (cleanedEmployee.employment_type_id === '') cleanedEmployee.employment_type_id = null;

    // Start a transaction
    const { data, error } = await supabase
      .from('employees')
      .insert({
        ...cleanedEmployee,
        organization_id: organizationId,
      })
      .select(
        `
        *,
        department:department_id (
          id,
          name,
          description
        ),
        position:position_id (
          id,
          title,
          description
        ),
        employment_type:employment_type_id (
          id,
          name,
          description
        )
      `,
      )
      .single();

    if (error) {
      console.error('Error creating employee:', error);
      return {
        error: error.message,
      };
    }

    // If government IDs are provided, create them
    if (governmentIds && data) {
      // Clean up government IDs to ensure we're only sending valid fields
      const cleanedGovIds: Record<string, any> = {
        employee_id: data.id
      };

      // Only include valid fields for the employee_government_ids table
      if (governmentIds.sss_number !== undefined) cleanedGovIds.sss_number = governmentIds.sss_number;
      if (governmentIds.philhealth_number !== undefined) cleanedGovIds.philhealth_number = governmentIds.philhealth_number;
      if (governmentIds.pagibig_number !== undefined) cleanedGovIds.pagibig_number = governmentIds.pagibig_number;
      if (governmentIds.tin_number !== undefined) cleanedGovIds.tin_number = governmentIds.tin_number;

      const { error: govIdError } = await supabase
        .from('employee_government_ids')
        .insert(cleanedGovIds);

      if (govIdError) {
        console.error('Error creating government IDs:', govIdError);
        return {
          employee: data as EmployeeWithDetails,
          error: 'Employee created but government IDs could not be saved: ' + govIdError.message,
        };
      }
    }

    return {
      employee: data as EmployeeWithDetails,
    };
  } catch (error: any) {
    console.error('Error in createEmployee:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update an existing employee
 */
export const updateEmployee = async (
  organizationId: string,
  employeeId: string,
  updates: Partial<Omit<Employee, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>,
  governmentIds?: Partial<Omit<EmployeeGovernmentId, 'id' | 'employee_id' | 'created_at' | 'updated_at'>>,
): Promise<{
  employee?: EmployeeWithDetails;
  error?: string;
}> => {
  try {
    // Clean up the updates to ensure UUID fields are not empty strings
    const cleanedUpdates = { ...updates };

    // Convert empty strings to null for UUID fields
    if (cleanedUpdates.department_id === '') cleanedUpdates.department_id = null;
    if (cleanedUpdates.position_id === '') cleanedUpdates.position_id = null;
    if (cleanedUpdates.employment_type_id === '') cleanedUpdates.employment_type_id = null;

    // Update employee
    const { data, error } = await supabase
      .from('employees')
      .update(cleanedUpdates)
      .eq('organization_id', organizationId)
      .eq('id', employeeId)
      .select(
        `
        *,
        department:department_id (
          id,
          name,
          description
        ),
        position:position_id (
          id,
          title,
          description
        ),
        employment_type:employment_type_id (
          id,
          name,
          description
        ),
        government_ids:employee_government_ids (*)
      `,
      )
      .single();

    if (error) {
      console.error('Error updating employee:', error);
      return {
        error: error.message,
      };
    }

    // If government IDs are provided, update them
    if (governmentIds && data) {
      // Check if government IDs exist
      const { data: existingGovIds, error: checkError } = await supabase
        .from('employee_government_ids')
        .select('id')
        .eq('employee_id', employeeId)
        .maybeSingle();

      if (checkError) {
        console.error('Error checking government IDs:', checkError);
        return {
          employee: data as EmployeeWithDetails,
          error: 'Employee updated but error checking government IDs: ' + checkError.message,
        };
      }

      if (existingGovIds) {
        // Clean up government IDs to ensure we're only sending valid fields
        const cleanedGovIds: Record<string, any> = {};

        // Include all fields to ensure they're updated properly
        cleanedGovIds.sss_number = governmentIds.sss_number;
        cleanedGovIds.philhealth_number = governmentIds.philhealth_number;
        cleanedGovIds.pagibig_number = governmentIds.pagibig_number;
        cleanedGovIds.tin_number = governmentIds.tin_number;

        // Update existing government IDs
        const { data: updatedGovIds, error: updateError } = await supabase
          .from('employee_government_ids')
          .update(cleanedGovIds)
          .eq('employee_id', employeeId)
          .select('*');

        if (updateError) {
          console.error('Error updating government IDs:', updateError);
          return {
            employee: data as EmployeeWithDetails,
            error: 'Employee updated but government IDs could not be updated: ' + updateError.message,
          };
        }
      } else {
                // Clean up government IDs to ensure we're only sending valid fields
        const cleanedGovIds: Record<string, any> = {
          employee_id: employeeId
        };

        // Include all fields to ensure they're created properly
        cleanedGovIds.sss_number = governmentIds.sss_number;
        cleanedGovIds.philhealth_number = governmentIds.philhealth_number;
        cleanedGovIds.pagibig_number = governmentIds.pagibig_number;
        cleanedGovIds.tin_number = governmentIds.tin_number;

        // Create new government IDs
        const { data: insertedGovIds, error: insertError } = await supabase
          .from('employee_government_ids')
          .insert(cleanedGovIds)
          .select('*');

        if (insertError) {
          console.error('Error creating government IDs:', insertError);
          return {
            employee: data as EmployeeWithDetails,
            error: 'Employee updated but government IDs could not be created: ' + insertError.message,
          };
        }
      }
    }

    return {
      employee: data as EmployeeWithDetails,
    };
  } catch (error: any) {
    console.error('Error in updateEmployee:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Delete an employee
 */
export const deleteEmployee = async (
  organizationId: string,
  employeeId: string,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('employees')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', employeeId);

    if (error) {
      console.error('Error deleting employee:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteEmployee:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
