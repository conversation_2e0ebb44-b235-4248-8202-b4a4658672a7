/**
 * Face Recognition Service
 * Handles database operations for facial recognition system
 */

import { supabase } from '../lib/supabase';
import { descriptorToArray, arrayToDescriptor } from '../utils/faceRecognition';
import { createTimeEntry as createExistingTimeEntry } from './timeEntry';
import { TimeEntry, TimeEntryStatus } from '../types/payroll';
import { calculateTimeEntryHours } from '../utils/timeCalculations';

export interface FaceDescriptor {
  id: string;
  employee_id: string;
  organization_id: string;
  descriptor_data: number[];
  confidence_threshold: number;
  enrollment_quality_score: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EmployeePin {
  id: string;
  employee_id: string;
  organization_id: string;
  pin_hash: string;
  salt: string;
  is_active: boolean;
  failed_attempts: number;
  locked_until?: string;
  created_at: string;
  updated_at: string;
}

export interface TimeTrackingSettings {
  id: string;
  organization_id: string;
  facial_recognition_enabled: boolean;
  pin_fallback_enabled: boolean;
  confidence_threshold: number;
  max_pin_attempts: number;
  pin_lockout_duration: number;
  created_at: string;
  updated_at: string;
}

export interface RecognitionLog {
  id: string;
  organization_id: string;
  employee_id?: string;
  recognition_type: 'clock_in' | 'clock_out' | 'enrollment' | 'verification';
  method_used: 'facial_recognition' | 'pin' | 'manual' | 'qr_code' | 'badge';
  success: boolean;
  confidence_score?: number;
  error_message?: string;
  created_at: string;
}

/**
 * Enroll employee face descriptor
 */
export const enrollEmployeeFace = async (
  organizationId: string,
  employeeId: string,
  descriptor: Float32Array,
  qualityScore: number,
  confidenceThreshold?: number
): Promise<{
  success: boolean;
  error?: string;
  faceDescriptor?: FaceDescriptor;
}> => {
  try {
    const descriptorArray = descriptorToArray(descriptor);
    
    const { data, error } = await supabase
      .from('face_descriptors')
      .upsert({
        employee_id: employeeId,
        organization_id: organizationId,
        descriptor_data: descriptorArray,
        enrollment_quality_score: qualityScore,
        confidence_threshold: confidenceThreshold || 0.6,
        is_active: true,
        last_updated: new Date().toISOString(),
      }, {
        onConflict: 'employee_id,organization_id'
      })
      .select()
      .single();

    if (error) {
      console.error('Error enrolling face:', error);
      return { success: false, error: error.message };
    }

    // Log enrollment
    await logRecognitionAttempt(
      organizationId,
      employeeId,
      'enrollment',
      'facial_recognition',
      true,
      qualityScore
    );

    return { success: true, faceDescriptor: data };
  } catch (error: any) {
    console.error('Error in enrollEmployeeFace:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get employee face descriptor
 */
export const getEmployeeFaceDescriptor = async (
  organizationId: string,
  employeeId: string
): Promise<{
  descriptor?: Float32Array;
  threshold?: number;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('face_descriptors')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('employee_id', employeeId)
      .eq('is_active', true)
      .single();

    if (error) {
      return { error: error.message };
    }

    if (!data) {
      return { error: 'No face descriptor found' };
    }

    const descriptor = arrayToDescriptor(data.descriptor_data as number[]);
    return {
      descriptor,
      threshold: data.confidence_threshold || 0.6,
    };
  } catch (error: any) {
    console.error('Error getting face descriptor:', error);
    return { error: error.message };
  }
};

/**
 * Get all active face descriptors for organization
 */
export const getOrganizationFaceDescriptors = async (
  organizationId: string
): Promise<{
  descriptors: Array<{
    employeeId: string;
    descriptor: Float32Array;
    threshold: number;
  }>;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('face_descriptors')
      .select('employee_id, descriptor_data, confidence_threshold')
      .eq('organization_id', organizationId)
      .eq('is_active', true);

    if (error) {
      return { descriptors: [], error: error.message };
    }

    const descriptors = data.map(item => ({
      employeeId: item.employee_id,
      descriptor: arrayToDescriptor(item.descriptor_data as number[]),
      threshold: item.confidence_threshold || 0.6,
    }));

    return { descriptors };
  } catch (error: any) {
    console.error('Error getting organization face descriptors:', error);
    return { descriptors: [], error: error.message };
  }
};

/**
 * Create time entry (clock in/out) using existing time entry system
 */
export const createTimeEntry = async (
  organizationId: string,
  employeeId: string,
  type: 'clock_in' | 'clock_out',
  method: 'facial_recognition' | 'pin' | 'manual' | 'qr_code' | 'badge',
  confidence?: number,
  location?: string
): Promise<{
  success: boolean;
  timeEntry?: TimeEntry;
  error?: string;
}> => {
  try {
    const now = new Date();
    const today = now.toISOString().split('T')[0];

    if (type === 'clock_in') {
      // Create new time entry for clock in using existing service
      const timeEntryData = {
        employee_id: employeeId,
        date: today,
        time_in: now.toISOString(),
        status: TimeEntryStatus.PRESENT,
        regular_hours: 0,
        overtime_hours: 0,
        night_diff_hours: 0,
        is_rest_day: false,
        is_holiday: false,
        exclude_lunch_break: true,
      };

      const result = await createExistingTimeEntry(organizationId, timeEntryData);

      if (result.error) {
        return { success: false, error: result.error };
      }

      // Update with facial recognition specific fields
      if (result.entry) {
        const { data, error } = await supabase
          .from('time_entries')
          .update({
            clock_in_method: method,
            clock_in_confidence: confidence,
            clock_in_location: location,
          })
          .eq('id', result.entry.id)
          .select()
          .single();

        if (error) {
          return { success: false, error: error.message };
        }

        return { success: true, timeEntry: data };
      }

      return { success: true, timeEntry: result.entry };
    } else {
      // Update existing time entry for clock out
      const { data: existingEntries, error: findError } = await supabase
        .from('time_entries')
        .select('*')
        .eq('employee_id', employeeId)
        .eq('organization_id', organizationId)
        .eq('date', today)
        .eq('status', 'present')
        .is('time_out', null)
        .order('created_at', { ascending: false })
        .limit(1);

      if (findError) {
        return { success: false, error: findError.message };
      }

      if (!existingEntries || existingEntries.length === 0) {
        return { success: false, error: 'No active time entry found to clock out' };
      }

      const existingEntry = existingEntries[0];

      // Calculate hours using the same logic as manual time entry
      const timeInDate = new Date(existingEntry.time_in);
      const timeOutDate = now;

      const calculations = calculateTimeEntryHours(timeInDate, timeOutDate, true); // true = exclude lunch break

      console.log('⏰ Time calculations for clock out (faceRecognition service):', {
        timeIn: timeInDate.toISOString(),
        timeOut: timeOutDate.toISOString(),
        calculations
      });

      const { data, error } = await supabase
        .from('time_entries')
        .update({
          time_out: now.toISOString(),
          clock_out_method: method,
          clock_out_confidence: confidence,
          clock_out_location: location,
          regular_hours: calculations.regular_hours,
          overtime_hours: calculations.overtime_hours,
          night_diff_hours: calculations.night_diff_hours,
        })
        .eq('id', existingEntry.id)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, timeEntry: data };
    }
  } catch (error: any) {
    console.error('Error creating time entry:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get current time entry status for employee
 */
export const getCurrentTimeEntryStatus = async (
  organizationId: string,
  employeeId: string
): Promise<{
  status: 'clocked_out' | 'clocked_in';
  timeEntry?: TimeEntry;
  error?: string;
}> => {
  try {
    const today = new Date().toISOString().split('T')[0];

    const { data, error } = await supabase
      .from('time_entries')
      .select('*')
      .eq('employee_id', employeeId)
      .eq('organization_id', organizationId)
      .eq('date', today)
      .eq('status', 'present')
      .order('created_at', { ascending: false })
      .limit(1);

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      return { status: 'clocked_out', error: error.message };
    }

    if (!data || data.length === 0) {
      return { status: 'clocked_out' };
    }

    const latestEntry = data[0];
    const status = latestEntry.time_out ? 'clocked_out' : 'clocked_in';
    return { status, timeEntry: latestEntry };
  } catch (error: any) {
    console.error('Error getting time entry status:', error);
    return { status: 'clocked_out', error: error.message };
  }
};

/**
 * Set employee PIN
 */
export const setEmployeePin = async (
  organizationId: string,
  employeeId: string,
  pin: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // Generate salt and hash PIN
    const salt = crypto.getRandomValues(new Uint8Array(16));
    const saltString = Array.from(salt, byte => byte.toString(16).padStart(2, '0')).join('');
    
    const encoder = new TextEncoder();
    const data = encoder.encode(pin + saltString);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const pinHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    const { error } = await supabase
      .from('employee_pins')
      .upsert({
        employee_id: employeeId,
        organization_id: organizationId,
        pin_hash: pinHash,
        salt: saltString,
        is_active: true,
        failed_attempts: 0,
        locked_until: null,
      });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error setting employee PIN:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Verify employee PIN
 */
export const verifyEmployeePin = async (
  organizationId: string,
  employeeId: string,
  pin: string
): Promise<{
  success: boolean;
  error?: string;
  locked?: boolean;
}> => {
  try {
    const { data, error } = await supabase
      .from('employee_pins')
      .select('*')
      .eq('employee_id', employeeId)
      .eq('organization_id', organizationId)
      .eq('is_active', true)
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    if (!data) {
      return { success: false, error: 'No PIN set for this employee' };
    }

    // Check if account is locked
    if (data.locked_until && new Date(data.locked_until) > new Date()) {
      return { success: false, locked: true, error: 'Account is temporarily locked' };
    }

    // Hash provided PIN with stored salt
    const encoder = new TextEncoder();
    const pinData = encoder.encode(pin + data.salt);
    const hashBuffer = await crypto.subtle.digest('SHA-256', pinData);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const pinHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    if (pinHash === data.pin_hash) {
      // Reset failed attempts on successful verification
      await supabase
        .from('employee_pins')
        .update({
          failed_attempts: 0,
          locked_until: null,
          last_used: new Date().toISOString(),
        })
        .eq('id', data.id);

      return { success: true };
    } else {
      // Increment failed attempts
      const newFailedAttempts = (data.failed_attempts || 0) + 1;
      const maxAttempts = 3; // TODO: Get from settings
      
      let updateData: any = { failed_attempts: newFailedAttempts };
      
      if (newFailedAttempts >= maxAttempts) {
        const lockoutDuration = 300; // 5 minutes in seconds
        const lockedUntil = new Date(Date.now() + lockoutDuration * 1000).toISOString();
        updateData.locked_until = lockedUntil;
      }

      await supabase
        .from('employee_pins')
        .update(updateData)
        .eq('id', data.id);

      return { 
        success: false, 
        error: 'Invalid PIN',
        locked: newFailedAttempts >= maxAttempts 
      };
    }
  } catch (error: any) {
    console.error('Error verifying PIN:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Log recognition attempt for audit
 */
export const logRecognitionAttempt = async (
  organizationId: string,
  employeeId: string | null,
  type: 'clock_in' | 'clock_out' | 'enrollment' | 'verification',
  method: 'facial_recognition' | 'pin' | 'manual' | 'qr_code' | 'badge',
  success: boolean,
  confidence?: number,
  errorMessage?: string,
  processingTime?: number
): Promise<void> => {
  try {
    await supabase
      .from('recognition_logs')
      .insert({
        organization_id: organizationId,
        employee_id: employeeId,
        recognition_type: type,
        method_used: method,
        success,
        confidence_score: confidence,
        error_message: errorMessage,
        processing_time_ms: processingTime,
      });
  } catch (error) {
    console.error('Error logging recognition attempt:', error);
  }
};

/**
 * Get time tracking settings for organization
 */
export const getTimeTrackingSettings = async (
  organizationId: string
): Promise<{
  settings?: TimeTrackingSettings;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('time_tracking_settings')
      .select('*')
      .eq('organization_id', organizationId)
      .single();

    if (error) {
      return { error: error.message };
    }

    return { settings: data };
  } catch (error: any) {
    console.error('Error getting time tracking settings:', error);
    return { error: error.message };
  }
};
