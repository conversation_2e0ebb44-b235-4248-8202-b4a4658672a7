import { supabase } from '../lib/supabase';

/**
 * Sync product stock quantities with inventory transactions
 * This function recalculates the stock quantity for each product based on its inventory transactions
 */
export const syncProductStockQuantities = async (
  organizationId: string
): Promise<{
  success: boolean;
  updatedProducts: number;
  error?: string;
}> => {
  try {
    console.log('Starting product stock quantity sync for organization:', organizationId);
    
    // Step 1: Get all products for the organization
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, name')
      .eq('organization_id', organizationId);
    
    if (productsError) {
      console.error('Error fetching products:', productsError);
      return { 
        success: false, 
        updatedProducts: 0,
        error: productsError.message 
      };
    }
    
    if (!products || products.length === 0) {
      console.log('No products found for organization');
      return { 
        success: true, 
        updatedProducts: 0 
      };
    }
    
    console.log(`Found ${products.length} products to sync`);
    
    // Step 2: Process each product
    let updatedCount = 0;
    
    for (const product of products) {
      // Get all inventory transactions for this product
      const { data: transactions, error: transactionsError } = await supabase
        .from('inventory_transactions')
        .select('quantity, transaction_type')
        .eq('organization_id', organizationId)
        .eq('product_id', product.id);
      
      if (transactionsError) {
        console.error(`Error fetching transactions for product ${product.id}:`, transactionsError);
        continue; // Skip this product but continue with others
      }
      
      // Calculate the total stock quantity
      let stockQuantity = 0;
      
      if (transactions && transactions.length > 0) {
        for (const transaction of transactions) {
          // Add quantity for receipts/purchases, subtract for sales
          if (['receipt', 'purchase'].includes(transaction.transaction_type.toLowerCase())) {
            stockQuantity += transaction.quantity;
          } else if (transaction.transaction_type.toLowerCase() === 'sale') {
            stockQuantity -= transaction.quantity;
          } else if (transaction.transaction_type.toLowerCase() === 'adjustment') {
            // For adjustments, the quantity could be positive or negative
            stockQuantity += transaction.quantity;
          }
        }
      }
      
      // Update the product's stock quantity
      const { error: updateError } = await supabase
        .from('products')
        .update({ 
          stock_quantity: stockQuantity,
          updated_at: new Date().toISOString()
        })
        .eq('id', product.id)
        .eq('organization_id', organizationId);
      
      if (updateError) {
        console.error(`Error updating stock quantity for product ${product.id}:`, updateError);
        continue; // Skip this product but continue with others
      }
      
      console.log(`Updated stock quantity for product ${product.name} (${product.id}): ${stockQuantity}`);
      updatedCount++;
    }
    
    console.log(`Successfully updated stock quantities for ${updatedCount} products`);
    
    return {
      success: true,
      updatedProducts: updatedCount
    };
  } catch (error: any) {
    console.error('Error in syncProductStockQuantities:', error);
    return {
      success: false,
      updatedProducts: 0,
      error: error.message
    };
  }
};

/**
 * Fix a specific product's stock quantity based on its inventory transactions
 */
export const fixProductStockQuantity = async (
  organizationId: string,
  productId: string
): Promise<{
  success: boolean;
  oldQuantity?: number;
  newQuantity?: number;
  error?: string;
}> => {
  try {
    console.log(`Fixing stock quantity for product ${productId}`);
    
    // Step 1: Get the current product stock quantity
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('stock_quantity, name')
      .eq('organization_id', organizationId)
      .eq('id', productId)
      .single();
    
    if (productError) {
      console.error('Error fetching product:', productError);
      return { 
        success: false, 
        error: productError.message 
      };
    }
    
    const oldQuantity = product.stock_quantity || 0;
    
    // Step 2: Get all inventory transactions for this product
    const { data: transactions, error: transactionsError } = await supabase
      .from('inventory_transactions')
      .select('quantity, transaction_type')
      .eq('organization_id', organizationId)
      .eq('product_id', productId);
    
    if (transactionsError) {
      console.error('Error fetching transactions:', transactionsError);
      return { 
        success: false, 
        error: transactionsError.message 
      };
    }
    
    // Step 3: Calculate the correct stock quantity
    let newQuantity = 0;
    
    if (transactions && transactions.length > 0) {
      for (const transaction of transactions) {
        // Add quantity for receipts/purchases, subtract for sales
        if (['receipt', 'purchase'].includes(transaction.transaction_type.toLowerCase())) {
          newQuantity += transaction.quantity;
        } else if (transaction.transaction_type.toLowerCase() === 'sale') {
          newQuantity -= transaction.quantity;
        } else if (transaction.transaction_type.toLowerCase() === 'adjustment') {
          // For adjustments, the quantity could be positive or negative
          newQuantity += transaction.quantity;
        }
      }
    }
    
    // Step 4: Update the product's stock quantity
    const { error: updateError } = await supabase
      .from('products')
      .update({ 
        stock_quantity: newQuantity,
        updated_at: new Date().toISOString()
      })
      .eq('id', productId)
      .eq('organization_id', organizationId);
    
    if (updateError) {
      console.error('Error updating stock quantity:', updateError);
      return { 
        success: false, 
        error: updateError.message 
      };
    }
    
    console.log(`Updated stock quantity for product ${product.name} from ${oldQuantity} to ${newQuantity}`);
    
    return {
      success: true,
      oldQuantity,
      newQuantity
    };
  } catch (error: any) {
    console.error('Error in fixProductStockQuantity:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
