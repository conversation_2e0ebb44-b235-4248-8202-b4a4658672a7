import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';
import { Product } from './product';

export type SupplierProduct = {
  id: string;
  supplier_id: string;
  product_id: string;
  unit_price: number | null;
  minimum_order_quantity: number | null;
  lead_time_days: number | null;
  is_preferred: boolean | null;
  notes: string | null;
  uom_id?: string | null;
  organization_id?: string;
  conversion_factor?: number;
  base_price?: number | null;
  created_at: string;
  updated_at: string;
  product?: Product;
  uom?: {
    id: string;
    code: string;
    name: string;
    supplier_price?: number; // Add supplier price to UoM
  };
  // Legacy field for backward compatibility
  uom_name?: string;
};

export type SupplierProductInput = Omit<
  SupplierProduct,
  'id' | 'created_at' | 'updated_at' | 'product'
>;

// Extended type with UI-specific fields
export type SupplierProductFormData = SupplierProductInput & {
  uom_name?: string;
  // Fields to help with UoM conversion in the UI
  available_conversion_factors?: Record<string, number>;
};

/**
 * Get all products for a supplier
 */
export const getSupplierProducts = async (
  organizationId: string,
  supplierId: string,
  options?: {
    searchQuery?: string;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  },
): Promise<{
  supplierProducts: SupplierProduct[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('supplier_products')
      .select(
        `
        *,
        product:product_id (
          id,
          name,
          sku,
          barcode,
          unit_price,
          image_url,
          category:category_id (
            id,
            name
          )
        ),
        uom:uom_id (
          id,
          code,
          name
        )
      `,
        { count: 'exact' },
      )
      .eq('supplier_id', supplierId);

    // Apply search filter on product name, sku, or barcode
    if (options?.searchQuery) {
      query = query.or(
        `product.name.ilike.%${options.searchQuery}%,product.sku.ilike.%${options.searchQuery}%,product.barcode.ilike.%${options.searchQuery}%`,
      );
    }

    // Apply sorting
    if (options?.sortBy) {
      if (options.sortBy.startsWith('product.')) {
        // For sorting by product fields, we need to use order by foreign table
        const field = options.sortBy.replace('product.', '');
        query = query.order(`product(${field})`, { ascending: options.sortOrder === 'asc' });
      } else {
        query = query.order(options.sortBy, { ascending: options.sortOrder === 'asc' });
      }
    } else {
      // Default sort by product name
      query = query.order('product(name)', { ascending: true });
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching supplier products:', error);
      return {
        supplierProducts: [],
        count: 0,
        error: error.message,
      };
    }

    return {
      supplierProducts: data as SupplierProduct[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error in getSupplierProducts:', error);
    return {
      supplierProducts: [],
      count: 0,
      error: error.message,
    };
  }
};

/**
 * Get available products for a supplier (products not already associated with the supplier)
 */
export const getAvailableProducts = async (
  organizationId: string,
  supplierId: string,
  options?: {
    searchQuery?: string;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  },
): Promise<{
  products: Product[];
  count: number;
  error?: string;
}> => {
  try {
    // First, get the IDs of products already associated with this supplier
    const { data: existingProducts, error: existingError } = await supabase
      .from('supplier_products')
      .select('product_id')
      .eq('supplier_id', supplierId);

    if (existingError) {
      console.error('Error fetching existing supplier products:', existingError);
      return {
        products: [],
        count: 0,
        error: existingError.message,
      };
    }

    // Extract product IDs
    const existingProductIds = existingProducts.map((item) => item.product_id);

    // Query for products not in the existing list
    let query = supabase
      .from('products')
      .select(
        `
        *,
        category:category_id (
          id,
          name
        )
      `,
        { count: 'exact' },
      )
      .eq('organization_id', organizationId)
      .eq('is_active', true);

    // Exclude existing products if there are any
    if (existingProductIds.length > 0) {
      query = query.not('id', 'in', `(${existingProductIds.join(',')})`);
    }

    // Apply search filter
    if (options?.searchQuery) {
      query = query.or(
        `name.ilike.%${options.searchQuery}%,sku.ilike.%${options.searchQuery}%,barcode.ilike.%${options.searchQuery}%`,
      );
    }

    // Apply sorting
    if (options?.sortBy) {
      query = query.order(options.sortBy, { ascending: options.sortOrder === 'asc' });
    } else {
      query = query.order('name', { ascending: true });
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching available products:', error);
      return {
        products: [],
        count: 0,
        error: error.message,
      };
    }

    return {
      products: data as Product[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error in getAvailableProducts:', error);
    return {
      products: [],
      count: 0,
      error: error.message,
    };
  }
};

/**
 * Add products to a supplier
 */
export const addSupplierProducts = async (
  supplierId: string,
  products: SupplierProductInput[],
): Promise<{
  success: boolean;
  error?: string;
  data?: any;
}> => {
  try {
    console.log('Adding supplier products:', products);

    if (!products || products.length === 0) {
      return { success: true };
    }

    // Ensure all products have organization_id and other required fields
    const productsWithOrg = products.map(product => {
      // Make a copy to avoid modifying the original
      const productCopy = { ...product };

      // If organization_id is missing, log a warning
      if (!productCopy.organization_id) {
        console.warn('Product missing organization_id:', productCopy);
      }

      // Log the product data for debugging
      console.log('Processing product for upsert:', productCopy);

      return productCopy;
    });

    // Process each product individually to avoid conflicts
    const results = [];
    let hasError = false;
    let errorMessage = '';

    // Process each product one by one
    for (const product of productsWithOrg) {
      console.log('Processing individual product:', product);

      // Check if this supplier-product combination already exists
      const { data: existingData, error: checkError } = await supabase
        .from('supplier_products')
        .select('id')
        .eq('supplier_id', product.supplier_id)
        .eq('product_id', product.product_id)
        .maybeSingle();

      console.log('Check for existing product:', { existingData, checkError });

      let result;

      if (existingData) {
        // Update existing record
        console.log('Updating existing supplier product:', existingData.id);
        const { data: updateData, error: updateError } = await supabase
          .from('supplier_products')
          .update(product)
          .eq('id', existingData.id)
          .select(`
            *,
            uom:uom_id (
              id,
              code,
              name
            )
          `)
          .single();

        if (updateError) {
          console.error('Error updating supplier product:', updateError);
          hasError = true;
          errorMessage = updateError.message;
        } else {
          console.log('Successfully updated supplier product:', updateData);
          results.push(updateData);
        }
      } else {
        // Insert new record
        console.log('Inserting new supplier product');
        const { data: insertData, error: insertError } = await supabase
          .from('supplier_products')
          .insert(product)
          .select(`
            *,
            uom:uom_id (
              id,
              code,
              name
            )
          `)
          .single();

        if (insertError) {
          console.error('Error inserting supplier product:', insertError);
          hasError = true;
          errorMessage = insertError.message;
        } else {
          console.log('Successfully inserted supplier product:', insertData);
          results.push(insertData);
        }
      }
    }

    // Return results
    if (hasError) {
      return {
        success: false,
        error: errorMessage,
        data: results
      };
    }

    console.log('All supplier products processed successfully:', results);

    return {
      success: true,
      data: results
    };
  } catch (error: any) {
    console.error('Error in addSupplierProducts:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Update a supplier product
 */
export const updateSupplierProduct = async (
  supplierProductId: string,
  updates: Partial<Omit<SupplierProduct, 'id' | 'supplier_id' | 'product_id' | 'created_at' | 'updated_at' | 'product'>>,
): Promise<{
  success: boolean;
  error?: string;
  data?: any;
}> => {
  try {
    console.log('Updating supplier product with ID:', supplierProductId);
    console.log('Update data:', updates);

    // Calculate base_price if both unit_price and conversion_factor are provided
    if (updates.unit_price !== undefined && updates.unit_price !== null &&
        updates.conversion_factor !== undefined && updates.conversion_factor !== null) {
      updates.base_price = updates.unit_price * updates.conversion_factor;
    }

    const { data, error } = await supabase
      .from('supplier_products')
      .update(updates)
      .eq('id', supplierProductId)
      .select(`
        *,
        uom:uom_id (
          id,
          code,
          name
        )
      `)
      .single();

    if (error) {
      console.error('Error updating supplier product:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    console.log('Supplier product updated successfully:', data);

    return {
      success: true,
      data
    };
  } catch (error: any) {
    console.error('Error in updateSupplierProduct:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Remove a product from a supplier
 */
export const removeSupplierProduct = async (
  supplierProductId: string,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('supplier_products')
      .delete()
      .eq('id', supplierProductId);

    if (error) {
      console.error('Error removing supplier product:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in removeSupplierProduct:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Get supplier product by supplier ID and product ID
 */
export const getSupplierProductByProductId = async (
  supplierId: string,
  productId: string
): Promise<{
  supplierProduct?: SupplierProduct;
  error?: string;
}> => {
  try {
    console.log('Fetching supplier product:', { supplierId, productId });

    const { data, error } = await supabase
      .from('supplier_products')
      .select(`
        *,
        product:product_id (
          id,
          name,
          sku,
          unit_price
        ),
        uom:uom_id (
          id,
          code,
          name
        )
      `)
      .eq('supplier_id', supplierId)
      .eq('product_id', productId)
      .single();

    console.log('Supplier product query result:', { data, error });

    if (error) {
      // If the error is "No rows found", it means the supplier doesn't have this product
      if (error.code === 'PGRST116') {
        console.log('No supplier product found for this product and supplier');

        // Let's get the product's default price as a fallback
        const { data: product, error: productError } = await supabase
          .from('products')
          .select('id, name, sku, unit_price')
          .eq('id', productId)
          .single();

        console.log('Product fallback check:', { product, productError });

        if (product && !productError) {
          console.log('Using default product price as fallback');
          return {
            supplierProduct: {
              id: `fallback-${productId}`,
              supplier_id: supplierId,
              product_id: productId,
              unit_price: product.unit_price || 0,
              product: product
            } as SupplierProduct,
          };
        }

        return {};
      }

      console.error('Error fetching supplier product:', error);
      return {
        error: error.message,
      };
    }

    // Add the unit_price to the UoM object for easy access in purchase orders
    if (data && data.uom && data.unit_price !== null && data.unit_price !== undefined) {
      data.uom.supplier_price = data.unit_price;
    }

    return {
      supplierProduct: data as SupplierProduct,
    };
  } catch (error: any) {
    console.error('Error in getSupplierProductByProductId:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Add or update a product for a supplier
 */
export const addOrUpdateSupplierProduct = async (
  supplierId: string,
  productId: string,
  unitPrice: number,
  organizationId: string,
  uomId?: string,
  uomName?: string, // This is for UI only, not stored in DB
  conversionFactor?: number // Added conversion factor parameter
): Promise<{
  success: boolean;
  supplierProduct?: SupplierProduct;
  error?: string;
}> => {
  try {
    console.log('Adding/updating supplier product with explicit data:', {
      supplierId,
      productId,
      unitPrice,
      organizationId,
      uomId,
      uomName,
      conversionFactor
    });

    // First check if the supplier product already exists
    const { data: existingProducts, error: checkError } = await supabase
      .from('supplier_products')
      .select('id')
      .eq('supplier_id', supplierId)
      .eq('product_id', productId)
      .eq('organization_id', organizationId);

    console.log('Check for existing supplier product:', { existingProducts, checkError });

    let result;

    // If the supplier product exists, update it
    if (existingProducts && existingProducts.length > 0) {
      console.log('Updating existing supplier product:', existingProducts[0].id);

      // Prepare update data with all fields explicitly set
      const updateData: any = {
        unit_price: unitPrice,
        organization_id: organizationId
      };

      // Always include UoM fields, even if null
      updateData.uom_id = uomId || null;
      updateData.uom_name = uomName || null;

      // Always include conversion factor, default to 1 if not provided
      updateData.conversion_factor = conversionFactor || 1;

      // Always calculate base price
      updateData.base_price = unitPrice * (conversionFactor || 1);

      console.log('Update data prepared:', updateData);

      console.log('Updating supplier product with data:', updateData);

      const { data, error } = await supabase
        .from('supplier_products')
        .update(updateData)
        .eq('id', existingProducts[0].id)
        .select(`
          *,
          uom:uom_id (
            id,
            code,
            name
          )
        `)
        .single();

      if (error) {
        console.error('Error updating supplier product:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      result = data;
    }
    // Otherwise, create a new supplier product
    else {
      console.log('Creating new supplier product');

      // Prepare insert data with all fields explicitly set
      const insertData: any = {
        supplier_id: supplierId,
        product_id: productId,
        unit_price: unitPrice,
        organization_id: organizationId
      };

      // Always include UoM fields, even if null
      insertData.uom_id = uomId || null;
      insertData.uom_name = uomName || null;

      // Always include conversion factor, default to 1 if not provided
      insertData.conversion_factor = conversionFactor || 1;

      // Always calculate base price
      insertData.base_price = unitPrice * (conversionFactor || 1);

      console.log('Insert data prepared:', insertData);

      console.log('Creating new supplier product with data:', insertData);

      const { data, error } = await supabase
        .from('supplier_products')
        .insert(insertData)
        .select(`
          *,
          uom:uom_id (
            id,
            code,
            name
          )
        `)
        .single();

      if (error) {
        console.error('Error creating supplier product:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      result = data;
    }

    console.log('Supplier product added/updated successfully:', result);

    return {
      success: true,
      supplierProduct: result as SupplierProduct,
    };
  } catch (error: any) {
    console.error('Error in addOrUpdateSupplierProduct:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Get supplier products by supplier ID for multiple products
 */
export const getSupplierProductsByProductIds = async (
  supplierId: string,
  productIds: string[]
): Promise<{
  supplierProducts: SupplierProduct[];
  error?: string;
}> => {
  try {
    console.log('Fetching supplier products for multiple products:', { supplierId, productIds });

    if (!productIds.length) {
      return { supplierProducts: [] };
    }

    // First, let's check if the supplier_products table exists and has the expected structure
    const { data: tableInfo, error: tableError } = await supabase
      .from('supplier_products')
      .select('*')
      .limit(1);

    console.log('Supplier products table check:', { tableInfo, tableError });

    const { data, error } = await supabase
      .from('supplier_products')
      .select(`
        *,
        product:product_id (
          id,
          name,
          sku,
          unit_price
        ),
        uom:uom_id (
          id,
          code,
          name
        )
      `)
      .eq('supplier_id', supplierId)
      .in('product_id', productIds);

    console.log('Supplier products query result:', { data, error });

    if (error) {
      console.error('Error fetching supplier products:', error);
      return {
        supplierProducts: [],
        error: error.message,
      };
    }

    if (!data || data.length === 0) {
      console.log('No supplier products found for these products');

      // Let's try a simpler query to see if the supplier exists
      const { data: supplierCheck, error: supplierError } = await supabase
        .from('suppliers')
        .select('id, name')
        .eq('id', supplierId)
        .single();

      console.log('Supplier check:', { supplierCheck, supplierError });

      // Let's also check if the products exist
      const { data: productsCheck, error: productsError } = await supabase
        .from('products')
        .select('id, name, unit_price')
        .in('id', productIds);

      console.log('Products check:', {
        productsCount: productsCheck?.length || 0,
        productsError
      });

      // If we have products, use their default prices
      if (productsCheck && productsCheck.length > 0) {
        console.log('Using default product prices as fallback');
        return {
          supplierProducts: productsCheck.map(product => ({
            id: `fallback-${product.id}`,
            supplier_id: supplierId,
            product_id: product.id,
            unit_price: product.unit_price || 0,
            product: product
          })) as SupplierProduct[],
        };
      }
    }

    // Add the unit_price to each UoM object for easy access in purchase orders
    if (data && data.length > 0) {
      data.forEach(item => {
        if (item.uom && item.unit_price !== null && item.unit_price !== undefined) {
          item.uom.supplier_price = item.unit_price;
        }
      });
    }

    return {
      supplierProducts: data as SupplierProduct[],
    };
  } catch (error: any) {
    console.error('Error in getSupplierProductsByProductIds:', error);
    return {
      supplierProducts: [],
      error: error.message,
    };
  }
};
