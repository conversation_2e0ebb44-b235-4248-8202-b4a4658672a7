import { supabase } from '../lib/supabase';
// import { Database } from '../types/database.types';

// Temporary types until chat tables are added to database.types.ts
export type ChatConversation = {
  id: string;
  organization_id: string;
  name?: string;
  is_group: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
};

export type ChatParticipant = {
  id: string;
  conversation_id: string;
  user_id: string;
  is_admin: boolean;
  last_read_message_id?: string;
  created_at: string;
  updated_at: string;
};

export type ChatMessage = {
  id: string;
  conversation_id: string;
  organization_id: string;
  sender_id: string;
  content: string;
  attachment_type?: string;
  attachment_url?: string;
  attachment_name?: string;
  attachment_size?: number;
  created_at: string;
  updated_at: string;
};

export type ChatMessageStatus = {
  id: string;
  message_id: string;
  user_id: string;
  is_read: boolean;
  read_at?: string;
  created_at: string;
  updated_at: string;
};

export interface ProfileInfo {
  id: string;
  first_name?: string | null;
  last_name?: string | null;
  avatar_url?: string | null;
}

export interface ParticipantWithProfile {
  id: string;
  user_id: string;
  is_admin: boolean;
  last_read_message_id: string | null;
  profiles?: ProfileInfo;
}

export interface ConversationWithParticipants extends ChatConversation {
  participants: ParticipantWithProfile[];
  last_message?: ChatMessage;
  unread_count?: number;
}

export interface MessageWithSender extends ChatMessage {
  sender?: ProfileInfo;
  is_read?: boolean;
}

/**
 * Get all conversations for the current user in a specific organization
 */
export const getUserConversations = async (organizationId?: string): Promise<ConversationWithParticipants[]> => {
  try {
    // Get current user
    const userId = (await supabase.auth.getUser()).data.user?.id;
    if (!userId) {
      throw new Error('User not authenticated');
    }

    // Build query with organization filter
    // @ts-ignore - Temporary ignore until chat tables are in database types
    let query = supabase
      .from('chat_conversations')
      .select(
        `
        *,
        participants:chat_participants(
          id,
          user_id,
          is_admin,
          last_read_message_id
        )
      `,
      );

    // Filter by organization if provided
    if (organizationId) {
      query = query.eq('organization_id', organizationId);
    }

    // Get conversation IDs where the current user is a participant
    // @ts-ignore - Temporary ignore until chat tables are in database types
    const { data: userParticipations } = await supabase
      .from('chat_participants')
      .select('conversation_id')
      .eq('user_id', userId);

    if (!userParticipations || userParticipations.length === 0) {
      return [];
    }

    const conversationIds = userParticipations.map(p => p.conversation_id);
    query = query.in('id', conversationIds);

    const { data: conversations, error: conversationsError } = await query
      .order('updated_at', { ascending: false });

    if (conversationsError) {
      throw conversationsError;
    }

    if (!conversations || conversations.length === 0) {
      return [];
    }

    // Step 1: Fetch profiles for participants efficiently (userId already declared above)
    // Collect all unique user IDs from all conversations
    const participantUserIds = new Set<string>();
    conversations.forEach((conversation) => {
      conversation.participants.forEach((participant) => {
        participantUserIds.add(participant.user_id);
      });
    });

    // Fetch all profiles in a single request
    const { data: profilesData } = await supabase
      .from('profiles')
      .select('id, first_name, last_name, avatar_url')
      .in('id', Array.from(participantUserIds));

    // Create a map of user IDs to profiles for quick lookup
    const profileMap = new Map<string, ProfileInfo>();
    if (profilesData) {
      profilesData.forEach((profile) => {
        profileMap.set(profile.id, profile);
      });
    }

    // Assign profiles to participants
    conversations.forEach((conversation) => {
      conversation.participants.forEach((participant) => {
        (participant as ParticipantWithProfile).profiles =
          profileMap.get(participant.user_id) || undefined;
      });
    });

    // Step 2: Get all conversation IDs for fetching last messages
    const conversationIdsForMessages = conversations.map((c) => c.id);

    // Step 3: Fetch last messages for all conversations in batches
    const lastMessageMap = new Map<string, ChatMessage>();
    const batchSize = 5;

    for (let i = 0; i < conversationIdsForMessages.length; i += batchSize) {
      const batchIds = conversationIdsForMessages.slice(i, i + batchSize);

      // Get last messages for this batch
      const { data: lastMessages } = await supabase
        .from('chat_messages')
        .select('*')
        .in('conversation_id', batchIds)
        .order('created_at', { ascending: false });

      if (lastMessages) {
        // Group by conversation_id and take the first one (most recent)
        const messagesByConversation = lastMessages.reduce((acc, msg) => {
          if (
            !acc[msg.conversation_id] ||
            new Date(msg.created_at) > new Date(acc[msg.conversation_id].created_at)
          ) {
            acc[msg.conversation_id] = msg;
          }
          return acc;
        }, {} as Record<string, ChatMessage>);

        // Add to map
        Object.entries(messagesByConversation).forEach(([convId, msg]) => {
          lastMessageMap.set(convId, msg);
        });
      }
    }

    // Step 4: Calculate unread counts
    const unreadCountMap = new Map<string, number>();

    // For each conversation, find the participant that is the current user
    const participantsWithLastRead = conversations.map((conv) => {
      const participant = conv.participants.find((p) => p.user_id === userId);
      return {
        conversationId: conv.id,
        lastReadMessageId: participant?.last_read_message_id || null,
      };
    });

    // Process unread counts in batches
    for (let i = 0; i < participantsWithLastRead.length; i += batchSize) {
      const batch = participantsWithLastRead.slice(i, i + batchSize);

      // Process each conversation in the batch
      await Promise.all(
        batch.map(async ({ conversationId, lastReadMessageId }) => {
          let unreadCount = 0;

          if (lastReadMessageId) {
            // Get the created_at time of the last read message
            const lastReadMessage = await getMessageCreatedAt(lastReadMessageId);

            if (lastReadMessage) {
              const { count } = await supabase
                .from('chat_messages')
                .select('*', { count: 'exact', head: true })
                .eq('conversation_id', conversationId)
                .gt('created_at', lastReadMessage)
                .neq('sender_id', userId || '');

              unreadCount = count || 0;
            }
          } else {
            // Count all messages not from the current user
            const { count } = await supabase
              .from('chat_messages')
              .select('*', { count: 'exact', head: true })
              .eq('conversation_id', conversationId)
              .neq('sender_id', userId || '');

            unreadCount = count || 0;
          }

          unreadCountMap.set(conversationId, unreadCount);
        }),
      );
    }

    // Step 5: Combine all data
    const conversationsWithLastMessage = conversations.map((conversation) => ({
      ...conversation,
      last_message: lastMessageMap.get(conversation.id) || undefined,
      unread_count: unreadCountMap.get(conversation.id) || 0,
    }));

    return conversationsWithLastMessage;
  } catch (error) {
    console.error('Error in getUserConversations:', error);
    throw error;
  }
};

/**
 * Get a single conversation by ID with participants
 */
export const getConversation = async (
  conversationId: string,
  organizationId?: string,
): Promise<ConversationWithParticipants> => {
  // Get current user to verify they're a participant
  const userId = (await supabase.auth.getUser()).data.user?.id;
  if (!userId) {
    throw new Error('User not authenticated');
  }

  let query = supabase
    .from('chat_conversations')
    .select(
      `
      *,
      participants:chat_participants(
        id,
        user_id,
        is_admin,
        last_read_message_id
      )
    `,
    )
    .eq('id', conversationId);

  // Filter by organization if provided
  if (organizationId) {
    query = query.eq('organization_id', organizationId);
  }

  const { data, error } = await query.maybeSingle();

  if (error) {
    throw error;
  }

  if (!data) {
    throw new Error('Conversation not found');
  }

  // TODO: Re-enable participant verification when RLS policies are implemented
  // const isParticipant = data.participants.some((p: any) => p.user_id === userId);
  // if (!isParticipant) {
  //   throw new Error('Access denied: You are not a participant in this conversation');
  // }

  // Fetch profiles for participants more efficiently
  if (data && data.participants.length > 0) {
    // Get all user IDs
    const userIds = data.participants.map((p: any) => p.user_id);

    // Fetch all profiles in a single request
    const { data: profilesData } = await supabase
      .from('profiles')
      .select('id, first_name, last_name, avatar_url')
      .in('id', userIds);

    // Create a map for quick lookup
    const profileMap = new Map<string, ProfileInfo>();
    if (profilesData) {
      profilesData.forEach((profile) => {
        profileMap.set(profile.id, profile);
      });
    }

    // Assign profiles to participants
    data.participants.forEach((participant: any) => {
      (participant as ParticipantWithProfile).profiles =
        profileMap.get(participant.user_id) || undefined;
    });

    // Get last message for the conversation
    const { data: lastMessage } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('conversation_id', data.id)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (lastMessage) {
      (data as ConversationWithParticipants).last_message = lastMessage;
    }

    // Get unread count (userId already declared above)
    const participant = data.participants.find((p: any) => p.user_id === userId);

    let unreadCount = 0;
    if (participant && participant.last_read_message_id) {
      const lastReadMessage = await getMessageCreatedAt(participant.last_read_message_id);

      if (lastReadMessage) {
        const { count } = await supabase
          .from('chat_messages')
          .select('*', { count: 'exact', head: true })
          .eq('conversation_id', data.id)
          .gt('created_at', lastReadMessage)
          .neq('sender_id', userId || '');

        unreadCount = count || 0;
      }
    } else {
      const { count } = await supabase
        .from('chat_messages')
        .select('*', { count: 'exact', head: true })
        .eq('conversation_id', data.id)
        .neq('sender_id', userId || '');

      unreadCount = count || 0;
    }

    (data as ConversationWithParticipants).unread_count = unreadCount;
  }

  return data as ConversationWithParticipants;
};

/**
 * Get messages for a conversation
 */
export const getConversationMessages = async (
  conversationId: string,
  options?: { limit?: number; offset?: number; searchQuery?: string; organizationId?: string },
): Promise<MessageWithSender[]> => {
  const { limit = 50, offset = 0, searchQuery = '', organizationId } = options || {};
  const userId = (await supabase.auth.getUser()).data.user?.id;

  if (!userId) {
    throw new Error('User not authenticated');
  }

  // TODO: Re-enable access control when RLS policies are implemented
  // For now, we'll skip access verification to allow development
  // if (organizationId) {
  //   const { data: conversation } = await supabase
  //     .from('chat_conversations')
  //     .select('id, organization_id')
  //     .eq('id', conversationId)
  //     .eq('organization_id', organizationId)
  //     .maybeSingle();

  //   if (!conversation) {
  //     throw new Error('Conversation not found or access denied');
  //   }

  //   // Verify user is a participant
  //   const { data: participant } = await supabase
  //     .from('chat_participants')
  //     .select('id')
  //     .eq('conversation_id', conversationId)
  //     .eq('user_id', userId)
  //     .maybeSingle();

  //   if (!participant) {
  //     throw new Error('Access denied: You are not a participant in this conversation');
  //   }
  // }

  let query = supabase.from('chat_messages').select('*').eq('conversation_id', conversationId);

  // Add search filter if provided
  if (searchQuery.trim()) {
    query = query.ilike('content', `%${searchQuery.trim()}%`);
  }

  // Add ordering and pagination - newest first for initial load, then paginate backwards
  const { data, error } = await query
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw error;
  }

  // Get read status and sender info more efficiently
  let messagesWithReadStatus: MessageWithSender[] = [];

  if (data && data.length > 0) {
    // Get all message IDs
    const messageIds = data.map((m) => m.id);

    // Get all sender IDs (unique)
    const senderIds = [...new Set(data.map((m) => m.sender_id))];

    // Run these two queries in parallel to save time
    const [statusResponse, sendersResponse] = await Promise.all([
      // Fetch all read statuses in a single request
      supabase
        .from('chat_message_status')
        .select('message_id, is_read')
        .in('message_id', messageIds)
        .eq('user_id', userId || ''),

      // Fetch all sender profiles in a single request
      supabase.from('profiles').select('id, first_name, last_name, avatar_url').in('id', senderIds),
    ]);

    const statusData = statusResponse.data;
    const sendersData = sendersResponse.data;

    // Create maps for quick lookup
    const statusMap = new Map<string, boolean>();
    if (statusData) {
      statusData.forEach((status) => {
        statusMap.set(status.message_id, status.is_read);
      });
    }

    const senderMap = new Map<string, ProfileInfo>();
    if (sendersData) {
      sendersData.forEach((sender) => {
        senderMap.set(sender.id, sender);
      });
    }

    // Combine all data
    messagesWithReadStatus = data.map((message) => ({
      ...message,
      is_read: statusMap.get(message.id) || false,
      sender: senderMap.get(message.sender_id) || undefined,
    }));

    // Mark messages as read in the background
    const unreadMessageIds = data
      .filter((msg) => msg.sender_id !== userId && !statusMap.get(msg.id))
      .map((msg) => msg.id);

    if (unreadMessageIds.length > 0) {
      // Don't await this - let it happen in the background
      markMessagesAsRead(conversationId, unreadMessageIds).catch((err) =>
        console.error('Error marking messages as read:', err),
      );
    }
  }

  return messagesWithReadStatus as MessageWithSender[];
};

/**
 * Send a message to a conversation
 */
export const sendMessage = async (
  conversationId: string,
  content: string,
  attachment?: {
    type: string;
    url: string;
    name: string;
    size: number;
  },
): Promise<ChatMessage> => {
  const userId = (await supabase.auth.getUser()).data.user?.id;

  if (!userId) {
    throw new Error('User not authenticated');
  }

  // Get the conversation to find the organization_id
  // @ts-ignore - Temporary ignore until chat tables are in database types
  const { data: conversation, error: convError } = await supabase
    .from('chat_conversations')
    .select('organization_id')
    .eq('id', conversationId)
    .maybeSingle();

  if (convError || !conversation) {
    throw new Error('Conversation not found');
  }

  // Prepare message data
  const messageData: any = {
    conversation_id: conversationId,
    organization_id: conversation.organization_id,
    content,
    sender_id: userId,
  };

  // Add attachment data if provided
  if (attachment) {
    messageData.attachment_type = attachment.type;
    messageData.attachment_url = attachment.url;
    messageData.attachment_name = attachment.name;
    messageData.attachment_size = attachment.size;
  }

  // Insert the message
  // @ts-ignore - Temporary ignore until chat tables are in database types
  const { data, error } = await supabase
    .from('chat_messages')
    .insert(messageData)
    .select()
    .maybeSingle();

  if (!data) {
    throw new Error('Failed to create message');
  }

  if (error) {
    throw error;
  }

  // Update the conversation's updated_at timestamp
  // @ts-ignore - Temporary ignore until chat tables are in database types
  await supabase
    .from('chat_conversations')
    .update({ updated_at: new Date().toISOString() })
    .eq('id', conversationId);

  return data;
};

/**
 * Delete a message
 */
export const deleteMessage = async (messageId: string): Promise<void> => {
  const userId = (await supabase.auth.getUser()).data.user?.id;

  if (!userId) {
    throw new Error('User not authenticated');
  }

  // First, check if the user is the sender of the message
  const { data: message, error: fetchError } = await supabase
    .from('chat_messages')
    .select('sender_id, conversation_id')
    .eq('id', messageId)
    .maybeSingle();

  if (!message) {
    throw new Error('Message not found');
  }

  if (fetchError) {
    throw fetchError;
  }

  if (message.sender_id !== userId) {
    throw new Error('You can only delete your own messages');
  }

  // Delete the message
  const { error: deleteError } = await supabase.from('chat_messages').delete().eq('id', messageId);

  if (deleteError) {
    throw deleteError;
  }

  // Update the conversation's updated_at timestamp
  await supabase
    .from('chat_conversations')
    .update({ updated_at: new Date().toISOString() })
    .eq('id', message.conversation_id);
};

/**
 * Edit a message
 */
export const editMessage = async (messageId: string, newContent: string): Promise<ChatMessage> => {
  const userId = (await supabase.auth.getUser()).data.user?.id;

  if (!userId) {
    throw new Error('User not authenticated');
  }

  // First, check if the user is the sender of the message
  const { data: message, error: fetchError } = await supabase
    .from('chat_messages')
    .select('sender_id, conversation_id')
    .eq('id', messageId)
    .maybeSingle();

  if (!message) {
    throw new Error('Message not found');
  }

  if (fetchError) {
    throw fetchError;
  }

  if (message.sender_id !== userId) {
    throw new Error('You can only edit your own messages');
  }

  // Update the message
  const { data, error: updateError } = await supabase
    .from('chat_messages')
    .update({
      content: newContent,
      updated_at: new Date().toISOString(),
    })
    .eq('id', messageId)
    .select()
    .maybeSingle();

  if (!data) {
    throw new Error('Failed to update message');
  }

  if (updateError) {
    throw updateError;
  }

  // Update the conversation's updated_at timestamp
  await supabase
    .from('chat_conversations')
    .update({ updated_at: new Date().toISOString() })
    .eq('id', message.conversation_id);

  return data;
};

/**
 * Create a new one-on-one conversation
 */
export const createOneOnOneConversation = async (
  organizationId: string,
  otherUserId: string,
): Promise<ChatConversation> => {
  const currentUserId = (await supabase.auth.getUser()).data.user?.id;

  if (!currentUserId) {
    throw new Error('User not authenticated');
  }

  // Check if a conversation already exists between these users
  const { data: existingConversations, error: fetchError } = await supabase
    .from('chat_conversations')
    .select(
      `
      *,
      participants:chat_participants(user_id)
    `,
    )
    .eq('organization_id', organizationId)
    .eq('is_group', false);

  if (fetchError) {
    throw fetchError;
  }

  // Find a conversation where both users are participants
  const existingConversation = existingConversations?.find((conv) => {
    const participantIds = conv.participants.map((p: any) => p.user_id);
    return participantIds.includes(currentUserId) && participantIds.includes(otherUserId);
  });

  if (existingConversation) {
    return existingConversation;
  }

  // Create a new conversation
  const { data: newConversation, error: createError } = await supabase
    .from('chat_conversations')
    .insert({
      organization_id: organizationId,
      is_group: false,
      created_by: currentUserId,
    })
    .select()
    .maybeSingle();

  if (!newConversation) {
    throw new Error('Failed to create conversation');
  }

  if (createError) {
    throw createError;
  }

  // Add participants
  await supabase.from('chat_participants').insert([
    {
      conversation_id: newConversation.id,
      user_id: currentUserId,
      is_admin: true,
    },
    {
      conversation_id: newConversation.id,
      user_id: otherUserId,
      is_admin: false,
    },
  ]);

  return newConversation;
};

/**
 * Create a new group conversation
 */
export const createGroupConversation = async (
  organizationId: string,
  name: string,
  participantIds: string[],
): Promise<ChatConversation> => {
  const currentUserId = (await supabase.auth.getUser()).data.user?.id;

  if (!currentUserId) {
    throw new Error('User not authenticated');
  }

  if (!name) {
    throw new Error('Group name is required');
  }

  // Create a new conversation
  const { data: newConversation, error: createError } = await supabase
    .from('chat_conversations')
    .insert({
      organization_id: organizationId,
      name,
      is_group: true,
      created_by: currentUserId,
    })
    .select()
    .maybeSingle();

  if (!newConversation) {
    throw new Error('Failed to create group conversation');
  }

  if (createError) {
    throw createError;
  }

  // Add participants
  const participants = [
    {
      conversation_id: newConversation.id,
      user_id: currentUserId,
      is_admin: true,
    },
    ...participantIds.map((userId) => ({
      conversation_id: newConversation.id,
      user_id: userId,
      is_admin: false,
    })),
  ];

  await supabase.from('chat_participants').insert(participants);

  return newConversation;
};

/**
 * Mark messages as read
 */
export const markMessagesAsRead = async (
  conversationId: string,
  messageIds: string[],
): Promise<void> => {
  const currentUserId = (await supabase.auth.getUser()).data.user?.id;

  if (!currentUserId) {
    throw new Error('User not authenticated');
  }

  if (messageIds.length === 0) {
    return;
  }

  // Update the last read message ID for the user in this conversation
  await supabase
    .from('chat_participants')
    .update({ last_read_message_id: messageIds[messageIds.length - 1] })
    .eq('conversation_id', conversationId)
    .eq('user_id', currentUserId);

  // Mark individual messages as read
  const statusUpdates = messageIds.map((messageId) => ({
    message_id: messageId,
    user_id: currentUserId,
    is_read: true,
    read_at: new Date().toISOString(),
  }));

  await supabase.from('chat_message_status').upsert(statusUpdates, {
    onConflict: 'message_id,user_id',
  });
};

/**
 * Helper function to get a message's created_at timestamp by its ID
 */
const getMessageCreatedAt = async (messageId: string): Promise<string | null> => {
  const { data, error } = await supabase
    .from('chat_messages')
    .select('created_at')
    .eq('id', messageId)
    .maybeSingle();

  if (error || !data) {
    return null;
  }

  return data.created_at;
};

/**
 * Subscribe to new messages in a conversation
 */
/**
 * Subscribe to conversation updates (new conversations, updates to existing ones)
 */
export const subscribeToConversations = (
  organizationId: string,
  onConversationUpdate: (conversation: ChatConversation) => void,
  onConversationDelete: (conversationId: string) => void,
) => {
  console.log(
    `Setting up realtime subscription for conversations in organization ${organizationId}`,
  );

  // Create a channel with a consistent name for this organization
  const channelName = `org-conversations:${organizationId}`;
  console.log(`Creating channel with name: ${channelName}`);

  // First, remove any existing channels with similar names to avoid duplicates
  supabase.getChannels().forEach((existingChannel) => {
    if (existingChannel.topic.includes(`org-conversations:${organizationId}`)) {
      console.log(`Removing existing channel: ${existingChannel.topic}`);
      supabase.removeChannel(existingChannel);
    }
  });

  const channel = supabase
    .channel(channelName)
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'chat_conversations',
        filter: `organization_id=eq.${organizationId}`,
      },
      (payload) => {
        console.log('New conversation created via realtime:', payload.new);
        onConversationUpdate(payload.new as ChatConversation);
      },
    )
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'chat_conversations',
        filter: `organization_id=eq.${organizationId}`,
      },
      (payload) => {
        console.log('Conversation updated via realtime:', payload.new);
        onConversationUpdate(payload.new as ChatConversation);
      },
    )
    .on(
      'postgres_changes',
      {
        event: 'DELETE',
        schema: 'public',
        table: 'chat_conversations',
        filter: `organization_id=eq.${organizationId}`,
      },
      (payload) => {
        console.log('Conversation deleted via realtime:', payload.old);
        onConversationDelete(payload.old.id);
      },
    );

  // Subscribe to the channel with error handling
  channel.subscribe((status) => {
    console.log(`Subscription status for organization conversations ${organizationId}:`, status);

    if (status === 'SUBSCRIBED') {
      console.log(`Successfully subscribed to organization conversations ${organizationId}`);
    } else if (status === 'CHANNEL_ERROR') {
      console.error(`Error subscribing to organization conversations ${organizationId}`);
      // Try to reconnect after a short delay
      setTimeout(() => {
        console.log(`Attempting to reconnect to organization conversations ${organizationId}`);
        channel.subscribe();
      }, 2000);
    } else if (status === 'TIMED_OUT') {
      console.error(`Subscription timed out for organization conversations ${organizationId}`);
      // Attempt to reconnect
      setTimeout(() => {
        console.log(`Attempting to reconnect to organization conversations ${organizationId}`);
        channel.subscribe();
      }, 2000);
    }
  });

  return () => {
    console.log(`Removing realtime subscription for organization conversations ${organizationId}`);
    supabase.removeChannel(channel);
  };
};

/**
 * Subscribe to new messages in a conversation
 */
export const subscribeToConversation = (
  conversationId: string,
  onNewMessage: (message: ChatMessage) => void,
) => {
  console.log(`Setting up realtime subscription for conversation ${conversationId}`);

  // Create a channel with a consistent name for this conversation
  const channelName = `conversation:${conversationId}`;
  console.log(`Creating channel with name: ${channelName}`);

  // First, remove any existing channels with similar names to avoid duplicates
  supabase.getChannels().forEach((existingChannel) => {
    if (existingChannel.topic.includes(`conversation:${conversationId}`)) {
      console.log(`Removing existing channel: ${existingChannel.topic}`);
      supabase.removeChannel(existingChannel);
    }
  });

  // Create a new channel with a unique name
  const channel = supabase
    .channel(channelName)
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'chat_messages',
        filter: `conversation_id=eq.${conversationId}`,
      },
      (payload) => {
        console.log('Received message via realtime:', payload.new);

        // Don't fetch sender info here to reduce API calls
        // The ChatContext will handle enriching the message with sender data
        // from its existing state or fetch it if needed
        onNewMessage(payload.new as ChatMessage);
      },
    )
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'chat_messages',
        filter: `conversation_id=eq.${conversationId}`,
      },
      (payload) => {
        console.log('Message updated via realtime:', payload.new);
        onNewMessage(payload.new as ChatMessage);
      },
    )
    .on(
      'postgres_changes',
      {
        event: 'DELETE',
        schema: 'public',
        table: 'chat_messages',
        filter: `conversation_id=eq.${conversationId}`,
      },
      (payload) => {
        console.log('Message deleted via realtime:', payload.old);
        // Handle message deletion in the UI if needed
      },
    );

  // Subscribe to the channel with error handling
  channel.subscribe((status) => {
    console.log(`Subscription status for conversation ${conversationId}:`, status);

    if (status === 'SUBSCRIBED') {
      console.log(`Successfully subscribed to conversation ${conversationId}`);
    } else if (status === 'CHANNEL_ERROR') {
      console.error(`Error subscribing to conversation ${conversationId}`);
      // Try to reconnect after a short delay
      setTimeout(() => {
        console.log(`Attempting to reconnect to conversation ${conversationId}`);
        channel.subscribe();
      }, 2000);
    } else if (status === 'TIMED_OUT') {
      console.error(`Subscription timed out for conversation ${conversationId}`);
      // Attempt to reconnect
      setTimeout(() => {
        console.log(`Attempting to reconnect to conversation ${conversationId}`);
        channel.subscribe();
      }, 2000);
    }
  });

  return () => {
    console.log(`Removing realtime subscription for conversation ${conversationId}`);
    supabase.removeChannel(channel);
  };
};

/**
 * Leave a group conversation
 */
export const leaveConversation = async (conversationId: string): Promise<void> => {
  const userId = (await supabase.auth.getUser()).data.user?.id;

  if (!userId) {
    throw new Error('User not authenticated');
  }

  // Check if this is a group conversation
  const { data: conversation, error: conversationError } = await supabase
    .from('chat_conversations')
    .select('is_group, created_by')
    .eq('id', conversationId)
    .maybeSingle();

  if (!conversation) {
    throw new Error('Conversation not found');
  }

  if (conversationError) {
    throw new Error('Failed to fetch conversation');
  }

  if (!conversation.is_group) {
    throw new Error('Cannot leave a one-on-one conversation');
  }

  // Check if user is the creator and there are other participants
  if (conversation.created_by === userId) {
    // Count other participants
    const { data: otherParticipants, error: participantsError } = await supabase
      .from('chat_participants')
      .select('id, user_id, is_admin')
      .eq('conversation_id', conversationId)
      .neq('user_id', userId);

    if (participantsError) {
      throw new Error('Failed to check other participants');
    }

    // If there are no other participants, just delete the group
    if (otherParticipants.length === 0) {
      console.log('No other participants, deleting the group');

      // Delete the conversation directly without permission checks
      // since we already know this user is the creator

      // Delete all messages in the conversation
      const { error: messagesError } = await supabase
        .from('chat_messages')
        .delete()
        .eq('conversation_id', conversationId);

      if (messagesError) {
        throw messagesError;
      }

      // Delete all participants (which is just the current user)
      const { error: participantsError } = await supabase
        .from('chat_participants')
        .delete()
        .eq('conversation_id', conversationId);

      if (participantsError) {
        throw participantsError;
      }

      // Finally, delete the conversation
      const { error: deleteConversationError } = await supabase
        .from('chat_conversations')
        .delete()
        .eq('id', conversationId);

      if (deleteConversationError) {
        throw deleteConversationError;
      }

      return;
    }

    // If there are other participants but no admins, make someone else an admin
    const otherAdmins = otherParticipants.filter((p) => p.is_admin);

    if (otherAdmins.length === 0) {
      // Make the first participant an admin
      const { error: updateError } = await supabase
        .from('chat_participants')
        .update({ is_admin: true })
        .eq('id', otherParticipants[0].id);

      if (updateError) {
        throw new Error('Failed to assign new admin');
      }
    }
  }

  // Check if user is the last participant (regardless of creator status)
  const { data: allParticipants, error: allParticipantsError } = await supabase
    .from('chat_participants')
    .select('id, user_id')
    .eq('conversation_id', conversationId);

  if (allParticipantsError) {
    throw new Error('Failed to check participants');
  }

  // If you're the only participant left, delete the entire conversation
  if (allParticipants.length === 1 && allParticipants[0].user_id === userId) {
    console.log('User is the only participant, deleting the group');

    // Delete the conversation directly without permission checks

    // Delete all messages in the conversation
    const { error: messagesError } = await supabase
      .from('chat_messages')
      .delete()
      .eq('conversation_id', conversationId);

    if (messagesError) {
      throw messagesError;
    }

    // Delete all participants (which is just the current user)
    const { error: participantsError } = await supabase
      .from('chat_participants')
      .delete()
      .eq('conversation_id', conversationId);

    if (participantsError) {
      throw participantsError;
    }

    // Finally, delete the conversation
    const { error: deleteConversationError } = await supabase
      .from('chat_conversations')
      .delete()
      .eq('id', conversationId);

    if (deleteConversationError) {
      throw deleteConversationError;
    }

    return;
  }

  // Otherwise, just remove the user from the conversation
  const { error: removeError } = await supabase
    .from('chat_participants')
    .delete()
    .eq('conversation_id', conversationId)
    .eq('user_id', userId);

  if (removeError) {
    throw removeError;
  }
};

/**
 * Delete a conversation
 */
export const deleteConversation = async (conversationId: string): Promise<void> => {
  const userId = (await supabase.auth.getUser()).data.user?.id;

  if (!userId) {
    throw new Error('User not authenticated');
  }

  // Check if the user is an admin of the conversation or the creator
  const { data: conversation, error: conversationError } = await supabase
    .from('chat_conversations')
    .select('created_by')
    .eq('id', conversationId)
    .maybeSingle();

  if (conversationError) {
    throw new Error('Failed to fetch conversation');
  }

  if (!conversation) {
    throw new Error('Conversation not found');
  }

  // Check if the user is a participant and if they're an admin
  const { data: participant, error: participantError } = await supabase
    .from('chat_participants')
    .select('is_admin')
    .eq('conversation_id', conversationId)
    .eq('user_id', userId)
    .maybeSingle(); // Use maybeSingle instead of single to handle case where user might not be a participant anymore

  // Allow if user is the creator of the conversation
  if (conversation.created_by === userId) {
    // Creator can always delete their conversations
    console.log('User is the creator, allowing deletion');
  }
  // Or if they're an admin participant
  else if (participant && participant.is_admin) {
    console.log('User is an admin, allowing deletion');
  }
  // Otherwise, deny permission
  else if (!participantError) {
    // Only check if there was no error fetching participant
    throw new Error('Only admins or the creator can delete conversations');
  }

  // Delete all messages in the conversation
  const { error: messagesError } = await supabase
    .from('chat_messages')
    .delete()
    .eq('conversation_id', conversationId);

  if (messagesError) {
    throw messagesError;
  }

  // Delete all message statuses related to this conversation
  // First get all message IDs
  const { data: messageIds } = await supabase
    .from('chat_messages')
    .select('id')
    .eq('conversation_id', conversationId);

  if (messageIds && messageIds.length > 0) {
    const ids = messageIds.map((m) => m.id);

    // Then delete statuses for these messages
    const { error: statusError } = await supabase
      .from('chat_message_status')
      .delete()
      .in('message_id', ids);

    if (statusError) {
      console.error('Error deleting message statuses:', statusError);
      // Continue anyway
    }
  }

  // Delete all participants
  const { error: participantsError } = await supabase
    .from('chat_participants')
    .delete()
    .eq('conversation_id', conversationId);

  if (participantsError) {
    throw participantsError;
  }

  // Finally, delete the conversation
  const { error: deleteConversationError } = await supabase
    .from('chat_conversations')
    .delete()
    .eq('id', conversationId);

  if (deleteConversationError) {
    throw deleteConversationError;
  }
};
