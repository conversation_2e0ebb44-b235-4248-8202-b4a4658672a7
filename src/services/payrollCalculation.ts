import { supabase } from '../lib/supabase';
import {
  PayrollPeriod,
  PayrollItem,
  PayrollItemStatus,
  PayrollCalculationResult,
  PayrollCalculationInput,
  DeductionType,
  AllowanceType,
  RateType,
  TimeEntry,
  TimeEntryStatus
} from '../types/payroll';
import { Employee } from './employee';
import { getPayrollSettings } from './payroll';

/**
 * Calculate SSS contribution based on monthly salary
 * @param monthlySalary Monthly salary
 * @returns SSS contribution for employee and employer
 */
export const calculateSSSContribution = (monthlySalary: number): {
  employeeShare: number;
  employerShare: number;
} => {
  // SSS Contribution Table (2023)
  // This is a simplified version of the SSS contribution table
  const sssTable = [
    { min: 0, max: 3249.99, employeeShare: 135, employerShare: 255 },
    { min: 3250, max: 3749.99, employeeShare: 157.50, employerShare: 297.50 },
    { min: 3750, max: 4249.99, employeeShare: 180, employerShare: 340 },
    { min: 4250, max: 4749.99, employeeShare: 202.50, employerShare: 382.50 },
    { min: 4750, max: 5249.99, employeeShare: 225, employerShare: 425 },
    { min: 5250, max: 5749.99, employeeShare: 247.50, employerShare: 467.50 },
    { min: 5750, max: 6249.99, employeeShare: 270, employerShare: 510 },
    { min: 6250, max: 6749.99, employeeShare: 292.50, employerShare: 552.50 },
    { min: 6750, max: 7249.99, employeeShare: 315, employerShare: 595 },
    { min: 7250, max: 7749.99, employeeShare: 337.50, employerShare: 637.50 },
    { min: 7750, max: 8249.99, employeeShare: 360, employerShare: 680 },
    { min: 8250, max: 8749.99, employeeShare: 382.50, employerShare: 722.50 },
    { min: 8750, max: 9249.99, employeeShare: 405, employerShare: 765 },
    { min: 9250, max: 9749.99, employeeShare: 427.50, employerShare: 807.50 },
    { min: 9750, max: 10249.99, employeeShare: 450, employerShare: 850 },
    { min: 10250, max: 10749.99, employeeShare: 472.50, employerShare: 892.50 },
    { min: 10750, max: 11249.99, employeeShare: 495, employerShare: 935 },
    { min: 11250, max: 11749.99, employeeShare: 517.50, employerShare: 977.50 },
    { min: 11750, max: 12249.99, employeeShare: 540, employerShare: 1020 },
    { min: 12250, max: 12749.99, employeeShare: 562.50, employerShare: 1062.50 },
    { min: 12750, max: 13249.99, employeeShare: 585, employerShare: 1105 },
    { min: 13250, max: 13749.99, employeeShare: 607.50, employerShare: 1147.50 },
    { min: 13750, max: 14249.99, employeeShare: 630, employerShare: 1190 },
    { min: 14250, max: 14749.99, employeeShare: 652.50, employerShare: 1232.50 },
    { min: 14750, max: 15249.99, employeeShare: 675, employerShare: 1275 },
    { min: 15250, max: 15749.99, employeeShare: 697.50, employerShare: 1317.50 },
    { min: 15750, max: 16249.99, employeeShare: 720, employerShare: 1360 },
    { min: 16250, max: 16749.99, employeeShare: 742.50, employerShare: 1402.50 },
    { min: 16750, max: 17249.99, employeeShare: 765, employerShare: 1445 },
    { min: 17250, max: 17749.99, employeeShare: 787.50, employerShare: 1487.50 },
    { min: 17750, max: 18249.99, employeeShare: 810, employerShare: 1530 },
    { min: 18250, max: 18749.99, employeeShare: 832.50, employerShare: 1572.50 },
    { min: 18750, max: 19249.99, employeeShare: 855, employerShare: 1615 },
    { min: 19250, max: 19749.99, employeeShare: 877.50, employerShare: 1657.50 },
    { min: 19750, max: 20249.99, employeeShare: 900, employerShare: 1700 },
    { min: 20250, max: 20749.99, employeeShare: 922.50, employerShare: 1742.50 },
    { min: 20750, max: 21249.99, employeeShare: 945, employerShare: 1785 },
    { min: 21250, max: 21749.99, employeeShare: 967.50, employerShare: 1827.50 },
    { min: 21750, max: 22249.99, employeeShare: 990, employerShare: 1870 },
    { min: 22250, max: 22749.99, employeeShare: 1012.50, employerShare: 1912.50 },
    { min: 22750, max: 23249.99, employeeShare: 1035, employerShare: 1955 },
    { min: 23250, max: 23749.99, employeeShare: 1057.50, employerShare: 1997.50 },
    { min: 23750, max: 24249.99, employeeShare: 1080, employerShare: 2040 },
    { min: 24250, max: 24749.99, employeeShare: 1102.50, employerShare: 2082.50 },
    { min: 24750, max: Infinity, employeeShare: 1125, employerShare: 2125 }
  ];

  // Find the appropriate bracket
  const bracket = sssTable.find(
    bracket => monthlySalary >= bracket.min && monthlySalary <= bracket.max
  ) || sssTable[sssTable.length - 1]; // Use the highest bracket if salary exceeds the table

  return {
    employeeShare: bracket.employeeShare,
    employerShare: bracket.employerShare
  };
};

/**
 * Calculate PhilHealth contribution based on monthly salary
 * @param monthlySalary Monthly salary
 * @returns PhilHealth contribution for employee and employer
 */
export const calculatePhilHealthContribution = (monthlySalary: number): {
  employeeShare: number;
  employerShare: number;
} => {
  // PhilHealth Contribution (2023)
  // 2% of monthly salary, split equally between employee and employer
  // Minimum monthly salary: 10,000
  // Maximum monthly salary: 80,000

  const adjustedSalary = Math.min(Math.max(monthlySalary, 10000), 80000);
  const totalContribution = adjustedSalary * 0.04; // 4% total
  const employeeShare = totalContribution / 2;
  const employerShare = totalContribution / 2;

  return {
    employeeShare,
    employerShare
  };
};

/**
 * Calculate Pag-IBIG contribution based on monthly salary
 * @param monthlySalary Monthly salary
 * @returns Pag-IBIG contribution for employee and employer
 */
export const calculatePagIbigContribution = (monthlySalary: number): {
  employeeShare: number;
  employerShare: number;
} => {
  // Pag-IBIG Contribution (2023)
  // For monthly salary up to 1,500: 1% employee, 2% employer
  // For monthly salary over 1,500: 2% employee, 2% employer
  // Maximum monthly contribution: 100 for employee, 100 for employer

  let employeeRate = monthlySalary <= 1500 ? 0.01 : 0.02;
  let employerRate = 0.02;

  let employeeShare = Math.min(monthlySalary * employeeRate, 100);
  let employerShare = Math.min(monthlySalary * employerRate, 100);

  return {
    employeeShare,
    employerShare
  };
};

/**
 * Calculate withholding tax based on taxable income
 * @param taxableIncome Taxable income
 * @returns Withholding tax amount
 */
export const calculateWithholdingTax = (taxableIncome: number): number => {
  // Withholding Tax Table (2023)
  // This is a simplified version of the withholding tax table
  if (taxableIncome <= 20833) {
    return 0;
  } else if (taxableIncome <= 33332) {
    return (taxableIncome - 20833) * 0.15;
  } else if (taxableIncome <= 66666) {
    return 1875 + (taxableIncome - 33333) * 0.20;
  } else if (taxableIncome <= 166666) {
    return 8541.80 + (taxableIncome - 66667) * 0.25;
  } else if (taxableIncome <= 666666) {
    return 33541.80 + (taxableIncome - 166667) * 0.30;
  } else {
    return 183541.80 + (taxableIncome - 666667) * 0.35;
  }
};

/**
 * Calculate regular day pay
 * @param hourlyRate Hourly rate
 * @param regularHours Regular hours
 * @returns Regular day pay amount
 */
export const calculateRegularPay = (
  hourlyRate: number,
  regularHours: number
): number => {
  return hourlyRate * regularHours;
};

/**
 * Calculate rest day pay
 * @param hourlyRate Hourly rate
 * @param hours Hours worked on rest day
 * @returns Rest day pay amount
 */
export const calculateRestDayPay = (
  hourlyRate: number,
  hours: number
): number => {
  // Rest day pay is 130% of regular rate
  return hourlyRate * hours * 1.3;
};

/**
 * Calculate holiday pay
 * @param hourlyRate Hourly rate
 * @param hours Hours worked on holiday
 * @param holidayType Type of holiday (regular or special)
 * @param isRestDay Whether the holiday falls on a rest day
 * @returns Holiday pay amount
 */
export const calculateHolidayPay = (
  hourlyRate: number,
  hours: number,
  holidayType: 'regular' | 'special',
  isRestDay: boolean = false
): number => {
  let rate = 1.0;

  if (holidayType === 'regular') {
    // Regular holiday: 200% of regular rate
    // If it falls on rest day: 260% (200% holiday + 30% rest day premium)
    rate = isRestDay ? 2.6 : 2.0;
  } else {
    // Special holiday: 130% of regular rate
    // If it falls on rest day: 150% (130% holiday + 20% additional)
    rate = isRestDay ? 1.5 : 1.3;
  }

  return hourlyRate * hours * rate;
};

/**
 * Calculate overtime pay
 * @param hourlyRate Hourly rate
 * @param overtimeHours Overtime hours
 * @param isRestDay Whether it's a rest day
 * @param isHoliday Whether it's a holiday
 * @param holidayType Type of holiday (regular or special)
 * @returns Overtime pay amount
 */
export const calculateOvertimePay = (
  hourlyRate: number,
  overtimeHours: number,
  isRestDay: boolean = false,
  isHoliday: boolean = false,
  holidayType: 'regular' | 'special' = 'regular'
): number => {
  let overtimeRate = 1.25; // Regular overtime (125%)

  if (isRestDay && !isHoliday) {
    // Rest day overtime: 169% (130% rest day * 1.3 overtime)
    overtimeRate = 1.69;
  } else if (isHoliday && !isRestDay) {
    if (holidayType === 'regular') {
      // Regular holiday overtime: 260% (200% holiday * 1.3 overtime)
      overtimeRate = 2.6;
    } else {
      // Special holiday overtime: 169% (130% special holiday * 1.3 overtime)
      overtimeRate = 1.69;
    }
  } else if (isHoliday && isRestDay) {
    if (holidayType === 'regular') {
      // Rest day and regular holiday overtime: 338% (260% rest day holiday * 1.3 overtime)
      overtimeRate = 3.38;
    } else {
      // Rest day and special holiday overtime: 195% (150% rest day special holiday * 1.3 overtime)
      overtimeRate = 1.95;
    }
  }

  return hourlyRate * overtimeHours * overtimeRate;
};

/**
 * Calculate night differential pay
 * @param hourlyRate Hourly rate
 * @param nightDiffHours Night differential hours
 * @returns Night differential pay amount
 */
export const calculateNightDifferentialPay = (
  hourlyRate: number,
  nightDiffHours: number
): number => {
  // Night differential is 10% of hourly rate
  return hourlyRate * nightDiffHours * 0.1;
};

/**
 * Calculate the number of working days in a pay period
 * @param startDate Start date of the period
 * @param endDate End date of the period
 * @param paySchedule Pay schedule (monthly, semi-monthly, weekly, daily)
 * @returns Number of working days in the period
 */
export const calculateDaysInPeriod = (
  startDate: string,
  endDate: string,
  paySchedule: string
): number => {
  // Convert dates to Date objects
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Calculate total days in period (inclusive)
  const totalDays = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

  // For daily pay schedule, return the total days
  if (paySchedule === 'daily') {
    return totalDays;
  }

  // For weekly pay schedule, return 5 days per week (assuming 5-day work week)
  if (paySchedule === 'weekly') {
    return Math.min(5, totalDays);
  }

  // For semi-monthly, typically 11 working days (half of 22)
  if (paySchedule === 'semi-monthly') {
    return 11;
  }

  // For monthly, standard 22 working days
  return 22;
};

/**
 * Calculate 13th month pay
 * @param organizationId Organization ID
 * @param employeeId Employee ID
 * @param year Year to calculate 13th month pay for
 * @returns 13th month pay amount
 */
export const calculate13thMonthPay = async (
  organizationId: string,
  employeeId: string,
  year: number
): Promise<number> => {
  try {
    // Get all payroll items for the employee for the year
    const { data: payrollItems } = await supabase
      .from('payroll_items')
      .select('basic_pay, is_thirteenth_month')
      .eq('organization_id', organizationId)
      .eq('employee_id', employeeId)
      .eq('is_thirteenth_month', false) // Exclude previous 13th month payments
      .gte('created_at', `${year}-01-01`)
      .lte('created_at', `${year}-12-31`);

    if (!payrollItems || payrollItems.length === 0) {
      // If no payroll items, get the employee's current salary
      const { data: salaryData } = await supabase
        .from('employee_salary')
        .select('*')
        .eq('organization_id', organizationId)
        .eq('employee_id', employeeId)
        .single();

      if (!salaryData) {
        return 0;
      }

      // Return one month's salary as 13th month pay
      // Use basic_salary for monthly rate or calculate monthly from daily rate
      if (salaryData.rate_type === 'daily') {
        // Convert daily rate to monthly (daily rate * 22 working days)
        return Number(salaryData.daily_rate) * 22;
      } else {
        // Use basic_salary for monthly rate
        return Number(salaryData.basic_salary);
      }
    }

    // Calculate total basic pay for the year
    const totalBasicPay = payrollItems.reduce((sum, item) => sum + Number(item.basic_pay), 0);

    // 13th month pay is 1/12 of total basic pay for the year
    // or prorated based on months worked
    const monthsWorked = Math.min(12, payrollItems.length);
    return (totalBasicPay / 12) * monthsWorked;
  } catch (error) {
    console.error('Error calculating 13th month pay:', error);
    return 0;
  }
};

/**
 * Process payroll for an employee
 * @param organizationId Organization ID
 * @param periodId Payroll period ID
 * @param employeeId Employee ID
 * @returns Payroll calculation result
 */
export const processEmployeePayroll = async (
  organizationId: string,
  periodId: string,
  employeeId: string
): Promise<PayrollCalculationResult> => {
  try {
    // Get payroll period
    const { data: periodData } = await supabase
      .from('payroll_periods')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('id', periodId)
      .single();

    if (!periodData) {
      throw new Error('Payroll period not found');
    }

    const period = periodData as PayrollPeriod;

    // Get employee salary
    const { data: salaryData } = await supabase
      .from('employee_salary')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('employee_id', employeeId)
      .single();

    if (!salaryData) {
      throw new Error('Employee salary not found');
    }

    // Get employee allowances
    const { data: allowancesData } = await supabase
      .from('employee_allowances')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('employee_id', employeeId);

    // Get employee deductions
    const { data: deductionsData } = await supabase
      .from('employee_deductions')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('employee_id', employeeId);

    // Get time entries for the period
    const { data: timeEntriesData } = await supabase
      .from('time_entries')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('employee_id', employeeId)
      .gte('date', period.start_date)
      .lte('date', period.end_date);

    // Get holidays for the period
    const { data: holidaysData } = await supabase
      .from('holidays')
      .select('*')
      .eq('organization_id', organizationId)
      .gte('date', period.start_date)
      .lte('date', period.end_date);

    // Get payroll settings
    const { settings } = await getPayrollSettings(organizationId);

    if (!settings) {
      throw new Error('Payroll settings not found');
    }

    // Calculate basic pay based on rate type
    let monthlySalary = 0;
    let dailyRate = 0;
    let basicPay = 0;
    let hourlyRate = 0;

    // Determine salary based on rate type
    if (salaryData.rate_type === 'daily') {
      // If daily rate is the primary rate
      dailyRate = Number(salaryData.daily_rate);
      monthlySalary = dailyRate * 22; // Convert to monthly (22 working days)
      hourlyRate = dailyRate / 8; // Daily rate / 8 hours
    } else {
      // If monthly rate is the primary rate
      monthlySalary = Number(salaryData.basic_salary);
      dailyRate = monthlySalary / 22; // Convert to daily
      hourlyRate = dailyRate / 8; // Daily rate / 8 hours
    }

    // Process time entries to calculate pay
    let regularPay = 0;
    let overtimePay = 0;
    let restDayPay = 0;
    let holidayPay = 0;
    let nightDifferentialPay = 0;

    // Track total hours for reporting
    let totalRegularHours = 0;
    let totalOvertimeHours = 0;
    let totalRestDayHours = 0;
    let totalHolidayHours = 0;
    let totalNightDiffHours = 0;

    if (timeEntriesData && timeEntriesData.length > 0) {
      // Process each time entry
      timeEntriesData.forEach((entry: TimeEntry) => {
        // Check if the date is a holiday
        const isHoliday = entry.is_holiday ||
          (holidaysData && holidaysData.some(h => h.date === entry.date));

        const holidayType = entry.holiday_type ||
          (isHoliday && holidaysData?.find(h => h.date === entry.date)?.type) ||
          'regular';

        const isRestDay = entry.is_rest_day;

        // Calculate regular hours pay
        if (entry.regular_hours > 0) {
          if (isHoliday) {
            // Holiday pay
            holidayPay += calculateHolidayPay(
              hourlyRate,
              entry.regular_hours,
              holidayType as 'regular' | 'special',
              isRestDay
            );
            totalHolidayHours += entry.regular_hours;
          } else if (isRestDay) {
            // Rest day pay
            restDayPay += calculateRestDayPay(hourlyRate, entry.regular_hours);
            totalRestDayHours += entry.regular_hours;
          } else {
            // Regular day pay
            regularPay += calculateRegularPay(hourlyRate, entry.regular_hours);
            totalRegularHours += entry.regular_hours;
          }
        }

        // Calculate overtime pay
        if (entry.overtime_hours > 0) {
          overtimePay += calculateOvertimePay(
            hourlyRate,
            entry.overtime_hours,
            isRestDay,
            isHoliday,
            holidayType as 'regular' | 'special'
          );
          totalOvertimeHours += entry.overtime_hours;
        }

        // Calculate night differential pay
        if (entry.night_diff_hours > 0) {
          nightDifferentialPay += calculateNightDifferentialPay(hourlyRate, entry.night_diff_hours);
          totalNightDiffHours += entry.night_diff_hours;
        }
      });

      // Total pay from time entries
      const timeEntryBasedPay = regularPay + overtimePay + restDayPay + holidayPay + nightDifferentialPay;

      // Calculate basic pay based on rate type and time entries
      // Default to true if pay_based_on_time_entries is not set (for backward compatibility)
      const useTimeEntries = settings.pay_based_on_time_entries ?? true;

      if (salaryData.rate_type === 'daily') {
        // For daily rate employees
        if (useTimeEntries && timeEntriesData && timeEntriesData.length > 0) {
          // Calculate based on actual days worked from time entries
          const actualDaysWorked = timeEntriesData.filter(entry =>
            entry.status === 'present' && (entry.regular_hours > 0 || entry.overtime_hours > 0)
          ).length;

          // Use time entry based pay if available, otherwise use daily rate × actual days
          if (timeEntryBasedPay > 0) {
            basicPay = timeEntryBasedPay;
          } else {
            basicPay = dailyRate * actualDaysWorked;
          }
        } else {
          // No time entries or setting disabled - calculate based on scheduled days
          const daysInPeriod = calculateDaysInPeriod(period.start_date, period.end_date, settings.pay_schedule);
          basicPay = dailyRate * daysInPeriod;
        }
      } else {
        // For monthly rate employees
        if (useTimeEntries && timeEntriesData && timeEntriesData.length > 0) {
          // For monthly employees with time entries, use time-based calculation
          if (timeEntryBasedPay > 0) {
            basicPay = timeEntryBasedPay;
          } else {
            // Calculate prorated salary based on actual days worked
            const actualDaysWorked = timeEntriesData.filter(entry =>
              entry.status === 'present' && (entry.regular_hours > 0 || entry.overtime_hours > 0)
            ).length;
            const expectedDaysInPeriod = calculateDaysInPeriod(period.start_date, period.end_date, settings.pay_schedule);

            if (settings.pay_schedule === 'monthly') {
              basicPay = (monthlySalary / expectedDaysInPeriod) * actualDaysWorked;
            } else if (settings.pay_schedule === 'semi-monthly') {
              basicPay = ((monthlySalary / 2) / expectedDaysInPeriod) * actualDaysWorked;
            } else if (settings.pay_schedule === 'weekly') {
              basicPay = (((monthlySalary * 12) / 52) / expectedDaysInPeriod) * actualDaysWorked;
            } else {
              basicPay = dailyRate * actualDaysWorked;
            }
          }
        } else {
          // No time entries or setting disabled - use full scheduled salary
          if (settings.pay_schedule === 'monthly') {
            basicPay = monthlySalary;
          } else if (settings.pay_schedule === 'semi-monthly') {
            basicPay = monthlySalary / 2;
          } else if (settings.pay_schedule === 'weekly') {
            basicPay = (monthlySalary * 12) / 52;
          } else if (settings.pay_schedule === 'daily') {
            const daysInPeriod = calculateDaysInPeriod(period.start_date, period.end_date, settings.pay_schedule);
            basicPay = dailyRate * daysInPeriod;
          }
        }
      }
    } else {
      // No time entries available
      if (useTimeEntries) {
        // If pay is based on time entries but none exist, pay should be 0
        basicPay = 0;
      } else {
        // Calculate based on scheduled period (traditional payroll)
        if (salaryData.rate_type === 'daily') {
          const daysInPeriod = calculateDaysInPeriod(period.start_date, period.end_date, settings.pay_schedule);
          basicPay = dailyRate * daysInPeriod;
        } else {
          // For monthly rate employees
          if (settings.pay_schedule === 'monthly') {
            basicPay = monthlySalary;
          } else if (settings.pay_schedule === 'semi-monthly') {
            basicPay = monthlySalary / 2;
          } else if (settings.pay_schedule === 'weekly') {
            basicPay = (monthlySalary * 12) / 52;
          } else if (settings.pay_schedule === 'daily') {
            const daysInPeriod = calculateDaysInPeriod(period.start_date, period.end_date, settings.pay_schedule);
            basicPay = dailyRate * daysInPeriod;
          }
        }
      }
    }

    // Calculate allowances
    const allowances = allowancesData?.map(allowance => ({
      name: allowance.name,
      amount: Number(allowance.amount),
      type: allowance.type as AllowanceType
    })) || [];

    const totalAllowances = allowances.reduce((sum, allowance) => sum + allowance.amount, 0);

    // Calculate deductions
    const deductions = deductionsData?.map(deduction => ({
      name: deduction.name,
      amount: Number(deduction.amount),
      type: deduction.type as DeductionType
    })) || [];

    // Calculate government contributions
    const sssContribution = calculateSSSContribution(monthlySalary);
    const philhealthContribution = calculatePhilHealthContribution(monthlySalary);
    const pagibigContribution = calculatePagIbigContribution(monthlySalary);

    // Add government contributions to deductions
    deductions.push({
      name: 'SSS Contribution',
      amount: sssContribution.employeeShare,
      type: DeductionType.GOVERNMENT
    });

    deductions.push({
      name: 'PhilHealth Contribution',
      amount: philhealthContribution.employeeShare,
      type: DeductionType.GOVERNMENT
    });

    deductions.push({
      name: 'Pag-IBIG Contribution',
      amount: pagibigContribution.employeeShare,
      type: DeductionType.GOVERNMENT
    });

    const totalDeductions = deductions.reduce((sum, deduction) => sum + deduction.amount, 0);

    // Calculate taxable income
    const taxableIncome = basicPay + totalAllowances - deductions
      .filter(d => d.type === DeductionType.GOVERNMENT)
      .reduce((sum, d) => sum + d.amount, 0);

    // Calculate withholding tax
    const withholdingTax = calculateWithholdingTax(taxableIncome);

    // Add withholding tax to deductions
    deductions.push({
      name: 'Withholding Tax',
      amount: withholdingTax,
      type: DeductionType.TAX
    });

    // Calculate gross pay and net pay
    const grossPay = basicPay + totalAllowances;
    const netPay = grossPay - totalDeductions - withholdingTax;

    // Add time entry details to the result
    const timeEntryDetails = {
      regularPay,
      overtimePay,
      restDayPay,
      holidayPay,
      nightDifferentialPay,
      totalRegularHours,
      totalOvertimeHours,
      totalRestDayHours,
      totalHolidayHours,
      totalNightDiffHours
    };

    return {
      employeeId,
      periodId,
      basicPay,
      grossPay,
      netPay,
      totalAllowances,
      totalDeductions: totalDeductions + withholdingTax,
      allowances,
      deductions,
      taxableIncome,
      withholdingTax,
      sssContribution,
      philhealthContribution,
      pagibigContribution,
      timeEntryDetails
    };
  } catch (error: any) {
    console.error('Error processing employee payroll:', error);
    throw new Error(`Failed to process payroll: ${error.message}`);
  }
};
