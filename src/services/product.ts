import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';

// Base product type from database
export type Product = Database['public']['Tables']['products']['Row'] & {
  category?: {
    id: string;
    name: string;
    description: string | null;
    organization_id?: string;
    created_at?: string;
    updated_at?: string;
  };
};

// Extended product type with UoMs (for when we specifically fetch UoMs)
export type ProductWithUoms = Product & {
  product_uoms?: Array<{
    id: string;
    product_id: string;
    uom_id: string;
    conversion_factor: number;
    is_default: boolean;
    is_purchasing_unit: boolean;
    is_selling_unit: boolean;
    created_by: string | null;
    created_at: string;
    updated_at: string;
    organization_id: string;
    uom: {
      id: string;
      code: string;
      name: string;
      is_active: boolean;
      created_at: string;
      created_by: string;
      updated_at: string;
      description: string;
      organization_id: string;
    };
  }>;
};
export type Category = Database['public']['Tables']['categories']['Row'];

/**
 * Get all products for an organization
 */
export const getProducts = async (
  organizationId: string,
  options?: {
    categoryId?: string;
    searchQuery?: string;
    isActive?: boolean;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    tagIds?: string[];
  },
): Promise<{
  products: Product[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('products')
      .select(
        `
        *,
        category:category_id (
          id,
          name,
          description
        )
      `,
        { count: 'exact' },
      )
      .eq('organization_id', organizationId);

    // Apply filters
    if (options?.categoryId) {
      query = query.eq('category_id', options.categoryId);
    }

    if (options?.isActive !== undefined) {
      query = query.eq('is_active', options.isActive);
    }

    if (options?.searchQuery) {
      query = query.or(
        `name.ilike.%${options.searchQuery}%,sku.ilike.%${options.searchQuery}%,barcode.ilike.%${options.searchQuery}%`,
      );
    }

    // Filter by tags if provided
    if (options?.tagIds && options.tagIds.length > 0) {
      // For now, skip tag filtering to avoid complexity
      // TODO: Implement tag filtering properly
    }

    // Apply sorting
    if (options?.sortBy) {
      query = query.order(options.sortBy, { ascending: options.sortOrder === 'asc' });
    } else {
      query = query.order('name', { ascending: true });
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching products:', error);
      return {
        products: [],
        count: 0,
        error: error.message,
      };
    }

    return {
      products: data as Product[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error in getProducts:', error);
    return {
      products: [],
      count: 0,
      error: error.message,
    };
  }
};

/**
 * Get a single product by ID
 */
export const getProductById = async (
  organizationId: string,
  productId: string,
): Promise<{
  product?: Product;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(
        `
        *,
        category:category_id (
          id,
          name,
          description
        )
      `,
      )
      .eq('organization_id', organizationId)
      .eq('id', productId)
      .single();

    if (error) {
      console.error('Error fetching product:', error);
      return {
        error: error.message,
      };
    }

    return {
      product: data as Product,
    };
  } catch (error: any) {
    console.error('Error in getProductById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Get a single product by ID with UoMs included
 */
export const getProductByIdWithUoms = async (
  organizationId: string,
  productId: string,
): Promise<{
  product?: ProductWithUoms;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(
        `
        *,
        category:category_id (
          id,
          name,
          description
        ),
        product_uoms (
          *,
          uom:uom_id (*)
        )
      `,
      )
      .eq('organization_id', organizationId)
      .eq('id', productId)
      .single();

    if (error) {
      console.error('Error fetching product with UoMs:', error);
      return {
        error: error.message,
      };
    }

    return {
      product: data as ProductWithUoms,
    };
  } catch (error: any) {
    console.error('Error in getProductByIdWithUoms:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new product
 */
export const createProduct = async (
  organizationId: string,
  product: Omit<Product, 'id' | 'organization_id' | 'created_at' | 'updated_at'>,
): Promise<{
  product?: Product;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('products')
      .insert({
        ...product,
        organization_id: organizationId,
      })
      .select(
        `
        *,
        category:category_id (
          id,
          name,
          description
        )
      `,
      )
      .single();

    if (error) {
      console.error('Error creating product:', error);

      // Provide user-friendly error messages for common issues
      if (error.code === '23505') {
        if (error.message.includes('sku') || error.message.includes('products_organization_sku_unique')) {
          return {
            error: 'A product with this SKU already exists in your organization',
          };
        }
        if (error.message.includes('barcode') || error.message.includes('products_organization_barcode_unique')) {
          return {
            error: 'A product with this barcode already exists in your organization',
          };
        }
        return {
          error: 'A product with these details already exists',
        };
      }

      if (error.code === '23503') {
        return {
          error: 'Invalid category selected',
        };
      }

      return {
        error: error.message || 'Failed to create product',
      };
    }

    return {
      product: data as Product,
    };
  } catch (error: any) {
    console.error('Error in createProduct:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update an existing product
 */
export const updateProduct = async (
  organizationId: string,
  productId: string,
  updates: Partial<Omit<Product, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>,
): Promise<{
  product?: Product;
  error?: string;
}> => {
  try {
    // Validate required fields
    if (!organizationId || !productId) {
      return {
        error: 'Organization ID and Product ID are required',
      };
    }

    // Debug the updates object
    console.log('Updating product with data:', updates);
    console.log('Category ID type:', typeof updates.category_id);

    // Create a clean updates object without the category property and stock_quantity
    const cleanUpdates = { ...updates };
    if ('category' in cleanUpdates) {
      delete cleanUpdates.category;
      console.log('Removed category property from updates');
    }

    // Remove stock_quantity from manual updates - it should only be updated through inventory transactions
    if ('stock_quantity' in cleanUpdates) {
      delete cleanUpdates.stock_quantity;
      console.log('Removed stock_quantity from manual updates - use inventory transactions instead');
    }

    // Validate required fields in updates
    if (cleanUpdates.name !== undefined && (!cleanUpdates.name || cleanUpdates.name.trim() === '')) {
      return {
        error: 'Product name is required',
      };
    }

    if (cleanUpdates.unit_price !== undefined && (cleanUpdates.unit_price === null || cleanUpdates.unit_price < 0)) {
      return {
        error: 'Unit price must be a positive number',
      };
    }

    // Ensure numeric fields are properly typed
    if (cleanUpdates.unit_price !== undefined) {
      cleanUpdates.unit_price = Number(cleanUpdates.unit_price);
    }
    if (cleanUpdates.cost_price !== undefined) {
      cleanUpdates.cost_price = cleanUpdates.cost_price ? Number(cleanUpdates.cost_price) : null;
    }
    if (cleanUpdates.tax_rate !== undefined) {
      cleanUpdates.tax_rate = cleanUpdates.tax_rate ? Number(cleanUpdates.tax_rate) : null;
    }
    if (cleanUpdates.stock_quantity !== undefined) {
      cleanUpdates.stock_quantity = cleanUpdates.stock_quantity ? Number(cleanUpdates.stock_quantity) : null;
    }
    if (cleanUpdates.min_stock_level !== undefined) {
      cleanUpdates.min_stock_level = cleanUpdates.min_stock_level ? Number(cleanUpdates.min_stock_level) : null;
    }

    console.log('Clean updates object:', cleanUpdates);

    const { data, error } = await supabase
      .from('products')
      .update(cleanUpdates)
      .eq('organization_id', organizationId)
      .eq('id', productId)
      .select(
        `
        *,
        category:category_id (
          id,
          name,
          description
        )
      `,
      )
      .single();

    if (error) {
      console.error('Error updating product:', error);

      // Provide user-friendly error messages for common issues
      if (error.code === '23505') {
        if (error.message.includes('sku') || error.message.includes('products_organization_sku_unique')) {
          return {
            error: 'A product with this SKU already exists in your organization',
          };
        }
        if (error.message.includes('barcode') || error.message.includes('products_organization_barcode_unique')) {
          return {
            error: 'A product with this barcode already exists in your organization',
          };
        }
        return {
          error: 'A product with these details already exists',
        };
      }

      if (error.code === '23503') {
        return {
          error: 'Invalid category selected',
        };
      }

      return {
        error: error.message || 'Failed to update product',
      };
    }

    return {
      product: data as Product,
    };
  } catch (error: any) {
    console.error('Error in updateProduct:', error);
    return {
      error: error.message || 'An unexpected error occurred while updating the product',
    };
  }
};

/**
 * Delete a product
 */
export const deleteProduct = async (
  organizationId: string,
  productId: string,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', productId);

    if (error) {
      console.error('Error deleting product:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteProduct:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Get all categories for an organization
 */
export const getCategories = async (
  organizationId: string,
  options?: {
    searchQuery?: string;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  },
): Promise<{
  categories: Category[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('categories')
      .select('*', { count: 'exact' })
      .eq('organization_id', organizationId);

    // Apply search filter
    if (options?.searchQuery) {
      query = query.or(
        `name.ilike.%${options.searchQuery}%,description.ilike.%${options.searchQuery}%`,
      );
    }

    // Apply sorting
    if (options?.sortBy) {
      query = query.order(options.sortBy, { ascending: options.sortOrder === 'asc' });
    } else {
      query = query.order('name', { ascending: true });
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching categories:', error);
      return {
        categories: [],
        count: 0,
        error: error.message,
      };
    }

    return {
      categories: data as Category[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error in getCategories:', error);
    return {
      categories: [],
      count: 0,
      error: error.message,
    };
  }
};

/**
 * Create a new category
 */
export const createCategory = async (
  organizationId: string,
  name: string,
  description?: string,
): Promise<{
  category?: Category;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('categories')
      .insert({
        organization_id: organizationId,
        name,
        description,
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error creating category:', error);
      return {
        error: error.message,
      };
    }

    return {
      category: data as Category,
    };
  } catch (error: any) {
    console.error('Error in createCategory:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Get a category by ID
 */
export const getCategoryById = async (
  organizationId: string,
  categoryId: string,
): Promise<{
  category?: Category;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('id', categoryId)
      .single();

    if (error) {
      console.error('Error fetching category:', error);
      return {
        error: error.message,
      };
    }

    return {
      category: data as Category,
    };
  } catch (error: any) {
    console.error('Error in getCategoryById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update a category
 */
export const updateCategory = async (
  organizationId: string,
  categoryId: string,
  updates: {
    name?: string;
    description?: string | null;
  },
): Promise<{
  category?: Category;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('categories')
      .update(updates)
      .eq('organization_id', organizationId)
      .eq('id', categoryId)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating category:', error);
      return {
        error: error.message,
      };
    }

    return {
      category: data as Category,
    };
  } catch (error: any) {
    console.error('Error in updateCategory:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Delete a category
 */
export const deleteCategory = async (
  organizationId: string,
  categoryId: string,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', categoryId);

    if (error) {
      console.error('Error deleting category:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteCategory:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
