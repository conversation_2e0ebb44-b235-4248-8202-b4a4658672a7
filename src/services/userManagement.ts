import { supabase } from '../lib/supabase';

export interface UserProfile {
  id: string;
  first_name: string;
  last_name: string;
  avatar_url?: string;
  email?: string;
  created_at: string;
  updated_at: string;
}

export interface OrganizationMember {
  id: string;
  organization_id: string;
  user_id: string;
  role: string;
  created_at: string;
  updated_at: string;
  profile?: UserProfile;
}

export interface RolePermission {
  id: string;
  organization_id: string;
  role: string;
  permissions: Record<string, Record<string, boolean>>;
  created_at: string;
  updated_at: string;
}

export type Role =
  | 'owner'
  | 'admin'
  | 'member'
  | 'cashier'
  | 'inventory_manager'
  | 'purchaser'
  | 'employee';

/**
 * Check if a user is an owner of an organization
 */
export const isOrganizationOwner = async (
  organizationId: string,
  userId: string,
): Promise<{
  isOwner: boolean;
  error?: string;
}> => {
  try {
    console.log('Checking if user is owner:', { organizationId, userId });

    const { data, error } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organizationId)
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error checking if user is owner:', error);
      return {
        isOwner: false,
        error: error.message,
      };
    }

    console.log('User role:', data?.role);

    return {
      isOwner: data?.role === 'owner',
    };
  } catch (error: any) {
    console.error('Error in isOrganizationOwner:', error);
    return {
      isOwner: false,
      error: error.message,
    };
  }
};

/**
 * Get all members of an organization with their profiles
 */
export const getOrganizationMembers = async (
  organizationId: string,
): Promise<{
  members: OrganizationMember[];
  error?: string;
}> => {
  try {
    // Try to use the view first
    const { data: viewData, error: viewError } = await supabase
      .from('organization_members_with_profiles')
      .select('*')
      .eq('organization_id', organizationId);

    if (!viewError && viewData) {
      // Transform the data to match the expected format
      const members = await Promise.all(
        viewData.map(async (member) => {
          try {
            // Try to get email from auth if available
            const { data: userData } = await supabase.auth.getUser(member.user_id);

            // Create a profile object from the view data
            const profile = {
              id: member.user_id,
              first_name: member.first_name,
              last_name: member.last_name,
              avatar_url: member.avatar_url,
              email: userData?.user?.email,
              created_at: member.created_at,
              updated_at: member.updated_at,
            };

            return {
              id: member.id,
              organization_id: member.organization_id,
              user_id: member.user_id,
              role: member.role,
              created_at: member.created_at,
              updated_at: member.updated_at,
              profile,
            };
          } catch (err) {
            console.error('Error processing member:', err);
            // Return the member with a basic profile
            return {
              id: member.id,
              organization_id: member.organization_id,
              user_id: member.user_id,
              role: member.role,
              created_at: member.created_at,
              updated_at: member.updated_at,
              profile: {
                id: member.user_id,
                first_name: member.first_name,
                last_name: member.last_name,
                avatar_url: member.avatar_url,
                created_at: member.created_at,
                updated_at: member.updated_at,
              },
            };
          }
        }),
      );

      return {
        members: members as OrganizationMember[],
      };
    }

    // If the view doesn't exist or there's an error, fall back to the old method
    console.warn('View not available, falling back to separate queries:', viewError);

    // First, get all members of the organization
    const { data: membersData, error: membersError } = await supabase
      .from('organization_members')
      .select('*')
      .eq('organization_id', organizationId);

    if (membersError) {
      console.error('Error fetching organization members:', membersError);
      return {
        members: [],
        error: membersError.message,
      };
    }

    // Then, get profiles for each member
    const members = await Promise.all(
      membersData.map(async (member) => {
        try {
          // Get the profile for this member
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', member.user_id)
            .maybeSingle();

          if (profileError) {
            console.error('Error fetching profile for member:', profileError);
            // Return the member without a profile
            return {
              ...member,
              profile: null,
            };
          }

          // Try to get email from auth if available
          try {
            const { data: userData } = await supabase.auth.getUser(member.user_id);

            if (userData && userData.user) {
              return {
                ...member,
                profile: {
                  ...profileData,
                  email: userData.user.email,
                },
              };
            }
          } catch (authErr) {
            console.error('Error fetching user email:', authErr);
            // Continue without email
          }

          // Return the member with their profile
          return {
            ...member,
            profile: profileData,
          };
        } catch (err) {
          console.error('Error processing member:', err);
          // Return the member without a profile
          return {
            ...member,
            profile: null,
          };
        }
      }),
    );

    return {
      members: members as OrganizationMember[],
    };
  } catch (error: any) {
    console.error('Error in getOrganizationMembers:', error);
    return {
      members: [],
      error: error.message,
    };
  }
};

/**
 * Get a user's permissions in an organization
 */
export const getUserPermissions = async (
  organizationId: string,
  userId: string,
): Promise<{
  permissions: Record<string, Record<string, boolean>>;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('get_user_permissions', {
      p_organization_id: organizationId,
      p_user_id: userId,
    });

    if (error) {
      console.error('Error fetching user permissions:', error);
      return {
        permissions: {},
        error: error.message,
      };
    }

    return {
      permissions: data || {},
    };
  } catch (error: any) {
    console.error('Error in getUserPermissions:', error);
    return {
      permissions: {},
      error: error.message,
    };
  }
};

/**
 * Check if a user has a specific permission
 */
export const hasPermission = async (
  organizationId: string,
  userId: string,
  module: string,
  action: string,
): Promise<{
  hasPermission: boolean;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('has_permission', {
      p_organization_id: organizationId,
      p_user_id: userId,
      p_module: module,
      p_action: action,
    });

    if (error) {
      console.error('Error checking permission:', error);
      return {
        hasPermission: false,
        error: error.message,
      };
    }

    return {
      hasPermission: data || false,
    };
  } catch (error: any) {
    console.error('Error in hasPermission:', error);
    return {
      hasPermission: false,
      error: error.message,
    };
  }
};

/**
 * Update a user's role in an organization
 */
export const updateUserRole = async (
  organizationId: string,
  userId: string,
  newRole: Role,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('organization_members')
      .update({ role: newRole })
      .eq('organization_id', organizationId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating user role:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in updateUserRole:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Check if an admin can reset a user's password
 */
export const canResetUserPassword = async (
  organizationId: string,
  adminId: string,
  userId: string,
): Promise<{
  canReset: boolean;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('can_reset_user_password', {
      p_organization_id: organizationId,
      p_admin_id: adminId,
      p_user_id: userId,
    });

    if (error) {
      console.error('Error checking password reset permission:', error);
      return {
        canReset: false,
        error: error.message,
      };
    }

    if (!data.can_reset) {
      return {
        canReset: false,
        error: data.error,
      };
    }

    return {
      canReset: true,
    };
  } catch (error: any) {
    console.error('Error in canResetUserPassword:', error);
    return {
      canReset: false,
      error: error.message,
    };
  }
};

/**
 * Reset a user's password (admin function)
 */
export const adminResetPassword = async (
  organizationId: string,
  adminId: string,
  userId: string,
  newPassword: string,
): Promise<{
  success: boolean;
  message?: string;
  error?: string;
}> => {
  try {
    // First, check if the admin can reset the user's password
    const { canReset, error: permissionError } = await canResetUserPassword(
      organizationId,
      adminId,
      userId,
    );

    if (!canReset) {
      return {
        success: false,
        error: permissionError || "You do not have permission to reset this user's password",
      };
    }

    // Use the Supabase admin API to reset the password
    // Note: This requires the service role key, which should only be used on the server
    // For a client-side app, you would need to create a serverless function or API endpoint
    // that calls this function securely

    // For now, we'll return a message indicating that the password would be reset
    // In a real implementation, you would call an API endpoint that uses the service role key
    return {
      success: true,
      message:
        "Password reset functionality requires a server-side implementation. In a production environment, this would reset the user's password.",
    };
  } catch (error: any) {
    console.error('Error in adminResetPassword:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Invite a user to an organization
 */
export const inviteUserToOrganization = async (
  organizationId: string,
  email: string,
  role: Role,
  invitedBy: string,
): Promise<{
  success: boolean;
  invitationUrl?: string;
  error?: string;
}> => {
  try {
    // Check if there's already an invitation for this email in this organization
    try {
      const { data: existingInvitation, error: checkError } = await supabase
        .from('invitations')
        .select('id, token, expires_at, accepted_at')
        .eq('organization_id', organizationId)
        .eq('email', email)
        .is('accepted_at', null)
        .single();

      if (!checkError && existingInvitation) {
        console.log('Found existing invitation:', existingInvitation);

        // Check if the invitation has expired
        const expiresAt = new Date(existingInvitation.expires_at);
        if (expiresAt < new Date()) {
          // If expired, delete it
          console.log('Existing invitation has expired, deleting it');
          await supabase.from('invitations').delete().eq('id', existingInvitation.id);
        } else {
          // If not expired, use the existing invitation
          console.log('Using existing invitation');

          // Use the regular invitation page
          const siteUrl = import.meta.env.VITE_SITE_URL || window.location.origin;
          const invitationUrl = `${siteUrl}/auth/accept-invitation?token=${encodeURIComponent(
            existingInvitation.token,
          )}&id=${existingInvitation.id}`;

          return {
            success: true,
            invitationUrl: invitationUrl,
            error:
              'Using existing invitation. Email sending is not configured. Please share the invitation link manually.',
          };
        }
      }
    } catch (err) {
      console.error('Error checking for existing invitation:', err);
      // Continue with creating a new invitation
    }

    // Create the invitation in the database
    const { data, error } = await supabase.rpc('invite_user', {
      p_organization_id: organizationId,
      p_email: email,
      p_role: role,
      p_invited_by: invitedBy,
    });

    if (error) {
      console.error('Error creating invitation:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    if (!data.success) {
      return {
        success: false,
        error: data.error,
      };
    }

    // Generate the invitation URL directly without using the Edge Function
    try {
      // Generate the invitation URL
      const siteUrl = import.meta.env.VITE_SITE_URL || window.location.origin;

      // Log the token for debugging
      console.log('Invitation token from database:', data.token);
      console.log('Invitation ID:', data.invitation_id);

      // Use the regular invitation page
      const invitationUrl = `${siteUrl}/auth/accept-invitation?token=${encodeURIComponent(
        data.token,
      )}&id=${data.invitation_id}`;

      console.log('Generated invitation URL:', invitationUrl);

      // In a production environment, you would send an email here
      // For now, we'll just return the URL for manual sharing

      return {
        success: true,
        invitationUrl: invitationUrl,
        error: 'Email sending is not configured. Please share the invitation link manually.',
      };
    } catch (error: any) {
      console.error('Error generating invitation URL:', error);
      return {
        success: true,
        error:
          'Invitation created, but could not generate invitation link. Please try again later.',
      };
    }
  } catch (error: any) {
    console.error('Error in inviteUserToOrganization:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Get all pending invitations for an organization
 */
export const getOrganizationInvitations = async (
  organizationId: string,
): Promise<{
  invitations: any[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('invitations')
      .select('*')
      .eq('organization_id', organizationId)
      .is('accepted_at', null)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching invitations:', error);
      return {
        invitations: [],
        error: error.message,
      };
    }

    return {
      invitations: data,
    };
  } catch (error: any) {
    console.error('Error in getOrganizationInvitations:', error);
    return {
      invitations: [],
      error: error.message,
    };
  }
};

/**
 * Delete an invitation
 */
export const deleteInvitation = async (
  invitationId: string,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase.from('invitations').delete().eq('id', invitationId);

    if (error) {
      console.error('Error deleting invitation:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteInvitation:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Resend an invitation email
 */
export const resendInvitation = async (
  invitationId: string,
): Promise<{
  success: boolean;
  invitationUrl?: string;
  error?: string;
}> => {
  try {
    console.log('Resending invitation with ID:', invitationId);

    // Get the invitation details
    const { data: invitation, error: invitationError } = await supabase
      .from('invitations')
      .select('*, organizations(name), profiles(first_name, last_name)')
      .eq('id', invitationId)
      .single();

    console.log('Invitation query result:', { invitation, invitationError });

    if (invitationError || !invitation) {
      console.error('Error fetching invitation:', invitationError);
      return {
        success: false,
        error: invitationError?.message || 'Invitation not found',
      };
    }

    // Check if the invitation has already been accepted
    if (invitation.accepted_at) {
      return {
        success: false,
        error: 'This invitation has already been accepted',
      };
    }

    // Check if the invitation has expired
    const expiresAt = new Date(invitation.expires_at);
    if (expiresAt < new Date()) {
      return {
        success: false,
        error: 'This invitation has expired. Please create a new invitation.',
      };
    }

    // Generate the invitation URL directly without using the Edge Function
    try {
      // Generate the invitation URL
      const siteUrl = import.meta.env.VITE_SITE_URL || window.location.origin;

      // Log the token for debugging
      console.log('Resend invitation token from database:', invitation.token);
      console.log('Resend invitation ID:', invitation.id);

      // Use the regular invitation page
      const invitationUrl = `${siteUrl}/auth/accept-invitation?token=${encodeURIComponent(
        invitation.token,
      )}&id=${invitation.id}`;

      console.log('Generated invitation URL for resend:', invitationUrl);

      // In a production environment, you would send an email here
      // For now, we'll just return the URL for manual sharing

      return {
        success: true,
        invitationUrl: invitationUrl,
        error: 'Email sending is not configured. Please share the invitation link manually.',
      };
    } catch (emailError: any) {
      console.error('Error sending invitation email:', emailError);
      return {
        success: false,
        error: emailError.message || 'Failed to send invitation email. Please try again later.',
      };
    }
  } catch (error: any) {
    console.error('Error in resendInvitation:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Get all available roles with their permissions
 */
export const getRolePermissions = async (
  organizationId: string,
): Promise<{
  roles: RolePermission[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('role_permissions')
      .select('*')
      .eq('organization_id', organizationId);

    if (error) {
      console.error('Error fetching role permissions:', error);
      return {
        roles: [],
        error: error.message,
      };
    }

    return {
      roles: data as RolePermission[],
    };
  } catch (error: any) {
    console.error('Error in getRolePermissions:', error);
    return {
      roles: [],
      error: error.message,
    };
  }
};
