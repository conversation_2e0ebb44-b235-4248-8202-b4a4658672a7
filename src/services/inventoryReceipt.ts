import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';
import { createTransactionsFromReceiptItems } from './inventoryTransaction';

// Define types
export type InventoryReceipt = Database['public']['Tables']['inventory_receipts']['Row'];
export type InventoryReceiptInsert = Database['public']['Tables']['inventory_receipts']['Insert'];
export type InventoryReceiptUpdate = Database['public']['Tables']['inventory_receipts']['Update'];

export type InventoryReceiptItem = Database['public']['Tables']['inventory_receipt_items']['Row'];
export type InventoryReceiptItemInsert = Database['public']['Tables']['inventory_receipt_items']['Insert'];
export type InventoryReceiptItemUpdate = Database['public']['Tables']['inventory_receipt_items']['Update'];

export type InventoryReceiptWithItems = InventoryReceipt & {
  items: InventoryReceiptItemWithDetails[];
  purchase_order?: {
    id: string;
    order_number: string;
    supplier_id: string;
    supplier_name?: string;
  };
  creator_name?: string;
};

export type InventoryReceiptItemWithDetails = InventoryReceiptItem & {
  product?: {
    id: string;
    name: string;
    sku?: string;
    description?: string;
  };
  uom?: {
    id: string;
    code: string;
    name: string;
  };
  purchase_order_item?: {
    id: string;
    quantity: number;
    unit_price: number;
  };
};

export interface InventoryReceiptFilters {
  status?: string;
  purchaseOrderId?: string;
  searchQuery?: string;
  startDate?: string;
  endDate?: string;
}

/**
 * Get inventory receipts for an organization
 */
export const getInventoryReceipts = async (
  organizationId: string,
  options?: InventoryReceiptFilters
): Promise<{
  receipts: InventoryReceiptWithItems[];
  error?: string;
}> => {
  try {
    let query = supabase
      .from('inventory_receipts')
      .select(`
        *,
        items:inventory_receipt_items(
          *,
          product:product_id(id, name, sku, description),
          uom:uom_id(id, code, name)
        ),
        purchase_order:purchase_order_id(
          id,
          order_number,
          supplier_id,
          supplier:supplier_id(id, name)
        )
      `)
      .eq('organization_id', organizationId);

    // Apply filters
    if (options?.status) {
      query = query.eq('status', options.status);
    }

    if (options?.purchaseOrderId) {
      query = query.eq('purchase_order_id', options.purchaseOrderId);
    }

    if (options?.searchQuery) {
      query = query.ilike('receipt_number', `%${options.searchQuery}%`);
    }

    if (options?.startDate) {
      query = query.gte('receipt_date', options.startDate);
    }

    if (options?.endDate) {
      query = query.lte('receipt_date', options.endDate);
    }

    // Order by created_at
    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      return {
        receipts: [],
        error: error.message,
      };
    }

    if (!data || data.length === 0) {
      return {
        receipts: [],
      };
    }

    // Transform the data
    const receipts = data.map((receipt: any) => ({
      ...receipt,
      purchase_order: receipt.purchase_order ? {
        ...receipt.purchase_order,
        supplier_name: receipt.purchase_order.supplier?.name || 'Unknown Supplier'
      } : undefined,
      creator_name: 'Staff', // We'll improve this later
    }));

    return {
      receipts: receipts as InventoryReceiptWithItems[],
    };
  } catch (error: any) {
    return {
      receipts: [],
      error: error.message,
    };
  }
};

/**
 * Get an inventory receipt by ID
 */
export const getInventoryReceiptById = async (
  organizationId: string,
  receiptId: string
): Promise<{
  receipt?: InventoryReceiptWithItems;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('inventory_receipts')
      .select(`
        *,
        items:inventory_receipt_items(
          *,
          product:product_id(id, name, sku, description),
          uom:uom_id(id, code, name),
          purchase_order_item:purchase_order_item_id(id, quantity, unit_price)
        ),
        purchase_order:purchase_order_id(
          id,
          order_number,
          supplier_id,
          supplier:supplier_id(id, name)
        )
      `)
      .eq('organization_id', organizationId)
      .eq('id', receiptId)
      .single();

    if (error) {
      return {
        error: error.message,
      };
    }

    // Transform the data
    const receipt = {
      ...data,
      purchase_order: data.purchase_order ? {
        ...data.purchase_order,
        supplier_name: data.purchase_order.supplier?.name || 'Unknown Supplier'
      } : undefined,
      creator_name: 'Staff', // We'll improve this later
    };

    return {
      receipt: receipt as InventoryReceiptWithItems,
    };
  } catch (error: any) {
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new inventory receipt
 */
// Modify the createInventoryReceipt function to properly handle conversion factors
export const createInventoryReceipt = async (
  organizationId: string,
  data: {
    purchaseOrderId?: string;
    receiptDate: string;
    notes: string;
    status: string;
    items: Array<{
      purchaseOrderItemId?: string;
      productId: string;
      quantity: number;
      uomId: string;
      unitCost: number;
      expectedQuantity?: number;
      qcStatus: string;
      damagedQuantity: number;
      damageReason?: string;
      lotNumber?: string;
      expiryDate?: string;
      serialNumbers?: string[];
      conversionFactor: number;
    }>;
  }
) => {
  try {
    // Get current user session
    const { data: sessionData } = await supabase.auth.getSession();
    const userId = sessionData?.session?.user?.id;

    // Generate a receipt number
    const dateStr = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const randStr = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const receiptNumber = `RCP-${dateStr}-${randStr}`;

    // First create the inventory receipt
    const { data: receiptData, error: receiptError } = await supabase
      .from('inventory_receipts')
      .insert({
        organization_id: organizationId,
        purchase_order_id: data.purchaseOrderId,
        receipt_date: data.receiptDate,
        notes: data.notes,
        status: data.status,
        created_by: userId,
        receipt_number: receiptNumber // Add the receipt number
      })
      .select()
      .single();

    if (receiptError) {
      return { receipt: null, error: receiptError.message };
    }

    if (!receiptData) {
      return { receipt: null, error: 'Failed to create inventory receipt' };
    }

    // Modify items preparation to include base_quantity
    const items = data.items
      .filter(item => item.quantity > 0) // Filter out items with zero or negative quantity
      .map(item => {
      // Ensure conversion factor is a valid number
      const conversionFactor = parseFloat(String(item.conversionFactor));
      const validConversionFactor = !isNaN(conversionFactor) && conversionFactor > 0
        ? conversionFactor
        : 1;

      // Calculate the base quantity (what should go into inventory)
      // For example: 2 boxes with 12 units each = 24 units total
      const baseQuantity = item.quantity * validConversionFactor;

      console.log(`Creating receipt item: Product ${item.productId}, Quantity: ${item.quantity}, UoM: ${item.uomId}, Conversion Factor: ${validConversionFactor}, Base Quantity: ${baseQuantity}`);

      return {
        inventory_receipt_id: receiptData.id,
        purchase_order_item_id: item.purchaseOrderItemId,
        product_id: item.productId,
        quantity: item.quantity, // Original quantity in the UoM specified
        uom_id: item.uomId,
        unit_cost: item.unitCost,
        qc_status: item.qcStatus,
        damaged_quantity: item.damagedQuantity,
        damage_reason: item.damageReason,
        lot_number: item.lotNumber,
        expiry_date: item.expiryDate,
        conversion_factor: validConversionFactor, // Store the conversion factor for the UoM
        base_quantity: baseQuantity, // Store the calculated base quantity
        serial_numbers: item.serialNumbers
      };
    });

    // Then insert the items
    const { error: itemsError } = await supabase
      .from('inventory_receipt_items')
      .insert(items);

    if (itemsError) {
      return { receipt: receiptData, error: itemsError.message };
    }

    // If the receipt status is 'completed', create inventory transactions
    if (data.status === 'completed') {
      console.log('Creating inventory transactions for new completed receipt');

      // Create transactions for all items in this receipt
      const { error: transactionError } = await createTransactionsFromReceiptItems(
        organizationId,
        receiptData.id,
        userId || '' // Ensure userId is not undefined
      );

      if (transactionError) {
        console.error('Error creating inventory transactions:', transactionError);
        // Continue anyway, as the receipt is already created
      } else {
        console.log('Successfully created inventory transactions for new receipt');
      }
    }

    // Get the created receipt with items
    return await getInventoryReceiptById(organizationId, receiptData.id);
  } catch (error: any) {
    return {
      error: error.message,
    };
  }
};

/**
 * Update an inventory receipt
 */
export const updateInventoryReceipt = async (
  organizationId: string,
  receiptId: string,
  data: {
    receiptDate?: string;
    notes?: string;
    status?: string;
  }
): Promise<{
  receipt?: InventoryReceiptWithItems;
  error?: string;
}> => {
  try {
    // First, get the current receipt to check if status is changing from draft to completed
    const { data: currentReceipt, error: fetchError } = await supabase
      .from('inventory_receipts')
      .select('*')
      .eq('id', receiptId)
      .eq('organization_id', organizationId)
      .single();

    if (fetchError) {
      return {
        error: fetchError.message,
      };
    }

    // For TypeScript, we need to cast the currentReceipt to include status
    const receiptWithStatus = currentReceipt as any;

    // Check if we're changing from draft to completed
    const isCompletingReceipt = receiptWithStatus?.status === 'draft' && data.status === 'completed';

    if (isCompletingReceipt) {
      console.log(`Completing receipt ${receiptId}: Status changing from draft to completed`);
    }

    // Update the receipt
    const { error: updateError } = await supabase
      .from('inventory_receipts')
      .update({
        receipt_date: data.receiptDate,
        notes: data.notes,
        status: data.status,
        updated_at: new Date().toISOString(),
      })
      .eq('id', receiptId)
      .eq('organization_id', organizationId);

    if (updateError) {
      return {
        error: updateError.message,
      };
    }

    // If we're completing the receipt, create inventory transactions
    if (isCompletingReceipt) {
      console.log('Creating inventory transactions for completed receipt');

      // Get the user ID from the receipt
      const userId = receiptWithStatus.created_by;

      // Create transactions for all items in this receipt
      const { error: transactionError } = await createTransactionsFromReceiptItems(
        organizationId,
        receiptId,
        userId || '' // Ensure userId is not undefined
      );

      if (transactionError) {
        console.error('Error creating inventory transactions:', transactionError);
        // Continue anyway, as the receipt is already updated
      } else {
        console.log('Successfully created inventory transactions');
      }
    }

    // Get the updated receipt with items
    return await getInventoryReceiptById(organizationId, receiptId);
  } catch (error: any) {
    return {
      error: error.message,
    };
  }
};

/**
 * Get inventory receipts by purchase order ID
 */
export const getInventoryReceiptsByPurchaseOrderId = async (
  organizationId: string,
  purchaseOrderId: string
): Promise<{
  receipts: InventoryReceiptWithItems[];
  error?: string;
}> => {
  try {
    return await getInventoryReceipts(organizationId, { purchaseOrderId });
  } catch (error: any) {
    return {
      receipts: [],
      error: error.message,
    };
  }
};
