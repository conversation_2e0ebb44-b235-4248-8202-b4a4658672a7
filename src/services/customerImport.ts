/**
 * Customer Import Service
 * Handles bulk import of customers from CSV files
 */

import { supabase } from '../lib/supabase';
import { parseAndValidateCSV, CSVValidationRule, ParsedCSVRow } from '../utils/csvParser';
import { createCustomer } from './customer';

export interface CustomerImportRow {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  tax_id?: string;
  notes?: string;
}

export interface CustomerImportResult {
  success: boolean;
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: string[];
  warnings: string[];
  createdCustomers: any[];
  createdItems: any[]; // Alias for compatibility with GenericImport
}

export interface CustomerImportPreview {
  isValid: boolean;
  headers: string[];
  sampleData: ParsedCSVRow[];
  errors: string[];
  warnings: string[];
  totalRows: number;
}

/**
 * Customer import validation rules
 */
export const CUSTOMER_IMPORT_RULES: CSVValidationRule[] = [
  {
    field: 'name',
    required: true,
    type: 'string',
    customValidator: (value) => {
      if (typeof value === 'string' && value.length > 255) {
        return 'Customer name must be 255 characters or less';
      }
      return null;
    }
  },
  {
    field: 'email',
    type: 'email'
  },
  {
    field: 'phone',
    type: 'string',
    customValidator: (value) => {
      if (value && typeof value === 'string' && value.length > 50) {
        return 'Phone number must be 50 characters or less';
      }
      return null;
    }
  },
  {
    field: 'tax_id',
    type: 'string',
    customValidator: (value) => {
      if (value && typeof value === 'string' && value.length > 50) {
        return 'Tax ID must be 50 characters or less';
      }
      return null;
    }
  }
];

/**
 * Expected CSV headers for customer import
 */
export const CUSTOMER_IMPORT_HEADERS = [
  'name',
  'email',
  'phone',
  'address',
  'city',
  'state',
  'postal_code',
  'country',
  'tax_id',
  'notes'
];

/**
 * Preview CSV file before import
 */
export const previewCustomerImport = (csvText: string): CustomerImportPreview => {
  const result = parseAndValidateCSV(csvText, CUSTOMER_IMPORT_RULES);
  
  return {
    isValid: result.success,
    headers: result.headers,
    sampleData: result.data.slice(0, 5), // Show first 5 rows as preview
    errors: result.errors,
    warnings: result.warnings,
    totalRows: result.data.length
  };
};

/**
 * Check for duplicate customers by name
 */
const checkDuplicateCustomers = async (
  organizationId: string,
  customers: CustomerImportRow[]
): Promise<string[]> => {
  const names = customers.map(c => c.name);
  const duplicates: string[] = [];

  if (names.length > 0) {
    const { data: existingCustomers } = await supabase
      .from('customers')
      .select('name')
      .eq('organization_id', organizationId)
      .in('name', names);

    if (existingCustomers) {
      duplicates.push(...existingCustomers.map(c => c.name));
    }
  }

  return duplicates;
};

/**
 * Import customers from CSV
 */
export const importCustomers = async (
  organizationId: string,
  csvText: string,
  userId: string,
  skipDuplicates: boolean = true
): Promise<CustomerImportResult> => {
  const result = parseAndValidateCSV(csvText, CUSTOMER_IMPORT_RULES);
  
  if (!result.success) {
    return {
      success: false,
      totalRows: result.data.length,
      successCount: 0,
      errorCount: result.data.length,
      errors: result.errors,
      warnings: result.warnings,
      createdCustomers: [],
      createdItems: []
    };
  }

  const customers = result.data as CustomerImportRow[];
  const errors: string[] = [...result.errors];
  const warnings: string[] = [...result.warnings];
  const createdCustomers: any[] = [];

  // Check for duplicates
  const duplicateNames = await checkDuplicateCustomers(organizationId, customers);

  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < customers.length; i++) {
    const customer = customers[i];
    const rowNum = i + 2; // +2 because row 1 is headers and we're 0-indexed

    try {
      // Check for duplicate name
      if (duplicateNames.includes(customer.name)) {
        if (skipDuplicates) {
          warnings.push(`Row ${rowNum}: Skipped - Customer '${customer.name}' already exists`);
          continue;
        } else {
          errors.push(`Row ${rowNum}: Customer '${customer.name}' already exists`);
          errorCount++;
          continue;
        }
      }

      // Prepare customer data
      const customerData = {
        name: customer.name,
        email: customer.email || null,
        phone: customer.phone || null,
        address: customer.address || null,
        city: customer.city || null,
        state: customer.state || null,
        postal_code: customer.postal_code || null,
        country: customer.country || null,
        tax_id: customer.tax_id || null,
        notes: customer.notes || null
      };

      // Create customer
      const createResult = await createCustomer(organizationId, customerData);
      
      if (createResult.customer && !createResult.error) {
        createdCustomers.push(createResult.customer);
        successCount++;
      } else {
        const errorMsg = createResult.error || 'Failed to create customer';
        errors.push(`Row ${rowNum}: ${errorMsg}`);
        errorCount++;
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Row ${rowNum}: ${errorMsg}`);
      errorCount++;
    }
  }

  return {
    success: errorCount === 0,
    totalRows: customers.length,
    successCount,
    errorCount,
    errors,
    warnings,
    createdCustomers,
    createdItems: createdCustomers // Alias for compatibility
  };
};

/**
 * Download customer import template
 */
export const downloadCustomerImportTemplate = (): void => {
  const headers = CUSTOMER_IMPORT_HEADERS;
  const sampleData = [
    '"John Doe","<EMAIL>","555-0123","123 Main St","New York","NY","10001","USA","TAX123","VIP Customer"',
    '"Jane Smith","<EMAIL>","555-0124","456 Oak Ave","Los Angeles","CA","90210","USA","TAX456","Regular Customer"',
    '"Bob Johnson","","555-0125","789 Pine Rd","Chicago","IL","60601","USA","","Walk-in Customer"'
  ];

  const csvContent = [
    headers.map(h => `"${h}"`).join(','),
    ...sampleData
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', 'customer_import_template.csv');
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};
