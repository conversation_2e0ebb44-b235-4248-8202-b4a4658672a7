// Enhanced Receiving Service
// Comprehensive service for partial receiving, over-receiving, and quality control

import { supabase } from '../lib/supabase';
import {
  EnhancedReceiptItem,
  ReceivingTransaction,
  ReceivingDiscrepancy,
  ReceivingAction,
  ReceivingBatch,
  ReceivingProcessResult,
  ReceivingMetrics,
  PayableCalculation,
  ReceivingValidation,
  ReceivingStatusSummary
} from '../types/receiving.types';

/**
 * Get enhanced receipt items with comprehensive receiving data
 */
export const getEnhancedReceiptItems = async (
  organizationId: string,
  receiptId: string
): Promise<{ items: EnhancedReceiptItem[]; error?: string }> => {
  try {
    const { data, error } = await supabase
      .from('inventory_receipt_items')
      .select(`
        *,
        product:products(id, name, sku, description),
        uom:units_of_measure(id, name, abbreviation)
      `)
      .eq('inventory_receipt_id', receiptId)
      .order('created_at');

    if (error) {
      return { items: [], error: error.message };
    }

    return { items: data || [] };
  } catch (error: any) {
    return { items: [], error: error.message };
  }
};

/**
 * Process receiving transaction for an item
 */
export const processReceivingTransaction = async (
  organizationId: string,
  action: ReceivingAction,
  userId: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error } = await supabase.rpc('process_receiving_transaction', {
      p_organization_id: organizationId,
      p_receipt_item_id: action.item_id,
      p_transaction_type: action.type,
      p_quantity: action.quantity || 0,
      p_reason: action.reason,
      p_notes: action.notes,
      p_user_id: userId
    });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

/**
 * Process batch receiving actions
 */
export const processBatchReceiving = async (
  organizationId: string,
  batch: ReceivingBatch,
  userId: string
): Promise<ReceivingProcessResult> => {
  try {
    let processedItems = 0;
    let discrepanciesCreated = 0;
    const errors: string[] = [];

    // Process each action in the batch
    for (const action of batch.items) {
      const result = await processReceivingTransaction(organizationId, action, userId);

      if (result.success) {
        processedItems++;

        // Create discrepancy if needed
        if (batch.auto_create_discrepancies && shouldCreateDiscrepancy(action)) {
          const discrepancyResult = await createDiscrepancy(organizationId, action, userId);
          if (discrepancyResult.success) {
            discrepanciesCreated++;
          }
        }
      } else {
        errors.push(`Item ${action.item_id}: ${result.error}`);
      }
    }

    // Calculate payable amount
    const receiptId = batch.items[0]?.item_id ?
      await getReceiptIdFromItemId(batch.items[0].item_id) : null;

    let payableAmount = 0;
    if (receiptId) {
      const payableCalc = await calculatePayableAmount(organizationId, receiptId);
      payableAmount = payableCalc.payable_amount;
    }

    return {
      success: errors.length === 0,
      receipt_id: receiptId || '',
      processed_items: processedItems,
      discrepancies_created: discrepanciesCreated,
      requires_approval: false, // TODO: Implement approval logic
      payable_amount: payableAmount,
      error: errors.length > 0 ? errors.join('; ') : undefined
    };
  } catch (error: any) {
    return {
      success: false,
      receipt_id: '',
      processed_items: 0,
      discrepancies_created: 0,
      requires_approval: false,
      payable_amount: 0,
      error: error.message
    };
  }
};

/**
 * Calculate receiving metrics for a receipt
 */
export const calculateReceivingMetrics = async (
  organizationId: string,
  receiptId: string
): Promise<{ metrics: ReceivingMetrics; error?: string }> => {
  try {
    const { items, error } = await getEnhancedReceiptItems(organizationId, receiptId);

    if (error) {
      return {
        metrics: getEmptyMetrics(),
        error
      };
    }

    const metrics: ReceivingMetrics = {
      total_items: items.length,
      pending_items: items.filter(i => i.receiving_status === 'pending').length,
      partial_items: items.filter(i => i.receiving_status === 'partial').length,
      complete_items: items.filter(i => i.receiving_status === 'complete').length,
      over_received_items: items.filter(i => i.receiving_status === 'over_received').length,
      rejected_items: items.filter(i => i.receiving_status === 'rejected').length,
      mixed_items: items.filter(i => i.receiving_status === 'mixed').length,

      total_expected_value: items.reduce((sum, i) => sum + (i.expected_quantity || i.quantity) * i.unit_cost, 0),
      total_received_value: items.reduce((sum, i) => sum + i.received_quantity * i.unit_cost, 0),
      total_accepted_value: items.reduce((sum, i) => sum + i.accepted_quantity * i.unit_cost, 0),
      total_rejected_value: items.reduce((sum, i) => sum + i.rejected_quantity * i.unit_cost, 0),
      total_variance_value: items.reduce((sum, i) => sum + (i.variance_quantity || 0) * i.unit_cost, 0),

      completion_percentage: items.length > 0 ?
        (items.filter(i => ['complete', 'mixed'].includes(i.receiving_status)).length / items.length) * 100 : 0,
      acceptance_rate: items.length > 0 ?
        items.reduce((sum, i) => sum + i.accepted_quantity, 0) / items.reduce((sum, i) => sum + i.received_quantity, 0) * 100 : 0,
      rejection_rate: items.length > 0 ?
        items.reduce((sum, i) => sum + i.rejected_quantity, 0) / items.reduce((sum, i) => sum + i.received_quantity, 0) * 100 : 0,
      over_receiving_rate: items.length > 0 ?
        items.filter(i => i.over_received_quantity > 0).length / items.length * 100 : 0
    };

    return { metrics };
  } catch (error: any) {
    return {
      metrics: getEmptyMetrics(),
      error: error.message
    };
  }
};

/**
 * Calculate payable amount based on accepted quantities
 */
export const calculatePayableAmount = async (
  organizationId: string,
  receiptId: string
): Promise<PayableCalculation> => {
  try {
    const { data, error } = await supabase.rpc('calculate_receipt_payable_amount', {
      p_receipt_id: receiptId
    });

    if (error) {
      throw new Error(error.message);
    }

    const result = data[0] || {};

    return {
      total_amount: result.total_amount || 0,
      accepted_amount: result.accepted_amount || 0,
      rejected_amount: result.rejected_amount || 0,
      over_received_amount: result.over_received_amount || 0,
      payable_amount: result.payable_amount || 0,
      variance_amount: (result.total_amount || 0) - (result.payable_amount || 0),
      requires_approval: (result.over_received_amount || 0) > 0
    };
  } catch (error: any) {
    return {
      total_amount: 0,
      accepted_amount: 0,
      rejected_amount: 0,
      over_received_amount: 0,
      payable_amount: 0,
      variance_amount: 0,
      requires_approval: false
    };
  }
};

/**
 * Validate receiving actions before processing
 */
export const validateReceivingActions = async (
  organizationId: string,
  actions: ReceivingAction[]
): Promise<ReceivingValidation> => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const approvalReasons: string[] = [];

  for (const action of actions) {
    // Validate quantity
    if (action.quantity && action.quantity <= 0) {
      errors.push(`Invalid quantity for item ${action.item_id}`);
    }

    // Validate rejection requires reason
    if (action.type === 'reject' && !action.reason) {
      errors.push(`Rejection reason required for item ${action.item_id}`);
    }

    // Check for over-receiving
    if (action.type === 'receive' && action.quantity) {
      // TODO: Get expected quantity and compare
      // This would require fetching the item data
    }
  }

  return {
    is_valid: errors.length === 0,
    errors,
    warnings,
    requires_approval: approvalReasons.length > 0,
    approval_reasons: approvalReasons
  };
};

/**
 * Get receiving status summary for a receipt
 */
export const getReceivingStatusSummary = async (
  organizationId: string,
  receiptId: string
): Promise<{ summary: ReceivingStatusSummary; error?: string }> => {
  try {
    // Get receipt info
    const { data: receipt, error: receiptError } = await supabase
      .from('inventory_receipts')
      .select('receipt_number, status')
      .eq('id', receiptId)
      .eq('organization_id', organizationId)
      .single();

    if (receiptError) {
      return { summary: {} as ReceivingStatusSummary, error: receiptError.message };
    }

    // Get metrics
    const { metrics, error: metricsError } = await calculateReceivingMetrics(organizationId, receiptId);

    if (metricsError) {
      return { summary: {} as ReceivingStatusSummary, error: metricsError };
    }

    // Get discrepancies
    const { data: discrepancies } = await supabase
      .from('receiving_discrepancies')
      .select('*')
      .eq('inventory_receipt_id', receiptId)
      .eq('organization_id', organizationId);

    const summary: ReceivingStatusSummary = {
      receipt_id: receiptId,
      receipt_number: receipt.receipt_number,
      status: receipt.status,
      metrics,
      discrepancies: discrepancies || [],
      requires_attention: (discrepancies?.length || 0) > 0 || metrics.pending_items > 0,
      next_actions: generateNextActions(metrics, discrepancies || [])
    };

    return { summary };
  } catch (error: any) {
    return { summary: {} as ReceivingStatusSummary, error: error.message };
  }
};

// Helper functions
const shouldCreateDiscrepancy = (action: ReceivingAction): boolean => {
  return action.type === 'reject' || action.type === 'approve_over_receipt';
};

const createDiscrepancy = async (
  organizationId: string,
  action: ReceivingAction,
  userId: string
): Promise<{ success: boolean; error?: string }> => {
  // TODO: Implement discrepancy creation logic
  return { success: true };
};

const getReceiptIdFromItemId = async (itemId: string): Promise<string | null> => {
  const { data } = await supabase
    .from('inventory_receipt_items')
    .select('inventory_receipt_id')
    .eq('id', itemId)
    .single();

  return data?.inventory_receipt_id || null;
};

const getEmptyMetrics = (): ReceivingMetrics => ({
  total_items: 0,
  pending_items: 0,
  partial_items: 0,
  complete_items: 0,
  over_received_items: 0,
  rejected_items: 0,
  mixed_items: 0,
  total_expected_value: 0,
  total_received_value: 0,
  total_accepted_value: 0,
  total_rejected_value: 0,
  total_variance_value: 0,
  completion_percentage: 0,
  acceptance_rate: 0,
  rejection_rate: 0,
  over_receiving_rate: 0
});

const generateNextActions = (metrics: ReceivingMetrics, discrepancies: ReceivingDiscrepancy[]): string[] => {
  const actions: string[] = [];

  if (metrics.pending_items > 0) {
    actions.push(`Process ${metrics.pending_items} pending items`);
  }

  if (metrics.over_received_items > 0) {
    actions.push(`Approve ${metrics.over_received_items} over-received items`);
  }

  if (discrepancies.filter(d => d.status === 'open').length > 0) {
    actions.push(`Resolve ${discrepancies.filter(d => d.status === 'open').length} open discrepancies`);
  }

  return actions;
};

/**
 * Get receiving transactions for an item
 */
export const getReceivingTransactions = async (
  organizationId: string,
  itemId: string
): Promise<{ transactions: ReceivingTransaction[]; error?: string }> => {
  try {
    const { data, error } = await supabase
      .from('receiving_transactions')
      .select('*')
      .eq('inventory_receipt_item_id', itemId)
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });

    if (error) {
      return { transactions: [], error: error.message };
    }

    return { transactions: data || [] };
  } catch (error: any) {
    return { transactions: [], error: error.message };
  }
};

/**
 * Create receiving discrepancy
 */
export const createReceivingDiscrepancy = async (
  organizationId: string,
  discrepancy: Partial<ReceivingDiscrepancy>,
  userId: string
): Promise<{ success: boolean; discrepancy?: ReceivingDiscrepancy; error?: string }> => {
  try {
    const { data, error } = await supabase
      .from('receiving_discrepancies')
      .insert({
        organization_id: organizationId,
        inventory_receipt_id: discrepancy.inventory_receipt_id,
        inventory_receipt_item_id: discrepancy.inventory_receipt_item_id,
        discrepancy_type: discrepancy.discrepancy_type,
        severity: discrepancy.severity,
        description: discrepancy.description,
        expected_value: discrepancy.expected_value,
        actual_value: discrepancy.actual_value,
        financial_impact: discrepancy.financial_impact || 0,
        created_by: userId
      })
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, discrepancy: data };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

/**
 * Approve over-received quantity
 */
export const approveOverReceivedQuantity = async (
  organizationId: string,
  itemId: string,
  userId: string,
  notes?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error } = await supabase
      .from('inventory_receipt_items')
      .update({
        over_received_approved: true,
        over_received_approved_by: userId,
        over_received_approved_at: new Date().toISOString()
      })
      .eq('id', itemId);

    if (error) {
      return { success: false, error: error.message };
    }

    // Log the approval transaction
    await processReceivingTransaction(organizationId, {
      type: 'approve_over_receipt',
      item_id: itemId,
      notes: notes || 'Over-received quantity approved'
    }, userId);

    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};
