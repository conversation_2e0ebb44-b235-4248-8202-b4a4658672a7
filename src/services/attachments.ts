import { supabase } from '../lib/supabase';
import {
  PayableAttachment,
  CreateAttachmentRequest,
  AttachmentUploadResult,
  AttachmentsResponse,
  ALLOWED_ATTACHMENT_TYPES,
  AllowedMimeType,
  FileValidationResult
} from '../types/attachments.types';

/**
 * Validate file for attachment upload
 */
export const validateAttachmentFile = (file: File): FileValidationResult => {
  // Check file type
  if (!(file.type in ALLOWED_ATTACHMENT_TYPES)) {
    return {
      valid: false,
      error: `File type ${file.type} is not allowed. Allowed types: ${Object.keys(ALLOWED_ATTACHMENT_TYPES).join(', ')}`
    };
  }

  // Check file size
  const allowedType = ALLOWED_ATTACHMENT_TYPES[file.type as AllowedMimeType];
  if (file.size > allowedType.maxSize) {
    const maxSizeMB = allowedType.maxSize / (1024 * 1024);
    return {
      valid: false,
      error: `File size exceeds ${maxSizeMB}MB limit`
    };
  }

  return { valid: true };
};

/**
 * Upload file to storage and create attachment record
 */
export const uploadAttachment = async (
  organizationId: string,
  attachableType: 'payable' | 'payment',
  attachableId: string,
  file: File,
  userId: string,
  description?: string,
  isPrimary?: boolean
): Promise<AttachmentUploadResult> => {
  try {
    // Validate file
    const validation = validateAttachmentFile(file);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    // Generate unique file name
    const fileExt = ALLOWED_ATTACHMENT_TYPES[file.type as AllowedMimeType].ext;
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const fileName = `${attachableType}_${attachableId}_${timestamp}_${randomId}.${fileExt}`;
    const filePath = `${organizationId}/${attachableType}s/${fileName}`;

    // Upload to storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('payable-attachments')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error('Storage upload error:', uploadError);
      return { success: false, error: 'Failed to upload file to storage' };
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('payable-attachments')
      .getPublicUrl(uploadData.path);

    // Create attachment record
    const attachmentData: CreateAttachmentRequest = {
      attachable_type: attachableType,
      attachable_id: attachableId,
      file_name: file.name,
      file_url: publicUrl,
      file_size: file.size,
      file_type: file.type,
      file_extension: fileExt,
      description: description || '',
      is_primary: isPrimary || false
    };

    const result = await createAttachment(organizationId, attachmentData, userId);
    return result;

  } catch (error: any) {
    console.error('Error in uploadAttachment:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create attachment record in database
 */
export const createAttachment = async (
  organizationId: string,
  attachmentData: CreateAttachmentRequest,
  userId: string
): Promise<AttachmentUploadResult> => {
  try {
    // If this is set as primary, clear other primary flags first
    if (attachmentData.is_primary) {
      await supabase
        .from('payable_attachments')
        .update({ is_primary: false })
        .eq('organization_id', organizationId)
        .eq('attachable_type', attachmentData.attachable_type)
        .eq('attachable_id', attachmentData.attachable_id);
    }

    const { data, error } = await supabase
      .from('payable_attachments')
      .insert({
        organization_id: organizationId,
        ...attachmentData,
        uploaded_by: userId
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating attachment:', error);
      return { success: false, error: error.message };
    }

    return { success: true, attachment: data };
  } catch (error: any) {
    console.error('Error in createAttachment:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all attachments for a payable or payment
 */
export const getAttachments = async (
  organizationId: string,
  attachableType: 'payable' | 'payment',
  attachableId: string
): Promise<AttachmentsResponse> => {
  try {
    const { data, error } = await supabase
      .from('payable_attachments')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('attachable_type', attachableType)
      .eq('attachable_id', attachableId)
      .order('is_primary', { ascending: false })
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching attachments:', error);
      return { success: false, error: error.message };
    }

    return { success: true, attachments: data || [] };
  } catch (error: any) {
    console.error('Error in getAttachments:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Delete attachment
 */
export const deleteAttachment = async (
  attachmentId: string,
  userId: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Get attachment details first
    const { data: attachment, error: fetchError } = await supabase
      .from('payable_attachments')
      .select('*')
      .eq('id', attachmentId)
      .eq('uploaded_by', userId) // Only allow deletion by uploader
      .single();

    if (fetchError || !attachment) {
      return { success: false, error: 'Attachment not found or access denied' };
    }

    // Delete from storage
    const urlParts = attachment.file_url.split('/');
    const pathParts = urlParts.slice(-3); // Get last 3 parts: org/type/filename
    const filePath = pathParts.join('/');

    const { error: storageError } = await supabase.storage
      .from('payable-attachments')
      .remove([filePath]);

    if (storageError) {
      console.warn('Storage deletion error:', storageError);
      // Continue with database deletion even if storage fails
    }

    // Delete from database
    const { error: deleteError } = await supabase
      .from('payable_attachments')
      .delete()
      .eq('id', attachmentId);

    if (deleteError) {
      console.error('Error deleting attachment:', deleteError);
      return { success: false, error: deleteError.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in deleteAttachment:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Set attachment as primary
 */
export const setPrimaryAttachment = async (
  attachmentId: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { data, error } = await supabase.rpc('set_primary_attachment', {
      p_attachment_id: attachmentId
    });

    if (error) {
      console.error('Error setting primary attachment:', error);
      return { success: false, error: error.message };
    }

    if (!data) {
      return { success: false, error: 'Attachment not found' };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in setPrimaryAttachment:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update attachment description
 */
export const updateAttachmentDescription = async (
  attachmentId: string,
  description: string,
  userId: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error } = await supabase
      .from('payable_attachments')
      .update({ description })
      .eq('id', attachmentId)
      .eq('uploaded_by', userId); // Only allow updates by uploader

    if (error) {
      console.error('Error updating attachment description:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in updateAttachmentDescription:', error);
    return { success: false, error: error.message };
  }
};
