import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';

export type Organization = Database['public']['Tables']['organizations']['Row'];
export type OrganizationMember = Database['public']['Tables']['organization_members']['Row'];
export type OrganizationSettings = Database['public']['Tables']['organization_settings']['Row'];
export type Profile = Database['public']['Tables']['profiles']['Row'];

/**
 * Get all organizations for the current user
 */
export const getUserOrganizations = async (): Promise<Organization[]> => {
  const { data, error } = await supabase.from('organizations').select('*').order('name');

  if (error) {
    throw error;
  }

  return data || [];
};

/**
 * Get an organization by ID
 */
export const getOrganization = async (id: string): Promise<Organization> => {
  const { data, error } = await supabase.from('organizations').select('*').eq('id', id).single();

  if (error) {
    throw error;
  }

  return data;
};

/**
 * Get an organization by slug
 */
export const getOrganizationBySlug = async (slug: string): Promise<Organization> => {
  const { data, error } = await supabase
    .from('organizations')
    .select('*')
    .eq('slug', slug)
    .single();

  if (error) {
    throw error;
  }

  return data;
};

/**
 * Create a new organization
 */
export const createOrganization = async (name: string, userId: string): Promise<Organization> => {
  // Create a slug from the organization name
  const slug = name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');

  try {
    console.log('Creating organization with name:', name, 'and slug:', slug);

    // First try the normal way
    const { data: orgData, error: orgError } = await supabase
      .from('organizations')
      .insert({
        name,
        slug,
      })
      .select('*')
      .single();

    if (orgError) {
      console.error('Error creating organization:', orgError);
      console.error('Organization error details:', {
        code: orgError.code,
        message: orgError.message,
        details: orgError.details,
        hint: orgError.hint,
      });

      // Try using the bypass RLS function
      console.log('Trying to create organization using bypass RLS function...');
      const { data: bypassData, error: bypassError } = await supabase.rpc(
        'create_organization_bypass_rls',
        {
          org_name: name,
          org_slug: slug,
          user_id: userId,
        },
      );

      if (bypassError) {
        console.error('Bypass RLS function error:', bypassError);
        throw new Error(`Failed to create organization: ${bypassError.message}`);
      }

      console.log('Organization created successfully via bypass function:', bypassData);

      // Check if the function returned success
      if (bypassData && bypassData.success) {
        return bypassData.organization as Organization;
      } else {
        // Function returned an error
        throw new Error(bypassData.error || 'Unknown error creating organization');
      }
    }

    // If we get here, the normal way worked
    console.log('Organization created successfully:', orgData);

    // Add the user as an owner of the organization
    const { error: memberError } = await supabase.from('organization_members').insert({
      organization_id: orgData.id,
      user_id: userId,
      role: 'owner',
    });

    if (memberError) {
      console.error('Error adding user as organization owner:', memberError);
      // Continue anyway - the organization was created
    }

    // Create default organization settings
    const { error: settingsError } = await supabase.from('organization_settings').insert({
      organization_id: orgData.id,
      settings: {
        currency: 'USD',
        tax_rate: 0,
        chat_enabled: true,
        business_hours: {
          monday: { open: '09:00', close: '17:00' },
          tuesday: { open: '09:00', close: '17:00' },
          wednesday: { open: '09:00', close: '17:00' },
          thursday: { open: '09:00', close: '17:00' },
          friday: { open: '09:00', close: '17:00' },
          saturday: { open: '', close: '' },
          sunday: { open: '', close: '' },
        },
      },
    });

    if (settingsError) {
      console.error('Error creating organization settings:', settingsError);
      // Continue anyway - the organization was created
    }

    return orgData;
  } catch (error: any) {
    console.error('Error in createOrganization:', error);
    throw error;
  }
};

/**
 * Update an organization
 */
export const updateOrganization = async (
  id: string,
  updates: Partial<Organization>,
): Promise<Organization> => {
  const { data, error } = await supabase
    .from('organizations')
    .update(updates)
    .eq('id', id)
    .select('*')
    .single();

  if (error) {
    throw error;
  }

  return data;
};

/**
 * Get organization settings
 */
export const getOrganizationSettings = async (
  organizationId: string,
): Promise<OrganizationSettings> => {
  const { data, error } = await supabase
    .from('organization_settings')
    .select('*')
    .eq('organization_id', organizationId)
    .single();

  if (error) {
    throw error;
  }

  return data;
};

/**
 * Update organization settings
 */
export const updateOrganizationSettings = async (
  organizationId: string,
  settings: any,
): Promise<OrganizationSettings> => {
  const { data, error } = await supabase
    .from('organization_settings')
    .update({ settings })
    .eq('organization_id', organizationId)
    .select('*')
    .single();

  if (error) {
    throw error;
  }

  return data;
};

/**
 * Get organization members
 */
export const getOrganizationMembers = async (
  organizationId: string,
): Promise<(OrganizationMember & { profile: Profile })[]> => {
  const { data, error } = await supabase
    .from('organization_members')
    .select(
      `
      *,
      profile:profiles(*)
    `,
    )
    .eq('organization_id', organizationId);

  if (error) {
    throw error;
  }

  return data as (OrganizationMember & { profile: Profile })[];
};

/**
 * Invite a user to an organization
 */
export const inviteToOrganization = async (
  organizationId: string,
  email: string,
  role: 'admin' | 'member',
): Promise<void> => {
  // This is a simplified version. In a real app, you would:
  // 1. Check if the user exists
  // 2. If they do, add them to the organization
  // 3. If they don't, send an invitation email with a signup link

  // For now, we'll just throw an error
  throw new Error('Invitation functionality not implemented yet');
};

/**
 * Update a member's role in an organization
 */
export const updateMemberRole = async (
  memberId: string,
  role: 'admin' | 'member',
): Promise<void> => {
  const { error } = await supabase.from('organization_members').update({ role }).eq('id', memberId);

  if (error) {
    throw error;
  }
};

/**
 * Remove a member from an organization
 */
export const removeMember = async (memberId: string): Promise<void> => {
  const { error } = await supabase.from('organization_members').delete().eq('id', memberId);

  if (error) {
    throw error;
  }
};
