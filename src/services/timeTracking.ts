/**
 * Time Tracking Service
 * Handles time tracking reports and analytics
 */

import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';

export type TimeEntry = Database['public']['Tables']['time_entries']['Row'];
export type TimeTrackingSettings = Database['public']['Tables']['time_tracking_settings']['Row'];

export interface TimeEntryWithEmployee extends TimeEntry {
  employee: {
    id: string;
    first_name: string;
    last_name: string;
    employee_number?: string;
  };
}

export interface TimeTrackingReport {
  employeeId: string;
  employeeName: string;
  employeeNumber?: string;
  totalHours: number;
  regularHours: number;
  overtimeHours: number;
  daysWorked: number;
  entries: TimeEntryWithEmployee[];
}

/**
 * Get time entries for a date range
 */
export const getTimeEntries = async (
  organizationId: string,
  options?: {
    employeeId?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }
): Promise<{
  entries: TimeEntryWithEmployee[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('time_entries')
      .select(`
        *,
        employee:employees!inner (
          id,
          first_name,
          last_name,
          employee_number
        )
      `, { count: 'exact' })
      .eq('organization_id', organizationId);

    if (options?.employeeId) {
      query = query.eq('employee_id', options.employeeId);
    }

    if (options?.startDate) {
      query = query.gte('entry_date', options.startDate);
    }

    if (options?.endDate) {
      query = query.lte('entry_date', options.endDate);
    }

    if (options?.status) {
      query = query.eq('status', options.status);
    }

    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, (options.offset + (options.limit || 50)) - 1);
    }

    query = query.order('entry_date', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      return { entries: [], count: 0, error: error.message };
    }

    return {
      entries: data as TimeEntryWithEmployee[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error getting time entries:', error);
    return { entries: [], count: 0, error: error.message };
  }
};

/**
 * Get time tracking report for employees
 */
export const getTimeTrackingReport = async (
  organizationId: string,
  startDate: string,
  endDate: string,
  employeeIds?: string[]
): Promise<{
  reports: TimeTrackingReport[];
  error?: string;
}> => {
  try {
    let query = supabase
      .from('time_entries')
      .select(`
        *,
        employee:employees!inner (
          id,
          first_name,
          last_name,
          employee_number
        )
      `)
      .eq('organization_id', organizationId)
      .gte('entry_date', startDate)
      .lte('entry_date', endDate);

    if (employeeIds && employeeIds.length > 0) {
      query = query.in('employee_id', employeeIds);
    }

    const { data, error } = await query;

    if (error) {
      return { reports: [], error: error.message };
    }

    // Group entries by employee
    const employeeGroups = (data as TimeEntryWithEmployee[]).reduce((groups, entry) => {
      const employeeId = entry.employee_id;
      if (!groups[employeeId]) {
        groups[employeeId] = [];
      }
      groups[employeeId].push(entry);
      return groups;
    }, {} as Record<string, TimeEntryWithEmployee[]>);

    // Calculate reports for each employee
    const reports: TimeTrackingReport[] = Object.entries(employeeGroups).map(([employeeId, entries]) => {
      const employee = entries[0].employee;
      const totalHours = entries.reduce((sum, entry) => sum + (entry.total_hours || 0), 0);
      const overtimeHours = entries.reduce((sum, entry) => sum + (entry.overtime_hours || 0), 0);
      const regularHours = totalHours - overtimeHours;
      const daysWorked = new Set(entries.map(entry => entry.entry_date)).size;

      return {
        employeeId,
        employeeName: `${employee.first_name} ${employee.last_name}`,
        employeeNumber: employee.employee_number,
        totalHours,
        regularHours,
        overtimeHours,
        daysWorked,
        entries,
      };
    });

    return { reports };
  } catch (error: any) {
    console.error('Error getting time tracking report:', error);
    return { reports: [], error: error.message };
  }
};

/**
 * Get time tracking analytics
 */
export const getTimeTrackingAnalytics = async (
  organizationId: string,
  startDate: string,
  endDate: string
): Promise<{
  analytics: {
    totalEmployees: number;
    totalHours: number;
    averageHoursPerEmployee: number;
    totalOvertimeHours: number;
    attendanceRate: number;
    methodBreakdown: {
      facial_recognition: number;
      pin: number;
      manual: number;
    };
  };
  error?: string;
}> => {
  try {
    // Get time entries
    const { data: timeEntries, error: entriesError } = await supabase
      .from('time_entries')
      .select('*')
      .eq('organization_id', organizationId)
      .gte('entry_date', startDate)
      .lte('entry_date', endDate);

    if (entriesError) {
      return {
        analytics: {
          totalEmployees: 0,
          totalHours: 0,
          averageHoursPerEmployee: 0,
          totalOvertimeHours: 0,
          attendanceRate: 0,
          methodBreakdown: { facial_recognition: 0, pin: 0, manual: 0 },
        },
        error: entriesError.message,
      };
    }

    // Get total employees in organization
    const { count: totalEmployees, error: employeesError } = await supabase
      .from('employees')
      .select('*', { count: 'exact', head: true })
      .eq('organization_id', organizationId)
      .eq('is_active', true);

    if (employeesError) {
      return {
        analytics: {
          totalEmployees: 0,
          totalHours: 0,
          averageHoursPerEmployee: 0,
          totalOvertimeHours: 0,
          attendanceRate: 0,
          methodBreakdown: { facial_recognition: 0, pin: 0, manual: 0 },
        },
        error: employeesError.message,
      };
    }

    // Calculate analytics
    const uniqueEmployees = new Set(timeEntries?.map(entry => entry.employee_id)).size;
    const totalHours = timeEntries?.reduce((sum, entry) => sum + (entry.total_hours || 0), 0) || 0;
    const totalOvertimeHours = timeEntries?.reduce((sum, entry) => sum + (entry.overtime_hours || 0), 0) || 0;
    const averageHoursPerEmployee = uniqueEmployees > 0 ? totalHours / uniqueEmployees : 0;
    const attendanceRate = totalEmployees ? (uniqueEmployees / totalEmployees) * 100 : 0;

    // Method breakdown
    const methodBreakdown = timeEntries?.reduce((breakdown, entry) => {
      const method = entry.clock_in_method || 'manual';
      if (method === 'facial_recognition') {
        breakdown.facial_recognition++;
      } else if (method === 'pin') {
        breakdown.pin++;
      } else {
        breakdown.manual++;
      }
      return breakdown;
    }, { facial_recognition: 0, pin: 0, manual: 0 }) || { facial_recognition: 0, pin: 0, manual: 0 };

    return {
      analytics: {
        totalEmployees: totalEmployees || 0,
        totalHours: Math.round(totalHours * 100) / 100,
        averageHoursPerEmployee: Math.round(averageHoursPerEmployee * 100) / 100,
        totalOvertimeHours: Math.round(totalOvertimeHours * 100) / 100,
        attendanceRate: Math.round(attendanceRate * 100) / 100,
        methodBreakdown,
      },
    };
  } catch (error: any) {
    console.error('Error getting time tracking analytics:', error);
    return {
      analytics: {
        totalEmployees: 0,
        totalHours: 0,
        averageHoursPerEmployee: 0,
        totalOvertimeHours: 0,
        attendanceRate: 0,
        methodBreakdown: { facial_recognition: 0, pin: 0, manual: 0 },
      },
      error: error.message,
    };
  }
};

/**
 * Update time tracking settings
 */
export const updateTimeTrackingSettings = async (
  organizationId: string,
  settings: Partial<TimeTrackingSettings>
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('time_tracking_settings')
      .upsert({
        organization_id: organizationId,
        ...settings,
      });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error updating time tracking settings:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get recognition logs for audit
 */
export const getRecognitionLogs = async (
  organizationId: string,
  options?: {
    employeeId?: string;
    startDate?: string;
    endDate?: string;
    success?: boolean;
    limit?: number;
    offset?: number;
  }
): Promise<{
  logs: any[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('recognition_logs')
      .select(`
        *,
        employee:employees (
          first_name,
          last_name,
          employee_number
        )
      `, { count: 'exact' })
      .eq('organization_id', organizationId);

    if (options?.employeeId) {
      query = query.eq('employee_id', options.employeeId);
    }

    if (options?.startDate) {
      query = query.gte('created_at', options.startDate);
    }

    if (options?.endDate) {
      query = query.lte('created_at', options.endDate);
    }

    if (options?.success !== undefined) {
      query = query.eq('success', options.success);
    }

    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, (options.offset + (options.limit || 50)) - 1);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      return { logs: [], count: 0, error: error.message };
    }

    return {
      logs: data || [],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error getting recognition logs:', error);
    return { logs: [], count: 0, error: error.message };
  }
};
