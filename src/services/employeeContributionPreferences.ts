import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';

export type EmployeeContributionPreference = Database['public']['Tables']['employee_contribution_preferences']['Row'];
export type EmployeeContributionPreferenceInsert = Database['public']['Tables']['employee_contribution_preferences']['Insert'];
export type EmployeeContributionPreferenceUpdate = Database['public']['Tables']['employee_contribution_preferences']['Update'];

/**
 * Get active contribution preferences for an employee
 */
export const getEmployeeContributionPreferences = async (
  organizationId: string,
  employeeId: string
): Promise<{
  preferences?: EmployeeContributionPreference;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('employee_contribution_preferences')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('employee_id', employeeId)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching employee contribution preferences:', error);
      return { error: error.message };
    }

    return { preferences: data || undefined };
  } catch (error: any) {
    console.error('Error in getEmployeeContributionPreferences:', error);
    return { error: error.message };
  }
};

/**
 * Save or update employee contribution preferences
 */
export const saveEmployeeContributionPreferences = async (
  organizationId: string,
  employeeId: string,
  preferences: {
    sss_contribution_override?: number | null;
    philhealth_contribution_override?: number | null;
    pagibig_contribution_override?: number | null;
    withholding_tax_override?: number | null;
    notes?: string | null;
  },
  userId: string
): Promise<{
  preferences?: EmployeeContributionPreference;
  error?: string;
}> => {
  try {
    // First, deactivate any existing active preferences
    const { error: deactivateError } = await supabase
      .from('employee_contribution_preferences')
      .update({ is_active: false })
      .eq('organization_id', organizationId)
      .eq('employee_id', employeeId)
      .eq('is_active', true);

    if (deactivateError) {
      console.error('Error deactivating existing preferences:', deactivateError);
      return { error: deactivateError.message };
    }

    // Create new active preference
    const { data, error } = await supabase
      .from('employee_contribution_preferences')
      .insert({
        organization_id: organizationId,
        employee_id: employeeId,
        sss_contribution_override: preferences.sss_contribution_override,
        philhealth_contribution_override: preferences.philhealth_contribution_override,
        pagibig_contribution_override: preferences.pagibig_contribution_override,
        withholding_tax_override: preferences.withholding_tax_override,
        notes: preferences.notes,
        is_active: true,
        created_by: userId,
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error saving employee contribution preferences:', error);
      return { error: error.message };
    }

    return { preferences: data };
  } catch (error: any) {
    console.error('Error in saveEmployeeContributionPreferences:', error);
    return { error: error.message };
  }
};

/**
 * Delete employee contribution preferences (deactivate)
 */
export const deleteEmployeeContributionPreferences = async (
  organizationId: string,
  employeeId: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('employee_contribution_preferences')
      .update({ is_active: false })
      .eq('organization_id', organizationId)
      .eq('employee_id', employeeId)
      .eq('is_active', true);

    if (error) {
      console.error('Error deleting employee contribution preferences:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in deleteEmployeeContributionPreferences:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all employees with contribution preferences for an organization
 */
export const getEmployeesWithContributionPreferences = async (
  organizationId: string
): Promise<{
  employees?: Array<EmployeeContributionPreference & {
    employee: {
      id: string;
      first_name: string;
      middle_name?: string | null;
      last_name: string;
      employee_number?: string | null;
    };
  }>;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('employee_contribution_preferences')
      .select(`
        *,
        employee:employee_id (
          id,
          first_name,
          middle_name,
          last_name,
          employee_number
        )
      `)
      .eq('organization_id', organizationId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching employees with contribution preferences:', error);
      return { error: error.message };
    }

    return { employees: data as any };
  } catch (error: any) {
    console.error('Error in getEmployeesWithContributionPreferences:', error);
    return { error: error.message };
  }
};

/**
 * Apply employee contribution preferences to payroll calculation
 * This function checks if an employee has saved preferences and returns the override values
 */
export const applyEmployeeContributionPreferences = async (
  organizationId: string,
  employeeId: string,
  calculatedContributions: {
    sss_contribution: number;
    philhealth_contribution: number;
    pagibig_contribution: number;
    withholding_tax: number;
  }
): Promise<{
  contributions: {
    sss_contribution: number;
    philhealth_contribution: number;
    pagibig_contribution: number;
    withholding_tax: number;
    contributions_manually_edited: boolean;
    sss_contribution_override?: number | null;
    philhealth_contribution_override?: number | null;
    pagibig_contribution_override?: number | null;
    withholding_tax_override?: number | null;
  };
  error?: string;
}> => {
  try {
    const { preferences, error } = await getEmployeeContributionPreferences(organizationId, employeeId);

    if (error) {
      return { 
        contributions: {
          ...calculatedContributions,
          contributions_manually_edited: false
        },
        error 
      };
    }

    // If no preferences found, return calculated values
    if (!preferences) {
      return {
        contributions: {
          ...calculatedContributions,
          contributions_manually_edited: false
        }
      };
    }

    // Apply preferences (use override if available, otherwise use calculated)
    const finalContributions = {
      sss_contribution: preferences.sss_contribution_override ?? calculatedContributions.sss_contribution,
      philhealth_contribution: preferences.philhealth_contribution_override ?? calculatedContributions.philhealth_contribution,
      pagibig_contribution: preferences.pagibig_contribution_override ?? calculatedContributions.pagibig_contribution,
      withholding_tax: preferences.withholding_tax_override ?? calculatedContributions.withholding_tax,
      contributions_manually_edited: true,
      sss_contribution_override: preferences.sss_contribution_override,
      philhealth_contribution_override: preferences.philhealth_contribution_override,
      pagibig_contribution_override: preferences.pagibig_contribution_override,
      withholding_tax_override: preferences.withholding_tax_override,
    };

    return { contributions: finalContributions };
  } catch (error: any) {
    console.error('Error in applyEmployeeContributionPreferences:', error);
    return { 
      contributions: {
        ...calculatedContributions,
        contributions_manually_edited: false
      },
      error: error.message 
    };
  }
};
