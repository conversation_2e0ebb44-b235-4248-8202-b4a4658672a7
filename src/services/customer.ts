import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';

export type Customer = Database['public']['Tables']['customers']['Row'];

/**
 * Get all customers for an organization
 */
export const getCustomers = async (
  organizationId: string,
  options?: {
    searchQuery?: string;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  },
): Promise<{
  customers: Customer[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('customers')
      .select('*', { count: 'exact' })
      .eq('organization_id', organizationId);

    // Apply search filter if provided
    if (options?.searchQuery) {
      query = query.or(
        `name.ilike.%${options.searchQuery}%,email.ilike.%${options.searchQuery}%,phone.ilike.%${options.searchQuery}%`
      );
    }

    // Apply sorting
    if (options?.sortBy) {
      query = query.order(options.sortBy, { ascending: options.sortOrder === 'asc' });
    } else {
      query = query.order('name', { ascending: true });
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching customers:', error);
      return {
        customers: [],
        count: 0,
        error: error.message,
      };
    }

    return {
      customers: data as Customer[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error in getCustomers:', error);
    return {
      customers: [],
      count: 0,
      error: error.message,
    };
  }
};

/**
 * Get a single customer by ID
 */
export const getCustomerById = async (
  organizationId: string,
  customerId: string,
): Promise<{
  customer?: Customer;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('id', customerId)
      .single();

    if (error) {
      console.error('Error fetching customer:', error);
      return {
        error: error.message,
      };
    }

    return {
      customer: data as Customer,
    };
  } catch (error: any) {
    console.error('Error in getCustomerById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new customer
 */
export const createCustomer = async (
  organizationId: string,
  customer: Omit<Customer, 'id' | 'organization_id' | 'created_at' | 'updated_at'>,
): Promise<{
  customer?: Customer;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('customers')
      .insert({
        ...customer,
        organization_id: organizationId,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating customer:', error);
      return {
        error: error.message,
      };
    }

    return {
      customer: data as Customer,
    };
  } catch (error: any) {
    console.error('Error in createCustomer:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update an existing customer
 */
export const updateCustomer = async (
  organizationId: string,
  customerId: string,
  updates: Partial<Omit<Customer, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>,
): Promise<{
  customer?: Customer;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('customers')
      .update(updates)
      .eq('organization_id', organizationId)
      .eq('id', customerId)
      .select()
      .single();

    if (error) {
      console.error('Error updating customer:', error);
      return {
        error: error.message,
      };
    }

    return {
      customer: data as Customer,
    };
  } catch (error: any) {
    console.error('Error in updateCustomer:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Delete a customer
 */
export const deleteCustomer = async (
  organizationId: string,
  customerId: string,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', customerId);

    if (error) {
      console.error('Error deleting customer:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteCustomer:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
