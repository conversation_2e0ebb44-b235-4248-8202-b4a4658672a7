import { supabase } from '../lib/supabase';
import {
  PayrollPeriod,
  PayrollItem,
  PayrollItemWithDetails,
  PayrollDeduction,
  PayrollAllowance,
  PayslipTemplate
} from '../types/payroll';
import { getPayrollPeriodById } from './payroll';
import { format } from 'date-fns';
import { formatCurrency } from '../utils/formatters';

/**
 * Get payroll item details for payslip
 * @param organizationId Organization ID
 * @param payrollItemId Payroll item ID
 * @returns Payroll item with details and error if any
 */
export const getPayrollItemForPayslip = async (
  organizationId: string,
  payrollItemId: string
): Promise<{
  item?: PayrollItemWithDetails;
  error?: string;
}> => {
  try {
    // Get payroll item
    const { data: itemData, error: itemError } = await supabase
      .from('payroll_items')
      .select(`
        *,
        employee:employee_id (*),
        payroll_period:payroll_period_id (*)
      `)
      .eq('organization_id', organizationId)
      .eq('id', payrollItemId)
      .single();

    if (itemError) {
      throw new Error(itemError.message);
    }

    const item = itemData as PayrollItemWithDetails;

    // Get allowances
    const { data: allowancesData, error: allowancesError } = await supabase
      .from('payroll_allowances')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('payroll_item_id', payrollItemId);

    if (allowancesError) {
      throw new Error(allowancesError.message);
    }

    // Get deductions
    const { data: deductionsData, error: deductionsError } = await supabase
      .from('payroll_deductions')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('payroll_item_id', payrollItemId);

    if (deductionsError) {
      throw new Error(deductionsError.message);
    }

    item.allowances = allowancesData as PayrollAllowance[];
    item.deductions = deductionsData as PayrollDeduction[];

    return { item };
  } catch (err: any) {
    console.error('Error getting payroll item for payslip:', err);
    return { error: err.message };
  }
};

/**
 * Generate HTML payslip for a payroll item
 * @param organizationId Organization ID
 * @param payrollItemId Payroll item ID
 * @param template Payslip template
 * @returns HTML payslip content and error if any
 */
export const generatePayslipHTML = async (
  organizationId: string,
  payrollItemId: string,
  template: PayslipTemplate = PayslipTemplate.DEFAULT
): Promise<{
  html?: string;
  error?: string;
}> => {
  try {
    // Get payroll item with details
    const { item, error } = await getPayrollItemForPayslip(organizationId, payrollItemId);

    if (error || !item) {
      throw new Error(error || 'Payroll item not found');
    }

    // Get organization details
    const { data: orgData, error: orgError } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', organizationId)
      .single();

    if (orgError) {
      throw new Error(orgError.message);
    }

    const organization = orgData;

    // Format dates
    const periodStartDate = format(new Date(item.payroll_period.start_date), 'MMMM d, yyyy');
    const periodEndDate = format(new Date(item.payroll_period.end_date), 'MMMM d, yyyy');
    const paymentDate = format(new Date(item.payroll_period.payment_date), 'MMMM d, yyyy');

    // Generate HTML based on template
    let html = '';

    switch (template) {
      case PayslipTemplate.DEFAULT:
        html = generateDefaultPayslipTemplate(organization, item, periodStartDate, periodEndDate, paymentDate);
        break;
      case PayslipTemplate.COMPACT:
        html = generateCompactPayslipTemplate(organization, item, periodStartDate, periodEndDate, paymentDate);
        break;
      case PayslipTemplate.DETAILED:
        html = generateDetailedPayslipTemplate(organization, item, periodStartDate, periodEndDate, paymentDate);
        break;
      default:
        html = generateDefaultPayslipTemplate(organization, item, periodStartDate, periodEndDate, paymentDate);
    }

    return { html };
  } catch (err: any) {
    console.error('Error generating payslip HTML:', err);
    return { error: err.message };
  }
};

/**
 * Generate default payslip template
 */
const generateDefaultPayslipTemplate = (
  organization: any,
  item: PayrollItemWithDetails,
  periodStartDate: string,
  periodEndDate: string,
  paymentDate: string
): string => {
  // Group deductions by type
  const governmentDeductions = item.deductions?.filter(d => d.type === 'government') || [];
  const taxDeductions = item.deductions?.filter(d => d.type === 'tax') || [];
  const otherDeductions = item.deductions?.filter(d => d.type === 'other') || [];

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Payslip - ${item.employee.first_name} ${item.employee.last_name}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
        }
        .payslip {
          max-width: 800px;
          margin: 0 auto;
          border: 1px solid #ddd;
          padding: 20px;
        }
        .header {
          text-align: center;
          margin-bottom: 20px;
          padding-bottom: 10px;
          border-bottom: 2px solid #333;
        }
        .company-name {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .payslip-title {
          font-size: 18px;
          margin-bottom: 10px;
        }
        .period-info {
          margin-bottom: 5px;
        }
        .employee-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
        }
        .employee-details, .payroll-details {
          width: 48%;
        }
        .section-title {
          font-weight: bold;
          margin-bottom: 10px;
          border-bottom: 1px solid #ddd;
          padding-bottom: 5px;
        }
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }
        .detail-label {
          font-weight: normal;
        }
        .detail-value {
          text-align: right;
        }
        .earnings-section, .deductions-section {
          margin-bottom: 20px;
        }
        .summary-section {
          margin-top: 20px;
          border-top: 1px solid #ddd;
          padding-top: 10px;
        }
        .total-row {
          font-weight: bold;
          margin-top: 10px;
        }
        .net-pay {
          font-size: 18px;
          font-weight: bold;
          color: #2c7be5;
        }
        .footer {
          margin-top: 30px;
          text-align: center;
          font-size: 12px;
          color: #777;
        }
      </style>
    </head>
    <body>
      <div class="payslip">
        <div class="header">
          <div class="company-name">${organization.name}</div>
          <div class="payslip-title">PAYSLIP</div>
          <div class="period-info">Pay Period: ${periodStartDate} to ${periodEndDate}</div>
          <div class="period-info">Payment Date: ${paymentDate}</div>
        </div>

        <div class="employee-info">
          <div class="employee-details">
            <div class="section-title">Employee Information</div>
            <div class="detail-row">
              <span class="detail-label">Name:</span>
              <span class="detail-value">${item.employee.first_name} ${item.employee.middle_name || ''} ${item.employee.last_name}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Employee ID:</span>
              <span class="detail-value">${item.employee.employee_number || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Position:</span>
              <span class="detail-value">${item.employee.position?.title || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Department:</span>
              <span class="detail-value">${item.employee.department?.name || 'N/A'}</span>
            </div>
          </div>

          <div class="payroll-details">
            <div class="section-title">Payroll Information</div>
            <div class="detail-row">
              <span class="detail-label">Payroll Period:</span>
              <span class="detail-value">${item.payroll_period.name}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Payslip #:</span>
              <span class="detail-value">${item.id.substring(0, 8)}</span>
            </div>
          </div>
        </div>

        <div class="earnings-section">
          <div class="section-title">Earnings</div>
          <div class="detail-row">
            <span class="detail-label">Basic Pay</span>
            <span class="detail-value">${formatCurrency(Number(item.basic_pay))}</span>
          </div>

          ${item.allowances?.map(allowance => `
            <div class="detail-row">
              <span class="detail-label">${allowance.name}</span>
              <span class="detail-value">${formatCurrency(Number(allowance.amount))}</span>
            </div>
          `).join('') || ''}

          <div class="detail-row total-row">
            <span class="detail-label">Total Earnings</span>
            <span class="detail-value">${formatCurrency(Number(item.gross_pay))}</span>
          </div>
        </div>

        <div class="deductions-section">
          <div class="section-title">Deductions</div>

          ${governmentDeductions.length > 0 ? `
            <div class="detail-row">
              <span class="detail-label"><strong>Government Contributions</strong></span>
              <span class="detail-value"></span>
            </div>
            ${governmentDeductions.map(deduction => `
              <div class="detail-row">
                <span class="detail-label">${deduction.name}</span>
                <span class="detail-value">${formatCurrency(Number(deduction.amount))}</span>
              </div>
            `).join('')}
          ` : ''}

          ${taxDeductions.length > 0 ? `
            <div class="detail-row">
              <span class="detail-label"><strong>Taxes</strong></span>
              <span class="detail-value"></span>
            </div>
            ${taxDeductions.map(deduction => `
              <div class="detail-row">
                <span class="detail-label">${deduction.name}</span>
                <span class="detail-value">${formatCurrency(Number(deduction.amount))}</span>
              </div>
            `).join('')}
          ` : ''}

          ${otherDeductions.length > 0 ? `
            <div class="detail-row">
              <span class="detail-label"><strong>Other Deductions</strong></span>
              <span class="detail-value"></span>
            </div>
            ${otherDeductions.map(deduction => `
              <div class="detail-row">
                <span class="detail-label">${deduction.name}</span>
                <span class="detail-value">${formatCurrency(Number(deduction.amount))}</span>
              </div>
            `).join('')}
          ` : ''}

          <div class="detail-row total-row">
            <span class="detail-label">Total Deductions</span>
            <span class="detail-value">${formatCurrency(Number(item.total_deductions))}</span>
          </div>
        </div>

        <div class="summary-section">
          <div class="detail-row">
            <span class="detail-label">Gross Pay</span>
            <span class="detail-value">${formatCurrency(Number(item.gross_pay))}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Total Deductions</span>
            <span class="detail-value">${formatCurrency(Number(item.total_deductions))}</span>
          </div>
          <div class="detail-row total-row">
            <span class="detail-label">Net Pay</span>
            <span class="detail-value net-pay">${formatCurrency(Number(item.net_pay))}</span>
          </div>
        </div>

        <div class="footer">
          <p>This is a computer-generated document. No signature is required.</p>
          <p>Generated on ${format(new Date(), 'MMMM d, yyyy h:mm a')}</p>
        </div>
      </div>
    </body>
    </html>
  `;
};

/**
 * Generate compact payslip template
 */
const generateCompactPayslipTemplate = (
  organization: any,
  item: PayrollItemWithDetails,
  periodStartDate: string,
  periodEndDate: string,
  paymentDate: string
): string => {
  // Simplified compact template
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Payslip - ${item.employee.first_name} ${item.employee.last_name}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 10px;
          color: #333;
          font-size: 12px;
        }
        .payslip {
          max-width: 400px;
          margin: 0 auto;
          border: 1px solid #ddd;
          padding: 15px;
        }
        .header {
          text-align: center;
          margin-bottom: 15px;
          padding-bottom: 5px;
          border-bottom: 1px solid #ddd;
        }
        .company-name {
          font-size: 16px;
          font-weight: bold;
        }
        .payslip-title {
          font-size: 14px;
          margin: 5px 0;
        }
        .section {
          margin-bottom: 10px;
        }
        .row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 3px;
        }
        .total-row {
          font-weight: bold;
          border-top: 1px solid #ddd;
          padding-top: 3px;
          margin-top: 3px;
        }
        .footer {
          margin-top: 15px;
          text-align: center;
          font-size: 10px;
          color: #777;
        }
      </style>
    </head>
    <body>
      <div class="payslip">
        <div class="header">
          <div class="company-name">${organization.name}</div>
          <div class="payslip-title">PAYSLIP</div>
          <div>${periodStartDate} to ${periodEndDate}</div>
        </div>

        <div class="section">
          <div class="row">
            <span>Employee:</span>
            <span>${item.employee.first_name} ${item.employee.last_name}</span>
          </div>
          <div class="row">
            <span>ID:</span>
            <span>${item.employee.employee_number || 'N/A'}</span>
          </div>
          <div class="row">
            <span>Position:</span>
            <span>${item.employee.position?.title || 'N/A'}</span>
          </div>
        </div>

        <div class="section">
          <div class="row">
            <span>Basic Pay:</span>
            <span>${formatCurrency(Number(item.basic_pay))}</span>
          </div>
          <div class="row">
            <span>Allowances:</span>
            <span>${formatCurrency(Number(item.total_allowances))}</span>
          </div>
          <div class="row total-row">
            <span>Gross Pay:</span>
            <span>${formatCurrency(Number(item.gross_pay))}</span>
          </div>
        </div>

        <div class="section">
          <div class="row">
            <span>Deductions:</span>
            <span>${formatCurrency(Number(item.total_deductions))}</span>
          </div>
          <div class="row total-row">
            <span>Net Pay:</span>
            <span>${formatCurrency(Number(item.net_pay))}</span>
          </div>
        </div>

        <div class="footer">
          <p>Payment Date: ${paymentDate}</p>
          <p>This is a computer-generated document.</p>
        </div>
      </div>
    </body>
    </html>
  `;
};

/**
 * Generate detailed payslip template
 */
const generateDetailedPayslipTemplate = (
  organization: any,
  item: PayrollItemWithDetails,
  periodStartDate: string,
  periodEndDate: string,
  paymentDate: string
): string => {
  // Group deductions by type
  const governmentDeductions = item.deductions?.filter(d => d.type === 'government') || [];
  const taxDeductions = item.deductions?.filter(d => d.type === 'tax') || [];
  const otherDeductions = item.deductions?.filter(d => d.type === 'other') || [];

  // Find specific government contributions
  const sssDeduction = governmentDeductions.find(d => d.name === 'SSS Contribution');
  const philhealthDeduction = governmentDeductions.find(d => d.name === 'PhilHealth Contribution');
  const pagibigDeduction = governmentDeductions.find(d => d.name === 'Pag-IBIG Contribution');

  // Calculate monthly salary (for explanation purposes)
  const monthlySalary = Number(item.basic_pay) * (item.payroll_period.name.includes('Semi-Monthly') ? 2 : 1);

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Detailed Payslip - ${item.employee.first_name} ${item.employee.last_name}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
          font-size: 12px;
        }
        .payslip {
          max-width: 800px;
          margin: 0 auto;
          border: 1px solid #ddd;
          padding: 20px;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          padding-bottom: 10px;
          border-bottom: 2px solid #2563eb;
        }
        .company-info {
          flex: 1;
        }
        .company-name {
          font-size: 20px;
          font-weight: bold;
          color: #2563eb;
        }
        .company-address {
          margin-top: 5px;
          font-size: 12px;
        }
        .payslip-title {
          text-align: right;
          font-size: 24px;
          font-weight: bold;
          color: #2563eb;
        }
        .employee-info {
          display: flex;
          margin-bottom: 20px;
        }
        .employee-details {
          flex: 1;
        }
        .payroll-details {
          flex: 1;
          text-align: right;
        }
        .section-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 10px;
          padding-bottom: 5px;
          border-bottom: 1px solid #ddd;
          color: #2563eb;
        }
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }
        .detail-label {
          flex: 1;
        }
        .detail-value {
          flex: 1;
          text-align: right;
        }
        .total-row {
          font-weight: bold;
          margin-top: 10px;
          padding-top: 5px;
          border-top: 1px solid #ddd;
        }
        .net-pay {
          color: #2563eb;
          font-size: 16px;
        }
        .earnings-section, .deductions-section, .summary-section {
          margin-bottom: 20px;
          padding: 15px;
          border: 1px solid #ddd;
          border-radius: 5px;
        }
        .footer {
          margin-top: 30px;
          text-align: center;
          font-size: 10px;
          color: #666;
        }
        .calculation-section {
          margin-top: 20px;
          padding: 15px;
          border: 1px solid #ddd;
          border-radius: 5px;
          background-color: #f9fafb;
        }
        .calculation-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 10px;
          color: #2563eb;
        }
        .formula {
          font-family: monospace;
          background-color: #f1f5f9;
          padding: 8px;
          border-radius: 4px;
          margin-bottom: 10px;
          font-size: 11px;
        }
        .formula-explanation {
          font-size: 11px;
          margin-bottom: 15px;
          color: #4b5563;
        }
      </style>
    </head>
    <body>
      <div class="payslip">
        <div class="header">
          <div class="company-info">
            <div class="company-name">${organization.name}</div>
            <div class="company-address">${organization.address || 'No address provided'}</div>
          </div>
          <div class="payslip-title">PAYSLIP</div>
        </div>

        <div class="employee-info">
          <div class="employee-details">
            <div class="section-title">Employee Information</div>
            <div class="detail-row">
              <span class="detail-label">Name:</span>
              <span class="detail-value">${item.employee.first_name} ${item.employee.middle_name ? item.employee.middle_name + ' ' : ''}${item.employee.last_name}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Employee ID:</span>
              <span class="detail-value">${item.employee.employee_number || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Position:</span>
              <span class="detail-value">${item.employee.position?.title || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Department:</span>
              <span class="detail-value">${item.employee.department?.name || 'N/A'}</span>
            </div>
          </div>

          <div class="payroll-details">
            <div class="section-title">Payroll Information</div>
            <div class="detail-row">
              <span class="detail-label">Payroll Period:</span>
              <span class="detail-value">${item.payroll_period.name}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Period Covered:</span>
              <span class="detail-value">${periodStartDate} to ${periodEndDate}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Payment Date:</span>
              <span class="detail-value">${paymentDate}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Payslip #:</span>
              <span class="detail-value">${item.id.substring(0, 8)}</span>
            </div>
          </div>
        </div>

        <div class="earnings-section">
          <div class="section-title">Earnings</div>
          <div class="detail-row">
            <span class="detail-label">Basic Pay</span>
            <span class="detail-value">${formatCurrency(Number(item.basic_pay))}</span>
          </div>

          ${item.regular_pay > 0 ? `
            <div class="detail-row">
              <span class="detail-label">Regular Pay (${item.total_regular_hours} hrs)</span>
              <span class="detail-value">${formatCurrency(Number(item.regular_pay))}</span>
            </div>
          ` : ''}

          ${item.overtime_pay > 0 ? `
            <div class="detail-row">
              <span class="detail-label">Overtime Pay (${item.total_overtime_hours} hrs)</span>
              <span class="detail-value">${formatCurrency(Number(item.overtime_pay))}</span>
            </div>
          ` : ''}

          ${item.rest_day_pay > 0 ? `
            <div class="detail-row">
              <span class="detail-label">Rest Day Pay (${item.total_rest_day_hours} hrs)</span>
              <span class="detail-value">${formatCurrency(Number(item.rest_day_pay))}</span>
            </div>
          ` : ''}

          ${item.holiday_pay > 0 ? `
            <div class="detail-row">
              <span class="detail-label">Holiday Pay (${item.total_holiday_hours} hrs)</span>
              <span class="detail-value">${formatCurrency(Number(item.holiday_pay))}</span>
            </div>
          ` : ''}

          ${item.night_differential_pay > 0 ? `
            <div class="detail-row">
              <span class="detail-label">Night Differential (${item.total_night_diff_hours} hrs)</span>
              <span class="detail-value">${formatCurrency(Number(item.night_differential_pay))}</span>
            </div>
          ` : ''}

          ${item.allowances?.map(allowance => `
            <div class="detail-row">
              <span class="detail-label">${allowance.name}</span>
              <span class="detail-value">${formatCurrency(Number(allowance.amount))}</span>
            </div>
          `).join('') || ''}

          <div class="detail-row total-row">
            <span class="detail-label">Total Earnings</span>
            <span class="detail-value">${formatCurrency(Number(item.gross_pay))}</span>
          </div>
        </div>

        <div class="deductions-section">
          <div class="section-title">Deductions</div>

          ${governmentDeductions.length > 0 ? `
            <div class="detail-row">
              <span class="detail-label"><strong>Government Contributions</strong></span>
              <span class="detail-value"></span>
            </div>
            ${governmentDeductions.map(deduction => `
              <div class="detail-row">
                <span class="detail-label">${deduction.name}</span>
                <span class="detail-value">${formatCurrency(Number(deduction.amount))}</span>
              </div>
            `).join('')}
          ` : ''}

          ${taxDeductions.length > 0 ? `
            <div class="detail-row">
              <span class="detail-label"><strong>Taxes</strong></span>
              <span class="detail-value"></span>
            </div>
            ${taxDeductions.map(deduction => `
              <div class="detail-row">
                <span class="detail-label">${deduction.name}</span>
                <span class="detail-value">${formatCurrency(Number(deduction.amount))}</span>
              </div>
            `).join('')}
          ` : ''}

          ${otherDeductions.length > 0 ? `
            <div class="detail-row">
              <span class="detail-label"><strong>Other Deductions</strong></span>
              <span class="detail-value"></span>
            </div>
            ${otherDeductions.map(deduction => `
              <div class="detail-row">
                <span class="detail-label">${deduction.name}</span>
                <span class="detail-value">${formatCurrency(Number(deduction.amount))}</span>
              </div>
            `).join('')}
          ` : ''}

          <div class="detail-row total-row">
            <span class="detail-label">Total Deductions</span>
            <span class="detail-value">${formatCurrency(Number(item.total_deductions))}</span>
          </div>
        </div>

        <div class="summary-section">
          <div class="detail-row">
            <span class="detail-label">Gross Pay</span>
            <span class="detail-value">${formatCurrency(Number(item.gross_pay))}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Total Deductions</span>
            <span class="detail-value">${formatCurrency(Number(item.total_deductions))}</span>
          </div>
          <div class="detail-row total-row">
            <span class="detail-label">Net Pay</span>
            <span class="detail-value net-pay">${formatCurrency(Number(item.net_pay))}</span>
          </div>
        </div>

        <div class="calculation-section">
          <div class="calculation-title">Government Contribution Calculations</div>

          ${sssDeduction ? `
            <div>
              <h4>SSS Contribution</h4>
              <div class="formula">
                Monthly Salary: ${formatCurrency(monthlySalary)}
                SSS Employee Share: ${formatCurrency(Number(sssDeduction.amount))}
              </div>
              <div class="formula-explanation">
                <strong>Formula:</strong> Based on SSS Contribution Table (2023)<br>
                For monthly salary of ${formatCurrency(monthlySalary)}:<br>
                - Employee pays a fixed amount based on salary bracket<br>
                - Brackets range from ₱135 (for salary up to ₱3,249.99) to ₱1,125 (for salary ₱24,750 and above)<br>
                - Employer pays 1.89 times the employee contribution
              </div>
            </div>
          ` : ''}

          ${philhealthDeduction ? `
            <div>
              <h4>PhilHealth Contribution</h4>
              <div class="formula">
                Monthly Salary: ${formatCurrency(monthlySalary)}
                PhilHealth Employee Share: ${formatCurrency(Number(philhealthDeduction.amount))}
              </div>
              <div class="formula-explanation">
                <strong>Formula:</strong> 2% of monthly salary (employee share)<br>
                ${formatCurrency(monthlySalary)} × 2% = ${formatCurrency(monthlySalary * 0.02)}<br>
                <br>
                <strong>Notes:</strong><br>
                - Total PhilHealth contribution is 4% of monthly salary<br>
                - Split equally between employee (2%) and employer (2%)<br>
                - Minimum monthly salary for calculation: ₱10,000<br>
                - Maximum monthly salary for calculation: ₱80,000
              </div>
            </div>
          ` : ''}

          ${pagibigDeduction ? `
            <div>
              <h4>Pag-IBIG Contribution</h4>
              <div class="formula">
                Monthly Salary: ${formatCurrency(monthlySalary)}
                Pag-IBIG Employee Share: ${formatCurrency(Number(pagibigDeduction.amount))}
              </div>
              <div class="formula-explanation">
                <strong>Formula:</strong><br>
                - For monthly salary up to ₱1,500: 1% employee contribution<br>
                - For monthly salary over ₱1,500: 2% employee contribution<br>
                <br>
                ${monthlySalary <= 1500
                  ? `${formatCurrency(monthlySalary)} × 1% = ${formatCurrency(monthlySalary * 0.01)}`
                  : `${formatCurrency(monthlySalary)} × 2% = ${formatCurrency(monthlySalary * 0.02)}`
                }<br>
                <br>
                <strong>Notes:</strong><br>
                - Employer always contributes 2% of monthly salary<br>
                - Maximum monthly contribution: ₱100 for employee, ₱100 for employer
              </div>
            </div>
          ` : ''}

          ${taxDeductions.length > 0 ? `
            <div>
              <h4>Withholding Tax</h4>
              <div class="formula">
                Taxable Income: ${formatCurrency(Number(item.taxable_income || 0))}
                Withholding Tax: ${formatCurrency(Number(item.withholding_tax || 0))}
              </div>
              <div class="formula-explanation">
                <strong>Formula:</strong> Based on BIR Withholding Tax Table (2023)<br>
                <br>
                <strong>Tax Brackets:</strong><br>
                - ₱0 to ₱20,833: 0%<br>
                - ₱20,834 to ₱33,332: 15% of excess over ₱20,833<br>
                - ₱33,333 to ₱66,666: ₱1,875 + 20% of excess over ₱33,333<br>
                - ₱66,667 to ₱166,666: ₱8,541.80 + 25% of excess over ₱66,667<br>
                - ₱166,667 to ₱666,666: ₱33,541.80 + 30% of excess over ₱166,667<br>
                - Over ₱666,667: ₱183,541.80 + 35% of excess over ₱666,667
              </div>
            </div>
          ` : ''}
        </div>

        <div class="footer">
          <p>This is a computer-generated document. No signature is required.</p>
          <p>For questions regarding this payslip, please contact the HR department.</p>
        </div>
      </div>
    </body>
    </html>
  `;
};
