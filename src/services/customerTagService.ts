import { supabase } from '../lib/supabase';
import { Tag } from '../types/tagging.types';

/**
 * Get all tags for a customer
 */
export const getCustomerTags = async (customerId: string): Promise<{
  tags: Tag[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('get_entity_tags', {
      p_entity_type: 'customer',
      p_entity_id: customerId
    });

    if (error) {
      console.error('Error fetching customer tags:', error);
      return { tags: [], error: error.message };
    }

    return { tags: data || [] };
  } catch (err: any) {
    console.error('Error in getCustomerTags:', err);
    return { tags: [], error: err.message };
  }
};

/**
 * Add a tag to a customer
 */
export const addTagToCustomer = async (customerId: string, tagId: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('add_tag_to_entity', {
      p_tag_id: tagId,
      p_entity_type: 'customer',
      p_entity_id: customerId
    });

    if (error) {
      console.error('Error adding tag to customer:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err: any) {
    console.error('Error in addTagToCustomer:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Remove a tag from a customer
 */
export const removeTagFromCustomer = async (customerId: string, tagId: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('remove_tag_from_entity', {
      p_tag_id: tagId,
      p_entity_type: 'customer',
      p_entity_id: customerId
    });

    if (error) {
      console.error('Error removing tag from customer:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err: any) {
    console.error('Error in removeTagFromCustomer:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Find customers by tag
 */
export const findCustomersByTag = async (tagId: string): Promise<{
  customerIds: string[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('find_entities_by_tag', {
      p_tag_id: tagId,
      p_entity_type: 'customer'
    });

    if (error) {
      console.error('Error finding customers by tag:', error);
      return { customerIds: [], error: error.message };
    }

    return { 
      customerIds: data ? data.map(item => item.entity_id) : [] 
    };
  } catch (err: any) {
    console.error('Error in findCustomersByTag:', err);
    return { customerIds: [], error: err.message };
  }
};
