import { supabase } from '../lib/supabase';

/**
 * Allowed file types for chat attachments
 */
export const ALLOWED_FILE_TYPES = {
  // Images
  'image/jpeg': { type: 'image', ext: 'jpg' },
  'image/png': { type: 'image', ext: 'png' },
  'image/gif': { type: 'image', ext: 'gif' },
  'image/webp': { type: 'image', ext: 'webp' },
  'image/svg+xml': { type: 'image', ext: 'svg' },

  // Documents
  'application/pdf': { type: 'document', ext: 'pdf' },
};

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

/**
 * Check if a file is valid for upload
 */
export const isValidFile = (file: File): { valid: boolean; error?: string } => {
  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `File size exceeds the maximum allowed size (${MAX_FILE_SIZE / (1024 * 1024)}MB)`,
    };
  }

  // Check file type
  if (!ALLOWED_FILE_TYPES[file.type]) {
    return {
      valid: false,
      error: 'File type not allowed',
    };
  }

  return { valid: true };
};

/**
 * Upload a file to Supabase storage
 */
export const uploadFile = async (
  file: File,
  userId: string,
): Promise<{ url: string; fileType: string; fileName: string; fileSize: number } | null> => {
  try {
    const fileValidation = isValidFile(file);
    if (!fileValidation.valid) {
      throw new Error(fileValidation.error);
    }

    // Generate a unique file name
    const fileExt = ALLOWED_FILE_TYPES[file.type].ext;
    const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    const filePath = `${userId}/${fileName}`;

    // Upload the file to Supabase storage
    const { data, error } = await supabase.storage.from('chat-attachments').upload(filePath, file, {
      cacheControl: '3600',
      upsert: false,
    });

    if (error) {
      console.error('Error uploading file:', error);
      throw error;
    }

    // Get the public URL for the file
    const {
      data: { publicUrl },
    } = supabase.storage.from('chat-attachments').getPublicUrl(data.path);

    return {
      url: publicUrl,
      fileType: ALLOWED_FILE_TYPES[file.type].type,
      fileName: file.name,
      fileSize: file.size,
    };
  } catch (error) {
    console.error('Error in uploadFile:', error);
    return null;
  }
};

/**
 * Delete a file from Supabase storage
 */
export const deleteFile = async (filePath: string): Promise<boolean> => {
  try {
    // Extract the path from the URL
    const url = new URL(filePath);
    const path = url.pathname.split('/').slice(-2).join('/');

    const { error } = await supabase.storage.from('chat-attachments').remove([path]);

    if (error) {
      console.error('Error deleting file:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteFile:', error);
    return false;
  }
};
