import { supabase } from '../lib/supabase';
import { Customer } from './customer';

export interface LoyaltyProgramSettings {
  id?: string;
  organization_id: string;
  is_enabled: boolean;
  points_earning_rate: number;
  points_redemption_rate: number;
  minimum_points_for_redemption: number;
  points_expiration_months: number | null;
  created_at?: string;
  updated_at?: string;
}

export interface CustomerLoyaltyProfile {
  id?: string;
  customer_id: string;
  organization_id: string;
  current_points_balance: number;
  lifetime_points_earned: number;
  lifetime_points_redeemed: number;
  tier_id?: string | null;
  tier?: LoyaltyTier;
  customer?: Customer;
  created_at?: string;
  updated_at?: string;
}

export interface LoyaltyTier {
  id?: string;
  organization_id: string;
  name: string;
  minimum_points: number;
  points_multiplier: number;
  created_at?: string;
  updated_at?: string;
}

export interface ProductLoyaltySetting {
  id?: string;
  organization_id: string;
  product_id?: string | null;
  category_id?: string | null;
  points_multiplier: number;
  is_excluded_from_earning: boolean;
  is_excluded_from_redemption: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface LoyaltyTransaction {
  id?: string;
  customer_id: string;
  organization_id: string;
  transaction_type: 'earn' | 'redeem' | 'expire' | 'adjust';
  points: number;
  sale_id?: string | null;
  notes?: string | null;
  created_by?: string | null;
  created_at?: string;
}

export interface LoyaltyPromotion {
  id?: string;
  organization_id: string;
  name: string;
  description?: string | null;
  points_multiplier: number;
  start_date: string;
  end_date: string;
  is_active: boolean;
  product_id?: string | null;
  category_id?: string | null;
  created_at?: string;
  updated_at?: string;
}

/**
 * Get loyalty program settings for an organization
 */
export const getLoyaltyProgramSettings = async (
  organizationId: string
): Promise<{
  settings?: LoyaltyProgramSettings;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('loyalty_program_settings')
      .select('*')
      .eq('organization_id', organizationId)
      .single();

    if (error) {
      // If the error is "No rows found", create default settings
      if (error.code === 'PGRST116') {
        return await createDefaultLoyaltySettings(organizationId);
      }
      
      console.error('Error fetching loyalty program settings:', error);
      return { error: error.message };
    }

    return { settings: data as LoyaltyProgramSettings };
  } catch (error: any) {
    console.error('Error in getLoyaltyProgramSettings:', error);
    return { error: error.message };
  }
};

/**
 * Create default loyalty program settings for an organization
 */
const createDefaultLoyaltySettings = async (
  organizationId: string
): Promise<{
  settings?: LoyaltyProgramSettings;
  error?: string;
}> => {
  try {
    const defaultSettings: Omit<LoyaltyProgramSettings, 'id' | 'created_at' | 'updated_at'> = {
      organization_id: organizationId,
      is_enabled: false,
      points_earning_rate: 1.0, // 1 point per currency unit
      points_redemption_rate: 0.01, // $0.01 per point
      minimum_points_for_redemption: 100,
      points_expiration_months: null // Points never expire
    };

    const { data, error } = await supabase
      .from('loyalty_program_settings')
      .insert(defaultSettings)
      .select()
      .single();

    if (error) {
      console.error('Error creating default loyalty settings:', error);
      return { error: error.message };
    }

    return { settings: data as LoyaltyProgramSettings };
  } catch (error: any) {
    console.error('Error in createDefaultLoyaltySettings:', error);
    return { error: error.message };
  }
};

/**
 * Update loyalty program settings
 */
export const updateLoyaltyProgramSettings = async (
  organizationId: string,
  settings: Partial<Omit<LoyaltyProgramSettings, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>
): Promise<{
  settings?: LoyaltyProgramSettings;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('loyalty_program_settings')
      .update(settings)
      .eq('organization_id', organizationId)
      .select()
      .single();

    if (error) {
      console.error('Error updating loyalty program settings:', error);
      return { error: error.message };
    }

    return { settings: data as LoyaltyProgramSettings };
  } catch (error: any) {
    console.error('Error in updateLoyaltyProgramSettings:', error);
    return { error: error.message };
  }
};

/**
 * Get customer loyalty profile
 */
export const getCustomerLoyaltyProfile = async (
  organizationId: string,
  customerId: string
): Promise<{
  profile?: CustomerLoyaltyProfile;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('customer_loyalty_profiles')
      .select(`
        *,
        tier:tier_id(*),
        customer:customer_id(*)
      `)
      .eq('organization_id', organizationId)
      .eq('customer_id', customerId)
      .single();

    if (error) {
      // If no profile exists, return empty object without error
      if (error.code === 'PGRST116') {
        return { profile: undefined };
      }
      
      console.error('Error fetching customer loyalty profile:', error);
      return { error: error.message };
    }

    return { profile: data as CustomerLoyaltyProfile };
  } catch (error: any) {
    console.error('Error in getCustomerLoyaltyProfile:', error);
    return { error: error.message };
  }
};

/**
 * Get loyalty transactions for a customer
 */
export const getCustomerLoyaltyTransactions = async (
  organizationId: string,
  customerId: string,
  options?: {
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }
): Promise<{
  transactions: LoyaltyTransaction[];
  count: number;
  error?: string;
}> => {
  try {
    const {
      limit = 50,
      offset = 0,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = options || {};

    let query = supabase
      .from('loyalty_transactions')
      .select('*', { count: 'exact' })
      .eq('organization_id', organizationId)
      .eq('customer_id', customerId)
      .order(sortBy, { ascending: sortOrder === 'asc' })
      .range(offset, offset + limit - 1);

    const { data, count, error } = await query;

    if (error) {
      console.error('Error fetching loyalty transactions:', error);
      return { transactions: [], count: 0, error: error.message };
    }

    return { 
      transactions: data as LoyaltyTransaction[], 
      count: count || 0 
    };
  } catch (error: any) {
    console.error('Error in getCustomerLoyaltyTransactions:', error);
    return { transactions: [], count: 0, error: error.message };
  }
};

/**
 * Manually adjust customer loyalty points
 */
export const adjustCustomerLoyaltyPoints = async (
  organizationId: string,
  customerId: string,
  userId: string,
  points: number,
  notes: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // Start a transaction
    const { error: transactionError } = await supabase.rpc('adjust_loyalty_points', {
      p_organization_id: organizationId,
      p_customer_id: customerId,
      p_points: points,
      p_notes: notes,
      p_created_by: userId
    });

    if (transactionError) {
      console.error('Error adjusting loyalty points:', transactionError);
      return { success: false, error: transactionError.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in adjustCustomerLoyaltyPoints:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Calculate points that would be earned for a given amount
 */
export const calculatePointsForAmount = async (
  organizationId: string,
  customerId: string | null,
  amount: number
): Promise<{
  points: number;
  error?: string;
}> => {
  try {
    // Get loyalty program settings
    const { settings, error: settingsError } = await getLoyaltyProgramSettings(organizationId);
    
    if (settingsError) {
      return { points: 0, error: settingsError };
    }
    
    // If program is disabled, return 0 points
    if (!settings?.is_enabled) {
      return { points: 0 };
    }
    
    // Calculate base points
    let points = Math.floor(amount * settings.points_earning_rate);
    
    // If customer is provided, apply tier multiplier if applicable
    if (customerId) {
      const { profile } = await getCustomerLoyaltyProfile(organizationId, customerId);
      
      if (profile?.tier?.points_multiplier) {
        points = Math.floor(points * profile.tier.points_multiplier);
      }
    }
    
    return { points };
  } catch (error: any) {
    console.error('Error in calculatePointsForAmount:', error);
    return { points: 0, error: error.message };
  }
};

/**
 * Calculate discount value for points redemption
 */
export const calculateDiscountForPoints = async (
  organizationId: string,
  customerId: string,
  points: number
): Promise<{
  discount: number;
  error?: string;
}> => {
  try {
    // Get loyalty program settings
    const { settings, error: settingsError } = await getLoyaltyProgramSettings(organizationId);

    if (settingsError) {
      return { discount: 0, error: settingsError };
    }

    // If program is disabled, return 0 discount
    if (!settings?.is_enabled) {
      return { discount: 0 };
    }

    // Check if points meet minimum redemption requirement
    if (points < settings.minimum_points_for_redemption) {
      return {
        discount: 0,
        error: `Minimum ${settings.minimum_points_for_redemption} points required for redemption`
      };
    }

    // Get customer profile to check available points
    const { profile, error: profileError } = await getCustomerLoyaltyProfile(organizationId, customerId);

    if (profileError) {
      return { discount: 0, error: profileError };
    }

    // Check if customer has enough points
    if (!profile || profile.current_points_balance < points) {
      return {
        discount: 0,
        error: 'Not enough points available for redemption'
      };
    }

    // Calculate discount value
    const discount = points * settings.points_redemption_rate;

    return { discount };
  } catch (error: any) {
    console.error('Error in calculateDiscountForPoints:', error);
    return { discount: 0, error: error.message };
  }
};

/**
 * Get loyalty points for multiple customers in batch
 */
export const getBatchCustomerLoyaltyPoints = async (
  organizationId: string,
  customerIds: string[]
): Promise<{
  pointsMap: {[key: string]: number};
  error?: string;
}> => {
  try {
    if (!customerIds || customerIds.length === 0) {
      return { pointsMap: {} };
    }

    const { data, error } = await (supabase as any)
      .from('customer_loyalty_profiles')
      .select('customer_id, current_points_balance')
      .eq('organization_id', organizationId)
      .in('customer_id', customerIds);

    if (error) {
      console.error('Error fetching batch loyalty points:', error);
      return { pointsMap: {}, error: error.message };
    }

    // Create a map of customer ID to points
    const pointsMap: {[key: string]: number} = {};
    if (data) {
      data.forEach((item: any) => {
        pointsMap[item.customer_id] = item.current_points_balance || 0;
      });
    }

    return { pointsMap };
  } catch (error: any) {
    console.error('Error in getBatchCustomerLoyaltyPoints:', error);
    return { pointsMap: {}, error: error.message };
  }
};
