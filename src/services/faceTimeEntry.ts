/**
 * Face Recognition Time Entry Bridge Service
 * Bridges facial recognition with existing time entry system
 */

import { supabase } from '../lib/supabase';
import { TimeEntry, TimeEntryStatus } from '../types/payroll';
import { createTimeEntry as createExistingTimeEntry, getTimeEntries } from './timeEntry';
import { logRecognitionAttempt } from './faceRecognition';
import { calculateTimeEntryHours } from '../utils/timeCalculations';

export interface FaceTimeEntryResult {
  success: boolean;
  timeEntry?: TimeEntry;
  error?: string;
  action?: 'clock_in' | 'clock_out';
}

/**
 * Create or update time entry with facial recognition data
 */
export const createFaceTimeEntry = async (
  organizationId: string,
  employeeId: string,
  method: 'facial_recognition' | 'pin' | 'manual' | 'qr_code' | 'badge',
  confidence?: number,
  location?: string,
  userId?: string
): Promise<FaceTimeEntryResult> => {
  try {
    const now = new Date();
    const today = now.toISOString().split('T')[0];

    // Check if employee has an active time entry today
    const { entries } = await getTimeEntries(organizationId, {
      employeeId,
      startDate: new Date(today),
      endDate: new Date(today),
      limit: 10,
    });

    // Find the latest entry for today that doesn't have time_out
    const activeEntry = entries.find(entry => 
      entry.date === today && 
      entry.status === 'present' && 
      !entry.time_out
    );

    if (activeEntry) {
      // Clock out - update existing entry with proper calculations
      const timeInDate = new Date(activeEntry.time_in);
      const timeOutDate = now;

      // Calculate hours using the same logic as manual time entry
      const calculations = calculateTimeEntryHours(timeInDate, timeOutDate, true); // true = exclude lunch break

      console.log('⏰ Time calculations for clock out:', {
        timeIn: timeInDate.toISOString(),
        timeOut: timeOutDate.toISOString(),
        calculations
      });

      const { data, error } = await supabase
        .from('time_entries')
        .update({
          time_out: now.toISOString(),
          clock_out_method: method,
          clock_out_confidence: confidence,
          clock_out_location: location,
          regular_hours: calculations.regular_hours,
          overtime_hours: calculations.overtime_hours,
          night_diff_hours: calculations.night_diff_hours,
          updated_at: now.toISOString(),
        })
        .eq('id', activeEntry.id)
        .select('*, employee:employee_id(*)')
        .single();

      if (error) {
        await logRecognitionAttempt(
          organizationId,
          employeeId,
          'clock_out',
          method,
          false,
          confidence,
          error.message
        );
        return { success: false, error: error.message };
      }

      await logRecognitionAttempt(
        organizationId,
        employeeId,
        'clock_out',
        method,
        true,
        confidence
      );

      return { 
        success: true, 
        timeEntry: data as TimeEntry, 
        action: 'clock_out' 
      };
    } else {
      // Clock in - create new entry
      const timeEntryData = {
        employee_id: employeeId,
        date: today,
        time_in: now.toISOString(),
        status: TimeEntryStatus.PRESENT,
        regular_hours: 0,
        overtime_hours: 0,
        night_diff_hours: 0,
        is_rest_day: false,
        is_holiday: false,
        exclude_lunch_break: true,
      };

      const result = await createExistingTimeEntry(organizationId, timeEntryData);
      
      if (result.error) {
        await logRecognitionAttempt(
          organizationId,
          employeeId,
          'clock_in',
          method,
          false,
          confidence,
          result.error
        );
        return { success: false, error: result.error };
      }

      // Update with facial recognition specific fields
      if (result.entry) {
        const { data, error } = await supabase
          .from('time_entries')
          .update({
            clock_in_method: method,
            clock_in_confidence: confidence,
            clock_in_location: location,
            created_by: userId,
          })
          .eq('id', result.entry.id)
          .select('*, employee:employee_id(*)')
          .single();

        if (error) {
          await logRecognitionAttempt(
            organizationId,
            employeeId,
            'clock_in',
            method,
            false,
            confidence,
            error.message
          );
          return { success: false, error: error.message };
        }

        await logRecognitionAttempt(
          organizationId,
          employeeId,
          'clock_in',
          method,
          true,
          confidence
        );

        return { 
          success: true, 
          timeEntry: data as TimeEntry, 
          action: 'clock_in' 
        };
      }

      await logRecognitionAttempt(
        organizationId,
        employeeId,
        'clock_in',
        method,
        true,
        confidence
      );

      return { 
        success: true, 
        timeEntry: result.entry, 
        action: 'clock_in' 
      };
    }
  } catch (error: any) {
    console.error('Error creating face time entry:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get current clock status for employee
 */
export const getCurrentClockStatus = async (
  organizationId: string,
  employeeId: string
): Promise<{
  status: 'clocked_out' | 'clocked_in';
  timeEntry?: TimeEntry;
  error?: string;
}> => {
  try {
    const today = new Date().toISOString().split('T')[0];

    const { entries, error } = await getTimeEntries(organizationId, {
      employeeId,
      startDate: new Date(today),
      endDate: new Date(today),
      limit: 1,
    });

    if (error) {
      return { status: 'clocked_out', error };
    }

    if (!entries || entries.length === 0) {
      return { status: 'clocked_out' };
    }

    const latestEntry = entries[0];
    
    // Check if the latest entry is for today and doesn't have time_out
    if (latestEntry.date === today && 
        latestEntry.status === 'present' && 
        !latestEntry.time_out) {
      return { status: 'clocked_in', timeEntry: latestEntry };
    }

    return { status: 'clocked_out', timeEntry: latestEntry };
  } catch (error: any) {
    console.error('Error getting clock status:', error);
    return { status: 'clocked_out', error: error.message };
  }
};

/**
 * Get today's time entries for employee
 */
export const getTodayTimeEntries = async (
  organizationId: string,
  employeeId: string
): Promise<{
  entries: TimeEntry[];
  error?: string;
}> => {
  try {
    const today = new Date().toISOString().split('T')[0];

    const { entries, error } = await getTimeEntries(organizationId, {
      employeeId,
      startDate: new Date(today),
      endDate: new Date(today),
    });

    if (error) {
      return { entries: [], error };
    }

    return { entries };
  } catch (error: any) {
    console.error('Error getting today time entries:', error);
    return { entries: [], error: error.message };
  }
};

/**
 * Manual time entry creation with facial recognition tracking
 */
export const createManualTimeEntry = async (
  organizationId: string,
  timeEntryData: any,
  userId: string,
  method: 'manual' | 'admin_override' = 'manual'
): Promise<{
  success: boolean;
  timeEntry?: TimeEntry;
  error?: string;
}> => {
  try {
    const result = await createExistingTimeEntry(organizationId, timeEntryData);
    
    if (result.error) {
      return { success: false, error: result.error };
    }

    // Update with method tracking
    if (result.entry) {
      const { data, error } = await supabase
        .from('time_entries')
        .update({
          clock_in_method: method,
          clock_out_method: method,
          created_by: userId,
        })
        .eq('id', result.entry.id)
        .select('*, employee:employee_id(*)')
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      // Log manual entry
      await logRecognitionAttempt(
        organizationId,
        timeEntryData.employee_id,
        'clock_in',
        method,
        true
      );

      return { success: true, timeEntry: data as TimeEntry };
    }

    return { success: true, timeEntry: result.entry };
  } catch (error: any) {
    console.error('Error creating manual time entry:', error);
    return { success: false, error: error.message };
  }
};
