import { Product, getProducts, getProductById } from './product';

/**
 * A simple in-memory cache for products to reduce database queries
 */
class ProductCacheService {
  private cache: Map<string, Product> = new Map();
  private organizationCache: Map<string, Product[]> = new Map();
  private lastFetchTime: Map<string, number> = new Map();
  private cacheTTL = 5 * 60 * 1000; // 5 minutes in milliseconds

  /**
   * Get a product from the cache or fetch it from the database
   * @param organizationId The organization ID
   * @param productId The product ID
   * @returns The product
   */
  async getProduct(organizationId: string, productId: string): Promise<Product | null> {
    const cacheKey = `${organizationId}:${productId}`;
    
    // Check if the product is in the cache and not expired
    if (this.cache.has(cacheKey)) {
      const lastFetch = this.lastFetchTime.get(cacheKey) || 0;
      const now = Date.now();
      
      // If the cache is still valid, return the cached product
      if (now - lastFetch < this.cacheTTL) {
        return this.cache.get(cacheKey) || null;
      }
    }
    
    // If not in cache or expired, fetch from the database
    try {
      const { product, error } = await getProductById(organizationId, productId);
      
      if (error || !product) {
        return null;
      }
      
      // Store in cache
      this.cache.set(cacheKey, product);
      this.lastFetchTime.set(cacheKey, Date.now());
      
      return product;
    } catch (error) {
      console.error('Error fetching product for cache:', error);
      return null;
    }
  }

  /**
   * Get multiple products from the cache or fetch them from the database
   * @param organizationId The organization ID
   * @param productIds Array of product IDs
   * @returns Array of products
   */
  async getProducts(organizationId: string, productIds: string[]): Promise<Product[]> {
    // Filter out products that are already in the cache and not expired
    const now = Date.now();
    const cachedProducts: Product[] = [];
    const productsToFetch: string[] = [];
    
    for (const productId of productIds) {
      const cacheKey = `${organizationId}:${productId}`;
      const lastFetch = this.lastFetchTime.get(cacheKey) || 0;
      
      if (this.cache.has(cacheKey) && now - lastFetch < this.cacheTTL) {
        const product = this.cache.get(cacheKey);
        if (product) {
          cachedProducts.push(product);
        }
      } else {
        productsToFetch.push(productId);
      }
    }
    
    // If all products are in the cache, return them
    if (productsToFetch.length === 0) {
      return cachedProducts;
    }
    
    // Fetch the remaining products
    try {
      // Use a single query with an IN clause to fetch all products at once
      const { products, error } = await getProducts(organizationId, {
        // We can't directly filter by ID in the getProducts function,
        // so we'll fetch all and filter client-side
        limit: 1000 // Set a high limit to ensure we get all products
      });
      
      if (error || !products) {
        return cachedProducts;
      }
      
      // Filter to only the products we need
      const fetchedProducts = products.filter(p => productsToFetch.includes(p.id));
      
      // Store in cache
      for (const product of fetchedProducts) {
        const cacheKey = `${organizationId}:${product.id}`;
        this.cache.set(cacheKey, product);
        this.lastFetchTime.set(cacheKey, now);
      }
      
      // Return all products (cached + newly fetched)
      return [...cachedProducts, ...fetchedProducts];
    } catch (error) {
      console.error('Error fetching products for cache:', error);
      return cachedProducts;
    }
  }

  /**
   * Search for products and cache the results
   * @param organizationId The organization ID
   * @param searchQuery The search query
   * @param page The page number (1-based)
   * @param pageSize The page size
   * @returns The products and total count
   */
  async searchProducts(
    organizationId: string, 
    searchQuery: string, 
    page: number = 1, 
    pageSize: number = 10
  ): Promise<{ products: Product[], totalCount: number }> {
    // Calculate offset
    const offset = (page - 1) * pageSize;
    
    try {
      const { products, count, error } = await getProducts(organizationId, {
        searchQuery,
        limit: pageSize,
        offset,
        isActive: true
      });
      
      if (error || !products) {
        return { products: [], totalCount: 0 };
      }
      
      // Cache the products
      const now = Date.now();
      for (const product of products) {
        const cacheKey = `${organizationId}:${product.id}`;
        this.cache.set(cacheKey, product);
        this.lastFetchTime.set(cacheKey, now);
      }
      
      return { products, totalCount: count };
    } catch (error) {
      console.error('Error searching products for cache:', error);
      return { products: [], totalCount: 0 };
    }
  }

  /**
   * Clear the cache for a specific organization
   * @param organizationId The organization ID
   */
  clearOrganizationCache(organizationId: string): void {
    // Clear the organization's product list cache
    this.organizationCache.delete(organizationId);
    
    // Clear individual product caches for this organization
    for (const key of this.cache.keys()) {
      if (key.startsWith(`${organizationId}:`)) {
        this.cache.delete(key);
        this.lastFetchTime.delete(key);
      }
    }
  }

  /**
   * Clear the entire cache
   */
  clearCache(): void {
    this.cache.clear();
    this.organizationCache.clear();
    this.lastFetchTime.clear();
  }
}

// Create a singleton instance
export const productCache = new ProductCacheService();
