import { supabase } from '../lib/supabase';
import { parseISO } from 'date-fns';

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export enum CostingMethodType {
  SIMPLE = 'simple',
  FIFO = 'fifo',
  LIFO = 'lifo',
  WEIGHTED_AVERAGE = 'weighted_average'
}

export interface InventoryLayer {
  id: string;
  productId: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
  transactionDate: Date;
  transactionId: string;
  transactionType: string;
  remainingQuantity: number;
}

export interface CostCalculationResult {
  totalCost: number;
  averageCost: number;
  layersUsed: InventoryLayer[];
  method: CostingMethodType;
}

export interface SaleItem {
  id: string;
  productId: string;
  quantity: number;
  baseQuantity: number;
  saleDate: Date;
}

// =====================================================
// ABSTRACT BASE CLASS
// =====================================================

export abstract class CostingMethod {
  protected organizationId: string;
  protected method: CostingMethodType;

  constructor(organizationId: string, method: CostingMethodType) {
    this.organizationId = organizationId;
    this.method = method;
  }

  abstract calculateCOGS(saleItems: SaleItem[]): Promise<CostCalculationResult>;
  
  getMethodName(): string {
    return this.method;
  }

  // Get inventory layers for a product (purchases/receipts)
  protected async getInventoryLayers(productId: string, beforeDate?: Date): Promise<InventoryLayer[]> {
    try {
      // First get inventory transactions
      let transactionQuery = supabase
        .from('inventory_transactions')
        .select(`
          id,
          product_id,
          quantity,
          created_at,
          transaction_type,
          reference_id,
          reference_type
        `)
        .eq('organization_id', this.organizationId)
        .eq('product_id', productId)
        .in('transaction_type', ['purchase', 'receipt'])
        .gt('quantity', 0)
        .order('created_at', { ascending: true });

      if (beforeDate) {
        transactionQuery = transactionQuery.lte('created_at', beforeDate.toISOString());
      }

      const { data: transactions, error: transactionError } = await transactionQuery;

      if (transactionError) {
        console.error('Error fetching inventory transactions:', transactionError);
        return [];
      }

      if (!transactions || transactions.length === 0) {
        return [];
      }

      // Get receipt item costs for transactions that reference receipt items
      const receiptItemIds = transactions
        .filter(t => t.reference_type === 'inventory_receipt_item' && t.reference_id)
        .map(t => t.reference_id)
        .filter((id): id is string => id !== null);

      let receiptItemCosts = new Map<string, number>();

      if (receiptItemIds.length > 0) {
        const { data: receiptItems, error: receiptError } = await supabase
          .from('inventory_receipt_items')
          .select('id, unit_cost')
          .in('id', receiptItemIds);

        if (!receiptError && receiptItems) {
          receiptItemCosts = new Map(receiptItems.map(item => [item.id, item.unit_cost]));
        }
      }

      // Get product cost_price as fallback
      const { data: product } = await supabase
        .from('products')
        .select('cost_price')
        .eq('id', productId)
        .eq('organization_id', this.organizationId)
        .single();

      const fallbackCost = product?.cost_price || 0;

      return transactions.map(transaction => {
        let unitCost = fallbackCost;

        // Use receipt item cost if available
        if (transaction.reference_type === 'inventory_receipt_item' && transaction.reference_id) {
          unitCost = receiptItemCosts.get(transaction.reference_id) || fallbackCost;
        }

        return {
          id: transaction.id,
          productId: transaction.product_id,
          quantity: transaction.quantity,
          unitCost: unitCost,
          totalCost: transaction.quantity * unitCost,
          transactionDate: parseISO(transaction.created_at),
          transactionId: transaction.reference_id || transaction.id,
          transactionType: transaction.transaction_type,
          remainingQuantity: transaction.quantity // Will be updated during calculation
        };
      });
    } catch (error) {
      console.error('Error in getInventoryLayers:', error);
      return [];
    }
  }

  // Get sales transactions that consumed inventory before a certain date
  protected async getConsumedQuantity(productId: string, beforeDate: Date): Promise<number> {
    try {
      const { data, error } = await supabase
        .from('inventory_transactions')
        .select('quantity')
        .eq('organization_id', this.organizationId)
        .eq('product_id', productId)
        .eq('transaction_type', 'sale')
        .lt('quantity', 0) // Sales are negative quantities
        .lte('created_at', beforeDate.toISOString());

      if (error) {
        console.error('Error fetching consumed quantity:', error);
        return 0;
      }

      return Math.abs((data || []).reduce((sum, transaction) => sum + transaction.quantity, 0));
    } catch (error) {
      console.error('Error in getConsumedQuantity:', error);
      return 0;
    }
  }
}

// =====================================================
// SIMPLE COSTING METHOD (FREE TIER)
// =====================================================

export class SimpleCostingMethod extends CostingMethod {
  constructor(organizationId: string) {
    super(organizationId, CostingMethodType.SIMPLE);
  }

  async calculateCOGS(saleItems: SaleItem[]): Promise<CostCalculationResult> {
    try {
      const productIds = [...new Set(saleItems.map(item => item.productId))];
      
      // Get product cost prices
      const { data: products, error } = await supabase
        .from('products')
        .select('id, cost_price')
        .in('id', productIds)
        .eq('organization_id', this.organizationId);

      if (error) {
        console.error('Error fetching product costs:', error);
        return { totalCost: 0, averageCost: 0, layersUsed: [], method: this.method };
      }

      const productCosts = new Map(products?.map(p => [p.id, p.cost_price || 0]) || []);
      
      let totalCost = 0;
      let totalQuantity = 0;

      for (const item of saleItems) {
        const costPrice = productCosts.get(item.productId) || 0;
        // IMPORTANT: Use actual sale quantity (not baseQuantity) because:
        // - Sales are always in pieces (pcs) from sale_items.quantity
        // - UOM conversions (base_quantity) are only used for purchases
        // - Using base_quantity would inflate COGS incorrectly
        const quantity = item.quantity; // This comes from sale_items.quantity
        totalCost += costPrice * quantity;
        totalQuantity += quantity;
      }

      return {
        totalCost,
        averageCost: totalQuantity > 0 ? totalCost / totalQuantity : 0,
        layersUsed: [],
        method: this.method
      };
    } catch (error) {
      console.error('Error in SimpleCostingMethod:', error);
      return { totalCost: 0, averageCost: 0, layersUsed: [], method: this.method };
    }
  }
}

// =====================================================
// FIFO COSTING METHOD (PAID TIER)
// =====================================================

export class FIFOCostingMethod extends CostingMethod {
  constructor(organizationId: string) {
    super(organizationId, CostingMethodType.FIFO);
  }

  async calculateCOGS(saleItems: SaleItem[]): Promise<CostCalculationResult> {
    try {
      const productGroups = new Map<string, SaleItem[]>();
      
      // Group sale items by product
      for (const item of saleItems) {
        if (!productGroups.has(item.productId)) {
          productGroups.set(item.productId, []);
        }
        productGroups.get(item.productId)!.push(item);
      }

      let totalCost = 0;
      let totalQuantity = 0;
      const allLayersUsed: InventoryLayer[] = [];

      // Calculate COGS for each product using FIFO
      for (const [productId, items] of productGroups) {
        // Use actual sale quantity (not baseQuantity) because sales are always in pieces
        const productQuantity = items.reduce((sum, item) => sum + item.quantity, 0);
        const latestSaleDate = new Date(Math.max(...items.map(item => item.saleDate.getTime())));
        
        // Get inventory layers (oldest first for FIFO)
        const layers = await this.getInventoryLayers(productId, latestSaleDate);
        
        // Adjust layers for previously consumed inventory
        const consumedQty = await this.getConsumedQuantity(productId, latestSaleDate);
        const adjustedLayers = this.adjustLayersForConsumption(layers, consumedQty);
        
        // Apply FIFO logic
        const result = this.applyFIFO(adjustedLayers, productQuantity);
        
        totalCost += result.cost;
        totalQuantity += productQuantity;
        allLayersUsed.push(...result.layersUsed);
      }

      return {
        totalCost,
        averageCost: totalQuantity > 0 ? totalCost / totalQuantity : 0,
        layersUsed: allLayersUsed,
        method: this.method
      };
    } catch (error) {
      console.error('Error in FIFOCostingMethod:', error);
      return { totalCost: 0, averageCost: 0, layersUsed: [], method: this.method };
    }
  }

  private adjustLayersForConsumption(layers: InventoryLayer[], consumedQty: number): InventoryLayer[] {
    const adjustedLayers = [...layers];
    let remainingToConsume = consumedQty;

    for (const layer of adjustedLayers) {
      if (remainingToConsume <= 0) break;
      
      const consumeFromLayer = Math.min(layer.remainingQuantity, remainingToConsume);
      layer.remainingQuantity -= consumeFromLayer;
      remainingToConsume -= consumeFromLayer;
    }

    return adjustedLayers.filter(layer => layer.remainingQuantity > 0);
  }

  private applyFIFO(layers: InventoryLayer[], quantityNeeded: number): { cost: number; layersUsed: InventoryLayer[] } {
    let remainingQuantity = quantityNeeded;
    let totalCost = 0;
    const layersUsed: InventoryLayer[] = [];

    for (const layer of layers) {
      if (remainingQuantity <= 0) break;
      
      const quantityFromLayer = Math.min(layer.remainingQuantity, remainingQuantity);
      const costFromLayer = quantityFromLayer * layer.unitCost;
      
      totalCost += costFromLayer;
      remainingQuantity -= quantityFromLayer;
      
      layersUsed.push({
        ...layer,
        quantity: quantityFromLayer,
        totalCost: costFromLayer,
        remainingQuantity: quantityFromLayer
      });
    }

    return { cost: totalCost, layersUsed };
  }
}

// =====================================================
// LIFO COSTING METHOD (PAID TIER)
// =====================================================

export class LIFOCostingMethod extends CostingMethod {
  constructor(organizationId: string) {
    super(organizationId, CostingMethodType.LIFO);
  }

  async calculateCOGS(saleItems: SaleItem[]): Promise<CostCalculationResult> {
    try {
      const productGroups = new Map<string, SaleItem[]>();

      // Group sale items by product
      for (const item of saleItems) {
        if (!productGroups.has(item.productId)) {
          productGroups.set(item.productId, []);
        }
        productGroups.get(item.productId)!.push(item);
      }

      let totalCost = 0;
      let totalQuantity = 0;
      const allLayersUsed: InventoryLayer[] = [];

      // Calculate COGS for each product using LIFO
      for (const [productId, items] of productGroups) {
        // Use actual sale quantity (not baseQuantity) because sales are always in pieces
        const productQuantity = items.reduce((sum, item) => sum + item.quantity, 0);
        const latestSaleDate = new Date(Math.max(...items.map(item => item.saleDate.getTime())));

        // Get inventory layers (newest first for LIFO)
        const layers = await this.getInventoryLayers(productId, latestSaleDate);
        const reversedLayers = layers.reverse(); // LIFO uses newest first

        // Adjust layers for previously consumed inventory (from oldest first)
        const consumedQty = await this.getConsumedQuantity(productId, latestSaleDate);
        const adjustedLayers = this.adjustLayersForConsumption(reversedLayers, consumedQty);

        // Apply LIFO logic
        const result = this.applyLIFO(adjustedLayers, productQuantity);

        totalCost += result.cost;
        totalQuantity += productQuantity;
        allLayersUsed.push(...result.layersUsed);
      }

      return {
        totalCost,
        averageCost: totalQuantity > 0 ? totalCost / totalQuantity : 0,
        layersUsed: allLayersUsed,
        method: this.method
      };
    } catch (error) {
      console.error('Error in LIFOCostingMethod:', error);
      return { totalCost: 0, averageCost: 0, layersUsed: [], method: this.method };
    }
  }

  private adjustLayersForConsumption(layers: InventoryLayer[], consumedQty: number): InventoryLayer[] {
    // For LIFO, we need to consume from newest layers first
    const adjustedLayers = [...layers];
    let remainingToConsume = consumedQty;

    for (const layer of adjustedLayers) {
      if (remainingToConsume <= 0) break;

      const consumeFromLayer = Math.min(layer.remainingQuantity, remainingToConsume);
      layer.remainingQuantity -= consumeFromLayer;
      remainingToConsume -= consumeFromLayer;
    }

    return adjustedLayers.filter(layer => layer.remainingQuantity > 0);
  }

  private applyLIFO(layers: InventoryLayer[], quantityNeeded: number): { cost: number; layersUsed: InventoryLayer[] } {
    let remainingQuantity = quantityNeeded;
    let totalCost = 0;
    const layersUsed: InventoryLayer[] = [];

    // LIFO: Use newest layers first
    for (const layer of layers) {
      if (remainingQuantity <= 0) break;

      const quantityFromLayer = Math.min(layer.remainingQuantity, remainingQuantity);
      const costFromLayer = quantityFromLayer * layer.unitCost;

      totalCost += costFromLayer;
      remainingQuantity -= quantityFromLayer;

      layersUsed.push({
        ...layer,
        quantity: quantityFromLayer,
        totalCost: costFromLayer,
        remainingQuantity: quantityFromLayer
      });
    }

    return { cost: totalCost, layersUsed };
  }
}

// =====================================================
// WEIGHTED AVERAGE COSTING METHOD (PAID TIER)
// =====================================================

export class WeightedAverageCostingMethod extends CostingMethod {
  constructor(organizationId: string) {
    super(organizationId, CostingMethodType.WEIGHTED_AVERAGE);
  }

  async calculateCOGS(saleItems: SaleItem[]): Promise<CostCalculationResult> {
    try {
      const productGroups = new Map<string, SaleItem[]>();

      // Group sale items by product
      for (const item of saleItems) {
        if (!productGroups.has(item.productId)) {
          productGroups.set(item.productId, []);
        }
        productGroups.get(item.productId)!.push(item);
      }

      let totalCost = 0;
      let totalQuantity = 0;
      const allLayersUsed: InventoryLayer[] = [];

      // Calculate COGS for each product using Weighted Average
      for (const [productId, items] of productGroups) {
        // Use actual sale quantity (not baseQuantity) because sales are always in pieces
        const productQuantity = items.reduce((sum, item) => sum + item.quantity, 0);
        const latestSaleDate = new Date(Math.max(...items.map(item => item.saleDate.getTime())));

        // Get all inventory layers
        const layers = await this.getInventoryLayers(productId, latestSaleDate);

        // Calculate weighted average cost
        const result = this.calculateWeightedAverage(layers, productQuantity);

        totalCost += result.cost;
        totalQuantity += productQuantity;
        allLayersUsed.push(...result.layersUsed);
      }

      return {
        totalCost,
        averageCost: totalQuantity > 0 ? totalCost / totalQuantity : 0,
        layersUsed: allLayersUsed,
        method: this.method
      };
    } catch (error) {
      console.error('Error in WeightedAverageCostingMethod:', error);
      return { totalCost: 0, averageCost: 0, layersUsed: [], method: this.method };
    }
  }

  private calculateWeightedAverage(layers: InventoryLayer[], quantityNeeded: number): { cost: number; layersUsed: InventoryLayer[] } {
    if (layers.length === 0) {
      return { cost: 0, layersUsed: [] };
    }

    // Calculate total available quantity and total cost
    const totalAvailableQuantity = layers.reduce((sum, layer) => sum + layer.remainingQuantity, 0);
    const totalAvailableCost = layers.reduce((sum, layer) => sum + (layer.remainingQuantity * layer.unitCost), 0);

    // Calculate weighted average cost per unit
    const weightedAverageCost = totalAvailableQuantity > 0 ? totalAvailableCost / totalAvailableQuantity : 0;

    // Calculate total cost for the quantity needed
    const totalCost = quantityNeeded * weightedAverageCost;

    // Create a single layer representing the weighted average
    const layersUsed: InventoryLayer[] = [{
      id: 'weighted-average',
      productId: layers[0]?.productId || '',
      quantity: quantityNeeded,
      unitCost: weightedAverageCost,
      totalCost: totalCost,
      transactionDate: new Date(),
      transactionId: 'weighted-average',
      transactionType: 'weighted_average',
      remainingQuantity: quantityNeeded
    }];

    return { cost: totalCost, layersUsed };
  }
}

// =====================================================
// COSTING METHOD FACTORY
// =====================================================

export class CostingMethodFactory {
  static createMethod(organizationId: string, method: CostingMethodType): CostingMethod {
    switch (method) {
      case CostingMethodType.SIMPLE:
        return new SimpleCostingMethod(organizationId);
      case CostingMethodType.FIFO:
        return new FIFOCostingMethod(organizationId);
      case CostingMethodType.LIFO:
        return new LIFOCostingMethod(organizationId);
      case CostingMethodType.WEIGHTED_AVERAGE:
        return new WeightedAverageCostingMethod(organizationId);
      default:
        return new SimpleCostingMethod(organizationId);
    }
  }

  static getAvailableMethods(): { value: CostingMethodType; label: string; description: string }[] {
    return [
      {
        value: CostingMethodType.SIMPLE,
        label: 'Simple Cost',
        description: 'Uses product cost price (Free tier)'
      },
      {
        value: CostingMethodType.FIFO,
        label: 'FIFO (First In, First Out)',
        description: 'Uses oldest inventory costs first'
      },
      {
        value: CostingMethodType.LIFO,
        label: 'LIFO (Last In, First Out)',
        description: 'Uses newest inventory costs first'
      },
      {
        value: CostingMethodType.WEIGHTED_AVERAGE,
        label: 'Weighted Average',
        description: 'Uses average cost of all inventory'
      }
    ];
  }
}

// =====================================================
// ORGANIZATION COSTING SETTINGS
// =====================================================

export interface OrganizationCostingSettings {
  organizationId: string;
  costingMethod: CostingMethodType;
  lastUpdated: Date;
  updatedBy: string;
}

export class CostingSettingsService {
  static async getCostingMethod(organizationId: string): Promise<CostingMethodType> {
    try {
      // For now, we'll assume all users are paid (as requested)
      // In the future, this would check subscription status

      // Try to get organization settings from a settings table
      // For now, return FIFO as default for paid users
      return CostingMethodType.FIFO;

      // Future implementation:
      // const { data } = await supabase
      //   .from('organization_settings')
      //   .select('costing_method')
      //   .eq('organization_id', organizationId)
      //   .single();
      //
      // return data?.costing_method || CostingMethodType.SIMPLE;
    } catch (error) {
      console.error('Error getting costing method:', error);
      return CostingMethodType.SIMPLE;
    }
  }

  static async setCostingMethod(
    organizationId: string,
    method: CostingMethodType,
    userId: string
  ): Promise<boolean> {
    try {
      // Future implementation for saving settings
      console.log(`Setting costing method for ${organizationId} to ${method} by ${userId}`);

      // const { error } = await supabase
      //   .from('organization_settings')
      //   .upsert({
      //     organization_id: organizationId,
      //     costing_method: method,
      //     updated_by: userId,
      //     updated_at: new Date().toISOString()
      //   });
      //
      // return !error;

      return true;
    } catch (error) {
      console.error('Error setting costing method:', error);
      return false;
    }
  }
}

// =====================================================
// MAIN COSTING SERVICE
// =====================================================

export class CostingService {
  static async calculateCOGS(
    organizationId: string,
    saleItems: SaleItem[],
    method?: CostingMethodType
  ): Promise<CostCalculationResult> {
    try {
      // Get the costing method (use provided method or get from settings)
      const costingMethod = method || await CostingSettingsService.getCostingMethod(organizationId);

      // Create the appropriate costing method instance
      const costingInstance = CostingMethodFactory.createMethod(organizationId, costingMethod);

      // Calculate COGS
      const result = await costingInstance.calculateCOGS(saleItems);

      console.log(`COGS calculated using ${costingMethod}:`, {
        totalCost: result.totalCost,
        averageCost: result.averageCost,
        layersUsed: result.layersUsed.length,
        method: result.method
      });

      return result;
    } catch (error) {
      console.error('Error in CostingService.calculateCOGS:', error);
      return {
        totalCost: 0,
        averageCost: 0,
        layersUsed: [],
        method: CostingMethodType.SIMPLE
      };
    }
  }
}
