import { supabase } from '../lib/supabase';
import { AuthError, AuthResponse, Session, User } from '@supabase/supabase-js';

export interface SignUpData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  organizationName?: string;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  error: AuthError | null;
}

/**
 * Sign up a new user
 *
 * Note: We're not creating profiles and organizations during signup anymore
 * because the user isn't fully authenticated yet. Instead, we'll use the
 * ProfileSetupCheck component to create these after login.
 */
export const signUp = async ({
  email,
  password,
  firstName,
  lastName,
  organizationName,
}: SignUpData): Promise<AuthResponse> => {
  try {
    console.log('Signing up user:', { email });

    // Sign up the user with Supabase Auth
    const response = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          // Only include these fields if they're provided
          ...(firstName && { first_name: firstName }),
          ...(lastName && { last_name: lastName }),
          ...(organizationName && { organization_name: organizationName }),
        },
        emailRedirectTo: `${window.location.origin}/auth/login`,
      },
    });

    console.log('Auth response:', response);

    if (response.error) {
      console.error('Auth error:', response.error);
      throw response.error;
    }

    // Store the user ID in localStorage for use after login
    // We don't need to store firstName, lastName, or organizationName anymore
    // since we'll ask for this information after login
    localStorage.setItem(
      'pendingProfileSetup',
      JSON.stringify({
        userId: response.data.user?.id,
      }),
    );

    return response;
  } catch (error) {
    console.error('Signup error:', error);
    throw error;
  }
};

/**
 * Sign in a user
 */
export const signIn = async ({ email, password }: SignInData): Promise<AuthResponse> => {
  try {
    console.log('Signing in user:', { email });

    const response = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    console.log('Sign in response:', {
      success: !response.error,
      hasSession: !!response.data.session,
      error: response.error ? response.error.message : null,
    });

    return response;
  } catch (error) {
    console.error('Sign in error:', error);
    throw error;
  }
};

/**
 * Sign out the current user
 */
export const signOut = async (): Promise<void> => {
  await supabase.auth.signOut();
};

/**
 * Get the current session
 */
export const getSession = async (): Promise<Session | null> => {
  const { data } = await supabase.auth.getSession();
  return data.session;
};

/**
 * Get the current user
 */
export const getUser = async (): Promise<User | null> => {
  const { data } = await supabase.auth.getUser();
  return data.user;
};

/**
 * Reset password
 */
export const resetPassword = async (email: string): Promise<{ error: AuthError | null }> => {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/reset-password`,
  });
  return { error };
};

/**
 * Update password
 */
export const updatePassword = async (password: string): Promise<{ error: AuthError | null }> => {
  const { error } = await supabase.auth.updateUser({
    password,
  });
  return { error };
};

/**
 * Update user profile
 */
export const updateProfile = async (
  userId: string,
  updates: { first_name?: string; last_name?: string; avatar_url?: string },
): Promise<{ error: any | null }> => {
  const { error } = await supabase.from('profiles').update(updates).eq('id', userId);
  return { error };
};
