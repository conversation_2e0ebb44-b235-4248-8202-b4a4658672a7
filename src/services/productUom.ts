import { supabase } from '../lib/supabase';
import { ProductUom, UnitOfMeasurement } from '../types/uom.types';
import { getProductById } from './product';
import { getUnitsOfMeasurement } from './uom';

/**
 * Get conversion factors for a product
 */
export const getProductConversionFactors = async (
  productId: string,
  organizationId: string
): Promise<{
  conversionFactors: Record<string, number>;
  error?: string;
}> => {
  try {
    console.log('Getting conversion factors for product:', productId);

    // Get product UoMs with conversion factors directly
    // Skip checking organization UoMs first to simplify
    const { data, error } = await supabase
      .from('product_uoms')
      .select(`
        uom_id,
        conversion_factor
      `)
      .eq('product_id', productId)
      .eq('organization_id', organizationId);

    if (error) {
      console.error('Error fetching product UoMs:', error);
      return {
        conversionFactors: {},
        error: error.message
      };
    }

    console.log('Product UoMs query result:', { data, error });

    // Create a map of UoM ID to conversion factor
    const conversionFactors: Record<string, number> = {};

    if (data && data.length > 0) {
      data.forEach(pu => {
        if (pu.uom_id && pu.conversion_factor) {
          conversionFactors[pu.uom_id] = pu.conversion_factor;
        }
      });
    } else {
      // If no product UoMs found, add a default conversion factor of 1 for all UoMs
      try {
        const { uoms } = await getUnitsOfMeasurement(organizationId);
        if (uoms && uoms.length > 0) {
          uoms.forEach(uom => {
            conversionFactors[uom.id] = 1;
          });
        }
      } catch (err) {
        console.error('Error fetching organization UoMs as fallback:', err);
      }
    }

    console.log('Conversion factors:', conversionFactors);

    return {
      conversionFactors
    };
  } catch (error: any) {
    console.error('Error in getProductConversionFactors:', error);
    return {
      conversionFactors: {},
      error: error.message
    };
  }
};

/**
 * Get all UoMs for a product
 */
export const getProductUoms = async (
  productId: string,
  organizationId?: string
): Promise<{
  productUoms: (ProductUom & { uom: UnitOfMeasurement })[];
  error?: string;
}> => {
  console.log('getProductUoms called with:', { productId, organizationId });

  try {
    // If organizationId is not provided, get it from the product
    if (!organizationId) {
      console.log('No organizationId provided, fetching from product');
      const { product, error: productError } = await getProductById(productId);
      console.log('Product fetch result:', { product, error: productError });

      if (productError || !product) {
        console.error('Failed to get product:', productError);
        return {
          productUoms: [],
          error: productError || 'Product not found',
        };
      }
      organizationId = product.organization_id;
      console.log('Using organization_id from product:', organizationId);
    }

    console.log('Querying product_uoms with:', { productId, organizationId });

    // First, check if the product exists
    const { data: productExists, error: productCheckError } = await supabase
      .from('products')
      .select('id')
      .eq('id', productId)
      .eq('organization_id', organizationId)
      .single();

    if (productCheckError) {
      console.error('Product does not exist or does not belong to this organization:', productCheckError);
      return {
        productUoms: [],
        error: 'Product not found in this organization',
      };
    }

    // Check if any UoMs exist for this organization
    const { data: orgUoms, error: orgUomsError } = await supabase
      .from('units_of_measurement')
      .select('id, code, name')
      .eq('organization_id', organizationId);

    if (orgUomsError) {
      console.error('Error checking organization UoMs:', orgUomsError);
    } else {
      console.log('Organization has UoMs:', orgUoms);
    }

    // Now query product UoMs
    const { data, error } = await supabase
      .from('product_uoms')
      .select(`
        *,
        uom:uom_id (*)
      `)
      .eq('product_id', productId)
      .eq('organization_id', organizationId)
      .order('is_default', { ascending: false });

    console.log('Product UoMs query result:', { data, error, count: data?.length || 0 });

    if (error) {
      console.error('Error fetching product UoMs:', error);
      return {
        productUoms: [],
        error: error.message,
      };
    }

    // If no UoMs found, try to create a default one
    if (!data || data.length === 0) {
      console.warn('No UoMs found for product. Checking for default UoM in organization');

      // Find the 'pieces' UoM in this organization
      const { data: piecesUom, error: piecesError } = await supabase
        .from('units_of_measurement')
        .select('*')
        .eq('organization_id', organizationId)
        .eq('code', 'pcs')
        .single();

      if (piecesError) {
        console.error('Error finding pieces UoM:', piecesError);

        // If no 'pcs' UoM exists, create one
        try {
          console.log('No pieces UoM found, creating one');
          const { data: newPcsUom, error: createPcsError } = await supabase
            .from('units_of_measurement')
            .insert({
              name: 'Pieces',
              code: 'pcs',
              organization_id: organizationId,
              is_base_unit: true
            })
            .select('*')
            .single();

          if (createPcsError) {
            console.error('Error creating pieces UoM:', createPcsError);
          } else {
            console.log('Created pieces UoM:', newPcsUom);

            // Now create a default UoM for this product using the new 'pcs' UoM
            try {
              console.log('Creating default UoM for product with new pieces UoM');
              const { data: newUom, error: createError } = await supabase
                .from('product_uoms')
                .insert({
                  product_id: productId,
                  uom_id: newPcsUom.id,
                  organization_id: organizationId,
                  conversion_factor: 1,
                  is_default: true,
                  is_purchasing_unit: true,
                  is_selling_unit: true
                })
                .select(`
                  *,
                  uom:uom_id (*)
                `)
                .single();

              if (createError) {
                console.error('Error creating default UoM with new pieces UoM:', createError);
              } else {
                console.log('Created default UoM with new pieces UoM:', newUom);
                // Return the newly created UoM
                return {
                  productUoms: [newUom] as (ProductUom & { uom: UnitOfMeasurement })[],
                };
              }
            } catch (err) {
              console.error('Exception creating default UoM with new pieces UoM:', err);
            }
          }
        } catch (err) {
          console.error('Exception creating pieces UoM:', err);
        }
      } else if (piecesUom) {
        console.log('Found pieces UoM:', piecesUom);

        // Try to create a default UoM for this product
        try {
          console.log('Creating default UoM for product');
          const { data: newUom, error: createError } = await supabase
            .from('product_uoms')
            .insert({
              product_id: productId,
              uom_id: piecesUom.id,
              organization_id: organizationId,
              conversion_factor: 1,
              is_default: true,
              is_purchasing_unit: true,
              is_selling_unit: true
            })
            .select(`
              *,
              uom:uom_id (*)
            `)
            .single();

          if (createError) {
            console.error('Error creating default UoM:', createError);
          } else {
            console.log('Created default UoM:', newUom);
            // Return the newly created UoM
            return {
              productUoms: [newUom] as (ProductUom & { uom: UnitOfMeasurement })[],
            };
          }
        } catch (err) {
          console.error('Exception creating default UoM:', err);
        }
      }
    }

    return {
      productUoms: data as (ProductUom & { uom: UnitOfMeasurement })[],
    };
  } catch (error: any) {
    console.error('Error in getProductUoms:', error);
    return {
      productUoms: [],
      error: error.message,
    };
  }
};

/**
 * Get the default UoM for a product
 */
export const getDefaultProductUom = async (
  productId: string,
  organizationId?: string
): Promise<{
  productUom?: ProductUom & { uom: UnitOfMeasurement };
  error?: string;
}> => {
  try {
    // If organizationId is not provided, we need it to query the product
    if (!organizationId) {
      return {
        error: 'Organization ID is required',
      };
    }

    const { data, error } = await supabase
      .from('product_uoms')
      .select(`
        *,
        uom:uom_id (*)
      `)
      .eq('product_id', productId)
      .eq('organization_id', organizationId)
      .eq('is_default', true)
      .single();

    if (error) {
      console.error('Error fetching default product UoM:', error);
      return {
        error: error.message,
      };
    }

    return {
      productUom: data as ProductUom & { uom: UnitOfMeasurement },
    };
  } catch (error: any) {
    console.error('Error in getDefaultProductUom:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new product UoM
 */
export const createProductUom = async (
  productUom: Omit<ProductUom, 'id' | 'organization_id' | 'created_at' | 'updated_at'>,
  organizationId?: string
): Promise<{
  productUom?: ProductUom & { uom: UnitOfMeasurement };
  error?: string;
}> => {
  try {
    // If organizationId is not provided, we need it to create the product UoM
    if (!organizationId) {
      return {
        error: 'Organization ID is required',
      };
    }

    // Verify that the UoM belongs to the same organization
    const { data: uomData, error: uomError } = await supabase
      .from('units_of_measurement')
      .select('organization_id')
      .eq('id', productUom.uom_id)
      .single();

    if (uomError) {
      return {
        error: 'Unit of measurement not found',
      };
    }

    if (uomData.organization_id !== organizationId) {
      return {
        error: 'Unit of measurement must belong to the same organization as the product',
      };
    }

    const { data, error } = await supabase
      .from('product_uoms')
      .insert({
        ...productUom,
        organization_id: organizationId
      })
      .select(`
        *,
        uom:uom_id (*)
      `)
      .single();

    if (error) {
      console.error('Error creating product UoM:', error);
      return {
        error: error.message,
      };
    }

    return {
      productUom: data as ProductUom & { uom: UnitOfMeasurement },
    };
  } catch (error: any) {
    console.error('Error in createProductUom:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update an existing product UoM
 */
export const updateProductUom = async (
  productUomId: string,
  updates: Partial<Omit<ProductUom, 'id' | 'product_id' | 'uom_id' | 'organization_id' | 'created_at' | 'updated_at'>>,
  organizationId?: string
): Promise<{
  productUom?: ProductUom & { uom: UnitOfMeasurement };
  error?: string;
}> => {
  try {
    // If organizationId is provided, verify that the product UoM belongs to this organization
    if (organizationId) {
      const { data: uomData, error: uomError } = await supabase
        .from('product_uoms')
        .select('organization_id')
        .eq('id', productUomId)
        .single();

      if (uomError) {
        return {
          error: 'Product UoM not found',
        };
      }

      if (uomData.organization_id !== organizationId) {
        return {
          error: 'You do not have permission to update this product UoM',
        };
      }
    }

    const { data, error } = await supabase
      .from('product_uoms')
      .update(updates)
      .eq('id', productUomId)
      .select(`
        *,
        uom:uom_id (*)
      `)
      .single();

    if (error) {
      console.error('Error updating product UoM:', error);
      return {
        error: error.message,
      };
    }

    return {
      productUom: data as ProductUom & { uom: UnitOfMeasurement },
    };
  } catch (error: any) {
    console.error('Error in updateProductUom:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Delete a product UoM
 */
export const deleteProductUom = async (
  productUomId: string,
  organizationId?: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // If organizationId is provided, verify that the product UoM belongs to this organization
    if (organizationId) {
      const { data: uomData, error: uomError } = await supabase
        .from('product_uoms')
        .select('organization_id')
        .eq('id', productUomId)
        .single();

      if (uomError) {
        return {
          success: false,
          error: 'Product UoM not found',
        };
      }

      if (uomData.organization_id !== organizationId) {
        return {
          success: false,
          error: 'You do not have permission to delete this product UoM',
        };
      }
    }

    const { error } = await supabase
      .from('product_uoms')
      .delete()
      .eq('id', productUomId);

    if (error) {
      console.error('Error deleting product UoM:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteProductUom:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Convert a quantity from one UoM to another
 *
 * This function can be called in two ways:
 * 1. With productId, quantity, fromUomId, toUomId, organizationId
 * 2. With productId, fromUomId, toUomId, quantity, organizationId
 *
 * The second form is for backward compatibility.
 */
export const convertQuantity = async (
  productId: string,
  quantityOrFromUomId: number | string,
  fromUomIdOrToUomId: string,
  toUomIdOrQuantity?: string | number,
  organizationId?: string
): Promise<{
  convertedQuantity?: number;
  error?: string;
}> => {
  try {
    let quantity: number;
    let fromUomId: string;
    let toUomId: string;

    // Determine which parameter pattern is being used
    if (typeof quantityOrFromUomId === 'number') {
      // First pattern: productId, quantity, fromUomId, toUomId, organizationId
      quantity = quantityOrFromUomId;
      fromUomId = fromUomIdOrToUomId;
      toUomId = toUomIdOrQuantity as string;
    } else {
      // Second pattern: productId, fromUomId, toUomId, quantity, organizationId
      fromUomId = quantityOrFromUomId;
      toUomId = fromUomIdOrToUomId;
      quantity = toUomIdOrQuantity as number;
      organizationId = organizationId;
    }

    console.log('Converting quantity:', { productId, quantity, fromUomId, toUomId, organizationId });

    // If fromUomId and toUomId are the same, no conversion needed
    if (fromUomId === toUomId) {
      return { convertedQuantity: quantity };
    }

    // If organizationId is not provided, we need it to query conversion factors
    if (!organizationId) {
      return {
        error: 'Organization ID is required',
      };
    }

    // Get the conversion factors for both UoMs
    const { data, error } = await supabase
      .from('product_uoms')
      .select('uom_id, conversion_factor')
      .eq('product_id', productId)
      .eq('organization_id', organizationId)
      .in('uom_id', [fromUomId, toUomId]);

    if (error) {
      console.error('Error fetching conversion factors:', error);
      return {
        error: error.message,
      };
    }

    if (data.length !== 2) {
      console.error('Could not find conversion factors for both UoMs. Found:', data.length);
      return {
        error: 'Could not find conversion factors for both UoMs',
      };
    }

    // Find the conversion factors
    const fromUom = data.find(u => u.uom_id === fromUomId);
    const toUom = data.find(u => u.uom_id === toUomId);

    if (!fromUom || !toUom) {
      console.error('Missing UoM data:', { fromUom, toUom });
      return {
        error: 'Could not find conversion factors for both UoMs',
      };
    }

    console.log('Conversion factors:', {
      fromUom: fromUom.conversion_factor,
      toUom: toUom.conversion_factor
    });

    // Convert to base quantity, then to target quantity
    const baseQuantity = quantity * fromUom.conversion_factor;
    const convertedQuantity = baseQuantity / toUom.conversion_factor;

    console.log('Conversion result:', {
      quantity,
      baseQuantity,
      convertedQuantity
    });

    return {
      convertedQuantity,
    };
  } catch (error: any) {
    console.error('Error in convertQuantity:', error);
    return {
      error: error.message,
    };
  }
};
