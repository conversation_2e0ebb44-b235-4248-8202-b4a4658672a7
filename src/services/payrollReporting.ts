import { supabase } from '../lib/supabase';
import {
  PayrollPeriod,
  PayrollItem,
  PayrollReportOptions,
  PayrollSummary,
  PayrollPeriodStatus,
  PayrollItemStatus
} from '../types/payroll';
import { format } from 'date-fns';

/**
 * Get payroll summary report
 * @param options Report options
 * @returns Payroll summary data and error if any
 */
export const getPayrollSummaryReport = async (
  options: PayrollReportOptions
): Promise<{
  data?: PayrollSummary[];
  error?: string;
}> => {
  try {
    const { organizationId, startDate, endDate } = options;

    // Build query for payroll periods
    let query = supabase
      .from('payroll_periods')
      .select(`
        id,
        name,
        start_date,
        end_date,
        payment_date,
        status,
        is_thirteenth_month,
        payroll_items:payroll_items(
          id,
          employee_id,
          basic_pay,
          gross_pay,
          net_pay,
          total_allowances,
          total_deductions
        )
      `)
      .eq('organization_id', organizationId)
      .not('status', 'eq', PayrollPeriodStatus.DRAFT);

    // Apply date filters if provided
    if (startDate) {
      query = query.gte('start_date', startDate.toISOString().split('T')[0]);
    }

    if (endDate) {
      query = query.lte('end_date', endDate.toISOString().split('T')[0]);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    // Transform data into summary format
    const summaryData: PayrollSummary[] = data.map((period: any) => {
      const payrollItems = period.payroll_items || [];
      
      return {
        periodId: period.id,
        periodName: period.name,
        startDate: new Date(period.start_date),
        endDate: new Date(period.end_date),
        paymentDate: new Date(period.payment_date),
        status: period.status,
        isThirteenthMonth: period.is_thirteenth_month,
        totalEmployees: payrollItems.length,
        totalGrossPay: payrollItems.reduce((sum: number, item: any) => sum + Number(item.gross_pay), 0),
        totalNetPay: payrollItems.reduce((sum: number, item: any) => sum + Number(item.net_pay), 0),
        totalDeductions: payrollItems.reduce((sum: number, item: any) => sum + Number(item.total_deductions), 0),
        totalAllowances: payrollItems.reduce((sum: number, item: any) => sum + Number(item.total_allowances), 0)
      };
    });

    return { data: summaryData };
  } catch (err: any) {
    console.error('Error getting payroll summary report:', err);
    return { error: err.message };
  }
};

/**
 * Get employee earnings report
 * @param options Report options
 * @returns Employee earnings data and error if any
 */
export const getEmployeeEarningsReport = async (
  options: PayrollReportOptions
): Promise<{
  data?: any[];
  error?: string;
}> => {
  try {
    const { organizationId, startDate, endDate, employeeIds } = options;

    // Build query for payroll items
    let query = supabase
      .from('payroll_items')
      .select(`
        id,
        employee_id,
        basic_pay,
        gross_pay,
        net_pay,
        total_allowances,
        total_deductions,
        regular_pay,
        overtime_pay,
        rest_day_pay,
        holiday_pay,
        night_differential_pay,
        is_thirteenth_month,
        employee:employee_id(
          id,
          first_name,
          middle_name,
          last_name,
          employee_number
        ),
        payroll_period:payroll_period_id(
          id,
          name,
          start_date,
          end_date,
          payment_date
        )
      `)
      .eq('organization_id', organizationId);

    // Apply employee filter if provided
    if (employeeIds && employeeIds.length > 0) {
      query = query.in('employee_id', employeeIds);
    }

    // Apply date filters via payroll period if provided
    if (startDate || endDate) {
      const periodQuery = supabase
        .from('payroll_periods')
        .select('id')
        .eq('organization_id', organizationId);

      if (startDate) {
        periodQuery.gte('start_date', startDate.toISOString().split('T')[0]);
      }

      if (endDate) {
        periodQuery.lte('end_date', endDate.toISOString().split('T')[0]);
      }

      const { data: periodData, error: periodError } = await periodQuery;

      if (periodError) {
        throw new Error(periodError.message);
      }

      if (periodData.length > 0) {
        const periodIds = periodData.map((p: any) => p.id);
        query = query.in('payroll_period_id', periodIds);
      } else {
        // No periods match the date criteria
        return { data: [] };
      }
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return { data };
  } catch (err: any) {
    console.error('Error getting employee earnings report:', err);
    return { error: err.message };
  }
};

/**
 * Get government contributions report
 * @param options Report options
 * @returns Government contributions data and error if any
 */
export const getGovernmentContributionsReport = async (
  options: PayrollReportOptions
): Promise<{
  data?: any;
  error?: string;
}> => {
  try {
    const { organizationId, startDate, endDate, employeeIds } = options;

    // Build query for payroll items
    let query = supabase
      .from('payroll_items')
      .select(`
        id,
        employee_id,
        sss_contribution,
        philhealth_contribution,
        pagibig_contribution,
        withholding_tax,
        employee:employee_id(
          id,
          first_name,
          middle_name,
          last_name,
          employee_number,
          tin_number,
          sss_number,
          philhealth_number,
          pagibig_number
        ),
        payroll_period:payroll_period_id(
          id,
          name,
          start_date,
          end_date,
          payment_date
        )
      `)
      .eq('organization_id', organizationId)
      .not('is_thirteenth_month', 'eq', true); // Exclude 13th month pay

    // Apply employee filter if provided
    if (employeeIds && employeeIds.length > 0) {
      query = query.in('employee_id', employeeIds);
    }

    // Apply date filters via payroll period if provided
    if (startDate || endDate) {
      const periodQuery = supabase
        .from('payroll_periods')
        .select('id')
        .eq('organization_id', organizationId);

      if (startDate) {
        periodQuery.gte('start_date', startDate.toISOString().split('T')[0]);
      }

      if (endDate) {
        periodQuery.lte('end_date', endDate.toISOString().split('T')[0]);
      }

      const { data: periodData, error: periodError } = await periodQuery;

      if (periodError) {
        throw new Error(periodError.message);
      }

      if (periodData.length > 0) {
        const periodIds = periodData.map((p: any) => p.id);
        query = query.in('payroll_period_id', periodIds);
      } else {
        // No periods match the date criteria
        return { data: { items: [], summary: {} } };
      }
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    // Calculate summary totals
    const summary = {
      totalSSS: data.reduce((sum: number, item: any) => sum + Number(item.sss_contribution || 0), 0),
      totalPhilHealth: data.reduce((sum: number, item: any) => sum + Number(item.philhealth_contribution || 0), 0),
      totalPagibig: data.reduce((sum: number, item: any) => sum + Number(item.pagibig_contribution || 0), 0),
      totalWithholdingTax: data.reduce((sum: number, item: any) => sum + Number(item.withholding_tax || 0), 0),
      totalContributions: data.reduce((sum: number, item: any) => 
        sum + 
        Number(item.sss_contribution || 0) + 
        Number(item.philhealth_contribution || 0) + 
        Number(item.pagibig_contribution || 0), 0),
      employeeCount: new Set(data.map((item: any) => item.employee_id)).size
    };

    return { data: { items: data, summary } };
  } catch (err: any) {
    console.error('Error getting government contributions report:', err);
    return { error: err.message };
  }
};

/**
 * Get BIR withholding tax report (2316)
 * @param options Report options
 * @returns BIR withholding tax data and error if any
 */
export const getBIRWithholdingTaxReport = async (
  options: PayrollReportOptions
): Promise<{
  data?: any;
  error?: string;
}> => {
  try {
    const { organizationId, startDate, endDate, employeeIds } = options;
    
    // For BIR reports, we need to aggregate by employee for the entire year
    const year = startDate ? startDate.getFullYear() : new Date().getFullYear();
    const yearStart = new Date(year, 0, 1);
    const yearEnd = new Date(year, 11, 31);
    
    // Get all payroll items for the year
    const { data: employeeEarnings, error } = await getEmployeeEarningsReport({
      organizationId,
      startDate: yearStart,
      endDate: yearEnd,
      employeeIds
    });
    
    if (error) {
      throw new Error(error);
    }
    
    // Group by employee
    const employeeMap = new Map();
    
    employeeEarnings?.forEach((item: any) => {
      const employeeId = item.employee_id;
      
      if (!employeeMap.has(employeeId)) {
        employeeMap.set(employeeId, {
          employee: item.employee,
          totalBasicPay: 0,
          totalGrossPay: 0,
          totalNetPay: 0,
          totalWithholdingTax: 0,
          totalSSS: 0,
          totalPhilHealth: 0,
          totalPagibig: 0,
          totalDeductions: 0,
          totalTaxableIncome: 0,
          periods: []
        });
      }
      
      const employeeData = employeeMap.get(employeeId);
      
      employeeData.totalBasicPay += Number(item.basic_pay || 0);
      employeeData.totalGrossPay += Number(item.gross_pay || 0);
      employeeData.totalNetPay += Number(item.net_pay || 0);
      employeeData.totalWithholdingTax += Number(item.withholding_tax || 0);
      employeeData.totalSSS += Number(item.sss_contribution || 0);
      employeeData.totalPhilHealth += Number(item.philhealth_contribution || 0);
      employeeData.totalPagibig += Number(item.pagibig_contribution || 0);
      employeeData.totalDeductions += Number(item.total_deductions || 0);
      employeeData.totalTaxableIncome += Number(item.taxable_income || 0);
      
      employeeData.periods.push({
        periodId: item.payroll_period.id,
        periodName: item.payroll_period.name,
        startDate: item.payroll_period.start_date,
        endDate: item.payroll_period.end_date,
        basicPay: Number(item.basic_pay || 0),
        grossPay: Number(item.gross_pay || 0),
        netPay: Number(item.net_pay || 0),
        withholdingTax: Number(item.withholding_tax || 0)
      });
    });
    
    // Convert map to array
    const result = Array.from(employeeMap.values());
    
    return { data: result };
  } catch (err: any) {
    console.error('Error getting BIR withholding tax report:', err);
    return { error: err.message };
  }
};
