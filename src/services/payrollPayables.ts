import { supabase } from '../lib/supabase';
import { PayrollPeriodWithDetails, PayrollItemWithDetails, PayrollPeriodStatus } from '../types/payroll';
import { PayableSourceType, CreatePayableRequest } from '../types/payables.types';
import { createPayable } from './payables';

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export interface PayrollPayableOptions {
  createEmployeePayables: boolean;      // Individual salary payables
  createGovernmentPayables: boolean;    // SSS, PhilHealth, Pag-IBIG, BIR
  createThirdPartyPayables: boolean;    // Loans, insurance, etc.
  groupByEmployee: boolean;             // One payable per employee vs. bulk
  customDueDates?: {                    // Override default due dates
    salary?: string;
    government?: string;
    thirdParty?: string;
  };
}

export interface PayrollPayableResult {
  success: boolean;
  error?: string;
  payables?: any[];
  summary?: {
    employeePayables: number;
    governmentPayables: number;
    thirdPartyPayables: number;
    totalAmount: number;
  };
}

export interface PayrollPayableMapping {
  id?: string;
  organization_id: string;
  payroll_period_id: string;
  payroll_item_id?: string;
  payable_id: string;
  payable_type: 'employee_salary' | 'sss_contribution' | 'philhealth_contribution' | 'pagibig_contribution' | 'withholding_tax' | 'loan_deduction' | 'other';
  amount: number;
  created_by: string;
}

export interface GovernmentContributions {
  sss: { employee: number; employer: number; total: number };
  philhealth: { employee: number; employer: number; total: number };
  pagibig: { employee: number; employer: number; total: number };
  withholding_tax: number;
}

// =====================================================
// MAIN PAYROLL TO PAYABLES FUNCTION
// =====================================================

/**
 * Create payables from an approved payroll period
 */
export const createPayablesFromPayroll = async (
  organizationId: string,
  payrollPeriodId: string,
  userId: string,
  options: PayrollPayableOptions
): Promise<PayrollPayableResult> => {
  try {
    console.log(`🏗️ Creating payables from payroll period ${payrollPeriodId}`);

    // 1. Validate payroll period
    const payrollPeriod = await validatePayrollPeriod(organizationId, payrollPeriodId);
    if (!payrollPeriod.success) {
      return payrollPeriod;
    }

    // 2. Check if payables already created
    if (payrollPeriod.period?.payables_created) {
      return { success: false, error: 'Payables already created for this payroll period' };
    }

    // 2.1. Double-check by looking for existing payables from this period
    const existingPayables = await checkExistingPayrollPayables(organizationId, payrollPeriodId);
    if (existingPayables.length > 0) {
      console.warn(`Found ${existingPayables.length} existing payables for this payroll period`);
      return { success: false, error: `Found ${existingPayables.length} existing payables for this payroll period. Please check the payables list.` };
    }

    // 3. Get payroll items with details
    const payrollItems = await getPayrollItemsWithDetails(organizationId, payrollPeriodId);
    if (!payrollItems.success || !payrollItems.items) {
      return { success: false, error: payrollItems.error || 'No payroll items found' };
    }

    console.log(`📊 Found ${payrollItems.items.length} payroll items to process`);

    // 4. Ensure government agencies exist as suppliers
    await ensureGovernmentAgencies(organizationId);

    const allPayables: any[] = [];
    let employeePayablesCount = 0;
    let governmentPayablesCount = 0;
    let thirdPartyPayablesCount = 0;

    // 5. Create employee salary payables
    if (options.createEmployeePayables) {
      console.log('💰 Creating employee salary payables...');
      const employeePayables = await createEmployeeSalaryPayables(
        organizationId,
        payrollPeriod.period!,
        payrollItems.items,
        userId,
        options
      );

      if (employeePayables.success && employeePayables.payables) {
        allPayables.push(...employeePayables.payables);
        employeePayablesCount = employeePayables.payables.length;
      }
    }

    // 6. Create government remittance payables
    if (options.createGovernmentPayables) {
      console.log('🏛️ Creating government remittance payables...');
      const governmentPayables = await createGovernmentRemittancePayables(
        organizationId,
        payrollPeriod.period!,
        payrollItems.items,
        userId,
        options
      );

      if (governmentPayables.success && governmentPayables.payables) {
        allPayables.push(...governmentPayables.payables);
        governmentPayablesCount = governmentPayables.payables.length;
      }
    }

    // 7. Create third-party payables (future implementation)
    if (options.createThirdPartyPayables) {
      console.log('🏢 Creating third-party payables...');
      // TODO: Implement third-party payables for loans, insurance, etc.
    }

    // 8. Mark payroll period as having payables created
    await markPayrollPayablesCreated(payrollPeriodId, userId);

    const totalAmount = allPayables.reduce((sum, payable) => sum + Number(payable.amount), 0);

    console.log(`✅ Successfully created ${allPayables.length} payables totaling ₱${totalAmount.toFixed(2)}`);

    return {
      success: true,
      payables: allPayables,
      summary: {
        employeePayables: employeePayablesCount,
        governmentPayables: governmentPayablesCount,
        thirdPartyPayables: thirdPartyPayablesCount,
        totalAmount
      }
    };

  } catch (error: any) {
    console.error('Error in createPayablesFromPayroll:', error);
    return { success: false, error: error.message };
  }
};

// =====================================================
// VALIDATION FUNCTIONS
// =====================================================

/**
 * Validate that payroll period exists and is approved
 */
const validatePayrollPeriod = async (
  organizationId: string,
  payrollPeriodId: string
): Promise<{ success: boolean; period?: PayrollPeriodWithDetails; error?: string }> => {
  try {
    const { data: period, error } = await supabase
      .from('payroll_periods')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('id', payrollPeriodId)
      .single();

    if (error || !period) {
      return { success: false, error: 'Payroll period not found' };
    }

    if (period.status !== PayrollPeriodStatus.APPROVED) {
      return { success: false, error: 'Payroll period must be approved before creating payables' };
    }

    return { success: true, period };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

/**
 * Get payroll items with employee details
 */
const getPayrollItemsWithDetails = async (
  organizationId: string,
  payrollPeriodId: string
): Promise<{ success: boolean; items?: PayrollItemWithDetails[]; error?: string }> => {
  try {
    const { data: items, error } = await supabase
      .from('payroll_items')
      .select(`
        *,
        employee:employees(
          id, first_name, last_name, employee_number, email
        )
      `)
      .eq('organization_id', organizationId)
      .eq('payroll_period_id', payrollPeriodId);

    if (error) {
      return { success: false, error: error.message };
    }

    if (!items || items.length === 0) {
      return { success: false, error: 'No payroll items found for this period' };
    }

    return { success: true, items: items as PayrollItemWithDetails[] };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

/**
 * Ensure government agencies exist as suppliers
 */
const ensureGovernmentAgencies = async (organizationId: string): Promise<void> => {
  try {
    console.log('🏛️ Ensuring government agencies exist as suppliers...');

    const { error } = await supabase
      .rpc('add_government_agencies_as_suppliers', {
        p_organization_id: organizationId
      });

    if (error) {
      console.warn('Warning: Could not add government agencies:', error.message);
    } else {
      console.log('✅ Government agencies ensured as suppliers');
    }
  } catch (error: any) {
    console.warn('Warning: Could not add government agencies:', error.message);
  }
};

// =====================================================
// EMPLOYEE SALARY PAYABLES
// =====================================================

/**
 * Create individual salary payables for employees
 */
const createEmployeeSalaryPayables = async (
  organizationId: string,
  payrollPeriod: PayrollPeriodWithDetails,
  payrollItems: PayrollItemWithDetails[],
  userId: string,
  options: PayrollPayableOptions
): Promise<{ success: boolean; payables?: any[]; error?: string }> => {
  try {
    const payables: any[] = [];
    const mappings: PayrollPayableMapping[] = [];

    for (const item of payrollItems) {
      // Skip if net pay is zero or negative
      if (Number(item.net_pay) <= 0) {
        console.log(`⏭️ Skipping ${item.employee?.first_name} ${item.employee?.last_name} - zero/negative net pay`);
        continue;
      }

      // Create payable for employee salary
      // Use payroll_item_id as source_id to ensure uniqueness
      const payableData: CreatePayableRequest = {
        source_type: PayableSourceType.PAYROLL,
        source_id: item.id, // Use individual payroll item ID instead of period ID
        employee_id: item.employee_id,
        reference_number: `SAL-${payrollPeriod.name.replace(/\s+/g, '-')}-${item.employee?.employee_number}`,
        invoice_date: payrollPeriod.end_date,
        due_date: options.customDueDates?.salary || payrollPeriod.payment_date,
        amount: Number(item.net_pay),
        vat_amount: 0, // Salaries are not subject to VAT
        withholding_tax_rate: 0, // Already deducted in payroll
        withholding_tax_amount: 0,
        currency: 'PHP',
        category: 'salary',
        notes: `Salary payable for ${item.employee?.first_name} ${item.employee?.last_name} - ${payrollPeriod.name}`
      };

      const result = await createPayable(organizationId, payableData, userId);

      if (result.success && result.payable) {
        payables.push(result.payable);

        // Create mapping record
        mappings.push({
          organization_id: organizationId,
          payroll_period_id: payrollPeriod.id,
          payroll_item_id: item.id,
          payable_id: result.payable.id,
          payable_type: 'employee_salary',
          amount: Number(item.net_pay),
          created_by: userId
        });

        console.log(`✅ Created salary payable for ${item.employee?.first_name} ${item.employee?.last_name}: ₱${Number(item.net_pay).toFixed(2)}`);
      } else {
        console.error(`❌ Failed to create salary payable for ${item.employee?.first_name} ${item.employee?.last_name}:`, result.error);
      }
    }

    // Bulk insert mappings
    if (mappings.length > 0) {
      await createPayrollPayableMappings(mappings);
    }

    return { success: true, payables };
  } catch (error: any) {
    console.error('Error creating employee salary payables:', error);
    return { success: false, error: error.message };
  }
};

// =====================================================
// GOVERNMENT REMITTANCE PAYABLES
// =====================================================

/**
 * Create government remittance payables (SSS, PhilHealth, Pag-IBIG, BIR)
 */
const createGovernmentRemittancePayables = async (
  organizationId: string,
  payrollPeriod: PayrollPeriodWithDetails,
  payrollItems: PayrollItemWithDetails[],
  userId: string,
  options: PayrollPayableOptions
): Promise<{ success: boolean; payables?: any[]; error?: string }> => {
  try {
    const payables: any[] = [];
    const mappings: PayrollPayableMapping[] = [];

    // Calculate total contributions
    const contributions = calculateGovernmentContributions(payrollItems);

    // Get government agency suppliers
    const agencies = await getGovernmentAgencySuppliers(organizationId);

    // Create SSS payable
    if (contributions.sss.total > 0) {
      const sssSupplier = agencies.find(a => a.name.includes('SSS'));
      if (sssSupplier) {
        const sssPayable = await createGovernmentPayable({
          organizationId,
          payrollPeriod,
          supplierId: sssSupplier.id,
          agencyName: 'SSS',
          amount: contributions.sss.total,
          payableType: 'sss_contribution',
          userId,
          options
        });

        if (sssPayable.success && sssPayable.payable) {
          payables.push(sssPayable.payable);
          mappings.push({
            organization_id: organizationId,
            payroll_period_id: payrollPeriod.id,
            payable_id: sssPayable.payable.id,
            payable_type: 'sss_contribution',
            amount: contributions.sss.total,
            created_by: userId
          });
        }
      }
    }

    // Create PhilHealth payable
    if (contributions.philhealth.total > 0) {
      const philhealthSupplier = agencies.find(a => a.name.includes('PhilHealth'));
      if (philhealthSupplier) {
        const philhealthPayable = await createGovernmentPayable({
          organizationId,
          payrollPeriod,
          supplierId: philhealthSupplier.id,
          agencyName: 'PhilHealth',
          amount: contributions.philhealth.total,
          payableType: 'philhealth_contribution',
          userId,
          options
        });

        if (philhealthPayable.success && philhealthPayable.payable) {
          payables.push(philhealthPayable.payable);
          mappings.push({
            organization_id: organizationId,
            payroll_period_id: payrollPeriod.id,
            payable_id: philhealthPayable.payable.id,
            payable_type: 'philhealth_contribution',
            amount: contributions.philhealth.total,
            created_by: userId
          });
        }
      }
    }

    // Create Pag-IBIG payable
    if (contributions.pagibig.total > 0) {
      const pagibigSupplier = agencies.find(a => a.name.includes('Pag-IBIG'));
      if (pagibigSupplier) {
        const pagibigPayable = await createGovernmentPayable({
          organizationId,
          payrollPeriod,
          supplierId: pagibigSupplier.id,
          agencyName: 'Pag-IBIG',
          amount: contributions.pagibig.total,
          payableType: 'pagibig_contribution',
          userId,
          options
        });

        if (pagibigPayable.success && pagibigPayable.payable) {
          payables.push(pagibigPayable.payable);
          mappings.push({
            organization_id: organizationId,
            payroll_period_id: payrollPeriod.id,
            payable_id: pagibigPayable.payable.id,
            payable_type: 'pagibig_contribution',
            amount: contributions.pagibig.total,
            created_by: userId
          });
        }
      }
    }

    // Create BIR withholding tax payable
    if (contributions.withholding_tax > 0) {
      const birSupplier = agencies.find(a => a.name.includes('BIR'));
      if (birSupplier) {
        const birPayable = await createGovernmentPayable({
          organizationId,
          payrollPeriod,
          supplierId: birSupplier.id,
          agencyName: 'BIR',
          amount: contributions.withholding_tax,
          payableType: 'withholding_tax',
          userId,
          options
        });

        if (birPayable.success && birPayable.payable) {
          payables.push(birPayable.payable);
          mappings.push({
            organization_id: organizationId,
            payroll_period_id: payrollPeriod.id,
            payable_id: birPayable.payable.id,
            payable_type: 'withholding_tax',
            amount: contributions.withholding_tax,
            created_by: userId
          });
        }
      }
    }

    // Bulk insert mappings
    if (mappings.length > 0) {
      await createPayrollPayableMappings(mappings);
    }

    console.log(`✅ Created ${payables.length} government remittance payables`);
    return { success: true, payables };

  } catch (error: any) {
    console.error('Error creating government remittance payables:', error);
    return { success: false, error: error.message };
  }
};

// =====================================================
// HELPER FUNCTIONS
// =====================================================

/**
 * Calculate total government contributions from payroll items
 * Use exactly what's shown in the payroll calculation details
 */
const calculateGovernmentContributions = (payrollItems: PayrollItemWithDetails[]): GovernmentContributions => {
  const contributions: GovernmentContributions = {
    sss: { employee: 0, employer: 0, total: 0 },
    philhealth: { employee: 0, employer: 0, total: 0 },
    pagibig: { employee: 0, employer: 0, total: 0 },
    withholding_tax: 0
  };

  payrollItems.forEach(item => {
    // Use exactly what's in the payroll items (employee contributions only)
    const sssEmployee = Number(item.sss_contribution) || 0;
    const philhealthEmployee = Number(item.philhealth_contribution) || 0;
    const pagibigEmployee = Number(item.pagibig_contribution) || 0;
    const withholdingTax = Number(item.withholding_tax) || 0;

    contributions.sss.employee += sssEmployee;
    contributions.philhealth.employee += philhealthEmployee;
    contributions.pagibig.employee += pagibigEmployee;
    contributions.withholding_tax += withholdingTax;

    // For payables, we only remit what's shown in calculation details
    // which is the employee contribution amounts
    contributions.sss.employer += 0; // No separate employer contribution in payables
    contributions.philhealth.employer += 0;
    contributions.pagibig.employer += 0;
  });

  // For payables, total = employee contribution (what's actually shown in calculation details)
  contributions.sss.total = contributions.sss.employee;
  contributions.philhealth.total = contributions.philhealth.employee;
  contributions.pagibig.total = contributions.pagibig.employee;

  // Debug logging
  console.log('Government Contributions for Payables (matching calculation details):', {
    sss: {
      employee: contributions.sss.employee,
      total: contributions.sss.total
    },
    philhealth: {
      employee: contributions.philhealth.employee,
      total: contributions.philhealth.total
    },
    pagibig: {
      employee: contributions.pagibig.employee,
      total: contributions.pagibig.total
    },
    withholdingTax: contributions.withholding_tax
  });

  return contributions;
};



/**
 * Get government agency suppliers
 */
const getGovernmentAgencySuppliers = async (organizationId: string): Promise<any[]> => {
  try {
    const { data: suppliers, error } = await supabase
      .from('suppliers')
      .select('id, name, supplier_type, payment_terms_days')
      .eq('organization_id', organizationId)
      .eq('supplier_type', 'government');

    if (error) {
      console.error('Error fetching government suppliers:', error);
      return [];
    }

    return suppliers || [];
  } catch (error: any) {
    console.error('Error fetching government suppliers:', error);
    return [];
  }
};

/**
 * Create a government payable
 */
const createGovernmentPayable = async (params: {
  organizationId: string;
  payrollPeriod: PayrollPeriodWithDetails;
  supplierId: string;
  agencyName: string;
  amount: number;
  payableType: string;
  userId: string;
  options: PayrollPayableOptions;
}): Promise<{ success: boolean; payable?: any; error?: string }> => {
  try {
    const { organizationId, payrollPeriod, supplierId, agencyName, amount, payableType, userId, options } = params;

    // Calculate due date based on agency type
    const dueDate = calculateGovernmentRemittanceDueDate(payrollPeriod.end_date, agencyName);

    // Generate unique source ID for government payables
    // Format: {payroll_period_id}-{agency_type}
    const uniqueSourceId = `${payrollPeriod.id}-${payableType}`;

    const payableData: CreatePayableRequest = {
      source_type: PayableSourceType.PAYROLL,
      source_id: uniqueSourceId, // Use unique source ID to avoid constraint violation
      supplier_id: supplierId,
      reference_number: `${agencyName.toUpperCase()}-${payrollPeriod.name.replace(/\s+/g, '-')}`,
      invoice_date: payrollPeriod.end_date,
      due_date: options.customDueDates?.government || dueDate,
      amount: amount,
      vat_amount: 0, // Government remittances are not subject to VAT
      withholding_tax_rate: 0,
      withholding_tax_amount: 0,
      currency: 'PHP',
      category: 'government_remittance',
      notes: `${agencyName} remittance for payroll period: ${payrollPeriod.name}`
    };

    const result = await createPayable(organizationId, payableData, userId);

    if (result.success) {
      console.log(`✅ Created ${agencyName} payable: ₱${amount.toFixed(2)}`);
    } else {
      console.error(`❌ Failed to create ${agencyName} payable:`, result.error);
    }

    return result;
  } catch (error: any) {
    console.error(`Error creating ${params.agencyName} payable:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * Calculate government remittance due date
 */
const calculateGovernmentRemittanceDueDate = (payrollEndDate: string, agencyName: string): string => {
  const endDate = new Date(payrollEndDate);
  const nextMonth = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 1);

  // BIR withholding tax is due by 15th of following month
  if (agencyName.includes('BIR')) {
    return new Date(nextMonth.getFullYear(), nextMonth.getMonth(), 15).toISOString().split('T')[0];
  }

  // SSS, PhilHealth, Pag-IBIG are due by 30th of following month
  return new Date(nextMonth.getFullYear(), nextMonth.getMonth(), 30).toISOString().split('T')[0];
};

/**
 * Create payroll payable mappings
 */
const createPayrollPayableMappings = async (mappings: PayrollPayableMapping[]): Promise<void> => {
  try {
    const { error } = await supabase
      .from('payroll_payable_mappings')
      .insert(mappings);

    if (error) {
      console.error('Error creating payroll payable mappings:', error);
    } else {
      console.log(`✅ Created ${mappings.length} payroll payable mappings`);
    }
  } catch (error: any) {
    console.error('Error creating payroll payable mappings:', error);
  }
};

/**
 * Mark payroll period as having payables created
 */
const markPayrollPayablesCreated = async (payrollPeriodId: string, userId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('payroll_periods')
      .update({
        payables_created: true,
        payables_created_at: new Date().toISOString(),
        payables_created_by: userId
      })
      .eq('id', payrollPeriodId);

    if (error) {
      console.error('Error marking payroll payables created:', error);
    } else {
      console.log('✅ Marked payroll period as having payables created');
    }
  } catch (error: any) {
    console.error('Error marking payroll payables created:', error);
  }
};

// =====================================================
// QUERY FUNCTIONS
// =====================================================

/**
 * Check if payables have been created for a payroll period
 */
export const hasPayrollPayablesBeenCreated = async (
  organizationId: string,
  payrollPeriodId: string
): Promise<boolean> => {
  try {
    const { data: period, error } = await supabase
      .from('payroll_periods')
      .select('payables_created')
      .eq('organization_id', organizationId)
      .eq('id', payrollPeriodId)
      .single();

    if (error || !period) {
      return false;
    }

    return period.payables_created || false;
  } catch (error: any) {
    console.error('Error checking payroll payables status:', error);
    return false;
  }
};

/**
 * Get payroll payables summary for a period
 */
export const getPayrollPayablesSummary = async (
  organizationId: string,
  payrollPeriodId: string
): Promise<{ success: boolean; summary?: any[]; error?: string }> => {
  try {
    const { data: summary, error } = await supabase
      .rpc('get_payroll_payables_summary', {
        p_organization_id: organizationId,
        p_payroll_period_id: payrollPeriodId
      });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, summary: summary || [] };
  } catch (error: any) {
    console.error('Error getting payroll payables summary:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Check for existing payables from a payroll period
 */
const checkExistingPayrollPayables = async (
  organizationId: string,
  payrollPeriodId: string
): Promise<any[]> => {
  try {
    // Check for existing payables using the mapping table instead
    // This is more reliable than trying to parse source_id
    const { data: mappings, error } = await supabase
      .from('payroll_payable_mappings')
      .select(`
        payable_id,
        payables!inner(id, source_id, reference_number, category)
      `)
      .eq('organization_id', organizationId)
      .eq('payroll_period_id', payrollPeriodId);

    if (error) {
      console.error('Error checking existing payables via mappings:', error);
      return [];
    }

    // Extract payables from the mappings
    const payables = mappings?.map(mapping => mapping.payables).filter(Boolean) || [];
    return payables;
  } catch (error: any) {
    console.error('Error checking existing payables:', error);
    return [];
  }
};
