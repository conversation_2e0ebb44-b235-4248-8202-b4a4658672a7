import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';

export type EmploymentType = Database['public']['Tables']['employment_types']['Row'];

/**
 * Get all employment types for an organization
 */
export const getEmploymentTypes = async (
  organizationId: string,
  options?: {
    searchQuery?: string;
    isActive?: boolean;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  },
): Promise<{
  employmentTypes: EmploymentType[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('employment_types')
      .select('*', { count: 'exact' })
      .eq('organization_id', organizationId);

    // Apply filters
    if (options?.isActive !== undefined) {
      query = query.eq('is_active', options.isActive);
    }

    if (options?.searchQuery) {
      query = query.ilike('name', `%${options.searchQuery}%`);
    }

    // Apply sorting
    if (options?.sortBy) {
      query = query.order(options.sortBy, { ascending: options.sortOrder === 'asc' });
    } else {
      query = query.order('name', { ascending: true });
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching employment types:', error);
      return {
        employmentTypes: [],
        count: 0,
        error: error.message,
      };
    }

    return {
      employmentTypes: data as EmploymentType[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error in getEmploymentTypes:', error);
    return {
      employmentTypes: [],
      count: 0,
      error: error.message,
    };
  }
};

/**
 * Get a single employment type by ID
 */
export const getEmploymentTypeById = async (
  organizationId: string,
  employmentTypeId: string,
): Promise<{
  employmentType?: EmploymentType;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('employment_types')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('id', employmentTypeId)
      .single();

    if (error) {
      console.error('Error fetching employment type:', error);
      return {
        error: error.message,
      };
    }

    return {
      employmentType: data as EmploymentType,
    };
  } catch (error: any) {
    console.error('Error in getEmploymentTypeById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new employment type
 */
export const createEmploymentType = async (
  organizationId: string,
  employmentType: Omit<EmploymentType, 'id' | 'organization_id' | 'created_at' | 'updated_at'>,
): Promise<{
  employmentType?: EmploymentType;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('employment_types')
      .insert({
        ...employmentType,
        organization_id: organizationId,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating employment type:', error);
      return {
        error: error.message,
      };
    }

    return {
      employmentType: data as EmploymentType,
    };
  } catch (error: any) {
    console.error('Error in createEmploymentType:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update an existing employment type
 */
export const updateEmploymentType = async (
  organizationId: string,
  employmentTypeId: string,
  updates: Partial<Omit<EmploymentType, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>,
): Promise<{
  employmentType?: EmploymentType;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('employment_types')
      .update(updates)
      .eq('organization_id', organizationId)
      .eq('id', employmentTypeId)
      .select()
      .single();

    if (error) {
      console.error('Error updating employment type:', error);
      return {
        error: error.message,
      };
    }

    return {
      employmentType: data as EmploymentType,
    };
  } catch (error: any) {
    console.error('Error in updateEmploymentType:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Delete an employment type
 */
export const deleteEmploymentType = async (
  organizationId: string,
  employmentTypeId: string,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('employment_types')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', employmentTypeId);

    if (error) {
      console.error('Error deleting employment type:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteEmploymentType:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
