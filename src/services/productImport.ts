/**
 * Product Import Service
 * Handles bulk import of products from CSV files
 */

import { supabase } from '../lib/supabase';
import { parseAndValidateCSV, CSVValidationRule, ParsedCSVRow } from '../utils/csvParser';
import { createProduct } from './product';

export interface ProductImportRow {
  name: string;
  sku?: string;
  barcode?: string;
  description?: string;
  category?: string;
  unit_price: number;
  cost_price?: number;
  stock_quantity?: number;
  min_stock_level?: number;
  is_active?: boolean;
  tags?: string;
}

export interface ImportResult {
  success: boolean;
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: string[];
  warnings: string[];
  createdProducts: any[];
}

export interface ImportPreview {
  isValid: boolean;
  headers: string[];
  sampleData: ParsedCSVRow[];
  errors: string[];
  warnings: string[];
  totalRows: number;
}

/**
 * Product import validation rules
 */
export const PRODUCT_IMPORT_RULES: CSVValidationRule[] = [
  {
    field: 'name',
    required: true,
    type: 'string',
    customValidator: (value) => {
      if (typeof value === 'string' && value.length > 255) {
        return 'Product name must be 255 characters or less';
      }
      return null;
    }
  },
  {
    field: 'sku',
    type: 'string',
    customValidator: (value) => {
      if (value && typeof value === 'string') {
        if (value.length > 50) {
          return 'SKU must be 50 characters or less';
        }
        return validateSKU(value);
      }
      return null;
    }
  },
  {
    field: 'barcode',
    type: 'string',
    customValidator: (value) => {
      if (value && typeof value === 'string') {
        if (value.length > 50) {
          return 'Barcode must be 50 characters or less';
        }
        return validateBarcode(value);
      }
      return null;
    }
  },
  {
    field: 'unit_price',
    required: true,
    type: 'number',
    min: 0
  },
  {
    field: 'cost_price',
    type: 'number',
    min: 0
  },
  {
    field: 'stock_quantity',
    type: 'number',
    min: 0
  },
  {
    field: 'min_stock_level',
    type: 'number',
    min: 0
  },

  {
    field: 'is_active',
    type: 'boolean'
  }
];

/**
 * Expected CSV headers for product import
 */
export const PRODUCT_IMPORT_HEADERS = [
  'name',
  'sku',
  'barcode',
  'description',
  'category',
  'unit_price',
  'cost_price',
  'stock_quantity',
  'min_stock_level',
  'is_active',
  'tags'
];

/**
 * Preview CSV file before import
 */
export const previewProductImport = (csvText: string): ImportPreview => {
  const result = parseAndValidateCSV(csvText, PRODUCT_IMPORT_RULES);
  
  return {
    isValid: result.success,
    headers: result.headers,
    sampleData: result.data.slice(0, 5), // Show first 5 rows as preview
    errors: result.errors,
    warnings: result.warnings,
    totalRows: result.data.length
  };
};

/**
 * Get or create category by name
 */
const getOrCreateCategory = async (organizationId: string, categoryName: string, userId: string) => {
  if (!categoryName || categoryName.trim() === '') {
    return null;
  }

  const trimmedName = categoryName.trim();
  console.log(`Looking for category: "${trimmedName}" in organization: ${organizationId}`);

  try {
    // First, try to find existing category
    const { data: existingCategory, error: findError } = await supabase
      .from('categories')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('name', trimmedName)
      .maybeSingle(); // Use maybeSingle instead of single to avoid error when not found

    if (findError) {
      console.error('Error finding category:', findError);
      return null;
    }

    if (existingCategory) {
      console.log(`Found existing category with ID: ${existingCategory.id}`);
      return existingCategory.id;
    }

    console.log(`Category not found, creating new category: "${trimmedName}"`);

    // Create new category
    const { data: newCategory, error: createError } = await supabase
      .from('categories')
      .insert({
        organization_id: organizationId,
        name: trimmedName,
        description: `Auto-created during product import`,
        created_by: userId
      })
      .select('id')
      .single();

    if (createError) {
      console.error('Error creating category:', createError);
      return null;
    }

    console.log(`Created new category with ID: ${newCategory.id}`);
    return newCategory.id;
  } catch (error) {
    console.error('Unexpected error in getOrCreateCategory:', error);
    return null;
  }
};

/**
 * Check for duplicate SKUs and barcodes
 */
const checkDuplicates = async (
  organizationId: string,
  products: ProductImportRow[]
): Promise<{ skuDuplicates: string[]; barcodeDuplicates: string[] }> => {
  const skus = products.filter(p => p.sku).map(p => p.sku!);
  const barcodes = products.filter(p => p.barcode).map(p => p.barcode!);

  const skuDuplicates: string[] = [];
  const barcodeDuplicates: string[] = [];

  if (skus.length > 0) {
    const { data: existingSkus } = await supabase
      .from('products')
      .select('sku')
      .eq('organization_id', organizationId)
      .in('sku', skus);

    if (existingSkus) {
      skuDuplicates.push(...existingSkus.map(p => p.sku));
    }
  }

  if (barcodes.length > 0) {
    const { data: existingBarcodes } = await supabase
      .from('products')
      .select('barcode')
      .eq('organization_id', organizationId)
      .in('barcode', barcodes);

    if (existingBarcodes) {
      barcodeDuplicates.push(...existingBarcodes.map(p => p.barcode));
    }
  }

  return { skuDuplicates, barcodeDuplicates };
};

/**
 * Import products from CSV
 */
export const importProducts = async (
  organizationId: string,
  csvText: string,
  userId: string,
  skipDuplicates: boolean = true
): Promise<ImportResult> => {
  const result = parseAndValidateCSV(csvText, PRODUCT_IMPORT_RULES);
  
  if (!result.success) {
    return {
      success: false,
      totalRows: result.data.length,
      successCount: 0,
      errorCount: result.data.length,
      errors: result.errors,
      warnings: result.warnings,
      createdProducts: []
    };
  }

  const products = result.data as ProductImportRow[];
  const errors: string[] = [...result.errors];
  const warnings: string[] = [...result.warnings];
  const createdProducts: any[] = [];

  // Check for duplicates
  const { skuDuplicates, barcodeDuplicates } = await checkDuplicates(organizationId, products);

  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < products.length; i++) {
    const product = products[i];
    const rowNum = i + 2; // +2 because row 1 is headers and we're 0-indexed

    try {
      console.log(`Processing row ${rowNum}:`, product);

      // Check for duplicate SKU
      if (product.sku && skuDuplicates.includes(product.sku)) {
        if (skipDuplicates) {
          warnings.push(`Row ${rowNum}: Skipped - SKU '${product.sku}' already exists`);
          continue;
        } else {
          errors.push(`Row ${rowNum}: SKU '${product.sku}' already exists`);
          errorCount++;
          continue;
        }
      }

      // Check for duplicate barcode
      if (product.barcode && barcodeDuplicates.includes(product.barcode)) {
        if (skipDuplicates) {
          warnings.push(`Row ${rowNum}: Skipped - Barcode '${product.barcode}' already exists`);
          continue;
        } else {
          errors.push(`Row ${rowNum}: Barcode '${product.barcode}' already exists`);
          errorCount++;
          continue;
        }
      }

      // Get or create category
      let categoryId = null;
      if (product.category && product.category.trim()) {
        console.log(`Creating/finding category: ${product.category}`);
        categoryId = await getOrCreateCategory(organizationId, product.category, userId);
        console.log(`Category ID: ${categoryId}`);
      }

      // Prepare product data - only include fields that exist in the database
      const productData = {
        name: product.name,
        sku: product.sku || null,
        barcode: product.barcode || null,
        description: product.description || null,
        category_id: categoryId,
        unit_price: Number(product.unit_price),
        cost_price: product.cost_price ? Number(product.cost_price) : null,
        stock_quantity: product.stock_quantity ? Number(product.stock_quantity) : null,
        min_stock_level: product.min_stock_level ? Number(product.min_stock_level) : null,
        is_active: product.is_active !== undefined ? product.is_active : true
      };

      console.log(`Creating product with data:`, productData);

      // Create product
      const createResult = await createProduct(organizationId, productData);

      console.log(`Create result:`, createResult);

      if (createResult.product && !createResult.error) {
        createdProducts.push(createResult.product);
        successCount++;
        console.log(`Successfully created product: ${createResult.product.name}`);

        // Handle tags if provided
        if (product.tags && product.tags.trim()) {
          const tagNames = product.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
          // Note: Tag handling would require additional implementation
          if (tagNames.length > 0) {
            warnings.push(`Row ${rowNum}: Tags '${tagNames.join(', ')}' not processed (feature not implemented)`);
          }
        }
      } else {
        const errorMsg = createResult.error || 'Failed to create product';
        console.error(`Failed to create product for row ${rowNum}:`, errorMsg);
        errors.push(`Row ${rowNum}: ${errorMsg}`);
        errorCount++;
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error processing row ${rowNum}:`, error);
      errors.push(`Row ${rowNum}: ${errorMsg}`);
      errorCount++;
    }
  }

  return {
    success: errorCount === 0,
    totalRows: products.length,
    successCount,
    errorCount,
    errors,
    warnings,
    createdProducts
  };
};

/**
 * Download product import template
 */
export const downloadProductImportTemplate = (): void => {
  const headers = PRODUCT_IMPORT_HEADERS;
  const sampleData = [
    '"Sample Product 1","SP001","123456789012","A sample product description","Electronics","29.99","15.00","100","10","true","electronics,gadget"',
    '"Sample Product 2","SP002","123456789013","Another sample product","Clothing","19.99","8.00","50","5","true","clothing,apparel"',
    '"Sample Product 3","","","Product without SKU","Books","12.99","6.00","25","5","true","books,education"'
  ];

  const csvContent = [
    headers.map(h => `"${h}"`).join(','),
    ...sampleData
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', 'product_import_template.csv');
  link.style.visibility = 'hidden';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};

/**
 * Validate SKU format (optional custom validation)
 */
export const validateSKU = (sku: string): string | null => {
  if (!sku) return null;

  // SKU should be alphanumeric with optional hyphens and underscores
  const skuPattern = /^[A-Za-z0-9_-]+$/;
  if (!skuPattern.test(sku)) {
    return 'SKU can only contain letters, numbers, hyphens, and underscores';
  }

  return null;
};

/**
 * Validate barcode format (optional custom validation)
 */
export const validateBarcode = (barcode: string): string | null => {
  if (!barcode) return null;

  // Basic barcode validation - should be numeric and common lengths
  const barcodePattern = /^\d+$/;
  if (!barcodePattern.test(barcode)) {
    return 'Barcode should contain only numbers';
  }

  const length = barcode.length;
  const validLengths = [8, 12, 13, 14]; // EAN-8, UPC-A, EAN-13, ITF-14
  if (!validLengths.includes(length)) {
    return `Barcode length should be one of: ${validLengths.join(', ')} digits`;
  }

  return null;
};
