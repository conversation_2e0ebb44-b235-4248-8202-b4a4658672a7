import { supabase } from '../lib/supabase';
import {
  PayrollCalculationResult,
  PayrollCalculationInput,
  DeductionType,
  AllowanceType,
  RateType,
  HolidayType
} from '../types/payroll';
import { getEmployeeById } from './employee';

/**
 * Calculate SSS contribution based on monthly salary
 * @param monthlySalary Monthly salary of the employee
 * @returns Object containing employee and employer contributions
 */
export const calculateSSSContribution = async (
  monthlySalary: number
): Promise<{
  employeeContribution: number;
  employerContribution: number;
  error?: string;
}> => {
  try {
    // Get the latest SSS contribution table
    const { data, error } = await supabase
      .from('sss_contribution_table')
      .select('*')
      .eq('version', '2023') // Use the latest version
      .lte('salary_from', monthlySalary)
      .gte('salary_to', monthlySalary)
      .single();

    if (error) {
      console.error('Error fetching SSS contribution:', error);
      
      // Fallback calculation if table lookup fails
      // This is a simplified version of the SSS contribution calculation
      // For accurate calculations, the table should be properly populated
      const employeeRate = 0.045; // 4.5%
      const employerRate = 0.085; // 8.5%
      const maxSalaryCredit = 25000; // Maximum salary credit for SSS
      
      const salaryCredit = Math.min(monthlySalary, maxSalaryCredit);
      
      return {
        employeeContribution: Math.round(salaryCredit * employeeRate * 100) / 100,
        employerContribution: Math.round(salaryCredit * employerRate * 100) / 100,
      };
    }

    return {
      employeeContribution: data.employee_contribution,
      employerContribution: data.employer_contribution,
    };
  } catch (error: any) {
    console.error('Error in calculateSSSContribution:', error);
    return {
      employeeContribution: 0,
      employerContribution: 0,
      error: error.message,
    };
  }
};

/**
 * Calculate PhilHealth contribution based on monthly salary
 * @param monthlySalary Monthly salary of the employee
 * @returns Object containing employee and employer contributions
 */
export const calculatePhilHealthContribution = async (
  monthlySalary: number
): Promise<{
  employeeContribution: number;
  employerContribution: number;
  error?: string;
}> => {
  try {
    // Get the latest PhilHealth contribution table
    const { data, error } = await supabase
      .from('philhealth_contribution_table')
      .select('*')
      .eq('version', '2023') // Use the latest version
      .lte('salary_from', monthlySalary)
      .gte('salary_to', monthlySalary)
      .single();

    if (error) {
      console.error('Error fetching PhilHealth contribution:', error);
      
      // Fallback calculation if table lookup fails
      // This is based on the 2023 PhilHealth contribution rates
      const rate = 0.02; // 2% total contribution (1% each for employee and employer)
      const minSalary = 10000;
      const maxSalary = 80000;
      
      const cappedSalary = Math.min(Math.max(monthlySalary, minSalary), maxSalary);
      const totalContribution = cappedSalary * rate;
      const halfContribution = totalContribution / 2;
      
      return {
        employeeContribution: Math.round(halfContribution * 100) / 100,
        employerContribution: Math.round(halfContribution * 100) / 100,
      };
    }

    const employeeContribution = monthlySalary * (data.employee_contribution_rate / 100);
    const employerContribution = monthlySalary * (data.employer_contribution_rate / 100);

    return {
      employeeContribution: Math.round(employeeContribution * 100) / 100,
      employerContribution: Math.round(employerContribution * 100) / 100,
    };
  } catch (error: any) {
    console.error('Error in calculatePhilHealthContribution:', error);
    return {
      employeeContribution: 0,
      employerContribution: 0,
      error: error.message,
    };
  }
};

/**
 * Calculate Pag-IBIG contribution based on monthly salary
 * @param monthlySalary Monthly salary of the employee
 * @returns Object containing employee and employer contributions
 */
export const calculatePagibigContribution = async (
  monthlySalary: number
): Promise<{
  employeeContribution: number;
  employerContribution: number;
  error?: string;
}> => {
  try {
    // Get the latest Pag-IBIG contribution table
    const { data, error } = await supabase
      .from('pagibig_contribution_table')
      .select('*')
      .eq('version', '2023') // Use the latest version
      .lte('salary_from', monthlySalary)
      .gte('salary_to', monthlySalary)
      .single();

    if (error) {
      console.error('Error fetching Pag-IBIG contribution:', error);
      
      // Fallback calculation if table lookup fails
      // This is based on the 2023 Pag-IBIG contribution rates
      let employeeRate = 0.02; // 2%
      const employerRate = 0.02; // 2%
      const maxContribution = 100; // Maximum contribution is ₱100
      
      // Employee contribution is 2% for salaries up to ₱5,000
      // and 2% for salaries above ₱5,000
      const employeeContribution = Math.min(monthlySalary * employeeRate, maxContribution);
      const employerContribution = Math.min(monthlySalary * employerRate, maxContribution);
      
      return {
        employeeContribution: Math.round(employeeContribution * 100) / 100,
        employerContribution: Math.round(employerContribution * 100) / 100,
      };
    }

    const employeeContribution = monthlySalary * (data.employee_contribution_rate / 100);
    const employerContribution = monthlySalary * (data.employer_contribution_rate / 100);

    // Pag-IBIG has a maximum contribution of ₱100
    const maxContribution = 100;

    return {
      employeeContribution: Math.min(Math.round(employeeContribution * 100) / 100, maxContribution),
      employerContribution: Math.min(Math.round(employerContribution * 100) / 100, maxContribution),
    };
  } catch (error: any) {
    console.error('Error in calculatePagibigContribution:', error);
    return {
      employeeContribution: 0,
      employerContribution: 0,
      error: error.message,
    };
  }
};

/**
 * Calculate withholding tax based on taxable income
 * @param taxableIncome Taxable income after deductions
 * @returns Withholding tax amount
 */
export const calculateWithholdingTax = async (
  taxableIncome: number
): Promise<{
  tax: number;
  error?: string;
}> => {
  try {
    // Get the appropriate tax table entry
    const { data, error } = await supabase
      .from('tax_tables')
      .select('*')
      .eq('version', '2023') // Use the latest version
      .lte('income_from', taxableIncome)
      .gte('income_to', taxableIncome)
      .single();

    if (error) {
      console.error('Error fetching tax table:', error);
      
      // Fallback calculation if table lookup fails
      // This is a simplified version of the Philippine tax calculation
      // For accurate calculations, the tax table should be properly populated
      let tax = 0;
      
      if (taxableIncome <= 20833) {
        tax = 0;
      } else if (taxableIncome <= 33332) {
        tax = (taxableIncome - 20833) * 0.15;
      } else if (taxableIncome <= 66666) {
        tax = 1875 + (taxableIncome - 33333) * 0.20;
      } else if (taxableIncome <= 166666) {
        tax = 8541.80 + (taxableIncome - 66667) * 0.25;
      } else if (taxableIncome <= 666666) {
        tax = 33541.80 + (taxableIncome - 166667) * 0.30;
      } else {
        tax = 183541.80 + (taxableIncome - 666667) * 0.35;
      }
      
      return {
        tax: Math.round(tax * 100) / 100,
      };
    }

    // Calculate tax: base tax + (taxable income - income_from) * excess_rate
    const excessIncome = taxableIncome - data.income_from;
    const excessTax = excessIncome * (data.excess_rate / 100);
    const totalTax = data.base_tax + excessTax;

    return {
      tax: Math.round(totalTax * 100) / 100,
    };
  } catch (error: any) {
    console.error('Error in calculateWithholdingTax:', error);
    return {
      tax: 0,
      error: error.message,
    };
  }
};

/**
 * Calculate overtime pay
 * @param hourlyRate Base hourly rate
 * @param overtimeHours Number of overtime hours
 * @param isRestDay Whether the overtime is on a rest day
 * @param isHoliday Whether the overtime is on a holiday
 * @param holidayType Type of holiday (regular or special)
 * @returns Overtime pay amount
 */
export const calculateOvertimePay = (
  hourlyRate: number,
  overtimeHours: number,
  isRestDay: boolean = false,
  isHoliday: boolean = false,
  holidayType: HolidayType | null = null
): number => {
  let overtimeRate = 1.25; // Regular overtime (125%)

  if (isHoliday) {
    if (holidayType === HolidayType.REGULAR) {
      overtimeRate = isRestDay ? 3.90 : 2.60; // Regular holiday on rest day (390%) or regular day (260%)
    } else if (holidayType === HolidayType.SPECIAL) {
      overtimeRate = isRestDay ? 1.95 : 1.69; // Special holiday on rest day (195%) or regular day (169%)
    }
  } else if (isRestDay) {
    overtimeRate = 1.69; // Rest day overtime (169%)
  }

  return hourlyRate * overtimeHours * overtimeRate;
};

/**
 * Calculate night differential pay
 * @param hourlyRate Base hourly rate
 * @param nightDiffHours Number of night differential hours (10pm to 6am)
 * @returns Night differential pay amount
 */
export const calculateNightDifferentialPay = (
  hourlyRate: number,
  nightDiffHours: number
): number => {
  const nightDiffRate = 0.10; // 10% night differential
  return hourlyRate * nightDiffHours * nightDiffRate;
};

/**
 * Calculate holiday pay
 * @param dailyRate Daily rate of the employee
 * @param isRegularHoliday Whether it's a regular holiday
 * @returns Holiday pay amount
 */
export const calculateHolidayPay = (
  dailyRate: number,
  isRegularHoliday: boolean
): number => {
  // Regular holiday: 100% of daily rate
  // Special holiday: 30% of daily rate
  const rate = isRegularHoliday ? 1.0 : 0.3;
  return dailyRate * rate;
};
