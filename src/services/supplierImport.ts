/**
 * Supplier Import Service
 * Handles bulk import of suppliers from CSV files
 */

import { supabase } from '../lib/supabase';
import { parseAndValidateCSV, CSVValidationRule, ParsedCSVRow } from '../utils/csvParser';
import { createSupplier } from './supplier';

export interface SupplierImportRow {
  name: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  tax_id?: string;
  notes?: string;
}

export interface SupplierImportResult {
  success: boolean;
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: string[];
  warnings: string[];
  createdSuppliers: any[];
  createdItems: any[]; // Alias for compatibility with GenericImport
}

export interface SupplierImportPreview {
  isValid: boolean;
  headers: string[];
  sampleData: ParsedCSVRow[];
  errors: string[];
  warnings: string[];
  totalRows: number;
}

/**
 * Supplier import validation rules
 */
export const SUPPLIER_IMPORT_RULES: CSVValidationRule[] = [
  {
    field: 'name',
    required: true,
    type: 'string',
    customValidator: (value) => {
      if (typeof value === 'string' && value.length > 255) {
        return 'Supplier name must be 255 characters or less';
      }
      return null;
    }
  },
  {
    field: 'email',
    type: 'email'
  },
  {
    field: 'phone',
    type: 'string',
    customValidator: (value) => {
      if (value && typeof value === 'string' && value.length > 50) {
        return 'Phone number must be 50 characters or less';
      }
      return null;
    }
  },
  {
    field: 'tax_id',
    type: 'string',
    customValidator: (value) => {
      if (value && typeof value === 'string' && value.length > 50) {
        return 'Tax ID must be 50 characters or less';
      }
      return null;
    }
  }
];

/**
 * Expected CSV headers for supplier import
 */
export const SUPPLIER_IMPORT_HEADERS = [
  'name',
  'contact_person',
  'email',
  'phone',
  'address',
  'city',
  'state',
  'postal_code',
  'country',
  'tax_id',
  'notes'
];

/**
 * Preview CSV file before import
 */
export const previewSupplierImport = (csvText: string): SupplierImportPreview => {
  const result = parseAndValidateCSV(csvText, SUPPLIER_IMPORT_RULES);
  
  return {
    isValid: result.success,
    headers: result.headers,
    sampleData: result.data.slice(0, 5), // Show first 5 rows as preview
    errors: result.errors,
    warnings: result.warnings,
    totalRows: result.data.length
  };
};

/**
 * Check for duplicate suppliers by name
 */
const checkDuplicateSuppliers = async (
  organizationId: string,
  suppliers: SupplierImportRow[]
): Promise<string[]> => {
  const names = suppliers.map(s => s.name);
  const duplicates: string[] = [];

  if (names.length > 0) {
    const { data: existingSuppliers } = await supabase
      .from('suppliers')
      .select('name')
      .eq('organization_id', organizationId)
      .in('name', names);

    if (existingSuppliers) {
      duplicates.push(...existingSuppliers.map(s => s.name));
    }
  }

  return duplicates;
};

/**
 * Import suppliers from CSV
 */
export const importSuppliers = async (
  organizationId: string,
  csvText: string,
  userId: string,
  skipDuplicates: boolean = true
): Promise<SupplierImportResult> => {
  const result = parseAndValidateCSV(csvText, SUPPLIER_IMPORT_RULES);
  
  if (!result.success) {
    return {
      success: false,
      totalRows: result.data.length,
      successCount: 0,
      errorCount: result.data.length,
      errors: result.errors,
      warnings: result.warnings,
      createdSuppliers: [],
      createdItems: []
    };
  }

  const suppliers = result.data as SupplierImportRow[];
  const errors: string[] = [...result.errors];
  const warnings: string[] = [...result.warnings];
  const createdSuppliers: any[] = [];

  // Check for duplicates
  const duplicateNames = await checkDuplicateSuppliers(organizationId, suppliers);

  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < suppliers.length; i++) {
    const supplier = suppliers[i];
    const rowNum = i + 2; // +2 because row 1 is headers and we're 0-indexed

    try {
      // Check for duplicate name
      if (duplicateNames.includes(supplier.name)) {
        if (skipDuplicates) {
          warnings.push(`Row ${rowNum}: Skipped - Supplier '${supplier.name}' already exists`);
          continue;
        } else {
          errors.push(`Row ${rowNum}: Supplier '${supplier.name}' already exists`);
          errorCount++;
          continue;
        }
      }

      // Prepare supplier data
      const supplierData = {
        name: supplier.name,
        contact_person: supplier.contact_person || null,
        email: supplier.email || null,
        phone: supplier.phone || null,
        address: supplier.address || null,
        city: supplier.city || null,
        state: supplier.state || null,
        postal_code: supplier.postal_code || null,
        country: supplier.country || null,
        tax_id: supplier.tax_id || null,
        notes: supplier.notes || null
      };

      // Create supplier
      const createResult = await createSupplier(organizationId, supplierData);
      
      if (createResult.supplier && !createResult.error) {
        createdSuppliers.push(createResult.supplier);
        successCount++;
      } else {
        const errorMsg = createResult.error || 'Failed to create supplier';
        errors.push(`Row ${rowNum}: ${errorMsg}`);
        errorCount++;
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Row ${rowNum}: ${errorMsg}`);
      errorCount++;
    }
  }

  return {
    success: errorCount === 0,
    totalRows: suppliers.length,
    successCount,
    errorCount,
    errors,
    warnings,
    createdSuppliers,
    createdItems: createdSuppliers // Alias for compatibility
  };
};

/**
 * Download supplier import template
 */
export const downloadSupplierImportTemplate = (): void => {
  const headers = SUPPLIER_IMPORT_HEADERS;
  const sampleData = [
    '"ABC Supply Co","John Manager","<EMAIL>","555-1001","100 Industrial Blvd","Houston","TX","77001","USA","SUP123","Primary supplier for electronics"',
    '"XYZ Materials","Jane Contact","<EMAIL>","555-1002","200 Commerce St","Dallas","TX","75201","USA","SUP456","Reliable material supplier"',
    '"Local Vendor","","","555-1003","300 Local Ave","Austin","TX","73301","USA","","Small local supplier"'
  ];

  const csvContent = [
    headers.map(h => `"${h}"`).join(','),
    ...sampleData
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', 'supplier_import_template.csv');
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};
