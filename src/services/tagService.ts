import { supabase } from '../lib/supabase';
import {
  Tag,
  TaggableEntityType,
  CreateTagParams,
  UpdateTagParams,
  AddTagToEntityParams,
  RemoveTagFromEntityParams,
  TagWithCount,
  EntityTagView
} from '../types/tagging.types';

/**
 * Create a new tag
 */
export const createTag = async (params: CreateTagParams): Promise<{
  success: boolean;
  tag?: Tag;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('tags')
      .insert(params)
      .select()
      .single();

    if (error) {
      console.error('Error creating tag:', error);
      return { success: false, error: error.message };
    }

    return { success: true, tag: data };
  } catch (error: any) {
    console.error('Error in createTag:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update an existing tag
 */
export const updateTag = async (
  tagId: string,
  params: UpdateTagParams
): Promise<{
  success: boolean;
  tag?: Tag;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('tags')
      .update(params)
      .eq('id', tagId)
      .select()
      .single();

    if (error) {
      console.error('Error updating tag:', error);
      return { success: false, error: error.message };
    }

    return { success: true, tag: data };
  } catch (error: any) {
    console.error('Error in updateTag:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Delete a tag
 */
export const deleteTag = async (tagId: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('tags')
      .delete()
      .eq('id', tagId);

    if (error) {
      console.error('Error deleting tag:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in deleteTag:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all tags for an organization
 */
export const getOrganizationTags = async (
  organizationId: string
): Promise<{
  success: boolean;
  tags?: Tag[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('tags')
      .select('*')
      .eq('organization_id', organizationId)
      .order('name');

    if (error) {
      console.error('Error fetching organization tags:', error);
      return { success: false, error: error.message };
    }

    return { success: true, tags: data };
  } catch (error: any) {
    console.error('Error in getOrganizationTags:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get tags with usage count
 */
export const getTagsWithCount = async (
  organizationId: string
): Promise<{
  success: boolean;
  tags?: TagWithCount[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .rpc('get_tags_with_count', { p_organization_id: organizationId });

    if (error) {
      console.error('Error fetching tags with count:', error);
      return { success: false, error: error.message };
    }

    return { success: true, tags: data };
  } catch (error: any) {
    console.error('Error in getTagsWithCount:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Add a tag to an entity
 */
export const addTagToEntity = async (
  params: AddTagToEntityParams
): Promise<{
  success: boolean;
  id?: string;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .rpc('add_tag_to_entity', {
        p_tag_id: params.tag_id,
        p_entity_type: params.entity_type,
        p_entity_id: params.entity_id
      });

    if (error) {
      console.error('Error adding tag to entity:', error);
      return { success: false, error: error.message };
    }

    return { success: true, id: data };
  } catch (error: any) {
    console.error('Error in addTagToEntity:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Remove a tag from an entity
 */
export const removeTagFromEntity = async (
  params: RemoveTagFromEntityParams
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .rpc('remove_tag_from_entity', {
        p_tag_id: params.tag_id,
        p_entity_type: params.entity_type,
        p_entity_id: params.entity_id
      });

    if (error) {
      console.error('Error removing tag from entity:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in removeTagFromEntity:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all tags for an entity
 */
export const getEntityTags = async (
  entityType: TaggableEntityType,
  entityId: string
): Promise<{
  success: boolean;
  tags?: Tag[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .rpc('get_entity_tags', {
        p_entity_type: entityType,
        p_entity_id: entityId
      });

    if (error) {
      console.error('Error fetching entity tags:', error);
      return { success: false, error: error.message };
    }

    return { success: true, tags: data };
  } catch (error: any) {
    console.error('Error in getEntityTags:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Find entities by tag
 */
export const findEntitiesByTag = async (
  tagId: string,
  entityType?: TaggableEntityType
): Promise<{
  success: boolean;
  entities?: { entity_type: TaggableEntityType; entity_id: string }[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .rpc('find_entities_by_tag', {
        p_tag_id: tagId,
        p_entity_type: entityType
      });

    if (error) {
      console.error('Error finding entities by tag:', error);
      return { success: false, error: error.message };
    }

    return { success: true, entities: data };
  } catch (error: any) {
    console.error('Error in findEntitiesByTag:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get a tag by ID
 */
export const getTagById = async (
  tagId: string
): Promise<{
  success: boolean;
  tag?: Tag;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('tags')
      .select('*')
      .eq('id', tagId)
      .single();

    if (error) {
      console.error('Error fetching tag by ID:', error);
      return { success: false, error: error.message };
    }

    return { success: true, tag: data };
  } catch (error: any) {
    console.error('Error in getTagById:', error);
    return { success: false, error: error.message };
  }
};
