import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';

// Define types
export type QcChecklistTemplate = Database['public']['Tables']['qc_checklist_templates']['Row'];
export type QcChecklistTemplateInsert = Database['public']['Tables']['qc_checklist_templates']['Insert'];
export type QcChecklistTemplateUpdate = Database['public']['Tables']['qc_checklist_templates']['Update'];

export type QcChecklistItem = Database['public']['Tables']['qc_checklist_items']['Row'];
export type QcChecklistItemInsert = Database['public']['Tables']['qc_checklist_items']['Insert'];
export type QcChecklistItemUpdate = Database['public']['Tables']['qc_checklist_items']['Update'];

export type QcInspection = Database['public']['Tables']['qc_inspections']['Row'];
export type QcInspectionInsert = Database['public']['Tables']['qc_inspections']['Insert'];
export type QcInspectionUpdate = Database['public']['Tables']['qc_inspections']['Update'];

export type QcInspectionResult = Database['public']['Tables']['qc_inspection_results']['Row'];
export type QcInspectionResultInsert = Database['public']['Tables']['qc_inspection_results']['Insert'];
export type QcInspectionResultUpdate = Database['public']['Tables']['qc_inspection_results']['Update'];

export type ProductQcTemplate = Database['public']['Tables']['product_qc_templates']['Row'];
export type ProductQcTemplateInsert = Database['public']['Tables']['product_qc_templates']['Insert'];
export type ProductQcTemplateUpdate = Database['public']['Tables']['product_qc_templates']['Update'];

export type QcChecklistTemplateWithItems = QcChecklistTemplate & {
  items: QcChecklistItem[];
};

export type QcInspectionWithResults = QcInspection & {
  template: QcChecklistTemplate;
  results: (QcInspectionResult & {
    checklist_item: QcChecklistItem;
  })[];
  product?: {
    id: string;
    name: string;
    sku?: string;
  };
  inspector?: {
    id: string;
    email?: string;
    name?: string;
  };
};

/**
 * Get QC checklist templates for an organization
 */
export const getQcChecklistTemplates = async (
  organizationId: string
): Promise<{
  templates: QcChecklistTemplate[];
  error?: string;
}> => {
  try {
    console.log('Fetching QC checklist templates for organization:', organizationId);

    const { data, error } = await supabase
      .from('qc_checklist_templates')
      .select('*')
      .eq('organization_id', organizationId)
      .order('name');

    if (error) {
      console.error('Error fetching QC checklist templates:', error);
      return {
        templates: [],
        error: error.message,
      };
    }

    return {
      templates: data || [],
    };
  } catch (error: any) {
    console.error('Error in getQcChecklistTemplates:', error);
    return {
      templates: [],
      error: error.message,
    };
  }
};

/**
 * Get a QC checklist template by ID with its items
 */
export const getQcChecklistTemplateById = async (
  organizationId: string,
  templateId: string
): Promise<{
  template?: QcChecklistTemplateWithItems;
  error?: string;
}> => {
  try {
    console.log('Fetching QC checklist template by ID:', templateId);

    const { data, error } = await supabase
      .from('qc_checklist_templates')
      .select(`
        *,
        items:qc_checklist_items(*)
      `)
      .eq('organization_id', organizationId)
      .eq('id', templateId)
      .single();

    if (error) {
      console.error('Error fetching QC checklist template:', error);
      return {
        error: error.message,
      };
    }

    // Sort items by sort_order
    if (data && data.items) {
      data.items.sort((a: any, b: any) => a.sort_order - b.sort_order);
    }

    return {
      template: data as QcChecklistTemplateWithItems,
    };
  } catch (error: any) {
    console.error('Error in getQcChecklistTemplateById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new QC checklist template
 */
export const createQcChecklistTemplate = async (
  organizationId: string,
  data: {
    name: string;
    description?: string;
    isActive?: boolean;
    items: {
      name: string;
      description?: string;
      itemType: 'boolean' | 'numeric' | 'text' | 'select';
      isRequired?: boolean;
      minValue?: number;
      maxValue?: number;
      unit?: string;
      options?: any;
      passCriteria?: any;
      sortOrder?: number;
    }[];
  }
): Promise<{
  template?: QcChecklistTemplateWithItems;
  error?: string;
}> => {
  try {
    console.log('Creating QC checklist template:', data);

    // Start a transaction
    const { data: templateData, error: templateError } = await supabase
      .from('qc_checklist_templates')
      .insert({
        organization_id: organizationId,
        name: data.name,
        description: data.description,
        is_active: data.isActive !== false,
        created_by: (await supabase.auth.getUser()).data.user?.id || '',
      })
      .select()
      .single();

    if (templateError) {
      console.error('Error creating QC checklist template:', templateError);
      return {
        error: templateError.message,
      };
    }

    console.log('QC checklist template created:', templateData);

    // Add items
    if (data.items.length > 0) {
      const itemsWithTemplateId = data.items.map((item, index) => {
        return {
          template_id: templateData.id,
          name: item.name,
          description: item.description,
          item_type: item.itemType,
          is_required: item.isRequired !== false,
          min_value: item.minValue,
          max_value: item.maxValue,
          unit: item.unit,
          options: item.options ? JSON.stringify(item.options) : null,
          pass_criteria: item.passCriteria ? JSON.stringify(item.passCriteria) : null,
          sort_order: item.sortOrder || index,
        };
      });

      const { error: itemsError } = await supabase
        .from('qc_checklist_items')
        .insert(itemsWithTemplateId);

      if (itemsError) {
        console.error('Error adding QC checklist items:', itemsError);
        return {
          error: itemsError.message,
        };
      }
    }

    // Get the created template with items
    return await getQcChecklistTemplateById(organizationId, templateData.id);
  } catch (error: any) {
    console.error('Error in createQcChecklistTemplate:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update a QC checklist template
 */
export const updateQcChecklistTemplate = async (
  organizationId: string,
  templateId: string,
  data: {
    name?: string;
    description?: string;
    isActive?: boolean;
  }
): Promise<{
  template?: QcChecklistTemplateWithItems;
  error?: string;
}> => {
  try {
    console.log('Updating QC checklist template:', { templateId, data });

    const { error: updateError } = await supabase
      .from('qc_checklist_templates')
      .update({
        name: data.name,
        description: data.description,
        is_active: data.isActive,
        updated_at: new Date().toISOString(),
      })
      .eq('id', templateId)
      .eq('organization_id', organizationId);

    if (updateError) {
      console.error('Error updating QC checklist template:', updateError);
      return {
        error: updateError.message,
      };
    }

    // Get the updated template with items
    return await getQcChecklistTemplateById(organizationId, templateId);
  } catch (error: any) {
    console.error('Error in updateQcChecklistTemplate:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Delete a QC checklist template
 */
export const deleteQcChecklistTemplate = async (
  organizationId: string,
  templateId: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    console.log('Deleting QC checklist template:', templateId);

    const { error } = await supabase
      .from('qc_checklist_templates')
      .delete()
      .eq('id', templateId)
      .eq('organization_id', organizationId);

    if (error) {
      console.error('Error deleting QC checklist template:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteQcChecklistTemplate:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Get QC checklist templates for a product
 */
export const getQcChecklistTemplatesForProduct = async (
  organizationId: string,
  productId: string
): Promise<{
  templates: QcChecklistTemplate[];
  error?: string;
}> => {
  try {
    console.log('Fetching QC checklist templates for product:', productId);

    const { data, error } = await supabase
      .from('product_qc_templates')
      .select(`
        template:template_id(*)
      `)
      .eq('product_id', productId);

    if (error) {
      console.error('Error fetching QC checklist templates for product:', error);
      return {
        templates: [],
        error: error.message,
      };
    }

    // Extract templates from the result
    const templates = data.map((item: any) => item.template);

    return {
      templates: templates || [],
    };
  } catch (error: any) {
    console.error('Error in getQcChecklistTemplatesForProduct:', error);
    return {
      templates: [],
      error: error.message,
    };
  }
};

/**
 * Assign a QC checklist template to a product
 */
export const assignQcChecklistTemplateToProduct = async (
  productId: string,
  templateId: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    console.log('Assigning QC checklist template to product:', { productId, templateId });

    const { error } = await supabase
      .from('product_qc_templates')
      .insert({
        product_id: productId,
        template_id: templateId,
      });

    if (error) {
      console.error('Error assigning QC checklist template to product:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in assignQcChecklistTemplateToProduct:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Remove a QC checklist template from a product
 */
export const removeQcChecklistTemplateFromProduct = async (
  productId: string,
  templateId: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    console.log('Removing QC checklist template from product:', { productId, templateId });

    const { error } = await supabase
      .from('product_qc_templates')
      .delete()
      .eq('product_id', productId)
      .eq('template_id', templateId);

    if (error) {
      console.error('Error removing QC checklist template from product:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in removeQcChecklistTemplateFromProduct:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
