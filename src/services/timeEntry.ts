import { supabase } from '../lib/supabase';
import { TimeEntry, TimeEntryStatus } from '../types/payroll';
import { differenceInHours, differenceInMinutes, parseISO, addDays } from 'date-fns';

/**
 * Get time entries for an organization
 * @param organizationId Organization ID
 * @param options Query options
 * @returns Time entries, count, and error if any
 */
export const getTimeEntries = async (
  organizationId: string,
  options?: {
    limit?: number;
    offset?: number;
    startDate?: Date;
    endDate?: Date;
    employeeId?: string;
  }
): Promise<{
  entries: TimeEntry[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('time_entries')
      .select('*, employee:employee_id(*)', { count: 'exact' })
      .eq('organization_id', organizationId);

    // Apply filters
    if (options?.startDate && options?.endDate) {
      // Format dates as YYYY-MM-DD strings
      // Use UTC date to avoid timezone issues
      const startDate = new Date(options.startDate);
      const endDate = new Date(options.endDate);

      // Format dates manually to ensure consistency
      const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${String(startDate.getDate()).padStart(2, '0')}`;
      const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;

      if (startDateStr === endDateStr) {
        // For exact date match, use eq
        query = query.eq('date', startDateStr);
      } else {
        // Date range
        query = query.gte('date', startDateStr).lte('date', endDateStr);
      }
    } else if (options?.startDate) {
      // Only start date provided
      const startDate = new Date(options.startDate);
      const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${String(startDate.getDate()).padStart(2, '0')}`;
      query = query.gte('date', startDateStr);
    } else if (options?.endDate) {
      // Only end date provided
      const endDate = new Date(options.endDate);
      const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;
      query = query.lte('date', endDateStr);
    }

    if (options?.employeeId) {
      query = query.eq('employee_id', options.employeeId);
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    // Order by date
    query = query.order('date', { ascending: false });



    const { data, error, count } = await query;

    if (error) {
      console.error('Error in time entries query:', error);
      throw new Error(error.message);
    }

    const mappedData = data?.map(entry => ({
        id: entry.id,
        date: entry.date,
        dateStr: new Date(entry.date).toISOString().split('T')[0],
        employee: entry.employee_id
      }));

    return {
      entries: data as TimeEntry[],
      count: count || 0
    };
  } catch (err: any) {
  }
};

/**
 * Get a time entry by ID
 * @param organizationId Organization ID
 * @param entryId Time entry ID
 * @returns Time entry and error if any
 */
export const getTimeEntryById = async (
  organizationId: string,
  entryId: string
): Promise<{
  entry?: TimeEntry;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('time_entries')
      .select('*, employee:employee_id(*)')
      .eq('organization_id', organizationId)
      .eq('id', entryId)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return { entry: data as TimeEntry };
  } catch (err: any) {
    console.error('Error fetching time entry:', err);
    return { error: err.message };
  }
};

/**
 * Create a new time entry
 * @param organizationId Organization ID
 * @param entryData Time entry data
 * @returns Created time entry and error if any
 */
export const createTimeEntry = async (
  organizationId: string,
  entryData: Omit<TimeEntry, 'id' | 'organization_id' | 'created_at' | 'updated_at'>
): Promise<{
  entry?: TimeEntry;
  error?: string;
}> => {
  try {
    // Calculate hours if time_in and time_out are provided
    let regular_hours = entryData.regular_hours || 0;
    let overtime_hours = entryData.overtime_hours || 0;
    let night_diff_hours = entryData.night_diff_hours || 0;

    if (entryData.time_in && entryData.time_out) {
      const timeIn = parseISO(entryData.time_in);
      const timeOut = parseISO(entryData.time_out);

      // Handle overnight shifts (timeOut is earlier than timeIn)
      let adjustedTimeOut = timeOut;
      if (timeOut < timeIn) {
        adjustedTimeOut = addDays(timeOut, 1);
      }

      // Calculate total minutes
      let totalMinutes = differenceInMinutes(adjustedTimeOut, timeIn);

      // Subtract break time if provided
      let breakMinutes = 0;
      if (entryData.break_start && entryData.break_end) {
        const breakStart = parseISO(entryData.break_start);
        const breakEnd = parseISO(entryData.break_end);
        breakMinutes = differenceInMinutes(breakEnd, breakStart);
      }

      // Subtract lunch break if applicable (1 hour = 60 minutes)
      if (entryData.exclude_lunch_break && totalMinutes > 300) { // Only subtract if shift is > 5 hours
        totalMinutes -= 60;
      }

      const workMinutes = totalMinutes - breakMinutes;
      const totalHours = workMinutes / 60;

      // Determine regular and overtime hours
      if (totalHours > 8) {
        regular_hours = 8;
        overtime_hours = totalHours - 8;
      } else {
        regular_hours = totalHours;
        overtime_hours = 0;
      }

      // Round to nearest 0.5
      regular_hours = Math.round(regular_hours * 2) / 2;
      overtime_hours = Math.round(overtime_hours * 2) / 2;

      // Night differential calculation is already handled in the UI
      night_diff_hours = entryData.night_diff_hours || 0;
    }

    // Remove exclude_lunch_break as it's not in the DB schema
    const { exclude_lunch_break, ...dbEntryData } = entryData;

    // Generate a shift_group to identify related split shifts
    const shiftGroup = `${dbEntryData.employee_id}-${dbEntryData.date}`;

    // Insert entry with shift_group
    const { data, error } = await supabase
      .from('time_entries')
      .insert({
        organization_id: organizationId,
        ...dbEntryData,
        shift_group: shiftGroup,
        regular_hours,
        overtime_hours,
        night_diff_hours
      })
      .select('*, employee:employee_id(*)')
      .single();

    if (error) {
      throw new Error(`Error creating time entry: ${error.message}`);
    }

    return { entry: data as TimeEntry };
  } catch (err: any) {
    console.error('Error creating time entry:', err);
    return { error: err.message };
  }
};

/**
 * Update a time entry
 * @param organizationId Organization ID
 * @param entryId Time entry ID
 * @param entryData Time entry data to update
 * @returns Updated time entry and error if any
 */
export const updateTimeEntry = async (
  organizationId: string,
  entryId: string,
  entryData: Partial<TimeEntry>
): Promise<{
  entry?: TimeEntry;
  error?: string;
}> => {
  try {
    // Calculate hours if time_in and time_out are provided
    let updateData: Partial<TimeEntry> = { ...entryData };

    if (entryData.time_in && entryData.time_out) {
      const timeIn = parseISO(entryData.time_in);
      const timeOut = parseISO(entryData.time_out);

      // Handle overnight shifts (timeOut is earlier than timeIn)
      let adjustedTimeOut = timeOut;
      if (timeOut < timeIn) {
        adjustedTimeOut = addDays(timeOut, 1);
      }

      // Calculate total minutes
      let totalMinutes = differenceInMinutes(adjustedTimeOut, timeIn);

      // Subtract break time if provided
      let breakMinutes = 0;
      if (entryData.break_start && entryData.break_end) {
        const breakStart = parseISO(entryData.break_start);
        const breakEnd = parseISO(entryData.break_end);
        breakMinutes = differenceInMinutes(breakEnd, breakStart);
      }

      // Subtract lunch break if applicable (1 hour = 60 minutes)
      if (entryData.exclude_lunch_break && totalMinutes > 300) { // Only subtract if shift is > 5 hours
        totalMinutes -= 60;
      }

      const workMinutes = totalMinutes - breakMinutes;
      const totalHours = workMinutes / 60;

      // Determine regular and overtime hours
      if (totalHours > 8) {
        updateData.regular_hours = 8;
        updateData.overtime_hours = totalHours - 8;
      } else {
        updateData.regular_hours = totalHours;
        updateData.overtime_hours = 0;
      }

      // Round to nearest 0.5
      if (updateData.regular_hours) {
        updateData.regular_hours = Math.round(updateData.regular_hours * 2) / 2;
      }
      if (updateData.overtime_hours) {
        updateData.overtime_hours = Math.round(updateData.overtime_hours * 2) / 2;
      }

      // Night differential calculation is already handled in the UI
      updateData.night_diff_hours = entryData.night_diff_hours;
    }

    // Remove exclude_lunch_break as it's not in the DB schema
    if ('exclude_lunch_break' in entryData) {
      const { exclude_lunch_break, ...rest } = updateData;
      updateData = rest;
    }

    const { data, error } = await supabase
      .from('time_entries')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('organization_id', organizationId)
      .eq('id', entryId)
      .select('*, employee:employee_id(*)')
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return { entry: data as TimeEntry };
  } catch (err: any) {
    console.error('Error updating time entry:', err);
    return { error: err.message };
  }
};

/**
 * Delete a time entry
 * @param organizationId Organization ID
 * @param entryId Time entry ID
 * @returns Success status and error if any
 */
export const deleteTimeEntry = async (
  organizationId: string,
  entryId: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('time_entries')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', entryId);

    if (error) {
      throw new Error(error.message);
    }

    return { success: true };
  } catch (err: any) {
    console.error('Error deleting time entry:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Create multiple time entries at once
 * @param organizationId Organization ID
 * @param entriesData Array of time entry data
 * @returns Created time entries and error if any
 */
export const createTimeEntries = async (
  organizationId: string,
  entriesData: Omit<TimeEntry, 'id' | 'organization_id' | 'created_at' | 'updated_at'>[]
): Promise<{
  entries?: TimeEntry[];
  error?: string;
}> => {
  try {
    // Process each entry to calculate hours
    const processedEntries = entriesData.map(entryData => {
      // Calculate hours if time_in and time_out are provided
      let regular_hours = entryData.regular_hours || 0;
      let overtime_hours = entryData.overtime_hours || 0;
      let night_diff_hours = entryData.night_diff_hours || 0;

      if (entryData.time_in && entryData.time_out) {
        const timeIn = parseISO(entryData.time_in);
        const timeOut = parseISO(entryData.time_out);

        // Handle overnight shifts (timeOut is earlier than timeIn)
        let adjustedTimeOut = timeOut;
        if (timeOut < timeIn) {
          adjustedTimeOut = addDays(timeOut, 1);
        }

        // Calculate total minutes
        let totalMinutes = differenceInMinutes(adjustedTimeOut, timeIn);

        // Subtract lunch break if applicable (1 hour = 60 minutes)
        // Note: exclude_lunch_break is not stored in the database
        if (entryData.exclude_lunch_break && totalMinutes > 300) { // Only subtract if shift is > 5 hours
          totalMinutes -= 60;
        }

        // Calculate total hours
        const totalHours = totalMinutes / 60;

        // Determine regular and overtime hours
        if (totalHours > 8) {
          regular_hours = 8;
          overtime_hours = totalHours - 8;
        } else {
          regular_hours = totalHours;
          overtime_hours = 0;
        }

        // Round to nearest 0.5
        regular_hours = Math.round(regular_hours * 2) / 2;
        overtime_hours = Math.round(overtime_hours * 2) / 2;

        // Night differential calculation is already handled in the UI
        night_diff_hours = entryData.night_diff_hours || 0;
      }

      // Remove exclude_lunch_break as it's not in the DB schema
      const { exclude_lunch_break, ...rest } = entryData;

      return {
        organization_id: organizationId,
        ...rest,
        regular_hours,
        overtime_hours,
        night_diff_hours
      };
    });

    // For split shifts, we need to handle the unique constraint
    // We'll check for existing entries and modify the date for additional entries
    const newEntries = [];
    const existingEntries = [];

    for (const entry of processedEntries) {
      const { data: existing } = await supabase
        .from('time_entries')
        .select('id')
        .eq('organization_id', organizationId)
        .eq('employee_id', entry.employee_id)
        .eq('date', entry.date)
        .maybeSingle();

      // Generate a shift_group to identify related split shifts
      const shiftGroup = `${entry.employee_id}-${entry.date}`;

      // Add shift_group to entry
      newEntries.push({
        ...entry,
        shift_group: shiftGroup
      });
    }

    // Insert new entries in bulk if any
    let insertedEntries = [];
    if (newEntries.length > 0) {
      const { data, error } = await supabase
        .from('time_entries')
        .insert(newEntries)
        .select('*, employee:employee_id(*)');

      if (error) {
        throw new Error(error.message);
      }

      insertedEntries = data || [];
    }

    // Combine existing and new entries
    const allEntries = [...existingEntries, ...insertedEntries];

    return { entries: allEntries as TimeEntry[] };
  } catch (err: any) {
    console.error('Error creating time entries:', err);
    return { error: err.message };
  }
};