import { supabase } from '../lib/supabase';
import { Product } from './product';
import { Customer } from './customer';

export interface SaleItem {
  id?: string;
  sale_id?: string;
  product_id: string;
  product?: Product;
  quantity: number;
  unit_price: number;
  uom_id: string;
  base_quantity: number;
  tax_rate: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  notes?: string;
}

export interface Cashier {
  id: string;
  first_name?: string | null;
  last_name?: string | null;
  avatar_url?: string | null;
}

export interface Sale {
  id?: string;
  organization_id: string;
  customer_id?: string | null;
  customer?: Customer | null;
  cashier?: Cashier | null;
  invoice_number: string;
  sale_date: string;
  status: 'draft' | 'completed' | 'cancelled' | 'refunded';
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  payment_method?: string | null;
  cash_tendered?: number | null;
  change_amount?: number | null;
  notes?: string | null;
  created_by: string;
  created_at?: string;
  updated_at?: string;
  items?: SaleItem[];
  loyalty_points_used?: number;
  loyalty_points_earned?: number;
  loyalty_points_discount?: number;
}

/**
 * Generate a unique invoice number for a new sale
 */
export const generateInvoiceNumber = async (
  organizationId: string
): Promise<{
  invoiceNumber: string;
  error?: string;
}> => {
  try {
    // Get the current date in YYYYMMDD format
    const today = new Date();
    const datePrefix = today.getFullYear().toString() +
      (today.getMonth() + 1).toString().padStart(2, '0') +
      today.getDate().toString().padStart(2, '0');

    // Get the count of sales for today to generate a sequential number
    const { count, error } = await supabase
      .from('sales')
      .select('*', { count: 'exact', head: true })
      .eq('organization_id', organizationId)
      .like('invoice_number', `INV-${datePrefix}%`);

    if (error) {
      console.error('Error getting sales count:', error);
      return { invoiceNumber: '', error: error.message };
    }

    // Generate the invoice number with format INV-YYYYMMDD-XXXX
    const sequentialNumber = ((count || 0) + 1).toString().padStart(4, '0');
    const invoiceNumber = `INV-${datePrefix}-${sequentialNumber}`;

    return { invoiceNumber };
  } catch (error: any) {
    console.error('Error generating invoice number:', error);
    return { invoiceNumber: '', error: error.message };
  }
};

/**
 * Create a new sale with items
 */
export const createSale = async (
  organizationId: string,
  userId: string,
  saleData: {
    customer_id?: string;
    items: {
      product_id: string;
      quantity: number;
      unit_price: number;
      uom_id: string;
      base_quantity: number;
      tax_rate: number;
      discount_amount: number;
      notes?: string;
    }[];
    subtotal: number;
    tax_amount: number;
    discount_amount: number;
    total_amount: number;
    payment_method: string;
    cash_tendered?: number;
    change_amount?: number;
    notes?: string;
    loyalty_points_used?: number;
    loyalty_points_discount?: number;
    loyalty_points_earned?: number;
  }
): Promise<{
  sale?: Sale;
  error?: string;
}> => {
  try {
    // Generate invoice number
    const { invoiceNumber, error: invoiceError } = await generateInvoiceNumber(organizationId);

    if (invoiceError) {
      return { error: invoiceError };
    }

    // Start a transaction
    const { data, error } = await supabase.rpc('create_sale_with_items', {
      p_organization_id: organizationId,
      p_customer_id: saleData.customer_id || null,
      p_invoice_number: invoiceNumber,
      p_status: 'completed',
      p_subtotal: saleData.subtotal,
      p_tax_amount: saleData.tax_amount,
      p_discount_amount: saleData.discount_amount,
      p_total_amount: saleData.total_amount,
      p_payment_method: saleData.payment_method,
      p_notes: saleData.notes || null,
      p_created_by: userId,
      p_loyalty_points_used: saleData.loyalty_points_used || 0,
      p_loyalty_points_discount: saleData.loyalty_points_discount || 0,
      p_cash_tendered: saleData.cash_tendered || null,
      p_change_amount: saleData.change_amount || null,
      p_items: saleData.items.map(item => ({
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        uom_id: item.uom_id,
        base_quantity: item.base_quantity,
        tax_rate: item.tax_rate,
        tax_amount: (item.unit_price * item.quantity * item.tax_rate / 100),
        discount_amount: item.discount_amount,
        total_amount: (item.unit_price * item.quantity) - item.discount_amount,
        notes: item.notes || ''
      }))
    });

    if (error) {
      console.error('Error creating sale:', error);
      return { error: error.message };
    }

    // Get the created sale with items
    const { data: saleWithItems, error: fetchError } = await supabase
      .from('sales')
      .select(`
        *,
        customer:customer_id(*),
        items:sale_items(
          *,
          product:product_id(*)
        )
      `)
      .eq('id', data.sale_id)
      .single();

    if (fetchError) {
      console.error('Error fetching created sale:', fetchError);
      return { error: fetchError.message };
    }

    return { sale: saleWithItems };
  } catch (error: any) {
    console.error('Error in createSale:', error);
    return { error: error.message };
  }
};

/**
 * Get a sale by ID
 */
export const getSaleById = async (
  organizationId: string,
  saleId: string
): Promise<{
  sale?: Sale;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('sales')
      .select(`
        *,
        customer:customer_id(*),
        items:sale_items(
          *,
          product:product_id(*)
        )
      `)
      .eq('organization_id', organizationId)
      .eq('id', saleId)
      .single();

    if (error) {
      console.error('Error fetching sale:', error);
      return { error: error.message };
    }

    // Fetch cashier profile if created_by exists
    let cashier: Cashier | null = null;
    if (data.created_by) {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, avatar_url')
        .eq('id', data.created_by)
        .single();

      if (!profileError && profileData) {
        cashier = profileData;
      }
    }

    return {
      sale: {
        ...data,
        cashier
      }
    };
  } catch (error: any) {
    console.error('Error in getSaleById:', error);
    return { error: error.message };
  }
};

/**
 * Get all sales for an organization
 */
export const getSales = async (
  organizationId: string,
  options?: {
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    startDate?: string;
    endDate?: string;
    status?: string;
    customerId?: string;
    searchQuery?: string;
  }
): Promise<{
  sales: Sale[];
  count: number;
  error?: string;
}> => {
  try {
    // Set default options
    const {
      limit = 50,
      offset = 0,
      sortBy = 'sale_date',
      sortOrder = 'desc',
      startDate,
      endDate,
      status,
      customerId,
      searchQuery
    } = options || {};

    // Build the query
    let query = supabase
      .from('sales')
      .select(`
        *,
        customer:customer_id(id, name, email, phone),
        items:sale_items(
          id,
          product_id,
          quantity,
          unit_price,
          total_amount
        )
      `, { count: 'exact' })
      .eq('organization_id', organizationId);

    // Apply filters
    if (startDate) {
      query = query.gte('sale_date', startDate);
    }

    if (endDate) {
      query = query.lte('sale_date', endDate);
    }

    if (status) {
      query = query.eq('status', status);
    }

    if (customerId) {
      query = query.eq('customer_id', customerId);
    }

    if (searchQuery) {
      query = query.or(`invoice_number.ilike.%${searchQuery}%,notes.ilike.%${searchQuery}%`);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    // Execute the query
    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching sales:', error);
      return { sales: [], count: 0, error: error.message };
    }

    if (!data || data.length === 0) {
      return {
        sales: [],
        count: count || 0
      };
    }

    // Get unique cashier IDs
    const cashierIds = [...new Set(data.map(sale => sale.created_by).filter(Boolean))];

    // Fetch cashier profiles
    let cashierProfiles: Record<string, Cashier> = {};
    if (cashierIds.length > 0) {
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, avatar_url')
        .in('id', cashierIds);

      if (!profilesError && profilesData) {
        cashierProfiles = profilesData.reduce((acc, profile) => {
          acc[profile.id] = profile;
          return acc;
        }, {} as Record<string, Cashier>);
      }
    }

    // Attach cashier data to sales
    const salesWithCashiers = data.map(sale => ({
      ...sale,
      cashier: cashierProfiles[sale.created_by] || null
    }));

    return {
      sales: salesWithCashiers,
      count: count || 0
    };
  } catch (error: any) {
    console.error('Error in getSales:', error);
    return {
      sales: [],
      count: 0,
      error: error.message
    };
  }
};
