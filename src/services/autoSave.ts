/**
 * Service for handling auto-save functionality
 */

/**
 * Save data to localStorage with a timestamp
 * @param key The key to save the data under
 * @param data The data to save
 */
export const saveToLocalStorage = <T extends unknown>(key: string, data: T): void => {
  try {
    const saveData = {
      data,
      timestamp: new Date().toISOString(),
    };
    localStorage.setItem(key, JSON.stringify(saveData));
  } catch (error) {
    console.error('Error saving data to localStorage:', error);
  }
};

/**
 * Load data from localStorage
 * @param key The key to load the data from
 * @returns The data and timestamp, or null if not found
 */
export const loadFromLocalStorage = <T extends unknown>(key: string): { data: T; timestamp: string } | null => {
  try {
    const savedData = localStorage.getItem(key);
    if (!savedData) return null;

    return JSON.parse(savedData);
  } catch (error) {
    console.error('Error loading data from localStorage:', error);
    return null;
  }
};

/**
 * Clear data from localStorage
 * @param key The key to clear
 */
export const clearFromLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Error clearing data from localStorage:', error);
  }
};

/**
 * Check if there is saved data for a key
 * @param key The key to check
 * @returns Whether there is saved data
 */
export const hasSavedData = (key: string): boolean => {
  try {
    return localStorage.getItem(key) !== null;
  } catch (error) {
    console.error('Error checking for saved data:', error);
    return false;
  }
};

/**
 * Format a timestamp as a relative time string (e.g., "2 minutes ago")
 * @param timestamp The timestamp to format
 * @returns A formatted string
 */
export const formatRelativeTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();

  // Convert to seconds
  const diffSec = Math.floor(diffMs / 1000);

  if (diffSec < 60) {
    return `${diffSec} second${diffSec !== 1 ? 's' : ''} ago`;
  }

  // Convert to minutes
  const diffMin = Math.floor(diffSec / 60);

  if (diffMin < 60) {
    return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
  }

  // Convert to hours
  const diffHour = Math.floor(diffMin / 60);

  if (diffHour < 24) {
    return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
  }

  // Convert to days
  const diffDay = Math.floor(diffHour / 24);

  return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;
};
