import { supabase } from '../lib/supabase';
import { Tag } from '../types/tagging.types';

/**
 * Get all tags for a product
 */
export const getProductTags = async (productId: string): Promise<{
  tags: Tag[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('get_entity_tags', {
      p_entity_type: 'product',
      p_entity_id: productId
    });

    if (error) {
      console.error('Error fetching product tags:', error);
      return { tags: [], error: error.message };
    }

    return { tags: data || [] };
  } catch (err: any) {
    console.error('Error in getProductTags:', err);
    return { tags: [], error: err.message };
  }
};

/**
 * Add a tag to a product
 */
export const addTagToProduct = async (productId: string, tagId: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('add_tag_to_entity', {
      p_tag_id: tagId,
      p_entity_type: 'product',
      p_entity_id: productId
    });

    if (error) {
      console.error('Error adding tag to product:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err: any) {
    console.error('Error in addTagToProduct:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Remove a tag from a product
 */
export const removeTagFromProduct = async (productId: string, tagId: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('remove_tag_from_entity', {
      p_tag_id: tagId,
      p_entity_type: 'product',
      p_entity_id: productId
    });

    if (error) {
      console.error('Error removing tag from product:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err: any) {
    console.error('Error in removeTagFromProduct:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Find products by tag
 */
export const findProductsByTag = async (tagId: string): Promise<{
  productIds: string[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('find_entities_by_tag', {
      p_tag_id: tagId,
      p_entity_type: 'product'
    });

    if (error) {
      console.error('Error finding products by tag:', error);
      return { productIds: [], error: error.message };
    }

    return { 
      productIds: data ? data.map(item => item.entity_id) : [] 
    };
  } catch (err: any) {
    console.error('Error in findProductsByTag:', err);
    return { productIds: [], error: err.message };
  }
};
