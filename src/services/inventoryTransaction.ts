import { supabase } from '../lib/supabase';
import { getOrCreatePcsUom } from './floatInventory';

export interface InventoryTransaction {
  id: string;
  organization_id: string;
  product_id: string;
  transaction_type: string;
  quantity: number;
  uom_id: string;
  reference_id: string | null;
  reference_type: string | null;
  notes: string | null;
  created_by: string;
  created_at: string;
}

/**
 * Automatically resolve floating inventory when new stock arrives
 * This implements Option 2: Automatic Resolution with Logging
 */
export const autoResolveFloatingInventory = async (
  organizationId: string,
  productId: string,
  receivedQuantity: number,
  userId: string,
  receiptReference: string
): Promise<{
  resolvedQuantity: number;
  remainingFloatQuantity: number;
  resolutionTransactions: string[];
  error?: string;
}> => {
  try {
    console.log(`🔄 Auto-resolving floating inventory for product ${productId}, received: ${receivedQuantity}`);

    // Get unresolved floating inventory for this product
    const { data: floatItems, error: floatError } = await supabase
      .from('float_inventory')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('product_id', productId)
      .eq('resolved', false)
      .order('created_at', { ascending: true }); // Resolve oldest first (FIFO)

    if (floatError) {
      throw new Error(`Error fetching floating inventory: ${floatError.message}`);
    }

    if (!floatItems || floatItems.length === 0) {
      console.log(`✅ No floating inventory to resolve for product ${productId}`);
      return {
        resolvedQuantity: 0,
        remainingFloatQuantity: 0,
        resolutionTransactions: []
      };
    }

    // Calculate total floating quantity
    const totalFloatQuantity = floatItems.reduce((sum, item) => sum + Math.abs(item.quantity), 0);
    console.log(`📊 Total floating quantity: ${totalFloatQuantity}, Received: ${receivedQuantity}`);

    // Determine how much we can resolve
    const quantityToResolve = Math.min(totalFloatQuantity, receivedQuantity);
    const remainingFloatQuantity = totalFloatQuantity - quantityToResolve;

    console.log(`🎯 Will resolve: ${quantityToResolve}, Remaining float: ${remainingFloatQuantity}`);

    if (quantityToResolve === 0) {
      return {
        resolvedQuantity: 0,
        remainingFloatQuantity: totalFloatQuantity,
        resolutionTransactions: []
      };
    }

    // Get or create PCS UoM for transactions
    const pcsUomId = await getOrCreatePcsUom(organizationId);
    const resolutionTransactions: string[] = [];
    let remainingToResolve = quantityToResolve;

    // Resolve floating inventory items (FIFO - oldest first)
    for (const floatItem of floatItems) {
      if (remainingToResolve <= 0) break;

      const floatQuantity = Math.abs(floatItem.quantity);
      const resolveAmount = Math.min(floatQuantity, remainingToResolve);

      console.log(`🔧 Resolving float item ${floatItem.id}: ${resolveAmount} of ${floatQuantity}`);

      // Create resolution transaction
      const { data: resolutionTransaction, error: transactionError } = await supabase
        .from('inventory_transactions')
        .insert({
          organization_id: organizationId,
          product_id: productId,
          transaction_type: 'float_resolution',
          quantity: resolveAmount,
          uom_id: pcsUomId,
          reference_id: floatItem.id,
          reference_type: 'float_inventory',
          notes: `🤖 AUTO-RESOLVED: Floating inventory resolved by receipt ${receiptReference}. Original sale: ${floatItem.sale_id}`,
          created_by: userId,
        })
        .select('id')
        .single();

      if (transactionError) {
        console.error(`❌ Error creating resolution transaction for float ${floatItem.id}:`, transactionError);
        continue; // Continue with other items
      }

      if (resolutionTransaction) {
        resolutionTransactions.push(resolutionTransaction.id);
      }

      // Update float inventory item
      const isFullyResolved = resolveAmount >= floatQuantity;
      const updateData: any = {
        resolved: isFullyResolved,
        resolved_at: new Date().toISOString(),
        resolution_type: 'automatic',
        resolution_notes: `Auto-resolved by receipt ${receiptReference}`,
        resolved_by: userId
      };

      // If partially resolved, update the quantity
      if (!isFullyResolved) {
        updateData.quantity = -(floatQuantity - resolveAmount); // Keep it negative
      }

      const { error: updateError } = await supabase
        .from('float_inventory')
        .update(updateData)
        .eq('id', floatItem.id);

      if (updateError) {
        console.error(`❌ Error updating float item ${floatItem.id}:`, updateError);
      } else {
        console.log(`✅ ${isFullyResolved ? 'Fully' : 'Partially'} resolved float item ${floatItem.id}`);
      }

      remainingToResolve -= resolveAmount;
    }

    console.log(`🎉 Auto-resolution complete: Resolved ${quantityToResolve}, Created ${resolutionTransactions.length} transactions`);

    return {
      resolvedQuantity: quantityToResolve,
      remainingFloatQuantity,
      resolutionTransactions
    };

  } catch (error: any) {
    console.error('❌ Error in autoResolveFloatingInventory:', error);
    return {
      resolvedQuantity: 0,
      remainingFloatQuantity: 0,
      resolutionTransactions: [],
      error: error.message
    };
  }
};

/**
 * Create an inventory transaction
 */
export const createInventoryTransaction = async (
  organizationId: string,
  userId: string,
  data: {
    productId: string;
    transactionType: string;
    quantity: number;
    uomId: string;
    referenceId?: string;
    referenceType?: string;
    notes?: string;
  }
): Promise<{
  transaction?: InventoryTransaction;
  autoResolution?: {
    resolvedQuantity: number;
    remainingFloatQuantity: number;
    resolutionTransactions: string[];
  };
  error?: string;
}> => {
  try {
    console.log('Creating inventory transaction:', data);

    let autoResolution;
    let enhancedNotes = data.notes || '';

    // 🔄 Auto-resolve floating inventory for receipt/purchase transactions
    if ((data.transactionType === 'receipt' || data.transactionType === 'purchase') && data.quantity > 0) {
      console.log(`🔄 Auto-resolving floating inventory for ${data.transactionType} transaction`);

      autoResolution = await autoResolveFloatingInventory(
        organizationId,
        data.productId,
        data.quantity,
        userId,
        data.referenceId || 'manual transaction'
      );

      if (autoResolution.error) {
        console.warn(`⚠️ Auto-resolution warning:`, autoResolution.error);
      } else if (autoResolution.resolvedQuantity > 0) {
        console.log(`✅ Auto-resolved ${autoResolution.resolvedQuantity} floating inventory`);
        enhancedNotes = enhancedNotes
          ? `${enhancedNotes} | 🤖 AUTO-RESOLVED ${autoResolution.resolvedQuantity} floating inventory`
          : `🤖 AUTO-RESOLVED ${autoResolution.resolvedQuantity} floating inventory`;
      }
    }

    // Try with the provided transaction type first
    let transactionData;
    let transactionError;

    try {
      const result = await supabase
        .from('inventory_transactions')
        .insert({
          organization_id: organizationId,
          product_id: data.productId,
          transaction_type: data.transactionType,
          quantity: data.quantity,
          uom_id: data.uomId,
          reference_id: data.referenceId || null,
          reference_type: data.referenceType || null,
          notes: enhancedNotes,
          created_by: userId,
        })
        .select()
        .single();

      transactionData = result.data;
      transactionError = result.error;
    } catch (err) {
      console.error('Error with primary transaction type, trying fallback:', err);

      // If that fails, try with 'purchase' as a fallback
      if (data.transactionType === 'receipt') {
        const fallbackResult = await supabase
          .from('inventory_transactions')
          .insert({
            organization_id: organizationId,
            product_id: data.productId,
            transaction_type: 'purchase', // Fallback type
            quantity: data.quantity,
            uom_id: data.uomId,
            reference_id: data.referenceId || null,
            reference_type: data.referenceType || null,
            notes: (data.notes || '') + ' (receipt transaction)',
            created_by: userId,
          })
          .select()
          .single();

        transactionData = fallbackResult.data;
        transactionError = fallbackResult.error;
      } else {
        // If it's not a receipt transaction, rethrow the error
        throw err;
      }
    }

    if (transactionError) {
      console.error('Error creating inventory transaction:', transactionError);
      return { error: transactionError.message };
    }

    return {
      transaction: transactionData,
      autoResolution
    };
  } catch (error: any) {
    console.error('Error in createInventoryTransaction:', error);
    return { error: error.message };
  }
};

/**
 * Get inventory transactions for a product
 */
export const getInventoryTransactionsByProduct = async (
  organizationId: string,
  productId: string
): Promise<{
  transactions: InventoryTransaction[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('inventory_transactions')
      .select(`
        *,
        product:product_id(id, name, sku),
        uom:uom_id(id, name, code)
      `)
      .eq('organization_id', organizationId)
      .eq('product_id', productId)
      .order('created_at', { ascending: false });

    if (error) {
      return { transactions: [], error: error.message };
    }

    return { transactions: data || [] };
  } catch (error: any) {
    return { transactions: [], error: error.message };
  }
};

/**
 * Get inventory transactions by reference
 */
export const getInventoryTransactionsByReference = async (
  organizationId: string,
  referenceType: string,
  referenceId: string
): Promise<{
  transactions: InventoryTransaction[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('inventory_transactions')
      .select(`
        *,
        product:product_id(id, name, sku),
        uom:uom_id(id, name, code)
      `)
      .eq('organization_id', organizationId)
      .eq('reference_type', referenceType)
      .eq('reference_id', referenceId)
      .order('created_at', { ascending: false });

    if (error) {
      return { transactions: [], error: error.message };
    }

    return { transactions: data || [] };
  } catch (error: any) {
    return { transactions: [], error: error.message };
  }
};

/**
 * Get a single inventory transaction by ID
 */
export const getInventoryTransactionById = async (
  organizationId: string,
  transactionId: string
): Promise<{
  transaction: any | null;
  error: string | null;
}> => {
  try {
    const { data, error } = await supabase
      .from('inventory_transactions')
      .select(`
        *,
        product:product_id(id, name, sku, cost_price),
        uom:uom_id(id, name, code)
      `)
      .eq('organization_id', organizationId)
      .eq('id', transactionId)
      .single();

    if (error) {
      console.error('Error fetching inventory transaction by ID:', error);
      return { transaction: null, error: error.message };
    }

    return {
      transaction: data,
      error: null
    };
  } catch (error: any) {
    console.error('Exception fetching inventory transaction by ID:', error);
    return {
      transaction: null,
      error: error.message
    };
  }
};

/**
 * Get all inventory transactions for an organization
 */
export const getInventoryTransactions = async (
  organizationId: string,
  options?: {
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    transactionType?: string;
    startDate?: string;
    endDate?: string;
    searchQuery?: string;
  }
): Promise<{
  transactions: InventoryTransaction[];
  count: number;
  error?: string;
}> => {
  try {
    // Set default options
    const {
      limit = 50,
      offset = 0,
      sortBy = 'created_at',
      sortOrder = 'desc',
      transactionType,
      startDate,
      endDate,
      searchQuery
    } = options || {};

    // Build the query
    let query = supabase
      .from('inventory_transactions')
      .select(`
        *,
        product:product_id(id, name, sku),
        uom:uom_id(id, name, code)
      `, { count: 'exact' })
      .eq('organization_id', organizationId);

    // Apply filters
    if (transactionType) {
      query = query.eq('transaction_type', transactionType);
    }

    if (startDate) {
      query = query.gte('created_at', startDate);
    }

    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    if (searchQuery) {
      // Search in related product name or SKU
      query = query.or(`product.name.ilike.%${searchQuery}%,product.sku.ilike.%${searchQuery}%`);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    // Execute the query
    const { data, error, count } = await query;

    if (error) {
      return { transactions: [], count: 0, error: error.message };
    }

    return {
      transactions: data || [],
      count: count || 0
    };
  } catch (error: any) {
    return {
      transactions: [],
      count: 0,
      error: error.message
    };
  }
};

/**
 * Create inventory transactions from receipt items
 * This is a manual way to create transactions if the trigger doesn't work
 */
export const createTransactionsFromReceiptItems = async (
  organizationId: string,
  receiptId: string,
  userId: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // Get the receipt
    const { data: receipt, error: receiptError } = await supabase
      .from('inventory_receipts')
      .select('*')
      .eq('id', receiptId)
      .eq('organization_id', organizationId)
      .single();

    if (receiptError) {
      return { success: false, error: receiptError.message };
    }

    // Get all receipt items
    const { data: items, error: itemsError } = await supabase
      .from('inventory_receipt_items')
      .select('*')
      .eq('inventory_receipt_id', receiptId);

    if (itemsError) {
      return { success: false, error: itemsError.message };
    }

    if (!items || items.length === 0) {
      return { success: false, error: 'No receipt items found' };
    }

    // Create transactions for each item
    for (const item of items) {
      // Check if a transaction already exists for this item
      const { data: existingTransactions } = await supabase
        .from('inventory_transactions')
        .select('id')
        .eq('reference_id', item.id)
        .eq('reference_type', 'inventory_receipt_item');

      if (existingTransactions && existingTransactions.length > 0) {
        console.log(`Transaction already exists for receipt item ${item.id}`);
        continue;
      }

      // Create the transaction
      // Try with 'receipt' first, then fall back to 'purchase' if needed
      console.log(`Creating transaction for item ${item.id}: Product ${item.product_id}, Quantity: ${item.base_quantity}`);

      let transactionError;

      try {
        // 🔄 STEP 1: Auto-resolve floating inventory before creating receipt transaction
        console.log(`🔄 Checking for floating inventory to resolve for product ${item.product_id}`);

        const autoResolution = await autoResolveFloatingInventory(
          organizationId,
          item.product_id,
          item.base_quantity,
          userId,
          receipt.receipt_number || 'unknown receipt'
        );

        if (autoResolution.error) {
          console.warn(`⚠️ Auto-resolution warning for product ${item.product_id}:`, autoResolution.error);
        } else if (autoResolution.resolvedQuantity > 0) {
          console.log(`✅ Auto-resolved ${autoResolution.resolvedQuantity} floating inventory for product ${item.product_id}`);
        }

        // 🔄 STEP 2: Create the receipt transaction
        // Note: We still record the full received quantity, but floating inventory is now resolved
        const receiptNotes = autoResolution.resolvedQuantity > 0
          ? `Inventory receipt from ${receipt.receipt_number || 'unknown receipt'} | 🤖 AUTO-RESOLVED ${autoResolution.resolvedQuantity} floating inventory`
          : `Inventory receipt from ${receipt.receipt_number || 'unknown receipt'}`;

        const result = await supabase
          .from('inventory_transactions')
          .insert({
            organization_id: organizationId,
            product_id: item.product_id,
            transaction_type: 'receipt',
            quantity: item.base_quantity,
            uom_id: item.uom_id,
            reference_id: item.id,
            reference_type: 'inventory_receipt_item',
            notes: receiptNotes,
            created_by: userId,
          });

        transactionError = result.error;

        if (transactionError) {
          console.error('Error with receipt transaction type, trying fallback:', transactionError);

          // If that fails, try with 'purchase' as a fallback
          const fallbackNotes = autoResolution.resolvedQuantity > 0
            ? `Inventory receipt from ${receipt.receipt_number || 'unknown receipt'} (receipt transaction) | 🤖 AUTO-RESOLVED ${autoResolution.resolvedQuantity} floating inventory`
            : `Inventory receipt from ${receipt.receipt_number || 'unknown receipt'} (receipt transaction)`;

          const fallbackResult = await supabase
            .from('inventory_transactions')
            .insert({
              organization_id: organizationId,
              product_id: item.product_id,
              transaction_type: 'purchase', // Fallback type
              quantity: item.base_quantity,
              uom_id: item.uom_id,
              reference_id: item.id,
              reference_type: 'inventory_receipt_item',
              notes: fallbackNotes,
              created_by: userId,
            });

          transactionError = fallbackResult.error;
        }
      } catch (err) {
        console.error('Exception creating transaction:', err);
        transactionError = { message: err.message };
      }

      if (transactionError) {
        console.error(`Error creating transaction for item ${item.id}:`, transactionError);
        // Continue with other items even if one fails
      }
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in createTransactionsFromReceiptItems:', error);
    return { success: false, error: error.message };
  }
};
