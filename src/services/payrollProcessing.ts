import { supabase } from '../lib/supabase';
import {
  PayrollItemStatus,
  PayrollPeriodStatus,
  ProcessPayrollOptions
} from '../types/payroll';
import { processEmployeePayroll, calculate13thMonthPay } from './payrollCalculation';
import { getPayrollPeriodById, updatePayrollPeriod } from './payroll';
import { getEmployees } from './employee';

/**
 * Process payroll for a specific period and employees
 * @param organizationId Organization ID
 * @param periodId Payroll period ID
 * @param employeeIds Employee IDs to process
 * @param options Processing options
 * @returns Success status and error if any
 */
export const processPayroll = async (
  organizationId: string,
  periodId: string,
  employeeIds: string[],
  options?: ProcessPayrollOptions
): Promise<{
  success: boolean;
  processedItems: number;
  error?: string;
}> => {
  try {
    // Get payroll period
    const { period, error: periodError } = await getPayrollPeriodById(organizationId, periodId);

    if (periodError || !period) {
      throw new Error(periodError || 'Payroll period not found');
    }

    // Check if period is in draft status
    if (period.status !== PayrollPeriodStatus.DRAFT && !options?.forceProcess) {
      throw new Error('Payroll period is not in draft status');
    }

    // Update period status to processing
    await updatePayrollPeriod(organizationId, periodId, {
      status: PayrollPeriodStatus.PROCESSING
    });

    // Delete existing payroll items if reprocessing
    if (options?.reprocess) {
      await supabase
        .from('payroll_items')
        .delete()
        .eq('organization_id', organizationId)
        .eq('payroll_period_id', periodId)
        .in('employee_id', employeeIds);
    }

    // Process each employee
    let processedItems = 0;
    const errors: string[] = [];

    for (const employeeId of employeeIds) {
      try {
        // Calculate payroll for employee
        const result = await processEmployeePayroll(organizationId, periodId, employeeId);

        // Create payroll item with time entry details
        // Create a base payload without the government contributions
        const payrollItemData = {
          organization_id: organizationId,
          payroll_period_id: periodId,
          employee_id: employeeId,
          basic_pay: result.basicPay,
          gross_pay: result.grossPay,
          net_pay: result.netPay,
          total_allowances: result.totalAllowances,
          total_deductions: result.totalDeductions,
          // Include time entry details if available
          regular_pay: result.timeEntryDetails?.regularPay || 0,
          overtime_pay: result.timeEntryDetails?.overtimePay || 0,
          rest_day_pay: result.timeEntryDetails?.restDayPay || 0,
          holiday_pay: result.timeEntryDetails?.holidayPay || 0,
          night_differential_pay: result.timeEntryDetails?.nightDifferentialPay || 0,
          total_regular_hours: result.timeEntryDetails?.totalRegularHours || 0,
          total_overtime_hours: result.timeEntryDetails?.totalOvertimeHours || 0,
          total_rest_day_hours: result.timeEntryDetails?.totalRestDayHours || 0,
          total_holiday_hours: result.timeEntryDetails?.totalHolidayHours || 0,
          total_night_diff_hours: result.timeEntryDetails?.totalNightDiffHours || 0,
          is_thirteenth_month: period.is_thirteenth_month || false,
          status: PayrollItemStatus.CALCULATED
        };

        let payrollItemId: string | undefined;

        // Try to add the government contributions
        try {
          // Apply employee contribution preferences if they exist
          const { applyEmployeeContributionPreferences } = await import('./employeeContributionPreferences');

          const { contributions: finalContributions } = await applyEmployeeContributionPreferences(
            organizationId,
            employeeId,
            {
              sss_contribution: result.sssContribution.employeeShare,
              philhealth_contribution: result.philhealthContribution.employeeShare,
              pagibig_contribution: result.pagibigContribution.employeeShare,
              withholding_tax: result.withholdingTax,
            }
          );

          // Recalculate totals if preferences were applied
          let finalTaxableIncome = result.taxableIncome;
          let finalNetPay = result.netPay;
          let finalTotalDeductions = result.totalDeductions;

          if (finalContributions.contributions_manually_edited) {
            // Recalculate taxable income with new contributions
            finalTaxableIncome = result.grossPay - finalContributions.sss_contribution -
                                finalContributions.philhealth_contribution - finalContributions.pagibig_contribution;

            // Recalculate total deductions
            const governmentDeductions = finalContributions.sss_contribution + finalContributions.philhealth_contribution +
                                       finalContributions.pagibig_contribution + finalContributions.withholding_tax;
            const otherDeductions = result.totalDeductions - (result.sssContribution.employeeShare +
                                  result.philhealthContribution.employeeShare + result.pagibigContribution.employeeShare + result.withholdingTax);
            finalTotalDeductions = governmentDeductions + otherDeductions;

            // Recalculate net pay
            finalNetPay = result.grossPay - finalTotalDeductions;
          }

          // Add government contributions to the payload
          const fullPayrollItemData = {
            ...payrollItemData,
            taxable_income: finalTaxableIncome,
            withholding_tax: finalContributions.withholding_tax,
            sss_contribution: finalContributions.sss_contribution,
            philhealth_contribution: finalContributions.philhealth_contribution,
            pagibig_contribution: finalContributions.pagibig_contribution,
            total_deductions: finalTotalDeductions,
            net_pay: finalNetPay,
            contributions_manually_edited: finalContributions.contributions_manually_edited,
            sss_contribution_override: finalContributions.sss_contribution_override,
            philhealth_contribution_override: finalContributions.philhealth_contribution_override,
            pagibig_contribution_override: finalContributions.pagibig_contribution_override,
            withholding_tax_override: finalContributions.withholding_tax_override,
          };

          // Try to insert with government contributions
          const { data, error } = await supabase
            .from('payroll_items')
            .insert(fullPayrollItemData)
            .select()
            .single();

          if (error) {
            // If there's an error, it might be because the columns don't exist
            console.error('Error inserting payroll item with government contributions:', error);
            throw error;
          }

          payrollItemId = data.id;
          processedItems++;
        } catch (err) {
          console.warn('Falling back to basic payroll item without government contributions');

          // Fall back to inserting without government contributions
          const { data, error } = await supabase
            .from('payroll_items')
            .insert(payrollItemData)
            .select()
            .single();

          if (error) {
            throw new Error(error.message);
          }

          payrollItemId = data.id;
          processedItems++;
        }

        // Create payroll allowances
        if (result.allowances.length > 0 && payrollItemId) {
          const allowancesData = result.allowances.map(allowance => ({
            organization_id: organizationId,
            payroll_item_id: payrollItemId,
            name: allowance.name,
            amount: allowance.amount,
            type: allowance.type
          }));

          const { error: allowancesError } = await supabase
            .from('payroll_allowances')
            .insert(allowancesData);

          if (allowancesError) {
            console.error('Error creating payroll allowances:', allowancesError);
          }
        }

        // Create payroll deductions
        if (result.deductions.length > 0 && payrollItemId) {
          const deductionsData = result.deductions.map(deduction => ({
            organization_id: organizationId,
            payroll_item_id: payrollItemId,
            name: deduction.name,
            amount: deduction.amount,
            type: deduction.type
          }));

          const { error: deductionsError } = await supabase
            .from('payroll_deductions')
            .insert(deductionsData);

          if (deductionsError) {
            console.error('Error creating payroll deductions:', deductionsError);
          }
        }
      } catch (err: any) {
        console.error(`Error processing payroll for employee ${employeeId}:`, err);
        errors.push(`Employee ${employeeId}: ${err.message}`);

        // Create error payroll item if requested
        if (options?.createErrorItems) {
          await supabase
            .from('payroll_items')
            .insert({
              organization_id: organizationId,
              payroll_period_id: periodId,
              employee_id: employeeId,
              basic_pay: 0,
              gross_pay: 0,
              net_pay: 0,
              total_allowances: 0,
              total_deductions: 0,
              taxable_income: 0,
              withholding_tax: 0,
              sss_contribution: 0,
              philhealth_contribution: 0,
              pagibig_contribution: 0,
              status: PayrollItemStatus.ERROR,
              error_message: err.message
            });
        }
      }
    }

    // If all employees were processed successfully, update period status
    if (processedItems === employeeIds.length) {
      await updatePayrollPeriod(organizationId, periodId, {
        status: PayrollPeriodStatus.PROCESSING
      });
    }

    return {
      success: errors.length === 0,
      processedItems,
      error: errors.length > 0 ? errors.join('; ') : undefined
    };
  } catch (error: any) {
    console.error('Error processing payroll:', error);

    // Revert period status to draft if there was an error
    await updatePayrollPeriod(organizationId, periodId, {
      status: PayrollPeriodStatus.DRAFT
    });

    return {
      success: false,
      processedItems: 0,
      error: error.message
    };
  }
};

/**
 * Approve a payroll period
 * @param organizationId Organization ID
 * @param periodId Payroll period ID
 * @returns Success status and error if any
 */
export const approvePayrollPeriod = async (
  organizationId: string,
  periodId: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // Get payroll period
    const { period, error: periodError } = await getPayrollPeriodById(organizationId, periodId, true);

    if (periodError || !period) {
      throw new Error(periodError || 'Payroll period not found');
    }

    // Check if period is in processing status
    if (period.status !== PayrollPeriodStatus.PROCESSING) {
      throw new Error('Payroll period is not in processing status');
    }

    // Check if all payroll items are calculated
    if (period.payroll_items) {
      const hasErrorItems = period.payroll_items.some(item =>
        item.status === PayrollItemStatus.ERROR || item.status === PayrollItemStatus.DRAFT
      );

      if (hasErrorItems) {
        throw new Error('Some payroll items have errors or are in draft status');
      }
    }

    // Update all payroll items to approved status
    const { error: itemsError } = await supabase
      .from('payroll_items')
      .update({ status: PayrollItemStatus.APPROVED })
      .eq('organization_id', organizationId)
      .eq('payroll_period_id', periodId);

    if (itemsError) {
      throw new Error(`Failed to update payroll items: ${itemsError.message}`);
    }

    // Update period status to approved
    const { error } = await updatePayrollPeriod(organizationId, periodId, {
      status: PayrollPeriodStatus.APPROVED
    });

    if (error) {
      throw new Error(error);
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error approving payroll period:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Mark a payroll period as paid
 * @param organizationId Organization ID
 * @param periodId Payroll period ID
 * @returns Success status and error if any
 */
export const markPayrollPeriodAsPaid = async (
  organizationId: string,
  periodId: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // Get payroll period
    const { period, error: periodError } = await getPayrollPeriodById(organizationId, periodId);

    if (periodError || !period) {
      throw new Error(periodError || 'Payroll period not found');
    }

    // Check if period is in approved status
    if (period.status !== PayrollPeriodStatus.APPROVED) {
      throw new Error('Payroll period is not in approved status');
    }

    // Update all payroll items to paid status
    const { error: itemsError } = await supabase
      .from('payroll_items')
      .update({
        status: PayrollItemStatus.PAID,
        payment_date: new Date().toISOString()
      })
      .eq('organization_id', organizationId)
      .eq('payroll_period_id', periodId);

    if (itemsError) {
      throw new Error(`Failed to update payroll items: ${itemsError.message}`);
    }

    // Update period status to paid
    const { error } = await updatePayrollPeriod(organizationId, periodId, {
      status: PayrollPeriodStatus.PAID,
      actual_payment_date: new Date().toISOString()
    });

    if (error) {
      throw new Error(error);
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error marking payroll period as paid:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Process 13th month pay for all employees
 * @param organizationId Organization ID
 * @param year Year to calculate 13th month pay for
 * @returns Success status and error if any
 */
export const process13thMonthPay = async (
  organizationId: string,
  year: number
): Promise<{
  success: boolean;
  periodId?: string;
  error?: string;
}> => {
  try {
    // Create a new payroll period for 13th month pay
    const periodName = `13th Month Pay - ${year}`;
    const startDate = `${year}-01-01`;
    const endDate = `${year}-12-31`;
    const paymentDate = `${year}-12-15`; // Standard payment date for 13th month pay

    // Check if a 13th month pay period already exists for this year
    const { data: existingPeriods } = await supabase
      .from('payroll_periods')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('name', periodName)
      .eq('is_thirteenth_month', true);

    if (existingPeriods && existingPeriods.length > 0) {
      return {
        success: false,
        error: `A 13th month pay period for ${year} already exists`
      };
    }

    // Create the payroll period
    const { data: periodData, error: periodError } = await supabase
      .from('payroll_periods')
      .insert({
        organization_id: organizationId,
        name: periodName,
        start_date: startDate,
        end_date: endDate,
        payment_date: paymentDate,
        status: PayrollPeriodStatus.DRAFT,
        is_thirteenth_month: true
      })
      .select()
      .single();

    if (periodError || !periodData) {
      throw new Error(periodError?.message || 'Failed to create 13th month pay period');
    }

    const periodId = periodData.id;

    // Get all active employees
    const { employees, error: employeesError } = await getEmployees(organizationId, { isActive: true });

    if (employeesError || !employees) {
      throw new Error(employeesError || 'Failed to get employees');
    }

    // Process 13th month pay for each employee
    for (const employee of employees) {
      try {
        // Calculate 13th month pay
        const thirteenthMonthPay = await calculate13thMonthPay(organizationId, employee.id, year);

        if (thirteenthMonthPay <= 0) {
          continue;
        }

        // Create base payroll item data for 13th month pay
        const basePayrollItemData = {
          organization_id: organizationId,
          payroll_period_id: periodId,
          employee_id: employee.id,
          basic_pay: thirteenthMonthPay,
          gross_pay: thirteenthMonthPay,
          net_pay: thirteenthMonthPay, // No deductions for 13th month pay up to ₱90,000
          total_allowances: 0,
          total_deductions: 0,
          is_thirteenth_month: true,
          status: PayrollItemStatus.CALCULATED
        };

        try {
          // Try to insert with government contributions
          const fullPayrollItemData = {
            ...basePayrollItemData,
            taxable_income: 0, // 13th month pay is tax-exempt up to ₱90,000
            withholding_tax: 0,
            sss_contribution: 0,
            philhealth_contribution: 0,
            pagibig_contribution: 0,
          };

          await supabase
            .from('payroll_items')
            .insert(fullPayrollItemData);
        } catch (err) {
          console.warn('Falling back to basic 13th month payroll item without government contributions');

          // Fall back to inserting without government contributions
          await supabase
            .from('payroll_items')
            .insert(basePayrollItemData);
        }
      } catch (err: any) {
        console.error(`Error processing 13th month pay for employee ${employee.id}:`, err);
      }
    }

    // Update period status to processing
    await updatePayrollPeriod(organizationId, periodId, {
      status: PayrollPeriodStatus.PROCESSING
    });

    return {
      success: true,
      periodId
    };
  } catch (error: any) {
    console.error('Error processing 13th month pay:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
