/**
 * Employee Import Service
 * Handles bulk import of employees from CSV files
 */

import { supabase } from '../lib/supabase';
import { parseAndValidateCSV, CSVValidationRule, ParsedCSVRow } from '../utils/csvParser';
import { createEmployee } from './employee';

export interface EmployeeImportRow {
  employee_number?: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  email?: string;
  phone?: string;
  date_of_birth?: string;
  gender?: string;
  marital_status?: string;
  nationality?: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  department?: string;
  position?: string;
  employment_type?: string;
  hire_date?: string;
  status?: string;
  notes?: string;
}

export interface EmployeeImportResult {
  success: boolean;
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: string[];
  warnings: string[];
  createdEmployees: any[];
  createdItems: any[]; // Alias for compatibility with GenericImport
}

export interface EmployeeImportPreview {
  isValid: boolean;
  headers: string[];
  sampleData: ParsedCSVRow[];
  errors: string[];
  warnings: string[];
  totalRows: number;
}

/**
 * Employee import validation rules
 */
export const EMPLOYEE_IMPORT_RULES: CSVValidationRule[] = [
  {
    field: 'first_name',
    required: true,
    type: 'string',
    customValidator: (value) => {
      if (typeof value === 'string' && value.length > 100) {
        return 'First name must be 100 characters or less';
      }
      return null;
    }
  },
  {
    field: 'last_name',
    required: true,
    type: 'string',
    customValidator: (value) => {
      if (typeof value === 'string' && value.length > 100) {
        return 'Last name must be 100 characters or less';
      }
      return null;
    }
  },
  {
    field: 'email',
    type: 'email'
  },
  {
    field: 'date_of_birth',
    type: 'string',
    customValidator: (value) => {
      if (value && typeof value === 'string') {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          return 'Date of birth must be a valid date (YYYY-MM-DD format)';
        }
      }
      return null;
    }
  },
  {
    field: 'hire_date',
    type: 'string',
    customValidator: (value) => {
      if (value && typeof value === 'string') {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          return 'Hire date must be a valid date (YYYY-MM-DD format)';
        }
      }
      return null;
    }
  },
  {
    field: 'gender',
    type: 'string',
    customValidator: (value) => {
      if (value && typeof value === 'string') {
        const validGenders = ['male', 'female', 'other', 'prefer_not_to_say'];
        if (!validGenders.includes(value.toLowerCase())) {
          return `Gender must be one of: ${validGenders.join(', ')}`;
        }
      }
      return null;
    }
  },
  {
    field: 'marital_status',
    type: 'string',
    customValidator: (value) => {
      if (value && typeof value === 'string') {
        const validStatuses = ['single', 'married', 'divorced', 'widowed', 'separated'];
        if (!validStatuses.includes(value.toLowerCase())) {
          return `Marital status must be one of: ${validStatuses.join(', ')}`;
        }
      }
      return null;
    }
  },
  {
    field: 'status',
    type: 'string',
    customValidator: (value) => {
      if (value && typeof value === 'string') {
        const validStatuses = ['active', 'on_leave', 'terminated', 'resigned', 'retired'];
        if (!validStatuses.includes(value.toLowerCase())) {
          return `Status must be one of: ${validStatuses.join(', ')}`;
        }
      }
      return null;
    }
  }
];

/**
 * Expected CSV headers for employee import
 */
export const EMPLOYEE_IMPORT_HEADERS = [
  'employee_number',
  'first_name',
  'middle_name',
  'last_name',
  'email',
  'phone',
  'date_of_birth',
  'gender',
  'marital_status',
  'nationality',
  'address',
  'city',
  'state',
  'postal_code',
  'country',
  'emergency_contact_name',
  'emergency_contact_phone',
  'emergency_contact_relationship',
  'department',
  'position',
  'employment_type',
  'hire_date',
  'status',
  'notes'
];

/**
 * Preview CSV file before import
 */
export const previewEmployeeImport = (csvText: string): EmployeeImportPreview => {
  const result = parseAndValidateCSV(csvText, EMPLOYEE_IMPORT_RULES);
  
  return {
    isValid: result.success,
    headers: result.headers,
    sampleData: result.data.slice(0, 5), // Show first 5 rows as preview
    errors: result.errors,
    warnings: result.warnings,
    totalRows: result.data.length
  };
};

/**
 * Get or create department by name
 */
const getOrCreateDepartment = async (organizationId: string, departmentName: string, userId: string) => {
  if (!departmentName || departmentName.trim() === '') {
    return null;
  }

  const trimmedName = departmentName.trim();

  try {
    // First, try to find existing department
    const { data: existingDepartment, error: findError } = await supabase
      .from('departments')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('name', trimmedName)
      .maybeSingle();

    if (findError) {
      console.error('Error finding department:', findError);
      return null;
    }

    if (existingDepartment) {
      return existingDepartment.id;
    }

    // Create new department
    const { data: newDepartment, error: createError } = await supabase
      .from('departments')
      .insert({
        organization_id: organizationId,
        name: trimmedName,
        description: `Auto-created during employee import`
      })
      .select('id')
      .single();

    if (createError) {
      console.error('Error creating department:', createError);
      return null;
    }

    return newDepartment.id;
  } catch (error) {
    console.error('Unexpected error in getOrCreateDepartment:', error);
    return null;
  }
};

/**
 * Get or create job position by title
 */
const getOrCreateJobPosition = async (organizationId: string, positionTitle: string, userId: string) => {
  if (!positionTitle || positionTitle.trim() === '') {
    return null;
  }

  const trimmedTitle = positionTitle.trim();

  try {
    // First, try to find existing position
    const { data: existingPosition, error: findError } = await supabase
      .from('job_positions')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('title', trimmedTitle)
      .maybeSingle();

    if (findError) {
      console.error('Error finding job position:', findError);
      return null;
    }

    if (existingPosition) {
      return existingPosition.id;
    }

    // Create new job position
    const { data: newPosition, error: createError } = await supabase
      .from('job_positions')
      .insert({
        organization_id: organizationId,
        title: trimmedTitle,
        description: `Auto-created during employee import`
      })
      .select('id')
      .single();

    if (createError) {
      console.error('Error creating job position:', createError);
      return null;
    }

    return newPosition.id;
  } catch (error) {
    console.error('Unexpected error in getOrCreateJobPosition:', error);
    return null;
  }
};

/**
 * Get or create employment type by name
 */
const getOrCreateEmploymentType = async (organizationId: string, typeName: string, userId: string) => {
  if (!typeName || typeName.trim() === '') {
    return null;
  }

  const trimmedName = typeName.trim();

  try {
    // First, try to find existing employment type
    const { data: existingType, error: findError } = await supabase
      .from('employment_types')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('name', trimmedName)
      .maybeSingle();

    if (findError) {
      console.error('Error finding employment type:', findError);
      return null;
    }

    if (existingType) {
      return existingType.id;
    }

    // Create new employment type
    const { data: newType, error: createError } = await supabase
      .from('employment_types')
      .insert({
        organization_id: organizationId,
        name: trimmedName,
        description: `Auto-created during employee import`
      })
      .select('id')
      .single();

    if (createError) {
      console.error('Error creating employment type:', createError);
      return null;
    }

    return newType.id;
  } catch (error) {
    console.error('Unexpected error in getOrCreateEmploymentType:', error);
    return null;
  }
};

/**
 * Check for duplicate employees by employee number
 */
const checkDuplicateEmployees = async (
  organizationId: string,
  employees: EmployeeImportRow[]
): Promise<string[]> => {
  const employeeNumbers = employees.filter(e => e.employee_number).map(e => e.employee_number!);
  const duplicates: string[] = [];

  if (employeeNumbers.length > 0) {
    const { data: existingEmployees } = await supabase
      .from('employees')
      .select('employee_number')
      .eq('organization_id', organizationId)
      .in('employee_number', employeeNumbers);

    if (existingEmployees) {
      duplicates.push(...existingEmployees.map(e => e.employee_number).filter(Boolean));
    }
  }

  return duplicates;
};

/**
 * Import employees from CSV
 */
export const importEmployees = async (
  organizationId: string,
  csvText: string,
  userId: string,
  skipDuplicates: boolean = true
): Promise<EmployeeImportResult> => {
  const result = parseAndValidateCSV(csvText, EMPLOYEE_IMPORT_RULES);

  if (!result.success) {
    return {
      success: false,
      totalRows: result.data.length,
      successCount: 0,
      errorCount: result.data.length,
      errors: result.errors,
      warnings: result.warnings,
      createdEmployees: [],
      createdItems: []
    };
  }

  const employees = result.data as EmployeeImportRow[];
  const errors: string[] = [...result.errors];
  const warnings: string[] = [...result.warnings];
  const createdEmployees: any[] = [];

  // Check for duplicates
  const duplicateEmployeeNumbers = await checkDuplicateEmployees(organizationId, employees);

  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < employees.length; i++) {
    const employee = employees[i];
    const rowNum = i + 2; // +2 because row 1 is headers and we're 0-indexed

    try {
      // Check for duplicate employee number
      if (employee.employee_number && duplicateEmployeeNumbers.includes(employee.employee_number)) {
        if (skipDuplicates) {
          warnings.push(`Row ${rowNum}: Skipped - Employee number '${employee.employee_number}' already exists`);
          continue;
        } else {
          errors.push(`Row ${rowNum}: Employee number '${employee.employee_number}' already exists`);
          errorCount++;
          continue;
        }
      }

      // Get or create related entities
      let departmentId = null;
      if (employee.department && employee.department.trim()) {
        departmentId = await getOrCreateDepartment(organizationId, employee.department, userId);
      }

      let positionId = null;
      if (employee.position && employee.position.trim()) {
        positionId = await getOrCreateJobPosition(organizationId, employee.position, userId);
      }

      let employmentTypeId = null;
      if (employee.employment_type && employee.employment_type.trim()) {
        employmentTypeId = await getOrCreateEmploymentType(organizationId, employee.employment_type, userId);
      }

      // Prepare employee data
      const employeeData = {
        employee_number: employee.employee_number || null,
        first_name: employee.first_name,
        middle_name: employee.middle_name || null,
        last_name: employee.last_name,
        email: employee.email || null,
        phone: employee.phone || null,
        date_of_birth: employee.date_of_birth || null,
        gender: employee.gender?.toLowerCase() || null,
        marital_status: employee.marital_status?.toLowerCase() || null,
        nationality: employee.nationality || null,
        address: employee.address || null,
        city: employee.city || null,
        state: employee.state || null,
        postal_code: employee.postal_code || null,
        country: employee.country || null,
        emergency_contact_name: employee.emergency_contact_name || null,
        emergency_contact_phone: employee.emergency_contact_phone || null,
        emergency_contact_relationship: employee.emergency_contact_relationship || null,
        department_id: departmentId,
        position_id: positionId,
        employment_type_id: employmentTypeId,
        hire_date: employee.hire_date || null,
        status: employee.status?.toLowerCase() || 'active',
        is_active: true,
        notes: employee.notes || null
      };

      // Create employee
      const createResult = await createEmployee(organizationId, employeeData);

      if (createResult.employee && !createResult.error) {
        createdEmployees.push(createResult.employee);
        successCount++;
      } else {
        const errorMsg = createResult.error || 'Failed to create employee';
        errors.push(`Row ${rowNum}: ${errorMsg}`);
        errorCount++;
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Row ${rowNum}: ${errorMsg}`);
      errorCount++;
    }
  }

  return {
    success: errorCount === 0,
    totalRows: employees.length,
    successCount,
    errorCount,
    errors,
    warnings,
    createdEmployees,
    createdItems: createdEmployees // Alias for compatibility
  };
};

/**
 * Download employee import template
 */
export const downloadEmployeeImportTemplate = (): void => {
  const headers = EMPLOYEE_IMPORT_HEADERS;
  const sampleData = [
    '"EMP001","John","M","Doe","<EMAIL>","555-0101","1990-01-15","male","single","American","123 Main St","New York","NY","10001","USA","Jane Doe","555-0102","spouse","Sales","Sales Manager","Full-time","2023-01-15","active","Experienced sales professional"',
    '"EMP002","Jane","","Smith","<EMAIL>","555-0103","1985-05-20","female","married","Canadian","456 Oak Ave","Los Angeles","CA","90210","USA","Bob Smith","555-0104","spouse","Marketing","Marketing Specialist","Full-time","2023-02-01","active","Creative marketing expert"',
    '"","Bob","R","Johnson","","555-0105","1992-08-10","male","single","British","789 Pine Rd","Chicago","IL","60601","USA","Mary Johnson","555-0106","mother","IT","Developer","Contract","2023-03-01","active","Skilled software developer"'
  ];

  const csvContent = [
    headers.map(h => `"${h}"`).join(','),
    ...sampleData
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', 'employee_import_template.csv');
  link.style.visibility = 'hidden';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};
