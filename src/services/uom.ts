import { supabase } from '../lib/supabase';
import { UnitOfMeasurement, ProductUom } from '../types/uom.types';

/**
 * Get all units of measurement for an organization
 */
export const getUnitsOfMeasurement = async (
  organizationId: string,
  options?: {
    isActive?: boolean;
    searchQuery?: string;
  }
): Promise<{
  uoms: UnitOfMeasurement[];
  error?: string;
}> => {
  try {
    let query = supabase
      .from('units_of_measurement')
      .select('*')
      .eq('organization_id', organizationId);

    // Apply filters
    if (options?.isActive !== undefined) {
      query = query.eq('is_active', options.isActive);
    }

    if (options?.searchQuery) {
      query = query.or(`name.ilike.%${options.searchQuery}%,code.ilike.%${options.searchQuery}%`);
    }

    // Order by name
    query = query.order('name', { ascending: true });

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching units of measurement:', error);
      return {
        uoms: [],
        error: error.message,
      };
    }

    return {
      uoms: data as UnitOfMeasurement[],
    };
  } catch (error: any) {
    console.error('Error in getUnitsOfMeasurement:', error);
    return {
      uoms: [],
      error: error.message,
    };
  }
};

/**
 * Get a single unit of measurement by ID
 */
export const getUnitOfMeasurementById = async (
  organizationId: string,
  uomId: string
): Promise<{
  uom?: UnitOfMeasurement;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('units_of_measurement')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('id', uomId)
      .single();

    if (error) {
      console.error('Error fetching unit of measurement:', error);
      return {
        error: error.message,
      };
    }

    return {
      uom: data as UnitOfMeasurement,
    };
  } catch (error: any) {
    console.error('Error in getUnitOfMeasurementById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new unit of measurement
 */
export const createUnitOfMeasurement = async (
  organizationId: string,
  uom: Omit<UnitOfMeasurement, 'id' | 'organization_id' | 'created_at' | 'updated_at'>
): Promise<{
  uom?: UnitOfMeasurement;
  error?: string;
}> => {
  try {
    console.log('Creating UoM with data:', { ...uom, organization_id: organizationId });

    const { data, error } = await supabase
      .from('units_of_measurement')
      .insert({
        ...uom,
        organization_id: organizationId,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating unit of measurement:', error);
      return {
        error: error.message,
      };
    }

    console.log('UoM created successfully:', data);
    return {
      uom: data as UnitOfMeasurement,
    };
  } catch (error: any) {
    console.error('Error in createUnitOfMeasurement:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update an existing unit of measurement
 */
export const updateUnitOfMeasurement = async (
  organizationId: string,
  uomId: string,
  updates: Partial<Omit<UnitOfMeasurement, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>
): Promise<{
  uom?: UnitOfMeasurement;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('units_of_measurement')
      .update(updates)
      .eq('organization_id', organizationId)
      .eq('id', uomId)
      .select()
      .single();

    if (error) {
      console.error('Error updating unit of measurement:', error);
      return {
        error: error.message,
      };
    }

    return {
      uom: data as UnitOfMeasurement,
    };
  } catch (error: any) {
    console.error('Error in updateUnitOfMeasurement:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Delete a unit of measurement
 */
export const deleteUnitOfMeasurement = async (
  organizationId: string,
  uomId: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('units_of_measurement')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', uomId);

    if (error) {
      console.error('Error deleting unit of measurement:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteUnitOfMeasurement:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
