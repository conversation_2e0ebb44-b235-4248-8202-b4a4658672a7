import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';
import { PurchaseRequestItemWithUom } from '../types/uom.types';

// Define types
export type PurchaseRequest = Database['public']['Tables']['purchase_requests']['Row'];
export type PurchaseRequestInsert = Database['public']['Tables']['purchase_requests']['Insert'];
export type PurchaseRequestUpdate = Database['public']['Tables']['purchase_requests']['Update'];

export type PurchaseRequestItem = Database['public']['Tables']['purchase_request_items']['Row'];
export type PurchaseRequestItemInsert = Database['public']['Tables']['purchase_request_items']['Insert'];
export type PurchaseRequestItemUpdate = Database['public']['Tables']['purchase_request_items']['Update'];

export type PurchaseRequestWithItems = PurchaseRequest & {
  items: PurchaseRequestItemWithUom[];
  requester_name?: string;
};

/**
 * Get all purchase requests for an organization
 */
export const getPurchaseRequests = async (
  organizationId: string,
  options?: {
    status?: string;
    searchQuery?: string;
  }
): Promise<{
  purchaseRequests: PurchaseRequestWithItems[];
  error?: string;
}> => {
  try {
    console.log('Fetching purchase requests for organization:', organizationId);

    // First, get the purchase requests without trying to join to auth.users
    let query = supabase
      .from('purchase_requests')
      .select(`
        *,
        items:purchase_request_items(
          *,
          product:product_id(*),
          uom:uom_id(*)
        )
      `)
      .eq('organization_id', organizationId);

    // Apply filters
    if (options?.status) {
      query = query.eq('status', options.status);
    }

    if (options?.searchQuery) {
      query = query.ilike('request_number', `%${options.searchQuery}%`);
    }

    // Order by created_at
    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching purchase requests:', error);
      return {
        purchaseRequests: [],
        error: error.message,
      };
    }

    if (!data || data.length === 0) {
      return {
        purchaseRequests: [],
      };
    }

    console.log('Found purchase requests:', data.length);

    // Get the requester information separately
    // We'll collect all requester IDs and fetch them in a single query
    const requesterIds = [...new Set(data.map(pr => pr.requester_id))];

    let requesterInfo: Record<string, { display_name?: string, email?: string }> = {};

    if (requesterIds.length > 0) {
      try {
        console.log('Fetching user profiles for requester IDs:', requesterIds);

        // Try to get user profiles
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, first_name, last_name')
          .in('id', requesterIds);

        console.log('Profiles query result:', { profiles, error: profilesError });

        // Try to get the current user's email if they're one of the requesters
        try {
          const { data: session } = await supabase.auth.getSession();
          console.log('Session check for requesters:', {
            sessionUserId: session?.session?.user?.id,
            requesterIds
          });

          if (session?.session?.user && requesterIds.includes(session.session.user.id)) {
            requesterInfo[session.session.user.id] = {
              ...requesterInfo[session.session.user.id],
              display_name: 'You'
            };
          }
        } catch (sessionErr) {
          console.warn('Could not check session:', sessionErr);
        }

        if (profiles && profiles.length > 0) {
          profiles.forEach(profile => {
            let displayName = `User ${profile.id.substring(0, 8)}...`;

            // Prefer first and last name if available
            if (profile.first_name && profile.last_name) {
              displayName = `${profile.first_name} ${profile.last_name}`;
            } else if (profile.first_name) {
              // Use just first name if that's all we have
              displayName = profile.first_name;
            }

            requesterInfo[profile.id] = {
              ...requesterInfo[profile.id],
              display_name: displayName
            };
          });
        }

        // If we still don't have info, use the user ID as a fallback
        requesterIds.forEach(id => {
          if (!requesterInfo[id]) {
            requesterInfo[id] = {
              display_name: `User ${id.substring(0, 6)}`,
              email: null
            };
          }
        });

        console.log('Final requester info:', requesterInfo);
      } catch (err) {
        console.warn('Could not fetch requester profiles:', err);
      }
    }

    // Transform the data
    const purchaseRequests = data.map((pr: any) => {
      const requester = requesterInfo[pr.requester_id] || {};
      return {
        ...pr,
        requester_name: requester.display_name || requester.email || 'Unknown User',
      };
    });

    return {
      purchaseRequests: purchaseRequests as PurchaseRequestWithItems[],
    };
  } catch (error: any) {
    console.error('Error in getPurchaseRequests:', error);
    return {
      purchaseRequests: [],
      error: error.message,
    };
  }
};

/**
 * Get a purchase request by ID
 */
export const getPurchaseRequestById = async (
  organizationId: string,
  purchaseRequestId: string
): Promise<{
  purchaseRequest?: PurchaseRequestWithItems;
  error?: string;
}> => {
  try {
    console.log('Fetching purchase request by ID:', purchaseRequestId);

    // First, get the purchase request without trying to join to auth.users
    const { data, error } = await supabase
      .from('purchase_requests')
      .select(`
        *,
        items:purchase_request_items(
          *,
          product:product_id(*),
          uom:uom_id(*)
        )
      `)
      .eq('organization_id', organizationId)
      .eq('id', purchaseRequestId)
      .single();

    if (error) {
      console.error('Error fetching purchase request:', error);
      return {
        error: error.message,
      };
    }

    // Get the requester information separately
    let requesterName = `User ${data.requester_id.substring(0, 6)}`;

    try {
      console.log('Fetching requester profile for ID:', data.requester_id);

      // Try to get user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, email')
        .eq('id', data.requester_id)
        .single();

      console.log('Profile query result:', { profile, error: profileError });

      if (profile) {
        // If we have a profile with first and last name, use that
        if (profile.first_name && profile.last_name) {
          requesterName = `${profile.first_name} ${profile.last_name}`;
        } else if (profile.first_name) {
          // If we only have first name
          requesterName = profile.first_name;
        }
        // Otherwise keep the default (User ID)
      } else {
        // Try to get the user from the current session (if it's the same user)
        try {
          const { data: session } = await supabase.auth.getSession();
          console.log('Session check:', {
            sessionUserId: session?.session?.user?.id,
            requesterId: data.requester_id
          });

          if (session?.session?.user?.id === data.requester_id) {
            requesterName = 'You';
          }
        } catch (sessionErr) {
          console.warn('Could not check session:', sessionErr);
        }
      }
    } catch (err) {
      console.warn('Could not fetch requester profile:', err);
    }

    // Transform the data
    const purchaseRequest = {
      ...data,
      requester_name: requesterName,
    };

    return {
      purchaseRequest: purchaseRequest as PurchaseRequestWithItems,
    };
  } catch (error: any) {
    console.error('Error in getPurchaseRequestById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new purchase request with items
 */
export const createPurchaseRequest = async (
  purchaseRequest: PurchaseRequestInsert,
  items: Omit<PurchaseRequestItemInsert, 'purchase_request_id'>[]
): Promise<{
  purchaseRequest?: PurchaseRequestWithItems;
  error?: string;
}> => {
  try {
    console.log('Creating purchase request:', purchaseRequest);

    // Start a transaction
    const { data: prData, error: prError } = await supabase
      .from('purchase_requests')
      .insert(purchaseRequest)
      .select()
      .single();

    if (prError) {
      console.error('Error creating purchase request:', prError);
      return {
        error: prError.message,
      };
    }

    console.log('Purchase request created:', prData);

    // Add items
    if (items.length > 0) {
      const itemsWithRequestId = items.map(item => ({
        ...item,
        purchase_request_id: prData.id,
      }));

      console.log('Adding purchase request items:', itemsWithRequestId);

      // Check if the purchase_request_items table exists and has the expected columns
      const { data: tableInfo, error: checkError } = await supabase
        .from('purchase_request_items')
        .select('id, purchase_request_id, product_id, quantity, uom_id, base_quantity')
        .limit(1);

      console.log('Table check result:', { tableInfo, checkError });

      if (checkError && checkError.code === '42P01') { // 42P01 is the PostgreSQL error code for "relation does not exist"
        console.error('The purchase_request_items table does not exist:', checkError);
        return {
          error: 'The purchase request items table does not exist. Please run the database migrations.',
        };
      }

      const { error: itemsError } = await supabase
        .from('purchase_request_items')
        .insert(itemsWithRequestId);

      if (itemsError) {
        console.error('Error adding purchase request items:', itemsError);

        // If there's an RLS error, we should still return the purchase request
        if (itemsError.code === 'PGRST301') { // PGRST301 is the error code for RLS violations
          console.warn('RLS policy violation when adding items. Returning purchase request without items.');
          return {
            purchaseRequest: {
              ...prData,
              items: [],
              requester_name: 'Unknown' // We don't have the requester name here
            },
            error: 'Items could not be added due to permission issues. Please check your database policies.'
          };
        }

        return {
          error: itemsError.message,
        };
      }
    }

    // Verify that the purchase request was created
    const { data: verifyData, error: verifyError } = await supabase
      .from('purchase_requests')
      .select('*')
      .eq('id', prData.id)
      .single();

    console.log('Verification check:', { verifyData, verifyError });

    // Verify that the items were created
    const { data: verifyItems, error: verifyItemsError } = await supabase
      .from('purchase_request_items')
      .select('*')
      .eq('purchase_request_id', prData.id);

    console.log('Items verification check:', {
      itemsCount: verifyItems?.length || 0,
      verifyItemsError
    });

    // Get the complete purchase request with items
    return await getPurchaseRequestById(purchaseRequest.organization_id, prData.id);
  } catch (error: any) {
    console.error('Error in createPurchaseRequest:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update a purchase request
 * @param organizationId The ID of the organization
 * @param purchaseRequestId The ID of the purchase request to update
 * @param updates The updated purchase request data
 * @param options Optional configuration for handling items
 * @returns Object with success flag and any error message
 */
export const updatePurchaseRequest = async (
  organizationId: string,
  purchaseRequestId: string,
  updates: Partial<PurchaseRequestUpdate> | { 
    notes?: string | null;
    status?: string;
  },
  options?: {
    itemsToCreate?: Omit<PurchaseRequestItemInsert, 'purchase_request_id'>[],
    itemsToUpdate?: Array<{
      id: string;
      product_id: string;
      quantity: number;
      uom_id: string;
      notes?: string | null;
    }>,
    itemsToDelete?: string[]
  }
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    console.log('Updating purchase request:', { organizationId, purchaseRequestId, updates, options });

    if (!organizationId || !purchaseRequestId) {
      return { success: false, error: 'Organization ID and purchase request ID are required' };
    }

    // If we're just updating the purchase request properties (no item changes)
    if (!options || (!options.itemsToCreate && !options.itemsToUpdate && !options.itemsToDelete)) {
      const { error } = await supabase
        .from('purchase_requests')
        .update(updates)
        .eq('id', purchaseRequestId)
        .eq('organization_id', organizationId);

      if (error) {
        console.error('Error updating purchase request:', error);
        return { success: false, error: error.message || 'Failed to update purchase request' };
      }

      console.log('Purchase request updated successfully');
      return { success: true, error: null };
    }
    // If we're also updating items, use a stored procedure to handle the transaction
    else {
      const { error } = await supabase.rpc('update_purchase_request', {
        p_organization_id: organizationId,
        p_purchase_request_id: purchaseRequestId,
        p_purchase_request_data: updates,
        p_items_to_create: options.itemsToCreate || [],
        p_items_to_update: options.itemsToUpdate || [],
        p_items_to_delete: options.itemsToDelete || []
      });

      if (error) {
        console.error('Error updating purchase request with items:', error);
        return { success: false, error: error.message || 'Failed to update purchase request' };
      }

      console.log('Purchase request and items updated successfully');
      return { success: true, error: null };
    }
  } catch (error: any) {
    console.error('Error in updatePurchaseRequest:', error);
    return { 
      success: false, 
      error: error.message || 'An error occurred while updating the purchase request' 
    };
  }
};

/**
 * Update the status of a purchase request
 */
export const updatePurchaseRequestStatus = async (
  organizationId: string,
  purchaseRequestId: string,
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'cancelled',
  notes?: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    console.log('Updating purchase request status:', { organizationId, purchaseRequestId, status, notes });

    const updates: Partial<PurchaseRequestUpdate> = { status };

    // If notes are provided, update them as well
    if (notes !== undefined) {
      updates.notes = notes;
    }

    const { error } = await supabase
      .from('purchase_requests')
      .update(updates)
      .eq('organization_id', organizationId)
      .eq('id', purchaseRequestId);

    if (error) {
      console.error('Error updating purchase request status:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    console.log('Purchase request status updated successfully to:', status);
    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in updatePurchaseRequestStatus:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Delete a purchase request
 */
export const deletePurchaseRequest = async (
  organizationId: string,
  purchaseRequestId: string
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('purchase_requests')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', purchaseRequestId);

    if (error) {
      console.error('Error deleting purchase request:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deletePurchaseRequest:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
