import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';

export type Department = Database['public']['Tables']['departments']['Row'];

/**
 * Get all departments for an organization
 */
export const getDepartments = async (
  organizationId: string,
  options?: {
    searchQuery?: string;
    isActive?: boolean;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  },
): Promise<{
  departments: Department[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('departments')
      .select('*', { count: 'exact' })
      .eq('organization_id', organizationId);

    // Apply filters
    if (options?.isActive !== undefined) {
      query = query.eq('is_active', options.isActive);
    }

    if (options?.searchQuery) {
      query = query.ilike('name', `%${options.searchQuery}%`);
    }

    // Apply sorting
    if (options?.sortBy) {
      query = query.order(options.sortBy, { ascending: options.sortOrder === 'asc' });
    } else {
      query = query.order('name', { ascending: true });
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching departments:', error);
      return {
        departments: [],
        count: 0,
        error: error.message,
      };
    }

    return {
      departments: data as Department[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error in getDepartments:', error);
    return {
      departments: [],
      count: 0,
      error: error.message,
    };
  }
};

/**
 * Get a single department by ID
 */
export const getDepartmentById = async (
  organizationId: string,
  departmentId: string,
): Promise<{
  department?: Department;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('departments')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('id', departmentId)
      .single();

    if (error) {
      console.error('Error fetching department:', error);
      return {
        error: error.message,
      };
    }

    return {
      department: data as Department,
    };
  } catch (error: any) {
    console.error('Error in getDepartmentById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new department
 */
export const createDepartment = async (
  organizationId: string,
  department: Omit<Department, 'id' | 'organization_id' | 'created_at' | 'updated_at'>,
): Promise<{
  department?: Department;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('departments')
      .insert({
        ...department,
        organization_id: organizationId,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating department:', error);
      return {
        error: error.message,
      };
    }

    return {
      department: data as Department,
    };
  } catch (error: any) {
    console.error('Error in createDepartment:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update an existing department
 */
export const updateDepartment = async (
  organizationId: string,
  departmentId: string,
  updates: Partial<Omit<Department, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>,
): Promise<{
  department?: Department;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('departments')
      .update(updates)
      .eq('organization_id', organizationId)
      .eq('id', departmentId)
      .select()
      .single();

    if (error) {
      console.error('Error updating department:', error);
      return {
        error: error.message,
      };
    }

    return {
      department: data as Department,
    };
  } catch (error: any) {
    console.error('Error in updateDepartment:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Delete a department
 */
export const deleteDepartment = async (
  organizationId: string,
  departmentId: string,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('departments')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', departmentId);

    if (error) {
      console.error('Error deleting department:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteDepartment:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
