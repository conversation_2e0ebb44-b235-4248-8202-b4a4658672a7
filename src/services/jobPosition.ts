import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';

export type JobPosition = Database['public']['Tables']['job_positions']['Row'];

/**
 * Get all job positions for an organization
 */
export const getJobPositions = async (
  organizationId: string,
  options?: {
    searchQuery?: string;
    departmentId?: string;
    isActive?: boolean;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  },
): Promise<{
  positions: JobPosition[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('job_positions')
      .select(
        `
        *,
        department:department_id (
          id,
          name,
          description
        )
      `,
        { count: 'exact' },
      )
      .eq('organization_id', organizationId);

    // Apply filters
    if (options?.departmentId) {
      query = query.eq('department_id', options.departmentId);
    }

    if (options?.isActive !== undefined) {
      query = query.eq('is_active', options.isActive);
    }

    if (options?.searchQuery) {
      query = query.ilike('title', `%${options.searchQuery}%`);
    }

    // Apply sorting
    if (options?.sortBy) {
      query = query.order(options.sortBy, { ascending: options.sortOrder === 'asc' });
    } else {
      query = query.order('title', { ascending: true });
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching job positions:', error);
      return {
        positions: [],
        count: 0,
        error: error.message,
      };
    }

    return {
      positions: data as JobPosition[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error in getJobPositions:', error);
    return {
      positions: [],
      count: 0,
      error: error.message,
    };
  }
};

/**
 * Get a single job position by ID
 */
export const getJobPositionById = async (
  organizationId: string,
  positionId: string,
): Promise<{
  position?: JobPosition;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('job_positions')
      .select(
        `
        *,
        department:department_id (
          id,
          name,
          description
        )
      `,
      )
      .eq('organization_id', organizationId)
      .eq('id', positionId)
      .single();

    if (error) {
      console.error('Error fetching job position:', error);
      return {
        error: error.message,
      };
    }

    return {
      position: data as JobPosition,
    };
  } catch (error: any) {
    console.error('Error in getJobPositionById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new job position
 */
export const createJobPosition = async (
  organizationId: string,
  position: Omit<JobPosition, 'id' | 'organization_id' | 'created_at' | 'updated_at'>,
): Promise<{
  position?: JobPosition;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('job_positions')
      .insert({
        ...position,
        organization_id: organizationId,
      })
      .select(
        `
        *,
        department:department_id (
          id,
          name,
          description
        )
      `,
      )
      .single();

    if (error) {
      console.error('Error creating job position:', error);
      return {
        error: error.message,
      };
    }

    return {
      position: data as JobPosition,
    };
  } catch (error: any) {
    console.error('Error in createJobPosition:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update an existing job position
 */
export const updateJobPosition = async (
  organizationId: string,
  positionId: string,
  updates: Partial<Omit<JobPosition, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>,
): Promise<{
  position?: JobPosition;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('job_positions')
      .update(updates)
      .eq('organization_id', organizationId)
      .eq('id', positionId)
      .select(
        `
        *,
        department:department_id (
          id,
          name,
          description
        )
      `,
      )
      .single();

    if (error) {
      console.error('Error updating job position:', error);
      return {
        error: error.message,
      };
    }

    return {
      position: data as JobPosition,
    };
  } catch (error: any) {
    console.error('Error in updateJobPosition:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Delete a job position
 */
export const deleteJobPosition = async (
  organizationId: string,
  positionId: string,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { error } = await supabase
      .from('job_positions')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', positionId);

    if (error) {
      console.error('Error deleting job position:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteJobPosition:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
