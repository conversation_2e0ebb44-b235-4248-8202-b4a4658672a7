import { supabase } from '../lib/supabase';

/**
 * Debug function to check if an invitation exists
 */
export const checkInvitation = async (
  token: string,
  invitationId?: string,
): Promise<{
  exists: boolean;
  data?: any;
  error?: string;
}> => {
  try {
    console.log('Checking invitation with token:', token);
    if (invitationId) {
      console.log('Invitation ID:', invitationId);
    }

    // First, try to get all invitations to see what's in the database
    const { data: allInvitations, error: allError } = await supabase
      .from('invitations')
      .select('id, token, email, expires_at, accepted_at')
      .limit(10);

    console.log('All recent invitations:', allInvitations);
    if (allError) {
      console.error('Error fetching all invitations:', allError);
    }

    // Now try to find the specific invitation
    let query = supabase.from('invitations').select('*');

    if (invitationId) {
      // If we have both token and ID, use them both
      query = query.eq('id', invitationId);
    } else {
      // Otherwise just use the token
      query = query.eq('token', token);
    }

    const { data, error } = await query.single();

    if (error) {
      console.error('Error finding invitation:', error);
      return {
        exists: false,
        error: error.message,
      };
    }

    return {
      exists: true,
      data,
    };
  } catch (error: any) {
    console.error('Error in checkInvitation:', error);
    return {
      exists: false,
      error: error.message,
    };
  }
};

/**
 * Debug function to create a test invitation
 */
export const createTestInvitation = async (
  email: string,
): Promise<{
  success: boolean;
  invitationUrl?: string;
  error?: string;
}> => {
  try {
    // Get the current user
    const { data: userData } = await supabase.auth.getUser();
    if (!userData.user) {
      return {
        success: false,
        error: 'User not authenticated',
      };
    }

    // Get the user's organization
    const { data: orgData, error: orgError } = await supabase
      .from('organization_members')
      .select('organization_id, role')
      .eq('user_id', userData.user.id);

    console.log('User organizations:', orgData);
    console.log('Organization query error:', orgError);

    if (orgError) {
      return {
        success: false,
        error: `Error fetching organizations: ${orgError.message}`,
      };
    }

    if (!orgData || orgData.length === 0) {
      // Let's check if there are any organizations at all
      const { data: allOrgs } = await supabase.from('organizations').select('id, name').limit(5);

      console.log('Available organizations:', allOrgs);

      return {
        success: false,
        error: 'User is not a member of any organization',
      };
    }

    // Use the first organization the user is a member of
    const organizationId = orgData[0].organization_id;

    // Check if there's already an invitation for this email in this organization
    const { data: existingInvitation, error: checkError } = await supabase
      .from('invitations')
      .select('id, token, expires_at, accepted_at')
      .eq('organization_id', organizationId)
      .eq('email', email)
      .is('accepted_at', null)
      .single();

    if (existingInvitation) {
      console.log('Found existing invitation:', existingInvitation);

      // Check if the invitation has expired
      const expiresAt = new Date(existingInvitation.expires_at);
      if (expiresAt < new Date()) {
        // If expired, delete it and create a new one
        console.log('Existing invitation has expired, deleting it');
        await supabase.from('invitations').delete().eq('id', existingInvitation.id);
      } else {
        // If not expired, use the existing invitation
        console.log('Using existing invitation');

        // Generate the invitation URL
        const siteUrl = import.meta.env.VITE_SITE_URL || window.location.origin;
        const invitationUrl = `${siteUrl}/debug/accept-invitation?token=${encodeURIComponent(
          existingInvitation.token,
        )}&id=${existingInvitation.id}`;

        console.log('Existing invitation URL:', invitationUrl);

        return {
          success: true,
          invitationUrl,
          error:
            'Using existing invitation. Email sending is not configured. Please share the invitation link manually.',
        };
      }
    }

    // Create a simple token
    const token = Math.random().toString(36).substring(2, 15);
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 48); // 48 hours from now

    // Insert the invitation directly
    const { data, error } = await supabase
      .from('invitations')
      .insert({
        organization_id: organizationId,
        email,
        role: 'member',
        invited_by: userData.user.id,
        token,
        expires_at: expiresAt.toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating test invitation:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    // Generate the invitation URL
    const siteUrl = import.meta.env.VITE_SITE_URL || window.location.origin;

    // Make sure the token is properly encoded
    const encodedToken = encodeURIComponent(token);
    console.log('Original token:', token);
    console.log('Encoded token:', encodedToken);

    const invitationUrl = `${siteUrl}/auth/accept-invitation?token=${encodedToken}&id=${data.id}`;

    console.log('Created test invitation:', data);
    console.log('Test invitation URL:', invitationUrl);

    return {
      success: true,
      invitationUrl,
    };
  } catch (error: any) {
    console.error('Error in createTestInvitation:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
