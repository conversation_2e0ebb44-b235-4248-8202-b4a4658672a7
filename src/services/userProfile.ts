import { supabase } from '../lib/supabase';

/**
 * Check if the user has a profile
 */
export const checkUserProfile = async (
  userId: string,
): Promise<{
  hasProfile: boolean;
  profile?: any;
  error?: string;
}> => {
  try {
    console.log('Checking if user has a profile:', userId);

    const { data, error } = await supabase.from('profiles').select('*').eq('id', userId).maybeSingle();

    if (error) {
      console.error('Error checking user profile:', error);
      return {
        hasProfile: false,
        error: error.message,
      };
    }

    return {
      hasProfile: !!data,
      profile: data,
    };
  } catch (error: any) {
    console.error('Error checking user profile:', error);
    return {
      hasProfile: false,
      error: error.message,
    };
  }
};

/**
 * Check if the user has organizations
 */
export const checkUserOrganizations = async (
  userId: string,
): Promise<{
  hasOrganizations: boolean;
  organizations?: any[];
  error?: string;
}> => {
  try {
    console.log('Checking if user has organizations:', userId);

    const { data, error } = await supabase
      .from('organization_members')
      .select(
        `
        *,
        organization:organizations(*)
      `,
      )
      .eq('user_id', userId);

    if (error) {
      console.error('Error checking user organizations:', error);
      return {
        hasOrganizations: false,
        error: error.message,
      };
    }

    return {
      hasOrganizations: data && data.length > 0,
      organizations: data,
    };
  } catch (error: any) {
    console.error('Error checking user organizations:', error);
    return {
      hasOrganizations: false,
      error: error.message,
    };
  }
};

/**
 * Create a profile for a user if they don't have one
 */
export const createUserProfile = async (
  userId: string,
  firstName: string,
  lastName: string,
): Promise<{
  success: boolean;
  profile?: any;
  error?: string;
}> => {
  try {
    console.log('Creating profile for user:', userId);

    const { data, error } = await supabase
      .from('profiles')
      .insert({
        id: userId,
        first_name: firstName,
        last_name: lastName,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating user profile:', error);
      console.error('Profile error details:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint,
      });

      // Try using the bypass RLS function
      console.log('Trying to create profile using bypass RLS function...');
      const { data: bypassData, error: bypassError } = await supabase.rpc(
        'create_profile_bypass_rls',
        {
          user_id: userId,
          first_name: firstName,
          last_name: lastName,
        },
      );

      if (bypassError) {
        console.error('Bypass RLS function error:', bypassError);
        return {
          success: false,
          error: `Failed to create profile: ${bypassError.message}`,
        };
      }

      console.log('Profile created successfully via bypass function:', bypassData);

      // Check if the function returned success
      if (bypassData && bypassData.success) {
        return {
          success: true,
          profile: bypassData.profile,
        };
      } else {
        // Function returned an error
        return {
          success: false,
          error: bypassData.error || 'Unknown error creating profile',
        };
      }
    }

    return {
      success: true,
      profile: data,
    };
  } catch (error: any) {
    console.error('Error creating user profile:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Create an organization for a user if they don't have one
 */
export const createUserOrganization = async (
  userId: string,
  organizationName: string,
): Promise<{
  success: boolean;
  organization?: any;
  error?: string;
}> => {
  try {
    console.log('Creating organization for user:', userId);

    // First check if user already has an organization
    const { data: existingMembership } = await supabase
      .from('organization_members')
      .select('organization_id, organizations(*)')
      .eq('user_id', userId)
      .maybeSingle();

    if (existingMembership && existingMembership.organizations) {
      console.log('User already has an organization:', existingMembership.organizations);
      return {
        success: true,
        organization: existingMembership.organizations,
      };
    }

    // Create a slug from the organization name with timestamp to ensure uniqueness
    const timestamp = Date.now();
    let baseSlug = organizationName
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Add timestamp to make it unique
    const slug = `${baseSlug}-${timestamp}`;

    console.log('Creating organization with unique slug:', slug);

    // Create the organization
    const orgResponse = await supabase
      .from('organizations')
      .insert({
        name: organizationName,
        slug,
        created_by: userId,
      })
      .select()
      .single();

    if (orgResponse.error) {
      console.error('Error creating organization:', orgResponse.error);
      console.error('Organization error details:', {
        code: orgResponse.error.code,
        message: orgResponse.error.message,
        details: orgResponse.error.details,
        hint: orgResponse.error.hint,
      });

      // Try a different approach - use RPC to bypass RLS
      console.log('Trying to create organization using RPC...');

      try {
        // Try the new bypass RLS function
        console.log('Trying to create organization using bypass RLS function...');
        // Generate a new unique slug for the RPC call with timestamp
        const rpcTimestamp = Date.now();
        const rpcSlug = `${baseSlug}-${rpcTimestamp}`;

        const { data: bypassData, error: bypassError } = await supabase.rpc(
          'create_organization_bypass_rls',
          {
            org_name: organizationName,
            org_slug: rpcSlug,
            user_id: userId,
          },
        );

        if (bypassError) {
          console.error('Bypass RLS function error:', bypassError);
          return {
            success: false,
            error: `Failed to create organization: ${bypassError.message}`,
          };
        }

        console.log('Organization created successfully via bypass function:', bypassData);

        // Check if the function returned success
        if (bypassData && bypassData.success) {
          return {
            success: true,
            organization: bypassData.organization,
          };
        } else {
          // Function returned an error
          return {
            success: false,
            error: bypassData.error || 'Unknown error creating organization',
          };
        }
      } catch (rpcErr: any) {
        console.error('RPC exception:', rpcErr);
        return {
          success: false,
          error: `Exception in RPC: ${rpcErr.message}`,
        };
      }
    }

    const orgData = orgResponse.data;
    console.log('Organization created successfully:', orgData);

    // Add the user as an owner of the organization
    console.log('Adding user as organization owner...');
    const memberResponse = await supabase.from('organization_members').insert({
      organization_id: orgData.id,
      user_id: userId,
      role: 'owner',
    });

    if (memberResponse.error) {
      console.error('Error adding user as organization owner:', memberResponse.error);
      console.error('Member error details:', {
        code: memberResponse.error.code,
        message: memberResponse.error.message,
        details: memberResponse.error.details,
        hint: memberResponse.error.hint,
      });

      // Continue anyway - we'll try to create the settings
    } else {
      console.log('User added as organization owner successfully');
    }

    // Create default organization settings
    console.log('Creating organization settings...');
    const settingsResponse = await supabase.from('organization_settings').insert({
      organization_id: orgData.id,
      settings: {
        currency: 'USD',
        tax_rate: 0,
        business_hours: {
          monday: { open: '09:00', close: '17:00' },
          tuesday: { open: '09:00', close: '17:00' },
          wednesday: { open: '09:00', close: '17:00' },
          thursday: { open: '09:00', close: '17:00' },
          friday: { open: '09:00', close: '17:00' },
          saturday: { open: '', close: '' },
          sunday: { open: '', close: '' },
        },
      },
    });

    if (settingsResponse.error) {
      console.error('Error creating organization settings:', settingsResponse.error);
      console.error('Settings error details:', {
        code: settingsResponse.error.code,
        message: settingsResponse.error.message,
        details: settingsResponse.error.details,
        hint: settingsResponse.error.hint,
      });

      // Continue anyway - we've created the organization
    } else {
      console.log('Organization settings created successfully');
    }

    return {
      success: true,
      organization: orgData,
    };
  } catch (error: any) {
    console.error('Error creating user organization:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
