import { supabase } from '../lib/supabase';
import { Tag } from '../types/tagging.types';

/**
 * Get all tags for a supplier
 */
export const getSupplierTags = async (supplierId: string): Promise<{
  tags: Tag[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('get_entity_tags', {
      p_entity_type: 'supplier',
      p_entity_id: supplierId
    });

    if (error) {
      console.error('Error fetching supplier tags:', error);
      return { tags: [], error: error.message };
    }

    return { tags: data || [] };
  } catch (err: any) {
    console.error('Error in getSupplierTags:', err);
    return { tags: [], error: err.message };
  }
};

/**
 * Add a tag to a supplier
 */
export const addTagToSupplier = async (supplierId: string, tagId: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('add_tag_to_entity', {
      p_tag_id: tagId,
      p_entity_type: 'supplier',
      p_entity_id: supplierId
    });

    if (error) {
      console.error('Error adding tag to supplier:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err: any) {
    console.error('Error in addTagToSupplier:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Remove a tag from a supplier
 */
export const removeTagFromSupplier = async (supplierId: string, tagId: string): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('remove_tag_from_entity', {
      p_tag_id: tagId,
      p_entity_type: 'supplier',
      p_entity_id: supplierId
    });

    if (error) {
      console.error('Error removing tag from supplier:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err: any) {
    console.error('Error in removeTagFromSupplier:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Find suppliers by tag
 */
export const findSuppliersByTag = async (tagId: string): Promise<{
  supplierIds: string[];
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.rpc('find_entities_by_tag', {
      p_tag_id: tagId,
      p_entity_type: 'supplier'
    });

    if (error) {
      console.error('Error finding suppliers by tag:', error);
      return { supplierIds: [], error: error.message };
    }

    return { 
      supplierIds: data ? data.map(item => item.entity_id) : [] 
    };
  } catch (err: any) {
    console.error('Error in findSuppliersByTag:', err);
    return { supplierIds: [], error: err.message };
  }
};
