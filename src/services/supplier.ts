import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';

export type Supplier = Database['public']['Tables']['suppliers']['Row'] & {
  is_default?: boolean;
};

/**
 * Get the default supplier for an organization
 * If no default supplier exists, creates one automatically
 */
export const getDefaultSupplier = async (
  organizationId: string,
): Promise<{
  supplier?: Supplier;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('suppliers')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('is_default', true)
      .single();

    if (error && error.code === 'PGRST116') {
      // No default supplier found, create one
      console.log('No default supplier found, creating one for organization:', organizationId);

      const { supplier: newSupplier, error: createError } = await createDefaultSupplier(organizationId);
      if (createError) {
        return {
          error: createError,
        };
      }

      return {
        supplier: newSupplier,
      };
    } else if (error) {
      console.error('Error fetching default supplier:', error);
      return {
        error: error.message,
      };
    }

    return {
      supplier: data as Supplier,
    };
  } catch (error: any) {
    console.error('Error in getDefaultSupplier:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a default supplier for an organization
 */
export const createDefaultSupplier = async (
  organizationId: string,
): Promise<{
  supplier?: Supplier;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('suppliers')
      .insert({
        organization_id: organizationId,
        name: 'Default Supplier',
        contact_person: 'To Be Determined',
        email: null,
        phone: null,
        address: null,
        notes: 'This is the default supplier used for purchase orders when the actual supplier is not yet determined. You can edit the purchase order later to assign the correct supplier.',
        is_default: true,
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error creating default supplier:', error);
      return {
        error: error.message,
      };
    }

    console.log('Created default supplier for organization:', organizationId);
    return {
      supplier: data as Supplier,
    };
  } catch (error: any) {
    console.error('Error in createDefaultSupplier:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Get all suppliers for an organization
 */
export const getSuppliers = async (
  organizationId: string,
  options?: {
    searchQuery?: string;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  },
): Promise<{
  suppliers: Supplier[];
  count: number;
  error?: string;
}> => {
  try {
    let query = supabase
      .from('suppliers')
      .select('*', { count: 'exact' })
      .eq('organization_id', organizationId);

    // Apply search filter
    if (options?.searchQuery) {
      query = query.or(
        `name.ilike.%${options.searchQuery}%,email.ilike.%${options.searchQuery}%,contact_person.ilike.%${options.searchQuery}%`,
      );
    }

    // Apply sorting
    if (options?.sortBy) {
      query = query.order(options.sortBy, { ascending: options.sortOrder === 'asc' });
    } else {
      query = query.order('name', { ascending: true });
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching suppliers:', error);
      return {
        suppliers: [],
        count: 0,
        error: error.message,
      };
    }

    return {
      suppliers: data as Supplier[],
      count: count || 0,
    };
  } catch (error: any) {
    console.error('Error in getSuppliers:', error);
    return {
      suppliers: [],
      count: 0,
      error: error.message,
    };
  }
};

/**
 * Get a single supplier by ID
 */
export const getSupplierById = async (
  organizationId: string,
  supplierId: string,
): Promise<{
  supplier?: Supplier;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('suppliers')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('id', supplierId)
      .single();

    if (error) {
      console.error('Error fetching supplier:', error);
      return {
        error: error.message,
      };
    }

    return {
      supplier: data as Supplier,
    };
  } catch (error: any) {
    console.error('Error in getSupplierById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a new supplier
 */
export const createSupplier = async (
  organizationId: string,
  supplier: Omit<Supplier, 'id' | 'organization_id' | 'created_at' | 'updated_at'>,
): Promise<{
  supplier?: Supplier;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('suppliers')
      .insert({
        ...supplier,
        organization_id: organizationId,
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error creating supplier:', error);
      return {
        error: error.message,
      };
    }

    return {
      supplier: data as Supplier,
    };
  } catch (error: any) {
    console.error('Error in createSupplier:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update an existing supplier
 */
export const updateSupplier = async (
  organizationId: string,
  supplierId: string,
  updates: Partial<Omit<Supplier, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>,
): Promise<{
  supplier?: Supplier;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .from('suppliers')
      .update(updates)
      .eq('organization_id', organizationId)
      .eq('id', supplierId)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating supplier:', error);
      return {
        error: error.message,
      };
    }

    return {
      supplier: data as Supplier,
    };
  } catch (error: any) {
    console.error('Error in updateSupplier:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Delete a supplier
 */
export const deleteSupplier = async (
  organizationId: string,
  supplierId: string,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // First check if this is a default supplier
    const { data: supplier, error: fetchError } = await supabase
      .from('suppliers')
      .select('is_default')
      .eq('organization_id', organizationId)
      .eq('id', supplierId)
      .single();

    if (fetchError) {
      console.error('Error fetching supplier for deletion check:', fetchError);
      return {
        success: false,
        error: fetchError.message,
      };
    }

    // Prevent deletion of default supplier
    if (supplier?.is_default) {
      return {
        success: false,
        error: 'Cannot delete the default supplier. This supplier is required for the organization.',
      };
    }

    const { error } = await supabase
      .from('suppliers')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', supplierId);

    if (error) {
      console.error('Error deleting supplier:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    console.error('Error in deleteSupplier:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
