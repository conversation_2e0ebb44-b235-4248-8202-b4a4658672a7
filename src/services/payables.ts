import { supabase } from '../lib/supabase';
import {
  Payable,
  PayableWithDetails,
  PayablePayment,
  PayablePaymentWithDetails,
  CreatePayableRequest,
  CreatePaymentRequest,
  PayableListOptions,
  PayableResponse,
  PaymentResponse,
  PayableSummaryResponse,
  PayableAgingResponse,
  PayableSummary,
  PayableAgingReport,
  PayableAgingBucket,
  PayableStatus,
  PayableSourceType
} from '../types/payables.types';

/**
 * Get all payables for an organization with multi-tenancy security
 */
export const getPayables = async (
  organizationId: string,
  options?: PayableListOptions
): Promise<PayableResponse> => {
  try {
    let query = supabase
      .from('payables')
      .select(`
        *,
        supplier:suppliers(id, name, contact_person, email, phone),
        employee:employees(id, first_name, last_name, employee_number),
        payments:payable_payments(*)
      `)
      .eq('organization_id', organizationId)
      // Only show APPROVED payables in traditional accounts payable
      .eq('approval_status', 'approved');

    // Apply filters
    if (options?.filters) {
      const { filters } = options;

      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.source_type) {
        query = query.eq('source_type', filters.source_type);
      }

      if (filters.supplier_id) {
        query = query.eq('supplier_id', filters.supplier_id);
      }

      if (filters.employee_id) {
        query = query.eq('employee_id', filters.employee_id);
      }

      if (filters.due_date_from) {
        query = query.gte('due_date', filters.due_date_from);
      }

      if (filters.due_date_to) {
        query = query.lte('due_date', filters.due_date_to);
      }

      if (filters.amount_from) {
        query = query.gte('amount', filters.amount_from);
      }

      if (filters.amount_to) {
        query = query.lte('amount', filters.amount_to);
      }

      if (filters.search_query) {
        query = query.or(`reference_number.ilike.%${filters.search_query}%,notes.ilike.%${filters.search_query}%`);
      }
    }

    // Apply sorting
    const sortBy = options?.sortBy || 'created_at';
    const sortOrder = options?.sortOrder || 'desc';
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 50) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching payables:', error);
      return { success: false, error: error.message };
    }

    // Calculate total_paid for each payable
    const payablesWithTotals: PayableWithDetails[] = (data || []).map(payable => ({
      ...payable,
      total_paid: payable.payments?.reduce((sum, payment) => sum + Number(payment.amount_paid), 0) || 0
    }));

    return {
      success: true,
      payables: payablesWithTotals,
      total_count: count || 0
    };
  } catch (error: any) {
    console.error('Error in getPayables:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get a single payable by ID
 */
export const getPayableById = async (
  organizationId: string,
  payableId: string
): Promise<PayableResponse> => {
  try {
    const { data, error } = await supabase
      .from('payables')
      .select(`
        *,
        supplier:suppliers(id, name, contact_person, email, phone, tax_id, address),
        employee:employees(id, first_name, last_name, employee_number),
        payments:payable_payments(*)
      `)
      .eq('organization_id', organizationId)
      .eq('id', payableId)
      // Only return APPROVED payables in traditional accounts payable
      .eq('approval_status', 'approved')
      .single();

    if (error) {
      console.error('Error fetching payable:', error);
      return { success: false, error: error.message };
    }

    const payableWithTotal: PayableWithDetails = {
      ...data,
      total_paid: data.payments?.reduce((sum, payment) => sum + Number(payment.amount_paid), 0) || 0
    };

    return { success: true, payable: payableWithTotal };
  } catch (error: any) {
    console.error('Error in getPayableById:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create a new payable
 */
export const createPayable = async (
  organizationId: string,
  payableData: CreatePayableRequest,
  userId: string
): Promise<PayableResponse> => {
  try {
    // Calculate balance (amount minus any withholding tax)
    const balance = payableData.amount - (payableData.withholding_tax_amount || 0);

    const { data, error } = await supabase
      .from('payables')
      .insert({
        organization_id: organizationId,
        ...payableData,
        balance,
        status: PayableStatus.OPEN,
        created_by: userId
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating payable:', error);
      return { success: false, error: error.message };
    }

    return { success: true, payable: data };
  } catch (error: any) {
    console.error('Error in createPayable:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update a payable
 */
export const updatePayable = async (
  organizationId: string,
  payableId: string,
  updates: Partial<CreatePayableRequest>
): Promise<PayableResponse> => {
  try {
    const { data, error } = await supabase
      .from('payables')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('organization_id', organizationId)
      .eq('id', payableId)
      .select()
      .single();

    if (error) {
      console.error('Error updating payable:', error);
      return { success: false, error: error.message };
    }

    return { success: true, payable: data };
  } catch (error: any) {
    console.error('Error in updatePayable:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Delete a payable
 */
export const deletePayable = async (
  organizationId: string,
  payableId: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Check if payable has payments
    const { data: payments } = await supabase
      .from('payable_payments')
      .select('id')
      .eq('payable_id', payableId)
      .limit(1);

    if (payments && payments.length > 0) {
      return { success: false, error: 'Cannot delete payable with existing payments' };
    }

    const { error } = await supabase
      .from('payables')
      .delete()
      .eq('organization_id', organizationId)
      .eq('id', payableId);

    if (error) {
      console.error('Error deleting payable:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in deletePayable:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create a payment for a payable
 */
export const createPayment = async (
  organizationId: string,
  paymentData: CreatePaymentRequest,
  userId: string
): Promise<PaymentResponse> => {
  try {
    // Verify payable exists and get current balance
    const { data: payable, error: payableError } = await supabase
      .from('payables')
      .select('id, balance, status')
      .eq('organization_id', organizationId)
      .eq('id', paymentData.payable_id)
      .single();

    if (payableError || !payable) {
      return { success: false, error: 'Payable not found' };
    }

    if (payable.status === PayableStatus.PAID) {
      return { success: false, error: 'Payable is already fully paid' };
    }

    if (paymentData.amount_paid > payable.balance) {
      return { success: false, error: 'Payment amount exceeds remaining balance' };
    }

    const { data, error } = await supabase
      .from('payable_payments')
      .insert({
        ...paymentData,
        created_by: userId
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating payment:', error);
      return { success: false, error: error.message };
    }

    return { success: true, payment: data };
  } catch (error: any) {
    console.error('Error in createPayment:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get payments for a payable
 */
export const getPayablePayments = async (
  organizationId: string,
  payableId: string
): Promise<PaymentResponse> => {
  try {
    const { data, error } = await supabase
      .from('payable_payments')
      .select(`
        *,
        payable:payables!inner(organization_id)
      `)
      .eq('payable.organization_id', organizationId)
      .eq('payable_id', payableId)
      .order('payment_date', { ascending: false });

    if (error) {
      console.error('Error fetching payments:', error);
      return { success: false, error: error.message };
    }

    return { success: true, payments: data || [] };
  } catch (error: any) {
    console.error('Error in getPayablePayments:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get payables summary statistics
 */
export const getPayablesSummary = async (
  organizationId: string
): Promise<PayableSummaryResponse> => {
  try {
    const { data, error } = await supabase
      .from('payables')
      .select('status, source_type, amount, balance')
      .eq('organization_id', organizationId)
      .eq('approval_status', 'approved');

    if (error) {
      console.error('Error fetching payables summary:', error);
      return { success: false, error: error.message };
    }

    const payables = data || [];
    const currentDate = new Date();

    // Calculate overdue payables
    const { data: overdueData } = await supabase
      .from('payables')
      .select('amount, balance')
      .eq('organization_id', organizationId)
      .eq('approval_status', 'approved')
      .lt('due_date', currentDate.toISOString().split('T')[0])
      .neq('status', PayableStatus.PAID);

    const overduePayables = overdueData || [];

    const summary: PayableSummary = {
      total_payables: payables.length,
      total_amount: payables.reduce((sum, p) => sum + Number(p.amount), 0),
      total_paid: payables.reduce((sum, p) => sum + (Number(p.amount) - Number(p.balance)), 0),
      total_outstanding: payables.reduce((sum, p) => sum + Number(p.balance), 0),
      overdue_count: overduePayables.length,
      overdue_amount: overduePayables.reduce((sum, p) => sum + Number(p.balance), 0),
      by_status: {
        [PayableStatus.DRAFT]: { count: 0, amount: 0 },
        [PayableStatus.OPEN]: { count: 0, amount: 0 },
        [PayableStatus.PARTIALLY_PAID]: { count: 0, amount: 0 },
        [PayableStatus.PAID]: { count: 0, amount: 0 },
        [PayableStatus.CANCELLED]: { count: 0, amount: 0 }
      },
      by_source_type: {
        [PayableSourceType.PURCHASE_RECEIPT]: { count: 0, amount: 0 },
        [PayableSourceType.PAYROLL]: { count: 0, amount: 0 },
        [PayableSourceType.UTILITY_BILL]: { count: 0, amount: 0 },
        [PayableSourceType.GOVERNMENT_REMITTANCE]: { count: 0, amount: 0 },
        [PayableSourceType.LOAN_REPAYMENT]: { count: 0, amount: 0 },
        [PayableSourceType.MANUAL_ENTRY]: { count: 0, amount: 0 }
      }
    };

    // Group by status and source type
    payables.forEach(payable => {
      const status = payable.status as PayableStatus;
      const sourceType = payable.source_type as PayableSourceType;

      summary.by_status[status].count++;
      summary.by_status[status].amount += Number(payable.amount);

      summary.by_source_type[sourceType].count++;
      summary.by_source_type[sourceType].amount += Number(payable.amount);
    });

    return { success: true, summary };
  } catch (error: any) {
    console.error('Error in getPayablesSummary:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get aging report for payables
 */
export const getPayablesAgingReport = async (
  organizationId: string,
  asOfDate?: string
): Promise<PayableAgingResponse> => {
  try {
    const reportDate = asOfDate || new Date().toISOString().split('T')[0];

    const { data, error } = await supabase
      .from('payables')
      .select(`
        *,
        supplier:suppliers(id, name),
        employee:employees(id, first_name, last_name)
      `)
      .eq('organization_id', organizationId)
      .eq('approval_status', 'approved')
      .neq('status', PayableStatus.PAID)
      .neq('status', PayableStatus.CANCELLED);

    if (error) {
      console.error('Error fetching aging report:', error);
      return { success: false, error: error.message };
    }

    const payables = data || [];
    const reportDateObj = new Date(reportDate);

    // Define aging buckets
    const buckets: PayableAgingBucket[] = [
      { label: 'Current', days_from: 0, days_to: 0, count: 0, total_amount: 0 },
      { label: '1-30 days', days_from: 1, days_to: 30, count: 0, total_amount: 0 },
      { label: '31-60 days', days_from: 31, days_to: 60, count: 0, total_amount: 0 },
      { label: '61-90 days', days_from: 61, days_to: 90, count: 0, total_amount: 0 },
      { label: 'Over 90 days', days_from: 91, days_to: null, count: 0, total_amount: 0 }
    ];

    // Categorize payables into buckets
    const categorizedPayables: PayableWithDetails[] = payables.map(payable => {
      const dueDate = new Date(payable.due_date);
      const daysPastDue = Math.floor((reportDateObj.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

      let bucketIndex = 0;
      if (daysPastDue <= 0) bucketIndex = 0;
      else if (daysPastDue <= 30) bucketIndex = 1;
      else if (daysPastDue <= 60) bucketIndex = 2;
      else if (daysPastDue <= 90) bucketIndex = 3;
      else bucketIndex = 4;

      buckets[bucketIndex].count++;
      buckets[bucketIndex].total_amount += Number(payable.balance);

      return {
        ...payable,
        days_past_due: daysPastDue
      };
    });

    const agingReport: PayableAgingReport = {
      organization_id: organizationId,
      as_of_date: reportDate,
      buckets,
      total_count: payables.length,
      total_amount: payables.reduce((sum, p) => sum + Number(p.balance), 0),
      payables: categorizedPayables
    };

    return { success: true, aging_report: agingReport };
  } catch (error: any) {
    console.error('Error in getPayablesAgingReport:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get source metadata for a payable (resolves source_id to human-readable info)
 */
export const getPayableSourceMetadata = async (
  organizationId: string,
  payableId: string
): Promise<{
  success: boolean;
  metadata?: {
    source_type: string;
    source_id: string;
    source_name: string;
    source_description: string;
    source_date: string;
    source_amount: number;
    source_url?: string;
  };
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .rpc('resolve_payable_source_metadata', {
        payable_id: payableId
      });

    if (error) {
      console.error('Error fetching source metadata:', error);
      return { success: false, error: error.message };
    }

    const metadata = data?.[0];
    return {
      success: true,
      metadata: metadata ? {
        source_type: metadata.source_type,
        source_id: metadata.source_id,
        source_name: metadata.source_name,
        source_description: metadata.source_description,
        source_date: metadata.source_date,
        source_amount: Number(metadata.source_amount),
        source_url: metadata.source_url
      } : undefined
    };
  } catch (error: any) {
    console.error('Error in getPayableSourceMetadata:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Calculate aging report using database function
 */
export const getPayablesAgingReportEnhanced = async (
  organizationId: string,
  asOfDate?: string
): Promise<PayableAgingResponse> => {
  try {
    const { data, error } = await supabase
      .rpc('calculate_payable_aging', {
        p_organization_id: organizationId,
        p_as_of_date: asOfDate || new Date().toISOString().split('T')[0]
      });

    if (error) {
      console.error('Error fetching enhanced aging report:', error);
      return { success: false, error: error.message };
    }

    // Group data into aging buckets
    const buckets: PayableAgingBucket[] = [
      { label: 'Current', days_from: 0, days_to: 0, count: 0, total_amount: 0 },
      { label: '1-30 days', days_from: 1, days_to: 30, count: 0, total_amount: 0 },
      { label: '31-60 days', days_from: 31, days_to: 60, count: 0, total_amount: 0 },
      { label: '61-90 days', days_from: 61, days_to: 90, count: 0, total_amount: 0 },
      { label: 'Over 90 days', days_from: 91, days_to: null, count: 0, total_amount: 0 }
    ];

    const payables: PayableWithDetails[] = [];

    (data || []).forEach((row: any) => {
      const bucketIndex = buckets.findIndex(b => b.label === row.aging_bucket);
      if (bucketIndex >= 0) {
        buckets[bucketIndex].count++;
        buckets[bucketIndex].total_amount += Number(row.balance);
      }

      payables.push({
        id: row.payable_id,
        reference_number: row.reference_number,
        amount: Number(row.amount),
        balance: Number(row.balance),
        due_date: row.due_date,
        supplier: row.supplier_name ? { name: row.supplier_name } : undefined,
        days_past_due: row.days_overdue
      } as PayableWithDetails);
    });

    const agingReport: PayableAgingReport = {
      organization_id: organizationId,
      as_of_date: asOfDate || new Date().toISOString().split('T')[0],
      buckets,
      total_count: payables.length,
      total_amount: payables.reduce((sum, p) => sum + Number(p.balance), 0),
      payables
    };

    return { success: true, aging_report: agingReport };
  } catch (error: any) {
    console.error('Error in getPayablesAgingReportEnhanced:', error);
    return { success: false, error: error.message };
  }
};

// Auto-creation functions removed - using manual creation only

/**
 * Check if an inventory receipt has been sent to payables
 */
export const checkReceiptPayableStatus = async (
  organizationId: string,
  receiptId: string
): Promise<{ hasPendingPayable: boolean; payableId?: string; error?: string }> => {
  try {
    const { data, error } = await supabase
      .from('payables')
      .select('id, reference_number, status')
      .eq('organization_id', organizationId)
      .eq('source_type', 'purchase_receipt')
      .eq('source_id', receiptId)
      .maybeSingle();

    if (error) {
      return { hasPendingPayable: false, error: error.message };
    }

    return {
      hasPendingPayable: !!data,
      payableId: data?.id
    };
  } catch (error: any) {
    return { hasPendingPayable: false, error: error.message };
  }
};

/**
 * Get payable status for multiple receipts
 */
export const getReceiptsPayableStatus = async (
  organizationId: string,
  receiptIds: string[]
): Promise<{ receiptsStatus: Record<string, boolean>; error?: string }> => {
  try {
    const { data, error } = await supabase
      .from('payables')
      .select('source_id')
      .eq('organization_id', organizationId)
      .eq('source_type', 'purchase_receipt')
      .in('source_id', receiptIds);

    if (error) {
      return { receiptsStatus: {}, error: error.message };
    }

    const receiptsStatus: Record<string, boolean> = {};
    receiptIds.forEach(id => {
      receiptsStatus[id] = data?.some(payable => payable.source_id === id) || false;
    });

    return { receiptsStatus };
  } catch (error: any) {
    return { receiptsStatus: {}, error: error.message };
  }
};

/**
 * Check payable workflow readiness
 */
export const checkPayableWorkflowReadiness = async (): Promise<{
  success: boolean;
  checks?: Array<{
    check_name: string;
    status: string;
    details: string;
  }>;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase
      .rpc('check_payable_workflow_readiness');

    if (error) {
      console.error('Error checking workflow readiness:', error);
      return { success: false, error: error.message };
    }

    return { success: true, checks: data || [] };
  } catch (error: any) {
    console.error('Error in checkPayableWorkflowReadiness:', error);
    return { success: false, error: error.message };
  }
};

// Trigger functions removed - using manual creation only

/**
 * Manually create payable from inventory receipt
 */
export const createPayableFromReceipt = async (
  organizationId: string,
  receiptId: string,
  userId: string
): Promise<{ success: boolean; payable?: any; error?: string }> => {
  try {
    // Check if receipt exists and is completed
    const { data: receipt, error: receiptError } = await supabase
      .from('inventory_receipts')
      .select(`
        id, receipt_number, status, receipt_date, purchase_order_id, organization_id,
        purchase_order:purchase_orders(
          id, order_number, supplier_id,
          supplier:suppliers(id, name, payment_terms_days)
        )
      `)
      .eq('organization_id', organizationId)
      .eq('id', receiptId)
      .single();

    if (receiptError || !receipt) {
      return { success: false, error: 'Receipt not found' };
    }

    if (receipt.status !== 'completed') {
      return { success: false, error: 'Receipt must be completed to create payable' };
    }

    if (!receipt.purchase_order) {
      return { success: false, error: 'Receipt must be linked to a purchase order' };
    }

    if (!receipt.purchase_order.supplier) {
      return { success: false, error: 'Purchase order must have a supplier' };
    }

    // Check if payable already exists
    const { data: existingPayable } = await supabase
      .from('payables')
      .select('id, reference_number')
      .eq('organization_id', organizationId)
      .eq('source_type', 'purchase_receipt')
      .eq('source_id', receiptId)
      .maybeSingle();

    if (existingPayable) {
      return {
        success: false,
        error: `Payable already exists with reference: ${existingPayable.reference_number}`
      };
    }

    // Get receipt items with basic fields first, then check for QC fields
    const { data: receiptItems, error: itemsError } = await supabase
      .from('inventory_receipt_items')
      .select('*')
      .eq('inventory_receipt_id', receiptId);

    if (itemsError || !receiptItems || receiptItems.length === 0) {
      return { success: false, error: 'Receipt has no items' };
    }

    // Calculate total based on QC status - only pay for items that passed QC
    const receiptTotal = receiptItems.reduce((sum, item: any) => {
      // Check if QC status exists and exclude failed items
      if (item.qc_status === 'failed') {
        console.log(`Excluding failed QC item: quantity=${item.quantity}, cost=${item.unit_cost}, total excluded=${item.quantity * item.unit_cost}`);
        return sum; // Don't add failed items to payable total
      }

      // For passed items, subtract damaged quantity from payable amount
      let payableQuantity = item.quantity;

      // Check if damaged_quantity field exists and has value
      if (item.damaged_quantity && item.damaged_quantity > 0) {
        payableQuantity = item.quantity - item.damaged_quantity;
        console.log(`Item has damage: original=${item.quantity}, damaged=${item.damaged_quantity}, payable=${payableQuantity}`);
      }

      // Ensure we don't have negative quantities
      payableQuantity = Math.max(0, payableQuantity);

      const itemTotal = payableQuantity * item.unit_cost;
      console.log(`Including item: quantity=${payableQuantity}, cost=${item.unit_cost}, total=${itemTotal}, qc_status=${item.qc_status || 'not_set'}`);

      return sum + itemTotal;
    }, 0);

    if (receiptTotal <= 0) {
      return { success: false, error: 'Receipt total must be greater than zero' };
    }

    // Get VAT rate from organization settings
    console.log(`🔍 Fetching VAT settings for organization: ${organizationId}`);

    const { data: orgSettings, error: settingsError } = await supabase
      .from('organization_settings')
      .select('settings')
      .eq('organization_id', organizationId)
      .single();

    console.log('📋 Raw organization settings:', JSON.stringify(orgSettings, null, 2));
    console.log('⚠️ Settings fetch error:', settingsError);

    // Extract VAT rate from settings, default to 0 if not found
    let vatRate = 0;
    if (orgSettings?.settings) {
      const settings = orgSettings.settings as any; // Cast to any for flexible property access

      console.log('🔧 Settings object:', JSON.stringify(settings, null, 2));

      // Check for tax_settings.vat_rate first (new structure)
      if (settings.tax_settings?.vat_rate !== undefined) {
        vatRate = Number(settings.tax_settings.vat_rate);
        console.log(`✅ Found VAT rate in tax_settings.vat_rate: ${vatRate}%`);
      }
      // Fallback to direct tax_rate (old structure)
      else if (settings.tax_rate !== undefined) {
        vatRate = Number(settings.tax_rate);
        console.log(`✅ Found VAT rate in tax_rate: ${vatRate}%`);
      }
      else {
        console.log('❌ No VAT rate found in settings, using default 0%');
      }
    } else {
      console.log('❌ No settings object found, using default 0%');
    }

    console.log(`🎯 Final VAT Rate: ${vatRate}% for organization ${organizationId}`);

    // Calculate VAT amount only if VAT rate > 0
    const vatAmount = vatRate > 0 ? receiptTotal * vatRate / (100 + vatRate) : 0;

    console.log(`💰 Receipt Total: ₱${receiptTotal}, VAT Rate: ${vatRate}%, VAT Amount: ₱${vatAmount}`);

    if (settingsError) {
      console.warn('⚠️ Could not fetch organization settings, using 0% VAT:', settingsError.message);
    }

    // Calculate due date - use default 30 days
    const paymentTerms = 30; // TODO: Get from supplier settings when available
    const dueDate = new Date(receipt.receipt_date);
    dueDate.setDate(dueDate.getDate() + paymentTerms);

    // Generate reference number
    const referenceNumber = `INV-${receipt.receipt_number}`;

    // Create payable
    const { data: newPayable, error: createError } = await supabase
      .from('payables')
      .insert({
        organization_id: organizationId,
        source_type: 'purchase_receipt',
        source_id: receiptId,
        supplier_id: receipt.purchase_order.supplier_id,
        reference_number: referenceNumber,
        invoice_date: receipt.receipt_date,
        payable_date: new Date().toISOString(),
        due_date: dueDate.toISOString(),
        amount: receiptTotal,
        vat_amount: vatAmount,
        balance: receiptTotal,
        currency: 'PHP',
        status: 'open',
        category: 'inventory',
        notes: `Manually created from receipt: ${receipt.receipt_number} (PO: ${receipt.purchase_order.order_number}). Amount calculated based on QC status - failed items excluded from payment.`,
        created_by: userId
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating payable:', createError);
      return { success: false, error: createError.message };
    }

    return { success: true, payable: newPayable };
  } catch (error: any) {
    console.error('Error in createPayableFromReceipt:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Validate withholding tax calculation
 */
export const validateWithholdingTax = (
  amount: number,
  vatAmount: number = 0,
  withholdingTaxRate: number = 0
): { isValid: boolean; calculatedAmount: number; error?: string } => {
  try {
    const netAmount = amount - vatAmount;
    const calculatedAmount = netAmount * withholdingTaxRate / 100;

    return {
      isValid: true,
      calculatedAmount: Math.round(calculatedAmount * 100) / 100 // Round to 2 decimal places
    };
  } catch (error: any) {
    return {
      isValid: false,
      calculatedAmount: 0,
      error: error.message
    };
  }
};
