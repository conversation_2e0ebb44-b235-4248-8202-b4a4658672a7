import { supabase } from '../lib/supabase';
import { Database } from '../types/database.types';
import { PurchaseRequestWithItems } from './purchaseRequest';
import { convertQuantity } from './productUom';

// Define types
export type PurchaseOrder = Database['public']['Tables']['purchase_orders']['Row'];
export type PurchaseOrderInsert = Database['public']['Tables']['purchase_orders']['Insert'];
export type PurchaseOrderUpdate = Database['public']['Tables']['purchase_orders']['Update'];

export type PurchaseOrderItem = Database['public']['Tables']['purchase_order_items']['Row'];
export type PurchaseOrderItemInsert = Database['public']['Tables']['purchase_order_items']['Insert'];
export type PurchaseOrderItemUpdate = Database['public']['Tables']['purchase_order_items']['Update'];

export type PurchaseOrderWithItems = PurchaseOrder & {
  items: PurchaseOrderItemWithDetails[];
  supplier_name?: string;
  creator_name?: string;
};

export type PurchaseOrderItemWithDetails = PurchaseOrderItem & {
  product?: {
    id: string;
    name: string;
    sku?: string;
    description?: string;
  };
  uom?: {
    id: string;
    code: string;
    name: string;
  };
};

/**
 * Get all purchase orders for an organization
 */
export const getPurchaseOrders = async (
  organizationId: string,
  options?: {
    status?: string;
    searchQuery?: string;
    supplierId?: string;
  }
): Promise<{
  purchaseOrders: PurchaseOrderWithItems[];
  error?: string;
}> => {
  try {
    console.log('Fetching purchase orders for organization:', organizationId);

    let query = supabase
      .from('purchase_orders')
      .select(`
        *,
        items:purchase_order_items(
          *,
          product:product_id(id, name, sku, description),
          uom:uom_id(id, code, name)
        ),
        supplier:supplier_id(id, name)
      `)
      .eq('organization_id', organizationId);

    // Apply filters
    if (options?.status) {
      query = query.eq('status', options.status);
    }

    if (options?.supplierId) {
      query = query.eq('supplier_id', options.supplierId);
    }

    if (options?.searchQuery) {
      query = query.ilike('order_number', `%${options.searchQuery}%`);
    }

    // Order by created_at
    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching purchase orders:', error);
      return {
        purchaseOrders: [],
        error: error.message,
      };
    }

    if (!data || data.length === 0) {
      return {
        purchaseOrders: [],
      };
    }

    console.log('Found purchase orders:', data.length);

    // Transform the data
    const purchaseOrders = data.map((po: any) => ({
      ...po,
      supplier_name: po.supplier?.name || 'Unknown Supplier',
      creator_name: 'Staff', // We'll improve this later
    }));

    return {
      purchaseOrders: purchaseOrders as PurchaseOrderWithItems[],
    };
  } catch (error: any) {
    console.error('Error in getPurchaseOrders:', error);
    return {
      purchaseOrders: [],
      error: error.message,
    };
  }
};

/**
 * Get a purchase order by ID
 */
export const getPurchaseOrderById = async (
  organizationId: string,
  purchaseOrderId: string,
  options?: {
    includeDetails?: boolean;
  }
) => {
  try {
    console.log('Fetching purchase order by ID:', purchaseOrderId);

    // Fix the query syntax to avoid the parsing error
    let selectQuery = `
      *,
      items:purchase_order_items(
        *,
        product:product_id(*)
    `;
    
    // If including detailed information, add product_uoms as a separate join
    if (options?.includeDetails) {
      selectQuery += `, 
        product_uoms:product_id(product_uoms(
          *,
          uom:uom_id(*)
        ))
      `;
    }
    
    selectQuery += `,
        uom:uom_id(*)
      ),
      supplier:supplier_id(*)
    `;
    
    const query = supabase
      .from('purchase_orders')
      .select(selectQuery)
      .eq('organization_id', organizationId)
      .eq('id', purchaseOrderId)
      .single();

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching purchase order:', error);
      return {
        error: error.message,
      };
    }

    // Transform the data
    const purchaseOrder = {
      ...data,
      supplier_name: data.supplier?.name || 'Unknown Supplier',
      creator_name: 'Staff', // We'll improve this later
    };

    // Process items to ensure they have proper UOM info if available
    if (purchaseOrder.items && Array.isArray(purchaseOrder.items)) {
      purchaseOrder.items = purchaseOrder.items.map(item => {
        // Get product UOMs if available
        let productUoms = [];
        if (item.product_uoms && item.product_uoms.product_uoms) {
          productUoms = item.product_uoms.product_uoms;
        }
        
        // Add product_uoms to the product if available
        if (item.product && productUoms.length > 0) {
          item.product.product_uoms = productUoms;
        }
        
        return item;
      });
    }

    return {
      purchaseOrder: purchaseOrder as PurchaseOrderWithItems,
    };
  } catch (error: any) {
    console.error('Error in getPurchaseOrderById:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Create a purchase order from a purchase request
 */
export const createPurchaseOrderFromRequest = async (
  organizationId: string,
  purchaseRequestId: string,
  data: {
    supplierId: string;
    expectedDeliveryDate?: string;
    notes?: string;
    items: {
      requestItemId: string;
      quantity: number;
      uomId: string;
      unitPrice: number;
    }[];
  }
): Promise<{
  purchaseOrder?: PurchaseOrderWithItems;
  error?: string;
}> => {
  try {
    console.log('Creating purchase order from request:', { purchaseRequestId, data });

    // Generate order number
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const orderNumber = `PO-${year}${month}${day}-${random}`;

    // Calculate total amount - the base quantity will be calculated by the database trigger
    const totalAmount = data.items.reduce(
      (sum, item) => sum + (item.quantity * item.unitPrice),
      0
    );

    // Create the purchase order
    const { data: poData, error: poError } = await supabase
      .from('purchase_orders')
      .insert({
        organization_id: organizationId,
        purchase_request_id: purchaseRequestId,
        supplier_id: data.supplierId,
        order_number: orderNumber,
        status: 'draft',
        order_date: new Date().toISOString(),
        expected_delivery_date: data.expectedDeliveryDate,
        total_amount: totalAmount,
        notes: data.notes,
        created_by: (await supabase.auth.getUser()).data.user?.id || '',
      })
      .select()
      .single();

    if (poError) {
      console.error('Error creating purchase order:', poError);
      return {
        error: poError.message,
      };
    }

    console.log('Purchase order created:', poData);

    // Add items
    if (data.items.length > 0) {
      const itemsWithOrderId = data.items.map(item => {
        return {
          purchase_order_id: poData.id,
          product_id: '', // We'll get this from the request item
          quantity: item.quantity,
          unit_price: item.unitPrice,
          uom_id: item.uomId,
          received_quantity: 0,
          base_quantity: 0 // Will be calculated by database trigger based on product_id and uom_id
        };
      });

      // Get product IDs from request items
      const { data: requestItems } = await supabase
        .from('purchase_request_items')
        .select('id, product_id')
        .in('id', data.items.map(item => item.requestItemId));

      if (requestItems) {
        // Map product IDs to items
        for (let i = 0; i < itemsWithOrderId.length; i++) {
          const requestItem = requestItems.find(
            ri => ri.id === data.items[i].requestItemId
          );
          if (requestItem) {
            itemsWithOrderId[i].product_id = requestItem.product_id;
          }
        }
      }

      const { error: itemsError } = await supabase
        .from('purchase_order_items')
        .insert(itemsWithOrderId);

      if (itemsError) {
        console.error('Error adding purchase order items:', itemsError);
        return {
          error: itemsError.message,
        };
      }
    }

    // Get the complete purchase order with items
    return await getPurchaseOrderById(organizationId, poData.id);
  } catch (error: any) {
    console.error('Error in createPurchaseOrderFromRequest:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update an existing purchase order
 */
export const updatePurchaseOrder = async (
  organizationId: string,
  purchaseOrderId: string,
  data: {
    supplierId?: string;
    expectedDeliveryDate?: string;
    notes?: string;
    status?: 'draft' | 'sent' | 'partially_received' | 'received' | 'cancelled';
    items?: {
      id?: string; // If provided, update existing item
      productId: string;
      quantity: number;
      uomId: string;
      unitPrice: number;
    }[];
  }
): Promise<{
  purchaseOrder?: PurchaseOrderWithItems;
  error?: string;
}> => {
  try {
    console.log('Updating purchase order:', { purchaseOrderId, data });

    // Check if the purchase order exists and belongs to the organization
    const { purchaseOrder: existingPO, error: fetchError } = await getPurchaseOrderById(
      organizationId,
      purchaseOrderId
    );

    if (fetchError || !existingPO) {
      return {
        error: fetchError || 'Purchase order not found',
      };
    }

    // Only allow editing draft purchase orders
    if (existingPO.status !== 'draft' && !data.status) {
      return {
        error: 'Only draft purchase orders can be edited',
      };
    }

    // Prepare updates for the purchase order
    const updates: Partial<PurchaseOrderUpdate> = {};

    if (data.supplierId) {
      updates.supplier_id = data.supplierId;
    }

    if (data.expectedDeliveryDate) {
      updates.expected_delivery_date = data.expectedDeliveryDate;
    }

    if (data.notes !== undefined) {
      updates.notes = data.notes;
    }

    if (data.status) {
      updates.status = data.status;
    }

    // Calculate total amount if items are provided
    if (data.items && data.items.length > 0) {
      const totalAmount = data.items.reduce(
        (sum, item) => sum + (item.quantity * item.unitPrice),
        0
      );
      updates.total_amount = totalAmount;
    }

    // Update the purchase order
    if (Object.keys(updates).length > 0) {
      const { error: updateError } = await supabase
        .from('purchase_orders')
        .update(updates)
        .eq('organization_id', organizationId)
        .eq('id', purchaseOrderId);

      if (updateError) {
        console.error('Error updating purchase order:', updateError);
        return {
          error: updateError.message,
        };
      }
    }

    // Handle items if provided
    if (data.items && data.items.length > 0) {
      // Get existing items
      const { data: existingItems, error: itemsError } = await supabase
        .from('purchase_order_items')
        .select('id, product_id')
        .eq('purchase_order_id', purchaseOrderId);

      if (itemsError) {
        console.error('Error fetching existing items:', itemsError);
        return {
          error: itemsError.message,
        };
      }

      // Identify items to update and items to create
      const itemsToUpdate: any[] = [];
      const itemsToCreate: any[] = [];

      data.items.forEach(item => {
        if (item.id) {
          // This is an existing item to update
          itemsToUpdate.push({
            id: item.id,
            product_id: item.productId,
            quantity: item.quantity,
            unit_price: item.unitPrice,
            uom_id: item.uomId,
          });
        } else {
          // This is a new item to create
          itemsToCreate.push({
            purchase_order_id: purchaseOrderId,
            product_id: item.productId,
            quantity: item.quantity,
            unit_price: item.unitPrice,
            uom_id: item.uomId,
            received_quantity: 0,
            base_quantity: 0, // Will be calculated by database trigger
          });
        }
      });

      // Update existing items
      if (itemsToUpdate.length > 0) {
        for (const item of itemsToUpdate) {
          const { error: updateItemError } = await supabase
            .from('purchase_order_items')
            .update({
              product_id: item.product_id,
              quantity: item.quantity,
              unit_price: item.unit_price,
              uom_id: item.uom_id,
            })
            .eq('id', item.id)
            .eq('purchase_order_id', purchaseOrderId);

          if (updateItemError) {
            console.error('Error updating purchase order item:', updateItemError);
            return {
              error: updateItemError.message,
            };
          }
        }
      }

      // Create new items
      if (itemsToCreate.length > 0) {
        const { error: createItemsError } = await supabase
          .from('purchase_order_items')
          .insert(itemsToCreate);

        if (createItemsError) {
          console.error('Error creating purchase order items:', createItemsError);
          return {
            error: createItemsError.message,
          };
        }
      }

      // Identify items to delete (items in the database but not in the update)
      const existingItemIds = existingItems.map(item => item.id);
      const updatedItemIds = itemsToUpdate.map(item => item.id);
      const itemsToDelete = existingItemIds.filter(id => !updatedItemIds.includes(id));

      if (itemsToDelete.length > 0) {
        const { error: deleteItemsError } = await supabase
          .from('purchase_order_items')
          .delete()
          .in('id', itemsToDelete);

        if (deleteItemsError) {
          console.error('Error deleting purchase order items:', deleteItemsError);
          return {
            error: deleteItemsError.message,
          };
        }
      }
    }

    // Get the updated purchase order
    return await getPurchaseOrderById(organizationId, purchaseOrderId);
  } catch (error: any) {
    console.error('Error in updatePurchaseOrder:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Update purchase order status to sent
 */
export const sendPurchaseOrderToSupplier = async (
  organizationId: string,
  purchaseOrderId: string
): Promise<{
  purchaseOrder?: PurchaseOrderWithItems;
  error?: string;
}> => {
  try {
    console.log('Sending purchase order to supplier:', purchaseOrderId);

    // Check if the purchase order exists and belongs to the organization
    const { purchaseOrder: existingPO, error: fetchError } = await getPurchaseOrderById(
      organizationId,
      purchaseOrderId
    );

    if (fetchError || !existingPO) {
      return {
        error: fetchError || 'Purchase order not found',
      };
    }

    // Only allow sending draft purchase orders
    if (existingPO.status !== 'draft') {
      return {
        error: `Cannot send purchase order with status "${existingPO.status}" to supplier`,
      };
    }

    // Update the purchase order status to sent
    const { error: updateError } = await supabase
      .from('purchase_orders')
      .update({
        status: 'sent',
        updated_at: new Date().toISOString(),
      })
      .eq('organization_id', organizationId)
      .eq('id', purchaseOrderId);

    if (updateError) {
      console.error('Error updating purchase order status:', updateError);
      return {
        error: updateError.message,
      };
    }

    // Get the updated purchase order
    return await getPurchaseOrderById(organizationId, purchaseOrderId);
  } catch (error: any) {
    console.error('Error in sendPurchaseOrderToSupplier:', error);
    return {
      error: error.message,
    };
  }
};

export const createPurchaseOrder = async (
  organizationId: string,
  data: {
    supplierId: string;
    expectedDeliveryDate?: string;
    notes?: string;
    items: {
      productId: string;
      quantity: number;
      uomId: string;
      unitPrice: number;
      conversionFactor?: number;
    }[];
  }
): Promise<{
  purchaseOrder?: PurchaseOrderWithItems;
  error?: string;
}> => {
  try {
    console.log('Creating purchase order:', data);

    // Generate order number
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const orderNumber = `PO-${year}${month}${day}-${random}`;

    // Calculate total amount - the base quantity will be calculated by the database trigger
    const totalAmount = data.items.reduce(
      (sum, item) => sum + (item.quantity * item.unitPrice),
      0
    );

    // Create the purchase order
    const { data: poData, error: poError } = await supabase
      .from('purchase_orders')
      .insert({
        organization_id: organizationId,
        supplier_id: data.supplierId,
        order_number: orderNumber,
        status: 'draft',
        order_date: new Date().toISOString(),
        expected_delivery_date: data.expectedDeliveryDate,
        total_amount: totalAmount,
        notes: data.notes,
        created_by: (await supabase.auth.getUser()).data.user?.id || '',
      })
      .select()
      .single();

    if (poError) {
      console.error('Error creating purchase order:', poError);
      return {
        error: poError.message,
      };
    }

    console.log('Purchase order created:', poData);

    // Add items
    if (data.items.length > 0) {
      const itemsWithOrderId = data.items.map(item => {
        return {
          purchase_order_id: poData.id,
          product_id: item.productId,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          uom_id: item.uomId,
          received_quantity: 0,
          base_quantity: 0 // Will be calculated by database trigger based on product_id and uom_id
        };
      });

      const { error: itemsError } = await supabase
        .from('purchase_order_items')
        .insert(itemsWithOrderId);

      if (itemsError) {
        console.error('Error adding purchase order items:', itemsError);
        return {
          error: itemsError.message,
        };
      }
    }

    // Get the complete purchase order with items
    return await getPurchaseOrderById(organizationId, poData.id);
  } catch (error: any) {
    console.error('Error in createPurchaseOrder:', error);
    return {
      error: error.message,
    };
  }
};

/**
 * Get purchase orders by purchase request ID
 * @param organizationId Organization ID
 * @param purchaseRequestId Purchase request ID
 * @returns List of purchase orders related to the purchase request
 */
export const getPurchaseOrdersByRequestId = async (
  organizationId: string,
  purchaseRequestId: string
) => {
  try {
    if (!organizationId || !purchaseRequestId) {
      return { purchaseOrders: [], error: 'Organization ID and purchase request ID are required' };
    }

    const { data, error } = await supabase
      .from('purchase_orders')
      .select(`
        *,
        supplier:supplier_id(name)
      `)
      .eq('organization_id', organizationId)
      .eq('purchase_request_id', purchaseRequestId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching purchase orders by request ID:', error);
      return { purchaseOrders: [], error: error.message };
    }

    // Transform the data to include supplier_name for easier access
    const purchaseOrders = data.map(order => ({
      ...order,
      supplier_name: order.supplier ? order.supplier.name : 'Unknown Supplier'
    }));

    return { purchaseOrders, error: null };
  } catch (error: any) {
    console.error('Exception in getPurchaseOrdersByRequestId:', error);
    return {
      purchaseOrders: [],
      error: error.message || 'An error occurred while fetching purchase orders'
    };
  }
};
