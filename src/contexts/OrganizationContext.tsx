import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '../lib/supabase';

// Define the Organization type
export interface Organization {
  id: string;
  name: string;
  logo_url?: string | null;
  created_at: string;
  updated_at: string;
}

// Define the context type
interface OrganizationContextType {
  organizations: Organization[];
  currentOrganization: Organization | null;
  setCurrentOrganization: (organization: Organization | null) => void;
  isLoading: boolean;
  error: string | null;
  fetchOrganizations: () => Promise<void>;
}

// Create the context with default values
const OrganizationContext = createContext<OrganizationContextType>({
  organizations: [],
  currentOrganization: null,
  setCurrentOrganization: () => {},
  isLoading: false,
  error: null,
  fetchOrganizations: async () => {},
});

// Custom hook to use the organization context
export const useOrganization = () => useContext(OrganizationContext);

// Provider component
interface OrganizationProviderProps {
  children: ReactNode;
}

export const OrganizationProvider: React.FC<OrganizationProviderProps> = ({ children }) => {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch organizations from Supabase
  const fetchOrganizations = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setIsLoading(false);
        return;
      }

      // Get organizations the user is a member of
      const { data: memberData, error: memberError } = await supabase
        .from('organization_members')
        .select('organization_id')
        .eq('user_id', user.id);

      if (memberError) {
        setError(memberError.message);
        setIsLoading(false);
        return;
      }

      if (!memberData || memberData.length === 0) {
        setIsLoading(false);
        return;
      }

      const organizationIds = memberData.map(member => member.organization_id);

      // Get organization details
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .in('id', organizationIds);

      if (orgError) {
        setError(orgError.message);
        setIsLoading(false);
        return;
      }

      setOrganizations(orgData as Organization[]);

      // Set the current organization if not already set
      if (orgData && orgData.length > 0 && !currentOrganization) {
        // Try to get the last selected organization from localStorage
        const savedOrgId = localStorage.getItem('currentOrganizationId');
        
        if (savedOrgId) {
          const savedOrg = orgData.find(org => org.id === savedOrgId);
          if (savedOrg) {
            setCurrentOrganization(savedOrg as Organization);
          } else {
            setCurrentOrganization(orgData[0] as Organization);
          }
        } else {
          setCurrentOrganization(orgData[0] as Organization);
        }
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Set up auth state listener
  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange((event) => {
      if (event === 'SIGNED_IN') {
        fetchOrganizations();
      } else if (event === 'SIGNED_OUT') {
        setOrganizations([]);
        setCurrentOrganization(null);
      }
    });

    // Initial fetch
    fetchOrganizations();

    // Cleanup
    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  // Save current organization to localStorage when it changes
  useEffect(() => {
    if (currentOrganization) {
      localStorage.setItem('currentOrganizationId', currentOrganization.id);
    } else {
      localStorage.removeItem('currentOrganizationId');
    }
  }, [currentOrganization]);

  // Custom setter for currentOrganization that also updates localStorage
  const handleSetCurrentOrganization = (organization: Organization | null) => {
    setCurrentOrganization(organization);
  };

  return (
    <OrganizationContext.Provider
      value={{
        organizations,
        currentOrganization,
        setCurrentOrganization: handleSetCurrentOrganization,
        isLoading,
        error,
        fetchOrganizations,
      }}
    >
      {children}
    </OrganizationContext.Provider>
  );
};

export default OrganizationContext;
