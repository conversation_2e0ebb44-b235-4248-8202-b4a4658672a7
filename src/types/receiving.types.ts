// Enhanced Receiving System Types
// Comprehensive types for partial receiving, over-receiving, and quality control

export interface ReceivingTransaction {
  id: string;
  organization_id: string;
  inventory_receipt_id: string;
  inventory_receipt_item_id: string;
  transaction_type: 'received' | 'accepted' | 'rejected' | 'returned' | 'adjusted';
  quantity: number;
  unit_cost: number;
  total_cost: number;
  reason?: string;
  notes?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface ReceivingDiscrepancy {
  id: string;
  organization_id: string;
  inventory_receipt_id: string;
  inventory_receipt_item_id: string;
  discrepancy_type: 'quantity_variance' | 'quality_issue' | 'price_variance' | 'delivery_timing' | 'documentation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  expected_value?: string;
  actual_value?: string;
  financial_impact: number;
  status: 'open' | 'investigating' | 'resolved' | 'closed' | 'escalated';
  resolution_notes?: string;
  resolved_by?: string;
  resolved_at?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface EnhancedReceiptItem {
  id: string;
  inventory_receipt_id: string;
  purchase_order_item_id?: string;
  product_id: string;
  product?: {
    id: string;
    name: string;
    sku?: string;
    description?: string;
  };
  
  // Quantity tracking
  quantity: number; // Original ordered quantity
  expected_quantity?: number; // Expected quantity for this receipt
  received_quantity: number; // Total quantity physically received
  accepted_quantity: number; // Quantity that passed QC and was accepted
  rejected_quantity: number; // Quantity that failed QC and was rejected
  over_received_quantity: number; // Quantity received above expected
  
  // UoM and costing
  uom_id: string;
  uom?: {
    id: string;
    name: string;
    abbreviation: string;
  };
  unit_cost: number;
  expected_unit_cost?: number;
  cost_variance?: number;
  conversion_factor: number;
  base_quantity: number;
  
  // Quality control
  qc_status: 'passed' | 'failed' | 'quarantine' | 'pending';
  damaged_quantity: number;
  damage_reason?: string;
  
  // Rejection details
  rejection_reason?: string;
  rejection_category?: 'damaged' | 'defective' | 'wrong_item' | 'expired' | 'quality_fail' | 'other';
  rejection_disposition?: 'return_to_supplier' | 'dispose' | 'rework' | 'use_as_is' | 'pending';
  rejection_cost_impact: number;
  
  // Receiving workflow
  receiving_status: 'pending' | 'partial' | 'complete' | 'over_received' | 'rejected' | 'mixed';
  
  // Over-receiving approval
  over_received_approved: boolean;
  over_received_approved_by?: string;
  over_received_approved_at?: string;
  
  // Inspection workflow
  inspection_required: boolean;
  inspection_completed: boolean;
  inspection_completed_by?: string;
  inspection_completed_at?: string;
  inspection_notes?: string;
  
  // Supplier communication
  supplier_notified: boolean;
  supplier_notified_at?: string;
  supplier_response?: string;
  supplier_response_date?: string;
  
  // Batch/lot tracking
  lot_number?: string;
  expiry_date?: string;
  serial_numbers?: string[];
  
  // Variance tracking
  variance_quantity?: number;
  variance_reason?: string;
  
  // Approval workflow
  cost_variance_approved: boolean;
  cost_variance_approved_by?: string;
  cost_variance_approved_at?: string;
  
  created_at: string;
  updated_at: string;
}

export interface ReceivingWorkflowStep {
  step: string;
  status: 'pending' | 'in_progress' | 'completed' | 'skipped' | 'failed';
  completed_by?: string;
  completed_at?: string;
  notes?: string;
}

export interface ReceivingWorkflow {
  receipt_id: string;
  receipt_item_id: string;
  steps: ReceivingWorkflowStep[];
  current_step: string;
  overall_status: 'pending' | 'in_progress' | 'completed' | 'requires_approval' | 'escalated';
}

export interface ReceivingMetrics {
  total_items: number;
  pending_items: number;
  partial_items: number;
  complete_items: number;
  over_received_items: number;
  rejected_items: number;
  mixed_items: number;
  
  total_expected_value: number;
  total_received_value: number;
  total_accepted_value: number;
  total_rejected_value: number;
  total_variance_value: number;
  
  completion_percentage: number;
  acceptance_rate: number;
  rejection_rate: number;
  over_receiving_rate: number;
}

export interface ReceivingAction {
  type: 'receive' | 'accept' | 'reject' | 'approve_over_receipt' | 'return_to_supplier' | 'escalate';
  item_id: string;
  quantity?: number;
  reason?: string;
  notes?: string;
  disposition?: string;
}

export interface ReceivingBatch {
  items: ReceivingAction[];
  notes?: string;
  auto_create_discrepancies: boolean;
  notify_supplier: boolean;
}

export interface PayableCalculation {
  total_amount: number;
  accepted_amount: number;
  rejected_amount: number;
  over_received_amount: number;
  payable_amount: number;
  variance_amount: number;
  requires_approval: boolean;
}

export interface ReceivingValidation {
  is_valid: boolean;
  errors: string[];
  warnings: string[];
  requires_approval: boolean;
  approval_reasons: string[];
}

// Form interfaces for UI components
export interface ReceivingFormItem extends EnhancedReceiptItem {
  // UI-specific fields
  isSelected?: boolean;
  isEditing?: boolean;
  hasChanges?: boolean;
  validationErrors?: string[];
  
  // Temporary form values
  temp_received_quantity?: number;
  temp_accepted_quantity?: number;
  temp_rejected_quantity?: number;
  temp_rejection_reason?: string;
  temp_rejection_category?: string;
  temp_notes?: string;
}

export interface ReceivingFormData {
  receipt_id: string;
  items: ReceivingFormItem[];
  notes?: string;
  require_inspection: boolean;
  auto_approve_over_receipts: boolean;
  notify_supplier_of_discrepancies: boolean;
}

// API response interfaces
export interface ReceivingProcessResult {
  success: boolean;
  receipt_id: string;
  processed_items: number;
  discrepancies_created: number;
  requires_approval: boolean;
  payable_amount: number;
  error?: string;
  warnings?: string[];
}

export interface ReceivingStatusSummary {
  receipt_id: string;
  receipt_number: string;
  status: string;
  metrics: ReceivingMetrics;
  discrepancies: ReceivingDiscrepancy[];
  requires_attention: boolean;
  next_actions: string[];
}

// Utility types
export type ReceivingStatus = 'pending' | 'partial' | 'complete' | 'over_received' | 'rejected' | 'mixed';
export type QCStatus = 'passed' | 'failed' | 'quarantine' | 'pending';
export type RejectionCategory = 'damaged' | 'defective' | 'wrong_item' | 'expired' | 'quality_fail' | 'other';
export type RejectionDisposition = 'return_to_supplier' | 'dispose' | 'rework' | 'use_as_is' | 'pending';
export type DiscrepancyType = 'quantity_variance' | 'quality_issue' | 'price_variance' | 'delivery_timing' | 'documentation';
export type DiscrepancySeverity = 'low' | 'medium' | 'high' | 'critical';
export type TransactionType = 'received' | 'accepted' | 'rejected' | 'returned' | 'adjusted';
