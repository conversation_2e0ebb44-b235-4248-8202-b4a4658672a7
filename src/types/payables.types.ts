import { Database } from './database.types';

// Base types from database
export type Payable = Database['public']['Tables']['payables']['Row'];
export type PayableInsert = Database['public']['Tables']['payables']['Insert'];
export type PayableUpdate = Database['public']['Tables']['payables']['Update'];

export type PayablePayment = Database['public']['Tables']['payable_payments']['Row'];
export type PayablePaymentInsert = Database['public']['Tables']['payable_payments']['Insert'];
export type PayablePaymentUpdate = Database['public']['Tables']['payable_payments']['Update'];

// Enums
export enum PayableSourceType {
  PURCHASE_RECEIPT = 'purchase_receipt',
  PAYROLL = 'payroll',
  UTILITY_BILL = 'utility_bill',
  GOVERNMENT_REMITTANCE = 'government_remittance',
  LOAN_REPAYMENT = 'loan_repayment',
  MANUAL_ENTRY = 'manual_entry'
}

export enum PayableStatus {
  DRAFT = 'draft',
  OPEN = 'open',
  PARTIALLY_PAID = 'partially_paid',
  PAID = 'paid',
  CANCELLED = 'cancelled'
}

export enum PaymentMethod {
  CASH = 'cash',
  CHECK = 'check',
  BANK_TRANSFER = 'bank_transfer',
  GCASH = 'gcash',
  PAYMAYA = 'paymaya',
  CREDIT_CARD = 'credit_card',
  OTHER = 'other'
}

// Extended types with relationships
export interface PayableWithDetails extends Payable {
  supplier?: {
    id: string;
    name: string;
    contact_person?: string;
    email?: string;
    phone?: string;
  };
  employee?: {
    id: string;
    first_name: string;
    last_name: string;
    employee_number: string;
  };
  payments?: PayablePayment[];
  total_paid?: number;
  source_details?: any; // Will contain details from the source table
}

export interface PayablePaymentWithDetails extends PayablePayment {
  payable?: Payable;
}

// Filter and search options
export interface PayableFilters {
  status?: PayableStatus;
  source_type?: PayableSourceType;
  supplier_id?: string;
  employee_id?: string;
  due_date_from?: string;
  due_date_to?: string;
  amount_from?: number;
  amount_to?: number;
  search_query?: string;
}

export interface PayableListOptions {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: PayableFilters;
}

// Create payable request
export interface CreatePayableRequest {
  source_type: PayableSourceType;
  source_id: string;
  supplier_id?: string;
  employee_id?: string;
  reference_number: string;
  invoice_date: string;
  due_date: string;
  amount: number;
  vat_amount?: number;
  withholding_tax_rate?: number;
  withholding_tax_amount?: number;
  currency?: string;
  category?: string;
  invoice_url?: string;
  notes?: string;
}

// Create payment request
export interface CreatePaymentRequest {
  payable_id: string;
  payment_date: string;
  amount_paid: number;
  payment_method: PaymentMethod;
  reference_number?: string;
  attachment_url?: string;
  remarks?: string;
}

// Aging report types
export interface PayableAgingBucket {
  label: string;
  days_from: number;
  days_to: number | null;
  count: number;
  total_amount: number;
}

export interface PayableAgingReport {
  organization_id: string;
  as_of_date: string;
  buckets: PayableAgingBucket[];
  total_count: number;
  total_amount: number;
  payables: PayableWithDetails[];
}

// Summary statistics
export interface PayableSummary {
  total_payables: number;
  total_amount: number;
  total_paid: number;
  total_outstanding: number;
  overdue_count: number;
  overdue_amount: number;
  by_status: {
    [key in PayableStatus]: {
      count: number;
      amount: number;
    };
  };
  by_source_type: {
    [key in PayableSourceType]: {
      count: number;
      amount: number;
    };
  };
}

// Voucher generation
export interface PayableVoucher {
  payable: PayableWithDetails;
  voucher_number: string;
  voucher_date: string;
  prepared_by: string;
  approved_by?: string;
  accounting_entries: AccountingEntry[];
}

export interface AccountingEntry {
  account_code: string;
  account_name: string;
  debit_amount: number;
  credit_amount: number;
  description: string;
}

// Form 2307 (Certificate of Creditable Tax Withheld at Source)
export interface Form2307Data {
  supplier_id: string;
  supplier_name: string;
  supplier_tin: string;
  period_from: string;
  period_to: string;
  total_amount_paid: number;
  total_tax_withheld: number;
  payables: PayableWithDetails[];
}

// API Response types
export interface PayableResponse {
  payable?: PayableWithDetails;
  payables?: PayableWithDetails[];
  total_count?: number;
  success: boolean;
  error?: string;
}

export interface PaymentResponse {
  payment?: PayablePaymentWithDetails;
  payments?: PayablePaymentWithDetails[];
  success: boolean;
  error?: string;
}

export interface PayableSummaryResponse {
  summary?: PayableSummary;
  success: boolean;
  error?: string;
}

export interface PayableAgingResponse {
  aging_report?: PayableAgingReport;
  success: boolean;
  error?: string;
}
