import { Database } from './database.types';

// Base types from database
export type Refund = Database['public']['Tables']['refunds']['Row'];
export type RefundInsert = Database['public']['Tables']['refunds']['Insert'];
export type RefundUpdate = Database['public']['Tables']['refunds']['Update'];

export type RefundItem = Database['public']['Tables']['refund_items']['Row'];
export type RefundItemInsert = Database['public']['Tables']['refund_items']['Insert'];
export type RefundItemUpdate = Database['public']['Tables']['refund_items']['Update'];

// Enums
export enum RefundType {
  FULL = 'full',
  PARTIAL = 'partial',
  EXCHANGE = 'exchange',
  STORE_CREDIT = 'store_credit'
}

export enum RefundStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PROCESSED = 'processed',
  CANCELLED = 'cancelled'
}

export enum RefundReason {
  DEFECTIVE = 'defective',
  WRONG_ITEM = 'wrong_item',
  CUSTOMER_CHANGED_MIND = 'customer_changed_mind',
  DAMAGED_IN_TRANSIT = 'damaged_in_transit',
  NOT_AS_DESCRIBED = 'not_as_described',
  DUPLICATE_ORDER = 'duplicate_order',
  OTHER = 'other'
}

export enum RefundMethod {
  CASH = 'cash',
  CARD = 'card',
  STORE_CREDIT = 'store_credit',
  ORIGINAL_PAYMENT = 'original_payment'
}

export enum ItemCondition {
  NEW = 'new',
  USED = 'used',
  DAMAGED = 'damaged',
  DEFECTIVE = 'defective'
}

// Extended types with relationships
export interface RefundWithDetails extends Refund {
  original_sale?: {
    id: string;
    invoice_number: string;
    total_amount: number;
    sale_date: string;
    customer?: {
      id: string;
      name: string;
      email?: string;
      phone?: string;
    };
  };
  refund_items?: RefundItemWithDetails[];
  processed_by?: {
    id: string;
    first_name: string;
    last_name: string;
  };
  approved_by?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

export interface RefundItemWithDetails extends RefundItem {
  product?: {
    id: string;
    name: string;
    sku: string;
    unit_price: number;
    image_url?: string;
  };
  original_sale_item?: {
    id: string;
    quantity: number;
    unit_price: number;
    total_price: number;
  };
}

// Request types
export interface CreateRefundRequest {
  original_sale_id: string;
  refund_type: RefundType;
  reason: RefundReason;
  reason_notes?: string;
  refund_method: RefundMethod;
  customer_id?: string;
  items: CreateRefundItemRequest[];
  restocking_fee?: number;
  requires_approval?: boolean;
}

export interface CreateRefundItemRequest {
  sale_item_id: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  condition: ItemCondition;
  restore_inventory: boolean;
  notes?: string;
}

export interface ProcessRefundRequest {
  refund_id: string;
  approved: boolean;
  approval_notes?: string;
  processed_by: string;
}

// Response types
export interface RefundResponse {
  success: boolean;
  data?: RefundWithDetails;
  error?: string;
}

export interface RefundListResponse {
  success: boolean;
  data?: RefundWithDetails[];
  total_count?: number;
  error?: string;
}

// Filter and search types
export interface RefundFilters {
  status?: RefundStatus;
  refund_type?: RefundType;
  reason?: RefundReason;
  refund_method?: RefundMethod;
  date_from?: string;
  date_to?: string;
  customer_id?: string;
  requires_approval?: boolean;
  search?: string;
}

export interface RefundListOptions {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: RefundFilters;
}

// Summary and analytics types
export interface RefundSummary {
  total_refunds: number;
  total_amount: number;
  pending_approvals: number;
  pending_amount: number;
  processed_today: number;
  processed_amount_today: number;
  by_reason: {
    [key in RefundReason]: {
      count: number;
      amount: number;
    };
  };
  by_method: {
    [key in RefundMethod]: {
      count: number;
      amount: number;
    };
  };
}

// Validation types
export interface RefundValidation {
  can_refund: boolean;
  reasons: string[];
  max_refund_amount: number;
  eligible_items: {
    sale_item_id: string;
    product_id: string;
    max_quantity: number;
    unit_price: number;
  }[];
}
