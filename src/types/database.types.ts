export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      departments: {
        Row: {
          id: string
          organization_id: string
          name: string
          description: string | null
          manager_id: string | null
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          name: string
          description?: string | null
          manager_id?: string | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          name?: string
          description?: string | null
          manager_id?: string | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "departments_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "departments_manager_id_fkey"
            columns: ["manager_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      employment_types: {
        Row: {
          id: string
          organization_id: string
          name: string
          description: string | null
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          name: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          name?: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "employment_types_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      }
      job_positions: {
        Row: {
          id: string
          organization_id: string
          department_id: string | null
          title: string
          description: string | null
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          department_id?: string | null
          title: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          department_id?: string | null
          title?: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "job_positions_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_positions_department_id_fkey"
            columns: ["department_id"]
            referencedRelation: "departments"
            referencedColumns: ["id"]
          }
        ]
      }
      employees: {
        Row: {
          id: string
          organization_id: string
          user_id: string | null
          employee_number: string | null
          first_name: string
          middle_name: string | null
          last_name: string
          email: string | null
          phone: string | null
          date_of_birth: string | null
          gender: string | null
          marital_status: string | null
          nationality: string | null
          address: string | null
          city: string | null
          state: string | null
          postal_code: string | null
          country: string | null
          emergency_contact_name: string | null
          emergency_contact_phone: string | null
          emergency_contact_relationship: string | null
          department_id: string | null
          position_id: string | null
          employment_type_id: string | null
          hire_date: string | null
          end_date: string | null
          status: string | null
          is_active: boolean | null
          profile_image_url: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          user_id?: string | null
          employee_number?: string | null
          first_name: string
          middle_name?: string | null
          last_name: string
          email?: string | null
          phone?: string | null
          date_of_birth?: string | null
          gender?: string | null
          marital_status?: string | null
          nationality?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          postal_code?: string | null
          country?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          emergency_contact_relationship?: string | null
          department_id?: string | null
          position_id?: string | null
          employment_type_id?: string | null
          hire_date?: string | null
          end_date?: string | null
          status?: string | null
          is_active?: boolean | null
          profile_image_url?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          user_id?: string | null
          employee_number?: string | null
          first_name?: string
          middle_name?: string | null
          last_name?: string
          email?: string | null
          phone?: string | null
          date_of_birth?: string | null
          gender?: string | null
          marital_status?: string | null
          nationality?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          postal_code?: string | null
          country?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          emergency_contact_relationship?: string | null
          department_id?: string | null
          position_id?: string | null
          employment_type_id?: string | null
          hire_date?: string | null
          end_date?: string | null
          status?: string | null
          is_active?: boolean | null
          profile_image_url?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "employees_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "employees_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "employees_department_id_fkey"
            columns: ["department_id"]
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "employees_position_id_fkey"
            columns: ["position_id"]
            referencedRelation: "job_positions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "employees_employment_type_id_fkey"
            columns: ["employment_type_id"]
            referencedRelation: "employment_types"
            referencedColumns: ["id"]
          }
        ]
      }
      employee_government_ids: {
        Row: {
          id: string
          employee_id: string
          sss_number: string | null
          philhealth_number: string | null
          pagibig_number: string | null
          tin_number: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          employee_id: string
          sss_number?: string | null
          philhealth_number?: string | null
          pagibig_number?: string | null
          tin_number?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          employee_id?: string
          sss_number?: string | null
          philhealth_number?: string | null
          pagibig_number?: string | null
          tin_number?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "employee_government_ids_employee_id_fkey"
            columns: ["employee_id"]
            referencedRelation: "employees"
            referencedColumns: ["id"]
          }
        ]
      }
      employee_salary: {
        Row: {
          id: string
          employee_id: string
          basic_salary: number
          effective_date: string
          end_date: string | null
          currency: string | null
          pay_frequency: string | null
          tax_exemption_status: string | null
          allowances: Json | null
          deductions: Json | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          employee_id: string
          basic_salary: number
          effective_date: string
          end_date?: string | null
          currency?: string | null
          pay_frequency?: string | null
          tax_exemption_status?: string | null
          allowances?: Json | null
          deductions?: Json | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          employee_id?: string
          basic_salary?: number
          effective_date?: string
          end_date?: string | null
          currency?: string | null
          pay_frequency?: string | null
          tax_exemption_status?: string | null
          allowances?: Json | null
          deductions?: Json | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "employee_salary_employee_id_fkey"
            columns: ["employee_id"]
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "employee_salary_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      employee_documents: {
        Row: {
          id: string
          employee_id: string
          document_type: string
          document_name: string
          file_url: string
          file_type: string | null
          file_size: number | null
          uploaded_by: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          employee_id: string
          document_type: string
          document_name: string
          file_url: string
          file_type?: string | null
          file_size?: number | null
          uploaded_by?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          employee_id?: string
          document_type?: string
          document_name?: string
          file_url?: string
          file_type?: string | null
          file_size?: number | null
          uploaded_by?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "employee_documents_employee_id_fkey"
            columns: ["employee_id"]
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "employee_documents_uploaded_by_fkey"
            columns: ["uploaded_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      employee_dependents: {
        Row: {
          id: string
          employee_id: string
          first_name: string
          middle_name: string | null
          last_name: string
          relationship: string
          date_of_birth: string | null
          is_beneficiary: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          employee_id: string
          first_name: string
          middle_name?: string | null
          last_name: string
          relationship: string
          date_of_birth?: string | null
          is_beneficiary?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          employee_id?: string
          first_name?: string
          middle_name?: string | null
          last_name?: string
          relationship?: string
          date_of_birth?: string | null
          is_beneficiary?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "employee_dependents_employee_id_fkey"
            columns: ["employee_id"]
            referencedRelation: "employees"
            referencedColumns: ["id"]
          }
        ]
      }
      categories: {
        Row: {
          id: string
          organization_id: string
          name: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          name: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          name?: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "categories_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      }
      customers: {
        Row: {
          id: string
          organization_id: string
          name: string
          email: string | null
          phone: string | null
          address: string | null
          city: string | null
          state: string | null
          postal_code: string | null
          country: string | null
          tax_id: string | null
          notes: string | null
          loyalty_eligible: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          name: string
          email?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          postal_code?: string | null
          country?: string | null
          tax_id?: string | null
          notes?: string | null
          loyalty_eligible?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          name?: string
          email?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          postal_code?: string | null
          country?: string | null
          tax_id?: string | null
          notes?: string | null
          loyalty_eligible?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "customers_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      }
      inventory_receipt_items: {
        Row: {
          id: string
          inventory_receipt_id: string
          purchase_order_item_id: string | null
          product_id: string
          quantity: number
          unit_cost: number
          uom_id: string
          base_quantity: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          inventory_receipt_id: string
          purchase_order_item_id?: string | null
          product_id: string
          quantity: number
          unit_cost: number
          uom_id: string
          base_quantity: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          inventory_receipt_id?: string
          purchase_order_item_id?: string | null
          product_id?: string
          quantity?: number
          unit_cost?: number
          uom_id?: string
          base_quantity?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "inventory_receipt_items_inventory_receipt_id_fkey"
            columns: ["inventory_receipt_id"]
            referencedRelation: "inventory_receipts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_receipt_items_product_id_fkey"
            columns: ["product_id"]
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_receipt_items_purchase_order_item_id_fkey"
            columns: ["purchase_order_item_id"]
            referencedRelation: "purchase_order_items"
            referencedColumns: ["id"]
          }
        ]
      }
      inventory_receipts: {
        Row: {
          id: string
          organization_id: string
          purchase_order_id: string | null
          receipt_number: string
          receipt_date: string
          status: string
          notes: string | null
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          purchase_order_id?: string | null
          receipt_number: string
          receipt_date?: string
          status?: string
          notes?: string | null
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          purchase_order_id?: string | null
          receipt_number?: string
          receipt_date?: string
          status?: string
          notes?: string | null
          created_by?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "inventory_receipts_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_receipts_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_receipts_purchase_order_id_fkey"
            columns: ["purchase_order_id"]
            referencedRelation: "purchase_orders"
            referencedColumns: ["id"]
          }
        ]
      }
      inventory_transactions: {
        Row: {
          id: string
          organization_id: string
          product_id: string
          transaction_type: string
          quantity: number
          uom_id: string
          reference_id: string | null
          reference_type: string | null
          notes: string | null
          created_by: string
          created_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          product_id: string
          transaction_type: string
          quantity: number
          uom_id: string
          reference_id?: string | null
          reference_type?: string | null
          notes?: string | null
          created_by: string
          created_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          product_id?: string
          transaction_type?: string
          quantity?: number
          uom_id?: string
          reference_id?: string | null
          reference_type?: string | null
          notes?: string | null
          created_by?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "inventory_transactions_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_transactions_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_transactions_product_id_fkey"
            columns: ["product_id"]
            referencedRelation: "products"
            referencedColumns: ["id"]
          }
        ]
      }
      organization_members: {
        Row: {
          id: string
          organization_id: string
          user_id: string
          role: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          user_id: string
          role: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          user_id?: string
          role?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "organization_members_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organization_members_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      organization_settings: {
        Row: {
          id: string
          organization_id: string
          settings: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          settings?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          settings?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "organization_settings_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      }
      organizations: {
        Row: {
          id: string
          name: string
          slug: string
          logo_url: string | null
          address: string | null
          phone: string | null
          email: string | null
          website: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          logo_url?: string | null
          address?: string | null
          phone?: string | null
          email?: string | null
          website?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          logo_url?: string | null
          address?: string | null
          phone?: string | null
          email?: string | null
          website?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          id: string
          first_name: string | null
          last_name: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      products: {
        Row: {
          id: string
          organization_id: string
          category_id: string | null
          name: string
          description: string | null
          sku: string | null
          barcode: string | null
          unit_price: number
          cost_price: number | null
          tax_rate: number | null
          stock_quantity: number | null
          min_stock_level: number | null
          image_url: string | null
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          category_id?: string | null
          name: string
          description?: string | null
          sku?: string | null
          barcode?: string | null
          unit_price: number
          cost_price?: number | null
          tax_rate?: number | null
          stock_quantity?: number | null
          min_stock_level?: number | null
          image_url?: string | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          category_id?: string | null
          name?: string
          description?: string | null
          sku?: string | null
          barcode?: string | null
          unit_price?: number
          cost_price?: number | null
          tax_rate?: number | null
          stock_quantity?: number | null
          min_stock_level?: number | null
          image_url?: string | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "products_category_id_fkey"
            columns: ["category_id"]
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      }
      purchase_order_items: {
        Row: {
          id: string
          purchase_order_id: string
          product_id: string
          quantity: number
          unit_price: number
          uom_id: string
          base_quantity: number
          received_quantity: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          purchase_order_id: string
          product_id: string
          quantity: number
          unit_price: number
          uom_id: string
          base_quantity: number
          received_quantity?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          purchase_order_id?: string
          product_id?: string
          quantity?: number
          unit_price?: number
          uom_id?: string
          base_quantity?: number
          received_quantity?: number | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "purchase_order_items_product_id_fkey"
            columns: ["product_id"]
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_order_items_purchase_order_id_fkey"
            columns: ["purchase_order_id"]
            referencedRelation: "purchase_orders"
            referencedColumns: ["id"]
          }
        ]
      }
      purchase_orders: {
        Row: {
          id: string
          organization_id: string
          purchase_request_id: string | null
          supplier_id: string
          order_number: string
          status: string
          order_date: string
          expected_delivery_date: string | null
          total_amount: number
          notes: string | null
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          purchase_request_id?: string | null
          supplier_id: string
          order_number: string
          status: string
          order_date?: string
          expected_delivery_date?: string | null
          total_amount: number
          notes?: string | null
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          purchase_request_id?: string | null
          supplier_id?: string
          order_number?: string
          status?: string
          order_date?: string
          expected_delivery_date?: string | null
          total_amount?: number
          notes?: string | null
          created_by?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "purchase_orders_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_orders_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_orders_purchase_request_id_fkey"
            columns: ["purchase_request_id"]
            referencedRelation: "purchase_requests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_orders_supplier_id_fkey"
            columns: ["supplier_id"]
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          }
        ]
      }
      purchase_request_items: {
        Row: {
          id: string
          purchase_request_id: string
          product_id: string
          quantity: number
          uom_id: string
          base_quantity: number
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          purchase_request_id: string
          product_id: string
          quantity: number
          uom_id: string
          base_quantity: number
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          purchase_request_id?: string
          product_id?: string
          quantity?: number
          uom_id?: string
          base_quantity?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "purchase_request_items_product_id_fkey"
            columns: ["product_id"]
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_request_items_purchase_request_id_fkey"
            columns: ["purchase_request_id"]
            referencedRelation: "purchase_requests"
            referencedColumns: ["id"]
          }
        ]
      }
      purchase_requests: {
        Row: {
          id: string
          organization_id: string
          request_number: string
          requester_id: string
          status: string
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          request_number: string
          requester_id: string
          status: string
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          request_number?: string
          requester_id?: string
          status?: string
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "purchase_requests_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_requests_requester_id_fkey"
            columns: ["requester_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      sale_items: {
        Row: {
          id: string
          sale_id: string
          product_id: string
          quantity: number
          unit_price: number
          uom_id: string
          base_quantity: number
          tax_rate: number | null
          tax_amount: number
          discount_amount: number | null
          total_amount: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          sale_id: string
          product_id: string
          quantity: number
          unit_price: number
          uom_id: string
          base_quantity: number
          tax_rate?: number | null
          tax_amount: number
          discount_amount?: number | null
          total_amount: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          sale_id?: string
          product_id?: string
          quantity?: number
          unit_price?: number
          uom_id?: string
          base_quantity?: number
          tax_rate?: number | null
          tax_amount?: number
          discount_amount?: number | null
          total_amount?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "sale_items_product_id_fkey"
            columns: ["product_id"]
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sale_items_sale_id_fkey"
            columns: ["sale_id"]
            referencedRelation: "sales"
            referencedColumns: ["id"]
          }
        ]
      }
      sales: {
        Row: {
          id: string
          organization_id: string
          customer_id: string | null
          invoice_number: string
          sale_date: string
          status: string
          subtotal: number
          tax_amount: number
          discount_amount: number | null
          total_amount: number
          payment_method: string | null
          notes: string | null
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          customer_id?: string | null
          invoice_number: string
          sale_date?: string
          status: string
          subtotal: number
          tax_amount: number
          discount_amount?: number | null
          total_amount: number
          payment_method?: string | null
          notes?: string | null
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          customer_id?: string | null
          invoice_number?: string
          sale_date?: string
          status?: string
          subtotal?: number
          tax_amount?: number
          discount_amount?: number | null
          total_amount?: number
          payment_method?: string | null
          notes?: string | null
          created_by?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "sales_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sales_customer_id_fkey"
            columns: ["customer_id"]
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sales_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      }
      suppliers: {
        Row: {
          id: string
          organization_id: string
          name: string
          contact_person: string | null
          email: string | null
          phone: string | null
          address: string | null
          city: string | null
          state: string | null
          postal_code: string | null
          country: string | null
          tax_id: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          name: string
          contact_person?: string | null
          email?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          postal_code?: string | null
          country?: string | null
          tax_id?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          name?: string
          contact_person?: string | null
          email?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          postal_code?: string | null
          country?: string | null
          tax_id?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "suppliers_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      }
      units_of_measurement: {
        Row: {
          id: string
          organization_id: string
          code: string
          name: string
          description: string | null
          is_active: boolean | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          code: string
          name: string
          description?: string | null
          is_active?: boolean | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          code?: string
          name?: string
          description?: string | null
          is_active?: boolean | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "units_of_measurement_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "units_of_measurement_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      product_uoms: {
        Row: {
          id: string
          product_id: string
          uom_id: string
          conversion_factor: number
          is_default: boolean
          is_purchasing_unit: boolean
          is_selling_unit: boolean
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          product_id: string
          uom_id: string
          conversion_factor: number
          is_default?: boolean
          is_purchasing_unit?: boolean
          is_selling_unit?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          product_id?: string
          uom_id?: string
          conversion_factor?: number
          is_default?: boolean
          is_purchasing_unit?: boolean
          is_selling_unit?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_uoms_product_id_fkey"
            columns: ["product_id"]
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_uoms_uom_id_fkey"
            columns: ["uom_id"]
            referencedRelation: "units_of_measurement"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_uoms_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      supplier_products: {
        Row: {
          id: string
          supplier_id: string
          product_id: string
          unit_price: number | null
          minimum_order_quantity: number | null
          lead_time_days: number | null
          is_preferred: boolean | null
          notes: string | null
          uom_id: string | null
          conversion_factor: number | null
          organization_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          supplier_id: string
          product_id: string
          unit_price?: number | null
          minimum_order_quantity?: number | null
          lead_time_days?: number | null
          is_preferred?: boolean | null
          notes?: string | null
          uom_id?: string | null
          conversion_factor?: number | null
          organization_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          supplier_id?: string
          product_id?: string
          unit_price?: number | null
          minimum_order_quantity?: number | null
          lead_time_days?: number | null
          is_preferred?: boolean | null
          notes?: string | null
          uom_id?: string | null
          conversion_factor?: number | null
          organization_id?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "supplier_products_supplier_id_fkey"
            columns: ["supplier_id"]
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "supplier_products_product_id_fkey"
            columns: ["product_id"]
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "supplier_products_uom_id_fkey"
            columns: ["uom_id"]
            referencedRelation: "units_of_measurement"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "supplier_products_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      },
      payroll_periods: {
        Row: {
          id: string
          organization_id: string
          name: string
          start_date: string
          end_date: string
          payment_date: string
          status: string
          is_thirteenth_month: boolean
          actual_payment_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          name: string
          start_date: string
          end_date: string
          payment_date: string
          status?: string
          is_thirteenth_month?: boolean
          actual_payment_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          name?: string
          start_date?: string
          end_date?: string
          payment_date?: string
          status?: string
          is_thirteenth_month?: boolean
          actual_payment_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payroll_periods_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      },
      payroll_items: {
        Row: {
          id: string
          organization_id: string
          payroll_period_id: string
          employee_id: string
          status: string
          basic_pay: number
          gross_pay: number
          net_pay: number
          total_deductions: number
          total_allowances: number
          total_overtime: number
          regular_hours: number
          overtime_hours: number
          absent_days: number
          late_minutes: number
          regular_pay: number
          overtime_pay: number
          rest_day_pay: number
          holiday_pay: number
          night_differential_pay: number
          total_regular_hours: number
          total_overtime_hours: number
          total_rest_day_hours: number
          total_holiday_hours: number
          total_night_diff_hours: number
          is_thirteenth_month: boolean
          sss_contribution: number
          philhealth_contribution: number
          pagibig_contribution: number
          taxable_income: number
          withholding_tax: number
          sss_contribution_override: number | null
          philhealth_contribution_override: number | null
          pagibig_contribution_override: number | null
          withholding_tax_override: number | null
          contributions_manually_edited: boolean
          payment_date: string | null
          error_message: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          payroll_period_id: string
          employee_id: string
          status?: string
          basic_pay?: number
          gross_pay?: number
          net_pay?: number
          total_deductions?: number
          total_allowances?: number
          total_overtime?: number
          regular_hours?: number
          overtime_hours?: number
          absent_days?: number
          late_minutes?: number
          regular_pay?: number
          overtime_pay?: number
          rest_day_pay?: number
          holiday_pay?: number
          night_differential_pay?: number
          total_regular_hours?: number
          total_overtime_hours?: number
          total_rest_day_hours?: number
          total_holiday_hours?: number
          total_night_diff_hours?: number
          is_thirteenth_month?: boolean
          sss_contribution?: number
          philhealth_contribution?: number
          pagibig_contribution?: number
          taxable_income?: number
          withholding_tax?: number
          sss_contribution_override?: number | null
          philhealth_contribution_override?: number | null
          pagibig_contribution_override?: number | null
          withholding_tax_override?: number | null
          contributions_manually_edited?: boolean
          payment_date?: string | null
          error_message?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          payroll_period_id?: string
          employee_id?: string
          status?: string
          basic_pay?: number
          gross_pay?: number
          net_pay?: number
          total_deductions?: number
          total_allowances?: number
          total_overtime?: number
          regular_hours?: number
          overtime_hours?: number
          absent_days?: number
          late_minutes?: number
          regular_pay?: number
          overtime_pay?: number
          rest_day_pay?: number
          holiday_pay?: number
          night_differential_pay?: number
          total_regular_hours?: number
          total_overtime_hours?: number
          total_rest_day_hours?: number
          total_holiday_hours?: number
          total_night_diff_hours?: number
          is_thirteenth_month?: boolean
          sss_contribution?: number
          philhealth_contribution?: number
          pagibig_contribution?: number
          taxable_income?: number
          withholding_tax?: number
          sss_contribution_override?: number | null
          philhealth_contribution_override?: number | null
          pagibig_contribution_override?: number | null
          withholding_tax_override?: number | null
          contributions_manually_edited?: boolean
          payment_date?: string | null
          error_message?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payroll_items_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_items_payroll_period_id_fkey"
            columns: ["payroll_period_id"]
            referencedRelation: "payroll_periods"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_items_employee_id_fkey"
            columns: ["employee_id"]
            referencedRelation: "employees"
            referencedColumns: ["id"]
          }
        ]
      },
      payroll_allowances: {
        Row: {
          id: string
          organization_id: string
          payroll_item_id: string
          type: string
          amount: number
          description: string | null
          taxable: boolean
          name: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          payroll_item_id: string
          type: string
          amount: number
          description?: string | null
          taxable?: boolean
          name: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          payroll_item_id?: string
          type?: string
          amount?: number
          description?: string | null
          taxable?: boolean
          name?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payroll_allowances_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_allowances_payroll_item_id_fkey"
            columns: ["payroll_item_id"]
            referencedRelation: "payroll_items"
            referencedColumns: ["id"]
          }
        ]
      },
      payroll_deductions: {
        Row: {
          id: string
          organization_id: string
          payroll_item_id: string
          type: string
          amount: number
          description: string | null
          name: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          payroll_item_id: string
          type: string
          amount: number
          description?: string | null
          name: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          payroll_item_id?: string
          type?: string
          amount?: number
          description?: string | null
          name?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payroll_deductions_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_deductions_payroll_item_id_fkey"
            columns: ["payroll_item_id"]
            referencedRelation: "payroll_items"
            referencedColumns: ["id"]
          }
        ]
      },
      employee_contribution_preferences: {
        Row: {
          id: string
          organization_id: string
          employee_id: string
          sss_contribution_override: number | null
          philhealth_contribution_override: number | null
          pagibig_contribution_override: number | null
          withholding_tax_override: number | null
          is_active: boolean
          notes: string | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          employee_id: string
          sss_contribution_override?: number | null
          philhealth_contribution_override?: number | null
          pagibig_contribution_override?: number | null
          withholding_tax_override?: number | null
          is_active?: boolean
          notes?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          employee_id?: string
          sss_contribution_override?: number | null
          philhealth_contribution_override?: number | null
          pagibig_contribution_override?: number | null
          withholding_tax_override?: number | null
          is_active?: boolean
          notes?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "employee_contribution_preferences_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "employee_contribution_preferences_employee_id_fkey"
            columns: ["employee_id"]
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "employee_contribution_preferences_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      payables: {
        Row: {
          id: string
          organization_id: string
          source_type: string
          source_id: string
          supplier_id: string | null
          employee_id: string | null
          reference_number: string
          invoice_date: string
          payable_date: string
          due_date: string
          amount: number
          vat_amount: number
          withholding_tax_rate: number
          withholding_tax_amount: number
          balance: number
          currency: string
          status: string
          category: string | null
          invoice_url: string | null
          notes: string | null
          journal_entry_id: string | null
          posted_to_ledger: boolean
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          source_type: string
          source_id: string
          supplier_id?: string | null
          employee_id?: string | null
          reference_number: string
          invoice_date: string
          payable_date?: string
          due_date: string
          amount: number
          vat_amount?: number
          withholding_tax_rate?: number
          withholding_tax_amount?: number
          balance: number
          currency?: string
          status?: string
          category?: string | null
          invoice_url?: string | null
          notes?: string | null
          journal_entry_id?: string | null
          posted_to_ledger?: boolean
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          source_type?: string
          source_id?: string
          supplier_id?: string | null
          employee_id?: string | null
          reference_number?: string
          invoice_date?: string
          payable_date?: string
          due_date?: string
          amount?: number
          vat_amount?: number
          withholding_tax_rate?: number
          withholding_tax_amount?: number
          balance?: number
          currency?: string
          status?: string
          category?: string | null
          invoice_url?: string | null
          notes?: string | null
          journal_entry_id?: string | null
          posted_to_ledger?: boolean
          created_by?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payables_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payables_supplier_id_fkey"
            columns: ["supplier_id"]
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payables_employee_id_fkey"
            columns: ["employee_id"]
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payables_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      payable_payments: {
        Row: {
          id: string
          payable_id: string
          payment_date: string
          amount_paid: number
          payment_method: string
          reference_number: string | null
          attachment_url: string | null
          remarks: string | null
          created_by: string
          created_at: string
        }
        Insert: {
          id?: string
          payable_id: string
          payment_date: string
          amount_paid: number
          payment_method: string
          reference_number?: string | null
          attachment_url?: string | null
          remarks?: string | null
          created_by: string
          created_at?: string
        }
        Update: {
          id?: string
          payable_id?: string
          payment_date?: string
          amount_paid?: number
          payment_method?: string
          reference_number?: string | null
          attachment_url?: string | null
          remarks?: string | null
          created_by?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payable_payments_payable_id_fkey"
            columns: ["payable_id"]
            referencedRelation: "payables"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payable_payments_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_organizations: {
        Args: Record<PropertyKey, never>
        Returns: string[]
      }
      has_organization_role: {
        Args: {
          organization_uuid: string
          user_uuid: string
          required_role: string
        }
        Returns: boolean
      }
      is_organization_member: {
        Args: {
          organization_uuid: string
          user_uuid: string
        }
        Returns: boolean
      }
      resolve_payable_source_metadata: {
        Args: {
          p_payable_id: string
        }
        Returns: {
          source_reference: string
          source_description: string
          source_date: string
          source_amount: number
        }[]
      }
      calculate_payable_aging: {
        Args: {
          p_organization_id: string
          p_as_of_date?: string
        }
        Returns: {
          payable_id: string
          reference_number: string
          supplier_name: string
          amount: number
          balance: number
          due_date: string
          days_overdue: number
          aging_bucket: string
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
