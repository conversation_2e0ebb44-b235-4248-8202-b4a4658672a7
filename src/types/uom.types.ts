import { Database } from './database.types';

// Unit of Measurement type
export type UnitOfMeasurement = Database['public']['Tables']['units_of_measurement']['Row'];
export type UnitOfMeasurementInsert = Database['public']['Tables']['units_of_measurement']['Insert'];
export type UnitOfMeasurementUpdate = Database['public']['Tables']['units_of_measurement']['Update'];

// Product UoM type
export type ProductUom = Database['public']['Tables']['product_uoms']['Row'];
export type ProductUomInsert = Database['public']['Tables']['product_uoms']['Insert'];
export type ProductUomUpdate = Database['public']['Tables']['product_uoms']['Update'];

// Extended types with UoM information
export type ProductWithUoms = Database['public']['Tables']['products']['Row'] & {
  uoms?: ProductUom[];
  default_uom?: ProductUom;
};

// Extended types for items with UoM
export type PurchaseRequestItemWithUom = Database['public']['Tables']['purchase_request_items']['Row'] & {
  uom?: UnitOfMeasurement;
};

export type PurchaseOrderItemWithUom = Database['public']['Tables']['purchase_order_items']['Row'] & {
  uom?: UnitOfMeasurement;
};

export type InventoryReceiptItemWithUom = Database['public']['Tables']['inventory_receipt_items']['Row'] & {
  uom?: UnitOfMeasurement;
};

export type SaleItemWithUom = Database['public']['Tables']['sale_items']['Row'] & {
  uom?: UnitOfMeasurement;
};

export type InventoryTransactionWithUom = Database['public']['Tables']['inventory_transactions']['Row'] & {
  uom?: UnitOfMeasurement;
};
