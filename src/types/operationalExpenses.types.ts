// Types for Enhanced Payables System - Operational Expenses
// Phase 1: Foundation types for expense management

// =====================================================
// EXPENSE TYPES
// =====================================================

export interface ExpenseType {
  id: string;
  organization_id: string;
  name: string;
  code: string;
  description?: string;
  category: ExpenseCategory;
  requires_approval: boolean;
  approval_limit: number;
  default_account_code?: string;
  is_recurring_type: boolean;
  default_frequency?: RecurrenceFrequency;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export enum ExpenseCategory {
  OPERATIONAL = 'operational',
  ADMINISTRATIVE = 'administrative',
  FINANCIAL = 'financial',
  MAINTENANCE = 'maintenance',
  PROFESSIONAL_SERVICES = 'professional_services',
  UTILITIES = 'utilities',
  OFFICE_SUPPLIES = 'office_supplies',
  TRAVEL = 'travel'
}

export enum RecurrenceFrequency {
  WEEKLY = 'weekly',
  BI_WEEKLY = 'bi_weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  SEMI_ANNUAL = 'semi_annual',
  ANNUAL = 'annual'
}

// =====================================================
// ENHANCED VENDOR MANAGEMENT
// =====================================================

export interface EnhancedSupplier {
  id: string;
  organization_id: string;
  name: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  payment_terms_days: number;
  
  // Enhanced fields
  service_categories?: string[];
  vendor_type: VendorType;
  tax_id_number?: string;
  is_vat_registered: boolean;
  default_withholding_tax_rate: number;
  performance_rating: number;
  last_performance_review?: string;
  preferred_payment_method: PaymentMethod;
  
  // Relationships
  contacts?: VendorContact[];
}

export enum VendorType {
  GOODS = 'goods',
  SERVICES = 'services',
  BOTH = 'both'
}

export enum PaymentMethod {
  CASH = 'cash',
  CHECK = 'check',
  BANK_TRANSFER = 'bank_transfer',
  GCASH = 'gcash',
  PAYMAYA = 'paymaya',
  CREDIT_CARD = 'credit_card',
  OTHER = 'other'
}

export interface VendorContact {
  id: string;
  supplier_id: string;
  contact_type: ContactType;
  name: string;
  title?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  is_active: boolean;
  created_at: string;
}

export enum ContactType {
  PRIMARY = 'primary',
  BILLING = 'billing',
  TECHNICAL = 'technical',
  EMERGENCY = 'emergency',
  SALES = 'sales'
}

// =====================================================
// RECURRING EXPENSES
// =====================================================

export interface RecurringExpense {
  id: string;
  organization_id: string;
  name: string;
  description?: string;
  expense_type_id?: string;
  supplier_id?: string;
  employee_id?: string;
  amount: number;
  vat_amount: number;
  withholding_tax_rate: number;
  currency: string;
  frequency: RecurrenceFrequency;
  start_date: string;
  end_date?: string;
  next_due_date: string;
  payment_terms_days: number;
  auto_create_payable: boolean;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
  
  // Relationships
  expense_type?: ExpenseType;
  supplier?: EnhancedSupplier;
  employee?: {
    id: string;
    first_name: string;
    last_name: string;
    employee_number: string;
  };
}

// =====================================================
// ENHANCED PAYABLES
// =====================================================

export interface EnhancedPayable {
  id: string;
  organization_id: string;
  source_type: EnhancedPayableSourceType;
  source_id: string;
  supplier_id?: string;
  employee_id?: string;
  reference_number: string;
  invoice_date: string;
  payable_date: string;
  due_date: string;
  amount: number;
  vat_amount: number;
  withholding_tax_rate: number;
  withholding_tax_amount: number;
  balance: number;
  currency: string;
  status: PayableStatus;
  category?: string;
  invoice_url?: string;
  notes?: string;
  
  // Enhanced fields
  expense_type_id?: string;
  recurring_expense_id?: string;
  department?: string;
  project_code?: string;
  approval_status: ApprovalStatus;
  approved_by?: string;
  approved_at?: string;
  rejection_reason?: string;
  
  // Audit fields
  journal_entry_id?: string;
  posted_to_ledger: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
  
  // Relationships
  expense_type?: ExpenseType;
  recurring_expense?: RecurringExpense;
  supplier?: EnhancedSupplier;
  employee?: {
    id: string;
    first_name: string;
    last_name: string;
    employee_number: string;
  };
  payments?: PayablePayment[];
  total_paid?: number;
}

export enum EnhancedPayableSourceType {
  PURCHASE_RECEIPT = 'purchase_receipt',
  PAYROLL = 'payroll',
  UTILITY_BILL = 'utility_bill',
  GOVERNMENT_REMITTANCE = 'government_remittance',
  LOAN_REPAYMENT = 'loan_repayment',
  MANUAL_ENTRY = 'manual_entry',
  RECURRING_EXPENSE = 'recurring_expense',
  OFFICE_SUPPLIES = 'office_supplies',
  MAINTENANCE = 'maintenance',
  PROFESSIONAL_SERVICES = 'professional_services',
  RENT = 'rent',
  INSURANCE = 'insurance',
  SUBSCRIPTION = 'subscription'
}

export enum PayableStatus {
  DRAFT = 'draft',
  OPEN = 'open',
  PARTIALLY_PAID = 'partially_paid',
  PAID = 'paid',
  CANCELLED = 'cancelled'
}

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  REQUIRES_HIGHER_APPROVAL = 'requires_higher_approval'
}

export interface PayablePayment {
  id: string;
  payable_id: string;
  payment_date: string;
  amount_paid: number;
  payment_method: PaymentMethod;
  reference_number?: string;
  attachment_url?: string;
  remarks?: string;
  created_by: string;
  created_at: string;
}

// =====================================================
// APPROVAL WORKFLOWS
// =====================================================

export interface ApprovalWorkflow {
  id: string;
  organization_id: string;
  name: string;
  description?: string;
  expense_type_id: string;
  min_amount: number;
  max_amount?: number;
  level_1_approver_role?: string;
  level_1_amount_limit?: number;
  level_2_approver_role?: string;
  level_2_amount_limit?: number;
  level_3_approver_role?: string;
  requires_receipt: boolean;
  auto_approve_below_limit: boolean;
  is_active: boolean;
  created_by: string;
  created_at: string;
  
  // Relationships
  expense_type?: ExpenseType;
}

// =====================================================
// REQUEST/RESPONSE TYPES
// =====================================================

export interface CreateExpenseTypeRequest {
  name: string;
  code: string;
  description?: string;
  category: ExpenseCategory;
  requires_approval?: boolean;
  approval_limit?: number;
  default_account_code?: string;
  is_recurring_type?: boolean;
  default_frequency?: RecurrenceFrequency;
}

export interface CreateRecurringExpenseRequest {
  name: string;
  description?: string;
  expense_type_id?: string;
  supplier_id?: string;
  employee_id?: string;
  amount: number;
  vat_amount?: number;
  withholding_tax_rate?: number;
  frequency: RecurrenceFrequency;
  start_date: string;
  end_date?: string;
  payment_terms_days?: number;
  auto_create_payable?: boolean;
}

export interface CreateEnhancedPayableRequest {
  source_type: EnhancedPayableSourceType;
  source_id: string;
  supplier_id?: string;
  employee_id?: string;
  reference_number: string;
  invoice_date: string;
  due_date: string;
  amount: number;
  vat_amount?: number;
  withholding_tax_rate?: number;
  withholding_tax_amount?: number;
  currency?: string;
  category?: string;
  invoice_url?: string;
  notes?: string;
  expense_type_id?: string;
  recurring_expense_id?: string;
  department?: string;
  project_code?: string;
}

export interface CreateVendorContactRequest {
  supplier_id: string;
  contact_type: ContactType;
  name: string;
  title?: string;
  email?: string;
  phone?: string;
  mobile?: string;
}

// =====================================================
// FILTER AND LIST OPTIONS
// =====================================================

export interface ExpenseTypeFilters {
  category?: ExpenseCategory;
  is_active?: boolean;
  is_recurring_type?: boolean;
  search?: string;
}

export interface RecurringExpenseFilters {
  expense_type_id?: string;
  supplier_id?: string;
  frequency?: RecurrenceFrequency;
  is_active?: boolean;
  due_soon?: boolean; // Next 30 days
  search?: string;
}

export interface EnhancedPayableFilters {
  source_type?: EnhancedPayableSourceType;
  status?: PayableStatus;
  approval_status?: ApprovalStatus;
  expense_type_id?: string;
  supplier_id?: string;
  department?: string;
  project_code?: string;
  date_from?: string;
  date_to?: string;
  amount_from?: number;
  amount_to?: number;
  overdue?: boolean;
  search?: string;
}

// =====================================================
// RESPONSE TYPES
// =====================================================

export interface OperationalExpenseResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ExpenseTypesResponse extends OperationalExpenseResponse<ExpenseType[]> {}
export interface RecurringExpensesResponse extends OperationalExpenseResponse<RecurringExpense[]> {}
export interface EnhancedPayablesResponse extends OperationalExpenseResponse<EnhancedPayable[]> {}

// =====================================================
// DASHBOARD AND ANALYTICS
// =====================================================

export interface ExpenseDashboardData {
  total_pending_approvals: number;
  total_overdue_payments: number;
  upcoming_recurring_expenses: number;
  monthly_expense_trend: {
    month: string;
    amount: number;
    count: number;
  }[];
  expense_by_category: {
    category: string;
    amount: number;
    percentage: number;
  }[];
  vendor_performance: {
    supplier_id: string;
    supplier_name: string;
    total_amount: number;
    payment_count: number;
    average_payment_days: number;
    performance_rating: number;
  }[];
}

export interface BudgetVsActual {
  category: string;
  budgeted_amount: number;
  actual_amount: number;
  variance: number;
  variance_percentage: number;
}
