import { Database } from './database.types';

// Base types from the database schema
export type PayrollPeriod = Database['public']['Tables']['payroll_periods']['Row'];
export type PayrollItem = Database['public']['Tables']['payroll_items']['Row'];
export type PayrollDeduction = Database['public']['Tables']['payroll_deductions']['Row'];
export type PayrollAllowance = Database['public']['Tables']['payroll_allowances']['Row'];
export type PayrollSetting = Database['public']['Tables']['payroll_settings']['Row'];
export type TimeEntry = Database['public']['Tables']['time_entries']['Row'];
export type EmployeeSalary = Database['public']['Tables']['employee_salary']['Row'];
export type EmployeeAllowance = Database['public']['Tables']['employee_allowances']['Row'];
export type EmployeeDeduction = Database['public']['Tables']['employee_deductions']['Row'];
export type TaxTable = Database['public']['Tables']['tax_tables']['Row'];
export type SSSContribution = Database['public']['Tables']['sss_contribution_table']['Row'];
export type PhilHealthContribution = Database['public']['Tables']['philhealth_contribution_table']['Row'];
export type PagibigContribution = Database['public']['Tables']['pagibig_contribution_table']['Row'];
export type Holiday = Database['public']['Tables']['holidays']['Row'];

// Extended types with relationships
export interface PayrollPeriodWithDetails extends PayrollPeriod {
  payroll_items?: PayrollItemWithDetails[];
}

export interface PayrollItemWithDetails extends PayrollItem {
  employee?: {
    id: string;
    first_name: string;
    middle_name?: string | null;
    last_name: string;
    employee_number?: string | null;
    profile_image_url?: string | null;
  };
  deductions?: PayrollDeduction[];
  allowances?: PayrollAllowance[];
}

export interface TimeEntryWithDetails extends TimeEntry {
  employee?: {
    id: string;
    first_name: string;
    middle_name?: string | null;
    last_name: string;
    employee_number?: string | null;
  };
}

export interface EmployeeSalaryWithDetails extends EmployeeSalary {
  employee?: {
    id: string;
    first_name: string;
    middle_name?: string | null;
    last_name: string;
    employee_number?: string | null;
  };
}

// Enums for type safety
export enum PayrollPeriodStatus {
  DRAFT = 'draft',
  PROCESSING = 'processing',
  APPROVED = 'approved',
  PAID = 'paid'
}

export enum PayrollItemStatus {
  DRAFT = 'draft',
  CALCULATED = 'calculated',
  APPROVED = 'approved',
  PAID = 'paid',
  ERROR = 'error'
}

export enum DeductionType {
  SSS = 'sss',
  PHILHEALTH = 'philhealth',
  PAGIBIG = 'pagibig',
  TAX = 'tax',
  LOAN = 'loan',
  OTHER = 'other'
}

export enum AllowanceType {
  TRANSPORTATION = 'transportation',
  MEAL = 'meal',
  HOUSING = 'housing',
  OTHER = 'other'
}

export enum PaySchedule {
  MONTHLY = 'monthly',
  SEMI_MONTHLY = 'semi-monthly',
  WEEKLY = 'weekly'
}

export enum TimeEntryStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  LEAVE = 'leave',
  HOLIDAY = 'holiday'
}

export enum RateType {
  MONTHLY = 'monthly',
  DAILY = 'daily',
  HOURLY = 'hourly'
}

export enum Frequency {
  PER_PAYROLL = 'per_payroll',
  MONTHLY = 'monthly',
  ANNUAL = 'annual',
  ONE_TIME = 'one_time'
}

export enum HolidayType {
  REGULAR = 'regular',
  SPECIAL = 'special'
}

export enum PayslipTemplate {
  DEFAULT = 'default',
  COMPACT = 'compact',
  DETAILED = 'detailed'
}

// Interfaces for payroll calculations
export interface PayrollCalculationResult {
  employeeId: string;
  periodId: string;
  basicPay: number;
  grossPay: number;
  netPay: number;
  totalDeductions: number;
  totalAllowances: number;
  deductions: {
    name: string;
    type: DeductionType;
    amount: number;
  }[];
  allowances: {
    name: string;
    type: AllowanceType;
    amount: number;
  }[];
  taxableIncome: number;
  withholdingTax: number;
  sssContribution: {
    employeeShare: number;
    employerShare: number;
  };
  philhealthContribution: {
    employeeShare: number;
    employerShare: number;
  };
  pagibigContribution: {
    employeeShare: number;
    employerShare: number;
  };
  timeEntryDetails?: {
    regularPay: number;
    overtimePay: number;
    restDayPay: number;
    holidayPay: number;
    nightDifferentialPay: number;
    totalRegularHours: number;
    totalOvertimeHours: number;
    totalRestDayHours: number;
    totalHolidayHours: number;
    totalNightDiffHours: number;
  };
}

export interface PayrollCalculationInput {
  employeeId: string;
  periodStartDate: Date;
  periodEndDate: Date;
  regularHours: number;
  overtimeHours: number;
  nightDiffHours: number;
  restDayHours: number;
  holidayHours: number;
  holidayType?: HolidayType;
  absentDays: number;
  lateMinutes: number;
}

// Interface for payroll processing
export interface ProcessPayrollOptions {
  forceProcess?: boolean; // Process even if not in draft status
  reprocess?: boolean; // Delete existing items and reprocess
  createErrorItems?: boolean; // Create error items for failed processing
}

// Interface for payroll reports
export interface PayrollReportOptions {
  organizationId: string;
  periodId?: string;
  startDate?: Date;
  endDate?: Date;
  employeeIds?: string[];
  includeDetails?: boolean;
  reportType?: PayrollReportType;
  format?: PayrollReportFormat;
}

export interface PayrollSummary {
  periodId: string;
  periodName: string;
  startDate: Date;
  endDate: Date;
  paymentDate: Date;
  status: PayrollPeriodStatus;
  isThirteenthMonth?: boolean;
  totalEmployees: number;
  totalGrossPay: number;
  totalNetPay: number;
  totalDeductions: number;
  totalAllowances: number;
}

export enum PayrollReportType {
  SUMMARY = 'summary',
  EMPLOYEE_EARNINGS = 'employee_earnings',
  GOVERNMENT_CONTRIBUTIONS = 'government_contributions',
  BIR_WITHHOLDING_TAX = 'bir_withholding_tax',
  SSS_CONTRIBUTIONS = 'sss_contributions',
  PHILHEALTH_CONTRIBUTIONS = 'philhealth_contributions',
  PAGIBIG_CONTRIBUTIONS = 'pagibig_contributions',
  THIRTEENTH_MONTH = 'thirteenth_month'
}

export enum PayrollReportFormat {
  HTML = 'html',
  PDF = 'pdf',
  CSV = 'csv',
  EXCEL = 'excel'
}

export interface PayrollTotals {
  totalBasicPay: number;
  totalGrossPay: number;
  totalNetPay: number;
  totalDeductions: number;
  totalAllowances: number;
  totalSSS: number;
  totalPhilHealth: number;
  totalPagibig: number;
  totalTax: number;
  totalOtherDeductions: number;
}
