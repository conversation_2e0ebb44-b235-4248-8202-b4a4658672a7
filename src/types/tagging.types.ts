import { Database } from './database.types';

// Enum for entity types that can be tagged
export type TaggableEntityType = 
  | 'product'
  | 'supplier'
  | 'customer'
  | 'purchase_request'
  | 'purchase_order';

// Tag type from database
export type Tag = Database['public']['Tables']['tags']['Row'];

// Tagged item type from database
export type TaggedItem = Database['public']['Tables']['tagged_items']['Row'];

// Type for creating a new tag
export type CreateTagParams = {
  organization_id: string;
  name: string;
  description?: string | null;
  color?: string | null;
};

// Type for updating an existing tag
export type UpdateTagParams = {
  name?: string;
  description?: string | null;
  color?: string | null;
};

// Type for adding a tag to an entity
export type AddTagToEntityParams = {
  tag_id: string;
  entity_type: TaggableEntityType;
  entity_id: string;
};

// Type for removing a tag from an entity
export type RemoveTagFromEntityParams = {
  tag_id: string;
  entity_type: TaggableEntityType;
  entity_id: string;
};

// Type for tag with count information
export type TagWithCount = Tag & {
  usage_count: number;
};

// Type for entity with its tags
export type EntityWithTags<T> = T & {
  tags: Tag[];
};

// Type for the entity tags view
export type EntityTagView = {
  entity_type: TaggableEntityType;
  entity_id: string;
  tag_id: string;
  tag_name: string;
  tag_description: string | null;
  tag_color: string | null;
  organization_id: string;
};
