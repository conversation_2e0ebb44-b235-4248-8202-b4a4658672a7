// Types for payable and payment attachments system

export interface PayableAttachment {
  id: string;
  organization_id: string;
  attachable_type: 'payable' | 'payment';
  attachable_id: string;
  file_name: string;
  file_url: string;
  file_size: number;
  file_type: string;
  file_extension: string;
  description?: string;
  is_primary: boolean;
  uploaded_by: string;
  created_at: string;
  updated_at: string;
}

export interface CreateAttachmentRequest {
  attachable_type: 'payable' | 'payment';
  attachable_id: string;
  file_name: string;
  file_url: string;
  file_size: number;
  file_type: string;
  file_extension: string;
  description?: string;
  is_primary?: boolean;
}

export interface AttachmentUploadResult {
  success: boolean;
  attachment?: PayableAttachment;
  error?: string;
}

export interface AttachmentsResponse {
  success: boolean;
  attachments?: PayableAttachment[];
  error?: string;
}

export interface FileUploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
  attachment?: PayableAttachment;
}

// Allowed file types for attachments
export const ALLOWED_ATTACHMENT_TYPES = {
  'image/jpeg': { type: 'image', ext: 'jpg', maxSize: 10 * 1024 * 1024 }, // 10MB
  'image/png': { type: 'image', ext: 'png', maxSize: 10 * 1024 * 1024 },
  'image/gif': { type: 'image', ext: 'gif', maxSize: 10 * 1024 * 1024 },
  'image/webp': { type: 'image', ext: 'webp', maxSize: 10 * 1024 * 1024 },
  'application/pdf': { type: 'document', ext: 'pdf', maxSize: 10 * 1024 * 1024 },
  'text/plain': { type: 'document', ext: 'txt', maxSize: 1 * 1024 * 1024 } // 1MB
} as const;

export type AllowedMimeType = keyof typeof ALLOWED_ATTACHMENT_TYPES;

// File validation
export interface FileValidationResult {
  valid: boolean;
  error?: string;
}

// Attachment display props
export interface AttachmentDisplayProps {
  attachment: PayableAttachment;
  onDelete?: (attachmentId: string) => void;
  onSetPrimary?: (attachmentId: string) => void;
  onUpdateDescription?: (attachmentId: string, description: string) => void;
  showActions?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

// Multiple file upload component props
export interface MultipleFileUploadProps {
  attachableType: 'payable' | 'payment';
  attachableId: string;
  onUploadComplete?: (attachments: PayableAttachment[]) => void;
  onUploadError?: (error: string) => void;
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
}

// Attachment gallery props
export interface AttachmentGalleryProps {
  attachableType: 'payable' | 'payment';
  attachableId: string;
  editable?: boolean;
  showUpload?: boolean;
  maxFiles?: number;
  className?: string;
}
