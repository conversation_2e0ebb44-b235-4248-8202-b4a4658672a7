import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  <PERSON>ton,
  Table,
  Badge,
  Spinner,
  <PERSON>ert,
  Modal,
  Textarea,
  Label,
  Tooltip
} from 'flowbite-react';
import {
  HiOutlineChevronLeft,
  HiOutlineExclamation,
  HiOutlineDocumentText,
  HiOutlineUser,
  HiOutlineCalendar,
  HiOutlineClipboardList,
  HiOutlinePlus,
  HiOutlineCheck,
  HiOutlineX,
  HiOutlineBan,
  HiOutlinePencil,
  HiOutlineClock,
  HiOutlineInformationCircle
} from 'react-icons/hi';
import { useOrganization } from '../../context/OrganizationContext';
import { getPurchaseRequestById, updatePurchaseRequestStatus } from '../../services/purchaseRequest';
import { PurchaseRequestWithItems } from '../../services/purchaseRequest';
import { formatDate, formatDateTime } from '../../utils/formatters';

const PurchaseRequestDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization } = useOrganization();

  const [purchaseRequest, setPurchaseRequest] = useState<PurchaseRequestWithItems | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for approval/rejection process
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [statusNotes, setStatusNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [statusUpdateError, setStatusUpdateError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPurchaseRequest = async () => {
      if (!id || !currentOrganization) return;

      setLoading(true);
      setError(null);

      try {
        const { purchaseRequest: pr, error: fetchError } = await getPurchaseRequestById(
          currentOrganization.id,
          id
        );

        console.log('Fetched purchase request:', pr);

        if (fetchError) {
          setError(fetchError);
        } else if (!pr) {
          setError('Purchase request not found');
        } else {
          setPurchaseRequest(pr);
        }
      } catch (err: any) {
        console.error('Error fetching purchase request:', err);
        setError(err.message || 'An error occurred while fetching the purchase request');
      } finally {
        setLoading(false);
      }
    };

    fetchPurchaseRequest();
  }, [id, currentOrganization]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'failure';
      case 'cancelled':
        return 'dark';
      default:
        return 'gray';
    }
  };

  const handleBackClick = () => {
    navigate('/purchases/requests');
  };

  // Open the approval modal
  const handleApproveClick = () => {
    setStatusNotes('');
    setStatusUpdateError(null);
    setIsApproving(true);
  };

  // Open the rejection modal
  const handleRejectClick = () => {
    setStatusNotes('');
    setStatusUpdateError(null);
    setIsRejecting(true);
  };

  // Open the cancellation modal
  const handleCancelClick = () => {
    setStatusNotes('');
    setStatusUpdateError(null);
    setIsCancelling(true);
  };

  // Close all modals
  const handleCloseModal = () => {
    setIsApproving(false);
    setIsRejecting(false);
    setIsCancelling(false);
    setStatusNotes('');
    setStatusUpdateError(null);
  };

  // Handle the status update
  const handleStatusUpdate = async (status: 'approved' | 'rejected' | 'cancelled') => {
    if (!id || !currentOrganization) return;

    setIsSubmitting(true);
    setStatusUpdateError(null);

    try {
      const { success, error } = await updatePurchaseRequestStatus(
        currentOrganization.id,
        id,
        status,
        statusNotes || undefined
      );

      if (error) {
        setStatusUpdateError(error);
      } else if (success) {
        // Close the modal
        handleCloseModal();

        // Refresh the purchase request data
        const { purchaseRequest: pr } = await getPurchaseRequestById(
          currentOrganization.id,
          id
        );

        if (pr) {
          setPurchaseRequest(pr);
        }
      }
    } catch (err: any) {
      setStatusUpdateError(err.message || 'An error occurred while updating the status');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="failure" icon={HiOutlineExclamation}>
          <h3 className="font-medium">Error</h3>
          <p>{error}</p>
          <div className="mt-4">
            <Button color="gray" onClick={handleBackClick}>
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back to Purchase Requests
            </Button>
          </div>
        </Alert>
      </div>
    );
  }

  if (!purchaseRequest) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert color="warning" icon={HiOutlineExclamation}>
          <h3 className="font-medium">Purchase Request Not Found</h3>
          <p>The requested purchase request could not be found.</p>
          <div className="mt-4">
            <Button color="gray" onClick={handleBackClick}>
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back to Purchase Requests
            </Button>
          </div>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <HiOutlineDocumentText className="mr-2 h-6 w-6" />
              Purchase Request: {purchaseRequest.request_number}
            </h1>
            <div className="flex flex-wrap gap-4 mt-2">
              <div className="flex items-center text-gray-500">
                <HiOutlineUser className="mr-1 h-5 w-5" />
                <span>Requester: {purchaseRequest.requester_name}</span>
              </div>
              <div className="flex items-center text-gray-500">
                <HiOutlineCalendar className="mr-1 h-5 w-5" />
                <span>Created: {formatDate(purchaseRequest.created_at)}</span>
              </div>
              <div className="flex items-center">
                <Badge color={getStatusColor(purchaseRequest.status)}>
                  {purchaseRequest.status.charAt(0).toUpperCase() + purchaseRequest.status.slice(1)}
                </Badge>
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button color="gray" onClick={handleBackClick}>
              <HiOutlineChevronLeft className="mr-2 h-5 w-5" />
              Back
            </Button>

            {/* Status-specific action buttons */}
            {purchaseRequest.status === 'draft' && (
              <>
                <Tooltip content="Edit purchase request">
                  <Button color="primary" onClick={() => navigate(`/purchases/requests/edit/${purchaseRequest.id}`)}>
                    <HiOutlinePencil className="h-5 w-5" />
                  </Button>
                </Tooltip>
                <Button color="success" onClick={handleApproveClick}>
                  <HiOutlineCheck className="mr-2 h-5 w-5" />
                  Approve
                </Button>
                <Button color="failure" onClick={handleRejectClick}>
                  <HiOutlineX className="mr-2 h-5 w-5" />
                  Reject
                </Button>
              </>
            )}

            {purchaseRequest.status === 'pending' && (
              <>
                <Button color="success" onClick={handleApproveClick}>
                  <HiOutlineCheck className="mr-2 h-5 w-5" />
                  Approve
                </Button>
                <Button color="failure" onClick={handleRejectClick}>
                  <HiOutlineX className="mr-2 h-5 w-5" />
                  Reject
                </Button>
              </>
            )}

            {purchaseRequest.status === 'approved' && (
              <>
                <Button
                  color="primary"
                  onClick={() => navigate(`/purchases/orders/from-request/${purchaseRequest.id}`)}
                >
                  <HiOutlinePlus className="mr-2 h-5 w-5" />
                  Create Purchase Order
                </Button>
                <Button color="failure" onClick={handleCancelClick}>
                  <HiOutlineBan className="mr-2 h-5 w-5" />
                  Cancel
                </Button>
              </>
            )}

            {purchaseRequest.status === 'rejected' && (
              <Button color="success" onClick={handleApproveClick}>
                <HiOutlineCheck className="mr-2 h-5 w-5" />
                Approve
              </Button>
            )}
          </div>
        </div>

        {/* Purchase Request Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-6">
          {/* Right Column - Additional Information */}
          <div className="space-y-4">
            {purchaseRequest.notes && (
              <div>
                <h2 className="text-lg font-semibold mb-2">Notes</h2>
                <div className="p-4 bg-gray-50 rounded-lg whitespace-pre-line">
                  {purchaseRequest.notes}
                </div>
              </div>
            )}

            {/* Related Purchase Orders */}
            {purchaseRequest.purchase_orders && purchaseRequest.purchase_orders.length > 0 && (
              <div>
                <h2 className="text-lg font-semibold mb-2">Related Purchase Orders</h2>
                <div className="space-y-2">
                  {purchaseRequest.purchase_orders.map(po => (
                    <div key={po.id} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">{po.order_number}</p>
                          <p className="text-sm text-gray-500">
                            {formatDate(po.created_at)}
                          </p>
                        </div>
                        <Button
                          size="xs"
                          color="primary"
                          onClick={() => navigate(`/purchases/orders/details/${po.id}`)}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Items Section */}
        <div>
          <h2 className="text-lg font-semibold mb-2 flex items-center">
            <HiOutlineClipboardList className="mr-2 h-5 w-5" />
            Items
          </h2>

          {purchaseRequest.items && purchaseRequest.items.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <Table.Head>
                  <Table.HeadCell>Product</Table.HeadCell>
                  <Table.HeadCell>Quantity</Table.HeadCell>
                  <Table.HeadCell>Unit</Table.HeadCell>
                  <Table.HeadCell>Notes</Table.HeadCell>
                </Table.Head>
                <Table.Body className="divide-y">
                  {purchaseRequest.items.map((item) => (
                    <Table.Row key={item.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
                      <Table.Cell className="font-medium">
                        {item.product ? (
                          <button
                            onClick={() => navigate(`/products/details/${item.product.id}`)}
                            className="text-primary-600 hover:text-primary-800 hover:underline focus:outline-none"
                            title="View product details"
                          >
                            {item.product.name}
                          </button>
                        ) : (
                          'Unknown Product'
                        )}
                        {item.product?.sku && (
                          <div className="text-xs text-gray-500">
                            SKU: {item.product.sku}
                          </div>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        {item.quantity}
                      </Table.Cell>
                      <Table.Cell>
                        {item.uom?.name || 'Unknown Unit'}
                        {item.uom?.code && <span className="text-xs text-gray-500 block">({item.uom.code})</span>}
                      </Table.Cell>
                      <Table.Cell>
                        {item.notes || '-'}
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
              No items found in this purchase request.
            </div>
          )}
        </div>

        {/* System Information */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h2 className="text-sm font-medium text-gray-500 mb-2">System Information</h2>
          <div className="flex flex-wrap gap-x-6 gap-y-2">
            <div>
              <p className="text-xs text-gray-500">Purchase Request ID</p>
              <p className="text-sm">{purchaseRequest.id}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Request Number</p>
              <p className="text-sm">{purchaseRequest.request_number}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Status</p>
              <p className="text-sm">{purchaseRequest.status.charAt(0).toUpperCase() + purchaseRequest.status.slice(1)}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Created</p>
              <p className="text-sm">{formatDateTime(purchaseRequest.created_at)}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Last Updated</p>
              <p className="text-sm">{formatDateTime(purchaseRequest.updated_at)}</p>
            </div>
            {purchaseRequest.status_updated_at && (
              <div>
                <p className="text-xs text-gray-500">Status Updated</p>
                <p className="text-sm">{formatDateTime(purchaseRequest.status_updated_at)}</p>
              </div>
            )}
            {purchaseRequest.requester_id && (
              <div>
                <p className="text-xs text-gray-500">Requester ID</p>
                <p className="text-sm">{purchaseRequest.requester_id}</p>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Approval Modal */}
      <Modal show={isApproving} onClose={handleCloseModal}>
        <Modal.Header>Approve Purchase Request</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>Are you sure you want to approve this purchase request?</p>
            <div>
              <div className="mb-2 block">
                <Label htmlFor="approval_notes" value="Notes (Optional)" />
              </div>
              <Textarea
                id="approval_notes"
                placeholder="Add any notes about this approval"
                value={statusNotes}
                onChange={(e) => setStatusNotes(e.target.value)}
                rows={3}
              />
            </div>
            {statusUpdateError && (
              <Alert color="failure">
                {statusUpdateError}
              </Alert>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="success" onClick={() => handleStatusUpdate('approved')} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Approving...
              </>
            ) : (
              'Approve'
            )}
          </Button>
          <Button color="gray" onClick={handleCloseModal}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Rejection Modal */}
      <Modal show={isRejecting} onClose={handleCloseModal}>
        <Modal.Header>Reject Purchase Request</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>Are you sure you want to reject this purchase request?</p>
            <div>
              <div className="mb-2 block">
                <Label htmlFor="rejection_notes" value="Reason for Rejection" />
              </div>
              <Textarea
                id="rejection_notes"
                placeholder="Please provide a reason for rejection"
                value={statusNotes}
                onChange={(e) => setStatusNotes(e.target.value)}
                rows={3}
                required
              />
            </div>
            {statusUpdateError && (
              <Alert color="failure">
                {statusUpdateError}
              </Alert>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="failure"
            onClick={() => handleStatusUpdate('rejected')}
            disabled={isSubmitting || !statusNotes.trim()}
          >
            {isSubmitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Rejecting...
              </>
            ) : (
              'Reject'
            )}
          </Button>
          <Button color="gray" onClick={handleCloseModal}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Cancellation Modal */}
      <Modal show={isCancelling} onClose={handleCloseModal}>
        <Modal.Header>Cancel Purchase Request</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>Are you sure you want to cancel this purchase request?</p>
            <div>
              <div className="mb-2 block">
                <Label htmlFor="cancellation_notes" value="Reason for Cancellation" />
              </div>
              <Textarea
                id="cancellation_notes"
                placeholder="Please provide a reason for cancellation"
                value={statusNotes}
                onChange={(e) => setStatusNotes(e.target.value)}
                rows={3}
              />
            </div>
            {statusUpdateError && (
              <Alert color="failure">
                {statusUpdateError}
              </Alert>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button color="failure" onClick={() => handleStatusUpdate('cancelled')} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Cancelling...
              </>
            ) : (
              'Cancel Request'
            )}
          </Button>
          <Button color="gray" onClick={handleCloseModal}>
            Go Back
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default PurchaseRequestDetails;