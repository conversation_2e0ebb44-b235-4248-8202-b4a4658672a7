// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { lazy } from 'react';
import { Navigate, createBrowserRouter } from 'react-router-dom';
import Loadable from 'src/layouts/full/shared/loadable/Loadable';
import ProtectedRoute from '../components/ProtectedRoute';
import ConditionalRoute from '../components/routing/ConditionalRoute';

/* ***Layouts**** */
const FullLayout = Loadable(lazy(() => import('../layouts/full/FullLayout')));
const BlankLayout = Loadable(lazy(() => import('../layouts/blank/BlankLayout')));
const POSLayout = Loadable(lazy(() => import('../layouts/full/POSLayout')));
const ChatLayout = Loadable(lazy(() => import('../layouts/full/ChatLayout')));

// Dashboard
const Dashboard = Loadable(lazy(() => import('../views/dashboards/Dashboard')));

// Products
const ProductList = Loadable(lazy(() => import('../views/products/ProductList')));
const CreateProduct = Loadable(lazy(() => import('../views/products/CreateProduct')));
const EditProduct = Loadable(lazy(() => import('../views/products/EditProduct')));
const ProductDetails = Loadable(lazy(() => import('../views/products/ProductDetails')));
const Categories = Loadable(lazy(() => import('../views/products/Categories')));

// Inventory
const InventoryList = Loadable(lazy(() => import('../views/inventory/InventoryList')));
const InventoryDetails = Loadable(lazy(() => import('../views/inventory/InventoryDetails')));
const AdjustInventory = Loadable(lazy(() => import('../views/inventory/AdjustInventory')));
const TransactionDetails = Loadable(lazy(() => import('../views/inventory/TransactionDetails')));
const InventoryDashboard = Loadable(lazy(() => import('../views/inventory/InventoryDashboard')));
const InventoryTransactions = Loadable(lazy(() => import('../views/inventory/Transactions')));
const InventoryReceipts = Loadable(lazy(() => import('../views/inventory/Receipts')));
const CreateInventoryReceipt = Loadable(lazy(() => import('../views/inventory/CreateReceipt')));
const FloatInventory = Loadable(lazy(() => import('../views/inventory/FloatInventory')));
const FloatInventoryReport = Loadable(lazy(() => import('../views/inventory/FloatInventoryReport')));
const FloatInventoryDetails = Loadable(lazy(() => import('../views/inventory/FloatInventoryDetails')));
const InventoryReceiptDetails = Loadable(lazy(() => import('../views/inventory/InventoryReceiptDetails')));

// Sales
const POSTerminal = Loadable(lazy(() => import('../views/sales/POS')));
const RetailPOS = Loadable(lazy(() => import('../views/sales/RetailPOS')));
const POSRouter = Loadable(lazy(() => import('../views/sales/POSRouter')));
const SalesList = Loadable(lazy(() => import('../views/sales/SalesList')));
const SalesHistoryPage = Loadable(lazy(() => import('../views/sales/SalesHistoryPage')));
const SalesSummaryPage = Loadable(lazy(() => import('../views/sales/SalesSummaryPage')));
const SaleDetails = Loadable(lazy(() => import('../views/sales/SaleDetails')));

// Refund components
const RefundManagement = Loadable(lazy(() => import('../views/refunds/RefundManagement')));

// Purchases
const PurchaseRequests = Loadable(lazy(() => import('../views/purchases/PurchaseRequests')));
const CreatePurchaseRequest = Loadable(lazy(() => import('../views/purchases/CreatePurchaseRequest')));
const PurchaseRequestDetails = Loadable(lazy(() => import('../views/purchases/PurchaseRequestDetails')));
const EditPurchaseRequest = Loadable(lazy(() => import('../views/purchases/EditPurchaseRequest')));
const PurchaseOrders = Loadable(lazy(() => import('../views/purchases/PurchaseOrders')));
const CreatePurchaseOrder = Loadable(lazy(() => import('../views/purchases/CreatePurchaseOrder')));
const EditPurchaseOrder = Loadable(lazy(() => import('../views/purchases/EditPurchaseOrder')));
const CreatePurchaseOrderFromRequest = Loadable(lazy(() => import('../views/purchases/CreatePurchaseOrderFromRequest')));
const PurchaseOrderDetails = Loadable(lazy(() => import('../views/purchases/PurchaseOrderDetails')));

// Customers
const CustomerList = Loadable(lazy(() => import('../views/customers/CustomerList')));
const CreateCustomer = Loadable(lazy(() => import('../views/customers/CreateCustomer')));
const EditCustomer = Loadable(lazy(() => import('../views/customers/EditCustomer')));
const CustomerDetails = Loadable(lazy(() => import('../views/customers/CustomerDetails')));

// Suppliers
const SupplierList = Loadable(lazy(() => import('../views/suppliers/SupplierList')));
const SupplierDetails = Loadable(lazy(() => import('../views/suppliers/SupplierDetails')));
const SupplierEdit = Loadable(lazy(() => import('../views/suppliers/SupplierEdit')));
const SupplierCreate = Loadable(lazy(() => import('../views/suppliers/SupplierCreate')));

// Employees
const EmployeeList = Loadable(lazy(() => import('../views/employees/EmployeeList')));
const EmployeeDetails = Loadable(lazy(() => import('../views/employees/EmployeeDetails')));
const EditEmployee = Loadable(lazy(() => import('../views/employees/EditEmployee')));

// Payroll
const PayrollList = Loadable(lazy(() => import('../views/payroll/PayrollList')));
const PayrollPeriodDetails = Loadable(lazy(() => import('../views/payroll/PayrollPeriodDetails')));
const EditPayrollPeriod = Loadable(lazy(() => import('../views/payroll/EditPayrollPeriod')));
const PayrollProcessing = Loadable(lazy(() => import('../views/payroll/PayrollProcessing')));
const PayrollSettings = Loadable(lazy(() => import('../views/payroll/PayrollSettings')));
const TimeEntries = Loadable(lazy(() => import('../views/payroll/TimeEntries')));
const AddTimeEntry = Loadable(lazy(() => import('../views/payroll/AddTimeEntry')));
const EditTimeEntry = Loadable(lazy(() => import('../views/payroll/EditTimeEntry')));
const PayrollPayslips = Loadable(lazy(() => import('../views/payroll/PayrollPayslips')));
const PayrollItemDetails = Loadable(lazy(() => import('../views/payroll/PayrollItemDetails')));
const EditPayrollItem = Loadable(lazy(() => import('../views/payroll/EditPayrollItem')));
const EmployeeContributionPreferences = Loadable(lazy(() => import('../views/payroll/EmployeeContributionPreferences')));
const EmployeeSalaries = Loadable(lazy(() => import('../views/payroll/EmployeeSalaries')));
const PayrollReports = Loadable(lazy(() => import('../views/payroll/PayrollReportsEnhanced')));
const PayrollAnalytics = Loadable(lazy(() => import('../views/payroll/PayrollAnalyticsEnhanced')));
const GovernmentContributionsReportPage = Loadable(lazy(() => import('../views/payroll/GovernmentContributionsReportPage')));
const Departments = Loadable(lazy(() => import('../views/employees/Departments')));
const JobPositions = Loadable(lazy(() => import('../views/employees/JobPositions')));
const EmploymentTypes = Loadable(lazy(() => import('../views/employees/EmploymentTypes')));

// Payables
const PayablesList = Loadable(lazy(() => import('../views/payables/PayablesList')));
const PayableDetails = Loadable(lazy(() => import('../views/payables/PayableDetails')));
const AddPayment = Loadable(lazy(() => import('../views/payables/AddPayment')));
const CreatePayable = Loadable(lazy(() => import('../views/payables/CreatePayable')));
// PayableWorkflowDebug removed - using manual creation only

// Enhanced Payables - Operational Expenses
const ExpenseDashboard = Loadable(lazy(() => import('../views/expenses/ExpenseDashboard')));
const ExpenseTypes = Loadable(lazy(() => import('../views/expenses/ExpenseTypes')));
const RecurringExpenses = Loadable(lazy(() => import('../views/expenses/RecurringExpenses')));
const EnhancedPayables = Loadable(lazy(() => import('../views/expenses/EnhancedPayables')));
const QuickExpenseEntry = Loadable(lazy(() => import('../views/expenses/QuickExpenseEntry')));

// Reports
const SalesReport = Loadable(lazy(() => import('../views/reports/SalesReport')));
const InventoryReport = Loadable(lazy(() => import('../views/reports/InventoryReport')));
const InventoryReports = Loadable(lazy(() => import('../views/reports/InventoryReports')));

// Users
const UserManagement = Loadable(lazy(() => import('../views/users/UserManagement')));
const InviteUser = Loadable(lazy(() => import('../views/users/InviteUser')));
const UserProfile = Loadable(lazy(() => import('../views/users/UserProfile')));
const EnhancedUserProfile = Loadable(lazy(() => import('../views/users/EnhancedUserProfile')));
const ChangePassword = Loadable(lazy(() => import('../views/users/ChangePassword')));

// utilities
const Typography = Loadable(lazy(() => import('../views/typography/Typography')));
const Table = Loadable(lazy(() => import('../views/tables/Table')));
const Form = Loadable(lazy(() => import('../views/forms/Form')));
const Shadow = Loadable(lazy(() => import('../views/shadows/Shadow')));

// icons
const Solar = Loadable(lazy(() => import('../views/icons/Solar')));

// authentication
const Login = Loadable(lazy(() => import('../views/auth/login/Login')));
const Register = Loadable(lazy(() => import('../views/auth/register/Register')));
const ForgotPassword = Loadable(lazy(() => import('../views/auth/forgot-password/ForgotPassword')));
const ResetPassword = Loadable(lazy(() => import('../views/auth/reset-password/ResetPassword')));
// Use our improved version for the regular invitation path
const AcceptInvitation = Loadable(lazy(() => import('../views/auth/NewAcceptInvitation')));
const OrganizationSelect = Loadable(lazy(() => import('../views/auth/organization-select/OrganizationSelect')));
const OnboardingFlow = Loadable(lazy(() => import('../components/onboarding/OnboardingFlow')));

// Time Clock & Face Recognition
const TimeClockPage = Loadable(lazy(() => import('../views/timeClock/TimeClockPage')));
const FaceRecognitionDiagnostic = Loadable(lazy(() => import('../components/faceRecognition/FaceRecognitionDiagnostic')));
const SamplePage = Loadable(lazy(() => import('../views/sample-page/SamplePage')));
const Error = Loadable(lazy(() => import('../views/auth/error/Error')));

// Organization
const CreateOrganization = Loadable(lazy(() => import('../views/organization/CreateOrganization')));
const OrganizationSettings = Loadable(lazy(() => import('../views/organization/OrganizationSettings')));

// Settings
const UnitOfMeasurementList = Loadable(lazy(() => import('../views/settings/UnitOfMeasurementList')));
const QcChecklists = Loadable(lazy(() => import('../views/settings/QcChecklists')));

// Tags
const TagManagement = Loadable(lazy(() => import('../views/tags/TagManagement')));
const TaggedItemsView = Loadable(lazy(() => import('../views/tags/TaggedItemsView')));

// Chat
const ChatList = Loadable(lazy(() => import('../views/chat/ChatList')));
const ChatDetail = Loadable(lazy(() => import('../views/chat/ChatDetail')));

// Debug
const InvitationDebug = Loadable(lazy(() => import('../views/debug/InvitationDebug')));
const AcceptInvitationDebug = Loadable(lazy(() => import('../views/debug/AcceptInvitationDebug')));
const InvitationDiagnostic = Loadable(lazy(() => import('../views/debug/InvitationDiagnostic')));
const CameraDebug = Loadable(lazy(() => import('../components/debug/CameraDebug')));

const Router = [
  {
    path: '/',
    element: <ProtectedRoute />,
    children: [
      // Smart POS Router - redirects to appropriate POS based on business type
      {
        path: '/sales/smart-pos',
        element: <POSLayout />,
        children: [
          { path: '', element: <POSRouter /> }
        ]
      },
      // Standard POS Terminal with its own layout (no sidebar)
      {
        path: '/sales/pos',
        element: <POSLayout />,
        children: [
          { path: '', element: <POSTerminal /> }
        ]
      },
      // Retail POS Terminal with its own layout (no sidebar)
      {
        path: '/sales/retail-pos',
        element: <POSLayout />,
        children: [
          { path: '', element: <RetailPOS /> }
        ]
      },
      // Time Clock Terminal with its own layout (no sidebar)
      {
        path: '/time-clock',
        element: <BlankLayout />,
        children: [
          { path: '', element: <TimeClockPage /> }
        ]
      },
      {
        path: '/',
        element: <FullLayout />,
        children: [
          { path: '/', exact: true, element: <Dashboard /> },

          // Chat routes with ChatProvider wrapper to prevent unnecessary API calls
          {
            path: '/chat',
            element: <ChatLayout />,
            children: [
              { path: '', element: <ChatList /> },
              { path: ':conversationId', element: <ChatDetail /> }
            ]
          },

          // Products
          { path: '/products', exact: true, element: <ProductList /> },
          { path: '/products/create', exact: true, element: <CreateProduct /> },
          { path: '/products/edit/:id', exact: true, element: <EditProduct /> },
          { path: '/products/details/:id', exact: true, element: <ProductDetails /> },
          { path: '/products/categories', exact: true, element: <Categories /> },

          // Inventory
          { path: '/inventory', exact: true, element: <InventoryList /> },
          { path: '/inventory/details/:id', exact: true, element: <InventoryDetails /> },
          { path: '/inventory/adjust/:id', exact: true, element: <AdjustInventory /> },
          { path: '/inventory/transactions', exact: true, element: <InventoryTransactions /> },
          { path: '/inventory/transactions/:id', exact: true, element: <TransactionDetails /> },
          { path: '/inventory/receipts', exact: true, element: <InventoryReceipts /> },
          { path: '/inventory/receipts/create', exact: true, element: <CreateInventoryReceipt /> },
          { path: '/inventory/receipts/:id', exact: true, element: <InventoryReceiptDetails /> },
          { path: '/inventory/float', exact: true, element: <FloatInventory /> },
          { path: '/inventory/float/report', exact: true, element: <FloatInventoryReport /> },
          { path: '/inventory/float/:id', exact: true, element: <FloatInventoryDetails /> },

          // Sales (except POS)
          { path: '/sales/history', exact: true, element: <SalesHistoryPage /> },
          { path: '/sales/summary', exact: true, element: <SalesSummaryPage /> },
          { path: '/sales/list', exact: true, element: <SalesList /> },
          { path: '/sales/details/:id', exact: true, element: <SaleDetails /> },

          // Refunds
          { path: '/refunds', exact: true, element: <RefundManagement /> },

          // Purchases
          { path: '/purchases/requests', exact: true, element: <PurchaseRequests /> },
          { path: '/purchases/requests/create', exact: true, element: <CreatePurchaseRequest /> },
          { path: '/purchases/requests/edit/:id', exact: true, element: <EditPurchaseRequest /> },
          { path: '/purchases/requests/:id', exact: true, element: <PurchaseRequestDetails /> },
          { path: '/purchases/orders', exact: true, element: <PurchaseOrders /> },
          { path: '/purchases/orders/create', exact: true, element: <CreatePurchaseOrder /> },
          { path: '/purchases/orders/edit/:id', exact: true, element: <EditPurchaseOrder /> },
          { path: '/purchases/orders/from-request/:id', exact: true, element: <CreatePurchaseOrderFromRequest /> },
          { path: '/purchases/orders/:id', exact: true, element: <PurchaseOrderDetails /> },

          // Customers
          { path: '/customers', exact: true, element: <CustomerList /> },
          { path: '/customers/create', exact: true, element: <CreateCustomer /> },
          { path: '/customers/edit/:id', exact: true, element: <EditCustomer /> },
          { path: '/customers/details/:id', exact: true, element: <CustomerDetails /> },

          // Suppliers
          { path: '/suppliers', exact: true, element: <SupplierList /> },
          { path: '/suppliers/create', exact: true, element: <SupplierCreate /> },
          { path: '/suppliers/edit/:id', exact: true, element: <SupplierEdit /> },
          { path: '/suppliers/:id', exact: true, element: <SupplierDetails /> },

          // Employees
          { path: '/employees', exact: true, element: <EmployeeList /> },
          { path: '/employees/edit/:id', exact: true, element: <EditEmployee /> },
          { path: '/employees/details/:id', exact: true, element: <EmployeeDetails /> },

          // Payroll
          { path: '/payroll', exact: true, element: <PayrollList /> },
          { path: '/payroll/periods/:id', exact: true, element: <PayrollPeriodDetails /> },
          { path: '/payroll/periods/:id/edit', exact: true, element: <EditPayrollPeriod /> },
          { path: '/payroll/periods/:id/process', exact: true, element: <PayrollProcessing /> },
          { path: '/payroll/periods/:id/payslips', exact: true, element: <PayrollPayslips /> },
          { path: '/payroll/items/:id', exact: true, element: <PayrollItemDetails /> },
          { path: '/payroll/items/:id/edit', exact: true, element: <EditPayrollItem /> },
          { path: '/payroll/settings', exact: true, element: <PayrollSettings /> },
          { path: '/payroll/employee-contribution-preferences', exact: true, element: <EmployeeContributionPreferences /> },
          { path: '/payroll/time-entries', exact: true, element: <TimeEntries /> },
          { path: '/payroll/add-time-entry', exact: true, element: <AddTimeEntry /> },
          { path: '/payroll/edit-time-entry/:id', exact: true, element: <EditTimeEntry /> },
          { path: '/payroll/employee-salaries', exact: true, element: <EmployeeSalaries /> },
          { path: '/payroll/reports', exact: true, element: <PayrollReports /> },
          { path: '/payroll/reports/government-contributions', exact: true, element: <GovernmentContributionsReportPage /> },
          { path: '/payroll/analytics', exact: true, element: <PayrollAnalytics /> },
          { path: '/employees/departments', exact: true, element: <Departments /> },
          { path: '/employees/job-positions', exact: true, element: <JobPositions /> },
          { path: '/employees/employment-types', exact: true, element: <EmploymentTypes /> },

          // Payables
          { path: '/payables', exact: true, element: <PayablesList /> },
          { path: '/payables/create', exact: true, element: <CreatePayable /> },
          { path: '/payables/:id', exact: true, element: <PayableDetails /> },
          { path: '/payables/:id/pay', exact: true, element: <AddPayment /> },

          // Enhanced Payables - Operational Expenses
          { path: '/expenses/dashboard', exact: true, element: <ExpenseDashboard /> },
          { path: '/expenses/types', exact: true, element: <ExpenseTypes /> },
          { path: '/expenses/recurring', exact: true, element: <RecurringExpenses /> },
          { path: '/expenses/payables', exact: true, element: <EnhancedPayables /> },
          { path: '/expenses/quick-entry', exact: true, element: <QuickExpenseEntry /> },

          // Reports
          { path: '/reports/sales', exact: true, element: <SalesReport /> },
          { path: '/reports/inventory', exact: true, element: <InventoryReports /> },

          // Users
          { path: '/users', exact: true, element: <UserManagement /> },
          { path: '/users/invite', exact: true, element: <InviteUser /> },
          { path: '/users/profile', exact: true, element: <EnhancedUserProfile /> },
          { path: '/users/profile/basic', exact: true, element: <UserProfile /> },
          { path: '/users/change-password', exact: true, element: <ChangePassword /> },

          // Organization settings route (protected)
          { path: '/organization/settings', exact: true, element: <OrganizationSettings /> },

          // Settings
          { path: '/settings/uom', exact: true, element: <UnitOfMeasurementList /> },
          { path: '/settings/qc-checklists', exact: true, element: <QcChecklists /> },

          // Tag Management
          { path: '/settings/tags', exact: true, element: <TagManagement /> },

          // Face Recognition Diagnostics
          { path: '/face-recognition-test', exact: true, element: <FaceRecognitionDiagnostic /> },
          { path: '/settings/tags/:tagId/items', exact: true, element: <TaggedItemsView /> },

          // Chat routes moved to separate layout to prevent unnecessary API calls

          // Debug
          { path: '/debug/invitations', exact: true, element: <InvitationDebug /> },
          { path: '/debug/accept-invitation', exact: true, element: <AcceptInvitationDebug /> },
          { path: '/debug/invitation-diagnostic', exact: true, element: <InvitationDiagnostic /> },

          // UI Components
          { path: '/ui/typography', exact: true, element: <Typography /> },
          { path: '/ui/table', exact: true, element: <Table /> },
          { path: '/ui/form', exact: true, element: <Form /> },
          { path: '/ui/shadow', exact: true, element: <Shadow /> },
          { path: '/icons/solar', exact: true, element: <Solar /> },
          { path: '/sample-page', exact: true, element: <SamplePage /> },

          // Move the edit-purchase-request route here to get the proper layout
          { path: '/edit-purchase-request/:id', exact: true, element: <EditPurchaseRequest /> },

          // This should be the last route in this section
          { path: '*', element: <Navigate to="/404" /> },
        ],
      },
    ],
  },  {
    path: '/',
    element: <BlankLayout />,
    children: [
      { path: '/auth/login', element: <Login /> },
      { path: '/auth/register', element: <Register /> },
      { path: '/auth/forgot-password', element: <ForgotPassword /> },
      { path: '/auth/reset-password', element: <ResetPassword /> },
      { path: '/auth/accept-invitation', element: <AcceptInvitationDebug /> },
      { path: '/auth/select-organization', element: <OrganizationSelect /> },
      { path: '/onboarding', element: <OnboardingFlow /> },
      { path: '/organization/create', element: <CreateOrganization /> },
      { path: '/debug/camera', element: <CameraDebug /> },
      { path: '/404', element: <Error /> },
      { path: '/auth/404', element: <Error /> },
      { path: '*', element: <Navigate to="/auth/404" /> },
    ],
  },
];

const router = createBrowserRouter(Router);
export default router;
