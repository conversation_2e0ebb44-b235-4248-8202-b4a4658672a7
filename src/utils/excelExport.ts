/**
 * Excel Export Utility
 * Provides functions to export data to Excel format
 */

export interface ExcelColumn {
  header: string;
  key: string;
  width?: number;
  format?: 'text' | 'number' | 'currency' | 'date' | 'percentage';
}

export interface ExcelExportOptions {
  filename: string;
  sheetName?: string;
  columns: ExcelColumn[];
  data: any[];
  title?: string;
  subtitle?: string;
}

/**
 * Format cell value based on column format
 */
const formatCellValue = (value: any, format?: string): string => {
  if (value === null || value === undefined) return '';
  
  switch (format) {
    case 'currency':
      return typeof value === 'number' ? value.toFixed(2) : String(value);
    case 'number':
      return typeof value === 'number' ? value.toString() : String(value);
    case 'date':
      if (value instanceof Date) {
        return value.toLocaleDateString();
      } else if (typeof value === 'string') {
        const date = new Date(value);
        return isNaN(date.getTime()) ? value : date.toLocaleDateString();
      }
      return String(value);
    case 'percentage':
      return typeof value === 'number' ? `${(value * 100).toFixed(2)}%` : String(value);
    default:
      return String(value);
  }
};

/**
 * Get nested object value by key path
 */
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : '';
  }, obj);
};

/**
 * Export data to Excel format (CSV for now, can be enhanced with xlsx library)
 */
export const exportToExcel = (options: ExcelExportOptions): void => {
  const { filename, columns, data, title, subtitle } = options;
  
  // Create CSV content
  const csvRows: string[] = [];
  
  // Add title and subtitle if provided
  if (title) {
    csvRows.push(`"${title}"`);
    csvRows.push(''); // Empty row
  }
  
  if (subtitle) {
    csvRows.push(`"${subtitle}"`);
    csvRows.push(''); // Empty row
  }
  
  // Add headers
  const headers = columns.map(col => `"${col.header}"`);
  csvRows.push(headers.join(','));
  
  // Add data rows
  data.forEach(row => {
    const csvRow = columns.map(col => {
      const value = getNestedValue(row, col.key);
      const formattedValue = formatCellValue(value, col.format);
      // Escape quotes and wrap in quotes
      return `"${String(formattedValue).replace(/"/g, '""')}"`;
    });
    csvRows.push(csvRow.join(','));
  });
  
  // Create and download file
  const csvContent = csvRows.join('\n');
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

/**
 * Export Sales History to Excel
 */
export const exportSalesHistory = (sales: any[], dateRange?: string): void => {
  const columns: ExcelColumn[] = [
    { header: 'Invoice Number', key: 'invoice_number', width: 15 },
    { header: 'Date', key: 'created_at', format: 'date', width: 12 },
    { header: 'Customer', key: 'customer.name', width: 20 },
    { header: 'Cashier', key: 'cashier_name', width: 15 },
    { header: 'Payment Method', key: 'payment_method', width: 15 },
    { header: 'Subtotal', key: 'subtotal', format: 'currency', width: 12 },
    { header: 'Tax Amount', key: 'tax_amount', format: 'currency', width: 12 },
    { header: 'Discount', key: 'discount_amount', format: 'currency', width: 12 },
    { header: 'Total Amount', key: 'total_amount', format: 'currency', width: 12 },
    { header: 'Status', key: 'status', width: 10 },
    { header: 'Notes', key: 'notes', width: 30 }
  ];

  // Process data to add computed fields
  const processedData = sales.map(sale => ({
    ...sale,
    customer: { name: sale.customer?.name || 'Walk-in Customer' },
    cashier_name: sale.cashier ? 
      `${sale.cashier.first_name || ''} ${sale.cashier.last_name || ''}`.trim() || 'Unknown' : 
      'Unknown'
  }));

  exportToExcel({
    filename: `sales_history_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'Sales History',
    title: 'Sales History Report',
    subtitle: dateRange ? `Period: ${dateRange}` : `Generated on ${new Date().toLocaleDateString()}`,
    columns,
    data: processedData
  });
};

/**
 * Export Products to Excel
 */
export const exportProducts = (products: any[]): void => {
  const columns: ExcelColumn[] = [
    { header: 'Name', key: 'name', width: 25 },
    { header: 'SKU', key: 'sku', width: 15 },
    { header: 'Barcode', key: 'barcode', width: 15 },
    { header: 'Category', key: 'category.name', width: 20 },
    { header: 'Unit Price', key: 'unit_price', format: 'currency', width: 12 },
    { header: 'Cost Price', key: 'cost_price', format: 'currency', width: 12 },
    { header: 'Stock Quantity', key: 'stock_quantity', format: 'number', width: 12 },
    { header: 'Min Stock Level', key: 'min_stock_level', format: 'number', width: 12 },
    { header: 'Reorder Quantity', key: 'reorder_quantity', format: 'number', width: 12 },
    { header: 'Status', key: 'status', width: 10 },
    { header: 'Created Date', key: 'created_at', format: 'date', width: 12 }
  ];

  exportToExcel({
    filename: `products_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'Products',
    title: 'Products Report',
    subtitle: `Generated on ${new Date().toLocaleDateString()}`,
    columns,
    data: products
  });
};

/**
 * Export Refunds to Excel
 */
export const exportRefunds = (refunds: any[]): void => {
  const columns: ExcelColumn[] = [
    { header: 'Refund Number', key: 'refund_number', width: 15 },
    { header: 'Date', key: 'created_at', format: 'date', width: 12 },
    { header: 'Original Sale', key: 'original_sale.invoice_number', width: 15 },
    { header: 'Customer', key: 'original_sale.customer.name', width: 20 },
    { header: 'Type', key: 'refund_type', width: 12 },
    { header: 'Reason', key: 'reason', width: 20 },
    { header: 'Subtotal', key: 'subtotal', format: 'currency', width: 12 },
    { header: 'Tax Amount', key: 'tax_amount', format: 'currency', width: 12 },
    { header: 'Restocking Fee', key: 'restocking_fee', format: 'currency', width: 12 },
    { header: 'Total Amount', key: 'total_amount', format: 'currency', width: 12 },
    { header: 'Method', key: 'refund_method', width: 15 },
    { header: 'Status', key: 'status', width: 10 },
    { header: 'Processed Date', key: 'processed_at', format: 'date', width: 12 }
  ];

  exportToExcel({
    filename: `refunds_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'Refunds',
    title: 'Refunds Report',
    subtitle: `Generated on ${new Date().toLocaleDateString()}`,
    columns,
    data: refunds
  });
};

/**
 * Export Inventory to Excel
 */
export const exportInventory = (inventory: any[]): void => {
  const columns: ExcelColumn[] = [
    { header: 'Product Name', key: 'product.name', width: 25 },
    { header: 'SKU', key: 'product.sku', width: 15 },
    { header: 'Category', key: 'product.category.name', width: 20 },
    { header: 'Current Stock', key: 'stock_quantity', format: 'number', width: 12 },
    { header: 'Unit Price', key: 'product.unit_price', format: 'currency', width: 12 },
    { header: 'Total Value', key: 'total_value', format: 'currency', width: 12 },
    { header: 'Min Stock Level', key: 'product.min_stock_level', format: 'number', width: 12 },
    { header: 'Reorder Quantity', key: 'product.reorder_quantity', format: 'number', width: 12 },
    { header: 'Last Updated', key: 'updated_at', format: 'date', width: 12 },
    { header: 'Status', key: 'status', width: 10 }
  ];

  // Process data to add computed fields
  const processedData = inventory.map(item => ({
    ...item,
    total_value: (item.stock_quantity || 0) * (item.product?.unit_price || 0),
    status: item.stock_quantity === 0 ? 'Out of Stock' : 
            item.stock_quantity <= (item.product?.min_stock_level || 0) ? 'Low Stock' : 'In Stock'
  }));

  exportToExcel({
    filename: `inventory_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'Inventory',
    title: 'Inventory Report',
    subtitle: `Generated on ${new Date().toLocaleDateString()}`,
    columns,
    data: processedData
  });
};

/**
 * Export Customers to Excel
 */
export const exportCustomers = (customers: any[]): void => {
  const columns: ExcelColumn[] = [
    { header: 'Name', key: 'name', width: 25 },
    { header: 'Email', key: 'email', width: 25 },
    { header: 'Phone', key: 'phone', width: 15 },
    { header: 'Address', key: 'address', width: 30 },
    { header: 'City', key: 'city', width: 15 },
    { header: 'State', key: 'state', width: 15 },
    { header: 'Postal Code', key: 'postal_code', width: 12 },
    { header: 'Country', key: 'country', width: 15 },
    { header: 'Tax ID', key: 'tax_id', width: 15 },
    { header: 'Created Date', key: 'created_at', format: 'date', width: 12 },
    { header: 'Notes', key: 'notes', width: 30 }
  ];

  exportToExcel({
    filename: `customers_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'Customers',
    title: 'Customers Report',
    subtitle: `Generated on ${new Date().toLocaleDateString()}`,
    columns,
    data: customers
  });
};

/**
 * Export Suppliers to Excel
 */
export const exportSuppliers = (suppliers: any[]): void => {
  const columns: ExcelColumn[] = [
    { header: 'Name', key: 'name', width: 25 },
    { header: 'Contact Person', key: 'contact_person', width: 20 },
    { header: 'Email', key: 'email', width: 25 },
    { header: 'Phone', key: 'phone', width: 15 },
    { header: 'Address', key: 'address', width: 30 },
    { header: 'City', key: 'city', width: 15 },
    { header: 'State', key: 'state', width: 15 },
    { header: 'Postal Code', key: 'postal_code', width: 12 },
    { header: 'Country', key: 'country', width: 15 },
    { header: 'Tax ID', key: 'tax_id', width: 15 },
    { header: 'Created Date', key: 'created_at', format: 'date', width: 12 },
    { header: 'Notes', key: 'notes', width: 30 }
  ];

  exportToExcel({
    filename: `suppliers_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'Suppliers',
    title: 'Suppliers Report',
    subtitle: `Generated on ${new Date().toLocaleDateString()}`,
    columns,
    data: suppliers
  });
};

/**
 * Export Employees to Excel
 */
export const exportEmployees = (employees: any[]): void => {
  const columns: ExcelColumn[] = [
    { header: 'Employee Number', key: 'employee_number', width: 15 },
    { header: 'First Name', key: 'first_name', width: 15 },
    { header: 'Last Name', key: 'last_name', width: 15 },
    { header: 'Email', key: 'email', width: 25 },
    { header: 'Phone', key: 'phone', width: 15 },
    { header: 'Department', key: 'department.name', width: 20 },
    { header: 'Position', key: 'position.title', width: 20 },
    { header: 'Employment Type', key: 'employment_type.name', width: 15 },
    { header: 'Hire Date', key: 'hire_date', format: 'date', width: 12 },
    { header: 'Status', key: 'status', width: 10 },
    { header: 'City', key: 'city', width: 15 },
    { header: 'State', key: 'state', width: 15 },
    { header: 'Country', key: 'country', width: 15 },
    { header: 'Created Date', key: 'created_at', format: 'date', width: 12 }
  ];

  exportToExcel({
    filename: `employees_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'Employees',
    title: 'Employees Report',
    subtitle: `Generated on ${new Date().toLocaleDateString()}`,
    columns,
    data: employees
  });
};

/**
 * Export Payables to Excel
 */
export const exportPayables = (payables: any[]): void => {
  const columns: ExcelColumn[] = [
    { header: 'Reference Number', key: 'reference_number', width: 15 },
    { header: 'Supplier', key: 'supplier.name', width: 25 },
    { header: 'Invoice Date', key: 'invoice_date', format: 'date', width: 12 },
    { header: 'Due Date', key: 'due_date', format: 'date', width: 12 },
    { header: 'Amount', key: 'amount', format: 'currency', width: 12 },
    { header: 'VAT Amount', key: 'vat_amount', format: 'currency', width: 12 },
    { header: 'Withholding Tax', key: 'withholding_tax_amount', format: 'currency', width: 12 },
    { header: 'Total Paid', key: 'total_paid', format: 'currency', width: 12 },
    { header: 'Balance', key: 'balance', format: 'currency', width: 12 },
    { header: 'Status', key: 'status', width: 10 },
    { header: 'Category', key: 'category', width: 15 },
    { header: 'Created Date', key: 'created_at', format: 'date', width: 12 }
  ];

  exportToExcel({
    filename: `payables_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'Payables',
    title: 'Payables Report',
    subtitle: `Generated on ${new Date().toLocaleDateString()}`,
    columns,
    data: payables
  });
};
