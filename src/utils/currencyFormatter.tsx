import React from 'react';
import { useOrganizationSettings } from '../context/OrganizationSettingsContext';
import { formatCurrency } from './formatters';

/**
 * Format a number as currency with the organization's currency symbol
 * @param value The number to format
 * @param includeCurrency Whether to include the currency symbol (default: true)
 * @returns Formatted currency string with the organization's currency symbol
 */
export const useCurrencyFormatter = () => {
  const { settings } = useOrganizationSettings();

  const formatWithCurrency = (
    value: number | null | undefined,
    includeCurrency = true
  ): string => {
    if (value === null || value === undefined) {
      return includeCurrency ? `${getCurrencySymbol(settings)} 0.00` : '0.00';
    }

    // Get currency code and symbol from settings
    const currencyCode = getCurrencyCode(settings);
    const currencySymbol = getCurrencySymbol(settings);

    // Format with the correct currency code
    const formattedValue = formatCurrency(
      value,
      settings?.currency_settings?.locale || 'en-PH',
      currencyCode,
      false // Don't include currency in formatCurrency since we'll add it manually
    );

    return includeCurrency
      ? `${currencySymbol} ${formattedValue}`
      : formattedValue;
  };

  return formatWithCurrency;
};

// Helper function to get currency code from settings
const getCurrencyCode = (settings: any): string => {
  // First try to get from currency_settings
  if (settings?.currency_settings?.code) {
    return settings.currency_settings.code;
  }

  // Fall back to the legacy currency field
  if (settings?.currency) {
    return settings.currency;
  }

  // Default to PHP
  return 'PHP';
};

// Helper function to get currency symbol from settings
const getCurrencySymbol = (settings: any): string => {
  // First try to get from currency_settings
  if (settings?.currency_settings?.symbol) {
    return settings.currency_settings.symbol;
  }

  // Fall back to the legacy currency field and map to symbol
  if (settings?.currency) {
    // Map common currency codes to symbols
    const symbolMap: Record<string, string> = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'PHP': '₱',
      'AUD': 'A$',
      'CAD': 'C$',
      'CHF': 'Fr',
      'CNY': '¥',
      'HKD': 'HK$',
      'NZD': 'NZ$',
      'SGD': 'S$',
      'INR': '₹',
      'MYR': 'RM',
      'THB': '฿',
      'KRW': '₩',
      'IDR': 'Rp',
      'VND': '₫',
    };

    return symbolMap[settings.currency] || settings.currency;
  }

  // Default to PHP peso symbol
  return '₱';
};

/**
 * React component to display a currency value
 * @param value The number to format
 * @param includeCurrency Whether to include the currency symbol (default: true)
 * @returns Formatted currency string with the organization's currency symbol
 */
export const CurrencyDisplay: React.FC<{
  value: number | null | undefined;
  includeCurrency?: boolean;
}> = ({ value, includeCurrency = true }) => {
  const formatWithCurrency = useCurrencyFormatter();

  return <>{formatWithCurrency(value, includeCurrency)}</>;
};
