import { supabase } from '../lib/supabase';

/**
 * Clean up corrupted authentication state
 * This fixes issues with malformed JWT tokens
 */
export const cleanupAuthState = async (): Promise<void> => {
  try {
    console.log('🧹 Cleaning up corrupted auth state...');
    
    // 1. Sign out from Supabase (this clears server-side session)
    await supabase.auth.signOut();
    
    // 2. Clear all auth-related localStorage items
    const authKeys = [
      'supabase.auth.token',
      'sb-emsqmhmteniizdwljfav-auth-token',
      'sb-auth-token',
      'selectedOrganizationId',
      'currentOrganizationId'
    ];
    
    authKeys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`🗑️ Removed localStorage key: ${key}`);
    });
    
    // 3. Clear all onboarding-related items (in case they're corrupted)
    const onboardingKeys = Object.keys(localStorage).filter(key => 
      key.startsWith('onboarding_') || key.startsWith('pendingProfileSetup')
    );
    
    onboardingKeys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`🗑️ Removed onboarding key: ${key}`);
    });
    
    // 4. Clear sessionStorage as well
    sessionStorage.clear();
    
    console.log('✅ Auth state cleanup completed');
    
    // 5. Force page reload to ensure clean state
    setTimeout(() => {
      window.location.href = '/auth/login';
    }, 100);
    
  } catch (error) {
    console.error('❌ Error during auth cleanup:', error);
    // Even if cleanup fails, redirect to login
    window.location.href = '/auth/login';
  }
};

/**
 * Check if the current JWT token is malformed
 */
export const isTokenMalformed = (token: string | null): boolean => {
  if (!token) return false;
  
  // JWT should have exactly 3 parts separated by dots
  const parts = token.split('.');
  return parts.length !== 3;
};

/**
 * Validate current session and clean up if corrupted
 */
export const validateAndCleanupSession = async (): Promise<boolean> => {
  try {
    console.log('🔍 Validating current session...');
    
    // Try to get current session
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Session validation error:', error);
      
      // Check if it's a JWT error
      if (error.message.includes('JWT') || error.message.includes('token')) {
        console.log('🧹 JWT error detected, cleaning up...');
        await cleanupAuthState();
        return false;
      }
    }
    
    if (!session) {
      console.log('ℹ️ No active session found');
      return false;
    }
    
    // Check if token is malformed
    if (session.access_token && isTokenMalformed(session.access_token)) {
      console.log('🧹 Malformed token detected, cleaning up...');
      await cleanupAuthState();
      return false;
    }
    
    console.log('✅ Session is valid');
    return true;
    
  } catch (error) {
    console.error('❌ Error validating session:', error);
    await cleanupAuthState();
    return false;
  }
};

/**
 * Enhanced error handler for auth-related API calls
 */
export const handleAuthError = async (error: any): Promise<void> => {
  console.error('🚨 Auth error detected:', error);
  
  // Check for JWT-related errors
  const isJWTError = error?.message?.includes('JWT') || 
                    error?.message?.includes('token') ||
                    error?.message?.includes('malformed') ||
                    error?.code === 'bad_jwt';
  
  if (isJWTError) {
    console.log('🧹 JWT error detected, cleaning up auth state...');
    await cleanupAuthState();
  }
};
