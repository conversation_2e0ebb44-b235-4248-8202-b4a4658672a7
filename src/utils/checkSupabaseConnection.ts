import { supabase } from '../lib/supabase';

/**
 * Check if the Supabase connection is working
 */
export const checkSupabaseConnection = async (): Promise<boolean> => {
  try {
    console.log('Checking Supabase connection...');
    
    // Try to get the current time from Supabase
    const { data, error } = await supabase.rpc('get_current_timestamp');
    
    if (error) {
      console.error('Supabase connection error:', error);
      return false;
    }
    
    console.log('Supabase connection successful:', data);
    return true;
  } catch (error) {
    console.error('Supabase connection check failed:', error);
    return false;
  }
};
