/**
 * Time Calculations Utility
 * Shared calculation logic for time entries, overtime, and night differential
 * Used by both manual time entry and face recognition systems
 */

import { differenceInMinutes, isValid } from 'date-fns';

export interface TimeCalculationResult {
  regular_hours: number;
  overtime_hours: number;
  night_diff_hours: number;
  total_hours: number;
  total_minutes: number;
}

/**
 * Calculate night differential hours (10:00 PM to 6:00 AM)
 * This is the exact same function used in AddTimeEntry.tsx and EditTimeEntry.tsx
 */
export const calculateNightDifferentialHours = (timeIn: Date, timeOut: Date): number => {
  // Ensure timeOut is after timeIn (handle overnight shifts)
  let adjustedTimeOut = new Date(timeOut);
  if (adjustedTimeOut < timeIn) {
    adjustedTimeOut.setDate(adjustedTimeOut.getDate() + 1);
  }

  // Create night shift start and end times for the day of timeIn
  const nightShiftStart = new Date(timeIn);
  nightShiftStart.setHours(22, 0, 0, 0); // 10:00 PM

  // Create night shift end for the next day
  const nightShiftEnd = new Date(timeIn);
  nightShiftEnd.setHours(6, 0, 0, 0); // 6:00 AM
  if (nightShiftEnd < nightShiftStart) {
    nightShiftEnd.setDate(nightShiftEnd.getDate() + 1);
  }

  let nightDiffMinutes = 0;

  // Check if the shift overlaps with the night shift on the first day
  if (timeIn <= nightShiftEnd && adjustedTimeOut >= nightShiftStart) {
    const overlapStart = timeIn > nightShiftStart ? timeIn : nightShiftStart;
    const overlapEnd = adjustedTimeOut < nightShiftEnd ? adjustedTimeOut : nightShiftEnd;

    if (overlapEnd > overlapStart) {
      nightDiffMinutes += differenceInMinutes(overlapEnd, overlapStart);
    }
  }

  // Create night shift times for the next day
  const nextDayNightShiftStart = new Date(nightShiftStart);
  nextDayNightShiftStart.setDate(nextDayNightShiftStart.getDate() + 1);

  const nextDayNightShiftEnd = new Date(nightShiftEnd);
  nextDayNightShiftEnd.setDate(nextDayNightShiftEnd.getDate() + 1);

  // Check if the shift overlaps with the night shift on the next day
  if (timeIn <= nextDayNightShiftEnd && adjustedTimeOut >= nextDayNightShiftStart) {
    const overlapStart = timeIn > nextDayNightShiftStart ? timeIn : nextDayNightShiftStart;
    const overlapEnd = adjustedTimeOut < nextDayNightShiftEnd ? adjustedTimeOut : nextDayNightShiftEnd;

    if (overlapEnd > overlapStart) {
      nightDiffMinutes += differenceInMinutes(overlapEnd, overlapStart);
    }
  }

  // Convert minutes to hours (rounded to nearest 0.5)
  return Math.round((nightDiffMinutes / 60) * 2) / 2;
};

/**
 * Calculate all time-related values for a time entry
 * This uses the exact same logic as the manual time entry system
 */
export const calculateTimeEntryHours = (
  timeIn: Date | string,
  timeOut: Date | string,
  excludeLunchBreak: boolean = true
): TimeCalculationResult => {
  // Convert to Date objects if needed
  const timeInDate = typeof timeIn === 'string' ? new Date(timeIn) : timeIn;
  const timeOutDate = typeof timeOut === 'string' ? new Date(timeOut) : timeOut;

  // Validate dates
  if (!isValid(timeInDate) || !isValid(timeOutDate)) {
    return {
      regular_hours: 0,
      overtime_hours: 0,
      night_diff_hours: 0,
      total_hours: 0,
      total_minutes: 0,
    };
  }

  // Handle shifts that span across midnight
  let totalMinutes = 0;

  // If timeOut is earlier than timeIn, it likely means the shift spans to the next day
  if (timeOutDate < timeInDate) {
    // Create a new timeOut date on the next day
    const nextDayTimeOut = new Date(timeOutDate);
    nextDayTimeOut.setDate(nextDayTimeOut.getDate() + 1);
    totalMinutes = differenceInMinutes(nextDayTimeOut, timeInDate);
  } else {
    totalMinutes = differenceInMinutes(timeOutDate, timeInDate);
  }

  // Subtract lunch break if applicable (1 hour = 60 minutes)
  if (excludeLunchBreak && totalMinutes > 300) { // Only subtract if shift is > 5 hours
    totalMinutes -= 60;
  }

  // Calculate regular and overtime hours
  const totalHours = totalMinutes / 60;
  const regularHours = Math.min(8, totalHours);
  const overtimeHours = Math.max(0, totalHours - 8);

  // Round to nearest 0.5
  const roundedRegularHours = Math.round(regularHours * 2) / 2;
  const roundedOvertimeHours = Math.round(overtimeHours * 2) / 2;

  // For night differential calculation, we need to adjust timeOut if it's across midnight
  let adjustedTimeOut = timeOutDate;
  if (timeOutDate < timeInDate) {
    adjustedTimeOut = new Date(timeOutDate);
    adjustedTimeOut.setDate(adjustedTimeOut.getDate() + 1);
  }

  // Calculate night differential hours
  const nightDiffHours = calculateNightDifferentialHours(timeInDate, adjustedTimeOut);

  return {
    regular_hours: roundedRegularHours,
    overtime_hours: roundedOvertimeHours,
    night_diff_hours: nightDiffHours,
    total_hours: roundedRegularHours + roundedOvertimeHours,
    total_minutes: totalMinutes,
  };
};

/**
 * Calculate hours for a time entry that only has time_in (for clock-in only entries)
 */
export const calculatePartialTimeEntry = (timeIn: Date | string): Partial<TimeCalculationResult> => {
  return {
    regular_hours: 0,
    overtime_hours: 0,
    night_diff_hours: 0,
    total_hours: 0,
    total_minutes: 0,
  };
};
