import { supabase } from '../lib/supabase';

/**
 * Check if the required tables exist in the database
 */
export const checkTables = async (): Promise<{
  tables: { name: string; exists: boolean }[];
  error?: string;
}> => {
  try {
    console.log('Checking database tables...');
    
    // List of tables that should exist
    const requiredTables = [
      'profiles',
      'organizations',
      'organization_members',
      'organization_settings'
    ];
    
    // Get the list of tables from the database
    const { data, error } = await supabase
      .from('pg_catalog.pg_tables')
      .select('tablename')
      .eq('schemaname', 'public');
    
    if (error) {
      console.error('Error checking tables:', error);
      return {
        tables: requiredTables.map(name => ({ name, exists: false })),
        error: error.message
      };
    }
    
    // Extract table names from the result
    const existingTables = data?.map(row => row.tablename) || [];
    console.log('Existing tables:', existingTables);
    
    // Check which tables exist
    const tableStatus = requiredTables.map(name => ({
      name,
      exists: existingTables.includes(name)
    }));
    
    return {
      tables: tableStatus
    };
  } catch (error: any) {
    console.error('Error checking tables:', error);
    return {
      tables: [],
      error: error.message
    };
  }
};
