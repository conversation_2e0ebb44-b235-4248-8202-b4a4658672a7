import { supabase } from '../lib/supabase';

export const debugAuth = async () => {
  try {
    console.log('=== AUTH DEBUG START ===');
    
    // Check current session
    const { data: session, error: sessionError } = await supabase.auth.getSession();
    console.log('Session:', session);
    console.log('Session Error:', sessionError);
    
    // Check current user
    const { data: user, error: userError } = await supabase.auth.getUser();
    console.log('User:', user);
    console.log('User Error:', userError);
    
    // Check if user exists in organization_members
    if (user.user) {
      const { data: orgMembers, error: orgError } = await supabase
        .from('organization_members')
        .select('*')
        .eq('user_id', user.user.id);
      
      console.log('Organization Members:', orgMembers);
      console.log('Organization Members Error:', orgError);
    }
    
    console.log('=== AUTH DEBUG END ===');
    
    return {
      session: session?.session,
      user: user.user,
      sessionError,
      userError
    };
  } catch (error) {
    console.error('Auth debug error:', error);
    return { error };
  }
};

// Call this function in the browser console to debug auth issues
(window as any).debugAuth = debugAuth;
