/**
 * CSV Parser Utility
 * Provides functions to parse CSV files and validate data
 */

export interface ParsedCSVRow {
  [key: string]: string | number | boolean | null;
}

export interface CSVParseResult {
  success: boolean;
  data: ParsedCSVRow[];
  headers: string[];
  errors: string[];
  warnings: string[];
}

export interface CSVValidationRule {
  field: string;
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'email' | 'url';
  min?: number;
  max?: number;
  pattern?: RegExp;
  customValidator?: (value: any) => string | null;
}

/**
 * Parse CSV content from text
 */
export const parseCSV = (csvText: string): { headers: string[]; rows: string[][] } => {
  const lines = csvText.trim().split('\n');
  if (lines.length === 0) {
    return { headers: [], rows: [] };
  }

  // Parse CSV with proper quote handling
  const parseCSVLine = (line: string): string[] => {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
      const char = line[i];
      const nextChar = line[i + 1];

      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          // Escaped quote
          current += '"';
          i += 2;
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === ',' && !inQuotes) {
        // Field separator
        result.push(current.trim());
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }

    // Add the last field
    result.push(current.trim());
    return result;
  };

  const headers = parseCSVLine(lines[0]).map(h => h.replace(/^"|"$/g, '').trim());
  const rows = lines.slice(1).map(line => parseCSVLine(line).map(cell => cell.replace(/^"|"$/g, '').trim()));

  return { headers, rows };
};

/**
 * Convert parsed CSV to objects
 */
export const csvToObjects = (headers: string[], rows: string[][]): ParsedCSVRow[] => {
  return rows.map(row => {
    const obj: ParsedCSVRow = {};
    headers.forEach((header, index) => {
      const value = row[index] || '';
      obj[header] = value;
    });
    return obj;
  });
};

/**
 * Validate CSV data against rules
 */
export const validateCSVData = (
  data: ParsedCSVRow[],
  rules: CSVValidationRule[]
): { errors: string[]; warnings: string[] } => {
  const errors: string[] = [];
  const warnings: string[] = [];

  data.forEach((row, rowIndex) => {
    rules.forEach(rule => {
      const value = row[rule.field];
      const rowNum = rowIndex + 2; // +2 because row 1 is headers and we're 0-indexed

      // Check required fields
      if (rule.required && (value === null || value === undefined || value === '')) {
        errors.push(`Row ${rowNum}: ${rule.field} is required`);
        return;
      }

      // Skip validation if field is empty and not required
      if (value === null || value === undefined || value === '') {
        return;
      }

      // Type validation
      if (rule.type) {
        switch (rule.type) {
          case 'number':
            const numValue = Number(value);
            if (isNaN(numValue)) {
              errors.push(`Row ${rowNum}: ${rule.field} must be a valid number`);
            } else {
              // Update the value to be a number
              row[rule.field] = numValue;
              
              // Min/max validation for numbers
              if (rule.min !== undefined && numValue < rule.min) {
                errors.push(`Row ${rowNum}: ${rule.field} must be at least ${rule.min}`);
              }
              if (rule.max !== undefined && numValue > rule.max) {
                errors.push(`Row ${rowNum}: ${rule.field} must be at most ${rule.max}`);
              }
            }
            break;

          case 'boolean':
            const boolValue = String(value).toLowerCase().trim();
            if (!['true', 'false', '1', '0', 'yes', 'no', 't', 'f', 'y', 'n'].includes(boolValue)) {
              errors.push(`Row ${rowNum}: ${rule.field} must be true/false, yes/no, 1/0, or t/f`);
            } else {
              row[rule.field] = ['true', '1', 'yes', 't', 'y'].includes(boolValue);
            }
            break;

          case 'email':
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailPattern.test(String(value))) {
              errors.push(`Row ${rowNum}: ${rule.field} must be a valid email address`);
            }
            break;

          case 'url':
            try {
              new URL(String(value));
            } catch {
              errors.push(`Row ${rowNum}: ${rule.field} must be a valid URL`);
            }
            break;
        }
      }

      // Pattern validation
      if (rule.pattern && !rule.pattern.test(String(value))) {
        errors.push(`Row ${rowNum}: ${rule.field} format is invalid`);
      }

      // Custom validation
      if (rule.customValidator) {
        const customError = rule.customValidator(value);
        if (customError) {
          errors.push(`Row ${rowNum}: ${rule.field} - ${customError}`);
        }
      }
    });
  });

  return { errors, warnings };
};

/**
 * Parse and validate CSV file
 */
export const parseAndValidateCSV = (
  csvText: string,
  validationRules: CSVValidationRule[]
): CSVParseResult => {
  try {
    const { headers, rows } = parseCSV(csvText);
    
    if (headers.length === 0) {
      return {
        success: false,
        data: [],
        headers: [],
        errors: ['CSV file is empty or has no headers'],
        warnings: []
      };
    }

    const data = csvToObjects(headers, rows);
    const { errors, warnings } = validateCSVData(data, validationRules);

    return {
      success: errors.length === 0,
      data,
      headers,
      errors,
      warnings
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      headers: [],
      errors: [`Failed to parse CSV: ${error instanceof Error ? error.message : 'Unknown error'}`],
      warnings: []
    };
  }
};

/**
 * Generate CSV template with headers
 */
export const generateCSVTemplate = (headers: string[]): string => {
  return headers.map(header => `"${header}"`).join(',');
};

/**
 * Download CSV template file
 */
export const downloadCSVTemplate = (filename: string, headers: string[]): void => {
  const csvContent = generateCSVTemplate(headers);
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};
