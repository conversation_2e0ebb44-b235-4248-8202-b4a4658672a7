import { supabase } from '../lib/supabase';
import { ProductUom, UnitOfMeasurement } from '../types/uom.types';

/**
 * Batch fetches product UoMs for multiple products at once
 * @param productIds Array of product IDs to fetch UoMs for
 * @param organizationId Current organization ID
 * @returns Object with productId as key and array of product UoMs as value
 */
export const batchFetchProductUoms = async (
  productIds: string[],
  organizationId: string
): Promise<Record<string, (ProductUom & { uom: UnitOfMeasurement })[]>> => {
  if (!productIds.length || !organizationId) {
    return {};
  }

  try {
    // Fetch all product UoMs for the given product IDs in a single query
    const { data, error } = await supabase
      .from('product_uoms')
      .select(`
        *,
        uom:uom_id (*)
      `)
      .in('product_id', productIds)
      .eq('organization_id', organizationId);

    if (error) {
      console.error('Error fetching product UoMs:', error);
      return {};
    }

    // Group the results by product ID
    const result: Record<string, (ProductUom & { uom: UnitOfMeasurement })[]> = {};
    
    data.forEach((item: ProductUom & { uom: UnitOfMeasurement }) => {
      if (!result[item.product_id]) {
        result[item.product_id] = [];
      }
      result[item.product_id].push(item);
    });

    return result;
  } catch (err) {
    console.error('Error in batchFetchProductUoms:', err);
    return {};
  }
};
