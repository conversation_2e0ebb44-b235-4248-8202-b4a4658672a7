/**
 * Face Recognition Utilities
 * Handles facial recognition using face-api.js library
 */

import * as faceapi from '@vladmandic/face-api';

// Configuration constants
export const FACE_RECOGNITION_CONFIG = {
  MODEL_URL: '/models', // Local models in public/models folder
  CONFIDENCE_THRESHOLD: 0.6,
  DESCRIPTOR_DISTANCE_THRESHOLD: 0.6,
  MIN_FACE_SIZE: 160,
  ENROLLMENT_QUALITY_THRESHOLD: 0.7,
  MAX_ENROLLMENT_ATTEMPTS: 5,
  RECOGNITION_TIMEOUT: 5000, // 5 seconds
};

// Face detection options
export const DETECTION_OPTIONS = new faceapi.TinyFaceDetectorOptions({
  inputSize: 416,
  scoreThreshold: 0.5,
});

// Face recognition model options
export const RECOGNITION_OPTIONS = new faceapi.SsdMobilenetv1Options({
  minConfidence: 0.5,
  maxResults: 1,
});

/**
 * Initialize face-api.js models
 */
export const initializeFaceAPI = async (): Promise<boolean> => {
  try {
    console.log('Loading essential face recognition models...');

    // Load only the essential models that are guaranteed to work
    await Promise.all([
      faceapi.nets.tinyFaceDetector.loadFromUri(FACE_RECOGNITION_CONFIG.MODEL_URL),
      faceapi.nets.faceLandmark68Net.loadFromUri(FACE_RECOGNITION_CONFIG.MODEL_URL),
      faceapi.nets.faceRecognitionNet.loadFromUri(FACE_RECOGNITION_CONFIG.MODEL_URL),
    ]);

    // Try to load additional models (optional)
    try {
      await faceapi.nets.ssdMobilenetv1.loadFromUri(FACE_RECOGNITION_CONFIG.MODEL_URL);
      console.log('✅ SSD MobileNet loaded');
    } catch (err) {
      console.log('⚠️ SSD MobileNet not available, using TinyFaceDetector only');
    }

    // Try to load age/gender and expression models for live analysis
    try {
      await faceapi.nets.ageGenderNet.loadFromUri(FACE_RECOGNITION_CONFIG.MODEL_URL);
      console.log('✅ Age/Gender model loaded');
    } catch (err) {
      console.log('⚠️ Age/Gender model not available');
    }

    try {
      await faceapi.nets.faceExpressionNet.loadFromUri(FACE_RECOGNITION_CONFIG.MODEL_URL);
      console.log('✅ Expression model loaded');
    } catch (err) {
      console.log('⚠️ Expression model not available');
    }

    console.log('✅ Face recognition models loaded successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to load face recognition models:', error);
    return false;
  }
};

/**
 * Detect faces in an image/video element
 */
export const detectFaces = async (
  input: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement
): Promise<faceapi.WithFaceLandmarks<{ detection: faceapi.FaceDetection }, faceapi.FaceLandmarks68>[]> => {
  try {
    const detections = await faceapi
      .detectAllFaces(input, DETECTION_OPTIONS)
      .withFaceLandmarks();

    return detections;
  } catch (error) {
    console.error('Face detection failed:', error);
    return [];
  }
};

/**
 * Live face analysis with age, gender, expression, and landmarks
 */
export interface FaceAnalysis {
  detection: faceapi.FaceDetection;
  landmarks: faceapi.FaceLandmarks68;
  age?: number;
  gender?: string;
  genderProbability?: number;
  expressions?: { [key: string]: number };
  dominantExpression?: string;
  expressionProbability?: number;
  faceOrientation?: {
    roll: number;
    pitch: number;
    yaw: number;
  };
}

export const analyzeFace = async (
  input: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement
): Promise<FaceAnalysis | null> => {
  try {
    // Start with basic detection and landmarks
    let detection = await faceapi
      .detectSingleFace(input, DETECTION_OPTIONS)
      .withFaceLandmarks();

    if (!detection) {
      return null;
    }

    const result: FaceAnalysis = {
      detection: detection.detection,
      landmarks: detection.landmarks,
    };

    // Try to add age and gender if model is loaded
    if (faceapi.nets.ageGenderNet.isLoaded) {
      try {
        const ageGenderResult = await faceapi
          .detectSingleFace(input, DETECTION_OPTIONS)
          .withFaceLandmarks()
          .withAgeAndGender();

        if (ageGenderResult) {
          result.age = Math.round(ageGenderResult.age);
          result.gender = ageGenderResult.gender;
          result.genderProbability = ageGenderResult.genderProbability;
        }
      } catch (err) {
        console.log('Age/Gender analysis failed:', err);
      }
    }

    // Try to add expressions if model is loaded
    if (faceapi.nets.faceExpressionNet.isLoaded) {
      try {
        const expressionResult = await faceapi
          .detectSingleFace(input, DETECTION_OPTIONS)
          .withFaceLandmarks()
          .withFaceExpressions();

        if (expressionResult) {
          result.expressions = expressionResult.expressions;

          // Find dominant expression
          const expressions = expressionResult.expressions;
          let maxExpression = '';
          let maxProbability = 0;

          Object.entries(expressions).forEach(([expression, probability]) => {
            if (probability > maxProbability) {
              maxExpression = expression;
              maxProbability = probability;
            }
          });

          result.dominantExpression = maxExpression;
          result.expressionProbability = maxProbability;
        }
      } catch (err) {
        console.log('Expression analysis failed:', err);
      }
    }

    // Calculate face orientation from landmarks
    if (result.landmarks) {
      result.faceOrientation = calculateFaceOrientation(result.landmarks);
    }

    return result;
  } catch (error) {
    console.error('Face analysis failed:', error);
    return null;
  }
};

/**
 * Calculate face orientation (roll, pitch, yaw) from landmarks
 */
const calculateFaceOrientation = (landmarks: faceapi.FaceLandmarks68) => {
  const points = landmarks.positions;

  // Get key facial points
  const leftEye = points[36]; // Left eye outer corner
  const rightEye = points[45]; // Right eye outer corner
  const nose = points[30]; // Nose tip
  const leftMouth = points[48]; // Left mouth corner
  const rightMouth = points[54]; // Right mouth corner

  // Calculate roll (rotation around z-axis)
  const eyeVector = {
    x: rightEye.x - leftEye.x,
    y: rightEye.y - leftEye.y
  };
  const roll = Math.atan2(eyeVector.y, eyeVector.x) * (180 / Math.PI);

  // Calculate yaw (rotation around y-axis) - simplified
  const faceWidth = Math.abs(rightEye.x - leftEye.x);
  const noseOffset = nose.x - (leftEye.x + rightEye.x) / 2;
  const yaw = (noseOffset / faceWidth) * 60; // Approximate scaling

  // Calculate pitch (rotation around x-axis) - simplified
  const eyeLevel = (leftEye.y + rightEye.y) / 2;
  const mouthLevel = (leftMouth.y + rightMouth.y) / 2;
  const faceHeight = Math.abs(mouthLevel - eyeLevel);
  const noseVerticalOffset = nose.y - eyeLevel;
  const pitch = (noseVerticalOffset / faceHeight) * 30; // Approximate scaling

  return {
    roll: Math.round(roll),
    pitch: Math.round(pitch),
    yaw: Math.round(yaw)
  };
};

/**
 * Draw live face analysis overlay on canvas
 */
export const drawFaceAnalysisOverlay = (
  canvas: HTMLCanvasElement,
  analysis: FaceAnalysis,
  options: {
    showLandmarks?: boolean;
    showBoundingBox?: boolean;
    showAnalysisText?: boolean;
  } = {}
) => {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  const {
    showLandmarks = true,
    showBoundingBox = true,
    showAnalysisText = true
  } = options;

  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // Draw bounding box
  if (showBoundingBox) {
    const { x, y, width, height } = analysis.detection.box;
    ctx.strokeStyle = '#00bfff';
    ctx.lineWidth = 3;
    ctx.strokeRect(x, y, width, height);
  }

  // Draw landmarks
  if (showLandmarks && analysis.landmarks) {
    ctx.fillStyle = '#ffffff';
    analysis.landmarks.positions.forEach(point => {
      ctx.beginPath();
      ctx.arc(point.x, point.y, 2, 0, 2 * Math.PI);
      ctx.fill();
    });
  }

  // Draw analysis text - compact style like in demo
  if (showAnalysisText) {
    const { x, y, width } = analysis.detection.box;
    let textY = y - 15;

    // Use smaller, more compact font
    ctx.font = 'bold 14px Arial';
    ctx.fillStyle = '#ffffff';
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 3;

    // Gender - compact format
    if (analysis.gender && analysis.genderProbability) {
      const genderText = `GENDER: ${Math.round(analysis.genderProbability * 100)}% ${analysis.gender.toUpperCase()}`;
      ctx.strokeText(genderText, x, textY);
      ctx.fillText(genderText, x, textY);
      textY -= 20;
    }

    // Expression - compact format
    if (analysis.dominantExpression && analysis.expressionProbability) {
      const expressionText = `EXPRESSION: ${Math.round(analysis.expressionProbability * 100)}% ${analysis.dominantExpression.toUpperCase()}`;
      ctx.strokeText(expressionText, x, textY);
      ctx.fillText(expressionText, x, textY);
      textY -= 20;
    }

    // Age - compact format
    if (analysis.age) {
      const ageText = `AGE: ${analysis.age} YEARS`;
      ctx.strokeText(ageText, x, textY);
      ctx.fillText(ageText, x, textY);
      textY -= 20;
    }

    // Face orientation - compact format on one line
    if (analysis.faceOrientation) {
      const { roll, pitch, yaw } = analysis.faceOrientation;
      const orientationText = `ROLL: ${roll}° PITCH: ${pitch}° YAW: ${yaw}°`;
      ctx.strokeText(orientationText, x, textY);
      ctx.fillText(orientationText, x, textY);
    }
  }
};

/**
 * Extract face descriptor from detected face
 */
export const extractFaceDescriptor = async (
  input: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement
): Promise<Float32Array | null> => {
  try {
    const detection = await faceapi
      .detectSingleFace(input, DETECTION_OPTIONS)
      .withFaceLandmarks()
      .withFaceDescriptor();
    
    if (!detection) {
      return null;
    }
    
    return detection.descriptor;
  } catch (error) {
    console.error('Face descriptor extraction failed:', error);
    return null;
  }
};

/**
 * Calculate quality score for face enrollment
 */
export const calculateFaceQuality = async (
  input: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement
): Promise<number> => {
  try {
    const detection = await faceapi
      .detectSingleFace(input, DETECTION_OPTIONS)
      .withFaceLandmarks();
    
    if (!detection) {
      return 0;
    }
    
    const { detection: faceDetection } = detection;
    const faceSize = Math.min(faceDetection.box.width, faceDetection.box.height);
    
    // Quality factors
    const sizeScore = Math.min(faceSize / FACE_RECOGNITION_CONFIG.MIN_FACE_SIZE, 1);
    const confidenceScore = faceDetection.score;
    
    // Calculate overall quality (0-1)
    const qualityScore = (sizeScore * 0.4 + confidenceScore * 0.6);
    
    return Math.round(qualityScore * 100) / 100;
  } catch (error) {
    console.error('Face quality calculation failed:', error);
    return 0;
  }
};

/**
 * Compare face descriptors and return similarity score
 */
export const compareFaceDescriptors = (
  descriptor1: Float32Array,
  descriptor2: Float32Array
): number => {
  try {
    const distance = faceapi.euclideanDistance(descriptor1, descriptor2);
    // Convert distance to similarity score (0-1, where 1 is perfect match)
    const similarity = Math.max(0, 1 - distance);
    return Math.round(similarity * 100) / 100;
  } catch (error) {
    console.error('Face descriptor comparison failed:', error);
    return 0;
  }
};

/**
 * Find best matching face from a list of known descriptors
 */
export const findBestMatch = (
  inputDescriptor: Float32Array,
  knownDescriptors: { id: string; descriptor: Float32Array; threshold?: number }[]
): { id: string; confidence: number } | null => {
  try {
    let bestMatch: { id: string; confidence: number } | null = null;
    
    for (const known of knownDescriptors) {
      const confidence = compareFaceDescriptors(inputDescriptor, known.descriptor);
      const threshold = known.threshold || FACE_RECOGNITION_CONFIG.CONFIDENCE_THRESHOLD;
      
      if (confidence >= threshold && (!bestMatch || confidence > bestMatch.confidence)) {
        bestMatch = { id: known.id, confidence };
      }
    }
    
    return bestMatch;
  } catch (error) {
    console.error('Face matching failed:', error);
    return null;
  }
};

/**
 * Capture face from video stream with enhanced detection
 */
export const captureFaceFromVideo = async (
  video: HTMLVideoElement,
  canvas: HTMLCanvasElement
): Promise<{
  imageData: string;
  descriptor: Float32Array | null;
  quality: number;
  detectionMethod?: string;
}> => {
  try {
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Canvas context not available');
    }

    // Check if video has valid dimensions
    if (!video.videoWidth || !video.videoHeight) {
      console.warn('⚠️ Video dimensions are 0:', {
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight,
        readyState: video.readyState,
        paused: video.paused,
        srcObject: !!video.srcObject
      });
      throw new Error('Video not ready - dimensions are 0x0');
    }

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    console.log('📐 Canvas dimensions set to:', canvas.width, 'x', canvas.height);

    // Draw current video frame to canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Try multiple detection methods for better success rate
    let descriptor: Float32Array | null = null;
    let quality = 0;
    let detectionMethod = '';

    console.log('🔍 Attempting face capture with multiple detection methods...');

    // Method 1: TinyFaceDetector (default)
    try {
      console.log('Trying TinyFaceDetector...');
      descriptor = await extractFaceDescriptor(canvas);
      if (descriptor) {
        quality = await calculateFaceQuality(canvas);
        detectionMethod = 'TinyFaceDetector';
        console.log(`✅ TinyFaceDetector success - Quality: ${(quality * 100).toFixed(1)}%`);
      }
    } catch (err) {
      console.log('TinyFaceDetector failed:', err);
    }

    // Method 2: SSD MobileNet (if first method failed)
    if (!descriptor) {
      try {
        console.log('Trying SSD MobileNet...');
        // Check if model is loaded
        if (faceapi.nets.ssdMobilenetv1.isLoaded) {
          const detection = await faceapi
            .detectSingleFace(canvas, new faceapi.SsdMobilenetv1Options())
            .withFaceLandmarks()
            .withFaceDescriptor();

          if (detection) {
            descriptor = detection.descriptor;
            quality = await calculateFaceQuality(canvas);
            detectionMethod = 'SSD MobileNet';
            console.log(`✅ SSD MobileNet success - Quality: ${(quality * 100).toFixed(1)}%`);
          }
        } else {
          console.log('SSD MobileNet model not loaded, skipping...');
        }
      } catch (err) {
        console.log('SSD MobileNet failed:', err);
      }
    }

    // If no detection method worked, try one more time with TinyFaceDetector with different options
    if (!descriptor) {
      try {
        console.log('Trying TinyFaceDetector with relaxed options...');
        const detection = await faceapi
          .detectSingleFace(canvas, new faceapi.TinyFaceDetectorOptions({ inputSize: 320, scoreThreshold: 0.3 }))
          .withFaceLandmarks()
          .withFaceDescriptor();

        if (detection) {
          descriptor = detection.descriptor;
          quality = await calculateFaceQuality(canvas);
          detectionMethod = 'TinyFaceDetector (relaxed)';
          console.log(`✅ TinyFaceDetector (relaxed) success - Quality: ${(quality * 100).toFixed(1)}%`);
        }
      } catch (err) {
        console.log('TinyFaceDetector (relaxed) failed:', err);
      }
    }

    const imageData = canvas.toDataURL('image/jpeg', 0.8);

    if (descriptor) {
      console.log(`🎯 Face capture successful using ${detectionMethod}`);
    } else {
      console.log('❌ All detection methods failed');
    }

    return {
      imageData,
      descriptor,
      quality,
      detectionMethod,
    };
  } catch (error) {
    console.error('Face capture failed:', error);
    return {
      imageData: '',
      descriptor: null,
      quality: 0,
    };
  }
};

/**
 * Validate face enrollment quality with more lenient thresholds
 */
export const validateEnrollmentQuality = (quality: number): {
  isValid: boolean;
  message: string;
  level: 'excellent' | 'good' | 'acceptable' | 'poor';
} => {
  console.log(`🔍 Validating enrollment quality: ${(quality * 100).toFixed(1)}%`);

  if (quality < 0.2) {
    return {
      isValid: false,
      message: 'Face not detected clearly. Please ensure good lighting and face the camera directly.',
      level: 'poor',
    };
  }

  if (quality < 0.3) {
    return {
      isValid: false,
      message: 'Face quality is low. Please move closer to the camera and ensure good lighting.',
      level: 'poor',
    };
  }

  if (quality < 0.4) {
    return {
      isValid: true, // More lenient - accept lower quality
      message: 'Face quality is acceptable. Consider retaking for better accuracy.',
      level: 'acceptable',
    };
  }

  if (quality < 0.6) {
    return {
      isValid: true,
      message: 'Face quality is good for enrollment.',
      level: 'good',
    };
  }

  return {
    isValid: true,
    message: 'Excellent face quality for enrollment.',
    level: 'excellent',
  };
};

/**
 * Convert Float32Array to regular array for JSON storage
 */
export const descriptorToArray = (descriptor: Float32Array): number[] => {
  return Array.from(descriptor);
};

/**
 * Convert regular array back to Float32Array
 */
export const arrayToDescriptor = (array: number[]): Float32Array => {
  return new Float32Array(array);
};

/**
 * Get camera stream with optimal settings for face recognition
 */
export const getCameraStream = async (deviceId?: string): Promise<MediaStream | null> => {
  try {
    const constraints: MediaStreamConstraints = {
      video: {
        width: { ideal: 640 },
        height: { ideal: 480 },
        frameRate: { ideal: 30 },
        facingMode: 'user',
        ...(deviceId && { deviceId: { exact: deviceId } }),
      },
      audio: false,
    };
    
    const stream = await navigator.mediaDevices.getUserMedia(constraints);
    return stream;
  } catch (error) {
    console.error('Failed to get camera stream:', error);
    return null;
  }
};

/**
 * Get available camera devices
 */
export const getCameraDevices = async (): Promise<MediaDeviceInfo[]> => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices();
    return devices.filter(device => device.kind === 'videoinput');
  } catch (error) {
    console.error('Failed to enumerate camera devices:', error);
    return [];
  }
};

/**
 * Check if browser supports required features
 */
export const checkBrowserSupport = (): {
  supported: boolean;
  missing: string[];
} => {
  const missing: string[] = [];
  
  if (!navigator.mediaDevices?.getUserMedia) {
    missing.push('Camera access (getUserMedia)');
  }
  
  if (!window.HTMLCanvasElement) {
    missing.push('Canvas support');
  }
  
  if (!window.WebAssembly) {
    missing.push('WebAssembly support');
  }
  
  return {
    supported: missing.length === 0,
    missing,
  };
};
