/**
 * Format a number as currency
 * @param value The number to format
 * @param locale The locale to use for formatting (default: 'en-PH')
 * @param currency The currency code to use (default: 'PHP')
 * @param includeCurrency Whether to include the currency symbol (default: true)
 * @returns Formatted currency string
 */
export const formatCurrency = (
  value: number | null | undefined,
  locale = 'en-PH',
  currency = 'PHP',
  includeCurrency = true
): string => {
  if (value === null || value === undefined) {
    return includeCurrency ? '₱0.00' : '0.00';
  }

  if (includeCurrency) {
    // Format with currency symbol
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  } else {
    // Just format the number without currency symbol
    return new Intl.NumberFormat(locale, {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  }
};

/**
 * Format a date
 * @param date The date to format
 * @param locale The locale to use for formatting (default: 'en-US')
 * @param options The options to use for formatting
 * @returns Formatted date string
 */
export const formatDate = (
  date: Date | string | null | undefined,
  locale = 'en-US',
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }
): string => {
  if (!date) {
    return '';
  }

  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat(locale, options).format(dateObj);
};

/**
 * Format a number
 * @param value The number to format
 * @param locale The locale to use for formatting (default: 'en-US')
 * @param options The options to use for formatting
 * @returns Formatted number string
 */
export const formatNumber = (
  value: number | null | undefined,
  locale = 'en-US',
  options: Intl.NumberFormatOptions = {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }
): string => {
  if (value === null || value === undefined) {
    return '0';
  }

  return new Intl.NumberFormat(locale, options).format(value);
};

/**
 * Format a percentage
 * @param value The number to format as percentage
 * @param locale The locale to use for formatting (default: 'en-US')
 * @param decimalPlaces The number of decimal places to show (default: 2)
 * @returns Formatted percentage string
 */
export const formatPercentage = (
  value: number | null | undefined,
  locale = 'en-US',
  decimalPlaces = 2
): string => {
  if (value === null || value === undefined) {
    return '0%';
  }

  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces,
  }).format(value / 100);
};

/**
 * Format a phone number
 * @param phone The phone number to format
 * @returns Formatted phone number string
 */
export const formatPhoneNumber = (phone: string | null | undefined): string => {
  if (!phone) {
    return '';
  }

  // Remove all non-numeric characters
  const cleaned = phone.replace(/\D/g, '');

  // Format based on length
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }

  // If it doesn't match expected formats, return as is
  return phone;
};

/**
 * Format a date and time
 * @param date The date to format
 * @param locale The locale to use for formatting (default: 'en-US')
 * @returns Formatted date and time string
 */
export const formatDateTime = (
  date: Date | string | null | undefined,
  locale = 'en-US'
): string => {
  if (!date) {
    return '';
  }

  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(dateObj);
};

/**
 * Format a quantity with thousand separators
 * @param quantity The quantity to format
 * @param locale The locale to use for formatting (default: 'en-US')
 * @returns Formatted quantity string with thousand separators
 */
export const formatQuantityWithSeparators = (
  quantity: number | null | undefined,
  locale = 'en-US'
): string => {
  if (quantity === null || quantity === undefined) {
    return '0';
  }

  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(quantity);
};

/**
 * Format a quantity with unit of measure
 * @param quantity The quantity to format
 * @param uom The unit of measure code or name
 * @param locale The locale to use for formatting (default: 'en-US')
 * @returns Formatted quantity string with unit of measure
 */
export const formatQuantity = (
  quantity: number | null | undefined,
  uom?: string | null | undefined,
  locale = 'en-US'
): string => {
  if (quantity === null || quantity === undefined) {
    return '';
  }

  const formattedNumber = formatQuantityWithSeparators(quantity, locale);
  return uom ? `${formattedNumber} ${uom}` : formattedNumber;
};

