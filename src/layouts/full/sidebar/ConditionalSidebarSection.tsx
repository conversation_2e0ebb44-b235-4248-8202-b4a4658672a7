import React from 'react';
import { MenuItem } from './Sidebaritems';
import ConditionalNavItem from './ConditionalNavItem';
import { useOrganizationSettings } from '../../../context/OrganizationSettingsContext';

interface ConditionalSidebarSectionProps {
  item: MenuItem;
}

/**
 * A component that conditionally renders a sidebar section based on feature flags
 * in the organization settings.
 */
const ConditionalSidebarSection: React.FC<ConditionalSidebarSectionProps> = ({ item }) => {
  const { settings } = useOrganizationSettings();
  
  // Check if the section should be hidden based on feature flags
  const shouldHideSection = () => {
    // Hide COMMUNICATION section if chat is disabled
    if (item.heading === 'COMMUNICATION') {
      return settings.chat_enabled === false;
    }
    
    // Add more conditions here for other features
    
    return false;
  };
  
  // If the section should be hidden, don't render anything
  if (shouldHideSection()) {
    return null;
  }
  
  // Otherwise, render the section
  return (
    <div className="caption">
      <React.Fragment>
        <h5 className="text-link dark:text-white/70 caption font-semibold leading-5 tracking-widest text-xs pb-1 uppercase">
          {item.heading}
        </h5>
        {item.children?.map((child, index) => (
          <React.Fragment key={child.id && index}>
            <ConditionalNavItem item={child} />
          </React.Fragment>
        ))}
      </React.Fragment>
    </div>
  );
};

export default ConditionalSidebarSection;
