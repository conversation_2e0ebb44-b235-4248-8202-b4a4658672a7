import React from 'react';
import { ChildItem, MenuItem } from './Sidebaritems';
import NavItems from './NavItems';
import { useOrganizationSettings } from '../../../context/OrganizationSettingsContext';

interface ConditionalNavItemProps {
  item: ChildItem;
}

/**
 * A component that conditionally renders a navigation item based on feature flags
 * in the organization settings.
 */
const ConditionalNavItem: React.FC<ConditionalNavItemProps> = ({ item }) => {
  const { settings } = useOrganizationSettings();

  // Check if the item should be hidden based on feature flags
  const shouldHideItem = () => {
    // Hide chat menu item if chat is disabled
    if (item.url === '/chat') {
      return settings.chat_enabled === false;
    }

    // Add more conditions here for other features

    return false;
  };

  // If the item should be hidden, don't render anything
  if (shouldHideItem()) {
    return null;
  }

  // Otherwise, render the navigation item
  return <NavItems item={item} />;
};

export default ConditionalNavItem;
