export interface ChildItem {
  id?: number | string;
  name?: string;
  icon?: any;
  children?: ChildItem[];
  item?: any;
  url?: any;
  color?: string;
}

export interface MenuItem {
  heading?: string;
  name?: string;
  icon?: any;
  id?: number;
  to?: string;
  items?: MenuItem[];
  children?: ChildItem[];
  url?: any;
}

import { uniqueId } from 'lodash';

const SidebarContent: MenuItem[] = [
  {
    heading: 'HOME',
    children: [
      {
        name: 'Dashboard',
        icon: 'solar:widget-add-line-duotone',
        id: uniqueId(),
        url: '/',
      },
    ],
  },

  // Products
  {
    heading: 'PRODUCTS',
    children: [
      {
        name: 'Products',
        icon: 'solar:box-outline',
        id: uniqueId(),
        url: '/products',
      },
      {
        name: 'Categories',
        icon: 'solar:tag-outline',
        id: uniqueId(),
        url: '/products/categories',
      },
    ],
  },

  // Inventory
  {
    heading: 'INVENTORY',
    children: [
      {
        name: 'Inventory',
        icon: 'solar:inventory-outline',
        id: uniqueId(),
        url: '/inventory',
      },
      {
        name: 'Transactions',
        icon: 'solar:transfer-vertical-outline',
        id: uniqueId(),
        url: '/inventory/transactions',
      },
      {
        name: 'Receipts',
        icon: 'solar:clipboard-list-outline',
        id: uniqueId(),
        url: '/inventory/receipts',
      },
    ],
  },

  // Sales
  {
    heading: 'SALES',
    children: [
      {
        name: 'POS Terminal',
        icon: 'solar:cart-large-outline',
        id: uniqueId(),
        url: '/sales/smart-pos',
        color: 'success',
      },
      {
        name: 'Sales Summary',
        icon: 'solar:chart-outline',
        id: uniqueId(),
        url: '/sales/summary',
      },
      {
        name: 'Sales History',
        icon: 'solar:history-outline',
        id: uniqueId(),
        url: '/sales/history',
      },
      {
        name: 'Refunds',
        icon: 'solar:restart-outline',
        id: uniqueId(),
        url: '/refunds',
      },
    ],
  },

  // Purchases
  {
    heading: 'PURCHASES',
    children: [
      {
        name: 'Purchase Requests',
        icon: 'solar:document-add-outline',
        id: uniqueId(),
        url: '/purchases/requests',
      },
      {
        name: 'Purchase Orders',
        icon: 'solar:clipboard-outline',
        id: uniqueId(),
        url: '/purchases/orders',
      },
    ],
  },

  // Payables
  {
    heading: 'PAYABLES',
    children: [
      {
        name: 'Accounts Payable',
        icon: 'solar:bill-list-outline',
        id: uniqueId(),
        url: '/payables',
      },
    ],
  },

  // Expense Management
  {
    heading: 'EXPENSES',
    children: [
      {
        name: 'Dashboard',
        icon: 'solar:chart-square-outline',
        id: uniqueId(),
        url: '/expenses/dashboard',
        color: 'primary',
      },
      {
        name: 'Quick Entry',
        icon: 'solar:add-circle-outline',
        id: uniqueId(),
        url: '/expenses/quick-entry',
        color: 'success',
      },
      {
        name: 'Expense Types',
        icon: 'solar:tag-outline',
        id: uniqueId(),
        url: '/expenses/types',
      },
      {
        name: 'Recurring Expenses',
        icon: 'solar:refresh-outline',
        id: uniqueId(),
        url: '/expenses/recurring',
      },
      {
        name: 'All Payables',
        icon: 'solar:bill-list-outline',
        id: uniqueId(),
        url: '/expenses/payables',
      },
    ],
  },

  // Contacts
  {
    heading: 'CONTACTS',
    children: [
      {
        name: 'Customers',
        icon: 'solar:user-outline',
        id: uniqueId(),
        url: '/customers',
      },
      {
        name: 'Suppliers',
        icon: 'solar:users-group-rounded-outline',
        id: uniqueId(),
        url: '/suppliers',
      },
    ],
  },

  // Employees
  {
    heading: 'EMPLOYEES',
    children: [
      {
        name: 'Employee List',
        icon: 'solar:users-group-rounded-outline',
        id: uniqueId(),
        url: '/employees',
      },
      {
        name: 'Departments',
        icon: 'solar:building-outline',
        id: uniqueId(),
        url: '/employees/departments',
      },
      {
        name: 'Job Positions',
        icon: 'solar:briefcase-outline',
        id: uniqueId(),
        url: '/employees/job-positions',
      },
      {
        name: 'Employment Types',
        icon: 'solar:document-text-outline',
        id: uniqueId(),
        url: '/employees/employment-types',
      },
    ],
  },

  // Time Tracking & Face Recognition
  {
    heading: 'TIME TRACKING',
    children: [
      {
        name: 'Time Clock',
        icon: 'solar:clock-circle-outline',
        id: uniqueId(),
        url: '/time-clock',
        color: 'success',
      },
      {
        name: 'Time Entries',
        icon: 'solar:calendar-outline',
        id: uniqueId(),
        url: '/payroll/time-entries',
      },
      {
        name: 'Add Time Entry',
        icon: 'solar:add-circle-outline',
        id: uniqueId(),
        url: '/payroll/add-time-entry',
      },
    ],
  },

  // Payroll
  {
    heading: 'PAYROLL',
    children: [
      {
        name: 'Payroll Periods',
        icon: 'solar:calendar-outline',
        id: uniqueId(),
        url: '/payroll',
      },
      {
        name: 'Employee Salaries',
        icon: 'solar:money-bag-outline',
        id: uniqueId(),
        url: '/payroll/employee-salaries',
      },
      {
        name: 'Time & Attendance',
        icon: 'solar:clock-circle-outline',
        id: uniqueId(),
        url: '/payroll/time-entries',
      },
      {
        name: 'Payroll Settings',
        icon: 'solar:settings-outline',
        id: uniqueId(),
        url: '/payroll/settings',
      },
      {
        name: 'Reports',
        icon: 'solar:document-text-outline',
        id: uniqueId(),
        url: '/payroll/reports',
      },
      {
        name: 'Analytics',
        icon: 'solar:chart-outline',
        id: uniqueId(),
        url: '/payroll/analytics',
      },
    ],
  },

  // Chat moved to header notification icon

  // Reports
  {
    heading: 'REPORTS',
    children: [
      {
        name: 'Sales Reports',
        icon: 'solar:chart-outline',
        id: uniqueId(),
        url: '/reports/sales',
      },
      {
        name: 'Inventory Reports',
        icon: 'solar:chart-2-outline',
        id: uniqueId(),
        url: '/reports/inventory',
      },
    ],
  },

  // Users
  {
    heading: 'USERS',
    children: [
      {
        name: 'User Management',
        icon: 'solar:users-group-rounded-outline',
        id: uniqueId(),
        url: '/users',
      },
      {
        name: 'Invite User',
        icon: 'solar:user-plus-outline',
        id: uniqueId(),
        url: '/users/invite',
      },
      {
        name: 'My Profile',
        icon: 'solar:user-outline',
        id: uniqueId(),
        url: '/users/profile',
      },
      {
        name: 'Change Password',
        icon: 'solar:lock-password-outline',
        id: uniqueId(),
        url: '/users/change-password',
      },
    ],
  },

  // Settings
  {
    heading: 'SETTINGS',
    children: [
      {
        name: 'Organization Settings',
        icon: 'solar:settings-outline',
        id: uniqueId(),
        url: '/organization/settings',
      },
      {
        name: 'Units of Measurement',
        icon: 'solar:ruler-outline',
        id: uniqueId(),
        url: '/settings/uom',
      },
      {
        name: 'QC Checklists',
        icon: 'solar:clipboard-check-outline',
        id: uniqueId(),
        url: '/settings/qc-checklists',
      },
      {
        name: 'Tag Management',
        icon: 'solar:tag-outline',
        id: uniqueId(),
        url: '/settings/tags',
      },
    ],
  },

  // UI Components
  {
    heading: 'UTILITIES',
    children: [
    ],
  },
];

export default SidebarContent;
