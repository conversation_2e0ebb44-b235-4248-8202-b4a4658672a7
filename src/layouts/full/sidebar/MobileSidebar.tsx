
import { Sidebar } from "flowbite-react";
import SidebarContent from "./SidebaritemsNew";
import ConditionalSidebarSection from "./ConditionalSidebarSection";
import SimpleBar from "simplebar-react";
import React from "react";
import FullLogo from "../shared/logo/FullLogo";
import 'simplebar-react/dist/simplebar.min.css';
import MobileQuickAccess from "./MobileQuickAccess";

const MobileSidebar = () => {
  return (
    <>
      <div>
        <Sidebar
          className="fixed menu-sidebar pt-0 bg-white dark:bg-darkgray transition-all"
          aria-label="Sidebar with multi-level dropdown example"
        >
          <div className="px-5 py-4 pb-7 flex items-center justify-between sidebarlogo">
            <FullLogo />
          </div>
          <MobileQuickAccess />
          <SimpleBar className="h-[calc(100vh_-_150px)]">
            <Sidebar.Items className="px-5 mt-2">
              <Sidebar.ItemGroup className="sidebar-nav hide-menu">
                {SidebarContent &&
                  SidebarContent?.map((item, index) => (
                    <ConditionalSidebarSection key={item.heading + index} item={item} />
                  ))}
              </Sidebar.ItemGroup>
            </Sidebar.Items>
          </SimpleBar>
        </Sidebar>
      </div>
    </>
  );
};

export default MobileSidebar;
