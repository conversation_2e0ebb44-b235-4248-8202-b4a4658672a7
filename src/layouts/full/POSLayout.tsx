import { FC } from 'react';
import { Outlet } from "react-router-dom";
import ScrollToTop from 'src/components/shared/ScrollToTop';
import Header from './header/Header';
import OnboardingCheck from 'src/components/OnboardingCheck';
import { Button } from 'flowbite-react';
import { HiOutlineArrowLeft, HiOutlineTag, HiOutlineDocumentDownload, HiOutlineQuestionMarkCircle, HiOutlineViewGrid, HiOutlineRefresh, HiOutlineClock } from 'react-icons/hi';
import { useNavigate } from 'react-router-dom';
import { useOrganization } from '../../context/OrganizationContext';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';

const POSLayout: FC = () => {
  const navigate = useNavigate();
  const { currentMember } = useOrganization();
  const { settings } = useOrganizationSettings();
  const isOwner = currentMember?.role === 'owner';

  const handleBack = () => {
    navigate('/');
  };

  const handleBusinessTypeSettings = () => {
    window.open('/organization/settings', '_blank');
  };

  const handleViewSales = () => {
    // Dispatch custom event to trigger sales modal in POS component
    window.dispatchEvent(new CustomEvent('pos-show-sales-modal'));
  };

  const handleShowManual = () => {
    // Dispatch custom event to trigger manual modal in POS component
    window.dispatchEvent(new CustomEvent('pos-show-manual-modal'));
  };

  const handleShowShortcuts = () => {
    // Dispatch custom event to trigger shortcuts modal in POS component
    window.dispatchEvent(new CustomEvent('pos-show-shortcuts-modal'));
  };

  const handleShowRefund = () => {
    // Dispatch custom event to trigger refund modal in POS component
    window.dispatchEvent(new CustomEvent('pos-show-refund-modal'));
  };

  const handleShowTimeClock = () => {
    // Dispatch custom event to trigger time clock modal in POS component
    window.dispatchEvent(new CustomEvent('pos-show-timeclock-modal'));
  };

  return (
    <>
      <OnboardingCheck />
      <div className="flex flex-col w-full h-screen bg-gray-50 dark:bg-darkgray">
        {/* Simplified Header for POS */}
        <div className="bg-white dark:bg-darkgray shadow-sm py-2 px-4 flex items-center justify-between flex-shrink-0">
          <div className="flex items-center">
            <Button
              color="light"
              size="sm"
              onClick={handleBack}
              className="mr-4"
            >
              <HiOutlineArrowLeft className="mr-2 h-5 w-5" />
              Back to Dashboard
            </Button>
            <h1 className="text-xl font-bold">POS Terminal</h1>
          </div>

          {/* Right side buttons */}
          <div className="flex items-center gap-2">
            {/* Business Type */}
            <div className="flex items-center mr-4">
              <span className="text-sm font-medium mr-2">Business Type:</span>
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full capitalize">
                {settings?.business_type || 'Retail'}
              </span>
              {isOwner && (
                <Button
                  size="xs"
                  color="light"
                  pill
                  className="ml-2"
                  onClick={handleBusinessTypeSettings}
                  title="Change in Organization Settings"
                >
                  <HiOutlineTag className="h-3 w-3" />
                </Button>
              )}
            </div>

            {/* Sales Button */}
            <Button
              size="sm"
              color="light"
              onClick={handleViewSales}
              title="View Sales (F8)"
              className="flex items-center gap-1"
            >
              <HiOutlineDocumentDownload className="h-4 w-4" />
              <span>Sales</span>
            </Button>

            {/* Refund Button */}
            <Button
              size="sm"
              color="warning"
              onClick={handleShowRefund}
              title="Process Refund (F5)"
              className="flex items-center gap-1"
            >
              <HiOutlineRefresh className="h-4 w-4" />
              <span>Refund</span>
            </Button>

            {/* Time Clock Button */}
            <Button
              size="sm"
              color="primary"
              onClick={handleShowTimeClock}
              title="Time Clock (F6)"
              className="flex items-center gap-1"
            >
              <HiOutlineClock className="h-4 w-4" />
              <span>Time Clock</span>
            </Button>

            {/* Manual Button */}
            <Button
              size="sm"
              color="light"
              onClick={handleShowManual}
              title="User Manual (F7)"
              className="flex items-center gap-1"
            >
              <HiOutlineQuestionMarkCircle className="h-4 w-4" />
              <span>Manual</span>
            </Button>

            {/* Shortcuts Button */}
            <Button
              size="sm"
              color="light"
              onClick={handleShowShortcuts}
              title="Keyboard Shortcuts"
              className="flex items-center gap-1"
            >
              <HiOutlineViewGrid className="h-4 w-4" />
              <span>Shortcuts</span>
            </Button>
          </div>
        </div>

        {/* Full-width content area */}
        <div className="flex-1 w-full min-h-0">
          <ScrollToTop>
            <Outlet />
          </ScrollToTop>
        </div>
      </div>
    </>
  );
};

export default POSLayout;
