import React from 'react';
import { Outlet, Navigate } from 'react-router-dom';
import { ChatProvider } from '../../context/ChatContext';
import { useOrganizationSettings } from '../../context/OrganizationSettingsContext';

/**
 * Chat Layout - Only loads ChatProvider when on chat routes
 * This prevents unnecessary API calls when not using chat features
 * Note: This layout doesn't include Header/Sidebar as they're already provided by FullLayout
 */
const ChatLayout: React.FC = () => {
  const { settings } = useOrganizationSettings();

  // Check if chat is enabled in the organization settings
  const isChatEnabled = settings.chat_enabled !== false; // Default to true if not set

  // If chat is disabled, redirect to dashboard
  if (!isChatEnabled) {
    return <Navigate to="/" replace />;
  }

  // If chat is enabled, wrap with ChatProvider
  return (
    <ChatProvider>
      <Outlet />
    </ChatProvider>
  );
};

export default ChatLayout;
