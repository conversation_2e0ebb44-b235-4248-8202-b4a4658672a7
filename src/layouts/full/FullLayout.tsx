import { FC } from 'react';
import { Outlet } from "react-router-dom";
import ScrollToTop from 'src/components/shared/ScrollToTop';
import Sidebar from './sidebar/Sidebar';
import Header from './header/Header';
import OnboardingCheck from 'src/components/OnboardingCheck';
import InvitationNotification from 'src/components/notifications/InvitationNotification';



const FullLayout: FC = () => {
  return (
      <>
    <OnboardingCheck />
    <div className="flex w-full min-h-screen dark:bg-darkgray">
      <div className="page-wrapper flex w-full  ">
        {/* Header/sidebar */}
            <Sidebar />
        <div className="page-wrapper-sub flex flex-col w-full dark:bg-darkgray">
          {/* Top Header  */}
           <Header/>

          <div
            className={`bg-lightgray dark:bg-dark  h-full rounded-bb`}
          >
            {/* Body Content  */}
            <div
              className={`w-full`}
            >
              <ScrollToTop>
                <div className="container py-30">
                  <InvitationNotification />
                  <Outlet/>
                </div>
              </ScrollToTop>
            </div>
          </div>
        </div>
      </div>
    </div>
      </>
  );
};

export default FullLayout;
