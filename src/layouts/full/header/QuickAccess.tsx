import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Tooltip } from 'flowbite-react';
import { Icon } from '@iconify/react';

interface QuickAction {
  name: string;
  icon: string;
  path: string;
  color: string;
}

const QuickAccess: React.FC = () => {
  const navigate = useNavigate();

  const quickActions: QuickAction[] = [
    {
      name: 'New Sale',
      icon: 'solar:cart-plus-bold',
      path: '/sales/smart-pos',
      color: 'green'
	}
  ];

  return (
    <div className="flex items-center gap-2">
      {quickActions.map((action) => (
        <Button
          key={action.path}
          size="sm"
          color={action.color as any}
          onClick={() => navigate(action.path)}
          className="flex items-center gap-1 px-2 py-1"
        >
          <Icon icon={action.icon} className="h-4 w-4" />
          <span className="text-xs hidden lg:inline">{action.name}</span>
        </Button>
      ))}
    </div>
  );
};

export default QuickAccess;
