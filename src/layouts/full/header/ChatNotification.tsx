import React from 'react';
import { Badge } from "flowbite-react";
import { Link } from "react-router";
import { useChatNotifications } from '../../../context/ChatNotificationContext';
import { useOrganizationSettings } from '../../../context/OrganizationSettingsContext';
import ChatIcon from '../../../assets/images/svgs/chat-icon.svg';

/**
 * Chat notification icon with badge for the header
 * Shows unread message count and links to chat page
 */
const ChatNotification: React.FC = () => {
  const { totalUnreadCount, hasNewMessages } = useChatNotifications();
  const { settings } = useOrganizationSettings();



  // Don't show if chat is disabled
  if (settings.chat_enabled === false) {
    return null;
  }

  return (
    <Link
      to="/chat"
      className="relative group/chat"
      aria-label={`Chat ${totalUnreadCount > 0 ? `(${totalUnreadCount} unread)` : ''}`}
    >
      <span
        className={`h-10 w-10 text-black dark:text-white text-opacity-65 hover:text-primary group-hover/chat:bg-lightprimary group-hover/chat:text-primary hover:bg-lightprimary rounded-full flex justify-center items-center cursor-pointer relative transition-all duration-200 ${
          hasNewMessages ? 'animate-pulse' : ''
        }`}
      >
        <img
          src={ChatIcon}
          alt="Chat"
          className={`w-5 h-5 ${totalUnreadCount > 0 ? 'filter brightness-0 saturate-100' : ''}`}
          style={{
            filter: totalUnreadCount > 0
              ? 'brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)'
              : undefined
          }}
        />

        {/* Unread count badge */}
        {totalUnreadCount > 0 && (
          <div className={`absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center font-medium min-w-[20px] ${
              hasNewMessages ? 'animate-bounce' : ''
            }`}>
            {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
          </div>
        )}

        {/* New message indicator (when no specific count) */}
        {totalUnreadCount === 0 && hasNewMessages && (
          <div className="h-3 w-3 rounded-full absolute -top-1 -right-1 bg-red-500 animate-ping" />
        )}
      </span>
    </Link>
  );
};

export default ChatNotification;
