import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { Button } from 'flowbite-react';

interface FeatureGroup {
  name: string;
  icon: string;
  features: Feature[];
}

interface Feature {
  name: string;
  icon: string;
  path: string;
  description?: string;
}

const FeatureNavigation: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Feature groups
  const featureGroups: FeatureGroup[] = [
    {
      name: 'Products',
      icon: 'solar:box-outline',
      features: [
        {
          name: 'Product List',
          icon: 'solar:list-outline',
          path: '/products',
          description: 'View and manage all products'
        },
        {
          name: 'Categories',
          icon: 'solar:folder-outline',
          path: '/products/categories',
          description: 'Manage product categories'
        }
      ]
    },
    {
      name: 'Inventory',
      icon: 'solar:inventory-outline',
      features: [
        {
          name: 'Inventory List',
          icon: 'solar:list-outline',
          path: '/inventory',
          description: 'View and manage inventory'
        },
        {
          name: 'Transactions',
          icon: 'solar:transfer-vertical-outline',
          path: '/inventory/transactions',
          description: 'View inventory transactions'
        },
        {
          name: 'Receipts',
          icon: 'solar:clipboard-list-outline',
          path: '/inventory/receipts',
          description: 'Manage inventory receipts'
        }
      ]
    },
    {
      name: 'Customers',
      icon: 'solar:user-outline',
      features: [
        {
          name: 'Customer List',
          icon: 'solar:users-group-outline',
          path: '/customers',
          description: 'View and manage customers'
        }
      ]
    },
    {
      name: 'Suppliers',
      icon: 'solar:factory-outline',
      features: [
        {
          name: 'Supplier List',
          icon: 'solar:users-group-outline',
          path: '/suppliers',
          description: 'View and manage suppliers'
        }
      ]
    },
    {
      name: 'Sales',
      icon: 'solar:cart-outline',
      features: [
        {
          name: 'POS Terminal',
          icon: 'solar:cart-large-outline',
          path: '/sales/smart-pos',
          description: 'Use the POS terminal'
        },
        {
          name: 'Sales History',
          icon: 'solar:history-outline',
          path: '/sales/history',
          description: 'View sales history'
        }
      ]
    },
    {
      name: 'Purchases',
      icon: 'solar:document-add-outline',
      features: [
        {
          name: 'Purchase Requests',
          icon: 'solar:document-add-outline',
          path: '/purchases/requests',
          description: 'Manage purchase requests'
        },
        {
          name: 'Purchase Orders',
          icon: 'solar:clipboard-outline',
          path: '/purchases/orders',
          description: 'Manage purchase orders'
        }
      ]
    },
    {
      name: 'Reports',
      icon: 'solar:chart-outline',
      features: [
        {
          name: 'Sales Reports',
          icon: 'solar:chart-outline',
          path: '/reports/sales',
          description: 'View sales reports'
        },
        {
          name: 'Inventory Reports',
          icon: 'solar:chart-2-outline',
          path: '/reports/inventory',
          description: 'View inventory reports'
        }
      ]
    },
    {
      name: 'Settings',
      icon: 'solar:settings-outline',
      features: [
        {
          name: 'Organization Settings',
          icon: 'solar:building-outline',
          path: '/organization/settings',
          description: 'Manage organization settings'
        },
        {
          name: 'Tag Management',
          icon: 'solar:tag-outline',
          path: '/settings/tags',
          description: 'Manage tags across the system'
        },
        {
          name: 'User Management',
          icon: 'solar:users-group-rounded-outline',
          path: '/users',
          description: 'Manage users and permissions'
        }
      ]
    }
  ];

  const handleNavigate = (path: string) => {
    navigate(path);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        color="light"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2"
      >
        <Icon icon="solar:menu-dots-outline" className="h-5 w-5" />
        <span>Features</span>
      </Button>

      {isOpen && (
        <div className="absolute z-50 mt-2 w-screen max-w-4xl bg-white rounded-lg shadow-lg dark:bg-gray-800 border border-gray-200 dark:border-gray-700 left-0 sm:left-auto sm:right-0">
          <div className="p-4 max-h-[80vh] overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {featureGroups.map((group) => (
                <div key={group.name} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Icon icon={group.icon} className="h-5 w-5 text-blue-500" />
                    <h3 className="font-semibold text-gray-900 dark:text-white">{group.name}</h3>
                  </div>
                  <ul className="space-y-2">
                    {group.features.map((feature) => (
                      <li key={feature.path}>
                        <button
                          onClick={() => handleNavigate(feature.path)}
                          className="w-full text-left flex items-start p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors"
                        >
                          <Icon icon={feature.icon} className="h-5 w-5 mt-0.5 mr-2 text-gray-500 dark:text-gray-400" />
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">{feature.name}</div>
                            {feature.description && (
                              <div className="text-xs text-gray-500 dark:text-gray-400">{feature.description}</div>
                            )}
                          </div>
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FeatureNavigation;
