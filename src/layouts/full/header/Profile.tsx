
import { useState, useEffect } from "react";
import { Button, Dropdown } from "flowbite-react";
import { Icon } from "@iconify/react";
import user1 from "/src/assets/images/profile/user-1.jpg";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../../../context/AuthContext";
import { supabase } from "../../../lib/supabase";

const Profile = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();

  // Get user profile data
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    avatarUrl: ''
  });

  useEffect(() => {
    const fetchProfileData = async () => {
      if (!user) return;

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('first_name, last_name, avatar_url')
          .eq('id', user.id)
          .maybeSingle();

        if (error) {
          console.error('Error fetching profile:', error);
          return;
        }

        if (data) {
          setProfileData({
            firstName: data.first_name || '',
            lastName: data.last_name || '',
            avatarUrl: data.avatar_url || ''
          });
        }
      } catch (err) {
        console.error('Error fetching profile data:', err);
      }
    };

    fetchProfileData();
  }, [user]);

  const handleLogout = async () => {
    try {
      await signOut();
      navigate('/auth/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleProfileClick = () => {
    navigate('/users/profile');
  };

  const handleAccountClick = () => {
    navigate('/users/change-password');
  };

  return (
    <div className="relative group/menu">
      <Dropdown
        label=""
        className="rounded-sm w-44"
        dismissOnClick={false}
        renderTrigger={() => (
          <span className="h-10 w-10 hover:text-primary hover:bg-lightprimary rounded-full flex justify-center items-center cursor-pointer group-hover/menu:bg-lightprimary group-hover/menu:text-primary">
            {profileData.avatarUrl ? (
              <img
                src={profileData.avatarUrl}
                alt={`${profileData.firstName} ${profileData.lastName}`}
                height="35"
                width="35"
                className="rounded-full object-cover"
              />
            ) : (
              <img
                src={user1}
                alt="User"
                height="35"
                width="35"
                className="rounded-full"
              />
            )}
          </span>
        )}
      >
        <Dropdown.Item
          onClick={handleProfileClick}
          className="px-3 py-3 flex items-center bg-hover group/link w-full gap-3 text-dark"
        >
          <Icon icon="solar:user-circle-outline" height={20} />
          My Profile
        </Dropdown.Item>
        <Dropdown.Item
          onClick={handleAccountClick}
          className="px-3 py-3 flex items-center bg-hover group/link w-full gap-3 text-dark"
        >
          <Icon icon="solar:letter-linear" height={20} />
          Change Password
        </Dropdown.Item>
        <Dropdown.Item
          as={Link}
          to="/organization/settings"
          className="px-3 py-3 flex items-center bg-hover group/link w-full gap-3 text-dark"
        >
          <Icon icon="solar:checklist-linear" height={20} />
          Organization Settings
        </Dropdown.Item>
        <div className="p-3 pt-0">
          <Button
            size={'sm'}
            onClick={handleLogout}
            className="mt-2 border border-primary text-primary bg-transparent hover:bg-lightprimary outline-none focus:outline-none w-full"
          >
            Logout
          </Button>
        </div>
      </Dropdown>
    </div>
  );
};

export default Profile;
