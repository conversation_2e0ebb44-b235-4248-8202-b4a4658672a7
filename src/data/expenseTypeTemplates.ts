export interface ExpenseTypeTemplate {
  code: string;
  name: string;
  category: string;
  description: string;
  is_recurring: boolean;
  requires_approval: boolean;
  approval_limit: number;
  icon: string;
  color: string;
}

export interface BusinessTypeTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  expense_types: ExpenseTypeTemplate[];
}

const COMMON_EXPENSES: ExpenseTypeTemplate[] = [
  { code: 'RENT', name: 'Rent & Utilities', category: 'operational', description: 'Monthly rent, electricity, water, internet', is_recurring: true, requires_approval: true, approval_limit: 50000, icon: '🏢', color: 'blue' },
  { code: 'SALARY', name: 'Salaries & Wages', category: 'operational', description: 'Employee salaries, wages, and benefits', is_recurring: true, requires_approval: true, approval_limit: 100000, icon: '👥', color: 'green' },
  { code: 'OFFICE', name: 'Office Supplies', category: 'office_supplies', description: 'Stationery, printing, office equipment', is_recurring: false, requires_approval: false, approval_limit: 5000, icon: '📝', color: 'gray' },
  { code: 'TRANSPORT', name: 'Transportation', category: 'travel', description: 'Fuel, vehicle maintenance, delivery costs', is_recurring: false, requires_approval: false, approval_limit: 10000, icon: '🚗', color: 'orange' },
  { code: 'MARKETING', name: 'Marketing & Advertising', category: 'operational', description: 'Social media ads, flyers, promotional materials', is_recurring: false, requires_approval: true, approval_limit: 25000, icon: '📢', color: 'purple' },
  { code: 'INSURANCE', name: 'Insurance', category: 'operational', description: 'Business insurance, health insurance', is_recurring: true, requires_approval: true, approval_limit: 30000, icon: '🛡️', color: 'indigo' },
  { code: 'BANK', name: 'Bank Charges', category: 'financial', description: 'Bank fees, transaction charges, loan interest', is_recurring: false, requires_approval: false, approval_limit: 2000, icon: '🏦', color: 'red' },
  { code: 'GOVT', name: 'Government Fees', category: 'administrative', description: 'Business permits, licenses, taxes', is_recurring: false, requires_approval: true, approval_limit: 20000, icon: '🏛️', color: 'yellow' }
];

export const BUSINESS_TYPE_TEMPLATES: BusinessTypeTemplate[] = [
  {
    id: 'sari_sari_store',
    name: 'Sari-Sari Store',
    description: 'Perfect for neighborhood convenience stores',
    icon: '🏪',
    color: 'green',
    expense_types: [
      ...COMMON_EXPENSES,
      { code: 'INVENTORY', name: 'Inventory Purchase', category: 'operational', description: 'Buying products to sell (snacks, drinks, household items)', is_recurring: true, requires_approval: true, approval_limit: 50000, icon: '📦', color: 'blue' },
      { code: 'LOAD', name: 'Load & E-Money', category: 'operational', description: 'Mobile load, GCash, PayMaya top-ups for resale', is_recurring: true, requires_approval: false, approval_limit: 20000, icon: '📱', color: 'cyan' }
    ]
  },
  {
    id: 'retail_store',
    name: 'Retail Store',
    description: 'For clothing, electronics, and general retail',
    icon: '🛍️',
    color: 'purple',
    expense_types: [
      ...COMMON_EXPENSES,
      { code: 'MERCHANDISE', name: 'Merchandise Purchase', category: 'operational', description: 'Buying products for resale', is_recurring: true, requires_approval: true, approval_limit: 200000, icon: '👕', color: 'blue' },
      { code: 'SECURITY', name: 'Security Services', category: 'professional_services', description: 'Security guards, CCTV, alarm systems', is_recurring: true, requires_approval: true, approval_limit: 25000, icon: '🔒', color: 'red' }
    ]
  },
  {
    id: 'restaurant',
    name: 'Restaurant & Food Service',
    description: 'For restaurants, cafes, and food businesses',
    icon: '🍽️',
    color: 'orange',
    expense_types: [
      ...COMMON_EXPENSES,
      { code: 'INGREDIENTS', name: 'Food Ingredients', category: 'operational', description: 'Raw materials, ingredients, beverages', is_recurring: true, requires_approval: true, approval_limit: 100000, icon: '🥘', color: 'green' },
      { code: 'KITCHEN', name: 'Kitchen Equipment', category: 'operational', description: 'Cooking equipment, utensils, maintenance', is_recurring: false, requires_approval: true, approval_limit: 75000, icon: '🍳', color: 'red' }
    ]
  },
  {
    id: 'service_business',
    name: 'Service Business',
    description: 'For salons, repair shops, consulting',
    icon: '🔧',
    color: 'blue',
    expense_types: [
      ...COMMON_EXPENSES,
      { code: 'TOOLS', name: 'Tools & Equipment', category: 'operational', description: 'Professional tools, equipment, maintenance', is_recurring: false, requires_approval: true, approval_limit: 50000, icon: '🔨', color: 'gray' }
    ]
  },
  {
    id: 'accounting_services',
    name: 'Accounting & Professional Services',
    description: 'For accounting firms, consultants, and professional services',
    icon: '📊',
    color: 'indigo',
    expense_types: [
      ...COMMON_EXPENSES,
      { code: 'SOFTWARE', name: 'Accounting Software', category: 'operational', description: 'QuickBooks, Xero, accounting software subscriptions', is_recurring: true, requires_approval: true, approval_limit: 20000, icon: '💻', color: 'blue' },
      { code: 'LICENSES', name: 'Professional Licenses', category: 'professional_services', description: 'CPA licenses, professional certifications', is_recurring: true, requires_approval: true, approval_limit: 15000, icon: '📜', color: 'purple' }
    ]
  },
  {
    id: 'online_business',
    name: 'Online Business',
    description: 'For e-commerce and digital businesses',
    icon: '💻',
    color: 'cyan',
    expense_types: [
      ...COMMON_EXPENSES,
      { code: 'PLATFORM', name: 'Platform Fees', category: 'operational', description: 'Shopee, Lazada, Facebook Marketplace fees', is_recurring: true, requires_approval: false, approval_limit: 10000, icon: '🛒', color: 'orange' },
      { code: 'SHIPPING', name: 'Shipping & Logistics', category: 'operational', description: 'Courier fees, packaging, logistics', is_recurring: true, requires_approval: false, approval_limit: 20000, icon: '📦', color: 'blue' }
    ]
  }
];
