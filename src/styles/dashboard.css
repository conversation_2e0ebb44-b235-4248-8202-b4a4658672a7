/* Dashboard Tab Styling - Smooth transitions without colorful borders */
.dashboard-tabs .flowbite-tabs-tablist {
  border-bottom: 1px solid #e5e7eb;
}

.dashboard-tabs .flowbite-tabs-tabpanel {
  padding: 0;
}

.dashboard-tabs button[role="tab"] {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  color: #6b7280 !important;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 0;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease-in-out;
  position: relative;
}

.dashboard-tabs button[role="tab"]:hover {
  color: #374151 !important;
  background: #f9fafb !important;
  border-bottom-color: #d1d5db !important;
}

.dashboard-tabs button[role="tab"][aria-selected="true"] {
  color: #1f2937 !important;
  background: transparent !important;
  border-bottom-color: #3b82f6 !important;
  font-weight: 600;
}

.dashboard-tabs button[role="tab"]:focus {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

.dashboard-tabs button[role="tab"]:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Remove any default focus styles from Flowbite */
.dashboard-tabs .flowbite-tabs-tablist button:focus {
  box-shadow: none !important;
  outline: none !important;
}

/* Smooth content transitions */
.dashboard-tabs .flowbite-tabs-tabpanel {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
