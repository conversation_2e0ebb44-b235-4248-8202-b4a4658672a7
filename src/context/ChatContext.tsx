import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback, useRef } from 'react';
import { useAuth } from './AuthContext';
import { useOrganization } from './OrganizationContext';
import {
  getUserConversations,
  getConversation,
  getConversationMessages,
  sendMessage,
  createOneOnOneConversation,
  createGroupConversation,
  markMessagesAsRead,
  subscribeToConversation,
  subscribeToConversations,
  deleteMessage,
  editMessage,
  deleteConversation,
  leaveConversation,
  ConversationWithParticipants,
  MessageWithSender
} from '../services/chat';
import { uploadFile } from '../services/fileUpload';
import { toast } from 'react-hot-toast';

interface ChatContextType {
  conversations: ConversationWithParticipants[];
  currentConversation: ConversationWithParticipants | null;
  messages: MessageWithSender[];
  loadingConversations: boolean;
  loadingMessages: boolean;
  error: string | null;
  uploadingFile: boolean;
  searchQuery: string;
  setCurrentConversationId: (id: string | null) => void;
  refreshConversations: (showLoading?: boolean) => Promise<void>;
  loadMoreMessages: (showLoading?: boolean) => Promise<void>;
  sendNewMessage: (content: string, file?: File) => Promise<void>;
  startOneOnOneChat: (userId: string) => Promise<string>;
  startGroupChat: (organizationId: string, name: string, participantIds: string[]) => Promise<string>;
  markAsRead: (messageIds: string[]) => Promise<void>;
  deleteUserMessage: (messageId: string) => Promise<void>;
  editUserMessage: (messageId: string, newContent: string) => Promise<void>;
  deleteConversation: (conversationId: string) => Promise<boolean>;
  leaveConversation: (conversationId: string) => Promise<boolean>;
  searchMessages: (query: string) => Promise<void>;
  clearSearch: () => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const useChatContext = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
};

export const ChatProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const [conversations, setConversations] = useState<ConversationWithParticipants[]>([]);
  const [currentConversation, setCurrentConversation] = useState<ConversationWithParticipants | null>(null);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [messages, setMessages] = useState<MessageWithSender[]>([]);
  const [loadingConversations, setLoadingConversations] = useState<boolean>(false);
  const [loadingMessages, setLoadingMessages] = useState<boolean>(false);
  const [uploadingFile, setUploadingFile] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [messagesPage, setMessagesPage] = useState<number>(0);
  const [hasMoreMessages, setHasMoreMessages] = useState<boolean>(true);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const messagesPerPage = 20;

  // Fetch conversations and set up realtime subscription when user or organization changes
  useEffect(() => {
    if (user && currentOrganization) {
      console.log(`Setting up chat for organization ${currentOrganization.id}`);

      // Initial fetch of conversations - only once
      fetchConversations(true);

      // Set up realtime subscription for conversation updates
      const unsubscribe = subscribeToConversations(
        currentOrganization.id,
        (updatedConversation) => {
          console.log('Received conversation update via realtime:', updatedConversation);

          // Update the conversations list with the new or updated conversation
          setConversations(prevConversations => {
            // Check if the conversation already exists
            const existingIndex = prevConversations.findIndex(c => c.id === updatedConversation.id);

            if (existingIndex !== -1) {
              // Update existing conversation without fetching additional data
              const updatedConversations = [...prevConversations];
              updatedConversations[existingIndex] = {
                ...updatedConversations[existingIndex],
                ...updatedConversation,
              };

              // Sort by updated_at
              return updatedConversations.sort((a, b) =>
                new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
              );
            } else {
              // Add new conversation directly - avoid additional API calls
              const newConversations = [...prevConversations, updatedConversation as ConversationWithParticipants];
              return newConversations.sort((a, b) =>
                new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
              );
            }
          });
        },
        (deletedConversationId) => {
          console.log('Received conversation deletion via realtime:', deletedConversationId);

          // Remove the deleted conversation from the list
          setConversations(prevConversations =>
            prevConversations.filter(c => c.id !== deletedConversationId)
          );

          // If this was the current conversation, clear it
          if (currentConversationId === deletedConversationId) {
            setCurrentConversationId(null);
          }
        }
      );

      // Cleanup function
      return () => {
        unsubscribe();
      };
    } else {
      setConversations([]);
      setCurrentConversation(null);
      setMessages([]);
    }
  }, [user?.id, currentOrganization?.id]); // Only depend on IDs, not the full objects

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
      if (realtimeUpdateTimeoutRef.current) {
        clearTimeout(realtimeUpdateTimeoutRef.current);
      }
    };
  }, []);

  // Fetch current conversation and messages when currentConversationId changes
  useEffect(() => {
    if (currentConversationId) {
      fetchCurrentConversation();
      // Show loading indicator for initial message load
      fetchMessages(true, true);
    } else {
      setCurrentConversation(null);
      setMessages([]);
    }
  }, [currentConversationId]);

  // Subscribe to new messages when current conversation changes
  useEffect(() => {
    if (!currentConversationId) return;

    console.log(`Setting up realtime subscription for conversation ${currentConversationId}`);

    const unsubscribe = subscribeToConversation(
      currentConversationId,
      (message) => {


        // Process the message based on whether it's new or updated
        setMessages(prevMessages => {
          // Check if message already exists (for updates or duplicates)
          const existingMessageIndex = prevMessages.findIndex(msg => msg.id === message.id);

          // Try to get sender info from existing messages to avoid API calls
          let senderInfo = null;
          const existingSenderMessage = prevMessages.find(msg => msg.sender_id === message.sender_id);
          if (existingSenderMessage?.sender) {
            senderInfo = existingSenderMessage.sender;
          }

          // Get sender details for display
          const enrichedMessage = {
            ...message,
            sender: senderInfo || null
          } as MessageWithSender;

          // If message exists, update it
          if (existingMessageIndex !== -1) {
            // Replace the existing message with the updated one
            const updatedMessages = [...prevMessages];
            updatedMessages[existingMessageIndex] = enrichedMessage;
            return updatedMessages;
          }

          // Only add new messages from other users via realtime
          // Messages from current user are handled by sendNewMessage function
          if (message.sender_id === user?.id) {
            // This is our own message, it should already be handled by sendNewMessage
            // Don't add it again to prevent duplicates
            return prevMessages;
          }

          // If it's a new message from someone else, add it to the end (chronological order)
          return [...prevMessages, enrichedMessage];
        });

        // Don't auto-mark messages as read from realtime subscription
        // Messages should only be marked as read when user actively views them

        // Update the conversation list to show the latest message (optimized)
        setConversations(prevConversations => {
          return prevConversations.map(conv => {
            if (conv.id === currentConversationId) {
              return {
                ...conv,
                last_message: message,
                updated_at: message.created_at
              };
            }
            return conv;
          }).sort((a, b) => {
            return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
          });
        });
      }
    );

    return () => {
      console.log(`Cleaning up realtime subscription for conversation ${currentConversationId}`);
      unsubscribe();
    };
  }, [currentConversationId, user?.id]);

  // Use refs to track the last fetch time and debounce realtime updates
  const lastFetchTimeRef = useRef<number>(0);
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const realtimeUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const fetchConversations = useCallback(async (showLoading: boolean = true) => {
    if (!user || !currentOrganization) return;

    // Simple debouncing - prevent fetching too frequently (at least 1 second between fetches)
    const now = Date.now();
    if (now - lastFetchTimeRef.current < 1000) {
      console.log('Skipping fetch - too frequent');
      return;
    }

    // Update the last fetch time
    lastFetchTimeRef.current = now;

    // Only show loading indicator if explicitly requested
    if (showLoading) {
      setLoadingConversations(true);
    }
    setError(null);

    try {
      const data = await getUserConversations(currentOrganization.id);
      setConversations(data);
    } catch (err: any) {
      console.error('Error fetching conversations:', err);
      setError(err.message || 'Failed to load conversations');
    } finally {
      // Only hide loading indicator if we showed it
      if (showLoading) {
        setLoadingConversations(false);
      }
    }
  }, []); // Remove dependencies to prevent infinite loops

  const fetchCurrentConversation = async () => {
    if (!currentConversationId || !currentOrganization) return;

    try {
      const data = await getConversation(currentConversationId, currentOrganization.id);
      setCurrentConversation(data);
    } catch (err: any) {
      console.error('Error fetching current conversation:', err);
      setError(err.message || 'Failed to load conversation details');
    }
  };

  const fetchMessages = async (reset: boolean = false, showLoading: boolean = true) => {
    if (!currentConversationId) return;

    // Only show loading indicator if explicitly requested
    if (showLoading) {
      setLoadingMessages(true);
    }
    setError(null);

    try {
      const page = reset ? 0 : messagesPage;
      const data = await getConversationMessages(currentConversationId, {
        limit: messagesPerPage,
        offset: page * messagesPerPage,
        searchQuery: searchQuery,
        organizationId: currentOrganization?.id
      });



      if (reset) {
        // For initial load, reverse the data to show chronological order (oldest to newest)
        setMessages(data.reverse());
        setMessagesPage(0);
      } else {
        // For pagination (load more), prepend older messages to the beginning
        setMessages(prevMessages => [...data.reverse(), ...prevMessages]);
      }

      setHasMoreMessages(data.length === messagesPerPage);

      if (reset) {
        setMessagesPage(1);
      } else {
        setMessagesPage(page + 1);
      }

      // Only mark messages as read when explicitly requested (not on initial fetch)
      // This prevents auto-marking messages as read when just loading the conversation
    } catch (err: any) {
      console.error('Error fetching messages:', err);
      setError(err.message || 'Failed to load messages');
    } finally {
      // Only hide loading indicator if we showed it
      if (showLoading) {
        setLoadingMessages(false);
      }
    }
  };

  const refreshConversations = useCallback(async (showLoading: boolean = true) => {
    await fetchConversations(showLoading);
    if (currentConversationId) {
      await fetchCurrentConversation();
    }
  }, [fetchConversations, currentConversationId]);

  const loadMoreMessages = async (showLoading: boolean = true) => {
    if (loadingMessages || !hasMoreMessages) return;
    await fetchMessages(false, showLoading);
  };

  const sendNewMessage = async (content: string, file?: File) => {
    if (!currentConversationId || (!content.trim() && !file)) return;
    if (!user?.id) return;

    try {
      let attachment;

      // Handle file upload if provided
      if (file) {
        setUploadingFile(true);
        const uploadResult = await uploadFile(file, user.id);
        setUploadingFile(false);

        if (!uploadResult) {
          throw new Error('Failed to upload file');
        }

        attachment = {
          type: uploadResult.fileType,
          url: uploadResult.url,
          name: uploadResult.fileName,
          size: uploadResult.fileSize
        };
      }

      // Create message content
      const messageContent = content.trim() || (file ? `Sent ${file.name}` : '');

      // Create a temporary message to show immediately in the UI
      const tempId = `temp-${Date.now()}`;
      const tempMessage: MessageWithSender = {
        id: tempId,
        conversation_id: currentConversationId,
        organization_id: currentOrganization?.id || '',
        sender_id: user.id,
        content: messageContent,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_read: true,
        sender: {
          id: user.id,
          first_name: user.user_metadata?.first_name || '',
          last_name: user.user_metadata?.last_name || '',
          avatar_url: user.user_metadata?.avatar_url || null
        },
        attachment_type: attachment?.type,
        attachment_url: attachment?.url,
        attachment_name: attachment?.name,
        attachment_size: attachment?.size
      };

      // Add the temporary message to the UI immediately
      setMessages(prevMessages => [...prevMessages, tempMessage]);

      // Send the message to the server
      const sentMessage = await sendMessage(
        currentConversationId,
        messageContent,
        attachment
      );

      // Replace the temporary message with the real one from the server
      if (sentMessage) {
        setMessages(prevMessages =>
          prevMessages.map(msg =>
            msg.id === tempId ? {
              ...sentMessage,
              sender: tempMessage.sender
            } as MessageWithSender : msg
          )
        );
      }

      // Note: The realtime subscription will NOT add this message again
      // because it checks for existing messages by ID

      // The subscription will handle any updates from other clients
    } catch (err: any) {
      console.error('Error sending message:', err);
      setError(err.message || 'Failed to send message');
      toast.error(err.message || 'Failed to send message');
    } finally {
      setUploadingFile(false);
    }
  };

  const startOneOnOneChat = async (userId: string): Promise<string> => {
    if (!currentOrganization) {
      throw new Error('No organization selected');
    }

    try {
      const conversation = await createOneOnOneConversation(
        currentOrganization.id,
        userId
      );

      // Refresh conversations to include the new one
      await refreshConversations();

      return conversation.id;
    } catch (err: any) {
      console.error('Error starting one-on-one chat:', err);
      setError(err.message || 'Failed to start chat');
      throw err;
    }
  };

  const startGroupChat = async (organizationId: string, name: string, participantIds: string[]): Promise<string> => {
    if (!organizationId) {
      throw new Error('Organization ID is required');
    }

    try {
      const conversation = await createGroupConversation(
        organizationId,
        name,
        participantIds
      );

      // Refresh conversations to include the new one
      await refreshConversations();

      return conversation.id;
    } catch (err: any) {
      console.error('Error creating group chat:', err);
      setError(err.message || 'Failed to create group chat');
      throw err;
    }
  };

  const markAsRead = async (messageIds: string[]) => {
    if (!currentConversationId || messageIds.length === 0) return;

    try {
      await markMessagesAsRead(currentConversationId, messageIds);

      // Update local state to reflect read status
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          messageIds.includes(msg.id)
            ? { ...msg, is_read: true }
            : msg
        )
      );

      // Update unread count in conversations list
      setConversations(prevConversations =>
        prevConversations.map(conv =>
          conv.id === currentConversationId
            ? { ...conv, unread_count: 0 }
            : conv
        )
      );

      // Refresh global chat notifications
      if ((window as any).refreshChatNotifications) {
        (window as any).refreshChatNotifications();
      }
    } catch (err: any) {
      console.error('Error marking messages as read:', err);
    }
  };

  const deleteUserMessage = async (messageId: string) => {
    if (!currentConversationId) return;

    try {
      await deleteMessage(messageId);

      // Update local state to remove the deleted message
      setMessages(prevMessages =>
        prevMessages.filter(msg => msg.id !== messageId)
      );

      toast.success('Message deleted');
    } catch (err: any) {
      console.error('Error deleting message:', err);
      setError(err.message || 'Failed to delete message');
      toast.error(err.message || 'Failed to delete message');
    }
  };

  const editUserMessage = async (messageId: string, newContent: string) => {
    if (!currentConversationId || !newContent.trim()) return;

    try {
      const updatedMessage = await editMessage(messageId, newContent);

      // Update local state with the edited message
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === messageId ? { ...msg, content: updatedMessage.content } : msg
        )
      );

      toast.success('Message updated');
    } catch (err: any) {
      console.error('Error editing message:', err);
      setError(err.message || 'Failed to edit message');
      toast.error(err.message || 'Failed to edit message');
    }
  };

  const searchMessages = async (query: string) => {
    setSearchQuery(query);
    await fetchMessages(true, true);
  };

  const clearSearch = () => {
    setSearchQuery('');
    fetchMessages(true, true);
  };

  const handleDeleteConversation = async (conversationId: string) => {
    try {
      await deleteConversation(conversationId);

      // Remove the conversation from the local state
      setConversations(prevConversations =>
        prevConversations.filter(conv => conv.id !== conversationId)
      );

      // If the deleted conversation is the current one, clear it
      if (currentConversationId === conversationId) {
        setCurrentConversationId(null);
      }

      toast.success('Conversation deleted successfully');
      return true;
    } catch (err: any) {
      console.error('Error deleting conversation:', err);
      setError(err.message || 'Failed to delete conversation');
      toast.error(err.message || 'Failed to delete conversation');
      return false;
    }
  };

  const handleLeaveConversation = async (conversationId: string) => {
    try {
      await leaveConversation(conversationId);

      // Remove the conversation from the local state
      setConversations(prevConversations =>
        prevConversations.filter(conv => conv.id !== conversationId)
      );

      // If the left conversation is the current one, clear it
      if (currentConversationId === conversationId) {
        setCurrentConversationId(null);
      }

      toast.success('You have left the conversation');
      return true;
    } catch (err: any) {
      console.error('Error leaving conversation:', err);
      setError(err.message || 'Failed to leave conversation');
      toast.error(err.message || 'Failed to leave conversation');
      return false;
    }
  };

  const value = {
    conversations,
    currentConversation,
    messages,
    loadingConversations,
    loadingMessages,
    uploadingFile,
    error,
    searchQuery,
    setCurrentConversationId,
    refreshConversations,
    loadMoreMessages,
    sendNewMessage,
    startOneOnOneChat,
    startGroupChat,
    markAsRead,
    deleteUserMessage,
    editUserMessage,
    deleteConversation: handleDeleteConversation,
    leaveConversation: handleLeaveConversation,
    searchMessages,
    clearSearch
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};
