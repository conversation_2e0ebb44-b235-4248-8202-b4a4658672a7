import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getUnitsOfMeasurement } from '../services/uom';
import { UnitOfMeasurement } from '../types/uom.types';
import { useOrganization } from './OrganizationContext';

interface UomContextType {
  uoms: UnitOfMeasurement[];
  loading: boolean;
  error: string | null;
  refreshUoms: () => Promise<void>;
}

const UomContext = createContext<UomContextType>({
  uoms: [],
  loading: true,
  error: null,
  refreshUoms: async () => {}
});

export const useUom = () => useContext(UomContext);

interface UomProviderProps {
  children: ReactNode;
}

export const UomProvider: React.FC<UomProviderProps> = ({ children }) => {
  const { currentOrganization } = useOrganization();
  const [uoms, setUoms] = useState<UnitOfMeasurement[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUoms = async () => {
    if (!currentOrganization) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { uoms: fetchedUoms, error: fetchError } = await getUnitsOfMeasurement(
        currentOrganization.id
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setUoms(fetchedUoms);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load units of measurement');
    } finally {
      setLoading(false);
    }
  };

  // Fetch UoMs when the organization changes
  useEffect(() => {
    fetchUoms();
  }, [currentOrganization?.id]);

  return (
    <UomContext.Provider
      value={{
        uoms,
        loading,
        error,
        refreshUoms: fetchUoms
      }}
    >
      {children}
    </UomContext.Provider>
  );
};
