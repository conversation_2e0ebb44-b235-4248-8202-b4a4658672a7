import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useOrganization } from './OrganizationContext';
import { getOrganizationSettings } from '../services/organization';

interface OrganizationSettingsContextType {
  settings: {
    currency: string;
    tax_rate: number;
    business_hours?: Record<string, { open: string; close: string }>;
    [key: string]: any;
  };
  isLoading: boolean;
  error: string | null;
  refreshSettings: () => Promise<void>;
}

const defaultSettings = {
  currency: 'PHP',
  tax_rate: 0,
  chat_enabled: true,
  currency_settings: {
    code: 'PHP',
    name: 'Philippine Peso',
    symbol: '₱',
    locale: 'en-PH'
  }
};

const OrganizationSettingsContext = createContext<OrganizationSettingsContextType>({
  settings: defaultSettings,
  isLoading: false,
  error: null,
  refreshSettings: async () => {},
});

export const useOrganizationSettings = () => useContext(OrganizationSettingsContext);

interface OrganizationSettingsProviderProps {
  children: ReactNode;
}

export const OrganizationSettingsProvider: React.FC<OrganizationSettingsProviderProps> = ({ children }) => {
  const { currentOrganization } = useOrganization();
  const [settings, setSettings] = useState<any>(defaultSettings);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = async () => {
    if (!currentOrganization) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const orgSettings = await getOrganizationSettings(currentOrganization.id);
      console.log('Fetched organization settings:', orgSettings.settings);
      setSettings(orgSettings.settings || defaultSettings);
    } catch (err: any) {
      console.error('Error fetching organization settings:', err);
      setError(err.message || 'Failed to load organization settings');
      setSettings(defaultSettings);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, [currentOrganization]);

  const refreshSettings = async () => {
    await fetchSettings();
  };

  return (
    <OrganizationSettingsContext.Provider
      value={{
        settings,
        isLoading,
        error,
        refreshSettings,
      }}
    >
      {children}
    </OrganizationSettingsContext.Provider>
  );
};
