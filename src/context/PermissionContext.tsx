import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { useOrganization } from './OrganizationContext';
import { getUserPermissions } from '../services/userManagement';

interface PermissionContextType {
  permissions: Record<string, Record<string, boolean>>;
  loading: boolean;
  error: string | null;
  checkPermission: (module: string, action: string) => boolean;
  refreshPermissions: () => Promise<void>;
}

const PermissionContext = createContext<PermissionContextType>({
  permissions: {},
  loading: true,
  error: null,
  checkPermission: () => false,
  refreshPermissions: async () => {},
});

export const usePermission = () => useContext(PermissionContext);

export const PermissionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const [permissions, setPermissions] = useState<Record<string, Record<string, boolean>>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPermissions = async () => {
    if (!user || !currentOrganization) {
      setPermissions({});
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { permissions: userPermissions, error: permissionsError } = await getUserPermissions(
        currentOrganization.id,
        user.id
      );

      if (permissionsError) {
        setError(permissionsError);
        setPermissions({});
      } else {
        setPermissions(userPermissions);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch permissions');
      setPermissions({});
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, [user, currentOrganization]);

  const checkPermission = (module: string, action: string): boolean => {
    // If no user or organization, no permissions
    if (!user || !currentOrganization) {
      return false;
    }

    // If permissions are still loading, be conservative
    if (loading) {
      return false;
    }

    // Check if the module exists
    if (!permissions[module]) {
      return false;
    }

    // Check if the action exists in the module
    if (permissions[module][action] === undefined) {
      return false;
    }

    // Return the permission value
    return permissions[module][action];
  };

  const refreshPermissions = async () => {
    await fetchPermissions();
  };

  return (
    <PermissionContext.Provider
      value={{
        permissions,
        loading,
        error,
        checkPermission,
        refreshPermissions,
      }}
    >
      {children}
    </PermissionContext.Provider>
  );
};

// Higher-order component to protect routes based on permissions
export const withPermission = (
  WrappedComponent: React.ComponentType<any>,
  module: string,
  action: string
) => {
  return (props: any) => {
    const { checkPermission, loading } = usePermission();
    const hasPermission = checkPermission(module, action);

    if (loading) {
      return <div>Loading permissions...</div>;
    }

    if (!hasPermission) {
      return <div>You don't have permission to access this page.</div>;
    }

    return <WrappedComponent {...props} />;
  };
};

// Component to conditionally render children based on permissions
export const PermissionGate: React.FC<{
  module: string;
  action: string;
  children: ReactNode;
  fallback?: ReactNode;
}> = ({ module, action, children, fallback = null }) => {
  const { checkPermission } = usePermission();
  const hasPermission = checkPermission(module, action);

  return hasPermission ? <>{children}</> : <>{fallback}</>;
};
