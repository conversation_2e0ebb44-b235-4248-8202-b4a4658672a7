import React, { createContext, useContext, useState, ReactNode } from 'react';
import { ProductUom, UnitOfMeasurement } from '../types/uom.types';

interface ProductUomCacheType {
  // Key is productId, value is array of product UoMs
  cache: Record<string, (ProductUom & { uom: UnitOfMeasurement })[]>;
  addToCache: (productId: string, uoms: (ProductUom & { uom: UnitOfMeasurement })[]) => void;
  getFromCache: (productId: string) => (ProductUom & { uom: UnitOfMeasurement })[] | null;
  clearCache: () => void;
}

const ProductUomContext = createContext<ProductUomCacheType>({
  cache: {},
  addToCache: () => {},
  getFromCache: () => null,
  clearCache: () => {}
});

export const useProductUomCache = () => useContext(ProductUomContext);

interface ProductUomProviderProps {
  children: ReactNode;
}

export const ProductUomProvider: React.FC<ProductUomProviderProps> = ({ children }) => {
  const [cache, setCache] = useState<Record<string, (ProductUom & { uom: UnitOfMeasurement })[]>>({});

  const addToCache = (productId: string, uoms: (ProductUom & { uom: UnitOfMeasurement })[]) => {
    setCache(prevCache => ({
      ...prevCache,
      [productId]: uoms
    }));
  };

  const getFromCache = (productId: string) => {
    return cache[productId] || null;
  };

  const clearCache = () => {
    setCache({});
  };

  return (
    <ProductUomContext.Provider
      value={{
        cache,
        addToCache,
        getFromCache,
        clearCache
      }}
    >
      {children}
    </ProductUomContext.Provider>
  );
};
