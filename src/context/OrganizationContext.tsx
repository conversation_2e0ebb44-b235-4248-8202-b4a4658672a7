import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Organization, getUserOrganizations } from '../services/organization';
import { useAuth } from './AuthContext';
import { OrganizationMember, getOrganizationMembers } from '../services/userManagement';

interface OrganizationContextType {
  organizations: Organization[];
  currentOrganization: Organization | null;
  setCurrentOrganization: (org: Organization) => void;
  currentMember: OrganizationMember | null;
  loading: boolean;
  error: Error | null;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export const OrganizationProvider = ({ children }: { children: ReactNode }) => {
  const { user } = useAuth();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null);
  const [currentMember, setCurrentMember] = useState<OrganizationMember | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Fetch the current member when the current organization changes
  useEffect(() => {
    const fetchCurrentMember = async () => {
      if (!user || !currentOrganization) {
        setCurrentMember(null);
        return;
      }

      try {
        const { members, error: membersError } = await getOrganizationMembers(currentOrganization.id);

        if (membersError) {
          console.error('Error fetching organization members:', membersError);
          return;
        }

        // Find the current user's membership
        const member = members.find(m => m.user_id === user.id);

        if (member) {
          setCurrentMember(member);
          console.log('Current member role:', member.role);
        } else {
          console.error('Current user is not a member of the organization');
          setCurrentMember(null);
        }
      } catch (err) {
        console.error('Error in fetchCurrentMember:', err);
      }
    };

    fetchCurrentMember();
  }, [user, currentOrganization]);

  useEffect(() => {
    const fetchOrganizations = async () => {
      if (!user) {
        setOrganizations([]);
        setCurrentOrganization(null);
        setCurrentMember(null);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const orgs = await getUserOrganizations();
        setOrganizations(orgs);

        // Check if there's a selected organization ID in local storage
        const selectedOrgId = localStorage.getItem('selectedOrganizationId');
        console.log('Selected organization ID from localStorage:', selectedOrgId);

        if (selectedOrgId) {
          // Find the organization with the selected ID
          const selectedOrg = orgs.find(org => org.id === selectedOrgId);

          if (selectedOrg) {
            // Set the current organization to the selected one
            console.log('Setting current organization to:', selectedOrg.name);
            setCurrentOrganization(selectedOrg);
          } else if (orgs.length > 0) {
            // If the selected organization doesn't exist, use the first one
            console.log('Selected organization not found, using first one:', orgs[0].name);
            setCurrentOrganization(orgs[0]);
            // Update local storage with the new organization ID
            localStorage.setItem('selectedOrganizationId', orgs[0].id);
          }
        } else if (orgs.length > 0 && !currentOrganization) {
          // If no organization is selected and there are organizations, use the first one
          console.log('No organization selected, using first one:', orgs[0].name);
          setCurrentOrganization(orgs[0]);
          // Store the selected organization ID in local storage
          localStorage.setItem('selectedOrganizationId', orgs[0].id);
        }

        setLoading(false);
      } catch (err) {
        setError(err as Error);
        setLoading(false);
      }
    };

    fetchOrganizations();
  }, [user]);

  // Update the setCurrentOrganization function to also update local storage
  const setCurrentOrganizationWithStorage = (org: Organization) => {
    console.log('Setting organization in context and localStorage:', org.name);
    // Store the selected organization ID in local storage
    localStorage.setItem('selectedOrganizationId', org.id);
    // Update the state
    setCurrentOrganization(org);
  };

  const value = {
    organizations,
    currentOrganization,
    setCurrentOrganization: setCurrentOrganizationWithStorage,
    currentMember,
    loading,
    error,
  };

  return <OrganizationContext.Provider value={value}>{children}</OrganizationContext.Provider>;
};

export const useOrganization = (): OrganizationContextType => {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
};
