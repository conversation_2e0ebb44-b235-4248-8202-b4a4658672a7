import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useOrganization } from './OrganizationContext';
import { 
  getLoyaltyProgramSettings, 
  LoyaltyProgramSettings,
  CustomerLoyaltyProfile,
  getCustomerLoyaltyProfile,
  calculatePointsForAmount,
  calculateDiscountForPoints
} from '../services/loyalty';

interface LoyaltyContextType {
  settings: LoyaltyProgramSettings | null;
  isLoading: boolean;
  error: string | null;
  refreshSettings: () => Promise<void>;
  getCustomerProfile: (customerId: string) => Promise<CustomerLoyaltyProfile | null>;
  calculatePoints: (amount: number, customerId?: string | null) => Promise<number>;
  calculateDiscount: (points: number, customerId: string) => Promise<{ discount: number; error?: string }>;
  canRedeemPoints: (customerId: string, points: number) => Promise<boolean>;
}

const defaultSettings: LoyaltyProgramSettings = {
  organization_id: '',
  is_enabled: false,
  points_earning_rate: 1.0,
  points_redemption_rate: 0.01,
  minimum_points_for_redemption: 100,
  points_expiration_months: null
};

const LoyaltyContext = createContext<LoyaltyContextType>({
  settings: null,
  isLoading: false,
  error: null,
  refreshSettings: async () => {},
  getCustomerProfile: async () => null,
  calculatePoints: async () => 0,
  calculateDiscount: async () => ({ discount: 0 }),
  canRedeemPoints: async () => false
});

export const useLoyalty = () => useContext(LoyaltyContext);

interface LoyaltyProviderProps {
  children: ReactNode;
}

export const LoyaltyProvider: React.FC<LoyaltyProviderProps> = ({ children }) => {
  const { currentOrganization } = useOrganization();
  const [settings, setSettings] = useState<LoyaltyProgramSettings | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [customerProfiles, setCustomerProfiles] = useState<Record<string, CustomerLoyaltyProfile>>({});

  const fetchSettings = async () => {
    if (!currentOrganization) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { settings: loyaltySettings, error: loyaltyError } = await getLoyaltyProgramSettings(currentOrganization.id);
      
      if (loyaltyError) {
        setError(loyaltyError);
        setSettings(null);
      } else {
        setSettings(loyaltySettings || null);
      }
    } catch (err: any) {
      console.error('Error fetching loyalty settings:', err);
      setError(err.message || 'Failed to load loyalty settings');
      setSettings(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, [currentOrganization]);

  const refreshSettings = async () => {
    await fetchSettings();
  };

  const getCustomerProfile = async (customerId: string): Promise<CustomerLoyaltyProfile | null> => {
    if (!currentOrganization || !customerId) {
      return null;
    }

    // Check if we already have the profile cached
    if (customerProfiles[customerId]) {
      return customerProfiles[customerId];
    }

    try {
      const { profile, error: profileError } = await getCustomerLoyaltyProfile(
        currentOrganization.id,
        customerId
      );

      if (profileError) {
        console.error('Error fetching customer loyalty profile:', profileError);
        return null;
      }

      if (profile) {
        // Cache the profile
        setCustomerProfiles(prev => ({
          ...prev,
          [customerId]: profile
        }));
        return profile;
      }

      return null;
    } catch (err: any) {
      console.error('Error in getCustomerProfile:', err);
      return null;
    }
  };

  const calculatePoints = async (amount: number, customerId?: string | null): Promise<number> => {
    if (!currentOrganization || !settings || !settings.is_enabled) {
      return 0;
    }

    try {
      const { points } = await calculatePointsForAmount(
        currentOrganization.id,
        customerId || null,
        amount
      );

      return points;
    } catch (err: any) {
      console.error('Error calculating points:', err);
      return 0;
    }
  };

  const calculateDiscount = async (points: number, customerId: string): Promise<{ discount: number; error?: string }> => {
    if (!currentOrganization || !settings || !settings.is_enabled) {
      return { discount: 0 };
    }

    try {
      const result = await calculateDiscountForPoints(
        currentOrganization.id,
        customerId,
        points
      );

      return result;
    } catch (err: any) {
      console.error('Error calculating discount:', err);
      return { discount: 0, error: err.message };
    }
  };

  const canRedeemPoints = async (customerId: string, points: number): Promise<boolean> => {
    if (!currentOrganization || !settings || !settings.is_enabled) {
      return false;
    }

    // Check if points meet minimum redemption requirement
    if (points < settings.minimum_points_for_redemption) {
      return false;
    }

    // Get customer profile to check available points
    const profile = await getCustomerProfile(customerId);
    
    // Check if customer has enough points
    if (!profile || profile.current_points_balance < points) {
      return false;
    }

    return true;
  };

  return (
    <LoyaltyContext.Provider
      value={{
        settings,
        isLoading,
        error,
        refreshSettings,
        getCustomerProfile,
        calculatePoints,
        calculateDiscount,
        canRedeemPoints
      }}
    >
      {children}
    </LoyaltyContext.Provider>
  );
};
