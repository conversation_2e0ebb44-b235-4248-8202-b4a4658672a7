import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { useOrganization } from './OrganizationContext';
import { useOrganizationSettings } from './OrganizationSettingsContext';
import { supabase } from '../lib/supabase';

interface ChatNotificationContextType {
  totalUnreadCount: number;
  hasNewMessages: boolean;
  refreshUnreadCount: () => Promise<void>;
  markAllAsRead: () => void;
}

const ChatNotificationContext = createContext<ChatNotificationContextType | undefined>(undefined);

export const useChatNotifications = () => {
  const context = useContext(ChatNotificationContext);
  if (context === undefined) {
    throw new Error('useChatNotifications must be used within a ChatNotificationProvider');
  }
  return context;
};

interface ChatNotificationProviderProps {
  children: ReactNode;
}

/**
 * Lightweight chat notification provider that only tracks unread counts
 * without loading the full chat context. This runs globally to show
 * notification badges in the header.
 */
export const ChatNotificationProvider: React.FC<ChatNotificationProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const { settings } = useOrganizationSettings();
  const [totalUnreadCount, setTotalUnreadCount] = useState<number>(0);
  const [hasNewMessages, setHasNewMessages] = useState<boolean>(false);

  // Check if chat is enabled
  const isChatEnabled = settings.chat_enabled !== false;

  // Fetch unread count efficiently
  const fetchUnreadCount = useCallback(async () => {
    if (!user || !currentOrganization || !isChatEnabled) {
      setTotalUnreadCount(0);
      return;
    }

    try {
      // Get user's conversations first
      const { data: conversations, error: convError } = await supabase
        .from('chat_conversations')
        .select(`
          id,
          participants:chat_participants!inner(
            user_id,
            last_read_message_id
          )
        `)
        .eq('organization_id', currentOrganization.id)
        .eq('participants.user_id', user.id);

      if (convError) {
        console.error('Error fetching conversations for notifications:', convError);
        return;
      }

      if (!conversations || conversations.length === 0) {
        setTotalUnreadCount(0);
        return;
      }

      // Calculate total unread count across all conversations
      let totalUnread = 0;

      for (const conversation of conversations) {
        const participant = conversation.participants.find(p => p.user_id === user.id);

        if (participant?.last_read_message_id) {
          // Get the timestamp of the last read message
          const { data: lastReadMessage } = await supabase
            .from('chat_messages')
            .select('created_at')
            .eq('id', participant.last_read_message_id)
            .maybeSingle();

          if (lastReadMessage) {
            // Count messages after the last read message
            const { count } = await supabase
              .from('chat_messages')
              .select('*', { count: 'exact', head: true })
              .eq('conversation_id', conversation.id)
              .gt('created_at', lastReadMessage.created_at)
              .neq('sender_id', user.id);

            totalUnread += count || 0;
          }
        } else {
          // Count all messages not from the current user
          const { count } = await supabase
            .from('chat_messages')
            .select('*', { count: 'exact', head: true })
            .eq('conversation_id', conversation.id)
            .neq('sender_id', user.id);

          totalUnread += count || 0;
        }
      }

      setTotalUnreadCount(totalUnread);

      // Set hasNewMessages flag if there are unread messages
      if (totalUnread > 0 && totalUnreadCount === 0) {
        setHasNewMessages(true);
        // Reset the flag after a short delay
        setTimeout(() => setHasNewMessages(false), 3000);
      }
    } catch (error) {
      console.error('Error fetching unread count:', error);
    }
  }, [user, currentOrganization, isChatEnabled, totalUnreadCount]);

  // Set up realtime subscription for new messages (lightweight)
  useEffect(() => {
    if (!user || !currentOrganization || !isChatEnabled) return;

    console.log('Setting up chat notification subscription');

    // Subscribe to new messages across all conversations in the organization
    const channel = supabase
      .channel(`notifications:${currentOrganization.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `organization_id=eq.${currentOrganization.id}`,
        },
        (payload) => {
          // Only count messages from other users
          if (payload.new.sender_id !== user.id) {
            console.log('New message notification received');
            setTotalUnreadCount(prev => prev + 1);
            setHasNewMessages(true);
            setTimeout(() => setHasNewMessages(false), 3000);
          }
        }
      )
      .subscribe();

    return () => {
      console.log('Cleaning up chat notification subscription');
      supabase.removeChannel(channel);
    };
  }, [user, currentOrganization, isChatEnabled]);

  // Initial fetch and periodic refresh
  useEffect(() => {
    if (!isChatEnabled) {
      setTotalUnreadCount(0);
      return;
    }

    fetchUnreadCount();

    // Refresh every 30 seconds (much less frequent than chat context)
    const interval = setInterval(fetchUnreadCount, 30000);

    return () => clearInterval(interval);
  }, [fetchUnreadCount, isChatEnabled]);

  const refreshUnreadCount = useCallback(async () => {
    await fetchUnreadCount();
  }, [fetchUnreadCount]);

  const markAllAsRead = useCallback(() => {
    setTotalUnreadCount(0);
    setHasNewMessages(false);
  }, []);

  // Expose the context globally for other components to trigger refresh
  useEffect(() => {
    // Store the refresh function globally so chat components can call it
    (window as any).refreshChatNotifications = refreshUnreadCount;

    return () => {
      delete (window as any).refreshChatNotifications;
    };
  }, [refreshUnreadCount]);

  // If chat is disabled, don't provide any notification data
  if (!isChatEnabled) {
    return (
      <ChatNotificationContext.Provider value={{
        totalUnreadCount: 0,
        hasNewMessages: false,
        refreshUnreadCount: async () => {},
        markAllAsRead: () => {}
      }}>
        {children}
      </ChatNotificationContext.Provider>
    );
  }

  const value = {
    totalUnreadCount,
    hasNewMessages,
    refreshUnreadCount,
    markAllAsRead
  };

  return (
    <ChatNotificationContext.Provider value={value}>
      {children}
    </ChatNotificationContext.Provider>
  );
};
