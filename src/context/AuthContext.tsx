import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';
import { AuthState } from '../services/auth';
import { handleAuthError, validateAndCleanupSession } from '../utils/authCleanup';

interface AuthContextType extends AuthState {
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    error: null,
  });

  useEffect(() => {
    // Get initial session with JWT error handling
    const getInitialSession = async () => {
      try {
        console.log('🔄 Getting initial session...');

        // First validate and cleanup if needed
        const isValid = await validateAndCleanupSession();

        if (!isValid) {
          // Session was invalid and cleaned up
          setAuthState({
            user: null,
            session: null,
            loading: false,
            error: null,
          });
          return;
        }

        // Get session normally if validation passed
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('❌ Error getting session:', error);
          await handleAuthError(error);
          setAuthState({
            user: null,
            session: null,
            loading: false,
            error: error as any,
          });
          return;
        }

        console.log('✅ Session retrieved successfully');
        setAuthState({
          user: session?.user ?? null,
          session,
          loading: false,
          error: null,
        });

      } catch (error) {
        console.error('❌ Unexpected error in getInitialSession:', error);
        await handleAuthError(error);
        setAuthState({
          user: null,
          session: null,
          loading: false,
          error: error as any,
        });
      }
    };

    getInitialSession();

    // Listen for auth changes with error handling
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        try {
          console.log('🔄 Auth state change:', event);

          // Handle sign out events
          if (event === 'SIGNED_OUT') {
            setAuthState({
              user: null,
              session: null,
              loading: false,
              error: null,
            });
            return;
          }

          // For other events, validate session if it exists
          if (session && session.access_token) {
            // Check if token is malformed
            const parts = session.access_token.split('.');
            if (parts.length !== 3) {
              console.log('🧹 Malformed token in auth state change, cleaning up...');
              await handleAuthError(new Error('Malformed JWT token'));
              return;
            }
          }

          setAuthState({
            user: session?.user ?? null,
            session,
            loading: false,
            error: null,
          });

        } catch (error) {
          console.error('❌ Error in auth state change:', error);
          await handleAuthError(error);
          setAuthState({
            user: null,
            session: null,
            loading: false,
            error: error as any,
          });
        }
      }
    );

    // Cleanup subscription
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  const value = {
    ...authState,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
