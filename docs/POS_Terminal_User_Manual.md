# POS Terminal User Manual

## Introduction

The POS (Point of Sale) Terminal is designed to help you process sales quickly and efficiently. This manual will guide you through the various features and functions of the POS Terminal.

## Getting Started

### Accessing the POS Terminal

1. Log in to your account
2. Navigate to the "Sales" section in the main menu
3. Click on "POS Terminal"

### Interface Overview

The POS Terminal is divided into three main sections:

1. **Left Panel**: Contains product search, barcode scanner, and customer selection
2. **Middle Panel**: Displays the current cart with items, quantities, and prices
3. **Right Panel**: Shows the order summary, including subtotal, tax, discount, and total

## Processing a Sale

### Step 1: Select a Customer (Optional)

- Click on the customer dropdown to search for and select a customer
- If no customer is selected, the sale will be processed as a walk-in customer

### Step 2: Add Products to Cart

There are multiple ways to add products to the cart:

#### Method 1: Barcode Scanner
1. Make sure the cursor is in the barcode input field (press Ctrl+B to focus)
2. Scan the product barcode or type it manually
3. Set the quantity (default is 1)
4. Press Enter or click "Add Item"

#### Method 2: Product Search
1. Use the product search field to find products by name, SKU, or barcode
2. Set the quantity (default is 1)
3. Select the product from the dropdown to add it to the cart

### Step 3: Modify Cart Items (If Needed)

- **Change Quantity**: Use the + and - buttons next to each item, or directly edit the quantity field
- **Remove Item**: Click the trash icon next to the item or press F3 to remove the last item
- **Add Notes**: Click the notes icon to add special instructions for an item

### Step 4: Apply Discount (Optional)

1. Click the "Apply Discount" button
2. Enter the discount amount
3. Select whether it's a percentage or fixed amount
4. Click "Apply"

### Step 5: Process Payment

1. Click "Pay" button or use keyboard shortcuts:
   - Ctrl+P for cash payment
   - Ctrl+K for card payment
2. For cash payments:
   - Enter the amount tendered
   - The system will calculate the change
3. For card payments:
   - Enter the card details (or use a card reader if available)
4. Click "Process Payment"

### Step 6: Complete the Sale

1. A success message will appear with the invoice number
2. You can view and print the receipt by clicking "View Receipt" or pressing F9
3. The cart will be cleared automatically for the next sale

## Additional Features

### Price Check (F2)

1. Press F2 or click the "Price Check" button
2. Search for a product
3. View the product details, including price and stock level
4. Optionally add the product to the cart

### View Sales List (F8)

1. Press F8 or click the "Sales" button
2. View a list of today's sales
3. Click on any sale to view its details and receipt

### Keyboard Shortcuts

For faster operation, use these keyboard shortcuts:

#### Navigation
- Ctrl+B: Focus on barcode input
- Ctrl+F: Focus on search input
- Ctrl+C: Focus on customer search
- Esc: Close modal / Clear cart

#### Payment
- Ctrl+P: Cash payment
- Ctrl+K: Card payment
- Ctrl+D: Apply discount

#### Quantity
- 1: Set quantity to 1
- 5: Set quantity to 5
- 0: Set quantity to 10

#### Function Keys
- F1: Show keyboard shortcuts
- F2: Price check
- F3: Void selected item
- F4: Toggle tax exempt
- F8: View sales list
- F9: View receipt (after sale)

## Handling Special Scenarios

### Inventory Float (Negative Inventory)

The system allows selling products even if they're not currently in inventory. This creates a negative inventory balance that should be resolved by receiving new stock.

### Returns and Refunds

To process a return:
1. Create a new sale
2. Add the items being returned
3. Apply a 100% discount
4. Process the payment as a refund

### Offline Mode

If the internet connection is lost:
1. The POS will continue to function in offline mode
2. Sales will be stored locally
3. Once the connection is restored, the system will sync with the server

## Troubleshooting

### Common Issues and Solutions

1. **Barcode not scanning**
   - Check if the barcode scanner is properly connected
   - Try entering the barcode manually

2. **Product not found**
   - Verify the product exists in the system
   - Check if the product is active

3. **Payment processing error**
   - Verify the internet connection
   - Check if the payment gateway is operational

4. **Printer not working**
   - Ensure the printer is connected and has paper
   - Check printer settings in the system

## Support

If you encounter any issues not covered in this manual, please contact support:
- Email: <EMAIL>
- Phone: ************
