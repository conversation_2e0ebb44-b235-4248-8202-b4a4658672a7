import{b as $,d as K,r as o,j as e,B as f,V as U,y as q,bA as A,co as te,a3 as re,c5 as ae,M as j,i as H,al as L,bQ as le,D as R,bT as ne,bd as ie,cp as ce,P as G,Z as oe,aj as Q,cq as de,cr as ue,cs as me,Q as W,ab as xe,Y as he,o as fe,aL as V}from"./index-C6AV3cVN.js";import{C as ge}from"./Card-yj7fueH8.js";import{u as B,A as Y,M as je,i as pe}from"./ChatContext-6er2fA7_.js";import{c as be}from"./formatters-Cypx7G-j.js";import{E as ye}from"./EmptyState-DxqWNypU.js";import"./CardBox-YV_4IKGE.js";const Ne=({conversation:s,onClose:l})=>{const{user:r}=$(),{deleteConversation:t,leaveConversation:d}=B(),c=K(),[x,u]=o.useState(!1),[g,m]=o.useState(!1),[p,b]=o.useState(!1),[y,v]=o.useState(!1),w=s.participants.some(a=>a.user_id===(r==null?void 0:r.id)&&a.is_admin)||s.created_by===(r==null?void 0:r.id);console.log("User ID:",r==null?void 0:r.id),console.log("Created by:",s.created_by),console.log("Is admin:",w),console.log("Participants:",s.participants);const k=()=>{if(s.is_group)return s.name||"Unnamed Group";const a=s.participants.filter(h=>h.profiles&&h.user_id!==(r==null?void 0:r.id));if(a.length>0){const h=a[0].profiles;return h!=null&&h.first_name&&(h!=null&&h.last_name)?`${h.first_name} ${h.last_name}`:"Unknown User"}return"Chat"},I=()=>{var h;if(s.is_group)return null;const a=s.participants.filter(N=>N.profiles&&N.user_id!==(r==null?void 0:r.id));return a.length>0?(h=a[0].profiles)==null?void 0:h.avatar_url:null},C=async()=>{if(s){b(!0);try{await d(s.id)&&(u(!1),l(),c("/chat"))}catch(a){console.error("Error leaving group:",a)}finally{b(!1)}}},F=async()=>{if(s){v(!0);try{await t(s.id)&&(m(!1),l(),c("/chat"))}catch(a){console.error("Error deleting group:",a)}finally{v(!1)}}};return e.jsxs("div",{className:"fixed top-0 right-0 h-full w-80 bg-white dark:bg-gray-800 shadow-lg z-50 overflow-y-auto",children:[e.jsxs("div",{className:"p-4 border-b flex justify-between items-center",children:[e.jsx("h3",{className:"font-medium",children:"Chat Info"}),e.jsx(f,{color:"light",size:"sm",onClick:l,children:e.jsx(U,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex flex-col items-center mb-6",children:[s.is_group?e.jsx("div",{className:"w-20 h-20 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-3",children:e.jsx(q,{className:"w-10 h-10 text-blue-600 dark:text-blue-300"})}):e.jsx(A,{img:I()||void 0,rounded:!0,size:"xl",className:"mb-3",placeholderInitials:k().substring(0,2)}),e.jsx("h2",{className:"text-xl font-semibold",children:k()}),s.is_group&&e.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[s.participants.length," participants"]}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Created ",be(s.created_at)]})]}),s.is_group&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Participants"}),e.jsx("ul",{className:"space-y-2",children:s.participants.map(a=>{var h,N,S,M,z,E,D;return e.jsxs("li",{className:"flex items-center",children:[e.jsx(A,{img:((h=a.profiles)==null?void 0:h.avatar_url)||void 0,rounded:!0,size:"xs",className:"mr-2",placeholderInitials:(N=a.profiles)!=null&&N.first_name&&((S=a.profiles)!=null&&S.last_name)?`${a.profiles.first_name[0]}${a.profiles.last_name[0]}`:(M=a.profiles)!=null&&M.first_name?a.profiles.first_name[0]:(z=a.profiles)!=null&&z.last_name?a.profiles.last_name[0]:""}),e.jsxs("span",{className:"text-sm",children:[(E=a.profiles)==null?void 0:E.first_name," ",(D=a.profiles)==null?void 0:D.last_name,a.user_id===(r==null?void 0:r.id)&&" (You)"]}),a.is_admin&&e.jsx("span",{className:"ml-2 text-xs bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded",children:"Admin"})]},a.id)})})]}),e.jsxs("div",{className:"space-y-2",children:[w&&e.jsxs(e.Fragment,{children:[e.jsxs(f,{color:"light",className:"w-full flex items-center justify-center",disabled:!0,children:[e.jsx(te,{className:"mr-2"}),"Add Participants"]}),e.jsxs(f,{color:"failure",className:"w-full flex items-center justify-center",onClick:()=>m(!0),children:[e.jsx(re,{className:"mr-2"}),"Delete Group"]})]}),e.jsxs(f,{color:"failure",className:"w-full flex items-center justify-center",onClick:()=>u(!0),children:[e.jsx(ae,{className:"mr-2"}),"Leave Group"]})]})]})]}),e.jsxs(j,{show:x,onClose:()=>u(!1),size:"sm",children:[e.jsx(j.Header,{children:"Leave Group"}),e.jsx(j.Body,{children:e.jsx("p",{children:"Are you sure you want to leave this group? You won't receive any new messages from this conversation."})}),e.jsxs(j.Footer,{children:[e.jsx(f,{color:"gray",onClick:()=>u(!1),children:"Cancel"}),e.jsxs(f,{color:"failure",onClick:C,disabled:p,children:[p?e.jsx(H,{size:"sm",className:"mr-2"}):null,"Leave Group"]})]})]}),e.jsxs(j,{show:g,onClose:()=>m(!1),size:"sm",children:[e.jsx(j.Header,{children:"Delete Group"}),e.jsxs(j.Body,{children:[e.jsx("p",{className:"text-red-600 font-medium",children:"Warning: This action cannot be undone."}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to delete this group? All messages and data will be permanently removed."})]}),e.jsxs(j.Footer,{children:[e.jsx(f,{color:"gray",onClick:()=>m(!1),children:"Cancel"}),e.jsxs(f,{color:"failure",onClick:F,disabled:y,children:[y?e.jsx(H,{size:"sm",className:"mr-2"}):null,"Delete Group"]})]})]})]})},ve=({onFileSelected:s,disabled:l=!1,selectedFile:r=null})=>{const t=o.useRef(null),[d,c]=o.useState(null),[x,u]=o.useState(null),g=r!==void 0?r:d,m=()=>{t.current&&t.current.click()},p=v=>{const w=v.target.files;if(!w||w.length===0)return;const k=w[0],I=pe(k);if(!I.valid){u(I.error||"Invalid file"),c(null),s(null),t.current&&(t.current.value="");return}u(null),c(k),s(k)},b=()=>{c(null),s(null),u(null),t.current&&(t.current.value="")},y=Object.values(Y).map(v=>`.${v.ext}`).join(", ");return e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"file",ref:t,onChange:p,className:"hidden",accept:Object.keys(Y).join(","),disabled:l}),g?e.jsxs("div",{className:"flex items-center bg-blue-50 dark:bg-blue-900 p-2 rounded-lg",children:[e.jsx("span",{className:"text-sm truncate max-w-[150px]",children:g.name}),e.jsx(f,{color:"light",size:"xs",className:"ml-2 p-1",onClick:b,children:e.jsx(U,{className:"w-4 h-4"})})]}):e.jsx(L,{content:`Attach a file (Max: ${je/(1024*1024)}MB)
Allowed: ${y}`,children:e.jsx(f,{color:"light",size:"sm",onClick:m,disabled:l,className:"p-2",children:e.jsx(le,{className:"w-5 h-5"})})}),x&&e.jsx("div",{className:"absolute bottom-full left-0 mb-1 text-xs text-red-500 bg-red-50 dark:bg-red-900 p-1 rounded",children:x})]})},we=({message:s})=>{const{user:l}=$(),{deleteUserMessage:r,editUserMessage:t}=B(),[d,c]=o.useState(!1),[x,u]=o.useState(!1),[g,m]=o.useState(s.content),[p,b]=o.useState(!1);if(s.sender_id!==(l==null?void 0:l.id))return null;const y=async()=>{b(!0);try{await r(s.id),c(!1)}finally{b(!1)}},v=async()=>{if(g.trim()){b(!0);try{await t(s.id,g),u(!1)}finally{b(!1)}}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"absolute top-0 right-0 p-1 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsxs(R,{label:e.jsx(ce,{className:"text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"}),arrowIcon:!1,inline:!0,children:[e.jsx(R.Item,{icon:ne,onClick:()=>u(!0),children:"Edit"}),e.jsx(R.Item,{icon:ie,onClick:()=>c(!0),children:"Delete"})]})}),e.jsxs(j,{show:d,onClose:()=>c(!1),size:"sm",children:[e.jsx(j.Header,{children:"Delete Message"}),e.jsx(j.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"mb-4",children:"Are you sure you want to delete this message?"}),e.jsxs("p",{className:"text-sm text-gray-500 italic",children:['"',s.content,'"']})]})}),e.jsxs(j.Footer,{children:[e.jsx(f,{color:"gray",onClick:()=>c(!1),children:"Cancel"}),e.jsx(f,{color:"failure",onClick:y,disabled:p,children:p?"Deleting...":"Delete"})]})]}),e.jsxs(j,{show:x,onClose:()=>u(!1),children:[e.jsx(j.Header,{children:"Edit Message"}),e.jsx(j.Body,{children:e.jsx(G,{value:g,onChange:w=>m(w.target.value),placeholder:"Edit your message",className:"mb-4"})}),e.jsxs(j.Footer,{children:[e.jsx(f,{color:"gray",onClick:()=>u(!1),children:"Cancel"}),e.jsx(f,{color:"blue",onClick:v,disabled:p||!g.trim(),children:p?"Saving...":"Save"})]})]})]})},_e=s=>{if(!s)return"0 B";const l=["B","KB","MB","GB"];let r=s,t=0;for(;r>=1024&&t<l.length-1;)r/=1024,t++;return`${r.toFixed(1)} ${l[t]}`},ke=({type:s,url:l,name:r,size:t,darkMode:d=!1})=>{if(!l||!s)return null;const c=r||l.split("/").pop()||"file",x=_e(t);if(s==="image")return e.jsxs("div",{className:"mt-2 w-full max-w-sm",children:[e.jsx("a",{href:l,target:"_blank",rel:"noopener noreferrer",className:"block",children:e.jsx("img",{src:l,alt:c,className:"rounded-lg max-h-60 w-full object-cover hover:object-contain transition-all duration-200 cursor-pointer"})}),e.jsxs("div",{className:"flex items-center justify-between mt-1 text-xs text-gray-500 dark:text-gray-400",children:[e.jsx("span",{className:"truncate flex-1 mr-2",children:c}),e.jsx("span",{className:"flex-shrink-0",children:x})]})]});const u=()=>{switch(s){case"document":return e.jsx(Q,{className:"w-5 h-5"});case"archive":return e.jsx(me,{className:"w-5 h-5"});case"audio":return e.jsx(ue,{className:"w-5 h-5"});case"video":return e.jsx(de,{className:"w-5 h-5"});default:return e.jsx(Q,{className:"w-5 h-5"})}};return e.jsx("div",{className:"mt-2",children:e.jsxs("a",{href:l,target:"_blank",rel:"noopener noreferrer",className:`flex items-center p-3 rounded-lg ${d?"bg-gray-800 hover:bg-gray-700":"bg-gray-100 hover:bg-gray-200"} transition-colors`,children:[e.jsx("div",{className:`mr-3 p-2 rounded-full ${d?"bg-gray-700":"bg-white"}`,children:u()}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-medium truncate",children:c}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:x})]}),e.jsx(oe,{className:"w-5 h-5 ml-2"})]})})},Ce=({message:s,showAvatar:l=!0})=>{var x,u,g;const{user:r}=$(),t=s.sender_id===(r==null?void 0:r.id),d=m=>new Date(m).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),c=()=>{var m,p,b,y;return(m=s.sender)!=null&&m.first_name&&((p=s.sender)!=null&&p.last_name)?`${s.sender.first_name[0]}${s.sender.last_name[0]}`:(b=s.sender)!=null&&b.first_name?s.sender.first_name[0]:(y=s.sender)!=null&&y.last_name?s.sender.last_name[0]:""};return e.jsxs("div",{className:`flex items-end ${t?"justify-end":"justify-start"} ${l?"mb-3":"mb-1"} group`,"data-message-id":s.id,children:[!t&&l&&e.jsx(A,{img:((x=s.sender)==null?void 0:x.avatar_url)||void 0,rounded:!0,size:"xs",placeholderInitials:c(),className:"mr-2 self-end flex-shrink-0 ring-1 ring-gray-200 dark:ring-gray-600"}),e.jsxs("div",{className:`relative max-w-[75%] px-3 py-2 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md ${t?"bg-gradient-to-r from-blue-500 to-blue-600 text-white":"bg-white dark:bg-gray-700 dark:text-white border border-gray-200 dark:border-gray-600"}`,style:{borderBottomRightRadius:t&&!l?"6px":"12px",borderBottomLeftRadius:!t&&!l?"6px":"12px"},children:[!t&&l&&e.jsxs("div",{className:"text-xs font-medium mb-1 text-gray-600 dark:text-gray-300",children:[(u=s.sender)==null?void 0:u.first_name," ",(g=s.sender)==null?void 0:g.last_name]}),e.jsx("div",{className:"text-sm leading-relaxed",children:s.content}),s.attachment_url&&e.jsx("div",{className:"mt-1",children:e.jsx(ke,{type:s.attachment_type||"",url:s.attachment_url,name:s.attachment_name||"Attachment",size:s.attachment_size||0})}),e.jsxs("div",{className:`text-xs mt-1 flex items-center justify-between opacity-75 ${t?"text-blue-100":"text-gray-500 dark:text-gray-400"}`,children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{children:d(s.created_at)}),s.created_at!==s.updated_at&&e.jsx("span",{className:"italic",children:"(edited)"})]}),t&&e.jsx("div",{className:"flex items-center ml-1",title:s.is_read?"Read":"Delivered",children:s.is_read?e.jsxs("div",{className:"flex items-center text-blue-200",children:[e.jsx("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),e.jsx("svg",{className:"w-3 h-3 -ml-0.5",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]}):e.jsx("svg",{className:"w-3 h-3 text-blue-200/60",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]}),e.jsx(we,{message:s})]}),!t&&!l&&e.jsx("div",{className:"w-6 mr-2 flex-shrink-0"})]})},Se=()=>{const{searchQuery:s,searchMessages:l,clearSearch:r}=B(),[t,d]=o.useState(s),[c,x]=o.useState(!1);o.useEffect(()=>{d(s)},[s]);const u=async m=>{if(m.preventDefault(),!t.trim()){r();return}x(!0);try{await l(t)}finally{x(!1)}},g=()=>{d(""),r()};return e.jsxs("div",{className:"mb-4",children:[e.jsxs("form",{onSubmit:u,className:"flex items-center",children:[e.jsxs("div",{className:"relative w-full",children:[e.jsx(G,{type:"text",placeholder:"Search messages...",value:t,onChange:m=>d(m.target.value),icon:W,className:"w-full"}),t&&e.jsx("button",{type:"button",className:"absolute inset-y-0 right-10 flex items-center pr-3",onClick:g,children:e.jsx(U,{className:"h-4 w-4 text-gray-500 hover:text-gray-700"})})]}),e.jsx(f,{type:"submit",color:"blue",size:"sm",className:"ml-2",disabled:c,children:c?"Searching...":"Search"})]}),s&&e.jsxs("div",{className:"mt-2 text-sm text-gray-500 flex justify-between items-center",children:[e.jsxs("span",{children:['Showing results for: "',s,'"']}),e.jsx(f,{color:"light",size:"xs",onClick:g,children:"Clear"})]})]})},Fe=()=>{const{conversationId:s}=xe(),l=K(),{user:r}=$(),{currentConversation:t,messages:d,loadingMessages:c,uploadingFile:x,error:u,searchQuery:g,setCurrentConversationId:m,sendNewMessage:p,loadMoreMessages:b,markAsRead:y,deleteUserMessage:v,editUserMessage:w,searchMessages:k,clearSearch:I}=B(),[C,F]=o.useState(""),[a,h]=o.useState(null),[N,S]=o.useState(!1),M=o.useRef(null),z=o.useRef(null),[E,D]=o.useState(!1);o.useEffect(()=>(s&&m(s),()=>{m(null)}),[s,m]),o.useEffect(()=>{M.current&&!E&&M.current.scrollIntoView({behavior:"smooth"})},[d,E]),o.useEffect(()=>{const i=z.current;if(!i)return;const n=()=>{const{scrollTop:_,scrollHeight:O,clientHeight:se}=i;D(O-_-se>100),_===0&&!c&&b(!0)};return i.addEventListener("scroll",n),()=>{i.removeEventListener("scroll",n)}},[b]);const T=i=>{i.preventDefault(),!(!C.trim()&&!a)&&p(C,a||void 0).then(()=>{F(""),h(null),D(!1)}).catch(n=>{console.error("Error sending message:",n)})},X=i=>{h(i)},Z=()=>{l("/chat")};o.useEffect(()=>{if(!d.length||!(r!=null&&r.id))return;const i=d.filter(n=>n.sender_id!==r.id&&!n.is_read).map(n=>n.id);if(i.length>0){const n=setTimeout(()=>{y(i)},1e3);return()=>clearTimeout(n)}},[d,r==null?void 0:r.id,y]);const P=()=>{if(!t)return"Chat";if(t.is_group)return t.name||"Unnamed Group";const i=t.participants.filter(n=>n.profiles&&n.user_id!==(r==null?void 0:r.id));if(i.length>0){const n=i[0].profiles;return n!=null&&n.first_name&&(n!=null&&n.last_name)?`${n.first_name} ${n.last_name}`:"Unknown User"}return"Chat"},J=()=>{var n;if(!t||t.is_group)return null;const i=t.participants.filter(_=>_.profiles&&_.user_id!==(r==null?void 0:r.id));return i.length>0?(n=i[0].profiles)==null?void 0:n.avatar_url:null},ee=()=>t?t.participants.length:0;return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(ge,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[t!=null&&t.is_group?e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:e.jsx(q,{className:"w-5 h-5 text-white"})}):e.jsx("div",{className:"w-10 h-10 rounded-full overflow-hidden ring-2 ring-blue-200 dark:ring-blue-700",children:e.jsx(A,{img:J()||void 0,rounded:!0,size:"md",placeholderInitials:P().substring(0,2),className:"w-full h-full object-cover"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:P()}),t!=null&&t.is_group?e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[ee()," participants"]}):e.jsx("p",{className:"text-xs text-green-500 dark:text-green-400",children:"Online"})]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(L,{content:"Search messages",children:e.jsx(f,{color:"light",size:"xs",onClick:()=>S(!N),className:`p-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors ${N?"bg-gray-200 dark:bg-gray-700":""}`,children:e.jsx(W,{className:"w-4 h-4"})})}),e.jsx(L,{content:"Conversation info",children:e.jsx(f,{color:"light",size:"xs",onClick:()=>S(!N),className:`p-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors ${N?"bg-gray-200 dark:bg-gray-700":""}`,children:e.jsx(he,{className:"w-4 h-4"})})}),e.jsx(L,{content:"Back to messages",children:e.jsx(f,{color:"light",size:"xs",onClick:Z,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",children:e.jsx(fe,{className:"w-4 h-4"})})})]})]}),t&&g&&e.jsx("div",{className:"mb-4",children:e.jsx(Se,{})}),e.jsx("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg mb-4",children:e.jsx("div",{className:"h-96 overflow-y-auto p-3 bg-gray-50 dark:bg-gray-800",ref:z,children:c&&d.length===0?e.jsx("div",{className:"flex justify-center items-center h-full",children:e.jsxs("div",{className:"text-center",children:[e.jsx(H,{size:"xl",className:"mb-4"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Loading messages..."})]})}):u?e.jsxs("div",{className:"text-center text-red-500 p-6 bg-red-50 dark:bg-red-900/20 rounded-xl",children:[e.jsx("p",{className:"font-medium",children:"Error loading messages"}),e.jsx("p",{className:"text-sm mt-1",children:u})]}):d.length===0?e.jsx("div",{className:"flex justify-center items-center h-full",children:e.jsx(ye,{title:"No messages yet",description:"Start the conversation by sending a message",icon:e.jsx(V,{className:"w-16 h-16 text-blue-500"})})}):e.jsxs(e.Fragment,{children:[c&&E&&e.jsxs("div",{className:"text-center py-4",children:[e.jsx(H,{size:"sm"}),e.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"Loading more messages..."})]}),e.jsx("div",{className:"space-y-2",children:d.map((i,n)=>{const _=n>0?d[n-1]:null,O=_&&_.sender_id===i.sender_id;return e.jsx(Ce,{message:i,showAvatar:!O},i.id)})}),e.jsx("div",{ref:M})]})})}),e.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-3",children:e.jsxs("form",{onSubmit:T,className:"flex items-end space-x-3",children:[e.jsx(ve,{onFileSelected:X,disabled:!t||x,selectedFile:a}),e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(G,{id:"message",type:"text",placeholder:a?"Add a message or send without text...":"Type your message...",value:C,onChange:i=>F(i.target.value),disabled:!t||x,className:"w-full pr-12 py-2 text-sm rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-transparent",onKeyDown:i=>{i.key==="Enter"&&!i.shiftKey&&(i.preventDefault(),T(i))}}),a&&e.jsx("div",{className:"absolute top-1 right-12 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs",children:a.name})]})}),e.jsx(f,{type:"submit",color:"primary",size:"sm",disabled:!C.trim()&&!a||!t||x,className:"px-4 py-2 rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-300 transition-colors",children:x?e.jsx(H,{size:"sm"}):e.jsx(V,{className:"w-4 h-4 transform rotate-45"})})]})})]}),N&&t&&e.jsx(Ne,{conversation:t,onClose:()=>S(!1)})]})};export{Fe as default};
