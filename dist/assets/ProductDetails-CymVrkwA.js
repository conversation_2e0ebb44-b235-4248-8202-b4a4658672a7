import{ab as ae,h as ce,d as le,r as a,j as e,A as h,J as y,i as v,L as u,B as o,o as F,e as j,a2 as S,a3 as ie,p as q,af as ne,ag as de,ah as oe,ai as Y,G as Q,aj as me,ak as xe,_ as c,M as k}from"./index-C6AV3cVN.js";import{C as w}from"./Card-yj7fueH8.js";import{a as he,d as ue}from"./product-Ca8DWaNR.js";import{u as je}from"./useProductPermissions-DyxXl-nR.js";import{getProductUoms as fe}from"./productUom-k6aUg6b7.js";import{T as ge,g as pe,a as Ne,r as ye}from"./TagList-S4Y14div.js";import{T as ve}from"./TagSelector-DMJHWHGW.js";import"./tagService-sPq402Av.js";const De=()=>{var $;const{id:l}=ae(),{currentOrganization:d}=ce(),R=le(),{canUpdateProducts:f,canDeleteProducts:G}=je(),[s,J]=a.useState(null),[K,T]=a.useState(!0),[D,g]=a.useState(null),[E,W]=a.useState([]),[V,U]=a.useState(!0),[H,b]=a.useState(null),[x,p]=a.useState([]),[X,L]=a.useState(!1),[O,C]=a.useState(null),[Z,N]=a.useState(!1),[I,z]=a.useState(!1),[A,_]=a.useState(null);a.useEffect(()=>{(async()=>{if(!(!d||!l)){T(!0),g(null);try{const{product:r,error:n}=await he(d.id,l);n?g(n):r?J(r):g("Product not found")}catch(r){g(r.message||"An error occurred while fetching the product")}finally{T(!1)}}})()},[d,l]),a.useEffect(()=>{s&&(async()=>{if(!(!d||!l)){U(!0),b(null);try{const{productUoms:r,error:n}=await fe(l,d.id);n?b(n):W(r)}catch(r){b(r.message||"An error occurred while fetching product UoMs")}finally{U(!1)}}})()},[d,l,s]),a.useEffect(()=>{(async()=>{if(l){L(!0),C(null);try{const{tags:r,error:n}=await pe(l);n?C(n):p(r)}catch(r){C(r.message||"An error occurred while fetching tags")}finally{L(!1)}}})()},[l]);const ee=()=>{N(!0)},se=async()=>{if(!(!d||!s)){z(!0),_(null);try{const{success:t,error:r}=await ue(d.id,s.id);r?_(r):t&&(N(!1),R("/products"))}catch(t){_(t.message||"An error occurred while deleting the product")}finally{z(!1)}}},B=t=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(t),M=t=>t==null?"0":new Intl.NumberFormat("en-US").format(t),te=async t=>{if(!l)return;const r=t.filter(i=>!x.some(m=>m.id===i.id)),n=x.filter(i=>!t.some(m=>m.id===i.id));p(t);for(const i of r)try{await Ne(l,i.id)}catch(m){console.error(`Failed to add tag ${i.name}:`,m),p(P=>P.filter(re=>re.id!==i.id))}for(const i of n)try{await ye(l,i.id)}catch(m){console.error(`Failed to remove tag ${i.name}:`,m),p(P=>[...P,i])}};return d?K?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(w,{children:e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(v,{size:"xl"})})})}):D||!s?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(w,{children:e.jsxs(h,{color:"failure",icon:y,children:[e.jsx("h3",{className:"text-lg font-medium",children:"Error"}),e.jsx("p",{children:D||"Product not found"}),e.jsx("div",{className:"mt-4",children:e.jsx(u,{to:"/products",children:e.jsxs(o,{color:"gray",size:"sm",children:[e.jsx(F,{className:"mr-2 h-4 w-4"}),"Back to Products"]})})})]})})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(w,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("h1",{className:"text-2xl font-bold",children:s.name}),e.jsx(j,{color:s.is_active?"success":"gray",className:"ml-3",children:s.is_active?"Active":"Inactive"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(u,{to:"/products",children:e.jsxs(o,{color:"gray",children:[e.jsx(F,{className:"mr-2 h-5 w-5"}),"Back"]})}),f&&e.jsx(u,{to:`/products/edit/${s.id}`,children:e.jsxs(o,{color:"primary",children:[e.jsx(S,{className:"mr-2 h-5 w-5"}),"Edit"]})}),G&&e.jsxs(o,{color:"failure",onClick:ee,children:[e.jsx(ie,{className:"mr-2 h-5 w-5"}),"Delete"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsx("div",{className:"md:col-span-1",children:s.image_url?e.jsx("img",{src:s.image_url,alt:s.name,className:"w-full h-auto rounded-lg object-cover shadow-md"}):e.jsx("div",{className:"w-full aspect-square bg-gray-200 rounded-lg flex items-center justify-center text-gray-500",children:e.jsx("span",{className:"text-lg",children:"No Image Available"})})}),e.jsxs("div",{className:"md:col-span-2 space-y-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Product Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx(q,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"SKU"}),e.jsx("p",{children:s.sku||"Not specified"})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(ne,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Barcode"}),e.jsx("p",{children:s.barcode||"Not specified"})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(de,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Category"}),e.jsx("p",{children:s.category_id?(($=s.category)==null?void 0:$.name)||"Category":"Uncategorized"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx(oe,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Selling Price"}),e.jsx("p",{className:"font-medium",children:B(s.unit_price)})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(Y,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Cost Price"}),e.jsx("p",{children:s.cost_price?B(s.cost_price):"Not specified"})]})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Inventory Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:`mt-1 mr-3 h-5 w-5 text-${(s.stock_quantity||0)<=(s.min_stock_level||0)?"red":"green"}-500`,children:(s.stock_quantity||0)<=(s.min_stock_level||0)?e.jsx(Q,{}):e.jsx(Y,{})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Current Stock"}),e.jsxs("div",{className:"flex items-center",children:[e.jsxs("p",{className:`font-medium ${(s.stock_quantity||0)<=(s.min_stock_level||0)?"text-red-500":"text-green-500"}`,children:[M(s.stock_quantity||0)," units"]}),(s.stock_quantity||0)<=(s.min_stock_level||0)&&e.jsx(j,{color:"failure",className:"ml-2",children:"Low Stock"})]})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(Q,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Minimum Stock Level"}),e.jsxs("p",{children:[M(s.min_stock_level||0)," units"]})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Tags"}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(q,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsx("div",{className:"w-full",children:X?e.jsxs("div",{className:"flex items-center",children:[e.jsx(v,{size:"sm",className:"mr-2"}),e.jsx("span",{className:"text-gray-500",children:"Loading tags..."})]}):O?e.jsx(h,{color:"failure",className:"mb-4",children:O}):e.jsx("div",{children:f?e.jsx(ve,{entityType:"product",entityId:s.id,selectedTags:x,onTagsChange:te}):x.length>0?e.jsx(ge,{tags:x}):e.jsx("p",{className:"text-gray-500",children:"No tags assigned to this product."})})})]})]}),s.description&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Description"}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(me,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsx("div",{children:e.jsx("p",{className:"whitespace-pre-line",children:s.description})})]})]}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[e.jsx(xe,{className:"mr-2 h-5 w-5"}),"Units of Measurement"]}),H&&e.jsx(h,{color:"failure",icon:y,className:"mb-4",children:H}),V?e.jsx("div",{className:"flex justify-center items-center p-4",children:e.jsx(v,{size:"md"})}):E.length===0?e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg text-center text-gray-500",children:[e.jsx("p",{children:"No units of measurement defined for this product"}),f&&e.jsx("div",{className:"mt-2",children:e.jsx(u,{to:`/products/edit/${s.id}`,children:e.jsxs(o,{color:"primary",size:"xs",children:[e.jsx(S,{className:"mr-2 h-4 w-4"}),"Add Units in Edit Mode"]})})})]}):e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs(c,{hoverable:!0,children:[e.jsxs(c.Head,{children:[e.jsx(c.HeadCell,{children:"Unit"}),e.jsx(c.HeadCell,{children:"Conversion Factor"}),e.jsx(c.HeadCell,{children:"Default"}),e.jsx(c.HeadCell,{children:"Purchasing"}),e.jsx(c.HeadCell,{children:"Selling"})]}),e.jsx(c.Body,{className:"divide-y",children:E.map(t=>e.jsxs(c.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsxs(c.Cell,{className:"font-medium",children:[t.uom.name," (",t.uom.code,")"]}),e.jsx(c.Cell,{children:t.conversion_factor}),e.jsx(c.Cell,{children:t.is_default?e.jsx(j,{color:"success",children:"Default"}):e.jsx("span",{className:"text-gray-400",children:"No"})}),e.jsx(c.Cell,{children:t.is_purchasing_unit?e.jsx(j,{color:"info",children:"Yes"}):e.jsx("span",{className:"text-gray-400",children:"No"})}),e.jsx(c.Cell,{children:t.is_selling_unit?e.jsx(j,{color:"info",children:"Yes"}):e.jsx("span",{className:"text-gray-400",children:"No"})})]},t.id))})]}),f&&e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsx(u,{to:`/products/edit/${s.id}`,children:e.jsxs(o,{color:"primary",size:"xs",children:[e.jsx(S,{className:"mr-2 h-4 w-4"}),"Manage Units"]})})})]})]})]})]}),e.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[e.jsx("h2",{className:"text-sm font-medium text-gray-500 mb-2",children:"System Information"}),e.jsxs("div",{className:"flex flex-wrap gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Product ID"}),e.jsx("p",{className:"text-sm",children:s.id})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Created"}),e.jsx("p",{className:"text-sm",children:new Date(s.created_at).toLocaleString()})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Last Updated"}),e.jsx("p",{className:"text-sm",children:new Date(s.updated_at).toLocaleString()})]})]})]})]}),e.jsxs(k,{show:Z,onClose:()=>N(!1),children:[e.jsx(k.Header,{children:"Confirm Deletion"}),e.jsx(k.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(y,{className:"mx-auto mb-4 h-14 w-14 text-red-500"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500",children:["Are you sure you want to delete ",s.name,"?"]}),A&&e.jsx(h,{color:"failure",className:"mb-4",children:A}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(o,{color:"failure",onClick:se,disabled:I,children:I?e.jsx(v,{size:"sm"}):"Yes, delete"}),e.jsx(o,{color:"gray",onClick:()=>N(!1),children:"No, cancel"})]})]})})]})]}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(w,{children:e.jsxs(h,{color:"failure",icon:y,children:[e.jsx("h3",{className:"text-lg font-medium",children:"No Organization Selected"}),e.jsx("p",{children:"Please select an organization to view product details."})]})})})};export{De as default};
