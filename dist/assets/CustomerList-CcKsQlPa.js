import{s as X,h as q,r as o,j as e,B as p,Z as G,X as W,L as N,a0 as M,P as Z,Q as K,A as T,J as R,i as L,_ as i,aX as ee,aY as se,D,a1 as te,a2 as re,a3 as ae,a4 as ne,M as O}from"./index-C6AV3cVN.js";import{C as le}from"./Card-yj7fueH8.js";import{c as oe,g as ie,d as ce}from"./customer-COogBrXM.js";import{P as me}from"./Pagination-CVEzfctr.js";import{d as de}from"./excelExport-BekG2cQR.js";import{G as ue}from"./GenericImport-Bi3EwZHp.js";import{p as U}from"./csvParser-Ls709fVB.js";const $=[{field:"name",required:!0,type:"string",customValidator:t=>typeof t=="string"&&t.length>255?"Customer name must be 255 characters or less":null},{field:"email",type:"email"},{field:"phone",type:"string",customValidator:t=>t&&typeof t=="string"&&t.length>50?"Phone number must be 50 characters or less":null},{field:"tax_id",type:"string",customValidator:t=>t&&typeof t=="string"&&t.length>50?"Tax ID must be 50 characters or less":null}],he=["name","email","phone","address","city","state","postal_code","country","tax_id","notes"],xe=t=>{const n=U(t,$);return{isValid:n.success,headers:n.headers,sampleData:n.data.slice(0,5),errors:n.errors,warnings:n.warnings,totalRows:n.data.length}},pe=async(t,n)=>{const f=n.map(a=>a.name),m=[];if(f.length>0){const{data:a}=await X.from("customers").select("name").eq("organization_id",t).in("name",f);a&&m.push(...a.map(l=>l.name))}return m},fe=async(t,n,f,m=!0)=>{const a=U(n,$);if(!a.success)return{success:!1,totalRows:a.data.length,successCount:0,errorCount:a.data.length,errors:a.errors,warnings:a.warnings,createdCustomers:[],createdItems:[]};const l=a.data,d=[...a.errors],y=[...a.warnings],h=[],w=await pe(t,l);let b=0,u=0;for(let x=0;x<l.length;x++){const r=l[x],g=x+2;try{if(w.includes(r.name))if(m){y.push(`Row ${g}: Skipped - Customer '${r.name}' already exists`);continue}else{d.push(`Row ${g}: Customer '${r.name}' already exists`),u++;continue}const C={name:r.name,email:r.email||null,phone:r.phone||null,address:r.address||null,city:r.city||null,state:r.state||null,postal_code:r.postal_code||null,country:r.country||null,tax_id:r.tax_id||null,notes:r.notes||null},c=await oe(t,C);if(c.customer&&!c.error)h.push(c.customer),b++;else{const j=c.error||"Failed to create customer";d.push(`Row ${g}: ${j}`),u++}}catch(C){const c=C instanceof Error?C.message:"Unknown error";d.push(`Row ${g}: ${c}`),u++}}return{success:u===0,totalRows:l.length,successCount:b,errorCount:u,errors:d,warnings:y,createdCustomers:h,createdItems:h}},je=()=>{const t=he,n=['"John Doe","<EMAIL>","555-0123","123 Main St","New York","NY","10001","USA","TAX123","VIP Customer"','"Jane Smith","<EMAIL>","555-0124","456 Oak Ave","Los Angeles","CA","90210","USA","TAX456","Regular Customer"','"Bob Johnson","","555-0125","789 Pine Rd","Chicago","IL","60601","USA","","Walk-in Customer"'],f=[t.map(d=>`"${d}"`).join(","),...n].join(`
`),m=new Blob([f],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(m),l=document.createElement("a");l.setAttribute("href",a),l.setAttribute("download","customer_import_template.csv"),l.style.visibility="hidden",document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(a)},ve=()=>{const{currentOrganization:t}=q(),[n,f]=o.useState([]),[m,a]=o.useState(0),[l,d]=o.useState(!0),[y,h]=o.useState(null),[w,b]=o.useState(""),[u,x]=o.useState(1),[r,g]=o.useState(10),[C,c]=o.useState(!1),[j,k]=o.useState(null),[A,E]=o.useState(!1),[H,I]=o.useState(null),[V,P]=o.useState(!1),S=async()=>{if(t){d(!0),h(null);try{const{customers:s,count:v,error:_}=await ie(t.id,{searchQuery:w||void 0,limit:r,offset:(u-1)*r});_?h(_):(f(s),a(v))}catch(s){h(s.message||"An error occurred while fetching customers")}finally{d(!1)}}};o.useEffect(()=>{S()},[t,u,w,r]);const z=s=>{s.preventDefault(),x(1),S()},B=s=>{k(s),c(!0)},F=async()=>{if(!(!t||!j)){E(!0),I(null);try{const{success:s,error:v}=await ce(t.id,j.id);v?I(v):s&&(c(!1),k(null),S())}catch(s){I(s.message||"An error occurred while deleting the customer")}finally{E(!1)}}},Y=()=>{if(n.length===0){h("No customers to export");return}de(n)},J=s=>{s.successCount>0&&S(),P(!1)},Q=Math.ceil(m/r);return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(le,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Customers"}),e.jsx("p",{className:"text-gray-500",children:"Manage your customer database. You can add, edit, and delete customer information."})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(p,{color:"light",onClick:Y,children:[e.jsx(G,{className:"mr-2 h-4 w-4"}),"Export"]}),e.jsxs(p,{color:"light",onClick:()=>P(!0),children:[e.jsx(W,{className:"mr-2 h-4 w-4"}),"Import"]}),e.jsx(N,{to:"/customers/create",children:e.jsxs(p,{color:"primary",children:[e.jsx(M,{className:"mr-2 h-5 w-5"}),"Add Customer"]})})]})]}),e.jsx("form",{onSubmit:z,className:"mb-6",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(Z,{id:"search",type:"text",placeholder:"Search customers by name, email, or phone",value:w,onChange:s=>b(s.target.value),className:"flex-1",icon:K}),e.jsx(p,{type:"submit",children:"Search"})]})}),y&&e.jsx(T,{color:"failure",icon:R,className:"mb-4",children:y}),H&&e.jsx(T,{color:"failure",icon:R,className:"mb-4",children:H}),l?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(L,{size:"xl"})}):n.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No customers found"}),e.jsx(N,{to:"/customers/create",children:e.jsxs(p,{color:"primary",size:"sm",children:[e.jsx(M,{className:"mr-2 h-4 w-4"}),"Add Your First Customer"]})})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(i,{hoverable:!0,children:[e.jsxs(i.Head,{children:[e.jsx(i.HeadCell,{children:"Name"}),e.jsx(i.HeadCell,{children:"Contact"}),e.jsx(i.HeadCell,{children:"Location"}),e.jsx(i.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(i.Body,{className:"divide-y",children:n.map(s=>e.jsxs(i.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(i.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:e.jsx(N,{to:`/customers/details/${s.id}`,className:"hover:text-primary hover:underline",children:s.name})}),e.jsx(i.Cell,{children:e.jsxs("div",{className:"flex flex-col",children:[s.email&&e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx(ee,{className:"mr-1 h-4 w-4 text-gray-500"}),e.jsx("span",{children:s.email})]}),s.phone&&e.jsxs("div",{className:"flex items-center text-sm mt-1",children:[e.jsx(se,{className:"mr-1 h-4 w-4 text-gray-500"}),e.jsx("span",{children:s.phone})]})]})}),e.jsx(i.Cell,{children:s.city&&s.state?e.jsxs("span",{children:[s.city,", ",s.state]}):s.city?e.jsx("span",{children:s.city}):s.state?e.jsx("span",{children:s.state}):e.jsx("span",{className:"text-gray-400",children:"Not specified"})}),e.jsx(i.Cell,{children:e.jsx("div",{className:"flex items-center",children:e.jsxs(D,{label:"",dismissOnClick:!0,renderTrigger:()=>e.jsx(p,{color:"gray",size:"xs",children:e.jsx(ne,{className:"h-4 w-4"})}),children:[e.jsxs(D.Item,{as:N,to:`/customers/details/${s.id}`,className:"text-blue-600 hover:bg-blue-50",children:[e.jsx(te,{className:"mr-2 h-4 w-4"}),"View Details"]}),e.jsxs(D.Item,{as:N,to:`/customers/edit/${s.id}`,className:"text-primary hover:bg-primary/10",children:[e.jsx(re,{className:"mr-2 h-4 w-4"}),"Edit"]}),e.jsxs(D.Item,{onClick:()=>B(s),className:"text-red-600 hover:bg-red-50",children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Delete"]})]})})})]},s.id))})]})}),e.jsx(me,{currentPage:u,totalPages:Q,itemsPerPage:r,totalItems:m,onPageChange:x,onItemsPerPageChange:s=>{g(s),x(1)},itemName:"customers"})]})]}),e.jsxs(O,{show:C,onClose:()=>c(!1),children:[e.jsx(O.Header,{children:"Confirm Deletion"}),e.jsx(O.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(R,{className:"mx-auto mb-4 h-14 w-14 text-red-500"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500",children:["Are you sure you want to delete ",j==null?void 0:j.name,"?"]}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(p,{color:"failure",onClick:F,disabled:A,children:A?e.jsx(L,{size:"sm"}):"Yes, delete"}),e.jsx(p,{color:"gray",onClick:()=>c(!1),children:"No, cancel"})]})]})})]}),e.jsx(ue,{show:V,onClose:()=>P(!1),onImportComplete:J,title:"Import Customers",entityName:"customers",previewFunction:xe,importFunction:fe,downloadTemplateFunction:je})]})};export{ve as default};
