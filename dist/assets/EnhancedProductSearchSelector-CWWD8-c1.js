var Zn=Object.defineProperty;var Qn=(t,e,r)=>e in t?Zn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var we=(t,e,r)=>Qn(t,typeof e!="symbol"?e+"":e,r);import{bi as Ft,r as f,bj as er,bk as tr,h as nr,j as q,B as rr,V as ar,Q as ir}from"./index-C6AV3cVN.js";import{a as or,g as $t}from"./product-Ca8DWaNR.js";import{_ as Pe}from"./typeof-QjJsDpFa.js";function mt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,a=Array(e);r<e;r++)a[r]=t[r];return a}function ln(t,e){if(t){if(typeof t=="string")return mt(t,e);var r={}.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?mt(t,e):void 0}}function sr(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function gt(t,e){return gt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,a){return r.__proto__=a,r},gt(t,e)}function ur(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&gt(t,e)}function je(t){return je=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},je(t)}function cn(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(cn=function(){return!!t})()}function lr(t,e){if(e&&(Pe(e)=="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return sr(t)}function cr(t){var e=cn();return function(){var r,a=je(t);if(e){var n=je(this).constructor;r=Reflect.construct(a,arguments,n)}else r=a.apply(this,arguments);return lr(this,r)}}function dr(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function fr(t,e){if(Pe(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var a=r.call(t,e);if(Pe(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function dn(t){var e=fr(t,"string");return Pe(e)=="symbol"?e:e+""}function kt(t,e){for(var r=0;r<e.length;r++){var a=e[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,dn(a.key),a)}}function pr(t,e,r){return e&&kt(t.prototype,e),r&&kt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function me(t,e,r){return(e=dn(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class vr{constructor(){we(this,"cache",new Map);we(this,"organizationCache",new Map);we(this,"lastFetchTime",new Map);we(this,"cacheTTL",5*60*1e3)}async getProduct(e,r){const a=`${e}:${r}`;if(this.cache.has(a)){const n=this.lastFetchTime.get(a)||0;if(Date.now()-n<this.cacheTTL)return this.cache.get(a)||null}try{const{product:n,error:i}=await or(e,r);return i||!n?null:(this.cache.set(a,n),this.lastFetchTime.set(a,Date.now()),n)}catch(n){return console.error("Error fetching product for cache:",n),null}}async getProducts(e,r){const a=Date.now(),n=[],i=[];for(const s of r){const l=`${e}:${s}`,o=this.lastFetchTime.get(l)||0;if(this.cache.has(l)&&a-o<this.cacheTTL){const u=this.cache.get(l);u&&n.push(u)}else i.push(s)}if(i.length===0)return n;try{const{products:s,error:l}=await $t(e,{limit:1e3});if(l||!s)return n;const o=s.filter(u=>i.includes(u.id));for(const u of o){const c=`${e}:${u.id}`;this.cache.set(c,u),this.lastFetchTime.set(c,a)}return[...n,...o]}catch(s){return console.error("Error fetching products for cache:",s),n}}async searchProducts(e,r,a=1,n=10){const i=(a-1)*n;try{const{products:s,count:l,error:o}=await $t(e,{searchQuery:r,limit:n,offset:i,isActive:!0});if(o||!s)return{products:[],totalCount:0};const u=Date.now();for(const c of s){const d=`${e}:${c.id}`;this.cache.set(d,c),this.lastFetchTime.set(d,u)}return{products:s,totalCount:l}}catch(s){return console.error("Error searching products for cache:",s),{products:[],totalCount:0}}}clearOrganizationCache(e){this.organizationCache.delete(e);for(const r of this.cache.keys())r.startsWith(`${e}:`)&&(this.cache.delete(r),this.lastFetchTime.delete(r))}clearCache(){this.cache.clear(),this.organizationCache.clear(),this.lastFetchTime.clear()}}const At=new vr;function x(){return x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var a in r)({}).hasOwnProperty.call(r,a)&&(t[a]=r[a])}return t},x.apply(null,arguments)}function Ht(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),r.push.apply(r,a)}return r}function C(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ht(Object(r),!0).forEach(function(a){me(t,a,r[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ht(Object(r)).forEach(function(a){Object.defineProperty(t,a,Object.getOwnPropertyDescriptor(r,a))})}return t}function hr(t){if(Array.isArray(t))return mt(t)}function mr(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function gr(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wt(t){return hr(t)||mr(t)||ln(t)||gr()}function br(t){if(t.sheet)return t.sheet;for(var e=0;e<document.styleSheets.length;e++)if(document.styleSheets[e].ownerNode===t)return document.styleSheets[e]}function Sr(t){var e=document.createElement("style");return e.setAttribute("data-emotion",t.key),t.nonce!==void 0&&e.setAttribute("nonce",t.nonce),e.appendChild(document.createTextNode("")),e.setAttribute("data-s",""),e}var yr=function(){function t(r){var a=this;this._insertTag=function(n){var i;a.tags.length===0?a.insertionPoint?i=a.insertionPoint.nextSibling:a.prepend?i=a.container.firstChild:i=a.before:i=a.tags[a.tags.length-1].nextSibling,a.container.insertBefore(n,i),a.tags.push(n)},this.isSpeedy=r.speedy===void 0?!0:r.speedy,this.tags=[],this.ctr=0,this.nonce=r.nonce,this.key=r.key,this.container=r.container,this.prepend=r.prepend,this.insertionPoint=r.insertionPoint,this.before=null}var e=t.prototype;return e.hydrate=function(a){a.forEach(this._insertTag)},e.insert=function(a){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(Sr(this));var n=this.tags[this.tags.length-1];if(this.isSpeedy){var i=br(n);try{i.insertRule(a,i.cssRules.length)}catch{}}else n.appendChild(document.createTextNode(a));this.ctr++},e.flush=function(){this.tags.forEach(function(a){var n;return(n=a.parentNode)==null?void 0:n.removeChild(a)}),this.tags=[],this.ctr=0},t}(),W="-ms-",Ue="-moz-",E="-webkit-",fn="comm",xt="rule",It="decl",Or="@import",pn="@keyframes",Cr="@layer",wr=Math.abs,Ge=String.fromCharCode,xr=Object.assign;function Ir(t,e){return U(t,0)^45?(((e<<2^U(t,0))<<2^U(t,1))<<2^U(t,2))<<2^U(t,3):0}function vn(t){return t.trim()}function Pr(t,e){return(t=e.exec(t))?t[0]:t}function R(t,e,r){return t.replace(e,r)}function bt(t,e){return t.indexOf(e)}function U(t,e){return t.charCodeAt(e)|0}function Me(t,e,r){return t.slice(e,r)}function ue(t){return t.length}function Pt(t){return t.length}function De(t,e){return e.push(t),t}function Mr(t,e){return t.map(e).join("")}var Ye=1,be=1,hn=0,X=0,H=0,Se="";function Ke(t,e,r,a,n,i,s){return{value:t,root:e,parent:r,type:a,props:n,children:i,line:Ye,column:be,length:s,return:""}}function xe(t,e){return xr(Ke("",null,null,"",null,null,0),t,{length:-t.length},e)}function Vr(){return H}function Er(){return H=X>0?U(Se,--X):0,be--,H===10&&(be=1,Ye--),H}function ee(){return H=X<hn?U(Se,X++):0,be++,H===10&&(be=1,Ye++),H}function ce(){return U(Se,X)}function Ae(){return X}function Re(t,e){return Me(Se,t,e)}function Ve(t){switch(t){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function mn(t){return Ye=be=1,hn=ue(Se=t),X=0,[]}function gn(t){return Se="",t}function He(t){return vn(Re(X-1,St(t===91?t+2:t===40?t+1:t)))}function Rr(t){for(;(H=ce())&&H<33;)ee();return Ve(t)>2||Ve(H)>3?"":" "}function Lr(t,e){for(;--e&&ee()&&!(H<48||H>102||H>57&&H<65||H>70&&H<97););return Re(t,Ae()+(e<6&&ce()==32&&ee()==32))}function St(t){for(;ee();)switch(H){case t:return X;case 34:case 39:t!==34&&t!==39&&St(H);break;case 40:t===41&&St(t);break;case 92:ee();break}return X}function Tr(t,e){for(;ee()&&t+H!==57;)if(t+H===84&&ce()===47)break;return"/*"+Re(e,X-1)+"*"+Ge(t===47?t:ee())}function Dr(t){for(;!Ve(ce());)ee();return Re(t,X)}function Fr(t){return gn(_e("",null,null,null,[""],t=mn(t),0,[0],t))}function _e(t,e,r,a,n,i,s,l,o){for(var u=0,c=0,d=s,g=0,b=0,S=0,m=1,v=1,h=1,p=0,y="",I=n,V=i,M=a,O=y;v;)switch(S=p,p=ee()){case 40:if(S!=108&&U(O,d-1)==58){bt(O+=R(He(p),"&","&\f"),"&\f")!=-1&&(h=-1);break}case 34:case 39:case 91:O+=He(p);break;case 9:case 10:case 13:case 32:O+=Rr(S);break;case 92:O+=Lr(Ae()-1,7);continue;case 47:switch(ce()){case 42:case 47:De($r(Tr(ee(),Ae()),e,r),o);break;default:O+="/"}break;case 123*m:l[u++]=ue(O)*h;case 125*m:case 59:case 0:switch(p){case 0:case 125:v=0;case 59+c:h==-1&&(O=R(O,/\f/g,"")),b>0&&ue(O)-d&&De(b>32?Nt(O+";",a,r,d-1):Nt(R(O," ","")+";",a,r,d-2),o);break;case 59:O+=";";default:if(De(M=_t(O,e,r,u,c,n,l,y,I=[],V=[],d),i),p===123)if(c===0)_e(O,e,M,M,I,i,d,l,V);else switch(g===99&&U(O,3)===110?100:g){case 100:case 108:case 109:case 115:_e(t,M,M,a&&De(_t(t,M,M,0,0,n,l,y,n,I=[],d),V),n,V,d,l,a?I:V);break;default:_e(O,M,M,M,[""],V,0,l,V)}}u=c=b=0,m=h=1,y=O="",d=s;break;case 58:d=1+ue(O),b=S;default:if(m<1){if(p==123)--m;else if(p==125&&m++==0&&Er()==125)continue}switch(O+=Ge(p),p*m){case 38:h=c>0?1:(O+="\f",-1);break;case 44:l[u++]=(ue(O)-1)*h,h=1;break;case 64:ce()===45&&(O+=He(ee())),g=ce(),c=d=ue(y=O+=Dr(Ae())),p++;break;case 45:S===45&&ue(O)==2&&(m=0)}}return i}function _t(t,e,r,a,n,i,s,l,o,u,c){for(var d=n-1,g=n===0?i:[""],b=Pt(g),S=0,m=0,v=0;S<a;++S)for(var h=0,p=Me(t,d+1,d=wr(m=s[S])),y=t;h<b;++h)(y=vn(m>0?g[h]+" "+p:R(p,/&\f/g,g[h])))&&(o[v++]=y);return Ke(t,e,r,n===0?xt:l,o,u,c)}function $r(t,e,r){return Ke(t,e,r,fn,Ge(Vr()),Me(t,2,-2),0)}function Nt(t,e,r,a){return Ke(t,e,r,It,Me(t,0,a),Me(t,a+1,-1),a)}function ge(t,e){for(var r="",a=Pt(t),n=0;n<a;n++)r+=e(t[n],n,t,e)||"";return r}function kr(t,e,r,a){switch(t.type){case Cr:if(t.children.length)break;case Or:case It:return t.return=t.return||t.value;case fn:return"";case pn:return t.return=t.value+"{"+ge(t.children,a)+"}";case xt:t.value=t.props.join(",")}return ue(r=ge(t.children,a))?t.return=t.value+"{"+r+"}":""}function Ar(t){var e=Pt(t);return function(r,a,n,i){for(var s="",l=0;l<e;l++)s+=t[l](r,a,n,i)||"";return s}}function Hr(t){return function(e){e.root||(e=e.return)&&t(e)}}function _r(t){var e=Object.create(null);return function(r){return e[r]===void 0&&(e[r]=t(r)),e[r]}}var Nr=function(e,r,a){for(var n=0,i=0;n=i,i=ce(),n===38&&i===12&&(r[a]=1),!Ve(i);)ee();return Re(e,X)},jr=function(e,r){var a=-1,n=44;do switch(Ve(n)){case 0:n===38&&ce()===12&&(r[a]=1),e[a]+=Nr(X-1,r,a);break;case 2:e[a]+=He(n);break;case 4:if(n===44){e[++a]=ce()===58?"&\f":"",r[a]=e[a].length;break}default:e[a]+=Ge(n)}while(n=ee());return e},Ur=function(e,r){return gn(jr(mn(e),r))},jt=new WeakMap,Br=function(e){if(!(e.type!=="rule"||!e.parent||e.length<1)){for(var r=e.value,a=e.parent,n=e.column===a.column&&e.line===a.line;a.type!=="rule";)if(a=a.parent,!a)return;if(!(e.props.length===1&&r.charCodeAt(0)!==58&&!jt.get(a))&&!n){jt.set(e,!0);for(var i=[],s=Ur(r,i),l=a.props,o=0,u=0;o<s.length;o++)for(var c=0;c<l.length;c++,u++)e.props[u]=i[o]?s[o].replace(/&\f/g,l[c]):l[c]+" "+s[o]}}},zr=function(e){if(e.type==="decl"){var r=e.value;r.charCodeAt(0)===108&&r.charCodeAt(2)===98&&(e.return="",e.value="")}};function bn(t,e){switch(Ir(t,e)){case 5103:return E+"print-"+t+t;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return E+t+t;case 5349:case 4246:case 4810:case 6968:case 2756:return E+t+Ue+t+W+t+t;case 6828:case 4268:return E+t+W+t+t;case 6165:return E+t+W+"flex-"+t+t;case 5187:return E+t+R(t,/(\w+).+(:[^]+)/,E+"box-$1$2"+W+"flex-$1$2")+t;case 5443:return E+t+W+"flex-item-"+R(t,/flex-|-self/,"")+t;case 4675:return E+t+W+"flex-line-pack"+R(t,/align-content|flex-|-self/,"")+t;case 5548:return E+t+W+R(t,"shrink","negative")+t;case 5292:return E+t+W+R(t,"basis","preferred-size")+t;case 6060:return E+"box-"+R(t,"-grow","")+E+t+W+R(t,"grow","positive")+t;case 4554:return E+R(t,/([^-])(transform)/g,"$1"+E+"$2")+t;case 6187:return R(R(R(t,/(zoom-|grab)/,E+"$1"),/(image-set)/,E+"$1"),t,"")+t;case 5495:case 3959:return R(t,/(image-set\([^]*)/,E+"$1$`$1");case 4968:return R(R(t,/(.+:)(flex-)?(.*)/,E+"box-pack:$3"+W+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+E+t+t;case 4095:case 3583:case 4068:case 2532:return R(t,/(.+)-inline(.+)/,E+"$1$2")+t;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(ue(t)-1-e>6)switch(U(t,e+1)){case 109:if(U(t,e+4)!==45)break;case 102:return R(t,/(.+:)(.+)-([^]+)/,"$1"+E+"$2-$3$1"+Ue+(U(t,e+3)==108?"$3":"$2-$3"))+t;case 115:return~bt(t,"stretch")?bn(R(t,"stretch","fill-available"),e)+t:t}break;case 4949:if(U(t,e+1)!==115)break;case 6444:switch(U(t,ue(t)-3-(~bt(t,"!important")&&10))){case 107:return R(t,":",":"+E)+t;case 101:return R(t,/(.+:)([^;!]+)(;|!.+)?/,"$1"+E+(U(t,14)===45?"inline-":"")+"box$3$1"+E+"$2$3$1"+W+"$2box$3")+t}break;case 5936:switch(U(t,e+11)){case 114:return E+t+W+R(t,/[svh]\w+-[tblr]{2}/,"tb")+t;case 108:return E+t+W+R(t,/[svh]\w+-[tblr]{2}/,"tb-rl")+t;case 45:return E+t+W+R(t,/[svh]\w+-[tblr]{2}/,"lr")+t}return E+t+W+t+t}return t}var Wr=function(e,r,a,n){if(e.length>-1&&!e.return)switch(e.type){case It:e.return=bn(e.value,e.length);break;case pn:return ge([xe(e,{value:R(e.value,"@","@"+E)})],n);case xt:if(e.length)return Mr(e.props,function(i){switch(Pr(i,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return ge([xe(e,{props:[R(i,/:(read-\w+)/,":"+Ue+"$1")]})],n);case"::placeholder":return ge([xe(e,{props:[R(i,/:(plac\w+)/,":"+E+"input-$1")]}),xe(e,{props:[R(i,/:(plac\w+)/,":"+Ue+"$1")]}),xe(e,{props:[R(i,/:(plac\w+)/,W+"input-$1")]})],n)}return""})}},Gr=[Wr],Yr=function(e){var r=e.key;if(r==="css"){var a=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(a,function(m){var v=m.getAttribute("data-emotion");v.indexOf(" ")!==-1&&(document.head.appendChild(m),m.setAttribute("data-s",""))})}var n=e.stylisPlugins||Gr,i={},s,l=[];s=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+r+' "]'),function(m){for(var v=m.getAttribute("data-emotion").split(" "),h=1;h<v.length;h++)i[v[h]]=!0;l.push(m)});var o,u=[Br,zr];{var c,d=[kr,Hr(function(m){c.insert(m)})],g=Ar(u.concat(n,d)),b=function(v){return ge(Fr(v),g)};o=function(v,h,p,y){c=p,b(v?v+"{"+h.styles+"}":h.styles),y&&(S.inserted[h.name]=!0)}}var S={key:r,sheet:new yr({key:r,container:s,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:i,registered:{},insert:o};return S.sheet.hydrate(l),S},Sn={exports:{}},L={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var j=typeof Symbol=="function"&&Symbol.for,Mt=j?Symbol.for("react.element"):60103,Vt=j?Symbol.for("react.portal"):60106,qe=j?Symbol.for("react.fragment"):60107,Xe=j?Symbol.for("react.strict_mode"):60108,Je=j?Symbol.for("react.profiler"):60114,Ze=j?Symbol.for("react.provider"):60109,Qe=j?Symbol.for("react.context"):60110,Et=j?Symbol.for("react.async_mode"):60111,et=j?Symbol.for("react.concurrent_mode"):60111,tt=j?Symbol.for("react.forward_ref"):60112,nt=j?Symbol.for("react.suspense"):60113,Kr=j?Symbol.for("react.suspense_list"):60120,rt=j?Symbol.for("react.memo"):60115,at=j?Symbol.for("react.lazy"):60116,qr=j?Symbol.for("react.block"):60121,Xr=j?Symbol.for("react.fundamental"):60117,Jr=j?Symbol.for("react.responder"):60118,Zr=j?Symbol.for("react.scope"):60119;function te(t){if(typeof t=="object"&&t!==null){var e=t.$$typeof;switch(e){case Mt:switch(t=t.type,t){case Et:case et:case qe:case Je:case Xe:case nt:return t;default:switch(t=t&&t.$$typeof,t){case Qe:case tt:case at:case rt:case Ze:return t;default:return e}}case Vt:return e}}}function yn(t){return te(t)===et}L.AsyncMode=Et;L.ConcurrentMode=et;L.ContextConsumer=Qe;L.ContextProvider=Ze;L.Element=Mt;L.ForwardRef=tt;L.Fragment=qe;L.Lazy=at;L.Memo=rt;L.Portal=Vt;L.Profiler=Je;L.StrictMode=Xe;L.Suspense=nt;L.isAsyncMode=function(t){return yn(t)||te(t)===Et};L.isConcurrentMode=yn;L.isContextConsumer=function(t){return te(t)===Qe};L.isContextProvider=function(t){return te(t)===Ze};L.isElement=function(t){return typeof t=="object"&&t!==null&&t.$$typeof===Mt};L.isForwardRef=function(t){return te(t)===tt};L.isFragment=function(t){return te(t)===qe};L.isLazy=function(t){return te(t)===at};L.isMemo=function(t){return te(t)===rt};L.isPortal=function(t){return te(t)===Vt};L.isProfiler=function(t){return te(t)===Je};L.isStrictMode=function(t){return te(t)===Xe};L.isSuspense=function(t){return te(t)===nt};L.isValidElementType=function(t){return typeof t=="string"||typeof t=="function"||t===qe||t===et||t===Je||t===Xe||t===nt||t===Kr||typeof t=="object"&&t!==null&&(t.$$typeof===at||t.$$typeof===rt||t.$$typeof===Ze||t.$$typeof===Qe||t.$$typeof===tt||t.$$typeof===Xr||t.$$typeof===Jr||t.$$typeof===Zr||t.$$typeof===qr)};L.typeOf=te;Sn.exports=L;var Qr=Sn.exports,On=Qr,ea={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},ta={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Cn={};Cn[On.ForwardRef]=ea;Cn[On.Memo]=ta;var na=!0;function ra(t,e,r){var a="";return r.split(" ").forEach(function(n){t[n]!==void 0?e.push(t[n]+";"):n&&(a+=n+" ")}),a}var wn=function(e,r,a){var n=e.key+"-"+r.name;(a===!1||na===!1)&&e.registered[n]===void 0&&(e.registered[n]=r.styles)},aa=function(e,r,a){wn(e,r,a);var n=e.key+"-"+r.name;if(e.inserted[r.name]===void 0){var i=r;do e.insert(r===i?"."+n:"",i,e.sheet,!0),i=i.next;while(i!==void 0)}};function ia(t){for(var e=0,r,a=0,n=t.length;n>=4;++a,n-=4)r=t.charCodeAt(a)&255|(t.charCodeAt(++a)&255)<<8|(t.charCodeAt(++a)&255)<<16|(t.charCodeAt(++a)&255)<<24,r=(r&65535)*1540483477+((r>>>16)*59797<<16),r^=r>>>24,e=(r&65535)*1540483477+((r>>>16)*59797<<16)^(e&65535)*1540483477+((e>>>16)*59797<<16);switch(n){case 3:e^=(t.charCodeAt(a+2)&255)<<16;case 2:e^=(t.charCodeAt(a+1)&255)<<8;case 1:e^=t.charCodeAt(a)&255,e=(e&65535)*1540483477+((e>>>16)*59797<<16)}return e^=e>>>13,e=(e&65535)*1540483477+((e>>>16)*59797<<16),((e^e>>>15)>>>0).toString(36)}var oa={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},sa=/[A-Z]|^ms/g,ua=/_EMO_([^_]+?)_([^]*?)_EMO_/g,xn=function(e){return e.charCodeAt(1)===45},Ut=function(e){return e!=null&&typeof e!="boolean"},lt=_r(function(t){return xn(t)?t:t.replace(sa,"-$&").toLowerCase()}),Bt=function(e,r){switch(e){case"animation":case"animationName":if(typeof r=="string")return r.replace(ua,function(a,n,i){return le={name:n,styles:i,next:le},n})}return oa[e]!==1&&!xn(e)&&typeof r=="number"&&r!==0?r+"px":r};function Ee(t,e,r){if(r==null)return"";var a=r;if(a.__emotion_styles!==void 0)return a;switch(typeof r){case"boolean":return"";case"object":{var n=r;if(n.anim===1)return le={name:n.name,styles:n.styles,next:le},n.name;var i=r;if(i.styles!==void 0){var s=i.next;if(s!==void 0)for(;s!==void 0;)le={name:s.name,styles:s.styles,next:le},s=s.next;var l=i.styles+";";return l}return la(t,e,r)}case"function":{if(t!==void 0){var o=le,u=r(t);return le=o,Ee(t,e,u)}break}}var c=r;return c}function la(t,e,r){var a="";if(Array.isArray(r))for(var n=0;n<r.length;n++)a+=Ee(t,e,r[n])+";";else for(var i in r){var s=r[i];if(typeof s!="object"){var l=s;Ut(l)&&(a+=lt(i)+":"+Bt(i,l)+";")}else if(Array.isArray(s)&&typeof s[0]=="string"&&e==null)for(var o=0;o<s.length;o++)Ut(s[o])&&(a+=lt(i)+":"+Bt(i,s[o])+";");else{var u=Ee(t,e,s);switch(i){case"animation":case"animationName":{a+=lt(i)+":"+u+";";break}default:a+=i+"{"+u+"}"}}}return a}var zt=/label:\s*([^\s;{]+)\s*(;|$)/g,le;function In(t,e,r){if(t.length===1&&typeof t[0]=="object"&&t[0]!==null&&t[0].styles!==void 0)return t[0];var a=!0,n="";le=void 0;var i=t[0];if(i==null||i.raw===void 0)a=!1,n+=Ee(r,e,i);else{var s=i;n+=s[0]}for(var l=1;l<t.length;l++)if(n+=Ee(r,e,t[l]),a){var o=i;n+=o[l]}zt.lastIndex=0;for(var u="",c;(c=zt.exec(n))!==null;)u+="-"+c[1];var d=ia(n)+u;return{name:d,styles:n,next:le}}var ca=function(e){return e()},da=Ft.useInsertionEffect?Ft.useInsertionEffect:!1,fa=da||ca,Pn=f.createContext(typeof HTMLElement<"u"?Yr({key:"css"}):null);Pn.Provider;var pa=function(e){return f.forwardRef(function(r,a){var n=f.useContext(Pn);return e(r,n,a)})},va=f.createContext({}),Rt={}.hasOwnProperty,yt="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",ha=function(e,r){var a={};for(var n in r)Rt.call(r,n)&&(a[n]=r[n]);return a[yt]=e,a},ma=function(e){var r=e.cache,a=e.serialized,n=e.isStringTag;return wn(r,a,n),fa(function(){return aa(r,a,n)}),null},ga=pa(function(t,e,r){var a=t.css;typeof a=="string"&&e.registered[a]!==void 0&&(a=e.registered[a]);var n=t[yt],i=[a],s="";typeof t.className=="string"?s=ra(e.registered,i,t.className):t.className!=null&&(s=t.className+" ");var l=In(i,void 0,f.useContext(va));s+=e.key+"-"+l.name;var o={};for(var u in t)Rt.call(t,u)&&u!=="css"&&u!==yt&&(o[u]=t[u]);return o.className=s,r&&(o.ref=r),f.createElement(f.Fragment,null,f.createElement(ma,{cache:e,serialized:l,isStringTag:typeof n=="string"}),f.createElement(n,o))}),ba=ga,w=function(e,r){var a=arguments;if(r==null||!Rt.call(r,"css"))return f.createElement.apply(void 0,a);var n=a.length,i=new Array(n);i[0]=ba,i[1]=ha(e,r);for(var s=2;s<n;s++)i[s]=a[s];return f.createElement.apply(null,i)};(function(t){var e;e||(e=t.JSX||(t.JSX={}))})(w||(w={}));function Lt(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return In(e)}function Sa(){var t=Lt.apply(void 0,arguments),e="animation-"+t.name;return{name:e,styles:"@keyframes "+e+"{"+t.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}function ya(t){if(Array.isArray(t))return t}function Oa(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var a,n,i,s,l=[],o=!0,u=!1;try{if(i=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;o=!1}else for(;!(o=(a=i.call(r)).done)&&(l.push(a.value),l.length!==e);o=!0);}catch(c){u=!0,n=c}finally{try{if(!o&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw n}}return l}}function Ca(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function N(t,e){return ya(t)||Oa(t,e)||ln(t,e)||Ca()}function wa(t,e){if(t==null)return{};var r={};for(var a in t)if({}.hasOwnProperty.call(t,a)){if(e.indexOf(a)!==-1)continue;r[a]=t[a]}return r}function de(t,e){if(t==null)return{};var r,a,n=wa(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(a=0;a<i.length;a++)r=i[a],e.indexOf(r)===-1&&{}.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function xa(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}var Ot=f.useLayoutEffect,Ia=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],Be=function(){};function Pa(t,e){return e?e[0]==="-"?t+e:t+"__"+e:t}function Ma(t,e){for(var r=arguments.length,a=new Array(r>2?r-2:0),n=2;n<r;n++)a[n-2]=arguments[n];var i=[].concat(a);if(e&&t)for(var s in e)e.hasOwnProperty(s)&&e[s]&&i.push("".concat(Pa(t,s)));return i.filter(function(l){return l}).map(function(l){return String(l).trim()}).join(" ")}var Wt=function(e){return Aa(e)?e.filter(Boolean):Pe(e)==="object"&&e!==null?[e]:[]},Mn=function(e){e.className,e.clearValue,e.cx,e.getStyles,e.getClassNames,e.getValue,e.hasValue,e.isMulti,e.isRtl,e.options,e.selectOption,e.selectProps,e.setValue,e.theme;var r=de(e,Ia);return C({},r)},k=function(e,r,a){var n=e.cx,i=e.getStyles,s=e.getClassNames,l=e.className;return{css:i(r,e),className:n(a??{},s(r,e),l)}};function Va(t,e,r){if(r){var a=r(t,e);if(typeof a=="string")return a}return t}function it(t){return[document.documentElement,document.body,window].indexOf(t)>-1}function Ea(t){return it(t)?window.innerHeight:t.clientHeight}function Vn(t){return it(t)?window.pageYOffset:t.scrollTop}function ze(t,e){if(it(t)){window.scrollTo(0,e);return}t.scrollTop=e}function Ra(t){var e=getComputedStyle(t),r=e.position==="absolute",a=/(auto|scroll)/;if(e.position==="fixed")return document.documentElement;for(var n=t;n=n.parentElement;)if(e=getComputedStyle(n),!(r&&e.position==="static")&&a.test(e.overflow+e.overflowY+e.overflowX))return n;return document.documentElement}function La(t,e,r,a){return r*((t=t/a-1)*t*t+1)+e}function Fe(t,e){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:200,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:Be,n=Vn(t),i=e-n,s=10,l=0;function o(){l+=s;var u=La(l,n,i,r);ze(t,u),l<r?window.requestAnimationFrame(o):a(t)}o()}function Gt(t,e){var r=t.getBoundingClientRect(),a=e.getBoundingClientRect(),n=e.offsetHeight/3;a.bottom+n>r.bottom?ze(t,Math.min(e.offsetTop+e.clientHeight-t.offsetHeight+n,t.scrollHeight)):a.top-n<r.top&&ze(t,Math.max(e.offsetTop-n,0))}function Ta(t){var e=t.getBoundingClientRect();return{bottom:e.bottom,height:e.height,left:e.left,right:e.right,top:e.top,width:e.width}}function Yt(){try{return document.createEvent("TouchEvent"),!0}catch{return!1}}function Da(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch{return!1}}var En=!1,Fa={get passive(){return En=!0}},$e=typeof window<"u"?window:{};$e.addEventListener&&$e.removeEventListener&&($e.addEventListener("p",Be,Fa),$e.removeEventListener("p",Be,!1));var $a=En;function ka(t){return t!=null}function Aa(t){return Array.isArray(t)}function ke(t,e,r){return t?e:r}var Ha=function(e){for(var r=arguments.length,a=new Array(r>1?r-1:0),n=1;n<r;n++)a[n-1]=arguments[n];var i=Object.entries(e).filter(function(s){var l=N(s,1),o=l[0];return!a.includes(o)});return i.reduce(function(s,l){var o=N(l,2),u=o[0],c=o[1];return s[u]=c,s},{})},_a=["children","innerProps"],Na=["children","innerProps"];function ja(t){var e=t.maxHeight,r=t.menuEl,a=t.minHeight,n=t.placement,i=t.shouldScroll,s=t.isFixedPosition,l=t.controlHeight,o=Ra(r),u={placement:"bottom",maxHeight:e};if(!r||!r.offsetParent)return u;var c=o.getBoundingClientRect(),d=c.height,g=r.getBoundingClientRect(),b=g.bottom,S=g.height,m=g.top,v=r.offsetParent.getBoundingClientRect(),h=v.top,p=s?window.innerHeight:Ea(o),y=Vn(o),I=parseInt(getComputedStyle(r).marginBottom,10),V=parseInt(getComputedStyle(r).marginTop,10),M=h-V,O=p-m,P=M+y,T=d-y-m,F=b-p+y+I,B=y+m-V,_=160;switch(n){case"auto":case"bottom":if(O>=S)return{placement:"bottom",maxHeight:e};if(T>=S&&!s)return i&&Fe(o,F,_),{placement:"bottom",maxHeight:e};if(!s&&T>=a||s&&O>=a){i&&Fe(o,F,_);var J=s?O-I:T-I;return{placement:"bottom",maxHeight:J}}if(n==="auto"||s){var Z=e,A=s?M:P;return A>=a&&(Z=Math.min(A-I-l,e)),{placement:"top",maxHeight:Z}}if(n==="bottom")return i&&ze(o,F),{placement:"bottom",maxHeight:e};break;case"top":if(M>=S)return{placement:"top",maxHeight:e};if(P>=S&&!s)return i&&Fe(o,B,_),{placement:"top",maxHeight:e};if(!s&&P>=a||s&&M>=a){var $=e;return(!s&&P>=a||s&&M>=a)&&($=s?M-V:P-V),i&&Fe(o,B,_),{placement:"top",maxHeight:$}}return{placement:"bottom",maxHeight:e};default:throw new Error('Invalid placement provided "'.concat(n,'".'))}return u}function Ua(t){var e={bottom:"top",top:"bottom"};return t?e[t]:"bottom"}var Rn=function(e){return e==="auto"?"bottom":e},Ba=function(e,r){var a,n=e.placement,i=e.theme,s=i.borderRadius,l=i.spacing,o=i.colors;return C((a={label:"menu"},me(a,Ua(n),"100%"),me(a,"position","absolute"),me(a,"width","100%"),me(a,"zIndex",1),a),r?{}:{backgroundColor:o.neutral0,borderRadius:s,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:l.menuGutter,marginTop:l.menuGutter})},Ln=f.createContext(null),za=function(e){var r=e.children,a=e.minMenuHeight,n=e.maxMenuHeight,i=e.menuPlacement,s=e.menuPosition,l=e.menuShouldScrollIntoView,o=e.theme,u=f.useContext(Ln)||{},c=u.setPortalPlacement,d=f.useRef(null),g=f.useState(n),b=N(g,2),S=b[0],m=b[1],v=f.useState(null),h=N(v,2),p=h[0],y=h[1],I=o.spacing.controlHeight;return Ot(function(){var V=d.current;if(V){var M=s==="fixed",O=l&&!M,P=ja({maxHeight:n,menuEl:V,minHeight:a,placement:i,shouldScroll:O,isFixedPosition:M,controlHeight:I});m(P.maxHeight),y(P.placement),c==null||c(P.placement)}},[n,i,s,l,a,c,I]),r({ref:d,placerProps:C(C({},e),{},{placement:p||Rn(i),maxHeight:S})})},Wa=function(e){var r=e.children,a=e.innerRef,n=e.innerProps;return w("div",x({},k(e,"menu",{menu:!0}),{ref:a},n),r)},Ga=Wa,Ya=function(e,r){var a=e.maxHeight,n=e.theme.spacing.baseUnit;return C({maxHeight:a,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},r?{}:{paddingBottom:n,paddingTop:n})},Ka=function(e){var r=e.children,a=e.innerProps,n=e.innerRef,i=e.isMulti;return w("div",x({},k(e,"menuList",{"menu-list":!0,"menu-list--is-multi":i}),{ref:n},a),r)},Tn=function(e,r){var a=e.theme,n=a.spacing.baseUnit,i=a.colors;return C({textAlign:"center"},r?{}:{color:i.neutral40,padding:"".concat(n*2,"px ").concat(n*3,"px")})},qa=Tn,Xa=Tn,Ja=function(e){var r=e.children,a=r===void 0?"No options":r,n=e.innerProps,i=de(e,_a);return w("div",x({},k(C(C({},i),{},{children:a,innerProps:n}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),n),a)},Za=function(e){var r=e.children,a=r===void 0?"Loading...":r,n=e.innerProps,i=de(e,Na);return w("div",x({},k(C(C({},i),{},{children:a,innerProps:n}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),n),a)},Qa=function(e){var r=e.rect,a=e.offset,n=e.position;return{left:r.left,position:n,top:a,width:r.width,zIndex:1}},ei=function(e){var r=e.appendTo,a=e.children,n=e.controlElement,i=e.innerProps,s=e.menuPlacement,l=e.menuPosition,o=f.useRef(null),u=f.useRef(null),c=f.useState(Rn(s)),d=N(c,2),g=d[0],b=d[1],S=f.useMemo(function(){return{setPortalPlacement:b}},[]),m=f.useState(null),v=N(m,2),h=v[0],p=v[1],y=f.useCallback(function(){if(n){var O=Ta(n),P=l==="fixed"?0:window.pageYOffset,T=O[g]+P;(T!==(h==null?void 0:h.offset)||O.left!==(h==null?void 0:h.rect.left)||O.width!==(h==null?void 0:h.rect.width))&&p({offset:T,rect:O})}},[n,l,g,h==null?void 0:h.offset,h==null?void 0:h.rect.left,h==null?void 0:h.rect.width]);Ot(function(){y()},[y]);var I=f.useCallback(function(){typeof u.current=="function"&&(u.current(),u.current=null),n&&o.current&&(u.current=er(n,o.current,y,{elementResize:"ResizeObserver"in window}))},[n,y]);Ot(function(){I()},[I]);var V=f.useCallback(function(O){o.current=O,I()},[I]);if(!r&&l!=="fixed"||!h)return null;var M=w("div",x({ref:V},k(C(C({},e),{},{offset:h.offset,position:l,rect:h.rect}),"menuPortal",{"menu-portal":!0}),i),a);return w(Ln.Provider,{value:S},r?tr.createPortal(M,r):M)},ti=function(e){var r=e.isDisabled,a=e.isRtl;return{label:"container",direction:a?"rtl":void 0,pointerEvents:r?"none":void 0,position:"relative"}},ni=function(e){var r=e.children,a=e.innerProps,n=e.isDisabled,i=e.isRtl;return w("div",x({},k(e,"container",{"--is-disabled":n,"--is-rtl":i}),a),r)},ri=function(e,r){var a=e.theme.spacing,n=e.isMulti,i=e.hasValue,s=e.selectProps.controlShouldRenderValue;return C({alignItems:"center",display:n&&i&&s?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},r?{}:{padding:"".concat(a.baseUnit/2,"px ").concat(a.baseUnit*2,"px")})},ai=function(e){var r=e.children,a=e.innerProps,n=e.isMulti,i=e.hasValue;return w("div",x({},k(e,"valueContainer",{"value-container":!0,"value-container--is-multi":n,"value-container--has-value":i}),a),r)},ii=function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},oi=function(e){var r=e.children,a=e.innerProps;return w("div",x({},k(e,"indicatorsContainer",{indicators:!0}),a),r)},Kt,si=["size"],ui=["innerProps","isRtl","size"],li={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},Dn=function(e){var r=e.size,a=de(e,si);return w("svg",x({height:r,width:r,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:li},a))},Tt=function(e){return w(Dn,x({size:20},e),w("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},Fn=function(e){return w(Dn,x({size:20},e),w("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},$n=function(e,r){var a=e.isFocused,n=e.theme,i=n.spacing.baseUnit,s=n.colors;return C({label:"indicatorContainer",display:"flex",transition:"color 150ms"},r?{}:{color:a?s.neutral60:s.neutral20,padding:i*2,":hover":{color:a?s.neutral80:s.neutral40}})},ci=$n,di=function(e){var r=e.children,a=e.innerProps;return w("div",x({},k(e,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),a),r||w(Fn,null))},fi=$n,pi=function(e){var r=e.children,a=e.innerProps;return w("div",x({},k(e,"clearIndicator",{indicator:!0,"clear-indicator":!0}),a),r||w(Tt,null))},vi=function(e,r){var a=e.isDisabled,n=e.theme,i=n.spacing.baseUnit,s=n.colors;return C({label:"indicatorSeparator",alignSelf:"stretch",width:1},r?{}:{backgroundColor:a?s.neutral10:s.neutral20,marginBottom:i*2,marginTop:i*2})},hi=function(e){var r=e.innerProps;return w("span",x({},r,k(e,"indicatorSeparator",{"indicator-separator":!0})))},mi=Sa(Kt||(Kt=xa([`
  0%, 80%, 100% { opacity: 0; }
  40% { opacity: 1; }
`]))),gi=function(e,r){var a=e.isFocused,n=e.size,i=e.theme,s=i.colors,l=i.spacing.baseUnit;return C({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:n,lineHeight:1,marginRight:n,textAlign:"center",verticalAlign:"middle"},r?{}:{color:a?s.neutral60:s.neutral20,padding:l*2})},ct=function(e){var r=e.delay,a=e.offset;return w("span",{css:Lt({animation:"".concat(mi," 1s ease-in-out ").concat(r,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:a?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},bi=function(e){var r=e.innerProps,a=e.isRtl,n=e.size,i=n===void 0?4:n,s=de(e,ui);return w("div",x({},k(C(C({},s),{},{innerProps:r,isRtl:a,size:i}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),r),w(ct,{delay:0,offset:a}),w(ct,{delay:160,offset:!0}),w(ct,{delay:320,offset:!a}))},Si=function(e,r){var a=e.isDisabled,n=e.isFocused,i=e.theme,s=i.colors,l=i.borderRadius,o=i.spacing;return C({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:o.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},r?{}:{backgroundColor:a?s.neutral5:s.neutral0,borderColor:a?s.neutral10:n?s.primary:s.neutral20,borderRadius:l,borderStyle:"solid",borderWidth:1,boxShadow:n?"0 0 0 1px ".concat(s.primary):void 0,"&:hover":{borderColor:n?s.primary:s.neutral30}})},yi=function(e){var r=e.children,a=e.isDisabled,n=e.isFocused,i=e.innerRef,s=e.innerProps,l=e.menuIsOpen;return w("div",x({ref:i},k(e,"control",{control:!0,"control--is-disabled":a,"control--is-focused":n,"control--menu-is-open":l}),s,{"aria-disabled":a||void 0}),r)},Oi=yi,Ci=["data"],wi=function(e,r){var a=e.theme.spacing;return r?{}:{paddingBottom:a.baseUnit*2,paddingTop:a.baseUnit*2}},xi=function(e){var r=e.children,a=e.cx,n=e.getStyles,i=e.getClassNames,s=e.Heading,l=e.headingProps,o=e.innerProps,u=e.label,c=e.theme,d=e.selectProps;return w("div",x({},k(e,"group",{group:!0}),o),w(s,x({},l,{selectProps:d,theme:c,getStyles:n,getClassNames:i,cx:a}),u),w("div",null,r))},Ii=function(e,r){var a=e.theme,n=a.colors,i=a.spacing;return C({label:"group",cursor:"default",display:"block"},r?{}:{color:n.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:i.baseUnit*3,paddingRight:i.baseUnit*3,textTransform:"uppercase"})},Pi=function(e){var r=Mn(e);r.data;var a=de(r,Ci);return w("div",x({},k(e,"groupHeading",{"group-heading":!0}),a))},Mi=xi,Vi=["innerRef","isDisabled","isHidden","inputClassName"],Ei=function(e,r){var a=e.isDisabled,n=e.value,i=e.theme,s=i.spacing,l=i.colors;return C(C({visibility:a?"hidden":"visible",transform:n?"translateZ(0)":""},Ri),r?{}:{margin:s.baseUnit/2,paddingBottom:s.baseUnit/2,paddingTop:s.baseUnit/2,color:l.neutral80})},kn={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},Ri={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":C({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},kn)},Li=function(e){return C({label:"input",color:"inherit",background:0,opacity:e?0:1,width:"100%"},kn)},Ti=function(e){var r=e.cx,a=e.value,n=Mn(e),i=n.innerRef,s=n.isDisabled,l=n.isHidden,o=n.inputClassName,u=de(n,Vi);return w("div",x({},k(e,"input",{"input-container":!0}),{"data-value":a||""}),w("input",x({className:r({input:!0},o),ref:i,style:Li(l),disabled:s},u)))},Di=Ti,Fi=function(e,r){var a=e.theme,n=a.spacing,i=a.borderRadius,s=a.colors;return C({label:"multiValue",display:"flex",minWidth:0},r?{}:{backgroundColor:s.neutral10,borderRadius:i/2,margin:n.baseUnit/2})},$i=function(e,r){var a=e.theme,n=a.borderRadius,i=a.colors,s=e.cropWithEllipsis;return C({overflow:"hidden",textOverflow:s||s===void 0?"ellipsis":void 0,whiteSpace:"nowrap"},r?{}:{borderRadius:n/2,color:i.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},ki=function(e,r){var a=e.theme,n=a.spacing,i=a.borderRadius,s=a.colors,l=e.isFocused;return C({alignItems:"center",display:"flex"},r?{}:{borderRadius:i/2,backgroundColor:l?s.dangerLight:void 0,paddingLeft:n.baseUnit,paddingRight:n.baseUnit,":hover":{backgroundColor:s.dangerLight,color:s.danger}})},An=function(e){var r=e.children,a=e.innerProps;return w("div",a,r)},Ai=An,Hi=An;function _i(t){var e=t.children,r=t.innerProps;return w("div",x({role:"button"},r),e||w(Tt,{size:14}))}var Ni=function(e){var r=e.children,a=e.components,n=e.data,i=e.innerProps,s=e.isDisabled,l=e.removeProps,o=e.selectProps,u=a.Container,c=a.Label,d=a.Remove;return w(u,{data:n,innerProps:C(C({},k(e,"multiValue",{"multi-value":!0,"multi-value--is-disabled":s})),i),selectProps:o},w(c,{data:n,innerProps:C({},k(e,"multiValueLabel",{"multi-value__label":!0})),selectProps:o},r),w(d,{data:n,innerProps:C(C({},k(e,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(r||"option")},l),selectProps:o}))},ji=Ni,Ui=function(e,r){var a=e.isDisabled,n=e.isFocused,i=e.isSelected,s=e.theme,l=s.spacing,o=s.colors;return C({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},r?{}:{backgroundColor:i?o.primary:n?o.primary25:"transparent",color:a?o.neutral20:i?o.neutral0:"inherit",padding:"".concat(l.baseUnit*2,"px ").concat(l.baseUnit*3,"px"),":active":{backgroundColor:a?void 0:i?o.primary:o.primary50}})},Bi=function(e){var r=e.children,a=e.isDisabled,n=e.isFocused,i=e.isSelected,s=e.innerRef,l=e.innerProps;return w("div",x({},k(e,"option",{option:!0,"option--is-disabled":a,"option--is-focused":n,"option--is-selected":i}),{ref:s,"aria-disabled":a},l),r)},zi=Bi,Wi=function(e,r){var a=e.theme,n=a.spacing,i=a.colors;return C({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},r?{}:{color:i.neutral50,marginLeft:n.baseUnit/2,marginRight:n.baseUnit/2})},Gi=function(e){var r=e.children,a=e.innerProps;return w("div",x({},k(e,"placeholder",{placeholder:!0}),a),r)},Yi=Gi,Ki=function(e,r){var a=e.isDisabled,n=e.theme,i=n.spacing,s=n.colors;return C({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},r?{}:{color:a?s.neutral40:s.neutral80,marginLeft:i.baseUnit/2,marginRight:i.baseUnit/2})},qi=function(e){var r=e.children,a=e.isDisabled,n=e.innerProps;return w("div",x({},k(e,"singleValue",{"single-value":!0,"single-value--is-disabled":a}),n),r)},Xi=qi,Ji={ClearIndicator:pi,Control:Oi,DropdownIndicator:di,DownChevron:Fn,CrossIcon:Tt,Group:Mi,GroupHeading:Pi,IndicatorsContainer:oi,IndicatorSeparator:hi,Input:Di,LoadingIndicator:bi,Menu:Ga,MenuList:Ka,MenuPortal:ei,LoadingMessage:Za,NoOptionsMessage:Ja,MultiValue:ji,MultiValueContainer:Ai,MultiValueLabel:Hi,MultiValueRemove:_i,Option:zi,Placeholder:Yi,SelectContainer:ni,SingleValue:Xi,ValueContainer:ai},Zi=function(e){return C(C({},Ji),e.components)},qt=Number.isNaN||function(e){return typeof e=="number"&&e!==e};function Qi(t,e){return!!(t===e||qt(t)&&qt(e))}function eo(t,e){if(t.length!==e.length)return!1;for(var r=0;r<t.length;r++)if(!Qi(t[r],e[r]))return!1;return!0}function to(t,e){e===void 0&&(e=eo);var r=null;function a(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];if(r&&r.lastThis===this&&e(n,r.lastArgs))return r.lastResult;var s=t.apply(this,n);return r={lastResult:s,lastArgs:n,lastThis:this},s}return a.clear=function(){r=null},a}var no={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},ro=function(e){return w("span",x({css:no},e))},Xt=ro,ao={guidance:function(e){var r=e.isSearchable,a=e.isMulti,n=e.tabSelectsValue,i=e.context,s=e.isInitialFocus;switch(i){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(n?", press Tab to select the option and exit the menu":"",".");case"input":return s?"".concat(e["aria-label"]||"Select"," is focused ").concat(r?",type to refine list":"",", press Down to open the menu, ").concat(a?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(e){var r=e.action,a=e.label,n=a===void 0?"":a,i=e.labels,s=e.isDisabled;switch(r){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(n,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(i.length>1?"s":""," ").concat(i.join(","),", selected.");case"select-option":return s?"option ".concat(n," is disabled. Select another option."):"option ".concat(n,", selected.");default:return""}},onFocus:function(e){var r=e.context,a=e.focused,n=e.options,i=e.label,s=i===void 0?"":i,l=e.selectValue,o=e.isDisabled,u=e.isSelected,c=e.isAppleDevice,d=function(m,v){return m&&m.length?"".concat(m.indexOf(v)+1," of ").concat(m.length):""};if(r==="value"&&l)return"value ".concat(s," focused, ").concat(d(l,a),".");if(r==="menu"&&c){var g=o?" disabled":"",b="".concat(u?" selected":"").concat(g);return"".concat(s).concat(b,", ").concat(d(n,a),".")}return""},onFilter:function(e){var r=e.inputValue,a=e.resultsMessage;return"".concat(a).concat(r?" for search term "+r:"",".")}},io=function(e){var r=e.ariaSelection,a=e.focusedOption,n=e.focusedValue,i=e.focusableOptions,s=e.isFocused,l=e.selectValue,o=e.selectProps,u=e.id,c=e.isAppleDevice,d=o.ariaLiveMessages,g=o.getOptionLabel,b=o.inputValue,S=o.isMulti,m=o.isOptionDisabled,v=o.isSearchable,h=o.menuIsOpen,p=o.options,y=o.screenReaderStatus,I=o.tabSelectsValue,V=o.isLoading,M=o["aria-label"],O=o["aria-live"],P=f.useMemo(function(){return C(C({},ao),d||{})},[d]),T=f.useMemo(function(){var A="";if(r&&P.onChange){var $=r.option,ne=r.options,z=r.removedValue,oe=r.removedValues,ie=r.value,fe=function(ae){return Array.isArray(ae)?null:ae},D=z||$||fe(ie),G=D?g(D):"",re=ne||oe||void 0,Y=re?re.map(g):[],K=C({isDisabled:D&&m(D,l),label:G,labels:Y},r);A=P.onChange(K)}return A},[r,P,m,l,g]),F=f.useMemo(function(){var A="",$=a||n,ne=!!(a&&l&&l.includes(a));if($&&P.onFocus){var z={focused:$,label:g($),isDisabled:m($,l),isSelected:ne,options:i,context:$===a?"menu":"value",selectValue:l,isAppleDevice:c};A=P.onFocus(z)}return A},[a,n,g,m,P,i,l,c]),B=f.useMemo(function(){var A="";if(h&&p.length&&!V&&P.onFilter){var $=y({count:i.length});A=P.onFilter({inputValue:b,resultsMessage:$})}return A},[i,b,h,P,p,y,V]),_=(r==null?void 0:r.action)==="initial-input-focus",J=f.useMemo(function(){var A="";if(P.guidance){var $=n?"value":h?"menu":"input";A=P.guidance({"aria-label":M,context:$,isDisabled:a&&m(a,l),isMulti:S,isSearchable:v,tabSelectsValue:I,isInitialFocus:_})}return A},[M,a,n,S,m,v,h,P,l,I,_]),Z=w(f.Fragment,null,w("span",{id:"aria-selection"},T),w("span",{id:"aria-focused"},F),w("span",{id:"aria-results"},B),w("span",{id:"aria-guidance"},J));return w(f.Fragment,null,w(Xt,{id:u},_&&Z),w(Xt,{"aria-live":O,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},s&&!_&&Z))},oo=io,Ct=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],so=new RegExp("["+Ct.map(function(t){return t.letters}).join("")+"]","g"),Hn={};for(var dt=0;dt<Ct.length;dt++)for(var ft=Ct[dt],pt=0;pt<ft.letters.length;pt++)Hn[ft.letters[pt]]=ft.base;var _n=function(e){return e.replace(so,function(r){return Hn[r]})},uo=to(_n),Jt=function(e){return e.replace(/^\s+|\s+$/g,"")},lo=function(e){return"".concat(e.label," ").concat(e.value)},co=function(e){return function(r,a){if(r.data.__isNew__)return!0;var n=C({ignoreCase:!0,ignoreAccents:!0,stringify:lo,trim:!0,matchFrom:"any"},e),i=n.ignoreCase,s=n.ignoreAccents,l=n.stringify,o=n.trim,u=n.matchFrom,c=o?Jt(a):a,d=o?Jt(l(r)):l(r);return i&&(c=c.toLowerCase(),d=d.toLowerCase()),s&&(c=uo(c),d=_n(d)),u==="start"?d.substr(0,c.length)===c:d.indexOf(c)>-1}},fo=["innerRef"];function po(t){var e=t.innerRef,r=de(t,fo),a=Ha(r,"onExited","in","enter","exit","appear");return w("input",x({ref:e},a,{css:Lt({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var vo=function(e){e.cancelable&&e.preventDefault(),e.stopPropagation()};function ho(t){var e=t.isEnabled,r=t.onBottomArrive,a=t.onBottomLeave,n=t.onTopArrive,i=t.onTopLeave,s=f.useRef(!1),l=f.useRef(!1),o=f.useRef(0),u=f.useRef(null),c=f.useCallback(function(v,h){if(u.current!==null){var p=u.current,y=p.scrollTop,I=p.scrollHeight,V=p.clientHeight,M=u.current,O=h>0,P=I-V-y,T=!1;P>h&&s.current&&(a&&a(v),s.current=!1),O&&l.current&&(i&&i(v),l.current=!1),O&&h>P?(r&&!s.current&&r(v),M.scrollTop=I,T=!0,s.current=!0):!O&&-h>y&&(n&&!l.current&&n(v),M.scrollTop=0,T=!0,l.current=!0),T&&vo(v)}},[r,a,n,i]),d=f.useCallback(function(v){c(v,v.deltaY)},[c]),g=f.useCallback(function(v){o.current=v.changedTouches[0].clientY},[]),b=f.useCallback(function(v){var h=o.current-v.changedTouches[0].clientY;c(v,h)},[c]),S=f.useCallback(function(v){if(v){var h=$a?{passive:!1}:!1;v.addEventListener("wheel",d,h),v.addEventListener("touchstart",g,h),v.addEventListener("touchmove",b,h)}},[b,g,d]),m=f.useCallback(function(v){v&&(v.removeEventListener("wheel",d,!1),v.removeEventListener("touchstart",g,!1),v.removeEventListener("touchmove",b,!1))},[b,g,d]);return f.useEffect(function(){if(e){var v=u.current;return S(v),function(){m(v)}}},[e,S,m]),function(v){u.current=v}}var Zt=["boxSizing","height","overflow","paddingRight","position"],Qt={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function en(t){t.cancelable&&t.preventDefault()}function tn(t){t.stopPropagation()}function nn(){var t=this.scrollTop,e=this.scrollHeight,r=t+this.offsetHeight;t===0?this.scrollTop=1:r===e&&(this.scrollTop=t-1)}function rn(){return"ontouchstart"in window||navigator.maxTouchPoints}var an=!!(typeof window<"u"&&window.document&&window.document.createElement),Ie=0,he={capture:!1,passive:!1};function mo(t){var e=t.isEnabled,r=t.accountForScrollbars,a=r===void 0?!0:r,n=f.useRef({}),i=f.useRef(null),s=f.useCallback(function(o){if(an){var u=document.body,c=u&&u.style;if(a&&Zt.forEach(function(S){var m=c&&c[S];n.current[S]=m}),a&&Ie<1){var d=parseInt(n.current.paddingRight,10)||0,g=document.body?document.body.clientWidth:0,b=window.innerWidth-g+d||0;Object.keys(Qt).forEach(function(S){var m=Qt[S];c&&(c[S]=m)}),c&&(c.paddingRight="".concat(b,"px"))}u&&rn()&&(u.addEventListener("touchmove",en,he),o&&(o.addEventListener("touchstart",nn,he),o.addEventListener("touchmove",tn,he))),Ie+=1}},[a]),l=f.useCallback(function(o){if(an){var u=document.body,c=u&&u.style;Ie=Math.max(Ie-1,0),a&&Ie<1&&Zt.forEach(function(d){var g=n.current[d];c&&(c[d]=g)}),u&&rn()&&(u.removeEventListener("touchmove",en,he),o&&(o.removeEventListener("touchstart",nn,he),o.removeEventListener("touchmove",tn,he)))}},[a]);return f.useEffect(function(){if(e){var o=i.current;return s(o),function(){l(o)}}},[e,s,l]),function(o){i.current=o}}var go=function(e){var r=e.target;return r.ownerDocument.activeElement&&r.ownerDocument.activeElement.blur()},bo={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function So(t){var e=t.children,r=t.lockEnabled,a=t.captureEnabled,n=a===void 0?!0:a,i=t.onBottomArrive,s=t.onBottomLeave,l=t.onTopArrive,o=t.onTopLeave,u=ho({isEnabled:n,onBottomArrive:i,onBottomLeave:s,onTopArrive:l,onTopLeave:o}),c=mo({isEnabled:r}),d=function(b){u(b),c(b)};return w(f.Fragment,null,r&&w("div",{onClick:go,css:bo}),e(d))}var yo={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},Oo=function(e){var r=e.name,a=e.onFocus;return w("input",{required:!0,name:r,tabIndex:-1,"aria-hidden":"true",onFocus:a,css:yo,value:"",onChange:function(){}})},Co=Oo;function Dt(t){var e;return typeof window<"u"&&window.navigator!=null?t.test(((e=window.navigator.userAgentData)===null||e===void 0?void 0:e.platform)||window.navigator.platform):!1}function wo(){return Dt(/^iPhone/i)}function Nn(){return Dt(/^Mac/i)}function xo(){return Dt(/^iPad/i)||Nn()&&navigator.maxTouchPoints>1}function Io(){return wo()||xo()}function Po(){return Nn()||Io()}var Mo=function(e){return e.label},Vo=function(e){return e.label},Eo=function(e){return e.value},Ro=function(e){return!!e.isDisabled},Lo={clearIndicator:fi,container:ti,control:Si,dropdownIndicator:ci,group:wi,groupHeading:Ii,indicatorsContainer:ii,indicatorSeparator:vi,input:Ei,loadingIndicator:gi,loadingMessage:Xa,menu:Ba,menuList:Ya,menuPortal:Qa,multiValue:Fi,multiValueLabel:$i,multiValueRemove:ki,noOptionsMessage:qa,option:Ui,placeholder:Wi,singleValue:Ki,valueContainer:ri},To={primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},Do=4,jn=4,Fo=38,$o=jn*2,ko={baseUnit:jn,controlHeight:Fo,menuGutter:$o},vt={borderRadius:Do,colors:To,spacing:ko},Ao={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:Yt(),captureMenuScroll:!Yt(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:co(),formatGroupLabel:Mo,getOptionLabel:Vo,getOptionValue:Eo,isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:Ro,loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!Da(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(e){var r=e.count;return"".concat(r," result").concat(r!==1?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function on(t,e,r,a){var n=zn(t,e,r),i=Wn(t,e,r),s=Bn(t,e),l=We(t,e);return{type:"option",data:e,isDisabled:n,isSelected:i,label:s,value:l,index:a}}function Ne(t,e){return t.options.map(function(r,a){if("options"in r){var n=r.options.map(function(s,l){return on(t,s,e,l)}).filter(function(s){return un(t,s)});return n.length>0?{type:"group",data:r,options:n,index:a}:void 0}var i=on(t,r,e,a);return un(t,i)?i:void 0}).filter(ka)}function Un(t){return t.reduce(function(e,r){return r.type==="group"?e.push.apply(e,wt(r.options.map(function(a){return a.data}))):e.push(r.data),e},[])}function sn(t,e){return t.reduce(function(r,a){return a.type==="group"?r.push.apply(r,wt(a.options.map(function(n){return{data:n.data,id:"".concat(e,"-").concat(a.index,"-").concat(n.index)}}))):r.push({data:a.data,id:"".concat(e,"-").concat(a.index)}),r},[])}function Ho(t,e){return Un(Ne(t,e))}function un(t,e){var r=t.inputValue,a=r===void 0?"":r,n=e.data,i=e.isSelected,s=e.label,l=e.value;return(!Yn(t)||!i)&&Gn(t,{label:s,value:l,data:n},a)}function _o(t,e){var r=t.focusedValue,a=t.selectValue,n=a.indexOf(r);if(n>-1){var i=e.indexOf(r);if(i>-1)return r;if(n<e.length)return e[n]}return null}function No(t,e){var r=t.focusedOption;return r&&e.indexOf(r)>-1?r:e[0]}var ht=function(e,r){var a,n=(a=e.find(function(i){return i.data===r}))===null||a===void 0?void 0:a.id;return n||null},Bn=function(e,r){return e.getOptionLabel(r)},We=function(e,r){return e.getOptionValue(r)};function zn(t,e,r){return typeof t.isOptionDisabled=="function"?t.isOptionDisabled(e,r):!1}function Wn(t,e,r){if(r.indexOf(e)>-1)return!0;if(typeof t.isOptionSelected=="function")return t.isOptionSelected(e,r);var a=We(t,e);return r.some(function(n){return We(t,n)===a})}function Gn(t,e,r){return t.filterOption?t.filterOption(e,r):!0}var Yn=function(e){var r=e.hideSelectedOptions,a=e.isMulti;return r===void 0?a:r},jo=1,Kn=function(t){ur(r,t);var e=cr(r);function r(a){var n;if(dr(this,r),n=e.call(this,a),n.state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:""},n.blockOptionHover=!1,n.isComposing=!1,n.commonProps=void 0,n.initialTouchX=0,n.initialTouchY=0,n.openAfterFocus=!1,n.scrollToFocusedOptionOnUpdate=!1,n.userIsDragging=void 0,n.isAppleDevice=Po(),n.controlRef=null,n.getControlRef=function(o){n.controlRef=o},n.focusedOptionRef=null,n.getFocusedOptionRef=function(o){n.focusedOptionRef=o},n.menuListRef=null,n.getMenuListRef=function(o){n.menuListRef=o},n.inputRef=null,n.getInputRef=function(o){n.inputRef=o},n.focus=n.focusInput,n.blur=n.blurInput,n.onChange=function(o,u){var c=n.props,d=c.onChange,g=c.name;u.name=g,n.ariaOnChange(o,u),d(o,u)},n.setValue=function(o,u,c){var d=n.props,g=d.closeMenuOnSelect,b=d.isMulti,S=d.inputValue;n.onInputChange("",{action:"set-value",prevInputValue:S}),g&&(n.setState({inputIsHiddenAfterUpdate:!b}),n.onMenuClose()),n.setState({clearFocusValueOnUpdate:!0}),n.onChange(o,{action:u,option:c})},n.selectOption=function(o){var u=n.props,c=u.blurInputOnSelect,d=u.isMulti,g=u.name,b=n.state.selectValue,S=d&&n.isOptionSelected(o,b),m=n.isOptionDisabled(o,b);if(S){var v=n.getOptionValue(o);n.setValue(b.filter(function(h){return n.getOptionValue(h)!==v}),"deselect-option",o)}else if(!m)d?n.setValue([].concat(wt(b),[o]),"select-option",o):n.setValue(o,"select-option");else{n.ariaOnChange(o,{action:"select-option",option:o,name:g});return}c&&n.blurInput()},n.removeValue=function(o){var u=n.props.isMulti,c=n.state.selectValue,d=n.getOptionValue(o),g=c.filter(function(S){return n.getOptionValue(S)!==d}),b=ke(u,g,g[0]||null);n.onChange(b,{action:"remove-value",removedValue:o}),n.focusInput()},n.clearValue=function(){var o=n.state.selectValue;n.onChange(ke(n.props.isMulti,[],null),{action:"clear",removedValues:o})},n.popValue=function(){var o=n.props.isMulti,u=n.state.selectValue,c=u[u.length-1],d=u.slice(0,u.length-1),g=ke(o,d,d[0]||null);c&&n.onChange(g,{action:"pop-value",removedValue:c})},n.getFocusedOptionId=function(o){return ht(n.state.focusableOptionsWithIds,o)},n.getFocusableOptionsWithIds=function(){return sn(Ne(n.props,n.state.selectValue),n.getElementId("option"))},n.getValue=function(){return n.state.selectValue},n.cx=function(){for(var o=arguments.length,u=new Array(o),c=0;c<o;c++)u[c]=arguments[c];return Ma.apply(void 0,[n.props.classNamePrefix].concat(u))},n.getOptionLabel=function(o){return Bn(n.props,o)},n.getOptionValue=function(o){return We(n.props,o)},n.getStyles=function(o,u){var c=n.props.unstyled,d=Lo[o](u,c);d.boxSizing="border-box";var g=n.props.styles[o];return g?g(d,u):d},n.getClassNames=function(o,u){var c,d;return(c=(d=n.props.classNames)[o])===null||c===void 0?void 0:c.call(d,u)},n.getElementId=function(o){return"".concat(n.state.instancePrefix,"-").concat(o)},n.getComponents=function(){return Zi(n.props)},n.buildCategorizedOptions=function(){return Ne(n.props,n.state.selectValue)},n.getCategorizedOptions=function(){return n.props.menuIsOpen?n.buildCategorizedOptions():[]},n.buildFocusableOptions=function(){return Un(n.buildCategorizedOptions())},n.getFocusableOptions=function(){return n.props.menuIsOpen?n.buildFocusableOptions():[]},n.ariaOnChange=function(o,u){n.setState({ariaSelection:C({value:o},u)})},n.onMenuMouseDown=function(o){o.button===0&&(o.stopPropagation(),o.preventDefault(),n.focusInput())},n.onMenuMouseMove=function(o){n.blockOptionHover=!1},n.onControlMouseDown=function(o){if(!o.defaultPrevented){var u=n.props.openMenuOnClick;n.state.isFocused?n.props.menuIsOpen?o.target.tagName!=="INPUT"&&o.target.tagName!=="TEXTAREA"&&n.onMenuClose():u&&n.openMenu("first"):(u&&(n.openAfterFocus=!0),n.focusInput()),o.target.tagName!=="INPUT"&&o.target.tagName!=="TEXTAREA"&&o.preventDefault()}},n.onDropdownIndicatorMouseDown=function(o){if(!(o&&o.type==="mousedown"&&o.button!==0)&&!n.props.isDisabled){var u=n.props,c=u.isMulti,d=u.menuIsOpen;n.focusInput(),d?(n.setState({inputIsHiddenAfterUpdate:!c}),n.onMenuClose()):n.openMenu("first"),o.preventDefault()}},n.onClearIndicatorMouseDown=function(o){o&&o.type==="mousedown"&&o.button!==0||(n.clearValue(),o.preventDefault(),n.openAfterFocus=!1,o.type==="touchend"?n.focusInput():setTimeout(function(){return n.focusInput()}))},n.onScroll=function(o){typeof n.props.closeMenuOnScroll=="boolean"?o.target instanceof HTMLElement&&it(o.target)&&n.props.onMenuClose():typeof n.props.closeMenuOnScroll=="function"&&n.props.closeMenuOnScroll(o)&&n.props.onMenuClose()},n.onCompositionStart=function(){n.isComposing=!0},n.onCompositionEnd=function(){n.isComposing=!1},n.onTouchStart=function(o){var u=o.touches,c=u&&u.item(0);c&&(n.initialTouchX=c.clientX,n.initialTouchY=c.clientY,n.userIsDragging=!1)},n.onTouchMove=function(o){var u=o.touches,c=u&&u.item(0);if(c){var d=Math.abs(c.clientX-n.initialTouchX),g=Math.abs(c.clientY-n.initialTouchY),b=5;n.userIsDragging=d>b||g>b}},n.onTouchEnd=function(o){n.userIsDragging||(n.controlRef&&!n.controlRef.contains(o.target)&&n.menuListRef&&!n.menuListRef.contains(o.target)&&n.blurInput(),n.initialTouchX=0,n.initialTouchY=0)},n.onControlTouchEnd=function(o){n.userIsDragging||n.onControlMouseDown(o)},n.onClearIndicatorTouchEnd=function(o){n.userIsDragging||n.onClearIndicatorMouseDown(o)},n.onDropdownIndicatorTouchEnd=function(o){n.userIsDragging||n.onDropdownIndicatorMouseDown(o)},n.handleInputChange=function(o){var u=n.props.inputValue,c=o.currentTarget.value;n.setState({inputIsHiddenAfterUpdate:!1}),n.onInputChange(c,{action:"input-change",prevInputValue:u}),n.props.menuIsOpen||n.onMenuOpen()},n.onInputFocus=function(o){n.props.onFocus&&n.props.onFocus(o),n.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(n.openAfterFocus||n.props.openMenuOnFocus)&&n.openMenu("first"),n.openAfterFocus=!1},n.onInputBlur=function(o){var u=n.props.inputValue;if(n.menuListRef&&n.menuListRef.contains(document.activeElement)){n.inputRef.focus();return}n.props.onBlur&&n.props.onBlur(o),n.onInputChange("",{action:"input-blur",prevInputValue:u}),n.onMenuClose(),n.setState({focusedValue:null,isFocused:!1})},n.onOptionHover=function(o){if(!(n.blockOptionHover||n.state.focusedOption===o)){var u=n.getFocusableOptions(),c=u.indexOf(o);n.setState({focusedOption:o,focusedOptionId:c>-1?n.getFocusedOptionId(o):null})}},n.shouldHideSelectedOptions=function(){return Yn(n.props)},n.onValueInputFocus=function(o){o.preventDefault(),o.stopPropagation(),n.focus()},n.onKeyDown=function(o){var u=n.props,c=u.isMulti,d=u.backspaceRemovesValue,g=u.escapeClearsValue,b=u.inputValue,S=u.isClearable,m=u.isDisabled,v=u.menuIsOpen,h=u.onKeyDown,p=u.tabSelectsValue,y=u.openMenuOnFocus,I=n.state,V=I.focusedOption,M=I.focusedValue,O=I.selectValue;if(!m&&!(typeof h=="function"&&(h(o),o.defaultPrevented))){switch(n.blockOptionHover=!0,o.key){case"ArrowLeft":if(!c||b)return;n.focusValue("previous");break;case"ArrowRight":if(!c||b)return;n.focusValue("next");break;case"Delete":case"Backspace":if(b)return;if(M)n.removeValue(M);else{if(!d)return;c?n.popValue():S&&n.clearValue()}break;case"Tab":if(n.isComposing||o.shiftKey||!v||!p||!V||y&&n.isOptionSelected(V,O))return;n.selectOption(V);break;case"Enter":if(o.keyCode===229)break;if(v){if(!V||n.isComposing)return;n.selectOption(V);break}return;case"Escape":v?(n.setState({inputIsHiddenAfterUpdate:!1}),n.onInputChange("",{action:"menu-close",prevInputValue:b}),n.onMenuClose()):S&&g&&n.clearValue();break;case" ":if(b)return;if(!v){n.openMenu("first");break}if(!V)return;n.selectOption(V);break;case"ArrowUp":v?n.focusOption("up"):n.openMenu("last");break;case"ArrowDown":v?n.focusOption("down"):n.openMenu("first");break;case"PageUp":if(!v)return;n.focusOption("pageup");break;case"PageDown":if(!v)return;n.focusOption("pagedown");break;case"Home":if(!v)return;n.focusOption("first");break;case"End":if(!v)return;n.focusOption("last");break;default:return}o.preventDefault()}},n.state.instancePrefix="react-select-"+(n.props.instanceId||++jo),n.state.selectValue=Wt(a.value),a.menuIsOpen&&n.state.selectValue.length){var i=n.getFocusableOptionsWithIds(),s=n.buildFocusableOptions(),l=s.indexOf(n.state.selectValue[0]);n.state.focusableOptionsWithIds=i,n.state.focusedOption=s[l],n.state.focusedOptionId=ht(i,s[l])}return n}return pr(r,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&Gt(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(n){var i=this.props,s=i.isDisabled,l=i.menuIsOpen,o=this.state.isFocused;(o&&!s&&n.isDisabled||o&&l&&!n.menuIsOpen)&&this.focusInput(),o&&s&&!n.isDisabled?this.setState({isFocused:!1},this.onMenuClose):!o&&!s&&n.isDisabled&&this.inputRef===document.activeElement&&this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(Gt(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(n,i){this.props.onInputChange(n,i)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(n){var i=this,s=this.state,l=s.selectValue,o=s.isFocused,u=this.buildFocusableOptions(),c=n==="first"?0:u.length-1;if(!this.props.isMulti){var d=u.indexOf(l[0]);d>-1&&(c=d)}this.scrollToFocusedOptionOnUpdate=!(o&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:u[c],focusedOptionId:this.getFocusedOptionId(u[c])},function(){return i.onMenuOpen()})}},{key:"focusValue",value:function(n){var i=this.state,s=i.selectValue,l=i.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var o=s.indexOf(l);l||(o=-1);var u=s.length-1,c=-1;if(s.length){switch(n){case"previous":o===0?c=0:o===-1?c=u:c=o-1;break;case"next":o>-1&&o<u&&(c=o+1);break}this.setState({inputIsHidden:c!==-1,focusedValue:s[c]})}}}},{key:"focusOption",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"first",i=this.props.pageSize,s=this.state.focusedOption,l=this.getFocusableOptions();if(l.length){var o=0,u=l.indexOf(s);s||(u=-1),n==="up"?o=u>0?u-1:l.length-1:n==="down"?o=(u+1)%l.length:n==="pageup"?(o=u-i,o<0&&(o=0)):n==="pagedown"?(o=u+i,o>l.length-1&&(o=l.length-1)):n==="last"&&(o=l.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:l[o],focusedValue:null,focusedOptionId:this.getFocusedOptionId(l[o])})}}},{key:"getTheme",value:function(){return this.props.theme?typeof this.props.theme=="function"?this.props.theme(vt):C(C({},vt),this.props.theme):vt}},{key:"getCommonProps",value:function(){var n=this.clearValue,i=this.cx,s=this.getStyles,l=this.getClassNames,o=this.getValue,u=this.selectOption,c=this.setValue,d=this.props,g=d.isMulti,b=d.isRtl,S=d.options,m=this.hasValue();return{clearValue:n,cx:i,getStyles:s,getClassNames:l,getValue:o,hasValue:m,isMulti:g,isRtl:b,options:S,selectOption:u,selectProps:d,setValue:c,theme:this.getTheme()}}},{key:"hasValue",value:function(){var n=this.state.selectValue;return n.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var n=this.props,i=n.isClearable,s=n.isMulti;return i===void 0?s:i}},{key:"isOptionDisabled",value:function(n,i){return zn(this.props,n,i)}},{key:"isOptionSelected",value:function(n,i){return Wn(this.props,n,i)}},{key:"filterOption",value:function(n,i){return Gn(this.props,n,i)}},{key:"formatOptionLabel",value:function(n,i){if(typeof this.props.formatOptionLabel=="function"){var s=this.props.inputValue,l=this.state.selectValue;return this.props.formatOptionLabel(n,{context:i,inputValue:s,selectValue:l})}else return this.getOptionLabel(n)}},{key:"formatGroupLabel",value:function(n){return this.props.formatGroupLabel(n)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var n=this.props,i=n.isDisabled,s=n.isSearchable,l=n.inputId,o=n.inputValue,u=n.tabIndex,c=n.form,d=n.menuIsOpen,g=n.required,b=this.getComponents(),S=b.Input,m=this.state,v=m.inputIsHidden,h=m.ariaSelection,p=this.commonProps,y=l||this.getElementId("input"),I=C(C(C({"aria-autocomplete":"list","aria-expanded":d,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":g,role:"combobox","aria-activedescendant":this.isAppleDevice?void 0:this.state.focusedOptionId||""},d&&{"aria-controls":this.getElementId("listbox")}),!s&&{"aria-readonly":!0}),this.hasValue()?(h==null?void 0:h.action)==="initial-input-focus"&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return s?f.createElement(S,x({},p,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:y,innerRef:this.getInputRef,isDisabled:i,isHidden:v,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:u,form:c,type:"text",value:o},I)):f.createElement(po,x({id:y,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:Be,onFocus:this.onInputFocus,disabled:i,tabIndex:u,inputMode:"none",form:c,value:""},I))}},{key:"renderPlaceholderOrValue",value:function(){var n=this,i=this.getComponents(),s=i.MultiValue,l=i.MultiValueContainer,o=i.MultiValueLabel,u=i.MultiValueRemove,c=i.SingleValue,d=i.Placeholder,g=this.commonProps,b=this.props,S=b.controlShouldRenderValue,m=b.isDisabled,v=b.isMulti,h=b.inputValue,p=b.placeholder,y=this.state,I=y.selectValue,V=y.focusedValue,M=y.isFocused;if(!this.hasValue()||!S)return h?null:f.createElement(d,x({},g,{key:"placeholder",isDisabled:m,isFocused:M,innerProps:{id:this.getElementId("placeholder")}}),p);if(v)return I.map(function(P,T){var F=P===V,B="".concat(n.getOptionLabel(P),"-").concat(n.getOptionValue(P));return f.createElement(s,x({},g,{components:{Container:l,Label:o,Remove:u},isFocused:F,isDisabled:m,key:B,index:T,removeProps:{onClick:function(){return n.removeValue(P)},onTouchEnd:function(){return n.removeValue(P)},onMouseDown:function(J){J.preventDefault()}},data:P}),n.formatOptionLabel(P,"value"))});if(h)return null;var O=I[0];return f.createElement(c,x({},g,{data:O,isDisabled:m}),this.formatOptionLabel(O,"value"))}},{key:"renderClearIndicator",value:function(){var n=this.getComponents(),i=n.ClearIndicator,s=this.commonProps,l=this.props,o=l.isDisabled,u=l.isLoading,c=this.state.isFocused;if(!this.isClearable()||!i||o||!this.hasValue()||u)return null;var d={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(i,x({},s,{innerProps:d,isFocused:c}))}},{key:"renderLoadingIndicator",value:function(){var n=this.getComponents(),i=n.LoadingIndicator,s=this.commonProps,l=this.props,o=l.isDisabled,u=l.isLoading,c=this.state.isFocused;if(!i||!u)return null;var d={"aria-hidden":"true"};return f.createElement(i,x({},s,{innerProps:d,isDisabled:o,isFocused:c}))}},{key:"renderIndicatorSeparator",value:function(){var n=this.getComponents(),i=n.DropdownIndicator,s=n.IndicatorSeparator;if(!i||!s)return null;var l=this.commonProps,o=this.props.isDisabled,u=this.state.isFocused;return f.createElement(s,x({},l,{isDisabled:o,isFocused:u}))}},{key:"renderDropdownIndicator",value:function(){var n=this.getComponents(),i=n.DropdownIndicator;if(!i)return null;var s=this.commonProps,l=this.props.isDisabled,o=this.state.isFocused,u={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(i,x({},s,{innerProps:u,isDisabled:l,isFocused:o}))}},{key:"renderMenu",value:function(){var n=this,i=this.getComponents(),s=i.Group,l=i.GroupHeading,o=i.Menu,u=i.MenuList,c=i.MenuPortal,d=i.LoadingMessage,g=i.NoOptionsMessage,b=i.Option,S=this.commonProps,m=this.state.focusedOption,v=this.props,h=v.captureMenuScroll,p=v.inputValue,y=v.isLoading,I=v.loadingMessage,V=v.minMenuHeight,M=v.maxMenuHeight,O=v.menuIsOpen,P=v.menuPlacement,T=v.menuPosition,F=v.menuPortalTarget,B=v.menuShouldBlockScroll,_=v.menuShouldScrollIntoView,J=v.noOptionsMessage,Z=v.onMenuScrollToTop,A=v.onMenuScrollToBottom;if(!O)return null;var $=function(G,re){var Y=G.type,K=G.data,se=G.isDisabled,ae=G.isSelected,pe=G.label,ot=G.value,Le=m===K,ye=se?void 0:function(){return n.onOptionHover(K)},st=se?void 0:function(){return n.selectOption(K)},Te="".concat(n.getElementId("option"),"-").concat(re),Oe={id:Te,onClick:st,onMouseMove:ye,onMouseOver:ye,tabIndex:-1,role:"option","aria-selected":n.isAppleDevice?void 0:ae};return f.createElement(b,x({},S,{innerProps:Oe,data:K,isDisabled:se,isSelected:ae,key:Te,label:pe,type:Y,value:ot,isFocused:Le,innerRef:Le?n.getFocusedOptionRef:void 0}),n.formatOptionLabel(G.data,"menu"))},ne;if(this.hasOptions())ne=this.getCategorizedOptions().map(function(D){if(D.type==="group"){var G=D.data,re=D.options,Y=D.index,K="".concat(n.getElementId("group"),"-").concat(Y),se="".concat(K,"-heading");return f.createElement(s,x({},S,{key:K,data:G,options:re,Heading:l,headingProps:{id:se,data:D.data},label:n.formatGroupLabel(D.data)}),D.options.map(function(ae){return $(ae,"".concat(Y,"-").concat(ae.index))}))}else if(D.type==="option")return $(D,"".concat(D.index))});else if(y){var z=I({inputValue:p});if(z===null)return null;ne=f.createElement(d,S,z)}else{var oe=J({inputValue:p});if(oe===null)return null;ne=f.createElement(g,S,oe)}var ie={minMenuHeight:V,maxMenuHeight:M,menuPlacement:P,menuPosition:T,menuShouldScrollIntoView:_},fe=f.createElement(za,x({},S,ie),function(D){var G=D.ref,re=D.placerProps,Y=re.placement,K=re.maxHeight;return f.createElement(o,x({},S,ie,{innerRef:G,innerProps:{onMouseDown:n.onMenuMouseDown,onMouseMove:n.onMenuMouseMove},isLoading:y,placement:Y}),f.createElement(So,{captureEnabled:h,onTopArrive:Z,onBottomArrive:A,lockEnabled:B},function(se){return f.createElement(u,x({},S,{innerRef:function(pe){n.getMenuListRef(pe),se(pe)},innerProps:{role:"listbox","aria-multiselectable":S.isMulti,id:n.getElementId("listbox")},isLoading:y,maxHeight:K,focusedOption:m}),ne)}))});return F||T==="fixed"?f.createElement(c,x({},S,{appendTo:F,controlElement:this.controlRef,menuPlacement:P,menuPosition:T}),fe):fe}},{key:"renderFormField",value:function(){var n=this,i=this.props,s=i.delimiter,l=i.isDisabled,o=i.isMulti,u=i.name,c=i.required,d=this.state.selectValue;if(c&&!this.hasValue()&&!l)return f.createElement(Co,{name:u,onFocus:this.onValueInputFocus});if(!(!u||l))if(o)if(s){var g=d.map(function(m){return n.getOptionValue(m)}).join(s);return f.createElement("input",{name:u,type:"hidden",value:g})}else{var b=d.length>0?d.map(function(m,v){return f.createElement("input",{key:"i-".concat(v),name:u,type:"hidden",value:n.getOptionValue(m)})}):f.createElement("input",{name:u,type:"hidden",value:""});return f.createElement("div",null,b)}else{var S=d[0]?this.getOptionValue(d[0]):"";return f.createElement("input",{name:u,type:"hidden",value:S})}}},{key:"renderLiveRegion",value:function(){var n=this.commonProps,i=this.state,s=i.ariaSelection,l=i.focusedOption,o=i.focusedValue,u=i.isFocused,c=i.selectValue,d=this.getFocusableOptions();return f.createElement(oo,x({},n,{id:this.getElementId("live-region"),ariaSelection:s,focusedOption:l,focusedValue:o,isFocused:u,selectValue:c,focusableOptions:d,isAppleDevice:this.isAppleDevice}))}},{key:"render",value:function(){var n=this.getComponents(),i=n.Control,s=n.IndicatorsContainer,l=n.SelectContainer,o=n.ValueContainer,u=this.props,c=u.className,d=u.id,g=u.isDisabled,b=u.menuIsOpen,S=this.state.isFocused,m=this.commonProps=this.getCommonProps();return f.createElement(l,x({},m,{className:c,innerProps:{id:d,onKeyDown:this.onKeyDown},isDisabled:g,isFocused:S}),this.renderLiveRegion(),f.createElement(i,x({},m,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:g,isFocused:S,menuIsOpen:b}),f.createElement(o,x({},m,{isDisabled:g}),this.renderPlaceholderOrValue(),this.renderInput()),f.createElement(s,x({},m,{isDisabled:g}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(n,i){var s=i.prevProps,l=i.clearFocusValueOnUpdate,o=i.inputIsHiddenAfterUpdate,u=i.ariaSelection,c=i.isFocused,d=i.prevWasFocused,g=i.instancePrefix,b=n.options,S=n.value,m=n.menuIsOpen,v=n.inputValue,h=n.isMulti,p=Wt(S),y={};if(s&&(S!==s.value||b!==s.options||m!==s.menuIsOpen||v!==s.inputValue)){var I=m?Ho(n,p):[],V=m?sn(Ne(n,p),"".concat(g,"-option")):[],M=l?_o(i,p):null,O=No(i,I),P=ht(V,O);y={selectValue:p,focusedOption:O,focusedOptionId:P,focusableOptionsWithIds:V,focusedValue:M,clearFocusValueOnUpdate:!1}}var T=o!=null&&n!==s?{inputIsHidden:o,inputIsHiddenAfterUpdate:void 0}:{},F=u,B=c&&d;return c&&!B&&(F={value:ke(h,p,p[0]||null),options:p,action:"initial-input-focus"},B=!d),(u==null?void 0:u.action)==="initial-input-focus"&&(F=null),C(C(C({},y),T),{},{prevProps:n,ariaSelection:F,prevWasFocused:B})}}]),r}(f.Component);Kn.defaultProps=Ao;var Uo=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function Bo(t){var e=t.defaultInputValue,r=e===void 0?"":e,a=t.defaultMenuIsOpen,n=a===void 0?!1:a,i=t.defaultValue,s=i===void 0?null:i,l=t.inputValue,o=t.menuIsOpen,u=t.onChange,c=t.onInputChange,d=t.onMenuClose,g=t.onMenuOpen,b=t.value,S=de(t,Uo),m=f.useState(l!==void 0?l:r),v=N(m,2),h=v[0],p=v[1],y=f.useState(o!==void 0?o:n),I=N(y,2),V=I[0],M=I[1],O=f.useState(b!==void 0?b:s),P=N(O,2),T=P[0],F=P[1],B=f.useCallback(function(z,oe){typeof u=="function"&&u(z,oe),F(z)},[u]),_=f.useCallback(function(z,oe){var ie;typeof c=="function"&&(ie=c(z,oe)),p(ie!==void 0?ie:z)},[c]),J=f.useCallback(function(){typeof g=="function"&&g(),M(!0)},[g]),Z=f.useCallback(function(){typeof d=="function"&&d(),M(!1)},[d]),A=l!==void 0?l:h,$=o!==void 0?o:V,ne=b!==void 0?b:T;return C(C({},S),{},{inputValue:A,menuIsOpen:$,onChange:B,onInputChange:_,onMenuClose:Z,onMenuOpen:J,value:ne})}var zo=["defaultOptions","cacheOptions","loadOptions","options","isLoading","onInputChange","filterOption"];function Wo(t){var e=t.defaultOptions,r=e===void 0?!1:e,a=t.cacheOptions,n=a===void 0?!1:a,i=t.loadOptions;t.options;var s=t.isLoading,l=s===void 0?!1:s,o=t.onInputChange,u=t.filterOption,c=u===void 0?null:u,d=de(t,zo),g=d.inputValue,b=f.useRef(void 0),S=f.useRef(!1),m=f.useState(Array.isArray(r)?r:void 0),v=N(m,2),h=v[0],p=v[1],y=f.useState(typeof g<"u"?g:""),I=N(y,2),V=I[0],M=I[1],O=f.useState(r===!0),P=N(O,2),T=P[0],F=P[1],B=f.useState(void 0),_=N(B,2),J=_[0],Z=_[1],A=f.useState([]),$=N(A,2),ne=$[0],z=$[1],oe=f.useState(!1),ie=N(oe,2),fe=ie[0],D=ie[1],G=f.useState({}),re=N(G,2),Y=re[0],K=re[1],se=f.useState(void 0),ae=N(se,2),pe=ae[0],ot=ae[1],Le=f.useState(void 0),ye=N(Le,2),st=ye[0],Te=ye[1];n!==st&&(K({}),Te(n)),r!==pe&&(p(Array.isArray(r)?r:void 0),ot(r)),f.useEffect(function(){return S.current=!0,function(){S.current=!1}},[]);var Oe=f.useCallback(function(Ce,ve){if(!i)return ve();var Q=i(Ce,ve);Q&&typeof Q.then=="function"&&Q.then(ve,function(){return ve()})},[i]);f.useEffect(function(){r===!0&&Oe(V,function(Ce){S.current&&(p(Ce||[]),F(!!b.current))})},[]);var qn=f.useCallback(function(Ce,ve){var Q=Va(Ce,ve,o);if(!Q){b.current=void 0,M(""),Z(""),z([]),F(!1),D(!1);return}if(n&&Y[Q])M(Q),Z(Q),z(Y[Q]),F(!1),D(!1);else{var Jn=b.current={};M(Q),F(!0),D(!J),Oe(Q,function(ut){S&&Jn===b.current&&(b.current=void 0,F(!1),Z(Q),z(ut||[]),D(!1),K(ut?C(C({},Y),{},me({},Q,ut)):Y))})}},[n,Oe,J,Y,o]),Xn=fe?[]:V&&J?ne:h||[];return C(C({},d),{},{options:Xn,isLoading:T||l,onInputChange:qn,filterOption:c})}var Go=f.forwardRef(function(t,e){var r=Wo(t),a=Bo(r);return f.createElement(Kn,x({ref:e},a))}),Yo=Go;const Zo=({value:t,onChange:e,placeholder:r="Search for a product...",className:a="",disabled:n=!1,required:i=!1,pageSize:s=10,instanceId:l})=>{const{currentOrganization:o}=nr(),[u,c]=f.useState(null),[d,g]=f.useState(!1),b=p=>{let y=p.name;return p.sku&&(y+=` (SKU: ${p.sku})`),p.barcode&&p.barcode!==p.sku&&(y+=` | Barcode: ${p.barcode}`),{value:p.id,label:y,product:p}},S=async p=>{if(!o)return[];try{const{products:y}=await At.searchProducts(o.id,p,1,s);return y.map(b)}catch{return[]}};f.useEffect(()=>{(async()=>{if(!(!t||!o)&&!(u&&u.id===t)){g(!0);try{const y=await At.getProduct(o.id,t);y&&c(y)}catch{}finally{g(!1)}}})()},[t,o,u]);const m=p=>{p?(c(p.product),e(p.value,p.product)):(c(null),e("",null))},v=()=>{c(null),e("",null)},h={control:(p,y)=>({...p,minHeight:"38px",paddingLeft:"30px",borderRadius:"0.5rem",boxShadow:y.isFocused?"0 0 0 1px rgb(59, 130, 246)":"none",borderColor:y.isFocused?"rgb(59, 130, 246)":"rgb(209, 213, 219)","&:hover":{borderColor:y.isFocused?"rgb(59, 130, 246)":"rgb(156, 163, 175)"}}),menu:p=>({...p,borderRadius:"0.5rem",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",zIndex:99999}),menuList:p=>({...p,padding:"5px",maxHeight:"300px"}),option:(p,{isSelected:y,isFocused:I})=>({...p,backgroundColor:y?"rgb(59, 130, 246)":I?"rgb(243, 244, 246)":"white",color:y?"white":"rgb(17, 24, 39)",padding:"8px 12px",cursor:"pointer","&:active":{backgroundColor:y?"rgb(59, 130, 246)":"rgb(229, 231, 235)"}}),input:p=>({...p,color:"rgb(17, 24, 39)"}),singleValue:p=>({...p,color:"rgb(17, 24, 39)"}),placeholder:p=>({...p,color:"rgb(156, 163, 175)"}),indicatorSeparator:p=>({...p,backgroundColor:"rgb(209, 213, 219)"}),dropdownIndicator:p=>({...p,color:"rgb(107, 114, 128)","&:hover":{color:"rgb(75, 85, 99)"}}),clearIndicator:p=>({...p,color:"rgb(107, 114, 128)","&:hover":{color:"rgb(239, 68, 68)"}}),loadingIndicator:p=>({...p,color:"rgb(107, 114, 128)"})};return q.jsx("div",{className:`relative ${a}`,children:u?q.jsxs("div",{className:"flex items-center border rounded-lg p-2 bg-white",children:[q.jsxs("div",{className:"flex-grow",children:[q.jsx("div",{className:"font-medium",children:u.name}),q.jsxs("div",{className:"text-xs text-gray-500",children:[u.sku&&`SKU: ${u.sku}`,u.barcode&&` | Barcode: ${u.barcode}`]})]}),q.jsx(rr,{color:"light",size:"xs",onClick:v,disabled:n,title:"Clear selection",children:q.jsx(ar,{className:"h-4 w-4"})})]}):q.jsxs("div",{className:"relative",children:[q.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-10",children:q.jsx(ir,{className:"h-5 w-5 text-gray-400"})}),q.jsx(Yo,{loadOptions:S,onChange:m,placeholder:r,isDisabled:n,className:"react-select-container",classNamePrefix:"react-select",isLoading:d,isClearable:!0,required:i,menuPlacement:"auto",menuPosition:"fixed",menuPortalTarget:document.body,instanceId:l,formatOptionLabel:p=>q.jsxs("div",{className:"flex flex-col",children:[q.jsx("div",{className:"font-medium",children:p.product.name}),q.jsxs("div",{className:"text-xs text-gray-500",children:[p.product.sku&&`SKU: ${p.product.sku}`,p.product.barcode&&p.product.barcode!==p.product.sku&&` | Barcode: ${p.product.barcode}`]})]}),styles:h,noOptionsMessage:({inputValue:p})=>p.trim()?"No products found":"Type to search for products",cacheOptions:!0,defaultOptions:!0})]})})};export{Zo as E,At as p};
