import{h as z,r as n,j as e,C as R,e as I,B as d,bQ as k,E as V,a1 as $,M as E,V as W,aj as Q,ab as X,d as q,i as M,A as J,o as B,aS as F,a2 as Z,a3 as ee,T as v,a6 as w}from"./index-C6AV3cVN.js";import{C as p}from"./Card-yj7fueH8.js";import{u as se}from"./currencyFormatter-BsFWv3sX.js";import{c as S,d as ae}from"./formatters-Cypx7G-j.js";import{h as l,d as y,i as te,j as le,k as re,P as g,l as ne}from"./payables-q7zOb02j.js";import{P as ce}from"./PageTitle-FHPo8gWi.js";import{A as Y,g as ie}from"./AttachmentGallery-BWWhOZ7l.js";const de=({payment:a,formatWithCurrency:o,formatDate:c,formatDateTime:m,getPaymentMethodLabel:s,isLast:C})=>{const{currentOrganization:x}=z(),[H,r]=n.useState([]),[f,T]=n.useState(0),[A,u]=n.useState(!1),[j,P]=n.useState(!0);n.useEffect(()=>{(async()=>{if(x)try{const h=await ie(x.id,"payment",a.id);h.success&&h.attachments&&(r(h.attachments),T(h.attachments.length))}catch(h){console.error("Error loading attachments:",h)}finally{P(!1)}})()},[x,a.id]);const _=N=>{switch(N){case l.CASH:return e.jsx(R,{className:"w-4 h-4"});case l.CHECK:case l.BANK_TRANSFER:case l.CREDIT_CARD:return e.jsx(Q,{className:"w-4 h-4"});default:return e.jsx(R,{className:"w-4 h-4"})}},D=N=>{switch(N){case l.CASH:return"green";case l.CHECK:return"blue";case l.BANK_TRANSFER:return"purple";case l.CREDIT_CARD:return"orange";case l.GCASH:case l.PAYMAYA:return"cyan";default:return"gray"}};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:`relative ${C?"":"pb-4"}`,children:[!C&&e.jsx("div",{className:"absolute left-6 top-12 w-0.5 h-full bg-gray-200"}),e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center border-4 border-white shadow-sm",children:e.jsx(R,{className:"w-5 h-5 text-green-600"})}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs(p,{className:"shadow-sm hover:shadow-md transition-shadow duration-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("h6",{className:"text-lg font-semibold text-gray-900",children:o(a.amount_paid)}),e.jsxs(I,{color:D(a.payment_method),className:"flex items-center gap-1",children:[_(a.payment_method),s(a.payment_method)]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[f>0&&e.jsxs(d,{size:"xs",color:"light",onClick:()=>u(!0),className:"flex items-center gap-1",children:[e.jsx(k,{className:"w-3 h-3"}),f]}),e.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[e.jsx(V,{className:"w-4 h-4 mr-1"}),c(a.payment_date)]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[a.reference_number&&e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"Reference:"}),e.jsx("p",{className:"font-medium text-gray-900",children:a.reference_number})]}),a.remarks&&e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("span",{className:"text-gray-500",children:"Remarks:"}),e.jsx("p",{className:"text-gray-900",children:a.remarks})]})]}),e.jsxs("div",{className:"mt-3 pt-3 border-t border-gray-100 flex items-center justify-between text-xs text-gray-500",children:[e.jsxs("span",{children:["Created ",m(a.created_at)]}),f>0&&e.jsxs(d,{size:"xs",color:"light",onClick:()=>u(!0),children:[e.jsx($,{className:"w-3 h-3 mr-1"}),"View Attachments"]})]})]})})]})]}),e.jsxs(E,{show:A,onClose:()=>u(!1),size:"4xl",children:[e.jsx(E.Header,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(k,{className:"w-5 h-5"}),"Payment Attachments",e.jsx(I,{color:"gray",children:f})]})}),e.jsx(E.Body,{children:e.jsx(Y,{attachableType:"payment",attachableId:a.id,editable:!0,showUpload:!0,maxFiles:10})}),e.jsx(E.Footer,{children:e.jsxs(d,{color:"light",onClick:()=>u(!1),children:[e.jsx(W,{className:"mr-2 h-4 w-4"}),"Close"]})})]})]})},pe=()=>{const{id:a}=X(),o=q(),{currentOrganization:c}=z(),m=se(),[s,C]=n.useState(null),[x,H]=n.useState([]),[r,f]=n.useState(null),[T,A]=n.useState(!0),[u,j]=n.useState(null),[P,_]=n.useState(!1),D=async()=>{if(!(!c||!a)){A(!0),j(null);try{const{payable:t,error:i}=await te(c.id,a);if(i){j(i);return}if(!t){j("Payable not found");return}C(t);const{payments:b,error:K}=await le(c.id,a);!K&&b&&H(b);const{metadata:O,error:U}=await re(c.id,a);!U&&O&&f(O)}catch(t){j(t.message||"Failed to fetch payable details")}finally{A(!1)}}};n.useEffect(()=>{D()},[c,a]);const N=async()=>{if(!(!c||!a||!s)&&confirm("Are you sure you want to delete this payable? This action cannot be undone.")){_(!0);try{const{success:t,error:i}=await ne(c.id,a);if(i){j(i);return}t&&o("/payables")}catch(t){j(t.message||"Failed to delete payable")}finally{_(!1)}}},h=t=>{const b={[y.DRAFT]:{color:"gray",label:"Draft"},[y.OPEN]:{color:"blue",label:"Open"},[y.PARTIALLY_PAID]:{color:"yellow",label:"Partially Paid"},[y.PAID]:{color:"green",label:"Paid"},[y.CANCELLED]:{color:"red",label:"Cancelled"}}[t];return e.jsx(I,{color:b.color,children:b.label})},L=t=>({[g.PURCHASE_RECEIPT]:"Purchase Receipt",[g.PAYROLL]:"Payroll",[g.UTILITY_BILL]:"Utility Bill",[g.GOVERNMENT_REMITTANCE]:"Government Remittance",[g.LOAN_REPAYMENT]:"Loan Repayment",[g.MANUAL_ENTRY]:"Manual Entry"})[t]||t,G=t=>({[l.CASH]:"Cash",[l.CHECK]:"Check",[l.BANK_TRANSFER]:"Bank Transfer",[l.GCASH]:"GCash",[l.PAYMAYA]:"PayMaya",[l.CREDIT_CARD]:"Credit Card",[l.OTHER]:"Other"})[t]||t;return c?T?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(M,{size:"xl"})}):u||!s?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(J,{color:"failure",children:u||"Payable not found"}),e.jsxs(d,{color:"light",onClick:()=>o("/payables"),className:"mt-4",children:[e.jsx(B,{className:"mr-2 h-5 w-5"}),"Back to Payables"]})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(d,{color:"light",onClick:()=>o("/payables"),children:[e.jsx(B,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(ce,{title:`Payable ${s.reference_number}`,subtitle:L(s.source_type)})]}),e.jsxs("div",{className:"flex gap-2",children:[s.status!==y.PAID&&s.status!==y.CANCELLED&&e.jsxs(e.Fragment,{children:[e.jsxs(d,{color:"primary",onClick:()=>o(`/payables/${s.id}/pay`),children:[e.jsx(F,{className:"mr-2 h-5 w-5"}),"Add Payment"]}),e.jsxs(d,{color:"light",onClick:()=>o(`/payables/${s.id}/edit`),children:[e.jsx(Z,{className:"mr-2 h-5 w-5"}),"Edit"]})]}),e.jsxs(d,{color:"failure",onClick:N,disabled:P||x.length>0,children:[e.jsx(ee,{className:"mr-2 h-5 w-5"}),P?"Deleting...":"Delete"]})]})]}),e.jsxs(v,{"aria-label":"Payable details tabs",variant:"underline",children:[e.jsx(v.Item,{active:!0,title:"Details",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(p,{children:[e.jsx("h5",{className:"text-lg font-bold mb-4",children:"Basic Information"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Reference Number:"}),e.jsx("span",{className:"font-medium",children:s.reference_number})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Status:"}),h(s.status)]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Source Type:"}),e.jsx("span",{children:L(s.source_type)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Invoice Date:"}),e.jsx("span",{children:S(s.invoice_date)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Due Date:"}),e.jsx("span",{children:S(s.due_date)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Currency:"}),e.jsx("span",{children:s.currency})]})]})]}),e.jsxs(p,{children:[e.jsx("h5",{className:"text-lg font-bold mb-4",children:s.supplier?"Supplier Information":"Employee Information"}),s.supplier?e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Name:"}),e.jsx("span",{className:"font-medium",children:s.supplier.name})]}),s.supplier.contact_person&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Contact Person:"}),e.jsx("span",{children:s.supplier.contact_person})]}),s.supplier.email&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Email:"}),e.jsx("span",{children:s.supplier.email})]}),s.supplier.phone&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Phone:"}),e.jsx("span",{children:s.supplier.phone})]})]}):s.employee?e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Name:"}),e.jsxs("span",{className:"font-medium",children:[s.employee.first_name," ",s.employee.last_name]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Employee Number:"}),e.jsx("span",{children:s.employee.employee_number})]})]}):e.jsx("p",{className:"text-gray-500",children:"No supplier or employee information available"})]}),e.jsxs(p,{children:[e.jsx("h5",{className:"text-lg font-bold mb-4",children:"Financial Information"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Gross Amount:"}),e.jsx("span",{className:"font-medium",children:m(s.amount)})]}),s.vat_amount>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"VAT Amount:"}),e.jsx("span",{children:m(s.vat_amount)})]}),s.withholding_tax_amount>0&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Withholding Tax Rate:"}),e.jsxs("span",{children:[s.withholding_tax_rate,"%"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Withholding Tax Amount:"}),e.jsx("span",{children:m(s.withholding_tax_amount)})]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Total Paid:"}),e.jsx("span",{children:m(s.total_paid||0)})]}),e.jsxs("div",{className:"flex justify-between border-t pt-3",children:[e.jsx("span",{className:"text-gray-500 font-medium",children:"Outstanding Balance:"}),e.jsx("span",{className:"font-bold text-lg",children:m(s.balance)})]})]})]}),s.notes&&e.jsxs(p,{children:[e.jsx("h5",{className:"text-lg font-bold mb-4",children:"Notes"}),e.jsx("p",{className:"text-gray-700",children:s.notes})]})]})}),e.jsx(v.Item,{title:"Payment History",children:e.jsxs(p,{children:[e.jsx("h5",{className:"text-lg font-bold mb-4",children:"Payment History"}),x.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center",children:e.jsx(F,{className:"w-8 h-8 text-gray-400"})}),e.jsx("p",{className:"text-gray-500 text-lg mb-2",children:"No payments recorded"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Payments will appear here once they are added"})]}):e.jsx("div",{className:"space-y-4",children:x.map((t,i)=>e.jsx(de,{payment:t,formatWithCurrency:m,formatDate:S,formatDateTime:ae,getPaymentMethodLabel:G,isLast:i===x.length-1},t.id))})]})}),e.jsx(v.Item,{title:"Attachments",children:e.jsx(Y,{attachableType:"payable",attachableId:s.id,editable:!0,showUpload:!0,maxFiles:20})}),e.jsx(v.Item,{title:"Source Details",children:e.jsxs(p,{children:[e.jsx("h5",{className:"text-lg font-bold mb-4",children:"Source Document Information"}),r?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(w,{value:"Source Name"}),r.source_url?e.jsx(d,{color:"light",size:"sm",onClick:()=>o(r.source_url),className:"p-0 text-left justify-start",children:e.jsx("span",{className:"font-medium text-blue-600 hover:text-blue-800",children:r.source_name})}):e.jsx("p",{className:"font-medium text-gray-900",children:r.source_name})]}),e.jsxs("div",{children:[e.jsx(w,{value:"Source Date"}),e.jsx("p",{className:"text-gray-700",children:S(r.source_date)})]})]}),e.jsxs("div",{children:[e.jsx(w,{value:"Description"}),e.jsx("p",{className:"text-gray-700",children:r.source_description})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(w,{value:"Source Amount"}),e.jsx("p",{className:"font-medium text-gray-900",children:m(r.source_amount)})]}),e.jsxs("div",{children:[e.jsx(w,{value:"Source ID"}),e.jsx("p",{className:"text-gray-500 text-sm font-mono",children:r.source_id})]})]}),r.source_url&&e.jsx("div",{className:"pt-4 border-t",children:e.jsx(d,{color:"primary",size:"sm",onClick:()=>o(r.source_url),children:"View Source Document"})})]}):e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-500",children:"Loading source information..."})})]})})]})]}):e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(M,{size:"xl"})})};export{pe as default};
