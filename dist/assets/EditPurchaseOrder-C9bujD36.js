import{s as ee,ab as re,d as se,h as te,bm as ae,r as m,j as e,i as B,A as $,J as A,B as y,aD as M,al as oe,ay as ce,a6 as P,a7 as ne,P as ie,ad as le,a0 as de,_ as c,a3 as ue,a9 as me}from"./index-C6AV3cVN.js";import{C as he}from"./Card-yj7fueH8.js";import{g as pe}from"./supplier-BJDz25mb.js";import{g as xe,u as fe}from"./purchaseOrder-DppPMsdd.js";import{E as je}from"./EnhancedProductSearchSelector-CWWD8-c1.js";import{U as ve}from"./UomSelector-CTe3hN2d.js";import{c as ge}from"./formatters-Cypx7G-j.js";import{u as ye}from"./currencyFormatter-BsFWv3sX.js";import{E as L}from"./EnhancedNumberInput-Bwo1E3yF.js";import"./product-Ca8DWaNR.js";import"./typeof-QjJsDpFa.js";import"./productUom-k6aUg6b7.js";const Ne=async(u,j)=>{if(!u.length||!j)return{};try{const{data:l,error:C}=await ee.from("product_uoms").select(`
        *,
        uom:uom_id (*)
      `).in("product_id",u).eq("organization_id",j);if(C)return console.error("Error fetching product UoMs:",C),{};const p={};return l.forEach(x=>{p[x.product_id]||(p[x.product_id]=[]),p[x.product_id].push(x)}),p}catch(l){return console.error("Error in batchFetchProductUoms:",l),{}}},ke=()=>{const{id:u}=re(),j=se(),{currentOrganization:l}=te(),C=ae(),p=ye(),[x,R]=m.useState(null),[G,U]=m.useState(!0),[E,F]=m.useState(!1),[_,n]=m.useState(null),[w,q]=m.useState(""),[k,D]=m.useState(""),[H,T]=m.useState(""),[d,v]=m.useState([]),[J,Q]=m.useState([]),[W,K]=m.useState([]);m.useEffect(()=>{(async()=>{if(!(!u||!l)){U(!0),n(null);try{const{purchaseOrder:r,error:a}=await xe(l.id,u);if(a){n(a);return}if(!r){n("Purchase order not found");return}if(r.status!=="draft"){n("Only draft purchase orders can be edited");return}R(r),q(r.supplier_id),D(r.expected_delivery_date?ge(r.expected_delivery_date,"yyyy-MM-dd"):""),T(r.notes||"");const t=r.items.map(o=>o.product_id).filter(Boolean);let i={};if(t.length>0&&l)try{const o=await Ne(t,l.id);i=o,Object.entries(o).forEach(([I,f])=>{f&&f.length>0&&C.addToCache(I,f)})}catch(o){console.error("Error preloading product UoMs:",o)}const h=r.items.map(o=>{const I=i[o.product_id]||[],f=I.find(Z=>Z.uom_id===o.uom_id);return{id:o.id,productId:o.product_id,product:o.product,quantity:o.quantity,uomId:o.uom_id,unitPrice:o.unit_price,productUoms:I,conversionFactor:Number(o.conversion_factor??(f==null?void 0:f.conversion_factor)??1)}});v(h);const{suppliers:S,error:N}=await pe(l.id);N?console.error("Error fetching suppliers:",N):Q(S);const{uoms:b,error:z}=await me(l.id);z?console.error("Error fetching UoMs:",z):K(b)}catch(r){n(r.message||"An error occurred while fetching the purchase order")}finally{U(!1)}}})()},[u,l]);const V=()=>{v([...d,{productId:"",quantity:1,uomId:"",unitPrice:0,conversionFactor:1,productUoms:[]}])},X=s=>{v(d.filter((r,a)=>a!==s))},g=(s,r,a)=>{const t=[...d];t[s]={...t[s],[r]:a},v(t)},Y=()=>d.reduce((s,r)=>{var i;const a=((i=r.productUoms.find(h=>h.uom_id===r.uomId))==null?void 0:i.conversion_factor)??r.conversionFactor??1,t=r.quantity*a*r.unitPrice;return s+t},0),O=async s=>{if(s.preventDefault(),!l||!u){n("Organization or purchase order ID not found");return}if(!w){n("Please select a supplier");return}if(d.length===0){n("Please add at least one item");return}for(let r=0;r<d.length;r++){const a=d[r];if(!a.productId){n(`Please select a product for item #${r+1}`);return}if(!a.uomId){n(`Please select a unit of measurement for item #${r+1}`);return}if(a.quantity<=0){n(`Please enter a valid quantity for item #${r+1}`);return}if(a.unitPrice<0){n(`Please enter a valid unit price for item #${r+1}`);return}}F(!0),n(null);try{const{error:r}=await fe(l.id,u,{supplierId:w,expectedDeliveryDate:k||void 0,notes:H||void 0,items:d.map(a=>({...a,conversionFactor:Number(a.conversionFactor||1)}))});r?n(r):j(`/purchases/orders/${u}`)}catch(r){n(r.message||"An error occurred while updating the purchase order")}finally{F(!1)}};return G?e.jsx("div",{className:"container mx-auto px-4 py-8 flex justify-center",children:e.jsx(B,{size:"xl"})}):_?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs($,{color:"failure",icon:A,children:[e.jsx("h3",{className:"font-medium",children:"Error"}),e.jsx("p",{children:_}),e.jsx("div",{className:"mt-4",children:e.jsxs(y,{color:"gray",onClick:()=>j(`/purchases/orders/${u}`),children:[e.jsx(M,{className:"mr-2 h-5 w-5"}),"Back to Purchase Order"]})})]})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(he,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Edit Purchase Order"}),x&&e.jsxs("p",{className:"text-gray-500",children:["Order Number: ",x.order_number]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(y,{color:"gray",onClick:()=>j(`/purchases/orders/${u}`),title:"Go back to purchase order details",children:[e.jsx(M,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(oe,{content:"Save changes",children:e.jsxs(y,{color:"primary",onClick:O,disabled:E,children:[e.jsx(ce,{className:"mr-2 h-5 w-5"}),"Save"]})})]})]}),_&&e.jsx($,{color:"failure",icon:A,className:"mb-4",children:_}),e.jsxs("form",{onSubmit:O,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-1",children:e.jsx(P,{htmlFor:"supplier_id",value:"Supplier",className:"text-sm font-medium"})}),e.jsxs(ne,{id:"supplier_id",value:w,onChange:s=>q(s.target.value),required:!0,children:[e.jsx("option",{value:"",children:"Select a supplier"}),J.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-1",children:e.jsx(P,{htmlFor:"expected_delivery_date",value:"Expected Delivery Date",className:"text-sm font-medium"})}),e.jsx(ie,{id:"expected_delivery_date",type:"date",value:k,onChange:s=>D(s.target.value)})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-1",children:e.jsx(P,{htmlFor:"notes",value:"Notes",className:"text-sm font-medium"})}),e.jsx(le,{id:"notes",value:H,onChange:s=>T(s.target.value),placeholder:"Enter any additional notes",rows:3})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx(P,{value:"Items",className:"text-sm font-medium"}),e.jsxs(y,{size:"xs",color:"primary",onClick:V,children:[e.jsx(de,{className:"mr-2 h-4 w-4"}),"Add Item"]})]}),d.length===0?e.jsx("div",{className:"p-4 bg-gray-50 rounded-lg text-center text-gray-500",children:'No items added. Click "Add Item" to add products to this purchase order.'}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(c,{children:[e.jsxs(c.Head,{children:[e.jsx(c.HeadCell,{children:"Product"}),e.jsx(c.HeadCell,{children:"Quantity"}),e.jsx(c.HeadCell,{children:"Unit"}),e.jsx(c.HeadCell,{children:"Unit Price"}),e.jsx(c.HeadCell,{children:"Total"}),e.jsx(c.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(c.Body,{className:"divide-y",children:d.map((s,r)=>{var a;return e.jsxs(c.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(c.Cell,{children:e.jsx(je,{value:s.productId,onChange:(t,i)=>{if(i){const h=[...d];h[r]={...h[r],productId:t,product:i},v(h)}else g(r,"productId",t)},required:!0,className:"text-sm",placeholder:"Search for a product...",pageSize:5})}),e.jsx(c.Cell,{children:e.jsx(L,{min:"0.01",step:"0.01",value:s.quantity,onChange:t=>g(r,"quantity",parseFloat(t.target.value)||0),onBlur:t=>{(t.target.value===""||parseFloat(t.target.value)<=0)&&g(r,"quantity",1)},required:!0,className:"text-sm",sizing:"sm",autoSelect:!0,preventScrollChange:!0})}),e.jsx(c.Cell,{children:s.productId?e.jsx(ve,{productId:s.productId,value:s.uomId||"",onChange:t=>g(r,"uomId",t),onUomChange:t=>{const i=[...d];i[r]={...i[r],uomId:t.uom_id,conversionFactor:Number(t.conversion_factor??1)},v(i)},filter:"all",disabled:!1,preloadedUoms:(a=s.product)==null?void 0:a.product_uoms},`uom-selector-${s.productId}-${r}`):e.jsx("div",{className:"text-sm text-gray-500 p-2 border border-gray-300 rounded-lg bg-gray-50",children:"Select a product first"})}),e.jsx(c.Cell,{children:e.jsx(L,{min:"0",step:"0.01",value:s.unitPrice,onChange:t=>g(r,"unitPrice",parseFloat(t.target.value)||0),onBlur:t=>{(t.target.value===""||parseFloat(t.target.value)<0)&&g(r,"unitPrice",.01)},required:!0,className:"text-sm",sizing:"sm",autoSelect:!0,preventScrollChange:!0})}),e.jsx(c.Cell,{children:(()=>{var S,N;const t=((S=s.productUoms.find(b=>b.uom_id===s.uomId))==null?void 0:S.conversion_factor)??s.conversionFactor??1,i=s.quantity*t*s.unitPrice,h=((N=W.find(b=>b.id===s.uomId))==null?void 0:N.code)??"unit";return e.jsxs(e.Fragment,{children:[p(i),e.jsxs("div",{className:"text-xs text-gray-500",children:[s.quantity," ",h," × CF:",t," × ",p(s.unitPrice)]})]})})()}),e.jsx(c.Cell,{children:e.jsx(y,{color:"failure",size:"xs",onClick:()=>X(r),children:e.jsx(ue,{className:"h-4 w-4"})})})]},r)})})]})}),e.jsx("div",{className:"flex justify-end mt-4",children:e.jsxs("div",{className:"text-lg font-semibold",children:["Total: ",p(Y())]})})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(y,{type:"submit",color:"primary",disabled:E,onClick:O,children:E?e.jsxs(e.Fragment,{children:[e.jsx(B,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Changes"})})]})]})})};export{ke as default};
