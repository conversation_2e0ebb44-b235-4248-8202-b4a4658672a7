import{b as w,h as O,d as b,r,j as e,i as C,A as S,J as k,B as d,k as E,ca as R,by as A}from"./index-C6AV3cVN.js";import{C as f}from"./Card-yj7fueH8.js";import{F as j}from"./FullLogo-CXg185jT.js";const y={background:"linear-gradient(45deg, rgb(238, 119, 82,0.2), rgb(231, 60, 126,0.2), rgb(35, 166, 213,0.2), rgb(35, 213, 171,0.2))",backgroundSize:"400% 400%",animation:"gradient 15s ease infinite",height:"100vh"},U=()=>{const{user:l}=w(),{setCurrentOrganization:N}=O(),s=b(),[m,z]=r.useState([]),[p,x]=r.useState(!0),[h,g]=r.useState(null);r.useEffect(()=>{(async()=>{if(!l){s("/auth/login");return}try{x(!0),g(null);const t=await R(),n=await Promise.all(t.map(async i=>{try{const{members:o}=await A(i.id),c=o.find(v=>v.user_id===l.id);return{...i,userRole:(c==null?void 0:c.role)||"Unknown"}}catch(o){return console.error(`Error fetching role for org ${i.id}:`,o),{...i,userRole:"Unknown"}}}));z(n),n.length===1&&(console.log("Only one organization found, automatically selecting:",n[0].name),u(n[0]))}catch(t){console.error("Error fetching organizations:",t),g(t.message||"Failed to load organizations")}finally{x(!1)}})()},[l,s]);const u=a=>{localStorage.setItem("selectedOrganizationId",a.id),N(a),s("/")};return p?e.jsx("div",{style:y,className:"relative overflow-hidden h-screen",children:e.jsx("div",{className:"flex h-full justify-center items-center px-4",children:e.jsx(f,{className:"w-full max-w-md",children:e.jsxs("div",{className:"flex flex-col items-center justify-center p-4",children:[e.jsx(j,{}),e.jsx("h2",{className:"text-xl font-bold mt-4 mb-2",children:"Loading Organizations"}),e.jsx(C,{size:"xl"}),e.jsx("p",{className:"mt-4 text-gray-500",children:"Please wait while we load your organizations..."})]})})})}):e.jsx("div",{style:y,className:"relative overflow-hidden h-screen",children:e.jsx("div",{className:"flex h-full justify-center items-center px-4",children:e.jsxs(f,{className:"w-full max-w-md",children:[e.jsxs("div",{className:"flex flex-col items-center mb-4",children:[e.jsx(j,{}),e.jsx("h2",{className:"text-xl font-bold mt-4",children:"Select an Organization"}),e.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"Choose which organization you want to access"})]}),h&&e.jsx(S,{color:"failure",icon:k,className:"mb-4",children:h}),m.length===0?e.jsxs("div",{className:"text-center p-4",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"You don't have access to any organizations."}),e.jsx(d,{color:"primary",onClick:()=>s("/organization/create"),children:"Create an Organization"})]}):e.jsx("div",{className:"space-y-3",children:m.map(a=>e.jsx(d,{color:"light",className:"w-full justify-start p-4 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>u(a),children:e.jsxs("div",{className:"flex items-center",children:[a.logo_url?e.jsx("img",{src:a.logo_url,alt:a.name,className:"w-10 h-10 rounded-full mr-3"}):e.jsx("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3",children:e.jsx(E,{className:"h-5 w-5 text-primary"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-left",children:a.name}),e.jsxs("p",{className:"text-xs text-gray-500 text-left",children:["Role: ",a.userRole]})]})]})},a.id))}),e.jsx("div",{className:"mt-4 text-center",children:e.jsx(d,{color:"light",onClick:()=>s("/organization/create"),children:"Create New Organization"})})]})})})};export{U as default};
