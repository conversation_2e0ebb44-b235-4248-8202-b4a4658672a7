import{b as ne,h as te,am as ie,r as a,j as e,bV as y,B as i,a0 as ce,A as h,i as b,_ as l,e as de,a2 as ue,bP as he,a3 as me,M as c,a6 as S,a7 as xe,P as T,by as je,bW as fe,bX as pe,bY as we}from"./index-C6AV3cVN.js";import{C as ge}from"./Card-yj7fueH8.js";import{P as Pe}from"./Pagination-CVEzfctr.js";const Ne=()=>{var L,q;const{user:m}=ne(),{currentOrganization:t}=te(),{checkPermission:v}=ie(),[x,Y]=a.useState([]),[G,R]=a.useState(!0),[_,C]=a.useState(null),[M,k]=a.useState(null),[E,z]=a.useState(1),[j,K]=a.useState(10),[V,f]=a.useState(!1),[W,p]=a.useState(!1),[r,F]=a.useState(null),[H,I]=a.useState("member"),[w,U]=a.useState(""),[O,$]=a.useState(""),[g,u]=a.useState(!1),[P,o]=a.useState(null),B=async()=>{if(t){R(!0),C(null);try{const{members:s,error:n}=await je(t.id);n?C(n):Y(s)}catch(s){C(s.message||"Failed to fetch organization members")}finally{R(!1)}}};a.useEffect(()=>{B()},[t]);const X=async()=>{var s,n;if(!(!t||!m||!r)){u(!0),o(null);try{const{success:d,error:N}=await fe(t.id,r.user_id,H);d?(f(!1),k(`Role updated successfully for ${(s=r.profile)==null?void 0:s.first_name} ${(n=r.profile)==null?void 0:n.last_name}`),B()):o(N||"Failed to update role")}catch(d){o(d.message||"Failed to update role")}finally{u(!1)}}},D=async()=>{var s,n;if(!(!t||!m||!r)){if(w!==O){o("Passwords do not match");return}if(w.length<8){o("Password must be at least 8 characters long");return}u(!0),o(null);try{const{canReset:d,error:N}=await pe(t.id,m.id,r.user_id);if(!d){o(N||"You do not have permission to reset this user's password"),u(!1);return}const{success:ae,message:le,error:oe}=await we(t.id,m.id,r.user_id,w);ae?(p(!1),k(le||`Password reset successfully for ${(s=r.profile)==null?void 0:s.first_name} ${(n=r.profile)==null?void 0:n.last_name}`)):o(oe||"Failed to reset password")}catch(d){o(d.message||"Failed to reset password")}finally{u(!1)}}},J=s=>{F(s),I(s.role),o(null),f(!0)},Q=s=>{F(s),U(""),$(""),o(null),p(!0)},Z=s=>{switch(s){case"owner":return"purple";case"admin":return"red";case"cashier":return"green";case"inventory_manager":return"blue";case"purchaser":return"yellow";case"employee":return"gray";default:return"indigo"}},ee=Math.ceil(x.length/j),A=E*j,se=A-j,re=x.slice(se,A);return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(ge,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"User Management"}),e.jsx(y,{module:"users",action:"create",children:e.jsxs(i,{color:"primary",size:"sm",children:[e.jsx(ce,{className:"mr-2 h-4 w-4"}),"Invite User"]})})]}),_&&e.jsx(h,{color:"failure",className:"mb-4",children:_}),M&&e.jsx(h,{color:"success",className:"mb-4",children:M}),G?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(b,{size:"xl"})}):e.jsxs(l,{children:[e.jsxs(l.Head,{children:[e.jsx(l.HeadCell,{children:"Name"}),e.jsx(l.HeadCell,{children:"Email"}),e.jsx(l.HeadCell,{children:"Role"}),e.jsx(l.HeadCell,{children:"Actions"})]}),e.jsx(l.Body,{className:"divide-y",children:re.map(s=>{var n;return e.jsxs(l.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(l.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:s.profile?`${s.profile.first_name||""} ${s.profile.last_name||""}`:"Unknown User"}),e.jsx(l.Cell,{children:((n=s.profile)==null?void 0:n.email)||"No email available"}),e.jsx(l.Cell,{children:e.jsx(de,{color:Z(s.role),children:s.role})}),e.jsx(l.Cell,{children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(y,{module:"users",action:"change_role",children:e.jsx(i,{color:"light",size:"xs",onClick:()=>J(s),disabled:s.role==="owner"&&!v("users","delete"),children:e.jsx(ue,{className:"h-4 w-4"})})}),e.jsx(y,{module:"users",action:"reset_password",children:e.jsx(i,{color:"light",size:"xs",onClick:()=>Q(s),disabled:s.role==="owner"&&!v("users","delete"),children:e.jsx(he,{className:"h-4 w-4"})})}),e.jsx(y,{module:"users",action:"delete",children:e.jsx(i,{color:"failure",size:"xs",disabled:s.role==="owner",children:e.jsx(me,{className:"h-4 w-4"})})})]})})]},s.id)})})]}),x.length>0&&e.jsx(Pe,{currentPage:E,totalPages:ee,itemsPerPage:j,totalItems:x.length,onPageChange:z,onItemsPerPageChange:s=>{K(s),z(1)},itemName:"users"})]}),e.jsxs(c,{show:V,onClose:()=>f(!1),children:[e.jsx(c.Header,{children:"Change User Role"}),e.jsxs(c.Body,{children:[P&&e.jsx(h,{color:"failure",className:"mb-4",children:P}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("p",{children:["Changing role for: ",e.jsxs("strong",{children:[(L=r==null?void 0:r.profile)==null?void 0:L.first_name," ",(q=r==null?void 0:r.profile)==null?void 0:q.last_name]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(S,{htmlFor:"role",value:"Select Role"})}),e.jsxs(xe,{id:"role",value:H,onChange:s=>I(s.target.value),required:!0,children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"member",children:"Member"}),e.jsx("option",{value:"cashier",children:"Cashier"}),e.jsx("option",{value:"inventory_manager",children:"Inventory Manager"}),e.jsx("option",{value:"purchaser",children:"Purchaser"}),e.jsx("option",{value:"employee",children:"Employee"}),v("users","delete")&&e.jsx("option",{value:"owner",children:"Owner"})]})]})]})]}),e.jsxs(c.Footer,{children:[e.jsxs(i,{color:"primary",onClick:X,disabled:g,children:[g?e.jsx(b,{size:"sm",className:"mr-2"}):null,"Save Changes"]}),e.jsx(i,{color:"gray",onClick:()=>f(!1),children:"Cancel"})]})]}),e.jsxs(c,{show:W,onClose:()=>p(!1),children:[e.jsx(c.Header,{children:"Reset User Password"}),e.jsxs(c.Body,{children:[P&&e.jsx(h,{color:"failure",className:"mb-4",children:P}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("p",{children:["Resetting password for: ",e.jsx("strong",{children:r!=null&&r.profile?`${r.profile.first_name||""} ${r.profile.last_name||""}`:"Unknown User"})]}),e.jsx(h,{color:"info",children:e.jsxs("p",{className:"text-sm",children:[e.jsx("strong",{children:"Note:"})," In this demo, password reset is simulated. In a production environment, this would call a secure server-side API to reset the password."]})}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(S,{htmlFor:"newPassword",value:"New Password"})}),e.jsx(T,{id:"newPassword",type:"password",value:w,onChange:s=>U(s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(S,{htmlFor:"confirmPassword",value:"Confirm Password"})}),e.jsx(T,{id:"confirmPassword",type:"password",value:O,onChange:s=>$(s.target.value),required:!0})]})]})]}),e.jsxs(c.Footer,{children:[e.jsxs(i,{color:"primary",onClick:D,disabled:g,children:[g?e.jsx(b,{size:"sm",className:"mr-2"}):null,"Reset Password"]}),e.jsx(i,{color:"gray",onClick:()=>p(!1),children:"Cancel"})]})]})]})};export{Ne as default};
