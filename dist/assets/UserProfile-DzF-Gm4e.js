import{b as O,h as $,r,j as e,i as E,A as P,bA as M,a6 as d,P as u,K as A,aX as B,B as D,s as o}from"./index-C6AV3cVN.js";import{C as F}from"./Card-yj7fueH8.js";const T=()=>{const{user:t}=O(),{currentOrganization:H}=$(),[f,x]=r.useState(""),[h,p]=r.useState(""),[U,C]=r.useState(""),[_,v]=r.useState(null),[q,g]=r.useState(!0),[b,n]=r.useState(!1),[j,l]=r.useState(null),[N,m]=r.useState(null);r.useEffect(()=>{(async()=>{if(!t){g(!1);return}try{const{data:a,error:c}=await o.from("profiles").select("*").eq("id",t.id).maybeSingle();if(c)throw c;a&&(x(a.first_name||""),p(a.last_name||""),v(a.avatar_url||null)),C(t.email||"")}catch(a){console.error("Error fetching profile:",a),l(a.message||"Failed to fetch profile")}finally{g(!1)}})()},[t]);const z=async s=>{if(s.preventDefault(),!t){l("You must be logged in to update your profile");return}n(!0),l(null),m(null);try{const{error:a}=await o.from("profiles").update({first_name:f,last_name:h}).eq("id",t.id);if(a)throw a;m("Profile updated successfully")}catch(a){console.error("Error updating profile:",a),l(a.message||"Failed to update profile")}finally{n(!1)}},k=async s=>{if(!t||!s.target.files||s.target.files.length===0)return;const a=s.target.files[0],c=a.name.split(".").pop(),y=`avatars/${`${t.id}-${Math.random().toString(36).substring(2)}.${c}`}`;n(!0),l(null);try{const{error:i}=await o.storage.from("avatars").upload(y,a);if(i)throw i;const{data:L}=o.storage.from("avatars").getPublicUrl(y),S=L.publicUrl,{error:w}=await o.from("profiles").update({avatar_url:S}).eq("id",t.id);if(w)throw w;v(S),m("Avatar updated successfully")}catch(i){console.error("Error uploading avatar:",i),l(i.message||"Failed to upload avatar")}finally{n(!1)}};return q?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(F,{children:e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(E,{size:"xl"})})})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(F,{children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"My Profile"}),j&&e.jsx(P,{color:"failure",className:"mb-4",children:j}),N&&e.jsx(P,{color:"success",className:"mb-4",children:N}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-8",children:[e.jsx("div",{className:"md:w-1/3",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx(M,{img:_||void 0,size:"xl",rounded:!0,className:"mb-4"}),e.jsxs("div",{className:"w-full",children:[e.jsx(d,{htmlFor:"avatar",value:"Profile Picture",className:"mb-2"}),e.jsx("input",{id:"avatar",type:"file",accept:"image/*",onChange:k,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"})]})]})}),e.jsx("div",{className:"md:w-2/3",children:e.jsxs("form",{onSubmit:z,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"firstName",value:"First Name"})}),e.jsx(u,{id:"firstName",type:"text",icon:A,value:f,onChange:s=>x(s.target.value),required:!0})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"lastName",value:"Last Name"})}),e.jsx(u,{id:"lastName",type:"text",icon:A,value:h,onChange:s=>p(s.target.value),required:!0})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"email",value:"Email Address"})}),e.jsx(u,{id:"email",type:"email",icon:B,value:U,disabled:!0}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Email address cannot be changed"})]}),e.jsxs(D,{type:"submit",disabled:b,children:[b?e.jsx(E,{size:"sm",className:"mr-2"}):null,"Save Changes"]})]})})]})]})})};export{T as default};
