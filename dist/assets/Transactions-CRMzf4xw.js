import{h as J,r as a,j as e,L as w,B as d,q as V,A as W,J as G,P as X,Q as Y,a7 as L,ax as Q,W as Z,t as q,i as ee,_ as t,aC as C,aD as T,aB as se,e as c}from"./index-C6AV3cVN.js";import{C as te}from"./Card-yj7fueH8.js";import{b as ae}from"./inventoryTransaction-1UXV5RDN.js";import{c as z,d as re}from"./formatters-Cypx7G-j.js";import"./floatInventory-k_pEQeIK.js";const de=()=>{const{currentOrganization:x}=J(),[v,U]=a.useState([]),[b,k]=a.useState(!0),[D,g]=a.useState(null),[h,F]=a.useState(0),[i,u]=a.useState(1),[r,K]=a.useState(10),[P,O]=a.useState(""),[H,_]=a.useState(""),[m,I]=a.useState(null),[j,R]=a.useState(null),[l,E]=a.useState("created_at"),[n,y]=a.useState("desc");a.useEffect(()=>{x&&N()},[x,i,r,l,n]);const N=async()=>{if(x){k(!0),g(null);try{const s={limit:r,offset:(i-1)*r,sortBy:l,sortOrder:n,transactionType:H||void 0,startDate:m?m.toISOString():void 0,endDate:j?j.toISOString():void 0,searchQuery:P||void 0},{transactions:p,count:f,error:o}=await ae(x.id,s);o?g(o):(U(p),F(f))}catch(s){g(s.message||"An error occurred while fetching transactions")}finally{k(!1)}}},B=()=>{u(1),N()},A=()=>{O(""),_(""),I(null),R(null),E("created_at"),y("desc"),u(1),N()},S=s=>{l===s?y(n==="asc"?"desc":"asc"):(E(s),y("asc"))},M=s=>{switch(s.toLowerCase()){case"purchase":case"receipt":return e.jsx(c,{color:"success",children:"Purchase"});case"sale":return e.jsx(c,{color:"info",children:"Sale"});case"adjustment":return e.jsx(c,{color:"warning",children:"Adjustment"});case"transfer":return e.jsx(c,{color:"purple",children:"Transfer"});case"return":return e.jsx(c,{color:"pink",children:"Return"});default:return e.jsx(c,{color:"gray",children:s})}},$=Math.ceil(h/r);return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(te,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Inventory Transactions"}),e.jsx("p",{className:"text-gray-500",children:"View all inventory transactions including purchases, sales, adjustments, and transfers."})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(w,{to:"/inventory",children:e.jsx(d,{color:"light",children:"Back to Inventory"})}),e.jsxs(d,{color:"light",onClick:()=>window.print(),children:[e.jsx(V,{className:"mr-2 h-5 w-5"}),"Export"]})]})]}),D&&e.jsx(W,{color:"failure",icon:G,className:"mb-4",children:D}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[e.jsx("div",{className:"flex-1",children:e.jsx(X,{id:"search",type:"text",placeholder:"Search by product name or SKU...",value:P,onChange:s=>O(s.target.value),icon:Y,onKeyDown:s=>s.key==="Enter"&&B()})}),e.jsx("div",{className:"w-full md:w-48",children:e.jsxs(L,{id:"transactionType",value:H,onChange:s=>_(s.target.value),children:[e.jsx("option",{value:"",children:"All Types"}),e.jsx("option",{value:"purchase",children:"Purchases"}),e.jsx("option",{value:"receipt",children:"Receipts"}),e.jsx("option",{value:"sale",children:"Sales"}),e.jsx("option",{value:"adjustment",children:"Adjustments"}),e.jsx("option",{value:"transfer",children:"Transfers"}),e.jsx("option",{value:"return",children:"Returns"})]})}),e.jsx("div",{className:"w-full md:w-48",children:e.jsx(Q,{value:m?z(m.toISOString()):"",onSelectedDateChanged:I,placeholder:"Start Date"})}),e.jsx("div",{className:"w-full md:w-48",children:e.jsx(Q,{value:j?z(j.toISOString()):"",onSelectedDateChanged:R,placeholder:"End Date"})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(d,{color:"primary",onClick:B,children:[e.jsx(Z,{className:"mr-2 h-5 w-5"}),"Filter"]}),e.jsxs(d,{color:"gray",onClick:A,children:[e.jsx(q,{className:"mr-2 h-5 w-5"}),"Reset"]})]})]}),b?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(ee,{size:"xl"})}):v.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No transactions found"}),e.jsxs(d,{color:"light",onClick:A,children:[e.jsx(q,{className:"mr-2 h-5 w-5"}),"Reset Filters"]})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(t,{hoverable:!0,children:[e.jsxs(t.Head,{children:[e.jsx(t.HeadCell,{className:"cursor-pointer",onClick:()=>S("created_at"),children:e.jsxs("div",{className:"flex items-center",children:["Date/Time",l==="created_at"&&(n==="asc"?e.jsx(C,{className:"ml-1"}):e.jsx(T,{className:"ml-1"}))]})}),e.jsx(t.HeadCell,{className:"cursor-pointer",onClick:()=>S("product.name"),children:e.jsxs("div",{className:"flex items-center",children:["Product",l==="product.name"&&(n==="asc"?e.jsx(C,{className:"ml-1"}):e.jsx(T,{className:"ml-1"}))]})}),e.jsx(t.HeadCell,{children:"Type"}),e.jsx(t.HeadCell,{className:"cursor-pointer",onClick:()=>S("quantity"),children:e.jsxs("div",{className:"flex items-center",children:["Quantity",l==="quantity"&&(n==="asc"?e.jsx(C,{className:"ml-1"}):e.jsx(T,{className:"ml-1"}))]})}),e.jsx(t.HeadCell,{children:"Reference"}),e.jsx(t.HeadCell,{children:"Notes"})]}),e.jsx(t.Body,{className:"divide-y",children:v.map(s=>{var p,f,o;return e.jsxs(t.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(t.Cell,{children:e.jsx(w,{to:`/inventory/transactions/${s.id}`,className:"text-blue-600 hover:underline",children:re(s.created_at)})}),e.jsx(t.Cell,{children:e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:s.product_id?e.jsx(w,{to:`/inventory/details/${s.product_id}`,className:"text-blue-600 hover:underline",children:((p=s.product)==null?void 0:p.name)||"Unknown Product"}):((f=s.product)==null?void 0:f.name)||"Unknown Product"}),((o=s.product)==null?void 0:o.sku)&&e.jsxs("div",{className:"text-xs text-gray-500",children:["SKU: ",s.product.sku]})]})}),e.jsx(t.Cell,{children:M(s.transaction_type)}),e.jsx(t.Cell,{children:s.quantity}),e.jsx(t.Cell,{children:s.reference_type&&s.reference_id?e.jsx("span",{className:"text-sm",children:s.reference_type.replace(/_/g," ")}):e.jsx("span",{className:"text-gray-500",children:"-"})}),e.jsx(t.Cell,{children:e.jsx("div",{className:"max-w-xs truncate",children:s.notes||"-"})})]},s.id)})})]})}),!b&&v.length>0&&e.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between mt-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2 md:mb-0",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Items per page:"}),e.jsxs(L,{id:"itemsPerPage",value:r,onChange:s=>{K(Number(s.target.value)),u(1)},className:"w-20",size:"sm",children:[e.jsx("option",{value:"10",children:"10"}),e.jsx("option",{value:"25",children:"25"}),e.jsx("option",{value:"50",children:"50"}),e.jsx("option",{value:"100",children:"100"})]}),e.jsxs("span",{className:"text-sm text-gray-500",children:["Showing ",Math.min((i-1)*r+1,h)," to ",Math.min(i*r,h)," of ",h," results"]})]}),e.jsx("div",{className:"flex mt-2 md:mt-0",children:e.jsx(se,{currentPage:i,totalPages:$,onPageChange:u,showIcons:!0})})]})]})})};export{de as default};
