import{r as l,j as e,bA as q,i as D,c2 as R,e as B,aX as Y,aY as J,bt as V,B as S,a2 as L,c3 as X,k as G,aj as K,ai as Q,c4 as z,K as M,c5 as W,c6 as Z,A as F,a6 as d,P as x,c7 as $,a8 as ee,c8 as I,b as se,h as te,F as ae,s as C}from"./index-C6AV3cVN.js";import{C as A}from"./Card-yj7fueH8.js";const re=({profile:t,onEditClick:h,onAvatarUpload:s})=>{const[m,g]=l.useState(!1),[r,c]=l.useState(!1),u=l.useRef(null),p=()=>{var n;r||(n=u.current)==null||n.click()},o=async n=>{if(n.target.files&&n.target.files.length>0){c(!0);try{await s(n.target.files[0])}finally{c(!1)}}},k=n=>{switch(n.toLowerCase()){case"owner":return"purple";case"admin":return"red";case"manager":return"green";case"employee":return"blue";case"cashier":return"yellow";case"inventory_manager":return"indigo";default:return"gray"}},j=n=>{if(!n)return"N/A";const f=new Date(n);return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric"}).format(f)};return e.jsx(A,{children:e.jsxs("div",{className:"flex flex-col items-center pb-6",children:[e.jsxs("div",{className:`relative cursor-pointer group ${r?"cursor-wait":""}`,onMouseEnter:()=>!r&&g(!0),onMouseLeave:()=>!r&&g(!1),onClick:p,children:[e.jsx(q,{img:t.avatarUrl||void 0,size:"xl",rounded:!0,className:`mb-3 ring-2 ${r?"opacity-60 ring-blue-300 dark:ring-blue-700":"ring-gray-200 dark:ring-gray-700"}`,status:"online",statusPosition:"bottom-right"}),r?e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-30 rounded-full flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(D,{size:"md",className:"mx-auto"}),e.jsx("span",{className:"text-xs text-white mt-1 block",children:"Uploading..."})]})}):e.jsx("div",{className:`absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center transition-opacity duration-200 ${m?"opacity-100":"opacity-0"}`,children:e.jsx(R,{className:"h-8 w-8 text-white"})}),e.jsx("input",{type:"file",ref:u,className:"hidden",accept:"image/*",onChange:o,disabled:r})]}),e.jsxs("h5",{className:"mb-1 text-xl font-medium text-gray-900 dark:text-white",children:[t.firstName," ",t.lastName]}),e.jsx("div",{className:"mb-3",children:e.jsx(B,{color:k(t.role),size:"sm",children:t.role.replace("_"," ").replace(/\b\w/g,n=>n.toUpperCase())})}),t.jobTitle&&e.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:[t.jobTitle,t.department&&` • ${t.department}`]}),e.jsxs("div",{className:"space-y-3 w-full",children:[e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx(Y,{className:"mr-2 h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-gray-700 dark:text-gray-300 truncate",children:t.email})]}),t.phoneNumber&&e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx(J,{className:"mr-2 h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:t.phoneNumber})]}),t.location&&e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx(V,{className:"mr-2 h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:t.location})]})]}),e.jsx("div",{className:"mt-6 w-full border-t border-gray-200 dark:border-gray-700 pt-4",children:e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"Member since"}),e.jsx("span",{className:"font-medium",children:j(t.joinDate)})]})}),e.jsx("div",{className:"mt-6 w-full",children:e.jsxs(S,{color:"light",className:"w-full",onClick:h,children:[e.jsx(L,{className:"mr-2 h-4 w-4"}),"Edit Profile"]})})]})})},ie=({activities:t})=>{const h=r=>{switch(r){case"login":return e.jsx(Z,{className:"h-5 w-5 text-blue-500"});case"logout":return e.jsx(W,{className:"h-5 w-5 text-gray-500"});case"profile_update":return e.jsx(M,{className:"h-5 w-5 text-green-500"});case"password_change":return e.jsx(z,{className:"h-5 w-5 text-yellow-500"});case"purchase":return e.jsx(Q,{className:"h-5 w-5 text-purple-500"});case"document":return e.jsx(K,{className:"h-5 w-5 text-red-500"});case"organization":return e.jsx(G,{className:"h-5 w-5 text-indigo-500"});default:return e.jsx(X,{className:"h-5 w-5 text-gray-500"})}},s=r=>{switch(r){case"login":return"Logged in";case"logout":return"Logged out";case"profile_update":return"Updated profile";case"password_change":return"Changed password";case"purchase":return"Made a purchase";case"document":return"Document action";case"organization":return"Organization update";default:return"Activity"}},m=r=>{const c=new Date(r),p=Math.abs(new Date().getTime()-c.getTime()),o=Math.floor(p/(1e3*60*60*24));return o===0?`Today at ${c.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}`:o===1?`Yesterday at ${c.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}`:o<7?`${o} days ago`:c.toLocaleDateString([],{year:"numeric",month:"short",day:"numeric"})},g=[...t].sort((r,c)=>new Date(c.timestamp).getTime()-new Date(r.timestamp).getTime());return e.jsx("div",{className:"flow-root",children:g.length===0?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-500",children:"No activity recorded yet"})}):e.jsx("ul",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:g.map(r=>e.jsx("li",{className:"py-4",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"flex-shrink-0 mt-1",children:h(r.type)}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:s(r.type)}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:r.details})]}),e.jsx("div",{className:"flex-shrink-0 text-sm text-gray-500 dark:text-gray-400",children:m(r.timestamp)})]})},r.id))})})},le=({userId:t})=>{const[h,s]=l.useState(""),[m,g]=l.useState(""),[r,c]=l.useState(""),[u,p]=l.useState(null),[o,k]=l.useState(null),[j,n]=l.useState(!1),[f,b]=l.useState(!1),[P,T]=l.useState(!1),[E,O]=l.useState([{id:"1",device:"Chrome on macOS",lastActive:new Date().toISOString(),current:!0},{id:"2",device:"Safari on iPhone",lastActive:new Date(Date.now()-1e3*60*60*24).toISOString(),current:!1}]),_=async i=>{if(i.preventDefault(),p(null),k(null),m!==r){p("New passwords do not match");return}if(m.length<8){p("Password must be at least 8 characters long");return}n(!0),setTimeout(()=>{n(!1),k("Password changed successfully"),s(""),g(""),c("")},1500)},N=()=>{f?setTimeout(()=>{b(!1)},500):T(!0)},y=()=>{setTimeout(()=>{b(!0),T(!1)},1500)},a=i=>{O(E.filter(w=>w.id!==i))},v=i=>{const w=new Date(i);return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",hour:"numeric",minute:"numeric"}).format(w)};return e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[e.jsx(z,{className:"mr-2 h-5 w-5 text-gray-500"}),"Password"]}),u&&e.jsx(F,{color:"failure",className:"mb-4",children:u}),o&&e.jsx(F,{color:"success",className:"mb-4",children:o}),e.jsxs("form",{onSubmit:_,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(d,{htmlFor:"currentPassword",value:"Current Password"}),e.jsx(x,{id:"currentPassword",type:"password",value:h,onChange:i=>s(i.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"newPassword",value:"New Password"}),e.jsx(x,{id:"newPassword",type:"password",value:m,onChange:i=>g(i.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"confirmPassword",value:"Confirm New Password"}),e.jsx(x,{id:"confirmPassword",type:"password",value:r,onChange:i=>c(i.target.value),required:!0})]}),e.jsxs(S,{type:"submit",disabled:j,children:[j?e.jsx(D,{size:"sm",className:"mr-2"}):null,"Change Password"]})]})]}),e.jsxs("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsxs("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[e.jsx($,{className:"mr-2 h-5 w-5 text-gray-500"}),"Two-Factor Authentication"]}),e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Add an extra layer of security to your account"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"We'll ask for a verification code in addition to your password when you sign in"})]}),e.jsx(ee,{checked:f,onChange:N,label:""})]}),P&&e.jsx(A,{className:"mb-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h4",{className:"text-md font-medium mb-2",children:"Set up Two-Factor Authentication"}),e.jsx("p",{className:"text-sm text-gray-500 mb-4",children:"Scan this QR code with your authenticator app"}),e.jsx("div",{className:"bg-gray-100 dark:bg-gray-700 p-4 inline-block rounded-lg mb-4",children:e.jsx("div",{className:"w-40 h-40 bg-gray-300 dark:bg-gray-600 rounded-md mx-auto flex items-center justify-center",children:e.jsx(I,{className:"h-16 w-16 text-gray-500"})})}),e.jsxs("div",{className:"mb-4",children:[e.jsx(d,{htmlFor:"verificationCode",value:"Verification Code"}),e.jsx(x,{id:"verificationCode",type:"text",placeholder:"Enter 6-digit code",className:"max-w-xs mx-auto"})]}),e.jsxs("div",{className:"flex justify-center space-x-3",children:[e.jsx(S,{color:"gray",onClick:()=>T(!1),children:"Cancel"}),e.jsx(S,{onClick:y,children:"Verify and Enable"})]})]})})]}),e.jsxs("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsxs("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[e.jsx(I,{className:"mr-2 h-5 w-5 text-gray-500"}),"Active Sessions"]}),e.jsx("div",{className:"space-y-4",children:E.map(i=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium",children:i.device}),i.current&&e.jsx("span",{className:"ml-2 px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800 rounded-full",children:"Current"})]}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Last active: ",v(i.lastActive)]})]}),!i.current&&e.jsx(S,{color:"failure",size:"xs",onClick:()=>a(i.id),children:"Revoke"})]},i.id))})]})]})},ce=()=>{const{user:t}=se(),{currentOrganization:h}=te();l.useRef(null);const[s,m]=l.useState({firstName:"",lastName:"",email:"",role:"",avatarUrl:"",jobTitle:"",department:"",phoneNumber:"",location:"",bio:"",joinDate:""}),[g,r]=l.useState(!0),[c,u]=l.useState(!1),[p,o]=l.useState(null),[k,j]=l.useState(null),[n,f]=l.useState(!1),[b,P]=l.useState(0),[T,E]=l.useState([{id:"1",type:"login",timestamp:new Date(Date.now()-1e3*60*60*2).toISOString(),details:"Logged in from Chrome on Mac OS X"},{id:"2",type:"profile_update",timestamp:new Date(Date.now()-1e3*60*60*24*2).toISOString(),details:"Updated profile information"},{id:"3",type:"password_change",timestamp:new Date(Date.now()-1e3*60*60*24*7).toISOString(),details:"Changed password"}]);l.useEffect(()=>{(async()=>{if(!t){r(!1);return}try{const{data:a,error:v}=await C.from("profiles").select("*").eq("id",t.id).maybeSingle();if(v)throw v;const{data:i,error:w}=await C.from("organization_members").select("role").eq("user_id",t.id).eq("organization_id",h==null?void 0:h.id).single();w&&w.code!=="PGRST116"&&console.error("Error fetching member role:",w),a&&m({firstName:a.first_name||"",lastName:a.last_name||"",email:t.email||"",role:(i==null?void 0:i.role)||"member",avatarUrl:a.avatar_url||"",jobTitle:a.job_title||"",department:a.department||"",phoneNumber:a.phone_number||"",location:a.location||"",bio:a.bio||"",joinDate:a.created_at||""})}catch(a){console.error("Error fetching profile:",a),o(a.message||"Failed to fetch profile")}finally{r(!1)}})()},[t,h]);const O=async y=>{if(y.preventDefault(),!t){o("You must be logged in to update your profile");return}u(!0),o(null),j(null);try{const{error:a}=await C.from("profiles").update({first_name:s.firstName,last_name:s.lastName,job_title:s.jobTitle,department:s.department,phone_number:s.phoneNumber,location:s.location,bio:s.bio}).eq("id",t.id);if(a)throw a;j("Profile updated successfully"),f(!1)}catch(a){console.error("Error updating profile:",a),o(a.message||"Failed to update profile")}finally{u(!1)}},_=async y=>{if(!t){o("You must be logged in to update your avatar");return}u(!0),o(null),j(null);try{const a=y.name.split(".").pop(),v=`${t.id}-${Math.random().toString(36).substring(2)}.${a}`,{error:i}=await C.storage.from("avatars").upload(v,y);if(i)throw i;const{data:w}=C.storage.from("avatars").getPublicUrl(v),U=w.publicUrl,{error:H}=await C.from("profiles").update({avatar_url:U}).eq("id",t.id);if(H)throw H;m({...s,avatarUrl:U}),j("Avatar updated successfully")}catch(a){console.error("Error uploading avatar:",a),o(a.message||"Failed to upload avatar")}finally{u(!1)}},N=y=>{const{name:a,value:v}=y.target;m({...s,[a]:v})};return g?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(A,{children:e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(D,{size:"xl"})})})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx(re,{profile:s,onEditClick:()=>f(!0),onAvatarUpload:_})}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs(A,{children:[p&&e.jsx(F,{color:"failure",className:"mb-4",children:p}),k&&e.jsx(F,{color:"success",className:"mb-4",children:k}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-4 border-b border-gray-200",children:e.jsxs("ul",{className:"flex flex-wrap -mb-px text-sm font-medium text-center",children:[e.jsx("li",{className:"mr-2",role:"presentation",children:e.jsx("button",{className:`inline-block p-4 border-b-2 rounded-t-lg ${b===0?"border-blue-600 text-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,type:"button",onClick:()=>P(0),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(M,{className:"mr-2 h-5 w-5"}),"Profile Information"]})})}),e.jsx("li",{className:"mr-2",role:"presentation",children:e.jsx("button",{className:`inline-block p-4 border-b-2 rounded-t-lg ${b===1?"border-blue-600 text-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,type:"button",onClick:()=>P(1),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ae,{className:"mr-2 h-5 w-5"}),"Activity"]})})}),e.jsx("li",{className:"mr-2",role:"presentation",children:e.jsx("button",{className:`inline-block p-4 border-b-2 rounded-t-lg ${b===2?"border-blue-600 text-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,type:"button",onClick:()=>P(2),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx($,{className:"mr-2 h-5 w-5"}),"Security"]})})})]})}),b===0&&e.jsx(e.Fragment,{children:n?e.jsxs("form",{onSubmit:O,className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(d,{htmlFor:"firstName",value:"First Name"}),e.jsx(x,{id:"firstName",name:"firstName",type:"text",value:s.firstName,onChange:N,required:!0})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"lastName",value:"Last Name"}),e.jsx(x,{id:"lastName",name:"lastName",type:"text",value:s.lastName,onChange:N,required:!0})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"email",value:"Email Address"}),e.jsx(x,{id:"email",name:"email",type:"email",value:s.email,disabled:!0})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"phoneNumber",value:"Phone Number"}),e.jsx(x,{id:"phoneNumber",name:"phoneNumber",type:"tel",value:s.phoneNumber,onChange:N})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"jobTitle",value:"Job Title"}),e.jsx(x,{id:"jobTitle",name:"jobTitle",type:"text",value:s.jobTitle,onChange:N})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"department",value:"Department"}),e.jsx(x,{id:"department",name:"department",type:"text",value:s.department,onChange:N})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx(d,{htmlFor:"location",value:"Location"}),e.jsx(x,{id:"location",name:"location",type:"text",value:s.location,onChange:N})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx(d,{htmlFor:"bio",value:"Bio"}),e.jsx("textarea",{id:"bio",name:"bio",rows:4,className:"block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",value:s.bio,onChange:N})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx(S,{color:"gray",onClick:()=>f(!1),disabled:c,children:"Cancel"}),e.jsxs(S,{type:"submit",disabled:c,children:[c?e.jsx(D,{size:"sm",className:"mr-2"}):null,"Save Changes"]})]})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-xl font-semibold",children:"Personal Information"}),e.jsxs(S,{color:"light",size:"sm",onClick:()=>f(!0),children:[e.jsx(L,{className:"mr-2 h-4 w-4"}),"Edit"]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Full Name"}),e.jsxs("p",{children:[s.firstName," ",s.lastName]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Email"}),e.jsx("p",{children:s.email})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Phone"}),e.jsx("p",{children:s.phoneNumber||"Not specified"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Location"}),e.jsx("p",{children:s.location||"Not specified"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Job Title"}),e.jsx("p",{children:s.jobTitle||"Not specified"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Department"}),e.jsx("p",{children:s.department||"Not specified"})]}),s.bio&&e.jsxs("div",{className:"md:col-span-2 mt-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Bio"}),e.jsx("p",{className:"whitespace-pre-line",children:s.bio})]})]})]})}),b===1&&e.jsx(ie,{activities:T}),b===2&&e.jsx(le,{userId:t==null?void 0:t.id})]})]})})]})})};export{ce as default};
