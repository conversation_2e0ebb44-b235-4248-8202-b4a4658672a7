import{s as u}from"./index-C6AV3cVN.js";import{d as h}from"./inventoryTransaction-1UXV5RDN.js";const S=async(c,e)=>{try{let r=u.from("inventory_receipts").select(`
        *,
        items:inventory_receipt_items(
          *,
          product:product_id(id, name, sku, description),
          uom:uom_id(id, code, name)
        ),
        purchase_order:purchase_order_id(
          id,
          order_number,
          supplier_id,
          supplier:supplier_id(id, name)
        )
      `).eq("organization_id",c);e!=null&&e.status&&(r=r.eq("status",e.status)),e!=null&&e.purchaseOrderId&&(r=r.eq("purchase_order_id",e.purchaseOrderId)),e!=null&&e.searchQuery&&(r=r.ilike("receipt_number",`%${e.searchQuery}%`)),e!=null&&e.startDate&&(r=r.gte("receipt_date",e.startDate)),e!=null&&e.endDate&&(r=r.lte("receipt_date",e.endDate)),r=r.order("created_at",{ascending:!1});const{data:a,error:s}=await r;return s?{receipts:[],error:s.message}:!a||a.length===0?{receipts:[]}:{receipts:a.map(i=>{var d;return{...i,purchase_order:i.purchase_order?{...i.purchase_order,supplier_name:((d=i.purchase_order.supplier)==null?void 0:d.name)||"Unknown Supplier"}:void 0,creator_name:"Staff"}})}}catch(r){return{receipts:[],error:r.message}}},v=async(c,e)=>{var r;try{const{data:a,error:s}=await u.from("inventory_receipts").select(`
        *,
        items:inventory_receipt_items(
          *,
          product:product_id(id, name, sku, description),
          uom:uom_id(id, code, name),
          purchase_order_item:purchase_order_item_id(id, quantity, unit_price)
        ),
        purchase_order:purchase_order_id(
          id,
          order_number,
          supplier_id,
          supplier:supplier_id(id, name)
        )
      `).eq("organization_id",c).eq("id",e).single();return s?{error:s.message}:{receipt:{...a,purchase_order:a.purchase_order?{...a.purchase_order,supplier_name:((r=a.purchase_order.supplier)==null?void 0:r.name)||"Unknown Supplier"}:void 0,creator_name:"Staff"}}}catch(a){return{error:a.message}}},b=async(c,e)=>{var r,a;try{const{data:s}=await u.auth.getSession(),o=(a=(r=s==null?void 0:s.session)==null?void 0:r.user)==null?void 0:a.id,i=new Date().toISOString().slice(0,10).replace(/-/g,""),d=Math.floor(Math.random()*1e4).toString().padStart(4,"0"),g=`RCP-${i}-${d}`,{data:n,error:m}=await u.from("inventory_receipts").insert({organization_id:c,purchase_order_id:e.purchaseOrderId,receipt_date:e.receiptDate,notes:e.notes,status:e.status,created_by:o,receipt_number:g}).select().single();if(m)return{receipt:null,error:m.message};if(!n)return{receipt:null,error:"Failed to create inventory receipt"};const f=e.items.filter(t=>t.quantity>0).map(t=>{const _=parseFloat(String(t.conversionFactor)),p=!isNaN(_)&&_>0?_:1,y=t.quantity*p;return console.log(`Creating receipt item: Product ${t.productId}, Quantity: ${t.quantity}, UoM: ${t.uomId}, Conversion Factor: ${p}, Base Quantity: ${y}`),{inventory_receipt_id:n.id,purchase_order_item_id:t.purchaseOrderItemId,product_id:t.productId,quantity:t.quantity,uom_id:t.uomId,unit_cost:t.unitCost,qc_status:t.qcStatus,damaged_quantity:t.damagedQuantity,damage_reason:t.damageReason,lot_number:t.lotNumber,expiry_date:t.expiryDate,conversion_factor:p,base_quantity:y,serial_numbers:t.serialNumbers}}),{error:l}=await u.from("inventory_receipt_items").insert(f);if(l)return{receipt:n,error:l.message};if(e.status==="completed"){console.log("Creating inventory transactions for new completed receipt");const{error:t}=await h(c,n.id,o||"");t?console.error("Error creating inventory transactions:",t):console.log("Successfully created inventory transactions for new receipt")}return await v(c,n.id)}catch(s){return{error:s.message}}};export{v as a,b as c,S as g};
