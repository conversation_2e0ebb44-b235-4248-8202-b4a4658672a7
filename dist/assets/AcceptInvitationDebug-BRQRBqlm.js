import{aG as ee,d as te,r as d,j as e,A as $,i as A,a6 as j,P as b,aX as Y,K as D,c4 as ae,B as N,s as o}from"./index-C6AV3cVN.js";import{C as se}from"./Card-yj7fueH8.js";const ne=()=>{var O;const[U]=ee(),k=te(),E=U.get("token"),h=U.get("id"),[a,B]=d.useState(E?decodeURIComponent(E):null),[S,H]=d.useState(h);console.log("Raw token from URL:",E),console.log("Decoded token:",a);const[w,f]=d.useState(!1),[l,J]=d.useState(null),[F,n]=d.useState(null),[R,C]=d.useState(null),[G,T]=d.useState(!1),[L,K]=d.useState(""),[q,W]=d.useState(""),[_,X]=d.useState(""),[z,Q]=d.useState(""),[P,I]=d.useState(!1);d.useEffect(()=>{(async()=>{var g;if(!a&&!h)return;f(!0),n(null);const{data:m}=await o.auth.getSession(),u=(g=m.session)==null?void 0:g.user;try{console.log("Fetching invitation with:",{token:a,invitationId:h});const{data:x}=await o.from("invitations").select("id, token, email, expires_at, accepted_at").limit(10);console.log("All recent invitations:",x);let s=null,v=null;if(h){console.log("Trying to find invitation with ID:",h);const{data:r,error:c}=await o.from("invitations").select("id, token, email, organization_id, role, expires_at, accepted_at").eq("id",h).is("accepted_at",null).single();if(console.log("Simple query result:",{data:r,error:c}),r&&!c)try{const{data:i,error:p}=await o.from("invitations").select("*, organizations(name)").eq("id",h).is("accepted_at",null).single();console.log("Full query result:",{data:i,error:p}),i&&!p?s=i:s=r}catch(i){console.error("Error in full query:",i),s=r}else v=c}if(!s&&a){console.log("Trying to find invitation with token:",a);const{data:r,error:c}=await o.from("invitations").select("id, token, email, organization_id, role, expires_at, accepted_at").eq("token",a).is("accepted_at",null).single();if(console.log("Simple token query result:",{data:r,error:c}),r&&!c)try{const{data:i,error:p}=await o.from("invitations").select("*, organizations(name)").eq("token",a).is("accepted_at",null).single();console.log("Full token query result:",{data:i,error:p}),i&&!p?s=i:s=r}catch(i){console.error("Error in full token query:",i),s=r}else v||(v=c)}if(!s&&a){const r=[a.replace(/\+/g," "),a.replace(/\s/g,"+"),encodeURIComponent(a),decodeURIComponent(a)];for(const c of r){if(c===a)continue;console.log("Trying alternative token format:",c);const{data:i,error:p}=await o.from("invitations").select("id, token, email, organization_id, role, expires_at, accepted_at").eq("token",c).is("accepted_at",null).single();if(console.log("Simple alt token query result:",{data:i,error:p}),i&&!p)try{const{data:y,error:M}=await o.from("invitations").select("*, organizations(name)").eq("token",c).is("accepted_at",null).single();if(console.log("Full alt token query result:",{data:y,error:M}),y&&!M){s=y;break}else{s=i;break}}catch(y){console.error("Error in full alt token query:",y),s=i;break}}}if(s){const r=s;console.log("Found invitation:",r),J(r),K(r.email||""),new Date(r.expires_at)<new Date&&n("This invitation has expired."),u||(console.log("User not logged in, showing registration form"),T(!0))}else console.error("Could not find invitation with any method"),console.error("Last error:",v),n("Invalid or expired invitation.")}catch(x){console.error("Error fetching invitation:",x),n(x.message||"An error occurred")}finally{f(!1)}})()},[a,h]);const V=async t=>{if(t.preventDefault(),!l){n("No valid invitation found");return}if(!_||!z){n("Please provide your first and last name");return}if(q.length<8){n("Password must be at least 8 characters long");return}I(!0),n(null);try{const{data:m,error:u}=await o.auth.signUp({email:L,password:q,options:{data:{first_name:_,last_name:z}}});if(u||!m.user){n((u==null?void 0:u.message)||"Failed to create account"),I(!1);return}const g=m.user.id,{error:x}=await o.from("profiles").insert({id:g,first_name:_,last_name:z});x&&console.error("Error creating profile:",x);const{error:s}=await o.from("organization_members").insert({organization_id:l.organization_id,user_id:g,role:l.role});if(s){console.error("Error adding user to organization:",s),n("Error adding user to organization: "+s.message),I(!1);return}const{error:v}=await o.from("invitations").update({accepted_at:new Date().toISOString()}).eq("id",l.id);v&&console.error("Error updating invitation:",v),C("Account created and invitation accepted! Redirecting to dashboard..."),setTimeout(()=>{k("/dashboard")},3e3)}catch(m){console.error("Error in registration:",m),n(m.message||"An error occurred")}finally{I(!1)}},Z=async()=>{var t;if(!l){n("No valid invitation found");return}f(!0),n(null),C(null);try{const{data:m}=await o.auth.getSession(),u=(t=m.session)==null?void 0:t.user;if(!u){T(!0),f(!1);return}console.log("Accepting invitation as user:",u.id);const{error:g}=await o.from("organization_members").insert({organization_id:l.organization_id,user_id:u.id,role:l.role});if(g)if(g.code==="23505")console.log("User is already a member of this organization");else{console.error("Error adding user to organization:",g),n(`Error adding user to organization: ${g.message}`),f(!1);return}const{error:x}=await o.from("invitations").update({accepted_at:new Date().toISOString()}).eq("id",l.id);x&&console.error("Error updating invitation:",x),C("Invitation accepted successfully! You have been added to the organization."),setTimeout(()=>{k("/dashboard")},3e3)}catch(m){console.error("Error accepting invitation:",m),n(m.message||"An error occurred")}finally{f(!1)}};return e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50 dark:bg-gray-900 px-4",children:e.jsxs("div",{className:"w-full max-w-md",children:[F&&e.jsx($,{color:"failure",className:"mb-4",children:F}),R&&e.jsx($,{color:"success",className:"mb-4",children:R}),e.jsx(se,{className:"shadow-lg",children:w?e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsx(A,{size:"xl"}),e.jsx("p",{className:"mt-4 text-gray-500",children:"Loading invitation details..."})]}):l?e.jsxs("div",{children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-800 dark:text-white",children:"Join Organization"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mt-2",children:"You've been invited to join an organization"})]}),e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-6",children:[e.jsxs("div",{className:"text-center mb-2",children:[e.jsx("p",{className:"text-lg font-semibold text-blue-800 dark:text-blue-200",children:((O=l.organizations)==null?void 0:O.name)||"Organization"}),e.jsxs("p",{className:"text-sm text-blue-600 dark:text-blue-300",children:["has invited you to join as a ",e.jsx("span",{className:"font-medium capitalize",children:l.role})]})]}),e.jsxs("div",{className:"flex justify-between text-sm text-blue-700 dark:text-blue-300 mt-3",children:[e.jsx("span",{children:l.email}),e.jsxs("span",{children:["Expires: ",new Date(l.expires_at).toLocaleDateString()]})]})]}),G?e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-center mb-4",children:"Create Your Account"}),e.jsxs("form",{onSubmit:V,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(j,{htmlFor:"email",value:"Email Address",className:"text-sm font-medium"})}),e.jsx(b,{id:"email",type:"email",value:L,icon:Y,disabled:!0,className:"bg-gray-50"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(j,{htmlFor:"firstName",value:"First Name",className:"text-sm font-medium"})}),e.jsx(b,{id:"firstName",type:"text",value:_,icon:D,onChange:t=>X(t.target.value),placeholder:"John",required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(j,{htmlFor:"lastName",value:"Last Name",className:"text-sm font-medium"})}),e.jsx(b,{id:"lastName",type:"text",value:z,icon:D,onChange:t=>Q(t.target.value),placeholder:"Doe",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(j,{htmlFor:"password",value:"Create Password",className:"text-sm font-medium"})}),e.jsx(b,{id:"password",type:"password",value:q,icon:ae,onChange:t=>W(t.target.value),required:!0}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Password must be at least 8 characters long"})]}),e.jsxs(N,{type:"submit",color:"blue",className:"w-full mt-6",disabled:P,size:"lg",children:[P?e.jsx(A,{size:"sm",className:"mr-2"}):null,"Create Account & Join"]}),e.jsxs("div",{className:"text-center mt-4",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:"Already have an account?"}),e.jsx(N,{color:"light",size:"sm",onClick:()=>{const t=`/auth/accept-invitation?token=${encodeURIComponent(a||"")}&id=${h||""}`;k(`/auth/login?returnUrl=${encodeURIComponent(t)}`)},children:"Sign in instead"})]})]})]}):e.jsxs("div",{className:"text-center",children:[e.jsxs(N,{color:"blue",onClick:Z,disabled:w,size:"lg",className:"px-8",children:[w?e.jsx(A,{size:"sm",className:"mr-2"}):null,"Accept Invitation"]}),e.jsx("p",{className:"text-sm text-gray-500 mt-4",children:"You'll be added to the organization after accepting"})]})]}):e.jsxs("div",{className:"text-center py-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("svg",{className:"w-16 h-16 text-gray-400 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),e.jsx("h3",{className:"text-lg font-semibold mt-4 mb-2",children:"Invalid Invitation"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"The invitation link appears to be invalid or has expired."})]}),e.jsxs("div",{className:"space-y-4 max-w-sm mx-auto",children:[e.jsx("p",{className:"text-sm text-gray-500 mb-4",children:"If you have the invitation details, you can enter them manually:"}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(j,{htmlFor:"token",value:"Invitation Token",className:"text-sm font-medium"})}),e.jsx(b,{id:"token",type:"text",placeholder:"Enter invitation token",value:a||"",onChange:t=>B(t.target.value),icon:Y})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(j,{htmlFor:"invitationId",value:"Invitation ID",className:"text-sm font-medium"})}),e.jsx(b,{id:"invitationId",type:"text",placeholder:"Enter invitation ID",value:S||"",onChange:t=>H(t.target.value),icon:D})]}),e.jsx(N,{color:"blue",onClick:()=>{const t=new URLSearchParams;a&&t.set("token",a),S&&t.set("id",S),window.history.replaceState({},"",`${window.location.pathname}?${t}`),window.location.reload()},disabled:w,className:"w-full mt-4",children:"Check Invitation"}),e.jsxs("div",{className:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:"Need a new invitation?"}),e.jsx(N,{color:"light",size:"sm",onClick:()=>k("/auth/login"),children:"Contact your administrator"})]})]})]})})]})})};export{ne as default};
