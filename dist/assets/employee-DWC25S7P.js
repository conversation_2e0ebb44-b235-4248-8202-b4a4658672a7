import{s as n}from"./index-C6AV3cVN.js";const p=async(s,e)=>{try{let r=n.from("employees").select(`
        *,
        department:department_id (
          id,
          name,
          description
        ),
        position:position_id (
          id,
          title,
          description
        ),
        employment_type:employment_type_id (
          id,
          name,
          description
        ),
        government_ids:employee_government_ids (*)
      `,{count:"exact"}).eq("organization_id",s);e!=null&&e.departmentId&&(r=r.eq("department_id",e.departmentId)),e!=null&&e.positionId&&(r=r.eq("position_id",e.positionId)),e!=null&&e.employmentTypeId&&(r=r.eq("employment_type_id",e.employmentTypeId)),e!=null&&e.status&&(r=r.eq("status",e.status)),(e==null?void 0:e.isActive)!==void 0&&(r=r.eq("is_active",e.isActive)),e!=null&&e.searchQuery&&(r=r.or(`first_name.ilike.%${e.searchQuery}%,last_name.ilike.%${e.searchQuery}%,email.ilike.%${e.searchQuery}%,employee_number.ilike.%${e.searchQuery}%`)),e!=null&&e.sortBy?r=r.order(e.sortBy,{ascending:e.sortOrder==="asc"}):r=r.order("last_name",{ascending:!0}),e!=null&&e.limit&&(r=r.limit(e.limit)),e!=null&&e.offset&&(r=r.range(e.offset,e.offset+(e.limit||10)-1));const{data:i,error:t,count:a}=await r;return t?(console.error("Error fetching employees:",t),{employees:[],count:0,error:t.message}):{employees:await Promise.all(i.map(async m=>{if(m.user_id)try{const{data:d,error:o}=await n.from("profiles").select("avatar_url").eq("id",m.user_id).single();if(!o&&d&&d.avatar_url)return{...m,profile_image_url:d.avatar_url}}catch(d){console.error("Error fetching user profile:",d)}return m})),count:a||0}}catch(r){return console.error("Error in getEmployees:",r),{employees:[],count:0,error:r.message}}},y=async(s,e)=>{try{const{data:r,error:i}=await n.from("employees").select(`
        *,
        department:department_id (
          id,
          name,
          description
        ),
        position:position_id (
          id,
          title,
          description
        ),
        employment_type:employment_type_id (
          id,
          name,
          description
        ),
        government_ids:employee_government_ids (*)
      `).eq("organization_id",s).eq("id",e).single();if(i)return console.error("Error fetching employee:",i),{error:i.message};if(r.user_id)try{const{data:t,error:a}=await n.from("profiles").select("avatar_url").eq("id",r.user_id).single();!a&&t&&t.avatar_url&&(r.profile_image_url=t.avatar_url)}catch(t){console.error("Error fetching user profile:",t)}return{employee:r}}catch(r){return console.error("Error in getEmployeeById:",r),{error:r.message}}},f=async(s,e,r)=>{try{const i={...e};i.department_id===""&&(i.department_id=null),i.position_id===""&&(i.position_id=null),i.employment_type_id===""&&(i.employment_type_id=null);const{data:t,error:a}=await n.from("employees").insert({...i,organization_id:s}).select(`
        *,
        department:department_id (
          id,
          name,
          description
        ),
        position:position_id (
          id,
          title,
          description
        ),
        employment_type:employment_type_id (
          id,
          name,
          description
        )
      `).single();if(a)return console.error("Error creating employee:",a),{error:a.message};if(r&&t){const l={employee_id:t.id};r.sss_number!==void 0&&(l.sss_number=r.sss_number),r.philhealth_number!==void 0&&(l.philhealth_number=r.philhealth_number),r.pagibig_number!==void 0&&(l.pagibig_number=r.pagibig_number),r.tin_number!==void 0&&(l.tin_number=r.tin_number);const{error:m}=await n.from("employee_government_ids").insert(l);if(m)return console.error("Error creating government IDs:",m),{employee:t,error:"Employee created but government IDs could not be saved: "+m.message}}return{employee:t}}catch(i){return console.error("Error in createEmployee:",i),{error:i.message}}},g=async(s,e,r,i)=>{try{const t={...r};t.department_id===""&&(t.department_id=null),t.position_id===""&&(t.position_id=null),t.employment_type_id===""&&(t.employment_type_id=null);const{data:a,error:l}=await n.from("employees").update(t).eq("organization_id",s).eq("id",e).select(`
        *,
        department:department_id (
          id,
          name,
          description
        ),
        position:position_id (
          id,
          title,
          description
        ),
        employment_type:employment_type_id (
          id,
          name,
          description
        ),
        government_ids:employee_government_ids (*)
      `).single();if(l)return console.error("Error updating employee:",l),{error:l.message};if(i&&a){const{data:m,error:d}=await n.from("employee_government_ids").select("id").eq("employee_id",e).maybeSingle();if(d)return console.error("Error checking government IDs:",d),{employee:a,error:"Employee updated but error checking government IDs: "+d.message};if(m){const o={};o.sss_number=i.sss_number,o.philhealth_number=i.philhealth_number,o.pagibig_number=i.pagibig_number,o.tin_number=i.tin_number;const{data:_,error:c}=await n.from("employee_government_ids").update(o).eq("employee_id",e).select("*");if(c)return console.error("Error updating government IDs:",c),{employee:a,error:"Employee updated but government IDs could not be updated: "+c.message}}else{const o={employee_id:e};o.sss_number=i.sss_number,o.philhealth_number=i.philhealth_number,o.pagibig_number=i.pagibig_number,o.tin_number=i.tin_number;const{data:_,error:c}=await n.from("employee_government_ids").insert(o).select("*");if(c)return console.error("Error creating government IDs:",c),{employee:a,error:"Employee updated but government IDs could not be created: "+c.message}}}return{employee:a}}catch(t){return console.error("Error in updateEmployee:",t),{error:t.message}}},b=async(s,e)=>{try{const{error:r}=await n.from("employees").delete().eq("organization_id",s).eq("id",e);return r?(console.error("Error deleting employee:",r),{success:!1,error:r.message}):{success:!0}}catch(r){return console.error("Error in deleteEmployee:",r),{success:!1,error:r.message}}};export{y as a,f as c,b as d,p as g,g as u};
