import{ab as E,d as T,h as A,r as i,j as e,i as I,A as N,B as d,o as p,aK as F,a1 as L,b0 as M,E as U,K as q,L as C,ah as K,aj as W,e as x,_ as l}from"./index-C6AV3cVN.js";import{C as u}from"./Card-yj7fueH8.js";import{a as $,R as Q}from"./refund-CcMk-dC8.js";import{u as V}from"./currencyFormatter-BsFWv3sX.js";import{d as _}from"./formatters-Cypx7G-j.js";import{S as G}from"./SaleReceipt-jLjyCjTQ.js";import"./inventoryTransaction-1UXV5RDN.js";import"./floatInventory-k_pEQeIK.js";const le=()=>{const{id:j}=E(),f=T(),{currentOrganization:m}=A(),n=V(),[s,P]=i.useState(null),[g,H]=i.useState([]),[r,k]=i.useState({totalRefunded:0,refundCount:0,refundPercentage:0,hasProcessedRefunds:!1}),[z,y]=i.useState(!0),[v,h]=i.useState(null),[O,b]=i.useState(!1);i.useEffect(()=>{(async()=>{if(!(!m||!j)){y(!0),h(null);try{const{sale:t,error:c}=await $(m.id,j);c?h(c):t?(P(t),await B(t.id)):h("Sale not found")}catch(t){h(t.message||"An error occurred while fetching the sale")}finally{y(!1)}}})()},[m,j]);const B=async a=>{if(m)try{const t=await Q.getRefunds(m.id,{limit:100});if(t.success&&t.data){const c=t.data.filter(o=>o.original_sale_id===a);H(c);const R=c.filter(o=>o.status==="processed"),S=R.reduce((o,D)=>o+Number(D.total_amount),0);k({totalRefunded:S,refundCount:c.length,refundPercentage:s?S/s.total_amount*100:0,hasProcessedRefunds:R.length>0})}}catch(t){console.error("Error fetching refund data:",t)}},w=()=>{window.print()};return z?e.jsx("div",{className:"flex justify-center items-center min-h-screen",children:e.jsx(I,{size:"xl"})}):v?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(N,{color:"failure",children:[e.jsx("span",{className:"font-medium",children:"Error:"})," ",v]}),e.jsx("div",{className:"mt-4",children:e.jsxs(d,{color:"gray",onClick:()=>f("/sales/history"),children:[e.jsx(p,{className:"mr-2 h-5 w-5"}),"Back to Sales"]})})]}):s?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs(d,{color:"gray",onClick:()=>f("/sales/history"),className:"mr-4",children:[e.jsx(p,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold flex items-center",children:[e.jsx(F,{className:"mr-2 h-6 w-6"}),"Sale Details"]}),e.jsxs("p",{className:"text-gray-600",children:["Invoice: ",s.invoice_number]})]})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(d,{color:"light",onClick:()=>b(!0),children:[e.jsx(L,{className:"mr-2 h-5 w-5"}),"View Receipt"]}),e.jsxs(d,{color:"blue",onClick:w,children:[e.jsx(M,{className:"mr-2 h-5 w-5"}),"Print"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2",children:[e.jsxs(u,{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Sale Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(U,{className:"mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Sale Date"}),e.jsx("p",{className:"font-medium",children:_(s.sale_date)})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(q,{className:"mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Customer"}),e.jsx("p",{className:"font-medium",children:s.customer?e.jsx(C,{to:`/customers/details/${s.customer.id}`,className:"text-blue-600 hover:underline",children:s.customer.name}):"Walk-in Customer"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(K,{className:"mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Payment Method"}),e.jsx("p",{className:"font-medium",children:s.payment_method||"Cash"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(W,{className:"mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Status"}),e.jsx(x,{color:s.status==="completed"?"success":s.status==="cancelled"?"failure":s.status==="refunded"?"warning":"gray",children:s.status})]})]})]}),s.notes&&e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Notes"}),e.jsx("p",{className:"text-gray-700",children:s.notes})]})]}),e.jsxs(u,{className:"mt-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Items Sold"}),s.items&&s.items.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(l,{children:[e.jsxs(l.Head,{children:[e.jsx(l.HeadCell,{children:"Product"}),e.jsx(l.HeadCell,{children:"Quantity"}),e.jsx(l.HeadCell,{children:"Unit Price"}),e.jsx(l.HeadCell,{children:"Total"})]}),e.jsx(l.Body,{className:"divide-y",children:s.items.map((a,t)=>e.jsxs(l.Row,{className:"bg-white",children:[e.jsxs(l.Cell,{className:"font-medium",children:[a.product?e.jsx(C,{to:`/products/details/${a.product.id}`,className:"text-blue-600 hover:underline",children:a.product.name}):"Unknown Product",a.notes&&e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:a.notes})]}),e.jsx(l.Cell,{children:a.quantity}),e.jsx(l.Cell,{children:n(a.unit_price)}),e.jsx(l.Cell,{className:"font-medium",children:n(a.unit_price*a.quantity)})]},a.id||t))})]})}):e.jsx(N,{color:"info",children:"No items found for this sale."})]})]}),e.jsxs("div",{children:[e.jsxs(u,{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Sale Summary"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Subtotal:"}),e.jsx("span",{className:"font-medium",children:n(s.subtotal)})]}),s.discount_amount>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Discount:"}),e.jsxs("span",{className:"font-medium text-red-600",children:["-",n(s.discount_amount)]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Tax:"}),e.jsx("span",{className:"font-medium",children:n(s.tax_amount)})]}),e.jsx("div",{className:"border-t pt-3",children:e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-lg font-semibold",children:"Total:"}),e.jsx("span",{className:"text-lg font-bold text-green-600",children:n(s.total_amount)})]})})]}),r.refundCount>0&&e.jsxs("div",{className:"border-t pt-4 mt-4",children:[e.jsx("h3",{className:"text-lg font-semibold mb-3 text-red-600",children:"Refund Information"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Total Refunded:"}),e.jsxs("span",{className:"font-medium text-red-600",children:["-",n(r.totalRefunded)]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Refund Percentage:"}),e.jsxs("span",{className:"font-medium",children:[r.refundPercentage.toFixed(1),"%"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Net Amount:"}),e.jsx("span",{className:"font-medium text-green-600",children:n((s==null?void 0:s.total_amount)-r.totalRefunded)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Refund Count:"}),e.jsx("span",{className:"font-medium",children:r.refundCount})]}),e.jsx("div",{className:"pt-2",children:r.refundPercentage>=100?e.jsx(x,{color:"failure",size:"lg",children:"Fully Refunded"}):r.refundPercentage>0?e.jsx(x,{color:"warning",size:"lg",children:"Partially Refunded"}):e.jsx(x,{color:"gray",size:"lg",children:"Refund Pending"})})]})]})]}),g.length>0&&e.jsxs(u,{className:"mt-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Refund History"}),e.jsx("div",{className:"space-y-4",children:g.map((a,t)=>e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:a.refund_number}),e.jsx("p",{className:"text-sm text-gray-600",children:_(a.created_at)})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-medium text-red-600",children:["-",n(a.total_amount)]}),e.jsx(x,{color:a.status==="processed"?"success":a.status==="pending"?"warning":a.status==="rejected"?"failure":"gray",size:"sm",children:a.status})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Reason:"}),e.jsx("span",{className:"ml-2 capitalize",children:a.reason.replace(/_/g," ")})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Method:"}),e.jsx("span",{className:"ml-2 capitalize",children:a.refund_method.replace(/_/g," ")})]})]}),a.reason_notes&&e.jsxs("div",{className:"mt-2 text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Notes:"}),e.jsx("span",{className:"ml-2",children:a.reason_notes})]})]},a.id))})]})]})]}),O&&s&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Receipt Preview"}),e.jsx(d,{color:"gray",size:"sm",onClick:()=>b(!1),children:"Close"})]}),e.jsx(G,{sale:s,showPrintButton:!0,onPrint:w})]})})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(N,{color:"warning",children:e.jsx("span",{className:"font-medium",children:"Sale Not Found"})}),e.jsx("div",{className:"mt-4",children:e.jsxs(d,{color:"gray",onClick:()=>f("/sales/history"),children:[e.jsx(p,{className:"mr-2 h-5 w-5"}),"Back to Sales"]})})]})};export{le as default};
