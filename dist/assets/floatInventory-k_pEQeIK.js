import{s}from"./index-C6AV3cVN.js";const q=async c=>{try{const{data:t,error:e}=await s.from("units_of_measure").select("id").eq("organization_id",c).eq("code","pcs").single();if(!e&&t)return console.log("Found existing pcs UoM:",t.id),t.id;const{data:o,error:u}=await s.from("units_of_measure").insert({organization_id:c,code:"pcs",name:"Pieces",is_base_unit:!0,conversion_factor:1}).select("id").single();if(!u&&o)return console.log("Created new pcs UoM:",o.id),o.id;console.warn("Error creating pcs UoM:",u);const{data:r,error:g}=await s.from("units_of_measure").select("id").eq("organization_id",c).limit(1).single();if(!g&&r)return console.warn("Using fallback UoM:",r.id),r.id;console.warn("Could not find any UoM for organization:",c);const i=crypto.randomUUID();console.warn("Using temporary UoM ID:",i);try{await s.from("units_of_measure").insert({id:i,organization_id:c,code:"pcs",name:"Pieces",is_base_unit:!0,conversion_factor:1}),console.log("Created emergency pcs UoM with custom ID:",i)}catch(_){console.error("Failed to create emergency UoM, but will use the ID anyway:",_)}return i}catch(t){console.error("Error in getOrCreatePcsUom:",t);const e=crypto.randomUUID();return console.warn("Using emergency UoM ID due to error:",e),e}},D=async(c,t)=>{try{const{resolved:e,productId:o,limit:u=50,offset:r=0}=t||{};let g=s.from("float_inventory").select(`
        *,
        product:product_id(*),
        sale:sale_id(id, invoice_number, sale_date)
      `,{count:"exact"}).eq("organization_id",c).order("created_at",{ascending:!1}).range(r,r+u-1);e!==void 0&&(g=g.eq("resolved",e)),o&&(g=g.eq("product_id",o));const{data:i,error:_,count:y}=await g;if(_)throw new Error(_.message);return{floatItems:i||[],count:y||0}}catch(e){return console.error("Error fetching float inventory:",e),{floatItems:[],count:0,error:e.message}}},C=async c=>{try{const{data:t,error:e}=await s.rpc("get_float_inventory_summary",{p_organization_id:c});if(e)throw new Error(e.message);return{summary:t||[]}}catch(t){return console.error("Error fetching float inventory summary:",t),{summary:[],error:t.message}}},k=async(c,t,e="Manually resolved")=>{try{const{data:o,error:u}=await s.from("float_inventory").select("*").eq("id",c).single();if(u||!o)throw new Error((u==null?void 0:u.message)||"Float inventory item not found");let r=null;try{console.log("Checking product UoMs for product:",o.product_id),console.log("Checking product UoMs with product_id:",o.product_id,"and organization_id:",o.organization_id);const{data:d,error:a}=await s.from("product_uoms").select(`
          id,
          uom_id,
          uom:units_of_measurement!uom_id (id, code, name)
        `).eq("product_id",o.product_id).eq("organization_id",o.organization_id);if(a&&console.error("Error fetching product UoMs:",a),console.log("Product UoMs found:",(d==null?void 0:d.length)||0),d&&d.length>0){const f=d.find(p=>{var m;return((m=p.uom)==null?void 0:m.code)==="pcs"});f&&f.uom_id?(r=f.uom_id,console.log("Found pcs UoM for product:",r)):(r=d[0].uom_id,console.log("Using first product UoM:",r))}else{console.log("No product UoMs found, checking organization UoMs"),console.log("Checking for pcs UoM in organization:",o.organization_id);const{data:f,error:p}=await s.from("units_of_measurement").select("id, code, name").eq("organization_id",o.organization_id).eq("code","pcs").limit(1);if(!p&&f&&f.length>0){r=f[0].id,console.log("Found organization pcs UoM:",r);try{console.log("Creating product UoM association"),await s.from("product_uoms").insert({product_id:o.product_id,uom_id:r,organization_id:o.organization_id,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0}),console.log("Product UoM association created successfully")}catch(m){console.warn("Could not create product UoM association:",m)}}else{console.log("No organization pcs UoM found, checking any organization UoM"),console.log("Checking for any UoM in organization:",o.organization_id);const{data:m,error:h}=await s.from("units_of_measurement").select("id, code, name").eq("organization_id",o.organization_id).limit(1);if(!h&&m&&m.length>0){r=m[0].id,console.log("Using organization UoM:",m[0].code,r);try{console.log("Creating product UoM association"),await s.from("product_uoms").insert({product_id:o.product_id,uom_id:r,organization_id:o.organization_id,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0}),console.log("Product UoM association created successfully")}catch(v){console.warn("Could not create product UoM association:",v)}}else{console.log("No organization UoMs found, creating pcs UoM");try{console.log("Creating new pcs UoM in units_of_measurement table");const{data:v,error:w}=await s.from("units_of_measurement").insert({organization_id:o.organization_id,code:"pcs",name:"Pieces",description:"Individual units or items",is_active:!0}).select("id").single();if(w)throw console.error("Error creating pcs UoM:",w),new Error(`Failed to create UoM: ${w.message}`);if(!v)throw console.error("No UoM returned after creation"),new Error("No UoM returned after creation");r=v.id,console.log("Created new pcs UoM:",r);try{console.log("Creating product UoM association"),await s.from("product_uoms").insert({product_id:o.product_id,uom_id:r,organization_id:o.organization_id,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0}),console.log("Product UoM association created successfully")}catch(b){console.warn("Could not create product UoM association:",b)}}catch(v){console.error("Error creating UoM:",v),console.log("Trying to find any UoM in the system");const{data:w,error:b}=await s.from("units_of_measurement").select("id").limit(1);if(!b&&w&&w.length>0)r=w[0].id,console.log("Using system UoM as last resort:",r);else throw new Error("Could not find or create any UoM in the system")}}}}}catch(d){throw console.error("Error in UoM handling:",d),new Error(`UoM error: ${d.message}`)}if(!r)throw console.error("No valid UoM ID found after all attempts"),new Error("Could not find or create a valid UoM");console.log("Final valid UoM ID to use:",r);let g=null;console.log("Checking if inventory_transactions table exists");try{const{data:d,error:a}=await s.from("inventory_transactions").select("*").limit(1);if(a){console.error("Error checking inventory_transactions table:",a),console.warn("Inventory transactions table might not exist, skipping transaction creation");return}const f=d&&d.length>0&&"conversion_factor"in d[0],p={organization_id:o.organization_id,product_id:o.product_id,transaction_type:"adjustment",quantity:o.quantity,notes:`Manual resolution of float inventory: ${e}`,created_by:t,uom_id:r,...f?{conversion_factor:1}:{}};console.log("Creating inventory transaction with data:",p);const{data:m,error:h}=await s.from("inventory_transactions").insert(p).select("id").single();if(h)throw console.error("Error creating inventory transaction:",h),new Error(`Error creating inventory transaction: ${h.message}`);console.log("Inventory transaction created successfully"),g=(m==null?void 0:m.id)||null}catch(d){console.error("Error in inventory transaction creation:",d),console.log("Marking float as resolved despite transaction error");const{data:a,error:f}=await s.from("float_inventory").select("*").limit(1),p=a&&a.length>0&&"notes"in a[0],m=a&&a.length>0&&"resolution_type"in a[0],h=a&&a.length>0&&"resolution_notes"in a[0],v=a&&a.length>0&&"resolved_by"in a[0],{error:w}=await s.from("float_inventory").update({resolved:!0,resolved_at:new Date().toISOString(),...p?{notes:`Manually resolved with error: ${d.message}`}:{},...m?{resolution_type:"manual"}:{},...h?{resolution_notes:`Manual resolution attempted but failed: ${d.message}`}:{},...v?{resolved_by:t}:{}}).eq("id",c);if(w)throw console.error("Error updating float inventory:",w),new Error(`Error updating float inventory: ${w.message}`);return{success:!0,warning:`Float marked as resolved, but transaction failed: ${d.message}`}}const{data:i,error:_}=await s.from("float_inventory").select("*").limit(1),y=i&&i.length>0&&"notes"in i[0],n=i&&i.length>0&&"resolution_type"in i[0],U=i&&i.length>0&&"resolution_notes"in i[0],l=i&&i.length>0&&"resolution_transaction_id"in i[0],M=i&&i.length>0&&"resolved_by"in i[0];console.log("Column availability:",{hasNotesColumn:y,hasResolutionType:n,hasResolutionNotes:U,hasResolutionTransactionId:l,hasResolvedBy:M});const{error:E}=await s.from("float_inventory").update({resolved:!0,resolved_at:new Date().toISOString(),...y?{notes:`Manually resolved: ${e}`}:{},...n?{resolution_type:"manual"}:{},...U?{resolution_notes:e}:{},...l?{resolution_transaction_id:g}:{},...M?{resolved_by:t}:{}}).eq("id",c);if(E)throw new Error(`Error updating float inventory: ${E.message}`);return{success:!0}}catch(o){return console.error("Error resolving float inventory:",o),{success:!1,error:o.message}}},I=async c=>{try{const{data:t,error:e}=await s.from("organization_settings").select("inventory_settings").eq("organization_id",c).single();if(e)throw new Error(e.message);const o={allow_negative_inventory:!0,warn_on_low_inventory:!0,auto_create_purchase_requests:!0};return{settings:(t==null?void 0:t.inventory_settings)||o}}catch(t){return console.error("Error fetching inventory settings:",t),{settings:{allow_negative_inventory:!0,warn_on_low_inventory:!0,auto_create_purchase_requests:!0},error:t.message}}},F=async(c,t)=>{try{const{error:e}=await s.from("organization_settings").update({inventory_settings:t}).eq("organization_id",c);if(e)throw new Error(e.message);return{success:!0}}catch(e){return console.error("Error updating inventory settings:",e),{success:!1,error:e.message}}},P=async(c,t)=>{var e,o,u,r,g,i,_;try{let y=!1;try{const{data:a}=await s.from("float_inventory").select("resolved_by").limit(1);y=!0}catch(a){console.log("resolved_by column not available:",a.message),y=!1}let n,U;if(y){const a=await s.from("float_inventory").select(`
          id,
          product_id,
          quantity,
          sale_id,
          resolved,
          created_at,
          resolved_at,
          resolution_type,
          resolution_notes,
          notes,
          resolved_by,
          product:product_id(name, sku),
          sale:sale_id(
            id,
            invoice_number,
            sale_date,
            customer_id,
            customer:customer_id(id, name)
          )
        `).eq("organization_id",c).eq("id",t).single();n=a.data,U=a.error}else{console.log("Using fallback query without resolved_by column");const a=await s.from("float_inventory").select(`
          id,
          product_id,
          quantity,
          sale_id,
          resolved,
          created_at,
          resolved_at,
          resolution_type,
          resolution_notes,
          notes,
          product:product_id(name, sku),
          sale:sale_id(
            id,
            invoice_number,
            sale_date,
            customer_id,
            customer:customer_id(id, name)
          )
        `).eq("organization_id",c).eq("id",t).single();n=a.data,U=a.error}if(U)throw new Error(U.message);if(!n)return{floatItem:null,error:"Float inventory item not found"};const l=new Date(n.created_at),M=n.resolved_at?new Date(n.resolved_at):null,E=new Date;return{floatItem:{id:n.id,product_id:n.product_id,product_name:((e=n.product)==null?void 0:e.name)||"Unknown Product",product_sku:((o=n.product)==null?void 0:o.sku)||"",quantity:n.quantity,sale_id:n.sale_id,sale_number:((u=n.sale)==null?void 0:u.invoice_number)||"N/A",sale_date:((r=n.sale)==null?void 0:r.sale_date)||n.created_at,customer_id:((g=n.sale)==null?void 0:g.customer_id)||null,customer_name:((_=(i=n.sale)==null?void 0:i.customer)==null?void 0:_.name)||null,resolved:n.resolved,created_at:n.created_at,resolved_at:n.resolved_at,resolution_type:n.resolution_type,resolution_notes:n.resolution_notes||n.notes,resolved_by_id:y&&n.resolved_by?n.resolved_by:null,resolved_by_name:y&&n.resolved_by?"User":null,days_to_resolve:n.resolved&&M?Math.floor((M.getTime()-l.getTime())/(1e3*60*60*24)):null,days_unresolved:n.resolved?null:Math.floor((E.getTime()-l.getTime())/(1e3*60*60*24))}}}catch(y){return console.error("Error fetching float inventory item:",y),{floatItem:null,error:y.message}}},N=async(c,t)=>{try{const{startDate:e,endDate:o,productId:u,resolved:r}=t||{},{data:g,error:i}=await s.rpc("get_float_inventory_report",{p_organization_id:c,p_start_date:e||null,p_end_date:o||null,p_product_id:u||null,p_resolved:r===void 0?null:r});if(i){console.warn("Database function failed, falling back to direct query:",i.message);let _=s.from("float_inventory").select(`
          id,
          product_id,
          quantity,
          sale_id,
          resolved,
          created_at,
          resolved_at,
          resolution_type,
          resolution_notes,
          notes,
          product:product_id(name, sku),
          sale:sale_id(id, invoice_number, sale_date, customer:customer_id(name))
        `).eq("organization_id",c).order("created_at",{ascending:!1});e&&(_=_.gte("created_at",e)),o&&(_=_.lte("created_at",o)),u&&(_=_.eq("product_id",u)),r!==void 0&&(_=_.eq("resolved",r));const{data:y,error:n}=await _;if(n)throw new Error(n.message);return{report:(y||[]).map(l=>{var a,f,p,m,h,v;const M=new Date(l.created_at),E=l.resolved_at?new Date(l.resolved_at):null,d=new Date;return{id:l.id,product_id:l.product_id,product_name:((a=l.product)==null?void 0:a.name)||"Unknown Product",product_sku:((f=l.product)==null?void 0:f.sku)||"",quantity:l.quantity,sale_id:l.sale_id,sale_number:((p=l.sale)==null?void 0:p.invoice_number)||"N/A",sale_date:((m=l.sale)==null?void 0:m.sale_date)||l.created_at,customer_name:((v=(h=l.sale)==null?void 0:h.customer)==null?void 0:v.name)||"Walk-in Customer",resolved:l.resolved,created_at:l.created_at,resolved_at:l.resolved_at,resolution_type:l.resolution_type,resolution_notes:l.resolution_notes||l.notes,days_to_resolve:l.resolved&&E?Math.floor((E.getTime()-M.getTime())/(1e3*60*60*24)):null,days_unresolved:l.resolved?null:Math.floor((d.getTime()-M.getTime())/(1e3*60*60*24))}})}}return{report:g||[]}}catch(e){return console.error("Error fetching float inventory report:",e),{report:[],error:e.message}}},$=async(c,t)=>{try{const{startDate:e,endDate:o}=t||{},{data:u,error:r}=await s.rpc("get_float_inventory_summary_by_product",{p_organization_id:c,p_start_date:e||null,p_end_date:o||null});return r?(console.warn("Database function failed, returning empty summary:",r.message),{summary:[],error:`Database function not available: ${r.message}`}):{summary:u||[]}}catch(e){return console.error("Error fetching float inventory summary by product:",e),{summary:[],error:e.message}}},T=async(c,t)=>{try{const{startDate:e,endDate:o}=t||{},{data:u,error:r}=await s.rpc("get_float_inventory_summary_by_date",{p_organization_id:c,p_start_date:e||null,p_end_date:o||null});return r?(console.warn("Database function failed, returning empty summary:",r.message),{summary:[],error:`Database function not available: ${r.message}`}):{summary:u||[]}}catch(e){return console.error("Error fetching float inventory summary by date:",e),{summary:[],error:e.message}}};export{C as a,N as b,$ as c,T as d,P as e,I as f,D as g,q as h,k as r,F as u};
