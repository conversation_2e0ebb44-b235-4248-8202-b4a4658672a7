import{s as n}from"./index-C6AV3cVN.js";const z=async o=>{try{console.log("Checking if user has a profile:",o);const{data:r,error:e}=await n.from("profiles").select("*").eq("id",o).maybeSingle();return e?(console.error("Error checking user profile:",e),{hasProfile:!1,error:e.message}):{hasProfile:!!r,profile:r}}catch(r){return console.error("Error checking user profile:",r),{hasProfile:!1,error:r.message}}},y=async o=>{try{console.log("Checking if user has organizations:",o);const{data:r,error:e}=await n.from("organization_members").select(`
        *,
        organization:organizations(*)
      `).eq("user_id",o);return e?(console.error("Error checking user organizations:",e),{hasOrganizations:!1,error:e.message}):{hasOrganizations:r&&r.length>0,organizations:r}}catch(r){return console.error("Error checking user organizations:",r),{hasOrganizations:!1,error:r.message}}},h=async(o,r,e)=>{try{console.log("Creating profile for user:",o);const{data:t,error:a}=await n.from("profiles").insert({id:o,first_name:r,last_name:e}).select().single();if(a){console.error("Error creating user profile:",a),console.error("Profile error details:",{code:a.code,message:a.message,details:a.details,hint:a.hint}),console.log("Trying to create profile using bypass RLS function...");const{data:i,error:s}=await n.rpc("create_profile_bypass_rls",{user_id:o,first_name:r,last_name:e});return s?(console.error("Bypass RLS function error:",s),{success:!1,error:`Failed to create profile: ${s.message}`}):(console.log("Profile created successfully via bypass function:",i),i&&i.success?{success:!0,profile:i.profile}:{success:!1,error:i.error||"Unknown error creating profile"})}return{success:!0,profile:t}}catch(t){return console.error("Error creating user profile:",t),{success:!1,error:t.message}}},_=async(o,r)=>{try{console.log("Creating organization for user:",o);const{data:e}=await n.from("organization_members").select("organization_id, organizations(*)").eq("user_id",o).maybeSingle();if(e&&e.organizations)return console.log("User already has an organization:",e.organizations),{success:!0,organization:e.organizations};const t=Date.now();let a=r.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,"");const i=`${a}-${t}`;console.log("Creating organization with unique slug:",i);const s=await n.from("organizations").insert({name:r,slug:i,created_by:o}).select().single();if(s.error){console.error("Error creating organization:",s.error),console.error("Organization error details:",{code:s.error.code,message:s.error.message,details:s.error.details,hint:s.error.hint}),console.log("Trying to create organization using RPC...");try{console.log("Trying to create organization using bypass RLS function...");const f=Date.now(),p=`${a}-${f}`,{data:g,error:d}=await n.rpc("create_organization_bypass_rls",{org_name:r,org_slug:p,user_id:o});return d?(console.error("Bypass RLS function error:",d),{success:!1,error:`Failed to create organization: ${d.message}`}):(console.log("Organization created successfully via bypass function:",g),g&&g.success?{success:!0,organization:g.organization}:{success:!1,error:g.error||"Unknown error creating organization"})}catch(f){return console.error("RPC exception:",f),{success:!1,error:`Exception in RPC: ${f.message}`}}}const u=s.data;console.log("Organization created successfully:",u),console.log("Adding user as organization owner...");const c=await n.from("organization_members").insert({organization_id:u.id,user_id:o,role:"owner"});c.error?(console.error("Error adding user as organization owner:",c.error),console.error("Member error details:",{code:c.error.code,message:c.error.message,details:c.error.details,hint:c.error.hint})):console.log("User added as organization owner successfully"),console.log("Creating organization settings...");const l=await n.from("organization_settings").insert({organization_id:u.id,settings:{currency:"USD",tax_rate:0,business_hours:{monday:{open:"09:00",close:"17:00"},tuesday:{open:"09:00",close:"17:00"},wednesday:{open:"09:00",close:"17:00"},thursday:{open:"09:00",close:"17:00"},friday:{open:"09:00",close:"17:00"},saturday:{open:"",close:""},sunday:{open:"",close:""}}}});return l.error?(console.error("Error creating organization settings:",l.error),console.error("Settings error details:",{code:l.error.code,message:l.error.message,details:l.error.details,hint:l.error.hint})):console.log("Organization settings created successfully"),{success:!0,organization:u}}catch(e){return console.error("Error creating user organization:",e),{success:!1,error:e.message}}};export{y as checkUserOrganizations,z as checkUserProfile,_ as createUserOrganization,h as createUserProfile};
