var Pe=Object.defineProperty;var Ae=(g,n,t)=>n in g?Pe(g,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):g[n]=t;var ce=(g,n,t)=>Ae(g,typeof n!="symbol"?n+"":n,t);import{s as R,j as e,b1 as be,b2 as de,b3 as ue,M as J,e as E,h as Ue,r as L,b4 as ve,a7 as X,B as me,t as Ne,A as Ee,i as qe,b5 as $,b6 as k}from"./index-C6AV3cVN.js";import{C as D}from"./Card-yj7fueH8.js";import{s as Re,C as Qe,a as he}from"./react-apexcharts.min-gcvzl5Eq.js";import{g as $e,R as Se}from"./refund-CcMk-dC8.js";import{u as Ge}from"./currencyFormatter-BsFWv3sX.js";import{p as q}from"./index-DNTDRJcs.js";import{e as ee}from"./index-BJwYa9ck.js";import{f as Z}from"./index-qirzObrW.js";function we(){return ee(Date.now())}function Ce(){return Re(Date.now())}var Oe=(g=>(g.SIMPLE="simple",g.FIFO="fifo",g.LIFO="lifo",g.WEIGHTED_AVERAGE="weighted_average",g))(Oe||{});class te{constructor(n,t){ce(this,"organizationId");ce(this,"method");this.organizationId=n,this.method=t}getMethodName(){return this.method}async getInventoryLayers(n,t){try{let o=R.from("inventory_transactions").select(`
          id,
          product_id,
          quantity,
          created_at,
          transaction_type,
          reference_id,
          reference_type
        `).eq("organization_id",this.organizationId).eq("product_id",n).in("transaction_type",["purchase","receipt"]).gt("quantity",0).order("created_at",{ascending:!0});t&&(o=o.lte("created_at",t.toISOString()));const{data:l,error:d}=await o;if(d)return console.error("Error fetching inventory transactions:",d),[];if(!l||l.length===0)return[];const a=l.filter(s=>s.reference_type==="inventory_receipt_item"&&s.reference_id).map(s=>s.reference_id).filter(s=>s!==null);let m=new Map;if(a.length>0){const{data:s,error:b}=await R.from("inventory_receipt_items").select("id, unit_cost").in("id",a);!b&&s&&(m=new Map(s.map(y=>[y.id,y.unit_cost])))}const{data:c}=await R.from("products").select("cost_price").eq("id",n).eq("organization_id",this.organizationId).single(),h=(c==null?void 0:c.cost_price)||0;return l.map(s=>{let b=h;return s.reference_type==="inventory_receipt_item"&&s.reference_id&&(b=m.get(s.reference_id)||h),{id:s.id,productId:s.product_id,quantity:s.quantity,unitCost:b,totalCost:s.quantity*b,transactionDate:q(s.created_at),transactionId:s.reference_id||s.id,transactionType:s.transaction_type,remainingQuantity:s.quantity}})}catch(o){return console.error("Error in getInventoryLayers:",o),[]}}async getConsumedQuantity(n,t){try{const{data:o,error:l}=await R.from("inventory_transactions").select("quantity").eq("organization_id",this.organizationId).eq("product_id",n).eq("transaction_type","sale").lt("quantity",0).lte("created_at",t.toISOString());return l?(console.error("Error fetching consumed quantity:",l),0):Math.abs((o||[]).reduce((d,a)=>d+a.quantity,0))}catch(o){return console.error("Error in getConsumedQuantity:",o),0}}}class _e extends te{constructor(n){super(n,"simple")}async calculateCOGS(n){try{const t=[...new Set(n.map(c=>c.productId))],{data:o,error:l}=await R.from("products").select("id, cost_price").in("id",t).eq("organization_id",this.organizationId);if(l)return console.error("Error fetching product costs:",l),{totalCost:0,averageCost:0,layersUsed:[],method:this.method};const d=new Map((o==null?void 0:o.map(c=>[c.id,c.cost_price||0]))||[]);let a=0,m=0;for(const c of n){const h=d.get(c.productId)||0,s=c.quantity;a+=h*s,m+=s}return{totalCost:a,averageCost:m>0?a/m:0,layersUsed:[],method:this.method}}catch(t){return console.error("Error in SimpleCostingMethod:",t),{totalCost:0,averageCost:0,layersUsed:[],method:this.method}}}}class ke extends te{constructor(n){super(n,"fifo")}async calculateCOGS(n){try{const t=new Map;for(const a of n)t.has(a.productId)||t.set(a.productId,[]),t.get(a.productId).push(a);let o=0,l=0;const d=[];for(const[a,m]of t){const c=m.reduce((w,i)=>w+i.quantity,0),h=new Date(Math.max(...m.map(w=>w.saleDate.getTime()))),s=await this.getInventoryLayers(a,h),b=await this.getConsumedQuantity(a,h),y=this.adjustLayersForConsumption(s,b),O=this.applyFIFO(y,c);o+=O.cost,l+=c,d.push(...O.layersUsed)}return{totalCost:o,averageCost:l>0?o/l:0,layersUsed:d,method:this.method}}catch(t){return console.error("Error in FIFOCostingMethod:",t),{totalCost:0,averageCost:0,layersUsed:[],method:this.method}}}adjustLayersForConsumption(n,t){const o=[...n];let l=t;for(const d of o){if(l<=0)break;const a=Math.min(d.remainingQuantity,l);d.remainingQuantity-=a,l-=a}return o.filter(d=>d.remainingQuantity>0)}applyFIFO(n,t){let o=t,l=0;const d=[];for(const a of n){if(o<=0)break;const m=Math.min(a.remainingQuantity,o),c=m*a.unitCost;l+=c,o-=m,d.push({...a,quantity:m,totalCost:c,remainingQuantity:m})}return{cost:l,layersUsed:d}}}class He extends te{constructor(n){super(n,"lifo")}async calculateCOGS(n){try{const t=new Map;for(const a of n)t.has(a.productId)||t.set(a.productId,[]),t.get(a.productId).push(a);let o=0,l=0;const d=[];for(const[a,m]of t){const c=m.reduce((i,u)=>i+u.quantity,0),h=new Date(Math.max(...m.map(i=>i.saleDate.getTime()))),b=(await this.getInventoryLayers(a,h)).reverse(),y=await this.getConsumedQuantity(a,h),O=this.adjustLayersForConsumption(b,y),w=this.applyLIFO(O,c);o+=w.cost,l+=c,d.push(...w.layersUsed)}return{totalCost:o,averageCost:l>0?o/l:0,layersUsed:d,method:this.method}}catch(t){return console.error("Error in LIFOCostingMethod:",t),{totalCost:0,averageCost:0,layersUsed:[],method:this.method}}}adjustLayersForConsumption(n,t){const o=[...n];let l=t;for(const d of o){if(l<=0)break;const a=Math.min(d.remainingQuantity,l);d.remainingQuantity-=a,l-=a}return o.filter(d=>d.remainingQuantity>0)}applyLIFO(n,t){let o=t,l=0;const d=[];for(const a of n){if(o<=0)break;const m=Math.min(a.remainingQuantity,o),c=m*a.unitCost;l+=c,o-=m,d.push({...a,quantity:m,totalCost:c,remainingQuantity:m})}return{cost:l,layersUsed:d}}}class Be extends te{constructor(n){super(n,"weighted_average")}async calculateCOGS(n){try{const t=new Map;for(const a of n)t.has(a.productId)||t.set(a.productId,[]),t.get(a.productId).push(a);let o=0,l=0;const d=[];for(const[a,m]of t){const c=m.reduce((y,O)=>y+O.quantity,0),h=new Date(Math.max(...m.map(y=>y.saleDate.getTime()))),s=await this.getInventoryLayers(a,h),b=this.calculateWeightedAverage(s,c);o+=b.cost,l+=c,d.push(...b.layersUsed)}return{totalCost:o,averageCost:l>0?o/l:0,layersUsed:d,method:this.method}}catch(t){return console.error("Error in WeightedAverageCostingMethod:",t),{totalCost:0,averageCost:0,layersUsed:[],method:this.method}}}calculateWeightedAverage(n,t){var c;if(n.length===0)return{cost:0,layersUsed:[]};const o=n.reduce((h,s)=>h+s.remainingQuantity,0),l=n.reduce((h,s)=>h+s.remainingQuantity*s.unitCost,0),d=o>0?l/o:0,a=t*d,m=[{id:"weighted-average",productId:((c=n[0])==null?void 0:c.productId)||"",quantity:t,unitCost:d,totalCost:a,transactionDate:new Date,transactionId:"weighted-average",transactionType:"weighted_average",remainingQuantity:t}];return{cost:a,layersUsed:m}}}class H{static createMethod(n,t){switch(t){case"simple":return new _e(n);case"fifo":return new ke(n);case"lifo":return new He(n);case"weighted_average":return new Be(n);default:return new _e(n)}}static getAvailableMethods(){return[{value:"simple",label:"Simple Cost",description:"Uses product cost price (Free tier)"},{value:"fifo",label:"FIFO (First In, First Out)",description:"Uses oldest inventory costs first"},{value:"lifo",label:"LIFO (Last In, First Out)",description:"Uses newest inventory costs first"},{value:"weighted_average",label:"Weighted Average",description:"Uses average cost of all inventory"}]}}class ze{static async getCostingMethod(n){try{return"fifo"}catch(t){return console.error("Error getting costing method:",t),"simple"}}static async setCostingMethod(n,t,o){try{return console.log(`Setting costing method for ${n} to ${t} by ${o}`),!0}catch(l){return console.error("Error setting costing method:",l),!1}}}class We{static async calculateCOGS(n,t,o){try{const l=o||await ze.getCostingMethod(n),a=await H.createMethod(n,l).calculateCOGS(t);return console.log(`COGS calculated using ${l}:`,{totalCost:a.totalCost,averageCost:a.averageCost,layersUsed:a.layersUsed.length,method:a.method}),a}catch(l){return console.error("Error in CostingService.calculateCOGS:",l),{totalCost:0,averageCost:0,layersUsed:[],method:"simple"}}}}const Ve=({isOpen:g,onClose:n,salesData:t,costingMethod:o,sampleCalculations:l,dateRange:d})=>{const a=Ge(),m=[{id:"1",subtotal:150,discount_amount:10,loyalty_points_discount:5,tax_amount:16.2,total_amount:151.2},{id:"2",subtotal:200,discount_amount:0,loyalty_points_discount:0,tax_amount:24,total_amount:224},{id:"3",subtotal:138,discount_amount:8,loyalty_points_discount:0,tax_amount:15.6,total_amount:145.6}],c=[{total_amount:75.5},{total_amount:985.5}],h=(l==null?void 0:l.sampleSales)||m,s=(l==null?void 0:l.sampleRefunds)||c,b=i=>{switch(i){case"today":return"Today";case"7days":return"Last 7 Days";case"30days":return"Last 30 Days";case"90days":return"Last 90 Days";default:return"Current Period"}},y=[{name:"Gross Sales",icon:e.jsx(be,{className:"h-5 w-5 text-green-600"}),value:t.grossSales,color:"green",formula:"Sum of all sale subtotals (before any deductions)",calculation:"Subtotal₁ + Subtotal₂ + Subtotal₃ + ...",example:{breakdown:h.map((i,u)=>`Sale ${u+1}: ${a(i.subtotal)}`),total:h.reduce((i,u)=>i+u.subtotal,0)},description:"The total revenue from all sales before any discounts, taxes, or deductions are applied."},{name:"Refunds",icon:e.jsx(de,{className:"h-5 w-5 text-red-600 rotate-180"}),value:t.refunds,color:"red",formula:"Sum of all refunded amounts",calculation:"Refund₁ + Refund₂ + Refund₃ + ...",example:{breakdown:s.map((i,u)=>`Refund ${u+1}: ${a(i.total_amount)}`),total:s.reduce((i,u)=>i+u.total_amount,0)},description:"Total amount refunded to customers for returned or cancelled orders."},{name:"Discounts",icon:e.jsx(de,{className:"h-5 w-5 text-orange-600 rotate-180"}),value:t.discounts,color:"orange",formula:"Sum of all discount types applied to sales",calculation:"(Regular Discounts + Loyalty Points Discounts) for all sales",example:{breakdown:h.map((i,u)=>{const T=(i.discount_amount||0)+(i.loyalty_points_discount||0);return`Sale ${u+1}: ${a(i.discount_amount||0)} + ${a(i.loyalty_points_discount||0)} = ${a(T)}`}),total:h.reduce((i,u)=>i+(u.discount_amount||0)+(u.loyalty_points_discount||0),0)},description:"Total value of all discounts given, including regular discounts and loyalty point redemptions."},{name:"Net Sales",icon:e.jsx(ue,{className:"h-5 w-5 text-blue-600"}),value:t.netSales,color:"blue",formula:"Gross Sales - Total Discounts",calculation:"Gross Sales - Discounts",example:{breakdown:[`Gross Sales: ${a(h.reduce((i,u)=>i+u.subtotal,0))}`,`Total Discounts: ${a(h.reduce((i,u)=>i+(u.discount_amount||0)+(u.loyalty_points_discount||0),0))}`,"Net Sales = Gross Sales - Discounts"],total:h.reduce((i,u)=>i+u.subtotal,0)-h.reduce((i,u)=>i+(u.discount_amount||0)+(u.loyalty_points_discount||0),0)},description:"Revenue after discounts but before taxes. This represents the actual selling price of goods."},{name:"Total Amount",icon:e.jsx(be,{className:"h-5 w-5 text-purple-600"}),value:t.totalAmount,color:"purple",formula:"Sum of all final amounts received from customers",calculation:"Total Amount₁ + Total Amount₂ + Total Amount₃ + ...",example:{breakdown:h.map((i,u)=>`Sale ${u+1}: ${a(i.total_amount)}`),total:h.reduce((i,u)=>i+u.total_amount,0)},description:"The final amount received from customers, including taxes. This is the actual cash collected."},{name:"Cost of Goods",icon:e.jsx(ue,{className:"h-5 w-5 text-indigo-600"}),value:t.costOfGoods,color:"indigo",formula:`Calculated using ${o} costing method`,calculation:O(o),example:{breakdown:[`Method: ${o}`,"Data Source: sale_items.quantity (actual pieces sold)","NOT base_quantity (UOM conversion not used)","Reason: Sales are always in pieces, UOM only for purchases","Calculated based on inventory layers and purchase history"],total:t.costOfGoods},description:"The actual cost of products sold using sale_items.quantity (pieces sold), calculated with advanced costing methods. UOM conversions are NOT used since sales are always in pieces."},{name:"Gross Profit",icon:e.jsx(de,{className:"h-5 w-5 text-emerald-600"}),value:t.grossProfit,color:"emerald",formula:"Net Sales - Cost of Goods Sold",calculation:"Net Sales - COGS",example:{breakdown:[`Net Sales: ${a(t.netSales)}`,`Cost of Goods: ${a(t.costOfGoods)}`,"Gross Profit = Net Sales - COGS"],total:t.netSales-t.costOfGoods},description:"The profit remaining after deducting the cost of goods sold from net sales. This shows the profitability of your products."}];function O(i){switch(i.toLowerCase()){case"fifo":case"fifo (first in, first out)":return"Uses oldest inventory costs first (First In, First Out)";case"lifo":case"lifo (last in, first out)":return"Uses newest inventory costs first (Last In, First Out)";case"weighted_average":case"weighted average":return"Uses average cost of all available inventory";case"simple":case"simple cost":return"Uses product cost_price × quantity sold";default:return"Advanced costing method based on inventory transactions"}}const w=i=>({green:"bg-green-50 border-green-200 text-green-800",red:"bg-red-50 border-red-200 text-red-800",orange:"bg-orange-50 border-orange-200 text-orange-800",blue:"bg-blue-50 border-blue-200 text-blue-800",purple:"bg-purple-50 border-purple-200 text-purple-800",indigo:"bg-indigo-50 border-indigo-200 text-indigo-800",emerald:"bg-emerald-50 border-emerald-200 text-emerald-800"})[i]||"bg-gray-50 border-gray-200 text-gray-800";return e.jsxs(J,{show:g,onClose:n,size:"7xl",children:[e.jsx(J.Header,{className:"border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ue,{className:"h-6 w-6 text-blue-600"}),e.jsx("span",{className:"text-xl font-semibold",children:"Sales Metrics Explained"})]})}),e.jsx(J.Body,{className:"p-0",children:e.jsxs("div",{className:"max-h-[80vh] overflow-y-auto p-6",children:[e.jsxs("div",{className:"mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[e.jsx("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"📊 Understanding Your Sales Metrics"}),e.jsx("p",{className:"text-blue-800 text-sm mb-3",children:"This guide explains how each metric is calculated, shows the formulas used, and provides sample calculations based on your actual data structure."}),e.jsxs("div",{className:"text-sm text-blue-700",children:[e.jsx("strong",{children:"Current Period:"})," ",b(d)," |",e.jsx("strong",{children:" Sales Count:"})," ",t.salesCount," transactions"]})]}),e.jsxs("div",{className:"mb-6 p-4 bg-green-50 rounded-lg border border-green-200",children:[e.jsx("h3",{className:"text-lg font-semibold text-green-900 mb-3",children:"🔄 Calculation Flow"}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2 text-sm",children:[e.jsx(E,{color:"green",children:"Gross Sales"}),e.jsx("span",{children:"→"}),e.jsx(E,{color:"orange",children:"- Discounts"}),e.jsx("span",{children:"="}),e.jsx(E,{color:"blue",children:"Net Sales"}),e.jsx("span",{children:"→"}),e.jsx(E,{color:"indigo",children:"- Cost of Goods"}),e.jsx("span",{children:"="}),e.jsx(E,{color:"emerald",children:"Gross Profit"})]}),e.jsx("div",{className:"mt-2 text-xs text-green-700",children:"Total Amount = Net Sales + Taxes | Refunds = Separate tracking"})]}),e.jsx("div",{className:"space-y-6",children:y.map((i,u)=>e.jsx(D,{className:`border-2 ${w(i.color)}`,children:e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[i.icon,e.jsx("h4",{className:"text-lg font-semibold",children:i.name}),e.jsx(E,{color:i.color,size:"sm",children:a(i.value)})]})}),e.jsx("p",{className:"text-sm mb-4 opacity-90",children:i.description}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-semibold mb-2 flex items-center gap-2",children:"📐 Formula"}),e.jsx("div",{className:"bg-white bg-opacity-50 p-3 rounded border",children:e.jsx("code",{className:"text-sm font-mono",children:i.formula})}),e.jsx("div",{className:"mt-2 bg-white bg-opacity-50 p-3 rounded border",children:e.jsx("code",{className:"text-sm font-mono",children:i.calculation})})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-semibold mb-2 flex items-center gap-2",children:"🧮 Sample Calculation"}),e.jsxs("div",{className:"bg-white bg-opacity-50 p-3 rounded border space-y-1",children:[i.example.breakdown.map((T,se)=>e.jsx("div",{className:"text-sm font-mono",children:T},se)),e.jsx("div",{className:"border-t pt-2 mt-2",children:e.jsxs("div",{className:"text-sm font-mono font-semibold",children:["Result: ",a(i.example.total)]})})]})]})]})]})},u))}),e.jsxs("div",{className:"mt-8 p-4 bg-purple-50 rounded-lg border border-purple-200",children:[e.jsx("h3",{className:"text-lg font-semibold text-purple-900 mb-3",children:"📈 Your Current Period Results"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:a(t.grossSales)}),e.jsx("div",{className:"text-sm text-purple-700",children:"Gross Sales"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:a(t.netSales)}),e.jsx("div",{className:"text-sm text-blue-700",children:"Net Sales"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-indigo-600",children:a(t.costOfGoods)}),e.jsx("div",{className:"text-sm text-indigo-700",children:"Cost of Goods"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-emerald-600",children:a(t.grossProfit)}),e.jsx("div",{className:"text-sm text-emerald-700",children:"Gross Profit"})]})]}),e.jsx("div",{className:"mt-4 text-center",children:e.jsxs("div",{className:"text-sm text-purple-700",children:[e.jsx("strong",{children:"Profit Margin:"})," ",t.netSales>0?(t.grossProfit/t.netSales*100).toFixed(1):"0","% |",e.jsx("strong",{children:" Costing Method:"})," ",o]})})]}),e.jsxs("div",{className:"mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200",children:[e.jsx("h3",{className:"text-lg font-semibold text-yellow-900 mb-3",children:"📊 Percentage Calculations Explained"}),e.jsx("p",{className:"text-yellow-800 text-sm mb-4",children:"The percentage changes shown below each metric compare the current period with the previous period using this formula:"}),e.jsxs("div",{className:"bg-white bg-opacity-50 p-4 rounded border mb-4",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"📐 Percentage Change Formula:"}),e.jsx("code",{className:"text-sm font-mono block mb-2",children:"Percentage Change = ((Current Value - Previous Value) / Previous Value) × 100"}),e.jsxs("div",{className:"mt-3 space-y-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"🟢 Positive %:"})," Indicates growth/increase from previous period"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"🔴 Negative %:"})," Indicates decline/decrease from previous period"]}),e.jsx("div",{children:e.jsx("strong",{children:"⚠️ Special Cases:"})}),e.jsxs("ul",{className:"list-disc list-inside ml-4 space-y-1",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Refunds:"})," Lower refunds = Good (green), Higher refunds = Bad (red)"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Discounts:"})," Higher discounts = Caution (red), Lower discounts = Good (green)"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Sales/Profit:"})," Higher = Good (green), Lower = Bad (red)"]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-white bg-opacity-50 p-3 rounded border",children:[e.jsx("h5",{className:"font-semibold mb-2",children:"🧮 Example Calculation:"}),e.jsxs("div",{className:"text-sm font-mono space-y-1",children:[e.jsxs("div",{children:["Current Gross Sales: ",a(t.grossSales)]}),e.jsxs("div",{children:["Previous Gross Sales: ",a(t.grossSales*.85)]}),e.jsxs("div",{children:["Calculation: ((",a(t.grossSales)," - ",a(t.grossSales*.85),") / ",a(t.grossSales*.85),") × 100"]}),e.jsxs("div",{className:"font-semibold",children:["Result: ",((t.grossSales-t.grossSales*.85)/(t.grossSales*.85)*100).toFixed(1),"%"]})]})]}),e.jsxs("div",{className:"bg-white bg-opacity-50 p-3 rounded border",children:[e.jsx("h5",{className:"font-semibold mb-2",children:"📈 Interpretation Guide:"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"0% to 5%:"})," Stable performance"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"5% to 15%:"})," Moderate growth/decline"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"15% to 30%:"})," Significant change"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"30%+:"})," Major change (investigate)"]}),e.jsx("div",{className:"mt-2 text-xs text-gray-600",children:e.jsx("em",{children:"Note: Percentages are calculated against simulated previous period data for demonstration."})})]})]})]})]}),e.jsxs("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"💡 Additional Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Calculation Order:"}),e.jsxs("ol",{className:"list-decimal list-inside space-y-1",children:[e.jsx("li",{children:"Gross Sales (base revenue)"}),e.jsx("li",{children:"Discounts (deductions)"}),e.jsx("li",{children:"Net Sales (after discounts)"}),e.jsx("li",{children:"Cost of Goods (using costing method)"}),e.jsx("li",{children:"Gross Profit (Net Sales - COGS)"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Key Relationships:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[e.jsx("li",{children:"Net Sales = Gross Sales - Discounts"}),e.jsx("li",{children:"Gross Profit = Net Sales - COGS"}),e.jsx("li",{children:"Total Amount includes taxes"}),e.jsx("li",{children:"COGS varies by costing method"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"COGS Data Source:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Table:"})," sale_items"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Quantity:"})," quantity field (pcs sold)"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"NOT base_quantity"})," (UOM conversion)"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Reason:"})," Sales are always in pieces"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"UOM:"})," Only used for purchases"]})]})]})]})]})]})}),e.jsx(J.Footer,{className:"border-t border-gray-200",children:e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:n,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors",children:"Close"})})})]})},rt=({className:g="",showHeader:n=!0})=>{var xe,ge;const{currentOrganization:t}=Ue(),o=Ge(),[l,d]=L.useState(!1),[a,m]=L.useState(null),[c,h]=L.useState("today"),[s,b]=L.useState({grossSales:0,refunds:0,discounts:0,netSales:0,totalAmount:0,costOfGoods:0,grossProfit:0,salesCount:0,hourlyData:[],dailyData:[]}),[y,O]=L.useState(Oe.FIFO),[w,i]=L.useState(!1),[u,T]=L.useState(!1),se=r=>{const x=new Date;let N,f;switch(r){case"today":N=Ce(),f=we();break;case"7days":N=he(x,7),f=ee(x);break;case"30days":N=he(x,30),f=ee(x);break;case"90days":N=he(x,90),f=ee(x);break;default:N=Ce(),f=we()}return{startDate:N,endDate:f}},B=async()=>{if(t){d(!0),m(null);try{const{startDate:r,endDate:x}=se(c),{sales:N,error:f}=await $e(t.id,{startDate:r.toISOString(),endDate:x.toISOString(),limit:1e3,sortBy:"sale_date",sortOrder:"desc"});if(f)throw new Error(f);const U=await Se.getRefundSummary(t.id,r.toISOString(),x.toISOString()),S=await Se.getRefunds(t.id,{filters:{date_from:r.toISOString(),date_to:x.toISOString()},limit:1e3}),C=S.success?S.data:[],z=await Me(N,r,x,U,C);b(z)}catch(r){console.error("Error fetching sales data:",r),m(r.message||"Failed to fetch sales data")}finally{d(!1)}}},ae=async r=>{if(!r.length||!(t!=null&&t.id))return 0;try{i(!0);const x=r.map(C=>C.id).filter(C=>!!C);if(x.length===0)return 0;const{data:N,error:f}=await R.from("sale_items").select(`
          id,
          quantity,
          base_quantity,
          product_id,
          sale_id,
          sales!inner(sale_date)
        `).in("sale_id",x);if(f||!N)return console.warn("Could not fetch sale items for COGS calculation:",f),0;const U=N.map(C=>({id:C.id,productId:C.product_id,quantity:C.quantity,baseQuantity:C.quantity,saleDate:q(C.sales.sale_date)})),S=await We.calculateCOGS(t.id,U,y);return console.log("COGS calculation result:",{method:S.method,totalCost:S.totalCost,layersUsed:S.layersUsed.length}),S.totalCost}catch(x){return console.error("Error calculating COGS:",x),0}finally{i(!1)}},Me=async(r,x,N,f,U=[])=>{const S=r.filter(M=>M.status==="completed"),C=S.reduce((M,j)=>M+j.subtotal,0),z=S.reduce((M,j)=>M+(j.discount_amount||0)+(j.loyalty_points_discount||0),0),pe=C-z,Fe=S.reduce((M,j)=>M+j.total_amount,0),Le=(f==null?void 0:f.total_amount)||0,ye=await ae(S),Te=pe-ye;let fe=[],je=[];if(c==="today"){const M=Array.from({length:24},(j,I)=>I);fe=await Promise.all(M.map(async j=>{const I=S.filter(_=>q(_.sale_date).getHours()===j),P=I.reduce((_,p)=>_+p.subtotal,0),G=I.reduce((_,p)=>_+(p.discount_amount||0)+(p.loyalty_points_discount||0),0),Q=P-G,W=I.reduce((_,p)=>_+p.total_amount,0),V=U.filter(_=>q(_.created_at).getHours()===j).reduce((_,p)=>_+Number(p.total_amount),0);let A;j===0?A="12 AM":j<12?A=`${j} AM`:j===12?A="12 PM":A=`${j-12} PM`;const Y=await ae(I),K=Q-Y;return{hour:A,grossSales:P,netSales:Q,totalAmount:W,discounts:G,refunds:V,costOfGoods:Y,grossProfit:K,salesCount:I.length}}))}else{const M=Math.ceil((N.getTime()-x.getTime())/864e5),j=Array.from({length:M+1},(I,P)=>{const G=new Date(x);return G.setDate(G.getDate()+P),G});je=await Promise.all(j.map(async I=>{const P=Z(I,"yyyy-MM-dd"),G=S.filter(p=>Z(q(p.sale_date),"yyyy-MM-dd")===P),Q=G.reduce((p,F)=>p+F.subtotal,0),W=G.reduce((p,F)=>p+(F.discount_amount||0)+(F.loyalty_points_discount||0),0),V=Q-W,A=G.reduce((p,F)=>p+F.total_amount,0),Y=U.filter(p=>Z(q(p.created_at),"yyyy-MM-dd")===P).reduce((p,F)=>p+Number(F.total_amount),0),K=await ae(G),_=V-K;return{date:Z(I,"MMM dd"),grossSales:Q,netSales:V,totalAmount:A,discounts:W,refunds:Y,costOfGoods:K,grossProfit:_,salesCount:G.length}}))}return{grossSales:C,refunds:Le,discounts:z,netSales:pe,totalAmount:Fe,costOfGoods:ye,grossProfit:Te,salesCount:S.length,hourlyData:fe,dailyData:je}},Ie={chart:{type:"area",height:300,toolbar:{show:!1},zoom:{enabled:!1}},colors:["#10B981","#3B82F6","#F59E0B","#8B5CF6","#EF4444"],dataLabels:{enabled:!1},stroke:{curve:"smooth",width:2},fill:{type:"gradient",gradient:{shadeIntensity:1,opacityFrom:.3,opacityTo:.1,stops:[0,90,100]}},grid:{borderColor:"#E5E7EB",strokeDashArray:3},xaxis:{categories:c==="today"?s.hourlyData.map(r=>r.hour):s.dailyData.map(r=>r.date),labels:{style:{colors:"#6B7280",fontSize:"12px"},rotate:c==="today"?-45:0}},yaxis:{labels:{style:{colors:"#6B7280",fontSize:"12px"},formatter:r=>o(r,!1)}},tooltip:{theme:"light",y:{formatter:r=>o(r)}},legend:{position:"top",horizontalAlign:"right"}},De=[{name:"Gross Sales",data:c==="today"?s.hourlyData.map(r=>r.grossSales):s.dailyData.map(r=>r.grossSales)},{name:"Net Sales",data:c==="today"?s.hourlyData.map(r=>r.netSales):s.dailyData.map(r=>r.netSales)},{name:"Discounts",data:c==="today"?s.hourlyData.map(r=>r.discounts):s.dailyData.map(r=>r.discounts)},{name:"Cost of Goods",data:c==="today"?s.hourlyData.map(r=>r.costOfGoods):s.dailyData.map(r=>r.costOfGoods)},{name:"Refunds",data:c==="today"?s.hourlyData.map(r=>r.refunds):s.dailyData.map(r=>r.refunds)}];L.useEffect(()=>{B()},[t,c]),L.useEffect(()=>{s.salesCount>0&&B()},[y]);const v=(r,x)=>x===0?0:(r-x)/x*100,re=s.grossSales*.85,oe=s.netSales*.88,ne=s.discounts*1.15,le=s.refunds*.75,ie=s.grossProfit*.82;return e.jsxs("div",{className:`space-y-6 ${g}`,children:[n&&e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Sales Summary"}),e.jsx("button",{onClick:()=>T(!0),className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"View metrics explanation",children:e.jsx(ve,{className:"h-5 w-5"})})]}),e.jsx("p",{className:"text-gray-600",children:"Track your sales performance and trends"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(X,{value:y,onChange:r=>O(r.target.value),className:"w-48",title:"Costing Method",children:H.getAvailableMethods().map(r=>e.jsx("option",{value:r.value,children:r.label},r.value))}),e.jsxs(X,{value:c,onChange:r=>h(r.target.value),className:"w-40",children:[e.jsx("option",{value:"today",children:"Today"}),e.jsx("option",{value:"7days",children:"Last 7 days"}),e.jsx("option",{value:"30days",children:"Last 30 days"}),e.jsx("option",{value:"90days",children:"Last 90 days"})]}),e.jsx(me,{color:"light",onClick:B,disabled:l||w,children:e.jsx(Ne,{className:`h-4 w-4 ${l||w?"animate-spin":""}`})})]})]}),!n&&e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("button",{onClick:()=>T(!0),className:"flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-blue-600 transition-colors",title:"View metrics explanation",children:[e.jsx(ve,{className:"h-4 w-4"}),e.jsx("span",{children:"How are these calculated?"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(X,{value:y,onChange:r=>O(r.target.value),className:"w-48",title:"Costing Method",children:H.getAvailableMethods().map(r=>e.jsx("option",{value:r.value,children:r.label},r.value))}),e.jsxs(X,{value:c,onChange:r=>h(r.target.value),className:"w-40",children:[e.jsx("option",{value:"today",children:"Today"}),e.jsx("option",{value:"7days",children:"Last 7 days"}),e.jsx("option",{value:"30days",children:"Last 30 days"}),e.jsx("option",{value:"90days",children:"Last 90 days"})]}),e.jsx(me,{color:"light",onClick:B,disabled:l||w,children:e.jsx(Ne,{className:`h-4 w-4 ${l||w?"animate-spin":""}`})})]})]}),a&&e.jsxs(Ee,{color:"failure",children:[e.jsx("span",{className:"font-medium",children:"Error:"})," ",a]}),l?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(qe,{size:"xl"}),e.jsx("span",{className:"ml-3",children:"Loading sales data..."})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-7 gap-4",children:[e.jsx(D,{className:"p-4",children:e.jsx("div",{className:"flex justify-between items-start",children:e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Gross Sales"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:o(s.grossSales)}),e.jsxs("div",{className:"flex items-center mt-1",children:[v(s.grossSales,re)>=0?e.jsx($,{className:"h-4 w-4 text-green-500 mr-1"}):e.jsx(k,{className:"h-4 w-4 text-red-500 mr-1"}),e.jsxs("span",{className:`text-sm ${v(s.grossSales,re)>=0?"text-green-600":"text-red-600"}`,children:[Math.abs(v(s.grossSales,re)).toFixed(1),"%"]})]})]})})}),e.jsx(D,{className:"p-4",children:e.jsx("div",{className:"flex justify-between items-start",children:e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Refunds"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:o(s.refunds)}),e.jsxs("div",{className:"flex items-center mt-1",children:[v(s.refunds,le)>=0?e.jsx($,{className:"h-4 w-4 text-red-500 mr-1"}):e.jsx(k,{className:"h-4 w-4 text-green-500 mr-1"}),e.jsxs("span",{className:`text-sm ${v(s.refunds,le)>=0?"text-red-600":"text-green-600"}`,children:[Math.abs(v(s.refunds,le)).toFixed(1),"%"]})]})]})})}),e.jsx(D,{className:"p-4",children:e.jsx("div",{className:"flex justify-between items-start",children:e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Discounts"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:o(s.discounts)}),e.jsxs("div",{className:"flex items-center mt-1",children:[v(s.discounts,ne)>=0?e.jsx($,{className:"h-4 w-4 text-red-500 mr-1"}):e.jsx(k,{className:"h-4 w-4 text-green-500 mr-1"}),e.jsxs("span",{className:`text-sm ${v(s.discounts,ne)>=0?"text-red-600":"text-green-600"}`,children:[Math.abs(v(s.discounts,ne)).toFixed(1),"%"]})]})]})})}),e.jsx(D,{className:"p-4",children:e.jsx("div",{className:"flex justify-between items-start",children:e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Net Sales"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:o(s.netSales)}),e.jsxs("div",{className:"flex items-center mt-1",children:[v(s.netSales,oe)>=0?e.jsx($,{className:"h-4 w-4 text-green-500 mr-1"}):e.jsx(k,{className:"h-4 w-4 text-red-500 mr-1"}),e.jsxs("span",{className:`text-sm ${v(s.netSales,oe)>=0?"text-green-600":"text-red-600"}`,children:[Math.abs(v(s.netSales,oe)).toFixed(1),"%"]})]})]})})}),e.jsx(D,{className:"p-4",children:e.jsx("div",{className:"flex justify-between items-start",children:e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total Amount"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:o(s.totalAmount)}),e.jsx("div",{className:"flex items-center mt-1",children:e.jsx("span",{className:"text-sm text-gray-500",children:"Final received"})})]})})}),e.jsx(D,{className:"p-4",children:e.jsx("div",{className:"flex justify-between items-start",children:e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Cost of Goods"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:o(s.costOfGoods)}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx("span",{className:"text-sm text-gray-500",children:((xe=H.getAvailableMethods().find(r=>r.value===y))==null?void 0:xe.label)||"Simple Cost"}),w&&e.jsx("div",{className:"ml-2",children:e.jsx("div",{className:"animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"})})]})]})})}),e.jsx(D,{className:"p-4",children:e.jsx("div",{className:"flex justify-between items-start",children:e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Gross Profit"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:o(s.grossProfit)}),e.jsxs("div",{className:"flex items-center mt-1",children:[v(s.grossProfit,ie)>=0?e.jsx($,{className:"h-4 w-4 text-green-500 mr-1"}):e.jsx(k,{className:"h-4 w-4 text-red-500 mr-1"}),e.jsxs("span",{className:`text-sm ${v(s.grossProfit,ie)>=0?"text-green-600":"text-red-600"}`,children:[Math.abs(v(s.grossProfit,ie)).toFixed(1),"%"]})]})]})})})]}),e.jsxs(D,{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:c==="today"?"Today's Sales (Hourly)":"Sales Trend"}),e.jsx("div",{className:"flex items-center space-x-4 text-sm",children:e.jsxs("span",{className:"text-gray-600",children:["Total Sales: ",e.jsx("strong",{children:s.salesCount})]})})]}),e.jsx(Qe,{options:Ie,series:De,type:"area",height:300})]}),e.jsxs(D,{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:c==="today"?"Hourly Breakdown":"Daily Breakdown"}),e.jsx(me,{color:"light",size:"sm",children:"Export Data"})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm text-left",children:[e.jsx("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3",children:c==="today"?"Hour":"Date"}),e.jsx("th",{className:"px-6 py-3",children:"Gross Sales"}),e.jsx("th",{className:"px-6 py-3",children:"Discounts"}),e.jsx("th",{className:"px-6 py-3",children:"Net Sales"}),e.jsx("th",{className:"px-6 py-3",children:"Total Amount"}),e.jsx("th",{className:"px-6 py-3",children:"Cost of Goods"}),e.jsx("th",{className:"px-6 py-3",children:"Gross Profit"})]})}),e.jsx("tbody",{children:(c==="today"?s.hourlyData:s.dailyData).map((r,x)=>{const N=r.costOfGoods,f=r.grossProfit;return e.jsxs("tr",{className:"bg-white border-b hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 font-medium text-gray-900",children:c==="today"?r.hour:r.date}),e.jsx("td",{className:"px-6 py-4",children:o(r.grossSales)}),e.jsx("td",{className:"px-6 py-4 text-red-600",children:o(r.discounts)}),e.jsx("td",{className:"px-6 py-4 font-semibold",children:o(r.netSales)}),e.jsx("td",{className:"px-6 py-4 text-blue-600 font-semibold",children:o(r.totalAmount)}),e.jsx("td",{className:"px-6 py-4",children:o(N)}),e.jsx("td",{className:"px-6 py-4 text-green-600 font-semibold",children:o(f)})]},x)})})]})})]})]}),e.jsx(Ve,{isOpen:u,onClose:()=>T(!1),salesData:s,costingMethod:((ge=H.getAvailableMethods().find(r=>r.value===y))==null?void 0:ge.label)||"Simple Cost",dateRange:c})]})};export{rt as S};
