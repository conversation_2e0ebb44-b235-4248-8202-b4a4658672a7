import{s as h}from"./index-C6AV3cVN.js";const p=async(n,s)=>{var r,a,t;try{console.log("Fetching purchase requests for organization:",n);let o=h.from("purchase_requests").select(`
        *,
        items:purchase_request_items(
          *,
          product:product_id(*),
          uom:uom_id(*)
        )
      `).eq("organization_id",n);s!=null&&s.status&&(o=o.eq("status",s.status)),s!=null&&s.searchQuery&&(o=o.ilike("request_number",`%${s.searchQuery}%`)),o=o.order("created_at",{ascending:!1});const{data:u,error:m}=await o;if(m)return console.error("Error fetching purchase requests:",m),{purchaseRequests:[],error:m.message};if(!u||u.length===0)return{purchaseRequests:[]};console.log("Found purchase requests:",u.length);const d=[...new Set(u.map(c=>c.requester_id))];let q={};if(d.length>0)try{console.log("Fetching user profiles for requester IDs:",d);const{data:c,error:l}=await h.from("profiles").select("id, first_name, last_name").in("id",d);console.log("Profiles query result:",{profiles:c,error:l});try{const{data:e}=await h.auth.getSession();console.log("Session check for requesters:",{sessionUserId:(a=(r=e==null?void 0:e.session)==null?void 0:r.user)==null?void 0:a.id,requesterIds:d}),(t=e==null?void 0:e.session)!=null&&t.user&&d.includes(e.session.user.id)&&(q[e.session.user.id]={...q[e.session.user.id],display_name:"You"})}catch(e){console.warn("Could not check session:",e)}c&&c.length>0&&c.forEach(e=>{let f=`User ${e.id.substring(0,8)}...`;e.first_name&&e.last_name?f=`${e.first_name} ${e.last_name}`:e.first_name&&(f=e.first_name),q[e.id]={...q[e.id],display_name:f}}),d.forEach(e=>{q[e]||(q[e]={display_name:`User ${e.substring(0,6)}`,email:null})}),console.log("Final requester info:",q)}catch(c){console.warn("Could not fetch requester profiles:",c)}return{purchaseRequests:u.map(c=>{const l=q[c.requester_id]||{};return{...c,requester_name:l.display_name||l.email||"Unknown User"}})}}catch(o){return console.error("Error in getPurchaseRequests:",o),{purchaseRequests:[],error:o.message}}},g=async(n,s)=>{var r,a,t,o;try{console.log("Fetching purchase request by ID:",s);const{data:u,error:m}=await h.from("purchase_requests").select(`
        *,
        items:purchase_request_items(
          *,
          product:product_id(*),
          uom:uom_id(*)
        )
      `).eq("organization_id",n).eq("id",s).single();if(m)return console.error("Error fetching purchase request:",m),{error:m.message};let d=`User ${u.requester_id.substring(0,6)}`;try{console.log("Fetching requester profile for ID:",u.requester_id);const{data:i,error:c}=await h.from("profiles").select("id, first_name, last_name, email").eq("id",u.requester_id).single();if(console.log("Profile query result:",{profile:i,error:c}),i)i.first_name&&i.last_name?d=`${i.first_name} ${i.last_name}`:i.first_name&&(d=i.first_name);else try{const{data:l}=await h.auth.getSession();console.log("Session check:",{sessionUserId:(a=(r=l==null?void 0:l.session)==null?void 0:r.user)==null?void 0:a.id,requesterId:u.requester_id}),((o=(t=l==null?void 0:l.session)==null?void 0:t.user)==null?void 0:o.id)===u.requester_id&&(d="You")}catch(l){console.warn("Could not check session:",l)}}catch(i){console.warn("Could not fetch requester profile:",i)}return{purchaseRequest:{...u,requester_name:d}}}catch(u){return console.error("Error in getPurchaseRequestById:",u),{error:u.message}}},y=async(n,s)=>{try{console.log("Creating purchase request:",n);const{data:r,error:a}=await h.from("purchase_requests").insert(n).select().single();if(a)return console.error("Error creating purchase request:",a),{error:a.message};if(console.log("Purchase request created:",r),s.length>0){const d=s.map(l=>({...l,purchase_request_id:r.id}));console.log("Adding purchase request items:",d);const{data:q,error:i}=await h.from("purchase_request_items").select("id, purchase_request_id, product_id, quantity, uom_id, base_quantity").limit(1);if(console.log("Table check result:",{tableInfo:q,checkError:i}),i&&i.code==="42P01")return console.error("The purchase_request_items table does not exist:",i),{error:"The purchase request items table does not exist. Please run the database migrations."};const{error:c}=await h.from("purchase_request_items").insert(d);if(c)return console.error("Error adding purchase request items:",c),c.code==="PGRST301"?(console.warn("RLS policy violation when adding items. Returning purchase request without items."),{purchaseRequest:{...r,items:[],requester_name:"Unknown"},error:"Items could not be added due to permission issues. Please check your database policies."}):{error:c.message}}const{data:t,error:o}=await h.from("purchase_requests").select("*").eq("id",r.id).single();console.log("Verification check:",{verifyData:t,verifyError:o});const{data:u,error:m}=await h.from("purchase_request_items").select("*").eq("purchase_request_id",r.id);return console.log("Items verification check:",{itemsCount:(u==null?void 0:u.length)||0,verifyItemsError:m}),await g(n.organization_id,r.id)}catch(r){return console.error("Error in createPurchaseRequest:",r),{error:r.message}}},w=async(n,s,r,a)=>{try{if(console.log("Updating purchase request:",{organizationId:n,purchaseRequestId:s,updates:r,options:a}),!n||!s)return{success:!1,error:"Organization ID and purchase request ID are required"};if(!a||!a.itemsToCreate&&!a.itemsToUpdate&&!a.itemsToDelete){const{error:t}=await h.from("purchase_requests").update(r).eq("id",s).eq("organization_id",n);return t?(console.error("Error updating purchase request:",t),{success:!1,error:t.message||"Failed to update purchase request"}):(console.log("Purchase request updated successfully"),{success:!0,error:null})}}catch(t){return console.error("Error in updatePurchaseRequest:",t),{success:!1,error:t.message||"An error occurred while updating the purchase request"}}},E=async(n,s,r,a)=>{try{console.log("Updating purchase request status:",{organizationId:n,purchaseRequestId:s,status:r,notes:a});const t={status:r};a!==void 0&&(t.notes=a);const{error:o}=await h.from("purchase_requests").update(t).eq("organization_id",n).eq("id",s);return o?(console.error("Error updating purchase request status:",o),{success:!1,error:o.message}):(console.log("Purchase request status updated successfully to:",r),{success:!0})}catch(t){return console.error("Error in updatePurchaseRequestStatus:",t),{success:!1,error:t.message}}};export{g as a,w as b,y as c,p as g,E as u};
