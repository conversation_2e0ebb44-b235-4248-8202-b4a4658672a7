import{u as l,r as n,b as g,d as u,j as t,i as b,s as c}from"./index-C6AV3cVN.js";function h({children:e}){const{pathname:o}=l();return n.useEffect(()=>{window.scrollTo({top:0,left:0,behavior:"smooth"})},[o]),e||null}const _=()=>{const{user:e}=g(),o=u(),[d,a]=n.useState(!0);return n.useEffect(()=>{(async()=>{if(!e){a(!1);return}try{console.log("Checking onboarding status for user:",e.id);const{data:r,error:s}=await c.from("profiles").select("onboarding_completed, onboarding_completed_at").eq("id",e.id).maybeSingle();if(s){console.error("Error checking profile onboarding status:",s),o("/onboarding");return}if(r&&r.onboarding_completed){console.log("User has completed onboarding in database"),localStorage.setItem(`onboarding_completed_${e.id}`,"true"),localStorage.removeItem(`onboarding_data_${e.id}`),a(!1);return}if(localStorage.getItem(`onboarding_completed_${e.id}`)&&r){console.log("User has completed onboarding in localStorage but not in database, updating database");const{error:i}=await c.rpc("mark_onboarding_completed",{user_id:e.id});i&&console.error("Error updating onboarding status in database:",i),a(!1);return}console.log("User has not completed onboarding, redirecting"),localStorage.removeItem(`onboarding_data_${e.id}`),o("/onboarding");return}catch(r){console.error("Error checking onboarding status:",r),o("/onboarding");return}a(!1)})()},[e,o]),d?t.jsx("div",{className:"fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50",children:t.jsxs("div",{className:"text-center",children:[t.jsx(b,{size:"xl"}),t.jsx("p",{className:"mt-2 text-white",children:"Checking your account setup..."})]})}):null};export{_ as O,h as S};
