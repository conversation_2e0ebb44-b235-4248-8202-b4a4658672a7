import{h as ce,r as a,j as e,p as I,B as c,a0 as $,P as z,Q as U,A as Y,i as D,_ as l,L as J,e as oe,a1 as de,a2 as me,a3 as he,M as m,a6 as O,J as xe,bw as w,aN as ue}from"./index-C6AV3cVN.js";import{C as Q}from"./Card-yj7fueH8.js";import{b as ge,u as fe,c as je,d as pe}from"./tagService-sPq402Av.js";import{P as Ne}from"./Pagination-CVEzfctr.js";const ye=({className:V=""})=>{const{currentOrganization:h}=ce(),[M,u]=a.useState([]),[W,L]=a.useState(!1),[E,i]=a.useState(null),[g,_]=a.useState(""),[A,B]=a.useState(1),[f,G]=a.useState(10),[K,d]=a.useState(!1),[j,p]=a.useState(!1),[N,F]=a.useState(null),[T,v]=a.useState(""),[S,k]=a.useState(""),[y,b]=a.useState("#3b82f6"),[X,C]=a.useState(!1),[o,Z]=a.useState(null);a.useEffect(()=>{if(!h)return;(async()=>{L(!0),i(null);try{const{success:t,tags:r,error:n}=await ge(h.id);t&&r?u(r):n&&i(n)}catch(t){i(t.message||"Failed to fetch tags")}finally{L(!1)}})()},[h]);const q=()=>{F(null),v(""),k(""),b("#3b82f6"),d(!0)},ee=s=>{F(s),v(s.name),k(s.description||""),b(s.color||"#3b82f6"),d(!0)},se=s=>{Z(s),C(!0)},ae=async s=>{if(s.preventDefault(),!!h){p(!0),i(null);try{if(N){const{success:t,tag:r,error:n}=await fe(N.id,{name:T,description:S||null,color:y||null});t&&r?(u(P=>P.map(H=>H.id===r.id?{...H,...r}:H)),d(!1)):n&&i(n)}else{const{success:t,tag:r,error:n}=await je({organization_id:h.id,name:T,description:S||null,color:y||null});t&&r?(u(P=>[...P,{...r,usage_count:0}]),d(!1)):n&&i(n)}}catch(t){i(t.message||"Failed to save tag")}finally{p(!1)}}},te=async()=>{if(o){p(!0),i(null);try{const{success:s,error:t}=await pe(o.id);s?(u(r=>r.filter(n=>n.id!==o.id)),C(!1)):t&&i(t)}catch(s){i(s.message||"Failed to delete tag")}finally{p(!1)}}},x=M.filter(s=>g===""||s.name.toLowerCase().includes(g.toLowerCase())||s.description&&s.description.toLowerCase().includes(g.toLowerCase())),le=Math.ceil(x.length/f),R=A*f,re=R-f,ie=x.slice(re,R),ne=["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9","#3b82f6","#6366f1","#8b5cf6","#a855f7","#d946ef","#ec4899","#f43f5e","#6b7280"];return e.jsxs("div",{className:V,children:[e.jsxs(Q,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(I,{className:"mr-2 h-5 w-5"}),"Tag Management"]}),e.jsxs(c,{size:"sm",onClick:q,children:[e.jsx($,{className:"mr-2 h-4 w-4"}),"Add Tag"]})]}),e.jsx("div",{className:"mb-4",children:e.jsx(z,{type:"text",placeholder:"Search tags...",icon:U,onChange:s=>_(s.target.value),value:g})}),E&&e.jsx(Y,{color:"failure",className:"mb-4",children:E}),W?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(D,{size:"xl"})}):M.length===0?e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx(I,{className:"mx-auto h-12 w-12 mb-4 text-gray-400"}),e.jsx("p",{className:"text-lg font-medium",children:"No tags found"}),e.jsx("p",{className:"mt-1",children:"Create your first tag to start organizing your data"}),e.jsxs(c,{color:"light",className:"mt-4",onClick:q,children:[e.jsx($,{className:"mr-2 h-4 w-4"}),"Add Tag"]})]}):x.length===0?e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx(U,{className:"mx-auto h-12 w-12 mb-4 text-gray-400"}),e.jsx("p",{className:"text-lg font-medium",children:"No matching tags found"}),e.jsx("p",{className:"mt-1",children:"Try a different search term or create a new tag"}),e.jsx(c,{color:"light",className:"mt-4",onClick:()=>_(""),children:"Clear Search"})]}):e.jsxs(l,{children:[e.jsxs(l.Head,{children:[e.jsx(l.HeadCell,{children:"Name"}),e.jsx(l.HeadCell,{children:"Description"}),e.jsx(l.HeadCell,{children:"Usage"}),e.jsx(l.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(l.Body,{className:"divide-y",children:ie.map(s=>e.jsxs(l.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(l.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:s.color||"#3b82f6"}}),s.usage_count>0?e.jsx(J,{to:`/settings/tags/${s.id}/items`,className:"text-blue-600 hover:underline dark:text-blue-500",children:s.name}):s.name]})}),e.jsx(l.Cell,{children:s.description||e.jsx("span",{className:"text-gray-400 italic",children:"No description"})}),e.jsx(l.Cell,{children:e.jsxs(oe,{color:"gray",className:"font-normal",children:[s.usage_count," ",s.usage_count===1?"item":"items"]})}),e.jsx(l.Cell,{children:e.jsxs("div",{className:"flex space-x-2",children:[s.usage_count>0&&e.jsx(J,{to:`/settings/tags/${s.id}/items`,children:e.jsxs(c,{color:"info",size:"xs",children:[e.jsx(de,{className:"h-4 w-4 mr-1"}),"View Items"]})}),e.jsx(c,{color:"light",size:"xs",onClick:()=>ee(s),children:e.jsx(me,{className:"h-4 w-4"})}),e.jsx(c,{color:"failure",size:"xs",onClick:()=>se(s),children:e.jsx(he,{className:"h-4 w-4"})})]})})]},s.id))})]}),x.length>0&&e.jsx(Ne,{currentPage:A,totalPages:le,itemsPerPage:f,totalItems:x.length,onPageChange:B,onItemsPerPageChange:s=>{G(s),B(1)},itemName:"tags"})]}),e.jsxs(m,{show:K,onClose:()=>d(!1),children:[e.jsx(m.Header,{children:N?"Edit Tag":"Create New Tag"}),e.jsx(m.Body,{children:e.jsxs("form",{onSubmit:ae,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(O,{htmlFor:"tagName",value:"Tag Name"}),e.jsx(z,{id:"tagName",value:T,onChange:s=>v(s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(O,{htmlFor:"tagDescription",value:"Description (Optional)"}),e.jsx(z,{id:"tagDescription",value:S,onChange:s=>k(s.target.value)})]}),e.jsxs("div",{children:[e.jsx(O,{htmlFor:"tagColor",value:"Color"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"color",id:"tagColor",value:y,onChange:s=>b(s.target.value),className:"w-10 h-10 border-0 p-0"}),e.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:ne.map(s=>e.jsx("button",{type:"button",className:`w-6 h-6 rounded-full ${y===s?"ring-2 ring-offset-2 ring-blue-500":""}`,style:{backgroundColor:s},onClick:()=>b(s)},s))})]})]}),e.jsxs("div",{className:"flex justify-end space-x-2 pt-4",children:[e.jsx(c,{color:"gray",onClick:()=>d(!1),children:"Cancel"}),e.jsxs(c,{type:"submit",disabled:j,children:[j?e.jsx(D,{size:"sm",className:"mr-2"}):null,N?"Update Tag":"Create Tag"]})]})]})})]}),e.jsxs(m,{show:X,onClose:()=>C(!1),size:"md",children:[e.jsx(m.Header,{children:"Confirm Deletion"}),e.jsx(m.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(xe,{className:"mx-auto mb-4 h-14 w-14 text-red-500"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500 dark:text-gray-400",children:['Are you sure you want to delete the tag "',o==null?void 0:o.name,'"?']}),o&&o.usage_count>0&&e.jsxs(Y,{color:"warning",className:"mb-4",children:["This tag is used by ",o.usage_count," items. Deleting it will remove the tag from all items."]}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(c,{color:"gray",onClick:()=>C(!1),children:"Cancel"}),e.jsxs(c,{color:"failure",onClick:te,disabled:j,children:[j?e.jsx(D,{size:"sm",className:"mr-2"}):null,"Delete Tag"]})]})]})})]})]})},ve=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Tag Management"}),e.jsxs(w,{className:"hidden md:flex",children:[e.jsx(w.Item,{href:"/",icon:ue,children:"Home"}),e.jsx(w.Item,{href:"/settings",children:"Settings"}),e.jsx(w.Item,{children:"Tag Management"})]})]}),e.jsx(Q,{className:"mb-4",children:e.jsxs("div",{className:"p-4",children:[e.jsxs("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[e.jsx(I,{className:"mr-2 h-6 w-6 text-blue-500"}),"About Tags"]}),e.jsx("p",{className:"mb-3",children:"Tags help you organize and categorize your data across the system. You can add tags to:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4 space-y-1",children:[e.jsx("li",{children:"Products - categorize products by attributes, seasons, promotions, etc."}),e.jsx("li",{children:"Suppliers - identify supplier specialties, reliability, or geographic regions"}),e.jsx("li",{children:"Customers - segment customers by preferences, purchase history, or demographics"}),e.jsx("li",{children:"Purchase Requests - track request priorities, departments, or approval status"}),e.jsx("li",{children:"Purchase Orders - organize orders by shipping method, payment terms, or urgency"})]}),e.jsx("p",{children:"Create tags with descriptive names and optional colors to make them easily identifiable. You can then filter and search by tags throughout the application."})]})}),e.jsx(ye,{})]});export{ve as default};
