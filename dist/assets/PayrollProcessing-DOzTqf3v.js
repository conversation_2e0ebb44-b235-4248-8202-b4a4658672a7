import{ab as X,d as q,h as Q,r as o,j as e,i as W,A as v,G as w,B as c,o as b,bJ as $,bK as Y,_ as l,U as A,e as M,$ as O,V as Z,M as u}from"./index-C6AV3cVN.js";import{C as z}from"./Card-yj7fueH8.js";import{a as T}from"./payroll-DcVgVc3z.js";import{g as ee}from"./employee-DWC25S7P.js";import{a as se}from"./payrollProcessing-DLMQSSy3.js";import{P as le}from"./payroll-j3fcCwK0.js";import{P as re}from"./PageTitle-FHPo8gWi.js";import"./payrollCalculation-DBybei6f.js";const he=()=>{const{id:i}=X(),n=q(),{currentOrganization:t}=Q(),[g,S]=o.useState(null),[d,D]=o.useState([]),[r,j]=o.useState([]),[I,E]=o.useState(!0),[m,k]=o.useState(!1),[H,h]=o.useState(null),[_,P]=o.useState(null),[R,N]=o.useState(0),[L,f]=o.useState(!1),[F,C]=o.useState(!1),V=async()=>{if(!(!t||!i)){E(!0),h(null);try{const{period:s,error:a}=await T(t.id,i,!0);a?h(a):s?(S(s),s.status!==le.DRAFT&&n(`/payroll/periods/${i}`)):h("Payroll period not found")}catch(s){h(s.message)}finally{E(!1)}}},G=async()=>{if(t)try{const{employees:s,error:a}=await ee(t.id);a?h(a):(D(s),j(s.map(x=>x.id)))}catch(s){h(s.message)}};o.useEffect(()=>{V(),G()},[t,i]);const J=()=>{r.length===d.length?j([]):j(d.map(s=>s.id))},K=s=>{r.includes(s)?j(r.filter(a=>a!==s)):j([...r,s])},U=async()=>{if(!(!t||!i||r.length===0)){k(!0),P(null),N(0);try{const s=r.length;N(10);const{success:a,processedItems:x,error:p}=await se(t.id,i,r,{reprocess:!0});if(!a){P(p||"Failed to process payroll");return}const y=Math.round(x/s*100);N(Math.max(y,100));const{period:B}=await T(t.id,i,!0);B&&S(B),C(!0)}catch(s){console.error("Error processing payroll:",s),P(s.message)}finally{k(!1)}}};return I?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(W,{size:"xl"})}):H?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(v,{color:"failure",icon:w,children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",H]}),e.jsx("div",{className:"mt-4",children:e.jsxs(c,{color:"gray",onClick:()=>n("/payroll"),children:[e.jsx(b,{className:"mr-2 h-5 w-5"}),"Back to Payroll Periods"]})})]}):g?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs(c,{color:"gray",className:"mr-4",onClick:()=>n(`/payroll/periods/${i}`),children:[e.jsx(b,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(re,{title:`Process Payroll: ${g.name}`})]}),e.jsx("div",{children:e.jsxs(c,{color:"primary",onClick:()=>f(!0),disabled:r.length===0||m,children:[e.jsx($,{className:"mr-2 h-5 w-5"}),"Process Payroll"]})})]}),_&&e.jsxs(v,{color:"failure",icon:w,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",_]}),m&&e.jsxs(z,{className:"mb-4",children:[e.jsx("h3",{className:"text-lg font-medium mb-2",children:"Processing Payroll..."}),e.jsx(Y,{progress:R,size:"lg",color:"blue",labelText:!0,labelPosition:"inside"}),e.jsxs("p",{className:"text-sm text-gray-500 mt-2",children:["Processing ",r.length," employees..."]})]}),e.jsxs(z,{children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Select Employees to Process"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Select the employees you want to include in this payroll run."})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(l,{striped:!0,children:[e.jsxs(l.Head,{children:[e.jsx(l.HeadCell,{className:"w-10",children:e.jsx(A,{checked:r.length===d.length&&d.length>0,onChange:J})}),e.jsx(l.HeadCell,{children:"Employee"}),e.jsx(l.HeadCell,{children:"Employee ID"}),e.jsx(l.HeadCell,{children:"Department"}),e.jsx(l.HeadCell,{children:"Position"}),e.jsx(l.HeadCell,{children:"Status"})]}),e.jsx(l.Body,{children:d.length>0?d.map(s=>{var a,x,p,y;return e.jsxs(l.Row,{children:[e.jsx(l.Cell,{children:e.jsx(A,{checked:r.includes(s.id),onChange:()=>K(s.id)})}),e.jsx(l.Cell,{className:"font-medium",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2",children:s.profile_image_url?e.jsx("img",{src:s.profile_image_url,alt:`${s.first_name} ${s.last_name}`,className:"w-8 h-8 rounded-full object-cover"}):e.jsxs("span",{className:"text-xs font-semibold",children:[(a=s.first_name)==null?void 0:a[0],(x=s.last_name)==null?void 0:x[0]]})}),e.jsx("div",{children:e.jsxs("p",{className:"font-medium",children:[s.first_name," ",s.last_name]})})]})}),e.jsx(l.Cell,{children:s.employee_number||"N/A"}),e.jsx(l.Cell,{children:((p=s.department)==null?void 0:p.name)||"Not assigned"}),e.jsx(l.Cell,{children:((y=s.position)==null?void 0:y.title)||"Not assigned"}),e.jsx(l.Cell,{children:s.is_active?e.jsx(M,{color:"success",icon:O,children:"Active"}):e.jsx(M,{color:"gray",icon:Z,children:"Inactive"})})]},s.id)}):e.jsx(l.Row,{children:e.jsx(l.Cell,{colSpan:6,className:"text-center py-4",children:"No employees found."})})})]})}),e.jsxs("div",{className:"flex justify-between items-center mt-4",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:[r.length," of ",d.length," employees selected"]})}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(c,{color:"gray",onClick:()=>n(`/payroll/periods/${i}`),disabled:m,children:"Cancel"}),e.jsxs(c,{color:"primary",onClick:()=>f(!0),disabled:r.length===0||m,children:[e.jsx($,{className:"mr-2 h-5 w-5"}),"Process Payroll"]})]})]})]}),e.jsxs(u,{show:L,onClose:()=>f(!1),size:"md",children:[e.jsx(u.Header,{children:"Confirm Process Payroll"}),e.jsx(u.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{children:["Are you sure you want to process payroll for ",r.length," employees? This will calculate their pay for the period ",g.name,"."]}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(c,{color:"gray",onClick:()=>f(!1),disabled:m,children:"Cancel"}),e.jsx(c,{color:"primary",onClick:()=>{f(!1),U()},isProcessing:m,children:"Process Payroll"})]})]})})]}),e.jsxs(u,{show:F,onClose:()=>{C(!1),n(`/payroll/periods/${i}`)},size:"md",children:[e.jsx(u.Header,{children:"Payroll Processing Complete"}),e.jsx(u.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"bg-green-100 rounded-full p-3",children:e.jsx(O,{className:"h-10 w-10 text-green-600"})})}),e.jsxs("p",{className:"text-center",children:["Payroll has been successfully processed for ",r.length," employees."]}),e.jsx("div",{className:"flex justify-center",children:e.jsx(c,{color:"primary",onClick:()=>{C(!1),n(`/payroll/periods/${i}`)},children:"View Payroll Details"})})]})})]})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(v,{color:"failure",icon:w,children:[e.jsx("span",{className:"font-medium",children:"Error!"})," Payroll period not found"]}),e.jsx("div",{className:"mt-4",children:e.jsxs(c,{color:"gray",onClick:()=>n("/payroll"),children:[e.jsx(b,{className:"mr-2 h-5 w-5"}),"Back to Payroll Periods"]})})]})};export{he as default};
