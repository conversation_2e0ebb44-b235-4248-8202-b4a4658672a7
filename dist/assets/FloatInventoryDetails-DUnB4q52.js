import{ab as L,d as K,h as q,b as J,r as n,j as e,i as F,A as k,B as l,o as v,J as P,$ as f,L as m,aj as S,aJ as T,aK as Q,E as $,K as D,e as E,M as u,a7 as V,ad as W}from"./index-C6AV3cVN.js";import{C as y}from"./Card-yj7fueH8.js";import{e as O,r as G}from"./floatInventory-k_pEQeIK.js";import{u as X}from"./currencyFormatter-BsFWv3sX.js";import{d as N}from"./formatters-Cypx7G-j.js";const ae=()=>{const{id:x}=L(),j=K(),{currentOrganization:h}=q(),{user:p}=J();X();const[s,g]=n.useState(null),[A,w]=n.useState(!0),[b,i]=n.useState(null),[B,d]=n.useState(!1),[R,_]=n.useState(!1),[I,C]=n.useState(""),[H,M]=n.useState("manual");n.useEffect(()=>{(async()=>{if(!(!h||!x)){w(!0),i(null);try{const{floatItem:a,error:r}=await O(h.id,x);r?i(r):a?g(a):i("Float inventory item not found")}catch(a){i(a.message||"An error occurred while fetching the float inventory item")}finally{w(!1)}}})()},[h,x]);const U=async()=>{if(!(!s||!p)){_(!0);try{const{success:t,error:a}=await G(s.id,p.id,I||"Manually resolved from details page");if(a)i(a);else if(t){const{floatItem:r}=await O(h.id,x);r&&g(r),d(!1),C("")}}catch(t){i(t.message||"Failed to resolve float inventory")}finally{_(!1)}}},z=()=>{if(!s)return null;const t=new Date(s.created_at),a=new Date,r=s.resolved_at?new Date(s.resolved_at):null;if(s.resolved&&r){const c=Math.floor((r.getTime()-t.getTime())/864e5);return{type:"resolved",days:c,label:`Resolved in ${c} day${c!==1?"s":""}`}}else{const c=Math.floor((a.getTime()-t.getTime())/864e5);return{type:"unresolved",days:c,label:`Unresolved for ${c} day${c!==1?"s":""}`}}};if(A)return e.jsx("div",{className:"flex justify-center items-center min-h-screen",children:e.jsx(F,{size:"xl"})});if(b)return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(k,{color:"failure",children:[e.jsx("span",{className:"font-medium",children:"Error:"})," ",b]}),e.jsx("div",{className:"mt-4",children:e.jsxs(l,{color:"gray",onClick:()=>j("/inventory/float"),children:[e.jsx(v,{className:"mr-2 h-5 w-5"}),"Back to Float Inventory"]})})]});if(!s)return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(k,{color:"warning",children:e.jsx("span",{className:"font-medium",children:"Float Inventory Item Not Found"})}),e.jsx("div",{className:"mt-4",children:e.jsxs(l,{color:"gray",onClick:()=>j("/inventory/float"),children:[e.jsx(v,{className:"mr-2 h-5 w-5"}),"Back to Float Inventory"]})})]});const o=z();return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs(l,{color:"gray",onClick:()=>j("/inventory/float"),className:"mr-4",children:[e.jsx(v,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold flex items-center",children:[e.jsx(P,{className:"mr-2 h-6 w-6"}),"Float Inventory Details"]}),e.jsxs("p",{className:"text-gray-600",children:[s.product_name," - ",s.quantity," units"]})]})]}),e.jsxs("div",{className:"flex space-x-2",children:[!s.resolved&&e.jsxs(l,{color:"success",onClick:()=>d(!0),children:[e.jsx(f,{className:"mr-2 h-5 w-5"}),"Resolve"]}),e.jsx(m,{to:"/inventory/float/report",children:e.jsxs(l,{color:"light",children:[e.jsx(S,{className:"mr-2 h-5 w-5"}),"View Reports"]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs(y,{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Float Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(T,{className:"mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Product"}),e.jsx("p",{className:"font-medium",children:e.jsx(m,{to:`/products/details/${s.product_id}`,className:"text-blue-600 hover:underline",children:s.product_name})}),s.product_sku&&e.jsxs("p",{className:"text-sm text-gray-500",children:["SKU: ",s.product_sku]})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Related Sale"}),e.jsx("p",{className:"font-medium",children:e.jsx(m,{to:`/sales/details/${s.sale_id}`,className:"text-blue-600 hover:underline",children:s.sale_number})}),e.jsx("p",{className:"text-sm text-gray-500",children:N(s.sale_date)})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx($,{className:"mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Created"}),e.jsx("p",{className:"font-medium",children:N(s.created_at)}),o&&e.jsx("p",{className:`text-sm ${o.type==="unresolved"?"text-red-600":"text-green-600"}`,children:o.label})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(D,{className:"mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Customer"}),e.jsx("p",{className:"font-medium",children:s.customer_name?e.jsx(m,{to:`/customers/details/${s.customer_id}`,className:"text-blue-600 hover:underline",children:s.customer_name}):"Walk-in Customer"})]})]})]})]}),s.resolved&&e.jsxs(y,{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Resolution Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx($,{className:"mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Resolved Date"}),e.jsx("p",{className:"font-medium",children:s.resolved_at?N(s.resolved_at):"N/A"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(S,{className:"mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Resolution Type"}),e.jsx(E,{color:s.resolution_type==="automatic"?"success":"info",children:s.resolution_type||"manual"})]})]}),s.resolved_by_id&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(D,{className:"mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Resolved By"}),e.jsx("p",{className:"font-medium",children:s.resolved_by_name||"User"})]})]})]}),s.resolution_notes&&e.jsxs("div",{className:"mt-4 border-t pt-4",children:[e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Resolution Notes"}),e.jsx("p",{className:"text-gray-700",children:s.resolution_notes})]})]})]}),e.jsx("div",{children:e.jsxs(y,{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Status"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"text-center",children:e.jsx(E,{color:s.resolved?"success":"warning",className:"text-lg px-4 py-2",children:s.resolved?"Resolved":"Unresolved"})}),e.jsxs("div",{className:"text-center",children:[e.jsxs("p",{className:"text-2xl font-bold text-red-600",children:[s.quantity," units"]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Float Quantity"})]}),o&&e.jsxs("div",{className:"text-center",children:[e.jsxs("p",{className:`text-lg font-semibold ${o.type==="unresolved"?"text-red-600":"text-green-600"}`,children:[o.days," days"]}),e.jsx("p",{className:"text-sm text-gray-500",children:o.type==="unresolved"?"Unresolved":"To resolve"})]}),!s.resolved&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs(l,{color:"success",className:"w-full",onClick:()=>d(!0),children:[e.jsx(f,{className:"mr-2 h-5 w-5"}),"Resolve Float"]}),e.jsx(m,{to:`/inventory/receipts/create?product=${s.product_id}`,children:e.jsxs(l,{color:"info",className:"w-full",children:[e.jsx(T,{className:"mr-2 h-5 w-5"}),"Receive Stock"]})})]})]})]})})]}),e.jsxs(u,{show:B,onClose:()=>d(!1),children:[e.jsx(u.Header,{children:"Resolve Float Inventory"}),e.jsx(u.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block mb-2 text-sm font-medium",children:"Resolution Type"}),e.jsxs(V,{value:H,onChange:t=>M(t.target.value),children:[e.jsx("option",{value:"manual",children:"Manual Resolution"}),e.jsx("option",{value:"automatic",children:"Automatic Resolution"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-2 text-sm font-medium",children:"Resolution Notes"}),e.jsx(W,{value:I,onChange:t=>C(t.target.value),placeholder:"Enter notes about how this float inventory was resolved...",rows:4})]})]})}),e.jsxs(u.Footer,{children:[e.jsxs(l,{color:"success",onClick:U,disabled:R,children:[R?e.jsx(F,{size:"sm",className:"mr-2"}):e.jsx(f,{className:"mr-2 h-5 w-5"}),"Resolve"]}),e.jsx(l,{color:"gray",onClick:()=>d(!1),children:"Cancel"})]})]})]})};export{ae as default};
