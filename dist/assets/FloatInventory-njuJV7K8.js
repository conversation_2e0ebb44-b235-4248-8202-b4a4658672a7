import{b as G,h as J,a as K,r as l,j as e,L as x,B as n,I as X,av as Z,i as b,A as N,_ as t,e as ee,aB as se,M as o,a6 as te,P as le,aH as f}from"./index-C6AV3cVN.js";import{C as _}from"./Card-yj7fueH8.js";import{c as ae,d as ne}from"./formatters-Cypx7G-j.js";import{g as D,a as re,r as oe,u as ie}from"./floatInventory-k_pEQeIK.js";const je=()=>{var q;const{user:S}=G(),{currentOrganization:i}=J(),{settings:ce}=K(),[g,C]=l.useState([]),[w,A]=l.useState([]),[k,F]=l.useState(!0),[I,R]=l.useState(null),[m,B]=l.useState(1),[M,H]=l.useState(0),[h]=l.useState(20),[c,L]=l.useState(!1),[de,he]=l.useState(""),[O,v]=l.useState(!1),[r,U]=l.useState(null),[z,W]=l.useState(""),[P,T]=l.useState(!1),[d,p]=l.useState({allow_negative_inventory:!0,warn_on_low_inventory:!0,auto_create_purchase_requests:!0}),[$,j]=l.useState(!1);l.useEffect(()=>{(async()=>{if(i){F(!0),R(null);try{const{floatItems:a,count:u,error:y}=await D(i.id,{resolved:c,limit:h,offset:(m-1)*h});if(y)throw new Error(y);C(a),H(u);const{summary:V,error:E}=await re(i.id);if(E)throw new Error(E);A(V)}catch(a){R(a.message||"Failed to fetch float inventory")}finally{F(!1)}}})()},[i,m,c,h]);const Q=async()=>{if(!(!S||!r)){T(!0);try{const{success:s,error:a}=await oe(r.id,S.id,z);if(a)throw new Error(a);if(s){f.success("Float inventory resolved successfully"),v(!1);const{floatItems:u,count:y}=await D(i.id,{resolved:c,limit:h,offset:(m-1)*h});C(u),H(y)}}catch(s){f.error(s.message||"Failed to resolve float inventory")}finally{T(!1)}}},Y=async()=>{if(i)try{const{success:s,error:a}=await ie(i.id,d);if(a)throw new Error(a);s&&(f.success("Inventory settings updated successfully"),j(!1))}catch(s){f.error(s.message||"Failed to update inventory settings")}};return e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Float Inventory Management"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(x,{to:"/inventory/float/report",children:e.jsxs(n,{size:"sm",color:"warning",children:[e.jsx(X,{className:"mr-2 h-5 w-5"}),"Reports"]})}),e.jsxs(n,{size:"sm",color:"light",onClick:()=>j(!0),children:[e.jsx(Z,{className:"mr-2 h-5 w-5"}),"Settings"]})]})]}),e.jsxs(_,{className:"mb-4",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"What is Float Inventory?"}),e.jsx("p",{className:"text-gray-700 mb-2",children:'Float inventory occurs when items are sold without sufficient stock. This creates a "negative" inventory balance that needs to be resolved when new stock arrives.'}),e.jsx("p",{className:"text-gray-700",children:"The system automatically tracks float inventory and resolves it when new stock is received. You can also manually resolve float inventory using the actions below."})]}),e.jsxs(_,{className:"mb-4",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Float Inventory Summary"}),k&&w.length===0?e.jsx("div",{className:"flex justify-center items-center p-4",children:e.jsx(b,{size:"xl"})}):w.length===0?e.jsxs(N,{color:"success",children:[e.jsx("span",{className:"font-medium",children:"Good news!"})," You have no unresolved float inventory."]}):e.jsxs(t,{children:[e.jsxs(t.Head,{children:[e.jsx(t.HeadCell,{children:"Product"}),e.jsx(t.HeadCell,{children:"Unresolved Quantity"}),e.jsx(t.HeadCell,{children:"Since"}),e.jsx(t.HeadCell,{children:"Actions"})]}),e.jsx(t.Body,{className:"divide-y",children:w.map(s=>e.jsxs(t.Row,{className:"bg-white",children:[e.jsx(t.Cell,{className:"whitespace-nowrap font-medium text-gray-900",children:e.jsx(x,{to:`/inventory/details/${s.product_id}`,className:"text-blue-600 hover:underline",children:s.product_name})}),e.jsx(t.Cell,{children:s.unresolved_float_quantity}),e.jsx(t.Cell,{children:ae(s.oldest_float_date)}),e.jsx(t.Cell,{children:e.jsx(x,{to:`/inventory/receipts/create?product=${s.product_id}`,children:e.jsx(n,{size:"xs",color:"info",children:"Receive Stock"})})})]},s.product_id))})]})]}),e.jsxs(_,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-lg font-semibold",children:"Float Inventory Details"}),e.jsx("div",{className:"flex gap-2",children:e.jsx(n,{size:"xs",color:c?"light":"gray",onClick:()=>L(!c),children:c?"Hide Resolved":"Show Resolved"})})]}),k&&g.length===0?e.jsx("div",{className:"flex justify-center items-center p-4",children:e.jsx(b,{size:"xl"})}):I?e.jsxs(N,{color:"failure",children:[e.jsx("span",{className:"font-medium",children:"Error:"})," ",I]}):g.length===0?e.jsxs(N,{color:"info",children:[e.jsx("span",{className:"font-medium",children:"No float inventory found."})," ",c?"Try showing unresolved items.":""]}):e.jsxs(e.Fragment,{children:[e.jsxs(t,{children:[e.jsxs(t.Head,{children:[e.jsx(t.HeadCell,{children:"Product"}),e.jsx(t.HeadCell,{children:"Quantity"}),e.jsx(t.HeadCell,{children:"Sale"}),e.jsx(t.HeadCell,{children:"Date"}),e.jsx(t.HeadCell,{children:"Status"}),e.jsx(t.HeadCell,{children:"Actions"})]}),e.jsx(t.Body,{className:"divide-y",children:g.map(s=>{var a,u;return e.jsxs(t.Row,{className:"bg-white",children:[e.jsx(t.Cell,{className:"whitespace-nowrap font-medium text-gray-900",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx(x,{to:`/products/details/${s.product_id}`,className:"text-blue-600 hover:underline",children:((a=s.product)==null?void 0:a.name)||"Unknown Product"}),e.jsx(x,{to:`/inventory/float/${s.id}`,className:"text-sm text-gray-500 hover:underline",children:"View Float Details"})]})}),e.jsx(t.Cell,{children:s.quantity}),e.jsx(t.Cell,{children:e.jsx(x,{to:`/sales/details/${s.sale_id}`,className:"text-blue-600 hover:underline",children:((u=s.sale)==null?void 0:u.invoice_number)||"Unknown Sale"})}),e.jsx(t.Cell,{children:ne(s.created_at)}),e.jsx(t.Cell,{children:e.jsx(ee,{color:s.resolved?"success":"warning",children:s.resolved?"Resolved":"Unresolved"})}),e.jsx(t.Cell,{children:!s.resolved&&e.jsx(n,{size:"xs",color:"success",onClick:()=>{U(s),v(!0)},children:"Resolve"})})]},s.id)})})]}),e.jsx("div",{className:"flex justify-center mt-4",children:e.jsx(se,{currentPage:m,totalPages:Math.ceil(M/h),onPageChange:B,showIcons:!0})})]})]}),e.jsxs(o,{show:O,onClose:()=>v(!1),size:"md",children:[e.jsx(o.Header,{children:"Resolve Float Inventory"}),e.jsx(o.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{children:["You are about to manually resolve float inventory for:",e.jsx("span",{className:"font-bold block mt-1",children:(q=r==null?void 0:r.product)==null?void 0:q.name})]}),e.jsxs("p",{children:["Quantity: ",e.jsx("span",{className:"font-bold",children:r==null?void 0:r.quantity})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"This will create an inventory adjustment to resolve the float. The product's stock quantity will be increased."}),e.jsxs("div",{children:[e.jsx(te,{htmlFor:"notes",value:"Notes (optional)"}),e.jsx(le,{id:"notes",type:"text",placeholder:"Enter reason for manual resolution",value:z,onChange:s=>W(s.target.value),className:"mt-1"})]})]})}),e.jsxs(o.Footer,{children:[e.jsx(n,{color:"gray",onClick:()=>v(!1),children:"Cancel"}),e.jsx(n,{color:"success",onClick:Q,disabled:P,children:P?e.jsxs(e.Fragment,{children:[e.jsx(b,{size:"sm",className:"mr-2"}),"Resolving..."]}):"Resolve Float"})]})]}),e.jsxs(o,{show:$,onClose:()=>j(!1),size:"md",children:[e.jsx(o.Header,{children:"Inventory Settings"}),e.jsx(o.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"allow_negative_inventory",type:"checkbox",checked:d.allow_negative_inventory,onChange:s=>p({...d,allow_negative_inventory:s.target.checked}),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("label",{htmlFor:"allow_negative_inventory",className:"ml-2 text-sm font-medium text-gray-900",children:"Allow Negative Inventory (Float)"})]}),e.jsx("p",{className:"text-xs text-gray-500 ml-6",children:'When enabled, sales can proceed even when there is insufficient stock. The system will track this as "float inventory".'}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"warn_on_low_inventory",type:"checkbox",checked:d.warn_on_low_inventory,onChange:s=>p({...d,warn_on_low_inventory:s.target.checked}),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("label",{htmlFor:"warn_on_low_inventory",className:"ml-2 text-sm font-medium text-gray-900",children:"Show Low Inventory Warnings"})]}),e.jsx("p",{className:"text-xs text-gray-500 ml-6",children:"When enabled, warnings will be shown when selling products with insufficient stock."}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"auto_create_purchase_requests",type:"checkbox",checked:d.auto_create_purchase_requests,onChange:s=>p({...d,auto_create_purchase_requests:s.target.checked}),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("label",{htmlFor:"auto_create_purchase_requests",className:"ml-2 text-sm font-medium text-gray-900",children:"Auto-Create Purchase Requests"})]}),e.jsx("p",{className:"text-xs text-gray-500 ml-6",children:"When enabled, purchase requests will be automatically created when inventory falls below minimum levels."})]})}),e.jsxs(o.Footer,{children:[e.jsx(n,{color:"gray",onClick:()=>j(!1),children:"Cancel"}),e.jsx(n,{color:"blue",onClick:Y,children:"Save Settings"})]})]})]})};export{je as default};
