import{s}from"./index-C6AV3cVN.js";const n=async(i,r)=>{try{let e=s.from("job_positions").select(`
        *,
        department:department_id (
          id,
          name,
          description
        )
      `,{count:"exact"}).eq("organization_id",i);r!=null&&r.departmentId&&(e=e.eq("department_id",r.departmentId)),(r==null?void 0:r.isActive)!==void 0&&(e=e.eq("is_active",r.isActive)),r!=null&&r.searchQuery&&(e=e.ilike("title",`%${r.searchQuery}%`)),r!=null&&r.sortBy?e=e.order(r.sortBy,{ascending:r.sortOrder==="asc"}):e=e.order("title",{ascending:!0}),r!=null&&r.limit&&(e=e.limit(r.limit)),r!=null&&r.offset&&(e=e.range(r.offset,r.offset+(r.limit||10)-1));const{data:t,error:a,count:o}=await e;return a?(console.error("Error fetching job positions:",a),{positions:[],count:0,error:a.message}):{positions:t,count:o||0}}catch(e){return console.error("Error in getJobPositions:",e),{positions:[],count:0,error:e.message}}},d=async(i,r)=>{try{const{data:e,error:t}=await s.from("job_positions").insert({...r,organization_id:i}).select(`
        *,
        department:department_id (
          id,
          name,
          description
        )
      `).single();return t?(console.error("Error creating job position:",t),{error:t.message}):{position:e}}catch(e){return console.error("Error in createJobPosition:",e),{error:e.message}}},u=async(i,r,e)=>{try{const{data:t,error:a}=await s.from("job_positions").update(e).eq("organization_id",i).eq("id",r).select(`
        *,
        department:department_id (
          id,
          name,
          description
        )
      `).single();return a?(console.error("Error updating job position:",a),{error:a.message}):{position:t}}catch(t){return console.error("Error in updateJobPosition:",t),{error:t.message}}},m=async(i,r)=>{try{const{error:e}=await s.from("job_positions").delete().eq("organization_id",i).eq("id",r);return e?(console.error("Error deleting job position:",e),{success:!1,error:e.message}):{success:!0}}catch(e){return console.error("Error in deleteJobPosition:",e),{success:!1,error:e.message}}};export{d as c,m as d,n as g,u};
