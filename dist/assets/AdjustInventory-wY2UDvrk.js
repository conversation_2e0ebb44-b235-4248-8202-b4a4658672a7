import{ab as de,d as me,h as he,b as pe,r as s,j as e,i as K,A as I,J as xe,L as M,B as F,o as U,a6 as l,a7 as V,P as b,ad as je,ay as fe,s as G}from"./index-C6AV3cVN.js";import{C as P}from"./Card-yj7fueH8.js";import{i as ve}from"./product-Ca8DWaNR.js";import{c as ye}from"./inventoryTransaction-1UXV5RDN.js";import{b as ge}from"./formatters-Cypx7G-j.js";import{u as be}from"./currencyFormatter-BsFWv3sX.js";import{E as Ne}from"./EnhancedNumberInput-Bwo1E3yF.js";import"./floatInventory-k_pEQeIK.js";const qe=()=>{var B,H;const{id:x}=de(),X=me(),{currentOrganization:a}=he(),{user:N}=pe(),q=be(),[r,Y]=s.useState(null),[Z,A]=s.useState(!0),[D,j]=s.useState(null),[S,R]=s.useState(""),[i,ee]=s.useState("add"),[d,te]=s.useState(""),[w,re]=s.useState(""),[f,k]=s.useState(""),[v,E]=s.useState(""),[y,_]=s.useState(""),[T,L]=s.useState(""),[O,z]=s.useState(!1),[Q,c]=s.useState(null),[se,ne]=s.useState(!1);s.useEffect(()=>{a&&x&&oe()},[a,x]);const oe=async()=>{if(!(!a||!x)){A(!0),j(null);try{const{product:t,error:n}=await ve(a.id,x);n?j(n):t?Y(t):j("Product not found")}catch(t){j(t.message||"An error occurred while fetching the product")}finally{A(!1)}}},g=()=>{const t=["Physical count adjustment","Cycle count adjustment","Customer return","Supplier return correction","System correction","Initial stock setup"].includes(d),n=["Transfer between locations","Transfer to production","Transfer from production"].includes(d),C=["Expired items","Initial stock setup"].includes(d);return{showReferenceNumber:t,showLocations:n,showExpirationDate:C}},ae=async t=>{var W;if(t.preventDefault(),!a||!r||!S||!d||!N){c("Please fill in all required fields");return}const n=parseFloat(S);if(isNaN(n)||n<.1){c("Please enter a valid quantity (minimum 0.1)");return}const{showReferenceNumber:C,showLocations:ie}=g();if(C&&!f){c("Please enter a reference number");return}if(ie){if(i==="subtract"&&!y){c("Please enter the destination location");return}if(i==="add"&&!v){c("Please enter the source location");return}}z(!0),c(null);try{let o=(W=r.product_uoms)==null?void 0:W.find(u=>u.is_default),m=(o==null?void 0:o.uom_id)||null;if(!m&&r.product_uoms&&r.product_uoms.length>0&&(o=r.product_uoms[0],m=o==null?void 0:o.uom_id),!m)try{const{data:u}=await G.from("units_of_measurement").select("*").eq("organization_id",a.id).eq("code","pcs").single();if(u){const{data:ce,error:ue}=await G.from("product_uoms").insert({product_id:r.id,uom_id:u.id,organization_id:a.id,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0}).select().single();!ue&&ce&&(m=u.id)}}catch(u){console.warn("Could not create default UOM:",u)}if(!m)throw new Error("No unit of measure available for this product. Please add a unit of measure first.");const le=i==="add"?n:-n;let h=`${d}`;f&&(h+=` (Ref: ${f})`),v&&i==="add"&&(h+=` (From: ${v})`),y&&i==="subtract"&&(h+=` (To: ${y})`),T&&(h+=` (Exp: ${T})`),w&&(h+=`: ${w}`),console.log("Creating inventory transaction with user ID:",N.id);const{transaction:Se,error:J}=await ye(a.id,N.id,{productId:r.id,transactionType:"adjustment",quantity:le,uomId:m,notes:h});if(J)throw new Error(J);ne(!0),setTimeout(()=>{X("/inventory")},2e3)}catch(o){c(o.message||"An error occurred while adjusting inventory")}finally{z(!1)}};if(Z)return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(P,{children:e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(K,{size:"xl"})})})});if(D||!r)return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(P,{children:e.jsxs(I,{color:"failure",icon:xe,children:[e.jsx("h3",{className:"text-lg font-medium",children:"Error"}),e.jsx("p",{children:D||"Product not found"}),e.jsx("div",{className:"mt-4",children:e.jsx(M,{to:"/inventory",children:e.jsxs(F,{color:"gray",size:"sm",children:[e.jsx(U,{className:"mr-2 h-4 w-4"}),"Back to Inventory"]})})})]})})});let p=(B=r.product_uoms)==null?void 0:B.find(t=>t.is_default);!p&&r.product_uoms&&r.product_uoms.length>0&&(p=r.product_uoms[0]);const $=((H=p==null?void 0:p.uom)==null?void 0:H.code)||"pcs";return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(P,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Adjust Inventory"}),e.jsxs("p",{className:"text-gray-500",children:[r.name," ",r.sku&&`(SKU: ${r.sku})`]})]}),e.jsx(M,{to:"/inventory",children:e.jsxs(F,{color:"light",children:[e.jsx(U,{className:"mr-2 h-5 w-5"}),"Back to Inventory"]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Current Stock Information"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"Current Stock:"}),e.jsx("span",{className:"ml-2 font-medium",children:ge(r.stock_quantity||0,$)})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"Cost Price:"}),e.jsx("span",{className:"ml-2 font-medium",children:q(r.cost_price)})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"Total Value:"}),e.jsx("span",{className:"ml-2 font-medium",children:q((r.stock_quantity||0)*Number(r.cost_price||0))})]})]})]}),e.jsx("form",{onSubmit:ae,className:"space-y-4",children:se?e.jsxs(I,{color:"success",children:[e.jsx("h3",{className:"font-medium",children:"Inventory Adjusted Successfully"}),e.jsx("p",{children:"Redirecting to inventory list..."})]}):e.jsxs(e.Fragment,{children:[Q&&e.jsxs(I,{color:"failure",children:[e.jsx("h3",{className:"font-medium",children:"Error"}),e.jsx("p",{children:Q})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"adjustmentType",value:"Adjustment Type"}),e.jsxs(V,{id:"adjustmentType",value:i,onChange:t=>{ee(t.target.value),E(""),_("")},required:!0,children:[e.jsx("option",{value:"add",children:"Add to Inventory"}),e.jsx("option",{value:"subtract",children:"Remove from Inventory"})]})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"quantity",value:"Quantity"}),e.jsxs("div",{className:"flex",children:[e.jsx(Ne,{id:"quantity",min:"0.1",step:"0.1",value:S,onChange:t=>R(t.target.value),onBlur:t=>{(t.target.value===""||parseFloat(t.target.value)<.1)&&R("")},required:!0,className:"flex-1",autoSelect:!0,preventScrollChange:!0,placeholder:"0.1"}),e.jsx("div",{className:"flex items-center bg-gray-100 px-3 rounded-r-lg",children:$})]})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"reason",value:"Reason"}),e.jsxs(V,{id:"reason",value:d,onChange:t=>{te(t.target.value),k(""),E(""),_(""),L("")},required:!0,children:[e.jsx("option",{value:"",children:"Select a reason"}),e.jsxs("optgroup",{label:"Count Adjustments",children:[e.jsx("option",{value:"Physical count adjustment",children:"Physical count adjustment"}),e.jsx("option",{value:"Cycle count adjustment",children:"Cycle count adjustment"}),e.jsx("option",{value:"System correction",children:"System correction"})]}),e.jsxs("optgroup",{label:"Write-offs",children:[e.jsx("option",{value:"Damaged goods",children:"Damaged goods"}),e.jsx("option",{value:"Expired items",children:"Expired items"}),e.jsx("option",{value:"Theft or loss",children:"Theft or loss"}),e.jsx("option",{value:"Quality control rejection",children:"Quality control rejection"}),e.jsx("option",{value:"Scrapped items",children:"Scrapped items"})]}),e.jsxs("optgroup",{label:"Internal Operations",children:[e.jsx("option",{value:"Internal use",children:"Internal use"}),e.jsx("option",{value:"Samples",children:"Samples"}),e.jsx("option",{value:"Promotional items",children:"Promotional items"}),e.jsx("option",{value:"Employee use",children:"Employee use"})]}),e.jsxs("optgroup",{label:"Returns",children:[e.jsx("option",{value:"Returned to inventory",children:"Returned to inventory"}),e.jsx("option",{value:"Customer return",children:"Customer return"}),e.jsx("option",{value:"Supplier return correction",children:"Supplier return correction"})]}),e.jsxs("optgroup",{label:"Transfers",children:[e.jsx("option",{value:"Transfer between locations",children:"Transfer between locations"}),e.jsx("option",{value:"Transfer to production",children:"Transfer to production"}),e.jsx("option",{value:"Transfer from production",children:"Transfer from production"})]}),e.jsxs("optgroup",{label:"Other",children:[e.jsx("option",{value:"Initial stock setup",children:"Initial stock setup"}),e.jsx("option",{value:"Donation",children:"Donation"}),e.jsx("option",{value:"Other",children:"Other"})]})]})]}),g().showReferenceNumber&&e.jsxs("div",{children:[e.jsx(l,{htmlFor:"referenceNumber",value:"Reference Number"}),e.jsx(b,{id:"referenceNumber",type:"text",value:f,onChange:t=>k(t.target.value),placeholder:"Enter reference number",required:!0})]}),g().showLocations&&e.jsxs(e.Fragment,{children:[i==="add"&&e.jsxs("div",{children:[e.jsx(l,{htmlFor:"locationFrom",value:"Source Location"}),e.jsx(b,{id:"locationFrom",type:"text",value:v,onChange:t=>E(t.target.value),placeholder:"Enter source location",required:!0})]}),i==="subtract"&&e.jsxs("div",{children:[e.jsx(l,{htmlFor:"locationTo",value:"Destination Location"}),e.jsx(b,{id:"locationTo",type:"text",value:y,onChange:t=>_(t.target.value),placeholder:"Enter destination location",required:!0})]})]}),g().showExpirationDate&&e.jsxs("div",{children:[e.jsx(l,{htmlFor:"expirationDate",value:"Expiration Date"}),e.jsx(b,{id:"expirationDate",type:"date",value:T,onChange:t=>L(t.target.value)})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"notes",value:"Additional Notes"}),e.jsx(je,{id:"notes",value:w,onChange:t=>re(t.target.value),rows:3,placeholder:"Enter any additional details about this adjustment"})]}),e.jsx(F,{type:"submit",color:"primary",disabled:O,children:O?e.jsxs(e.Fragment,{children:[e.jsx(K,{size:"sm",className:"mr-2"}),"Processing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(fe,{className:"mr-2 h-5 w-5"}),"Save Adjustment"]})})]})})]})]})})};export{qe as default};
