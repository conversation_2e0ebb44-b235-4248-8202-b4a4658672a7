import{ab as Y,d as q,h as J,r as c,j as e,i as K,A as w,G as C,B as t,o as S,L as u,a2 as Q,a3 as W,$ as M,ah as Z,aj as ee,E as se,bB as le,e as i,V as re,_ as a,M as p}from"./index-C6AV3cVN.js";import{C as R}from"./Card-yj7fueH8.js";import{a as ae,d as te,u as oe}from"./payroll-DcVgVc3z.js";import{P as o,a as D}from"./payroll-j3fcCwK0.js";import{S as ie,h as ce}from"./SendPayrollToPayableButton-DAdxPmzk.js";import{P as ne}from"./PageTitle-FHPo8gWi.js";import{f as n}from"./formatters-Cypx7G-j.js";import{f as _}from"./index-qirzObrW.js";import"./payables-q7zOb02j.js";import"./currencyFormatter-BsFWv3sX.js";import"./index-4YOzgfrD.js";import"./typeof-QjJsDpFa.js";import"./index-Cn2wB4rc.js";import"./index-DT2YvziZ.js";const Ce=()=>{const{id:d}=Y(),j=q(),{currentOrganization:m}=J(),[l,k]=c.useState(null),[I,T]=c.useState(!0),[B,f]=c.useState(null),[$,H]=c.useState(!1),[P,O]=c.useState("overview"),[G,A]=c.useState(!1),[N,g]=c.useState(!1),[v,h]=c.useState(null),[z,b]=c.useState(!1),E=async()=>{var s;if(!(!m||!d)){T(!0),f(null);try{const{period:r,error:y}=await ae(m.id,d,!0);if(y)f(y),console.error("Error fetching payroll period:",y);else if(r){if(console.log("Fetched payroll period:",r),console.log("Payroll items:",((s=r.payroll_items)==null?void 0:s.length)||0),k(r),r.status===o.APPROVED){const X=await ce(m.id,d);H(X)}}else f("Payroll period not found")}catch(r){console.error("Error in fetchPayrollPeriod:",r),f(r.message)}finally{T(!1)}}};c.useEffect(()=>{E()},[m,d]);const F=async()=>{if(!(!m||!d)){g(!0),h(null);try{const{success:s,error:r}=await te(m.id,d);r?h(r):s&&j("/payroll")}catch(s){h(s.message)}finally{g(!1)}}},L=async()=>{if(!(!m||!d||!l)){g(!0),h(null);try{const{period:s,error:r}=await oe(m.id,d,{status:o.APPROVED});r?h(r):s&&(b(!1),E())}catch(s){h(s.message)}finally{g(!1)}}},V=s=>{switch(s){case o.DRAFT:return e.jsx(i,{color:"gray",children:"Draft"});case o.PROCESSING:return e.jsx(i,{color:"blue",children:"Processing"});case o.APPROVED:return e.jsx(i,{color:"green",children:"Approved"});case o.PAID:return e.jsx(i,{color:"purple",children:"Paid"});default:return e.jsx(i,{color:"gray",children:s})}},U=s=>{switch(s){case D.DRAFT:return e.jsx(i,{color:"gray",children:"Draft"});case D.CALCULATED:return e.jsx(i,{color:"blue",children:"Calculated"});case D.APPROVED:return e.jsx(i,{color:"green",children:"Approved"});case D.PAID:return e.jsx(i,{color:"purple",children:"Paid"});default:return e.jsx(i,{color:"gray",children:s})}},x=!(l!=null&&l.payroll_items)||l.payroll_items.length===0?{totalBasicPay:0,totalGrossPay:0,totalNetPay:0,totalDeductions:0,totalAllowances:0,totalEmployees:0}:l.payroll_items.reduce((s,r)=>({totalBasicPay:s.totalBasicPay+Number(r.basic_pay),totalGrossPay:s.totalGrossPay+Number(r.gross_pay),totalNetPay:s.totalNetPay+Number(r.net_pay),totalDeductions:s.totalDeductions+Number(r.total_deductions),totalAllowances:s.totalAllowances+Number(r.total_allowances),totalEmployees:s.totalEmployees+1}),{totalBasicPay:0,totalGrossPay:0,totalNetPay:0,totalDeductions:0,totalAllowances:0,totalEmployees:0});return I?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(K,{size:"xl"})}):B?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(w,{color:"failure",icon:C,children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",B]}),e.jsx("div",{className:"mt-4",children:e.jsxs(t,{color:"gray",onClick:()=>j("/payroll"),children:[e.jsx(S,{className:"mr-2 h-5 w-5"}),"Back to Payroll Periods"]})})]}):l?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs(t,{color:"gray",className:"mr-4",onClick:()=>j("/payroll"),children:[e.jsx(S,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(ne,{title:`Payroll Period: ${l.name}`})]}),e.jsxs("div",{className:"flex space-x-2",children:[l.status===o.DRAFT&&e.jsxs(e.Fragment,{children:[e.jsx(u,{to:`/payroll/periods/${l.id}/edit`,children:e.jsxs(t,{color:"warning",children:[e.jsx(Q,{className:"mr-2 h-5 w-5"}),"Edit"]})}),e.jsxs(t,{color:"failure",onClick:()=>A(!0),children:[e.jsx(W,{className:"mr-2 h-5 w-5"}),"Delete"]})]}),l.status===o.PROCESSING&&e.jsxs(t,{color:"success",onClick:()=>b(!0),children:[e.jsx(M,{className:"mr-2 h-5 w-5"}),"Approve"]}),l.status===o.DRAFT&&e.jsx(u,{to:`/payroll/periods/${l.id}/process`,children:e.jsxs(t,{color:"primary",children:[e.jsx(Z,{className:"mr-2 h-5 w-5"}),"Process Payroll"]})}),l.status!==o.DRAFT&&e.jsx(u,{to:`/payroll/periods/${l.id}/payslips`,children:e.jsxs(t,{color:"light",children:[e.jsx(ee,{className:"mr-2 h-5 w-5"}),"View Payslips"]})}),l.status===o.APPROVED&&e.jsx(ie,{payrollPeriodId:l.id,payrollPeriodName:l.name,payrollStatus:l.status,totalNetPay:x.totalNetPay,totalEmployees:x.totalEmployees,payablesCreated:$,onSuccess:()=>{H(!0),E()}})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("ul",{className:"flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500",children:[e.jsx("li",{className:"mr-2",children:e.jsxs("button",{className:`inline-flex items-center p-4 border-b-2 rounded-t-lg ${P==="overview"?"text-blue-600 border-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,onClick:()=>O("overview"),children:[e.jsx(se,{className:"w-4 h-4 mr-2"}),"Overview"]})}),e.jsx("li",{className:"mr-2",children:e.jsxs("button",{className:`inline-flex items-center p-4 border-b-2 rounded-t-lg ${P==="employees"?"text-blue-600 border-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,onClick:()=>O("employees"),children:[e.jsx(le,{className:"w-4 h-4 mr-2"}),"Employees"]})})]})})}),P==="overview"&&e.jsx(R,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Period Information"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Period Name"}),e.jsx("p",{className:"font-medium",children:l.name})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Start Date"}),e.jsx("p",{children:_(new Date(l.start_date),"MMMM d, yyyy")})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"End Date"}),e.jsx("p",{children:_(new Date(l.end_date),"MMMM d, yyyy")})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Payment Date"}),e.jsx("p",{children:_(new Date(l.payment_date),"MMMM d, yyyy")})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Status"}),e.jsx("div",{children:V(l.status)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"13th Month Pay"}),e.jsx("div",{children:l.is_thirteenth_month?e.jsx(i,{color:"success",icon:M,children:"Yes"}):e.jsx(i,{color:"gray",icon:re,children:"No"})})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Summary"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Employees"}),e.jsx("p",{className:"font-medium",children:x.totalEmployees})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Basic Pay"}),e.jsx("p",{className:"font-medium",children:n(x.totalBasicPay)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Allowances"}),e.jsx("p",{className:"font-medium",children:n(x.totalAllowances)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Deductions"}),e.jsx("p",{className:"font-medium",children:n(x.totalDeductions)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Gross Pay"}),e.jsx("p",{className:"font-medium",children:n(x.totalGrossPay)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Net Pay"}),e.jsx("p",{className:"font-medium text-lg text-green-600",children:n(x.totalNetPay)})]})]})]})]})}),P==="employees"&&e.jsx(R,{children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(a,{striped:!0,children:[e.jsxs(a.Head,{children:[e.jsx(a.HeadCell,{children:"Employee"}),e.jsx(a.HeadCell,{children:"Status"}),e.jsx(a.HeadCell,{children:"Basic Pay"}),e.jsx(a.HeadCell,{children:"Allowances"}),e.jsx(a.HeadCell,{children:"Deductions"}),e.jsx(a.HeadCell,{children:"Gross Pay"}),e.jsx(a.HeadCell,{children:"Net Pay"}),e.jsx(a.HeadCell,{children:"Actions"})]}),e.jsx(a.Body,{children:l.payroll_items&&l.payroll_items.length>0?l.payroll_items.map(s=>{var r,y;return e.jsxs(a.Row,{children:[e.jsx(a.Cell,{className:"font-medium",children:s.employee?e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2",children:s.employee.profile_image_url?e.jsx("img",{src:s.employee.profile_image_url,alt:`${s.employee.first_name} ${s.employee.last_name}`,className:"w-8 h-8 rounded-full object-cover"}):e.jsxs("span",{className:"text-xs font-semibold",children:[(r=s.employee.first_name)==null?void 0:r[0],(y=s.employee.last_name)==null?void 0:y[0]]})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium",children:[s.employee.first_name," ",s.employee.last_name]}),s.employee.employee_number&&e.jsxs("p",{className:"text-xs text-gray-500",children:["#",s.employee.employee_number]})]})]}):"Unknown Employee"}),e.jsx(a.Cell,{children:U(s.status)}),e.jsx(a.Cell,{children:n(Number(s.basic_pay))}),e.jsx(a.Cell,{children:n(Number(s.total_allowances))}),e.jsx(a.Cell,{children:n(Number(s.total_deductions))}),e.jsx(a.Cell,{children:n(Number(s.gross_pay))}),e.jsx(a.Cell,{className:"font-medium text-green-600",children:n(Number(s.net_pay))}),e.jsx(a.Cell,{children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(u,{to:`/payroll/items/${s.id}`,children:e.jsx(t,{size:"xs",color:"info",children:"Details"})}),(l.status===o.PROCESSING||l.status===o.DRAFT)&&e.jsx(u,{to:`/payroll/items/${s.id}/edit`,children:e.jsx(t,{size:"xs",color:"warning",children:"Edit"})})]})})]},s.id)}):e.jsx(a.Row,{children:e.jsxs(a.Cell,{colSpan:8,className:"text-center py-4",children:["No payroll items found for this period.",l.status===o.DRAFT&&e.jsx("div",{className:"mt-2",children:e.jsx(u,{to:`/payroll/periods/${l.id}/process`,children:e.jsx(t,{size:"xs",color:"primary",children:"Process Payroll"})})})]})})})]})})}),e.jsxs(p,{show:G,onClose:()=>A(!1),size:"md",children:[e.jsx(p.Header,{children:"Delete Payroll Period"}),e.jsx(p.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"Are you sure you want to delete this payroll period? This action cannot be undone."}),v&&e.jsx(w,{color:"failure",icon:C,children:v}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(t,{color:"gray",onClick:()=>A(!1),disabled:N,children:"Cancel"}),e.jsx(t,{color:"failure",onClick:F,isProcessing:N,children:"Delete"})]})]})})]}),e.jsxs(p,{show:z,onClose:()=>b(!1),size:"md",children:[e.jsx(p.Header,{children:"Approve Payroll Period"}),e.jsx(p.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"Are you sure you want to approve this payroll period? This will finalize all payroll calculations and prepare the payslips for payment."}),v&&e.jsx(w,{color:"failure",icon:C,children:v}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(t,{color:"gray",onClick:()=>b(!1),disabled:N,children:"Cancel"}),e.jsx(t,{color:"success",onClick:L,isProcessing:N,children:"Approve"})]})]})})]})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(w,{color:"failure",icon:C,children:[e.jsx("span",{className:"font-medium",children:"Error!"})," Payroll period not found"]}),e.jsx("div",{className:"mt-4",children:e.jsxs(t,{color:"gray",onClick:()=>j("/payroll"),children:[e.jsx(S,{className:"mr-2 h-5 w-5"}),"Back to Payroll Periods"]})})]})};export{Ce as default};
