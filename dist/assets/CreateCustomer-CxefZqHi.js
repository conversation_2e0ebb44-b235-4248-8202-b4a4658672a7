import{h as x,d as f,r as o,j as e,A as h,J as g}from"./index-C6AV3cVN.js";import{C as n}from"./Card-yj7fueH8.js";import{C as j}from"./CustomerForm-DEWqk2b_.js";import{c as p}from"./customer-COogBrXM.js";const v=()=>{const{currentOrganization:t}=x(),c=f(),[l,a]=o.useState(!1),[m,r]=o.useState(null),u=async d=>{if(!t){r("No organization selected");return}a(!0),r(null);try{const{customer:s,error:i}=await p(t.id,d);i?r(i):s&&c("/customers")}catch(s){r(s.message||"An error occurred while creating the customer")}finally{a(!1)}};return t?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(n,{children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsx("h1",{className:"text-2xl font-bold",children:"Create New Customer"})}),e.jsx(j,{onSubmit:u,isSubmitting:l,error:m||void 0})]})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(n,{children:e.jsxs(h,{color:"failure",icon:g,children:[e.jsx("h3",{className:"text-lg font-medium",children:"No Organization Selected"}),e.jsx("p",{children:"Please select an organization before creating a customer."})]})})})};export{v as default};
