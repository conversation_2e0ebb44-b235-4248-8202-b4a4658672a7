import{r as a,R as $,j as e,_ as g,aB as ae,h as le,d as re,I as H,B as A,aI as oe,e as F,J as ne,A as j,a7 as B,P as Q,T as P,i as O,aJ as ie,E as ce,L}from"./index-C6AV3cVN.js";import{C as de}from"./Card-yj7fueH8.js";import{b as ue,c as me,d as xe}from"./floatInventory-k_pEQeIK.js";import{B as he,f as ve,C as pe,a as je,L as fe,P as ye,b as ge,c as be,p as Ne,d as _e,e as Se}from"./index-DAKkILcO.js";const V=({columns:l,data:i,pagination:o=!1,pageSize:f=10})=>{const[x,S]=a.useState(1),[n,D]=a.useState(null),[d,C]=a.useState("asc"),h=t=>{n===t?C(d==="asc"?"desc":"asc"):(D(t),C("asc"))},b=$.useMemo(()=>n?[...i].sort((t,u)=>{const c=t[n],r=u[n];return c===r?0:c==null?1:r==null?-1:typeof c=="string"&&typeof r=="string"?d==="asc"?c.localeCompare(r):r.localeCompare(c):d==="asc"?c-r:r-c}):i,[i,n,d]),N=$.useMemo(()=>{if(!o)return b;const t=(x-1)*f;return b.slice(t,t+f)},[b,o,x,f]),R=Math.ceil(i.length/f),y=t=>{S(t)};return e.jsxs("div",{children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(g,{children:[e.jsx(g.Head,{children:l.map((t,u)=>e.jsxs(g.HeadCell,{onClick:()=>t.sortable&&t.sortField&&h(t.sortField),className:t.sortable?"cursor-pointer":"",children:[t.name,t.sortable&&t.sortField===n&&e.jsx("span",{className:"ml-1",children:d==="asc"?"↑":"↓"})]},u))}),e.jsx(g.Body,{className:"divide-y",children:N.map((t,u)=>e.jsx(g.Row,{className:"bg-white",children:l.map((c,r)=>e.jsx(g.Cell,{children:c.selector(t)},r))},u))})]})}),o&&R>1&&e.jsx("div",{className:"flex justify-center mt-4",children:e.jsx(ae,{currentPage:x,totalPages:R,onPageChange:y,showIcons:!0})})]})};pe.register(je,fe,ye,ge,be,Ne,_e,Se);const De={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0}},interaction:{mode:"nearest",axis:"x",intersect:!1}},Ce={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0},x:{ticks:{maxRotation:45,minRotation:45}}}},Re=({data:l,options:i})=>{const o={...De,...i};return e.jsx(ve,{data:l,options:o})},we=({data:l,options:i})=>{const o={...Ce,...i};return e.jsx(he,{data:l,options:o})},Te=()=>{const{currentOrganization:l}=le(),i=re(),[o,f]=a.useState("detailed"),[x,S]=a.useState(!0),[n,D]=a.useState(null),[d,C]=a.useState(null),[h,b]=a.useState(null),[N,R]=a.useState(null),[y,t]=a.useState("all"),[u,c]=a.useState([]),[r,M]=a.useState([]),[w,J]=a.useState([]),[K,Z]=a.useState([]),[E,z]=a.useState(!1),[I,G]=a.useState(7),[v,W]=a.useState([]);a.useEffect(()=>{(async()=>{l!=null&&l.id&&Z([])})()},[l]),a.useEffect(()=>{(async()=>{if(l!=null&&l.id){S(!0),D(null);try{const p=d?d.toISOString():void 0,T=h?h.toISOString():void 0;let k;y==="resolved"?k=!0:y==="unresolved"&&(k=!1);const{report:q,error:U}=await ue(l.id,{startDate:p,endDate:T,productId:N||void 0,resolved:k});if(U)throw new Error(U);c(q);const te=q.filter(m=>!m.resolved).filter(m=>m.days_unresolved&&m.days_unresolved>=I);if(W(te),o==="product"){const{summary:m,error:_}=await me(l.id,{startDate:p,endDate:T});if(_)throw new Error(_);M(m)}if(o==="date"){const{summary:m,error:_}=await xe(l.id,{startDate:p,endDate:T});if(_)throw new Error(_);J(m)}}catch(p){D(p.message||"Failed to load report data")}finally{S(!1)}}})()},[l,o,d,h,N,y,I]);const X={labels:r.slice(0,10).map(s=>s.product_name),datasets:[{label:"Unresolved Quantity",data:r.slice(0,10).map(s=>s.unresolved_float_quantity),backgroundColor:"rgba(239, 68, 68, 0.5)",borderColor:"rgb(239, 68, 68)"},{label:"Resolved Quantity",data:r.slice(0,10).map(s=>s.resolved_float_quantity),backgroundColor:"rgba(34, 197, 94, 0.5)",borderColor:"rgb(34, 197, 94)"}]},Y={labels:w.slice(0,30).map(s=>new Date(s.date_group).toLocaleDateString()),datasets:[{label:"Total Float Quantity",data:w.slice(0,30).map(s=>s.total_float_quantity),borderColor:"rgb(59, 130, 246)",tension:.1,fill:!1}]},ee=[{name:"Product",selector:s=>e.jsx(L,{to:`/products/details/${s.product_id}`,className:"text-blue-600 hover:underline",children:s.product_name})},{name:"SKU",selector:s=>s.product_sku||"N/A"},{name:"Quantity",selector:s=>s.quantity},{name:"Sale #",selector:s=>e.jsx(L,{to:`/sales/details/${s.sale_id}`,className:"text-blue-600 hover:underline",children:s.sale_number})},{name:"Sale Date",selector:s=>new Date(s.sale_date).toLocaleDateString()},{name:"Customer",selector:s=>s.customer_name||"N/A"},{name:"Status",selector:s=>e.jsx(F,{color:s.resolved?"success":"warning",children:s.resolved?"Resolved":"Unresolved"})},{name:"Days",selector:s=>s.resolved?`Resolved in ${s.days_to_resolve} days`:`Unresolved for ${s.days_unresolved} days`},{name:"Actions",selector:s=>e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(A,{size:"xs",color:"light",onClick:()=>i(`/inventory/float/${s.id}`),children:"View"}),!s.resolved&&e.jsx(A,{size:"xs",color:"success",onClick:()=>i(`/inventory/float/${s.id}/resolve`),children:"Resolve"})]})}],se=[{name:"Product",selector:s=>e.jsx(L,{to:`/products/details/${s.product_id}`,className:"text-blue-600 hover:underline",children:s.product_name})},{name:"SKU",selector:s=>s.product_sku||"N/A"},{name:"Total Quantity",selector:s=>s.total_float_quantity},{name:"Unresolved",selector:s=>s.unresolved_float_quantity},{name:"Resolved",selector:s=>s.resolved_float_quantity},{name:"Resolution Rate",selector:s=>e.jsxs(F,{color:s.resolution_rate>80?"success":s.resolution_rate>50?"warning":"failure",children:[s.resolution_rate,"%"]})},{name:"Avg Days to Resolve",selector:s=>s.avg_days_to_resolve?s.avg_days_to_resolve.toFixed(1):"N/A"},{name:"Oldest Unresolved",selector:s=>s.oldest_unresolved_days?`${s.oldest_unresolved_days} days ago`:"None"}];return e.jsxs("div",{className:"container mx-auto px-4 py-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("h1",{className:"text-2xl font-bold flex items-center",children:[e.jsx(H,{className:"mr-2 h-8 w-8"}),"Float Inventory Reports"]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(A,{color:E?"warning":"light",onClick:()=>z(!E),children:[e.jsx(oe,{className:"mr-2 h-5 w-5"}),v.length>0&&e.jsx(F,{color:"failure",className:"ml-2",children:v.length}),"Alerts"]}),e.jsxs(A,{color:"primary",onClick:()=>i("/inventory/float"),children:[e.jsx(ne,{className:"mr-2 h-5 w-5"}),"Float Inventory"]})]})]}),E&&v.length>0&&e.jsxs(j,{color:"warning",className:"mb-4",children:[e.jsxs("div",{className:"font-medium mb-2",children:[v.length," items have been unresolved for ",I,"+ days"]}),e.jsxs("ul",{className:"list-disc pl-5 space-y-1",children:[v.slice(0,5).map(s=>e.jsxs("li",{children:[s.product_name," - ",s.quantity," units - Unresolved for ",s.days_unresolved," days"]},s.id)),v.length>5&&e.jsxs("li",{children:["And ",v.length-5," more items..."]})]}),e.jsxs("div",{className:"mt-2",children:[e.jsx("label",{className:"mr-2",children:"Alert threshold:"}),e.jsxs(B,{value:I,onChange:s=>G(parseInt(s.target.value)),className:"inline-block w-24",children:[e.jsx("option",{value:"3",children:"3 days"}),e.jsx("option",{value:"7",children:"7 days"}),e.jsx("option",{value:"14",children:"14 days"}),e.jsx("option",{value:"30",children:"30 days"})]})]})]}),e.jsxs(de,{className:"mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Filters"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block mb-2",children:"Date Range Start"}),e.jsx(Q,{type:"date",value:d?d.toISOString().split("T")[0]:"",onChange:s=>C(s.target.value?new Date(s.target.value):null),className:"w-full"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-2",children:"Date Range End"}),e.jsx(Q,{type:"date",value:h?h.toISOString().split("T")[0]:"",onChange:s=>b(s.target.value?new Date(s.target.value):null),className:"w-full"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-2",children:"Product"}),e.jsxs(B,{value:N||"",onChange:s=>R(s.target.value||null),className:"w-full",children:[e.jsx("option",{value:"",children:"All Products"}),K.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-2",children:"Status"}),e.jsxs(B,{value:y,onChange:s=>t(s.target.value),className:"w-full",children:[e.jsx("option",{value:"all",children:"All"}),e.jsx("option",{value:"resolved",children:"Resolved"}),e.jsx("option",{value:"unresolved",children:"Unresolved"})]})]})]})]}),e.jsxs(P,{onActiveTabChange:s=>f(["detailed","product","date"][s]),children:[e.jsx(P.Item,{title:"Detailed Report",icon:H,active:o==="detailed",children:x?e.jsx("div",{className:"flex justify-center p-8",children:e.jsx(O,{size:"xl"})}):n?e.jsx(j,{color:"failure",children:n}):u.length===0?e.jsx(j,{color:"info",children:"No float inventory items found with the current filters."}):e.jsx(V,{columns:ee,data:u,pagination:!0})}),e.jsx(P.Item,{title:"Product Summary",icon:ie,active:o==="product",children:x?e.jsx("div",{className:"flex justify-center p-8",children:e.jsx(O,{size:"xl"})}):n?e.jsx(j,{color:"failure",children:n}):r.length===0?e.jsx(j,{color:"info",children:"No product summary data found with the current filters."}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Top 10 Products with Float Inventory"}),e.jsx("div",{className:"h-80",children:e.jsx(we,{data:X})})]}),e.jsx(V,{columns:se,data:r,pagination:!0})]})}),e.jsx(P.Item,{title:"Date Trends",icon:ce,active:o==="date",children:x?e.jsx("div",{className:"flex justify-center p-8",children:e.jsx(O,{size:"xl"})}):n?e.jsx(j,{color:"failure",children:n}):w.length===0?e.jsx(j,{color:"info",children:"No date trend data found with the current filters."}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Float Inventory Trends Over Time"}),e.jsx("div",{className:"h-80",children:e.jsx(Re,{data:Y})})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm text-left text-gray-500",children:[e.jsx("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3",children:"Date"}),e.jsx("th",{className:"px-6 py-3",children:"Total Quantity"}),e.jsx("th",{className:"px-6 py-3",children:"Resolved"}),e.jsx("th",{className:"px-6 py-3",children:"Unresolved"}),e.jsx("th",{className:"px-6 py-3",children:"Resolution Rate"})]})}),e.jsx("tbody",{children:w.map((s,p)=>e.jsxs("tr",{className:"bg-white border-b",children:[e.jsx("td",{className:"px-6 py-4",children:new Date(s.date_group).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4",children:s.total_float_quantity}),e.jsx("td",{className:"px-6 py-4",children:s.resolved_float_quantity}),e.jsx("td",{className:"px-6 py-4",children:s.unresolved_float_quantity}),e.jsx("td",{className:"px-6 py-4",children:e.jsxs(F,{color:s.resolution_rate>80?"success":s.resolution_rate>50?"warning":"failure",children:[s.resolution_rate,"%"]})})]},p))})]})})]})})]})]})};export{Te as default};
