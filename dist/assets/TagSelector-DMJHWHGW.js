import{h as L,r as t,j as s,e as O,V as S,B as v,p as _,P as z,a0 as R}from"./index-C6AV3cVN.js";import{g as B,r as A,a as H}from"./tagService-sPq402Av.js";const $=({entityType:g,entityId:m,selectedTags:n,onTagsChange:l,readOnly:i=!1,className:w=""})=>{const{currentOrganization:o}=L(),[b,j]=t.useState([]),[N,x]=t.useState(!1),[f,h]=t.useState(""),[p,c]=t.useState(!1),d=t.useRef(null);t.useEffect(()=>{if(!o)return;(async()=>{x(!0);try{const{success:r,tags:a}=await B(o.id);r&&a&&j(a)}catch(r){console.error("Error fetching tags:",r)}finally{x(!1)}})()},[o]),t.useEffect(()=>{const e=r=>{d.current&&!d.current.contains(r.target)&&c(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);const y=b.filter(e=>e.name.toLowerCase().includes(f.toLowerCase())&&!n.some(r=>r.id===e.id)),k=async e=>{if(!(!o||i))try{const{success:r}=await H({tag_id:e.id,entity_type:g,entity_id:m});if(r){const a=[...n,e];l&&l(a)}}catch(r){console.error("Error adding tag:",r)}},T=async e=>{if(!(!o||i))try{const{success:r}=await A({tag_id:e,entity_type:g,entity_id:m});if(r){const a=n.filter(u=>u.id!==e);l&&l(a)}}catch(r){console.error("Error removing tag:",r)}},C=e=>{if(!e)return"blue";const r={red:"red",green:"green",blue:"blue",yellow:"yellow",purple:"purple",pink:"pink",indigo:"indigo",gray:"gray"},a=e.toLowerCase();for(const[u,E]of Object.entries(r))if(a.includes(u))return E;return"blue"};return s.jsxs("div",{className:`flex flex-col space-y-2 ${w}`,children:[s.jsx("div",{className:"flex flex-wrap gap-2",children:n.map(e=>s.jsxs(O,{color:C(e.color),className:"flex items-center",children:[e.name,!i&&s.jsx("button",{onClick:()=>T(e.id),className:"ml-1 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700","aria-label":`Remove ${e.name} tag`,children:s.jsx(S,{className:"h-3 w-3"})})]},e.id))}),!i&&s.jsxs("div",{className:"relative",ref:d,children:[s.jsxs(v,{size:"xs",color:"light",onClick:()=>c(!p),className:"flex items-center text-xs",children:[s.jsx(_,{className:"mr-1 h-4 w-4"}),"Add Tags"]}),p&&s.jsx("div",{className:"absolute z-10 mt-2 w-64 bg-white rounded-lg shadow-lg dark:bg-gray-800 border border-gray-200 dark:border-gray-700",children:s.jsxs("div",{className:"p-3",children:[s.jsx(z,{type:"text",placeholder:"Search tags...",value:f,onChange:e=>h(e.target.value),className:"mb-2"}),s.jsx("div",{className:"max-h-40 overflow-y-auto",children:N?s.jsx("div",{className:"text-center py-2 text-sm text-gray-500",children:"Loading tags..."}):y.length>0?s.jsx("div",{className:"py-1",children:y.map(e=>s.jsxs("button",{onClick:()=>{k(e),h("")},className:"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 flex items-center",children:[s.jsx("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.color||"#3b82f6"}}),s.jsx("span",{children:e.name})]},e.id))}):s.jsx("div",{className:"text-center py-2 text-sm text-gray-500",children:"No matching tags found"})}),s.jsx("div",{className:"mt-2 pt-2 border-t border-gray-200 dark:border-gray-700",children:s.jsxs(v,{size:"xs",color:"light",className:"w-full text-xs",onClick:()=>{console.log("Create new tag"),c(!1),window.open("/settings/tags","_blank")},children:[s.jsx(R,{className:"mr-1 h-3 w-3"}),"Create New Tag"]})})]})})]})]})};export{$ as T};
