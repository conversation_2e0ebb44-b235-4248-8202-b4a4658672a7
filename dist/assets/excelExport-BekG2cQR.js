const f=(e,t)=>{if(e==null)return"";switch(t){case"currency":return typeof e=="number"?e.toFixed(2):String(e);case"number":return typeof e=="number"?e.toString():String(e);case"date":if(e instanceof Date)return e.toLocaleDateString();if(typeof e=="string"){const a=new Date(e);return isNaN(a.getTime())?e:a.toLocaleDateString()}return String(e);case"percentage":return typeof e=="number"?`${(e*100).toFixed(2)}%`:String(e);default:return String(e)}},_=(e,t)=>t.split(".").reduce((a,r)=>a&&a[r]!==void 0?a[r]:"",e),n=e=>{const{filename:t,columns:a,data:r,title:d,subtitle:o}=e,i=[];d&&(i.push(`"${d}"`),i.push("")),o&&(i.push(`"${o}"`),i.push(""));const u=a.map(s=>`"${s.header}"`);i.push(u.join(",")),r.forEach(s=>{const w=a.map(y=>{const k=_(s,y.key),p=f(k,y.format);return`"${String(p).replace(/"/g,'""')}"`});i.push(w.join(","))});const m=i.join(`
`),l=new Blob([m],{type:"text/csv;charset=utf-8;"}),c=URL.createObjectURL(l),h=document.createElement("a");h.setAttribute("href",c),h.setAttribute("download",`${t}.csv`),h.style.visibility="hidden",document.body.appendChild(h),h.click(),document.body.removeChild(h),URL.revokeObjectURL(c)},S=(e,t)=>{const a=[{header:"Invoice Number",key:"invoice_number",width:15},{header:"Date",key:"created_at",format:"date",width:12},{header:"Customer",key:"customer.name",width:20},{header:"Cashier",key:"cashier_name",width:15},{header:"Payment Method",key:"payment_method",width:15},{header:"Subtotal",key:"subtotal",format:"currency",width:12},{header:"Tax Amount",key:"tax_amount",format:"currency",width:12},{header:"Discount",key:"discount_amount",format:"currency",width:12},{header:"Total Amount",key:"total_amount",format:"currency",width:12},{header:"Status",key:"status",width:10},{header:"Notes",key:"notes",width:30}],r=e.map(d=>{var o;return{...d,customer:{name:((o=d.customer)==null?void 0:o.name)||"Walk-in Customer"},cashier_name:d.cashier&&`${d.cashier.first_name||""} ${d.cashier.last_name||""}`.trim()||"Unknown"}});n({filename:`sales_history_${new Date().toISOString().split("T")[0]}`,title:"Sales History Report",subtitle:t?`Period: ${t}`:`Generated on ${new Date().toLocaleDateString()}`,columns:a,data:r})},b=e=>{const t=[{header:"Name",key:"name",width:25},{header:"SKU",key:"sku",width:15},{header:"Barcode",key:"barcode",width:15},{header:"Category",key:"category.name",width:20},{header:"Unit Price",key:"unit_price",format:"currency",width:12},{header:"Cost Price",key:"cost_price",format:"currency",width:12},{header:"Stock Quantity",key:"stock_quantity",format:"number",width:12},{header:"Min Stock Level",key:"min_stock_level",format:"number",width:12},{header:"Reorder Quantity",key:"reorder_quantity",format:"number",width:12},{header:"Status",key:"status",width:10},{header:"Created Date",key:"created_at",format:"date",width:12}];n({filename:`products_${new Date().toISOString().split("T")[0]}`,title:"Products Report",subtitle:`Generated on ${new Date().toLocaleDateString()}`,columns:t,data:e})},D=e=>{const t=[{header:"Refund Number",key:"refund_number",width:15},{header:"Date",key:"created_at",format:"date",width:12},{header:"Original Sale",key:"original_sale.invoice_number",width:15},{header:"Customer",key:"original_sale.customer.name",width:20},{header:"Type",key:"refund_type",width:12},{header:"Reason",key:"reason",width:20},{header:"Subtotal",key:"subtotal",format:"currency",width:12},{header:"Tax Amount",key:"tax_amount",format:"currency",width:12},{header:"Restocking Fee",key:"restocking_fee",format:"currency",width:12},{header:"Total Amount",key:"total_amount",format:"currency",width:12},{header:"Method",key:"refund_method",width:15},{header:"Status",key:"status",width:10},{header:"Processed Date",key:"processed_at",format:"date",width:12}];n({filename:`refunds_${new Date().toISOString().split("T")[0]}`,title:"Refunds Report",subtitle:`Generated on ${new Date().toLocaleDateString()}`,columns:t,data:e})},g=e=>{const t=[{header:"Product Name",key:"product.name",width:25},{header:"SKU",key:"product.sku",width:15},{header:"Category",key:"product.category.name",width:20},{header:"Current Stock",key:"stock_quantity",format:"number",width:12},{header:"Unit Price",key:"product.unit_price",format:"currency",width:12},{header:"Total Value",key:"total_value",format:"currency",width:12},{header:"Min Stock Level",key:"product.min_stock_level",format:"number",width:12},{header:"Reorder Quantity",key:"product.reorder_quantity",format:"number",width:12},{header:"Last Updated",key:"updated_at",format:"date",width:12},{header:"Status",key:"status",width:10}],a=e.map(r=>{var d,o;return{...r,total_value:(r.stock_quantity||0)*(((d=r.product)==null?void 0:d.unit_price)||0),status:r.stock_quantity===0?"Out of Stock":r.stock_quantity<=(((o=r.product)==null?void 0:o.min_stock_level)||0)?"Low Stock":"In Stock"}});n({filename:`inventory_${new Date().toISOString().split("T")[0]}`,title:"Inventory Report",subtitle:`Generated on ${new Date().toLocaleDateString()}`,columns:t,data:a})},C=e=>{const t=[{header:"Name",key:"name",width:25},{header:"Email",key:"email",width:25},{header:"Phone",key:"phone",width:15},{header:"Address",key:"address",width:30},{header:"City",key:"city",width:15},{header:"State",key:"state",width:15},{header:"Postal Code",key:"postal_code",width:12},{header:"Country",key:"country",width:15},{header:"Tax ID",key:"tax_id",width:15},{header:"Created Date",key:"created_at",format:"date",width:12},{header:"Notes",key:"notes",width:30}];n({filename:`customers_${new Date().toISOString().split("T")[0]}`,title:"Customers Report",subtitle:`Generated on ${new Date().toLocaleDateString()}`,columns:t,data:e})},$=e=>{const t=[{header:"Name",key:"name",width:25},{header:"Contact Person",key:"contact_person",width:20},{header:"Email",key:"email",width:25},{header:"Phone",key:"phone",width:15},{header:"Address",key:"address",width:30},{header:"City",key:"city",width:15},{header:"State",key:"state",width:15},{header:"Postal Code",key:"postal_code",width:12},{header:"Country",key:"country",width:15},{header:"Tax ID",key:"tax_id",width:15},{header:"Created Date",key:"created_at",format:"date",width:12},{header:"Notes",key:"notes",width:30}];n({filename:`suppliers_${new Date().toISOString().split("T")[0]}`,title:"Suppliers Report",subtitle:`Generated on ${new Date().toLocaleDateString()}`,columns:t,data:e})},x=e=>{const t=[{header:"Employee Number",key:"employee_number",width:15},{header:"First Name",key:"first_name",width:15},{header:"Last Name",key:"last_name",width:15},{header:"Email",key:"email",width:25},{header:"Phone",key:"phone",width:15},{header:"Department",key:"department.name",width:20},{header:"Position",key:"position.title",width:20},{header:"Employment Type",key:"employment_type.name",width:15},{header:"Hire Date",key:"hire_date",format:"date",width:12},{header:"Status",key:"status",width:10},{header:"City",key:"city",width:15},{header:"State",key:"state",width:15},{header:"Country",key:"country",width:15},{header:"Created Date",key:"created_at",format:"date",width:12}];n({filename:`employees_${new Date().toISOString().split("T")[0]}`,title:"Employees Report",subtitle:`Generated on ${new Date().toLocaleDateString()}`,columns:t,data:e})},T=e=>{const t=[{header:"Reference Number",key:"reference_number",width:15},{header:"Supplier",key:"supplier.name",width:25},{header:"Invoice Date",key:"invoice_date",format:"date",width:12},{header:"Due Date",key:"due_date",format:"date",width:12},{header:"Amount",key:"amount",format:"currency",width:12},{header:"VAT Amount",key:"vat_amount",format:"currency",width:12},{header:"Withholding Tax",key:"withholding_tax_amount",format:"currency",width:12},{header:"Total Paid",key:"total_paid",format:"currency",width:12},{header:"Balance",key:"balance",format:"currency",width:12},{header:"Status",key:"status",width:10},{header:"Category",key:"category",width:15},{header:"Created Date",key:"created_at",format:"date",width:12}];n({filename:`payables_${new Date().toISOString().split("T")[0]}`,title:"Payables Report",subtitle:`Generated on ${new Date().toLocaleDateString()}`,columns:t,data:e})};export{g as a,S as b,D as c,C as d,b as e,$ as f,x as g,T as h};
