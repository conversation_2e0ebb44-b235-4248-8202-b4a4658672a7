import{s as f}from"./index-C6AV3cVN.js";import{c as g}from"./payroll-j3fcCwK0.js";import{f as s}from"./formatters-Cypx7G-j.js";import{f as h}from"./index-qirzObrW.js";const P=async(n,a)=>{try{const{data:r,error:l}=await f.from("payroll_items").select(`
        *,
        employee:employee_id (*),
        payroll_period:payroll_period_id (*)
      `).eq("organization_id",n).eq("id",a).single();if(l)throw new Error(l.message);const p=r,{data:i,error:c}=await f.from("payroll_allowances").select("*").eq("organization_id",n).eq("payroll_item_id",a);if(c)throw new Error(c.message);const{data:m,error:d}=await f.from("payroll_deductions").select("*").eq("organization_id",n).eq("payroll_item_id",a);if(d)throw new Error(d.message);return p.allowances=i,p.deductions=m,{item:p}}catch(r){return console.error("Error getting payroll item for payslip:",r),{error:r.message}}},z=async(n,a,r=g.DEFAULT)=>{try{const{item:l,error:p}=await P(n,a);if(p||!l)throw new Error(p||"Payroll item not found");const{data:i,error:c}=await f.from("organizations").select("*").eq("id",n).single();if(c)throw new Error(c.message);const m=i,d=h(new Date(l.payroll_period.start_date),"MMMM d, yyyy"),v=h(new Date(l.payroll_period.end_date),"MMMM d, yyyy"),y=h(new Date(l.payroll_period.payment_date),"MMMM d, yyyy");let t="";switch(r){case g.DEFAULT:t=_(m,l,d,v,y);break;case g.COMPACT:t=N(m,l,d,v,y);break;case g.DETAILED:t=D(m,l,d,v,y);break;default:t=_(m,l,d,v,y)}return{html:t}}catch(l){return console.error("Error generating payslip HTML:",l),{error:l.message}}},_=(n,a,r,l,p)=>{var d,v,y,t,b,u;const i=((d=a.deductions)==null?void 0:d.filter(o=>o.type==="government"))||[],c=((v=a.deductions)==null?void 0:v.filter(o=>o.type==="tax"))||[],m=((y=a.deductions)==null?void 0:y.filter(o=>o.type==="other"))||[];return`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Payslip - ${a.employee.first_name} ${a.employee.last_name}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
        }
        .payslip {
          max-width: 800px;
          margin: 0 auto;
          border: 1px solid #ddd;
          padding: 20px;
        }
        .header {
          text-align: center;
          margin-bottom: 20px;
          padding-bottom: 10px;
          border-bottom: 2px solid #333;
        }
        .company-name {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .payslip-title {
          font-size: 18px;
          margin-bottom: 10px;
        }
        .period-info {
          margin-bottom: 5px;
        }
        .employee-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
        }
        .employee-details, .payroll-details {
          width: 48%;
        }
        .section-title {
          font-weight: bold;
          margin-bottom: 10px;
          border-bottom: 1px solid #ddd;
          padding-bottom: 5px;
        }
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }
        .detail-label {
          font-weight: normal;
        }
        .detail-value {
          text-align: right;
        }
        .earnings-section, .deductions-section {
          margin-bottom: 20px;
        }
        .summary-section {
          margin-top: 20px;
          border-top: 1px solid #ddd;
          padding-top: 10px;
        }
        .total-row {
          font-weight: bold;
          margin-top: 10px;
        }
        .net-pay {
          font-size: 18px;
          font-weight: bold;
          color: #2c7be5;
        }
        .footer {
          margin-top: 30px;
          text-align: center;
          font-size: 12px;
          color: #777;
        }
      </style>
    </head>
    <body>
      <div class="payslip">
        <div class="header">
          <div class="company-name">${n.name}</div>
          <div class="payslip-title">PAYSLIP</div>
          <div class="period-info">Pay Period: ${r} to ${l}</div>
          <div class="period-info">Payment Date: ${p}</div>
        </div>

        <div class="employee-info">
          <div class="employee-details">
            <div class="section-title">Employee Information</div>
            <div class="detail-row">
              <span class="detail-label">Name:</span>
              <span class="detail-value">${a.employee.first_name} ${a.employee.middle_name||""} ${a.employee.last_name}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Employee ID:</span>
              <span class="detail-value">${a.employee.employee_number||"N/A"}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Position:</span>
              <span class="detail-value">${((t=a.employee.position)==null?void 0:t.title)||"N/A"}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Department:</span>
              <span class="detail-value">${((b=a.employee.department)==null?void 0:b.name)||"N/A"}</span>
            </div>
          </div>

          <div class="payroll-details">
            <div class="section-title">Payroll Information</div>
            <div class="detail-row">
              <span class="detail-label">Payroll Period:</span>
              <span class="detail-value">${a.payroll_period.name}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Payslip #:</span>
              <span class="detail-value">${a.id.substring(0,8)}</span>
            </div>
          </div>
        </div>

        <div class="earnings-section">
          <div class="section-title">Earnings</div>
          <div class="detail-row">
            <span class="detail-label">Basic Pay</span>
            <span class="detail-value">${s(Number(a.basic_pay))}</span>
          </div>

          ${((u=a.allowances)==null?void 0:u.map(o=>`
            <div class="detail-row">
              <span class="detail-label">${o.name}</span>
              <span class="detail-value">${s(Number(o.amount))}</span>
            </div>
          `).join(""))||""}

          <div class="detail-row total-row">
            <span class="detail-label">Total Earnings</span>
            <span class="detail-value">${s(Number(a.gross_pay))}</span>
          </div>
        </div>

        <div class="deductions-section">
          <div class="section-title">Deductions</div>

          ${i.length>0?`
            <div class="detail-row">
              <span class="detail-label"><strong>Government Contributions</strong></span>
              <span class="detail-value"></span>
            </div>
            ${i.map(o=>`
              <div class="detail-row">
                <span class="detail-label">${o.name}</span>
                <span class="detail-value">${s(Number(o.amount))}</span>
              </div>
            `).join("")}
          `:""}

          ${c.length>0?`
            <div class="detail-row">
              <span class="detail-label"><strong>Taxes</strong></span>
              <span class="detail-value"></span>
            </div>
            ${c.map(o=>`
              <div class="detail-row">
                <span class="detail-label">${o.name}</span>
                <span class="detail-value">${s(Number(o.amount))}</span>
              </div>
            `).join("")}
          `:""}

          ${m.length>0?`
            <div class="detail-row">
              <span class="detail-label"><strong>Other Deductions</strong></span>
              <span class="detail-value"></span>
            </div>
            ${m.map(o=>`
              <div class="detail-row">
                <span class="detail-label">${o.name}</span>
                <span class="detail-value">${s(Number(o.amount))}</span>
              </div>
            `).join("")}
          `:""}

          <div class="detail-row total-row">
            <span class="detail-label">Total Deductions</span>
            <span class="detail-value">${s(Number(a.total_deductions))}</span>
          </div>
        </div>

        <div class="summary-section">
          <div class="detail-row">
            <span class="detail-label">Gross Pay</span>
            <span class="detail-value">${s(Number(a.gross_pay))}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Total Deductions</span>
            <span class="detail-value">${s(Number(a.total_deductions))}</span>
          </div>
          <div class="detail-row total-row">
            <span class="detail-label">Net Pay</span>
            <span class="detail-value net-pay">${s(Number(a.net_pay))}</span>
          </div>
        </div>

        <div class="footer">
          <p>This is a computer-generated document. No signature is required.</p>
          <p>Generated on ${h(new Date,"MMMM d, yyyy h:mm a")}</p>
        </div>
      </div>
    </body>
    </html>
  `},N=(n,a,r,l,p)=>{var i;return`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Payslip - ${a.employee.first_name} ${a.employee.last_name}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 10px;
          color: #333;
          font-size: 12px;
        }
        .payslip {
          max-width: 400px;
          margin: 0 auto;
          border: 1px solid #ddd;
          padding: 15px;
        }
        .header {
          text-align: center;
          margin-bottom: 15px;
          padding-bottom: 5px;
          border-bottom: 1px solid #ddd;
        }
        .company-name {
          font-size: 16px;
          font-weight: bold;
        }
        .payslip-title {
          font-size: 14px;
          margin: 5px 0;
        }
        .section {
          margin-bottom: 10px;
        }
        .row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 3px;
        }
        .total-row {
          font-weight: bold;
          border-top: 1px solid #ddd;
          padding-top: 3px;
          margin-top: 3px;
        }
        .footer {
          margin-top: 15px;
          text-align: center;
          font-size: 10px;
          color: #777;
        }
      </style>
    </head>
    <body>
      <div class="payslip">
        <div class="header">
          <div class="company-name">${n.name}</div>
          <div class="payslip-title">PAYSLIP</div>
          <div>${r} to ${l}</div>
        </div>

        <div class="section">
          <div class="row">
            <span>Employee:</span>
            <span>${a.employee.first_name} ${a.employee.last_name}</span>
          </div>
          <div class="row">
            <span>ID:</span>
            <span>${a.employee.employee_number||"N/A"}</span>
          </div>
          <div class="row">
            <span>Position:</span>
            <span>${((i=a.employee.position)==null?void 0:i.title)||"N/A"}</span>
          </div>
        </div>

        <div class="section">
          <div class="row">
            <span>Basic Pay:</span>
            <span>${s(Number(a.basic_pay))}</span>
          </div>
          <div class="row">
            <span>Allowances:</span>
            <span>${s(Number(a.total_allowances))}</span>
          </div>
          <div class="row total-row">
            <span>Gross Pay:</span>
            <span>${s(Number(a.gross_pay))}</span>
          </div>
        </div>

        <div class="section">
          <div class="row">
            <span>Deductions:</span>
            <span>${s(Number(a.total_deductions))}</span>
          </div>
          <div class="row total-row">
            <span>Net Pay:</span>
            <span>${s(Number(a.net_pay))}</span>
          </div>
        </div>

        <div class="footer">
          <p>Payment Date: ${p}</p>
          <p>This is a computer-generated document.</p>
        </div>
      </div>
    </body>
    </html>
  `},D=(n,a,r,l,p)=>{var b,u,o,x,$,w;const i=((b=a.deductions)==null?void 0:b.filter(e=>e.type==="government"))||[],c=((u=a.deductions)==null?void 0:u.filter(e=>e.type==="tax"))||[],m=((o=a.deductions)==null?void 0:o.filter(e=>e.type==="other"))||[],d=i.find(e=>e.name==="SSS Contribution"),v=i.find(e=>e.name==="PhilHealth Contribution"),y=i.find(e=>e.name==="Pag-IBIG Contribution"),t=Number(a.basic_pay)*(a.payroll_period.name.includes("Semi-Monthly")?2:1);return`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Detailed Payslip - ${a.employee.first_name} ${a.employee.last_name}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
          font-size: 12px;
        }
        .payslip {
          max-width: 800px;
          margin: 0 auto;
          border: 1px solid #ddd;
          padding: 20px;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          padding-bottom: 10px;
          border-bottom: 2px solid #2563eb;
        }
        .company-info {
          flex: 1;
        }
        .company-name {
          font-size: 20px;
          font-weight: bold;
          color: #2563eb;
        }
        .company-address {
          margin-top: 5px;
          font-size: 12px;
        }
        .payslip-title {
          text-align: right;
          font-size: 24px;
          font-weight: bold;
          color: #2563eb;
        }
        .employee-info {
          display: flex;
          margin-bottom: 20px;
        }
        .employee-details {
          flex: 1;
        }
        .payroll-details {
          flex: 1;
          text-align: right;
        }
        .section-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 10px;
          padding-bottom: 5px;
          border-bottom: 1px solid #ddd;
          color: #2563eb;
        }
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }
        .detail-label {
          flex: 1;
        }
        .detail-value {
          flex: 1;
          text-align: right;
        }
        .total-row {
          font-weight: bold;
          margin-top: 10px;
          padding-top: 5px;
          border-top: 1px solid #ddd;
        }
        .net-pay {
          color: #2563eb;
          font-size: 16px;
        }
        .earnings-section, .deductions-section, .summary-section {
          margin-bottom: 20px;
          padding: 15px;
          border: 1px solid #ddd;
          border-radius: 5px;
        }
        .footer {
          margin-top: 30px;
          text-align: center;
          font-size: 10px;
          color: #666;
        }
        .calculation-section {
          margin-top: 20px;
          padding: 15px;
          border: 1px solid #ddd;
          border-radius: 5px;
          background-color: #f9fafb;
        }
        .calculation-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 10px;
          color: #2563eb;
        }
        .formula {
          font-family: monospace;
          background-color: #f1f5f9;
          padding: 8px;
          border-radius: 4px;
          margin-bottom: 10px;
          font-size: 11px;
        }
        .formula-explanation {
          font-size: 11px;
          margin-bottom: 15px;
          color: #4b5563;
        }
      </style>
    </head>
    <body>
      <div class="payslip">
        <div class="header">
          <div class="company-info">
            <div class="company-name">${n.name}</div>
            <div class="company-address">${n.address||"No address provided"}</div>
          </div>
          <div class="payslip-title">PAYSLIP</div>
        </div>

        <div class="employee-info">
          <div class="employee-details">
            <div class="section-title">Employee Information</div>
            <div class="detail-row">
              <span class="detail-label">Name:</span>
              <span class="detail-value">${a.employee.first_name} ${a.employee.middle_name?a.employee.middle_name+" ":""}${a.employee.last_name}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Employee ID:</span>
              <span class="detail-value">${a.employee.employee_number||"N/A"}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Position:</span>
              <span class="detail-value">${((x=a.employee.position)==null?void 0:x.title)||"N/A"}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Department:</span>
              <span class="detail-value">${(($=a.employee.department)==null?void 0:$.name)||"N/A"}</span>
            </div>
          </div>

          <div class="payroll-details">
            <div class="section-title">Payroll Information</div>
            <div class="detail-row">
              <span class="detail-label">Payroll Period:</span>
              <span class="detail-value">${a.payroll_period.name}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Period Covered:</span>
              <span class="detail-value">${r} to ${l}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Payment Date:</span>
              <span class="detail-value">${p}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Payslip #:</span>
              <span class="detail-value">${a.id.substring(0,8)}</span>
            </div>
          </div>
        </div>

        <div class="earnings-section">
          <div class="section-title">Earnings</div>
          <div class="detail-row">
            <span class="detail-label">Basic Pay</span>
            <span class="detail-value">${s(Number(a.basic_pay))}</span>
          </div>

          ${a.regular_pay>0?`
            <div class="detail-row">
              <span class="detail-label">Regular Pay (${a.total_regular_hours} hrs)</span>
              <span class="detail-value">${s(Number(a.regular_pay))}</span>
            </div>
          `:""}

          ${a.overtime_pay>0?`
            <div class="detail-row">
              <span class="detail-label">Overtime Pay (${a.total_overtime_hours} hrs)</span>
              <span class="detail-value">${s(Number(a.overtime_pay))}</span>
            </div>
          `:""}

          ${a.rest_day_pay>0?`
            <div class="detail-row">
              <span class="detail-label">Rest Day Pay (${a.total_rest_day_hours} hrs)</span>
              <span class="detail-value">${s(Number(a.rest_day_pay))}</span>
            </div>
          `:""}

          ${a.holiday_pay>0?`
            <div class="detail-row">
              <span class="detail-label">Holiday Pay (${a.total_holiday_hours} hrs)</span>
              <span class="detail-value">${s(Number(a.holiday_pay))}</span>
            </div>
          `:""}

          ${a.night_differential_pay>0?`
            <div class="detail-row">
              <span class="detail-label">Night Differential (${a.total_night_diff_hours} hrs)</span>
              <span class="detail-value">${s(Number(a.night_differential_pay))}</span>
            </div>
          `:""}

          ${((w=a.allowances)==null?void 0:w.map(e=>`
            <div class="detail-row">
              <span class="detail-label">${e.name}</span>
              <span class="detail-value">${s(Number(e.amount))}</span>
            </div>
          `).join(""))||""}

          <div class="detail-row total-row">
            <span class="detail-label">Total Earnings</span>
            <span class="detail-value">${s(Number(a.gross_pay))}</span>
          </div>
        </div>

        <div class="deductions-section">
          <div class="section-title">Deductions</div>

          ${i.length>0?`
            <div class="detail-row">
              <span class="detail-label"><strong>Government Contributions</strong></span>
              <span class="detail-value"></span>
            </div>
            ${i.map(e=>`
              <div class="detail-row">
                <span class="detail-label">${e.name}</span>
                <span class="detail-value">${s(Number(e.amount))}</span>
              </div>
            `).join("")}
          `:""}

          ${c.length>0?`
            <div class="detail-row">
              <span class="detail-label"><strong>Taxes</strong></span>
              <span class="detail-value"></span>
            </div>
            ${c.map(e=>`
              <div class="detail-row">
                <span class="detail-label">${e.name}</span>
                <span class="detail-value">${s(Number(e.amount))}</span>
              </div>
            `).join("")}
          `:""}

          ${m.length>0?`
            <div class="detail-row">
              <span class="detail-label"><strong>Other Deductions</strong></span>
              <span class="detail-value"></span>
            </div>
            ${m.map(e=>`
              <div class="detail-row">
                <span class="detail-label">${e.name}</span>
                <span class="detail-value">${s(Number(e.amount))}</span>
              </div>
            `).join("")}
          `:""}

          <div class="detail-row total-row">
            <span class="detail-label">Total Deductions</span>
            <span class="detail-value">${s(Number(a.total_deductions))}</span>
          </div>
        </div>

        <div class="summary-section">
          <div class="detail-row">
            <span class="detail-label">Gross Pay</span>
            <span class="detail-value">${s(Number(a.gross_pay))}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Total Deductions</span>
            <span class="detail-value">${s(Number(a.total_deductions))}</span>
          </div>
          <div class="detail-row total-row">
            <span class="detail-label">Net Pay</span>
            <span class="detail-value net-pay">${s(Number(a.net_pay))}</span>
          </div>
        </div>

        <div class="calculation-section">
          <div class="calculation-title">Government Contribution Calculations</div>

          ${d?`
            <div>
              <h4>SSS Contribution</h4>
              <div class="formula">
                Monthly Salary: ${s(t)}
                SSS Employee Share: ${s(Number(d.amount))}
              </div>
              <div class="formula-explanation">
                <strong>Formula:</strong> Based on SSS Contribution Table (2023)<br>
                For monthly salary of ${s(t)}:<br>
                - Employee pays a fixed amount based on salary bracket<br>
                - Brackets range from ₱135 (for salary up to ₱3,249.99) to ₱1,125 (for salary ₱24,750 and above)<br>
                - Employer pays 1.89 times the employee contribution
              </div>
            </div>
          `:""}

          ${v?`
            <div>
              <h4>PhilHealth Contribution</h4>
              <div class="formula">
                Monthly Salary: ${s(t)}
                PhilHealth Employee Share: ${s(Number(v.amount))}
              </div>
              <div class="formula-explanation">
                <strong>Formula:</strong> 2% of monthly salary (employee share)<br>
                ${s(t)} × 2% = ${s(t*.02)}<br>
                <br>
                <strong>Notes:</strong><br>
                - Total PhilHealth contribution is 4% of monthly salary<br>
                - Split equally between employee (2%) and employer (2%)<br>
                - Minimum monthly salary for calculation: ₱10,000<br>
                - Maximum monthly salary for calculation: ₱80,000
              </div>
            </div>
          `:""}

          ${y?`
            <div>
              <h4>Pag-IBIG Contribution</h4>
              <div class="formula">
                Monthly Salary: ${s(t)}
                Pag-IBIG Employee Share: ${s(Number(y.amount))}
              </div>
              <div class="formula-explanation">
                <strong>Formula:</strong><br>
                - For monthly salary up to ₱1,500: 1% employee contribution<br>
                - For monthly salary over ₱1,500: 2% employee contribution<br>
                <br>
                ${t<=1500?`${s(t)} × 1% = ${s(t*.01)}`:`${s(t)} × 2% = ${s(t*.02)}`}<br>
                <br>
                <strong>Notes:</strong><br>
                - Employer always contributes 2% of monthly salary<br>
                - Maximum monthly contribution: ₱100 for employee, ₱100 for employer
              </div>
            </div>
          `:""}

          ${c.length>0?`
            <div>
              <h4>Withholding Tax</h4>
              <div class="formula">
                Taxable Income: ${s(Number(a.taxable_income||0))}
                Withholding Tax: ${s(Number(a.withholding_tax||0))}
              </div>
              <div class="formula-explanation">
                <strong>Formula:</strong> Based on BIR Withholding Tax Table (2023)<br>
                <br>
                <strong>Tax Brackets:</strong><br>
                - ₱0 to ₱20,833: 0%<br>
                - ₱20,834 to ₱33,332: 15% of excess over ₱20,833<br>
                - ₱33,333 to ₱66,666: ₱1,875 + 20% of excess over ₱33,333<br>
                - ₱66,667 to ₱166,666: ₱8,541.80 + 25% of excess over ₱66,667<br>
                - ₱166,667 to ₱666,666: ₱33,541.80 + 30% of excess over ₱166,667<br>
                - Over ₱666,667: ₱183,541.80 + 35% of excess over ₱666,667
              </div>
            </div>
          `:""}
        </div>

        <div class="footer">
          <p>This is a computer-generated document. No signature is required.</p>
          <p>For questions regarding this payslip, please contact the HR department.</p>
        </div>
      </div>
    </body>
    </html>
  `};export{P as a,z as g};
