import{j as e}from"./index-C6AV3cVN.js";import{C as s}from"./Card-yj7fueH8.js";const t=()=>e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(s,{children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Sales Reports"}),e.jsx("p",{className:"text-gray-500",children:"View and generate sales reports. Analyze sales data by product, category, customer, and time period."}),e.jsx("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:e.jsx("p",{className:"text-center text-gray-400",children:"Sales reports will be displayed here"})})]})});export{t as default};
