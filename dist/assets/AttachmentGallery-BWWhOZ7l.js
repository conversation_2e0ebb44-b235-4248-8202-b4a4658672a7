import{s as S,b as T,h as W,r as w,j as e,B as z,X as L,bR as Q,V as K,bK as U,A as q,i as X,aj as O,e as I,bS as J,P as V,a1 as Y,bv as Z,a2 as ee,a3 as se,M as B,Z as te}from"./index-C6AV3cVN.js";import{C as G}from"./Card-yj7fueH8.js";const E={"image/jpeg":{type:"image",ext:"jpg",maxSize:10*1024*1024},"image/png":{type:"image",ext:"png",maxSize:10*1024*1024},"image/gif":{type:"image",ext:"gif",maxSize:10*1024*1024},"image/webp":{type:"image",ext:"webp",maxSize:10*1024*1024},"application/pdf":{type:"document",ext:"pdf",maxSize:10*1024*1024},"text/plain":{type:"document",ext:"txt",maxSize:1*1024*1024}},re=l=>{if(!(l.type in E))return{valid:!1,error:`File type ${l.type} is not allowed. Allowed types: ${Object.keys(E).join(", ")}`};const r=E[l.type];return l.size>r.maxSize?{valid:!1,error:`File size exceeds ${r.maxSize/1048576}MB limit`}:{valid:!0}},ae=async(l,r,a,t,o,D,f)=>{try{const x=re(t);if(!x.valid)return{success:!1,error:x.error};const g=E[t.type].ext,j=Date.now(),b=Math.random().toString(36).substring(2,15),y=`${r}_${a}_${j}_${b}.${g}`,F=`${l}/${r}s/${y}`,{data:u,error:n}=await S.storage.from("payable-attachments").upload(F,t,{cacheControl:"3600",upsert:!1});if(n)return console.error("Storage upload error:",n),{success:!1,error:"Failed to upload file to storage"};const{data:{publicUrl:C}}=S.storage.from("payable-attachments").getPublicUrl(u.path),k={attachable_type:r,attachable_id:a,file_name:t.name,file_url:C,file_size:t.size,file_type:t.type,file_extension:g,description:D||"",is_primary:f||!1};return await ie(l,k,o)}catch(x){return console.error("Error in uploadAttachment:",x),{success:!1,error:x.message}}},ie=async(l,r,a)=>{try{r.is_primary&&await S.from("payable_attachments").update({is_primary:!1}).eq("organization_id",l).eq("attachable_type",r.attachable_type).eq("attachable_id",r.attachable_id);const{data:t,error:o}=await S.from("payable_attachments").insert({organization_id:l,...r,uploaded_by:a}).select().single();return o?(console.error("Error creating attachment:",o),{success:!1,error:o.message}):{success:!0,attachment:t}}catch(t){return console.error("Error in createAttachment:",t),{success:!1,error:t.message}}},le=async(l,r,a)=>{try{const{data:t,error:o}=await S.from("payable_attachments").select("*").eq("organization_id",l).eq("attachable_type",r).eq("attachable_id",a).order("is_primary",{ascending:!1}).order("created_at",{ascending:!0});return o?(console.error("Error fetching attachments:",o),{success:!1,error:o.message}):{success:!0,attachments:t||[]}}catch(t){return console.error("Error in getAttachments:",t),{success:!1,error:t.message}}},ce=async(l,r)=>{try{const{data:a,error:t}=await S.from("payable_attachments").select("*").eq("id",l).eq("uploaded_by",r).single();if(t||!a)return{success:!1,error:"Attachment not found or access denied"};const f=a.file_url.split("/").slice(-3).join("/"),{error:x}=await S.storage.from("payable-attachments").remove([f]);x&&console.warn("Storage deletion error:",x);const{error:g}=await S.from("payable_attachments").delete().eq("id",l);return g?(console.error("Error deleting attachment:",g),{success:!1,error:g.message}):{success:!0}}catch(a){return console.error("Error in deleteAttachment:",a),{success:!1,error:a.message}}},ne=async l=>{try{const{data:r,error:a}=await S.rpc("set_primary_attachment",{p_attachment_id:l});return a?(console.error("Error setting primary attachment:",a),{success:!1,error:a.message}):r?{success:!0}:{success:!1,error:"Attachment not found"}}catch(r){return console.error("Error in setPrimaryAttachment:",r),{success:!1,error:r.message}}},oe=async(l,r,a)=>{try{const{error:t}=await S.from("payable_attachments").update({description:r}).eq("id",l).eq("uploaded_by",a);return t?(console.error("Error updating attachment description:",t),{success:!1,error:t.message}):{success:!0}}catch(t){return console.error("Error in updateAttachmentDescription:",t),{success:!1,error:t.message}}},de=({attachableType:l,attachableId:r,onUploadComplete:a,onUploadError:t,maxFiles:o=10,disabled:D=!1,className:f=""})=>{const{user:x}=T(),{currentOrganization:g}=W(),j=w.useRef(null),[b,y]=w.useState([]),[F,u]=w.useState(!1),n=i=>{const p=Array.from(i.target.files||[]);if(p.length===0)return;if(b.length+p.length>o){t==null||t(`Maximum ${o} files allowed`);return}const d=p.map(h=>({file:h,progress:0,status:"pending"}));y(h=>[...h,...d]),C([...b,...d]),j.current&&(j.current.value="")},C=async i=>{if(!g||!x)return;u(!0);const p=[];for(let d=0;d<i.length;d++){const h=i[d];if(h.status==="pending")try{y(N=>N.map((m,A)=>A===d?{...m,status:"uploading",progress:0}:m));const $=setInterval(()=>{y(N=>N.map((m,A)=>A===d&&m.status==="uploading"?{...m,progress:Math.min(m.progress+10,90)}:m))},100),v=await ae(g.id,l,r,h.file,x.id,"",!1);clearInterval($),v.success&&v.attachment?(y(N=>N.map((m,A)=>A===d?{...m,status:"completed",progress:100,attachment:v.attachment}:m)),p.push(v.attachment)):(y(N=>N.map((m,A)=>A===d?{...m,status:"error",error:v.error||"Upload failed"}:m)),t==null||t(v.error||"Upload failed"))}catch($){y(v=>v.map((N,m)=>m===d?{...N,status:"error",error:$.message}:N)),t==null||t($.message)}}u(!1),p.length>0&&(a==null||a(p)),setTimeout(()=>{y(d=>d.filter(h=>h.status!=="completed"))},3e3)},k=i=>{y(p=>p.filter((d,h)=>h!==i))},M=i=>{switch(i){case"pending":return"gray";case"uploading":return"blue";case"completed":return"green";case"error":return"red";default:return"gray"}};return e.jsxs("div",{className:`space-y-4 ${f}`,children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(z,{color:"light",size:"sm",onClick:()=>{var i;return(i=j.current)==null?void 0:i.click()},disabled:D||F||b.length>=o,children:[e.jsx(L,{className:"mr-2 h-4 w-4"}),"Upload Images"]}),e.jsxs("span",{className:"text-sm text-gray-500",children:[b.length,"/",o," files"]})]}),e.jsx("input",{ref:j,type:"file",multiple:!0,accept:"image/*,.pdf",onChange:n,className:"hidden"}),b.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium",children:"Upload Progress"}),b.map((i,p)=>e.jsxs("div",{className:"border rounded-lg p-3",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Q,{className:"h-4 w-4 text-gray-400"}),e.jsx("span",{className:"text-sm font-medium truncate max-w-xs",children:i.file.name}),e.jsxs("span",{className:"text-xs text-gray-500",children:["(",(i.file.size/1024/1024).toFixed(1)," MB)"]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:`text-xs px-2 py-1 rounded-full bg-${M(i.status)}-100 text-${M(i.status)}-800`,children:i.status}),i.status==="pending"&&e.jsx(z,{color:"light",size:"xs",onClick:()=>k(p),children:e.jsx(K,{className:"h-3 w-3"})})]})]}),i.status==="uploading"&&e.jsx(U,{progress:i.progress,color:"blue",size:"sm"}),i.status==="error"&&i.error&&e.jsx(q,{color:"failure",className:"mt-2",children:e.jsx("span",{className:"text-sm",children:i.error})}),i.status==="completed"&&e.jsx(q,{color:"success",className:"mt-2",children:e.jsx("span",{className:"text-sm",children:"Upload completed successfully!"})})]},p))]}),e.jsxs("div",{className:"text-xs text-gray-500",children:[e.jsx("p",{children:"• Supported formats: JPEG, PNG, GIF, WebP, PDF"}),e.jsx("p",{children:"• Maximum file size: 10MB per file"}),e.jsxs("p",{children:["• Maximum ",o," files total"]})]})]})},pe=({attachableType:l,attachableId:r,editable:a=!0,showUpload:t=!0,maxFiles:o=10,className:D=""})=>{const{user:f}=T(),{currentOrganization:x}=W(),[g,j]=w.useState([]),[b,y]=w.useState(!0),[F,u]=w.useState(null),[n,C]=w.useState(null),[k,M]=w.useState(!1),[i,p]=w.useState(null),[d,h]=w.useState(""),$=async()=>{if(x){y(!0),u(null);try{const s=await le(x.id,l,r);s.success?j(s.attachments||[]):u(s.error||"Failed to load attachments")}catch(s){u(s.message)}finally{y(!1)}}};w.useEffect(()=>{$()},[x,l,r]);const v=s=>{j(c=>[...c,...s])},N=async s=>{if(f)try{const c=await ce(s,f.id);c.success?j(P=>P.filter(_=>_.id!==s)):u(c.error||"Failed to delete attachment")}catch(c){u(c.message)}},m=async s=>{try{const c=await ne(s);c.success?j(P=>P.map(_=>({..._,is_primary:_.id===s}))):u(c.error||"Failed to set primary attachment")}catch(c){u(c.message)}},A=async s=>{if(f)try{const c=await oe(s,d,f.id);c.success?(j(P=>P.map(_=>_.id===s?{..._,description:d}:_)),p(null),h("")):u(c.error||"Failed to update description")}catch(c){u(c.message)}},R=s=>s.startsWith("image/")?null:e.jsx(O,{className:"h-8 w-8 text-gray-400"}),H=s=>{if(s===0)return"0 B";const c=1024,P=["B","KB","MB","GB"],_=Math.floor(Math.log(s)/Math.log(c));return parseFloat((s/Math.pow(c,_)).toFixed(1))+" "+P[_]};return b?e.jsx("div",{className:"flex justify-center items-center h-32",children:e.jsx(X,{size:"lg"})}):e.jsxs("div",{className:`space-y-4 ${D}`,children:[F&&e.jsx(q,{color:"failure",onDismiss:()=>u(null),children:F}),t&&a&&e.jsxs(G,{children:[e.jsx("h4",{className:"text-lg font-semibold mb-3",children:"Upload Attachments"}),e.jsx(de,{attachableType:l,attachableId:r,onUploadComplete:v,onUploadError:u,maxFiles:o})]}),e.jsxs(G,{children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("h4",{className:"text-lg font-semibold",children:["Attachments (",g.length,")"]})}),g.length===0?e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx(O,{className:"h-12 w-12 mx-auto mb-2 text-gray-300"}),e.jsx("p",{children:"No attachments yet"}),t&&a&&e.jsx("p",{className:"text-sm",children:"Upload some files to get started"})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:g.map(s=>e.jsxs("div",{className:"border rounded-lg p-3 hover:shadow-md transition-shadow",children:[e.jsx("div",{className:"aspect-square mb-3 bg-gray-50 rounded-lg flex items-center justify-center overflow-hidden",children:s.file_type.startsWith("image/")?e.jsx("img",{src:s.file_url,alt:s.file_name,className:"w-full h-full object-cover cursor-pointer",onClick:()=>{C(s),M(!0)}}):e.jsxs("div",{className:"text-center",children:[R(s.file_type),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:s.file_extension.toUpperCase()})]})}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h5",{className:"text-sm font-medium truncate flex-1",children:s.file_name}),s.is_primary&&e.jsxs(I,{color:"yellow",size:"xs",children:[e.jsx(J,{className:"h-3 w-3 mr-1"}),"Primary"]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:H(s.file_size)}),i===s.id?e.jsxs("div",{className:"space-y-2",children:[e.jsx(V,{size:"sm",value:d,onChange:c=>h(c.target.value),placeholder:"Add description..."}),e.jsxs("div",{className:"flex gap-1",children:[e.jsx(z,{size:"xs",onClick:()=>A(s.id),children:"Save"}),e.jsx(z,{size:"xs",color:"light",onClick:()=>{p(null),h("")},children:"Cancel"})]})]}):e.jsx("p",{className:"text-xs text-gray-600",children:s.description||"No description"}),a&&e.jsxs("div",{className:"flex gap-1 pt-2",children:[e.jsx(z,{size:"xs",color:"light",onClick:()=>{C(s),M(!0)},children:e.jsx(Y,{className:"h-3 w-3"})}),!s.is_primary&&e.jsx(z,{size:"xs",color:"light",onClick:()=>m(s.id),title:"Set as primary",children:e.jsx(Z,{className:"h-3 w-3"})}),e.jsx(z,{size:"xs",color:"light",onClick:()=>{p(s.id),h(s.description||"")},title:"Edit description",children:e.jsx(ee,{className:"h-3 w-3"})}),s.uploaded_by===(f==null?void 0:f.id)&&e.jsx(z,{size:"xs",color:"failure",onClick:()=>N(s.id),title:"Delete",children:e.jsx(se,{className:"h-3 w-3"})})]})]})]},s.id))})]}),e.jsxs(B,{show:k,onClose:()=>{M(!1),C(null)},size:"4xl",children:[e.jsx(B.Header,{children:n==null?void 0:n.file_name}),e.jsx(B.Body,{children:n&&e.jsxs("div",{className:"space-y-4",children:[n.file_type.startsWith("image/")?e.jsx("img",{src:n.file_url,alt:n.file_name,className:"w-full h-auto max-h-96 object-contain mx-auto"}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(O,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),e.jsx("p",{className:"text-lg font-medium",children:n.file_name}),e.jsxs("p",{className:"text-gray-500",children:[n.file_extension.toUpperCase()," • ",H(n.file_size)]})]}),n.description&&e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Description"}),e.jsx("p",{className:"text-gray-600",children:n.description})]})]})}),e.jsxs(B.Footer,{children:[e.jsxs(z,{color:"primary",onClick:()=>window.open(n==null?void 0:n.file_url,"_blank"),children:[e.jsx(te,{className:"mr-2 h-4 w-4"}),"Download"]}),e.jsx(z,{color:"light",onClick:()=>{M(!1),C(null)},children:"Close"})]})]})]})};export{pe as A,le as g};
