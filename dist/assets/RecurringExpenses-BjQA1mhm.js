import{b as ye,h as _e,r,j as e,i as O,B as p,H as ve,a7 as g,P as x,A as J,_ as i,e as v,b9 as fe,bb as be,bT as Ne,M as h,ad as Ce,U as Se}from"./index-C6AV3cVN.js";import{C as we}from"./Card-yj7fueH8.js";import{P as Ee}from"./Pagination-CVEzfctr.js";import{R,e as Pe,b as De,f as Re,h as ke,i as Ae,j as Te}from"./operationalExpenses-C7_pxLV1.js";import{g as Fe}from"./supplier-BJDz25mb.js";import{g as He}from"./employee-DWC25S7P.js";const Le=()=>{var X,Y;const{user:b}=ye(),{currentOrganization:o}=_e(),[k,K]=r.useState([]),[Q,Z]=r.useState([]),[ee,ae]=r.useState([]),[se,te]=r.useState([]),[le,I]=r.useState(!0),[N,c]=r.useState(null),[re,C]=r.useState(!1),[ie,f]=r.useState(!1),[m,A]=r.useState(null),[t,T]=r.useState(null),[S,F]=r.useState(null),[j,w]=r.useState(!1),[u,E]=r.useState({}),[z,M]=r.useState(1),[P,ne]=r.useState(10),[$,de]=r.useState(0),[s,n]=r.useState({name:"",description:"",expense_type_id:"",supplier_id:"",employee_id:"",amount:0,vat_amount:0,withholding_tax_rate:0,frequency:R.MONTHLY,start_date:new Date().toISOString().split("T")[0],end_date:"",payment_terms_days:30,auto_create_payable:!1}),H=async()=>{if(o)try{I(!0);const[a,l,d,y]=await Promise.all([Pe(o.id,u),De(o.id,{is_active:!0}),Fe(o.id),He(o.id)]);a.success&&a.data?(K(a.data),de(a.data.length)):c(a.error||"Failed to load recurring expenses"),l.success&&l.data&&Z(l.data),d.suppliers&&ae(d.suppliers),y.employees&&te(y.employees)}catch(a){c(a.message)}finally{I(!1)}};r.useEffect(()=>{H()},[o,u]);const ce=Math.ceil($/P),L=(z-1)*P,oe=L+P,ue=k.slice(L,oe),B=async a=>{if(a.preventDefault(),!(!o||!b))try{w(!0),c(null);const l={...s,expense_type_id:s.expense_type_id||void 0,supplier_id:s.supplier_id||null,employee_id:s.employee_id||null,end_date:s.end_date||void 0};if(!l.supplier_id&&!l.employee_id){c("Please select either a supplier or employee");return}if(l.supplier_id&&l.employee_id){c("Please select either a supplier OR employee, not both");return}let d;S?d=await Re(S.id,l):d=await ke(o.id,l,b.id),d.success?(C(!1),F(null),U(),H()):c(d.error||"Failed to save recurring expense")}catch(l){c(l.message)}finally{w(!1)}},me=a=>{F(a),n({name:a.name,description:a.description||"",expense_type_id:a.expense_type_id||"",supplier_id:a.supplier_id||"",employee_id:a.employee_id||"",amount:a.amount,vat_amount:a.vat_amount,withholding_tax_rate:a.withholding_tax_rate,frequency:a.frequency,start_date:a.start_date,end_date:a.end_date||"",payment_terms_days:a.payment_terms_days,auto_create_payable:a.auto_create_payable}),C(!0)},U=()=>{n({name:"",description:"",expense_type_id:"",supplier_id:"",employee_id:"",amount:0,vat_amount:0,withholding_tax_rate:0,frequency:R.MONTHLY,start_date:new Date().toISOString().split("T")[0],end_date:"",payment_terms_days:30,auto_create_payable:!1})},V=()=>{C(!1),F(null),U(),c(null)},D=a=>a.replace(/_/g," ").replace(/\b\w/g,l=>l.toUpperCase()),pe=a=>new Date(a).toLocaleDateString(),W=a=>{const l=new Date(a),d=new Date,y=l.getTime()-d.getTime(),_=Math.ceil(y/(1e3*60*60*24));return _<=7&&_>=0},q=a=>new Date(a)<new Date,xe=a=>{T(a),A("approve"),f(!0)},he=a=>{T(a),A("reject"),f(!0)},ge=async()=>{if(!(!t||!m||!o||!b))try{if(w(!0),c(null),m==="approve"){const a=Date.now(),l=`${t.id}-${a}`,d=`REC-${t.name.replace(/\s+/g,"-").toUpperCase()}-${a}`,y=new Date,_=new Date;_.setDate(_.getDate()+t.payment_terms_days);const je={source_type:Ae.RECURRING_EXPENSE,source_id:l,supplier_id:t.supplier_id||void 0,employee_id:t.employee_id||void 0,reference_number:d,invoice_date:y.toISOString().split("T")[0],due_date:_.toISOString().split("T")[0],amount:t.amount,vat_amount:t.vat_amount,withholding_tax_rate:t.withholding_tax_rate,withholding_tax_amount:t.withholding_tax_rate>0?t.amount*t.withholding_tax_rate/100:0,currency:"PHP",category:t.category||"",notes:`Created from recurring expense: ${t.name}`,expense_type_id:t.expense_type_id||void 0,department:t.department||"",project_code:t.project_code||""},G=await Te(o.id,je,b.id);if(!G.success){c(G.error||"Failed to create payable");return}}else m==="reject"&&console.log("Rejected recurring expense:",t.name);f(!1),T(null),A(null),await H()}catch(a){c(a.message)}finally{w(!1)}};return le?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(O,{size:"xl"})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(we,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Recurring Expenses"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Manage recurring expense templates and schedules"})]}),e.jsxs(p,{onClick:()=>C(!0),className:"bg-primary",children:[e.jsx(ve,{className:"mr-2 h-4 w-4"}),"Add Recurring Expense"]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs(g,{value:u.frequency||"",onChange:a=>E({...u,frequency:a.target.value||void 0}),children:[e.jsx("option",{value:"",children:"All Frequencies"}),Object.values(R).map(a=>e.jsx("option",{value:a,children:D(a)},a))]}),e.jsxs(g,{value:((X=u.is_active)==null?void 0:X.toString())||"",onChange:a=>E({...u,is_active:a.target.value?a.target.value==="true":void 0}),children:[e.jsx("option",{value:"",children:"All Status"}),e.jsx("option",{value:"true",children:"Active"}),e.jsx("option",{value:"false",children:"Inactive"})]}),e.jsxs(g,{value:((Y=u.due_soon)==null?void 0:Y.toString())||"",onChange:a=>E({...u,due_soon:a.target.value?a.target.value==="true":void 0}),children:[e.jsx("option",{value:"",children:"All Due Dates"}),e.jsx("option",{value:"true",children:"Due Soon (30 days)"})]}),e.jsx(x,{placeholder:"Search expenses...",value:u.search||"",onChange:a=>E({...u,search:a.target.value||void 0})})]}),N&&e.jsx(J,{color:"failure",className:"mb-4",children:N}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(i,{children:[e.jsxs(i.Head,{children:[e.jsx(i.HeadCell,{children:"Name"}),e.jsx(i.HeadCell,{children:"Amount"}),e.jsx(i.HeadCell,{children:"Frequency"}),e.jsx(i.HeadCell,{children:"Next Due"}),e.jsx(i.HeadCell,{children:"Payee"}),e.jsx(i.HeadCell,{children:"Status"}),e.jsx(i.HeadCell,{children:"Actions"})]}),e.jsx(i.Body,{className:"divide-y",children:ue.map(a=>e.jsxs(i.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(i.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold",children:a.name}),a.description&&e.jsx("div",{className:"text-sm text-gray-500",children:a.description}),a.expense_type&&e.jsx(v,{color:"gray",size:"sm",className:"mt-1",children:a.expense_type.name})]})}),e.jsxs(i.Cell,{children:[e.jsxs("div",{className:"font-semibold",children:["₱",a.amount.toLocaleString()]}),a.vat_amount>0&&e.jsxs("div",{className:"text-xs text-gray-500",children:["VAT: ₱",a.vat_amount.toLocaleString()]})]}),e.jsx(i.Cell,{children:e.jsx(v,{color:"blue",children:D(a.frequency)})}),e.jsxs(i.Cell,{children:[e.jsx("div",{className:`font-medium ${q(a.next_due_date)?"text-red-600":W(a.next_due_date)?"text-yellow-600":"text-gray-900 dark:text-white"}`,children:pe(a.next_due_date)}),q(a.next_due_date)&&e.jsx(v,{color:"failure",size:"sm",children:"Overdue"}),W(a.next_due_date)&&!q(a.next_due_date)&&e.jsx(v,{color:"warning",size:"sm",children:"Due Soon"})]}),e.jsx(i.Cell,{children:a.supplier?e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:a.supplier.name}),e.jsx("div",{className:"text-sm text-gray-500",children:"Supplier"})]}):a.employee?e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium",children:[a.employee.first_name," ",a.employee.last_name]}),e.jsx("div",{className:"text-sm text-gray-500",children:"Employee"})]}):e.jsx("span",{className:"text-gray-500",children:"Not specified"})}),e.jsx(i.Cell,{children:e.jsxs("div",{className:"flex flex-col space-y-1",children:[e.jsx(v,{color:a.is_active?"green":"red",children:a.is_active?"Active":"Inactive"}),a.auto_create_payable&&e.jsx(v,{color:"purple",size:"sm",children:"Auto-create"})]})}),e.jsx(i.Cell,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(p,{size:"xs",color:"success",onClick:()=>xe(a),disabled:!a.is_active||j,title:"Send to Enhanced Payables",children:e.jsx(fe,{className:"h-3 w-3"})}),e.jsx(p,{size:"xs",color:"failure",onClick:()=>he(a),disabled:j,title:"Reject",children:e.jsx(be,{className:"h-3 w-3"})}),e.jsx(p,{size:"xs",color:"gray",onClick:()=>me(a),title:"Edit",children:e.jsx(Ne,{className:"h-3 w-3"})})]})})]},a.id))})]})}),k.length===0&&e.jsx("div",{className:"text-center py-8 text-gray-500",children:"No recurring expenses found. Create your first recurring expense template to get started."}),k.length>0&&e.jsx(Ee,{currentPage:z,totalPages:ce,itemsPerPage:P,totalItems:$,onPageChange:M,onItemsPerPageChange:a=>{ne(a),M(1)},itemName:"recurring expenses"})]}),e.jsxs(h,{show:re,onClose:V,size:"xl",children:[e.jsx(h.Header,{children:S?"Edit Recurring Expense":"Create Recurring Expense"}),e.jsx(h.Body,{children:e.jsxs("form",{onSubmit:B,className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Name *"}),e.jsx(x,{value:s.name,onChange:a=>n({...s,name:a.target.value}),required:!0,placeholder:"e.g., Office Rent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Expense Type"}),e.jsxs(g,{value:s.expense_type_id,onChange:a=>n({...s,expense_type_id:a.target.value}),children:[e.jsx("option",{value:"",children:"Select expense type"}),Q.map(a=>e.jsxs("option",{value:a.id,children:[a.name," (",a.code,")"]},a.id))]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Description"}),e.jsx(Ce,{value:s.description,onChange:a=>n({...s,description:a.target.value}),placeholder:"Optional description",rows:3})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Supplier"}),e.jsxs(g,{value:s.supplier_id,onChange:a=>n({...s,supplier_id:a.target.value,employee_id:""}),children:[e.jsx("option",{value:"",children:"Select supplier"}),ee.map(a=>e.jsx("option",{value:a.id,children:a.name},a.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Employee"}),e.jsxs(g,{value:s.employee_id,onChange:a=>n({...s,employee_id:a.target.value,supplier_id:""}),children:[e.jsx("option",{value:"",children:"Select employee"}),se.map(a=>e.jsxs("option",{value:a.id,children:[a.first_name," ",a.last_name]},a.id))]})]})]}),e.jsxs("div",{className:"text-sm text-gray-500 mb-4",children:[e.jsx("strong",{children:"Note:"})," Please select either a supplier OR an employee (not both)."]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Amount (₱) *"}),e.jsx(x,{type:"number",value:s.amount,onChange:a=>n({...s,amount:parseFloat(a.target.value)||0}),required:!0,min:"0",step:"0.01",placeholder:"0.00"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"VAT Amount (₱)"}),e.jsx(x,{type:"number",value:s.vat_amount,onChange:a=>n({...s,vat_amount:parseFloat(a.target.value)||0}),min:"0",step:"0.01",placeholder:"0.00"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Withholding Tax (%)"}),e.jsx(x,{type:"number",value:s.withholding_tax_rate,onChange:a=>n({...s,withholding_tax_rate:parseFloat(a.target.value)||0}),min:"0",max:"100",step:"0.01",placeholder:"0.00"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Frequency *"}),e.jsx(g,{value:s.frequency,onChange:a=>n({...s,frequency:a.target.value}),required:!0,children:Object.values(R).map(a=>e.jsx("option",{value:a,children:D(a)},a))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Payment Terms (Days)"}),e.jsx(x,{type:"number",value:s.payment_terms_days,onChange:a=>n({...s,payment_terms_days:parseInt(a.target.value)||30}),min:"0",placeholder:"30"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Start Date *"}),e.jsx(x,{type:"date",value:s.start_date,onChange:a=>n({...s,start_date:a.target.value}),required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"End Date (Optional)"}),e.jsx(x,{type:"date",value:s.end_date,onChange:a=>n({...s,end_date:a.target.value})})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(Se,{checked:s.auto_create_payable,onChange:a=>n({...s,auto_create_payable:a.target.checked})}),e.jsx("label",{className:"text-sm font-medium",children:"Auto-create payables when due"})]}),N&&e.jsx(J,{color:"failure",children:N})]})}),e.jsxs(h.Footer,{children:[e.jsxs(p,{onClick:B,disabled:j,className:"bg-primary",children:[j?e.jsx(O,{size:"sm",className:"mr-2"}):null,S?"Update":"Create"]}),e.jsx(p,{color:"gray",onClick:V,children:"Cancel"})]})]}),e.jsxs(h,{show:ie,onClose:()=>f(!1),children:[e.jsx(h.Header,{children:m==="approve"?"Send to Enhanced Payables":"Reject Recurring Expense"}),e.jsx(h.Body,{children:t&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm text-gray-700",children:m==="approve"?`Are you sure you want to create a payable from "${t.name}"? This will create a payable in Enhanced Payables for approval.`:`Are you sure you want to reject "${t.name}"?`}),m==="approve"&&e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"Payable Details:"}),e.jsxs("div",{className:"text-sm text-blue-800 space-y-1",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Amount:"})," ₱",t.amount.toLocaleString()]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Frequency:"})," ",D(t.frequency)]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Payee:"})," ",t.supplier?t.supplier.name:t.employee?`${t.employee.first_name} ${t.employee.last_name}`:"Not specified"]}),e.jsxs("div",{className:"mt-2 text-xs text-blue-700 bg-blue-100 p-2 rounded",children:[e.jsx("strong",{children:"Workflow:"})," This will create a payable in Enhanced Payables (pending approval) → Approve/Reject → Traditional Accounts Payable"]})]})]})]})}),e.jsxs(h.Footer,{children:[e.jsxs(p,{color:m==="approve"?"success":"failure",onClick:ge,disabled:j,children:[j?e.jsx(O,{size:"sm",className:"mr-2"}):null,m==="approve"?"Send to Enhanced Payables":"Reject"]}),e.jsx(p,{color:"gray",onClick:()=>f(!1),children:"Cancel"})]})]})]})};export{Le as default};
