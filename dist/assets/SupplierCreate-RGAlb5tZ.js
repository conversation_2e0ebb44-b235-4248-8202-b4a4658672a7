import{d,h as x,r as c,j as e,bw as s,bx as h,A as b,J as f}from"./index-C6AV3cVN.js";import{C as j}from"./Card-yj7fueH8.js";import{c as S}from"./supplier-BJDz25mb.js";import{S as g}from"./SupplierForm-j86Gm3W5.js";const C=()=>{const t=d(),{currentOrganization:l}=x(),[u,n]=c.useState(!1),[a,i]=c.useState(null),p=async m=>{if(l){n(!0),i(null);try{const{supplier:r,error:o}=await S(l.id,m);o?i(o):r&&t(`/suppliers/${r.id}`)}catch(r){i(r.message||"An error occurred while creating the supplier")}finally{n(!1)}}};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"mb-6",children:e.jsxs(s,{children:[e.jsx(s.Item,{href:"/",icon:h,children:"Dashboard"}),e.jsx(s.Item,{href:"/suppliers",children:"Suppliers"}),e.jsx(s.Item,{children:"Create New Supplier"})]})}),e.jsxs(j,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Create New Supplier"}),e.jsx("p",{className:"text-gray-500",children:"Add a new supplier to your database"})]}),a&&e.jsx(b,{color:"failure",icon:f,className:"mb-4",children:a}),e.jsx(g,{onSubmit:p,isSubmitting:u,error:a||void 0,onCancel:()=>t("/suppliers")})]})]})};export{C as default};
