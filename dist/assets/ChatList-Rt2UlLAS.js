import{h as I,b as q,r as l,by as X,j as e,M as z,P as H,Q as A,B as C,i as $,bA as R,U as J,y as U,a6 as K,V as W,d as Y,a0 as Z}from"./index-C6AV3cVN.js";import{u as T}from"./ChatContext-6er2fA7_.js";import{c as ee}from"./formatters-Cypx7G-j.js";import{C as se}from"./CardBox-YV_4IKGE.js";import{E as te}from"./EmptyState-DxqWNypU.js";import"./Card-yj7fueH8.js";const re=({show:j,onClose:b,onChatCreated:O})=>{const{currentOrganization:o}=I(),{user:g}=q(),{startOneOnOneChat:P}=T(),[h,u]=l.useState([]),[y,_]=l.useState([]),[m,w]=l.useState(""),[k,N]=l.useState(!1),[L,p]=l.useState(null),[S,d]=l.useState(!1);l.useEffect(()=>{j&&o&&M()},[j,o]),l.useEffect(()=>{if(!m.trim()){_(h);return}const r=m.toLowerCase(),s=h.filter(c=>{var a,x;return((a=c.first_name)==null?void 0:a.toLowerCase().includes(r))||((x=c.last_name)==null?void 0:x.toLowerCase().includes(r))});_(s)},[m,h]);const M=async()=>{if(o){N(!0),p(null);try{const{members:r,error:s}=await X(o.id);if(s)throw new Error(s);const c=r.filter(a=>a.user_id!==(g==null?void 0:g.id)).map(a=>{var x,B,f;return{id:a.id,user_id:a.user_id,first_name:((x=a.profile)==null?void 0:x.first_name)||null,last_name:((B=a.profile)==null?void 0:B.last_name)||null,avatar_url:((f=a.profile)==null?void 0:f.avatar_url)||null}});u(c),_(c)}catch(r){console.error("Error fetching members:",r),p(r.message||"Failed to load organization members")}finally{N(!1)}}},G=async r=>{if(o){d(!0);try{const s=await P(r);O(s)}catch(s){console.error("Error creating chat:",s),p(s.message||"Failed to create chat")}finally{d(!1)}}},E=()=>{w(""),p(null),b()};return e.jsxs(z,{show:j,onClose:E,size:"md",children:[e.jsx(z.Header,{children:"New Chat"}),e.jsxs(z.Body,{children:[e.jsx("div",{className:"mb-4",children:e.jsx(H,{id:"search-members",type:"text",icon:A,placeholder:"Search members...",value:m,onChange:r=>w(r.target.value)})}),L&&e.jsxs("div",{className:"mb-4 p-3 bg-red-100 text-red-800 rounded-lg",children:[e.jsx("p",{children:L}),e.jsx(C,{color:"light",size:"xs",onClick:M,className:"mt-2",children:"Retry"})]}),e.jsx("div",{className:"max-h-60 overflow-y-auto",children:k?e.jsx("div",{className:"flex justify-center items-center h-40",children:e.jsx($,{size:"lg"})}):y.length===0?e.jsx("div",{className:"text-center text-gray-500 py-8",children:m?"No members found matching your search":"No members available"}):e.jsx("ul",{className:"space-y-2",children:y.map(r=>e.jsx("li",{className:"p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-colors",onClick:()=>G(r.user_id),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(R,{img:r.avatar_url||void 0,rounded:!0,size:"md",placeholderInitials:r.first_name&&r.last_name?`${r.first_name[0]}${r.last_name[0]}`:r.first_name?r.first_name[0]:r.last_name?r.last_name[0]:""}),e.jsx("div",{className:"ml-3",children:e.jsxs("p",{className:"font-medium",children:[r.first_name," ",r.last_name]})})]})},r.id))})})]}),e.jsx(z.Footer,{children:e.jsx(C,{color:"gray",onClick:E,children:"Cancel"})}),S&&e.jsx("div",{className:"absolute inset-0 bg-white/50 dark:bg-gray-900/50 flex justify-center items-center rounded-lg",children:e.jsx($,{size:"xl"})})]})},ae=({show:j,onClose:b,onGroupCreated:O})=>{const{currentOrganization:o}=I(),{user:g}=q(),{startGroupChat:P}=T(),[h,u]=l.useState([]),[y,_]=l.useState([]),[m,w]=l.useState(""),[k,N]=l.useState(""),[L,p]=l.useState(!1),[S,d]=l.useState(null),[M,G]=l.useState(!1),[E,r]=l.useState("select-members");l.useEffect(()=>{j&&o&&(s(),r("select-members"),N(""))},[j,o]),l.useEffect(()=>{if(!m.trim()){_(h);return}const t=m.toLowerCase(),n=h.filter(i=>{var v,Q;return((v=i.first_name)==null?void 0:v.toLowerCase().includes(t))||((Q=i.last_name)==null?void 0:Q.toLowerCase().includes(t))});_(n)},[m,h]);const s=async()=>{if(o){p(!0),d(null);try{const{members:t,error:n}=await X(o.id);if(n)throw new Error(n);const i=t.filter(v=>v.user_id!==(g==null?void 0:g.id)).map(v=>{var Q,D,V;return{id:v.id,user_id:v.user_id,first_name:((Q=v.profile)==null?void 0:Q.first_name)||null,last_name:((D=v.profile)==null?void 0:D.last_name)||null,avatar_url:((V=v.profile)==null?void 0:V.avatar_url)||null,selected:!1}});u(i),_(i)}catch(t){console.error("Error fetching members:",t),d(t.message||"Failed to load organization members")}finally{p(!1)}}},c=t=>{const n=h.map(i=>i.user_id===t?{...i,selected:!i.selected}:i);u(n),_(y.map(i=>i.user_id===t?{...i,selected:!i.selected}:i))},a=()=>{if(h.filter(n=>n.selected).length===0){d("Please select at least one member for the group");return}d(null),r("set-name")},x=async()=>{if(!o)return;if(!k.trim()){d("Please enter a group name");return}const t=h.filter(n=>n.selected).map(n=>n.user_id);if(t.length===0){d("Please select at least one member for the group");return}G(!0),d(null);try{const n=await P(o.id,k.trim(),t);O(n)}catch(n){console.error("Error creating group:",n),d(n.message||"Failed to create group"),G(!1)}},B=()=>{r("select-members"),d(null)},f=()=>{w(""),N(""),d(null),b()},F=h.filter(t=>t.selected).length;return e.jsxs(z,{show:j,onClose:f,size:"md",children:[e.jsx(z.Header,{children:E==="select-members"?"New Group Chat":"Create Group"}),e.jsx(z.Body,{children:E==="select-members"?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-4",children:e.jsx(H,{id:"search-members",type:"text",icon:A,placeholder:"Search members...",value:m,onChange:t=>w(t.target.value)})}),S&&e.jsx("div",{className:"mb-4 p-3 bg-red-100 text-red-800 rounded-lg",children:e.jsx("p",{children:S})}),e.jsxs("div",{className:"mb-2 flex justify-between items-center",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Select members to add to the group"}),e.jsxs("span",{className:"text-sm font-medium",children:[F," selected"]})]}),e.jsx("div",{className:"max-h-60 overflow-y-auto",children:L?e.jsx("div",{className:"flex justify-center items-center h-40",children:e.jsx($,{size:"lg"})}):y.length===0?e.jsx("div",{className:"text-center text-gray-500 py-8",children:m?"No members found matching your search":"No members available"}):e.jsx("ul",{className:"space-y-2",children:y.map(t=>e.jsx("li",{className:"p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-colors",onClick:()=>c(t.user_id),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{id:`member-${t.id}`,checked:t.selected,onChange:()=>{},className:"mr-3"}),e.jsx(R,{img:t.avatar_url||void 0,rounded:!0,size:"md",placeholderInitials:t.first_name&&t.last_name?`${t.first_name[0]}${t.last_name[0]}`:t.first_name?t.first_name[0]:t.last_name?t.last_name[0]:""}),e.jsx("div",{className:"ml-3",children:e.jsxs("p",{className:"font-medium",children:[t.first_name," ",t.last_name]})})]})},t.id))})})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx("div",{className:"w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center",children:e.jsx(U,{className:"w-8 h-8 text-blue-600 dark:text-blue-300"})})}),e.jsxs("div",{className:"mb-4",children:[e.jsx(K,{htmlFor:"group-name",value:"Group Name",className:"mb-2 block"}),e.jsx(H,{id:"group-name",type:"text",placeholder:"Enter group name",value:k,onChange:t=>N(t.target.value),required:!0})]}),S&&e.jsx("div",{className:"mb-4 p-3 bg-red-100 text-red-800 rounded-lg",children:e.jsx("p",{children:S})}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("p",{className:"text-sm font-medium mb-2",children:["Selected Members (",F,")"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:h.filter(t=>t.selected).map(t=>e.jsxs("div",{className:"flex items-center bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1",children:[e.jsxs("span",{className:"text-sm",children:[t.first_name," ",t.last_name]}),e.jsx("button",{type:"button",className:"ml-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300",onClick:()=>c(t.user_id),children:e.jsx(W,{className:"w-4 h-4"})})]},t.id))})]})]})}),e.jsx(z.Footer,{children:E==="select-members"?e.jsxs(e.Fragment,{children:[e.jsx(C,{color:"gray",onClick:f,children:"Cancel"}),e.jsx(C,{color:"primary",onClick:a,disabled:F===0,children:"Next"})]}):e.jsxs(e.Fragment,{children:[e.jsx(C,{color:"gray",onClick:B,children:"Back"}),e.jsxs(C,{color:"primary",onClick:x,disabled:!k.trim()||M,children:[M?e.jsx($,{size:"sm",className:"mr-2"}):null,"Create Group"]})]})}),M&&e.jsx("div",{className:"absolute inset-0 bg-white/50 dark:bg-gray-900/50 flex justify-center items-center rounded-lg",children:e.jsx($,{size:"xl"})})]})},ue=()=>{const j=Y(),{conversations:b,loadingConversations:O,error:o,refreshConversations:g,setCurrentConversationId:P}=T(),{currentOrganization:h}=I(),{user:u}=q(),[y,_]=l.useState(""),[m,w]=l.useState(b),[k,N]=l.useState(!1),[L,p]=l.useState(!1);l.useEffect(()=>{if(!y.trim()){w(b);return}const s=y.toLowerCase(),c=b.filter(a=>{var x;return a.is_group?(x=a.name)==null?void 0:x.toLowerCase().includes(s):a.participants.filter(f=>f.profiles&&f.user_id!==(u==null?void 0:u.id)&&(f.profiles.first_name||f.profiles.last_name)).some(f=>{var F,t,n,i;return((t=(F=f.profiles)==null?void 0:F.first_name)==null?void 0:t.toLowerCase().includes(s))||((i=(n=f.profiles)==null?void 0:n.last_name)==null?void 0:i.toLowerCase().includes(s))})});w(c)},[y,b]),l.useEffect(()=>{b.length===0&&g(!0)},[]);const S=s=>{P(s),j(`/chat/${s}`)},d=s=>{N(!1),g(!0).then(()=>{j(`/chat/${s}`)})},M=s=>{p(!1),g(!0).then(()=>{j(`/chat/${s}`)})},G=s=>{if(s.is_group)return s.name||"Unnamed Group";const c=s.participants.filter(a=>a.profiles&&a.user_id!==(u==null?void 0:u.id));if(c.length>0){const a=c[0].profiles;return a!=null&&a.first_name&&(a!=null&&a.last_name)?`${a.first_name} ${a.last_name}`:"Unknown User"}return"Chat"},E=s=>{var a;if(s.is_group)return null;const c=s.participants.filter(x=>x.profiles&&x.user_id!==(u==null?void 0:u.id));return c.length>0?(a=c[0].profiles)==null?void 0:a.avatar_url:null},r=s=>s.last_message?s.last_message.content.length>30?`${s.last_message.content.substring(0,30)}...`:s.last_message.content:"No messages yet";return e.jsxs("div",{className:"h-full flex flex-col bg-white dark:bg-gray-900",children:[e.jsxs("div",{className:"flex-shrink-0 border-b border-gray-200 dark:border-gray-700 p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Messages"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(C,{color:"primary",size:"sm",onClick:()=>N(!0),className:"flex items-center",children:[e.jsx(Z,{className:"mr-2 h-4 w-4"}),"New Chat"]}),e.jsxs(C,{color:"light",size:"sm",onClick:()=>p(!0),className:"flex items-center",children:[e.jsx(U,{className:"mr-2 h-4 w-4"}),"Group"]})]})]}),e.jsx("div",{className:"relative",children:e.jsx(H,{id:"search",type:"text",icon:A,placeholder:"Search conversations...",value:y,onChange:s=>_(s.target.value),className:"w-full"})})]}),e.jsx("div",{className:"flex-1 overflow-y-auto",children:O?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx($,{size:"xl"})}):o?e.jsx("div",{className:"p-6",children:e.jsx(se,{children:e.jsxs("div",{className:"text-center text-red-500 p-4",children:[e.jsxs("p",{children:["Error loading conversations: ",o]}),e.jsx(C,{color:"light",onClick:()=>g(!0),className:"mt-2",children:"Retry"})]})})}):m.length===0?e.jsx("div",{className:"p-6",children:e.jsx(te,{title:"No conversations yet",description:"Start a new chat or create a group to begin messaging",icon:e.jsx(U,{className:"w-12 h-12"}),actions:e.jsxs("div",{className:"flex space-x-2 justify-center",children:[e.jsx(C,{color:"primary",onClick:()=>N(!0),children:"Start a Chat"}),e.jsx(C,{color:"light",onClick:()=>p(!0),children:"Create a Group"})]})})}):e.jsx("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:m.map(s=>e.jsx("div",{className:"p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-150",onClick:()=>S(s.id),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative flex-shrink-0",children:[s.is_group?e.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:e.jsx(U,{className:"w-6 h-6 text-white"})}):e.jsx("div",{className:"w-12 h-12 rounded-full overflow-hidden ring-2 ring-gray-200 dark:ring-gray-700",children:e.jsx(R,{img:E(s),rounded:!0,size:"md",placeholderInitials:G(s).substring(0,2),className:"w-full h-full object-cover"})}),s.unread_count>0&&e.jsx("div",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium",children:s.unread_count>9?"9+":s.unread_count})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsx("h3",{className:`font-medium truncate ${s.unread_count>0?"text-gray-900 dark:text-white":"text-gray-700 dark:text-gray-300"}`,children:G(s)}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400 flex-shrink-0 ml-2",children:s.last_message?ee(s.last_message.created_at,"en-US",{hour:"numeric",minute:"numeric",hour12:!0}):""})]}),e.jsx("p",{className:`text-sm truncate mt-1 ${s.unread_count>0?"text-gray-600 dark:text-gray-400 font-medium":"text-gray-500 dark:text-gray-500"}`,children:r(s)})]})]})},s.id))})}),e.jsx(re,{show:k,onClose:()=>N(!1),onChatCreated:d}),e.jsx(ae,{show:L,onClose:()=>p(!1),onGroupCreated:M})]})};export{ue as default};
