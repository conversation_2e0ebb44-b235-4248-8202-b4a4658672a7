import{d as re,h as ne,r as l,j as e,aE as M,B as d,a0 as R,A as z,J as O,i as $,_ as i,aj as ce,e as B,$ as oe,V as me,a2 as de,a3 as L,M as h,a6 as n,P as g,ad as q,a8 as ue,a7 as he,U as pe}from"./index-C6AV3cVN.js";import{C as xe}from"./Card-yj7fueH8.js";import{b as je,u as ge,c as Ce,d as fe}from"./qcChecklist-Di86OBsm.js";import{c as ve}from"./formatters-Cypx7G-j.js";import{P as ye}from"./Pagination-CVEzfctr.js";import{E as Ne}from"./EmptyState-743bE0hR.js";const Ie=()=>{re();const{currentOrganization:c}=ne(),[j,U]=l.useState([]),[_,D]=l.useState(!0),[C,r]=l.useState(null),[J,p]=l.useState(!1),[X,f]=l.useState(!1),[o,F]=l.useState(null),[v,y]=l.useState(!1),[H,P]=l.useState(1),[N,Y]=l.useState(10),[k,w]=l.useState(""),[S,A]=l.useState(""),[I,V]=l.useState(!0),[m,T]=l.useState([]);l.useEffect(()=>{c&&b()},[c]);const b=async()=>{if(c){D(!0),r(null);try{const{templates:s,error:t}=await je(c.id);t?r(t):U(s)}catch(s){r(s.message||"An error occurred while fetching QC checklist templates")}finally{D(!1)}}},Q=()=>{w(""),A(""),V(!0),T([{name:"",description:"",itemType:"boolean",isRequired:!0,minValue:null,maxValue:null,unit:"",options:null,passCriteria:null}]),p(!0)},G=s=>{F(s),w(s.name),A(s.description||""),V(s.is_active),p(!0)},K=s=>{F(s),f(!0)},W=()=>{T([...m,{name:"",description:"",itemType:"boolean",isRequired:!0,minValue:null,maxValue:null,unit:"",options:null,passCriteria:null}])},Z=s=>{const t=[...m];t.splice(s,1),T(t)},u=(s,t,a)=>{const x=[...m];x[s]={...x[s],[t]:a},T(x)},ee=async()=>{if(c){if(!k.trim()){r("Template name is required");return}if(m.length===0){r("At least one checklist item is required");return}for(let s=0;s<m.length;s++)if(!m[s].name.trim()){r(`Item #${s+1}: Name is required`);return}y(!0),r(null);try{if(o){const{template:s,error:t}=await ge(c.id,o.id,{name:k,description:S,isActive:I});t?r(t):(b(),p(!1))}else{const{template:s,error:t}=await Ce(c.id,{name:k,description:S,isActive:I,items:m.map(a=>({name:a.name,description:a.description,itemType:a.itemType,isRequired:a.isRequired,minValue:a.minValue,maxValue:a.maxValue,unit:a.unit,options:a.options,passCriteria:a.passCriteria}))});t?r(t):(b(),p(!1))}}catch(s){r(s.message||"An error occurred while saving the QC checklist template")}finally{y(!1)}}},se=async()=>{if(!(!c||!o)){y(!0),r(null);try{const{success:s,error:t}=await fe(c.id,o.id);t?r(t):s&&(b(),f(!1))}catch(s){r(s.message||"An error occurred while deleting the QC checklist template")}finally{y(!1)}}},te=Math.ceil(j.length/N),E=H*N,ae=E-N,le=j.slice(ae,E);return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(xe,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold flex items-center",children:[e.jsx(M,{className:"mr-2 h-6 w-6"}),"QC Checklist Templates"]}),e.jsx("p",{className:"text-gray-500",children:"Manage quality control checklist templates for your products"})]}),e.jsxs(d,{color:"primary",onClick:Q,children:[e.jsx(R,{className:"mr-2 h-5 w-5"}),"Create Template"]})]}),C&&e.jsx(z,{color:"failure",icon:O,className:"mb-4",children:C}),_?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx($,{size:"xl"})}):j.length===0?e.jsx(Ne,{title:"No QC checklist templates found",description:"Create your first QC checklist template to start defining quality control standards for your products.",icon:e.jsx(M,{className:"h-12 w-12"}),actionLabel:"Create Template",onActionClick:Q}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(i,{hoverable:!0,children:[e.jsxs(i.Head,{children:[e.jsx(i.HeadCell,{children:"Template Name"}),e.jsx(i.HeadCell,{children:"Description"}),e.jsx(i.HeadCell,{children:"Status"}),e.jsx(i.HeadCell,{children:"Created"}),e.jsx(i.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(i.Body,{className:"divide-y",children:le.map(s=>e.jsxs(i.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(i.Cell,{className:"font-medium",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ce,{className:"mr-2 h-5 w-5 text-gray-500"}),s.name]})}),e.jsx(i.Cell,{children:s.description||e.jsx("span",{className:"text-gray-500",children:"No description"})}),e.jsx(i.Cell,{children:s.is_active?e.jsx(B,{color:"success",icon:oe,children:"Active"}):e.jsx(B,{color:"gray",icon:me,children:"Inactive"})}),e.jsx(i.Cell,{children:ve(s.created_at)}),e.jsx(i.Cell,{children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(d,{size:"xs",color:"primary",onClick:()=>G(s),children:e.jsx(de,{className:"h-4 w-4"})}),e.jsx(d,{size:"xs",color:"failure",onClick:()=>K(s),children:e.jsx(L,{className:"h-4 w-4"})})]})})]},s.id))})]})}),j.length>0&&e.jsx(ye,{currentPage:H,totalPages:te,itemsPerPage:N,totalItems:j.length,onPageChange:P,onItemsPerPageChange:s=>{Y(s),P(1)},itemName:"templates"})]}),e.jsxs(h,{show:J,onClose:()=>p(!1),size:"xl",children:[e.jsx(h.Header,{children:o?"Edit QC Checklist Template":"Create QC Checklist Template"}),e.jsxs(h.Body,{children:[C&&e.jsx(z,{color:"failure",icon:O,className:"mb-4",children:C}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx(n,{htmlFor:"templateName",value:"Template Name"}),e.jsx(g,{id:"templateName",value:k,onChange:s=>w(s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"templateDescription",value:"Description"}),e.jsx(q,{id:"templateDescription",value:S,onChange:s=>A(s.target.value),rows:3})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(ue,{checked:I,onChange:V,label:"Active"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx(n,{value:"Checklist Items"}),e.jsxs(d,{size:"xs",color:"primary",onClick:W,children:[e.jsx(R,{className:"mr-2 h-4 w-4"}),"Add Item"]})]}),m.length===0?e.jsx("div",{className:"p-4 bg-gray-50 rounded-lg text-center text-gray-500",children:'No items added yet. Click "Add Item" to add your first checklist item.'}):e.jsx("div",{className:"space-y-6",children:m.map((s,t)=>e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"font-medium",children:["Item #",t+1]}),e.jsx(d,{size:"xs",color:"failure",onClick:()=>Z(t),children:e.jsx(L,{className:"h-4 w-4"})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(n,{htmlFor:`itemName-${t}`,value:"Name"}),e.jsx(g,{id:`itemName-${t}`,value:s.name,onChange:a=>u(t,"name",a.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:`itemType-${t}`,value:"Type"}),e.jsxs(he,{id:`itemType-${t}`,value:s.itemType,onChange:a=>u(t,"itemType",a.target.value),required:!0,children:[e.jsx("option",{value:"boolean",children:"Yes/No"}),e.jsx("option",{value:"numeric",children:"Numeric"}),e.jsx("option",{value:"text",children:"Text"}),e.jsx("option",{value:"select",children:"Select"})]})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx(n,{htmlFor:`itemDescription-${t}`,value:"Description"}),e.jsx(q,{id:`itemDescription-${t}`,value:s.description,onChange:a=>u(t,"description",a.target.value),rows:2})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(pe,{id:`itemRequired-${t}`,checked:s.isRequired,onChange:a=>u(t,"isRequired",a.target.checked)}),e.jsx(n,{htmlFor:`itemRequired-${t}`,value:"Required"})]}),s.itemType==="numeric"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx(n,{htmlFor:`itemUnit-${t}`,value:"Unit"}),e.jsx(g,{id:`itemUnit-${t}`,value:s.unit||"",onChange:a=>u(t,"unit",a.target.value),placeholder:"e.g., mm, kg, °C"})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:`itemMinValue-${t}`,value:"Minimum Value"}),e.jsx(g,{id:`itemMinValue-${t}`,type:"number",value:s.minValue||"",onChange:a=>u(t,"minValue",a.target.value?parseFloat(a.target.value):null)})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:`itemMaxValue-${t}`,value:"Maximum Value"}),e.jsx(g,{id:`itemMaxValue-${t}`,type:"number",value:s.maxValue||"",onChange:a=>u(t,"maxValue",a.target.value?parseFloat(a.target.value):null)})]})]}),s.itemType==="select"&&e.jsxs("div",{className:"md:col-span-2",children:[e.jsx(n,{htmlFor:`itemOptions-${t}`,value:"Options (one per line)"}),e.jsx(q,{id:`itemOptions-${t}`,value:s.options?s.options.join(`
`):"",onChange:a=>{const x=a.target.value.split(`
`).filter(ie=>ie.trim());u(t,"options",x.length>0?x:null)},rows:3,placeholder:`Option 1
Option 2
Option 3`})]})]})]},t))})]})]})]}),e.jsxs(h.Footer,{children:[e.jsx(d,{color:"gray",onClick:()=>p(!1),children:"Cancel"}),e.jsxs(d,{color:"primary",onClick:ee,disabled:v,children:[v?e.jsx($,{size:"sm",className:"mr-2"}):null,o?"Update Template":"Create Template"]})]})]}),e.jsxs(h,{show:X,onClose:()=>f(!1),size:"md",children:[e.jsx(h.Header,{children:"Delete QC Checklist Template"}),e.jsx(h.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(O,{className:"mx-auto mb-4 h-14 w-14 text-gray-400"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500",children:['Are you sure you want to delete the template "',o==null?void 0:o.name,'"?']}),e.jsx("p",{className:"text-sm text-gray-500 mb-5",children:"This action cannot be undone. All QC checklists using this template will be affected."})]})}),e.jsxs(h.Footer,{children:[e.jsx(d,{color:"gray",onClick:()=>f(!1),children:"Cancel"}),e.jsxs(d,{color:"failure",onClick:se,disabled:v,children:[v?e.jsx($,{size:"sm",className:"mr-2"}):null,"Delete Template"]})]})]})]})};export{Ie as default};
