import{h as he,b as ue,r as t,j as e,B as c,a0 as G,A as w,J as A,P as F,Q as xe,a7 as je,i as k,_ as a,e as K,D as M,a2 as pe,a3 as ge,a4 as fe,M as r,a6 as m,ad as W}from"./index-C6AV3cVN.js";import{C as ve}from"./Card-yj7fueH8.js";import{g as Ne,c as ye,u as De,d as be}from"./department-NDftHEGx.js";import{P as Ce}from"./Pagination-CVEzfctr.js";const Pe=()=>{const{currentOrganization:l}=he(),{user:Se}=ue(),[_,X]=t.useState([]),[Z,B]=t.useState(!0),[T,I]=t.useState(null),[q,$]=t.useState(0),[P,E]=t.useState(1),[H,ee]=t.useState(""),[z,se]=t.useState(!0),[te,x]=t.useState(!1),[ae,j]=t.useState(!1),[re,p]=t.useState(!1),[o,Q]=t.useState(null),[g,f]=t.useState(!1),[v,i]=t.useState(null),[O,L]=t.useState(!1),[R,N]=t.useState(null),[d,y]=t.useState(""),[D,b]=t.useState(""),[C,S]=t.useState(!0),[U,Y]=t.useState(""),[h,le]=t.useState(10);t.useEffect(()=>{l&&u()},[l,P,H,z,h]);const u=async()=>{if(l){B(!0),I(null);try{const{departments:s,count:n,error:V}=await Ne(l.id,{searchQuery:H,isActive:z?!0:void 0,limit:h,offset:(P-1)*h});V?I(V):(X(s),$(n))}catch(s){I(s.message||"An error occurred while fetching departments")}finally{B(!1)}}},ie=s=>{s.preventDefault(),E(1),u()},J=()=>{y(""),b(""),S(!0),Y(""),i(null),x(!0)},ne=s=>{Q(s),y(s.name),b(s.description||""),S(s.is_active!==!1),Y(s.manager_id||""),i(null),j(!0)},ce=s=>{Q(s),N(null),p(!0)},oe=async()=>{if(l){if(!d.trim()){i("Department name is required");return}f(!0),i(null);try{const{department:s,error:n}=await ye(l.id,{name:d.trim(),description:D.trim()||null,is_active:C,manager_id:U||null});n?i(n):(x(!1),u())}catch(s){i(s.message||"An error occurred while creating the department")}finally{f(!1)}}},de=async()=>{if(!(!l||!o)){if(!d.trim()){i("Department name is required");return}f(!0),i(null);try{const{department:s,error:n}=await De(l.id,o.id,{name:d.trim(),description:D.trim()||null,is_active:C,manager_id:U||null});n?i(n):(j(!1),u())}catch(s){i(s.message||"An error occurred while updating the department")}finally{f(!1)}}},me=async()=>{if(!(!l||!o)){L(!0),N(null);try{const{success:s,error:n}=await be(l.id,o.id);n?N(n):s&&(p(!1),u())}catch(s){N(s.message||"An error occurred while deleting the department")}finally{L(!1)}}};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(ve,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Departments"}),e.jsxs(c,{color:"primary",onClick:J,children:[e.jsx(G,{className:"mr-2 h-5 w-5"}),"Add Department"]})]}),T&&e.jsxs(w,{color:"failure",className:"mb-4",children:[e.jsx(A,{className:"h-4 w-4 mr-2"}),T]}),e.jsxs("div",{className:"mb-4 flex flex-col md:flex-row gap-4",children:[e.jsx("form",{onSubmit:ie,className:"flex-1",children:e.jsx(F,{type:"text",placeholder:"Search departments...",value:H,onChange:s=>ee(s.target.value),icon:xe,rightIcon:()=>e.jsx(c,{type:"submit",size:"xs",color:"light",children:"Search"})})}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("label",{className:"mr-2",children:"Show:"}),e.jsxs(je,{value:z?"active":"all",onChange:s=>se(s.target.value==="active"),className:"w-32",children:[e.jsx("option",{value:"active",children:"Active Only"}),e.jsx("option",{value:"all",children:"All"})]})]})]}),Z?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(k,{size:"xl"})}):_.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No departments found"}),e.jsxs(c,{color:"primary",size:"sm",onClick:J,children:[e.jsx(G,{className:"mr-2 h-4 w-4"}),"Add Your First Department"]})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(a,{hoverable:!0,children:[e.jsxs(a.Head,{children:[e.jsx(a.HeadCell,{children:"Name"}),e.jsx(a.HeadCell,{children:"Description"}),e.jsx(a.HeadCell,{children:"Status"}),e.jsx(a.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(a.Body,{children:_.map(s=>e.jsxs(a.Row,{children:[e.jsx(a.Cell,{className:"font-medium",children:s.name}),e.jsx(a.Cell,{children:s.description||e.jsx("span",{className:"text-gray-400",children:"No description"})}),e.jsx(a.Cell,{children:s.is_active!==!1?e.jsx(K,{color:"success",children:"Active"}):e.jsx(K,{color:"gray",children:"Inactive"})}),e.jsx(a.Cell,{children:e.jsx("div",{className:"flex justify-end",children:e.jsxs(M,{label:"",dismissOnClick:!0,renderTrigger:()=>e.jsx(c,{color:"light",size:"xs",children:e.jsx(fe,{className:"h-4 w-4"})}),children:[e.jsx(M.Item,{icon:pe,onClick:()=>ne(s),children:"Edit"}),e.jsx(M.Item,{icon:ge,onClick:()=>ce(s),children:"Delete"})]})})})]},s.id))})]})}),e.jsx(Ce,{currentPage:P,totalPages:Math.ceil(q/h),itemsPerPage:h,totalItems:q,onPageChange:E,onItemsPerPageChange:s=>{le(s),E(1)},itemName:"departments"})]}),e.jsxs(r,{show:te,onClose:()=>x(!1),size:"md",children:[e.jsx(r.Header,{children:"Add New Department"}),e.jsxs(r.Body,{children:[v&&e.jsxs(w,{color:"failure",className:"mb-4",children:[e.jsx(A,{className:"h-4 w-4 mr-2"}),v]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(m,{htmlFor:"departmentName",value:"Department Name *"})}),e.jsx(F,{id:"departmentName",value:d,onChange:s=>y(s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(m,{htmlFor:"departmentDescription",value:"Description"})}),e.jsx(W,{id:"departmentDescription",value:D,onChange:s=>b(s.target.value),rows:3})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{id:"isActive",type:"checkbox",checked:C,onChange:s=>S(s.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),e.jsx(m,{htmlFor:"isActive",value:"Active Department"})]})]})]}),e.jsxs(r.Footer,{children:[e.jsx(c,{color:"primary",onClick:oe,disabled:g,children:g?e.jsxs(e.Fragment,{children:[e.jsx(k,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Department"}),e.jsx(c,{color:"gray",onClick:()=>x(!1),children:"Cancel"})]})]}),e.jsxs(r,{show:ae,onClose:()=>j(!1),size:"md",children:[e.jsx(r.Header,{children:"Edit Department"}),e.jsxs(r.Body,{children:[v&&e.jsxs(w,{color:"failure",className:"mb-4",children:[e.jsx(A,{className:"h-4 w-4 mr-2"}),v]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(m,{htmlFor:"editDepartmentName",value:"Department Name *"})}),e.jsx(F,{id:"editDepartmentName",value:d,onChange:s=>y(s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(m,{htmlFor:"editDepartmentDescription",value:"Description"})}),e.jsx(W,{id:"editDepartmentDescription",value:D,onChange:s=>b(s.target.value),rows:3})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{id:"editIsActive",type:"checkbox",checked:C,onChange:s=>S(s.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),e.jsx(m,{htmlFor:"editIsActive",value:"Active Department"})]})]})]}),e.jsxs(r.Footer,{children:[e.jsx(c,{color:"primary",onClick:de,disabled:g,children:g?e.jsxs(e.Fragment,{children:[e.jsx(k,{size:"sm",className:"mr-2"}),"Saving..."]}):"Update Department"}),e.jsx(c,{color:"gray",onClick:()=>j(!1),children:"Cancel"})]})]}),e.jsxs(r,{show:re,onClose:()=>p(!1),size:"md",popup:!0,children:[e.jsx(r.Header,{}),e.jsx(r.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(A,{className:"mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500 dark:text-gray-400",children:["Are you sure you want to delete"," ",e.jsx("span",{className:"font-semibold",children:o==null?void 0:o.name}),"?"]}),R&&e.jsx(w,{color:"failure",className:"mb-4",children:R}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(c,{color:"failure",onClick:me,disabled:O,children:O?e.jsx(k,{size:"sm"}):"Yes, delete"}),e.jsx(c,{color:"gray",onClick:()=>p(!1),disabled:O,children:"No, cancel"})]})]})})]})]})};export{Pe as default};
