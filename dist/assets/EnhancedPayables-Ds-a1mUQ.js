import{b as _e,h as be,r as l,j as e,i as F,B as u,H as fe,a7 as h,P as m,A as Q,_ as r,e as _,ba as Ne,b9 as Ce,bb as we,M as o,ad as W}from"./index-C6AV3cVN.js";import{C as Se}from"./Card-yj7fueH8.js";import{P as Pe}from"./Pagination-CVEzfctr.js";import{i as b,P as p,A as f,k as ke,b as De,l as Ae,j as Ee,r as Re}from"./operationalExpenses-C7_pxLV1.js";import{g as Te}from"./supplier-BJDz25mb.js";import{g as Ie}from"./employee-DWC25S7P.js";const Ue=()=>{const{user:j}=_e(),{currentOrganization:x}=be(),[E,K]=l.useState([]),[Z,ee]=l.useState([]),[se,ae]=l.useState([]),[te,le]=l.useState([]),[re,O]=l.useState(!0),[w,d]=l.useState(null),[ie,R]=l.useState(!1),[ne,T]=l.useState(!1),[ce,S]=l.useState(!1),[n,I]=l.useState(null),[P,L]=l.useState(""),[g,v]=l.useState(!1),[i,y]=l.useState({}),[M,z]=l.useState(1),[k,oe]=l.useState(10),[U,de]=l.useState(0),[a,c]=l.useState({source_type:b.MANUAL_ENTRY,source_id:"",supplier_id:"",employee_id:"",reference_number:"",invoice_date:new Date().toISOString().split("T")[0],due_date:"",amount:0,vat_amount:0,withholding_tax_rate:0,withholding_tax_amount:0,currency:"PHP",category:"",invoice_url:"",notes:"",expense_type_id:"",department:"",project_code:""}),D=async()=>{if(x)try{O(!0);const[s,t,N,C]=await Promise.all([ke(x.id,i),De(x.id,{is_active:!0}),Te(x.id),Ie(x.id)]);s.success&&s.data?(K(s.data),de(s.data.length)):d(s.error||"Failed to load payables"),t.success&&t.data&&ee(t.data),N.suppliers&&ae(N.suppliers),C.employees&&le(C.employees)}catch(s){d(s.message)}finally{O(!1)}};l.useEffect(()=>{D()},[x,i]);const me=Math.ceil(U/k),B=(M-1)*k,ue=B+k,xe=E.slice(B,ue),V=async s=>{if(s.preventDefault(),!(!x||!j))try{v(!0),d(null);const t=a.source_type===b.MANUAL_ENTRY?`manual-${Date.now()}`:a.source_id,N=a.due_date||(()=>{const H=new Date(a.invoice_date);return H.setDate(H.getDate()+30),H.toISOString().split("T")[0]})(),C=a.withholding_tax_rate>0?a.amount*a.withholding_tax_rate/100:0,ye={...a,source_id:t,due_date:N,withholding_tax_amount:C,supplier_id:a.supplier_id||null,employee_id:a.employee_id||null,expense_type_id:a.expense_type_id||void 0},J=await Ee(x.id,ye,j.id);J.success?(R(!1),q(),D()):d(J.error||"Failed to create payable")}catch(t){d(t.message)}finally{v(!1)}},q=()=>{c({source_type:b.MANUAL_ENTRY,source_id:"",supplier_id:"",employee_id:"",reference_number:"",invoice_date:new Date().toISOString().split("T")[0],due_date:"",amount:0,vat_amount:0,withholding_tax_rate:0,withholding_tax_amount:0,currency:"PHP",category:"",invoice_url:"",notes:"",expense_type_id:"",department:"",project_code:""})},G=()=>{R(!1),q(),d(null)},A=s=>s.replace(/_/g," ").replace(/\b\w/g,t=>t.toUpperCase()),he=s=>new Date(s).toLocaleDateString(),Y=s=>{switch(s){case p.PAID:return"green";case p.PARTIALLY_PAID:return"yellow";case p.OPEN:return"blue";case p.DRAFT:return"gray";case p.CANCELLED:return"red";default:return"gray"}},X=s=>{switch(s){case f.APPROVED:return"green";case f.PENDING:return"yellow";case f.REJECTED:return"red";case f.REQUIRES_HIGHER_APPROVAL:return"purple";default:return"gray"}},$=(s,t)=>t===p.PAID?!1:new Date(s)<new Date,pe=async s=>{I(s),T(!0)},je=async s=>{if(j)try{v(!0);const t=await Ae(s.id,j.id);t.success?D():d(t.error||"Failed to approve payable")}catch(t){d(t.message)}finally{v(!1)}},ge=s=>{I(s),S(!0)},ve=async()=>{if(!(!n||!j||!P.trim()))try{v(!0);const s=await Re(n.id,j.id,P);s.success?(S(!1),L(""),I(null),D()):d(s.error||"Failed to reject payable")}catch(s){d(s.message)}finally{v(!1)}};return re?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(F,{size:"xl"})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(Se,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Enhanced Payables"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Manage operational expenses and payables"})]}),e.jsxs(u,{onClick:()=>R(!0),className:"bg-primary",children:[e.jsx(fe,{className:"mr-2 h-4 w-4"}),"Create Payable"]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4 mb-6",children:[e.jsxs(h,{value:i.status||"",onChange:s=>y({...i,status:s.target.value||void 0}),children:[e.jsx("option",{value:"",children:"All Status"}),Object.values(p).map(s=>e.jsx("option",{value:s,children:s.replace(/_/g," ").replace(/\b\w/g,t=>t.toUpperCase())},s))]}),e.jsxs(h,{value:i.approval_status||"",onChange:s=>y({...i,approval_status:s.target.value||void 0}),children:[e.jsx("option",{value:"",children:"All Approval Status"}),Object.values(f).map(s=>e.jsx("option",{value:s,children:s.replace(/_/g," ").replace(/\b\w/g,t=>t.toUpperCase())},s))]}),e.jsxs(h,{value:i.source_type||"",onChange:s=>y({...i,source_type:s.target.value||void 0}),children:[e.jsx("option",{value:"",children:"All Source Types"}),Object.values(b).filter(s=>s!==b.RECURRING_EXPENSE).map(s=>e.jsx("option",{value:s,children:A(s)},s))]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"From Date"}),e.jsx(m,{type:"date",value:i.date_from||"",onChange:s=>y({...i,date_from:s.target.value||void 0})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"To Date"}),e.jsx(m,{type:"date",value:i.date_to||"",onChange:s=>y({...i,date_to:s.target.value||void 0})})]}),e.jsx(m,{placeholder:"Search payables...",value:i.search||"",onChange:s=>y({...i,search:s.target.value||void 0})})]}),w&&e.jsx(Q,{color:"failure",className:"mb-4",children:w}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(r,{children:[e.jsxs(r.Head,{children:[e.jsx(r.HeadCell,{children:"Reference"}),e.jsx(r.HeadCell,{children:"Payee"}),e.jsx(r.HeadCell,{children:"Amount"}),e.jsx(r.HeadCell,{children:"Due Date"}),e.jsx(r.HeadCell,{children:"Status"}),e.jsx(r.HeadCell,{children:"Approval"}),e.jsx(r.HeadCell,{children:"Actions"})]}),e.jsx(r.Body,{className:"divide-y",children:xe.map(s=>e.jsxs(r.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(r.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold",children:s.reference_number}),e.jsx("div",{className:"text-sm text-gray-500",children:A(s.source_type)}),s.expense_type&&e.jsx(_,{color:"gray",size:"sm",className:"mt-1",children:s.expense_type.name})]})}),e.jsx(r.Cell,{children:s.supplier?e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:s.supplier.name}),e.jsx("div",{className:"text-sm text-gray-500",children:"Supplier"})]}):s.employee?e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium",children:[s.employee.first_name," ",s.employee.last_name]}),e.jsx("div",{className:"text-sm text-gray-500",children:"Employee"})]}):e.jsx("span",{className:"text-gray-500",children:"Not specified"})}),e.jsxs(r.Cell,{children:[e.jsxs("div",{className:"font-semibold",children:["₱",s.amount.toLocaleString()]}),e.jsxs("div",{className:"text-sm text-gray-500",children:["Balance: ₱",s.balance.toLocaleString()]})]}),e.jsxs(r.Cell,{children:[e.jsx("div",{className:`font-medium ${$(s.due_date,s.status)?"text-red-600":"text-gray-900 dark:text-white"}`,children:he(s.due_date)}),$(s.due_date,s.status)&&e.jsx(_,{color:"failure",size:"sm",children:"Overdue"})]}),e.jsx(r.Cell,{children:e.jsx(_,{color:Y(s.status),children:s.status.replace(/_/g," ").replace(/\b\w/g,t=>t.toUpperCase())})}),e.jsx(r.Cell,{children:e.jsx(_,{color:X(s.approval_status),children:s.approval_status.replace(/_/g," ").replace(/\b\w/g,t=>t.toUpperCase())})}),e.jsx(r.Cell,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(u,{size:"xs",color:"gray",title:"View Details",onClick:()=>pe(s),children:e.jsx(Ne,{className:"h-3 w-3"})}),s.approval_status===f.PENDING&&e.jsxs(e.Fragment,{children:[e.jsx(u,{size:"xs",color:"success",title:"Approve",onClick:()=>je(s),disabled:g,children:e.jsx(Ce,{className:"h-3 w-3"})}),e.jsx(u,{size:"xs",color:"failure",title:"Reject",onClick:()=>ge(s),disabled:g,children:e.jsx(we,{className:"h-3 w-3"})})]})]})})]},s.id))})]})}),E.length===0&&e.jsx("div",{className:"text-center py-8 text-gray-500",children:"No payables found. Create your first payable to get started."}),E.length>0&&e.jsx(Pe,{currentPage:M,totalPages:me,itemsPerPage:k,totalItems:U,onPageChange:z,onItemsPerPageChange:s=>{oe(s),z(1)},itemName:"payables"})]}),e.jsxs(o,{show:ie,onClose:G,size:"xl",children:[e.jsx(o.Header,{children:"Create New Payable"}),e.jsx(o.Body,{children:e.jsxs("form",{onSubmit:V,className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Source Type *"}),e.jsx(h,{value:a.source_type,onChange:s=>c({...a,source_type:s.target.value}),required:!0,children:Object.values(b).map(s=>e.jsx("option",{value:s,children:A(s)},s))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Reference Number *"}),e.jsx(m,{value:a.reference_number,onChange:s=>c({...a,reference_number:s.target.value}),required:!0,placeholder:"e.g., INV-2024-001"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Expense Type"}),e.jsxs(h,{value:a.expense_type_id,onChange:s=>c({...a,expense_type_id:s.target.value}),children:[e.jsx("option",{value:"",children:"Select expense type"}),Z.map(s=>e.jsxs("option",{value:s.id,children:[s.name," (",s.code,")"]},s.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Department"}),e.jsx(m,{value:a.department,onChange:s=>c({...a,department:s.target.value}),placeholder:"e.g., IT, HR, Finance"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Supplier"}),e.jsxs(h,{value:a.supplier_id,onChange:s=>c({...a,supplier_id:s.target.value,employee_id:""}),children:[e.jsx("option",{value:"",children:"Select supplier"}),se.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Employee"}),e.jsxs(h,{value:a.employee_id,onChange:s=>c({...a,employee_id:s.target.value,supplier_id:""}),children:[e.jsx("option",{value:"",children:"Select employee"}),te.map(s=>e.jsxs("option",{value:s.id,children:[s.first_name," ",s.last_name]},s.id))]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Amount (₱) *"}),e.jsx(m,{type:"number",value:a.amount,onChange:s=>c({...a,amount:parseFloat(s.target.value)||0}),required:!0,min:"0",step:"0.01",placeholder:"0.00"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"VAT Amount (₱)"}),e.jsx(m,{type:"number",value:a.vat_amount,onChange:s=>c({...a,vat_amount:parseFloat(s.target.value)||0}),min:"0",step:"0.01",placeholder:"0.00"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Withholding Tax (%)"}),e.jsx(m,{type:"number",value:a.withholding_tax_rate,onChange:s=>c({...a,withholding_tax_rate:parseFloat(s.target.value)||0}),min:"0",max:"100",step:"0.01",placeholder:"0.00"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Invoice Date *"}),e.jsx(m,{type:"date",value:a.invoice_date,onChange:s=>c({...a,invoice_date:s.target.value}),required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Due Date"}),e.jsx(m,{type:"date",value:a.due_date,onChange:s=>c({...a,due_date:s.target.value})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Notes"}),e.jsx(W,{value:a.notes,onChange:s=>c({...a,notes:s.target.value}),placeholder:"Optional notes",rows:3})]}),w&&e.jsx(Q,{color:"failure",children:w})]})}),e.jsxs(o.Footer,{children:[e.jsxs(u,{onClick:V,disabled:g,className:"bg-primary",children:[g?e.jsx(F,{size:"sm",className:"mr-2"}):null,"Create Payable"]}),e.jsx(u,{color:"gray",onClick:G,children:"Cancel"})]})]}),e.jsxs(o,{show:ne,onClose:()=>T(!1),size:"xl",children:[e.jsx(o.Header,{children:"Payable Details"}),e.jsx(o.Body,{children:n&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Reference Number"}),e.jsx("p",{className:"text-sm text-gray-900",children:n.reference_number})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Source Type"}),e.jsx("p",{className:"text-sm text-gray-900",children:A(n.source_type)})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Amount"}),e.jsxs("p",{className:"text-sm text-gray-900",children:["₱",n.amount.toLocaleString()]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Balance"}),e.jsxs("p",{className:"text-sm text-gray-900",children:["₱",n.balance.toLocaleString()]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),e.jsx(_,{color:Y(n.status),children:n.status.replace(/_/g," ").replace(/\b\w/g,s=>s.toUpperCase())})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Approval Status"}),e.jsx(_,{color:X(n.approval_status),children:n.approval_status.replace(/_/g," ").replace(/\b\w/g,s=>s.toUpperCase())})]})]}),n.notes&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Notes"}),e.jsx("p",{className:"text-sm text-gray-900",children:n.notes})]})]})}),e.jsx(o.Footer,{children:e.jsx(u,{color:"gray",onClick:()=>T(!1),children:"Close"})})]}),e.jsxs(o,{show:ce,onClose:()=>S(!1),children:[e.jsx(o.Header,{children:"Reject Payable"}),e.jsx(o.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm text-gray-700",children:"Are you sure you want to reject this payable? Please provide a reason for rejection."}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Rejection Reason *"}),e.jsx(W,{value:P,onChange:s=>L(s.target.value),placeholder:"Please provide a reason for rejection...",rows:3,required:!0})]})]})}),e.jsxs(o.Footer,{children:[e.jsxs(u,{color:"failure",onClick:ve,disabled:g||!P.trim(),children:[g?e.jsx(F,{size:"sm",className:"mr-2"}):null,"Reject Payable"]}),e.jsx(u,{color:"gray",onClick:()=>S(!1),children:"Cancel"})]})]})]})};export{Ue as default};
