import{aG as M,r,s as l,j as e,A as _,a6 as g,P as D,B as C,i as h,ad as O}from"./index-C6AV3cVN.js";import{C as f}from"./Card-yj7fueH8.js";const $=()=>{const[y]=M(),n=y.get("token"),o=y.get("id"),[d,j]=r.useState(!0),[v,b]=r.useState(null),[L,p]=r.useState({}),[i,k]=r.useState(n||""),[c,S]=r.useState(o||""),[m,T]=r.useState(""),[N,q]=r.useState(null),[I,u]=r.useState(null),[w,E]=r.useState(!1);r.useEffect(()=>{R()},[n,o]);const R=async()=>{j(!0),b(null),p({});try{const s={params:{token:n,invitationId:o},timestamp:new Date().toISOString()};try{const{data:t,error:a}=await l.from("_test_connection").select("*").limit(1);s.connection={success:!a,error:a?a.message:null}}catch(t){s.connection={success:!1,error:t.message}}try{const{data:t,error:a}=await l.rpc("check_table_exists",{table_name:"invitations"});s.table={exists:t,error:a?a.message:null}}catch(t){s.table={exists:!1,error:t.message}}try{const{data:t,error:a}=await l.from("invitations").select("id, token, email, expires_at, accepted_at, created_at").order("created_at",{ascending:!1}).limit(10);s.allInvitations={success:!a,count:t?t.length:0,data:t,error:a?a.message:null}}catch(t){s.allInvitations={success:!1,count:0,error:t.message}}if(o)try{const{data:t,error:a}=await l.from("invitations").select("*").eq("id",o).single();s.byId={success:!a&&!!t,data:t,error:a?a.message:null}}catch(t){s.byId={success:!1,error:t.message}}if(n)try{const{data:t,error:a}=await l.from("invitations").select("*").eq("token",n).single();s.byToken={success:!a&&!!t,data:t,error:a?a.message:null}}catch(t){s.byToken={success:!1,error:t.message}}if(n){const t=decodeURIComponent(n);if(t!==n)try{const{data:a,error:x}=await l.from("invitations").select("*").eq("token",t).single();s.byDecodedToken={success:!x&&!!a,data:a,error:x?x.message:null}}catch(a){s.byDecodedToken={success:!1,error:a.message}}}p(s)}catch(s){b(`Error running diagnostics: ${s.message}`)}finally{j(!1)}},Q=()=>{k(i.trim()),S(c.trim());const s=new URLSearchParams;i&&s.set("token",i),c&&s.set("id",c),window.history.replaceState({},"",`${window.location.pathname}?${s}`),R()},F=async()=>{if(m.trim()){E(!0),u(null),q(null);try{const{data:s,error:t}=await l.rpc("run_diagnostic_query",{query_text:m});t?u(t.message):q(s)}catch(s){u(s.message)}finally{E(!1)}}};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Invitation System Diagnostic"}),v&&e.jsx(_,{color:"failure",className:"mb-4",children:v}),e.jsxs("div",{className:"grid grid-cols-1 gap-6",children:[e.jsxs(f,{children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Check Invitation"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(g,{htmlFor:"token",value:"Invitation Token"})}),e.jsx(D,{id:"token",type:"text",placeholder:"Enter invitation token",value:i,onChange:s=>k(s.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(g,{htmlFor:"id",value:"Invitation ID"})}),e.jsx(D,{id:"id",type:"text",placeholder:"Enter invitation ID",value:c,onChange:s=>S(s.target.value)})]})]}),e.jsxs(C,{onClick:Q,disabled:d,children:[d?e.jsx(h,{size:"sm",className:"mr-2"}):null,"Run Diagnostics"]})]}),e.jsxs(f,{children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"SQL Diagnostic"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(g,{htmlFor:"sql",value:"SQL Query"})}),e.jsx(O,{id:"sql",placeholder:"Enter SQL query",value:m,onChange:s=>T(s.target.value),rows:3}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Example: SELECT * FROM invitations WHERE token = 'your_token'"})]}),e.jsxs(C,{onClick:F,disabled:w,children:[w?e.jsx(h,{size:"sm",className:"mr-2"}):null,"Run SQL"]}),I&&e.jsx(_,{color:"failure",className:"mt-4",children:I}),N&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h3",{className:"font-bold mb-2",children:"SQL Result:"}),e.jsx("pre",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-auto max-h-60",children:JSON.stringify(N,null,2)})]})]}),e.jsxs(f,{children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Diagnostic Results"}),d?e.jsx("div",{className:"flex justify-center py-4",children:e.jsx(h,{size:"lg"})}):e.jsx("pre",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-auto max-h-[500px]",children:JSON.stringify(L,null,2)})]})]})]})};export{$ as default};
