import{s as j,h as F,r as x,j as e,i as $,a7 as S,a9 as D,A,J as O,e as L,a6 as U,al as T,Y as H,P as w,U as Q,ad as P,B,M}from"./index-C6AV3cVN.js";import{u as R}from"./currencyFormatter-BsFWv3sX.js";const V=async(s,d,r)=>{try{let i=j.from("supplier_products").select(`
        *,
        product:product_id (
          id,
          name,
          sku,
          barcode,
          unit_price,
          image_url,
          category:category_id (
            id,
            name
          )
        ),
        uom:uom_id (
          id,
          code,
          name
        )
      `,{count:"exact"}).eq("supplier_id",d);if(r!=null&&r.searchQuery&&(i=i.or(`product.name.ilike.%${r.searchQuery}%,product.sku.ilike.%${r.searchQuery}%,product.barcode.ilike.%${r.searchQuery}%`)),r!=null&&r.sortBy)if(r.sortBy.startsWith("product.")){const h=r.sortBy.replace("product.","");i=i.order(`product(${h})`,{ascending:r.sortOrder==="asc"})}else i=i.order(r.sortBy,{ascending:r.sortOrder==="asc"});else i=i.order("product(name)",{ascending:!0});r!=null&&r.limit&&(i=i.limit(r.limit)),r!=null&&r.offset&&(i=i.range(r.offset,r.offset+(r.limit||10)-1));const{data:c,error:u,count:_}=await i;return u?(console.error("Error fetching supplier products:",u),{supplierProducts:[],count:0,error:u.message}):{supplierProducts:c,count:_||0}}catch(i){return console.error("Error in getSupplierProducts:",i),{supplierProducts:[],count:0,error:i.message}}},X=async(s,d)=>{try{console.log("Fetching supplier product:",{supplierId:s,productId:d});const{data:r,error:i}=await j.from("supplier_products").select(`
        *,
        product:product_id (
          id,
          name,
          sku,
          unit_price
        ),
        uom:uom_id (
          id,
          code,
          name
        )
      `).eq("supplier_id",s).eq("product_id",d).single();if(console.log("Supplier product query result:",{data:r,error:i}),i){if(i.code==="PGRST116"){console.log("No supplier product found for this product and supplier");const{data:c,error:u}=await j.from("products").select("id, name, sku, unit_price").eq("id",d).single();return console.log("Product fallback check:",{product:c,productError:u}),c&&!u?(console.log("Using default product price as fallback"),{supplierProduct:{id:`fallback-${d}`,supplier_id:s,product_id:d,unit_price:c.unit_price||0,product:c}}):{}}return console.error("Error fetching supplier product:",i),{error:i.message}}return r&&r.uom&&r.unit_price!==null&&r.unit_price!==void 0&&(r.uom.supplier_price=r.unit_price),{supplierProduct:r}}catch(r){return console.error("Error in getSupplierProductByProductId:",r),{error:r.message}}},Z=async(s,d,r,i,c,u,_)=>{try{console.log("Adding/updating supplier product with explicit data:",{supplierId:s,productId:d,unitPrice:r,organizationId:i,uomId:c,uomName:u,conversionFactor:_});const{data:h,error:t}=await j.from("supplier_products").select("id").eq("supplier_id",s).eq("product_id",d).eq("organization_id",i);console.log("Check for existing supplier product:",{existingProducts:h,checkError:t});let y;if(h&&h.length>0){console.log("Updating existing supplier product:",h[0].id);const m={unit_price:r,organization_id:i};m.uom_id=c||null,m.uom_name=u||null,m.conversion_factor=_||1,m.base_price=r*(_||1),console.log("Update data prepared:",m),console.log("Updating supplier product with data:",m);const{data:n,error:g}=await j.from("supplier_products").update(m).eq("id",h[0].id).select(`
          *,
          uom:uom_id (
            id,
            code,
            name
          )
        `).single();if(g)return console.error("Error updating supplier product:",g),{success:!1,error:g.message};y=n}else{console.log("Creating new supplier product");const m={supplier_id:s,product_id:d,unit_price:r,organization_id:i};m.uom_id=c||null,m.uom_name=u||null,m.conversion_factor=_||1,m.base_price=r*(_||1),console.log("Insert data prepared:",m),console.log("Creating new supplier product with data:",m);const{data:n,error:g}=await j.from("supplier_products").insert(m).select(`
          *,
          uom:uom_id (
            id,
            code,
            name
          )
        `).single();if(g)return console.error("Error creating supplier product:",g),{success:!1,error:g.message};y=n}return console.log("Supplier product added/updated successfully:",y),{success:!0,supplierProduct:y}}catch(h){return console.error("Error in addOrUpdateSupplierProduct:",h),{success:!1,error:h.message}}},I=async(s,d)=>{try{if(console.log("Fetching supplier products for multiple products:",{supplierId:s,productIds:d}),!d.length)return{supplierProducts:[]};const{data:r,error:i}=await j.from("supplier_products").select("*").limit(1);console.log("Supplier products table check:",{tableInfo:r,tableError:i});const{data:c,error:u}=await j.from("supplier_products").select(`
        *,
        product:product_id (
          id,
          name,
          sku,
          unit_price
        ),
        uom:uom_id (
          id,
          code,
          name
        )
      `).eq("supplier_id",s).in("product_id",d);if(console.log("Supplier products query result:",{data:c,error:u}),u)return console.error("Error fetching supplier products:",u),{supplierProducts:[],error:u.message};if(!c||c.length===0){console.log("No supplier products found for these products");const{data:_,error:h}=await j.from("suppliers").select("id, name").eq("id",s).single();console.log("Supplier check:",{supplierCheck:_,supplierError:h});const{data:t,error:y}=await j.from("products").select("id, name, unit_price").in("id",d);if(console.log("Products check:",{productsCount:(t==null?void 0:t.length)||0,productsError:y}),t&&t.length>0)return console.log("Using default product prices as fallback"),{supplierProducts:t.map(m=>({id:`fallback-${m.id}`,supplier_id:s,product_id:m.id,unit_price:m.unit_price||0,product:m}))}}return c&&c.length>0&&c.forEach(_=>{_.uom&&_.unit_price!==null&&_.unit_price!==void 0&&(_.uom.supplier_price=_.unit_price)}),{supplierProducts:c}}catch(r){return console.error("Error in getSupplierProductsByProductIds:",r),{supplierProducts:[],error:r.message}}},G=({value:s,onChange:d,disabled:r=!1,className:i=""})=>{const{currentOrganization:c}=F(),[u,_]=x.useState([]),[h,t]=x.useState(!1),[y,m]=x.useState(null),[n,g]=x.useState(!1),[E,p]=x.useState(!1);x.useEffect(()=>{if(E||!c)return;(async()=>{t(!0),m(null),console.log("Fetching all UoMs for organization:",c.id);try{const{uoms:o,error:f}=await D(c.id);if(console.log("Fetched organization UoMs:",o),f)console.error("Error fetching UoMs:",f),m(f);else if(!o||o.length===0)console.warn("No UoMs found for organization"),m("No units of measurement found for this organization");else if(_(o),p(!0),!s&&!r&&o.length>0&&!n){console.log("Auto-selecting UoM because no value is provided"),g(!0);const v=o.find(b=>b.code==="pcs");if(v)d(v.id,`${v.name} (${v.code})`);else{const b=o[0];d(b.id,`${b.name} (${b.code})`)}}}catch(o){console.error("Exception in fetchUoms:",o),m(o.message||"Failed to load units of measurement")}finally{t(!1)}})()},[c,s,r,n,E,d]),x.useEffect(()=>{if(s&&u.length>0){const l=u.find(o=>o.id===s);l?console.log("UoM value from parent is valid:",{id:s,name:`${l.name} (${l.code})`}):console.warn("Selected UoM not found in available UoMs:",s)}},[s,u]);const a=l=>{const o=l.target.value,f=u.find(v=>v.id===o);console.log("UoM manually changed to:",{id:o,name:f?`${f.name} (${f.code})`:void 0}),d(o,f?`${f.name} (${f.code})`:void 0)};return x.useEffect(()=>{if(s&&u.length>0){const l=u.find(o=>o.id===s);l?console.log("UoM value updated from parent:",{id:s,name:l?`${l.name} (${l.code})`:void 0}):console.warn("Selected UoM not found in available UoMs:",s)}},[s,u]),h?e.jsxs("div",{className:"flex items-center",children:[e.jsx($,{size:"sm",className:"mr-2"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Loading..."})]}):y?e.jsx("div",{className:"text-sm text-red-500",children:y}):u.length===0?e.jsxs("div",{children:[e.jsx(S,{value:"",disabled:!0,className:i,children:e.jsx("option",{value:"",children:"No units available"})}),e.jsx("div",{className:"mt-1 text-xs text-red-500",children:"This organization has no units of measurement defined. Please add UoMs in the settings first."})]}):e.jsxs(S,{value:s,onChange:a,disabled:r||h,className:i,children:[e.jsx("option",{value:"",children:"Select a unit"}),u.map(l=>e.jsxs("option",{value:l.id,children:[l.name," (",l.code,")"]},l.id))]})},J=({product:s,supplierId:d,onSubmit:r,onCancel:i,isSubmitting:c=!1,error:u=null,currentUnitPrice:_,currentUomId:h,existingSupplierProduct:t})=>{const{currentOrganization:y}=F(),m=R(),[n,g]=x.useState({supplier_id:d,product_id:s.id,unit_price:(t==null?void 0:t.unit_price)!==void 0?t.unit_price:_!==void 0?_:s.unit_price||0,minimum_order_quantity:(t==null?void 0:t.minimum_order_quantity)||null,lead_time_days:(t==null?void 0:t.lead_time_days)||null,is_preferred:(t==null?void 0:t.is_preferred)||!1,notes:(t==null?void 0:t.notes)||"",uom_id:(t==null?void 0:t.uom_id)||h||null,uom_name:(t==null?void 0:t.uom_name)||null,conversion_factor:(t==null?void 0:t.conversion_factor)||1,available_conversion_factors:{}});console.log("Initializing form with current values:",{currentUnitPrice:_,currentUomId:h,formData:n}),x.useEffect(()=>{(async()=>{if(!(!y||!s.id))try{console.log("Checking if product already exists for supplier:",{supplierId:d,productId:s.id});const{data:a,error:l}=await j.from("supplier_products").select(`
            *,
            uom:uom_id (
              id,
              code,
              name
            )
          `).eq("supplier_id",d).eq("product_id",s.id).maybeSingle();console.log("Existing product check result:",{data:a,error:l}),a&&(console.log("Found existing supplier product, initializing form with:",a),g(o=>({...o,unit_price:a.unit_price||s.unit_price||0,minimum_order_quantity:a.minimum_order_quantity,lead_time_days:a.lead_time_days,is_preferred:a.is_preferred||!1,notes:a.notes||"",uom_id:a.uom_id,uom_name:a.uom_name,conversion_factor:a.conversion_factor||1})))}catch(a){console.error("Error checking for existing supplier product:",a)}})()},[d,s.id,y]),x.useEffect(()=>{(async()=>{if(!(!y||!s.id))try{console.log("Fetching UoMs for product:",s.id);const{data:a,error:l}=await j.from("product_uoms").select(`
            uom_id,
            conversion_factor,
            is_default,
            uom:uom_id (
              id,
              code,
              name
            )
          `).eq("product_id",s.id);if(l)console.error("Error fetching product UoMs:",l);else if(!a||a.length===0)console.warn("No UoMs found for product:",s.id);else{console.log("Found UoMs for product:",a);const o=a.find(v=>v.is_default),f={};a.forEach(v=>{f[v.uom_id]=v.conversion_factor||1}),g(v=>{const b={...v,available_conversion_factors:f};return v.uom_id?b.conversion_factor=f[v.uom_id]||1:o&&(b.uom_id=o.uom_id,b.conversion_factor=o.conversion_factor||1,o.uom&&(b.uom_name=`${o.uom.name} (${o.uom.code})`)),b})}}catch(a){console.error("Error fetching product UoMs:",a)}})()},[s.id,y]);const E=async p=>{p&&(p.preventDefault(),p.stopPropagation());const a=n.unit_price===null||n.unit_price===void 0?s.unit_price||0:n.unit_price,l=a*n.conversion_factor,o={supplier_id:d,product_id:s.id,unit_price:a,minimum_order_quantity:n.minimum_order_quantity,lead_time_days:n.lead_time_days,is_preferred:n.is_preferred,notes:n.notes||null,uom_id:n.uom_id,uom_name:n.uom_name,conversion_factor:n.conversion_factor,organization_id:y==null?void 0:y.id,base_price:l};await r(o)};return e.jsxs("div",{className:"space-y-4",children:[u&&e.jsx(A,{color:"failure",icon:O,children:u}),e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4",children:e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.image_url?e.jsx("img",{src:s.image_url,alt:s.name,className:"h-12 w-12 rounded-md object-cover"}):e.jsx("div",{className:"h-12 w-12 rounded-md bg-gray-200 flex items-center justify-center",children:e.jsx("span",{className:"text-gray-500 text-xs",children:"No image"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"font-medium text-lg",children:s.name}),t&&e.jsx(L,{color:"success",children:"Already in supplier catalog"})]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[e.jsxs("span",{className:"mr-3",children:["SKU: ",s.sku||"N/A"]}),e.jsxs("span",{children:["Current Price: ",m(s.unit_price)]}),t&&e.jsxs("span",{className:"ml-3",children:["Supplier Price: ",m(t.unit_price||0)]})]})]})]})}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(U,{htmlFor:"uom_id",value:"Unit of Measurement"})}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(G,{value:n.uom_id||"",onChange:(p,a)=>{g(l=>{var f;const o=((f=l.available_conversion_factors)==null?void 0:f[p])||1;return{...l,uom_id:p,uom_name:a,conversion_factor:o}})},className:"w-full"}),e.jsx(T,{content:"Select the unit of measurement for this supplier product",children:e.jsx(H,{className:"ml-2 h-5 w-5 text-gray-400"})})]}),e.jsxs("div",{className:"flex justify-between mt-1",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Choose the unit of measurement that this supplier uses for this product"}),n.conversion_factor&&n.conversion_factor!==1&&e.jsxs("p",{className:"text-sm font-medium",children:["Conversion Factor: ",n.conversion_factor]})]})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 block",children:[e.jsx(U,{htmlFor:"unit_price",value:"Unit Price from Supplier"}),e.jsx("span",{className:"text-xs text-gray-500 ml-2",children:"(Use 0 for products not supplied by this supplier)"})]}),e.jsx(w,{id:"unit_price",name:"unit_price",type:"number",min:"0",step:"0.01",value:n.unit_price===null?"":n.unit_price,onChange:p=>{const a=p.target.value===""?null:parseFloat(p.target.value);g(l=>({...l,unit_price:a}))},placeholder:"Enter unit price (0 for products not supplied)"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(U,{htmlFor:"minimum_order_quantity",value:"Minimum Order Quantity"})}),e.jsx(w,{id:"minimum_order_quantity",name:"minimum_order_quantity",type:"number",step:"1",min:"0",value:n.minimum_order_quantity===null?"":n.minimum_order_quantity,onChange:p=>{const a=p.target.value===""?null:parseInt(p.target.value);g(l=>({...l,minimum_order_quantity:a}))},placeholder:"Enter minimum order quantity"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(U,{htmlFor:"lead_time_days",value:"Lead Time (Days)"})}),e.jsx(w,{id:"lead_time_days",name:"lead_time_days",type:"number",step:"1",min:"0",value:n.lead_time_days===null?"":n.lead_time_days,onChange:p=>{const a=p.target.value===""?null:parseInt(p.target.value);g(l=>({...l,lead_time_days:a}))},placeholder:"Enter lead time in days"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Q,{id:"is_preferred",checked:n.is_preferred,onChange:p=>{g(a=>({...a,is_preferred:p.target.checked}))}}),e.jsx(U,{htmlFor:"is_preferred",children:"Preferred supplier for this product"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(U,{htmlFor:"notes",value:"Notes"})}),e.jsx(P,{id:"notes",name:"notes",value:n.notes||"",onChange:p=>{g(a=>({...a,notes:p.target.value}))},placeholder:"Enter any notes about this supplier product",rows:3})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx(B,{color:"gray",onClick:i,type:"button",children:"Cancel"}),e.jsx(B,{color:"primary",type:"button",disabled:c,onClick:p=>{p.preventDefault(),p.stopPropagation(),E(p)},children:c?e.jsxs(e.Fragment,{children:[e.jsx($,{size:"sm",className:"mr-2"}),"Saving..."]}):t?"Update Product":"Save Product"})]})]})},ee=({show:s,onClose:d,onSuccess:r,supplierId:i,product:c,currentUnitPrice:u,currentUomId:_})=>{const{currentOrganization:h}=F(),[t,y]=x.useState(!1),[m,n]=x.useState(null),[g,E]=x.useState(null),[p,a]=x.useState(!1);x.useEffect(()=>{s&&i&&c.id&&(async()=>{a(!0);try{const{data:f,error:v}=await j.from("supplier_products").select(`
              *,
              uom:uom_id (
                id,
                code,
                name
              )
            `).eq("supplier_id",i).eq("product_id",c.id).maybeSingle();v?console.error("Error fetching supplier product in modal:",v):E(f||null)}catch(f){console.error("Exception fetching supplier product in modal:",f)}finally{a(!1)}})()},[s,i,c.id]);const l=async o=>{if(!h){n("No organization selected");return}y(!0),n(null);try{const f=o.unit_price===null||o.unit_price===void 0?c.unit_price||0:o.unit_price,v=f*(o.conversion_factor||1),b={supplier_id:i,product_id:c.id,unit_price:f,minimum_order_quantity:o.minimum_order_quantity,lead_time_days:o.lead_time_days,is_preferred:o.is_preferred,notes:o.notes,uom_id:o.uom_id,uom_name:o.uom_name,conversion_factor:o.conversion_factor||1,organization_id:h.id,base_price:v},{data:C,error:K}=await j.from("supplier_products").select("id").eq("supplier_id",i).eq("product_id",c.id).maybeSingle();let z,N;if(C){const{data:k,error:q}=await j.from("supplier_products").update(b).eq("id",C.id).select("*").single();z=k,N=q}else{const{data:k,error:q}=await j.from("supplier_products").insert(b).select("*").single();z=k,N=q}N?(console.error("Error saving supplier product:",N),n(N.message)):(r(),d())}catch(f){console.error("Exception saving supplier product:",f),n(f.message||"An error occurred while saving the product")}finally{y(!1)}};return e.jsxs(M,{show:s,onClose:d,size:"lg",children:[e.jsx(M.Header,{children:g?"Edit Supplier Product":"Add Product to Supplier"}),e.jsx(M.Body,{children:p?e.jsx("div",{className:"flex justify-center py-4",children:e.jsx($,{size:"xl"})}):e.jsx(J,{product:c,supplierId:i,onSubmit:l,onCancel:d,isSubmitting:t,error:m,currentUnitPrice:u,currentUomId:_,existingSupplierProduct:g})})]})};export{ee as S,Z as a,X as b,V as c,J as d,I as g};
