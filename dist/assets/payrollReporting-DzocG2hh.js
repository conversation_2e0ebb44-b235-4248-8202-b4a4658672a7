import{s as c}from"./index-C6AV3cVN.js";import{P as g}from"./payroll-j3fcCwK0.js";const f=async y=>{try{const{organizationId:r,startDate:l,endDate:p}=y;let i=c.from("payroll_periods").select(`
        id,
        name,
        start_date,
        end_date,
        payment_date,
        status,
        is_thirteenth_month,
        payroll_items:payroll_items(
          id,
          employee_id,
          basic_pay,
          gross_pay,
          net_pay,
          total_allowances,
          total_deductions
        )
      `).eq("organization_id",r).not("status","eq",g.DRAFT);l&&(i=i.gte("start_date",l.toISOString().split("T")[0])),p&&(i=i.lte("end_date",p.toISOString().split("T")[0]));const{data:n,error:s}=await i;if(s)throw new Error(s.message);return{data:n.map(o=>{const e=o.payroll_items||[];return{periodId:o.id,periodName:o.name,startDate:new Date(o.start_date),endDate:new Date(o.end_date),paymentDate:new Date(o.payment_date),status:o.status,isThirteenthMonth:o.is_thirteenth_month,totalEmployees:e.length,totalGrossPay:e.reduce((t,d)=>t+Number(d.gross_pay),0),totalNetPay:e.reduce((t,d)=>t+Number(d.net_pay),0),totalDeductions:e.reduce((t,d)=>t+Number(d.total_deductions),0),totalAllowances:e.reduce((t,d)=>t+Number(d.total_allowances),0)}})}}catch(r){return console.error("Error getting payroll summary report:",r),{error:r.message}}},h=async y=>{try{const{organizationId:r,startDate:l,endDate:p,employeeIds:i}=y;let n=c.from("payroll_items").select(`
        id,
        employee_id,
        basic_pay,
        gross_pay,
        net_pay,
        total_allowances,
        total_deductions,
        regular_pay,
        overtime_pay,
        rest_day_pay,
        holiday_pay,
        night_differential_pay,
        is_thirteenth_month,
        employee:employee_id(
          id,
          first_name,
          middle_name,
          last_name,
          employee_number
        ),
        payroll_period:payroll_period_id(
          id,
          name,
          start_date,
          end_date,
          payment_date
        )
      `).eq("organization_id",r);if(i&&i.length>0&&(n=n.in("employee_id",i)),l||p){const o=c.from("payroll_periods").select("id").eq("organization_id",r);l&&o.gte("start_date",l.toISOString().split("T")[0]),p&&o.lte("end_date",p.toISOString().split("T")[0]);const{data:e,error:t}=await o;if(t)throw new Error(t.message);if(e.length>0){const d=e.map(a=>a.id);n=n.in("payroll_period_id",d)}else return{data:[]}}const{data:s,error:m}=await n;if(m)throw new Error(m.message);return{data:s}}catch(r){return console.error("Error getting employee earnings report:",r),{error:r.message}}},D=async y=>{try{const{organizationId:r,startDate:l,endDate:p,employeeIds:i}=y;let n=c.from("payroll_items").select(`
        id,
        employee_id,
        sss_contribution,
        philhealth_contribution,
        pagibig_contribution,
        withholding_tax,
        employee:employee_id(
          id,
          first_name,
          middle_name,
          last_name,
          employee_number,
          tin_number,
          sss_number,
          philhealth_number,
          pagibig_number
        ),
        payroll_period:payroll_period_id(
          id,
          name,
          start_date,
          end_date,
          payment_date
        )
      `).eq("organization_id",r).not("is_thirteenth_month","eq",!0);if(i&&i.length>0&&(n=n.in("employee_id",i)),l||p){const e=c.from("payroll_periods").select("id").eq("organization_id",r);l&&e.gte("start_date",l.toISOString().split("T")[0]),p&&e.lte("end_date",p.toISOString().split("T")[0]);const{data:t,error:d}=await e;if(d)throw new Error(d.message);if(t.length>0){const a=t.map(u=>u.id);n=n.in("payroll_period_id",a)}else return{data:{items:[],summary:{}}}}const{data:s,error:m}=await n;if(m)throw new Error(m.message);const o={totalSSS:s.reduce((e,t)=>e+Number(t.sss_contribution||0),0),totalPhilHealth:s.reduce((e,t)=>e+Number(t.philhealth_contribution||0),0),totalPagibig:s.reduce((e,t)=>e+Number(t.pagibig_contribution||0),0),totalWithholdingTax:s.reduce((e,t)=>e+Number(t.withholding_tax||0),0),totalContributions:s.reduce((e,t)=>e+Number(t.sss_contribution||0)+Number(t.philhealth_contribution||0)+Number(t.pagibig_contribution||0),0),employeeCount:new Set(s.map(e=>e.employee_id)).size};return{data:{items:s,summary:o}}}catch(r){return console.error("Error getting government contributions report:",r),{error:r.message}}},N=async y=>{try{const{organizationId:r,startDate:l,endDate:p,employeeIds:i}=y,n=l?l.getFullYear():new Date().getFullYear(),s=new Date(n,0,1),m=new Date(n,11,31),{data:o,error:e}=await h({organizationId:r,startDate:s,endDate:m,employeeIds:i});if(e)throw new Error(e);const t=new Map;return o==null||o.forEach(a=>{const u=a.employee_id;t.has(u)||t.set(u,{employee:a.employee,totalBasicPay:0,totalGrossPay:0,totalNetPay:0,totalWithholdingTax:0,totalSSS:0,totalPhilHealth:0,totalPagibig:0,totalDeductions:0,totalTaxableIncome:0,periods:[]});const _=t.get(u);_.totalBasicPay+=Number(a.basic_pay||0),_.totalGrossPay+=Number(a.gross_pay||0),_.totalNetPay+=Number(a.net_pay||0),_.totalWithholdingTax+=Number(a.withholding_tax||0),_.totalSSS+=Number(a.sss_contribution||0),_.totalPhilHealth+=Number(a.philhealth_contribution||0),_.totalPagibig+=Number(a.pagibig_contribution||0),_.totalDeductions+=Number(a.total_deductions||0),_.totalTaxableIncome+=Number(a.taxable_income||0),_.periods.push({periodId:a.payroll_period.id,periodName:a.payroll_period.name,startDate:a.payroll_period.start_date,endDate:a.payroll_period.end_date,basicPay:Number(a.basic_pay||0),grossPay:Number(a.gross_pay||0),netPay:Number(a.net_pay||0),withholdingTax:Number(a.withholding_tax||0)})}),{data:Array.from(t.values())}}catch(r){return console.error("Error getting BIR withholding tax report:",r),{error:r.message}}};export{D as a,h as b,f as c,N as g};
