import{ab as $,d as G,h as M,b as q,r as n,j as e,i as A,A as f,B as m,o as b,a6 as h,P as w,a7 as z,ad as L,X as Y,ay as k}from"./index-C6AV3cVN.js";import{C as E}from"./Card-yj7fueH8.js";import{u as K}from"./currencyFormatter-BsFWv3sX.js";import{c as U}from"./formatters-Cypx7G-j.js";import{h as l,i as W,m as J}from"./payables-q7zOb02j.js";import{A as V}from"./AttachmentGallery-BWWhOZ7l.js";import{E as X}from"./EnhancedNumberInput-Bwo1E3yF.js";import{P as Q}from"./PageTitle-FHPo8gWi.js";const ie=()=>{const{id:x}=$(),d=G(),{currentOrganization:o}=M(),{user:g}=q(),p=K(),[a,F]=n.useState(null),[D,N]=n.useState(!0),[j,v]=n.useState(!1),[y,r]=n.useState(null),[P,I]=n.useState(null),[T,B]=n.useState(!1),[t,_]=n.useState({payable_id:x||"",payment_date:new Date().toISOString().split("T")[0],amount_paid:0,payment_method:l.CASH,reference_number:"",attachment_url:"",remarks:""}),O=async()=>{if(!(!o||!x)){N(!0),r(null);try{const{payable:s,error:i}=await W(o.id,x);if(i){r(i);return}if(!s){r("Payable not found");return}F(s),_(c=>({...c,amount_paid:Number(s.balance)}))}catch(s){r(s.message||"Failed to fetch payable details")}finally{N(!1)}}};n.useEffect(()=>{O()},[o,x]);const u=(s,i)=>{_(c=>({...c,[s]:i}))},R=()=>t.payment_date?!t.amount_paid||t.amount_paid<=0?"Payment amount must be greater than zero":a&&t.amount_paid>Number(a.balance)?"Payment amount cannot exceed the outstanding balance":t.payment_method?null:"Payment method is required":"Payment date is required",H=async s=>{if(s.preventDefault(),!o||!g||!a)return;const i=R();if(i){r(i);return}v(!0),r(null);try{const{success:c,payment:C,error:S}=await J(o.id,t,g.id);if(S){r(S);return}c&&C&&(I(C.id),B(!0))}catch(c){r(c.message||"Failed to create payment")}finally{v(!1)}};return o?D?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(A,{size:"xl"})}):y&&!a?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(f,{color:"failure",children:y}),e.jsxs(m,{color:"light",onClick:()=>d("/payables"),className:"mt-4",children:[e.jsx(b,{className:"mr-2 h-5 w-5"}),"Back to Payables"]})]}):a?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsxs(m,{color:"light",onClick:()=>d(`/payables/${a.id}`),children:[e.jsx(b,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(Q,{title:"Add Payment",subtitle:`For payable ${a.reference_number}`})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs(E,{children:[e.jsx("h5",{className:"text-lg font-bold mb-4",children:"Payable Summary"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Reference:"}),e.jsx("span",{className:"font-medium",children:a.reference_number})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Supplier/Employee:"}),e.jsx("span",{children:a.supplier?a.supplier.name:a.employee?`${a.employee.first_name} ${a.employee.last_name}`:"N/A"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Due Date:"}),e.jsx("span",{children:U(a.due_date)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Total Amount:"}),e.jsx("span",{children:p(a.amount)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Total Paid:"}),e.jsx("span",{children:p(a.total_paid||0)})]}),e.jsxs("div",{className:"flex justify-between border-t pt-3",children:[e.jsx("span",{className:"text-gray-500 font-medium",children:"Outstanding Balance:"}),e.jsx("span",{className:"font-bold text-lg",children:p(a.balance)})]})]})]}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs(E,{children:[e.jsx("h5",{className:"text-lg font-bold mb-4",children:"Payment Details"}),y&&e.jsx(f,{color:"failure",className:"mb-4",children:y}),e.jsxs("form",{onSubmit:H,className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(h,{htmlFor:"payment_date",value:"Payment Date"}),e.jsx(w,{id:"payment_date",type:"date",value:t.payment_date,onChange:s=>u("payment_date",s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(h,{htmlFor:"amount_paid",value:"Payment Amount"}),e.jsx(X,{id:"amount_paid",step:"0.01",min:"0.01",max:Number(a.balance),value:t.amount_paid,onChange:s=>u("amount_paid",Number(s.target.value)),required:!0}),e.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:["Maximum: ",p(a.balance)]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(h,{htmlFor:"payment_method",value:"Payment Method"}),e.jsxs(z,{id:"payment_method",value:t.payment_method,onChange:s=>u("payment_method",s.target.value),required:!0,children:[e.jsx("option",{value:l.CASH,children:"Cash"}),e.jsx("option",{value:l.CHECK,children:"Check"}),e.jsx("option",{value:l.BANK_TRANSFER,children:"Bank Transfer"}),e.jsx("option",{value:l.GCASH,children:"GCash"}),e.jsx("option",{value:l.PAYMAYA,children:"PayMaya"}),e.jsx("option",{value:l.CREDIT_CARD,children:"Credit Card"}),e.jsx("option",{value:l.OTHER,children:"Other"})]})]}),e.jsxs("div",{children:[e.jsx(h,{htmlFor:"reference_number",value:"Reference Number"}),e.jsx(w,{id:"reference_number",type:"text",placeholder:"Check #, Transaction ID, etc.",value:t.reference_number,onChange:s=>u("reference_number",s.target.value)})]})]}),e.jsxs("div",{children:[e.jsx(h,{htmlFor:"remarks",value:"Remarks"}),e.jsx(L,{id:"remarks",rows:3,placeholder:"Additional notes about this payment",value:t.remarks,onChange:s=>u("remarks",s.target.value)})]}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(Y,{className:"h-5 w-5 text-blue-600"}),e.jsx("h6",{className:"font-medium text-blue-800",children:"Payment Attachments"})]}),e.jsx("p",{className:"text-sm text-blue-700 mb-2",children:"You can upload receipts, bank statements, or other proof of payment after saving this payment."}),e.jsx("p",{className:"text-xs text-blue-600",children:"Supported: Images (JPEG, PNG, GIF, WebP) and PDF files up to 10MB each"})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx(m,{color:"light",onClick:()=>d(`/payables/${a.id}`),disabled:j,children:"Cancel"}),e.jsxs(m,{type:"submit",color:"primary",disabled:j,children:[e.jsx(k,{className:"mr-2 h-5 w-5"}),j?"Saving...":"Save Payment"]})]})]}),T&&P&&e.jsxs("div",{className:"mt-6 border-t pt-6",children:[e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(k,{className:"h-5 w-5 text-green-600"}),e.jsx("h6",{className:"font-medium text-green-800",children:"Payment Saved Successfully!"})]}),e.jsx("p",{className:"text-sm text-green-700",children:"Now you can upload receipts, bank statements, or other proof of payment documents."})]}),e.jsx(V,{attachableType:"payment",attachableId:P,editable:!0,showUpload:!0,maxFiles:10}),e.jsxs("div",{className:"mt-4 flex justify-between",children:[e.jsx(m,{color:"light",onClick:()=>d(`/payables/${a.id}`),children:"Skip Attachments"}),e.jsx(m,{color:"success",onClick:()=>d(`/payables/${a.id}`),children:"Complete & View Payable"})]})]})]})})]})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(f,{color:"failure",children:"Payable not found"}),e.jsxs(m,{color:"light",onClick:()=>d("/payables"),className:"mt-4",children:[e.jsx(b,{className:"mr-2 h-5 w-5"}),"Back to Payables"]})]}):e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(A,{size:"xl"})})};export{ie as default};
