import{ab as D,h as E,r as d,j as e,i as B,A as L,J as P,L as x,B as m,o as g,q as A,e as a}from"./index-C6AV3cVN.js";import{C as h}from"./Card-yj7fueH8.js";import{a as S}from"./inventoryTransaction-1UXV5RDN.js";import{d as l}from"./formatters-Cypx7G-j.js";import{u as U}from"./currencyFormatter-BsFWv3sX.js";import"./floatInventory-k_pEQeIK.js";const H=()=>{var j,f;const{id:n}=D(),{currentOrganization:c}=E(),N=U(),[s,v]=d.useState(null),[b,u]=d.useState(!0),[p,i]=d.useState(null);d.useEffect(()=>{c&&n&&_()},[c,n]);const _=async()=>{if(!(!c||!n)){u(!0),i(null);try{const{transaction:t,error:o}=await S(c.id,n);o?i(o):t?v(t):i("Transaction not found")}catch(t){i(t.message||"An error occurred while fetching the transaction")}finally{u(!1)}}},w=t=>{switch(t.toLowerCase()){case"purchase":case"receipt":return e.jsx(a,{color:"success",children:"Purchase"});case"sale":return e.jsx(a,{color:"info",children:"Sale"});case"adjustment":return e.jsx(a,{color:"warning",children:"Adjustment"});case"transfer":return e.jsx(a,{color:"purple",children:"Transfer"});case"return":return e.jsx(a,{color:"pink",children:"Return"});default:return e.jsx(a,{color:"gray",children:t})}},T=()=>{var y;if(!s)return;const t=[["Transaction ID","Date/Time","Type","Product","Quantity","Reference","Notes"],[s.id,l(s.created_at),s.transaction_type,((y=s.product)==null?void 0:y.name)||"Unknown Product",s.quantity,s.reference_type&&s.reference_id?`${s.reference_type.replace(/_/g," ")} (${s.reference_id})`:"-",s.notes||"-"]].map(k=>k.join(",")).join(`
`),o=new Blob([t],{type:"text/csv;charset=utf-8;"}),C=URL.createObjectURL(o),r=document.createElement("a");r.setAttribute("href",C),r.setAttribute("download",`transaction_${s.id}.csv`),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)};return b?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(h,{children:e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(B,{size:"xl"})})})}):p||!s?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(h,{children:e.jsxs(L,{color:"failure",icon:P,children:[e.jsx("h3",{className:"text-lg font-medium",children:"Error"}),e.jsx("p",{children:p||"Transaction not found"}),e.jsx("div",{className:"mt-4",children:e.jsx(x,{to:"/inventory/transactions",children:e.jsxs(m,{color:"gray",size:"sm",children:[e.jsx(g,{className:"mr-2 h-4 w-4"}),"Back to Transactions"]})})})]})})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(h,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Transaction Details"}),e.jsxs("p",{className:"text-gray-500",children:["ID: ",s.id]})]}),e.jsxs("div",{className:"flex gap-2 mt-4 md:mt-0",children:[e.jsx(x,{to:"/inventory/transactions",children:e.jsxs(m,{color:"light",children:[e.jsx(g,{className:"mr-2 h-5 w-5"}),"Back to Transactions"]})}),e.jsxs(m,{color:"light",onClick:T,children:[e.jsx(A,{className:"mr-2 h-5 w-5"}),"Export CSV"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"text-xl font-bold mb-4",children:"Transaction Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Date/Time"}),e.jsx("p",{className:"font-medium",children:l(s.created_at)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Type"}),e.jsx("div",{children:w(s.transaction_type)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Quantity"}),e.jsx("p",{className:"font-medium",children:s.quantity})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Reference"}),e.jsx("p",{children:s.reference_type&&s.reference_id?`${s.reference_type.replace(/_/g," ")} (${s.reference_id})`:"-"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"text-xl font-bold mb-4",children:"Product Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Product"}),s.product?e.jsx(x,{to:`/inventory/details/${s.product_id}`,className:"text-blue-600 hover:underline",children:s.product.name}):e.jsx("p",{children:"Unknown Product"})]}),((j=s.product)==null?void 0:j.sku)&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"SKU"}),e.jsx("p",{children:s.product.sku})]}),s.uom&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Unit of Measure"}),e.jsxs("p",{children:[s.uom.name," (",s.uom.code,")"]})]}),((f=s.product)==null?void 0:f.cost_price)&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Cost Price"}),e.jsx("p",{children:N(s.product.cost_price)})]})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"text-xl font-bold mb-4",children:"Notes"}),e.jsx("p",{className:"whitespace-pre-line",children:s.notes||"No notes available"})]}),e.jsxs("div",{className:"mt-6 text-sm text-gray-500",children:[e.jsxs("p",{children:["Created: ",l(s.created_at)]}),s.updated_at&&s.updated_at!==s.created_at&&e.jsxs("p",{children:["Last Updated: ",l(s.updated_at)]})]})]})})};export{H as default};
