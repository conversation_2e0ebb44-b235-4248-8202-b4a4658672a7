import{j as s,a7 as j,B as c}from"./index-C6AV3cVN.js";const h=({currentPage:l,totalPages:e,itemsPerPage:o,totalItems:x,onPageChange:r,onItemsPerPageChange:n,itemName:t="items"})=>{const m=l*o,d=m-o;return x===0?null:s.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between mt-4",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2 md:mb-0",children:[s.jsx("span",{className:"text-sm text-gray-500",children:"Items per page:"}),s.jsxs(j,{id:"itemsPerPage",value:o,onChange:a=>n(parseInt(a.target.value)),className:"w-20",size:"sm",children:[s.jsx("option",{value:"5",children:"5"}),s.jsx("option",{value:"10",children:"10"}),s.jsx("option",{value:"25",children:"25"}),s.jsx("option",{value:"50",children:"50"}),s.jsx("option",{value:"100",children:"100"})]}),s.jsxs("span",{className:"text-sm text-gray-500",children:["Showing ",d+1," to ",Math.min(m,x)," of ",x," ",t]})]}),e>1&&s.jsxs("div",{className:"flex gap-2 items-center flex-wrap",children:[s.jsx(c,{color:"gray",onClick:()=>r(1),disabled:l===1,size:"xs",children:"First"}),s.jsx(c,{color:"gray",onClick:()=>r(l-1),disabled:l===1,size:"sm",children:"Previous"}),s.jsx("div",{className:"flex gap-1",children:[...Array(e)].map((a,i)=>i===0||i===e-1||i>=l-2&&i<=l+2?s.jsx(c,{color:l===i+1?"blue":"gray",onClick:()=>r(i+1),size:"xs",className:"min-w-[32px]",children:i+1},i):i===l-3||i===l+3?s.jsx("span",{className:"text-gray-400",children:"..."},i):null)}),s.jsx(c,{color:"gray",onClick:()=>r(l+1),disabled:l===e,size:"sm",children:"Next"}),s.jsx(c,{color:"gray",onClick:()=>r(e),disabled:l===e,size:"xs",children:"Last"})]})]})};export{h as P};
