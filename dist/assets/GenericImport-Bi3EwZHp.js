import{b as Z,h as _,r as a,R as K,j as e,M as j,X as N,B as m,i as T,Y as Q,Z as ee,A as p,e as se,_ as o,U as le,$ as te,J as re,T as v}from"./index-C6AV3cVN.js";const ne=({show:g,onClose:B,onImportComplete:O,title:w,entityName:x,previewFunction:M,importFunction:P,downloadTemplateFunction:U})=>{const{user:y}=Z(),{currentOrganization:b}=_(),h=a.useRef(null),[i,c]=a.useState("upload"),[ae,C]=a.useState(null),[S,k]=a.useState(""),[l,I]=a.useState(null),[r,R]=a.useState(null),[E,A]=a.useState(!0),[u,n]=a.useState(null),[F,f]=a.useState(!1);K.useEffect(()=>{g&&(c("upload"),C(null),k(""),I(null),R(null),n(null),f(!1))},[g]);const $=s=>{var V;const t=(V=s.target.files)==null?void 0:V[0];if(!t)return;if(!t.name.toLowerCase().endsWith(".csv")){n("Please select a CSV file");return}if(t.size>5*1024*1024){n("File size must be less than 5MB");return}C(t),n(null);const d=new FileReader;d.onload=X=>{var H;const z=(H=X.target)==null?void 0:H.result;k(z);const Y=M(z);I(Y),c("preview")},d.onerror=()=>{n("Failed to read file")},d.readAsText(t)},q=async()=>{if(!(!b||!y||!S)){f(!0),c("importing"),n(null);try{const s=await P(b.id,S,y.id,E);R(s),c("complete"),O(s)}catch(s){n(s.message||"Import failed"),c("preview")}finally{f(!1)}}},D=()=>{h.current&&(h.current.value=""),B()},W=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(N,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-lg font-medium text-gray-900",children:w}),e.jsxs("p",{className:"mt-1 text-sm text-gray-500",children:["Upload a CSV file to bulk import ",x," into your system"]})]}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(Q,{className:"h-5 w-5 text-blue-400 mt-0.5"}),e.jsxs("div",{className:"ml-3 flex-1",children:[e.jsx("h4",{className:"text-sm font-medium text-blue-800",children:"Need a template?"}),e.jsx("p",{className:"mt-1 text-sm text-blue-700",children:"Download our CSV template with sample data to get started quickly."}),e.jsxs(m,{size:"xs",color:"light",className:"mt-2",onClick:U,children:[e.jsx(ee,{className:"mr-1 h-4 w-4"}),"Download Template"]})]})]})}),e.jsx("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx("input",{ref:h,type:"file",accept:".csv",onChange:$,className:"hidden"}),e.jsxs(m,{color:"primary",onClick:()=>{var s;return(s=h.current)==null?void 0:s.click()},children:[e.jsx(N,{className:"mr-2 h-5 w-5"}),"Choose CSV File"]}),e.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"or drag and drop your CSV file here"})]})}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"CSV Requirements:"}),e.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[e.jsx("li",{children:"• First row must contain column headers"}),e.jsx("li",{children:"• Maximum file size: 5MB"}),e.jsx("li",{children:"• Use comma (,) as field separator"}),e.jsx("li",{children:"• Enclose text fields in quotes if they contain commas"})]})]}),u&&e.jsx(p,{color:"failure",children:u})]}),G=()=>l?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Import Preview"}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Review your data before importing ",l.totalRows," ",x]})]}),e.jsx(se,{color:l.isValid?"success":"failure",children:l.isValid?"Valid":"Has Errors"})]}),(l.errors.length>0||l.warnings.length>0)&&e.jsxs("div",{className:"space-y-3",children:[l.errors.length>0&&e.jsxs(p,{color:"failure",children:[e.jsxs("div",{className:"font-medium",children:["Errors found (",l.errors.length,"):"]}),e.jsxs("ul",{className:"mt-2 text-sm list-disc list-inside",children:[l.errors.slice(0,5).map((s,t)=>e.jsx("li",{children:s},t)),l.errors.length>5&&e.jsxs("li",{children:["... and ",l.errors.length-5," more errors"]})]})]}),l.warnings.length>0&&e.jsxs(p,{color:"warning",children:[e.jsxs("div",{className:"font-medium",children:["Warnings (",l.warnings.length,"):"]}),e.jsxs("ul",{className:"mt-2 text-sm list-disc list-inside",children:[l.warnings.slice(0,3).map((s,t)=>e.jsx("li",{children:s},t)),l.warnings.length>3&&e.jsxs("li",{children:["... and ",l.warnings.length-3," more warnings"]})]})]})]}),l.sampleData.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"Sample Data (first 5 rows):"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(o,{children:[e.jsx(o.Head,{children:l.headers.map(s=>e.jsx(o.HeadCell,{children:s},s))}),e.jsx(o.Body,{children:l.sampleData.map((s,t)=>e.jsx(o.Row,{children:l.headers.map(d=>e.jsx(o.Cell,{children:String(s[d]||"")},d))},t))})]})})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"Import Options:"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(le,{id:"skipDuplicates",checked:E,onChange:s=>A(s.target.checked)}),e.jsx("label",{htmlFor:"skipDuplicates",className:"ml-2 text-sm text-gray-700",children:"Skip duplicate entries (recommended)"})]})]}),u&&e.jsx(p,{color:"failure",children:u})]}):null,J=()=>e.jsxs("div",{className:"space-y-6 text-center",children:[e.jsx(T,{size:"xl"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium text-gray-900",children:["Importing ",x,"..."]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Please wait while we process your file. This may take a few moments."})]})]}),L=()=>r?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[r.success?e.jsx(te,{className:"mx-auto h-12 w-12 text-green-500"}):e.jsx(re,{className:"mx-auto h-12 w-12 text-red-500"}),e.jsxs("h3",{className:"mt-2 text-lg font-medium text-gray-900",children:["Import ",r.success?"Completed":"Completed with Errors"]})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:r.successCount}),e.jsx("div",{className:"text-sm text-green-700",children:"Imported"})]}),e.jsxs("div",{className:"text-center p-4 bg-red-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:r.errorCount}),e.jsx("div",{className:"text-sm text-red-700",children:"Errors"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-gray-600",children:r.totalRows}),e.jsx("div",{className:"text-sm text-gray-700",children:"Total"})]})]}),(r.errors.length>0||r.warnings.length>0)&&e.jsxs(v,{"aria-label":"Import results",children:[r.errors.length>0&&e.jsx(v.Item,{title:`Errors (${r.errors.length})`,children:e.jsx("div",{className:"max-h-60 overflow-y-auto",children:e.jsx("ul",{className:"text-sm text-red-600 space-y-1",children:r.errors.map((s,t)=>e.jsxs("li",{children:["• ",s]},t))})})}),r.warnings.length>0&&e.jsx(v.Item,{title:`Warnings (${r.warnings.length})`,children:e.jsx("div",{className:"max-h-60 overflow-y-auto",children:e.jsx("ul",{className:"text-sm text-yellow-600 space-y-1",children:r.warnings.map((s,t)=>e.jsxs("li",{children:["• ",s]},t))})})})]})]}):null;return e.jsxs(j,{show:g,onClose:D,size:"4xl",children:[e.jsx(j.Header,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-6 w-6"}),w]})}),e.jsxs(j.Body,{children:[i==="upload"&&W(),i==="preview"&&G(),i==="importing"&&J(),i==="complete"&&L()]}),e.jsx(j.Footer,{children:e.jsxs("div",{className:"flex justify-between w-full",children:[e.jsx("div",{children:i==="preview"&&e.jsx(m,{color:"light",onClick:()=>c("upload"),children:"Back"})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(m,{color:"gray",onClick:D,children:i==="complete"?"Close":"Cancel"}),i==="preview"&&(l==null?void 0:l.isValid)&&e.jsx(m,{color:"primary",onClick:q,disabled:F,children:F?e.jsxs(e.Fragment,{children:[e.jsx(T,{size:"sm",className:"mr-2"}),"Importing..."]}):`Import ${x}`})]})]})})]})};export{ne as G};
