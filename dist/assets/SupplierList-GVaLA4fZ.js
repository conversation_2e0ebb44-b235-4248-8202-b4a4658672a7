import{s as se,h as te,b as re,d as ae,r as a,j as e,B as p,Z as le,X as ne,a0 as z,P as ie,Q as oe,A as L,J as $,i as B,_ as n,a1 as ce,a2 as ue,a3 as de,M as R}from"./index-C6AV3cVN.js";import{C as pe}from"./Card-yj7fueH8.js";import{c as me,g as he,d as xe}from"./supplier-BJDz25mb.js";import{P as fe}from"./Pagination-CVEzfctr.js";import{f as ge}from"./excelExport-BekG2cQR.js";import{G as je}from"./GenericImport-Bi3EwZHp.js";import{p as V}from"./csvParser-Ls709fVB.js";const F=[{field:"name",required:!0,type:"string",customValidator:t=>typeof t=="string"&&t.length>255?"Supplier name must be 255 characters or less":null},{field:"email",type:"email"},{field:"phone",type:"string",customValidator:t=>t&&typeof t=="string"&&t.length>50?"Phone number must be 50 characters or less":null},{field:"tax_id",type:"string",customValidator:t=>t&&typeof t=="string"&&t.length>50?"Tax ID must be 50 characters or less":null}],Se=["name","contact_person","email","phone","address","city","state","postal_code","country","tax_id","notes"],ye=t=>{const c=V(t,F);return{isValid:c.success,headers:c.headers,sampleData:c.data.slice(0,5),errors:c.errors,warnings:c.warnings,totalRows:c.data.length}},we=async(t,c)=>{const d=c.map(l=>l.name),o=[];if(d.length>0){const{data:l}=await se.from("suppliers").select("name").eq("organization_id",t).in("name",d);l&&o.push(...l.map(i=>i.name))}return o},Ce=async(t,c,d,o=!0)=>{const l=V(c,F);if(!l.success)return{success:!1,totalRows:l.data.length,successCount:0,errorCount:l.data.length,errors:l.errors,warnings:l.warnings,createdSuppliers:[],createdItems:[]};const i=l.data,m=[...l.errors],b=[...l.warnings],y=[],v=await we(t,i);let g=0,u=0;for(let h=0;h<i.length;h++){const r=i[h],w=h+2;try{if(v.includes(r.name))if(o){b.push(`Row ${w}: Skipped - Supplier '${r.name}' already exists`);continue}else{m.push(`Row ${w}: Supplier '${r.name}' already exists`),u++;continue}const x={name:r.name,contact_person:r.contact_person||null,email:r.email||null,phone:r.phone||null,address:r.address||null,city:r.city||null,state:r.state||null,postal_code:r.postal_code||null,country:r.country||null,tax_id:r.tax_id||null,notes:r.notes||null},f=await me(t,x);if(f.supplier&&!f.error)y.push(f.supplier),g++;else{const C=f.error||"Failed to create supplier";m.push(`Row ${w}: ${C}`),u++}}catch(x){const f=x instanceof Error?x.message:"Unknown error";m.push(`Row ${w}: ${f}`),u++}}return{success:u===0,totalRows:i.length,successCount:g,errorCount:u,errors:m,warnings:b,createdSuppliers:y,createdItems:y}},Ne=()=>{const t=Se,c=['"ABC Supply Co","John Manager","<EMAIL>","555-1001","100 Industrial Blvd","Houston","TX","77001","USA","SUP123","Primary supplier for electronics"','"XYZ Materials","Jane Contact","<EMAIL>","555-1002","200 Commerce St","Dallas","TX","75201","USA","SUP456","Reliable material supplier"','"Local Vendor","","","555-1003","300 Local Ave","Austin","TX","73301","USA","","Small local supplier"'],d=[t.map(m=>`"${m}"`).join(","),...c].join(`
`),o=new Blob([d],{type:"text/csv;charset=utf-8;"}),l=URL.createObjectURL(o),i=document.createElement("a");i.setAttribute("href",l),i.setAttribute("download","supplier_import_template.csv"),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(l)},Ue=()=>{const{currentOrganization:t}=te(),{user:c}=re(),d=ae(),[o,l]=a.useState([]),[i,m]=a.useState(0),[b,y]=a.useState(!0),[v,g]=a.useState(null),[u,h]=a.useState(1),[r,w]=a.useState(10),[x,f]=a.useState(""),[C,X]=a.useState(null),[k,be]=a.useState("name"),[A,ve]=a.useState("asc"),[Pe,Ee]=a.useState(!1),[De,Ie]=a.useState(null),[J,P]=a.useState(!1),[N,Q]=a.useState(null),[I,H]=a.useState(!1),[_,E]=a.useState(null),[Y,T]=a.useState(!1);a.useEffect(()=>{D()},[t,u,k,A,r]),a.useEffect(()=>{C&&clearTimeout(C);const s=setTimeout(()=>{D()},300);return X(s),()=>{C&&clearTimeout(C)}},[x]);const D=async()=>{if(t){y(!0),g(null);try{const{suppliers:s,count:j,error:S}=await he(t.id,{searchQuery:x,limit:r,offset:(u-1)*r,sortBy:k,sortOrder:A});S?g(S):(l(s),m(j))}catch(s){g(s.message||"An error occurred while fetching suppliers")}finally{y(!1)}}},O=s=>{d(`/suppliers/${s.id}`)},q=s=>{d(`/suppliers/edit/${s.id}`)},U=()=>{d("/suppliers/create")},M=s=>{Q(s),E(null),P(!0)};a.useEffect(()=>{const j=new URLSearchParams(window.location.search).get("delete");if(j&&t){const S=o.find(ee=>ee.id===j);S&&M(S),d("/suppliers",{replace:!0})}},[o,t]);const G=async()=>{if(!(!t||!N)){H(!0),E(null);try{const{success:s,error:j}=await xe(t.id,N.id);if(j){E(j);return}s&&(l(o.filter(S=>S.id!==N.id)),P(!1),o.length===1&&u>1?h(u-1):D())}catch(s){E(s.message||"An error occurred while deleting the supplier")}finally{H(!1)}}},Z=()=>{if(o.length===0){g("No suppliers to export");return}ge(o)},K=s=>{s.successCount>0&&D(),T(!1)},W=Math.ceil(i/r);return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(pe,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Suppliers"}),e.jsx("p",{className:"text-gray-500",children:"Manage your supplier database. You can add, edit, and view supplier information."})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(p,{color:"light",onClick:Z,children:[e.jsx(le,{className:"mr-2 h-4 w-4"}),"Export"]}),e.jsxs(p,{color:"light",onClick:()=>T(!0),children:[e.jsx(ne,{className:"mr-2 h-4 w-4"}),"Import"]}),e.jsxs(p,{color:"primary",onClick:U,children:[e.jsx(z,{className:"mr-2 h-5 w-5"}),"Add Supplier"]})]})]}),e.jsx("div",{className:"mb-4",children:e.jsx(ie,{id:"search",type:"text",icon:oe,placeholder:"Search by name, email, or contact person",value:x,onChange:s=>f(s.target.value)})}),v&&e.jsx(L,{color:"failure",icon:$,className:"mb-4",children:v}),b?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(B,{size:"xl"})}):o.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No suppliers found"}),e.jsxs(p,{color:"primary",size:"sm",onClick:U,children:[e.jsx(z,{className:"mr-2 h-4 w-4"}),"Add Your First Supplier"]})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(n,{hoverable:!0,children:[e.jsxs(n.Head,{children:[e.jsx(n.HeadCell,{children:"Name"}),e.jsx(n.HeadCell,{children:"Contact Person"}),e.jsx(n.HeadCell,{children:"Email"}),e.jsx(n.HeadCell,{children:"Phone"}),e.jsx(n.HeadCell,{children:"City"}),e.jsx(n.HeadCell,{children:"Country"}),e.jsx(n.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(n.Body,{className:"divide-y",children:o.map(s=>e.jsxs(n.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(n.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:e.jsx("button",{onClick:()=>O(s),className:"hover:text-blue-600 hover:underline cursor-pointer focus:outline-none",children:s.name})}),e.jsx(n.Cell,{children:s.contact_person||e.jsx("span",{className:"text-gray-400",children:"-"})}),e.jsx(n.Cell,{children:s.email||e.jsx("span",{className:"text-gray-400",children:"-"})}),e.jsx(n.Cell,{children:s.phone||e.jsx("span",{className:"text-gray-400",children:"-"})}),e.jsx(n.Cell,{children:s.city||e.jsx("span",{className:"text-gray-400",children:"-"})}),e.jsx(n.Cell,{children:s.country||e.jsx("span",{className:"text-gray-400",children:"-"})}),e.jsx(n.Cell,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(p,{color:"light",size:"xs",onClick:()=>O(s),title:"View Details",children:e.jsx(ce,{className:"h-4 w-4"})}),e.jsx(p,{color:"light",size:"xs",onClick:()=>q(s),title:"Edit Supplier",children:e.jsx(ue,{className:"h-4 w-4"})}),e.jsx(p,{color:"light",size:"xs",onClick:()=>M(s),title:"Delete Supplier",children:e.jsx(de,{className:"h-4 w-4"})})]})})]},s.id))})]})}),e.jsx(fe,{currentPage:u,totalPages:W,itemsPerPage:r,totalItems:i,onPageChange:h,onItemsPerPageChange:s=>{w(s),h(1)},itemName:"suppliers"})]})]}),e.jsxs(R,{show:J,onClose:()=>P(!1),size:"md",children:[e.jsx(R.Header,{children:"Confirm Deletion"}),e.jsxs(R.Body,{children:[_&&e.jsx(L,{color:"failure",icon:$,className:"mb-4",children:_}),e.jsxs("p",{className:"text-gray-500",children:["Are you sure you want to delete the supplier ",e.jsx("span",{className:"font-semibold",children:N==null?void 0:N.name}),"?"]}),e.jsx("p",{className:"text-gray-500 mt-2",children:"This action cannot be undone. This will permanently delete the supplier and remove all associated data."}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-4",children:[e.jsx(p,{color:"gray",onClick:()=>P(!1),disabled:I,children:"Cancel"}),e.jsx(p,{color:"failure",onClick:G,disabled:I,children:I?e.jsxs(e.Fragment,{children:[e.jsx(B,{size:"sm",className:"mr-2"}),"Deleting..."]}):"Delete Supplier"})]})]})]}),e.jsx(je,{show:Y,onClose:()=>T(!1),onImportComplete:K,title:"Import Suppliers",entityName:"suppliers",previewFunction:ye,importFunction:Ce,downloadTemplateFunction:Ne})]})};export{Ue as default};
