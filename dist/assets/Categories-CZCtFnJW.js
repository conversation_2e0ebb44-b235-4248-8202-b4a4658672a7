import{h as ne,r,j as e,B as i,a0 as V,A as v,J as D,P as Y,i as P,_ as a,D as E,a2 as ce,a3 as de,a4 as ue,M as o,a6 as _,ad as me}from"./index-C6AV3cVN.js";import{<PERSON> as he}from"./Card-yj7fueH8.js";import{b as xe,e as ge,f as je,h as ye}from"./product-Ca8DWaNR.js";import{u as fe}from"./useProductPermissions-DyxXl-nR.js";import{P as Ce}from"./Pagination-CVEzfctr.js";const ve=()=>{const{currentOrganization:l}=ne(),{canCreateProducts:$,canUpdateProducts:G}=fe(),[k,K]=r.useState([]),[W,A]=r.useState(!0),[M,p]=r.useState(null),[H,X]=r.useState(0),[N,b]=r.useState(1),[d,Z]=r.useState(10),[w,ee]=r.useState(""),[se,c]=r.useState(!1),[u,z]=r.useState("add"),[I,O]=r.useState(null),[j,m]=r.useState(""),[S,h]=r.useState(""),[F,T]=r.useState(!1),[B,n]=r.useState(null),[re,y]=r.useState(!1),[x,Q]=r.useState(null),[q,L]=r.useState(!1),[R,f]=r.useState(null),g=async()=>{if(l){A(!0),p(null);try{const{categories:s,count:t,error:U}=await xe(l.id,{searchQuery:w||void 0,limit:d,offset:(N-1)*d});U?p(U):(K(s),X(t))}catch(s){p(s.message||"An error occurred while fetching categories")}finally{A(!1)}}};r.useEffect(()=>{g()},[l,N,w,d]);const ae=s=>{s.preventDefault(),b(1),g()},J=()=>{z("add"),O(null),m(""),h(""),n(null),c(!0)},te=s=>{z("edit"),O(s),m(s.name),h(s.description||""),n(null),c(!0)},le=s=>{Q(s),f(null),y(!0)},ie=async()=>{if(l){if(!j.trim()){n("Category name is required");return}T(!0),n(null);try{if(u==="add"){const{category:s,error:t}=await ge(l.id,j.trim(),S.trim()||void 0);t?n(t):s&&(c(!1),m(""),h(""),g())}else{if(!I)return;const{category:s,error:t}=await je(l.id,I.id,{name:j.trim(),description:S.trim()||null});t?n(t):s&&(c(!1),m(""),h(""),g())}}catch(s){n(s.message||`An error occurred while ${u==="add"?"creating":"updating"} the category`)}finally{T(!1)}}},oe=async()=>{if(!(!l||!x)){L(!0),f(null);try{const{success:s,error:t}=await ye(l.id,x.id);t?f(t):s&&(y(!1),Q(null),g())}catch(s){f(s.message||"An error occurred while deleting the category")}finally{L(!1)}}},C=$&&G;return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(he,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Product Categories"}),e.jsx("p",{className:"text-gray-500",children:"Manage your product categories here. Categories help you organize your products."})]}),C&&e.jsxs(i,{color:"primary",onClick:J,children:[e.jsx(V,{className:"mr-2 h-5 w-5"}),"Add Category"]})]}),M&&e.jsx(v,{color:"failure",icon:D,className:"mb-4",children:M}),e.jsx("form",{onSubmit:ae,className:"mb-6",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(Y,{id:"search",type:"text",placeholder:"Search categories by name or description",value:w,onChange:s=>ee(s.target.value),className:"flex-1"}),e.jsx(i,{type:"submit",children:"Search"})]})}),W?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(P,{size:"xl"})}):k.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No categories found"}),C&&e.jsxs(i,{color:"primary",size:"sm",onClick:J,children:[e.jsx(V,{className:"mr-2 h-4 w-4"}),"Add Your First Category"]})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(a,{hoverable:!0,children:[e.jsxs(a.Head,{children:[e.jsx(a.HeadCell,{children:"Category Name"}),e.jsx(a.HeadCell,{children:"Description"}),e.jsx(a.HeadCell,{children:"Products"}),C&&e.jsx(a.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(a.Body,{className:"divide-y",children:k.map(s=>e.jsxs(a.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(a.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:s.name}),e.jsx(a.Cell,{children:s.description||e.jsx("span",{className:"text-gray-400",children:"No description"})}),e.jsx(a.Cell,{children:e.jsx("span",{className:"text-gray-500",children:"-"})}),C&&e.jsx(a.Cell,{children:e.jsx("div",{className:"flex items-center",children:e.jsxs(E,{label:"",dismissOnClick:!0,renderTrigger:()=>e.jsx(i,{color:"gray",size:"xs",children:e.jsx(ue,{className:"h-4 w-4"})}),children:[e.jsxs(E.Item,{onClick:()=>te(s),children:[e.jsx(ce,{className:"mr-2 h-4 w-4"}),"Edit"]}),e.jsxs(E.Item,{onClick:()=>le(s),children:[e.jsx(de,{className:"mr-2 h-4 w-4"}),"Delete"]})]})})})]},s.id))})]})}),e.jsx(Ce,{currentPage:N,totalPages:Math.ceil(H/d),itemsPerPage:d,totalItems:H,onPageChange:b,onItemsPerPageChange:s=>{Z(s),b(1)},itemName:"categories"})]}),e.jsxs(o,{show:se,onClose:()=>c(!1),children:[e.jsx(o.Header,{children:u==="add"?"Add New Category":"Edit Category"}),e.jsxs(o.Body,{children:[B&&e.jsx(v,{color:"failure",icon:D,className:"mb-4",children:B}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(_,{htmlFor:"categoryName",value:"Category Name *"})}),e.jsx(Y,{id:"categoryName",value:j,onChange:s=>m(s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(_,{htmlFor:"categoryDescription",value:"Description"})}),e.jsx(me,{id:"categoryDescription",value:S,onChange:s=>h(s.target.value),rows:3})]})]})]}),e.jsxs(o.Footer,{children:[e.jsx(i,{color:"primary",onClick:ie,disabled:F,children:F?e.jsxs(e.Fragment,{children:[e.jsx(P,{size:"sm",className:"mr-2"}),u==="add"?"Adding...":"Saving..."]}):u==="add"?"Add Category":"Save Changes"}),e.jsx(i,{color:"gray",onClick:()=>c(!1),children:"Cancel"})]})]}),e.jsxs(o,{show:re,onClose:()=>y(!1),size:"md",children:[e.jsx(o.Header,{children:"Delete Category"}),e.jsxs(o.Body,{children:[R&&e.jsx(v,{color:"failure",icon:D,className:"mb-4",children:R}),e.jsxs("p",{className:"text-gray-700",children:["Are you sure you want to delete ",e.jsx("strong",{children:x==null?void 0:x.name}),"?"]}),e.jsx("p",{className:"text-gray-500 mt-2",children:"This action cannot be undone. Products in this category will not be deleted, but they will no longer be associated with this category."})]}),e.jsxs(o.Footer,{children:[e.jsx(i,{color:"failure",onClick:oe,disabled:q,children:q?e.jsxs(e.Fragment,{children:[e.jsx(P,{size:"sm",className:"mr-2"}),"Deleting..."]}):"Delete"}),e.jsx(i,{color:"gray",onClick:()=>y(!1),children:"Cancel"})]})]})]})};export{ve as default};
