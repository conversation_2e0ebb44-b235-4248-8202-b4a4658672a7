import{d as w,b as y,h as S,r as t,j as e,i as N,A as O,J as v,a5 as b}from"./index-C6AV3cVN.js";import{C as o}from"./Card-yj7fueH8.js";import{P}from"./ProductForm-DjQkFMoJ.js";import{c as E}from"./product-Ca8DWaNR.js";import{u as C}from"./useProductPermissions-DyxXl-nR.js";const H=()=>{const d=w(),{user:i}=y(),{currentOrganization:s,currentMember:z}=S(),{canCreateProducts:m}=C(),[x,c]=t.useState(!1),[f,n]=t.useState(null),[h,p]=t.useState(null),[j,l]=t.useState(!0);t.useEffect(()=>{(async()=>{if(!s||!i){l(!1);return}try{const{isOwner:r,error:a}=await b(s.id,i.id);p(r)}catch{}finally{l(!1)}})()},[s,i]);const g=async u=>{if(!s){n("No organization selected");return}c(!0),n(null);try{const{product:r,error:a}=await E(s.id,u);a?n(a):r&&d("/products")}catch(r){n(r.message||"An error occurred while creating the product")}finally{c(!1)}};return j?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(o,{children:e.jsxs("div",{className:"flex justify-center items-center p-8",children:[e.jsx(N,{size:"xl"}),e.jsx("p",{className:"ml-4",children:"Checking permissions..."})]})})}):!m&&!h?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(o,{children:e.jsxs(O,{color:"failure",icon:v,children:[e.jsx("h3",{className:"text-lg font-medium",children:"Permission Denied"}),e.jsx("p",{children:"You don't have permission to create products. Only owners, admins, and inventory managers can create products."})]})})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(o,{children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsx("h1",{className:"text-2xl font-bold",children:"Create New Product"})}),e.jsx(P,{onSubmit:g,isSubmitting:x,error:f||void 0})]})})};export{H as default};
