import{s as S}from"./index-C6AV3cVN.js";import{D as w}from"./payroll-j3fcCwK0.js";import{b as L}from"./payroll-DcVgVc3z.js";const Q=e=>{const l=[{min:0,max:3249.99,employeeShare:135,employerShare:255},{min:3250,max:3749.99,employeeShare:157.5,employerShare:297.5},{min:3750,max:4249.99,employeeShare:180,employerShare:340},{min:4250,max:4749.99,employeeShare:202.5,employerShare:382.5},{min:4750,max:5249.99,employeeShare:225,employerShare:425},{min:5250,max:5749.99,employeeShare:247.5,employerShare:467.5},{min:5750,max:6249.99,employeeShare:270,employerShare:510},{min:6250,max:6749.99,employeeShare:292.5,employerShare:552.5},{min:6750,max:7249.99,employeeShare:315,employerShare:595},{min:7250,max:7749.99,employeeShare:337.5,employerShare:637.5},{min:7750,max:8249.99,employeeShare:360,employerShare:680},{min:8250,max:8749.99,employeeShare:382.5,employerShare:722.5},{min:8750,max:9249.99,employeeShare:405,employerShare:765},{min:9250,max:9749.99,employeeShare:427.5,employerShare:807.5},{min:9750,max:10249.99,employeeShare:450,employerShare:850},{min:10250,max:10749.99,employeeShare:472.5,employerShare:892.5},{min:10750,max:11249.99,employeeShare:495,employerShare:935},{min:11250,max:11749.99,employeeShare:517.5,employerShare:977.5},{min:11750,max:12249.99,employeeShare:540,employerShare:1020},{min:12250,max:12749.99,employeeShare:562.5,employerShare:1062.5},{min:12750,max:13249.99,employeeShare:585,employerShare:1105},{min:13250,max:13749.99,employeeShare:607.5,employerShare:1147.5},{min:13750,max:14249.99,employeeShare:630,employerShare:1190},{min:14250,max:14749.99,employeeShare:652.5,employerShare:1232.5},{min:14750,max:15249.99,employeeShare:675,employerShare:1275},{min:15250,max:15749.99,employeeShare:697.5,employerShare:1317.5},{min:15750,max:16249.99,employeeShare:720,employerShare:1360},{min:16250,max:16749.99,employeeShare:742.5,employerShare:1402.5},{min:16750,max:17249.99,employeeShare:765,employerShare:1445},{min:17250,max:17749.99,employeeShare:787.5,employerShare:1487.5},{min:17750,max:18249.99,employeeShare:810,employerShare:1530},{min:18250,max:18749.99,employeeShare:832.5,employerShare:1572.5},{min:18750,max:19249.99,employeeShare:855,employerShare:1615},{min:19250,max:19749.99,employeeShare:877.5,employerShare:1657.5},{min:19750,max:20249.99,employeeShare:900,employerShare:1700},{min:20250,max:20749.99,employeeShare:922.5,employerShare:1742.5},{min:20750,max:21249.99,employeeShare:945,employerShare:1785},{min:21250,max:21749.99,employeeShare:967.5,employerShare:1827.5},{min:21750,max:22249.99,employeeShare:990,employerShare:1870},{min:22250,max:22749.99,employeeShare:1012.5,employerShare:1912.5},{min:22750,max:23249.99,employeeShare:1035,employerShare:1955},{min:23250,max:23749.99,employeeShare:1057.5,employerShare:1997.5},{min:23750,max:24249.99,employeeShare:1080,employerShare:2040},{min:24250,max:24749.99,employeeShare:1102.5,employerShare:2082.5},{min:24750,max:1/0,employeeShare:1125,employerShare:2125}],o=l.find(t=>e>=t.min&&e<=t.max)||l[l.length-1];return{employeeShare:o.employeeShare,employerShare:o.employerShare}},U=e=>{const o=Math.min(Math.max(e,1e4),8e4)*.04,t=o/2,a=o/2;return{employeeShare:t,employerShare:a}},Y=e=>{let l=e<=1500?.01:.02,o=.02,t=Math.min(e*l,100),a=Math.min(e*o,100);return{employeeShare:t,employerShare:a}},Z=e=>e<=20833?0:e<=33332?(e-20833)*.15:e<=66666?1875+(e-33333)*.2:e<=166666?8541.8+(e-66667)*.25:e<=666666?33541.8+(e-166667)*.3:183541.8+(e-666667)*.35,I=(e,l)=>e*l,ee=(e,l)=>e*l*1.3,ae=(e,l,o,t=!1)=>{let a=1;return o==="regular"?a=t?2.6:2:a=t?1.5:1.3,e*l*a},re=(e,l,o=!1,t=!1,a="regular")=>{let s=1.25;return o&&!t?s=1.69:t&&!o?a==="regular"?s=2.6:s=1.69:t&&o&&(a==="regular"?s=3.38:s=1.95),e*l*s},te=(e,l)=>e*l*.1,E=(e,l,o)=>{const t=new Date(e),a=new Date(l),s=Math.floor((a.getTime()-t.getTime())/(1e3*60*60*24))+1;return o==="daily"?s:o==="weekly"?Math.min(5,s):o==="semi-monthly"?11:22},ie=async(e,l,o)=>{try{const{data:t}=await S.from("payroll_items").select("basic_pay, is_thirteenth_month").eq("organization_id",e).eq("employee_id",l).eq("is_thirteenth_month",!1).gte("created_at",`${o}-01-01`).lte("created_at",`${o}-12-31`);if(!t||t.length===0){const{data:u}=await S.from("employee_salary").select("*").eq("organization_id",e).eq("employee_id",l).single();return u?u.rate_type==="daily"?Number(u.daily_rate)*22:Number(u.basic_salary):0}const a=t.reduce((u,x)=>u+Number(x.basic_pay),0),s=Math.min(12,t.length);return a/12*s}catch(t){return console.error("Error calculating 13th month pay:",t),0}},se=async(e,l,o)=>{try{const{data:t}=await S.from("payroll_periods").select("*").eq("organization_id",e).eq("id",l).single();if(!t)throw new Error("Payroll period not found");const a=t,{data:s}=await S.from("employee_salary").select("*").eq("organization_id",e).eq("employee_id",o).single();if(!s)throw new Error("Employee salary not found");const{data:u}=await S.from("employee_allowances").select("*").eq("organization_id",e).eq("employee_id",o),{data:x}=await S.from("employee_deductions").select("*").eq("organization_id",e).eq("employee_id",o),{data:c}=await S.from("time_entries").select("*").eq("organization_id",e).eq("employee_id",o).gte("date",a.start_date).lte("date",a.end_date),{data:P}=await S.from("holidays").select("*").eq("organization_id",e).gte("date",a.start_date).lte("date",a.end_date),{settings:n}=await L(e);if(!n)throw new Error("Payroll settings not found");let h=0,y=0,i=0,_=0;s.rate_type==="daily"?(y=Number(s.daily_rate),h=y*22,_=y/8):(h=Number(s.basic_salary),y=h/22,_=y/8);let D=0,N=0,q=0,T=0,M=0,H=0,k=0,O=0,G=0,W=0;if(c&&c.length>0){c.forEach(r=>{var X;const p=r.is_holiday||P&&P.some(v=>v.date===r.date),g=r.holiday_type||p&&((X=P==null?void 0:P.find(v=>v.date===r.date))==null?void 0:X.type)||"regular",C=r.is_rest_day;r.regular_hours>0&&(p?(T+=ae(_,r.regular_hours,g,C),G+=r.regular_hours):C?(q+=ee(_,r.regular_hours),O+=r.regular_hours):(D+=I(_,r.regular_hours),H+=r.regular_hours)),r.overtime_hours>0&&(N+=re(_,r.overtime_hours,C,p,g),k+=r.overtime_hours),r.night_diff_hours>0&&(M+=te(_,r.night_diff_hours),W+=r.night_diff_hours)});const m=D+N+q+T+M,d=n.pay_based_on_time_entries??!0;if(s.rate_type==="daily")if(d&&c&&c.length>0){const r=c.filter(p=>p.status==="present"&&(p.regular_hours>0||p.overtime_hours>0)).length;m>0?i=m:i=y*r}else{const r=E(a.start_date,a.end_date,n.pay_schedule);i=y*r}else if(d&&c&&c.length>0)if(m>0)i=m;else{const r=c.filter(g=>g.status==="present"&&(g.regular_hours>0||g.overtime_hours>0)).length,p=E(a.start_date,a.end_date,n.pay_schedule);n.pay_schedule==="monthly"?i=h/p*r:n.pay_schedule==="semi-monthly"?i=h/2/p*r:n.pay_schedule==="weekly"?i=h*12/52/p*r:i=y*r}else if(n.pay_schedule==="monthly")i=h;else if(n.pay_schedule==="semi-monthly")i=h/2;else if(n.pay_schedule==="weekly")i=h*12/52;else if(n.pay_schedule==="daily"){const r=E(a.start_date,a.end_date,n.pay_schedule);i=y*r}}else if(useTimeEntries)i=0;else if(s.rate_type==="daily"){const m=E(a.start_date,a.end_date,n.pay_schedule);i=y*m}else if(n.pay_schedule==="monthly")i=h;else if(n.pay_schedule==="semi-monthly")i=h/2;else if(n.pay_schedule==="weekly")i=h*12/52;else if(n.pay_schedule==="daily"){const m=E(a.start_date,a.end_date,n.pay_schedule);i=y*m}const V=(u==null?void 0:u.map(m=>({name:m.name,amount:Number(m.amount),type:m.type})))||[],R=V.reduce((m,d)=>m+d.amount,0),f=(x==null?void 0:x.map(m=>({name:m.name,amount:Number(m.amount),type:m.type})))||[],B=Q(h),$=U(h),j=Y(h);f.push({name:"SSS Contribution",amount:B.employeeShare,type:w.GOVERNMENT}),f.push({name:"PhilHealth Contribution",amount:$.employeeShare,type:w.GOVERNMENT}),f.push({name:"Pag-IBIG Contribution",amount:j.employeeShare,type:w.GOVERNMENT});const A=f.reduce((m,d)=>m+d.amount,0),z=i+R-f.filter(m=>m.type===w.GOVERNMENT).reduce((m,d)=>m+d.amount,0),b=Z(z);f.push({name:"Withholding Tax",amount:b,type:w.TAX});const F=i+R,J=F-A-b,K={regularPay:D,overtimePay:N,restDayPay:q,holidayPay:T,nightDifferentialPay:M,totalRegularHours:H,totalOvertimeHours:k,totalRestDayHours:O,totalHolidayHours:G,totalNightDiffHours:W};return{employeeId:o,periodId:l,basicPay:i,grossPay:F,netPay:J,totalAllowances:R,totalDeductions:A+b,allowances:V,deductions:f,taxableIncome:z,withholdingTax:b,sssContribution:B,philhealthContribution:$,pagibigContribution:j,timeEntryDetails:K}}catch(t){throw console.error("Error processing employee payroll:",t),new Error(`Failed to process payroll: ${t.message}`)}};export{ie as calculate13thMonthPay,E as calculateDaysInPeriod,ae as calculateHolidayPay,te as calculateNightDifferentialPay,re as calculateOvertimePay,Y as calculatePagIbigContribution,U as calculatePhilHealthContribution,I as calculateRegularPay,ee as calculateRestDayPay,Q as calculateSSSContribution,Z as calculateWithholdingTax,se as processEmployeePayroll};
