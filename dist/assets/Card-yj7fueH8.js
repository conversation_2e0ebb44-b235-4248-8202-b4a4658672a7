import{bf as i,bg as m,j as a,bh as c}from"./index-C6AV3cVN.js";const f=r=>e=>{const t={};for(const o in e)r.includes(o)||(t[o]=e[o]);return t},b=r=>{const{children:e,className:t,horizontal:o,href:s,theme:l={}}=r,h=typeof s>"u"?"div":"a",d=u(r),n=i(m().card,l);return a.jsxs(h,{"data-testid":"flowbite-card",href:s,className:c(n.root.base,n.root.horizontal[o?"on":"off"],s&&n.root.href,t),...d,children:[a.jsx(g,{...r}),a.jsx("div",{className:n.root.children,children:e})]})},g=({theme:r={},...e})=>{const t=i(m().card,r);return e.renderImage?e.renderImage(t,e.horizontal??!1):e.imgSrc?a.jsx("img",{"data-testid":"flowbite-card-image",alt:e.imgAlt??"",src:e.imgSrc,className:c(t.img.base,t.img.horizontal[e.horizontal?"on":"off"])}):null},u=f(["renderImage","imgSrc","imgAlt","children","className","horizontal","href","theme"]);export{b as C};
