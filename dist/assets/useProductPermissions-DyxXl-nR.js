import{am as P,b as p,h as l}from"./index-C6AV3cVN.js";const O=()=>{const{checkPermission:t}=P(),{user:c}=p(),{currentOrganization:i,currentMember:s}=l(),d=t("products","view"),e=t("products","create"),r=t("products","update"),u=t("products","delete"),n=(s==null?void 0:s.role)==="owner"||(s==null?void 0:s.role)==="admin"||(s==null?void 0:s.role)==="inventory_manager",o=(s==null?void 0:s.role)==="owner",a=(s==null?void 0:s.role)==="admin";return{canViewProducts:o||d,canCreateProducts:o||e&&n,canUpdateProducts:o||r&&n,canDeleteProducts:o||a||u&&(o||a),isOwnerAdminOrManager:n,currentMember:s,user:c,currentOrganization:i}};export{O as u};
