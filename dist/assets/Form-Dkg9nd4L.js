import{j as e,a6 as s,P as r,a7 as l,B as a}from"./index-C6AV3cVN.js";const d=()=>e.jsxs("div",{className:"rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words",children:[e.jsx("h5",{className:"card-title",children:"Form"}),e.jsx("div",{className:"mt-6",children:e.jsxs("div",{className:"grid grid-cols-12 gap-30",children:[e.jsx("div",{className:"lg:col-span-6 col-span-12",children:e.jsxs("div",{className:"flex  flex-col gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(s,{htmlFor:"name",value:"Your Name"})}),e.jsx(r,{id:"name",type:"text",placeholder:"Your Name",required:!0,className:"form-control form-rounded-xl"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(s,{htmlFor:"email1",value:"Your email"})}),e.jsx(r,{id:"email1",type:"email",placeholder:"<EMAIL>",required:!0,className:"form-control form-rounded-xl"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(s,{htmlFor:"password1",value:"Your password"})}),e.jsx(r,{id:"password1",type:"password",required:!0,className:"form-control form-rounded-xl"})]})]})}),e.jsx("div",{className:"lg:col-span-6 col-span-12",children:e.jsxs("div",{className:"flex  flex-col gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(s,{htmlFor:"countries1",value:"Country"})}),e.jsxs(l,{id:"countries1",required:!0,className:"select-rounded",children:[e.jsx("option",{children:"India"}),e.jsx("option",{children:"Canada"}),e.jsx("option",{children:"France"}),e.jsx("option",{children:"Germany"})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(s,{htmlFor:"countries2",value:"State"})}),e.jsxs(l,{id:"countries2",required:!0,className:"select-rounded",children:[e.jsx("option",{children:"Delhi"}),e.jsx("option",{children:"Gujarat"}),e.jsx("option",{children:"Mumbai"}),e.jsx("option",{children:"Chennai"})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(s,{htmlFor:"countries3",value:"City"})}),e.jsxs(l,{id:"countries3",required:!0,className:"select-rounded",children:[e.jsx("option",{children:"Rajkot"}),e.jsx("option",{children:"Ahemedabad"})]})]})]})}),e.jsxs("div",{className:"col-span-12 flex gap-3",children:[e.jsx(a,{color:"primary",children:"Submit"}),e.jsx(a,{color:"error",children:"Cancel"})]})]})})]}),o=()=>e.jsx(d,{});export{o as default};
