import{h as U,r as t,j as e,i as V,L as x,B as m,a0 as F,ao as $,I as Y,J as O,z as K,t as J,az as N,aA as p,e as c,Q as W,P as G,D as h,_ as l,aB as X}from"./index-C6AV3cVN.js";import{C as a}from"./Card-yj7fueH8.js";import{g as Z}from"./product-Ca8DWaNR.js";import{a as y}from"./formatters-Cypx7G-j.js";const ee=[{id:"1",name:"Product A",quantity:245,trend:"up"},{id:"2",name:"Product B",quantity:187,trend:"up"},{id:"3",name:"Product C",quantity:152,trend:"down"},{id:"4",name:"Product D",quantity:124,trend:"up"},{id:"5",name:"Product E",quantity:98,trend:"down"}],ce=()=>{const{currentOrganization:f}=U(),[j,q]=t.useState([]),[n,H]=t.useState([]),[A,w]=t.useState(!0),[S,g]=t.useState(null),[o,B]=t.useState(""),[k,M]=t.useState(1),[u]=t.useState(10),[i,D]=t.useState("name"),[d,P]=t.useState("asc");t.useEffect(()=>{(async()=>{if(f){w(!0),g(null);try{const{products:r,error:I}=await Z(f.id,{sortBy:i,sortOrder:d,limit:100});if(I)g(I);else{q(r);const Q=r.filter(L=>L.stock_quantity<=L.min_stock_level);H(Q)}}catch(r){g(r.message||"An error occurred while fetching inventory data")}finally{w(!1)}}})()},[f,i,d]);const b=j.filter(s=>s.name.toLowerCase().includes(o.toLowerCase())||s.sku&&s.sku.toLowerCase().includes(o.toLowerCase())||s.barcode&&s.barcode.toLowerCase().includes(o.toLowerCase())),C=k*u,T=C-u,_=b.slice(T,C),E=Math.ceil(b.length/u),v=s=>{i===s?P(d==="asc"?"desc":"asc"):(D(s),P("asc"))},R=s=>s.stock_quantity<=0?e.jsx(c,{color:"failure",children:"Out of Stock"}):s.stock_quantity<=s.min_stock_level?e.jsx(c,{color:"warning",children:"Low Stock"}):e.jsx(c,{color:"success",children:"In Stock"}),z=j.reduce((s,r)=>s+r.stock_quantity*Number(r.cost_price||0),0);return A?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(V,{size:"xl"})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Inventory Management"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(x,{to:"/inventory/receipts/new",children:e.jsxs(m,{color:"primary",children:[e.jsx(F,{className:"mr-2 h-5 w-5"}),"Receive Inventory"]})}),e.jsx(x,{to:"/inventory/transactions",children:e.jsxs(m,{color:"light",children:[e.jsx($,{className:"mr-2 h-5 w-5"}),"Transactions"]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[e.jsx(a,{className:"border-l-4 border-blue-500",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Total Products"}),e.jsx("p",{className:"text-2xl font-bold",children:j.length})]}),e.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:e.jsx(Y,{className:"h-6 w-6 text-blue-500"})})]})}),e.jsx(a,{className:"border-l-4 border-yellow-500",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Low Stock Items"}),e.jsx("p",{className:"text-2xl font-bold",children:n.length})]}),e.jsx("div",{className:"p-3 bg-yellow-100 rounded-full",children:e.jsx(O,{className:"h-6 w-6 text-yellow-500"})})]})}),e.jsx(a,{className:"border-l-4 border-red-500",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Out of Stock"}),e.jsx("p",{className:"text-2xl font-bold",children:j.filter(s=>s.stock_quantity<=0).length})]}),e.jsx("div",{className:"p-3 bg-red-100 rounded-full",children:e.jsx(K,{className:"h-6 w-6 text-red-500"})})]})}),e.jsx(a,{className:"border-l-4 border-green-500",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Total Value"}),e.jsxs("p",{className:"text-2xl font-bold",children:["$",z.toFixed(2)]})]}),e.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:e.jsx(J,{className:"h-6 w-6 text-green-500"})})]})})]}),n.length>0&&e.jsx("div",{className:"mb-6",children:e.jsxs(a,{className:"bg-yellow-50 border-yellow-200",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(O,{className:"h-6 w-6 text-yellow-500 mr-2"}),e.jsx("h2",{className:"text-lg font-semibold text-yellow-700",children:"Low Stock Alert"})]}),e.jsxs("p",{className:"text-yellow-600 mb-4",children:["You have ",n.length," items that are running low on inventory."]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm text-left",children:[e.jsx("thead",{className:"text-xs text-gray-700 uppercase bg-yellow-100",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2",children:"Product Name"}),e.jsx("th",{className:"px-4 py-2",children:"Current Stock"}),e.jsx("th",{className:"px-4 py-2",children:"Min. Stock Level"}),e.jsx("th",{className:"px-4 py-2",children:"Action"})]})}),e.jsx("tbody",{children:n.slice(0,5).map(s=>e.jsxs("tr",{className:"border-b border-yellow-200",children:[e.jsx("td",{className:"px-4 py-2 font-medium",children:s.name}),e.jsx("td",{className:"px-4 py-2",children:y(s.stock_quantity)}),e.jsx("td",{className:"px-4 py-2",children:y(s.min_stock_level)}),e.jsx("td",{className:"px-4 py-2",children:e.jsx(x,{to:`/inventory/receipts/new?product=${s.id}`,children:e.jsx(m,{size:"xs",color:"warning",children:"Order More"})})})]},s.id))})]})}),n.length>5&&e.jsx("div",{className:"text-right mt-2",children:e.jsxs(m,{color:"warning",size:"xs",children:["View All ",n.length," Low Stock Items"]})})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[e.jsxs(a,{children:[e.jsx("h5",{className:"text-xl font-bold mb-4",children:"Top Moving Products"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm text-left",children:[e.jsx("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2",children:"Product"}),e.jsx("th",{className:"px-4 py-2",children:"Units Sold"}),e.jsx("th",{className:"px-4 py-2",children:"Trend"})]})}),e.jsx("tbody",{children:ee.map(s=>e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"px-4 py-2 font-medium",children:s.name}),e.jsx("td",{className:"px-4 py-2",children:s.quantity}),e.jsx("td",{className:"px-4 py-2",children:s.trend==="up"?e.jsxs("div",{className:"flex items-center text-green-500",children:[e.jsx(N,{className:"mr-1"}),e.jsx("span",{children:"Up"})]}):e.jsxs("div",{className:"flex items-center text-red-500",children:[e.jsx(p,{className:"mr-1"}),e.jsx("span",{children:"Down"})]})})]},s.id))})]})})]}),e.jsxs(a,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h5",{className:"text-xl font-bold",children:"Recent Transactions"}),e.jsx(x,{to:"/inventory/transactions",children:e.jsx(m,{size:"xs",color:"light",children:"View All"})})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm text-left",children:[e.jsx("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2",children:"Product"}),e.jsx("th",{className:"px-4 py-2",children:"Quantity"}),e.jsx("th",{className:"px-4 py-2",children:"Type"}),e.jsx("th",{className:"px-4 py-2",children:"Date"})]})}),e.jsxs("tbody",{children:[e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"px-4 py-2 font-medium",children:"Product A"}),e.jsx("td",{className:"px-4 py-2",children:"+50"}),e.jsx("td",{className:"px-4 py-2",children:e.jsx(c,{color:"success",children:"Received"})}),e.jsx("td",{className:"px-4 py-2",children:"Today, 10:30 AM"})]}),e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"px-4 py-2 font-medium",children:"Product B"}),e.jsx("td",{className:"px-4 py-2",children:"-15"}),e.jsx("td",{className:"px-4 py-2",children:e.jsx(c,{color:"failure",children:"Sale"})}),e.jsx("td",{className:"px-4 py-2",children:"Today, 9:45 AM"})]}),e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"px-4 py-2 font-medium",children:"Product C"}),e.jsx("td",{className:"px-4 py-2",children:"-8"}),e.jsx("td",{className:"px-4 py-2",children:e.jsx(c,{color:"failure",children:"Sale"})}),e.jsx("td",{className:"px-4 py-2",children:"Yesterday, 4:20 PM"})]}),e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"px-4 py-2 font-medium",children:"Product D"}),e.jsx("td",{className:"px-4 py-2",children:"+25"}),e.jsx("td",{className:"px-4 py-2",children:e.jsx(c,{color:"success",children:"Received"})}),e.jsx("td",{className:"px-4 py-2",children:"Yesterday, 1:15 PM"})]}),e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"px-4 py-2 font-medium",children:"Product E"}),e.jsx("td",{className:"px-4 py-2",children:"-3"}),e.jsx("td",{className:"px-4 py-2",children:e.jsx(c,{color:"warning",children:"Adjustment"})}),e.jsx("td",{className:"px-4 py-2",children:"May 21, 2023"})]})]})]})})]})]}),e.jsxs(a,{className:"mb-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4",children:[e.jsx("h5",{className:"text-xl font-bold",children:"Inventory List"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(W,{className:"text-gray-500"})}),e.jsx(G,{type:"text",placeholder:"Search products...",value:o,onChange:s=>B(s.target.value),className:"pl-10"})]}),e.jsxs(h,{label:"Filter",color:"light",children:[e.jsx(h.Item,{children:"All Products"}),e.jsx(h.Item,{children:"In Stock"}),e.jsx(h.Item,{children:"Low Stock"}),e.jsx(h.Item,{children:"Out of Stock"})]})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(l,{children:[e.jsxs(l.Head,{children:[e.jsxs(l.HeadCell,{onClick:()=>v("name"),className:"cursor-pointer",children:["Product Name",i==="name"&&(d==="asc"?e.jsx(N,{className:"inline ml-1"}):e.jsx(p,{className:"inline ml-1"}))]}),e.jsx(l.HeadCell,{children:"SKU/Barcode"}),e.jsxs(l.HeadCell,{onClick:()=>v("stock_quantity"),className:"cursor-pointer",children:["Quantity",i==="stock_quantity"&&(d==="asc"?e.jsx(N,{className:"inline ml-1"}):e.jsx(p,{className:"inline ml-1"}))]}),e.jsx(l.HeadCell,{children:"Status"}),e.jsxs(l.HeadCell,{onClick:()=>v("cost_price"),className:"cursor-pointer",children:["Value",i==="cost_price"&&(d==="asc"?e.jsx(N,{className:"inline ml-1"}):e.jsx(p,{className:"inline ml-1"}))]})]}),e.jsx(l.Body,{className:"divide-y",children:_.length>0?_.map(s=>e.jsxs(l.Row,{className:"bg-white",children:[e.jsx(l.Cell,{className:"font-medium",children:e.jsx(x,{to:`/products/${s.id}`,className:"text-blue-600 hover:underline",children:s.name})}),e.jsxs(l.Cell,{children:[s.sku&&e.jsxs("div",{children:["SKU: ",s.sku]}),s.barcode&&e.jsxs("div",{children:["Barcode: ",s.barcode]})]}),e.jsx(l.Cell,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-semibold",children:y(s.stock_quantity)}),s.min_stock_level>0&&e.jsxs("span",{className:"text-xs text-gray-500 ml-1",children:["(Min: ",y(s.min_stock_level),")"]})]})}),e.jsx(l.Cell,{children:R(s)}),e.jsxs(l.Cell,{children:["$",(Number(s.cost_price||0)*s.stock_quantity).toFixed(2)]})]},s.id)):e.jsx(l.Row,{children:e.jsx(l.Cell,{colSpan:5,className:"text-center py-4",children:o?"No products match your search.":"No products found."})})})]})}),b.length>u&&e.jsx("div",{className:"flex justify-center mt-4",children:e.jsx(X,{currentPage:k,totalPages:E,onPageChange:M,showIcons:!0})})]}),S&&e.jsx("div",{className:"mt-4",children:e.jsxs(a,{color:"failure",children:[e.jsx("p",{className:"font-medium",children:"Error"}),e.jsx("p",{children:S})]})})]})};export{ce as default};
