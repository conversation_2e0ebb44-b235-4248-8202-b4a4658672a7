import{r as l,R as W,bk as Be,d as Bn,h as Fn,j as i,i as Hn,B as ce,o as Kn,A as gt,G as Ht,$ as $e,bO as Kt,bP as Xn,a0 as Un,ay as Yn,M as Re,bc as Wn,a3 as an,P as qn,K as Vn,a6 as Te,E as Gn,z as Xt,U as Jn,bN as Qn}from"./index-C6AV3cVN.js";import{C as ln}from"./Card-yj7fueH8.js";import{D as mt}from"./react-datepicker-BrCvW-wJ.js";import{g as Zn}from"./employee-DWC25S7P.js";import{T as te}from"./payroll-j3fcCwK0.js";import{P as er}from"./PageTitle-FHPo8gWi.js";import{E as tr}from"./EmployeeSearchSelect-vG5mZJbz.js";import{c as nr,a as nt}from"./timeEntry-DfPyfZaA.js";import{i as Ut}from"./index-4YOzgfrD.js";import"./index-DNTDRJcs.js";import"./index-Cn2wB4rc.js";import"./typeof-QjJsDpFa.js";import"./index-idNacaog.js";import"./index-KY8jayTk.js";function rr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return l.useMemo(()=>r=>{t.forEach(s=>s(r))},t)}const at=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";function _e(e){const t=Object.prototype.toString.call(e);return t==="[object Window]"||t==="[object global]"}function Ct(e){return"nodeType"in e}function q(e){var t,n;return e?_e(e)?e:Ct(e)&&(t=(n=e.ownerDocument)==null?void 0:n.defaultView)!=null?t:window:window}function Et(e){const{Document:t}=q(e);return e instanceof t}function qe(e){return _e(e)?!1:e instanceof q(e).HTMLElement}function cn(e){return e instanceof q(e).SVGElement}function Oe(e){return e?_e(e)?e.document:Ct(e)?Et(e)?e:qe(e)||cn(e)?e.ownerDocument:document:document:document}const ue=at?l.useLayoutEffect:l.useEffect;function jt(e){const t=l.useRef(e);return ue(()=>{t.current=e}),l.useCallback(function(){for(var n=arguments.length,r=new Array(n),s=0;s<n;s++)r[s]=arguments[s];return t.current==null?void 0:t.current(...r)},[])}function sr(){const e=l.useRef(null),t=l.useCallback((r,s)=>{e.current=setInterval(r,s)},[]),n=l.useCallback(()=>{e.current!==null&&(clearInterval(e.current),e.current=null)},[]);return[t,n]}function Xe(e,t){t===void 0&&(t=[e]);const n=l.useRef(e);return ue(()=>{n.current!==e&&(n.current=e)},t),n}function Ve(e,t){const n=l.useRef();return l.useMemo(()=>{const r=e(n.current);return n.current=r,r},[...t])}function rt(e){const t=jt(e),n=l.useRef(null),r=l.useCallback(s=>{s!==n.current&&(t==null||t(s,n.current)),n.current=s},[]);return[n,r]}function yt(e){const t=l.useRef();return l.useEffect(()=>{t.current=e},[e]),t.current}let pt={};function Ge(e,t){return l.useMemo(()=>{if(t)return t;const n=pt[e]==null?0:pt[e]+1;return pt[e]=n,e+"-"+n},[e,t])}function un(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),s=1;s<n;s++)r[s-1]=arguments[s];return r.reduce((o,a)=>{const c=Object.entries(a);for(const[u,d]of c){const h=o[u];h!=null&&(o[u]=h+e*d)}return o},{...t})}}const Me=un(1),Ue=un(-1);function or(e){return"clientX"in e&&"clientY"in e}function Rt(e){if(!e)return!1;const{KeyboardEvent:t}=q(e.target);return t&&e instanceof t}function ir(e){if(!e)return!1;const{TouchEvent:t}=q(e.target);return t&&e instanceof t}function wt(e){if(ir(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return or(e)?{x:e.clientX,y:e.clientY}:null}const Ye=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[Ye.Translate.toString(e),Ye.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),Yt="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function ar(e){return e.matches(Yt)?e:e.querySelector(Yt)}const lr={display:"none"};function cr(e){let{id:t,value:n}=e;return W.createElement("div",{id:t,style:lr},n)}function ur(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;const s={position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return W.createElement("div",{id:t,style:s,role:"status","aria-live":r,"aria-atomic":!0},n)}function dr(){const[e,t]=l.useState("");return{announce:l.useCallback(r=>{r!=null&&t(r)},[]),announcement:e}}const dn=l.createContext(null);function fr(e){const t=l.useContext(dn);l.useEffect(()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}function hr(){const[e]=l.useState(()=>new Set),t=l.useCallback(r=>(e.add(r),()=>e.delete(r)),[e]);return[l.useCallback(r=>{let{type:s,event:o}=r;e.forEach(a=>{var c;return(c=a[s])==null?void 0:c.call(a,o)})},[e]),t]}const gr={draggable:`
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  `},mr={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function pr(e){let{announcements:t=mr,container:n,hiddenTextDescribedById:r,screenReaderInstructions:s=gr}=e;const{announce:o,announcement:a}=dr(),c=Ge("DndLiveRegion"),[u,d]=l.useState(!1);if(l.useEffect(()=>{d(!0)},[]),fr(l.useMemo(()=>({onDragStart(f){let{active:g}=f;o(t.onDragStart({active:g}))},onDragMove(f){let{active:g,over:m}=f;t.onDragMove&&o(t.onDragMove({active:g,over:m}))},onDragOver(f){let{active:g,over:m}=f;o(t.onDragOver({active:g,over:m}))},onDragEnd(f){let{active:g,over:m}=f;o(t.onDragEnd({active:g,over:m}))},onDragCancel(f){let{active:g,over:m}=f;o(t.onDragCancel({active:g,over:m}))}}),[o,t])),!u)return null;const h=W.createElement(W.Fragment,null,W.createElement(cr,{id:r,value:s.draggable}),W.createElement(ur,{id:c,announcement:a}));return n?Be.createPortal(h,n):h}var P;(function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"})(P||(P={}));function st(){}function Wt(e,t){return l.useMemo(()=>({sensor:e,options:t??{}}),[e,t])}function br(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return l.useMemo(()=>[...t].filter(r=>r!=null),[...t])}const se=Object.freeze({x:0,y:0});function fn(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function hn(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function vr(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function qt(e){let{left:t,top:n,height:r,width:s}=e;return[{x:t,y:n},{x:t+s,y:n},{x:t,y:n+r},{x:t+s,y:n+r}]}function gn(e,t){if(!e||e.length===0)return null;const[n]=e;return n[t]}function Vt(e,t,n){return t===void 0&&(t=e.left),n===void 0&&(n=e.top),{x:t+e.width*.5,y:n+e.height*.5}}const xr=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const s=Vt(t,t.left,t.top),o=[];for(const a of r){const{id:c}=a,u=n.get(c);if(u){const d=fn(Vt(u),s);o.push({id:c,data:{droppableContainer:a,value:d}})}}return o.sort(hn)},yr=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const s=qt(t),o=[];for(const a of r){const{id:c}=a,u=n.get(c);if(u){const d=qt(u),h=s.reduce((g,m,x)=>g+fn(d[x],m),0),f=Number((h/4).toFixed(4));o.push({id:c,data:{droppableContainer:a,value:f}})}}return o.sort(hn)};function wr(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),s=Math.min(t.left+t.width,e.left+e.width),o=Math.min(t.top+t.height,e.top+e.height),a=s-r,c=o-n;if(r<s&&n<o){const u=t.width*t.height,d=e.width*e.height,h=a*c,f=h/(u+d-h);return Number(f.toFixed(4))}return 0}const Sr=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const s=[];for(const o of r){const{id:a}=o,c=n.get(a);if(c){const u=wr(c,t);u>0&&s.push({id:a,data:{droppableContainer:o,value:u}})}}return s.sort(vr)};function Dr(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}function mn(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:se}function Nr(e){return function(n){for(var r=arguments.length,s=new Array(r>1?r-1:0),o=1;o<r;o++)s[o-1]=arguments[o];return s.reduce((a,c)=>({...a,top:a.top+e*c.y,bottom:a.bottom+e*c.y,left:a.left+e*c.x,right:a.right+e*c.x}),{...n})}}const Cr=Nr(1);function Er(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}else if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}function jr(e,t,n){const r=Er(t);if(!r)return e;const{scaleX:s,scaleY:o,x:a,y:c}=r,u=e.left-a-(1-s)*parseFloat(n),d=e.top-c-(1-o)*parseFloat(n.slice(n.indexOf(" ")+1)),h=s?e.width/s:e.width,f=o?e.height/o:e.height;return{width:h,height:f,top:d,right:u+h,bottom:d+f,left:u}}const Rr={ignoreTransform:!1};function Ie(e,t){t===void 0&&(t=Rr);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:d,transformOrigin:h}=q(e).getComputedStyle(e);d&&(n=jr(n,d,h))}const{top:r,left:s,width:o,height:a,bottom:c,right:u}=n;return{top:r,left:s,width:o,height:a,bottom:c,right:u}}function Gt(e){return Ie(e,{ignoreTransform:!0})}function Tr(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}function Mr(e,t){return t===void 0&&(t=q(e).getComputedStyle(e)),t.position==="fixed"}function _r(e,t){t===void 0&&(t=q(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(s=>{const o=t[s];return typeof o=="string"?n.test(o):!1})}function lt(e,t){const n=[];function r(s){if(t!=null&&n.length>=t||!s)return n;if(Et(s)&&s.scrollingElement!=null&&!n.includes(s.scrollingElement))return n.push(s.scrollingElement),n;if(!qe(s)||cn(s)||n.includes(s))return n;const o=q(e).getComputedStyle(s);return s!==e&&_r(s,o)&&n.push(s),Mr(s,o)?n:r(s.parentNode)}return e?r(e):n}function pn(e){const[t]=lt(e,1);return t??null}function bt(e){return!at||!e?null:_e(e)?e:Ct(e)?Et(e)||e===Oe(e).scrollingElement?window:qe(e)?e:null:null}function bn(e){return _e(e)?e.scrollX:e.scrollLeft}function vn(e){return _e(e)?e.scrollY:e.scrollTop}function St(e){return{x:bn(e),y:vn(e)}}var $;(function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"})($||($={}));function xn(e){return!at||!e?!1:e===document.scrollingElement}function yn(e){const t={x:0,y:0},n=xn(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},s=e.scrollTop<=t.y,o=e.scrollLeft<=t.x,a=e.scrollTop>=r.y,c=e.scrollLeft>=r.x;return{isTop:s,isLeft:o,isBottom:a,isRight:c,maxScroll:r,minScroll:t}}const Or={x:.2,y:.2};function Ir(e,t,n,r,s){let{top:o,left:a,right:c,bottom:u}=n;r===void 0&&(r=10),s===void 0&&(s=Or);const{isTop:d,isBottom:h,isLeft:f,isRight:g}=yn(e),m={x:0,y:0},x={x:0,y:0},b={height:t.height*s.y,width:t.width*s.x};return!d&&o<=t.top+b.height?(m.y=$.Backward,x.y=r*Math.abs((t.top+b.height-o)/b.height)):!h&&u>=t.bottom-b.height&&(m.y=$.Forward,x.y=r*Math.abs((t.bottom-b.height-u)/b.height)),!g&&c>=t.right-b.width?(m.x=$.Forward,x.x=r*Math.abs((t.right-b.width-c)/b.width)):!f&&a<=t.left+b.width&&(m.x=$.Backward,x.x=r*Math.abs((t.left+b.width-a)/b.width)),{direction:m,speed:x}}function Ar(e){if(e===document.scrollingElement){const{innerWidth:o,innerHeight:a}=window;return{top:0,left:0,right:o,bottom:a,width:o,height:a}}const{top:t,left:n,right:r,bottom:s}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:s,width:e.clientWidth,height:e.clientHeight}}function wn(e){return e.reduce((t,n)=>Me(t,St(n)),se)}function kr(e){return e.reduce((t,n)=>t+bn(n),0)}function Lr(e){return e.reduce((t,n)=>t+vn(n),0)}function zr(e,t){if(t===void 0&&(t=Ie),!e)return;const{top:n,left:r,bottom:s,right:o}=t(e);pn(e)&&(s<=0||o<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const Pr=[["x",["left","right"],kr],["y",["top","bottom"],Lr]];class Tt{constructor(t,n){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const r=lt(n),s=wn(r);this.rect={...t},this.width=t.width,this.height=t.height;for(const[o,a,c]of Pr)for(const u of a)Object.defineProperty(this,u,{get:()=>{const d=c(r),h=s[o]-d;return this.rect[u]+h},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class Fe{constructor(t){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(n=>{var r;return(r=this.target)==null?void 0:r.removeEventListener(...n)})},this.target=t}add(t,n,r){var s;(s=this.target)==null||s.addEventListener(t,n,r),this.listeners.push([t,n,r])}}function $r(e){const{EventTarget:t}=q(e);return e instanceof t?e:Oe(e)}function vt(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return typeof t=="number"?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t?r>t.y:!1}var re;(function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"})(re||(re={}));function Jt(e){e.preventDefault()}function Br(e){e.stopPropagation()}var T;(function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"})(T||(T={}));const Sn={start:[T.Space,T.Enter],cancel:[T.Esc],end:[T.Space,T.Enter,T.Tab]},Fr=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case T.Right:return{...n,x:n.x+25};case T.Left:return{...n,x:n.x-25};case T.Down:return{...n,y:n.y+25};case T.Up:return{...n,y:n.y-25}}};class Mt{constructor(t){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=t;const{event:{target:n}}=t;this.props=t,this.listeners=new Fe(Oe(n)),this.windowListeners=new Fe(q(n)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(re.Resize,this.handleCancel),this.windowListeners.add(re.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(re.Keydown,this.handleKeyDown))}handleStart(){const{activeNode:t,onStart:n}=this.props,r=t.node.current;r&&zr(r),n(se)}handleKeyDown(t){if(Rt(t)){const{active:n,context:r,options:s}=this.props,{keyboardCodes:o=Sn,coordinateGetter:a=Fr,scrollBehavior:c="smooth"}=s,{code:u}=t;if(o.end.includes(u)){this.handleEnd(t);return}if(o.cancel.includes(u)){this.handleCancel(t);return}const{collisionRect:d}=r.current,h=d?{x:d.left,y:d.top}:se;this.referenceCoordinates||(this.referenceCoordinates=h);const f=a(t,{active:n,context:r.current,currentCoordinates:h});if(f){const g=Ue(f,h),m={x:0,y:0},{scrollableAncestors:x}=r.current;for(const b of x){const v=t.code,{isTop:w,isRight:S,isLeft:y,isBottom:N,maxScroll:j,minScroll:M}=yn(b),C=Ar(b),R={x:Math.min(v===T.Right?C.right-C.width/2:C.right,Math.max(v===T.Right?C.left:C.left+C.width/2,f.x)),y:Math.min(v===T.Down?C.bottom-C.height/2:C.bottom,Math.max(v===T.Down?C.top:C.top+C.height/2,f.y))},k=v===T.Right&&!S||v===T.Left&&!y,z=v===T.Down&&!N||v===T.Up&&!w;if(k&&R.x!==f.x){const A=b.scrollLeft+g.x,V=v===T.Right&&A<=j.x||v===T.Left&&A>=M.x;if(V&&!g.y){b.scrollTo({left:A,behavior:c});return}V?m.x=b.scrollLeft-A:m.x=v===T.Right?b.scrollLeft-j.x:b.scrollLeft-M.x,m.x&&b.scrollBy({left:-m.x,behavior:c});break}else if(z&&R.y!==f.y){const A=b.scrollTop+g.y,V=v===T.Down&&A<=j.y||v===T.Up&&A>=M.y;if(V&&!g.x){b.scrollTo({top:A,behavior:c});return}V?m.y=b.scrollTop-A:m.y=v===T.Down?b.scrollTop-j.y:b.scrollTop-M.y,m.y&&b.scrollBy({top:-m.y,behavior:c});break}}this.handleMove(t,Me(Ue(f,this.referenceCoordinates),m))}}}handleMove(t,n){const{onMove:r}=this.props;t.preventDefault(),r(n)}handleEnd(t){const{onEnd:n}=this.props;t.preventDefault(),this.detach(),n()}handleCancel(t){const{onCancel:n}=this.props;t.preventDefault(),this.detach(),n()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}Mt.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=Sn,onActivation:s}=t,{active:o}=n;const{code:a}=e.nativeEvent;if(r.start.includes(a)){const c=o.activatorNode.current;return c&&e.target!==c?!1:(e.preventDefault(),s==null||s({event:e.nativeEvent}),!0)}return!1}}];function Qt(e){return!!(e&&"distance"in e)}function Zt(e){return!!(e&&"delay"in e)}class _t{constructor(t,n,r){var s;r===void 0&&(r=$r(t.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=t,this.events=n;const{event:o}=t,{target:a}=o;this.props=t,this.events=n,this.document=Oe(a),this.documentListeners=new Fe(this.document),this.listeners=new Fe(r),this.windowListeners=new Fe(q(a)),this.initialCoordinates=(s=wt(o))!=null?s:se,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:t,props:{options:{activationConstraint:n,bypassActivationConstraint:r}}}=this;if(this.listeners.add(t.move.name,this.handleMove,{passive:!1}),this.listeners.add(t.end.name,this.handleEnd),t.cancel&&this.listeners.add(t.cancel.name,this.handleCancel),this.windowListeners.add(re.Resize,this.handleCancel),this.windowListeners.add(re.DragStart,Jt),this.windowListeners.add(re.VisibilityChange,this.handleCancel),this.windowListeners.add(re.ContextMenu,Jt),this.documentListeners.add(re.Keydown,this.handleKeydown),n){if(r!=null&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(Zt(n)){this.timeoutId=setTimeout(this.handleStart,n.delay),this.handlePending(n);return}if(Qt(n)){this.handlePending(n);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),this.timeoutId!==null&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(t,n){const{active:r,onPending:s}=this.props;s(r,t,this.initialCoordinates,n)}handleStart(){const{initialCoordinates:t}=this,{onStart:n}=this.props;t&&(this.activated=!0,this.documentListeners.add(re.Click,Br,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(re.SelectionChange,this.removeTextSelection),n(t))}handleMove(t){var n;const{activated:r,initialCoordinates:s,props:o}=this,{onMove:a,options:{activationConstraint:c}}=o;if(!s)return;const u=(n=wt(t))!=null?n:se,d=Ue(s,u);if(!r&&c){if(Qt(c)){if(c.tolerance!=null&&vt(d,c.tolerance))return this.handleCancel();if(vt(d,c.distance))return this.handleStart()}if(Zt(c)&&vt(d,c.tolerance))return this.handleCancel();this.handlePending(c,d);return}t.cancelable&&t.preventDefault(),a(u)}handleEnd(){const{onAbort:t,onEnd:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleCancel(){const{onAbort:t,onCancel:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleKeydown(t){t.code===T.Esc&&this.handleCancel()}removeTextSelection(){var t;(t=this.document.getSelection())==null||t.removeAllRanges()}}const Hr={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class Ot extends _t{constructor(t){const{event:n}=t,r=Oe(n.target);super(t,Hr,r)}}Ot.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!n.isPrimary||n.button!==0?!1:(r==null||r({event:n}),!0)}}];const Kr={move:{name:"mousemove"},end:{name:"mouseup"}};var Dt;(function(e){e[e.RightClick=2]="RightClick"})(Dt||(Dt={}));class Xr extends _t{constructor(t){super(t,Kr,Oe(t.event.target))}}Xr.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button===Dt.RightClick?!1:(r==null||r({event:n}),!0)}}];const xt={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class Ur extends _t{constructor(t){super(t,xt)}static setup(){return window.addEventListener(xt.move.name,t,{capture:!1,passive:!1}),function(){window.removeEventListener(xt.move.name,t)};function t(){}}}Ur.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:s}=n;return s.length>1?!1:(r==null||r({event:n}),!0)}}];var He;(function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"})(He||(He={}));var ot;(function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"})(ot||(ot={}));function Yr(e){let{acceleration:t,activator:n=He.Pointer,canScroll:r,draggingRect:s,enabled:o,interval:a=5,order:c=ot.TreeOrder,pointerCoordinates:u,scrollableAncestors:d,scrollableAncestorRects:h,delta:f,threshold:g}=e;const m=qr({delta:f,disabled:!o}),[x,b]=sr(),v=l.useRef({x:0,y:0}),w=l.useRef({x:0,y:0}),S=l.useMemo(()=>{switch(n){case He.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case He.DraggableRect:return s}},[n,s,u]),y=l.useRef(null),N=l.useCallback(()=>{const M=y.current;if(!M)return;const C=v.current.x*w.current.x,R=v.current.y*w.current.y;M.scrollBy(C,R)},[]),j=l.useMemo(()=>c===ot.TreeOrder?[...d].reverse():d,[c,d]);l.useEffect(()=>{if(!o||!d.length||!S){b();return}for(const M of j){if((r==null?void 0:r(M))===!1)continue;const C=d.indexOf(M),R=h[C];if(!R)continue;const{direction:k,speed:z}=Ir(M,R,S,t,g);for(const A of["x","y"])m[A][k[A]]||(z[A]=0,k[A]=0);if(z.x>0||z.y>0){b(),y.current=M,x(N,a),v.current=z,w.current=k;return}}v.current={x:0,y:0},w.current={x:0,y:0},b()},[t,N,r,b,o,a,JSON.stringify(S),JSON.stringify(m),x,d,j,h,JSON.stringify(g)])}const Wr={x:{[$.Backward]:!1,[$.Forward]:!1},y:{[$.Backward]:!1,[$.Forward]:!1}};function qr(e){let{delta:t,disabled:n}=e;const r=yt(t);return Ve(s=>{if(n||!r||!s)return Wr;const o={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[$.Backward]:s.x[$.Backward]||o.x===-1,[$.Forward]:s.x[$.Forward]||o.x===1},y:{[$.Backward]:s.y[$.Backward]||o.y===-1,[$.Forward]:s.y[$.Forward]||o.y===1}}},[n,t,r])}function Vr(e,t){const n=t!=null?e.get(t):void 0,r=n?n.node.current:null;return Ve(s=>{var o;return t==null?null:(o=r??s)!=null?o:null},[r,t])}function Gr(e,t){return l.useMemo(()=>e.reduce((n,r)=>{const{sensor:s}=r,o=s.activators.map(a=>({eventName:a.eventName,handler:t(a.handler,r)}));return[...n,...o]},[]),[e,t])}var We;(function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"})(We||(We={}));var Nt;(function(e){e.Optimized="optimized"})(Nt||(Nt={}));const en=new Map;function Jr(e,t){let{dragging:n,dependencies:r,config:s}=t;const[o,a]=l.useState(null),{frequency:c,measure:u,strategy:d}=s,h=l.useRef(e),f=v(),g=Xe(f),m=l.useCallback(function(w){w===void 0&&(w=[]),!g.current&&a(S=>S===null?w:S.concat(w.filter(y=>!S.includes(y))))},[g]),x=l.useRef(null),b=Ve(w=>{if(f&&!n)return en;if(!w||w===en||h.current!==e||o!=null){const S=new Map;for(let y of e){if(!y)continue;if(o&&o.length>0&&!o.includes(y.id)&&y.rect.current){S.set(y.id,y.rect.current);continue}const N=y.node.current,j=N?new Tt(u(N),N):null;y.rect.current=j,j&&S.set(y.id,j)}return S}return w},[e,o,n,f,u]);return l.useEffect(()=>{h.current=e},[e]),l.useEffect(()=>{f||m()},[n,f]),l.useEffect(()=>{o&&o.length>0&&a(null)},[JSON.stringify(o)]),l.useEffect(()=>{f||typeof c!="number"||x.current!==null||(x.current=setTimeout(()=>{m(),x.current=null},c))},[c,f,m,...r]),{droppableRects:b,measureDroppableContainers:m,measuringScheduled:o!=null};function v(){switch(d){case We.Always:return!1;case We.BeforeDragging:return n;default:return!n}}}function Dn(e,t){return Ve(n=>e?n||(typeof t=="function"?t(e):e):null,[t,e])}function Qr(e,t){return Dn(e,t)}function Zr(e){let{callback:t,disabled:n}=e;const r=jt(t),s=l.useMemo(()=>{if(n||typeof window>"u"||typeof window.MutationObserver>"u")return;const{MutationObserver:o}=window;return new o(r)},[r,n]);return l.useEffect(()=>()=>s==null?void 0:s.disconnect(),[s]),s}function ct(e){let{callback:t,disabled:n}=e;const r=jt(t),s=l.useMemo(()=>{if(n||typeof window>"u"||typeof window.ResizeObserver>"u")return;const{ResizeObserver:o}=window;return new o(r)},[n]);return l.useEffect(()=>()=>s==null?void 0:s.disconnect(),[s]),s}function es(e){return new Tt(Ie(e),e)}function tn(e,t,n){t===void 0&&(t=es);const[r,s]=l.useState(null);function o(){s(u=>{if(!e)return null;if(e.isConnected===!1){var d;return(d=u??n)!=null?d:null}const h=t(e);return JSON.stringify(u)===JSON.stringify(h)?u:h})}const a=Zr({callback(u){if(e)for(const d of u){const{type:h,target:f}=d;if(h==="childList"&&f instanceof HTMLElement&&f.contains(e)){o();break}}}}),c=ct({callback:o});return ue(()=>{o(),e?(c==null||c.observe(e),a==null||a.observe(document.body,{childList:!0,subtree:!0})):(c==null||c.disconnect(),a==null||a.disconnect())},[e]),r}function ts(e){const t=Dn(e);return mn(e,t)}const nn=[];function ns(e){const t=l.useRef(e),n=Ve(r=>e?r&&r!==nn&&e&&t.current&&e.parentNode===t.current.parentNode?r:lt(e):nn,[e]);return l.useEffect(()=>{t.current=e},[e]),n}function rs(e){const[t,n]=l.useState(null),r=l.useRef(e),s=l.useCallback(o=>{const a=bt(o.target);a&&n(c=>c?(c.set(a,St(a)),new Map(c)):null)},[]);return l.useEffect(()=>{const o=r.current;if(e!==o){a(o);const c=e.map(u=>{const d=bt(u);return d?(d.addEventListener("scroll",s,{passive:!0}),[d,St(d)]):null}).filter(u=>u!=null);n(c.length?new Map(c):null),r.current=e}return()=>{a(e),a(o)};function a(c){c.forEach(u=>{const d=bt(u);d==null||d.removeEventListener("scroll",s)})}},[s,e]),l.useMemo(()=>e.length?t?Array.from(t.values()).reduce((o,a)=>Me(o,a),se):wn(e):se,[e,t])}function rn(e,t){t===void 0&&(t=[]);const n=l.useRef(null);return l.useEffect(()=>{n.current=null},t),l.useEffect(()=>{const r=e!==se;r&&!n.current&&(n.current=e),!r&&n.current&&(n.current=null)},[e]),n.current?Ue(e,n.current):se}function ss(e){l.useEffect(()=>{if(!at)return;const t=e.map(n=>{let{sensor:r}=n;return r.setup==null?void 0:r.setup()});return()=>{for(const n of t)n==null||n()}},e.map(t=>{let{sensor:n}=t;return n}))}function os(e,t){return l.useMemo(()=>e.reduce((n,r)=>{let{eventName:s,handler:o}=r;return n[s]=a=>{o(a,t)},n},{}),[e,t])}function Nn(e){return l.useMemo(()=>e?Tr(e):null,[e])}const sn=[];function is(e,t){t===void 0&&(t=Ie);const[n]=e,r=Nn(n?q(n):null),[s,o]=l.useState(sn);function a(){o(()=>e.length?e.map(u=>xn(u)?r:new Tt(t(u),u)):sn)}const c=ct({callback:a});return ue(()=>{c==null||c.disconnect(),a(),e.forEach(u=>c==null?void 0:c.observe(u))},[e]),s}function as(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return qe(t)?t:e}function ls(e){let{measure:t}=e;const[n,r]=l.useState(null),s=l.useCallback(d=>{for(const{target:h}of d)if(qe(h)){r(f=>{const g=t(h);return f?{...f,width:g.width,height:g.height}:g});break}},[t]),o=ct({callback:s}),a=l.useCallback(d=>{const h=as(d);o==null||o.disconnect(),h&&(o==null||o.observe(h)),r(h?t(h):null)},[t,o]),[c,u]=rt(a);return l.useMemo(()=>({nodeRef:c,rect:n,setRef:u}),[n,c,u])}const cs=[{sensor:Ot,options:{}},{sensor:Mt,options:{}}],us={current:{}},tt={draggable:{measure:Gt},droppable:{measure:Gt,strategy:We.WhileDragging,frequency:Nt.Optimized},dragOverlay:{measure:Ie}};class Ke extends Map{get(t){var n;return t!=null&&(n=super.get(t))!=null?n:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(t=>{let{disabled:n}=t;return!n})}getNodeFor(t){var n,r;return(n=(r=this.get(t))==null?void 0:r.node.current)!=null?n:void 0}}const ds={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new Ke,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:st},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:tt,measureDroppableContainers:st,windowRect:null,measuringScheduled:!1},fs={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:st,draggableNodes:new Map,over:null,measureDroppableContainers:st},ut=l.createContext(fs),Cn=l.createContext(ds);function hs(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new Ke}}}function gs(e,t){switch(t.type){case P.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case P.DragMove:return e.draggable.active==null?e:{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case P.DragEnd:case P.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case P.RegisterDroppable:{const{element:n}=t,{id:r}=n,s=new Ke(e.droppable.containers);return s.set(r,n),{...e,droppable:{...e.droppable,containers:s}}}case P.SetDroppableDisabled:{const{id:n,key:r,disabled:s}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const a=new Ke(e.droppable.containers);return a.set(n,{...o,disabled:s}),{...e,droppable:{...e.droppable,containers:a}}}case P.UnregisterDroppable:{const{id:n,key:r}=t,s=e.droppable.containers.get(n);if(!s||r!==s.key)return e;const o=new Ke(e.droppable.containers);return o.delete(n),{...e,droppable:{...e.droppable,containers:o}}}default:return e}}function ms(e){let{disabled:t}=e;const{active:n,activatorEvent:r,draggableNodes:s}=l.useContext(ut),o=yt(r),a=yt(n==null?void 0:n.id);return l.useEffect(()=>{if(!t&&!r&&o&&a!=null){if(!Rt(o)||document.activeElement===o.target)return;const c=s.get(a);if(!c)return;const{activatorNode:u,node:d}=c;if(!u.current&&!d.current)return;requestAnimationFrame(()=>{for(const h of[u.current,d.current]){if(!h)continue;const f=ar(h);if(f){f.focus();break}}})}},[r,t,s,a,o]),null}function ps(e,t){let{transform:n,...r}=t;return e!=null&&e.length?e.reduce((s,o)=>o({transform:s,...r}),n):n}function bs(e){return l.useMemo(()=>({draggable:{...tt.draggable,...e==null?void 0:e.draggable},droppable:{...tt.droppable,...e==null?void 0:e.droppable},dragOverlay:{...tt.dragOverlay,...e==null?void 0:e.dragOverlay}}),[e==null?void 0:e.draggable,e==null?void 0:e.droppable,e==null?void 0:e.dragOverlay])}function vs(e){let{activeNode:t,measure:n,initialRect:r,config:s=!0}=e;const o=l.useRef(!1),{x:a,y:c}=typeof s=="boolean"?{x:s,y:s}:s;ue(()=>{if(!a&&!c||!t){o.current=!1;return}if(o.current||!r)return;const d=t==null?void 0:t.node.current;if(!d||d.isConnected===!1)return;const h=n(d),f=mn(h,r);if(a||(f.x=0),c||(f.y=0),o.current=!0,Math.abs(f.x)>0||Math.abs(f.y)>0){const g=pn(d);g&&g.scrollBy({top:f.y,left:f.x})}},[t,a,c,r,n])}const En=l.createContext({...se,scaleX:1,scaleY:1});var ve;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"})(ve||(ve={}));const xs=l.memo(function(t){var n,r,s,o;let{id:a,accessibility:c,autoScroll:u=!0,children:d,sensors:h=cs,collisionDetection:f=Sr,measuring:g,modifiers:m,...x}=t;const b=l.useReducer(gs,void 0,hs),[v,w]=b,[S,y]=hr(),[N,j]=l.useState(ve.Uninitialized),M=N===ve.Initialized,{draggable:{active:C,nodes:R,translate:k},droppable:{containers:z}}=v,A=C!=null?R.get(C):null,V=l.useRef({initial:null,translated:null}),G=l.useMemo(()=>{var H;return C!=null?{id:C,data:(H=A==null?void 0:A.data)!=null?H:us,rect:V}:null},[C,A]),J=l.useRef(null),[xe,ye]=l.useState(null),[K,E]=l.useState(null),D=Xe(x,Object.values(x)),p=Ge("DndDescribedBy",a),L=l.useMemo(()=>z.getEnabled(),[z]),I=bs(g),{droppableRects:O,measureDroppableContainers:B,measuringScheduled:X}=Jr(L,{dragging:M,dependencies:[k.x,k.y],config:I.droppable}),U=Vr(R,C),we=l.useMemo(()=>K?wt(K):null,[K]),oe=$n(),Y=Qr(U,I.draggable.measure);vs({activeNode:C!=null?R.get(C):null,config:oe.layoutShiftCompensation,initialRect:Y,measure:I.draggable.measure});const _=tn(U,I.draggable.measure,Y),Ae=tn(U?U.parentElement:null),ie=l.useRef({activatorEvent:null,active:null,activeNode:U,collisionRect:null,collisions:null,droppableRects:O,draggableNodes:R,draggingNode:null,draggingNodeRect:null,droppableContainers:z,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),Se=z.getNodeFor((n=ie.current.over)==null?void 0:n.id),de=ls({measure:I.dragOverlay.measure}),De=(r=de.nodeRef.current)!=null?r:U,Ne=M?(s=de.rect)!=null?s:_:null,At=!!(de.nodeRef.current&&de.rect),kt=ts(At?null:_),dt=Nn(De?q(De):null),he=ns(M?Se??U:null),Je=is(he),Qe=ps(m,{transform:{x:k.x-kt.x,y:k.y-kt.y,scaleX:1,scaleY:1},activatorEvent:K,active:G,activeNodeRect:_,containerNodeRect:Ae,draggingNodeRect:Ne,over:ie.current.over,overlayNodeRect:de.rect,scrollableAncestors:he,scrollableAncestorRects:Je,windowRect:dt}),Lt=we?Me(we,k):null,zt=rs(he),On=rn(zt),In=rn(zt,[_]),Ce=Me(Qe,On),Ee=Ne?Cr(Ne,Qe):null,ke=G&&Ee?f({active:G,collisionRect:Ee,droppableRects:O,droppableContainers:L,pointerCoordinates:Lt}):null,Pt=gn(ke,"id"),[ge,$t]=l.useState(null),An=At?Qe:Me(Qe,In),kn=Dr(An,(o=ge==null?void 0:ge.rect)!=null?o:null,_),ft=l.useRef(null),Bt=l.useCallback((H,Q)=>{let{sensor:Z,options:me}=Q;if(J.current==null)return;const ne=R.get(J.current);if(!ne)return;const ee=H.nativeEvent,ae=new Z({active:J.current,activeNode:ne,event:ee,options:me,context:ie,onAbort(F){if(!R.get(F))return;const{onDragAbort:le}=D.current,fe={id:F};le==null||le(fe),S({type:"onDragAbort",event:fe})},onPending(F,pe,le,fe){if(!R.get(F))return;const{onDragPending:ze}=D.current,be={id:F,constraint:pe,initialCoordinates:le,offset:fe};ze==null||ze(be),S({type:"onDragPending",event:be})},onStart(F){const pe=J.current;if(pe==null)return;const le=R.get(pe);if(!le)return;const{onDragStart:fe}=D.current,Le={activatorEvent:ee,active:{id:pe,data:le.data,rect:V}};Be.unstable_batchedUpdates(()=>{fe==null||fe(Le),j(ve.Initializing),w({type:P.DragStart,initialCoordinates:F,active:pe}),S({type:"onDragStart",event:Le}),ye(ft.current),E(ee)})},onMove(F){w({type:P.DragMove,coordinates:F})},onEnd:je(P.DragEnd),onCancel:je(P.DragCancel)});ft.current=ae;function je(F){return async function(){const{active:le,collisions:fe,over:Le,scrollAdjustedTranslate:ze}=ie.current;let be=null;if(le&&ze){const{cancelDrop:Pe}=D.current;be={activatorEvent:ee,active:le,collisions:fe,delta:ze,over:Le},F===P.DragEnd&&typeof Pe=="function"&&await Promise.resolve(Pe(be))&&(F=P.DragCancel)}J.current=null,Be.unstable_batchedUpdates(()=>{w({type:F}),j(ve.Uninitialized),$t(null),ye(null),E(null),ft.current=null;const Pe=F===P.DragEnd?"onDragEnd":"onDragCancel";if(be){const ht=D.current[Pe];ht==null||ht(be),S({type:Pe,event:be})}})}}},[R]),Ln=l.useCallback((H,Q)=>(Z,me)=>{const ne=Z.nativeEvent,ee=R.get(me);if(J.current!==null||!ee||ne.dndKit||ne.defaultPrevented)return;const ae={active:ee};H(Z,Q.options,ae)===!0&&(ne.dndKit={capturedBy:Q.sensor},J.current=me,Bt(Z,Q))},[R,Bt]),Ft=Gr(h,Ln);ss(h),ue(()=>{_&&N===ve.Initializing&&j(ve.Initialized)},[_,N]),l.useEffect(()=>{const{onDragMove:H}=D.current,{active:Q,activatorEvent:Z,collisions:me,over:ne}=ie.current;if(!Q||!Z)return;const ee={active:Q,activatorEvent:Z,collisions:me,delta:{x:Ce.x,y:Ce.y},over:ne};Be.unstable_batchedUpdates(()=>{H==null||H(ee),S({type:"onDragMove",event:ee})})},[Ce.x,Ce.y]),l.useEffect(()=>{const{active:H,activatorEvent:Q,collisions:Z,droppableContainers:me,scrollAdjustedTranslate:ne}=ie.current;if(!H||J.current==null||!Q||!ne)return;const{onDragOver:ee}=D.current,ae=me.get(Pt),je=ae&&ae.rect.current?{id:ae.id,rect:ae.rect.current,data:ae.data,disabled:ae.disabled}:null,F={active:H,activatorEvent:Q,collisions:Z,delta:{x:ne.x,y:ne.y},over:je};Be.unstable_batchedUpdates(()=>{$t(je),ee==null||ee(F),S({type:"onDragOver",event:F})})},[Pt]),ue(()=>{ie.current={activatorEvent:K,active:G,activeNode:U,collisionRect:Ee,collisions:ke,droppableRects:O,draggableNodes:R,draggingNode:De,draggingNodeRect:Ne,droppableContainers:z,over:ge,scrollableAncestors:he,scrollAdjustedTranslate:Ce},V.current={initial:Ne,translated:Ee}},[G,U,ke,Ee,R,De,Ne,O,z,ge,he,Ce]),Yr({...oe,delta:k,draggingRect:Ee,pointerCoordinates:Lt,scrollableAncestors:he,scrollableAncestorRects:Je});const zn=l.useMemo(()=>({active:G,activeNode:U,activeNodeRect:_,activatorEvent:K,collisions:ke,containerNodeRect:Ae,dragOverlay:de,draggableNodes:R,droppableContainers:z,droppableRects:O,over:ge,measureDroppableContainers:B,scrollableAncestors:he,scrollableAncestorRects:Je,measuringConfiguration:I,measuringScheduled:X,windowRect:dt}),[G,U,_,K,ke,Ae,de,R,z,O,ge,B,he,Je,I,X,dt]),Pn=l.useMemo(()=>({activatorEvent:K,activators:Ft,active:G,activeNodeRect:_,ariaDescribedById:{draggable:p},dispatch:w,draggableNodes:R,over:ge,measureDroppableContainers:B}),[K,Ft,G,_,w,p,R,ge,B]);return W.createElement(dn.Provider,{value:y},W.createElement(ut.Provider,{value:Pn},W.createElement(Cn.Provider,{value:zn},W.createElement(En.Provider,{value:kn},d)),W.createElement(ms,{disabled:(c==null?void 0:c.restoreFocus)===!1})),W.createElement(pr,{...c,hiddenTextDescribedById:p}));function $n(){const H=(xe==null?void 0:xe.autoScrollEnabled)===!1,Q=typeof u=="object"?u.enabled===!1:u===!1,Z=M&&!H&&!Q;return typeof u=="object"?{...u,enabled:Z}:{enabled:Z}}}),ys=l.createContext(null),on="button",ws="Draggable";function Ss(e){let{id:t,data:n,disabled:r=!1,attributes:s}=e;const o=Ge(ws),{activators:a,activatorEvent:c,active:u,activeNodeRect:d,ariaDescribedById:h,draggableNodes:f,over:g}=l.useContext(ut),{role:m=on,roleDescription:x="draggable",tabIndex:b=0}=s??{},v=(u==null?void 0:u.id)===t,w=l.useContext(v?En:ys),[S,y]=rt(),[N,j]=rt(),M=os(a,t),C=Xe(n);ue(()=>(f.set(t,{id:t,key:o,node:S,activatorNode:N,data:C}),()=>{const k=f.get(t);k&&k.key===o&&f.delete(t)}),[f,t]);const R=l.useMemo(()=>({role:m,tabIndex:b,"aria-disabled":r,"aria-pressed":v&&m===on?!0:void 0,"aria-roledescription":x,"aria-describedby":h.draggable}),[r,m,b,v,x,h.draggable]);return{active:u,activatorEvent:c,activeNodeRect:d,attributes:R,isDragging:v,listeners:r?void 0:M,node:S,over:g,setNodeRef:y,setActivatorNodeRef:j,transform:w}}function Ds(){return l.useContext(Cn)}const Ns="Droppable",Cs={timeout:25};function Es(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:s}=e;const o=Ge(Ns),{active:a,dispatch:c,over:u,measureDroppableContainers:d}=l.useContext(ut),h=l.useRef({disabled:n}),f=l.useRef(!1),g=l.useRef(null),m=l.useRef(null),{disabled:x,updateMeasurementsFor:b,timeout:v}={...Cs,...s},w=Xe(b??r),S=l.useCallback(()=>{if(!f.current){f.current=!0;return}m.current!=null&&clearTimeout(m.current),m.current=setTimeout(()=>{d(Array.isArray(w.current)?w.current:[w.current]),m.current=null},v)},[v]),y=ct({callback:S,disabled:x||!a}),N=l.useCallback((R,k)=>{y&&(k&&(y.unobserve(k),f.current=!1),R&&y.observe(R))},[y]),[j,M]=rt(N),C=Xe(t);return l.useEffect(()=>{!y||!j.current||(y.disconnect(),f.current=!1,y.observe(j.current))},[j,y]),l.useEffect(()=>(c({type:P.RegisterDroppable,element:{id:r,key:o,disabled:n,node:j,rect:g,data:C}}),()=>c({type:P.UnregisterDroppable,key:o,id:r})),[r]),l.useEffect(()=>{n!==h.current.disabled&&(c({type:P.SetDroppableDisabled,id:r,key:o,disabled:n}),h.current.disabled=n)},[r,o,n,c]),{active:a,rect:g,isOver:(u==null?void 0:u.id)===r,node:j,over:u,setNodeRef:M}}function It(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function js(e,t){return e.reduce((n,r,s)=>{const o=t.get(r);return o&&(n[s]=o),n},Array(e.length))}function Ze(e){return e!==null&&e>=0}function Rs(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function Ts(e){return typeof e=="boolean"?{draggable:e,droppable:e}:e}const jn=e=>{let{rects:t,activeIndex:n,overIndex:r,index:s}=e;const o=It(t,r,n),a=t[s],c=o[s];return!c||!a?null:{x:c.left-a.left,y:c.top-a.top,scaleX:c.width/a.width,scaleY:c.height/a.height}},et={scaleX:1,scaleY:1},Ms=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:s,rects:o,overIndex:a}=e;const c=(t=o[n])!=null?t:r;if(!c)return null;if(s===n){const d=o[a];return d?{x:0,y:n<a?d.top+d.height-(c.top+c.height):d.top-c.top,...et}:null}const u=_s(o,s,n);return s>n&&s<=a?{x:0,y:-c.height-u,...et}:s<n&&s>=a?{x:0,y:c.height+u,...et}:{x:0,y:0,...et}};function _s(e,t,n){const r=e[t],s=e[t-1],o=e[t+1];return r?n<t?s?r.top-(s.top+s.height):o?o.top-(r.top+r.height):0:o?o.top-(r.top+r.height):s?r.top-(s.top+s.height):0:0}const Rn="Sortable",Tn=W.createContext({activeIndex:-1,containerId:Rn,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:jn,disabled:{draggable:!1,droppable:!1}});function Os(e){let{children:t,id:n,items:r,strategy:s=jn,disabled:o=!1}=e;const{active:a,dragOverlay:c,droppableRects:u,over:d,measureDroppableContainers:h}=Ds(),f=Ge(Rn,n),g=c.rect!==null,m=l.useMemo(()=>r.map(M=>typeof M=="object"&&"id"in M?M.id:M),[r]),x=a!=null,b=a?m.indexOf(a.id):-1,v=d?m.indexOf(d.id):-1,w=l.useRef(m),S=!Rs(m,w.current),y=v!==-1&&b===-1||S,N=Ts(o);ue(()=>{S&&x&&h(m)},[S,m,x,h]),l.useEffect(()=>{w.current=m},[m]);const j=l.useMemo(()=>({activeIndex:b,containerId:f,disabled:N,disableTransforms:y,items:m,overIndex:v,useDragOverlay:g,sortedRects:js(m,u),strategy:s}),[b,f,N.draggable,N.droppable,y,m,v,u,g,s]);return W.createElement(Tn.Provider,{value:j},t)}const Is=e=>{let{id:t,items:n,activeIndex:r,overIndex:s}=e;return It(n,r,s).indexOf(t)},As=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:s,items:o,newIndex:a,previousItems:c,previousContainerId:u,transition:d}=e;return!d||!r||c!==o&&s===a?!1:n?!0:a!==s&&t===u},ks={duration:200,easing:"ease"},Mn="transform",Ls=Ye.Transition.toString({property:Mn,duration:0,easing:"linear"}),zs={roleDescription:"sortable"};function Ps(e){let{disabled:t,index:n,node:r,rect:s}=e;const[o,a]=l.useState(null),c=l.useRef(n);return ue(()=>{if(!t&&n!==c.current&&r.current){const u=s.current;if(u){const d=Ie(r.current,{ignoreTransform:!0}),h={x:u.left-d.left,y:u.top-d.top,scaleX:u.width/d.width,scaleY:u.height/d.height};(h.x||h.y)&&a(h)}}n!==c.current&&(c.current=n)},[t,n,r,s]),l.useEffect(()=>{o&&a(null)},[o]),o}function $s(e){let{animateLayoutChanges:t=As,attributes:n,disabled:r,data:s,getNewIndex:o=Is,id:a,strategy:c,resizeObserverConfig:u,transition:d=ks}=e;const{items:h,containerId:f,activeIndex:g,disabled:m,disableTransforms:x,sortedRects:b,overIndex:v,useDragOverlay:w,strategy:S}=l.useContext(Tn),y=Bs(r,m),N=h.indexOf(a),j=l.useMemo(()=>({sortable:{containerId:f,index:N,items:h},...s}),[f,s,N,h]),M=l.useMemo(()=>h.slice(h.indexOf(a)),[h,a]),{rect:C,node:R,isOver:k,setNodeRef:z}=Es({id:a,data:j,disabled:y.droppable,resizeObserverConfig:{updateMeasurementsFor:M,...u}}),{active:A,activatorEvent:V,activeNodeRect:G,attributes:J,setNodeRef:xe,listeners:ye,isDragging:K,over:E,setActivatorNodeRef:D,transform:p}=Ss({id:a,data:j,attributes:{...zs,...n},disabled:y.draggable}),L=rr(z,xe),I=!!A,O=I&&!x&&Ze(g)&&Ze(v),B=!w&&K,X=B&&O?p:null,we=O?X??(c??S)({rects:b,activeNodeRect:G,activeIndex:g,overIndex:v,index:N}):null,oe=Ze(g)&&Ze(v)?o({id:a,items:h,activeIndex:g,overIndex:v}):N,Y=A==null?void 0:A.id,_=l.useRef({activeId:Y,items:h,newIndex:oe,containerId:f}),Ae=h!==_.current.items,ie=t({active:A,containerId:f,isDragging:K,isSorting:I,id:a,index:N,items:h,newIndex:_.current.newIndex,previousItems:_.current.items,previousContainerId:_.current.containerId,transition:d,wasDragging:_.current.activeId!=null}),Se=Ps({disabled:!ie,index:N,node:R,rect:C});return l.useEffect(()=>{I&&_.current.newIndex!==oe&&(_.current.newIndex=oe),f!==_.current.containerId&&(_.current.containerId=f),h!==_.current.items&&(_.current.items=h)},[I,oe,f,h]),l.useEffect(()=>{if(Y===_.current.activeId)return;if(Y!=null&&_.current.activeId==null){_.current.activeId=Y;return}const De=setTimeout(()=>{_.current.activeId=Y},50);return()=>clearTimeout(De)},[Y]),{active:A,activeIndex:g,attributes:J,data:j,rect:C,index:N,newIndex:oe,items:h,isOver:k,isSorting:I,isDragging:K,listeners:ye,node:R,overIndex:v,over:E,setNodeRef:L,setActivatorNodeRef:D,setDroppableNodeRef:z,setDraggableNodeRef:xe,transform:Se??we,transition:de()};function de(){if(Se||Ae&&_.current.newIndex===N)return Ls;if(!(B&&!Rt(V)||!d)&&(I||ie))return Ye.Transition.toString({...d,property:Mn})}}function Bs(e,t){var n,r;return typeof e=="boolean"?{draggable:e,droppable:!1}:{draggable:(n=e==null?void 0:e.draggable)!=null?n:t.draggable,droppable:(r=e==null?void 0:e.droppable)!=null?r:t.droppable}}function it(e){if(!e)return!1;const t=e.data.current;return!!(t&&"sortable"in t&&typeof t.sortable=="object"&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable)}const Fs=[T.Down,T.Right,T.Up,T.Left],Hs=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:s,droppableContainers:o,over:a,scrollableAncestors:c}}=t;if(Fs.includes(e.code)){if(e.preventDefault(),!n||!r)return;const u=[];o.getEnabled().forEach(f=>{if(!f||f!=null&&f.disabled)return;const g=s.get(f.id);if(g)switch(e.code){case T.Down:r.top<g.top&&u.push(f);break;case T.Up:r.top>g.top&&u.push(f);break;case T.Left:r.left>g.left&&u.push(f);break;case T.Right:r.left<g.left&&u.push(f);break}});const d=yr({collisionRect:r,droppableRects:s,droppableContainers:u});let h=gn(d,"id");if(h===(a==null?void 0:a.id)&&d.length>1&&(h=d[1].id),h!=null){const f=o.get(n.id),g=o.get(h),m=g?s.get(g.id):null,x=g==null?void 0:g.node.current;if(x&&m&&f&&g){const v=lt(x).some((M,C)=>c[C]!==M),w=_n(f,g),S=Ks(f,g),y=v||!w?{x:0,y:0}:{x:S?r.width-m.width:0,y:S?r.height-m.height:0},N={x:m.left,y:m.top};return y.x&&y.y?N:Ue(N,y)}}}};function _n(e,t){return!it(e)||!it(t)?!1:e.data.current.sortable.containerId===t.data.current.sortable.containerId}function Ks(e,t){return!it(e)||!it(t)||!_n(e,t)?!1:e.data.current.sortable.index<t.data.current.sortable.index}const Xs=(e,t)=>{let n=new Date(t);n<e&&n.setDate(n.getDate()+1);const r=new Date(e);r.setHours(22,0,0,0);const s=new Date(e);s.setHours(6,0,0,0),s<r&&s.setDate(s.getDate()+1);const o=new Date(r);o.setDate(o.getDate()+1);const a=new Date(s);a.setDate(a.getDate()+1);let c=0;if(e<=s&&n>=r){const u=e>r?e:r,d=n<s?n:s;d>u&&(c+=nt(d,u))}if(e<=a&&n>=o){const u=e>o?e:o,d=n<a?n:a;d>u&&(c+=nt(d,u))}return Math.round(c/60*2)/2},Us=({entry:e,index:t,updateTimeEntry:n,removeTimeEntryRow:r,employees:s,isLoading:o})=>{const{attributes:a,listeners:c,setNodeRef:u,transform:d,transition:h}=$s({id:`entry-${t}`}),f={transform:Ye.Transform.toString(d),transition:h};return i.jsxs(ln,{className:"overflow-hidden",ref:u,style:f,children:[i.jsxs("div",{className:"bg-gray-50 px-4 py-2 border-b flex justify-between items-center",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsxs("span",{className:"bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mr-2 cursor-move",...a,...c,children:["#",t+1]}),i.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Time Entry"})]}),i.jsx(ce,{color:"light",size:"xs",onClick:()=>r(t),disabled:!1,className:"text-gray-500 hover:text-red-500",children:i.jsx(an,{className:"h-4 w-4"})})]}),i.jsxs("div",{className:"p-4",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[i.jsxs("div",{children:[i.jsxs("div",{className:"mb-1 flex items-center",children:[i.jsx(Vn,{className:"h-4 w-4 text-gray-500 mr-1"}),i.jsx(Te,{htmlFor:`employeeId-${t}`,value:"Employee",className:"text-sm"})]}),i.jsx(tr,{employees:s,selectedEmployeeId:e.employee_id,onSelect:g=>n(t,"employee_id",g),required:!0,isLoading:o,placeholder:"Search for an employee..."})]}),i.jsxs("div",{children:[i.jsxs("div",{className:"mb-1 flex items-center",children:[i.jsx(Gn,{className:"h-4 w-4 text-gray-500 mr-1"}),i.jsx(Te,{htmlFor:`date-${t}`,value:"Date",className:"text-sm"})]}),i.jsx(mt,{id:`date-${t}`,selected:e.date?new Date(e.date):null,onChange:g=>n(t,"date",g.toISOString().split("T")[0]),dateFormat:"MMM d, yyyy",className:"w-full rounded-lg border border-gray-300 p-2 text-sm",placeholderText:"Select date",required:!0})]}),i.jsxs("div",{children:[i.jsx("div",{className:"mb-1 flex items-center",children:i.jsx(Te,{htmlFor:`status-${t}`,value:"Status",className:"text-sm"})}),i.jsxs("div",{className:"flex space-x-2",children:[i.jsx("div",{className:`flex-1 text-center py-1.5 px-2 rounded-md cursor-pointer text-sm border ${e.status===te.PRESENT?"bg-green-100 border-green-200 text-green-800 font-medium":"bg-white border-gray-200 text-gray-700 hover:bg-gray-50"}`,onClick:()=>n(t,"status",te.PRESENT),children:"Present"}),i.jsx("div",{className:`flex-1 text-center py-1.5 px-2 rounded-md cursor-pointer text-sm border ${e.status===te.ABSENT?"bg-red-100 border-red-200 text-red-800 font-medium":"bg-white border-gray-200 text-gray-700 hover:bg-gray-50"}`,onClick:()=>n(t,"status",te.ABSENT),children:"Absent"}),i.jsx("div",{className:`flex-1 text-center py-1.5 px-2 rounded-md cursor-pointer text-sm border ${e.status===te.LEAVE?"bg-blue-100 border-blue-200 text-blue-800 font-medium":"bg-white border-gray-200 text-gray-700 hover:bg-gray-50"}`,onClick:()=>n(t,"status",te.LEAVE),children:"Leave"})]})]})]}),e.status===te.PRESENT&&i.jsxs(i.Fragment,{children:[i.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg mb-4",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsxs("div",{children:[i.jsxs("div",{className:"mb-1 flex items-center",children:[i.jsx(Xt,{className:"h-4 w-4 text-gray-500 mr-1"}),i.jsx(Te,{htmlFor:`timeIn-${t}`,value:"Time In",className:"text-sm"})]}),i.jsx(mt,{id:`timeIn-${t}`,selected:e.time_in?new Date(e.time_in):null,onChange:g=>n(t,"time_in",g.toISOString()),showTimeSelect:!0,showTimeSelectOnly:!0,timeIntervals:15,timeCaption:"Time",dateFormat:"h:mm aa",className:"w-full rounded-lg border border-gray-300 p-2 text-sm",placeholderText:"Select time in",required:!0})]}),i.jsxs("div",{children:[i.jsxs("div",{className:"mb-1 flex items-center",children:[i.jsx(Xt,{className:"h-4 w-4 text-gray-500 mr-1"}),i.jsx(Te,{htmlFor:`timeOut-${t}`,value:"Time Out",className:"text-sm"})]}),i.jsx(mt,{id:`timeOut-${t}`,selected:e.time_out?new Date(e.time_out):null,onChange:g=>n(t,"time_out",g.toISOString()),showTimeSelect:!0,showTimeSelectOnly:!0,timeIntervals:15,timeCaption:"Time",dateFormat:"h:mm aa",className:"w-full rounded-lg border border-gray-300 p-2 text-sm",placeholderText:"Select time out",required:!0})]})]}),i.jsxs("div",{className:"mt-3 flex items-center",children:[i.jsx(Jn,{id:`excludeLunch-${t}`,checked:e.exclude_lunch_break,onChange:g=>n(t,"exclude_lunch_break",g.target.checked)}),i.jsx(Te,{htmlFor:`excludeLunch-${t}`,className:"ml-2 text-sm text-gray-700",children:"Exclude lunch break (1 hour)"})]})]}),i.jsxs("div",{className:"grid grid-cols-3 gap-2 mb-3",children:[i.jsxs("div",{className:"bg-blue-50 rounded-lg p-2 border border-blue-100",children:[i.jsx("div",{className:"text-xs text-blue-700 mb-1",children:"Regular Hours"}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"w-2 h-2 rounded-full bg-blue-500 mr-1"}),i.jsx("span",{className:"text-blue-800 font-medium",children:e.regular_hours||0})]})]}),i.jsxs("div",{className:"bg-orange-50 rounded-lg p-2 border border-orange-100",children:[i.jsx("div",{className:"text-xs text-orange-700 mb-1",children:"Overtime"}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"w-2 h-2 rounded-full bg-orange-500 mr-1"}),i.jsx("span",{className:"text-orange-800 font-medium",children:e.overtime_hours||0})]})]}),i.jsxs("div",{className:"bg-indigo-50 rounded-lg p-2 border border-indigo-100",children:[i.jsx("div",{className:"text-xs text-indigo-700 mb-1",children:"Night Diff"}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"w-2 h-2 rounded-full bg-indigo-500 mr-1"}),i.jsx("span",{className:"text-indigo-800 font-medium",children:e.night_diff_hours||0}),i.jsx(Qn,{className:"ml-1 h-3 w-3 text-indigo-600"})]})]})]}),i.jsx("div",{className:"text-xs text-gray-500 mb-3",children:"Hours are calculated automatically. You can manually adjust if needed."})]}),i.jsxs("div",{className:"flex flex-wrap gap-2 mt-3",children:[i.jsxs("div",{className:`flex items-center px-3 py-1.5 rounded-full text-xs cursor-pointer ${e.is_rest_day?"bg-purple-100 text-purple-800 border border-purple-200":"bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200"}`,onClick:()=>n(t,"is_rest_day",!e.is_rest_day),children:[i.jsx("span",{className:`w-2 h-2 rounded-full mr-1 ${e.is_rest_day?"bg-purple-500":"bg-gray-400"}`}),"Rest Day"]}),i.jsxs("div",{className:`flex items-center px-3 py-1.5 rounded-full text-xs cursor-pointer ${e.is_holiday?"bg-blue-100 text-blue-800 border border-blue-200":"bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200"}`,onClick:()=>n(t,"is_holiday",!e.is_holiday),children:[i.jsx("span",{className:`w-2 h-2 rounded-full mr-1 ${e.is_holiday?"bg-blue-500":"bg-gray-400"}`}),"Holiday"]})]})]})]})},io=()=>{const e=Bn(),{currentOrganization:t}=Fn(),[n,r]=l.useState([]),[s,o]=l.useState(!0),[a,c]=l.useState(null),[u,d]=l.useState(!1),[h,f]=l.useState(null),[g,m]=l.useState(!1),[x,b]=l.useState([{date:new Date().toISOString().split("T")[0],status:te.PRESENT,is_rest_day:!1,is_holiday:!1,exclude_lunch_break:!0,regular_hours:0,overtime_hours:0,night_diff_hours:0}]),[v,w]=l.useState(!1),[S,y]=l.useState(""),[N,j]=l.useState([]),[M,C]=l.useState(!1),R=br(Wt(Ot),Wt(Mt,{coordinateGetter:Hs})),k=l.useRef(null),z=async()=>{if(t)try{const{employees:E,error:D}=await Zn(t.id);D?c(D):r(E)}catch(E){c(E.message)}finally{o(!1)}};l.useEffect(()=>{z();const E=localStorage.getItem("timeEntryTemplates");if(E)try{j(JSON.parse(E))}catch(p){console.error("Error loading templates:",p)}else{const p=[{name:"Split Shift Template",entries:[{status:te.PRESENT,is_rest_day:!1,is_holiday:!1,exclude_lunch_break:!1,regular_hours:0,overtime_hours:0,night_diff_hours:0},{status:te.PRESENT,is_rest_day:!1,is_holiday:!1,exclude_lunch_break:!1,regular_hours:0,overtime_hours:0,night_diff_hours:0}]}];j(p),localStorage.setItem("timeEntryTemplates",JSON.stringify(p))}const D=p=>{var L;p.target instanceof HTMLInputElement||p.target instanceof HTMLTextAreaElement||p.target instanceof HTMLSelectElement||((p.ctrlKey||p.metaKey)&&p.shiftKey&&p.key==="K"&&(p.preventDefault(),C(!0)),(p.ctrlKey||p.metaKey)&&p.shiftKey&&p.key==="T"&&(p.preventDefault(),w(!0)),(p.ctrlKey||p.metaKey)&&p.shiftKey&&p.key==="S"&&(p.preventDefault(),(L=k.current)==null||L.dispatchEvent(new Event("submit",{cancelable:!0,bubbles:!0}))),(p.ctrlKey||p.metaKey)&&p.shiftKey&&p.key==="A"&&(p.preventDefault(),J()))};return document.addEventListener("keydown",D),()=>{document.removeEventListener("keydown",D)}},[t]);const A=()=>{if(!S.trim())return;const E=[...N,{name:S,entries:x.map(D=>({...D,employee_id:void 0,date:void 0}))}];j(E),localStorage.setItem("timeEntryTemplates",JSON.stringify(E)),w(!1),y("")},V=E=>{const D=N[E];if(!D)return;const p=D.entries.map(L=>({...L,date:new Date().toISOString().split("T")[0],employee_id:void 0}));b(p),w(!1)},G=E=>{const{active:D,over:p}=E;p&&D.id!==p.id&&b(L=>{const I=L.findIndex((B,X)=>`entry-${X}`===D.id),O=L.findIndex((B,X)=>`entry-${X}`===p.id);return It(L,I,O)})},J=()=>{b([...x,{date:new Date().toISOString().split("T")[0],status:te.PRESENT,is_rest_day:!1,is_holiday:!1,exclude_lunch_break:!0,regular_hours:0,overtime_hours:0,night_diff_hours:0}])},xe=E=>{x.length===1?b([{date:new Date().toISOString().split("T")[0],status:te.PRESENT,is_rest_day:!1,is_holiday:!1,exclude_lunch_break:!0,regular_hours:0,overtime_hours:0,night_diff_hours:0}]):b(x.filter((D,p)=>p!==E))},ye=(E,D,p)=>{const L=[...x];if(L[E]={...L[E],[D]:p},D==="time_in"||D==="time_out"||D==="exclude_lunch_break"){const I=L[E];if(I.time_in&&I.time_out){const O=new Date(I.time_in),B=new Date(I.time_out);if(Ut(O)&&Ut(B)){let X=0;if(B<O){const _=new Date(B);_.setDate(_.getDate()+1),X=nt(_,O)}else X=nt(B,O);I.exclude_lunch_break&&X>300&&(X-=60);const U=X/60,we=Math.min(8,U),oe=Math.max(0,U-8);L[E].regular_hours=Math.round(we*2)/2,L[E].overtime_hours=Math.round(oe*2)/2;let Y=B;B<O&&(Y=new Date(B),Y.setDate(Y.getDate()+1)),L[E].night_diff_hours=Xs(O,Y)}}}b(L)},K=async E=>{if(E.preventDefault(),!t)return;if(x.filter(p=>!p.employee_id||!p.date||p.status===te.PRESENT&&(!p.time_in||!p.time_out)).length>0){f("Please fill in all required fields for all time entries.");return}d(!0),f(null);try{const p=x.map(O=>({employee_id:O.employee_id,date:O.date,time_in:O.time_in,time_out:O.time_out,status:O.status,regular_hours:O.regular_hours,overtime_hours:O.overtime_hours,night_diff_hours:O.night_diff_hours,exclude_lunch_break:O.exclude_lunch_break,is_rest_day:O.is_rest_day,is_holiday:O.is_holiday,holiday_type:O.holiday_type})),{entries:L,error:I}=await nr(t.id,p);I?f(I):(m(!0),setTimeout(()=>{e("/payroll/time-entries")},2e3))}catch(p){f(p.message||"An error occurred while saving time entries")}finally{d(!1)}};return s?i.jsx("div",{className:"flex justify-center items-center h-64",children:i.jsx(Hn,{size:"xl"})}):i.jsxs("div",{className:"container mx-auto px-4 py-6",children:[i.jsx("div",{className:"flex justify-between items-center mb-4",children:i.jsxs("div",{className:"flex items-center",children:[i.jsxs(ce,{color:"light",size:"sm",onClick:()=>e("/payroll/time-entries"),className:"mr-3",children:[i.jsx(Kn,{className:"mr-1 h-4 w-4"}),"Back"]}),i.jsx(er,{title:"Add Time Entries"})]})}),a&&i.jsxs(gt,{color:"failure",icon:Ht,className:"mb-4",children:[i.jsx("span",{className:"font-medium",children:"Error!"})," ",a]}),g&&i.jsx(gt,{color:"success",icon:$e,className:"mb-4",children:"Time entries saved successfully! Redirecting..."}),h&&i.jsx(gt,{color:"failure",icon:Ht,className:"mb-4",children:h}),i.jsx(ln,{className:"mb-4",children:i.jsxs("div",{className:"p-4 bg-blue-50 border-b border-blue-100 rounded-t-lg",children:[i.jsx("h3",{className:"text-blue-800 font-medium",children:"Quick Tips"}),i.jsxs("ul",{className:"mt-2 text-sm text-blue-700 space-y-1",children:[i.jsxs("li",{className:"flex items-start",children:[i.jsx($e,{className:"h-4 w-4 mr-1 mt-0.5 flex-shrink-0"}),i.jsx("span",{children:"Add multiple time entries at once for bulk processing"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx($e,{className:"h-4 w-4 mr-1 mt-0.5 flex-shrink-0"}),i.jsx("span",{children:"Hours are automatically calculated based on time in/out"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx($e,{className:"h-4 w-4 mr-1 mt-0.5 flex-shrink-0"}),i.jsx("span",{children:"Night differential (10PM-6AM) is calculated automatically"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx($e,{className:"h-4 w-4 mr-1 mt-0.5 flex-shrink-0"}),i.jsxs("span",{children:[i.jsx("strong",{children:"For split shifts"})," (e.g., 7am-10am, then 3pm-7pm), create separate entries for each shift segment"]})]})]})]})}),i.jsx("div",{className:"flex justify-between mb-4",children:i.jsxs("div",{className:"flex space-x-2",children:[i.jsxs(ce,{size:"sm",color:"light",onClick:()=>w(!0),children:[i.jsx(Kt,{className:"mr-1 h-4 w-4"}),"Templates"]}),i.jsxs(ce,{size:"sm",color:"light",onClick:()=>C(!0),children:[i.jsx(Xn,{className:"mr-1 h-4 w-4"}),"Shortcuts"]})]})}),i.jsxs("form",{ref:k,onSubmit:K,children:[i.jsx(xs,{sensors:R,collisionDetection:xr,onDragEnd:G,children:i.jsx(Os,{items:x.map((E,D)=>`entry-${D}`),strategy:Ms,children:i.jsx("div",{className:"space-y-4",children:x.map((E,D)=>i.jsx(Us,{entry:E,index:D,updateTimeEntry:ye,removeTimeEntryRow:xe,employees:n,isLoading:s},`entry-${D}`))})})}),i.jsx("div",{className:"flex justify-center",children:i.jsxs(ce,{color:"light",size:"sm",onClick:J,className:"mt-4",children:[i.jsx(Un,{className:"mr-1 h-4 w-4"}),"Add Another Entry"]})}),i.jsxs("div",{className:"flex justify-between mt-6",children:[i.jsx(ce,{color:"light",size:"sm",onClick:()=>e("/payroll/time-entries"),children:"Cancel"}),i.jsxs(ce,{color:"primary",type:"submit",isProcessing:u,children:[i.jsx(Yn,{className:"mr-1 h-4 w-4"}),"Save Time Entries"]})]})]}),i.jsxs(Re,{show:v,onClose:()=>w(!1),size:"md",children:[i.jsx(Re.Header,{children:"Time Entry Templates"}),i.jsx(Re.Body,{children:i.jsxs("div",{className:"space-y-4",children:[N.length>0?i.jsxs("div",{className:"space-y-3",children:[i.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Saved Templates"}),i.jsx("div",{className:"space-y-2",children:N.map((E,D)=>i.jsxs("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200",children:[i.jsxs("div",{children:[i.jsx("div",{className:"font-medium text-gray-800",children:E.name}),i.jsxs("div",{className:"text-xs text-gray-500",children:[E.entries.length," entries"]})]}),i.jsxs("div",{className:"flex space-x-2",children:[i.jsx(ce,{size:"xs",color:"light",onClick:()=>V(D),children:i.jsx(Wn,{className:"h-4 w-4"})}),i.jsx(ce,{size:"xs",color:"failure",onClick:()=>{const p=N.filter((L,I)=>I!==D);j(p),localStorage.setItem("timeEntryTemplates",JSON.stringify(p))},children:i.jsx(an,{className:"h-4 w-4"})})]})]},D))})]}):i.jsxs("div",{className:"text-center py-4 text-gray-500",children:[i.jsx(Kt,{className:"mx-auto h-10 w-10 mb-2"}),i.jsx("p",{children:"No templates saved yet"})]}),i.jsxs("div",{className:"border-t pt-4 mt-4",children:[i.jsx("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Save Current Entries as Template"}),i.jsxs("div",{className:"flex space-x-2",children:[i.jsx(qn,{type:"text",placeholder:"Template name",value:S,onChange:E=>y(E.target.value),className:"flex-1"}),i.jsx(ce,{color:"blue",onClick:A,disabled:!S.trim()||x.length===0,children:"Save"})]})]})]})})]}),i.jsxs(Re,{show:M,onClose:()=>C(!1),size:"md",children:[i.jsx(Re.Header,{children:"Keyboard Shortcuts"}),i.jsx(Re.Body,{children:i.jsxs("div",{className:"space-y-3",children:[i.jsxs("div",{className:"flex justify-between items-center p-2 border-b",children:[i.jsx("span",{className:"text-gray-700",children:"Add new entry"}),i.jsxs("div",{className:"flex space-x-1",children:[i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"Ctrl"}),i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"Shift"}),i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"A"})]})]}),i.jsxs("div",{className:"flex justify-between items-center p-2 border-b",children:[i.jsx("span",{className:"text-gray-700",children:"Save entries"}),i.jsxs("div",{className:"flex space-x-1",children:[i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"Ctrl"}),i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"Shift"}),i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"S"})]})]}),i.jsxs("div",{className:"flex justify-between items-center p-2 border-b",children:[i.jsx("span",{className:"text-gray-700",children:"Show templates"}),i.jsxs("div",{className:"flex space-x-1",children:[i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"Ctrl"}),i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"Shift"}),i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"T"})]})]}),i.jsxs("div",{className:"flex justify-between items-center p-2",children:[i.jsx("span",{className:"text-gray-700",children:"Show this help"}),i.jsxs("div",{className:"flex space-x-1",children:[i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"Ctrl"}),i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"Shift"}),i.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"K"})]})]})]})})]})]})};export{io as default};
