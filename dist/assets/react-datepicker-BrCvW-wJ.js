var jr=Object.defineProperty;var Xr=(a,n,t)=>n in a?jr(a,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[n]=t;var w=(a,n,t)=>Xr(a,typeof n!="symbol"?n+"":n,t);import{r as y,bi as yr,bC as Gr,bD as $e,bE as $r,j as He,R as f,bF as zr,bj as Zr,bG as Jr,bH as en,bI as tn}from"./index-C6AV3cVN.js";function br(a){var n,t,e="";if(typeof a=="string"||typeof a=="number")e+=a;else if(typeof a=="object")if(Array.isArray(a)){var r=a.length;for(n=0;n<r;n++)a[n]&&(t=br(a[n]))&&(e&&(e+=" "),e+=t)}else for(t in a)a[t]&&(e&&(e+=" "),e+=t);return e}function $(){for(var a,n,t=0,e="",r=arguments.length;t<r;t++)(a=arguments[t])&&(n=br(a))&&(e&&(e+=" "),e+=n);return e}const kr=6048e5,rn=864e5,gt=6e4,Dt=36e5,nn=1e3,Kt=Symbol.for("constructDateFrom");function L(a,n){return typeof a=="function"?a(n):a&&typeof a=="object"&&Kt in a?a[Kt](n):a instanceof Date?new a.constructor(n):new Date(n)}function b(a,n){return L(n||a,a)}function oe(a,n,t){const e=b(a,t==null?void 0:t.in);return isNaN(n)?L((t==null?void 0:t.in)||a,NaN):(n&&e.setDate(e.getDate()+n),e)}function ie(a,n,t){const e=b(a,t==null?void 0:t.in);if(isNaN(n))return L(a,NaN);if(!n)return e;const r=e.getDate(),o=L(a,e.getTime());o.setMonth(e.getMonth()+n+1,0);const i=o.getDate();return r>=i?o:(e.setFullYear(o.getFullYear(),o.getMonth(),r),e)}function _r(a,n,t){return L(a,+b(a)+n)}function an(a,n,t){return _r(a,n*Dt)}let on={};function Oe(){return on}function De(a,n){var s,c,u,l;const t=Oe(),e=(n==null?void 0:n.weekStartsOn)??((c=(s=n==null?void 0:n.locale)==null?void 0:s.options)==null?void 0:c.weekStartsOn)??t.weekStartsOn??((l=(u=t.locale)==null?void 0:u.options)==null?void 0:l.weekStartsOn)??0,r=b(a,n==null?void 0:n.in),o=r.getDay(),i=(o<e?7:0)+o-e;return r.setDate(r.getDate()-i),r.setHours(0,0,0,0),r}function Re(a,n){return De(a,{...n,weekStartsOn:1})}function Mr(a,n){const t=b(a,n==null?void 0:n.in),e=t.getFullYear(),r=L(t,0);r.setFullYear(e+1,0,4),r.setHours(0,0,0,0);const o=Re(r),i=L(t,0);i.setFullYear(e,0,4),i.setHours(0,0,0,0);const s=Re(i);return t.getTime()>=o.getTime()?e+1:t.getTime()>=s.getTime()?e:e-1}function ct(a){const n=b(a),t=new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds()));return t.setUTCFullYear(n.getFullYear()),+a-+t}function we(a,...n){const t=L.bind(null,n.find(e=>typeof e=="object"));return n.map(t)}function Ee(a,n){const t=b(a,n==null?void 0:n.in);return t.setHours(0,0,0,0),t}function Le(a,n,t){const[e,r]=we(t==null?void 0:t.in,a,n),o=Ee(e),i=Ee(r),s=+o-ct(o),c=+i-ct(i);return Math.round((s-c)/rn)}function sn(a,n){const t=Mr(a,n),e=L(a,0);return e.setFullYear(t,0,4),e.setHours(0,0,0,0),Re(e)}function Et(a,n,t){const e=b(a,t==null?void 0:t.in);return e.setTime(e.getTime()+n*gt),e}function It(a,n,t){return ie(a,n*3,t)}function cn(a,n,t){return _r(a,n*1e3)}function lt(a,n,t){return oe(a,n*7,t)}function fe(a,n,t){return ie(a,n*12,t)}function qt(a,n){let t,e=n==null?void 0:n.in;return a.forEach(r=>{!e&&typeof r=="object"&&(e=L.bind(null,r));const o=b(r,e);(!t||t<o||isNaN(+o))&&(t=o)}),L(e,t||NaN)}function Vt(a,n){let t,e=n==null?void 0:n.in;return a.forEach(r=>{!e&&typeof r=="object"&&(e=L.bind(null,r));const o=b(r,e);(!t||t>o||isNaN(+o))&&(t=o)}),L(e,t||NaN)}function ln(a,n,t){const[e,r]=we(t==null?void 0:t.in,a,n);return+Ee(e)==+Ee(r)}function he(a){return a instanceof Date||typeof a=="object"&&Object.prototype.toString.call(a)==="[object Date]"}function ut(a){return!(!he(a)&&typeof a!="number"||isNaN(+b(a)))}function dt(a,n,t){const[e,r]=we(t==null?void 0:t.in,a,n),o=e.getFullYear()-r.getFullYear(),i=e.getMonth()-r.getMonth();return o*12+i}function Se(a,n){const t=b(a,n==null?void 0:n.in);return Math.trunc(t.getMonth()/3)+1}function pt(a,n,t){const[e,r]=we(t==null?void 0:t.in,a,n),o=e.getFullYear()-r.getFullYear(),i=Se(e)-Se(r);return o*4+i}function ft(a,n,t){const[e,r]=we(t==null?void 0:t.in,a,n);return e.getFullYear()-r.getFullYear()}function un(a,n,t){const[e,r]=we(t==null?void 0:t.in,a,n),o=Ut(e,r),i=Math.abs(Le(e,r));e.setDate(e.getDate()-o*i);const s=+(Ut(e,r)===-o),c=o*(i-s);return c===0?0:c}function Ut(a,n){const t=a.getFullYear()-n.getFullYear()||a.getMonth()-n.getMonth()||a.getDate()-n.getDate()||a.getHours()-n.getHours()||a.getMinutes()-n.getMinutes()||a.getSeconds()-n.getSeconds()||a.getMilliseconds()-n.getMilliseconds();return t<0?-1:t>0?1:t}function Cr(a,n){const t=b(a,n==null?void 0:n.in);return t.setHours(23,59,59,999),t}function xr(a,n){const t=b(a,n==null?void 0:n.in),e=t.getMonth();return t.setFullYear(t.getFullYear(),e+1,0),t.setHours(23,59,59,999),t}function Tt(a,n){const t=b(a,n==null?void 0:n.in),e=t.getMonth(),r=e-e%3;return t.setMonth(r,1),t.setHours(0,0,0,0),t}function Sr(a,n){const t=b(a,n==null?void 0:n.in);return t.setDate(1),t.setHours(0,0,0,0),t}function Pr(a,n){const t=b(a,n==null?void 0:n.in),e=t.getFullYear();return t.setFullYear(e+1,0,0),t.setHours(23,59,59,999),t}function wt(a,n){const t=b(a,n==null?void 0:n.in);return t.setFullYear(t.getFullYear(),0,1),t.setHours(0,0,0,0),t}function dn(a,n){var s,c;const t=Oe(),e=t.weekStartsOn??((c=(s=t.locale)==null?void 0:s.options)==null?void 0:c.weekStartsOn)??0,r=b(a,n==null?void 0:n.in),o=r.getDay(),i=(o<e?-7:0)+6-(o-e);return r.setDate(r.getDate()+i),r.setHours(23,59,59,999),r}const pn={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},fn=(a,n,t)=>{let e;const r=pn[a];return typeof r=="string"?e=r:n===1?e=r.one:e=r.other.replace("{{count}}",n.toString()),t!=null&&t.addSuffix?t.comparison&&t.comparison>0?"in "+e:e+" ago":e};function kt(a){return(n={})=>{const t=n.width?String(n.width):a.defaultWidth;return a.formats[t]||a.formats[a.defaultWidth]}}const hn={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},mn={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},vn={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},gn={date:kt({formats:hn,defaultWidth:"full"}),time:kt({formats:mn,defaultWidth:"full"}),dateTime:kt({formats:vn,defaultWidth:"full"})},Dn={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},wn=(a,n,t,e)=>Dn[a];function Qe(a){return(n,t)=>{const e=t!=null&&t.context?String(t.context):"standalone";let r;if(e==="formatting"&&a.formattingValues){const i=a.defaultFormattingWidth||a.defaultWidth,s=t!=null&&t.width?String(t.width):i;r=a.formattingValues[s]||a.formattingValues[i]}else{const i=a.defaultWidth,s=t!=null&&t.width?String(t.width):a.defaultWidth;r=a.values[s]||a.values[i]}const o=a.argumentCallback?a.argumentCallback(n):n;return r[o]}}const yn={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},bn={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},kn={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},_n={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Mn={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Cn={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},xn=(a,n)=>{const t=Number(a),e=t%100;if(e>20||e<10)switch(e%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},Sn={ordinalNumber:xn,era:Qe({values:yn,defaultWidth:"wide"}),quarter:Qe({values:bn,defaultWidth:"wide",argumentCallback:a=>a-1}),month:Qe({values:kn,defaultWidth:"wide"}),day:Qe({values:_n,defaultWidth:"wide"}),dayPeriod:Qe({values:Mn,defaultWidth:"wide",formattingValues:Cn,defaultFormattingWidth:"wide"})};function Be(a){return(n,t={})=>{const e=t.width,r=e&&a.matchPatterns[e]||a.matchPatterns[a.defaultMatchWidth],o=n.match(r);if(!o)return null;const i=o[0],s=e&&a.parsePatterns[e]||a.parsePatterns[a.defaultParseWidth],c=Array.isArray(s)?En(s,d=>d.test(i)):Pn(s,d=>d.test(i));let u;u=a.valueCallback?a.valueCallback(c):c,u=t.valueCallback?t.valueCallback(u):u;const l=n.slice(i.length);return{value:u,rest:l}}}function Pn(a,n){for(const t in a)if(Object.prototype.hasOwnProperty.call(a,t)&&n(a[t]))return t}function En(a,n){for(let t=0;t<a.length;t++)if(n(a[t]))return t}function Tn(a){return(n,t={})=>{const e=n.match(a.matchPattern);if(!e)return null;const r=e[0],o=n.match(a.parsePattern);if(!o)return null;let i=a.valueCallback?a.valueCallback(o[0]):o[0];i=t.valueCallback?t.valueCallback(i):i;const s=n.slice(r.length);return{value:i,rest:s}}}const On=/^(\d+)(th|st|nd|rd)?/i,Nn=/\d+/i,Yn={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},In={any:[/^b/i,/^(a|c)/i]},Rn={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Ln={any:[/1/i,/2/i,/3/i,/4/i]},Fn={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Wn={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},An={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Hn={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Qn={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Bn={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Kn={ordinalNumber:Tn({matchPattern:On,parsePattern:Nn,valueCallback:a=>parseInt(a,10)}),era:Be({matchPatterns:Yn,defaultMatchWidth:"wide",parsePatterns:In,defaultParseWidth:"any"}),quarter:Be({matchPatterns:Rn,defaultMatchWidth:"wide",parsePatterns:Ln,defaultParseWidth:"any",valueCallback:a=>a+1}),month:Be({matchPatterns:Fn,defaultMatchWidth:"wide",parsePatterns:Wn,defaultParseWidth:"any"}),day:Be({matchPatterns:An,defaultMatchWidth:"wide",parsePatterns:Hn,defaultParseWidth:"any"}),dayPeriod:Be({matchPatterns:Qn,defaultMatchWidth:"any",parsePatterns:Bn,defaultParseWidth:"any"})},Er={code:"en-US",formatDistance:fn,formatLong:gn,formatRelative:wn,localize:Sn,match:Kn,options:{weekStartsOn:0,firstWeekContainsDate:1}};function qn(a,n){const t=b(a,n==null?void 0:n.in);return Le(t,wt(t))+1}function Rt(a,n){const t=b(a,n==null?void 0:n.in),e=+Re(t)-+sn(t);return Math.round(e/kr)+1}function Lt(a,n){var l,d,p,h;const t=b(a,n==null?void 0:n.in),e=t.getFullYear(),r=Oe(),o=(n==null?void 0:n.firstWeekContainsDate)??((d=(l=n==null?void 0:n.locale)==null?void 0:l.options)==null?void 0:d.firstWeekContainsDate)??r.firstWeekContainsDate??((h=(p=r.locale)==null?void 0:p.options)==null?void 0:h.firstWeekContainsDate)??1,i=L((n==null?void 0:n.in)||a,0);i.setFullYear(e+1,0,o),i.setHours(0,0,0,0);const s=De(i,n),c=L((n==null?void 0:n.in)||a,0);c.setFullYear(e,0,o),c.setHours(0,0,0,0);const u=De(c,n);return+t>=+s?e+1:+t>=+u?e:e-1}function Vn(a,n){var s,c,u,l;const t=Oe(),e=(n==null?void 0:n.firstWeekContainsDate)??((c=(s=n==null?void 0:n.locale)==null?void 0:s.options)==null?void 0:c.firstWeekContainsDate)??t.firstWeekContainsDate??((l=(u=t.locale)==null?void 0:u.options)==null?void 0:l.firstWeekContainsDate)??1,r=Lt(a,n),o=L((n==null?void 0:n.in)||a,0);return o.setFullYear(r,0,e),o.setHours(0,0,0,0),De(o,n)}function Tr(a,n){const t=b(a,n==null?void 0:n.in),e=+De(t,n)-+Vn(t,n);return Math.round(e/kr)+1}function R(a,n){const t=a<0?"-":"",e=Math.abs(a).toString().padStart(n,"0");return t+e}const ye={y(a,n){const t=a.getFullYear(),e=t>0?t:1-t;return R(n==="yy"?e%100:e,n.length)},M(a,n){const t=a.getMonth();return n==="M"?String(t+1):R(t+1,2)},d(a,n){return R(a.getDate(),n.length)},a(a,n){const t=a.getHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return t.toUpperCase();case"aaa":return t;case"aaaaa":return t[0];case"aaaa":default:return t==="am"?"a.m.":"p.m."}},h(a,n){return R(a.getHours()%12||12,n.length)},H(a,n){return R(a.getHours(),n.length)},m(a,n){return R(a.getMinutes(),n.length)},s(a,n){return R(a.getSeconds(),n.length)},S(a,n){const t=n.length,e=a.getMilliseconds(),r=Math.trunc(e*Math.pow(10,t-3));return R(r,n.length)}},Ne={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},jt={G:function(a,n,t){const e=a.getFullYear()>0?1:0;switch(n){case"G":case"GG":case"GGG":return t.era(e,{width:"abbreviated"});case"GGGGG":return t.era(e,{width:"narrow"});case"GGGG":default:return t.era(e,{width:"wide"})}},y:function(a,n,t){if(n==="yo"){const e=a.getFullYear(),r=e>0?e:1-e;return t.ordinalNumber(r,{unit:"year"})}return ye.y(a,n)},Y:function(a,n,t,e){const r=Lt(a,e),o=r>0?r:1-r;if(n==="YY"){const i=o%100;return R(i,2)}return n==="Yo"?t.ordinalNumber(o,{unit:"year"}):R(o,n.length)},R:function(a,n){const t=Mr(a);return R(t,n.length)},u:function(a,n){const t=a.getFullYear();return R(t,n.length)},Q:function(a,n,t){const e=Math.ceil((a.getMonth()+1)/3);switch(n){case"Q":return String(e);case"QQ":return R(e,2);case"Qo":return t.ordinalNumber(e,{unit:"quarter"});case"QQQ":return t.quarter(e,{width:"abbreviated",context:"formatting"});case"QQQQQ":return t.quarter(e,{width:"narrow",context:"formatting"});case"QQQQ":default:return t.quarter(e,{width:"wide",context:"formatting"})}},q:function(a,n,t){const e=Math.ceil((a.getMonth()+1)/3);switch(n){case"q":return String(e);case"qq":return R(e,2);case"qo":return t.ordinalNumber(e,{unit:"quarter"});case"qqq":return t.quarter(e,{width:"abbreviated",context:"standalone"});case"qqqqq":return t.quarter(e,{width:"narrow",context:"standalone"});case"qqqq":default:return t.quarter(e,{width:"wide",context:"standalone"})}},M:function(a,n,t){const e=a.getMonth();switch(n){case"M":case"MM":return ye.M(a,n);case"Mo":return t.ordinalNumber(e+1,{unit:"month"});case"MMM":return t.month(e,{width:"abbreviated",context:"formatting"});case"MMMMM":return t.month(e,{width:"narrow",context:"formatting"});case"MMMM":default:return t.month(e,{width:"wide",context:"formatting"})}},L:function(a,n,t){const e=a.getMonth();switch(n){case"L":return String(e+1);case"LL":return R(e+1,2);case"Lo":return t.ordinalNumber(e+1,{unit:"month"});case"LLL":return t.month(e,{width:"abbreviated",context:"standalone"});case"LLLLL":return t.month(e,{width:"narrow",context:"standalone"});case"LLLL":default:return t.month(e,{width:"wide",context:"standalone"})}},w:function(a,n,t,e){const r=Tr(a,e);return n==="wo"?t.ordinalNumber(r,{unit:"week"}):R(r,n.length)},I:function(a,n,t){const e=Rt(a);return n==="Io"?t.ordinalNumber(e,{unit:"week"}):R(e,n.length)},d:function(a,n,t){return n==="do"?t.ordinalNumber(a.getDate(),{unit:"date"}):ye.d(a,n)},D:function(a,n,t){const e=qn(a);return n==="Do"?t.ordinalNumber(e,{unit:"dayOfYear"}):R(e,n.length)},E:function(a,n,t){const e=a.getDay();switch(n){case"E":case"EE":case"EEE":return t.day(e,{width:"abbreviated",context:"formatting"});case"EEEEE":return t.day(e,{width:"narrow",context:"formatting"});case"EEEEEE":return t.day(e,{width:"short",context:"formatting"});case"EEEE":default:return t.day(e,{width:"wide",context:"formatting"})}},e:function(a,n,t,e){const r=a.getDay(),o=(r-e.weekStartsOn+8)%7||7;switch(n){case"e":return String(o);case"ee":return R(o,2);case"eo":return t.ordinalNumber(o,{unit:"day"});case"eee":return t.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return t.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return t.day(r,{width:"short",context:"formatting"});case"eeee":default:return t.day(r,{width:"wide",context:"formatting"})}},c:function(a,n,t,e){const r=a.getDay(),o=(r-e.weekStartsOn+8)%7||7;switch(n){case"c":return String(o);case"cc":return R(o,n.length);case"co":return t.ordinalNumber(o,{unit:"day"});case"ccc":return t.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return t.day(r,{width:"narrow",context:"standalone"});case"cccccc":return t.day(r,{width:"short",context:"standalone"});case"cccc":default:return t.day(r,{width:"wide",context:"standalone"})}},i:function(a,n,t){const e=a.getDay(),r=e===0?7:e;switch(n){case"i":return String(r);case"ii":return R(r,n.length);case"io":return t.ordinalNumber(r,{unit:"day"});case"iii":return t.day(e,{width:"abbreviated",context:"formatting"});case"iiiii":return t.day(e,{width:"narrow",context:"formatting"});case"iiiiii":return t.day(e,{width:"short",context:"formatting"});case"iiii":default:return t.day(e,{width:"wide",context:"formatting"})}},a:function(a,n,t){const r=a.getHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return t.dayPeriod(r,{width:"narrow",context:"formatting"});case"aaaa":default:return t.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(a,n,t){const e=a.getHours();let r;switch(e===12?r=Ne.noon:e===0?r=Ne.midnight:r=e/12>=1?"pm":"am",n){case"b":case"bb":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return t.dayPeriod(r,{width:"narrow",context:"formatting"});case"bbbb":default:return t.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(a,n,t){const e=a.getHours();let r;switch(e>=17?r=Ne.evening:e>=12?r=Ne.afternoon:e>=4?r=Ne.morning:r=Ne.night,n){case"B":case"BB":case"BBB":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return t.dayPeriod(r,{width:"narrow",context:"formatting"});case"BBBB":default:return t.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(a,n,t){if(n==="ho"){let e=a.getHours()%12;return e===0&&(e=12),t.ordinalNumber(e,{unit:"hour"})}return ye.h(a,n)},H:function(a,n,t){return n==="Ho"?t.ordinalNumber(a.getHours(),{unit:"hour"}):ye.H(a,n)},K:function(a,n,t){const e=a.getHours()%12;return n==="Ko"?t.ordinalNumber(e,{unit:"hour"}):R(e,n.length)},k:function(a,n,t){let e=a.getHours();return e===0&&(e=24),n==="ko"?t.ordinalNumber(e,{unit:"hour"}):R(e,n.length)},m:function(a,n,t){return n==="mo"?t.ordinalNumber(a.getMinutes(),{unit:"minute"}):ye.m(a,n)},s:function(a,n,t){return n==="so"?t.ordinalNumber(a.getSeconds(),{unit:"second"}):ye.s(a,n)},S:function(a,n){return ye.S(a,n)},X:function(a,n,t){const e=a.getTimezoneOffset();if(e===0)return"Z";switch(n){case"X":return Gt(e);case"XXXX":case"XX":return Ce(e);case"XXXXX":case"XXX":default:return Ce(e,":")}},x:function(a,n,t){const e=a.getTimezoneOffset();switch(n){case"x":return Gt(e);case"xxxx":case"xx":return Ce(e);case"xxxxx":case"xxx":default:return Ce(e,":")}},O:function(a,n,t){const e=a.getTimezoneOffset();switch(n){case"O":case"OO":case"OOO":return"GMT"+Xt(e,":");case"OOOO":default:return"GMT"+Ce(e,":")}},z:function(a,n,t){const e=a.getTimezoneOffset();switch(n){case"z":case"zz":case"zzz":return"GMT"+Xt(e,":");case"zzzz":default:return"GMT"+Ce(e,":")}},t:function(a,n,t){const e=Math.trunc(+a/1e3);return R(e,n.length)},T:function(a,n,t){return R(+a,n.length)}};function Xt(a,n=""){const t=a>0?"-":"+",e=Math.abs(a),r=Math.trunc(e/60),o=e%60;return o===0?t+String(r):t+String(r)+n+R(o,2)}function Gt(a,n){return a%60===0?(a>0?"-":"+")+R(Math.abs(a)/60,2):Ce(a,n)}function Ce(a,n=""){const t=a>0?"-":"+",e=Math.abs(a),r=R(Math.trunc(e/60),2),o=R(e%60,2);return t+r+n+o}const $t=(a,n)=>{switch(a){case"P":return n.date({width:"short"});case"PP":return n.date({width:"medium"});case"PPP":return n.date({width:"long"});case"PPPP":default:return n.date({width:"full"})}},Or=(a,n)=>{switch(a){case"p":return n.time({width:"short"});case"pp":return n.time({width:"medium"});case"ppp":return n.time({width:"long"});case"pppp":default:return n.time({width:"full"})}},Un=(a,n)=>{const t=a.match(/(P+)(p+)?/)||[],e=t[1],r=t[2];if(!r)return $t(a,n);let o;switch(e){case"P":o=n.dateTime({width:"short"});break;case"PP":o=n.dateTime({width:"medium"});break;case"PPP":o=n.dateTime({width:"long"});break;case"PPPP":default:o=n.dateTime({width:"full"});break}return o.replace("{{date}}",$t(e,n)).replace("{{time}}",Or(r,n))},Ot={p:Or,P:Un},jn=/^D+$/,Xn=/^Y+$/,Gn=["D","DD","YY","YYYY"];function $n(a){return jn.test(a)}function zn(a){return Xn.test(a)}function Zn(a,n,t){const e=Jn(a,n,t);if(console.warn(e),Gn.includes(a))throw new RangeError(e)}function Jn(a,n,t){const e=a[0]==="Y"?"years":"days of the month";return`Use \`${a.toLowerCase()}\` instead of \`${a}\` (in \`${n}\`) for formatting ${e} to the input \`${t}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const ea=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,ta=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,ra=/^'([^]*?)'?$/,na=/''/g,aa=/[a-zA-Z]/;function zt(a,n,t){var l,d,p,h,m,g,v,k;const e=Oe(),r=(t==null?void 0:t.locale)??e.locale??Er,o=(t==null?void 0:t.firstWeekContainsDate)??((d=(l=t==null?void 0:t.locale)==null?void 0:l.options)==null?void 0:d.firstWeekContainsDate)??e.firstWeekContainsDate??((h=(p=e.locale)==null?void 0:p.options)==null?void 0:h.firstWeekContainsDate)??1,i=(t==null?void 0:t.weekStartsOn)??((g=(m=t==null?void 0:t.locale)==null?void 0:m.options)==null?void 0:g.weekStartsOn)??e.weekStartsOn??((k=(v=e.locale)==null?void 0:v.options)==null?void 0:k.weekStartsOn)??0,s=b(a,t==null?void 0:t.in);if(!ut(s))throw new RangeError("Invalid time value");let c=n.match(ta).map(x=>{const M=x[0];if(M==="p"||M==="P"){const T=Ot[M];return T(x,r.formatLong)}return x}).join("").match(ea).map(x=>{if(x==="''")return{isToken:!1,value:"'"};const M=x[0];if(M==="'")return{isToken:!1,value:oa(x)};if(jt[M])return{isToken:!0,value:x};if(M.match(aa))throw new RangeError("Format string contains an unescaped latin alphabet character `"+M+"`");return{isToken:!1,value:x}});r.localize.preprocessor&&(c=r.localize.preprocessor(s,c));const u={firstWeekContainsDate:o,weekStartsOn:i,locale:r};return c.map(x=>{if(!x.isToken)return x.value;const M=x.value;(!(t!=null&&t.useAdditionalWeekYearTokens)&&zn(M)||!(t!=null&&t.useAdditionalDayOfYearTokens)&&$n(M))&&Zn(M,n,String(a));const T=jt[M[0]];return T(s,M,r.localize,u)}).join("")}function oa(a){const n=a.match(ra);return n?n[1].replace(na,"'"):a}function Zt(a,n){return b(a,n==null?void 0:n.in).getDate()}function ia(a,n){return b(a,n==null?void 0:n.in).getDay()}function sa(a,n){const t=b(a,n==null?void 0:n.in),e=t.getFullYear(),r=t.getMonth(),o=L(t,0);return o.setFullYear(e,r+1,0),o.setHours(0,0,0,0),o.getDate()}function ca(){return Object.assign({},Oe())}function me(a,n){return b(a,n==null?void 0:n.in).getHours()}function la(a,n){const t=b(a,n==null?void 0:n.in).getDay();return t===0?7:t}function ve(a,n){return b(a,n==null?void 0:n.in).getMinutes()}function J(a,n){return b(a,n==null?void 0:n.in).getMonth()}function ke(a){return b(a).getSeconds()}function Nt(a){return+b(a)}function S(a,n){return b(a,n==null?void 0:n.in).getFullYear()}function Me(a,n){return+b(a)>+b(n)}function Te(a,n){return+b(a)<+b(n)}function ua(a,n){return+b(a)==+b(n)}function da(a,n){const t=pa(n)?new n(0):L(n,0);return t.setFullYear(a.getFullYear(),a.getMonth(),a.getDate()),t.setHours(a.getHours(),a.getMinutes(),a.getSeconds(),a.getMilliseconds()),t}function pa(a){var n;return typeof a=="function"&&((n=a.prototype)==null?void 0:n.constructor)===a}const fa=10;class Nr{constructor(){w(this,"subPriority",0)}validate(n,t){return!0}}class ha extends Nr{constructor(n,t,e,r,o){super(),this.value=n,this.validateValue=t,this.setValue=e,this.priority=r,o&&(this.subPriority=o)}validate(n,t){return this.validateValue(n,this.value,t)}set(n,t,e){return this.setValue(n,t,this.value,e)}}class ma extends Nr{constructor(t,e){super();w(this,"priority",fa);w(this,"subPriority",-1);this.context=t||(r=>L(e,r))}set(t,e){return e.timestampIsSet?t:L(t,da(t,this.context))}}class O{run(n,t,e,r){const o=this.parse(n,t,e,r);return o?{setter:new ha(o.value,this.validate,this.set,this.priority,this.subPriority),rest:o.rest}:null}validate(n,t,e){return!0}}class va extends O{constructor(){super(...arguments);w(this,"priority",140);w(this,"incompatibleTokens",["R","u","t","T"])}parse(t,e,r){switch(e){case"G":case"GG":case"GGG":return r.era(t,{width:"abbreviated"})||r.era(t,{width:"narrow"});case"GGGGG":return r.era(t,{width:"narrow"});case"GGGG":default:return r.era(t,{width:"wide"})||r.era(t,{width:"abbreviated"})||r.era(t,{width:"narrow"})}}set(t,e,r){return e.era=r,t.setFullYear(r,0,1),t.setHours(0,0,0,0),t}}const V={month:/^(1[0-2]|0?\d)/,date:/^(3[0-1]|[0-2]?\d)/,dayOfYear:/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,week:/^(5[0-3]|[0-4]?\d)/,hour23h:/^(2[0-3]|[0-1]?\d)/,hour24h:/^(2[0-4]|[0-1]?\d)/,hour11h:/^(1[0-1]|0?\d)/,hour12h:/^(1[0-2]|0?\d)/,minute:/^[0-5]?\d/,second:/^[0-5]?\d/,singleDigit:/^\d/,twoDigits:/^\d{1,2}/,threeDigits:/^\d{1,3}/,fourDigits:/^\d{1,4}/,anyDigitsSigned:/^-?\d+/,singleDigitSigned:/^-?\d/,twoDigitsSigned:/^-?\d{1,2}/,threeDigitsSigned:/^-?\d{1,3}/,fourDigitsSigned:/^-?\d{1,4}/},ue={basicOptionalMinutes:/^([+-])(\d{2})(\d{2})?|Z/,basic:/^([+-])(\d{2})(\d{2})|Z/,basicOptionalSeconds:/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,extended:/^([+-])(\d{2}):(\d{2})|Z/,extendedOptionalSeconds:/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/};function U(a,n){return a&&{value:n(a.value),rest:a.rest}}function Q(a,n){const t=n.match(a);return t?{value:parseInt(t[0],10),rest:n.slice(t[0].length)}:null}function de(a,n){const t=n.match(a);if(!t)return null;if(t[0]==="Z")return{value:0,rest:n.slice(1)};const e=t[1]==="+"?1:-1,r=t[2]?parseInt(t[2],10):0,o=t[3]?parseInt(t[3],10):0,i=t[5]?parseInt(t[5],10):0;return{value:e*(r*Dt+o*gt+i*nn),rest:n.slice(t[0].length)}}function Yr(a){return Q(V.anyDigitsSigned,a)}function q(a,n){switch(a){case 1:return Q(V.singleDigit,n);case 2:return Q(V.twoDigits,n);case 3:return Q(V.threeDigits,n);case 4:return Q(V.fourDigits,n);default:return Q(new RegExp("^\\d{1,"+a+"}"),n)}}function ht(a,n){switch(a){case 1:return Q(V.singleDigitSigned,n);case 2:return Q(V.twoDigitsSigned,n);case 3:return Q(V.threeDigitsSigned,n);case 4:return Q(V.fourDigitsSigned,n);default:return Q(new RegExp("^-?\\d{1,"+a+"}"),n)}}function Ft(a){switch(a){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;case"am":case"midnight":case"night":default:return 0}}function Ir(a,n){const t=n>0,e=t?n:1-n;let r;if(e<=50)r=a||100;else{const o=e+50,i=Math.trunc(o/100)*100,s=a>=o%100;r=a+i-(s?100:0)}return t?r:1-r}function Rr(a){return a%400===0||a%4===0&&a%100!==0}class ga extends O{constructor(){super(...arguments);w(this,"priority",130);w(this,"incompatibleTokens",["Y","R","u","w","I","i","e","c","t","T"])}parse(t,e,r){const o=i=>({year:i,isTwoDigitYear:e==="yy"});switch(e){case"y":return U(q(4,t),o);case"yo":return U(r.ordinalNumber(t,{unit:"year"}),o);default:return U(q(e.length,t),o)}}validate(t,e){return e.isTwoDigitYear||e.year>0}set(t,e,r){const o=t.getFullYear();if(r.isTwoDigitYear){const s=Ir(r.year,o);return t.setFullYear(s,0,1),t.setHours(0,0,0,0),t}const i=!("era"in e)||e.era===1?r.year:1-r.year;return t.setFullYear(i,0,1),t.setHours(0,0,0,0),t}}class Da extends O{constructor(){super(...arguments);w(this,"priority",130);w(this,"incompatibleTokens",["y","R","u","Q","q","M","L","I","d","D","i","t","T"])}parse(t,e,r){const o=i=>({year:i,isTwoDigitYear:e==="YY"});switch(e){case"Y":return U(q(4,t),o);case"Yo":return U(r.ordinalNumber(t,{unit:"year"}),o);default:return U(q(e.length,t),o)}}validate(t,e){return e.isTwoDigitYear||e.year>0}set(t,e,r,o){const i=Lt(t,o);if(r.isTwoDigitYear){const c=Ir(r.year,i);return t.setFullYear(c,0,o.firstWeekContainsDate),t.setHours(0,0,0,0),De(t,o)}const s=!("era"in e)||e.era===1?r.year:1-r.year;return t.setFullYear(s,0,o.firstWeekContainsDate),t.setHours(0,0,0,0),De(t,o)}}class wa extends O{constructor(){super(...arguments);w(this,"priority",130);w(this,"incompatibleTokens",["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"])}parse(t,e){return ht(e==="R"?4:e.length,t)}set(t,e,r){const o=L(t,0);return o.setFullYear(r,0,4),o.setHours(0,0,0,0),Re(o)}}class ya extends O{constructor(){super(...arguments);w(this,"priority",130);w(this,"incompatibleTokens",["G","y","Y","R","w","I","i","e","c","t","T"])}parse(t,e){return ht(e==="u"?4:e.length,t)}set(t,e,r){return t.setFullYear(r,0,1),t.setHours(0,0,0,0),t}}class ba extends O{constructor(){super(...arguments);w(this,"priority",120);w(this,"incompatibleTokens",["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"])}parse(t,e,r){switch(e){case"Q":case"QQ":return q(e.length,t);case"Qo":return r.ordinalNumber(t,{unit:"quarter"});case"QQQ":return r.quarter(t,{width:"abbreviated",context:"formatting"})||r.quarter(t,{width:"narrow",context:"formatting"});case"QQQQQ":return r.quarter(t,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(t,{width:"wide",context:"formatting"})||r.quarter(t,{width:"abbreviated",context:"formatting"})||r.quarter(t,{width:"narrow",context:"formatting"})}}validate(t,e){return e>=1&&e<=4}set(t,e,r){return t.setMonth((r-1)*3,1),t.setHours(0,0,0,0),t}}class ka extends O{constructor(){super(...arguments);w(this,"priority",120);w(this,"incompatibleTokens",["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"])}parse(t,e,r){switch(e){case"q":case"qq":return q(e.length,t);case"qo":return r.ordinalNumber(t,{unit:"quarter"});case"qqq":return r.quarter(t,{width:"abbreviated",context:"standalone"})||r.quarter(t,{width:"narrow",context:"standalone"});case"qqqqq":return r.quarter(t,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(t,{width:"wide",context:"standalone"})||r.quarter(t,{width:"abbreviated",context:"standalone"})||r.quarter(t,{width:"narrow",context:"standalone"})}}validate(t,e){return e>=1&&e<=4}set(t,e,r){return t.setMonth((r-1)*3,1),t.setHours(0,0,0,0),t}}class _a extends O{constructor(){super(...arguments);w(this,"incompatibleTokens",["Y","R","q","Q","L","w","I","D","i","e","c","t","T"]);w(this,"priority",110)}parse(t,e,r){const o=i=>i-1;switch(e){case"M":return U(Q(V.month,t),o);case"MM":return U(q(2,t),o);case"Mo":return U(r.ordinalNumber(t,{unit:"month"}),o);case"MMM":return r.month(t,{width:"abbreviated",context:"formatting"})||r.month(t,{width:"narrow",context:"formatting"});case"MMMMM":return r.month(t,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(t,{width:"wide",context:"formatting"})||r.month(t,{width:"abbreviated",context:"formatting"})||r.month(t,{width:"narrow",context:"formatting"})}}validate(t,e){return e>=0&&e<=11}set(t,e,r){return t.setMonth(r,1),t.setHours(0,0,0,0),t}}class Ma extends O{constructor(){super(...arguments);w(this,"priority",110);w(this,"incompatibleTokens",["Y","R","q","Q","M","w","I","D","i","e","c","t","T"])}parse(t,e,r){const o=i=>i-1;switch(e){case"L":return U(Q(V.month,t),o);case"LL":return U(q(2,t),o);case"Lo":return U(r.ordinalNumber(t,{unit:"month"}),o);case"LLL":return r.month(t,{width:"abbreviated",context:"standalone"})||r.month(t,{width:"narrow",context:"standalone"});case"LLLLL":return r.month(t,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(t,{width:"wide",context:"standalone"})||r.month(t,{width:"abbreviated",context:"standalone"})||r.month(t,{width:"narrow",context:"standalone"})}}validate(t,e){return e>=0&&e<=11}set(t,e,r){return t.setMonth(r,1),t.setHours(0,0,0,0),t}}function Ca(a,n,t){const e=b(a,t==null?void 0:t.in),r=Tr(e,t)-n;return e.setDate(e.getDate()-r*7),b(e,t==null?void 0:t.in)}class xa extends O{constructor(){super(...arguments);w(this,"priority",100);w(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","i","t","T"])}parse(t,e,r){switch(e){case"w":return Q(V.week,t);case"wo":return r.ordinalNumber(t,{unit:"week"});default:return q(e.length,t)}}validate(t,e){return e>=1&&e<=53}set(t,e,r,o){return De(Ca(t,r,o),o)}}function Sa(a,n,t){const e=b(a,t==null?void 0:t.in),r=Rt(e,t)-n;return e.setDate(e.getDate()-r*7),e}class Pa extends O{constructor(){super(...arguments);w(this,"priority",100);w(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"])}parse(t,e,r){switch(e){case"I":return Q(V.week,t);case"Io":return r.ordinalNumber(t,{unit:"week"});default:return q(e.length,t)}}validate(t,e){return e>=1&&e<=53}set(t,e,r){return Re(Sa(t,r))}}const Ea=[31,28,31,30,31,30,31,31,30,31,30,31],Ta=[31,29,31,30,31,30,31,31,30,31,30,31];class Oa extends O{constructor(){super(...arguments);w(this,"priority",90);w(this,"subPriority",1);w(this,"incompatibleTokens",["Y","R","q","Q","w","I","D","i","e","c","t","T"])}parse(t,e,r){switch(e){case"d":return Q(V.date,t);case"do":return r.ordinalNumber(t,{unit:"date"});default:return q(e.length,t)}}validate(t,e){const r=t.getFullYear(),o=Rr(r),i=t.getMonth();return o?e>=1&&e<=Ta[i]:e>=1&&e<=Ea[i]}set(t,e,r){return t.setDate(r),t.setHours(0,0,0,0),t}}class Na extends O{constructor(){super(...arguments);w(this,"priority",90);w(this,"subpriority",1);w(this,"incompatibleTokens",["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"])}parse(t,e,r){switch(e){case"D":case"DD":return Q(V.dayOfYear,t);case"Do":return r.ordinalNumber(t,{unit:"date"});default:return q(e.length,t)}}validate(t,e){const r=t.getFullYear();return Rr(r)?e>=1&&e<=366:e>=1&&e<=365}set(t,e,r){return t.setMonth(0,r),t.setHours(0,0,0,0),t}}function Wt(a,n,t){var d,p,h,m;const e=Oe(),r=(t==null?void 0:t.weekStartsOn)??((p=(d=t==null?void 0:t.locale)==null?void 0:d.options)==null?void 0:p.weekStartsOn)??e.weekStartsOn??((m=(h=e.locale)==null?void 0:h.options)==null?void 0:m.weekStartsOn)??0,o=b(a,t==null?void 0:t.in),i=o.getDay(),c=(n%7+7)%7,u=7-r,l=n<0||n>6?n-(i+u)%7:(c+u)%7-(i+u)%7;return oe(o,l,t)}class Ya extends O{constructor(){super(...arguments);w(this,"priority",90);w(this,"incompatibleTokens",["D","i","e","c","t","T"])}parse(t,e,r){switch(e){case"E":case"EE":case"EEE":return r.day(t,{width:"abbreviated",context:"formatting"})||r.day(t,{width:"short",context:"formatting"})||r.day(t,{width:"narrow",context:"formatting"});case"EEEEE":return r.day(t,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(t,{width:"short",context:"formatting"})||r.day(t,{width:"narrow",context:"formatting"});case"EEEE":default:return r.day(t,{width:"wide",context:"formatting"})||r.day(t,{width:"abbreviated",context:"formatting"})||r.day(t,{width:"short",context:"formatting"})||r.day(t,{width:"narrow",context:"formatting"})}}validate(t,e){return e>=0&&e<=6}set(t,e,r,o){return t=Wt(t,r,o),t.setHours(0,0,0,0),t}}class Ia extends O{constructor(){super(...arguments);w(this,"priority",90);w(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"])}parse(t,e,r,o){const i=s=>{const c=Math.floor((s-1)/7)*7;return(s+o.weekStartsOn+6)%7+c};switch(e){case"e":case"ee":return U(q(e.length,t),i);case"eo":return U(r.ordinalNumber(t,{unit:"day"}),i);case"eee":return r.day(t,{width:"abbreviated",context:"formatting"})||r.day(t,{width:"short",context:"formatting"})||r.day(t,{width:"narrow",context:"formatting"});case"eeeee":return r.day(t,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(t,{width:"short",context:"formatting"})||r.day(t,{width:"narrow",context:"formatting"});case"eeee":default:return r.day(t,{width:"wide",context:"formatting"})||r.day(t,{width:"abbreviated",context:"formatting"})||r.day(t,{width:"short",context:"formatting"})||r.day(t,{width:"narrow",context:"formatting"})}}validate(t,e){return e>=0&&e<=6}set(t,e,r,o){return t=Wt(t,r,o),t.setHours(0,0,0,0),t}}class Ra extends O{constructor(){super(...arguments);w(this,"priority",90);w(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"])}parse(t,e,r,o){const i=s=>{const c=Math.floor((s-1)/7)*7;return(s+o.weekStartsOn+6)%7+c};switch(e){case"c":case"cc":return U(q(e.length,t),i);case"co":return U(r.ordinalNumber(t,{unit:"day"}),i);case"ccc":return r.day(t,{width:"abbreviated",context:"standalone"})||r.day(t,{width:"short",context:"standalone"})||r.day(t,{width:"narrow",context:"standalone"});case"ccccc":return r.day(t,{width:"narrow",context:"standalone"});case"cccccc":return r.day(t,{width:"short",context:"standalone"})||r.day(t,{width:"narrow",context:"standalone"});case"cccc":default:return r.day(t,{width:"wide",context:"standalone"})||r.day(t,{width:"abbreviated",context:"standalone"})||r.day(t,{width:"short",context:"standalone"})||r.day(t,{width:"narrow",context:"standalone"})}}validate(t,e){return e>=0&&e<=6}set(t,e,r,o){return t=Wt(t,r,o),t.setHours(0,0,0,0),t}}function La(a,n,t){const e=b(a,t==null?void 0:t.in),r=la(e,t),o=n-r;return oe(e,o,t)}class Fa extends O{constructor(){super(...arguments);w(this,"priority",90);w(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"])}parse(t,e,r){const o=i=>i===0?7:i;switch(e){case"i":case"ii":return q(e.length,t);case"io":return r.ordinalNumber(t,{unit:"day"});case"iii":return U(r.day(t,{width:"abbreviated",context:"formatting"})||r.day(t,{width:"short",context:"formatting"})||r.day(t,{width:"narrow",context:"formatting"}),o);case"iiiii":return U(r.day(t,{width:"narrow",context:"formatting"}),o);case"iiiiii":return U(r.day(t,{width:"short",context:"formatting"})||r.day(t,{width:"narrow",context:"formatting"}),o);case"iiii":default:return U(r.day(t,{width:"wide",context:"formatting"})||r.day(t,{width:"abbreviated",context:"formatting"})||r.day(t,{width:"short",context:"formatting"})||r.day(t,{width:"narrow",context:"formatting"}),o)}}validate(t,e){return e>=1&&e<=7}set(t,e,r){return t=La(t,r),t.setHours(0,0,0,0),t}}class Wa extends O{constructor(){super(...arguments);w(this,"priority",80);w(this,"incompatibleTokens",["b","B","H","k","t","T"])}parse(t,e,r){switch(e){case"a":case"aa":case"aaa":return r.dayPeriod(t,{width:"abbreviated",context:"formatting"})||r.dayPeriod(t,{width:"narrow",context:"formatting"});case"aaaaa":return r.dayPeriod(t,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(t,{width:"wide",context:"formatting"})||r.dayPeriod(t,{width:"abbreviated",context:"formatting"})||r.dayPeriod(t,{width:"narrow",context:"formatting"})}}set(t,e,r){return t.setHours(Ft(r),0,0,0),t}}class Aa extends O{constructor(){super(...arguments);w(this,"priority",80);w(this,"incompatibleTokens",["a","B","H","k","t","T"])}parse(t,e,r){switch(e){case"b":case"bb":case"bbb":return r.dayPeriod(t,{width:"abbreviated",context:"formatting"})||r.dayPeriod(t,{width:"narrow",context:"formatting"});case"bbbbb":return r.dayPeriod(t,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(t,{width:"wide",context:"formatting"})||r.dayPeriod(t,{width:"abbreviated",context:"formatting"})||r.dayPeriod(t,{width:"narrow",context:"formatting"})}}set(t,e,r){return t.setHours(Ft(r),0,0,0),t}}class Ha extends O{constructor(){super(...arguments);w(this,"priority",80);w(this,"incompatibleTokens",["a","b","t","T"])}parse(t,e,r){switch(e){case"B":case"BB":case"BBB":return r.dayPeriod(t,{width:"abbreviated",context:"formatting"})||r.dayPeriod(t,{width:"narrow",context:"formatting"});case"BBBBB":return r.dayPeriod(t,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(t,{width:"wide",context:"formatting"})||r.dayPeriod(t,{width:"abbreviated",context:"formatting"})||r.dayPeriod(t,{width:"narrow",context:"formatting"})}}set(t,e,r){return t.setHours(Ft(r),0,0,0),t}}class Qa extends O{constructor(){super(...arguments);w(this,"priority",70);w(this,"incompatibleTokens",["H","K","k","t","T"])}parse(t,e,r){switch(e){case"h":return Q(V.hour12h,t);case"ho":return r.ordinalNumber(t,{unit:"hour"});default:return q(e.length,t)}}validate(t,e){return e>=1&&e<=12}set(t,e,r){const o=t.getHours()>=12;return o&&r<12?t.setHours(r+12,0,0,0):!o&&r===12?t.setHours(0,0,0,0):t.setHours(r,0,0,0),t}}class Ba extends O{constructor(){super(...arguments);w(this,"priority",70);w(this,"incompatibleTokens",["a","b","h","K","k","t","T"])}parse(t,e,r){switch(e){case"H":return Q(V.hour23h,t);case"Ho":return r.ordinalNumber(t,{unit:"hour"});default:return q(e.length,t)}}validate(t,e){return e>=0&&e<=23}set(t,e,r){return t.setHours(r,0,0,0),t}}class Ka extends O{constructor(){super(...arguments);w(this,"priority",70);w(this,"incompatibleTokens",["h","H","k","t","T"])}parse(t,e,r){switch(e){case"K":return Q(V.hour11h,t);case"Ko":return r.ordinalNumber(t,{unit:"hour"});default:return q(e.length,t)}}validate(t,e){return e>=0&&e<=11}set(t,e,r){return t.getHours()>=12&&r<12?t.setHours(r+12,0,0,0):t.setHours(r,0,0,0),t}}class qa extends O{constructor(){super(...arguments);w(this,"priority",70);w(this,"incompatibleTokens",["a","b","h","H","K","t","T"])}parse(t,e,r){switch(e){case"k":return Q(V.hour24h,t);case"ko":return r.ordinalNumber(t,{unit:"hour"});default:return q(e.length,t)}}validate(t,e){return e>=1&&e<=24}set(t,e,r){const o=r<=24?r%24:r;return t.setHours(o,0,0,0),t}}class Va extends O{constructor(){super(...arguments);w(this,"priority",60);w(this,"incompatibleTokens",["t","T"])}parse(t,e,r){switch(e){case"m":return Q(V.minute,t);case"mo":return r.ordinalNumber(t,{unit:"minute"});default:return q(e.length,t)}}validate(t,e){return e>=0&&e<=59}set(t,e,r){return t.setMinutes(r,0,0),t}}class Ua extends O{constructor(){super(...arguments);w(this,"priority",50);w(this,"incompatibleTokens",["t","T"])}parse(t,e,r){switch(e){case"s":return Q(V.second,t);case"so":return r.ordinalNumber(t,{unit:"second"});default:return q(e.length,t)}}validate(t,e){return e>=0&&e<=59}set(t,e,r){return t.setSeconds(r,0),t}}class ja extends O{constructor(){super(...arguments);w(this,"priority",30);w(this,"incompatibleTokens",["t","T"])}parse(t,e){const r=o=>Math.trunc(o*Math.pow(10,-e.length+3));return U(q(e.length,t),r)}set(t,e,r){return t.setMilliseconds(r),t}}class Xa extends O{constructor(){super(...arguments);w(this,"priority",10);w(this,"incompatibleTokens",["t","T","x"])}parse(t,e){switch(e){case"X":return de(ue.basicOptionalMinutes,t);case"XX":return de(ue.basic,t);case"XXXX":return de(ue.basicOptionalSeconds,t);case"XXXXX":return de(ue.extendedOptionalSeconds,t);case"XXX":default:return de(ue.extended,t)}}set(t,e,r){return e.timestampIsSet?t:L(t,t.getTime()-ct(t)-r)}}class Ga extends O{constructor(){super(...arguments);w(this,"priority",10);w(this,"incompatibleTokens",["t","T","X"])}parse(t,e){switch(e){case"x":return de(ue.basicOptionalMinutes,t);case"xx":return de(ue.basic,t);case"xxxx":return de(ue.basicOptionalSeconds,t);case"xxxxx":return de(ue.extendedOptionalSeconds,t);case"xxx":default:return de(ue.extended,t)}}set(t,e,r){return e.timestampIsSet?t:L(t,t.getTime()-ct(t)-r)}}class $a extends O{constructor(){super(...arguments);w(this,"priority",40);w(this,"incompatibleTokens","*")}parse(t){return Yr(t)}set(t,e,r){return[L(t,r*1e3),{timestampIsSet:!0}]}}class za extends O{constructor(){super(...arguments);w(this,"priority",20);w(this,"incompatibleTokens","*")}parse(t){return Yr(t)}set(t,e,r){return[L(t,r),{timestampIsSet:!0}]}}const Za={G:new va,y:new ga,Y:new Da,R:new wa,u:new ya,Q:new ba,q:new ka,M:new _a,L:new Ma,w:new xa,I:new Pa,d:new Oa,D:new Na,E:new Ya,e:new Ia,c:new Ra,i:new Fa,a:new Wa,b:new Aa,B:new Ha,h:new Qa,H:new Ba,K:new Ka,k:new qa,m:new Va,s:new Ua,S:new ja,X:new Xa,x:new Ga,t:new $a,T:new za},Ja=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,eo=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,to=/^'([^]*?)'?$/,ro=/''/g,no=/\S/,ao=/[a-zA-Z]/;function oo(a,n,t,e){var v,k,x,M,T,B,A,N;const r=()=>L((e==null?void 0:e.in)||t,NaN),o=ca(),i=(e==null?void 0:e.locale)??o.locale??Er,s=(e==null?void 0:e.firstWeekContainsDate)??((k=(v=e==null?void 0:e.locale)==null?void 0:v.options)==null?void 0:k.firstWeekContainsDate)??o.firstWeekContainsDate??((M=(x=o.locale)==null?void 0:x.options)==null?void 0:M.firstWeekContainsDate)??1,c=(e==null?void 0:e.weekStartsOn)??((B=(T=e==null?void 0:e.locale)==null?void 0:T.options)==null?void 0:B.weekStartsOn)??o.weekStartsOn??((N=(A=o.locale)==null?void 0:A.options)==null?void 0:N.weekStartsOn)??0;if(!n)return a?r():b(t,e==null?void 0:e.in);const u={firstWeekContainsDate:s,weekStartsOn:c,locale:i},l=[new ma(e==null?void 0:e.in,t)],d=n.match(eo).map(_=>{const C=_[0];if(C in Ot){const I=Ot[C];return I(_,i.formatLong)}return _}).join("").match(Ja),p=[];for(let _ of d){const C=_[0],I=Za[C];if(I){const{incompatibleTokens:j}=I;if(Array.isArray(j)){const z=p.find(se=>j.includes(se.token)||se.token===C);if(z)throw new RangeError(`The format string mustn't contain \`${z.fullToken}\` and \`${_}\` at the same time`)}else if(I.incompatibleTokens==="*"&&p.length>0)throw new RangeError(`The format string mustn't contain \`${_}\` and any other token at the same time`);p.push({token:C,fullToken:_});const K=I.run(a,_,i.match,u);if(!K)return r();l.push(K.setter),a=K.rest}else{if(C.match(ao))throw new RangeError("Format string contains an unescaped latin alphabet character `"+C+"`");if(_==="''"?_="'":C==="'"&&(_=io(_)),a.indexOf(_)===0)a=a.slice(_.length);else return r()}}if(a.length>0&&no.test(a))return r();const h=l.map(_=>_.priority).sort((_,C)=>C-_).filter((_,C,I)=>I.indexOf(_)===C).map(_=>l.filter(C=>C.priority===_).sort((C,I)=>I.subPriority-C.subPriority)).map(_=>_[0]);let m=b(t,e==null?void 0:e.in);if(isNaN(+m))return r();const g={};for(const _ of h){if(!_.validate(m,u))return r();const C=_.set(m,g,u);Array.isArray(C)?(m=C[0],Object.assign(g,C[1])):m=C}return m}function io(a){return a.match(to)[1].replace(ro,"'")}function so(a,n,t){const[e,r]=we(t==null?void 0:t.in,a,n);return e.getFullYear()===r.getFullYear()&&e.getMonth()===r.getMonth()}function co(a,n,t){const[e,r]=we(t==null?void 0:t.in,a,n);return+Tt(e)==+Tt(r)}function lo(a,n,t){const[e,r]=we(t==null?void 0:t.in,a,n);return e.getFullYear()===r.getFullYear()}function je(a,n,t){const e=+b(a,t==null?void 0:t.in),[r,o]=[+b(n.start,t==null?void 0:t.in),+b(n.end,t==null?void 0:t.in)].sort((i,s)=>i-s);return e>=r&&e<=o}function uo(a,n,t){return oe(a,-1,t)}function po(a,n){const t=()=>L(n==null?void 0:n.in,NaN),r=vo(a);let o;if(r.date){const u=go(r.date,2);o=Do(u.restDateString,u.year)}if(!o||isNaN(+o))return t();const i=+o;let s=0,c;if(r.time&&(s=wo(r.time),isNaN(s)))return t();if(r.timezone){if(c=yo(r.timezone),isNaN(c))return t()}else{const u=new Date(i+s),l=b(0,n==null?void 0:n.in);return l.setFullYear(u.getUTCFullYear(),u.getUTCMonth(),u.getUTCDate()),l.setHours(u.getUTCHours(),u.getUTCMinutes(),u.getUTCSeconds(),u.getUTCMilliseconds()),l}return b(i+s+c,n==null?void 0:n.in)}const ze={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},fo=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,ho=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,mo=/^([+-])(\d{2})(?::?(\d{2}))?$/;function vo(a){const n={},t=a.split(ze.dateTimeDelimiter);let e;if(t.length>2)return n;if(/:/.test(t[0])?e=t[0]:(n.date=t[0],e=t[1],ze.timeZoneDelimiter.test(n.date)&&(n.date=a.split(ze.timeZoneDelimiter)[0],e=a.substr(n.date.length,a.length))),e){const r=ze.timezone.exec(e);r?(n.time=e.replace(r[1],""),n.timezone=r[1]):n.time=e}return n}function go(a,n){const t=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+n)+"})|(\\d{2}|[+-]\\d{"+(2+n)+"})$)"),e=a.match(t);if(!e)return{year:NaN,restDateString:""};const r=e[1]?parseInt(e[1]):null,o=e[2]?parseInt(e[2]):null;return{year:o===null?r:o*100,restDateString:a.slice((e[1]||e[2]).length)}}function Do(a,n){if(n===null)return new Date(NaN);const t=a.match(fo);if(!t)return new Date(NaN);const e=!!t[4],r=Ke(t[1]),o=Ke(t[2])-1,i=Ke(t[3]),s=Ke(t[4]),c=Ke(t[5])-1;if(e)return Co(n,s,c)?bo(n,s,c):new Date(NaN);{const u=new Date(0);return!_o(n,o,i)||!Mo(n,r)?new Date(NaN):(u.setUTCFullYear(n,o,Math.max(r,i)),u)}}function Ke(a){return a?parseInt(a):1}function wo(a){const n=a.match(ho);if(!n)return NaN;const t=_t(n[1]),e=_t(n[2]),r=_t(n[3]);return xo(t,e,r)?t*Dt+e*gt+r*1e3:NaN}function _t(a){return a&&parseFloat(a.replace(",","."))||0}function yo(a){if(a==="Z")return 0;const n=a.match(mo);if(!n)return 0;const t=n[1]==="+"?-1:1,e=parseInt(n[2]),r=n[3]&&parseInt(n[3])||0;return So(e,r)?t*(e*Dt+r*gt):NaN}function bo(a,n,t){const e=new Date(0);e.setUTCFullYear(a,0,4);const r=e.getUTCDay()||7,o=(n-1)*7+t+1-r;return e.setUTCDate(e.getUTCDate()+o),e}const ko=[31,null,31,30,31,30,31,31,30,31,30,31];function Lr(a){return a%400===0||a%4===0&&a%100!==0}function _o(a,n,t){return n>=0&&n<=11&&t>=1&&t<=(ko[n]||(Lr(a)?29:28))}function Mo(a,n){return n>=1&&n<=(Lr(a)?366:365)}function Co(a,n,t){return n>=1&&n<=53&&t>=0&&t<=6}function xo(a,n,t){return a===24?n===0&&t===0:t>=0&&t<60&&n>=0&&n<60&&a>=0&&a<25}function So(a,n){return n>=0&&n<=59}function re(a,n,t){const e=b(a,t==null?void 0:t.in),r=e.getFullYear(),o=e.getDate(),i=L(a,0);i.setFullYear(r,n,15),i.setHours(0,0,0,0);const s=sa(i);return e.setMonth(n,Math.min(o,s)),e}function nt(a,n,t){const e=b(a,t==null?void 0:t.in);return e.setHours(n),e}function at(a,n,t){const e=b(a,t==null?void 0:t.in);return e.setMinutes(n),e}function Ye(a,n,t){const e=b(a,t==null?void 0:t.in),r=Math.trunc(e.getMonth()/3)+1,o=n-r;return re(e,e.getMonth()+o*3)}function ot(a,n,t){const e=b(a,t==null?void 0:t.in);return e.setSeconds(n),e}function ce(a,n,t){const e=b(a,t==null?void 0:t.in);return isNaN(+e)?L(a,NaN):(e.setFullYear(n),e)}function Pe(a,n,t){return ie(a,-n,t)}function Fr(a,n,t){return It(a,-1,t)}function Jt(a,n,t){return lt(a,-1,t)}function Fe(a,n,t){return fe(a,-n,t)}var mt=typeof document<"u"?y.useLayoutEffect:y.useEffect;const Po={...yr},Eo=Po.useInsertionEffect,To=Eo||(a=>a());function Oo(a){const n=y.useRef(()=>{});return To(()=>{n.current=a}),y.useCallback(function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return n.current==null?void 0:n.current(...e)},[])}const No={...yr};let er=!1,Yo=0;const tr=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Yo++;function Io(){const[a,n]=y.useState(()=>er?tr():void 0);return mt(()=>{a==null&&n(tr())},[]),y.useEffect(()=>{er=!0},[]),a}const Ro=No.useId,Wr=Ro||Io,Lo=y.forwardRef(function(n,t){const{context:{placement:e,elements:{floating:r},middlewareData:{arrow:o,shift:i}},width:s=14,height:c=7,tipRadius:u=0,strokeWidth:l=0,staticOffset:d,stroke:p,d:h,style:{transform:m,...g}={},...v}=n,k=Wr(),[x,M]=y.useState(!1);if(mt(()=>{if(!r)return;$r(r).direction==="rtl"&&M(!0)},[r]),!r)return null;const[T,B]=e.split("-"),A=T==="top"||T==="bottom";let N=d;(A&&i!=null&&i.x||!A&&i!=null&&i.y)&&(N=null);const _=l*2,C=_/2,I=s/2*(u/-8+1),j=c/2*u/4,K=!!h,z=N&&B==="end"?"bottom":"top";let se=N&&B==="end"?"right":"left";N&&x&&(se=B==="end"?"left":"right");const X=(o==null?void 0:o.x)!=null?N||o.x:"",F=(o==null?void 0:o.y)!=null?N||o.y:"",H=h||"M0,0"+(" H"+s)+(" L"+(s-I)+","+(c-j))+(" Q"+s/2+","+c+" "+I+","+(c-j))+" Z",ee={top:K?"rotate(180deg)":"",left:K?"rotate(90deg)":"rotate(-90deg)",bottom:K?"":"rotate(180deg)",right:K?"rotate(-90deg)":"rotate(90deg)"}[T];return He.jsxs("svg",{...v,"aria-hidden":!0,ref:t,width:K?s:s+_,height:s,viewBox:"0 0 "+s+" "+(c>s?c:s),style:{position:"absolute",pointerEvents:"none",[se]:X,[z]:F,[T]:A||K?"100%":"calc(100% - "+_/2+"px)",transform:[ee,m].filter(Ae=>!!Ae).join(" "),...g},children:[_>0&&He.jsx("path",{clipPath:"url(#"+k+")",fill:"none",stroke:p,strokeWidth:_+(h?0:1),d:H}),He.jsx("path",{stroke:_&&!h?v.fill:"none",d:H}),He.jsx("clipPath",{id:k,children:He.jsx("rect",{x:-C,y:C*(K?-1:1),width:s+_,height:s})})]})});function Fo(){const a=new Map;return{emit(n,t){var e;(e=a.get(n))==null||e.forEach(r=>r(t))},on(n,t){a.has(n)||a.set(n,new Set),a.get(n).add(t)},off(n,t){var e;(e=a.get(n))==null||e.delete(t)}}}const Wo=y.createContext(null),Ao=y.createContext(null),Ho=()=>{var a;return((a=y.useContext(Wo))==null?void 0:a.id)||null},Qo=()=>y.useContext(Ao);function Bo(a){const{open:n=!1,onOpenChange:t,elements:e}=a,r=Wr(),o=y.useRef({}),[i]=y.useState(()=>Fo()),s=Ho()!=null,[c,u]=y.useState(e.reference),l=Oo((h,m,g)=>{o.current.openEvent=h?m:void 0,i.emit("openchange",{open:h,event:m,reason:g,nested:s}),t==null||t(h,m,g)}),d=y.useMemo(()=>({setPositionReference:u}),[]),p=y.useMemo(()=>({reference:c||e.reference||null,floating:e.floating||null,domReference:e.reference}),[c,e.reference,e.floating]);return y.useMemo(()=>({dataRef:o,open:n,onOpenChange:l,elements:p,events:i,floatingId:r,refs:d}),[n,l,p,i,r,d])}function Ko(a){a===void 0&&(a={});const{nodeId:n}=a,t=Bo({...a,elements:{reference:null,floating:null,...a.elements}}),e=a.rootContext||t,r=e.elements,[o,i]=y.useState(null),[s,c]=y.useState(null),l=(r==null?void 0:r.domReference)||o,d=y.useRef(null),p=Qo();mt(()=>{l&&(d.current=l)},[l]);const h=Gr({...a,elements:{...r,...s&&{reference:s}}}),m=y.useCallback(M=>{const T=$e(M)?{getBoundingClientRect:()=>M.getBoundingClientRect(),getClientRects:()=>M.getClientRects(),contextElement:M}:M;c(T),h.refs.setReference(T)},[h.refs]),g=y.useCallback(M=>{($e(M)||M===null)&&(d.current=M,i(M)),($e(h.refs.reference.current)||h.refs.reference.current===null||M!==null&&!$e(M))&&h.refs.setReference(M)},[h.refs]),v=y.useMemo(()=>({...h.refs,setReference:g,setPositionReference:m,domReference:d}),[h.refs,g,m]),k=y.useMemo(()=>({...h.elements,domReference:l}),[h.elements,l]),x=y.useMemo(()=>({...h,...e,refs:v,elements:k,nodeId:n}),[h,v,k,n,e]);return mt(()=>{e.dataRef.current.floatingContext=x;const M=p==null?void 0:p.nodesRef.current.find(T=>T.id===n);M&&(M.context=x)}),y.useMemo(()=>({...h,context:x,refs:v,elements:k}),[h,v,k,x])}/*!
  react-datepicker v8.3.0
  https://github.com/Hacker0x01/react-datepicker
  Released under the MIT License.
*/var Yt=function(n,t){return Yt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])},Yt(n,t)};function G(a,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");Yt(a,n);function t(){this.constructor=a}a.prototype=n===null?Object.create(n):(t.prototype=n.prototype,new t)}var E=function(){return E=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++){e=arguments[r];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},E.apply(this,arguments)};function pe(a,n,t){if(t||arguments.length===2)for(var e=0,r=n.length,o;e<r;e++)(o||!(e in n))&&(o||(o=Array.prototype.slice.call(n,0,e)),o[e]=n[e]);return a.concat(o||Array.prototype.slice.call(n))}var qo=function(a){var n=a.showTimeSelectOnly,t=n===void 0?!1:n,e=a.showTime,r=e===void 0?!1:e,o=a.className,i=a.children,s=t?"Choose Time":"Choose Date".concat(r?" and Time":"");return f.createElement("div",{className:o,role:"dialog","aria-label":s,"aria-modal":"true"},i)},Vo=function(a,n){var t=y.useRef(null),e=y.useRef(a);e.current=a;var r=y.useCallback(function(o){var i,s=o.composed&&o.composedPath&&o.composedPath().find(function(c){return c instanceof Node})||o.target;t.current&&!t.current.contains(s)&&(n&&s instanceof HTMLElement&&s.classList.contains(n)||(i=e.current)===null||i===void 0||i.call(e,o))},[n]);return y.useEffect(function(){return document.addEventListener("mousedown",r),function(){document.removeEventListener("mousedown",r)}},[r]),t},yt=function(a){var n=a.children,t=a.onClickOutside,e=a.className,r=a.containerRef,o=a.style,i=a.ignoreClass,s=Vo(t,i);return f.createElement("div",{className:e,style:o,ref:function(c){s.current=c,r&&(r.current=c)}},n)},D;(function(a){a.ArrowUp="ArrowUp",a.ArrowDown="ArrowDown",a.ArrowLeft="ArrowLeft",a.ArrowRight="ArrowRight",a.PageUp="PageUp",a.PageDown="PageDown",a.Home="Home",a.End="End",a.Enter="Enter",a.Space=" ",a.Tab="Tab",a.Escape="Escape",a.Backspace="Backspace",a.X="x"})(D||(D={}));function Ar(){var a=typeof window<"u"?window:globalThis;return a}var Xe=12;function Y(a){if(a==null)return new Date;var n=typeof a=="string"?po(a):b(a);return bt(n)?n:new Date}function Mt(a,n,t,e,r){r===void 0&&(r=Y());for(var o=We(t)||We(At()),i=Array.isArray(n)?n:[n],s=0,c=i;s<c.length;s++){var u=c[s],l=oo(a,u,r,{locale:o});if(bt(l)&&(!e||a===W(l,u,t)))return l}return null}function bt(a,n){return ut(a)&&!Te(a,new Date("1/1/1800"))}function W(a,n,t){if(t==="en")return zt(a,n,{useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0});var e=t?We(t):void 0;return t&&!e&&console.warn('A locale object was not found for the provided string ["'.concat(t,'"].')),e=e||We(At()),zt(a,n,{locale:e,useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0})}function te(a,n){var t=n.dateFormat,e=n.locale,r=Array.isArray(t)&&t.length>0?t[0]:t;return a&&W(a,r,e)||""}function Uo(a,n,t){if(!a)return"";var e=te(a,t),r=n?te(n,t):"";return"".concat(e," - ").concat(r)}function jo(a,n){if(!(a!=null&&a.length))return"";var t=a[0]?te(a[0],n):"";if(a.length===1)return t;if(a.length===2&&a[1]){var e=te(a[1],n);return"".concat(t,", ").concat(e)}var r=a.length-1;return"".concat(t," (+").concat(r,")")}function Ct(a,n){var t=n.hour,e=t===void 0?0:t,r=n.minute,o=r===void 0?0:r,i=n.second,s=i===void 0?0:i;return nt(at(ot(a,s),o),e)}function Xo(a){return Rt(a)}function Go(a,n){return W(a,"ddd",n)}function it(a){return Ee(a)}function _e(a,n,t){var e=We(n||At());return De(a,{locale:e,weekStartsOn:t})}function ge(a){return Sr(a)}function Ve(a){return wt(a)}function rr(a){return Tt(a)}function nr(){return Ee(Y())}function ar(a){return Cr(a)}function $o(a){return dn(a)}function zo(a){return xr(a)}function le(a,n){return a&&n?lo(a,n):!a&&!n}function Z(a,n){return a&&n?so(a,n):!a&&!n}function vt(a,n){return a&&n?co(a,n):!a&&!n}function P(a,n){return a&&n?ln(a,n):!a&&!n}function xe(a,n){return a&&n?ua(a,n):!a&&!n}function Ue(a,n,t){var e,r=Ee(n),o=Cr(t);try{e=je(a,{start:r,end:o})}catch{e=!1}return e}function At(){var a=Ar();return a.__localeId__}function We(a){if(typeof a=="string"){var n=Ar();return n.__localeData__?n.__localeData__[a]:void 0}else return a}function Zo(a,n,t){return n(W(a,"EEEE",t))}function Jo(a,n){return W(a,"EEEEEE",n)}function ei(a,n){return W(a,"EEE",n)}function Ht(a,n){return W(re(Y(),a),"LLLL",n)}function Hr(a,n){return W(re(Y(),a),"LLL",n)}function ti(a,n){return W(Ye(Y(),a),"QQQ",n)}function ne(a,n){var t=n===void 0?{}:n,e=t.minDate,r=t.maxDate,o=t.excludeDates,i=t.excludeDateIntervals,s=t.includeDates,c=t.includeDateIntervals,u=t.filterDate;return Ge(a,{minDate:e,maxDate:r})||o&&o.some(function(l){return l instanceof Date?P(a,l):P(a,l.date)})||i&&i.some(function(l){var d=l.start,p=l.end;return je(a,{start:d,end:p})})||s&&!s.some(function(l){return P(a,l)})||c&&!c.some(function(l){var d=l.start,p=l.end;return je(a,{start:d,end:p})})||u&&!u(Y(a))||!1}function Qt(a,n){var t=n===void 0?{}:n,e=t.excludeDates,r=t.excludeDateIntervals;return r&&r.length>0?r.some(function(o){var i=o.start,s=o.end;return je(a,{start:i,end:s})}):e&&e.some(function(o){var i;return o instanceof Date?P(a,o):P(a,(i=o.date)!==null&&i!==void 0?i:new Date)})||!1}function Qr(a,n){var t=n===void 0?{}:n,e=t.minDate,r=t.maxDate,o=t.excludeDates,i=t.includeDates,s=t.filterDate;return Ge(a,{minDate:e?Sr(e):void 0,maxDate:r?xr(r):void 0})||(o==null?void 0:o.some(function(c){return Z(a,c instanceof Date?c:c.date)}))||i&&!i.some(function(c){return Z(a,c)})||s&&!s(Y(a))||!1}function Ze(a,n,t,e){var r=S(a),o=J(a),i=S(n),s=J(n),c=S(e);return r===i&&r===c?o<=t&&t<=s:r<i?c===r&&o<=t||c===i&&s>=t||c<i&&c>r:!1}function ri(a,n){var t=n===void 0?{}:n,e=t.minDate,r=t.maxDate,o=t.excludeDates,i=t.includeDates;return Ge(a,{minDate:e,maxDate:r})||o&&o.some(function(s){return Z(s instanceof Date?s:s.date,a)})||i&&!i.some(function(s){return Z(s,a)})||!1}function Je(a,n){var t=n===void 0?{}:n,e=t.minDate,r=t.maxDate,o=t.excludeDates,i=t.includeDates,s=t.filterDate;return Ge(a,{minDate:e,maxDate:r})||(o==null?void 0:o.some(function(c){return vt(a,c instanceof Date?c:c.date)}))||i&&!i.some(function(c){return vt(a,c)})||s&&!s(Y(a))||!1}function et(a,n,t){if(!n||!t||!ut(n)||!ut(t))return!1;var e=S(n),r=S(t);return e<=a&&r>=a}function st(a,n){var t=n===void 0?{}:n,e=t.minDate,r=t.maxDate,o=t.excludeDates,i=t.includeDates,s=t.filterDate,c=new Date(a,0,1);return Ge(c,{minDate:e?wt(e):void 0,maxDate:r?Pr(r):void 0})||(o==null?void 0:o.some(function(u){return le(c,u instanceof Date?u:u.date)}))||i&&!i.some(function(u){return le(c,u)})||s&&!s(Y(c))||!1}function tt(a,n,t,e){var r=S(a),o=Se(a),i=S(n),s=Se(n),c=S(e);return r===i&&r===c?o<=t&&t<=s:r<i?c===r&&o<=t||c===i&&s>=t||c<i&&c>r:!1}function Ge(a,n){var t,e=n===void 0?{}:n,r=e.minDate,o=e.maxDate;return(t=r&&Le(a,r)<0||o&&Le(a,o)>0)!==null&&t!==void 0?t:!1}function or(a,n){return n.some(function(t){return me(t)===me(a)&&ve(t)===ve(a)&&ke(t)===ke(a)})}function ir(a,n){var t=n===void 0?{}:n,e=t.excludeTimes,r=t.includeTimes,o=t.filterTime;return e&&or(a,e)||r&&!or(a,r)||o&&!o(a)||!1}function sr(a,n){var t=n.minTime,e=n.maxTime;if(!t||!e)throw new Error("Both minTime and maxTime props required");var r=Y();r=nt(r,me(a)),r=at(r,ve(a)),r=ot(r,ke(a));var o=Y();o=nt(o,me(t)),o=at(o,ve(t)),o=ot(o,ke(t));var i=Y();i=nt(i,me(e)),i=at(i,ve(e)),i=ot(i,ke(e));var s;try{s=!je(r,{start:o,end:i})}catch{s=!1}return s}function cr(a,n){var t=n===void 0?{}:n,e=t.minDate,r=t.includeDates,o=Pe(a,1);return e&&dt(e,o)>0||r&&r.every(function(i){return dt(i,o)>0})||!1}function lr(a,n){var t=n===void 0?{}:n,e=t.maxDate,r=t.includeDates,o=ie(a,1);return e&&dt(o,e)>0||r&&r.every(function(i){return dt(o,i)>0})||!1}function ni(a,n){var t=n===void 0?{}:n,e=t.minDate,r=t.includeDates,o=wt(a),i=Fr(o);return e&&pt(e,i)>0||r&&r.every(function(s){return pt(s,i)>0})||!1}function ai(a,n){var t=n===void 0?{}:n,e=t.maxDate,r=t.includeDates,o=Pr(a),i=It(o,1);return e&&pt(i,e)>0||r&&r.every(function(s){return pt(i,s)>0})||!1}function ur(a,n){var t=n===void 0?{}:n,e=t.minDate,r=t.includeDates,o=Fe(a,1);return e&&ft(e,o)>0||r&&r.every(function(i){return ft(i,o)>0})||!1}function oi(a,n){var t=n===void 0?{}:n,e=t.minDate,r=t.yearItemNumber,o=r===void 0?Xe:r,i=Ve(Fe(a,o)),s=be(i,o).endPeriod,c=e&&S(e);return c&&c>s||!1}function dr(a,n){var t=n===void 0?{}:n,e=t.maxDate,r=t.includeDates,o=fe(a,1);return e&&ft(o,e)>0||r&&r.every(function(i){return ft(o,i)>0})||!1}function ii(a,n){var t=n===void 0?{}:n,e=t.maxDate,r=t.yearItemNumber,o=r===void 0?Xe:r,i=fe(a,o),s=be(i,o).startPeriod,c=e&&S(e);return c&&c<s||!1}function Br(a){var n=a.minDate,t=a.includeDates;if(t&&n){var e=t.filter(function(r){return Le(r,n)>=0});return Vt(e)}else return t?Vt(t):n}function Kr(a){var n=a.maxDate,t=a.includeDates;if(t&&n){var e=t.filter(function(r){return Le(r,n)<=0});return qt(e)}else return t?qt(t):n}function pr(a,n){var t;a===void 0&&(a=[]),n===void 0&&(n="react-datepicker__day--highlighted");for(var e=new Map,r=0,o=a.length;r<o;r++){var i=a[r];if(he(i)){var s=W(i,"MM.dd.yyyy"),c=e.get(s)||[];c.includes(n)||(c.push(n),e.set(s,c))}else if(typeof i=="object"){var u=Object.keys(i),l=(t=u[0])!==null&&t!==void 0?t:"",d=i[l];if(typeof l=="string"&&Array.isArray(d))for(var p=0,h=d.length;p<h;p++){var m=d[p];if(m){var s=W(m,"MM.dd.yyyy"),c=e.get(s)||[];c.includes(l)||(c.push(l),e.set(s,c))}}}}return e}function si(a,n){return a.length!==n.length?!1:a.every(function(t,e){return t===n[e]})}function ci(a,n){a===void 0&&(a=[]),n===void 0&&(n="react-datepicker__day--holidays");var t=new Map;return a.forEach(function(e){var r=e.date,o=e.holidayName;if(he(r)){var i=W(r,"MM.dd.yyyy"),s=t.get(i)||{className:"",holidayNames:[]};if(!("className"in s&&s.className===n&&si(s.holidayNames,[o]))){s.className=n;var c=s.holidayNames;s.holidayNames=c?pe(pe([],c,!0),[o],!1):[o],t.set(i,s)}}}),t}function li(a,n,t,e,r){for(var o=r.length,i=[],s=0;s<o;s++){var c=a,u=r[s];u&&(c=an(c,me(u)),c=Et(c,ve(u)),c=cn(c,ke(u)));var l=Et(a,(t+1)*e);Me(c,n)&&Te(c,l)&&u!=null&&i.push(u)}return i}function fr(a){return a<10?"0".concat(a):"".concat(a)}function be(a,n){n===void 0&&(n=Xe);var t=Math.ceil(S(a)/n)*n,e=t-(n-1);return{startPeriod:e,endPeriod:t}}function ui(a){var n=new Date(a.getFullYear(),a.getMonth(),a.getDate()),t=new Date(a.getFullYear(),a.getMonth(),a.getDate(),24);return Math.round((+t-+n)/36e5)}function hr(a){var n=a.getSeconds(),t=a.getMilliseconds();return b(a.getTime()-n*1e3-t)}function di(a,n){return hr(a).getTime()===hr(n).getTime()}function mr(a){if(!he(a))throw new Error("Invalid date");var n=new Date(a);return n.setHours(0,0,0,0),n}function vr(a,n){if(!he(a)||!he(n))throw new Error("Invalid date received");var t=mr(a),e=mr(n);return Te(t,e)}function qr(a){return a.key===D.Space}var pi=function(a){G(n,a);function n(t){var e=a.call(this,t)||this;return e.inputRef=f.createRef(),e.onTimeChange=function(r){var o,i;e.setState({time:r});var s=e.props.date,c=s instanceof Date&&!isNaN(+s),u=c?s:new Date;if(r!=null&&r.includes(":")){var l=r.split(":"),d=l[0],p=l[1];u.setHours(Number(d)),u.setMinutes(Number(p))}(i=(o=e.props).onChange)===null||i===void 0||i.call(o,u)},e.renderTimeInput=function(){var r=e.state.time,o=e.props,i=o.date,s=o.timeString,c=o.customTimeInput;return c?y.cloneElement(c,{date:i,value:r,onChange:e.onTimeChange}):f.createElement("input",{type:"time",className:"react-datepicker-time__input",placeholder:"Time",name:"time-input",ref:e.inputRef,onClick:function(){var u;(u=e.inputRef.current)===null||u===void 0||u.focus()},required:!0,value:r,onChange:function(u){e.onTimeChange(u.target.value||s)}})},e.state={time:e.props.timeString},e}return n.getDerivedStateFromProps=function(t,e){return t.timeString!==e.time?{time:t.timeString}:null},n.prototype.render=function(){return f.createElement("div",{className:"react-datepicker__input-time-container"},f.createElement("div",{className:"react-datepicker-time__caption"},this.props.timeInputLabel),f.createElement("div",{className:"react-datepicker-time__input-container"},f.createElement("div",{className:"react-datepicker-time__input"},this.renderTimeInput())))},n}(y.Component),fi=function(a){G(n,a);function n(){var t=a!==null&&a.apply(this,arguments)||this;return t.dayEl=y.createRef(),t.handleClick=function(e){!t.isDisabled()&&t.props.onClick&&t.props.onClick(e)},t.handleMouseEnter=function(e){!t.isDisabled()&&t.props.onMouseEnter&&t.props.onMouseEnter(e)},t.handleOnKeyDown=function(e){var r,o,i=e.key;i===D.Space&&(e.preventDefault(),e.key=D.Enter),(o=(r=t.props).handleOnKeyDown)===null||o===void 0||o.call(r,e)},t.isSameDay=function(e){return P(t.props.day,e)},t.isKeyboardSelected=function(){var e;if(t.props.disabledKeyboardNavigation)return!1;var r=t.props.selectsMultiple?(e=t.props.selectedDates)===null||e===void 0?void 0:e.some(function(i){return t.isSameDayOrWeek(i)}):t.isSameDayOrWeek(t.props.selected),o=t.props.preSelection&&t.isDisabled(t.props.preSelection);return!r&&t.isSameDayOrWeek(t.props.preSelection)&&!o},t.isDisabled=function(e){return e===void 0&&(e=t.props.day),ne(e,{minDate:t.props.minDate,maxDate:t.props.maxDate,excludeDates:t.props.excludeDates,excludeDateIntervals:t.props.excludeDateIntervals,includeDateIntervals:t.props.includeDateIntervals,includeDates:t.props.includeDates,filterDate:t.props.filterDate})},t.isExcluded=function(){return Qt(t.props.day,{excludeDates:t.props.excludeDates,excludeDateIntervals:t.props.excludeDateIntervals})},t.isStartOfWeek=function(){return P(t.props.day,_e(t.props.day,t.props.locale,t.props.calendarStartDay))},t.isSameWeek=function(e){return t.props.showWeekPicker&&P(e,_e(t.props.day,t.props.locale,t.props.calendarStartDay))},t.isSameDayOrWeek=function(e){return t.isSameDay(e)||t.isSameWeek(e)},t.getHighLightedClass=function(){var e=t.props,r=e.day,o=e.highlightDates;if(!o)return!1;var i=W(r,"MM.dd.yyyy");return o.get(i)},t.getHolidaysClass=function(){var e,r=t.props,o=r.day,i=r.holidays;if(!i)return[void 0];var s=W(o,"MM.dd.yyyy");return i.has(s)?[(e=i.get(s))===null||e===void 0?void 0:e.className]:[void 0]},t.isInRange=function(){var e=t.props,r=e.day,o=e.startDate,i=e.endDate;return!o||!i?!1:Ue(r,o,i)},t.isInSelectingRange=function(){var e,r=t.props,o=r.day,i=r.selectsStart,s=r.selectsEnd,c=r.selectsRange,u=r.selectsDisabledDaysInRange,l=r.startDate,d=r.endDate,p=(e=t.props.selectingDate)!==null&&e!==void 0?e:t.props.preSelection;return!(i||s||c)||!p||!u&&t.isDisabled()?!1:i&&d&&(Te(p,d)||xe(p,d))?Ue(o,p,d):s&&l&&!d&&(Me(p,l)||xe(p,l))||c&&l&&!d&&(Me(p,l)||xe(p,l))?Ue(o,l,p):!1},t.isSelectingRangeStart=function(){var e;if(!t.isInSelectingRange())return!1;var r=t.props,o=r.day,i=r.startDate,s=r.selectsStart,c=(e=t.props.selectingDate)!==null&&e!==void 0?e:t.props.preSelection;return s?P(o,c):P(o,i)},t.isSelectingRangeEnd=function(){var e;if(!t.isInSelectingRange())return!1;var r=t.props,o=r.day,i=r.endDate,s=r.selectsEnd,c=r.selectsRange,u=(e=t.props.selectingDate)!==null&&e!==void 0?e:t.props.preSelection;return s||c?P(o,u):P(o,i)},t.isRangeStart=function(){var e=t.props,r=e.day,o=e.startDate,i=e.endDate;return!o||!i?!1:P(o,r)},t.isRangeEnd=function(){var e=t.props,r=e.day,o=e.startDate,i=e.endDate;return!o||!i?!1:P(i,r)},t.isWeekend=function(){var e=ia(t.props.day);return e===0||e===6},t.isAfterMonth=function(){return t.props.month!==void 0&&(t.props.month+1)%12===J(t.props.day)},t.isBeforeMonth=function(){return t.props.month!==void 0&&(J(t.props.day)+1)%12===t.props.month},t.isCurrentDay=function(){return t.isSameDay(Y())},t.isSelected=function(){var e;return t.props.selectsMultiple?(e=t.props.selectedDates)===null||e===void 0?void 0:e.some(function(r){return t.isSameDayOrWeek(r)}):t.isSameDayOrWeek(t.props.selected)},t.getClassNames=function(e){var r=t.props.dayClassName?t.props.dayClassName(e):void 0;return $("react-datepicker__day",r,"react-datepicker__day--"+Go(t.props.day),{"react-datepicker__day--disabled":t.isDisabled(),"react-datepicker__day--excluded":t.isExcluded(),"react-datepicker__day--selected":t.isSelected(),"react-datepicker__day--keyboard-selected":t.isKeyboardSelected(),"react-datepicker__day--range-start":t.isRangeStart(),"react-datepicker__day--range-end":t.isRangeEnd(),"react-datepicker__day--in-range":t.isInRange(),"react-datepicker__day--in-selecting-range":t.isInSelectingRange(),"react-datepicker__day--selecting-range-start":t.isSelectingRangeStart(),"react-datepicker__day--selecting-range-end":t.isSelectingRangeEnd(),"react-datepicker__day--today":t.isCurrentDay(),"react-datepicker__day--weekend":t.isWeekend(),"react-datepicker__day--outside-month":t.isAfterMonth()||t.isBeforeMonth()},t.getHighLightedClass(),t.getHolidaysClass())},t.getAriaLabel=function(){var e=t.props,r=e.day,o=e.ariaLabelPrefixWhenEnabled,i=o===void 0?"Choose":o,s=e.ariaLabelPrefixWhenDisabled,c=s===void 0?"Not available":s,u=t.isDisabled()||t.isExcluded()?c:i;return"".concat(u," ").concat(W(r,"PPPP",t.props.locale))},t.getTitle=function(){var e=t.props,r=e.day,o=e.holidays,i=o===void 0?new Map:o,s=e.excludeDates,c=W(r,"MM.dd.yyyy"),u=[];return i.has(c)&&u.push.apply(u,i.get(c).holidayNames),t.isExcluded()&&u.push(s==null?void 0:s.filter(function(l){return l instanceof Date?P(l,r):P(l==null?void 0:l.date,r)}).map(function(l){if(!(l instanceof Date))return l==null?void 0:l.message})),u.join(", ")},t.getTabIndex=function(){var e=t.props.selected,r=t.props.preSelection,o=!(t.props.showWeekPicker&&(t.props.showWeekNumber||!t.isStartOfWeek()))&&(t.isKeyboardSelected()||t.isSameDay(e)&&P(r,e))?0:-1;return o},t.handleFocusDay=function(){var e;t.shouldFocusDay()&&((e=t.dayEl.current)===null||e===void 0||e.focus({preventScroll:!0}))},t.renderDayContents=function(){return t.props.monthShowsDuplicateDaysEnd&&t.isAfterMonth()||t.props.monthShowsDuplicateDaysStart&&t.isBeforeMonth()?null:t.props.renderDayContents?t.props.renderDayContents(Zt(t.props.day),t.props.day):Zt(t.props.day)},t.render=function(){return f.createElement("div",{ref:t.dayEl,className:t.getClassNames(t.props.day),onKeyDown:t.handleOnKeyDown,onClick:t.handleClick,onMouseEnter:t.props.usePointerEvent?void 0:t.handleMouseEnter,onPointerEnter:t.props.usePointerEvent?t.handleMouseEnter:void 0,tabIndex:t.getTabIndex(),"aria-label":t.getAriaLabel(),role:"option",title:t.getTitle(),"aria-disabled":t.isDisabled(),"aria-current":t.isCurrentDay()?"date":void 0,"aria-selected":t.isSelected()||t.isInRange()},t.renderDayContents(),t.getTitle()!==""&&f.createElement("span",{className:"overlay"},t.getTitle()))},t}return n.prototype.componentDidMount=function(){this.handleFocusDay()},n.prototype.componentDidUpdate=function(){this.handleFocusDay()},n.prototype.shouldFocusDay=function(){var t=!1;return this.getTabIndex()===0&&this.isSameDay(this.props.preSelection)&&((!document.activeElement||document.activeElement===document.body)&&(t=!0),this.props.inline&&!this.props.shouldFocusDayInline&&(t=!1),this.isDayActiveElement()&&(t=!0),this.isDuplicateDay()&&(t=!1)),t},n.prototype.isDayActiveElement=function(){var t,e,r;return((e=(t=this.props.containerRef)===null||t===void 0?void 0:t.current)===null||e===void 0?void 0:e.contains(document.activeElement))&&((r=document.activeElement)===null||r===void 0?void 0:r.classList.contains("react-datepicker__day"))},n.prototype.isDuplicateDay=function(){return this.props.monthShowsDuplicateDaysEnd&&this.isAfterMonth()||this.props.monthShowsDuplicateDaysStart&&this.isBeforeMonth()},n}(y.Component),hi=function(a){G(n,a);function n(){var t=a!==null&&a.apply(this,arguments)||this;return t.weekNumberEl=y.createRef(),t.handleClick=function(e){t.props.onClick&&t.props.onClick(e)},t.handleOnKeyDown=function(e){var r,o,i=e.key;i===D.Space&&(e.preventDefault(),e.key=D.Enter),(o=(r=t.props).handleOnKeyDown)===null||o===void 0||o.call(r,e)},t.isKeyboardSelected=function(){return!t.props.disabledKeyboardNavigation&&!P(t.props.date,t.props.selected)&&P(t.props.date,t.props.preSelection)},t.getTabIndex=function(){return t.props.showWeekPicker&&t.props.showWeekNumber&&(t.isKeyboardSelected()||P(t.props.date,t.props.selected)&&P(t.props.preSelection,t.props.selected))?0:-1},t.handleFocusWeekNumber=function(e){var r=!1;t.getTabIndex()===0&&!(e!=null&&e.isInputFocused)&&P(t.props.date,t.props.preSelection)&&((!document.activeElement||document.activeElement===document.body)&&(r=!0),t.props.inline&&!t.props.shouldFocusDayInline&&(r=!1),t.props.containerRef&&t.props.containerRef.current&&t.props.containerRef.current.contains(document.activeElement)&&document.activeElement&&document.activeElement.classList.contains("react-datepicker__week-number")&&(r=!0)),r&&t.weekNumberEl.current&&t.weekNumberEl.current.focus({preventScroll:!0})},t}return Object.defineProperty(n,"defaultProps",{get:function(){return{ariaLabelPrefix:"week "}},enumerable:!1,configurable:!0}),n.prototype.componentDidMount=function(){this.handleFocusWeekNumber()},n.prototype.componentDidUpdate=function(t){this.handleFocusWeekNumber(t)},n.prototype.render=function(){var t=this.props,e=t.weekNumber,r=t.isWeekDisabled,o=t.ariaLabelPrefix,i=o===void 0?n.defaultProps.ariaLabelPrefix:o,s=t.onClick,c={"react-datepicker__week-number":!0,"react-datepicker__week-number--clickable":!!s&&!r,"react-datepicker__week-number--selected":!!s&&P(this.props.date,this.props.selected)};return f.createElement("div",{ref:this.weekNumberEl,className:$(c),"aria-label":"".concat(i," ").concat(this.props.weekNumber),onClick:this.handleClick,onKeyDown:this.handleOnKeyDown,tabIndex:this.getTabIndex()},e)},n}(y.Component),mi=function(a){G(n,a);function n(){var t=a!==null&&a.apply(this,arguments)||this;return t.isDisabled=function(e){return ne(e,{minDate:t.props.minDate,maxDate:t.props.maxDate,excludeDates:t.props.excludeDates,excludeDateIntervals:t.props.excludeDateIntervals,includeDateIntervals:t.props.includeDateIntervals,includeDates:t.props.includeDates,filterDate:t.props.filterDate})},t.handleDayClick=function(e,r){t.props.onDayClick&&t.props.onDayClick(e,r)},t.handleDayMouseEnter=function(e){t.props.onDayMouseEnter&&t.props.onDayMouseEnter(e)},t.handleWeekClick=function(e,r,o){for(var i,s,c,u=new Date(e),l=0;l<7;l++){var d=new Date(e);d.setDate(d.getDate()+l);var p=!t.isDisabled(d);if(p){u=d;break}}typeof t.props.onWeekSelect=="function"&&t.props.onWeekSelect(u,r,o),t.props.showWeekPicker&&t.handleDayClick(u,o),((i=t.props.shouldCloseOnSelect)!==null&&i!==void 0?i:n.defaultProps.shouldCloseOnSelect)&&((c=(s=t.props).setOpen)===null||c===void 0||c.call(s,!1))},t.formatWeekNumber=function(e){return t.props.formatWeekNumber?t.props.formatWeekNumber(e):Xo(e)},t.isWeekDisabled=function(){for(var e=t.startOfWeek(),r=oe(e,6),o=new Date(e);o<=r;){if(!t.isDisabled(o))return!1;o=oe(o,1)}return!0},t.renderDays=function(){var e=t.startOfWeek(),r=[],o=t.formatWeekNumber(e);if(t.props.showWeekNumber){var i=t.props.onWeekSelect||t.props.showWeekPicker?t.handleWeekClick.bind(t,e,o):void 0;r.push(f.createElement(hi,E({key:"W"},n.defaultProps,t.props,{weekNumber:o,isWeekDisabled:t.isWeekDisabled(),date:e,onClick:i})))}return r.concat([0,1,2,3,4,5,6].map(function(s){var c=oe(e,s);return f.createElement(fi,E({},n.defaultProps,t.props,{ariaLabelPrefixWhenEnabled:t.props.chooseDayAriaLabelPrefix,ariaLabelPrefixWhenDisabled:t.props.disabledDayAriaLabelPrefix,key:c.valueOf(),day:c,onClick:t.handleDayClick.bind(t,c),onMouseEnter:t.handleDayMouseEnter.bind(t,c)}))}))},t.startOfWeek=function(){return _e(t.props.day,t.props.locale,t.props.calendarStartDay)},t.isKeyboardSelected=function(){return!t.props.disabledKeyboardNavigation&&!P(t.startOfWeek(),t.props.selected)&&P(t.startOfWeek(),t.props.preSelection)},t}return Object.defineProperty(n,"defaultProps",{get:function(){return{shouldCloseOnSelect:!0}},enumerable:!1,configurable:!0}),n.prototype.render=function(){var t={"react-datepicker__week":!0,"react-datepicker__week--selected":P(this.startOfWeek(),this.props.selected),"react-datepicker__week--keyboard-selected":this.isKeyboardSelected()};return f.createElement("div",{className:$(t)},this.renderDays())},n}(y.Component),qe,vi=6,Ie={TWO_COLUMNS:"two_columns",THREE_COLUMNS:"three_columns",FOUR_COLUMNS:"four_columns"},xt=(qe={},qe[Ie.TWO_COLUMNS]={grid:[[0,1],[2,3],[4,5],[6,7],[8,9],[10,11]],verticalNavigationOffset:2},qe[Ie.THREE_COLUMNS]={grid:[[0,1,2],[3,4,5],[6,7,8],[9,10,11]],verticalNavigationOffset:3},qe[Ie.FOUR_COLUMNS]={grid:[[0,1,2,3],[4,5,6,7],[8,9,10,11]],verticalNavigationOffset:4},qe),rt=1;function gr(a,n){return a?Ie.FOUR_COLUMNS:n?Ie.TWO_COLUMNS:Ie.THREE_COLUMNS}var gi=function(a){G(n,a);function n(){var t=a!==null&&a.apply(this,arguments)||this;return t.MONTH_REFS=pe([],Array(12),!0).map(function(){return y.createRef()}),t.QUARTER_REFS=pe([],Array(4),!0).map(function(){return y.createRef()}),t.isDisabled=function(e){return ne(e,{minDate:t.props.minDate,maxDate:t.props.maxDate,excludeDates:t.props.excludeDates,excludeDateIntervals:t.props.excludeDateIntervals,includeDateIntervals:t.props.includeDateIntervals,includeDates:t.props.includeDates,filterDate:t.props.filterDate})},t.isExcluded=function(e){return Qt(e,{excludeDates:t.props.excludeDates,excludeDateIntervals:t.props.excludeDateIntervals})},t.handleDayClick=function(e,r){var o,i;(i=(o=t.props).onDayClick)===null||i===void 0||i.call(o,e,r,t.props.orderInDisplay)},t.handleDayMouseEnter=function(e){var r,o;(o=(r=t.props).onDayMouseEnter)===null||o===void 0||o.call(r,e)},t.handleMouseLeave=function(){var e,r;(r=(e=t.props).onMouseLeave)===null||r===void 0||r.call(e)},t.isRangeStartMonth=function(e){var r=t.props,o=r.day,i=r.startDate,s=r.endDate;return!i||!s?!1:Z(re(o,e),i)},t.isRangeStartQuarter=function(e){var r=t.props,o=r.day,i=r.startDate,s=r.endDate;return!i||!s?!1:vt(Ye(o,e),i)},t.isRangeEndMonth=function(e){var r=t.props,o=r.day,i=r.startDate,s=r.endDate;return!i||!s?!1:Z(re(o,e),s)},t.isRangeEndQuarter=function(e){var r=t.props,o=r.day,i=r.startDate,s=r.endDate;return!i||!s?!1:vt(Ye(o,e),s)},t.isInSelectingRangeMonth=function(e){var r,o=t.props,i=o.day,s=o.selectsStart,c=o.selectsEnd,u=o.selectsRange,l=o.startDate,d=o.endDate,p=(r=t.props.selectingDate)!==null&&r!==void 0?r:t.props.preSelection;return!(s||c||u)||!p?!1:s&&d?Ze(p,d,e,i):c&&l||u&&l&&!d?Ze(l,p,e,i):!1},t.isSelectingMonthRangeStart=function(e){var r;if(!t.isInSelectingRangeMonth(e))return!1;var o=t.props,i=o.day,s=o.startDate,c=o.selectsStart,u=re(i,e),l=(r=t.props.selectingDate)!==null&&r!==void 0?r:t.props.preSelection;return c?Z(u,l):Z(u,s)},t.isSelectingMonthRangeEnd=function(e){var r;if(!t.isInSelectingRangeMonth(e))return!1;var o=t.props,i=o.day,s=o.endDate,c=o.selectsEnd,u=o.selectsRange,l=re(i,e),d=(r=t.props.selectingDate)!==null&&r!==void 0?r:t.props.preSelection;return c||u?Z(l,d):Z(l,s)},t.isInSelectingRangeQuarter=function(e){var r,o=t.props,i=o.day,s=o.selectsStart,c=o.selectsEnd,u=o.selectsRange,l=o.startDate,d=o.endDate,p=(r=t.props.selectingDate)!==null&&r!==void 0?r:t.props.preSelection;return!(s||c||u)||!p?!1:s&&d?tt(p,d,e,i):c&&l||u&&l&&!d?tt(l,p,e,i):!1},t.isWeekInMonth=function(e){var r=t.props.day,o=oe(e,6);return Z(e,r)||Z(o,r)},t.isCurrentMonth=function(e,r){return S(e)===S(Y())&&r===J(Y())},t.isCurrentQuarter=function(e,r){return S(e)===S(Y())&&r===Se(Y())},t.isSelectedMonth=function(e,r,o){return J(o)===r&&S(e)===S(o)},t.isSelectMonthInList=function(e,r,o){return o.some(function(i){return t.isSelectedMonth(e,r,i)})},t.isSelectedQuarter=function(e,r,o){return Se(e)===r&&S(e)===S(o)},t.renderWeeks=function(){for(var e=[],r=t.props.fixedHeight,o=0,i=!1,s=_e(ge(t.props.day),t.props.locale,t.props.calendarStartDay),c=function(m){return t.props.showWeekPicker?_e(m,t.props.locale,t.props.calendarStartDay):t.props.preSelection},u=function(m){return t.props.showWeekPicker?_e(m,t.props.locale,t.props.calendarStartDay):t.props.selected},l=t.props.selected?u(t.props.selected):void 0,d=t.props.preSelection?c(t.props.preSelection):void 0;e.push(f.createElement(mi,E({},t.props,{ariaLabelPrefix:t.props.weekAriaLabelPrefix,key:o,day:s,month:J(t.props.day),onDayClick:t.handleDayClick,onDayMouseEnter:t.handleDayMouseEnter,selected:l,preSelection:d,showWeekNumber:t.props.showWeekNumbers}))),!i;){o++,s=lt(s,1);var p=r&&o>=vi,h=!r&&!t.isWeekInMonth(s);if(p||h)if(t.props.peekNextMonth)i=!0;else break}return e},t.onMonthClick=function(e,r){var o=t.isMonthDisabledForLabelDate(r),i=o.isDisabled,s=o.labelDate;i||t.handleDayClick(ge(s),e)},t.onMonthMouseEnter=function(e){var r=t.isMonthDisabledForLabelDate(e),o=r.isDisabled,i=r.labelDate;o||t.handleDayMouseEnter(ge(i))},t.handleMonthNavigation=function(e,r){var o,i,s,c;(i=(o=t.props).setPreSelection)===null||i===void 0||i.call(o,r),(c=(s=t.MONTH_REFS[e])===null||s===void 0?void 0:s.current)===null||c===void 0||c.focus()},t.handleKeyboardNavigation=function(e,r,o){var i,s=t.props,c=s.selected,u=s.preSelection,l=s.setPreSelection,d=s.minDate,p=s.maxDate,h=s.showFourColumnMonthYearPicker,m=s.showTwoColumnMonthYearPicker;if(u){var g=gr(h,m),v=t.getVerticalOffset(g),k=(i=xt[g])===null||i===void 0?void 0:i.grid,x=function(N,_,C){var I,j,K=_,z=C;switch(N){case D.ArrowRight:K=ie(_,rt),z=C===11?0:C+rt;break;case D.ArrowLeft:K=Pe(_,rt),z=C===0?11:C-rt;break;case D.ArrowUp:K=Pe(_,v),z=!((I=k==null?void 0:k[0])===null||I===void 0)&&I.includes(C)?C+12-v:C-v;break;case D.ArrowDown:K=ie(_,v),z=!((j=k==null?void 0:k[k.length-1])===null||j===void 0)&&j.includes(C)?C-12+v:C+v;break}return{newCalculatedDate:K,newCalculatedMonth:z}},M=function(N,_,C){for(var I=40,j=N,K=!1,z=0,se=x(j,_,C),X=se.newCalculatedDate,F=se.newCalculatedMonth;!K;){if(z>=I){X=_,F=C;break}if(d&&X<d){j=D.ArrowRight;var H=x(j,X,F);X=H.newCalculatedDate,F=H.newCalculatedMonth}if(p&&X>p){j=D.ArrowLeft;var H=x(j,X,F);X=H.newCalculatedDate,F=H.newCalculatedMonth}if(ri(X,t.props)){var H=x(j,X,F);X=H.newCalculatedDate,F=H.newCalculatedMonth}else K=!0;z++}return{newCalculatedDate:X,newCalculatedMonth:F}};if(r===D.Enter){t.isMonthDisabled(o)||(t.onMonthClick(e,o),l==null||l(c));return}var T=M(r,u,o),B=T.newCalculatedDate,A=T.newCalculatedMonth;switch(r){case D.ArrowRight:case D.ArrowLeft:case D.ArrowUp:case D.ArrowDown:t.handleMonthNavigation(A,B);break}}},t.getVerticalOffset=function(e){var r,o;return(o=(r=xt[e])===null||r===void 0?void 0:r.verticalNavigationOffset)!==null&&o!==void 0?o:0},t.onMonthKeyDown=function(e,r){var o=t.props,i=o.disabledKeyboardNavigation,s=o.handleOnMonthKeyDown,c=e.key;c!==D.Tab&&e.preventDefault(),i||t.handleKeyboardNavigation(e,c,r),s&&s(e)},t.onQuarterClick=function(e,r){var o=Ye(t.props.day,r);Je(o,t.props)||t.handleDayClick(rr(o),e)},t.onQuarterMouseEnter=function(e){var r=Ye(t.props.day,e);Je(r,t.props)||t.handleDayMouseEnter(rr(r))},t.handleQuarterNavigation=function(e,r){var o,i,s,c;t.isDisabled(r)||t.isExcluded(r)||((i=(o=t.props).setPreSelection)===null||i===void 0||i.call(o,r),(c=(s=t.QUARTER_REFS[e-1])===null||s===void 0?void 0:s.current)===null||c===void 0||c.focus())},t.onQuarterKeyDown=function(e,r){var o,i,s=e.key;if(!t.props.disabledKeyboardNavigation)switch(s){case D.Enter:t.onQuarterClick(e,r),(i=(o=t.props).setPreSelection)===null||i===void 0||i.call(o,t.props.selected);break;case D.ArrowRight:if(!t.props.preSelection)break;t.handleQuarterNavigation(r===4?1:r+1,It(t.props.preSelection,1));break;case D.ArrowLeft:if(!t.props.preSelection)break;t.handleQuarterNavigation(r===1?4:r-1,Fr(t.props.preSelection));break}},t.isMonthDisabledForLabelDate=function(e){var r,o=t.props,i=o.day,s=o.minDate,c=o.maxDate,u=o.excludeDates,l=o.includeDates,d=re(i,e);return{isDisabled:(r=(s||c||u||l)&&Qr(d,t.props))!==null&&r!==void 0?r:!1,labelDate:d}},t.isMonthDisabled=function(e){var r=t.isMonthDisabledForLabelDate(e).isDisabled;return r},t.getMonthClassNames=function(e){var r=t.props,o=r.day,i=r.startDate,s=r.endDate,c=r.preSelection,u=r.monthClassName,l=u?u(re(o,e)):void 0,d=t.getSelection();return $("react-datepicker__month-text","react-datepicker__month-".concat(e),l,{"react-datepicker__month-text--disabled":t.isMonthDisabled(e),"react-datepicker__month-text--selected":d?t.isSelectMonthInList(o,e,d):void 0,"react-datepicker__month-text--keyboard-selected":!t.props.disabledKeyboardNavigation&&c&&t.isSelectedMonth(o,e,c)&&!t.isMonthDisabled(e),"react-datepicker__month-text--in-selecting-range":t.isInSelectingRangeMonth(e),"react-datepicker__month-text--in-range":i&&s?Ze(i,s,e,o):void 0,"react-datepicker__month-text--range-start":t.isRangeStartMonth(e),"react-datepicker__month-text--range-end":t.isRangeEndMonth(e),"react-datepicker__month-text--selecting-range-start":t.isSelectingMonthRangeStart(e),"react-datepicker__month-text--selecting-range-end":t.isSelectingMonthRangeEnd(e),"react-datepicker__month-text--today":t.isCurrentMonth(o,e)})},t.getTabIndex=function(e){if(t.props.preSelection==null)return"-1";var r=J(t.props.preSelection),o=t.isMonthDisabledForLabelDate(r).isDisabled,i=e===r&&!(o||t.props.disabledKeyboardNavigation)?"0":"-1";return i},t.getQuarterTabIndex=function(e){if(t.props.preSelection==null)return"-1";var r=Se(t.props.preSelection),o=Je(t.props.day,t.props),i=e===r&&!(o||t.props.disabledKeyboardNavigation)?"0":"-1";return i},t.getAriaLabel=function(e){var r=t.props,o=r.chooseDayAriaLabelPrefix,i=o===void 0?"Choose":o,s=r.disabledDayAriaLabelPrefix,c=s===void 0?"Not available":s,u=r.day,l=r.locale,d=re(u,e),p=t.isDisabled(d)||t.isExcluded(d)?c:i;return"".concat(p," ").concat(W(d,"MMMM yyyy",l))},t.getQuarterClassNames=function(e){var r=t.props,o=r.day,i=r.startDate,s=r.endDate,c=r.selected,u=r.minDate,l=r.maxDate,d=r.excludeDates,p=r.includeDates,h=r.filterDate,m=r.preSelection,g=r.disabledKeyboardNavigation,v=(u||l||d||p||h)&&Je(Ye(o,e),t.props);return $("react-datepicker__quarter-text","react-datepicker__quarter-".concat(e),{"react-datepicker__quarter-text--disabled":v,"react-datepicker__quarter-text--selected":c?t.isSelectedQuarter(o,e,c):void 0,"react-datepicker__quarter-text--keyboard-selected":!g&&m&&t.isSelectedQuarter(o,e,m)&&!v,"react-datepicker__quarter-text--in-selecting-range":t.isInSelectingRangeQuarter(e),"react-datepicker__quarter-text--in-range":i&&s?tt(i,s,e,o):void 0,"react-datepicker__quarter-text--range-start":t.isRangeStartQuarter(e),"react-datepicker__quarter-text--range-end":t.isRangeEndQuarter(e),"react-datepicker__quarter-text--today":t.isCurrentQuarter(o,e)})},t.getMonthContent=function(e){var r=t.props,o=r.showFullMonthYearPicker,i=r.renderMonthContent,s=r.locale,c=r.day,u=Hr(e,s),l=Ht(e,s);return i?i(e,u,l,c):o?l:u},t.getQuarterContent=function(e){var r,o=t.props,i=o.renderQuarterContent,s=o.locale,c=ti(e,s);return(r=i==null?void 0:i(e,c))!==null&&r!==void 0?r:c},t.renderMonths=function(){var e,r=t.props,o=r.showTwoColumnMonthYearPicker,i=r.showFourColumnMonthYearPicker,s=r.day,c=r.selected,u=(e=xt[gr(i,o)])===null||e===void 0?void 0:e.grid;return u==null?void 0:u.map(function(l,d){return f.createElement("div",{className:"react-datepicker__month-wrapper",key:d},l.map(function(p,h){return f.createElement("div",{ref:t.MONTH_REFS[p],key:h,onClick:function(m){t.onMonthClick(m,p)},onKeyDown:function(m){qr(m)&&(m.preventDefault(),m.key=D.Enter),t.onMonthKeyDown(m,p)},onMouseEnter:t.props.usePointerEvent?void 0:function(){return t.onMonthMouseEnter(p)},onPointerEnter:t.props.usePointerEvent?function(){return t.onMonthMouseEnter(p)}:void 0,tabIndex:Number(t.getTabIndex(p)),className:t.getMonthClassNames(p),"aria-disabled":t.isMonthDisabled(p),role:"option","aria-label":t.getAriaLabel(p),"aria-current":t.isCurrentMonth(s,p)?"date":void 0,"aria-selected":c?t.isSelectedMonth(s,p,c):void 0},t.getMonthContent(p))}))})},t.renderQuarters=function(){var e=t.props,r=e.day,o=e.selected,i=[1,2,3,4];return f.createElement("div",{className:"react-datepicker__quarter-wrapper"},i.map(function(s,c){return f.createElement("div",{key:c,ref:t.QUARTER_REFS[c],role:"option",onClick:function(u){t.onQuarterClick(u,s)},onKeyDown:function(u){t.onQuarterKeyDown(u,s)},onMouseEnter:t.props.usePointerEvent?void 0:function(){return t.onQuarterMouseEnter(s)},onPointerEnter:t.props.usePointerEvent?function(){return t.onQuarterMouseEnter(s)}:void 0,className:t.getQuarterClassNames(s),"aria-selected":o?t.isSelectedQuarter(r,s,o):void 0,tabIndex:Number(t.getQuarterTabIndex(s)),"aria-current":t.isCurrentQuarter(r,s)?"date":void 0},t.getQuarterContent(s))}))},t.getClassNames=function(){var e=t.props,r=e.selectingDate,o=e.selectsStart,i=e.selectsEnd,s=e.showMonthYearPicker,c=e.showQuarterYearPicker,u=e.showWeekPicker;return $("react-datepicker__month",{"react-datepicker__month--selecting-range":r&&(o||i)},{"react-datepicker__monthPicker":s},{"react-datepicker__quarterPicker":c},{"react-datepicker__weekPicker":u})},t}return n.prototype.getSelection=function(){var t=this.props,e=t.selected,r=t.selectedDates,o=t.selectsMultiple;if(o)return r;if(e)return[e]},n.prototype.render=function(){var t=this.props,e=t.showMonthYearPicker,r=t.showQuarterYearPicker,o=t.day,i=t.ariaLabelPrefix,s=i===void 0?"Month ":i,c=s?s.trim()+" ":"";return f.createElement("div",{className:this.getClassNames(),onMouseLeave:this.props.usePointerEvent?void 0:this.handleMouseLeave,onPointerLeave:this.props.usePointerEvent?this.handleMouseLeave:void 0,"aria-label":"".concat(c).concat(W(o,"MMMM, yyyy",this.props.locale)),role:"listbox"},e?this.renderMonths():r?this.renderQuarters():this.renderWeeks())},n}(y.Component),Di=function(a){G(n,a);function n(){var t=a!==null&&a.apply(this,arguments)||this;return t.isSelectedMonth=function(e){return t.props.month===e},t.renderOptions=function(){return t.props.monthNames.map(function(e,r){return f.createElement("div",{className:t.isSelectedMonth(r)?"react-datepicker__month-option react-datepicker__month-option--selected_month":"react-datepicker__month-option",key:e,onClick:t.onChange.bind(t,r),"aria-selected":t.isSelectedMonth(r)?"true":void 0},t.isSelectedMonth(r)?f.createElement("span",{className:"react-datepicker__month-option--selected"},"✓"):"",e)})},t.onChange=function(e){return t.props.onChange(e)},t.handleClickOutside=function(){return t.props.onCancel()},t}return n.prototype.render=function(){return f.createElement(yt,{className:"react-datepicker__month-dropdown",onClickOutside:this.handleClickOutside},this.renderOptions())},n}(y.Component),wi=function(a){G(n,a);function n(){var t=a!==null&&a.apply(this,arguments)||this;return t.state={dropdownVisible:!1},t.renderSelectOptions=function(e){return e.map(function(r,o){return f.createElement("option",{key:r,value:o},r)})},t.renderSelectMode=function(e){return f.createElement("select",{value:t.props.month,className:"react-datepicker__month-select",onChange:function(r){return t.onChange(parseInt(r.target.value))}},t.renderSelectOptions(e))},t.renderReadView=function(e,r){return f.createElement("div",{key:"read",style:{visibility:e?"visible":"hidden"},className:"react-datepicker__month-read-view",onClick:t.toggleDropdown},f.createElement("span",{className:"react-datepicker__month-read-view--down-arrow"}),f.createElement("span",{className:"react-datepicker__month-read-view--selected-month"},r[t.props.month]))},t.renderDropdown=function(e){return f.createElement(Di,E({key:"dropdown"},t.props,{monthNames:e,onChange:t.onChange,onCancel:t.toggleDropdown}))},t.renderScrollMode=function(e){var r=t.state.dropdownVisible,o=[t.renderReadView(!r,e)];return r&&o.unshift(t.renderDropdown(e)),o},t.onChange=function(e){t.toggleDropdown(),e!==t.props.month&&t.props.onChange(e)},t.toggleDropdown=function(){return t.setState({dropdownVisible:!t.state.dropdownVisible})},t}return n.prototype.render=function(){var t=this,e=[0,1,2,3,4,5,6,7,8,9,10,11].map(this.props.useShortMonthInDropdown?function(o){return Hr(o,t.props.locale)}:function(o){return Ht(o,t.props.locale)}),r;switch(this.props.dropdownMode){case"scroll":r=this.renderScrollMode(e);break;case"select":r=this.renderSelectMode(e);break}return f.createElement("div",{className:"react-datepicker__month-dropdown-container react-datepicker__month-dropdown-container--".concat(this.props.dropdownMode)},r)},n}(y.Component);function yi(a,n){for(var t=[],e=ge(a),r=ge(n);!Me(e,r);)t.push(Y(e)),e=ie(e,1);return t}var bi=function(a){G(n,a);function n(t){var e=a.call(this,t)||this;return e.renderOptions=function(){return e.state.monthYearsList.map(function(r){var o=Nt(r),i=le(e.props.date,r)&&Z(e.props.date,r);return f.createElement("div",{className:i?"react-datepicker__month-year-option--selected_month-year":"react-datepicker__month-year-option",key:o,onClick:e.onChange.bind(e,o),"aria-selected":i?"true":void 0},i?f.createElement("span",{className:"react-datepicker__month-year-option--selected"},"✓"):"",W(r,e.props.dateFormat,e.props.locale))})},e.onChange=function(r){return e.props.onChange(r)},e.handleClickOutside=function(){e.props.onCancel()},e.state={monthYearsList:yi(e.props.minDate,e.props.maxDate)},e}return n.prototype.render=function(){var t=$({"react-datepicker__month-year-dropdown":!0,"react-datepicker__month-year-dropdown--scrollable":this.props.scrollableMonthYearDropdown});return f.createElement(yt,{className:t,onClickOutside:this.handleClickOutside},this.renderOptions())},n}(y.Component),ki=function(a){G(n,a);function n(){var t=a!==null&&a.apply(this,arguments)||this;return t.state={dropdownVisible:!1},t.renderSelectOptions=function(){for(var e=ge(t.props.minDate),r=ge(t.props.maxDate),o=[];!Me(e,r);){var i=Nt(e);o.push(f.createElement("option",{key:i,value:i},W(e,t.props.dateFormat,t.props.locale))),e=ie(e,1)}return o},t.onSelectChange=function(e){t.onChange(parseInt(e.target.value))},t.renderSelectMode=function(){return f.createElement("select",{value:Nt(ge(t.props.date)),className:"react-datepicker__month-year-select",onChange:t.onSelectChange},t.renderSelectOptions())},t.renderReadView=function(e){var r=W(t.props.date,t.props.dateFormat,t.props.locale);return f.createElement("div",{key:"read",style:{visibility:e?"visible":"hidden"},className:"react-datepicker__month-year-read-view",onClick:t.toggleDropdown},f.createElement("span",{className:"react-datepicker__month-year-read-view--down-arrow"}),f.createElement("span",{className:"react-datepicker__month-year-read-view--selected-month-year"},r))},t.renderDropdown=function(){return f.createElement(bi,E({key:"dropdown"},t.props,{onChange:t.onChange,onCancel:t.toggleDropdown}))},t.renderScrollMode=function(){var e=t.state.dropdownVisible,r=[t.renderReadView(!e)];return e&&r.unshift(t.renderDropdown()),r},t.onChange=function(e){t.toggleDropdown();var r=Y(e);le(t.props.date,r)&&Z(t.props.date,r)||t.props.onChange(r)},t.toggleDropdown=function(){return t.setState({dropdownVisible:!t.state.dropdownVisible})},t}return n.prototype.render=function(){var t;switch(this.props.dropdownMode){case"scroll":t=this.renderScrollMode();break;case"select":t=this.renderSelectMode();break}return f.createElement("div",{className:"react-datepicker__month-year-dropdown-container react-datepicker__month-year-dropdown-container--".concat(this.props.dropdownMode)},t)},n}(y.Component),_i=function(a){G(n,a);function n(){var t=a!==null&&a.apply(this,arguments)||this;return t.state={height:null},t.scrollToTheSelectedTime=function(){requestAnimationFrame(function(){var e,r,o;t.list&&(t.list.scrollTop=(o=t.centerLi&&n.calcCenterPosition(t.props.monthRef?t.props.monthRef.clientHeight-((r=(e=t.header)===null||e===void 0?void 0:e.clientHeight)!==null&&r!==void 0?r:0):t.list.clientHeight,t.centerLi))!==null&&o!==void 0?o:0)})},t.handleClick=function(e){var r,o;(t.props.minTime||t.props.maxTime)&&sr(e,t.props)||(t.props.excludeTimes||t.props.includeTimes||t.props.filterTime)&&ir(e,t.props)||(o=(r=t.props).onChange)===null||o===void 0||o.call(r,e)},t.isSelectedTime=function(e){return t.props.selected&&di(t.props.selected,e)},t.isDisabledTime=function(e){return(t.props.minTime||t.props.maxTime)&&sr(e,t.props)||(t.props.excludeTimes||t.props.includeTimes||t.props.filterTime)&&ir(e,t.props)},t.liClasses=function(e){var r,o=["react-datepicker__time-list-item",t.props.timeClassName?t.props.timeClassName(e):void 0];return t.isSelectedTime(e)&&o.push("react-datepicker__time-list-item--selected"),t.isDisabledTime(e)&&o.push("react-datepicker__time-list-item--disabled"),t.props.injectTimes&&(me(e)*3600+ve(e)*60+ke(e))%(((r=t.props.intervals)!==null&&r!==void 0?r:n.defaultProps.intervals)*60)!==0&&o.push("react-datepicker__time-list-item--injected"),o.join(" ")},t.handleOnKeyDown=function(e,r){var o,i;e.key===D.Space&&(e.preventDefault(),e.key=D.Enter),(e.key===D.ArrowUp||e.key===D.ArrowLeft)&&e.target instanceof HTMLElement&&e.target.previousSibling&&(e.preventDefault(),e.target.previousSibling instanceof HTMLElement&&e.target.previousSibling.focus()),(e.key===D.ArrowDown||e.key===D.ArrowRight)&&e.target instanceof HTMLElement&&e.target.nextSibling&&(e.preventDefault(),e.target.nextSibling instanceof HTMLElement&&e.target.nextSibling.focus()),e.key===D.Enter&&t.handleClick(r),(i=(o=t.props).handleOnKeyDown)===null||i===void 0||i.call(o,e)},t.renderTimes=function(){for(var e,r=[],o=typeof t.props.format=="string"?t.props.format:"p",i=(e=t.props.intervals)!==null&&e!==void 0?e:n.defaultProps.intervals,s=t.props.selected||t.props.openToDate||Y(),c=it(s),u=t.props.injectTimes&&t.props.injectTimes.sort(function(v,k){return v.getTime()-k.getTime()}),l=60*ui(s),d=l/i,p=0;p<d;p++){var h=Et(c,p*i);if(r.push(h),u){var m=li(c,h,p,i,u);r=r.concat(m)}}var g=r.reduce(function(v,k){return k.getTime()<=s.getTime()?k:v},r[0]);return r.map(function(v){return f.createElement("li",{key:v.valueOf(),onClick:t.handleClick.bind(t,v),className:t.liClasses(v),ref:function(k){v===g&&(t.centerLi=k)},onKeyDown:function(k){t.handleOnKeyDown(k,v)},tabIndex:v===g?0:-1,role:"option","aria-selected":t.isSelectedTime(v)?"true":void 0,"aria-disabled":t.isDisabledTime(v)?"true":void 0},W(v,o,t.props.locale))})},t.renderTimeCaption=function(){return t.props.showTimeCaption===!1?f.createElement(f.Fragment,null):f.createElement("div",{className:"react-datepicker__header react-datepicker__header--time ".concat(t.props.showTimeSelectOnly?"react-datepicker__header--time--only":""),ref:function(e){t.header=e}},f.createElement("div",{className:"react-datepicker-time__header"},t.props.timeCaption))},t}return Object.defineProperty(n,"defaultProps",{get:function(){return{intervals:30,todayButton:null,timeCaption:"Time",showTimeCaption:!0}},enumerable:!1,configurable:!0}),n.prototype.componentDidMount=function(){this.scrollToTheSelectedTime(),this.observeDatePickerHeightChanges()},n.prototype.componentWillUnmount=function(){var t;(t=this.resizeObserver)===null||t===void 0||t.disconnect()},n.prototype.observeDatePickerHeightChanges=function(){var t=this,e=this.props.monthRef;this.updateContainerHeight(),e&&(this.resizeObserver=new ResizeObserver(function(){t.updateContainerHeight()}),this.resizeObserver.observe(e))},n.prototype.updateContainerHeight=function(){this.props.monthRef&&this.header&&this.setState({height:this.props.monthRef.clientHeight-this.header.clientHeight})},n.prototype.render=function(){var t=this,e,r=this.state.height;return f.createElement("div",{className:"react-datepicker__time-container ".concat(((e=this.props.todayButton)!==null&&e!==void 0?e:n.defaultProps.todayButton)?"react-datepicker__time-container--with-today-button":"")},this.renderTimeCaption(),f.createElement("div",{className:"react-datepicker__time"},f.createElement("div",{className:"react-datepicker__time-box"},f.createElement("ul",{className:"react-datepicker__time-list",ref:function(o){t.list=o},style:r?{height:r}:{},role:"listbox","aria-label":this.props.timeCaption},this.renderTimes()))))},n.calcCenterPosition=function(t,e){return e.offsetTop-(t/2-e.clientHeight/2)},n}(y.Component),Dr=3,Mi=function(a){G(n,a);function n(t){var e=a.call(this,t)||this;return e.YEAR_REFS=pe([],Array(e.props.yearItemNumber),!0).map(function(){return y.createRef()}),e.isDisabled=function(r){return ne(r,{minDate:e.props.minDate,maxDate:e.props.maxDate,excludeDates:e.props.excludeDates,includeDates:e.props.includeDates,filterDate:e.props.filterDate})},e.isExcluded=function(r){return Qt(r,{excludeDates:e.props.excludeDates})},e.selectingDate=function(){var r;return(r=e.props.selectingDate)!==null&&r!==void 0?r:e.props.preSelection},e.updateFocusOnPaginate=function(r){var o=function(){var i,s;(s=(i=e.YEAR_REFS[r])===null||i===void 0?void 0:i.current)===null||s===void 0||s.focus()};window.requestAnimationFrame(o)},e.handleYearClick=function(r,o){e.props.onDayClick&&e.props.onDayClick(r,o)},e.handleYearNavigation=function(r,o){var i,s,c,u,l=e.props,d=l.date,p=l.yearItemNumber;if(!(d===void 0||p===void 0)){var h=be(d,p).startPeriod;e.isDisabled(o)||e.isExcluded(o)||((s=(i=e.props).setPreSelection)===null||s===void 0||s.call(i,o),r-h<0?e.updateFocusOnPaginate(p-(h-r)):r-h>=p?e.updateFocusOnPaginate(Math.abs(p-(r-h))):(u=(c=e.YEAR_REFS[r-h])===null||c===void 0?void 0:c.current)===null||u===void 0||u.focus())}},e.isSameDay=function(r,o){return P(r,o)},e.isCurrentYear=function(r){return r===S(Y())},e.isRangeStart=function(r){return e.props.startDate&&e.props.endDate&&le(ce(Y(),r),e.props.startDate)},e.isRangeEnd=function(r){return e.props.startDate&&e.props.endDate&&le(ce(Y(),r),e.props.endDate)},e.isInRange=function(r){return et(r,e.props.startDate,e.props.endDate)},e.isInSelectingRange=function(r){var o=e.props,i=o.selectsStart,s=o.selectsEnd,c=o.selectsRange,u=o.startDate,l=o.endDate;return!(i||s||c)||!e.selectingDate()?!1:i&&l?et(r,e.selectingDate(),l):s&&u||c&&u&&!l?et(r,u,e.selectingDate()):!1},e.isSelectingRangeStart=function(r){var o;if(!e.isInSelectingRange(r))return!1;var i=e.props,s=i.startDate,c=i.selectsStart,u=ce(Y(),r);return c?le(u,(o=e.selectingDate())!==null&&o!==void 0?o:null):le(u,s??null)},e.isSelectingRangeEnd=function(r){var o;if(!e.isInSelectingRange(r))return!1;var i=e.props,s=i.endDate,c=i.selectsEnd,u=i.selectsRange,l=ce(Y(),r);return c||u?le(l,(o=e.selectingDate())!==null&&o!==void 0?o:null):le(l,s??null)},e.isKeyboardSelected=function(r){if(!(e.props.date===void 0||e.props.selected==null||e.props.preSelection==null)){var o=e.props,i=o.minDate,s=o.maxDate,c=o.excludeDates,u=o.includeDates,l=o.filterDate,d=Ve(ce(e.props.date,r)),p=(i||s||c||u||l)&&st(r,e.props);return!e.props.disabledKeyboardNavigation&&!e.props.inline&&!P(d,Ve(e.props.selected))&&P(d,Ve(e.props.preSelection))&&!p}},e.isSelectedYear=function(r){var o=e.props,i=o.selectsMultiple,s=o.selected,c=o.selectedDates;return i?c==null?void 0:c.some(function(u){return r===S(u)}):!s||r===S(s)},e.onYearClick=function(r,o){var i=e.props.date;i!==void 0&&e.handleYearClick(Ve(ce(i,o)),r)},e.onYearKeyDown=function(r,o){var i,s,c=r.key,u=e.props,l=u.date,d=u.yearItemNumber,p=u.handleOnKeyDown;if(c!==D.Tab&&r.preventDefault(),!e.props.disabledKeyboardNavigation)switch(c){case D.Enter:if(e.props.selected==null)break;e.onYearClick(r,o),(s=(i=e.props).setPreSelection)===null||s===void 0||s.call(i,e.props.selected);break;case D.ArrowRight:if(e.props.preSelection==null)break;e.handleYearNavigation(o+1,fe(e.props.preSelection,1));break;case D.ArrowLeft:if(e.props.preSelection==null)break;e.handleYearNavigation(o-1,Fe(e.props.preSelection,1));break;case D.ArrowUp:{if(l===void 0||d===void 0||e.props.preSelection==null)break;var h=be(l,d).startPeriod,m=Dr,g=o-m;if(g<h){var v=d%m;o>=h&&o<h+v?m=v:m+=v,g=o-m}e.handleYearNavigation(g,Fe(e.props.preSelection,m));break}case D.ArrowDown:{if(l===void 0||d===void 0||e.props.preSelection==null)break;var k=be(l,d).endPeriod,m=Dr,g=o+m;if(g>k){var v=d%m;o<=k&&o>k-v?m=v:m+=v,g=o+m}e.handleYearNavigation(g,fe(e.props.preSelection,m));break}}p&&p(r)},e.getYearClassNames=function(r){var o=e.props,i=o.date,s=o.minDate,c=o.maxDate,u=o.excludeDates,l=o.includeDates,d=o.filterDate,p=o.yearClassName;return $("react-datepicker__year-text","react-datepicker__year-".concat(r),i?p==null?void 0:p(ce(i,r)):void 0,{"react-datepicker__year-text--selected":e.isSelectedYear(r),"react-datepicker__year-text--disabled":(s||c||u||l||d)&&st(r,e.props),"react-datepicker__year-text--keyboard-selected":e.isKeyboardSelected(r),"react-datepicker__year-text--range-start":e.isRangeStart(r),"react-datepicker__year-text--range-end":e.isRangeEnd(r),"react-datepicker__year-text--in-range":e.isInRange(r),"react-datepicker__year-text--in-selecting-range":e.isInSelectingRange(r),"react-datepicker__year-text--selecting-range-start":e.isSelectingRangeStart(r),"react-datepicker__year-text--selecting-range-end":e.isSelectingRangeEnd(r),"react-datepicker__year-text--today":e.isCurrentYear(r)})},e.getYearTabIndex=function(r){if(e.props.disabledKeyboardNavigation||e.props.preSelection==null)return"-1";var o=S(e.props.preSelection),i=st(r,e.props);return r===o&&!i?"0":"-1"},e.getYearContent=function(r){return e.props.renderYearContent?e.props.renderYearContent(r):r},e}return n.prototype.render=function(){var t=this,e=[],r=this.props,o=r.date,i=r.yearItemNumber,s=r.onYearMouseEnter,c=r.onYearMouseLeave;if(o===void 0)return null;for(var u=be(o,i),l=u.startPeriod,d=u.endPeriod,p=function(g){e.push(f.createElement("div",{ref:h.YEAR_REFS[g-l],onClick:function(v){t.onYearClick(v,g)},onKeyDown:function(v){qr(v)&&(v.preventDefault(),v.key=D.Enter),t.onYearKeyDown(v,g)},tabIndex:Number(h.getYearTabIndex(g)),className:h.getYearClassNames(g),onMouseEnter:h.props.usePointerEvent?void 0:function(v){return s(v,g)},onPointerEnter:h.props.usePointerEvent?function(v){return s(v,g)}:void 0,onMouseLeave:h.props.usePointerEvent?void 0:function(v){return c(v,g)},onPointerLeave:h.props.usePointerEvent?function(v){return c(v,g)}:void 0,key:g,"aria-current":h.isCurrentYear(g)?"date":void 0},h.getYearContent(g)))},h=this,m=l;m<=d;m++)p(m);return f.createElement("div",{className:"react-datepicker__year"},f.createElement("div",{className:"react-datepicker__year-wrapper",onMouseLeave:this.props.usePointerEvent?void 0:this.props.clearSelectingDate,onPointerLeave:this.props.usePointerEvent?this.props.clearSelectingDate:void 0},e))},n}(y.Component);function Ci(a,n,t,e){for(var r=[],o=0;o<2*n+1;o++){var i=a+n-o,s=!0;t&&(s=S(t)<=i),e&&s&&(s=S(e)>=i),s&&r.push(i)}return r}var xi=function(a){G(n,a);function n(t){var e=a.call(this,t)||this;e.renderOptions=function(){var s=e.props.year,c=e.state.yearsList.map(function(d){return f.createElement("div",{className:s===d?"react-datepicker__year-option react-datepicker__year-option--selected_year":"react-datepicker__year-option",key:d,onClick:e.onChange.bind(e,d),"aria-selected":s===d?"true":void 0},s===d?f.createElement("span",{className:"react-datepicker__year-option--selected"},"✓"):"",d)}),u=e.props.minDate?S(e.props.minDate):null,l=e.props.maxDate?S(e.props.maxDate):null;return(!l||!e.state.yearsList.find(function(d){return d===l}))&&c.unshift(f.createElement("div",{className:"react-datepicker__year-option",key:"upcoming",onClick:e.incrementYears},f.createElement("a",{className:"react-datepicker__navigation react-datepicker__navigation--years react-datepicker__navigation--years-upcoming"}))),(!u||!e.state.yearsList.find(function(d){return d===u}))&&c.push(f.createElement("div",{className:"react-datepicker__year-option",key:"previous",onClick:e.decrementYears},f.createElement("a",{className:"react-datepicker__navigation react-datepicker__navigation--years react-datepicker__navigation--years-previous"}))),c},e.onChange=function(s){e.props.onChange(s)},e.handleClickOutside=function(){e.props.onCancel()},e.shiftYears=function(s){var c=e.state.yearsList.map(function(u){return u+s});e.setState({yearsList:c})},e.incrementYears=function(){return e.shiftYears(1)},e.decrementYears=function(){return e.shiftYears(-1)};var r=t.yearDropdownItemNumber,o=t.scrollableYearDropdown,i=r||(o?10:5);return e.state={yearsList:Ci(e.props.year,i,e.props.minDate,e.props.maxDate)},e.dropdownRef=y.createRef(),e}return n.prototype.componentDidMount=function(){var t=this.dropdownRef.current;if(t){var e=t.children?Array.from(t.children):null,r=e?e.find(function(o){return o.ariaSelected}):null;t.scrollTop=r&&r instanceof HTMLElement?r.offsetTop+(r.clientHeight-t.clientHeight)/2:(t.scrollHeight-t.clientHeight)/2}},n.prototype.render=function(){var t=$({"react-datepicker__year-dropdown":!0,"react-datepicker__year-dropdown--scrollable":this.props.scrollableYearDropdown});return f.createElement(yt,{className:t,containerRef:this.dropdownRef,onClickOutside:this.handleClickOutside},this.renderOptions())},n}(y.Component),Si=function(a){G(n,a);function n(){var t=a!==null&&a.apply(this,arguments)||this;return t.state={dropdownVisible:!1},t.renderSelectOptions=function(){for(var e=t.props.minDate?S(t.props.minDate):1900,r=t.props.maxDate?S(t.props.maxDate):2100,o=[],i=e;i<=r;i++)o.push(f.createElement("option",{key:i,value:i},i));return o},t.onSelectChange=function(e){t.onChange(parseInt(e.target.value))},t.renderSelectMode=function(){return f.createElement("select",{value:t.props.year,className:"react-datepicker__year-select",onChange:t.onSelectChange},t.renderSelectOptions())},t.renderReadView=function(e){return f.createElement("div",{key:"read",style:{visibility:e?"visible":"hidden"},className:"react-datepicker__year-read-view",onClick:function(r){return t.toggleDropdown(r)}},f.createElement("span",{className:"react-datepicker__year-read-view--down-arrow"}),f.createElement("span",{className:"react-datepicker__year-read-view--selected-year"},t.props.year))},t.renderDropdown=function(){return f.createElement(xi,E({key:"dropdown"},t.props,{onChange:t.onChange,onCancel:t.toggleDropdown}))},t.renderScrollMode=function(){var e=t.state.dropdownVisible,r=[t.renderReadView(!e)];return e&&r.unshift(t.renderDropdown()),r},t.onChange=function(e){t.toggleDropdown(),e!==t.props.year&&t.props.onChange(e)},t.toggleDropdown=function(e){t.setState({dropdownVisible:!t.state.dropdownVisible},function(){t.props.adjustDateOnChange&&t.handleYearChange(t.props.date,e)})},t.handleYearChange=function(e,r){var o;(o=t.onSelect)===null||o===void 0||o.call(t,e,r),t.setOpen()},t.onSelect=function(e,r){var o,i;(i=(o=t.props).onSelect)===null||i===void 0||i.call(o,e,r)},t.setOpen=function(){var e,r;(r=(e=t.props).setOpen)===null||r===void 0||r.call(e,!0)},t}return n.prototype.render=function(){var t;switch(this.props.dropdownMode){case"scroll":t=this.renderScrollMode();break;case"select":t=this.renderSelectMode();break}return f.createElement("div",{className:"react-datepicker__year-dropdown-container react-datepicker__year-dropdown-container--".concat(this.props.dropdownMode)},t)},n}(y.Component),Pi=["react-datepicker__year-select","react-datepicker__month-select","react-datepicker__month-year-select"],Ei=function(a){var n=(a.className||"").split(/\s+/);return Pi.some(function(t){return n.indexOf(t)>=0})},Ti=function(a){G(n,a);function n(t){var e=a.call(this,t)||this;return e.monthContainer=void 0,e.handleClickOutside=function(r){e.props.onClickOutside(r)},e.setClickOutsideRef=function(){return e.containerRef.current},e.handleDropdownFocus=function(r){var o,i;Ei(r.target)&&((i=(o=e.props).onDropdownFocus)===null||i===void 0||i.call(o,r))},e.getDateInView=function(){var r=e.props,o=r.preSelection,i=r.selected,s=r.openToDate,c=Br(e.props),u=Kr(e.props),l=Y(),d=s||i||o;return d||(c&&Te(l,c)?c:u&&Me(l,u)?u:l)},e.increaseMonth=function(){e.setState(function(r){var o=r.date;return{date:ie(o,1)}},function(){return e.handleMonthChange(e.state.date)})},e.decreaseMonth=function(){e.setState(function(r){var o=r.date;return{date:Pe(o,1)}},function(){return e.handleMonthChange(e.state.date)})},e.handleDayClick=function(r,o,i){e.props.onSelect(r,o,i),e.props.setPreSelection&&e.props.setPreSelection(r)},e.handleDayMouseEnter=function(r){e.setState({selectingDate:r}),e.props.onDayMouseEnter&&e.props.onDayMouseEnter(r)},e.handleMonthMouseLeave=function(){e.setState({selectingDate:void 0}),e.props.onMonthMouseLeave&&e.props.onMonthMouseLeave()},e.handleYearMouseEnter=function(r,o){e.setState({selectingDate:ce(Y(),o)}),e.props.onYearMouseEnter&&e.props.onYearMouseEnter(r,o)},e.handleYearMouseLeave=function(r,o){e.props.onYearMouseLeave&&e.props.onYearMouseLeave(r,o)},e.handleYearChange=function(r){var o,i,s,c;(i=(o=e.props).onYearChange)===null||i===void 0||i.call(o,r),e.setState({isRenderAriaLiveMessage:!0}),e.props.adjustDateOnChange&&(e.props.onSelect(r),(c=(s=e.props).setOpen)===null||c===void 0||c.call(s,!0)),e.props.setPreSelection&&e.props.setPreSelection(r)},e.getEnabledPreSelectionDateForMonth=function(r){if(!ne(r,e.props))return r;for(var o=ge(r),i=zo(r),s=un(i,o),c=null,u=0;u<=s;u++){var l=oe(o,u);if(!ne(l,e.props)){c=l;break}}return c},e.handleMonthChange=function(r){var o,i,s,c=(o=e.getEnabledPreSelectionDateForMonth(r))!==null&&o!==void 0?o:r;e.handleCustomMonthChange(c),e.props.adjustDateOnChange&&(e.props.onSelect(c),(s=(i=e.props).setOpen)===null||s===void 0||s.call(i,!0)),e.props.setPreSelection&&e.props.setPreSelection(c)},e.handleCustomMonthChange=function(r){var o,i;(i=(o=e.props).onMonthChange)===null||i===void 0||i.call(o,r),e.setState({isRenderAriaLiveMessage:!0})},e.handleMonthYearChange=function(r){e.handleYearChange(r),e.handleMonthChange(r)},e.changeYear=function(r){e.setState(function(o){var i=o.date;return{date:ce(i,Number(r))}},function(){return e.handleYearChange(e.state.date)})},e.changeMonth=function(r){e.setState(function(o){var i=o.date;return{date:re(i,Number(r))}},function(){return e.handleMonthChange(e.state.date)})},e.changeMonthYear=function(r){e.setState(function(o){var i=o.date;return{date:ce(re(i,J(r)),S(r))}},function(){return e.handleMonthYearChange(e.state.date)})},e.header=function(r){r===void 0&&(r=e.state.date);var o=_e(r,e.props.locale,e.props.calendarStartDay),i=[];return e.props.showWeekNumbers&&i.push(f.createElement("div",{key:"W",className:"react-datepicker__day-name"},e.props.weekLabel||"#")),i.concat([0,1,2,3,4,5,6].map(function(s){var c=oe(o,s),u=e.formatWeekday(c,e.props.locale),l=e.props.weekDayClassName?e.props.weekDayClassName(c):void 0;return f.createElement("div",{key:s,"aria-label":W(c,"EEEE",e.props.locale),className:$("react-datepicker__day-name",l)},u)}))},e.formatWeekday=function(r,o){return e.props.formatWeekDay?Zo(r,e.props.formatWeekDay,o):e.props.useWeekdaysShort?ei(r,o):Jo(r,o)},e.decreaseYear=function(){e.setState(function(r){var o,i=r.date;return{date:Fe(i,e.props.showYearPicker?(o=e.props.yearItemNumber)!==null&&o!==void 0?o:n.defaultProps.yearItemNumber:1)}},function(){return e.handleYearChange(e.state.date)})},e.clearSelectingDate=function(){e.setState({selectingDate:void 0})},e.renderPreviousButton=function(){var r,o,i;if(!e.props.renderCustomHeader){var s=(r=e.props.monthsShown)!==null&&r!==void 0?r:n.defaultProps.monthsShown,c=e.props.showPreviousMonths?s-1:0,u=(o=e.props.monthSelectedIn)!==null&&o!==void 0?o:c,l=Pe(e.state.date,u),d;switch(!0){case e.props.showMonthYearPicker:d=ur(e.state.date,e.props);break;case e.props.showYearPicker:d=oi(e.state.date,e.props);break;case e.props.showQuarterYearPicker:d=ni(e.state.date,e.props);break;default:d=cr(l,e.props);break}if(!(!((i=e.props.forceShowMonthNavigation)!==null&&i!==void 0?i:n.defaultProps.forceShowMonthNavigation)&&!e.props.showDisabledMonthNavigation&&d||e.props.showTimeSelectOnly)){var p=["react-datepicker__navigation-icon","react-datepicker__navigation-icon--previous"],h=["react-datepicker__navigation","react-datepicker__navigation--previous"],m=e.decreaseMonth;(e.props.showMonthYearPicker||e.props.showQuarterYearPicker||e.props.showYearPicker)&&(m=e.decreaseYear),d&&e.props.showDisabledMonthNavigation&&(h.push("react-datepicker__navigation--previous--disabled"),m=void 0);var g=e.props.showMonthYearPicker||e.props.showQuarterYearPicker||e.props.showYearPicker,v=e.props,k=v.previousMonthButtonLabel,x=k===void 0?n.defaultProps.previousMonthButtonLabel:k,M=v.previousYearButtonLabel,T=M===void 0?n.defaultProps.previousYearButtonLabel:M,B=e.props,A=B.previousMonthAriaLabel,N=A===void 0?typeof x=="string"?x:"Previous Month":A,_=B.previousYearAriaLabel,C=_===void 0?typeof T=="string"?T:"Previous Year":_;return f.createElement("button",{type:"button",className:h.join(" "),onClick:m,onKeyDown:e.props.handleOnKeyDown,"aria-label":g?C:N},f.createElement("span",{className:p.join(" ")},g?T:x))}}},e.increaseYear=function(){e.setState(function(r){var o,i=r.date;return{date:fe(i,e.props.showYearPicker?(o=e.props.yearItemNumber)!==null&&o!==void 0?o:n.defaultProps.yearItemNumber:1)}},function(){return e.handleYearChange(e.state.date)})},e.renderNextButton=function(){var r;if(!e.props.renderCustomHeader){var o;switch(!0){case e.props.showMonthYearPicker:o=dr(e.state.date,e.props);break;case e.props.showYearPicker:o=ii(e.state.date,e.props);break;case e.props.showQuarterYearPicker:o=ai(e.state.date,e.props);break;default:o=lr(e.state.date,e.props);break}if(!(!((r=e.props.forceShowMonthNavigation)!==null&&r!==void 0?r:n.defaultProps.forceShowMonthNavigation)&&!e.props.showDisabledMonthNavigation&&o||e.props.showTimeSelectOnly)){var i=["react-datepicker__navigation","react-datepicker__navigation--next"],s=["react-datepicker__navigation-icon","react-datepicker__navigation-icon--next"];e.props.showTimeSelect&&i.push("react-datepicker__navigation--next--with-time"),e.props.todayButton&&i.push("react-datepicker__navigation--next--with-today-button");var c=e.increaseMonth;(e.props.showMonthYearPicker||e.props.showQuarterYearPicker||e.props.showYearPicker)&&(c=e.increaseYear),o&&e.props.showDisabledMonthNavigation&&(i.push("react-datepicker__navigation--next--disabled"),c=void 0);var u=e.props.showMonthYearPicker||e.props.showQuarterYearPicker||e.props.showYearPicker,l=e.props,d=l.nextMonthButtonLabel,p=d===void 0?n.defaultProps.nextMonthButtonLabel:d,h=l.nextYearButtonLabel,m=h===void 0?n.defaultProps.nextYearButtonLabel:h,g=e.props,v=g.nextMonthAriaLabel,k=v===void 0?typeof p=="string"?p:"Next Month":v,x=g.nextYearAriaLabel,M=x===void 0?typeof m=="string"?m:"Next Year":x;return f.createElement("button",{type:"button",className:i.join(" "),onClick:c,onKeyDown:e.props.handleOnKeyDown,"aria-label":u?M:k},f.createElement("span",{className:s.join(" ")},u?m:p))}}},e.renderCurrentMonth=function(r){r===void 0&&(r=e.state.date);var o=["react-datepicker__current-month"];return e.props.showYearDropdown&&o.push("react-datepicker__current-month--hasYearDropdown"),e.props.showMonthDropdown&&o.push("react-datepicker__current-month--hasMonthDropdown"),e.props.showMonthYearDropdown&&o.push("react-datepicker__current-month--hasMonthYearDropdown"),f.createElement("h2",{className:o.join(" ")},W(r,e.props.dateFormat,e.props.locale))},e.renderYearDropdown=function(r){if(r===void 0&&(r=!1),!(!e.props.showYearDropdown||r))return f.createElement(Si,E({},n.defaultProps,e.props,{date:e.state.date,onChange:e.changeYear,year:S(e.state.date)}))},e.renderMonthDropdown=function(r){if(r===void 0&&(r=!1),!(!e.props.showMonthDropdown||r))return f.createElement(wi,E({},n.defaultProps,e.props,{month:J(e.state.date),onChange:e.changeMonth}))},e.renderMonthYearDropdown=function(r){if(r===void 0&&(r=!1),!(!e.props.showMonthYearDropdown||r))return f.createElement(ki,E({},n.defaultProps,e.props,{date:e.state.date,onChange:e.changeMonthYear}))},e.handleTodayButtonClick=function(r){e.props.onSelect(nr(),r),e.props.setPreSelection&&e.props.setPreSelection(nr())},e.renderTodayButton=function(){if(!(!e.props.todayButton||e.props.showTimeSelectOnly))return f.createElement("div",{className:"react-datepicker__today-button",onClick:e.handleTodayButtonClick},e.props.todayButton)},e.renderDefaultHeader=function(r){var o=r.monthDate,i=r.i;return f.createElement("div",{className:"react-datepicker__header ".concat(e.props.showTimeSelect?"react-datepicker__header--has-time-select":"")},e.renderCurrentMonth(o),f.createElement("div",{className:"react-datepicker__header__dropdown react-datepicker__header__dropdown--".concat(e.props.dropdownMode),onFocus:e.handleDropdownFocus},e.renderMonthDropdown(i!==0),e.renderMonthYearDropdown(i!==0),e.renderYearDropdown(i!==0)),f.createElement("div",{className:"react-datepicker__day-names"},e.header(o)))},e.renderCustomHeader=function(r){var o,i,s=r.monthDate,c=r.i;if(e.props.showTimeSelect&&!e.state.monthContainer||e.props.showTimeSelectOnly)return null;var u=cr(e.state.date,e.props),l=lr(e.state.date,e.props),d=ur(e.state.date,e.props),p=dr(e.state.date,e.props),h=!e.props.showMonthYearPicker&&!e.props.showQuarterYearPicker&&!e.props.showYearPicker;return f.createElement("div",{className:"react-datepicker__header react-datepicker__header--custom",onFocus:e.props.onDropdownFocus},(i=(o=e.props).renderCustomHeader)===null||i===void 0?void 0:i.call(o,E(E({},e.state),{customHeaderCount:c,monthDate:s,changeMonth:e.changeMonth,changeYear:e.changeYear,decreaseMonth:e.decreaseMonth,increaseMonth:e.increaseMonth,decreaseYear:e.decreaseYear,increaseYear:e.increaseYear,prevMonthButtonDisabled:u,nextMonthButtonDisabled:l,prevYearButtonDisabled:d,nextYearButtonDisabled:p})),h&&f.createElement("div",{className:"react-datepicker__day-names"},e.header(s)))},e.renderYearHeader=function(r){var o=r.monthDate,i=e.props,s=i.showYearPicker,c=i.yearItemNumber,u=c===void 0?n.defaultProps.yearItemNumber:c,l=be(o,u),d=l.startPeriod,p=l.endPeriod;return f.createElement("div",{className:"react-datepicker__header react-datepicker-year-header"},s?"".concat(d," - ").concat(p):S(o))},e.renderHeader=function(r){var o=r.monthDate,i=r.i,s=i===void 0?0:i,c={monthDate:o,i:s};switch(!0){case e.props.renderCustomHeader!==void 0:return e.renderCustomHeader(c);case(e.props.showMonthYearPicker||e.props.showQuarterYearPicker||e.props.showYearPicker):return e.renderYearHeader(c);default:return e.renderDefaultHeader(c)}},e.renderMonths=function(){var r,o;if(!(e.props.showTimeSelectOnly||e.props.showYearPicker)){for(var i=[],s=(r=e.props.monthsShown)!==null&&r!==void 0?r:n.defaultProps.monthsShown,c=e.props.showPreviousMonths?s-1:0,u=e.props.showMonthYearPicker||e.props.showQuarterYearPicker?fe(e.state.date,c):Pe(e.state.date,c),l=(o=e.props.monthSelectedIn)!==null&&o!==void 0?o:c,d=0;d<s;++d){var p=d-l+c,h=e.props.showMonthYearPicker||e.props.showQuarterYearPicker?fe(u,p):ie(u,p),m="month-".concat(d),g=d<s-1,v=d>0;i.push(f.createElement("div",{key:m,ref:function(k){e.monthContainer=k??void 0},className:"react-datepicker__month-container"},e.renderHeader({monthDate:h,i:d}),f.createElement(gi,E({},n.defaultProps,e.props,{containerRef:e.containerRef,ariaLabelPrefix:e.props.monthAriaLabelPrefix,day:h,onDayClick:e.handleDayClick,handleOnKeyDown:e.props.handleOnDayKeyDown,handleOnMonthKeyDown:e.props.handleOnKeyDown,onDayMouseEnter:e.handleDayMouseEnter,onMouseLeave:e.handleMonthMouseLeave,orderInDisplay:d,selectingDate:e.state.selectingDate,monthShowsDuplicateDaysEnd:g,monthShowsDuplicateDaysStart:v}))))}return i}},e.renderYears=function(){if(!e.props.showTimeSelectOnly&&e.props.showYearPicker)return f.createElement("div",{className:"react-datepicker__year--container"},e.renderHeader({monthDate:e.state.date}),f.createElement(Mi,E({},n.defaultProps,e.props,{selectingDate:e.state.selectingDate,date:e.state.date,onDayClick:e.handleDayClick,clearSelectingDate:e.clearSelectingDate,onYearMouseEnter:e.handleYearMouseEnter,onYearMouseLeave:e.handleYearMouseLeave})))},e.renderTimeSection=function(){if(e.props.showTimeSelect&&(e.state.monthContainer||e.props.showTimeSelectOnly))return f.createElement(_i,E({},n.defaultProps,e.props,{onChange:e.props.onTimeChange,format:e.props.timeFormat,intervals:e.props.timeIntervals,monthRef:e.state.monthContainer}))},e.renderInputTimeSection=function(){var r=e.props.selected?new Date(e.props.selected):void 0,o=r&&bt(r)&&!!e.props.selected,i=o?"".concat(fr(r.getHours()),":").concat(fr(r.getMinutes())):"";if(e.props.showTimeInput)return f.createElement(pi,E({},n.defaultProps,e.props,{date:r,timeString:i,onChange:e.props.onTimeChange}))},e.renderAriaLiveRegion=function(){var r,o=be(e.state.date,(r=e.props.yearItemNumber)!==null&&r!==void 0?r:n.defaultProps.yearItemNumber),i=o.startPeriod,s=o.endPeriod,c;return e.props.showYearPicker?c="".concat(i," - ").concat(s):e.props.showMonthYearPicker||e.props.showQuarterYearPicker?c=S(e.state.date):c="".concat(Ht(J(e.state.date),e.props.locale)," ").concat(S(e.state.date)),f.createElement("span",{role:"alert","aria-live":"polite",className:"react-datepicker__aria-live"},e.state.isRenderAriaLiveMessage&&c)},e.renderChildren=function(){if(e.props.children)return f.createElement("div",{className:"react-datepicker__children-container"},e.props.children)},e.containerRef=y.createRef(),e.state={date:e.getDateInView(),selectingDate:void 0,monthContainer:void 0,isRenderAriaLiveMessage:!1},e}return Object.defineProperty(n,"defaultProps",{get:function(){return{monthsShown:1,forceShowMonthNavigation:!1,timeCaption:"Time",previousYearButtonLabel:"Previous Year",nextYearButtonLabel:"Next Year",previousMonthButtonLabel:"Previous Month",nextMonthButtonLabel:"Next Month",yearItemNumber:Xe}},enumerable:!1,configurable:!0}),n.prototype.componentDidMount=function(){var t=this;this.props.showTimeSelect&&(this.assignMonthContainer=function(){t.setState({monthContainer:t.monthContainer})}())},n.prototype.componentDidUpdate=function(t){var e=this;if(this.props.preSelection&&(!P(this.props.preSelection,t.preSelection)||this.props.monthSelectedIn!==t.monthSelectedIn)){var r=!Z(this.state.date,this.props.preSelection);this.setState({date:this.props.preSelection},function(){return r&&e.handleCustomMonthChange(e.state.date)})}else this.props.openToDate&&!P(this.props.openToDate,t.openToDate)&&this.setState({date:this.props.openToDate})},n.prototype.render=function(){var t=this.props.container||qo;return f.createElement(yt,{onClickOutside:this.handleClickOutside,style:{display:"contents"},ignoreClass:this.props.outsideClickIgnoreClass},f.createElement("div",{style:{display:"contents"},ref:this.containerRef},f.createElement(t,{className:$("react-datepicker",this.props.className,{"react-datepicker--time-only":this.props.showTimeSelectOnly}),showTime:this.props.showTimeSelect||this.props.showTimeInput,showTimeSelectOnly:this.props.showTimeSelectOnly},this.renderAriaLiveRegion(),this.renderPreviousButton(),this.renderNextButton(),this.renderMonths(),this.renderYears(),this.renderTodayButton(),this.renderTimeSection(),this.renderInputTimeSection(),this.renderChildren())))},n}(y.Component),Oi=function(a){var n=a.icon,t=a.className,e=t===void 0?"":t,r=a.onClick,o="react-datepicker__calendar-icon";if(typeof n=="string")return f.createElement("i",{className:"".concat(o," ").concat(n," ").concat(e),"aria-hidden":"true",onClick:r});if(f.isValidElement(n)){var i=n;return f.cloneElement(i,{className:"".concat(i.props.className||""," ").concat(o," ").concat(e),onClick:function(s){typeof i.props.onClick=="function"&&i.props.onClick(s),typeof r=="function"&&r(s)}})}return f.createElement("svg",{className:"".concat(o," ").concat(e),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",onClick:r},f.createElement("path",{d:"M96 32V64H48C21.5 64 0 85.5 0 112v48H448V112c0-26.5-21.5-48-48-48H352V32c0-17.7-14.3-32-32-32s-32 14.3-32 32V64H160V32c0-17.7-14.3-32-32-32S96 14.3 96 32zM448 192H0V464c0 26.5 21.5 48 48 48H400c26.5 0 48-21.5 48-48V192z"}))},Vr=function(a){G(n,a);function n(t){var e=a.call(this,t)||this;return e.portalRoot=null,e.el=document.createElement("div"),e}return n.prototype.componentDidMount=function(){this.portalRoot=(this.props.portalHost||document).getElementById(this.props.portalId),this.portalRoot||(this.portalRoot=document.createElement("div"),this.portalRoot.setAttribute("id",this.props.portalId),(this.props.portalHost||document.body).appendChild(this.portalRoot)),this.portalRoot.appendChild(this.el)},n.prototype.componentWillUnmount=function(){this.portalRoot&&this.portalRoot.removeChild(this.el)},n.prototype.render=function(){return zr.createPortal(this.props.children,this.el)},n}(y.Component),Ni="[tabindex], a, button, input, select, textarea",Yi=function(a){return(a instanceof HTMLAnchorElement||!a.disabled)&&a.tabIndex!==-1},Ur=function(a){G(n,a);function n(t){var e=a.call(this,t)||this;return e.getTabChildren=function(){var r;return Array.prototype.slice.call((r=e.tabLoopRef.current)===null||r===void 0?void 0:r.querySelectorAll(Ni),1,-1).filter(Yi)},e.handleFocusStart=function(){var r=e.getTabChildren();r&&r.length>1&&r[r.length-1].focus()},e.handleFocusEnd=function(){var r=e.getTabChildren();r&&r.length>1&&r[0].focus()},e.tabLoopRef=y.createRef(),e}return n.prototype.render=function(){var t;return((t=this.props.enableTabLoop)!==null&&t!==void 0?t:n.defaultProps.enableTabLoop)?f.createElement("div",{className:"react-datepicker__tab-loop",ref:this.tabLoopRef},f.createElement("div",{className:"react-datepicker__tab-loop__start",tabIndex:0,onFocus:this.handleFocusStart}),this.props.children,f.createElement("div",{className:"react-datepicker__tab-loop__end",tabIndex:0,onFocus:this.handleFocusEnd})):this.props.children},n.defaultProps={enableTabLoop:!0},n}(y.Component);function Ii(a){var n=function(t){var e,r=typeof t.hidePopper=="boolean"?t.hidePopper:!0,o=y.useRef(null),i=Ko(E({open:!r,whileElementsMounted:Zr,placement:t.popperPlacement,middleware:pe([Jr({padding:15}),en(10),tn({element:o})],(e=t.popperModifiers)!==null&&e!==void 0?e:[],!0)},t.popperProps)),s=E(E({},t),{hidePopper:r,popperProps:E(E({},i),{arrowRef:o})});return f.createElement(a,E({},s))};return n}var Ri=function(a){G(n,a);function n(){return a!==null&&a.apply(this,arguments)||this}return Object.defineProperty(n,"defaultProps",{get:function(){return{hidePopper:!0}},enumerable:!1,configurable:!0}),n.prototype.render=function(){var t=this.props,e=t.className,r=t.wrapperClassName,o=t.hidePopper,i=o===void 0?n.defaultProps.hidePopper:o,s=t.popperComponent,c=t.targetComponent,u=t.enableTabLoop,l=t.popperOnKeyDown,d=t.portalId,p=t.portalHost,h=t.popperProps,m=t.showArrow,g=void 0;if(!i){var v=$("react-datepicker-popper",e);g=f.createElement(Ur,{enableTabLoop:u},f.createElement("div",{ref:h.refs.setFloating,style:h.floatingStyles,className:v,"data-placement":h.placement,onKeyDown:l},s,m&&f.createElement(Lo,{ref:h.arrowRef,context:h.context,fill:"currentColor",strokeWidth:1,height:8,width:16,style:{transform:"translateY(-1px)"},className:"react-datepicker__triangle"})))}this.props.popperContainer&&(g=y.createElement(this.props.popperContainer,{},g)),d&&!i&&(g=f.createElement(Vr,{portalId:d,portalHost:p},g));var k=$("react-datepicker-wrapper",r);return f.createElement(f.Fragment,null,f.createElement("div",{ref:h.refs.setReference,className:k},c),g)},n}(y.Component),Li=Ii(Ri),wr="react-datepicker-ignore-onclickoutside";function Fi(a,n){return a&&n?J(a)!==J(n)||S(a)!==S(n):a!==n}var St="Date input not valid.",Qi=function(a){G(n,a);function n(t){var e=a.call(this,t)||this;return e.calendar=null,e.input=null,e.getPreSelection=function(){return e.props.openToDate?e.props.openToDate:e.props.selectsEnd&&e.props.startDate?e.props.startDate:e.props.selectsStart&&e.props.endDate?e.props.endDate:Y()},e.modifyHolidays=function(){var r;return(r=e.props.holidays)===null||r===void 0?void 0:r.reduce(function(o,i){var s=new Date(i.date);return bt(s)?pe(pe([],o,!0),[E(E({},i),{date:s})],!1):o},[])},e.calcInitialState=function(){var r,o=e.getPreSelection(),i=Br(e.props),s=Kr(e.props),c=i&&Te(o,it(i))?i:s&&Me(o,ar(s))?s:o;return{open:e.props.startOpen||!1,preventFocus:!1,inputValue:null,preSelection:(r=e.props.selectsRange?e.props.startDate:e.props.selected)!==null&&r!==void 0?r:c,highlightDates:pr(e.props.highlightDates),focused:!1,shouldFocusDayInline:!1,isRenderAriaLiveMessage:!1,wasHidden:!1}},e.resetHiddenStatus=function(){e.setState(E(E({},e.state),{wasHidden:!1}))},e.setHiddenStatus=function(){e.setState(E(E({},e.state),{wasHidden:!0}))},e.setHiddenStateOnVisibilityHidden=function(){document.visibilityState==="hidden"&&e.setHiddenStatus()},e.clearPreventFocusTimeout=function(){e.preventFocusTimeout&&clearTimeout(e.preventFocusTimeout)},e.setFocus=function(){var r,o;(o=(r=e.input)===null||r===void 0?void 0:r.focus)===null||o===void 0||o.call(r,{preventScroll:!0})},e.setBlur=function(){var r,o;(o=(r=e.input)===null||r===void 0?void 0:r.blur)===null||o===void 0||o.call(r),e.cancelFocusInput()},e.deferBlur=function(){requestAnimationFrame(function(){e.setBlur()})},e.setOpen=function(r,o){o===void 0&&(o=!1),e.setState({open:r,preSelection:r&&e.state.open?e.state.preSelection:e.calcInitialState().preSelection,lastPreSelectChange:Pt},function(){r||e.setState(function(i){return{focused:o?i.focused:!1}},function(){!o&&e.deferBlur(),e.setState({inputValue:null})})})},e.inputOk=function(){return he(e.state.preSelection)},e.isCalendarOpen=function(){return e.props.open===void 0?e.state.open&&!e.props.disabled&&!e.props.readOnly:e.props.open},e.handleFocus=function(r){var o,i,s=e.state.wasHidden,c=s?e.state.open:!0;s&&e.resetHiddenStatus(),e.state.preventFocus||((i=(o=e.props).onFocus)===null||i===void 0||i.call(o,r),c&&!e.props.preventOpenOnFocus&&!e.props.readOnly&&e.setOpen(!0)),e.setState({focused:!0})},e.sendFocusBackToInput=function(){e.preventFocusTimeout&&e.clearPreventFocusTimeout(),e.setState({preventFocus:!0},function(){e.preventFocusTimeout=setTimeout(function(){e.setFocus(),e.setState({preventFocus:!1})})})},e.cancelFocusInput=function(){clearTimeout(e.inputFocusTimeout),e.inputFocusTimeout=void 0},e.deferFocusInput=function(){e.cancelFocusInput(),e.inputFocusTimeout=setTimeout(function(){return e.setFocus()},1)},e.handleDropdownFocus=function(){e.cancelFocusInput()},e.handleBlur=function(r){var o,i;(!e.state.open||e.props.withPortal||e.props.showTimeInput)&&((i=(o=e.props).onBlur)===null||i===void 0||i.call(o,r)),e.state.open&&e.props.open===!1&&e.setOpen(!1),e.setState({focused:!1})},e.handleCalendarClickOutside=function(r){var o,i;e.props.inline||e.setOpen(!1),(i=(o=e.props).onClickOutside)===null||i===void 0||i.call(o,r),e.props.withPortal&&r.preventDefault()},e.handleChange=function(){for(var r,o,i,s,c,u=[],l=0;l<arguments.length;l++)u[l]=arguments[l];var d=u[0];if(!(e.props.onChangeRaw&&(e.props.onChangeRaw.apply(e,u),!d||typeof d.isDefaultPrevented!="function"||d.isDefaultPrevented()))){e.setState({inputValue:(d==null?void 0:d.target)instanceof HTMLInputElement?d.target.value:null,lastPreSelectChange:Wi});var p=e.props,h=p.selectsRange,m=p.startDate,g=p.endDate,v=(r=e.props.dateFormat)!==null&&r!==void 0?r:n.defaultProps.dateFormat,k=(o=e.props.strictParsing)!==null&&o!==void 0?o:n.defaultProps.strictParsing,x=(d==null?void 0:d.target)instanceof HTMLInputElement?d.target.value:"";if(h){var M=x.split("-",2).map(function(j){return j.trim()}),T=M[0],B=M[1],A=Mt(T??"",v,e.props.locale,k),N=Mt(B??"",v,e.props.locale,k),_=(m==null?void 0:m.getTime())!==(A==null?void 0:A.getTime()),C=(g==null?void 0:g.getTime())!==(N==null?void 0:N.getTime());if(!_&&!C||A&&ne(A,e.props)||N&&ne(N,e.props))return;(s=(i=e.props).onChange)===null||s===void 0||s.call(i,[A,N],d)}else{var I=Mt(x,v,e.props.locale,k,(c=e.props.selected)!==null&&c!==void 0?c:void 0);(I||!x)&&e.setSelected(I,d,!0)}}},e.handleSelect=function(r,o,i){if(e.props.shouldCloseOnSelect&&!e.props.showTimeSelect&&e.sendFocusBackToInput(),e.props.onChangeRaw&&e.props.onChangeRaw(o),e.setSelected(r,o,!1,i),e.props.showDateSelect&&e.setState({isRenderAriaLiveMessage:!0}),!e.props.shouldCloseOnSelect||e.props.showTimeSelect)e.setPreSelection(r);else if(!e.props.inline){e.props.selectsRange||e.setOpen(!1);var s=e.props,c=s.startDate,u=s.endDate;c&&!u&&(e.props.swapRange||!vr(r,c))&&e.setOpen(!1)}},e.setSelected=function(r,o,i,s){var c,u,l=r;if(e.props.showYearPicker){if(l!==null&&st(S(l),e.props))return}else if(e.props.showMonthYearPicker){if(l!==null&&Qr(l,e.props))return}else if(l!==null&&ne(l,e.props))return;var d=e.props,p=d.onChange,h=d.selectsRange,m=d.startDate,g=d.endDate,v=d.selectsMultiple,k=d.selectedDates,x=d.minTime,M=d.swapRange;if(!xe(e.props.selected,l)||e.props.allowSameDay||h||v)if(l!==null&&(e.props.selected&&(!i||!e.props.showTimeSelect&&!e.props.showTimeSelectOnly&&!e.props.showTimeInput)&&(l=Ct(l,{hour:me(e.props.selected),minute:ve(e.props.selected),second:ke(e.props.selected)})),!i&&(e.props.showTimeSelect||e.props.showTimeSelectOnly)&&x&&(l=Ct(l,{hour:x.getHours(),minute:x.getMinutes(),second:x.getSeconds()})),e.props.inline||e.setState({preSelection:l}),e.props.focusSelectedMonth||e.setState({monthSelectedIn:s})),h){var T=!m&&!g,B=m&&!g,A=m&&g;T?p==null||p([l,null],o):B&&(l===null?p==null||p([null,null],o):vr(l,m)?M?p==null||p([l,m],o):p==null||p([l,null],o):p==null||p([m,l],o)),A&&(p==null||p([l,null],o))}else if(v){if(l!==null)if(!(k!=null&&k.length))p==null||p([l],o);else{var N=k.some(function(C){return P(C,l)});if(N){var _=k.filter(function(C){return!P(C,l)});p==null||p(_,o)}else p==null||p(pe(pe([],k,!0),[l],!1),o)}}else p==null||p(l,o);i||((u=(c=e.props).onSelect)===null||u===void 0||u.call(c,l,o),e.setState({inputValue:null}))},e.setPreSelection=function(r){var o=he(e.props.minDate),i=he(e.props.maxDate),s=!0;if(r){var c=it(r);if(o&&i)s=Ue(r,e.props.minDate,e.props.maxDate);else if(o){var u=it(e.props.minDate);s=Me(r,u)||xe(c,u)}else if(i){var l=ar(e.props.maxDate);s=Te(r,l)||xe(c,l)}}s&&e.setState({preSelection:r})},e.toggleCalendar=function(){e.setOpen(!e.state.open)},e.handleTimeChange=function(r){var o,i;if(!(e.props.selectsRange||e.props.selectsMultiple)){var s=e.props.selected?e.props.selected:e.getPreSelection(),c=e.props.selected?r:Ct(s,{hour:me(r),minute:ve(r)});e.setState({preSelection:c}),(i=(o=e.props).onChange)===null||i===void 0||i.call(o,c),e.props.shouldCloseOnSelect&&!e.props.showTimeInput&&(e.sendFocusBackToInput(),e.setOpen(!1)),e.props.showTimeInput&&e.setOpen(!0),(e.props.showTimeSelectOnly||e.props.showTimeSelect)&&e.setState({isRenderAriaLiveMessage:!0}),e.setState({inputValue:null})}},e.onInputClick=function(){var r,o;!e.props.disabled&&!e.props.readOnly&&e.setOpen(!0),(o=(r=e.props).onInputClick)===null||o===void 0||o.call(r)},e.onInputKeyDown=function(r){var o,i,s,c,u,l;(i=(o=e.props).onKeyDown)===null||i===void 0||i.call(o,r);var d=r.key;if(!e.state.open&&!e.props.inline&&!e.props.preventOpenOnFocus){(d===D.ArrowDown||d===D.ArrowUp||d===D.Enter)&&((s=e.onInputClick)===null||s===void 0||s.call(e));return}if(e.state.open){if(d===D.ArrowDown||d===D.ArrowUp){r.preventDefault();var p=e.props.showTimeSelectOnly?".react-datepicker__time-list-item[tabindex='0']":e.props.showWeekPicker&&e.props.showWeekNumbers?'.react-datepicker__week-number[tabindex="0"]':e.props.showFullMonthYearPicker||e.props.showMonthYearPicker?'.react-datepicker__month-text[tabindex="0"]':'.react-datepicker__day[tabindex="0"]',h=((c=e.calendar)===null||c===void 0?void 0:c.containerRef.current)instanceof Element&&e.calendar.containerRef.current.querySelector(p);h instanceof HTMLElement&&h.focus({preventScroll:!0});return}var m=Y(e.state.preSelection);d===D.Enter?(r.preventDefault(),r.target.blur(),e.inputOk()&&e.state.lastPreSelectChange===Pt?(e.handleSelect(m,r),!e.props.shouldCloseOnSelect&&e.setPreSelection(m)):e.setOpen(!1)):d===D.Escape?(r.preventDefault(),r.target.blur(),e.sendFocusBackToInput(),e.setOpen(!1)):d===D.Tab&&e.setOpen(!1),e.inputOk()||(l=(u=e.props).onInputError)===null||l===void 0||l.call(u,{code:1,msg:St})}},e.onPortalKeyDown=function(r){var o=r.key;o===D.Escape&&(r.preventDefault(),e.setState({preventFocus:!0},function(){e.setOpen(!1),setTimeout(function(){e.setFocus(),e.setState({preventFocus:!1})})}))},e.onDayKeyDown=function(r){var o,i,s,c,u,l,d=e.props,p=d.minDate,h=d.maxDate,m=d.disabledKeyboardNavigation,g=d.showWeekPicker,v=d.shouldCloseOnSelect,k=d.locale,x=d.calendarStartDay,M=d.adjustDateOnChange,T=d.inline;if((i=(o=e.props).onKeyDown)===null||i===void 0||i.call(o,r),!m){var B=r.key,A=r.shiftKey,N=Y(e.state.preSelection),_=function(X,F){var H=F;switch(X){case D.ArrowRight:H=g?lt(F,1):oe(F,1);break;case D.ArrowLeft:H=g?Jt(F):uo(F);break;case D.ArrowUp:H=Jt(F);break;case D.ArrowDown:H=lt(F,1);break;case D.PageUp:H=A?Fe(F,1):Pe(F,1);break;case D.PageDown:H=A?fe(F,1):ie(F,1);break;case D.Home:H=_e(F,k,x);break;case D.End:H=$o(F);break}return H},C=function(X,F){for(var H=40,ee=X,Ae=!1,Bt=0,ae=_(X,F);!Ae;){if(Bt>=H){ae=F;break}p&&ae<p&&(ee=D.ArrowRight,ae=ne(p,e.props)?_(ee,ae):p),h&&ae>h&&(ee=D.ArrowLeft,ae=ne(h,e.props)?_(ee,ae):h),ne(ae,e.props)?((ee===D.PageUp||ee===D.Home)&&(ee=D.ArrowRight),(ee===D.PageDown||ee===D.End)&&(ee=D.ArrowLeft),ae=_(ee,ae)):Ae=!0,Bt++}return ae};if(B===D.Enter){r.preventDefault(),e.handleSelect(N,r),!v&&e.setPreSelection(N);return}else if(B===D.Escape){r.preventDefault(),e.setOpen(!1),e.inputOk()||(c=(s=e.props).onInputError)===null||c===void 0||c.call(s,{code:1,msg:St});return}var I=null;switch(B){case D.ArrowLeft:case D.ArrowRight:case D.ArrowUp:case D.ArrowDown:case D.PageUp:case D.PageDown:case D.Home:case D.End:I=C(B,N);break}if(!I){(l=(u=e.props).onInputError)===null||l===void 0||l.call(u,{code:1,msg:St});return}if(r.preventDefault(),e.setState({lastPreSelectChange:Pt}),M&&e.setSelected(I),e.setPreSelection(I),T){var j=J(N),K=J(I),z=S(N),se=S(I);j!==K||z!==se?e.setState({shouldFocusDayInline:!0}):e.setState({shouldFocusDayInline:!1})}}},e.onPopperKeyDown=function(r){var o=r.key;o===D.Escape&&(r.preventDefault(),e.sendFocusBackToInput())},e.onClearClick=function(r){r&&r.preventDefault&&r.preventDefault(),e.sendFocusBackToInput();var o=e.props,i=o.selectsRange,s=o.onChange;i?s==null||s([null,null],r):s==null||s(null,r),e.setState({inputValue:null})},e.clear=function(){e.onClearClick()},e.onScroll=function(r){typeof e.props.closeOnScroll=="boolean"&&e.props.closeOnScroll?(r.target===document||r.target===document.documentElement||r.target===document.body)&&e.setOpen(!1):typeof e.props.closeOnScroll=="function"&&e.props.closeOnScroll(r)&&e.setOpen(!1)},e.renderCalendar=function(){var r,o;return!e.props.inline&&!e.isCalendarOpen()?null:f.createElement(Ti,E({showMonthYearDropdown:void 0,ref:function(i){e.calendar=i}},e.props,e.state,{setOpen:e.setOpen,dateFormat:(r=e.props.dateFormatCalendar)!==null&&r!==void 0?r:n.defaultProps.dateFormatCalendar,onSelect:e.handleSelect,onClickOutside:e.handleCalendarClickOutside,holidays:ci(e.modifyHolidays()),outsideClickIgnoreClass:wr,onDropdownFocus:e.handleDropdownFocus,onTimeChange:e.handleTimeChange,className:e.props.calendarClassName,container:e.props.calendarContainer,handleOnKeyDown:e.props.onKeyDown,handleOnDayKeyDown:e.onDayKeyDown,setPreSelection:e.setPreSelection,dropdownMode:(o=e.props.dropdownMode)!==null&&o!==void 0?o:n.defaultProps.dropdownMode}),e.props.children)},e.renderAriaLiveRegion=function(){var r=e.props,o=r.dateFormat,i=o===void 0?n.defaultProps.dateFormat:o,s=r.locale,c=e.props.showTimeInput||e.props.showTimeSelect,u=c?"PPPPp":"PPPP",l;return e.props.selectsRange?l="Selected start date: ".concat(te(e.props.startDate,{dateFormat:u,locale:s}),". ").concat(e.props.endDate?"End date: "+te(e.props.endDate,{dateFormat:u,locale:s}):""):e.props.showTimeSelectOnly?l="Selected time: ".concat(te(e.props.selected,{dateFormat:i,locale:s})):e.props.showYearPicker?l="Selected year: ".concat(te(e.props.selected,{dateFormat:"yyyy",locale:s})):e.props.showMonthYearPicker?l="Selected month: ".concat(te(e.props.selected,{dateFormat:"MMMM yyyy",locale:s})):e.props.showQuarterYearPicker?l="Selected quarter: ".concat(te(e.props.selected,{dateFormat:"yyyy, QQQ",locale:s})):l="Selected date: ".concat(te(e.props.selected,{dateFormat:u,locale:s})),f.createElement("span",{role:"alert","aria-live":"polite",className:"react-datepicker__aria-live"},l)},e.renderDateInput=function(){var r,o,i,s=$(e.props.className,(r={},r[wr]=e.state.open,r)),c=e.props.customInput||f.createElement("input",{type:"text"}),u=e.props.customInputRef||"ref",l=e.props,d=l.dateFormat,p=d===void 0?n.defaultProps.dateFormat:d,h=l.locale,m=typeof e.props.value=="string"?e.props.value:typeof e.state.inputValue=="string"?e.state.inputValue:e.props.selectsRange?Uo(e.props.startDate,e.props.endDate,{dateFormat:p,locale:h}):e.props.selectsMultiple?jo((i=e.props.selectedDates)!==null&&i!==void 0?i:[],{dateFormat:p,locale:h}):te(e.props.selected,{dateFormat:p,locale:h});return y.cloneElement(c,(o={},o[u]=function(g){e.input=g},o.value=m,o.onBlur=e.handleBlur,o.onChange=e.handleChange,o.onClick=e.onInputClick,o.onFocus=e.handleFocus,o.onKeyDown=e.onInputKeyDown,o.id=e.props.id,o.name=e.props.name,o.form=e.props.form,o.autoFocus=e.props.autoFocus,o.placeholder=e.props.placeholderText,o.disabled=e.props.disabled,o.autoComplete=e.props.autoComplete,o.className=$(c.props.className,s),o.title=e.props.title,o.readOnly=e.props.readOnly,o.required=e.props.required,o.tabIndex=e.props.tabIndex,o["aria-describedby"]=e.props.ariaDescribedBy,o["aria-invalid"]=e.props.ariaInvalid,o["aria-labelledby"]=e.props.ariaLabelledBy,o["aria-required"]=e.props.ariaRequired,o))},e.renderClearButton=function(){var r=e.props,o=r.isClearable,i=r.disabled,s=r.selected,c=r.startDate,u=r.endDate,l=r.clearButtonTitle,d=r.clearButtonClassName,p=d===void 0?"":d,h=r.ariaLabelClose,m=h===void 0?"Close":h,g=r.selectedDates;return o&&(s!=null||c!=null||u!=null||g!=null&&g.length)?f.createElement("button",{type:"button",className:$("react-datepicker__close-icon",p,{"react-datepicker__close-icon--disabled":i}),disabled:i,"aria-label":m,onClick:e.onClearClick,title:l,tabIndex:-1}):null},e.state=e.calcInitialState(),e.preventFocusTimeout=void 0,e}return Object.defineProperty(n,"defaultProps",{get:function(){return{allowSameDay:!1,dateFormat:"MM/dd/yyyy",dateFormatCalendar:"LLLL yyyy",disabled:!1,disabledKeyboardNavigation:!1,dropdownMode:"scroll",preventOpenOnFocus:!1,monthsShown:1,readOnly:!1,withPortal:!1,selectsDisabledDaysInRange:!1,shouldCloseOnSelect:!0,showTimeSelect:!1,showTimeInput:!1,showPreviousMonths:!1,showMonthYearPicker:!1,showFullMonthYearPicker:!1,showTwoColumnMonthYearPicker:!1,showFourColumnMonthYearPicker:!1,showYearPicker:!1,showQuarterYearPicker:!1,showWeekPicker:!1,strictParsing:!1,swapRange:!1,timeIntervals:30,timeCaption:"Time",previousMonthAriaLabel:"Previous Month",previousMonthButtonLabel:"Previous Month",nextMonthAriaLabel:"Next Month",nextMonthButtonLabel:"Next Month",previousYearAriaLabel:"Previous Year",previousYearButtonLabel:"Previous Year",nextYearAriaLabel:"Next Year",nextYearButtonLabel:"Next Year",timeInputLabel:"Time",enableTabLoop:!0,yearItemNumber:Xe,focusSelectedMonth:!1,showPopperArrow:!0,excludeScrollbar:!0,customTimeInput:null,calendarStartDay:void 0,toggleCalendarOnIconClick:!1,usePointerEvent:!1}},enumerable:!1,configurable:!0}),n.prototype.componentDidMount=function(){window.addEventListener("scroll",this.onScroll,!0),document.addEventListener("visibilitychange",this.setHiddenStateOnVisibilityHidden)},n.prototype.componentDidUpdate=function(t,e){var r,o,i,s;t.inline&&Fi(t.selected,this.props.selected)&&this.setPreSelection(this.props.selected),this.state.monthSelectedIn!==void 0&&t.monthsShown!==this.props.monthsShown&&this.setState({monthSelectedIn:0}),t.highlightDates!==this.props.highlightDates&&this.setState({highlightDates:pr(this.props.highlightDates)}),!e.focused&&!xe(t.selected,this.props.selected)&&this.setState({inputValue:null}),e.open!==this.state.open&&(e.open===!1&&this.state.open===!0&&((o=(r=this.props).onCalendarOpen)===null||o===void 0||o.call(r)),e.open===!0&&this.state.open===!1&&((s=(i=this.props).onCalendarClose)===null||s===void 0||s.call(i)))},n.prototype.componentWillUnmount=function(){this.clearPreventFocusTimeout(),window.removeEventListener("scroll",this.onScroll,!0),document.removeEventListener("visibilitychange",this.setHiddenStateOnVisibilityHidden)},n.prototype.renderInputContainer=function(){var t=this.props,e=t.showIcon,r=t.icon,o=t.calendarIconClassname,i=t.calendarIconClassName,s=t.toggleCalendarOnIconClick,c=this.state.open;return o&&console.warn("calendarIconClassname props is deprecated. should use calendarIconClassName props."),f.createElement("div",{className:"react-datepicker__input-container".concat(e?" react-datepicker__view-calendar-icon":"")},e&&f.createElement(Oi,E({icon:r,className:$(i,!i&&o,c&&"react-datepicker-ignore-onclickoutside")},s?{onClick:this.toggleCalendar}:null)),this.state.isRenderAriaLiveMessage&&this.renderAriaLiveRegion(),this.renderDateInput(),this.renderClearButton())},n.prototype.render=function(){var t=this.renderCalendar();if(this.props.inline)return t;if(this.props.withPortal){var e=this.state.open?f.createElement(Ur,{enableTabLoop:this.props.enableTabLoop},f.createElement("div",{className:"react-datepicker__portal",tabIndex:-1,onKeyDown:this.onPortalKeyDown},t)):null;return this.state.open&&this.props.portalId&&(e=f.createElement(Vr,E({portalId:this.props.portalId},this.props),e)),f.createElement("div",null,this.renderInputContainer(),e)}return f.createElement(Li,E({},this.props,{className:this.props.popperClassName,hidePopper:!this.isCalendarOpen(),targetComponent:this.renderInputContainer(),popperComponent:t,popperOnKeyDown:this.onPopperKeyDown,showArrow:this.props.showPopperArrow}))},n}(y.Component),Wi="input",Pt="navigate";export{Qi as D};
