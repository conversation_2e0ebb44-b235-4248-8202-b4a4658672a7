import{d as g,r as a,j as e,A as w,a6 as c,P as m,B as p,L as j}from"./index-C6AV3cVN.js";import{u as b}from"./auth-DeOTzV2I.js";import{F as v}from"./FullLogo-CXg185jT.js";const N={background:"linear-gradient(45deg, rgb(238, 119, 82,0.2), rgb(231, 60, 126,0.2), rgb(35, 166, 213,0.2), rgb(35, 213, 171,0.2))",backgroundSize:"400% 400%",animation:"gradient 15s ease infinite",height:"100vh"},k=()=>{const u=g(),[t,x]=a.useState(""),[i,f]=a.useState(""),[d,o]=a.useState(null),[n,l]=a.useState(!1),h=async s=>{if(s.preventDefault(),o(null),l(!0),t!==i){o("Passwords do not match"),l(!1);return}try{const{error:r}=await b(t);if(r)throw r;u("/auth/login")}catch(r){o(r.message||"An error occurred while resetting your password")}finally{l(!1)}};return e.jsx("div",{style:N,className:"relative overflow-hidden h-screen",children:e.jsx("div",{className:"flex h-full justify-center items-center px-4",children:e.jsx("div",{className:"rounded-xl shadow-md bg-white dark:bg-darkgray p-6 w-full md:w-96 border-none",children:e.jsxs("div",{className:"flex flex-col gap-2 p-0 w-full",children:[e.jsx("div",{className:"mx-auto",children:e.jsx(v,{})}),e.jsx("p",{className:"text-sm text-center text-dark my-3",children:"Reset Your Password"}),d&&e.jsx(w,{color:"failure",className:"mb-4",children:d}),e.jsxs("form",{onSubmit:h,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"password",value:"New Password"})}),e.jsx(m,{id:"password",type:"password",sizing:"md",required:!0,value:t,onChange:s=>x(s.target.value),className:"form-control form-rounded-xl"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"confirmPassword",value:"Confirm Password"})}),e.jsx(m,{id:"confirmPassword",type:"password",sizing:"md",required:!0,value:i,onChange:s=>f(s.target.value),className:"form-control form-rounded-xl"})]}),e.jsx(p,{type:"submit",color:"primary",className:"w-full bg-primary text-white rounded-xl",disabled:n,children:n?"Resetting...":"Reset Password"})]}),e.jsxs("div",{className:"flex gap-2 text-base text-ld font-medium mt-6 items-center justify-center",children:[e.jsx("p",{children:"Remember your password?"}),e.jsx(j,{to:"/auth/login",className:"text-primary text-sm font-medium",children:"Sign in"})]})]})})})})};export{k as default};
