import{s as o,r as s,j as e,A as I,i as S,a6 as O,P as y,B as D}from"./index-C6AV3cVN.js";import{C as T}from"./Card-yj7fueH8.js";const L=async(c,n)=>{try{console.log("Checking invitation with token:",c),n&&console.log("Invitation ID:",n);const{data:r,error:g}=await o.from("invitations").select("id, token, email, expires_at, accepted_at").limit(10);console.log("All recent invitations:",r),g&&console.error("Error fetching all invitations:",g);let i=o.from("invitations").select("*");n?i=i.eq("id",n):i=i.eq("token",c);const{data:a,error:d}=await i.single();return d?(console.error("Error finding invitation:",d),{exists:!1,error:d.message}):{exists:!0,data:a}}catch(r){return console.error("Error in checkInvitation:",r),{exists:!1,error:r.message}}},F=async c=>{try{const{data:n}=await o.auth.getUser();if(!n.user)return{success:!1,error:"User not authenticated"};const{data:r,error:g}=await o.from("organization_members").select("organization_id, role").eq("user_id",n.user.id);if(console.log("User organizations:",r),console.log("Organization query error:",g),g)return{success:!1,error:`Error fetching organizations: ${g.message}`};if(!r||r.length===0){const{data:v}=await o.from("organizations").select("id, name").limit(5);return console.log("Available organizations:",v),{success:!1,error:"User is not a member of any organization"}}const i=r[0].organization_id,{data:a,error:d}=await o.from("invitations").select("id, token, expires_at, accepted_at").eq("organization_id",i).eq("email",c).is("accepted_at",null).single();if(a)if(console.log("Found existing invitation:",a),new Date(a.expires_at)<new Date)console.log("Existing invitation has expired, deleting it"),await o.from("invitations").delete().eq("id",a.id);else{console.log("Using existing invitation");const h=`${window.location.origin}/debug/accept-invitation?token=${encodeURIComponent(a.token)}&id=${a.id}`;return console.log("Existing invitation URL:",h),{success:!0,invitationUrl:h,error:"Using existing invitation. Email sending is not configured. Please share the invitation link manually."}}const m=Math.random().toString(36).substring(2,15),b=new Date;b.setHours(b.getHours()+48);const{data:p,error:j}=await o.from("invitations").insert({organization_id:i,email:c,role:"member",invited_by:n.user.id,token:m,expires_at:b.toISOString()}).select().single();if(j)return console.error("Error creating test invitation:",j),{success:!1,error:j.message};const l=window.location.origin,z=encodeURIComponent(m);console.log("Original token:",m),console.log("Encoded token:",z);const u=`${l}/auth/accept-invitation?token=${z}&id=${p.id}`;return console.log("Created test invitation:",p),console.log("Test invitation URL:",u),{success:!0,invitationUrl:u}}catch(n){return console.error("Error in createTestInvitation:",n),{success:!1,error:n.message}}},B=()=>{const[c,n]=s.useState(""),[r,g]=s.useState(""),[i,a]=s.useState(""),[d,m]=s.useState(!1),[b,p]=s.useState(null),[j,l]=s.useState(null),[z,u]=s.useState(null),[v,N]=s.useState(null),[h,x]=s.useState("checking"),[$,f]=s.useState(null);s.useEffect(()=>{(async()=>{try{x("checking");const{data:k}=await o.auth.getUser();if(!k.user){x("error"),f("User not authenticated");return}const{data:w,error:U}=await o.from("organization_members").select("organization_id, role").eq("user_id",k.user.id);if(U){console.error("Error checking organization membership:",U),x("error"),f(`Error checking organization: ${U.message}`);return}if(!w||w.length===0){console.log("User is not a member of any organization, creating one...");const{data:C,error:E}=await o.from("organizations").insert({name:"Debug Organization",created_by:k.user.id}).select().single();if(E||!C){console.error("Error creating organization:",E),x("error"),f(`Error creating organization: ${E==null?void 0:E.message}`);return}const{error:_}=await o.from("organization_members").insert({organization_id:C.id,user_id:k.user.id,role:"owner"});if(_){console.error("Error adding user to organization:",_),x("error"),f(`Error adding user to organization: ${_.message}`);return}x("ok"),f(`Created new organization: ${C.name}`)}else x("ok"),f(`User is a member of ${w.length} organization(s)`)}catch(k){console.error("Error in checkOrganization:",k),x("error"),f(`Unexpected error: ${k.message}`)}})()},[]);const A=async()=>{if(!c){l("Please enter a token");return}m(!0),l(null),u(null),p(null);try{const t=await L(c,r||void 0);p(t),t.exists?u("Invitation found!"):l(t.error||"Invitation not found")}catch(t){console.error("Error checking invitation:",t),l(t.message||"An error occurred")}finally{m(!1)}},R=async()=>{if(!i){l("Please enter an email");return}m(!0),l(null),u(null),p(null),N(null);try{const t=await F(i);t.success?(u("Test invitation created!"),N(t.invitationUrl||null)):l(t.error||"Failed to create test invitation")}catch(t){console.error("Error creating test invitation:",t),l(t.message||"An error occurred")}finally{m(!1)}},q=()=>{v&&(navigator.clipboard.writeText(v),u("Invitation link copied to clipboard"))};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Invitation System Debug"}),h==="checking"&&e.jsxs(I,{color:"info",className:"mb-6",children:[e.jsx(S,{size:"sm",className:"mr-2"}),"Checking organization status..."]}),h==="error"&&e.jsxs(I,{color:"failure",className:"mb-6",children:[e.jsx("p",{className:"font-medium",children:"Organization Error"}),e.jsx("p",{className:"text-sm",children:$})]}),h==="ok"&&e.jsxs(I,{color:"success",className:"mb-6",children:[e.jsx("p",{className:"font-medium",children:"Organization Status"}),e.jsx("p",{className:"text-sm",children:$})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs(T,{children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Check Invitation"}),j&&e.jsx(I,{color:"failure",className:"mb-4",children:j}),z&&e.jsx(I,{color:"success",className:"mb-4",children:z}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(O,{htmlFor:"token",value:"Invitation Token"})}),e.jsx(y,{id:"token",type:"text",placeholder:"Enter invitation token",value:c,onChange:t=>n(t.target.value)})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(O,{htmlFor:"invitationId",value:"Invitation ID (optional)"})}),e.jsx(y,{id:"invitationId",type:"text",placeholder:"Enter invitation ID",value:r,onChange:t=>g(t.target.value)})]}),e.jsxs(D,{onClick:A,disabled:d||h!=="ok",children:[d?e.jsx(S,{size:"sm",className:"mr-2"}):null,"Check Invitation"]}),b&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h3",{className:"font-bold mb-2",children:"Result:"}),e.jsx("pre",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-auto max-h-60",children:JSON.stringify(b,null,2)})]})]}),e.jsxs(T,{children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Create Test Invitation"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(O,{htmlFor:"email",value:"Email Address"})}),e.jsx(y,{id:"email",type:"email",placeholder:"Enter email address",value:i,onChange:t=>a(t.target.value)})]}),e.jsxs(D,{onClick:R,disabled:d||h!=="ok",children:[d?e.jsx(S,{size:"sm",className:"mr-2"}):null,"Create Test Invitation"]}),v&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h3",{className:"font-bold mb-2",children:"Invitation URL:"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(y,{id:"invitationLink",type:"text",value:v,readOnly:!0,className:"flex-1"}),e.jsx(D,{color:"light",onClick:q,children:"Copy"})]})]})]})]})]})};export{B as default};
