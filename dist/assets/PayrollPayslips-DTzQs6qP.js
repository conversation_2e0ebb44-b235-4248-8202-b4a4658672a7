import{ab as q,d as I,h as W,r as i,j as e,i as O,A as P,G as w,B as o,o as C,a7 as J,n as K,_ as l,a1 as Q,b0 as B,q as L,M as g}from"./index-C6AV3cVN.js";import{C as V}from"./Card-yj7fueH8.js";import{a as X}from"./payroll-DcVgVc3z.js";import{g as Y}from"./payslipGeneration-21BXX9ZZ.js";import{c as h}from"./payroll-j3fcCwK0.js";import{P as Z}from"./PageTitle-FHPo8gWi.js";import{f as u}from"./formatters-Cypx7G-j.js";import"./index-qirzObrW.js";import"./index-4YOzgfrD.js";import"./typeof-QjJsDpFa.js";import"./index-Cn2wB4rc.js";import"./index-DT2YvziZ.js";const xe=()=>{var D,T;const{id:m}=q(),j=I(),{currentOrganization:t}=W(),[c,A]=i.useState(null),[n,z]=i.useState(null),[M,v]=i.useState(!0),[b,x]=i.useState(null),[R,_]=i.useState(!1),[y,U]=i.useState(""),[$,E]=i.useState(!1),[p,f]=i.useState(null),[H,G]=i.useState(h.DEFAULT),F=async()=>{if(!(!t||!m)){v(!0),x(null);try{const{period:s,error:a}=await X(t.id,m,!0);a?x(a):s?A(s):x("Payroll period not found")}catch(s){x(s.message)}finally{v(!1)}}};i.useEffect(()=>{F()},[t,m]);const N=async s=>{if(t){z(s),E(!0),f(null),_(!0);try{const{html:a,error:r}=await Y(t.id,s.id,H);r?f(r):a&&U(a)}catch(a){f(a.message)}finally{E(!1)}}},k=()=>{const s=window.open("","_blank");s&&(s.document.write(y),s.document.close(),s.focus(),s.print())},S=()=>{var d;const s=new Blob([y],{type:"text/html"}),a=URL.createObjectURL(s),r=document.createElement("a");r.href=a,r.download=`payslip-${(d=n==null?void 0:n.employee)==null?void 0:d.last_name}-${c==null?void 0:c.name.replace(/\s+/g,"-")}.html`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(a)};return M?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(O,{size:"xl"})}):b?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(P,{color:"failure",icon:w,children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",b]}),e.jsx("div",{className:"mt-4",children:e.jsxs(o,{color:"gray",onClick:()=>j("/payroll"),children:[e.jsx(C,{className:"mr-2 h-5 w-5"}),"Back to Payroll Periods"]})})]}):c?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs(o,{color:"gray",className:"mr-4",onClick:()=>j(`/payroll/periods/${m}`),children:[e.jsx(C,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(Z,{title:`Payslips: ${c.name}`})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("div",{className:"w-48",children:e.jsxs(J,{value:H,onChange:s=>G(s.target.value),children:[e.jsx("option",{value:h.DEFAULT,children:"Default Template"}),e.jsx("option",{value:h.COMPACT,children:"Compact Template"}),e.jsx("option",{value:h.DETAILED,children:"Detailed Template"})]})}),e.jsxs(o,{color:"primary",children:[e.jsx(K,{className:"mr-2 h-5 w-5"}),"Email All Payslips"]})]})]}),e.jsx(V,{children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(l,{striped:!0,children:[e.jsxs(l.Head,{children:[e.jsx(l.HeadCell,{children:"Employee"}),e.jsx(l.HeadCell,{children:"Employee ID"}),e.jsx(l.HeadCell,{children:"Basic Pay"}),e.jsx(l.HeadCell,{children:"Gross Pay"}),e.jsx(l.HeadCell,{children:"Deductions"}),e.jsx(l.HeadCell,{children:"Net Pay"}),e.jsx(l.HeadCell,{children:"Actions"})]}),e.jsx(l.Body,{children:c.payroll_items&&c.payroll_items.length>0?c.payroll_items.map(s=>{var a,r,d;return e.jsxs(l.Row,{children:[e.jsx(l.Cell,{className:"font-medium",children:s.employee?e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2",children:s.employee.profile_image_url?e.jsx("img",{src:s.employee.profile_image_url,alt:`${s.employee.first_name} ${s.employee.last_name}`,className:"w-8 h-8 rounded-full object-cover"}):e.jsxs("span",{className:"text-xs font-semibold",children:[(a=s.employee.first_name)==null?void 0:a[0],(r=s.employee.last_name)==null?void 0:r[0]]})}),e.jsx("div",{children:e.jsxs("p",{className:"font-medium",children:[s.employee.first_name," ",s.employee.last_name]})})]}):"Unknown Employee"}),e.jsx(l.Cell,{children:((d=s.employee)==null?void 0:d.employee_number)||"N/A"}),e.jsx(l.Cell,{children:u(Number(s.basic_pay))}),e.jsx(l.Cell,{children:u(Number(s.gross_pay))}),e.jsx(l.Cell,{children:u(Number(s.total_deductions))}),e.jsx(l.Cell,{className:"font-medium text-green-600",children:u(Number(s.net_pay))}),e.jsx(l.Cell,{children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(o,{size:"xs",color:"info",onClick:()=>N(s),children:e.jsx(Q,{className:"h-4 w-4"})}),e.jsx(o,{size:"xs",color:"success",onClick:()=>{N(s).then(()=>{p||k()})},children:e.jsx(B,{className:"h-4 w-4"})}),e.jsx(o,{size:"xs",color:"light",onClick:()=>{N(s).then(()=>{p||S()})},children:e.jsx(L,{className:"h-4 w-4"})})]})})]},s.id)}):e.jsx(l.Row,{children:e.jsx(l.Cell,{colSpan:7,className:"text-center py-4",children:"No payroll items found for this period."})})})]})})}),e.jsxs(g,{show:R,onClose:()=>_(!1),size:"xl",children:[e.jsxs(g.Header,{children:["Payslip: ",(D=n==null?void 0:n.employee)==null?void 0:D.first_name," ",(T=n==null?void 0:n.employee)==null?void 0:T.last_name]}),e.jsx(g.Body,{children:$?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(O,{size:"xl"})}):p?e.jsxs(P,{color:"failure",icon:w,children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",p]}):e.jsxs("div",{className:"flex flex-col space-y-4",children:[e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsxs(o,{color:"light",onClick:S,children:[e.jsx(L,{className:"mr-2 h-5 w-5"}),"Download"]}),e.jsxs(o,{color:"primary",onClick:k,children:[e.jsx(B,{className:"mr-2 h-5 w-5"}),"Print"]})]}),e.jsx("div",{className:"border rounded-lg p-4 bg-white",children:e.jsx("iframe",{srcDoc:y,title:"Payslip Preview",className:"w-full h-[600px]",frameBorder:"0"})})]})})]})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(P,{color:"failure",icon:w,children:[e.jsx("span",{className:"font-medium",children:"Error!"})," Payroll period not found"]}),e.jsx("div",{className:"mt-4",children:e.jsxs(o,{color:"gray",onClick:()=>j("/payroll"),children:[e.jsx(C,{className:"mr-2 h-5 w-5"}),"Back to Payroll Periods"]})})]})};export{xe as default};
