import{s as a}from"./index-C6AV3cVN.js";const m=async(t,e)=>{try{let r=a.from("customers").select("*",{count:"exact"}).eq("organization_id",t);e!=null&&e.searchQuery&&(r=r.or(`name.ilike.%${e.searchQuery}%,email.ilike.%${e.searchQuery}%,phone.ilike.%${e.searchQuery}%`)),e!=null&&e.sortBy?r=r.order(e.sortBy,{ascending:e.sortOrder==="asc"}):r=r.order("name",{ascending:!0}),e!=null&&e.limit&&(r=r.limit(e.limit)),e!=null&&e.offset&&(r=r.range(e.offset,e.offset+(e.limit||10)-1));const{data:s,error:c,count:o}=await r;return c?(console.error("Error fetching customers:",c),{customers:[],count:0,error:c.message}):{customers:s,count:o||0}}catch(r){return console.error("Error in getCustomers:",r),{customers:[],count:0,error:r.message}}},n=async(t,e)=>{try{const{data:r,error:s}=await a.from("customers").select("*").eq("organization_id",t).eq("id",e).single();return s?(console.error("Error fetching customer:",s),{error:s.message}):{customer:r}}catch(r){return console.error("Error in getCustomerById:",r),{error:r.message}}},i=async(t,e)=>{try{const{data:r,error:s}=await a.from("customers").insert({...e,organization_id:t}).select().single();return s?(console.error("Error creating customer:",s),{error:s.message}):{customer:r}}catch(r){return console.error("Error in createCustomer:",r),{error:r.message}}},l=async(t,e,r)=>{try{const{data:s,error:c}=await a.from("customers").update(r).eq("organization_id",t).eq("id",e).select().single();return c?(console.error("Error updating customer:",c),{error:c.message}):{customer:s}}catch(s){return console.error("Error in updateCustomer:",s),{error:s.message}}},g=async(t,e)=>{try{const{error:r}=await a.from("customers").delete().eq("organization_id",t).eq("id",e);return r?(console.error("Error deleting customer:",r),{success:!1,error:r.message}):{success:!0}}catch(r){return console.error("Error in deleteCustomer:",r),{success:!1,error:r.message}}};export{n as a,i as c,g as d,m as g,l as u};
