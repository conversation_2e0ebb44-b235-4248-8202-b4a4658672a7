import{s as n}from"./index-C6AV3cVN.js";var f=(a=>(a.PURCHASE_RECEIPT="purchase_receipt",a.PAYROLL="payroll",a.UTILITY_BILL="utility_bill",a.GOVERNMENT_REMITTANCE="government_remittance",a.LOAN_REPAYMENT="loan_repayment",a.MANUAL_ENTRY="manual_entry",a))(f||{}),y=(a=>(a.DRAFT="draft",a.OPEN="open",a.PARTIALLY_PAID="partially_paid",a.PAID="paid",a.CANCELLED="cancelled",a))(y||{}),T=(a=>(a.CASH="cash",a.CHECK="check",a.BANK_TRANSFER="bank_transfer",a.GCASH="gcash",a.PAYMAYA="paymaya",a.CREDIT_CARD="credit_card",a.OTHER="other",a))(T||{});const w=async(a,r)=>{try{let e=n.from("payables").select(`
        *,
        supplier:suppliers(id, name, contact_person, email, phone),
        employee:employees(id, first_name, last_name, employee_number),
        payments:payable_payments(*)
      `).eq("organization_id",a).eq("approval_status","approved");if(r!=null&&r.filters){const{filters:s}=r;s.status&&(e=e.eq("status",s.status)),s.source_type&&(e=e.eq("source_type",s.source_type)),s.supplier_id&&(e=e.eq("supplier_id",s.supplier_id)),s.employee_id&&(e=e.eq("employee_id",s.employee_id)),s.due_date_from&&(e=e.gte("due_date",s.due_date_from)),s.due_date_to&&(e=e.lte("due_date",s.due_date_to)),s.amount_from&&(e=e.gte("amount",s.amount_from)),s.amount_to&&(e=e.lte("amount",s.amount_to)),s.search_query&&(e=e.or(`reference_number.ilike.%${s.search_query}%,notes.ilike.%${s.search_query}%`))}const t=(r==null?void 0:r.sortBy)||"created_at",o=(r==null?void 0:r.sortOrder)||"desc";e=e.order(t,{ascending:o==="asc"}),r!=null&&r.limit&&(e=e.limit(r.limit)),r!=null&&r.offset&&(e=e.range(r.offset,r.offset+(r.limit||50)-1));const{data:u,error:c,count:_}=await e;return c?(console.error("Error fetching payables:",c),{success:!1,error:c.message}):{success:!0,payables:(u||[]).map(s=>{var d;return{...s,total_paid:((d=s.payments)==null?void 0:d.reduce((b,m)=>b+Number(m.amount_paid),0))||0}}),total_count:_||0}}catch(e){return console.error("Error in getPayables:",e),{success:!1,error:e.message}}},$=async(a,r)=>{var e;try{const{data:t,error:o}=await n.from("payables").select(`
        *,
        supplier:suppliers(id, name, contact_person, email, phone, tax_id, address),
        employee:employees(id, first_name, last_name, employee_number),
        payments:payable_payments(*)
      `).eq("organization_id",a).eq("id",r).eq("approval_status","approved").single();return o?(console.error("Error fetching payable:",o),{success:!1,error:o.message}):{success:!0,payable:{...t,total_paid:((e=t.payments)==null?void 0:e.reduce((c,_)=>c+Number(_.amount_paid),0))||0}}}catch(t){return console.error("Error in getPayableById:",t),{success:!1,error:t.message}}},L=async(a,r,e)=>{try{const t=r.amount-(r.withholding_tax_amount||0),{data:o,error:u}=await n.from("payables").insert({organization_id:a,...r,balance:t,status:y.OPEN,created_by:e}).select().single();return u?(console.error("Error creating payable:",u),{success:!1,error:u.message}):{success:!0,payable:o}}catch(t){return console.error("Error in createPayable:",t),{success:!1,error:t.message}}},C=async(a,r)=>{try{const{data:e}=await n.from("payable_payments").select("id").eq("payable_id",r).limit(1);if(e&&e.length>0)return{success:!1,error:"Cannot delete payable with existing payments"};const{error:t}=await n.from("payables").delete().eq("organization_id",a).eq("id",r);return t?(console.error("Error deleting payable:",t),{success:!1,error:t.message}):{success:!0}}catch(e){return console.error("Error in deletePayable:",e),{success:!1,error:e.message}}},I=async(a,r,e)=>{try{const{data:t,error:o}=await n.from("payables").select("id, balance, status").eq("organization_id",a).eq("id",r.payable_id).single();if(o||!t)return{success:!1,error:"Payable not found"};if(t.status===y.PAID)return{success:!1,error:"Payable is already fully paid"};if(r.amount_paid>t.balance)return{success:!1,error:"Payment amount exceeds remaining balance"};const{data:u,error:c}=await n.from("payable_payments").insert({...r,created_by:e}).select().single();return c?(console.error("Error creating payment:",c),{success:!1,error:c.message}):{success:!0,payment:u}}catch(t){return console.error("Error in createPayment:",t),{success:!1,error:t.message}}},x=async(a,r)=>{try{const{data:e,error:t}=await n.from("payable_payments").select(`
        *,
        payable:payables!inner(organization_id)
      `).eq("payable.organization_id",a).eq("payable_id",r).order("payment_date",{ascending:!1});return t?(console.error("Error fetching payments:",t),{success:!1,error:t.message}):{success:!0,payments:e||[]}}catch(e){return console.error("Error in getPayablePayments:",e),{success:!1,error:e.message}}},O=async a=>{try{const{data:r,error:e}=await n.from("payables").select("status, source_type, amount, balance").eq("organization_id",a).eq("approval_status","approved");if(e)return console.error("Error fetching payables summary:",e),{success:!1,error:e.message};const t=r||[],o=new Date,{data:u}=await n.from("payables").select("amount, balance").eq("organization_id",a).eq("approval_status","approved").lt("due_date",o.toISOString().split("T")[0]).neq("status",y.PAID),c=u||[],_={total_payables:t.length,total_amount:t.reduce((i,s)=>i+Number(s.amount),0),total_paid:t.reduce((i,s)=>i+(Number(s.amount)-Number(s.balance)),0),total_outstanding:t.reduce((i,s)=>i+Number(s.balance),0),overdue_count:c.length,overdue_amount:c.reduce((i,s)=>i+Number(s.balance),0),by_status:{[y.DRAFT]:{count:0,amount:0},[y.OPEN]:{count:0,amount:0},[y.PARTIALLY_PAID]:{count:0,amount:0},[y.PAID]:{count:0,amount:0},[y.CANCELLED]:{count:0,amount:0}},by_source_type:{[f.PURCHASE_RECEIPT]:{count:0,amount:0},[f.PAYROLL]:{count:0,amount:0},[f.UTILITY_BILL]:{count:0,amount:0},[f.GOVERNMENT_REMITTANCE]:{count:0,amount:0},[f.LOAN_REPAYMENT]:{count:0,amount:0},[f.MANUAL_ENTRY]:{count:0,amount:0}}};return t.forEach(i=>{const s=i.status,d=i.source_type;_.by_status[s].count++,_.by_status[s].amount+=Number(i.amount),_.by_source_type[d].count++,_.by_source_type[d].amount+=Number(i.amount)}),{success:!0,summary:_}}catch(r){return console.error("Error in getPayablesSummary:",r),{success:!1,error:r.message}}},z=async(a,r)=>{try{const{data:e,error:t}=await n.rpc("resolve_payable_source_metadata",{payable_id:r});if(t)return console.error("Error fetching source metadata:",t),{success:!1,error:t.message};const o=e==null?void 0:e[0];return{success:!0,metadata:o?{source_type:o.source_type,source_id:o.source_id,source_name:o.source_name,source_description:o.source_description,source_date:o.source_date,source_amount:Number(o.source_amount),source_url:o.source_url}:void 0}}catch(e){return console.error("Error in getPayableSourceMetadata:",e),{success:!1,error:e.message}}},D=async(a,r)=>{try{const{data:e,error:t}=await n.from("payables").select("id, reference_number, status").eq("organization_id",a).eq("source_type","purchase_receipt").eq("source_id",r).maybeSingle();return t?{hasPendingPayable:!1,error:t.message}:{hasPendingPayable:!!e,payableId:e==null?void 0:e.id}}catch(e){return{hasPendingPayable:!1,error:e.message}}},S=async(a,r)=>{try{const{data:e,error:t}=await n.from("payables").select("source_id").eq("organization_id",a).eq("source_type","purchase_receipt").in("source_id",r);if(t)return{receiptsStatus:{},error:t.message};const o={};return r.forEach(u=>{o[u]=(e==null?void 0:e.some(c=>c.source_id===u))||!1}),{receiptsStatus:o}}catch(e){return{receiptsStatus:{},error:e.message}}},V=async(a,r,e)=>{var t;try{const{data:o,error:u}=await n.from("inventory_receipts").select(`
        id, receipt_number, status, receipt_date, purchase_order_id, organization_id,
        purchase_order:purchase_orders(
          id, order_number, supplier_id,
          supplier:suppliers(id, name, payment_terms_days)
        )
      `).eq("organization_id",a).eq("id",r).single();if(u||!o)return{success:!1,error:"Receipt not found"};if(o.status!=="completed")return{success:!1,error:"Receipt must be completed to create payable"};if(!o.purchase_order)return{success:!1,error:"Receipt must be linked to a purchase order"};if(!o.purchase_order.supplier)return{success:!1,error:"Purchase order must have a supplier"};const{data:c}=await n.from("payables").select("id, reference_number").eq("organization_id",a).eq("source_type","purchase_receipt").eq("source_id",r).maybeSingle();if(c)return{success:!1,error:`Payable already exists with reference: ${c.reference_number}`};const{data:_,error:i}=await n.from("inventory_receipt_items").select("*").eq("inventory_receipt_id",r);if(i||!_||_.length===0)return{success:!1,error:"Receipt has no items"};const s=_.reduce((p,l)=>{if(l.qc_status==="failed")return console.log(`Excluding failed QC item: quantity=${l.quantity}, cost=${l.unit_cost}, total excluded=${l.quantity*l.unit_cost}`),p;let g=l.quantity;l.damaged_quantity&&l.damaged_quantity>0&&(g=l.quantity-l.damaged_quantity,console.log(`Item has damage: original=${l.quantity}, damaged=${l.damaged_quantity}, payable=${g}`)),g=Math.max(0,g);const q=g*l.unit_cost;return console.log(`Including item: quantity=${g}, cost=${l.unit_cost}, total=${q}, qc_status=${l.qc_status||"not_set"}`),p+q},0);if(s<=0)return{success:!1,error:"Receipt total must be greater than zero"};console.log(`🔍 Fetching VAT settings for organization: ${a}`);const{data:d,error:b}=await n.from("organization_settings").select("settings").eq("organization_id",a).single();console.log("📋 Raw organization settings:",JSON.stringify(d,null,2)),console.log("⚠️ Settings fetch error:",b);let m=0;if(d!=null&&d.settings){const p=d.settings;console.log("🔧 Settings object:",JSON.stringify(p,null,2)),((t=p.tax_settings)==null?void 0:t.vat_rate)!==void 0?(m=Number(p.tax_settings.vat_rate),console.log(`✅ Found VAT rate in tax_settings.vat_rate: ${m}%`)):p.tax_rate!==void 0?(m=Number(p.tax_rate),console.log(`✅ Found VAT rate in tax_rate: ${m}%`)):console.log("❌ No VAT rate found in settings, using default 0%")}else console.log("❌ No settings object found, using default 0%");console.log(`🎯 Final VAT Rate: ${m}% for organization ${a}`);const A=m>0?s*m/(100+m):0;console.log(`💰 Receipt Total: ₱${s}, VAT Rate: ${m}%, VAT Amount: ₱${A}`),b&&console.warn("⚠️ Could not fetch organization settings, using 0% VAT:",b.message);const P=30,h=new Date(o.receipt_date);h.setDate(h.getDate()+P);const N=`INV-${o.receipt_number}`,{data:R,error:E}=await n.from("payables").insert({organization_id:a,source_type:"purchase_receipt",source_id:r,supplier_id:o.purchase_order.supplier_id,reference_number:N,invoice_date:o.receipt_date,payable_date:new Date().toISOString(),due_date:h.toISOString(),amount:s,vat_amount:A,balance:s,currency:"PHP",status:"open",category:"inventory",notes:`Manually created from receipt: ${o.receipt_number} (PO: ${o.purchase_order.order_number}). Amount calculated based on QC status - failed items excluded from payment.`,created_by:e}).select().single();return E?(console.error("Error creating payable:",E),{success:!1,error:E.message}):{success:!0,payable:R}}catch(o){return console.error("Error in createPayableFromReceipt:",o),{success:!1,error:o.message}}},Y=(a,r=0,e=0)=>{try{const o=(a-r)*e/100;return{isValid:!0,calculatedAmount:Math.round(o*100)/100}}catch(t){return{isValid:!1,calculatedAmount:0,error:t.message}}};export{f as P,D as a,L as b,V as c,y as d,w as e,O as f,S as g,T as h,$ as i,x as j,z as k,C as l,I as m,Y as v};
