import{h as Ne,r,j as e,B as h,a0 as de,i as S,J as I,A as X,P as D,Q as qe,_ as s,e as ze,a2 as Pe,a3 as Fe,M as d,s as x,a6 as f,a7 as Y,ah as me,E as he}from"./index-C6AV3cVN.js";import{C as Z}from"./Card-yj7fueH8.js";import{g as ke}from"./employee-DWC25S7P.js";import{P as He}from"./PageTitle-FHPo8gWi.js";import{E as Me}from"./EmployeeSearchSelect-vG5mZJbz.js";import{P as Ie}from"./Pagination-CVEzfctr.js";import{f as ye}from"./index-qirzObrW.js";import"./index-4YOzgfrD.js";import"./typeof-QjJsDpFa.js";import"./index-Cn2wB4rc.js";import"./index-DT2YvziZ.js";const Ge=()=>{const{currentOrganization:a,loading:N}=Ne(),[ee,ue]=r.useState([]),[q,g]=r.useState([]),[pe,le]=r.useState(!0),[ae,re]=r.useState(null),[te,xe]=r.useState(0),[z,se]=r.useState(1),[fe,ge]=r.useState(""),[O,P]=r.useState(!1),[je,F]=r.useState(!1),[Se,k]=r.useState(!1),[H,ie]=r.useState(null),[u,j]=r.useState(!1),[M,n]=r.useState(null),[v,R]=r.useState(""),[_,T]=r.useState(""),[b,A]=r.useState(""),[y,B]=r.useState("monthly"),[C,L]=r.useState(new Date().toISOString().split("T")[0]),[U,Q]=r.useState(""),[$,W]=r.useState("PHP"),[J,G]=r.useState("monthly"),[E,ve]=r.useState(10),w=async()=>{if(a){le(!0),re(null);try{console.log("Fetching employee salaries for organization:",a.id);let l=x.from("employee_salary").select(`
          *,
          employee:employee_id (
            id,
            first_name,
            middle_name,
            last_name,
            employee_number
          )
        `,{count:"exact"});if(a)try{const{data:o,error:p}=await x.from("employee_salary").select("id").limit(1);console.log("Checking if employee_salary table has records:",o,p),l=l.eq("organization_id",a.id)}catch(o){console.log("Organization ID filter not applied:",o),l=l.select(`
            *,
            employee:employee_id (
              id,
              first_name,
              middle_name,
              last_name,
              employee_number,
              organization_id
            )
          `,{count:"exact"}).filter("employee.organization_id","eq",a.id)}l=l.order("created_at",{ascending:!1}).range((z-1)*E,z*E-1);const{data:t,error:i,count:c}=await l;if(i)throw console.error("Error fetching employee salaries:",i),new Error(i.message);console.log("Fetched employee salaries:",t),ue(t||[]),xe(c||0)}catch(l){console.error("Error in fetchEmployeeSalaries:",l),re(l.message)}finally{le(!1)}}},oe=async()=>{if(!a){console.error("No current organization - waiting for organization context to load");return}try{console.log("Fetching employees for organization:",a.id,a.name);const{count:l,error:t}=await x.from("employees").select("*",{count:"exact",head:!0}).eq("organization_id",a.id);if(console.log("Employee count check:",l,t),t)throw console.error("Error checking employee count:",t),new Error(t.message);if(l===0){console.warn("No employees found for organization:",a.id),g([]);return}const{data:i,error:c}=await x.from("employees").select(`
          id,
          first_name,
          last_name,
          middle_name,
          employee_number,
          position_id,
          position:position_id (
            id,
            title
          )
        `).eq("organization_id",a.id).eq("is_active",!0);if(console.log("Direct employee query result:",i==null?void 0:i.length,c),c)throw console.error("Error fetching employees directly:",c),new Error(c.message);if(i&&i.length>0){console.log("Setting employees from direct query:",i.length),g(i);return}const{data:o,error:p}=await x.from("employees").select("id, first_name, last_name, middle_name, employee_number").eq("organization_id",a.id).eq("is_active",!0);if(console.log("Simple employee query result:",o==null?void 0:o.length,p),p)console.error("Error fetching employees with simple query:",p);else if(o&&o.length>0){console.log("Setting employees from simple query:",o.length),g(o);return}const{employees:m,error:V}=await ke(a.id,{isActive:!0});if(V)throw console.error("Error from getEmployees:",V),new Error(V);console.log("Fetched employees from service:",m==null?void 0:m.length),m&&m.length>0?g(m):(console.warn("No employees found from any method for organization:",a.id),g([]))}catch(l){console.error("Error fetching employees:",l),g([])}};r.useEffect(()=>{w()},[a,z,E]),r.useEffect(()=>{a&&(console.log("Organization changed, fetching employees for:",a.name),oe())},[a]),r.useEffect(()=>{O&&a&&(console.log("Add modal opened, fetching employees..."),oe())},[O,a]);const _e=l=>{l.preventDefault(),w()},K=()=>{R(""),T(""),A(""),B("monthly"),L(new Date().toISOString().split("T")[0]),Q(""),W("PHP"),G("monthly"),n(null)},ne=async()=>{if(K(),!a){console.error("Cannot add salary: No organization selected");return}P(!0),console.log("Opening add salary modal, current employees count:",q.length)},be=l=>{var t;ie(l.id),R(l.employee_id),T(l.basic_salary.toString()),A(((t=l.daily_rate)==null?void 0:t.toString())||""),B(l.rate_type),L(l.effective_date),Q(l.end_date||""),W(l.currency),G(l.pay_frequency),F(!0)},Ce=l=>{ie(l),k(!0)},Ee=async()=>{if(a){if(!v||!C){n("Please fill in all required fields");return}if(y==="monthly"&&!_){n("Please enter the monthly rate");return}if(y==="daily"&&!b){n("Please enter the daily rate");return}j(!0),n(null);try{let l=0,t=0;y==="monthly"?(l=parseFloat(_),t=l/22):(t=parseFloat(b),l=t*22);const{data:i,error:c}=await x.from("employee_salary").select("*").limit(1);console.log("Table info check:",i,c);const o={employee_id:v,basic_salary:l,daily_rate:t,rate_type:y,effective_date:C,end_date:U||null,currency:$,pay_frequency:J};a&&(o.organization_id=a.id),console.log("Inserting salary data:",o);const{data:p,error:m}=await x.from("employee_salary").insert(o).select();if(m)throw new Error(m.message);P(!1),w(),K()}catch(l){n(l.message)}finally{j(!1)}}},we=async()=>{if(!(!a||!H)){if(!v||!C){n("Please fill in all required fields");return}if(y==="monthly"&&!_){n("Please enter the monthly rate");return}if(y==="daily"&&!b){n("Please enter the daily rate");return}j(!0),n(null);try{let l=0,t=0;y==="monthly"?(l=parseFloat(_),t=l/22):(t=parseFloat(b),l=t*22);const i={employee_id:v,basic_salary:l,daily_rate:t,rate_type:y,effective_date:C,end_date:U||null,currency:$,pay_frequency:J};a&&(i.organization_id=a.id),console.log("Updating salary data:",i);let c=x.from("employee_salary").update(i).eq("id",H);if(a)try{c=c.eq("organization_id",a.id)}catch(m){console.log("Organization ID filter not applied:",m)}const{data:o,error:p}=await c.select();if(p)throw new Error(p.message);F(!1),w(),K()}catch(l){n(l.message)}finally{j(!1)}}},De=async()=>{if(!(!a||!H)){j(!0),n(null);try{let l=x.from("employee_salary").delete().eq("id",H);if(a)try{l=l.eq("organization_id",a.id)}catch(i){console.log("Organization ID filter not applied:",i)}const{error:t}=await l;if(t)throw new Error(t.message);k(!1),w()}catch(l){n(l.message)}finally{j(!1)}}},ce=()=>e.jsxs("div",{className:"space-y-4",children:[M&&e.jsx(X,{color:"failure",icon:I,children:M}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(f,{htmlFor:"employeeId",value:"Employee *"})}),e.jsx(Me,{employees:q,selectedEmployeeId:v,onSelect:R,required:!0,isLoading:!1,placeholder:"Search for an employee..."})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(f,{htmlFor:"rateType",value:"Rate Type *"})}),e.jsxs(Y,{id:"rateType",value:y,onChange:l=>B(l.target.value),required:!0,children:[e.jsx("option",{value:"monthly",children:"Monthly Rate"}),e.jsx("option",{value:"daily",children:"Daily Rate"})]})]}),y==="monthly"?e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(f,{htmlFor:"basicSalary",value:"Monthly Rate *"})}),e.jsx(D,{id:"basicSalary",type:"number",step:"0.01",min:"0",value:_,onChange:l=>T(l.target.value),required:!0,icon:me})]}):e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(f,{htmlFor:"dailyRate",value:"Daily Rate *"})}),e.jsx(D,{id:"dailyRate",type:"number",step:"0.01",min:"0",value:b,onChange:l=>A(l.target.value),required:!0,icon:me})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(f,{htmlFor:"effectiveDate",value:"Effective Date *"})}),e.jsx(D,{id:"effectiveDate",type:"date",value:C,onChange:l=>L(l.target.value),required:!0,icon:he})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(f,{htmlFor:"endDate",value:"End Date"})}),e.jsx(D,{id:"endDate",type:"date",value:U,onChange:l=>Q(l.target.value),icon:he})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(f,{htmlFor:"currency",value:"Currency"})}),e.jsxs(Y,{id:"currency",value:$,onChange:l=>W(l.target.value),children:[e.jsx("option",{value:"PHP",children:"PHP - Philippine Peso"}),e.jsx("option",{value:"USD",children:"USD - US Dollar"})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(f,{htmlFor:"payFrequency",value:"Pay Frequency"})}),e.jsxs(Y,{id:"payFrequency",value:J,onChange:l=>G(l.target.value),children:[e.jsx("option",{value:"monthly",children:"Monthly"}),e.jsx("option",{value:"semi_monthly",children:"Semi-Monthly"}),e.jsx("option",{value:"weekly",children:"Weekly"}),e.jsx("option",{value:"daily",children:"Daily"})]})]})]})]});return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx(He,{title:"Employee Salaries"}),e.jsxs(h,{color:"primary",onClick:ne,disabled:N||!a,children:[e.jsx(de,{className:"mr-2 h-5 w-5"}),"Add Salary"]})]}),N&&e.jsx(Z,{children:e.jsxs("div",{className:"flex justify-center items-center p-8",children:[e.jsx(S,{size:"xl"}),e.jsx("p",{className:"ml-4",children:"Loading organization data..."})]})}),!N&&!a&&e.jsx(Z,{children:e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(I,{className:"h-12 w-12 text-yellow-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No Organization Selected"}),e.jsx("p",{className:"text-gray-500",children:"Please select an organization to manage employee salaries."})]})}),!N&&a&&e.jsxs(e.Fragment,{children:[ae&&e.jsxs(X,{color:"failure",className:"mb-4",children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),ae]}),e.jsxs(Z,{children:[e.jsx("div",{className:"mb-4",children:e.jsx("form",{onSubmit:_e,children:e.jsx(D,{type:"text",placeholder:"Search employee salaries...",value:fe,onChange:l=>ge(l.target.value),icon:qe,rightIcon:()=>e.jsx(h,{type:"submit",size:"xs",color:"light",children:"Search"})})})}),pe?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(S,{size:"xl"})}):ee.length===0?e.jsxs("div",{className:"text-center p-8",children:[e.jsx("p",{className:"text-gray-500",children:"No employee salaries found."}),e.jsxs(h,{color:"primary",className:"mt-4",onClick:ne,children:[e.jsx(de,{className:"mr-2 h-5 w-5"}),"Add Salary"]})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(s,{striped:!0,children:[e.jsxs(s.Head,{children:[e.jsx(s.HeadCell,{children:"Employee"}),e.jsx(s.HeadCell,{children:"Rate Type"}),e.jsx(s.HeadCell,{children:"Monthly Rate"}),e.jsx(s.HeadCell,{children:"Daily Rate"}),e.jsx(s.HeadCell,{children:"Effective Date"}),e.jsx(s.HeadCell,{children:"End Date"}),e.jsx(s.HeadCell,{children:"Pay Frequency"}),e.jsx(s.HeadCell,{children:"Actions"})]}),e.jsx(s.Body,{children:ee.map(l=>e.jsxs(s.Row,{children:[e.jsx(s.Cell,{className:"font-medium",children:l.employee?e.jsxs("div",{children:[e.jsx("div",{children:`${l.employee.first_name} ${l.employee.middle_name?l.employee.middle_name+" ":""}${l.employee.last_name}`}),l.employee.employee_number&&e.jsxs("div",{className:"text-xs text-gray-500",children:["ID: ",l.employee.employee_number]})]}):"Unknown Employee"}),e.jsx(s.Cell,{children:e.jsx(ze,{color:l.rate_type==="daily"?"success":"info",children:l.rate_type==="daily"?"Daily":"Monthly"})}),e.jsxs(s.Cell,{children:[l.currency," ",(l.basic_salary||l.monthly_rate||0).toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),e.jsxs(s.Cell,{children:[l.currency," ",(l.daily_rate||0).toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),e.jsx(s.Cell,{children:ye(new Date(l.effective_date),"MMM d, yyyy")}),e.jsx(s.Cell,{children:l.end_date?ye(new Date(l.end_date),"MMM d, yyyy"):"-"}),e.jsx(s.Cell,{children:l.pay_frequency==="monthly"?"Monthly":l.pay_frequency==="semi_monthly"?"Semi-Monthly":l.pay_frequency==="weekly"?"Weekly":l.pay_frequency==="daily"?"Daily":l.pay_frequency}),e.jsx(s.Cell,{children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(h,{size:"xs",color:"light",onClick:()=>be(l),children:[e.jsx(Pe,{className:"mr-1 h-4 w-4"}),"Edit"]}),e.jsxs(h,{size:"xs",color:"failure",onClick:()=>Ce(l.id),children:[e.jsx(Fe,{className:"mr-1 h-4 w-4"}),"Delete"]})]})})]},l.id))})]})}),e.jsx(Ie,{currentPage:z,totalPages:Math.ceil(te/E),itemsPerPage:E,totalItems:te,onPageChange:se,onItemsPerPageChange:l=>{ve(l),se(1)},itemName:"salaries"})]}),e.jsxs(d,{show:O,onClose:()=>P(!1),size:"md",children:[e.jsx(d.Header,{children:"Add Employee Salary"}),e.jsx(d.Body,{children:q.length===0?e.jsxs("div",{className:"p-4 text-center",children:[e.jsx(S,{size:"xl",className:"mx-auto mb-4"}),e.jsx("p",{children:"Loading employees..."})]}):ce()}),e.jsxs(d.Footer,{children:[e.jsx(h,{color:"primary",onClick:Ee,disabled:u||q.length===0,children:u?e.jsxs(e.Fragment,{children:[e.jsx(S,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save"}),e.jsx(h,{color:"gray",onClick:()=>P(!1),disabled:u,children:"Cancel"})]})]}),e.jsxs(d,{show:je,onClose:()=>F(!1),size:"md",children:[e.jsx(d.Header,{children:"Edit Employee Salary"}),e.jsx(d.Body,{children:ce()}),e.jsxs(d.Footer,{children:[e.jsx(h,{color:"primary",onClick:we,disabled:u,children:u?e.jsxs(e.Fragment,{children:[e.jsx(S,{size:"sm",className:"mr-2"}),"Updating..."]}):"Update"}),e.jsx(h,{color:"gray",onClick:()=>F(!1),disabled:u,children:"Cancel"})]})]}),e.jsxs(d,{show:Se,onClose:()=>k(!1),size:"md",children:[e.jsx(d.Header,{children:"Delete Employee Salary"}),e.jsx(d.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"Are you sure you want to delete this salary record? This action cannot be undone."}),M&&e.jsxs(X,{color:"failure",children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),M]})]})}),e.jsxs(d.Footer,{children:[e.jsx(h,{color:"failure",onClick:De,disabled:u,children:u?e.jsxs(e.Fragment,{children:[e.jsx(S,{size:"sm",className:"mr-2"}),"Deleting..."]}):"Delete"}),e.jsx(h,{color:"gray",onClick:()=>k(!1),disabled:u,children:"Cancel"})]})]})]})]})};export{Ge as default};
