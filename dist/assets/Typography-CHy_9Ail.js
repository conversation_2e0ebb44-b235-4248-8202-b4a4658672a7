import{j as e}from"./index-C6AV3cVN.js";const s=()=>e.jsxs("div",{className:"rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words",children:[e.jsx("h5",{className:"card-title",children:"Typography"}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"border border-ld rounded-xl px-6 py-4 mb-6",children:[e.jsx("h1",{className:"font-semibold text-4xl",children:"h1.Heading"}),e.jsx("p",{className:"mt-2",children:"font size: 36 | line-height: 40 | font weight: 600"})]}),e.jsxs("div",{className:"border border-ld rounded-xl px-6 py-4 mb-6",children:[e.jsx("h2",{className:"font-semibold text-3xl",children:"h2.Heading"}),e.jsx("p",{className:"mt-2",children:"font size: 30 | line-height: 36 | font weight: 600"})]}),e.jsxs("div",{className:"border border-ld rounded-xl px-6 py-4 mb-6",children:[e.jsx("h3",{className:"font-semibold text-2xl",children:"h3.Heading"}),e.jsx("p",{className:"mt-2",children:"font size: 24 | line-height: 32 | font weight: 600"})]}),e.jsxs("div",{className:"border border-ld rounded-xl px-6 py-4 mb-6",children:[e.jsx("h4",{className:"font-semibold text-xl",children:"h4.Heading"}),e.jsx("p",{className:"mt-2",children:"font size: 20 | line-height: 28 | font weight: 600"})]}),e.jsxs("div",{className:"border border-ld rounded-xl px-6 py-4 mb-6",children:[e.jsx("h5",{className:"font-semibold text-lg",children:"h5.Heading"}),e.jsx("p",{className:"mt-2",children:"font size: 20 | line-height: 28 | font weight: 600"})]}),e.jsxs("div",{className:"border border-ld rounded-xl px-6 py-4 mb-6",children:[e.jsx("h6",{className:"font-semibold text-base",children:"h6.Heading"}),e.jsx("p",{className:"mt-2",children:"font size: 16 | line-height: 24 | font weight: 600"})]})]})]}),l=()=>e.jsx(s,{});export{l as default};
