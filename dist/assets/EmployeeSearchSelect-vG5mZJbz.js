import{r as t,j as s,P as b,Q as C,B as E,V as S,i as _}from"./index-C6AV3cVN.js";const L=({employees:a,selectedEmployeeId:n,onSelect:o,required:g=!1,isLoading:m=!1,className:p="",placeholder:v="Search and select an employee..."})=>{const[r,c]=t.useState(""),[j,i]=t.useState(!1),[f,x]=t.useState([]),d=t.useRef(null),u=a.find(e=>e.id===n),w=u?`${u.first_name} ${u.last_name}`:r;t.useEffect(()=>{if(!r.trim()){x(a);return}const e=r.toLowerCase(),h=a.filter(l=>l.first_name.toLowerCase().includes(e)||l.last_name.toLowerCase().includes(e)||`${l.first_name} ${l.last_name}`.toLowerCase().includes(e));x(h)},[r,a]),t.useEffect(()=>{const e=h=>{d.current&&!d.current.contains(h.target)&&i(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);const N=e=>{o(e),i(!1),c("")},y=()=>{o(""),c("")};return s.jsxs("div",{className:`relative ${p}`,ref:d,children:[s.jsxs("div",{className:"relative",children:[s.jsx(b,{type:"text",value:w,onChange:e=>{c(e.target.value),n&&o(""),i(!0)},onClick:()=>i(!0),placeholder:v,required:g,icon:C,rightIcon:n?()=>s.jsx(E,{size:"xs",color:"light",onClick:y,className:"absolute right-1 top-1/2 transform -translate-y-1/2",children:s.jsx(S,{className:"h-4 w-4"})}):void 0,disabled:m}),m&&s.jsx("div",{className:"absolute right-10 top-1/2 transform -translate-y-1/2",children:s.jsx(_,{size:"sm"})})]}),j&&s.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:a.length===0?s.jsx("div",{className:"px-4 py-2 text-gray-500",children:"No employees available. Please add employees first."}):f.length>0?s.jsx("ul",{className:"py-1",children:f.map(e=>s.jsxs("li",{className:`px-4 py-2 cursor-pointer hover:bg-gray-100 ${e.id===n?"bg-blue-50":""}`,onClick:()=>N(e.id),children:[s.jsxs("div",{className:"font-medium",children:[e.first_name," ",e.last_name]}),e.position&&s.jsx("div",{className:"text-sm text-gray-500",children:e.position.title})]},e.id))}):s.jsxs("div",{className:"px-4 py-2 text-gray-500",children:['No employees found matching "',r,'"']})})]})};export{L as E};
