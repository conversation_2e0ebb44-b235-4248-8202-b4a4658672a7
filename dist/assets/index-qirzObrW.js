import{t as p,i as K}from"./index-4YOzgfrD.js";import{r as g,t as T}from"./index-Cn2wB4rc.js";import{g as S,l as V,a as A}from"./index-DT2YvziZ.js";function J(n,t){g(2,arguments);var e=T(n).getTime(),r=p(t);return new Date(e+r)}function Z(n,t){g(2,arguments);var e=p(t);return J(n,-e)}var j=864e5;function z(n){g(1,arguments);var t=T(n),e=t.getTime();t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0);var r=t.getTime(),a=e-r;return Math.floor(a/j)+1}function _(n){g(1,arguments);var t=1,e=T(n),r=e.getUTCDay(),a=(r<t?7:0)+r-t;return e.setUTCDate(e.getUTCDate()-a),e.setUTCHours(0,0,0,0),e}function G(n){g(1,arguments);var t=T(n),e=t.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(e+1,0,4),r.setUTCHours(0,0,0,0);var a=_(r),i=new Date(0);i.setUTCFullYear(e,0,4),i.setUTCHours(0,0,0,0);var o=_(i);return t.getTime()>=a.getTime()?e+1:t.getTime()>=o.getTime()?e:e-1}function ee(n){g(1,arguments);var t=G(n),e=new Date(0);e.setUTCFullYear(t,0,4),e.setUTCHours(0,0,0,0);var r=_(e);return r}var te=6048e5;function re(n){g(1,arguments);var t=T(n),e=_(t).getTime()-ee(t).getTime();return Math.round(e/te)+1}function E(n,t){var e,r,a,i,o,s,c,f;g(1,arguments);var m=S(),d=p((e=(r=(a=(i=t==null?void 0:t.weekStartsOn)!==null&&i!==void 0?i:t==null||(o=t.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.weekStartsOn)!==null&&a!==void 0?a:m.weekStartsOn)!==null&&r!==void 0?r:(c=m.locale)===null||c===void 0||(f=c.options)===null||f===void 0?void 0:f.weekStartsOn)!==null&&e!==void 0?e:0);if(!(d>=0&&d<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var v=T(n),l=v.getUTCDay(),w=(l<d?7:0)+l-d;return v.setUTCDate(v.getUTCDate()-w),v.setUTCHours(0,0,0,0),v}function F(n,t){var e,r,a,i,o,s,c,f;g(1,arguments);var m=T(n),d=m.getUTCFullYear(),v=S(),l=p((e=(r=(a=(i=t==null?void 0:t.firstWeekContainsDate)!==null&&i!==void 0?i:t==null||(o=t.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.firstWeekContainsDate)!==null&&a!==void 0?a:v.firstWeekContainsDate)!==null&&r!==void 0?r:(c=v.locale)===null||c===void 0||(f=c.options)===null||f===void 0?void 0:f.firstWeekContainsDate)!==null&&e!==void 0?e:1);if(!(l>=1&&l<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var w=new Date(0);w.setUTCFullYear(d+1,0,l),w.setUTCHours(0,0,0,0);var x=E(w,t),b=new Date(0);b.setUTCFullYear(d,0,l),b.setUTCHours(0,0,0,0);var P=E(b,t);return m.getTime()>=x.getTime()?d+1:m.getTime()>=P.getTime()?d:d-1}function ae(n,t){var e,r,a,i,o,s,c,f;g(1,arguments);var m=S(),d=p((e=(r=(a=(i=t==null?void 0:t.firstWeekContainsDate)!==null&&i!==void 0?i:t==null||(o=t.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.firstWeekContainsDate)!==null&&a!==void 0?a:m.firstWeekContainsDate)!==null&&r!==void 0?r:(c=m.locale)===null||c===void 0||(f=c.options)===null||f===void 0?void 0:f.firstWeekContainsDate)!==null&&e!==void 0?e:1),v=F(n,t),l=new Date(0);l.setUTCFullYear(v,0,d),l.setUTCHours(0,0,0,0);var w=E(l,t);return w}var ne=6048e5;function ie(n,t){g(1,arguments);var e=T(n),r=E(e,t).getTime()-ae(e,t).getTime();return Math.round(r/ne)+1}function u(n,t){for(var e=n<0?"-":"",r=Math.abs(n).toString();r.length<t;)r="0"+r;return e+r}var C={y:function(t,e){var r=t.getUTCFullYear(),a=r>0?r:1-r;return u(e==="yy"?a%100:a,e.length)},M:function(t,e){var r=t.getUTCMonth();return e==="M"?String(r+1):u(r+1,2)},d:function(t,e){return u(t.getUTCDate(),e.length)},a:function(t,e){var r=t.getUTCHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];case"aaaa":default:return r==="am"?"a.m.":"p.m."}},h:function(t,e){return u(t.getUTCHours()%12||12,e.length)},H:function(t,e){return u(t.getUTCHours(),e.length)},m:function(t,e){return u(t.getUTCMinutes(),e.length)},s:function(t,e){return u(t.getUTCSeconds(),e.length)},S:function(t,e){var r=e.length,a=t.getUTCMilliseconds(),i=Math.floor(a*Math.pow(10,r-3));return u(i,e.length)}},D={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},oe={G:function(t,e,r){var a=t.getUTCFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return r.era(a,{width:"abbreviated"});case"GGGGG":return r.era(a,{width:"narrow"});case"GGGG":default:return r.era(a,{width:"wide"})}},y:function(t,e,r){if(e==="yo"){var a=t.getUTCFullYear(),i=a>0?a:1-a;return r.ordinalNumber(i,{unit:"year"})}return C.y(t,e)},Y:function(t,e,r,a){var i=F(t,a),o=i>0?i:1-i;if(e==="YY"){var s=o%100;return u(s,2)}return e==="Yo"?r.ordinalNumber(o,{unit:"year"}):u(o,e.length)},R:function(t,e){var r=G(t);return u(r,e.length)},u:function(t,e){var r=t.getUTCFullYear();return u(r,e.length)},Q:function(t,e,r){var a=Math.ceil((t.getUTCMonth()+1)/3);switch(e){case"Q":return String(a);case"QQ":return u(a,2);case"Qo":return r.ordinalNumber(a,{unit:"quarter"});case"QQQ":return r.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(a,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(a,{width:"wide",context:"formatting"})}},q:function(t,e,r){var a=Math.ceil((t.getUTCMonth()+1)/3);switch(e){case"q":return String(a);case"qq":return u(a,2);case"qo":return r.ordinalNumber(a,{unit:"quarter"});case"qqq":return r.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(a,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(a,{width:"wide",context:"standalone"})}},M:function(t,e,r){var a=t.getUTCMonth();switch(e){case"M":case"MM":return C.M(t,e);case"Mo":return r.ordinalNumber(a+1,{unit:"month"});case"MMM":return r.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(a,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(a,{width:"wide",context:"formatting"})}},L:function(t,e,r){var a=t.getUTCMonth();switch(e){case"L":return String(a+1);case"LL":return u(a+1,2);case"Lo":return r.ordinalNumber(a+1,{unit:"month"});case"LLL":return r.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(a,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(a,{width:"wide",context:"standalone"})}},w:function(t,e,r,a){var i=ie(t,a);return e==="wo"?r.ordinalNumber(i,{unit:"week"}):u(i,e.length)},I:function(t,e,r){var a=re(t);return e==="Io"?r.ordinalNumber(a,{unit:"week"}):u(a,e.length)},d:function(t,e,r){return e==="do"?r.ordinalNumber(t.getUTCDate(),{unit:"date"}):C.d(t,e)},D:function(t,e,r){var a=z(t);return e==="Do"?r.ordinalNumber(a,{unit:"dayOfYear"}):u(a,e.length)},E:function(t,e,r){var a=t.getUTCDay();switch(e){case"E":case"EE":case"EEE":return r.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(a,{width:"short",context:"formatting"});case"EEEE":default:return r.day(a,{width:"wide",context:"formatting"})}},e:function(t,e,r,a){var i=t.getUTCDay(),o=(i-a.weekStartsOn+8)%7||7;switch(e){case"e":return String(o);case"ee":return u(o,2);case"eo":return r.ordinalNumber(o,{unit:"day"});case"eee":return r.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(i,{width:"short",context:"formatting"});case"eeee":default:return r.day(i,{width:"wide",context:"formatting"})}},c:function(t,e,r,a){var i=t.getUTCDay(),o=(i-a.weekStartsOn+8)%7||7;switch(e){case"c":return String(o);case"cc":return u(o,e.length);case"co":return r.ordinalNumber(o,{unit:"day"});case"ccc":return r.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(i,{width:"narrow",context:"standalone"});case"cccccc":return r.day(i,{width:"short",context:"standalone"});case"cccc":default:return r.day(i,{width:"wide",context:"standalone"})}},i:function(t,e,r){var a=t.getUTCDay(),i=a===0?7:a;switch(e){case"i":return String(i);case"ii":return u(i,e.length);case"io":return r.ordinalNumber(i,{unit:"day"});case"iii":return r.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(a,{width:"short",context:"formatting"});case"iiii":default:return r.day(a,{width:"wide",context:"formatting"})}},a:function(t,e,r){var a=t.getUTCHours(),i=a/12>=1?"pm":"am";switch(e){case"a":case"aa":return r.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(i,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(i,{width:"wide",context:"formatting"})}},b:function(t,e,r){var a=t.getUTCHours(),i;switch(a===12?i=D.noon:a===0?i=D.midnight:i=a/12>=1?"pm":"am",e){case"b":case"bb":return r.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(i,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(t,e,r){var a=t.getUTCHours(),i;switch(a>=17?i=D.evening:a>=12?i=D.afternoon:a>=4?i=D.morning:i=D.night,e){case"B":case"BB":case"BBB":return r.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(i,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(t,e,r){if(e==="ho"){var a=t.getUTCHours()%12;return a===0&&(a=12),r.ordinalNumber(a,{unit:"hour"})}return C.h(t,e)},H:function(t,e,r){return e==="Ho"?r.ordinalNumber(t.getUTCHours(),{unit:"hour"}):C.H(t,e)},K:function(t,e,r){var a=t.getUTCHours()%12;return e==="Ko"?r.ordinalNumber(a,{unit:"hour"}):u(a,e.length)},k:function(t,e,r){var a=t.getUTCHours();return a===0&&(a=24),e==="ko"?r.ordinalNumber(a,{unit:"hour"}):u(a,e.length)},m:function(t,e,r){return e==="mo"?r.ordinalNumber(t.getUTCMinutes(),{unit:"minute"}):C.m(t,e)},s:function(t,e,r){return e==="so"?r.ordinalNumber(t.getUTCSeconds(),{unit:"second"}):C.s(t,e)},S:function(t,e){return C.S(t,e)},X:function(t,e,r,a){var i=a._originalDate||t,o=i.getTimezoneOffset();if(o===0)return"Z";switch(e){case"X":return N(o);case"XXXX":case"XX":return y(o);case"XXXXX":case"XXX":default:return y(o,":")}},x:function(t,e,r,a){var i=a._originalDate||t,o=i.getTimezoneOffset();switch(e){case"x":return N(o);case"xxxx":case"xx":return y(o);case"xxxxx":case"xxx":default:return y(o,":")}},O:function(t,e,r,a){var i=a._originalDate||t,o=i.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+q(o,":");case"OOOO":default:return"GMT"+y(o,":")}},z:function(t,e,r,a){var i=a._originalDate||t,o=i.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+q(o,":");case"zzzz":default:return"GMT"+y(o,":")}},t:function(t,e,r,a){var i=a._originalDate||t,o=Math.floor(i.getTime()/1e3);return u(o,e.length)},T:function(t,e,r,a){var i=a._originalDate||t,o=i.getTime();return u(o,e.length)}};function q(n,t){var e=n>0?"-":"+",r=Math.abs(n),a=Math.floor(r/60),i=r%60;if(i===0)return e+String(a);var o=t;return e+String(a)+o+u(i,2)}function N(n,t){if(n%60===0){var e=n>0?"-":"+";return e+u(Math.abs(n)/60,2)}return y(n,t)}function y(n,t){var e=t||"",r=n>0?"-":"+",a=Math.abs(n),i=u(Math.floor(a/60),2),o=u(a%60,2);return r+i+e+o}var H=function(t,e){switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});case"PPPP":default:return e.date({width:"full"})}},I=function(t,e){switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});case"pppp":default:return e.time({width:"full"})}},ue=function(t,e){var r=t.match(/(P+)(p+)?/)||[],a=r[1],i=r[2];if(!i)return H(t,e);var o;switch(a){case"P":o=e.dateTime({width:"short"});break;case"PP":o=e.dateTime({width:"medium"});break;case"PPP":o=e.dateTime({width:"long"});break;case"PPPP":default:o=e.dateTime({width:"full"});break}return o.replace("{{date}}",H(a,e)).replace("{{time}}",I(i,e))},se={p:I,P:ue},de=["D","DD"],ce=["YY","YYYY"];function fe(n){return de.indexOf(n)!==-1}function le(n){return ce.indexOf(n)!==-1}function L(n,t,e){if(n==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(t,"`) for formatting years to the input `").concat(e,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(n==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(t,"`) for formatting years to the input `").concat(e,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(n==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(t,"`) for formatting days of the month to the input `").concat(e,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(n==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(t,"`) for formatting days of the month to the input `").concat(e,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var ve=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,me=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,he=/^'([^]*?)'?$/,ge=/''/g,we=/[a-zA-Z]/;function ye(n,t,e){var r,a,i,o,s,c,f,m,d,v,l,w,x,b;g(2,arguments);var P=String(t),U=S(),k=(r=(a=void 0)!==null&&a!==void 0?a:U.locale)!==null&&r!==void 0?r:V,W=p((i=(o=(s=(c=void 0)!==null&&c!==void 0?c:void 0)!==null&&s!==void 0?s:U.firstWeekContainsDate)!==null&&o!==void 0?o:(f=U.locale)===null||f===void 0||(m=f.options)===null||m===void 0?void 0:m.firstWeekContainsDate)!==null&&i!==void 0?i:1);if(!(W>=1&&W<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var $=p((d=(v=(l=(w=void 0)!==null&&w!==void 0?w:void 0)!==null&&l!==void 0?l:U.weekStartsOn)!==null&&v!==void 0?v:(x=U.locale)===null||x===void 0||(b=x.options)===null||b===void 0?void 0:b.weekStartsOn)!==null&&d!==void 0?d:0);if(!($>=0&&$<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!k.localize)throw new RangeError("locale must contain localize property");if(!k.formatLong)throw new RangeError("locale must contain formatLong property");var Y=T(n);if(!K(Y))throw new RangeError("Invalid time value");var Q=A(Y),R=Z(Y,Q),B={firstWeekContainsDate:W,weekStartsOn:$,locale:k,_originalDate:Y},X=P.match(me).map(function(h){var O=h[0];if(O==="p"||O==="P"){var M=se[O];return M(h,k.formatLong)}return h}).join("").match(ve).map(function(h){if(h==="''")return"'";var O=h[0];if(O==="'")return Te(h);var M=oe[O];if(M)return le(h)&&L(h,t,String(n)),fe(h)&&L(h,t,String(n)),M(R,h,k.localize,B);if(O.match(we))throw new RangeError("Format string contains an unescaped latin alphabet character `"+O+"`");return h}).join("");return X}function Te(n){var t=n.match(he);return t?t[1].replace(ge,"'"):n}export{ye as f};
