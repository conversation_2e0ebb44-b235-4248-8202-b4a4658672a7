import{r as x,j as e,A as g,J as y,a6 as a,P as n,a8 as b,ad as p,B as N,i as f}from"./index-C6AV3cVN.js";const F=({initialData:d,onSubmit:h,isSubmitting:r,error:m})=>{const[s,t]=x.useState(()=>({name:"",email:"",phone:"",address:"",city:"",state:"",postal_code:"",country:"",tax_id:"",notes:"",...d}));x.useEffect(()=>{d&&t(i=>({...i,...d}))},[d]);const l=i=>{const{name:o,value:c}=i.target;t(u=>({...u,[o]:c}))},j=(i,o)=>{t(c=>({...c,[o]:i}))},v=async i=>{i.preventDefault(),await h(s)};return e.jsxs("form",{onSubmit:v,className:"space-y-6",children:[m&&e.jsx(g,{color:"failure",icon:y,children:m}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Basic Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(a,{htmlFor:"name",value:"Name *"})}),e.jsx(n,{id:"name",name:"name",value:s.name||"",onChange:l,required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(a,{htmlFor:"email",value:"Email"})}),e.jsx(n,{id:"email",name:"email",type:"email",value:s.email||"",onChange:l})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(a,{htmlFor:"phone",value:"Phone"})}),e.jsx(n,{id:"phone",name:"phone",value:s.phone||"",onChange:l})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Address Information"}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(a,{htmlFor:"address",value:"Address"})}),e.jsx(n,{id:"address",name:"address",value:s.address||"",onChange:l})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(a,{htmlFor:"city",value:"City"})}),e.jsx(n,{id:"city",name:"city",value:s.city||"",onChange:l})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(a,{htmlFor:"state",value:"State/Province"})}),e.jsx(n,{id:"state",name:"state",value:s.state||"",onChange:l})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(a,{htmlFor:"postal_code",value:"Postal Code"})}),e.jsx(n,{id:"postal_code",name:"postal_code",value:s.postal_code||"",onChange:l})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(a,{htmlFor:"country",value:"Country"})}),e.jsx(n,{id:"country",name:"country",value:s.country||"",onChange:l})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Additional Information"}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(a,{htmlFor:"tax_id",value:"Tax ID"})}),e.jsx(n,{id:"tax_id",name:"tax_id",value:s.tax_id||"",onChange:l})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx(a,{htmlFor:"loyalty_eligible",value:"Loyalty Program Eligible",className:"mb-0"}),e.jsx(b,{id:"loyalty_eligible",checked:s.loyalty_eligible||!1,onChange:i=>j(i,"loyalty_eligible"),label:""})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Enable this to allow the customer to earn and redeem loyalty points"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(a,{htmlFor:"notes",value:"Notes"})}),e.jsx(p,{id:"notes",name:"notes",rows:4,value:s.notes||"",onChange:l})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(N,{type:"submit",color:"primary",disabled:r,children:r?e.jsxs(e.Fragment,{children:[e.jsx(f,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Customer"})})]})};export{F as C};
