import{ab as R,h as U,r,j as e,i as O,A as p,J as g,bA as G,L as H,B as n,o as Y,a2 as J,a3 as F,aX as C,aY as k,bs as L,K,E as X,bu as q,bt as Q,k as V,M as N,e as i}from"./index-C6AV3cVN.js";import{C as x}from"./Card-yj7fueH8.js";import{a as W,d as Z}from"./employee-DWC25S7P.js";const re=()=>{var b,w,D,E,I,S,A;const{id:a}=R(),{currentOrganization:l}=U(),[s,P]=r.useState(null),[B,y]=r.useState(!0),[u,d]=r.useState(null),[$,o]=r.useState(!1),[v,m]=r.useState(null),[h,f]=r.useState(!1);r.useEffect(()=>{l&&a&&z()},[l,a]),r.useEffect(()=>{},[s]);const z=async()=>{if(!(!l||!a)){y(!0),d(null);try{const{employee:t,error:c}=await W(l.id,a);c?d(c):t?P(t):d("Employee not found")}catch(t){d(t.message||"An error occurred while fetching the employee")}finally{y(!1)}}},T=()=>{m(null),o(!0)},M=async()=>{if(!(!l||!a)){f(!0),m(null);try{const{success:t,error:c}=await Z(l.id,a);c?m(c):t&&(window.location.href="/employees")}catch(t){m(t.message||"An error occurred while deleting the employee")}finally{f(!1)}}},_=t=>{if(!t)return null;switch(t){case"active":return e.jsx(i,{color:"success",children:"Active"});case"on_leave":return e.jsx(i,{color:"warning",children:"On Leave"});case"terminated":return e.jsx(i,{color:"failure",children:"Terminated"});case"resigned":return e.jsx(i,{color:"gray",children:"Resigned"});case"retired":return e.jsx(i,{color:"purple",children:"Retired"});default:return e.jsx(i,{color:"gray",children:t})}},j=t=>t?new Date(t).toLocaleDateString():"N/A";return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[B?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(O,{size:"xl"})}):u?e.jsxs(p,{color:"failure",children:[e.jsx(g,{className:"h-4 w-4 mr-2"}),u]}):s?e.jsxs(e.Fragment,{children:[e.jsxs(x,{className:"mb-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"relative w-16 h-16 mr-4",children:s.profile_image_url?e.jsx("img",{src:s.profile_image_url,alt:`${s.first_name} ${s.last_name}`,className:"w-16 h-16 rounded-full object-cover border border-gray-200"}):e.jsx(G,{size:"lg",rounded:!0,placeholderInitials:`${(b=s.first_name)==null?void 0:b[0]}${(w=s.last_name)==null?void 0:w[0]}`,className:"w-16 h-16"})}),e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold",children:[s.first_name," ",s.middle_name?`${s.middle_name} `:"",s.last_name]}),e.jsxs("div",{className:"flex items-center mt-1",children:[((D=s.position)==null?void 0:D.title)&&e.jsx("span",{className:"text-gray-600 mr-2",children:s.position.title}),((E=s.department)==null?void 0:E.name)&&e.jsxs("span",{className:"text-gray-500",children:["• ",s.department.name]})]}),e.jsx("div",{className:"mt-2",children:s.status&&_(s.status)})]})]}),e.jsxs("div",{className:"flex space-x-2 mt-4 md:mt-0",children:[e.jsx(H,{to:"/employees",children:e.jsxs(n,{color:"gray",children:[e.jsx(Y,{className:"mr-2 h-5 w-5"}),"Back"]})}),e.jsx(H,{to:`/employees/edit/${s.id}`,children:e.jsxs(n,{color:"primary",children:[e.jsx(J,{className:"mr-2 h-5 w-5"}),"Edit"]})}),e.jsxs(n,{color:"failure",onClick:T,children:[e.jsx(F,{className:"mr-2 h-5 w-5"}),"Delete"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-200",children:[s.email&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(C,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),e.jsx("p",{children:s.email})]})]}),s.phone&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(k,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),e.jsx("p",{children:s.phone})]})]}),s.employee_number&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(L,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Employee ID"}),e.jsx("p",{children:s.employee_number})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6",children:[e.jsxs(x,{children:[e.jsxs("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[e.jsx(K,{className:"mr-2 h-5 w-5 text-gray-600"}),"Personal Information"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium mb-4 text-gray-700",children:"Contact Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx(C,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),e.jsx("p",{children:s.email||"Not provided"})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(k,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),e.jsx("p",{children:s.phone||"Not provided"})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(X,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Date of Birth"}),e.jsx("p",{children:s.date_of_birth?j(s.date_of_birth):"Not provided"})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(q,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Nationality"}),e.jsx("p",{children:s.nationality||"Not provided"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium mb-4 text-gray-700",children:"Address"}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(Q,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Address"}),e.jsx("p",{children:s.address||"Not provided"}),(s.city||s.state||s.postal_code)&&e.jsxs("p",{children:[s.city&&`${s.city}, `,s.state&&`${s.state} `,s.postal_code&&s.postal_code]}),e.jsx("p",{children:s.country||""})]})]})})]})]}),e.jsxs("div",{className:"pt-4 border-t border-gray-200",children:[e.jsx("h3",{className:"text-lg font-medium mb-4 text-gray-700",children:"Emergency Contact"}),s.emergency_contact_name?e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Name"}),e.jsx("p",{children:s.emergency_contact_name})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),e.jsx("p",{children:s.emergency_contact_phone||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Relationship"}),e.jsx("p",{children:s.emergency_contact_relationship||"Not provided"})]})]}):e.jsx("p",{className:"text-gray-500",children:"No emergency contact provided"})]})]}),e.jsxs(x,{children:[e.jsxs("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[e.jsx(V,{className:"mr-2 h-5 w-5 text-gray-600"}),"Employment Details"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium mb-4 text-gray-700",children:"Job Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Employee Number"}),e.jsx("p",{children:s.employee_number||"Not assigned"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Department"}),e.jsx("p",{children:((I=s.department)==null?void 0:I.name)||"Not assigned"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Position"}),e.jsx("p",{children:((S=s.position)==null?void 0:S.title)||"Not assigned"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Employment Type"}),e.jsx("p",{children:((A=s.employment_type)==null?void 0:A.name)||"Not assigned"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium mb-4 text-gray-700",children:"Employment Status"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Status"}),e.jsx("div",{className:"mt-1",children:_(s.status)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Hire Date"}),e.jsx("p",{children:s.hire_date?j(s.hire_date):"Not provided"})]}),s.end_date&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"End Date"}),e.jsx("p",{children:j(s.end_date)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Active"}),e.jsx("p",{children:s.is_active?"Yes":"No"})]}),s.user_id&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"System User Account"}),e.jsx("p",{children:"Linked to user account"})]})]})]})]}),s.notes&&e.jsxs("div",{className:"pt-4 border-t border-gray-200",children:[e.jsx("h3",{className:"text-lg font-medium mb-4 text-gray-700",children:"Notes"}),e.jsx("p",{className:"text-gray-700 whitespace-pre-line",children:s.notes})]})]}),e.jsxs(x,{children:[e.jsxs("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[e.jsx(L,{className:"mr-2 h-5 w-5 text-gray-600"}),"Government IDs"]}),Array.isArray(s.government_ids)&&s.government_ids.length>0?e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"SSS Number"}),e.jsx("p",{children:s.government_ids[0].sss_number||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"PhilHealth Number"}),e.jsx("p",{children:s.government_ids[0].philhealth_number||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Pag-IBIG Number"}),e.jsx("p",{children:s.government_ids[0].pagibig_number||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"TIN Number"}),e.jsx("p",{children:s.government_ids[0].tin_number||"Not provided"})]})]}):e.jsx("p",{className:"text-gray-500",children:"No government IDs provided"})]}),e.jsxs("div",{className:"mt-2 pt-6 border-t border-gray-200",children:[e.jsx("h2",{className:"text-sm font-medium text-gray-500 mb-2",children:"System Information"}),e.jsxs("div",{className:"flex flex-wrap gap-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Employee ID"}),e.jsx("p",{className:"text-sm",children:s.id})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Created"}),e.jsx("p",{className:"text-sm",children:new Date(s.created_at).toLocaleString()})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Last Updated"}),e.jsx("p",{className:"text-sm",children:new Date(s.updated_at).toLocaleString()})]}),s.department&&e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Department ID"}),e.jsx("p",{className:"text-sm",children:s.department_id})]}),s.position&&e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Position ID"}),e.jsx("p",{className:"text-sm",children:s.position_id})]}),s.employment_type&&e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Employment Type ID"}),e.jsx("p",{className:"text-sm",children:s.employment_type_id})]}),s.user_id&&e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"User Account ID"}),e.jsx("p",{className:"text-sm",children:s.user_id})]})]})]})]})]}):e.jsxs(p,{color:"failure",children:[e.jsx(g,{className:"h-4 w-4 mr-2"}),"Employee not found"]}),e.jsxs(N,{show:$,onClose:()=>o(!1),size:"md",popup:!0,children:[e.jsx(N.Header,{}),e.jsx(N.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(g,{className:"mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500 dark:text-gray-400",children:["Are you sure you want to delete"," ",e.jsxs("span",{className:"font-semibold",children:[s==null?void 0:s.first_name," ",s==null?void 0:s.last_name]}),"?"]}),v&&e.jsx(p,{color:"failure",className:"mb-4",children:v}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(n,{color:"failure",onClick:M,disabled:h,children:h?e.jsx(O,{size:"sm"}):"Yes, delete"}),e.jsx(n,{color:"gray",onClick:()=>o(!1),disabled:h,children:"No, cancel"})]})]})})]})]})};export{re as default};
