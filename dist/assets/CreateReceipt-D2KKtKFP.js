import{r as c,j as e,B as g,a0 as oe,e as ge,a3 as fe,M as k,A as de,J as ue,P as X,h as qe,i as W,aE as ce,Y as ie,a6 as y,a7 as Z,$ as Le,V as Je,aF as _e,ad as je,d as Ye,aG as Ve,b as Ge,aD as Ke,ax as Re,aj as ke,_ as b,U as Xe,a9 as Fe}from"./index-C6AV3cVN.js";import{C as Pe}from"./Card-yj7fueH8.js";import{g as We}from"./product-Ca8DWaNR.js";import{g as Ze}from"./purchaseOrder-DppPMsdd.js";import{c as es}from"./inventoryReceipt-CBnPt-rA.js";import{E as ss}from"./EnhancedProductSearchSelector-CWWD8-c1.js";import{U as ts}from"./UomSelector-CTe3hN2d.js";import{g as as,a as ls}from"./qcChecklist-Di86OBsm.js";import{E as pe}from"./EnhancedNumberInput-Bwo1E3yF.js";import"./inventoryTransaction-1UXV5RDN.js";import"./floatInventory-k_pEQeIK.js";import"./typeof-QjJsDpFa.js";import"./productUom-k6aUg6b7.js";const rs=({serialNumbers:f,onChange:U,quantity:p})=>{const[O,$]=c.useState(!1),[D,T]=c.useState(""),[F,q]=c.useState(null),n=()=>{if(!D.trim()){q("Serial number cannot be empty");return}if(f.includes(D)){q("Serial number already exists");return}if(f.length>=p){q(`Cannot add more than ${p} serial numbers`);return}U([...f,D]),T(""),q(null)},C=i=>{const m=[...f];m.splice(i,1),U(m)},E=i=>{const m=i.split(`
`).filter(Y=>Y.trim()),v=[...new Set(m)];if(v.length>p){q(`Cannot add more than ${p} serial numbers`);return}const B=new Set(f),L=v.filter(Y=>B.has(Y));if(L.length>0){q(`Duplicate serial numbers found: ${L.join(", ")}`);return}U([...f,...v]),$(!1),q(null)};return e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsxs(g,{size:"xs",color:"primary",onClick:()=>$(!0),disabled:f.length>=p,children:[e.jsx(oe,{className:"mr-1 h-4 w-4"}),"Manage Serial Numbers"]}),e.jsxs("span",{className:"text-sm text-gray-500",children:[f.length," of ",p," entered"]})]}),f.length>0&&e.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:f.map((i,m)=>e.jsxs(ge,{color:"info",className:"flex items-center",children:[i,e.jsx("button",{onClick:()=>C(m),className:"ml-2 text-xs",children:e.jsx(fe,{className:"h-3 w-3"})})]},m))}),e.jsxs(k,{show:O,onClose:()=>$(!1),children:[e.jsx(k.Header,{children:"Manage Serial Numbers"}),e.jsxs(k.Body,{children:[F&&e.jsx(de,{color:"failure",icon:ue,className:"mb-4",children:F}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"text-sm font-medium mb-2",children:"Add Individual Serial Number"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(X,{type:"text",value:D,onChange:i=>T(i.target.value),placeholder:"Enter serial number",className:"flex-1"}),e.jsx(g,{color:"primary",onClick:n,disabled:f.length>=p,children:e.jsx(oe,{className:"h-5 w-5"})})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("h3",{className:"text-sm font-medium mb-2",children:["Current Serial Numbers (",f.length," of ",p,")"]}),e.jsx("div",{className:"max-h-40 overflow-y-auto p-2 bg-gray-50 rounded",children:f.length===0?e.jsx("p",{className:"text-gray-500 text-center",children:"No serial numbers added yet"}):e.jsx("div",{className:"flex flex-wrap gap-2",children:f.map((i,m)=>e.jsxs(ge,{color:"info",className:"flex items-center",children:[i,e.jsx("button",{onClick:()=>C(m),className:"ml-2 text-xs",children:e.jsx(fe,{className:"h-3 w-3"})})]},m))})})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium mb-2",children:"Bulk Import"}),e.jsxs("p",{className:"text-xs text-gray-500 mb-2",children:["Enter one serial number per line. Maximum ",p," serial numbers."]}),e.jsx("textarea",{className:"w-full p-2 border border-gray-300 rounded",rows:5,placeholder:`SN001
SN002
SN003`,onChange:i=>{F&&q(null)}}),e.jsx("div",{className:"mt-2",children:e.jsx(g,{size:"sm",onClick:i=>{const m=i.currentTarget.previousSibling;E(m.value)},children:"Import"})})]})]}),e.jsx(k.Footer,{children:e.jsx(g,{onClick:()=>$(!1),children:"Close"})})]})]})},ns=({productId:f,onComplete:U,onCancel:p})=>{const{currentOrganization:O}=qe(),[$,D]=c.useState(!0),[T,F]=c.useState(null),[q,n]=c.useState([]),[C,E]=c.useState(""),[i,m]=c.useState(null),[v,B]=c.useState([]),[L,Y]=c.useState(""),[ee,ve]=c.useState(!1);c.useEffect(()=>{O&&f&&se()},[O,f]),c.useEffect(()=>{C?A():(m(null),B([]))},[C]);const se=async()=>{if(O){D(!0),F(null);try{const{templates:a,error:d}=await as(O.id,f);d?F(d):(n(a),a.length===1&&E(a[0].id))}catch(a){F(a.message||"An error occurred while fetching QC checklist templates")}finally{D(!1)}}},A=async()=>{if(!(!O||!C)){D(!0),F(null);try{const{template:a,error:d}=await ls(O.id,C);if(d)F(d);else if(a){m(a);const w=a.items.map(R=>({checklist_item_id:R.id,result_value:R.item_type==="boolean"?"false":"",numeric_value:null,is_passed:!1,notes:""}));B(w)}}catch(a){F(a.message||"An error occurred while fetching QC checklist template details")}finally{D(!1)}}},M=(a,d,w)=>{const R=[...v];R[a]={...R[a],[d]:w},d==="result_value"&&(i==null?void 0:i.items[a].item_type)==="numeric"&&(R[a].numeric_value=w?parseFloat(w):null);const P=i==null?void 0:i.items[a];if(P){let h=!0;if(P.pass_criteria)try{const N=JSON.parse(P.pass_criteria);switch(P.item_type){case"boolean":h=w===(N.expected||"true");break;case"numeric":const z=parseFloat(w);N.min!==void 0&&z<N.min&&(h=!1),N.max!==void 0&&z>N.max&&(h=!1);break;case"select":N.passing_values&&(h=N.passing_values.includes(w));break;case"text":N.pattern&&(h=new RegExp(N.pattern).test(w));break}}catch(N){console.error("Error parsing pass criteria:",N)}R[a].is_passed=h}B(R)},te=()=>{if(!i)return;const d=i.items.filter(h=>h.is_required).find((h,N)=>{const z=v[N];return!z.result_value&&z.result_value!==!1});if(d){F(`Please complete all required fields: ${d.name}`);return}const R=v.filter(h=>!h.is_passed).length>0?"failed":"passed",P={template_id:i.id,status:R,notes:L,results:v};U(P)};return $&&!i?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(W,{size:"xl"})}):e.jsxs(Pe,{children:[e.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:e.jsx("div",{children:e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(ce,{className:"mr-2 h-6 w-6"}),"Quality Control Inspection"]})})}),T&&e.jsx(de,{color:"failure",icon:ue,className:"mb-4",children:T}),e.jsx("div",{className:"space-y-6",children:q.length===0?e.jsx(de,{color:"info",icon:ie,children:"No QC checklist templates are assigned to this product."}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx(y,{htmlFor:"templateSelect",value:"Select Checklist Template"}),e.jsxs(Z,{id:"templateSelect",value:C,onChange:a=>E(a.target.value),required:!0,children:[e.jsx("option",{value:"",children:"Select a template..."}),q.map(a=>e.jsx("option",{value:a.id,children:a.name},a.id))]})]}),i&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsx("h3",{className:"font-medium mb-2",children:i.name}),i.description&&e.jsx("p",{className:"text-gray-700 mb-4",children:i.description}),e.jsx("div",{className:"space-y-6",children:i.items.map((a,d)=>{var w,R,P,h,N,z;return e.jsxs("div",{className:"p-4 bg-white rounded-lg shadow-sm",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"font-medium",children:[a.name,a.is_required&&e.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),a.description&&e.jsx("p",{className:"text-sm text-gray-500",children:a.description})]}),v[d]&&e.jsx(ge,{color:v[d].is_passed?"success":"failure",icon:v[d].is_passed?Le:Je,children:v[d].is_passed?"Pass":"Fail"})]}),e.jsxs("div",{className:"mt-3",children:[a.item_type==="boolean"&&e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(_e,{id:`${a.id}-true`,name:`item-${a.id}`,value:"true",checked:((w=v[d])==null?void 0:w.result_value)==="true",onChange:()=>M(d,"result_value","true")}),e.jsx(y,{htmlFor:`${a.id}-true`,className:"ml-2",children:"Yes"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(_e,{id:`${a.id}-false`,name:`item-${a.id}`,value:"false",checked:((R=v[d])==null?void 0:R.result_value)==="false",onChange:()=>M(d,"result_value","false")}),e.jsx(y,{htmlFor:`${a.id}-false`,className:"ml-2",children:"No"})]})]}),a.item_type==="numeric"&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(X,{type:"number",value:((P=v[d])==null?void 0:P.result_value)||"",onChange:S=>M(d,"result_value",S.target.value),required:a.is_required,className:"flex-1"}),a.unit&&e.jsx("span",{className:"text-gray-500",children:a.unit}),a.min_value!==null&&a.max_value!==null&&e.jsxs("span",{className:"text-xs text-gray-500",children:["(",a.min_value," - ",a.max_value,")"]})]}),a.item_type==="text"&&e.jsx(X,{type:"text",value:((h=v[d])==null?void 0:h.result_value)||"",onChange:S=>M(d,"result_value",S.target.value),required:a.is_required}),a.item_type==="select"&&a.options&&e.jsxs(Z,{value:((N=v[d])==null?void 0:N.result_value)||"",onChange:S=>M(d,"result_value",S.target.value),required:a.is_required,children:[e.jsx("option",{value:"",children:"Select an option..."}),JSON.parse(a.options).map(S=>e.jsx("option",{value:S,children:S},S))]}),e.jsxs("div",{className:"mt-2",children:[e.jsx(y,{htmlFor:`notes-${a.id}`,value:"Notes",className:"text-sm"}),e.jsx(X,{id:`notes-${a.id}`,type:"text",value:((z=v[d])==null?void 0:z.notes)||"",onChange:S=>M(d,"notes",S.target.value),placeholder:"Add any observations or notes..."})]})]})]},a.id)})})]}),e.jsxs("div",{children:[e.jsx(y,{htmlFor:"inspectionNotes",value:"Overall Inspection Notes"}),e.jsx(je,{id:"inspectionNotes",value:L,onChange:a=>Y(a.target.value),rows:3,placeholder:"Add any general notes about this inspection..."})]})]})]})}),e.jsxs("div",{className:"flex justify-end gap-4 mt-6",children:[e.jsx(g,{color:"gray",onClick:p,children:"Cancel"}),e.jsxs(g,{color:"primary",onClick:te,disabled:ee||!i||q.length===0,children:[ee?e.jsx(W,{size:"sm",className:"mr-2"}):null,"Complete Inspection"]})]})]})},ys=()=>{const f=Ye(),[U]=Ve(),{currentOrganization:p}=qe(),{user:O}=Ge(),$=U.get("purchaseOrderId"),[D,T]=c.useState(new Date),[F,q]=c.useState(""),[n,C]=c.useState([]),[E,i]=c.useState("completed"),[m,v]=c.useState(null),[B,L]=c.useState([]),[Y,ee]=c.useState([]),[ve,se]=c.useState(!0),[A,M]=c.useState(!1),[te,a]=c.useState(null),[d,w]=c.useState(!1),[R,P]=c.useState(!1),[h,N]=c.useState(-1),[z,S]=c.useState(!1),[ye,me]=c.useState(0),[ae,Ne]=c.useState(null),[j,le]=c.useState(1),[I,Qe]=c.useState(10),[H,re]=c.useState(1),$e=s=>{Ne(ae===s?null:s)},De=async(s,t)=>{if(!s||!t)return null;try{const{uoms:l,error:o}=await Fe(t,{id:s});return o||!l||l.length===0?null:l[0]}catch{return null}};c.useEffect(()=>{(async()=>{if(p){se(!0),a(null);try{const{products:t,error:l}=await We(p.id);l||L(t);const{uoms:o,error:r}=await Fe(p.id);if(r||ee(o),$){const{purchaseOrder:Q,error:G}=await Ze(p.id,$,{includeDetails:!0});if(G)a(`Error loading purchase order: ${G}`);else if(Q){v(Q);const ne=Q.items.map(async u=>{let K=u.uom;!K&&u.uom_id&&(K=await De(u.uom_id,p.id));let J=1;if(u.conversion_factor!==void 0&&u.conversion_factor!==null)J=parseFloat(u.conversion_factor);else if(u.product&&u.product.product_uoms){const he=u.product.product_uoms.find(Ie=>Ie.uom_id===u.uom_id&&Ie.product_id===u.product_id);he&&he.conversion_factor&&(J=parseFloat(he.conversion_factor))}(isNaN(J)||J<=0)&&(J=1);const Se=[];return K&&Se.push({id:`temp-${Date.now()}-${Math.random()}`,product_id:u.product_id,uom_id:u.uom_id,uom:K,conversion_factor:J,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!1,organization_id:p.id,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}),{purchaseOrderItemId:u.id,productId:u.product_id,product:u.product,quantity:u.quantity-(u.received_quantity||0),uomId:u.uom_id,unitCost:u.unit_price,expectedQuantity:u.quantity-(u.received_quantity||0),qcStatus:"passed",damagedQuantity:0,productUoms:Se,conversionFactor:J,uom:K}}),x=(await Promise.all(ne)).filter(u=>u.quantity>0);C(x),re(Math.ceil(x.length/I))}}}catch(t){a(t.message||"An error occurred while loading data")}finally{se(!1)}}})()},[p,$,I]);const Oe=()=>{const s=j*I,t=s-I;return n.slice(t,s)},V=s=>{Ne(null),le(s)},ze=s=>{const t=parseInt(s.target.value);Qe(t),le(1),re(Math.ceil(n.length/t))},be=()=>{const s=[...n,{productId:"",quantity:1,uomId:"",unitCost:0,qcStatus:"passed",damagedQuantity:0,productUoms:[],conversionFactor:1}];C(s),re(Math.ceil(s.length/I)),(n.length%I===0||j===H)&&le(Math.ceil(s.length/I))},Ee=s=>{const t=(j-1)*I+s,l=[...n];l.splice(t,1),C(l);const o=Math.max(1,Math.ceil(l.length/I));re(o),j>1&&j>o&&le(o)},_=(s,t,l)=>{const o=(j-1)*I+s,r=[...n];r[o]={...r[o],[t]:l},C(r)},Me=async(s,t,l)=>{const o=(j-1)*I+s,r=[...n];r[o]={...r[o],productId:t,product:l,uomId:"",productUoms:[],conversionFactor:1},C(r)},Ae=s=>{const t=(j-1)*I+s;N(t),w(!0)},He=s=>{const t=[...n],l=t[h];l.qcStatus=s.status,s.notes&&(l.damageReason=s.notes),C(t),P(!1)},Ue=s=>{const t=(j-1)*I+s,l=[...n],o=l[t];o.notReceived=!o.notReceived,o.notReceived?o.quantity=0:o.expectedQuantity!==void 0?o.quantity=o.expectedQuantity:o.quantity=1,C(l)},Te=()=>{if(!p||!O)return a("Organization or user information is missing"),!1;if(n.length===0)return a("Please add at least one item"),!1;let s=!1;for(let t=0;t<n.length;t++){const l=n[t];if(!l.productId)return a(`Item #${t+1}: Please select a product`),!1;if(!l.uomId)return a(`Item #${t+1}: Please select a unit of measurement`),!1;if(l.quantity<0)return a(`Item #${t+1}: Quantity cannot be negative`),!1;l.quantity>0&&(s=!0)}return s?!0:(a("At least one item must have a quantity greater than 0"),!1)},Be=async s=>{s.preventDefault(),Te()&&(E==="completed"?S(!0):await we())},we=async()=>{if(p){M(!0),a(null),me(0);try{const s=setInterval(()=>{me(r=>{const Q=r+10;return Q>90?90:Q})},300),t=n.map(r=>{const Q=typeof r.conversionFactor=="number"?r.conversionFactor:parseFloat(String(r.conversionFactor));return{...r,conversionFactor:!isNaN(Q)&&Q>0?Q:1}}),{receipt:l,error:o}=await es(p.id,{purchaseOrderId:m==null?void 0:m.id,receiptDate:D.toISOString(),notes:F,status:E,items:t.map(r=>({purchaseOrderItemId:r.purchaseOrderItemId,productId:r.productId,quantity:r.quantity,uomId:r.uomId,unitCost:r.unitCost,expectedQuantity:r.expectedQuantity,qcStatus:r.qcStatus,damagedQuantity:r.damagedQuantity,damageReason:r.damageReason,lotNumber:r.lotNumber,expiryDate:r.expiryDate?new Date(r.expiryDate).toISOString():void 0,serialNumbers:r.serialNumbers,conversionFactor:r.conversionFactor}))});clearInterval(s),me(100),o?a(o):l&&setTimeout(()=>{f(`/inventory/receipts/${l.id}`)},500)}catch(s){a(s.message||"An error occurred while creating the inventory receipt")}finally{M(!1)}}},Ce=()=>{f($?`/purchases/orders/${$}`:"/inventory/receipts")};return ve?e.jsx("div",{className:"container mx-auto px-4 py-8 flex justify-center",children:e.jsx(W,{size:"xl"})}):e.jsxs("div",{className:"container mx-auto px-4 py-4",children:[e.jsxs(Pe,{className:"shadow-sm",children:[e.jsxs("div",{className:"flex justify-between items-center mb-5 border-b pb-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-xl font-bold flex items-center text-gray-800",children:[e.jsx(ce,{className:"mr-2 h-6 w-6 text-primary-600"}),m?`Receive Items for PO: ${m.order_number}`:"Create Inventory Receipt"]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:m?`From ${m.supplier_name}`:"Record items received into inventory"})]}),e.jsxs(g,{color:"gray",size:"sm",onClick:Ce,className:"px-4",children:[e.jsx(Ke,{className:"mr-1 h-4 w-4"}),"Cancel"]})]}),te&&e.jsx(de,{color:"failure",icon:ue,className:"mb-4",children:te}),e.jsxs("form",{onSubmit:Be,children:[e.jsxs("div",{className:"grid grid-cols-12 gap-6 mb-6",children:[e.jsxs("div",{className:"col-span-12 md:col-span-3",children:[e.jsx(y,{htmlFor:"receiptDate",value:"Receipt Date",className:"font-medium mb-1.5 block"}),e.jsx(Re,{id:"receiptDate",value:D,onSelectedDateChanged:T,required:!0,className:"w-full"})]}),e.jsxs("div",{className:"col-span-12 md:col-span-3",children:[e.jsx(y,{htmlFor:"status",value:"Status",className:"font-medium mb-1.5 block"}),e.jsxs(Z,{id:"status",value:E,onChange:s=>i(s.target.value),required:!0,className:"w-full",children:[e.jsx("option",{value:"draft",children:"Draft"}),e.jsx("option",{value:"completed",children:"Completed"})]})]}),e.jsxs("div",{className:"col-span-12 md:col-span-6",children:[e.jsx(y,{htmlFor:"notes",value:"Notes",className:"font-medium mb-1.5 block"}),e.jsx(je,{id:"notes",value:F,onChange:s=>q(s.target.value),rows:2,placeholder:"Enter any notes about this receipt...",className:"w-full"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h2",{className:"text-lg font-semibold text-gray-800 flex items-center",children:[e.jsx(ke,{className:"mr-2 h-5 w-5 text-primary-600"}),"Items to Receive"]}),!m&&n.length===0&&e.jsxs(g,{color:"primary",size:"sm",onClick:be,className:"px-4 py-2 font-medium",children:[e.jsx(oe,{className:"mr-2 h-4 w-4"}),"Add First Item"]})]}),n.length===0?e.jsxs("div",{className:"p-6 bg-gray-50 rounded-lg text-center border border-gray-200",children:[e.jsx(ke,{className:"mx-auto h-10 w-10 text-gray-400 mb-2"}),e.jsx("p",{className:"text-gray-600",children:m?"No items to receive from this purchase order":'Click "Add First Item" to add products to this receipt'})]}):e.jsxs("div",{className:"overflow-x-auto border rounded-lg",children:[e.jsxs(b,{className:"min-w-full table-fixed",hoverable:!0,children:[e.jsxs(b.Head,{className:"bg-gray-50",children:[e.jsx(b.HeadCell,{className:"w-3/10 py-3",children:"Product"}),e.jsx(b.HeadCell,{className:"w-2/10 py-3",children:"Received Qty"}),e.jsx(b.HeadCell,{className:"w-2/10 py-3",children:"UoM"}),e.jsx(b.HeadCell,{className:"w-1.5/10 py-3",children:"Unit Cost"}),e.jsx(b.HeadCell,{className:"w-0.5/10 py-3 text-center",children:"Not Received"}),e.jsx(b.HeadCell,{className:"w-1/10 py-3",children:"Actions"})]}),e.jsx(b.Body,{className:"divide-y",children:Oe().map((s,t)=>{var Q,G,ne,xe;const l=s.notReceived?"not-received":s.expectedQuantity!==void 0?s.quantity===0?"not-received":s.quantity<s.expectedQuantity?"partial":s.quantity>s.expectedQuantity?"over":"full":"default",o={"not-received":"bg-gray-50 text-gray-500",partial:"bg-yellow-50",over:"bg-blue-50",full:"bg-green-50",default:""},r={"not-received":"◯ Not Received",partial:"◑ Partial",over:"⦿ Over",full:"● Complete",default:""};return[e.jsxs(b.Row,{className:o[l],children:[e.jsx(b.Cell,{className:"whitespace-normal py-3 align-top",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium text-gray-800",children:((Q=s.product)==null?void 0:Q.name)||`Product #${t+1}`}),((G=s.product)==null?void 0:G.sku)&&e.jsxs("span",{className:"text-xs text-gray-500 mt-0.5",children:["SKU: ",s.product.sku]}),s.expectedQuantity!==void 0&&e.jsx("span",{className:"text-xs text-gray-600 mt-1 flex items-center",children:e.jsxs("span",{className:"bg-gray-100 px-2 py-0.5 rounded",children:["Expected: ",s.expectedQuantity," ",((xe=(ne=s.productUoms[0])==null?void 0:ne.uom)==null?void 0:xe.name)||""]})}),l!=="default"&&e.jsx("span",{className:`text-xs mt-1.5 ${l==="full"?"text-green-600":l==="partial"?"text-yellow-600":l==="over"?"text-blue-600":"text-gray-500"}`,children:r[l]}),!m&&e.jsx("div",{className:"mt-2",children:e.jsx(ss,{products:B,selectedProductId:s.productId,onProductChange:(x,u)=>Me(t,x,u),placeholder:"Select product..."})})]})}),e.jsx(b.Cell,{className:"align-middle",children:e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(y,{htmlFor:`quantity-${t}`,value:"Quantity",className:"text-xs text-gray-500 mb-1"}),e.jsx(pe,{value:s.quantity,onChange:x=>_(t,"quantity",parseFloat(x.target.value)||0),onBlur:x=>{(x.target.value===""||parseFloat(x.target.value)<0)&&_(t,"quantity",0)},min:"0",step:"0.01",required:!0,sizing:"sm",disabled:s.notReceived,className:s.notReceived?"bg-gray-100":"",autoSelect:!0,preventScrollChange:!0})]})}),e.jsx(b.Cell,{className:"align-middle",children:e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(y,{htmlFor:`uom-${t}`,value:"Unit of Measure",className:"text-xs text-gray-500 mb-1"}),e.jsx(ts,{productId:s.productId,value:s.uomId,onChange:x=>{_(t,"uomId",x)},onUomChange:x=>{_(t,"uomId",x.uom_id),_(t,"uom",x.uom);const u=parseFloat(x.conversion_factor);!isNaN(u)&&u>0&&_(t,"conversionFactor",u)},preloadedUoms:s.productUoms,disabled:m!==null||s.notReceived,className:`text-xs ${s.notReceived?"bg-gray-100":""}`},`uom-selector-${s.productId}-${t}`)]})}),e.jsx(b.Cell,{className:"align-middle",children:e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(y,{htmlFor:`unitCost-${t}`,value:"Unit Cost",className:"text-xs text-gray-500 mb-1"}),e.jsx(pe,{value:s.unitCost,onChange:x=>_(t,"unitCost",parseFloat(x.target.value)||0),onBlur:x=>{(x.target.value===""||parseFloat(x.target.value)<0)&&_(t,"unitCost",.01)},min:"0",step:"0.01",required:!0,sizing:"sm",disabled:s.notReceived,className:s.notReceived?"bg-gray-100":"",autoSelect:!0,preventScrollChange:!0})]})}),e.jsx(b.Cell,{className:"text-center align-middle",children:e.jsxs("div",{className:"flex flex-col items-center justify-center h-full",children:[e.jsx(y,{htmlFor:`not-received-${t}`,className:"sr-only",children:"Not Received"}),e.jsxs("div",{className:"relative inline-flex items-center",children:[e.jsx(Xe,{id:`not-received-${t}`,checked:s.notReceived||!1,onChange:()=>Ue(t),className:"focus:ring-primary-500"}),e.jsx("span",{className:"ml-2 text-xs text-gray-500 whitespace-nowrap",children:"Not received"})]})]})}),e.jsx(b.Cell,{className:"align-middle",children:e.jsxs("div",{className:"flex gap-1.5 justify-center",children:[e.jsx(g,{size:"xs",color:ae===t?"light":"gray",onClick:()=>$e(t),title:ae===t?"Hide details":"Show details",className:"px-2 py-1",children:e.jsx(ie,{className:"h-4 w-4"})}),e.jsx(g,{size:"xs",color:"light",onClick:()=>Ae(t),disabled:s.notReceived,title:"Quality Control",className:"px-2 py-1",children:e.jsx("span",{className:"text-xs font-medium",children:"QC"})}),!m&&e.jsx(g,{size:"xs",color:"failure",onClick:()=>Ee(t),title:"Remove item",className:"px-2 py-1",children:e.jsx(fe,{className:"h-4 w-4"})})]})})]},`item-${t}`),ae===t&&e.jsx(b.Row,{className:"bg-gray-50 border-t-0",children:e.jsx(b.Cell,{colSpan:6,children:e.jsxs("div",{className:"p-4 text-sm border-t border-gray-200",children:[e.jsxs("div",{className:"flex items-center mb-2 text-primary-600 border-b pb-2",children:[e.jsx(ie,{className:"mr-2 h-4 w-4"}),e.jsx("span",{className:"font-medium",children:"Additional Details"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"bg-white p-3 rounded shadow-sm",children:[e.jsx(y,{htmlFor:`lotNumber-${t}`,value:"Lot/Batch Number",className:"text-xs font-medium text-gray-700 mb-1 block"}),e.jsx(X,{id:`lotNumber-${t}`,type:"text",value:s.lotNumber||"",onChange:x=>_(t,"lotNumber",x.target.value),placeholder:"Enter lot or batch number",sizing:"sm",disabled:s.notReceived,className:"w-full"})]}),e.jsxs("div",{className:"bg-white p-3 rounded shadow-sm",children:[e.jsx(y,{htmlFor:`expiryDate-${t}`,value:"Expiry Date",className:"text-xs font-medium text-gray-700 mb-1 block"}),e.jsx(Re,{id:`expiryDate-${t}`,value:s.expiryDate?new Date(s.expiryDate):void 0,onSelectedDateChanged:x=>_(t,"expiryDate",x.toISOString()),placeholder:"Select expiry date",disabled:s.notReceived,className:"w-full"})]}),e.jsxs("div",{className:"bg-white p-3 rounded shadow-sm",children:[e.jsx(y,{htmlFor:`qcStatus-${t}`,value:"QC Status",className:"text-xs font-medium text-gray-700 mb-1 block"}),e.jsxs(Z,{id:`qcStatus-${t}`,value:s.qcStatus,onChange:x=>_(t,"qcStatus",x.target.value),sizing:"sm",disabled:s.notReceived,className:"w-full",children:[e.jsx("option",{value:"passed",children:"Passed"}),e.jsx("option",{value:"failed",children:"Failed"}),e.jsx("option",{value:"quarantine",children:"Quarantine"})]})]}),e.jsxs("div",{className:"md:col-span-3 mt-1 bg-white p-3 rounded shadow-sm",children:[e.jsx(y,{value:"Serial Numbers",className:"text-xs font-medium text-gray-700 mb-1 block"}),e.jsx(rs,{serialNumbers:s.serialNumbers||[],onChange:x=>_(t,"serialNumbers",x),quantity:s.quantity,disabled:s.notReceived})]})]})]})})},`expanded-${t}`)]}).flat()})]}),e.jsxs("div",{className:"flex flex-wrap items-center justify-between border-t border-gray-200 bg-gray-50 px-4 py-3",children:[e.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-700",children:[e.jsx("span",{children:"Show"}),e.jsxs("select",{value:I,onChange:ze,className:"rounded-md border-gray-300 py-1 text-sm focus:border-primary-500 focus:ring-primary-500",children:[e.jsx("option",{value:"5",children:"5"}),e.jsx("option",{value:"10",children:"10"}),e.jsx("option",{value:"25",children:"25"}),e.jsx("option",{value:"50",children:"50"}),e.jsx("option",{value:"100",children:"100"})]}),e.jsx("span",{children:"items per page"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsxs("span",{className:"text-sm text-gray-700 mr-3",children:["Showing ",n.length>0?(j-1)*I+1:0," to ",Math.min(j*I,n.length)," of ",n.length," items"]}),e.jsxs("div",{className:"flex space-x-1",children:[e.jsx(g,{color:"light",size:"xs",onClick:()=>V(1),disabled:j===1,className:"px-2",children:"First"}),e.jsx(g,{color:"light",size:"xs",onClick:()=>V(j-1),disabled:j===1,className:"px-2",children:"Prev"}),e.jsx("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(5,H)},(s,t)=>{let l;return H<=5||j<=3?l=t+1:j>=H-2?l=H-4+t:l=j-2+t,e.jsx(g,{color:j===l?"primary":"light",size:"xs",onClick:()=>V(l),className:"px-3",children:l},l)})}),e.jsx(g,{color:"light",size:"xs",onClick:()=>V(j+1),disabled:j===H,className:"px-2",children:"Next"}),e.jsx(g,{color:"light",size:"xs",onClick:()=>V(H),disabled:j===H,className:"px-2",children:"Last"})]})]})]}),!m&&e.jsx("div",{className:"flex justify-center mt-4 mb-1 py-2",children:e.jsxs(g,{color:"primary",size:"sm",onClick:be,className:"border px-4 py-2 font-medium",outline:!0,children:[e.jsx(oe,{className:"mr-2 h-4 w-4"}),"Add Another Item"]})})]})]}),n.length>0&&e.jsxs("div",{className:"mb-6 mt-2 bg-gray-50 p-3 rounded-lg border border-gray-200",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Receipt Status Legend:"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2 text-sm",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-green-500 mr-2"}),e.jsx("span",{children:"Complete"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-yellow-500 mr-2"}),e.jsx("span",{children:"Partial"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-blue-500 mr-2"}),e.jsx("span",{children:"Over Received"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-gray-500 mr-2"}),e.jsx("span",{children:"Not Received"})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 mt-6 pt-4 border-t",children:[e.jsx(g,{color:"gray",size:"md",onClick:Ce,className:"px-5 py-2.5 font-medium",children:"Cancel"}),e.jsxs(g,{type:"submit",color:"primary",size:"md",disabled:A||n.length===0,className:"px-5 py-2.5 font-medium",children:[A?e.jsx(W,{size:"sm",className:"mr-2"}):null,E==="draft"?"Save as Draft":"Complete Receipt"]})]})]})]}),e.jsxs(k,{show:d,onClose:()=>w(!1),size:"lg",children:[e.jsx(k.Header,{className:"border-b bg-gray-50",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-primary-600 mr-2",children:e.jsx(ce,{className:"h-5 w-5"})}),"Quality Control Details"]})}),e.jsx(k.Body,{className:"p-4",children:h>=0&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(y,{htmlFor:"qcStatus",value:"QC Status",className:"font-medium mb-1.5 block"}),e.jsxs(Z,{id:"qcStatus",value:n[h].qcStatus,onChange:s=>_(h,"qcStatus",s.target.value),className:"w-full",children:[e.jsx("option",{value:"passed",children:"Passed"}),e.jsx("option",{value:"failed",children:"Failed"}),e.jsx("option",{value:"quarantine",children:"Quarantine"})]})]}),e.jsxs("div",{children:[e.jsx(y,{htmlFor:"damagedQuantity",value:"Damaged Quantity",className:"font-medium mb-1.5 block"}),e.jsx(pe,{value:n[h].damagedQuantity,onChange:s=>_(h,"damagedQuantity",parseFloat(s.target.value)||0),onBlur:s=>{(s.target.value===""||parseFloat(s.target.value)<0)&&_(h,"damagedQuantity",0)},min:"0",step:"0.01",className:"w-full",autoSelect:!0,preventScrollChange:!0}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Enter the quantity of damaged or rejected items"})]}),e.jsxs("div",{children:[e.jsx(y,{htmlFor:"damageReason",value:"Damage Reason / Notes",className:"font-medium mb-1.5 block"}),e.jsx(je,{id:"damageReason",value:n[h].damageReason||"",onChange:s=>_(h,"damageReason",s.target.value),rows:3,placeholder:"Describe the damage or quality issue...",className:"w-full"})]})]})}),e.jsx(k.Footer,{className:"border-t bg-gray-50",children:e.jsx("div",{className:"flex justify-end w-full",children:e.jsx(g,{color:"primary",onClick:()=>w(!1),className:"px-4 py-2 font-medium",children:"Save & Close"})})})]}),e.jsxs(k,{show:R,onClose:()=>P(!1),size:"4xl",children:[e.jsx(k.Header,{children:"Quality Control Inspection"}),e.jsx(k.Body,{children:h>=0&&n[h]&&e.jsx(ns,{productId:n[h].productId,onComplete:He,onCancel:()=>P(!1)})})]}),e.jsxs(k,{show:z,onClose:()=>S(!1),size:"lg",children:[e.jsx(k.Header,{className:"border-b bg-gray-50",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-primary-600 mr-2",children:e.jsx(ce,{className:"h-5 w-5"})}),"Complete Inventory Receipt"]})}),e.jsx(k.Body,{className:"p-5",children:e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0 bg-blue-100 p-2 rounded-full text-blue-600 mr-3",children:e.jsx(ie,{className:"h-6 w-6"})}),e.jsxs("p",{className:"text-gray-700",children:["You are about to complete this inventory receipt with ",n.length," item",n.length!==1?"s":"",". This will update your inventory levels and cannot be easily undone."]})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg border border-gray-200",children:[e.jsx("h4",{className:"font-medium text-sm mb-3 text-gray-700",children:"Receipt Summary:"}),(()=>{const s=n.filter(r=>r.expectedQuantity&&r.quantity===r.expectedQuantity).length,t=n.filter(r=>r.expectedQuantity&&r.quantity>0&&r.quantity<r.expectedQuantity).length,l=n.filter(r=>r.quantity===0).length,o=n.filter(r=>r.expectedQuantity&&r.quantity>r.expectedQuantity).length;return e.jsxs("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[s>0&&e.jsxs("div",{className:"flex items-center bg-white p-2 rounded shadow-sm",children:[e.jsx("div",{className:"w-4 h-4 rounded-full bg-green-500 mr-2 flex-shrink-0"}),e.jsxs("span",{children:[s," item",s!==1?"s":""," fully received"]})]}),t>0&&e.jsxs("div",{className:"flex items-center bg-white p-2 rounded shadow-sm",children:[e.jsx("div",{className:"w-4 h-4 rounded-full bg-yellow-500 mr-2 flex-shrink-0"}),e.jsxs("span",{children:[t," item",t!==1?"s":""," partially received"]})]}),l>0&&e.jsxs("div",{className:"flex items-center bg-white p-2 rounded shadow-sm",children:[e.jsx("div",{className:"w-4 h-4 rounded-full bg-gray-500 mr-2 flex-shrink-0"}),e.jsxs("span",{children:[l," item",l!==1?"s":""," not received"]})]}),o>0&&e.jsxs("div",{className:"flex items-center bg-white p-2 rounded shadow-sm",children:[e.jsx("div",{className:"w-4 h-4 rounded-full bg-blue-500 mr-2 flex-shrink-0"}),e.jsxs("span",{children:[o," item",o!==1?"s":""," over received"]})]})]})})()]}),e.jsxs("div",{className:"bg-yellow-50 p-3 rounded-lg border border-yellow-200 flex items-center text-yellow-700",children:[e.jsx(ue,{className:"h-5 w-5 mr-2 flex-shrink-0"}),e.jsx("p",{className:"text-sm",children:"Are you sure you want to proceed?"})]}),A&&e.jsxs("div",{className:"mt-5",children:[e.jsxs("div",{className:"mb-2 text-sm font-medium flex items-center justify-between",children:[e.jsx("span",{children:"Processing inventory receipt..."}),e.jsxs("span",{className:"text-primary-600",children:[ye,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:e.jsx("div",{className:"bg-primary-600 h-2.5 rounded-full transition-all duration-300",style:{width:`${ye}%`}})})]})]})}),e.jsx(k.Footer,{className:"border-t bg-gray-50",children:e.jsxs("div",{className:"flex justify-end gap-3 w-full",children:[e.jsx(g,{color:"gray",size:"md",onClick:()=>S(!1),disabled:A,className:"px-4 py-2 font-medium",children:"Cancel"}),e.jsxs(g,{color:"primary",size:"md",onClick:we,disabled:A,className:"px-4 py-2 font-medium",children:[A?e.jsx(W,{size:"sm",className:"mr-2"}):null,"Complete Receipt"]})]})})]})]})};export{ys as default};
