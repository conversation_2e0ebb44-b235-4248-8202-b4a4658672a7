import{ab as ne,d as ie,h as ce,b as ue,r as f,s as le,j as e,i as B,A as T,J as A,B as b,aD as M,aj as de,a6 as _,a7 as pe,P as me,ad as he,_ as d,t as fe,a9 as xe}from"./index-C6AV3cVN.js";import{C as ge}from"./Card-yj7fueH8.js";import{a as ve}from"./purchaseRequest-Bff56uzH.js";import{d as je}from"./purchaseOrder-DppPMsdd.js";import{g as ye,a as Ie}from"./supplier-BJDz25mb.js";import{getProductUoms as Ne,convertQuantity as Pe}from"./productUom-k6aUg6b7.js";import{g as Ce,a as Q,b as $}from"./SimpleSupplierProductModal-3fg5k0TC.js";import{a as we}from"./formatters-Cypx7G-j.js";import{u as Se}from"./currencyFormatter-BsFWv3sX.js";import{U as be}from"./UomSelector-CTe3hN2d.js";import{P as _e}from"./PurchaseOrderSupplierProductButton-Diy-5mp2.js";import{E as z}from"./EnhancedNumberInput-Bwo1E3yF.js";import"./product-Ca8DWaNR.js";const Qe=()=>{var k;const{id:j}=ne(),I=ie(),{currentOrganization:p}=ce(),{user:L}=ue(),U=Se(),[m,W]=f.useState(""),[q,J]=f.useState(""),[F,V]=f.useState(""),[x,v]=f.useState([]),[N,G]=f.useState(null),[E,K]=f.useState([]),[X,Y]=f.useState([]),[Z,D]=f.useState(!0),[O,R]=f.useState(!1),[P,g]=f.useState(null);f.useEffect(()=>{(async()=>{if(!(!p||!j)){D(!0),g(null);try{const{purchaseRequest:s,error:t}=await ve(p.id,j);if(t){g(t);return}if(!s){g("Purchase request not found");return}G(s);const o=s.items.map(l=>l.product_id),n=o.map(l=>Ne(l,p.id)),i=await Promise.all(n),a=o.reduce((l,w,S)=>(l[w]=i[S].productUoms||[],l),{}),c=s.items.map(l=>{var w,S;return{requestItemId:l.id,productId:l.product_id,productName:((w=l.product)==null?void 0:w.name)||"Unknown Product",quantity:l.quantity,originalQuantity:l.quantity,originalUomId:l.uom_id,originalUomName:((S=l.uom)==null?void 0:S.name)||"Unknown Unit",uomId:l.uom_id,unitPrice:0,conversionFactor:l.conversion_factor||1,notes:l.notes,productUoms:a[l.product_id]||[]}});v(c);const{suppliers:u,error:h}=await ye(p.id);h?console.error("Error fetching suppliers:",h):K(u);const{uoms:y,error:H}=await xe(p.id);H?console.error("Error fetching UoMs:",H):Y(y)}catch(s){console.error("Error fetching data:",s),g(s.message||"An error occurred while fetching data")}finally{D(!1)}}})()},[p,j]),f.useEffect(()=>{m&&x.length>0&&ee(m)},[m]);const ee=async r=>{var t;const s=x.map(o=>o.productId);if(s.length!==0)try{const{supplierProducts:o,error:n}=await Ce(r,s);if(n){console.error("Error fetching supplier products:",n);return}if(o.length>0){const i={};for(const a of o)i[a.product_id]={price:a.unit_price||0,uomId:a.uom_id||null,uomName:((t=a.uom)==null?void 0:t.name)||a.uom_name||null,conversionFactor:a.conversion_factor||1},a.uom_id;s.forEach(a=>{i[a]||(i[a]={price:0,uomId:null,uomName:null,conversionFactor:1})}),v(a=>a.map(u=>{const h=i[u.productId];if(h){const y={...u,unitPrice:h.price};return h.uomId&&(y.uomId=h.uomId),y}return u}))}else{const{data:i}=await le.from("products").select("id, unit_price").in("id",s);if(i&&i.length>0){const a=i.reduce((c,u)=>(u.unit_price&&(c[u.id]=u.unit_price),c),{});v(c=>c.map(u=>a[u.productId]?{...u,unitPrice:a[u.productId]}:u))}}}catch(o){console.error("Error updating prices for supplier:",o)}},C=async(r,s,t)=>{if(v(o=>{const n=[...o];return n[r]={...n[r],[s]:t},n}),s==="unitPrice"&&m&&p){const o=x[r];try{if(t>0){const{success:n,supplierProduct:i,error:a}=await Q(m,o.productId,t,p.id,o.uomId);n||console.error("Error adding/updating supplier product:",a)}else if(t===0){const{supplierProduct:n}=await $(m,o.productId);if(n&&n.unit_price)if(window.confirm(`This product is currently supplied by this supplier at a price of ${n.unit_price}. Setting the price to 0 indicates this product is not supplied by this supplier. Do you want to continue?`)){const{success:a}=await Q(m,o.productId,0,p.id,o.uomId)}else v(a=>{const c=[...a];return c[r]={...c[r],unitPrice:n.unit_price||0},c})}}catch(n){console.error("Error in handleItemChange when adding supplier product:",n)}}},re=async(r,s)=>{if(!p)return;const t=x[r];try{let o=1;if(t.productUoms&&t.productUoms.length>0){const a=t.productUoms.find(c=>c.uom_id===s);a&&(o=Number(a.conversion_factor))}const{convertedQuantity:n,error:i}=await Pe(t.productId,t.quantity,t.uomId,s,p.id);if(i)return;if(v(a=>{const c=[...a];return c[r]={...c[r],uomId:s,quantity:n||t.quantity,conversionFactor:o},c}),m)try{const{supplierProduct:a}=await $(m,t.productId);let c=null;a!=null&&a.unit_price?c=a.unit_price:c=0,c!==null&&v(u=>{const h=[...u];return h[r]={...h[r],unitPrice:c||0},h})}catch{}}catch{}},te=async r=>{if(!p)return;const s=x[r];let t=1;try{if(s.productUoms&&s.productUoms.length>0){const o=s.productUoms.find(n=>n.uom_id===s.originalUomId);o&&(t=Number(o.conversion_factor))}}catch{}v(o=>{const n=[...o];return n[r]={...n[r],quantity:n[r].originalQuantity,uomId:n[r].originalUomId,conversionFactor:t},n})},se=()=>x.reduce((r,s)=>{var i,a;const t=(a=(i=s.productUoms)==null?void 0:i.find(c=>c.uom_id===s.uomId))==null?void 0:a.conversion_factor,o=Number(t||s.conversionFactor||1),n=s.quantity*o*s.unitPrice;return r+n},0),oe=async r=>{if(r.preventDefault(),!p||!L||!j){g("Missing required data");return}let s=m;if(!s){const{supplier:t,error:o}=await Ie(p.id);if(t)s=t.id;else{g("No supplier available. Please contact support.");return}}for(let t=0;t<x.length;t++){const o=x[t];if(o.quantity<=0){g(`Please enter a valid quantity for item #${t+1}`);return}if(o.unitPrice<=0){g(`Please enter a valid unit price for item #${t+1}`);return}}R(!0),g(null);try{const t={supplierId:s,expectedDeliveryDate:q?new Date(q).toISOString():void 0,notes:F||void 0,items:x.map(i=>{var u,h;const a=(h=(u=i.productUoms)==null?void 0:u.find(y=>y.uom_id===i.uomId))==null?void 0:h.conversion_factor,c=Number(a||i.conversionFactor||1);return{requestItemId:i.requestItemId,quantity:i.quantity,uomId:i.uomId,unitPrice:i.unitPrice,conversionFactor:c}})},{purchaseOrder:o,error:n}=await je(p.id,j,t);n?g(n):o&&I(`/purchases/orders/${o.id}`)}catch(t){g(t.message||"An error occurred while creating the purchase order")}finally{R(!1)}},ae=()=>{I(`/purchases/requests/${j}`)};return Z?e.jsx("div",{className:"container mx-auto px-4 py-8 flex justify-center",children:e.jsx(B,{size:"xl"})}):P&&!N?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(T,{color:"failure",icon:A,children:[e.jsx("h3",{className:"font-medium",children:"Error"}),e.jsx("p",{children:P}),e.jsx("div",{className:"mt-4",children:e.jsxs(b,{color:"gray",onClick:()=>I("/purchases/requests"),children:[e.jsx(M,{className:"mr-2 h-5 w-5"}),"Back to Purchase Requests"]})})]})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(ge,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold flex items-center",children:[e.jsx(de,{className:"mr-2 h-6 w-6"}),"Create Purchase Order"]}),e.jsxs("p",{className:"text-gray-500",children:["Creating purchase order from request: ",N==null?void 0:N.request_number]})]}),e.jsxs(b,{color:"gray",onClick:ae,children:[e.jsx(M,{className:"mr-2 h-5 w-5"}),"Cancel"]})]}),P&&e.jsx(T,{color:"failure",icon:A,className:"mb-4",children:P}),e.jsxs("form",{onSubmit:oe,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 block",children:[e.jsx(_,{htmlFor:"supplier_id",value:"Supplier (Optional)"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Leave empty to use default supplier, or select a specific supplier"})]}),e.jsxs(pe,{id:"supplier_id",value:m,onChange:r=>W(r.target.value),children:[e.jsx("option",{value:"",children:"Use Default Supplier"}),E.filter(r=>!r.is_default).map(r=>e.jsx("option",{value:r.id,children:r.name},r.id))]}),!m&&e.jsxs("p",{className:"text-xs text-blue-600 mt-1",children:['ℹ️ Using "',((k=E.find(r=>r.is_default))==null?void 0:k.name)||"Default Supplier",'" - you can change the supplier later']})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(_,{htmlFor:"expected_delivery_date",value:"Expected Delivery Date"})}),e.jsx(me,{id:"expected_delivery_date",type:"date",value:q,onChange:r=>J(r.target.value),min:new Date().toISOString().split("T")[0]})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(_,{htmlFor:"notes",value:"Notes"})}),e.jsx(he,{id:"notes",value:F,onChange:r=>V(r.target.value),placeholder:"Enter any additional notes",rows:3})]}),e.jsxs("div",{children:[e.jsx("div",{className:"flex justify-between items-center mb-2",children:e.jsx(_,{value:"Items"})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(d,{children:[e.jsxs(d.Head,{children:[e.jsx(d.HeadCell,{children:"Product"}),e.jsx(d.HeadCell,{children:"Quantity"}),e.jsx(d.HeadCell,{children:"Unit"}),e.jsx(d.HeadCell,{children:"Unit Price"}),e.jsx(d.HeadCell,{children:"Total"}),e.jsx(d.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(d.Body,{className:"divide-y",children:x.map((r,s)=>e.jsxs(d.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(d.Cell,{className:"font-medium",children:e.jsx("button",{onClick:()=>I(`/products/details/${r.productId}`),className:"text-primary-600 hover:text-primary-800 hover:underline focus:outline-none",title:"View product details",children:r.productName})}),e.jsx(d.Cell,{children:e.jsx(z,{min:"0.01",step:"0.01",value:r.quantity,onChange:t=>C(s,"quantity",parseFloat(t.target.value)||0),onBlur:t=>{(t.target.value===""||parseFloat(t.target.value)<=0)&&C(s,"quantity",1)},required:!0,className:"w-24",autoSelect:!0,preventScrollChange:!0})}),e.jsx(d.Cell,{children:e.jsx(be,{productId:r.productId,value:r.uomId,onChange:t=>re(s,t),filter:"purchasing",className:"w-32",preloadedUoms:r.productUoms})}),e.jsx(d.Cell,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(z,{min:"0.01",step:"0.01",value:r.unitPrice,onChange:t=>C(s,"unitPrice",parseFloat(t.target.value)||0),onBlur:t=>{(t.target.value===""||parseFloat(t.target.value)<0)&&C(s,"unitPrice",.01)},required:!0,className:"w-24",autoSelect:!0,preventScrollChange:!0}),m&&e.jsx(_e,{supplierId:m,product:{id:r.productId,name:r.productName},onSuccess:(t,o,n=1)=>{const i=[...x];i[s]={...i[s],unitPrice:t,conversionFactor:n,...o&&{uomId:o}},v(i)},currentUnitPrice:r.unitPrice,currentUomId:r.uomId})]})}),e.jsx(d.Cell,{children:(()=>{var i,a,c;const t=(a=(i=r.productUoms)==null?void 0:i.find(u=>u.uom_id===r.uomId))==null?void 0:a.conversion_factor,o=Number(t||r.conversionFactor||1),n=r.quantity*o*r.unitPrice;return e.jsxs(e.Fragment,{children:[U(n),e.jsxs("div",{className:"text-xs text-gray-500",children:[we(r.quantity)," ",((c=X.find(u=>u.id===r.uomId))==null?void 0:c.code)||"unit"," × ",U(r.unitPrice)]})]})})()}),e.jsx(d.Cell,{children:e.jsx(b,{color:"light",size:"xs",onClick:()=>te(s),title:"Reset to original quantity and unit",children:e.jsx(fe,{className:"h-4 w-4"})})})]},s))})]})}),e.jsx("div",{className:"flex justify-end mt-4",children:e.jsxs("div",{className:"text-lg font-semibold",children:["Total: ",U(se())]})})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(b,{type:"submit",color:"primary",disabled:O,children:O?e.jsxs(e.Fragment,{children:[e.jsx(B,{size:"sm",className:"mr-2"}),"Creating..."]}):"Create Purchase Order"})})]})]})})};export{Qe as default};
