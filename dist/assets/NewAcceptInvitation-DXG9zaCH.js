import{aG as K,d as M,r as d,j as e,i as P,A as X,B as U,a6 as b,P as y,aX as Y,K as q,c4 as J,s as l}from"./index-C6AV3cVN.js";import{C as S}from"./Card-yj7fueH8.js";const ee=()=>{const[C]=K(),N=M(),_=C.get("token"),f=C.get("id"),i=_?decodeURIComponent(_):null;console.log("Raw token from URL:",_),console.log("Decoded token:",i);const[L,x]=d.useState(!0),[D,g]=d.useState(!1),[A,n]=d.useState(null),[m,T]=d.useState(null),[I,R]=d.useState(""),[h,O]=d.useState(""),[j,Q]=d.useState(""),[w,H]=d.useState(""),[p,B]=d.useState(!1);d.useEffect(()=>{(async()=>{var c;if(console.log("NewAcceptInvitation component mounted"),console.log("URL parameters:",{token:i,invitationId:f}),!i&&!f){n("Invalid invitation link. No token or ID provided."),x(!1);return}try{const{data:v}=await l.from("invitations").select("id, token, email, expires_at, accepted_at").limit(10);console.log("All recent invitations:",v);let r=null,o=null;if(f&&i){console.log("Trying to find invitation with both ID and token");const{data:s,error:a}=await l.from("invitations").select("*, organizations(name), profiles(first_name, last_name)").eq("id",f).eq("token",i).is("accepted_at",null).single();console.log("Query result (ID + token):",{data:s,error:a}),s&&!a?r=s:o=a}if(!r&&f){console.log("Trying to find invitation with ID only");const{data:s,error:a}=await l.from("invitations").select("*, organizations(name), profiles(first_name, last_name)").eq("id",f).is("accepted_at",null).single();console.log("Query result (ID only):",{data:s,error:a}),s&&!a?r=s:o||(o=a)}if(!r&&i){console.log("Trying to find invitation with token only");const{data:s,error:a}=await l.from("invitations").select("*, organizations(name), profiles(first_name, last_name)").eq("token",i).is("accepted_at",null).single();console.log("Query result (token only):",{data:s,error:a}),s&&!a?r=s:o||(o=a)}if(!r&&i){const s=[i.replace(/\+/g," "),i.replace(/\s/g,"+"),encodeURIComponent(i),decodeURIComponent(i)];for(const a of s){if(a===i)continue;console.log("Trying alternative token format:",a);const{data:E,error:F}=await l.from("invitations").select("*, organizations(name), profiles(first_name, last_name)").eq("token",a).is("accepted_at",null).single();if(console.log("Query result (alt token):",{data:E,error:F}),E&&!F){r=E;break}}}if(!r){console.error("Could not find invitation with any method"),console.error("Last error:",o),n("Invalid or expired invitation."),x(!1);return}const t=r;if(console.log("Found invitation:",t),!t){n("Invalid or expired invitation."),x(!1);return}if(new Date(t.expires_at)<new Date){n("This invitation has expired."),x(!1);return}const z={id:t.id,organization_id:t.organization_id,email:t.email,role:t.role,token:t.token,expires_at:t.expires_at,organization_name:(c=t.organizations)==null?void 0:c.name,inviter_name:t.profiles?`${t.profiles.first_name} ${t.profiles.last_name}`:void 0};T(z),R(z.email);const{data:W,error:$}=await l.auth.signInWithOtp({email:z.email,options:{shouldCreateUser:!1}});!$&&W&&B(!0)}catch(v){console.error("Error fetching invitation:",v),n("Failed to load invitation details.")}finally{x(!1)}})()},[i,f,N]);const G=async u=>{if(u.preventDefault(),!!m){g(!0),n(null);try{let c;if(p){const{data:o,error:t}=await l.auth.signInWithPassword({email:I,password:h});if(t||!o.user){n((t==null?void 0:t.message)||"Invalid email or password."),g(!1);return}c=o.user.id}else{if(!j||!w){n("Please provide your first and last name."),g(!1);return}if(h.length<8){n("Password must be at least 8 characters long."),g(!1);return}const{data:o,error:t}=await l.auth.signUp({email:I,password:h,options:{data:{first_name:j,last_name:w}}});if(t||!o.user){n((t==null?void 0:t.message)||"Failed to create account."),g(!1);return}c=o.user.id;const{error:k}=await l.from("profiles").insert({id:c,first_name:j,last_name:w});k&&console.error("Error creating profile:",k)}const{error:v}=await l.from("organization_members").insert({organization_id:m.organization_id,user_id:c,role:m.role});if(v)if(v.code==="23505")console.log("User is already a member of this organization");else{n("Failed to add you to the organization. Please try again."),g(!1);return}const{error:r}=await l.from("invitations").update({accepted_at:new Date().toISOString()}).eq("id",m.id);r&&console.error("Error updating invitation:",r),N("/dashboard")}catch(c){console.error("Error accepting invitation:",c),n(c.message||"Failed to accept invitation."),g(!1)}}};return L?e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-100",children:e.jsx(S,{className:"w-full max-w-md",children:e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(P,{size:"xl"})})})}):A?e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-100",children:e.jsxs(S,{className:"w-full max-w-md",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Invitation Error"}),e.jsx(X,{color:"failure",children:A}),e.jsx("div",{className:"mt-4",children:e.jsx(U,{onClick:()=>N("/auth/login"),children:"Go to Login"})})]})}):e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-100",children:e.jsxs(S,{className:"w-full max-w-md",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Accept Invitation"}),m&&e.jsxs("div",{className:"mb-6",children:[e.jsxs("p",{className:"text-gray-700",children:["You've been invited to join ",e.jsx("strong",{children:m.organization_name})," as a ",e.jsx("strong",{children:m.role}),"."]}),m.inviter_name&&e.jsxs("p",{className:"text-gray-500 text-sm mt-1",children:["Invited by ",m.inviter_name]})]}),e.jsxs("form",{onSubmit:G,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(b,{htmlFor:"email",value:"Email Address"})}),e.jsx(y,{id:"email",type:"email",icon:Y,value:I,disabled:!0})]}),!p&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(b,{htmlFor:"firstName",value:"First Name"})}),e.jsx(y,{id:"firstName",type:"text",icon:q,value:j,onChange:u=>Q(u.target.value),required:!0})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(b,{htmlFor:"lastName",value:"Last Name"})}),e.jsx(y,{id:"lastName",type:"text",icon:q,value:w,onChange:u=>H(u.target.value),required:!0})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(b,{htmlFor:"password",value:p?"Password":"Create Password"})}),e.jsx(y,{id:"password",type:"password",icon:J,value:h,onChange:u=>O(u.target.value),required:!0}),!p&&e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Password must be at least 8 characters long"})]}),e.jsxs(U,{type:"submit",className:"w-full",disabled:D,children:[D?e.jsx(P,{size:"sm",className:"mr-2"}):null,p?"Sign In & Accept":"Create Account & Accept"]})]})]})})};export{ee as default};
