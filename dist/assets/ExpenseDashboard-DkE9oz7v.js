import{h as O,r as l,j as e,i as C,A as p,B as d,t as R,J as v,z as S,E as o,aj as b,b5 as z,bK as A,e as h}from"./index-C6AV3cVN.js";import{C as r}from"./Card-yj7fueH8.js";import{g as H,a as P}from"./operationalExpenses-C7_pxLV1.js";import{u as T}from"./currencyFormatter-BsFWv3sX.js";import"./formatters-Cypx7G-j.js";const J=()=>{const{currentOrganization:i}=O(),g=T(),[a,w]=l.useState(null),[n,_]=l.useState([]),[k,u]=l.useState(!0),[j,x]=l.useState(null),f=async()=>{if(i)try{u(!0),x(null);const[s,t]=await Promise.all([H(i.id),P(i.id)]);s.success&&s.data?w(s.data):x(s.error||"Failed to load dashboard data"),t.success&&t.data&&_(t.data)}catch(s){x(s.message)}finally{u(!1)}};l.useEffect(()=>{f()},[i]);const D=s=>new Date(s).toLocaleDateString(),m=s=>{const t=new Date(s),y=new Date,E=t.getTime()-y.getTime(),N=Math.ceil(E/(1e3*60*60*24));return N<=7&&N>=0},c=s=>new Date(s)<new Date;return k?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(C,{size:"xl"})}):j?e.jsx(p,{color:"failure",className:"mb-4",children:j}):a?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Expense Dashboard"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Overview of operational expenses and pending actions"})]}),e.jsxs(d,{onClick:f,color:"gray",children:[e.jsx(R,{className:"mr-2 h-4 w-4"}),"Refresh"]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx(r,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg",children:e.jsx(v,{className:"w-6 h-6 text-yellow-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Pending Approvals"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a.total_pending_approvals})]})]})}),e.jsx(r,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"flex items-center justify-center w-12 h-12 bg-red-100 rounded-lg",children:e.jsx(S,{className:"w-6 h-6 text-red-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Overdue Payments"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a.total_overdue_payments})]})]})}),e.jsx(r,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg",children:e.jsx(o,{className:"w-6 h-6 text-blue-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Due Soon (30 days)"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a.upcoming_recurring_expenses})]})]})}),e.jsx(r,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg",children:e.jsx(b,{className:"w-6 h-6 text-green-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Active Categories"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a.expense_by_category.length})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs(r,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Expenses by Category (Last 6 Months)"}),e.jsx(z,{className:"w-5 h-5 text-gray-400"})]}),a.expense_by_category.length>0?e.jsx("div",{className:"space-y-4",children:a.expense_by_category.sort((s,t)=>t.amount-s.amount).slice(0,8).map((s,t)=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:s.category}),e.jsxs("div",{className:"text-right",children:[e.jsx("span",{className:"text-sm font-semibold text-gray-900 dark:text-white",children:g(s.amount)}),e.jsxs("span",{className:"text-xs text-gray-500 ml-2",children:[s.percentage.toFixed(1),"%"]})]})]}),e.jsx(A,{progress:s.percentage,color:"blue",size:"sm"})]},t))}):e.jsx("div",{className:"text-center py-8 text-gray-500",children:"No expense data available for the last 6 months."})]}),e.jsxs(r,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Upcoming Recurring Expenses"}),e.jsx(o,{className:"w-5 h-5 text-gray-400"})]}),n.length>0?e.jsxs("div",{className:"space-y-3",children:[n.slice(0,10).map(s=>{var t;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium text-gray-900 dark:text-white",children:s.name}),e.jsx("div",{className:"text-sm text-gray-500",children:((t=s.supplier)==null?void 0:t.name)||"No supplier"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-semibold text-gray-900 dark:text-white",children:g(s.amount)}),e.jsx("div",{className:`text-sm ${c(s.next_due_date)?"text-red-600":m(s.next_due_date)?"text-yellow-600":"text-gray-500"}`,children:D(s.next_due_date)})]}),e.jsxs("div",{className:"ml-3",children:[c(s.next_due_date)&&e.jsx(h,{color:"failure",size:"sm",children:"Overdue"}),m(s.next_due_date)&&!c(s.next_due_date)&&e.jsx(h,{color:"warning",size:"sm",children:"Due Soon"}),!m(s.next_due_date)&&!c(s.next_due_date)&&e.jsx(h,{color:"gray",size:"sm",children:"Upcoming"})]})]},s.id)}),n.length>10&&e.jsxs("div",{className:"text-center text-sm text-gray-500 pt-2",children:["And ",n.length-10," more..."]})]}):e.jsx("div",{className:"text-center py-8 text-gray-500",children:"No recurring expenses due in the next 30 days."})]})]}),e.jsxs(r,{className:"mt-8",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Quick Actions"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(d,{color:"blue",className:"justify-center",children:[e.jsx(b,{className:"mr-2 h-4 w-4"}),"Create New Expense"]}),e.jsxs(d,{color:"yellow",className:"justify-center",children:[e.jsx(v,{className:"mr-2 h-4 w-4"}),"Review Pending Approvals"]}),e.jsxs(d,{color:"green",className:"justify-center",children:[e.jsx(o,{className:"mr-2 h-4 w-4"}),"Process Due Expenses"]})]})]})]}):e.jsx(p,{color:"info",className:"mb-4",children:"No dashboard data available."})};export{J as default};
