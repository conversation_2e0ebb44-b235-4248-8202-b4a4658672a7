import{r as C,R as P,j as e,P as A,Q as z,D as y,B as L,Z as E,_ as s,e as H,J as O,L as M,az as U,aA as B,z as F,h as K,t as Q,I as $,A as W,G as Y,i as q,T}from"./index-C6AV3cVN.js";import{C as R}from"./Card-yj7fueH8.js";import{g as G}from"./product-Ca8DWaNR.js";import{b as J}from"./inventoryTransaction-1UXV5RDN.js";import{f as D}from"./formatters-Cypx7G-j.js";import{P as Z}from"./PageHeader-DFlcZMc0.js";import"./floatInventory-k_pEQeIK.js";const X=({products:l=[]})=>{const[r,b]=C.useState(""),[u,h]=C.useState("value"),[x,k]=C.useState("desc");P.useEffect(()=>{console.log("InventoryValueReport received products:",{count:(l==null?void 0:l.length)||0,sampleProduct:l!=null&&l[0]?{name:l[0].name,sku:l[0].sku,stock_quantity:l[0].stock_quantity,selling_price:l[0].selling_price}:"No products"})},[l]);const _=[...(l||[]).filter(o=>o&&o.name&&o.name.toLowerCase().includes((r||"").toLowerCase())||o&&o.sku&&o.sku.toLowerCase().includes((r||"").toLowerCase()))].sort((o,g)=>{var t,n;let c,a;return u==="name"?(c=o.name.toLowerCase(),a=g.name.toLowerCase()):u==="sku"?(c=((t=o.sku)==null?void 0:t.toLowerCase())||"",a=((n=g.sku)==null?void 0:n.toLowerCase())||""):u==="quantity"?(c=o.stock_quantity,a=g.stock_quantity):u==="price"?(c=o.selling_price,a=g.selling_price):(c=o.stock_quantity*o.selling_price,a=g.stock_quantity*g.selling_price),x==="asc"?c>a?1:-1:c<a?1:-1}),f=_.reduce((o,g)=>o+g.stock_quantity*g.selling_price,0),w=o=>{o===u?k(x==="asc"?"desc":"asc"):(h(o),k("desc"))},m=()=>{const o=["Name","SKU","Quantity","Unit Price","Total Value"],g=_.map(i=>[i.name,i.sku||"N/A",i.stock_quantity||0,i.unit_price||0,(i.stock_quantity||0)*(i.unit_price||0)]);g.push(["","","","TOTAL",f]);const c=[o.join(","),...g.map(i=>i.join(","))].join(`
`),a=new Blob([c],{type:"text/csv;charset=utf-8;"}),t=URL.createObjectURL(a),n=document.createElement("a");n.setAttribute("href",t),n.setAttribute("download","inventory_value_report.csv"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)};return e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4",children:[e.jsx("div",{className:"w-full md:w-auto",children:e.jsx(A,{id:"search",type:"text",icon:z,placeholder:"Search products...",value:r,onChange:o=>b(o.target.value)})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(y,{label:"Sort By",color:"light",size:"sm",children:[e.jsx(y.Item,{onClick:()=>w("name"),children:"Name"}),e.jsx(y.Item,{onClick:()=>w("sku"),children:"SKU"}),e.jsx(y.Item,{onClick:()=>w("quantity"),children:"Quantity"}),e.jsx(y.Item,{onClick:()=>w("price"),children:"Unit Price"}),e.jsx(y.Item,{onClick:()=>w("value"),children:"Total Value"})]}),e.jsxs(L,{color:"light",size:"sm",onClick:m,children:[e.jsx(E,{className:"mr-2 h-5 w-5"}),"Export"]})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(s,{striped:!0,children:[e.jsxs(s.Head,{children:[e.jsxs(s.HeadCell,{onClick:()=>w("name"),className:"cursor-pointer",children:["Product Name ",u==="name"&&(x==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>w("sku"),className:"cursor-pointer",children:["SKU ",u==="sku"&&(x==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>w("quantity"),className:"cursor-pointer",children:["Quantity ",u==="quantity"&&(x==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>w("price"),className:"cursor-pointer",children:["Unit Price ",u==="price"&&(x==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>w("value"),className:"cursor-pointer",children:["Total Value ",u==="value"&&(x==="asc"?"↑":"↓")]})]}),e.jsx(s.Body,{className:"divide-y",children:_.map(o=>e.jsxs(s.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(s.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:o.name}),e.jsx(s.Cell,{children:o.sku||"N/A"}),e.jsx(s.Cell,{children:o.stock_quantity||0}),e.jsx(s.Cell,{children:D(o.unit_price||0)}),e.jsx(s.Cell,{className:"font-medium",children:D((o.stock_quantity||0)*(o.unit_price||0))})]},o.id))}),e.jsx(s.Footer,{children:e.jsxs(s.Row,{className:"font-semibold text-gray-900 dark:text-white",children:[e.jsx(s.Cell,{colSpan:4,className:"text-right",children:"Total Inventory Value:"}),e.jsx(s.Cell,{children:D(f)})]})})]})})]})},ee=({products:l=[]})=>{const[r,b]=C.useState(""),[u,h]=C.useState("stock_level"),[x,k]=C.useState("asc");P.useEffect(()=>{console.log("LowStockReport received products:",{count:(l==null?void 0:l.length)||0,sampleProduct:l!=null&&l[0]?{name:l[0].name,sku:l[0].sku,stock_quantity:l[0].stock_quantity,min_stock_level:l[0].min_stock_level}:"No products"})},[l]);const _=[...(l||[]).filter(m=>m&&m.name&&m.name.toLowerCase().includes((r||"").toLowerCase())||m&&m.sku&&m.sku.toLowerCase().includes((r||"").toLowerCase()))].sort((m,o)=>{var a,t;let g,c;return u==="name"?(g=m.name.toLowerCase(),c=o.name.toLowerCase()):u==="sku"?(g=((a=m.sku)==null?void 0:a.toLowerCase())||"",c=((t=o.sku)==null?void 0:t.toLowerCase())||""):u==="stock_level"?(g=m.stock_quantity,c=o.stock_quantity):u==="min_level"?(g=m.min_stock_level,c=o.min_stock_level):u==="reorder_qty"&&(g=m.reorder_quantity||0,c=o.reorder_quantity||0),x==="asc"?g>c?1:-1:g<c?1:-1}),f=m=>{m===u?k(x==="asc"?"desc":"asc"):(h(m),k("asc"))},w=()=>{const m=["Name","SKU","Current Stock","Min Stock Level","Reorder Quantity","Status"],o=_.map(n=>{const i=n.stock_quantity===0?"Out of Stock":"Low Stock";return[n.name,n.sku||"N/A",n.stock_quantity,n.min_stock_level,n.reorder_quantity||"Not set",i]}),g=[m.join(","),...o.map(n=>n.join(","))].join(`
`),c=new Blob([g],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(c),t=document.createElement("a");t.setAttribute("href",a),t.setAttribute("download","low_stock_report.csv"),t.style.visibility="hidden",document.body.appendChild(t),t.click(),document.body.removeChild(t)};return e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4",children:[e.jsx("div",{className:"w-full md:w-auto",children:e.jsx(A,{id:"search",type:"text",icon:z,placeholder:"Search products...",value:r,onChange:m=>b(m.target.value)})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(y,{label:"Sort By",color:"light",size:"sm",children:[e.jsx(y.Item,{onClick:()=>f("name"),children:"Name"}),e.jsx(y.Item,{onClick:()=>f("sku"),children:"SKU"}),e.jsx(y.Item,{onClick:()=>f("stock_level"),children:"Current Stock"}),e.jsx(y.Item,{onClick:()=>f("min_level"),children:"Min Stock Level"}),e.jsx(y.Item,{onClick:()=>f("reorder_qty"),children:"Reorder Quantity"})]}),e.jsxs(L,{color:"light",size:"sm",onClick:w,children:[e.jsx(E,{className:"mr-2 h-5 w-5"}),"Export"]})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(s,{striped:!0,children:[e.jsxs(s.Head,{children:[e.jsxs(s.HeadCell,{onClick:()=>f("name"),className:"cursor-pointer",children:["Product Name ",u==="name"&&(x==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>f("sku"),className:"cursor-pointer",children:["SKU ",u==="sku"&&(x==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>f("stock_level"),className:"cursor-pointer",children:["Current Stock ",u==="stock_level"&&(x==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>f("min_level"),className:"cursor-pointer",children:["Min Stock Level ",u==="min_level"&&(x==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>f("reorder_qty"),className:"cursor-pointer",children:["Reorder Quantity ",u==="reorder_qty"&&(x==="asc"?"↑":"↓")]}),e.jsx(s.HeadCell,{children:"Status"}),e.jsx(s.HeadCell,{children:"Actions"})]}),e.jsx(s.Body,{className:"divide-y",children:_.map(m=>e.jsxs(s.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(s.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:m.name}),e.jsx(s.Cell,{children:m.sku||"N/A"}),e.jsx(s.Cell,{className:"text-red-600 font-medium",children:m.stock_quantity}),e.jsx(s.Cell,{children:m.min_stock_level}),e.jsx(s.Cell,{children:m.reorder_quantity||"Not set"}),e.jsx(s.Cell,{children:m.stock_quantity===0?e.jsx(H,{color:"failure",icon:O,children:"Out of Stock"}):e.jsx(H,{color:"warning",icon:O,children:"Low Stock"})}),e.jsx(s.Cell,{children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(M,{to:`/products/details/${m.id}`,children:e.jsx(L,{size:"xs",children:"View"})}),e.jsx(M,{to:"/purchases/requests/create",children:e.jsx(L,{size:"xs",color:"warning",children:"Order"})})]})})]},m.id))})]})}),_.length===0&&e.jsx("div",{className:"text-center py-4",children:e.jsx("p",{className:"text-gray-500",children:"No low stock items found."})})]})},te=({products:l=[],transactions:r=[]})=>{const[b,u]=C.useState(""),[h,x]=C.useState("movement"),[k,p]=C.useState("desc"),[_,f]=C.useState("30days");P.useEffect(()=>{console.log("InventoryMovementReport received:",{productsCount:(l==null?void 0:l.length)||0,transactionsCount:(r==null?void 0:r.length)||0,sampleProduct:l!=null&&l[0]?{name:l[0].name,sku:l[0].sku,stock_quantity:l[0].stock_quantity}:"No products",sampleTransaction:r!=null&&r[0]?{product_id:r[0].product_id,quantity:r[0].quantity,transaction_type:r[0].transaction_type,created_at:r[0].created_at}:"No transactions"})},[l,r]);const w=()=>{const t=new Date,n=new Date;switch(_){case"7days":n.setDate(t.getDate()-7);break;case"30days":n.setDate(t.getDate()-30);break;case"90days":n.setDate(t.getDate()-90);break;case"all":default:n.setFullYear(n.getFullYear()-10);break}return{startDate:n,endDate:t}},g=[...(l||[]).map(t=>{if(r&&r.length>0){const{startDate:n,endDate:i}=w(),d=r.filter(N=>N.product_id===t.id&&new Date(N.created_at)>=n&&new Date(N.created_at)<=i),v=d.filter(N=>N.quantity>0).reduce((N,I)=>N+I.quantity,0),j=d.filter(N=>N.quantity<0).reduce((N,I)=>N+Math.abs(I.quantity),0),S=v-j;return{...t,inflow:v||0,outflow:j||0,movement:S||0,previous_stock:(t.stock_quantity||0)-(S||0),transaction_count:d.length}}else{const n=Math.floor(Math.random()*50),i=Math.floor(Math.random()*30),d=n-i;return{...t,inflow:n,outflow:i,movement:d,previous_stock:(t.stock_quantity||0)-d,transaction_count:0}}}).filter(t=>{var n;return t.name.toLowerCase().includes(b.toLowerCase())||((n=t.sku)==null?void 0:n.toLowerCase().includes(b.toLowerCase()))})].sort((t,n)=>{var v,j;let i,d;return h==="name"?(i=t.name.toLowerCase(),d=n.name.toLowerCase()):h==="sku"?(i=((v=t.sku)==null?void 0:v.toLowerCase())||"",d=((j=n.sku)==null?void 0:j.toLowerCase())||""):h==="current_stock"?(i=t.stock_quantity,d=n.stock_quantity):h==="previous_stock"?(i=t.previous_stock,d=n.previous_stock):h==="inflow"?(i=t.inflow,d=n.inflow):h==="outflow"?(i=t.outflow,d=n.outflow):(i=t.movement,d=n.movement),k==="asc"?i>d?1:-1:i<d?1:-1}),c=t=>{t===h?p(k==="asc"?"desc":"asc"):(x(t),p("desc"))},a=()=>{const t=["Name","SKU","Previous Stock","Inflow","Outflow","Current Stock","Net Movement"],n=g.map(S=>[S.name,S.sku||"N/A",S.previous_stock,S.inflow,S.outflow,S.stock_quantity,S.movement]),i=[t.join(","),...n.map(S=>S.join(","))].join(`
`),d=new Blob([i],{type:"text/csv;charset=utf-8;"}),v=URL.createObjectURL(d),j=document.createElement("a");j.setAttribute("href",v),j.setAttribute("download","inventory_movement_report.csv"),j.style.visibility="hidden",document.body.appendChild(j),j.click(),document.body.removeChild(j)};return e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4",children:[e.jsx("div",{className:"w-full md:w-auto",children:e.jsx(A,{id:"search",type:"text",icon:z,placeholder:"Search products...",value:b,onChange:t=>u(t.target.value)})}),e.jsxs("div",{className:"flex gap-2 flex-wrap",children:[e.jsxs(y,{label:"Time Range",color:"info",size:"sm",children:[e.jsxs(y.Item,{onClick:()=>f("7days"),children:["Last 7 Days ",_==="7days"&&"✓"]}),e.jsxs(y.Item,{onClick:()=>f("30days"),children:["Last 30 Days ",_==="30days"&&"✓"]}),e.jsxs(y.Item,{onClick:()=>f("90days"),children:["Last 90 Days ",_==="90days"&&"✓"]}),e.jsxs(y.Item,{onClick:()=>f("all"),children:["All Time ",_==="all"&&"✓"]})]}),e.jsxs(y,{label:"Sort By",color:"light",size:"sm",children:[e.jsx(y.Item,{onClick:()=>c("name"),children:"Name"}),e.jsx(y.Item,{onClick:()=>c("sku"),children:"SKU"}),e.jsx(y.Item,{onClick:()=>c("previous_stock"),children:"Previous Stock"}),e.jsx(y.Item,{onClick:()=>c("inflow"),children:"Inflow"}),e.jsx(y.Item,{onClick:()=>c("outflow"),children:"Outflow"}),e.jsx(y.Item,{onClick:()=>c("current_stock"),children:"Current Stock"}),e.jsx(y.Item,{onClick:()=>c("movement"),children:"Net Movement"})]}),e.jsxs(L,{color:"light",size:"sm",onClick:a,children:[e.jsx(E,{className:"mr-2 h-5 w-5"}),"Export"]})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(s,{striped:!0,children:[e.jsxs(s.Head,{children:[e.jsxs(s.HeadCell,{onClick:()=>c("name"),className:"cursor-pointer",children:["Product Name ",h==="name"&&(k==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>c("sku"),className:"cursor-pointer",children:["SKU ",h==="sku"&&(k==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>c("previous_stock"),className:"cursor-pointer",children:["Previous Stock ",h==="previous_stock"&&(k==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>c("inflow"),className:"cursor-pointer",children:["Inflow ",h==="inflow"&&(k==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>c("outflow"),className:"cursor-pointer",children:["Outflow ",h==="outflow"&&(k==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>c("current_stock"),className:"cursor-pointer",children:["Current Stock ",h==="current_stock"&&(k==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>c("movement"),className:"cursor-pointer",children:["Net Movement ",h==="movement"&&(k==="asc"?"↑":"↓")]})]}),e.jsx(s.Body,{className:"divide-y",children:g.map(t=>e.jsxs(s.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(s.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:t.name}),e.jsx(s.Cell,{children:t.sku||"N/A"}),e.jsx(s.Cell,{children:t.previous_stock}),e.jsx(s.Cell,{className:"text-green-600",children:t.inflow}),e.jsx(s.Cell,{className:"text-red-600",children:t.outflow}),e.jsx(s.Cell,{children:t.stock_quantity}),e.jsx(s.Cell,{children:e.jsx("div",{className:"flex items-center",children:t.movement>0?e.jsxs(e.Fragment,{children:[e.jsx(U,{className:"text-green-600 mr-1"}),e.jsxs("span",{className:"text-green-600",children:["+",t.movement]})]}):t.movement<0?e.jsxs(e.Fragment,{children:[e.jsx(B,{className:"text-red-600 mr-1"}),e.jsx("span",{className:"text-red-600",children:t.movement})]}):e.jsx("span",{children:"0"})})})]},t.id))})]})}),e.jsx("div",{className:"mt-4 text-sm text-gray-500",children:e.jsx("p",{children:r&&r.length>0?`Note: This report shows inventory movement for the selected time period (${_==="7days"?"Last 7 Days":_==="30days"?"Last 30 Days":_==="90days"?"Last 90 Days":"All Time"}).`:"Note: No transaction data available. Showing simulated data for demonstration purposes."})})]})},se=({products:l=[],transactions:r=[]})=>{const[b,u]=C.useState(""),[h,x]=C.useState("days_in_inventory"),[k,p]=C.useState("desc");P.useEffect(()=>{console.log("InventoryAgingReport received:",{productsCount:(l==null?void 0:l.length)||0,transactionsCount:(r==null?void 0:r.length)||0,sampleProduct:l!=null&&l[0]?{name:l[0].name,sku:l[0].sku,stock_quantity:l[0].stock_quantity}:"No products",sampleTransaction:r!=null&&r[0]?{product_id:r[0].product_id,quantity:r[0].quantity,transaction_type:r[0].transaction_type,created_at:r[0].created_at}:"No transactions"})},[l,r]);const w=[...(l||[]).map(a=>{let t=null,n=0;if(r&&r.length>0){const d=r.filter(v=>v.product_id===a.id);if(d.length>0){d.sort((S,N)=>new Date(N.created_at).getTime()-new Date(S.created_at).getTime()),t=new Date(d[0].created_at);const j=new Date().getTime()-t.getTime();n=Math.floor(j/(1e3*3600*24))}}t||(n=Math.floor(Math.random()*365),t=new Date(Date.now()-n*24*60*60*1e3));let i="";return n<=30?i="0-30 days":n<=60?i="31-60 days":n<=90?i="61-90 days":n<=180?i="91-180 days":i="180+ days",{...a,days_in_inventory:n,aging_category:i,last_movement_date:t.toISOString().split("T")[0],has_real_data:t!==null}}).filter(a=>{var t;return a.name.toLowerCase().includes(b.toLowerCase())||((t=a.sku)==null?void 0:t.toLowerCase().includes(b.toLowerCase()))})].sort((a,t)=>{var d,v;let n,i;return h==="name"?(n=a.name.toLowerCase(),i=t.name.toLowerCase()):h==="sku"?(n=((d=a.sku)==null?void 0:d.toLowerCase())||"",i=((v=t.sku)==null?void 0:v.toLowerCase())||""):h==="stock_quantity"?(n=a.stock_quantity,i=t.stock_quantity):h==="days_in_inventory"?(n=a.days_in_inventory,i=t.days_in_inventory):h==="value"?(n=a.stock_quantity*a.selling_price,i=t.stock_quantity*t.selling_price):(n=new Date(a.last_movement_date).getTime(),i=new Date(t.last_movement_date).getTime()),k==="asc"?n>i?1:-1:n<i?1:-1}),m={"0-30 days":{count:0,value:0},"31-60 days":{count:0,value:0},"61-90 days":{count:0,value:0},"91-180 days":{count:0,value:0},"180+ days":{count:0,value:0}};w.forEach(a=>{const t=a.aging_category,n=a.stock_quantity*a.selling_price;m[t].count++,m[t].value+=n});const o=a=>{a===h?p(k==="asc"?"desc":"asc"):(x(a),p("desc"))},g=()=>{const a=["Name","SKU","Quantity","Days in Inventory","Last Movement","Aging Category","Value"],t=w.map(j=>[j.name,j.sku||"N/A",j.stock_quantity,j.days_in_inventory,j.last_movement_date,j.aging_category,j.stock_quantity*j.selling_price]),n=[a.join(","),...t.map(j=>j.join(","))].join(`
`),i=new Blob([n],{type:"text/csv;charset=utf-8;"}),d=URL.createObjectURL(i),v=document.createElement("a");v.setAttribute("href",d),v.setAttribute("download","inventory_aging_report.csv"),v.style.visibility="hidden",document.body.appendChild(v),v.click(),document.body.removeChild(v)},c=a=>{switch(a){case"0-30 days":return"success";case"31-60 days":return"info";case"61-90 days":return"warning";case"91-180 days":return"purple";case"180+ days":return"failure";default:return"gray"}};return e.jsxs("div",{children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4 mb-6",children:Object.entries(m).map(([a,t])=>e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h5",{className:"text-sm font-medium",children:a}),e.jsx(H,{color:c(a),children:t.count})]}),e.jsx("p",{className:"text-lg font-bold",children:D(t.value)})]},a))}),e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4",children:[e.jsx("div",{className:"w-full md:w-auto",children:e.jsx(A,{id:"search",type:"text",icon:z,placeholder:"Search products...",value:b,onChange:a=>u(a.target.value)})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(y,{label:"Sort By",color:"light",size:"sm",children:[e.jsx(y.Item,{onClick:()=>o("name"),children:"Name"}),e.jsx(y.Item,{onClick:()=>o("sku"),children:"SKU"}),e.jsx(y.Item,{onClick:()=>o("stock_quantity"),children:"Quantity"}),e.jsx(y.Item,{onClick:()=>o("days_in_inventory"),children:"Days in Inventory"}),e.jsx(y.Item,{onClick:()=>o("last_movement"),children:"Last Movement"}),e.jsx(y.Item,{onClick:()=>o("value"),children:"Value"})]}),e.jsxs(L,{color:"light",size:"sm",onClick:g,children:[e.jsx(E,{className:"mr-2 h-5 w-5"}),"Export"]})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(s,{striped:!0,children:[e.jsxs(s.Head,{children:[e.jsxs(s.HeadCell,{onClick:()=>o("name"),className:"cursor-pointer",children:["Product Name ",h==="name"&&(k==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>o("sku"),className:"cursor-pointer",children:["SKU ",h==="sku"&&(k==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>o("stock_quantity"),className:"cursor-pointer",children:["Quantity ",h==="stock_quantity"&&(k==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>o("days_in_inventory"),className:"cursor-pointer",children:["Days in Inventory ",h==="days_in_inventory"&&(k==="asc"?"↑":"↓")]}),e.jsxs(s.HeadCell,{onClick:()=>o("last_movement"),className:"cursor-pointer",children:["Last Movement ",h==="last_movement"&&(k==="asc"?"↑":"↓")]}),e.jsx(s.HeadCell,{children:"Aging Category"}),e.jsxs(s.HeadCell,{onClick:()=>o("value"),className:"cursor-pointer",children:["Value ",h==="value"&&(k==="asc"?"↑":"↓")]})]}),e.jsx(s.Body,{className:"divide-y",children:w.map(a=>e.jsxs(s.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(s.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:a.name}),e.jsx(s.Cell,{children:a.sku||"N/A"}),e.jsx(s.Cell,{children:a.stock_quantity}),e.jsx(s.Cell,{children:a.days_in_inventory}),e.jsx(s.Cell,{children:a.last_movement_date}),e.jsx(s.Cell,{children:e.jsx(H,{color:c(a.aging_category),icon:F,children:a.aging_category})}),e.jsx(s.Cell,{className:"font-medium",children:D((a.stock_quantity||0)*(a.unit_price||0))})]},a.id))})]})}),e.jsx("div",{className:"mt-4 text-sm text-gray-500",children:e.jsx("p",{children:r&&r.length>0?"Note: This report shows inventory aging based on the last transaction date for each product.":"Note: No transaction data available. Showing simulated aging data for demonstration purposes."})})]})},de=()=>{const{currentOrganization:l}=K(),[r,b]=C.useState("value"),[u,h]=C.useState(!0),[x,k]=C.useState(null),[p,_]=C.useState([]),[f,w]=C.useState([]),[m,o]=C.useState(0),[g,c]=C.useState([]),[a,t]=C.useState(!1),n=async()=>{if(console.log("Fetching data, organization:",l),!l||!l.id){console.warn("No organization or organization ID available"),k("Please select an organization first"),h(!1);return}h(!0),k(null);try{console.log("Making API call to getProducts with organization ID:",l.id);const d=await G(l.id);if(console.log("Product API result:",d),d.error)throw new Error(d.error);const v=d.products||[];console.log(`Received ${v.length} products`,v[0]),_(v);const j=v.filter(I=>(I.stock_quantity||0)<=(I.min_stock_level||0));w(j),console.log(`Found ${j.length} low stock items`);const S=v.reduce((I,V)=>I+(V.stock_quantity||0)*(V.unit_price||0),0);o(S);const N=await J(l.id,{limit:1e3,sortOrder:"desc"});N.error?console.warn("Error fetching inventory transactions:",N.error):(console.log(`Received ${N.transactions.length} inventory transactions`),c(N.transactions)),t(!0)}catch(d){console.error("Error fetching inventory report data:",d),k(d.message||"Failed to load inventory report data")}finally{h(!1)}};C.useEffect(()=>{console.log("Organization changed or component mounted:",l),l?(console.log("Organization available, fetching data"),n()):(console.log("No organization available, waiting..."),h(!1))},[l]),C.useEffect(()=>{console.log("Current state:",{activeTab:r,isLoading:u,organization:(l==null?void 0:l.name)||"No organization",productsCount:p.length,lowStockCount:f.length,hasError:!!x,initialized:a})},[r,u,p.length,f.length,x,l,a]);const i=()=>{n()};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Z,{title:"Inventory Reports",description:"View and analyze inventory data",icon:e.jsx($,{className:"h-8 w-8"}),actions:e.jsxs(L,{color:"light",onClick:i,children:[e.jsx(Q,{className:"mr-2 h-5 w-5"}),"Refresh"]})}),x&&e.jsxs(W,{color:"failure",icon:Y,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",x]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsx(R,{children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("h5",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"Total Inventory Value"}),u?e.jsx(q,{size:"md",className:"my-2"}):e.jsx("p",{className:"text-2xl font-bold text-blue-600",children:D(m)}),e.jsx("p",{className:"text-sm text-gray-500",children:"Based on current stock levels"})]})}),e.jsx(R,{children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("h5",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"Total Products"}),u?e.jsx(q,{size:"md",className:"my-2"}):e.jsx("p",{className:"text-2xl font-bold text-green-600",children:p.length}),e.jsx("p",{className:"text-sm text-gray-500",children:"Products in inventory"})]})}),e.jsx(R,{children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("h5",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"Low Stock Items"}),u?e.jsx(q,{size:"md",className:"my-2"}):e.jsx("p",{className:"text-2xl font-bold text-red-600",children:f.length}),e.jsx("p",{className:"text-sm text-gray-500",children:"Items below minimum stock level"})]})})]}),e.jsx(R,{children:e.jsxs(T,{"aria-label":"Inventory report tabs",onActiveTabChange:d=>{console.log("Tab changed to index:",d);const j=["value","low-stock","movement","aging"][d];console.log("Setting activeTab to:",j),b(j)},children:[e.jsx(T.Item,{title:"Inventory Value",children:u?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx(q,{size:"xl"})}):p.length>0?e.jsx(X,{products:p}):e.jsxs("div",{className:"p-6 text-center text-gray-500",children:["No product data available. ",x?`Error: ${x}`:""]})}),e.jsx(T.Item,{title:"Low Stock",children:u?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx(q,{size:"xl"})}):f.length>0?e.jsx(ee,{products:f}):e.jsxs("div",{className:"p-6 text-center text-gray-500",children:["No low stock items found. ",x?`Error: ${x}`:""]})}),e.jsx(T.Item,{title:"Inventory Movement",children:u?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx(q,{size:"xl"})}):p.length>0?e.jsx(te,{products:p,transactions:g}):e.jsxs("div",{className:"p-6 text-center text-gray-500",children:["No product data available. ",x?`Error: ${x}`:""]})}),e.jsx(T.Item,{title:"Inventory Aging",children:u?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx(q,{size:"xl"})}):p.length>0?e.jsx(se,{products:p,transactions:g}):e.jsxs("div",{className:"p-6 text-center text-gray-500",children:["No product data available. ",x?`Error: ${x}`:""]})})]})})]})};export{de as default};
