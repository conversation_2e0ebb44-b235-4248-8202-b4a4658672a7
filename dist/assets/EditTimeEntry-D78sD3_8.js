import{ab as G,d as J,h as K,r as x,j as e,i as Q,A as w,G as P,B as S,o as A,a6 as l,a7 as I,U as k,P as H,al as W,Y as X,bN as Z}from"./index-C6AV3cVN.js";import{C as $}from"./Card-yj7fueH8.js";import{D as C}from"./react-datepicker-BrCvW-wJ.js";import{g as ee}from"./employee-DWC25S7P.js";import{T as j}from"./payroll-j3fcCwK0.js";import{P as se}from"./PageTitle-FHPo8gWi.js";import{E as te}from"./EmployeeSearchSelect-vG5mZJbz.js";import{b as ae,u as re,a as T}from"./timeEntry-DfPyfZaA.js";import{i as q}from"./index-4YOzgfrD.js";import"./index-DNTDRJcs.js";import"./index-Cn2wB4rc.js";import"./typeof-QjJsDpFa.js";import"./index-idNacaog.js";import"./index-KY8jayTk.js";const ie=(r,f)=>{let a=new Date(f);a<r&&a.setDate(a.getDate()+1);const u=new Date(r);u.setHours(22,0,0,0);const c=new Date(r);c.setHours(6,0,0,0),c<u&&c.setDate(c.getDate()+1);const s=new Date(u);s.setDate(s.getDate()+1);const m=new Date(c);m.setDate(m.getDate()+1);let y=0;if(r<=c&&a>=u){const p=r>u?r:u,h=a<c?a:c;h>p&&(y+=T(h,p))}if(r<=m&&a>=s){const p=r>s?r:s,h=a<m?a:m;h>p&&(y+=T(h,p))}return Math.round(y/60*2)/2},ve=()=>{const{id:r}=G(),f=J(),{currentOrganization:a}=K(),[u,c]=x.useState([]),[s,m]=x.useState(null),[y,p]=x.useState(!0),[h,v]=x.useState(null),[L,F]=x.useState(!1),[O,_]=x.useState(null),[R,E]=x.useState(!1),B=async()=>{if(!(!a||!r))try{const{entry:t,error:o}=await ae(a.id,r);o?v(o):t?m({...t,exclude_lunch_break:t.exclude_lunch_break!==void 0?t.exclude_lunch_break:!0}):v("Time entry not found")}catch(t){v(t.message)}finally{p(!1)}},z=async()=>{if(a)try{const{employees:t,error:o}=await ee(a.id);o?v(o):c(t)}catch(t){v(t.message)}};x.useEffect(()=>{B(),z()},[a,r]);const i=(t,o)=>{if(!s)return;const d={...s,[t]:o};if((t==="time_in"||t==="time_out"||t==="exclude_lunch_break")&&d.time_in&&d.time_out){const n=new Date(d.time_in),g=new Date(d.time_out);if(q(n)&&q(g)){let b=0;if(g<n){const D=new Date(g);D.setDate(D.getDate()+1),b=T(D,n)}else b=T(g,n);d.exclude_lunch_break&&b>300&&(b-=60);const M=b/60,V=Math.min(8,M),Y=Math.max(0,M-8);d.regular_hours=Math.round(V*2)/2,d.overtime_hours=Math.round(Y*2)/2;let N=g;g<n&&(N=new Date(g),N.setDate(N.getDate()+1)),d.night_diff_hours=ie(n,N)}}m(d)},U=async t=>{if(t.preventDefault(),!(!a||!s||!r)){if(!s.employee_id||!s.date||s.status===j.PRESENT&&(!s.time_in||!s.time_out)){_("Please fill in all required fields.");return}F(!0),_(null);try{const o={employee_id:s.employee_id,date:s.date,time_in:s.time_in,time_out:s.time_out,status:s.status,regular_hours:s.regular_hours,overtime_hours:s.overtime_hours,night_diff_hours:s.night_diff_hours,exclude_lunch_break:s.exclude_lunch_break,is_rest_day:s.is_rest_day,is_holiday:s.is_holiday,holiday_type:s.holiday_type},{entry:d,error:n}=await re(a.id,r,o);n?n.includes("duplicate key value")||n.includes("already exists for this employee")?(_("An entry already exists for this employee on this date. The existing entry has been updated."),E(!0),setTimeout(()=>{f("/payroll/time-entries")},3e3)):_(n):(E(!0),setTimeout(()=>{f("/payroll/time-entries")},2e3))}catch(o){_(o.message||"An error occurred while saving the time entry")}finally{F(!1)}}};return y?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(Q,{size:"xl"})}):h?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(w,{color:"failure",icon:P,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",h]}),e.jsxs(S,{color:"light",onClick:()=>f("/payroll/time-entries"),children:[e.jsx(A,{className:"mr-2 h-5 w-5"}),"Back to Time Entries"]})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsxs(S,{color:"light",onClick:()=>f("/payroll/time-entries"),className:"mr-4",children:[e.jsx(A,{className:"mr-2 h-5 w-5"}),"Back to Time Entries"]}),e.jsx(se,{title:"Edit Time Entry"})]})}),R&&e.jsx(w,{color:"success",className:"mb-4",children:"Time entry updated successfully! Redirecting..."}),e.jsx($,{children:e.jsxs("form",{onSubmit:U,children:[O&&e.jsx(w,{color:"failure",icon:P,className:"mb-4",children:O}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(l,{htmlFor:"employeeId",value:"Employee"})}),e.jsx(te,{employees:u,selectedEmployeeId:s==null?void 0:s.employee_id,onSelect:t=>i("employee_id",t),required:!0,isLoading:y,placeholder:"Search for an employee..."})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(l,{htmlFor:"date",value:"Date"})}),e.jsx(C,{id:"date",selected:s!=null&&s.date?new Date(s.date):null,onChange:t=>i("date",t.toISOString().split("T")[0]),dateFormat:"MMMM d, yyyy",className:"w-full rounded-lg border border-gray-300 p-2.5",placeholderText:"Select date",required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(l,{htmlFor:"status",value:"Status"})}),e.jsxs(I,{id:"status",value:(s==null?void 0:s.status)||j.PRESENT,onChange:t=>i("status",t.target.value),required:!0,children:[e.jsx("option",{value:j.PRESENT,children:"Present"}),e.jsx("option",{value:j.ABSENT,children:"Absent"}),e.jsx("option",{value:j.LEAVE,children:"Leave"}),e.jsx("option",{value:j.HOLIDAY,children:"Holiday"})]})]})]}),(s==null?void 0:s.status)===j.PRESENT&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(l,{htmlFor:"timeIn",value:"Time In"})}),e.jsx(C,{id:"timeIn",selected:s!=null&&s.time_in?new Date(s.time_in):null,onChange:t=>i("time_in",t.toISOString()),showTimeSelect:!0,showTimeSelectOnly:!0,timeIntervals:15,timeCaption:"Time",dateFormat:"h:mm aa",className:"w-full rounded-lg border border-gray-300 p-2.5",placeholderText:"Select time in",required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(l,{htmlFor:"timeOut",value:"Time Out"})}),e.jsx(C,{id:"timeOut",selected:s!=null&&s.time_out?new Date(s.time_out):null,onChange:t=>i("time_out",t.toISOString()),showTimeSelect:!0,showTimeSelectOnly:!0,timeIntervals:15,timeCaption:"Time",dateFormat:"h:mm aa",className:"w-full rounded-lg border border-gray-300 p-2.5",placeholderText:"Select time out",required:!0})]})]}),e.jsx("div",{className:"mt-4",children:e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(k,{id:"excludeLunch",checked:s==null?void 0:s.exclude_lunch_break,onChange:t=>i("exclude_lunch_break",t.target.checked)}),e.jsx(l,{htmlFor:"excludeLunch",className:"ml-2",children:"Exclude lunch break (1 hour)"})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 flex items-center",children:e.jsx(l,{htmlFor:"regularHours",value:"Regular Hours"})}),e.jsx(H,{id:"regularHours",type:"number",step:"0.5",min:"0",value:(s==null?void 0:s.regular_hours)||0,onChange:t=>i("regular_hours",parseFloat(t.target.value)),required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 flex items-center",children:e.jsx(l,{htmlFor:"overtimeHours",value:"Overtime Hours"})}),e.jsx(H,{id:"overtimeHours",type:"number",step:"0.5",min:"0",value:(s==null?void 0:s.overtime_hours)||0,onChange:t=>i("overtime_hours",parseFloat(t.target.value)),required:!0})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center",children:[e.jsx(l,{htmlFor:"nightDiffHours",value:"Night Differential Hours"}),e.jsx(W,{content:"Hours worked between 10:00 PM and 6:00 AM. For shifts that span across midnight (e.g., 5 PM to 1 AM), the system will automatically calculate the night differential hours.",children:e.jsx(X,{className:"ml-1 h-4 w-4 text-gray-500"})})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(H,{id:"nightDiffHours",type:"number",step:"0.5",min:"0",value:(s==null?void 0:s.night_diff_hours)||0,onChange:t=>i("night_diff_hours",parseFloat(t.target.value)),className:"flex-1",required:!0}),e.jsx(Z,{className:"ml-2 h-5 w-5 text-blue-600"})]})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(k,{id:"restDay",checked:s==null?void 0:s.is_rest_day,onChange:t=>i("is_rest_day",t.target.checked)}),e.jsx(l,{htmlFor:"restDay",className:"ml-2",children:"Rest Day"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(k,{id:"holiday",checked:s==null?void 0:s.is_holiday,onChange:t=>i("is_holiday",t.target.checked)}),e.jsx(l,{htmlFor:"holiday",className:"ml-2",children:"Holiday"})]})]}),(s==null?void 0:s.is_holiday)&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(l,{htmlFor:"holidayType",value:"Holiday Type"})}),e.jsxs(I,{id:"holidayType",value:(s==null?void 0:s.holiday_type)||"regular",onChange:t=>i("holiday_type",t.target.value),children:[e.jsx("option",{value:"regular",children:"Regular Holiday"}),e.jsx("option",{value:"special",children:"Special Holiday"})]})]})]}),e.jsxs("div",{className:"flex justify-between mt-6",children:[e.jsx(S,{color:"light",onClick:()=>f("/payroll/time-entries"),children:"Cancel"}),e.jsx(S,{color:"primary",type:"submit",isProcessing:L,children:"Save Changes"})]})]})})]})};export{ve as default};
