import{r as p,j as o,al as x,B as P,a2 as C,F as D,s as l}from"./index-C6AV3cVN.js";import{S as $}from"./SimpleSupplierProductModal-3fg5k0TC.js";import{u as F}from"./currencyFormatter-BsFWv3sX.js";const k=({supplierId:c,product:t,onSuccess:w,buttonSize:m="xs",buttonColor:E="light",currentUnitPrice:j,currentUomId:U})=>{var h;const[q,u]=p.useState(!1),[d,f]=p.useState(null),[N,_]=p.useState(!1),v=F();p.useEffect(()=>{(async()=>{if(!(!c||!t.id)){_(!0);try{const{data:a,error:e}=await l.from("supplier_products").select(`
            *,
            uom:uom_id (
              id,
              code,
              name
            )
          `).eq("supplier_id",c).eq("product_id",t.id).maybeSingle();e?console.error("Error fetching supplier product:",e):a&&f(a)}catch(a){console.error("Exception fetching supplier product:",a)}finally{_(!1)}}})()},[c,t.id]);const M=()=>{(async()=>{try{const{data:e,error:n}=await l.from("product_uoms").select(`
            uom_id,
            conversion_factor,
            is_default,
            is_purchasing_unit,
            uom:uom_id (
              id,
              code,
              name
            )
          `).eq("product_id",t.id).eq("is_default",!0).maybeSingle();n&&console.error("Error fetching product default UoM:",n);const{data:r,error:g}=await l.from("supplier_products").select(`
            *,
            uom:uom_id (
              id,
              code,
              name
            )
          `).eq("supplier_id",c).eq("product_id",t.id).maybeSingle();if(g)console.error("Error fetching updated supplier product:",g);else if(r){if(console.log("Fetched updated supplier product:",r),e&&e.uom_id){let y=!1;const s={};if(r.uom_id!==e.uom_id&&(console.log(`Updating supplier product UoM to default: ${e.uom_id}`),s.uom_id=e.uom_id,s.conversion_factor=e.conversion_factor,e.uom&&(s.uom_name=`${e.uom.name} (${e.uom.code})`),r.unit_price&&(s.base_price=r.unit_price*e.conversion_factor),y=!0),y){console.log("Updating supplier product with default UoM data:",s);const{error:S}=await l.from("supplier_products").update(s).eq("id",r.id);S?console.error("Error updating supplier product with default UoM:",S):(console.log("Updated supplier product with default UoM successfully"),Object.assign(r,s))}}const b=r.unit_price!==null&&r.unit_price!==void 0?r.unit_price:0;w(b,r.uom_id,r.conversion_factor||1)}else console.warn("No supplier product found after update"),u(!1)}catch(e){console.error("Exception fetching updated supplier product:",e),u(!1)}})(),u(!1),(async()=>{try{const{data:e,error:n}=await l.from("supplier_products").select(`
            *,
            uom:uom_id (
              id,
              code,
              name
            )
          `).eq("supplier_id",c).eq("product_id",t.id).maybeSingle();n?console.error("Error refreshing supplier product:",n):f(e||null)}catch(e){console.error("Exception refreshing supplier product:",e)}})()};return o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"flex items-center gap-2",children:d?o.jsx(x,{content:`Edit supplier product: ${v(d.unit_price||0)} per ${((h=d.uom)==null?void 0:h.code)||"unit"}`,children:o.jsx(P,{size:m,color:"primary",type:"button",onClick:async i=>{i.preventDefault(),i.stopPropagation(),u(!0)},children:o.jsx(C,{className:"h-4 w-4"})})}):o.jsx(x,{content:"Configure supplier price and UoM",children:o.jsx(P,{size:m,color:E,type:"button",onClick:async i=>{i.preventDefault(),i.stopPropagation(),u(!0)},children:o.jsx(D,{className:"h-4 w-4"})})})}),o.jsx($,{show:q,onClose:()=>u(!1),onSuccess:M,supplierId:c,product:t,currentUnitPrice:j,currentUomId:U})]})};export{k as P};
