import{d as K,h as V,r as l,j as e,ao as R,B as i,a0 as W,A as q,J as U,P as X,Q as Y,a7 as f,W as Z,t as ee,i as se,_ as t,aj as ae,L as H,k as te,e as c,E as re}from"./index-C6AV3cVN.js";import{C as le}from"./Card-yj7fueH8.js";import{g as ie}from"./inventoryReceipt-CBnPt-rA.js";import{c as ne}from"./formatters-Cypx7G-j.js";import{E as ce}from"./EmptyState-743bE0hR.js";import{g as oe}from"./payables-q7zOb02j.js";import"./inventoryTransaction-1UXV5RDN.js";import"./floatInventory-k_pEQeIK.js";const fe=()=>{const m=K(),{currentOrganization:o}=V(),[x,O]=l.useState([]),[_,y]=l.useState(!0),[v,p]=l.useState(null),[N,C]=l.useState(""),[b,w]=l.useState(""),[z,E]=l.useState(""),[A,I]=l.useState({}),[r,S]=l.useState(1),[u,F]=l.useState(10),[d,D]=l.useState(1);l.useEffect(()=>{o&&j()},[o]);const j=async()=>{if(o){y(!0),p(null);try{const s={searchQuery:N||void 0,status:b||void 0},{receipts:a,error:n}=await ie(o.id,s);if(n)p(n);else if(O(a),D(Math.ceil(a.length/u)),a.length>0){const M=a.map(J=>J.id),{receiptsStatus:G}=await oe(o.id,M);I(G)}}catch(s){p(s.message||"An error occurred while fetching inventory receipts")}finally{y(!1)}}},P=()=>{j()},L=()=>{C(""),w(""),j()},B=()=>{m("/inventory/receipts/create")},h=s=>{S(s)},Q=s=>{F(s),S(1)},T=s=>{switch(s){case"draft":return e.jsx(c,{color:"warning",children:"Draft"});case"completed":return e.jsx(c,{color:"success",children:"Completed"});case"cancelled":return e.jsx(c,{color:"failure",children:"Cancelled"});default:return e.jsx(c,{color:"info",children:s})}},g=r*u,k=g-u,$=x.slice(k,g);return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(le,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold flex items-center",children:[e.jsx(R,{className:"mr-2 h-6 w-6"}),"Inventory Receipts"]}),e.jsx("p",{className:"text-gray-500",children:"Manage goods receipt notes (GRNs) for inventory received from suppliers."})]}),e.jsxs(i,{color:"primary",onClick:B,className:"px-4 py-2 font-medium shadow-sm hover:shadow-md transition-all",size:"lg",children:[e.jsx(W,{className:"mr-2 h-5 w-5"}),"Create Receipt"]})]}),v&&e.jsx(q,{color:"failure",icon:U,className:"mb-4",children:v}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[e.jsx("div",{className:"flex-1",children:e.jsx(X,{id:"search",type:"text",placeholder:"Search by receipt number...",value:N,onChange:s=>C(s.target.value),icon:Y,onKeyDown:s=>s.key==="Enter"&&P()})}),e.jsx("div",{className:"w-full md:w-48",children:e.jsxs(f,{id:"status",value:b,onChange:s=>w(s.target.value),children:[e.jsx("option",{value:"",children:"All Statuses"}),e.jsx("option",{value:"draft",children:"Draft"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"cancelled",children:"Cancelled"})]})}),e.jsx("div",{className:"w-full md:w-48",children:e.jsxs(f,{id:"payableStatus",value:z,onChange:s=>E(s.target.value),children:[e.jsx("option",{value:"",children:"All Payable Status"}),e.jsx("option",{value:"sent",children:"Sent to Payables"}),e.jsx("option",{value:"not_sent",children:"Not Sent to Payables"})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(i,{color:"primary",onClick:P,children:[e.jsx(Z,{className:"mr-2 h-5 w-5"}),"Filter"]}),e.jsxs(i,{color:"gray",onClick:L,children:[e.jsx(ee,{className:"mr-2 h-5 w-5"}),"Reset"]})]})]}),_?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(se,{size:"xl"})}):x.length===0?e.jsx(ce,{title:"No inventory receipts found",description:"Create your first inventory receipt to start tracking goods received from suppliers.",icon:e.jsx(R,{className:"h-12 w-12"})}):e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs(t,{hoverable:!0,children:[e.jsxs(t.Head,{children:[e.jsx(t.HeadCell,{children:"Receipt Number"}),e.jsx(t.HeadCell,{children:"Purchase Order"}),e.jsx(t.HeadCell,{children:"Supplier"}),e.jsx(t.HeadCell,{children:"Status"}),e.jsx(t.HeadCell,{children:"Payable Status"}),e.jsx(t.HeadCell,{children:"Date"}),e.jsx(t.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(t.Body,{className:"divide-y",children:$.map(s=>{var a;return e.jsxs(t.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800 hover:bg-gray-50",onClick:()=>m(`/inventory/receipts/${s.id}`),style:{cursor:"pointer"},children:[e.jsx(t.Cell,{className:"font-medium",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ae,{className:"mr-2 h-5 w-5 text-gray-500"}),s.receipt_number]})}),e.jsx(t.Cell,{children:s.purchase_order?e.jsx(H,{to:`/purchases/orders/${s.purchase_order.id}`,onClick:n=>n.stopPropagation(),className:"text-blue-600 hover:text-blue-800 hover:underline",children:s.purchase_order.order_number}):e.jsx("span",{className:"text-gray-500",children:"Direct Receipt"})}),e.jsx(t.Cell,{children:(a=s.purchase_order)!=null&&a.supplier_name?e.jsxs("div",{className:"flex items-center",children:[e.jsx(te,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx(H,{to:`/suppliers/${s.purchase_order.supplier_id}`,onClick:n=>n.stopPropagation(),className:"text-blue-600 hover:text-blue-800 hover:underline",children:s.purchase_order.supplier_name})]}):e.jsx("span",{className:"text-gray-500",children:"N/A"})}),e.jsx(t.Cell,{children:T(s.status)}),e.jsx(t.Cell,{children:s.status==="completed"?A[s.id]?e.jsx(c,{color:"success",children:"Sent to Payables"}):e.jsx(c,{color:"warning",children:"Not Sent"}):e.jsx(c,{color:"gray",children:"N/A"})}),e.jsx(t.Cell,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(re,{className:"mr-2 h-5 w-5 text-gray-500"}),ne(s.receipt_date)]})}),e.jsx(t.Cell,{children:e.jsx("div",{className:"flex gap-2",children:e.jsx(i,{size:"xs",color:"gray",onClick:n=>{n.stopPropagation(),m(`/inventory/receipts/${s.id}`)},children:"View"})})})]},s.id)})})]}),e.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between mt-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2 md:mb-0",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Items per page:"}),e.jsxs(f,{id:"itemsPerPage",value:u,onChange:s=>Q(parseInt(s.target.value)),className:"w-20",sizing:"sm",children:[e.jsx("option",{value:"5",children:"5"}),e.jsx("option",{value:"10",children:"10"}),e.jsx("option",{value:"25",children:"25"}),e.jsx("option",{value:"50",children:"50"}),e.jsx("option",{value:"100",children:"100"})]}),e.jsxs("span",{className:"text-sm text-gray-500",children:["Showing ",k+1," to ",Math.min(g,x.length)," of ",x.length," results"]})]}),e.jsxs("div",{className:"flex gap-2 items-center flex-wrap",children:[e.jsx(i,{color:"gray",onClick:()=>h(1),disabled:r===1,size:"xs",children:"First"}),e.jsx(i,{color:"gray",onClick:()=>h(r-1),disabled:r===1,size:"sm",children:"Previous"}),e.jsx("div",{className:"flex gap-1",children:[...Array(d)].map((s,a)=>a===0||a===d-1||a>=r-2&&a<=r+2?e.jsx(i,{color:r===a+1?"blue":"gray",onClick:()=>h(a+1),size:"xs",className:"min-w-[32px]",children:a+1},a):a===r-3||a===r+3?e.jsx("span",{children:"..."},a):null)}),e.jsx(i,{color:"gray",onClick:()=>h(r+1),disabled:r===d,size:"sm",children:"Next"}),e.jsx(i,{color:"gray",onClick:()=>h(d),disabled:r===d,size:"xs",children:"Last"})]})]})]})]})})};export{fe as default};
