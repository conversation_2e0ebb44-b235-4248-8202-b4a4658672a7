import{j as e,a0 as l,B as c}from"./index-C6AV3cVN.js";import{C as d}from"./CardBox-YV_4IKGE.js";const n=({title:m,description:t,icon:x,actions:s,actionLabel:r,onAction:a})=>e.jsxs(d,{className:"flex flex-col items-center justify-center py-12 px-4 text-center",children:[e.jsx("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:x||e.jsx(l,{className:"h-12 w-12"})}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:m}),t&&e.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6 max-w-md",children:t}),s&&e.jsx("div",{className:"mt-2",children:s}),!s&&r&&a&&e.jsx("div",{className:"mt-2",children:e.jsx(c,{color:"primary",onClick:a,children:r})})]});export{n as E};
