import{d as cr,h as ir,b as ar,r as I,s as _,j as e,i as A,aj as nr,B as E,aD as ur,A as dr,J as lr,a6 as U,a7 as F,P as pr,ad as mr,a0 as fr,_ as f,a3 as hr,a9 as gr}from"./index-C6AV3cVN.js";import{C as vr}from"./Card-yj7fueH8.js";import{c as _r}from"./purchaseOrder-DppPMsdd.js";import{g as Ir}from"./supplier-BJDz25mb.js";import{g as xr}from"./product-Ca8DWaNR.js";import{g as yr,a as O,b as B}from"./SimpleSupplierProductModal-3fg5k0TC.js";import{u as jr}from"./currencyFormatter-BsFWv3sX.js";import{a as Sr}from"./formatters-Cypx7G-j.js";import{U as wr}from"./UomSelector-CTe3hN2d.js";import{P as Nr}from"./PurchaseOrderSupplierProductButton-Diy-5mp2.js";import{E as J}from"./EnhancedNumberInput-Bwo1E3yF.js";import"./productUom-k6aUg6b7.js";const Tr=()=>{var k;const $=cr(),{currentOrganization:v}=ir(),{user:R}=ar(),P=jr(),[m,w]=I.useState(""),[b,L]=I.useState(""),[q,Q]=I.useState(""),[h,x]=I.useState([]),[C,W]=I.useState([]),[N,G]=I.useState([]),[K,V]=I.useState([]),[X,M]=I.useState(!0),[D,H]=I.useState(!1),[T,g]=I.useState(null);I.useEffect(()=>((async()=>{if(v){M(!0),g(null);try{const i=localStorage.getItem("purchaseOrderItems"),r=localStorage.getItem("purchaseOrderSupplierId");if(i)try{const n=JSON.parse(i);x(n)}catch(n){console.error("Error parsing stored items:",n),localStorage.removeItem("purchaseOrderItems")}const{suppliers:c,error:o}=await Ir(v.id);if(o){console.error("Error fetching suppliers:",o),g(o);return}if(W(c),r&&c.some(n=>n.id===r))w(r);else if(c.length===1&&!r)w(c[0].id);else if(!r){const n=c.find(l=>l.is_default);n&&w(n.id)}const{products:d,error:t}=await xr(v.id);if(t){console.error("Error fetching products:",t),g(t);return}G(d);const{uoms:a,error:u}=await gr(v.id);if(u){console.error("Error fetching UoMs:",u),g(u);return}V(a)}catch(i){console.error("Error fetching data:",i),g(i.message||"An error occurred while fetching data")}finally{M(!1)}}})(),()=>{}),[v]),I.useEffect(()=>{m&&(localStorage.setItem("purchaseOrderSupplierId",m),h.length>0&&Y(m))},[m]);const Y=async s=>{const i=h.filter(r=>r.productId).map(r=>r.productId);if(i.length!==0)try{const{supplierProducts:r,error:c}=await yr(s,i);if(c){console.error("Error fetching supplier products:",c);return}if(r.length>0){const o={};for(const t of r)t.unit_price?t.uom&&t.uom.supplier_price!==void 0&&t.uom.supplier_price!==null?o[t.product_id]={price:t.uom.supplier_price,uomId:t.uom_id||null,conversionFactor:t.conversion_factor||1}:o[t.product_id]={price:t.unit_price,uomId:t.uom_id||null,conversionFactor:t.conversion_factor||1}:o[t.product_id]={price:0,uomId:null,conversionFactor:1};await(async()=>{for(const t of i)if(!o[t]){o[t]={price:0,uomId:null,conversionFactor:1};try{const{data:a,error:u}=await _.from("products").select(`
                    id,
                    default_uom_id
                  `).eq("id",t).single();if(u)console.error("Error fetching product:",u);else if(a&&a.default_uom_id){const{data:n,error:l}=await _.from("product_uoms").select("conversion_factor").eq("product_id",t).eq("uom_id",a.default_uom_id).maybeSingle();l?console.error("Error fetching product UoM conversion factor:",l):n?o[t]={price:0,uomId:a.default_uom_id,conversionFactor:n.conversion_factor}:o[t]={price:0,uomId:a.default_uom_id,conversionFactor:1}}else{const{data:n,error:l}=await _.from("product_uoms").select(`
                      uom_id,
                      conversion_factor
                    `).eq("product_id",t).limit(1);l?console.error("Error fetching product UoMs:",l):n&&n.length>0&&(o[t]={price:0,uomId:n[0].uom_id,conversionFactor:n[0].conversion_factor})}}catch(a){console.error("Error fetching default UoM for product:",a)}}})(),console.log("Price map created:",o),x(t=>{const a=t.map(u=>{if(u.productId&&o[u.productId]){const n=o[u.productId];console.log(`Updating price for product ${u.productId} to ${n.price}`);const l={...u,unitPrice:n.price,conversionFactor:n.conversionFactor};return console.log(`Updating conversion factor for product ${u.productId} to ${n.conversionFactor}`),n.uomId&&(console.log(`Updating UoM for product ${u.productId} to ${n.uomId}`),l.uomId!==n.uomId&&(l.uomId=n.uomId)),l}return u});return console.log("Updated items:",a),a})}else{const{data:o}=await _.from("products").select(`
            id,
            unit_price,
            default_uom_id
          `).in("id",i);if(o&&o.length>0){const d={};for(const t of o)if(t.unit_price)if(d[t.id]={price:t.unit_price,uomId:t.default_uom_id||null,conversionFactor:1},t.default_uom_id)try{const{data:a,error:u}=await _.from("product_uoms").select("conversion_factor").eq("product_id",t.id).eq("uom_id",t.default_uom_id).maybeSingle();u?console.error("Error fetching product UoM conversion factor:",u):a&&(d[t.id].conversionFactor=a.conversion_factor)}catch(a){console.error("Error fetching conversion factor:",a)}else try{const{data:a,error:u}=await _.from("product_uoms").select(`
                      uom_id,
                      conversion_factor
                    `).eq("product_id",t.id).limit(1);u?console.error("Error fetching product UoMs:",u):a&&a.length>0&&(d[t.id].uomId=a[0].uom_id,d[t.id].conversionFactor=a[0].conversion_factor)}catch(a){console.error("Error fetching UoMs:",a)}x(t=>t.map(a=>{if(a.productId&&d[a.productId]){const u=d[a.productId];return{...a,unitPrice:u.price,...u.uomId&&{uomId:u.uomId},conversionFactor:u.conversionFactor}}return a}))}}}catch(r){console.error("Error updating prices for supplier:",r)}},Z=()=>{if(N.length===0)return;const s={productId:"",productName:"",quantity:1,uomId:"",unitPrice:0,conversionFactor:1};x(i=>{const r=[...i,s];return localStorage.setItem("purchaseOrderItems",JSON.stringify(r)),r})},rr=s=>{x(i=>{const r=i.filter((c,o)=>o!==s);return localStorage.setItem("purchaseOrderItems",JSON.stringify(r)),r})},S=async(s,i,r)=>{if(x(c=>{const o=[...c];if(o[s]={...o[s],[i]:r},i==="productId"){const d=N.find(t=>t.id===r);o[s].productName=(d==null?void 0:d.name)||"",o[s].uomId="",o[s].conversionFactor=1,m&&er(m,r,s)}return i==="uomId"&&(o[s].conversionFactor=1),localStorage.setItem("purchaseOrderItems",JSON.stringify(o)),o}),i==="unitPrice"&&m&&v){const c=h[s];if(c.productId)try{if(r>0){let o,d;if(c.uomId)try{const{data:n,error:l}=await _.from("units_of_measurement").select("id, name, code").eq("id",c.uomId).single();if(l)console.error("Error fetching UoM details:",l);else if(n){o=`${n.name} (${n.code})`;const{data:y,error:p}=await _.from("product_uoms").select("conversion_factor").eq("product_id",c.productId).eq("uom_id",c.uomId).maybeSingle();p?console.error("Error fetching product UoM conversion factor:",p):y&&(d=y.conversion_factor)}}catch(n){console.error("Exception fetching UoM details:",n)}const{success:t,supplierProduct:a,error:u}=await O(m,c.productId,r,v.id,c.uomId,o,d);t||console.error("Error adding/updating supplier product:",u)}else if(r===0){const{supplierProduct:o}=await B(m,c.productId);if(o&&o.unit_price)if(window.confirm(`This product is currently supplied by this supplier at a price of ${o.unit_price}. Setting the price to 0 indicates this product is not supplied by this supplier. Do you want to continue?`)){let t,a;if(c.uomId)try{const{data:n,error:l}=await _.from("units_of_measurement").select("id, name, code").eq("id",c.uomId).single();if(l)console.error("Error fetching UoM details:",l);else if(n){t=`${n.name} (${n.code})`;const{data:y,error:p}=await _.from("product_uoms").select("conversion_factor").eq("product_id",c.productId).eq("uom_id",c.uomId).maybeSingle();p?console.error("Error fetching product UoM conversion factor:",p):y&&(a=y.conversion_factor)}}catch(n){console.error("Exception fetching UoM details for zero price update:",n)}const{success:u}=await O(m,c.productId,0,v.id,c.uomId,t,a)}else x(t=>{const a=[...t];return a[s]={...a[s],unitPrice:o.unit_price||0},a})}}catch(o){console.error("Error in handleItemChange when adding supplier product:",o)}}},er=async(s,i,r)=>{var c;try{const{data:o,error:d}=await _.from("product_uoms").select(`
          uom_id,
          conversion_factor,
          is_default,
          uom:uom_id (
            id,
            code,
            name
          )
        `).eq("product_id",i).eq("is_default",!0).maybeSingle();d&&console.error("Error fetching product default UoM:",d);const{supplierProduct:t}=await B(s,i);let a=null,u=null,n=null,l=1;if(t)if(t.uom&&t.uom.supplier_price!==void 0&&t.uom.supplier_price!==null?a=t.uom.supplier_price:a=t.unit_price||0,o&&o.uom_id){if(u=o.uom_id,l=o.conversion_factor||1,o.uom&&(n=`${o.uom.name} (${o.uom.code})`),console.log(`Using default UoM: ${n} (${u}) with conversion factor: ${l}`),t.uom_id!==u){console.log(`Updating supplier product UoM to default: ${u}`);const{error:p}=await _.from("supplier_products").update({uom_id:u,conversion_factor:l,uom_name:n,base_price:a?a*l:null}).eq("id",t.id);p&&console.error("Error updating supplier product with default UoM:",p)}}else t.uom_id&&(u=t.uom_id,n=((c=t.uom)==null?void 0:c.name)||t.uom_name||"Unknown",l=t.conversion_factor||1);else if(a=0,o&&o.uom_id){if(u=o.uom_id,l=o.conversion_factor||1,o.uom&&(n=`${o.uom.name} (${o.uom.code})`),v)try{const{data:p,error:j}=await _.from("supplier_products").insert({supplier_id:s,product_id:i,unit_price:0,uom_id:u,uom_name:n,conversion_factor:l,organization_id:v.id,base_price:0}).select().single();j&&console.error("Error creating supplier product with default UoM:",j)}catch(p){console.error("Exception creating supplier product with default UoM:",p)}}else{console.warn("No default UoM found for product and no supplier product exists");try{const{data:p,error:j}=await _.from("product_uoms").select(`
                uom_id,
                conversion_factor,
                uom:uom_id (
                  id,
                  name,
                  code
                )
              `).eq("product_id",i).limit(1);j?console.error("Error fetching product UoMs:",j):p&&p.length>0&&p[0].uom&&(u=p[0].uom_id,n=`${p[0].uom.name} (${p[0].uom.code})`,l=p[0].conversion_factor)}catch(p){console.error("Error fetching any UoM for product:",p)}}x(p=>{const j=[...p],z={...j[r],unitPrice:a,conversionFactor:l};return u&&(z.uomId=u),j[r]=z,j});const y=[...h];y[r]&&(y[r]={...y[r],unitPrice:a,conversionFactor:l,...u&&{uomId:u}},localStorage.setItem("purchaseOrderItems",JSON.stringify(y)))}catch(o){console.error("Error fetching supplier price:",o)}},or=()=>h.reduce((s,i)=>{const r=Number(i.conversionFactor),c=i.quantity*r*i.unitPrice;return s+c},0),tr=async s=>{if(s.preventDefault(),!v||!R){g("Missing required data");return}let i=m;if(!i){const r=C.find(c=>c.is_default);if(r)i=r.id;else{g("No supplier available. Please contact support.");return}}if(h.length===0){g("Please add at least one item to the purchase order");return}for(let r=0;r<h.length;r++){const c=h[r];if(!c.productId){g(`Please select a product for item #${r+1}`);return}if(!c.uomId){g(`Please select a unit of measurement for item #${r+1}`);return}if(c.quantity<=0){g(`Please enter a valid quantity for item #${r+1}`);return}if(typeof c.unitPrice!="number"||c.unitPrice<0||isNaN(c.unitPrice)){g(`Please enter a valid unit price (0 or greater) for item #${r+1}`);return}}H(!0),g(null);try{const r={supplierId:i,expectedDeliveryDate:b?new Date(b).toISOString():void 0,notes:q||void 0,items:h.map(d=>({productId:d.productId,quantity:d.quantity,uomId:d.uomId,unitPrice:d.unitPrice,conversionFactor:d.conversionFactor||1}))},{purchaseOrder:c,error:o}=await _r(v.id,r);o?g(o):c&&(localStorage.removeItem("purchaseOrderItems"),localStorage.removeItem("purchaseOrderSupplierId"),$(`/purchases/orders/${c.id}`))}catch(r){g(r.message||"An error occurred while creating the purchase order")}finally{H(!1)}},sr=()=>{$("/purchases/orders")};return X?e.jsx("div",{className:"container mx-auto px-4 py-8 flex justify-center",children:e.jsx(A,{size:"xl"})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(vr,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold flex items-center",children:[e.jsx(nr,{className:"mr-2 h-6 w-6"}),"Create Purchase Order"]}),e.jsx("p",{className:"text-gray-500",children:"Create a new purchase order for a supplier"})]}),e.jsxs(E,{color:"gray",onClick:sr,children:[e.jsx(ur,{className:"mr-2 h-5 w-5"}),"Cancel"]})]}),T&&e.jsx(dr,{color:"failure",icon:lr,className:"mb-4",children:T}),e.jsxs("form",{onSubmit:tr,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 block",children:[e.jsx(U,{htmlFor:"supplier_id",value:"Supplier (Optional)"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Leave empty to use default supplier, or select a specific supplier"})]}),e.jsxs(F,{id:"supplier_id",value:m,onChange:s=>w(s.target.value),children:[e.jsx("option",{value:"",children:"Use Default Supplier"}),C.filter(s=>!s.is_default).map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]}),!m&&e.jsxs("p",{className:"text-xs text-blue-600 mt-1",children:['ℹ️ Using "',((k=C.find(s=>s.is_default))==null?void 0:k.name)||"Default Supplier",'" - you can change the supplier later']})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(U,{htmlFor:"expected_delivery_date",value:"Expected Delivery Date"})}),e.jsx(pr,{id:"expected_delivery_date",type:"date",value:b,onChange:s=>L(s.target.value),min:new Date().toISOString().split("T")[0]})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(U,{htmlFor:"notes",value:"Notes"})}),e.jsx(mr,{id:"notes",value:q,onChange:s=>Q(s.target.value),placeholder:"Enter any additional notes",rows:3})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx(U,{value:"Items"}),e.jsxs(E,{size:"xs",color:"primary",onClick:Z,children:[e.jsx(fr,{className:"mr-2 h-4 w-4"}),"Add Item"]})]}),h.length===0?e.jsx("div",{className:"p-4 bg-gray-50 rounded-lg text-center text-gray-500",children:'No items added. Click "Add Item" to add products to this purchase order.'}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(f,{children:[e.jsxs(f.Head,{children:[e.jsx(f.HeadCell,{children:"Product"}),e.jsx(f.HeadCell,{children:"Quantity"}),e.jsx(f.HeadCell,{children:"Unit"}),e.jsx(f.HeadCell,{children:"Unit Price"}),e.jsx(f.HeadCell,{children:"Total"}),e.jsx(f.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(f.Body,{className:"divide-y",children:h.map((s,i)=>e.jsxs(f.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(f.Cell,{children:e.jsxs(F,{value:s.productId,onChange:r=>S(i,"productId",r.target.value),required:!0,className:"text-sm",children:[e.jsx("option",{value:"",children:"Select a product"}),N.map(r=>e.jsx("option",{value:r.id,children:r.name},r.id))]})}),e.jsx(f.Cell,{children:e.jsx(J,{min:"0.01",step:"0.01",value:s.quantity,onChange:r=>S(i,"quantity",parseFloat(r.target.value)||0),onBlur:r=>{(r.target.value===""||parseFloat(r.target.value)<=0)&&S(i,"quantity",1)},required:!0,className:"w-24 text-sm",sizing:"sm",autoSelect:!0,preventScrollChange:!0})}),e.jsx(f.Cell,{children:s.productId?e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx("div",{className:"flex-grow",children:e.jsx(wr,{productId:s.productId,value:s.uomId,onChange:r=>{x(c=>{const o=[...c];return o[i]={...o[i],uomId:r},o})},onUomChange:r=>{x(c=>{const o=[...c];return o[i]={...o[i],uomId:r.uom_id,conversionFactor:r.conversion_factor},localStorage.setItem("purchaseOrderItems",JSON.stringify(o)),o}),m&&h[i].productId&&v&&(async()=>{try{const c=`${r.uom.name} (${r.uom.code})`,{success:o,supplierProduct:d,error:t}=await O(m,h[i].productId,h[i].unitPrice,v.id,r.uom_id,c,r.conversion_factor);o||console.error("Error updating supplier product with new UoM:",t)}catch(c){console.error("Exception updating supplier product with new UoM:",c)}})()},filter:"purchasing",className:"text-sm"},`uom-selector-${s.productId}-${i}`)})}):e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx("div",{className:"flex-grow",children:e.jsx(F,{disabled:!0,className:"text-sm",children:e.jsx("option",{value:"",children:"Select a product first"})})})})}),e.jsx(f.Cell,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(J,{min:"0",step:"0.01",value:s.unitPrice,onChange:r=>S(i,"unitPrice",parseFloat(r.target.value)||0),onBlur:r=>{(r.target.value===""||parseFloat(r.target.value)<0)&&S(i,"unitPrice",.01)},required:!0,className:"w-24 text-sm",sizing:"sm",autoSelect:!0,preventScrollChange:!0}),m&&s.productId&&e.jsx(Nr,{supplierId:m,product:N.find(r=>r.id===s.productId)||{id:s.productId,name:s.productName},onSuccess:(r,c,o=1)=>{console.log(`Supplier product updated for item ${i}: price=${r}, uomId=${c}, conversionFactor=${o}`);const d=[...h];d[i]={...d[i],unitPrice:r,conversionFactor:o,...c&&{uomId:c}},x(d),localStorage.setItem("purchaseOrderItems",JSON.stringify(d)),console.log("Stored updated items in localStorage after supplier product update")},currentUnitPrice:s.unitPrice,currentUomId:s.uomId})]})}),e.jsx(f.Cell,{children:(()=>{var o;const r=Number(s.conversionFactor||1),c=s.quantity*r*s.unitPrice;return e.jsxs(e.Fragment,{children:[P(c),e.jsxs("div",{className:"text-xs text-gray-500",children:[Sr(s.quantity)," ",s.uomId&&((o=K.find(d=>d.id===s.uomId))==null?void 0:o.code)||"unit"," × ",P(s.unitPrice)]})]})})()}),e.jsx(f.Cell,{children:e.jsx(E,{color:"failure",size:"xs",onClick:()=>rr(i),children:e.jsx(hr,{className:"h-4 w-4"})})})]},i))})]})}),e.jsx("div",{className:"flex justify-end mt-4",children:e.jsxs("div",{className:"text-lg font-semibold",children:["Total: ",P(or())]})})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(E,{type:"submit",color:"primary",disabled:D,children:D?e.jsxs(e.Fragment,{children:[e.jsx(A,{size:"sm",className:"mr-2"}),"Creating..."]}):"Create Purchase Order"})})]})]})})};export{Tr as default};
