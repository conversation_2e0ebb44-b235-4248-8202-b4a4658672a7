import{s as a,b as ee,h as te,r as y,j as re,aH as T}from"./index-C6AV3cVN.js";const ae=async e=>{var s;try{const r=(s=(await a.auth.getUser()).data.user)==null?void 0:s.id;if(!r)throw new Error("User not authenticated");let d=a.from("chat_conversations").select(`
        *,
        participants:chat_participants(
          id,
          user_id,
          is_admin,
          last_read_message_id
        )
      `);e&&(d=d.eq("organization_id",e));const{data:t}=await a.from("chat_participants").select("conversation_id").eq("user_id",r);if(!t||t.length===0)return[];const i=t.map(g=>g.conversation_id);d=d.in("id",i);const{data:u,error:n}=await d.order("updated_at",{ascending:!1});if(n)throw n;if(!u||u.length===0)return[];const f=new Set;u.forEach(g=>{g.participants.forEach(w=>{f.add(w.user_id)})});const{data:E}=await a.from("profiles").select("id, first_name, last_name, avatar_url").in("id",Array.from(f)),h=new Map;E&&E.forEach(g=>{h.set(g.id,g)}),u.forEach(g=>{g.participants.forEach(w=>{w.profiles=h.get(w.user_id)||void 0})});const b=u.map(g=>g.id),S=new Map,m=5;for(let g=0;g<b.length;g+=m){const w=b.slice(g,g+m),{data:M}=await a.from("chat_messages").select("*").in("conversation_id",w).order("created_at",{ascending:!1});if(M){const _=M.reduce((U,q)=>((!U[q.conversation_id]||new Date(q.created_at)>new Date(U[q.conversation_id].created_at))&&(U[q.conversation_id]=q),U),{});Object.entries(_).forEach(([U,q])=>{S.set(U,q)})}}const $=new Map,x=u.map(g=>{const w=g.participants.find(M=>M.user_id===r);return{conversationId:g.id,lastReadMessageId:(w==null?void 0:w.last_read_message_id)||null}});for(let g=0;g<x.length;g+=m){const w=x.slice(g,g+m);await Promise.all(w.map(async({conversationId:M,lastReadMessageId:_})=>{let U=0;if(_){const q=await J(_);if(q){const{count:A}=await a.from("chat_messages").select("*",{count:"exact",head:!0}).eq("conversation_id",M).gt("created_at",q).neq("sender_id",r||"");U=A||0}}else{const{count:q}=await a.from("chat_messages").select("*",{count:"exact",head:!0}).eq("conversation_id",M).neq("sender_id",r||"");U=q||0}$.set(M,U)}))}return u.map(g=>({...g,last_message:S.get(g.id)||void 0,unread_count:$.get(g.id)||0}))}catch(r){throw console.error("Error in getUserConversations:",r),r}},se=async(e,s)=>{var u;const r=(u=(await a.auth.getUser()).data.user)==null?void 0:u.id;if(!r)throw new Error("User not authenticated");let d=a.from("chat_conversations").select(`
      *,
      participants:chat_participants(
        id,
        user_id,
        is_admin,
        last_read_message_id
      )
    `).eq("id",e);s&&(d=d.eq("organization_id",s));const{data:t,error:i}=await d.maybeSingle();if(i)throw i;if(!t)throw new Error("Conversation not found");if(t&&t.participants.length>0){const n=t.participants.map(m=>m.user_id),{data:f}=await a.from("profiles").select("id, first_name, last_name, avatar_url").in("id",n),E=new Map;f&&f.forEach(m=>{E.set(m.id,m)}),t.participants.forEach(m=>{m.profiles=E.get(m.user_id)||void 0});const{data:h}=await a.from("chat_messages").select("*").eq("conversation_id",t.id).order("created_at",{ascending:!1}).limit(1).maybeSingle();h&&(t.last_message=h);const b=t.participants.find(m=>m.user_id===r);let S=0;if(b&&b.last_read_message_id){const m=await J(b.last_read_message_id);if(m){const{count:$}=await a.from("chat_messages").select("*",{count:"exact",head:!0}).eq("conversation_id",t.id).gt("created_at",m).neq("sender_id",r||"");S=$||0}}else{const{count:m}=await a.from("chat_messages").select("*",{count:"exact",head:!0}).eq("conversation_id",t.id).neq("sender_id",r||"");S=m||0}t.unread_count=S}return t},oe=async(e,s)=>{var h;const{limit:r=50,offset:d=0,searchQuery:t=""}=s||{},i=(h=(await a.auth.getUser()).data.user)==null?void 0:h.id;if(!i)throw new Error("User not authenticated");let u=a.from("chat_messages").select("*").eq("conversation_id",e);t.trim()&&(u=u.ilike("content",`%${t.trim()}%`));const{data:n,error:f}=await u.order("created_at",{ascending:!1}).range(d,d+r-1);if(f)throw f;let E=[];if(n&&n.length>0){const b=n.map(_=>_.id),S=[...new Set(n.map(_=>_.sender_id))],[m,$]=await Promise.all([a.from("chat_message_status").select("message_id, is_read").in("message_id",b).eq("user_id",i||""),a.from("profiles").select("id, first_name, last_name, avatar_url").in("id",S)]),x=m.data,O=$.data,g=new Map;x&&x.forEach(_=>{g.set(_.message_id,_.is_read)});const w=new Map;O&&O.forEach(_=>{w.set(_.id,_)}),E=n.map(_=>({..._,is_read:g.get(_.id)||!1,sender:w.get(_.sender_id)||void 0}));const M=n.filter(_=>_.sender_id!==i&&!g.get(_.id)).map(_=>_.id);M.length>0&&Z(e,M).catch(_=>console.error("Error marking messages as read:",_))}return E},ne=async(e,s,r)=>{var E;const d=(E=(await a.auth.getUser()).data.user)==null?void 0:E.id;if(!d)throw new Error("User not authenticated");const{data:t,error:i}=await a.from("chat_conversations").select("organization_id").eq("id",e).maybeSingle();if(i||!t)throw new Error("Conversation not found");const u={conversation_id:e,organization_id:t.organization_id,content:s,sender_id:d};r&&(u.attachment_type=r.type,u.attachment_url=r.url,u.attachment_name=r.name,u.attachment_size=r.size);const{data:n,error:f}=await a.from("chat_messages").insert(u).select().maybeSingle();if(!n)throw new Error("Failed to create message");if(f)throw f;return await a.from("chat_conversations").update({updated_at:new Date().toISOString()}).eq("id",e),n},ie=async e=>{var i;const s=(i=(await a.auth.getUser()).data.user)==null?void 0:i.id;if(!s)throw new Error("User not authenticated");const{data:r,error:d}=await a.from("chat_messages").select("sender_id, conversation_id").eq("id",e).maybeSingle();if(!r)throw new Error("Message not found");if(d)throw d;if(r.sender_id!==s)throw new Error("You can only delete your own messages");const{error:t}=await a.from("chat_messages").delete().eq("id",e);if(t)throw t;await a.from("chat_conversations").update({updated_at:new Date().toISOString()}).eq("id",r.conversation_id)},ce=async(e,s)=>{var n;const r=(n=(await a.auth.getUser()).data.user)==null?void 0:n.id;if(!r)throw new Error("User not authenticated");const{data:d,error:t}=await a.from("chat_messages").select("sender_id, conversation_id").eq("id",e).maybeSingle();if(!d)throw new Error("Message not found");if(t)throw t;if(d.sender_id!==r)throw new Error("You can only edit your own messages");const{data:i,error:u}=await a.from("chat_messages").update({content:s,updated_at:new Date().toISOString()}).eq("id",e).select().maybeSingle();if(!i)throw new Error("Failed to update message");if(u)throw u;return await a.from("chat_conversations").update({updated_at:new Date().toISOString()}).eq("id",d.conversation_id),i},de=async(e,s)=>{var f;const r=(f=(await a.auth.getUser()).data.user)==null?void 0:f.id;if(!r)throw new Error("User not authenticated");const{data:d,error:t}=await a.from("chat_conversations").select(`
      *,
      participants:chat_participants(user_id)
    `).eq("organization_id",e).eq("is_group",!1);if(t)throw t;const i=d==null?void 0:d.find(E=>{const h=E.participants.map(b=>b.user_id);return h.includes(r)&&h.includes(s)});if(i)return i;const{data:u,error:n}=await a.from("chat_conversations").insert({organization_id:e,is_group:!1,created_by:r}).select().maybeSingle();if(!u)throw new Error("Failed to create conversation");if(n)throw n;return await a.from("chat_participants").insert([{conversation_id:u.id,user_id:r,is_admin:!0},{conversation_id:u.id,user_id:s,is_admin:!1}]),u},le=async(e,s,r)=>{var n;const d=(n=(await a.auth.getUser()).data.user)==null?void 0:n.id;if(!d)throw new Error("User not authenticated");if(!s)throw new Error("Group name is required");const{data:t,error:i}=await a.from("chat_conversations").insert({organization_id:e,name:s,is_group:!0,created_by:d}).select().maybeSingle();if(!t)throw new Error("Failed to create group conversation");if(i)throw i;const u=[{conversation_id:t.id,user_id:d,is_admin:!0},...r.map(f=>({conversation_id:t.id,user_id:f,is_admin:!1}))];return await a.from("chat_participants").insert(u),t},Z=async(e,s)=>{var t;const r=(t=(await a.auth.getUser()).data.user)==null?void 0:t.id;if(!r)throw new Error("User not authenticated");if(s.length===0)return;await a.from("chat_participants").update({last_read_message_id:s[s.length-1]}).eq("conversation_id",e).eq("user_id",r);const d=s.map(i=>({message_id:i,user_id:r,is_read:!0,read_at:new Date().toISOString()}));await a.from("chat_message_status").upsert(d,{onConflict:"message_id,user_id"})},J=async e=>{const{data:s,error:r}=await a.from("chat_messages").select("created_at").eq("id",e).maybeSingle();return r||!s?null:s.created_at},ue=(e,s,r)=>{console.log(`Setting up realtime subscription for conversations in organization ${e}`);const d=`org-conversations:${e}`;console.log(`Creating channel with name: ${d}`),a.getChannels().forEach(i=>{i.topic.includes(`org-conversations:${e}`)&&(console.log(`Removing existing channel: ${i.topic}`),a.removeChannel(i))});const t=a.channel(d).on("postgres_changes",{event:"INSERT",schema:"public",table:"chat_conversations",filter:`organization_id=eq.${e}`},i=>{console.log("New conversation created via realtime:",i.new),s(i.new)}).on("postgres_changes",{event:"UPDATE",schema:"public",table:"chat_conversations",filter:`organization_id=eq.${e}`},i=>{console.log("Conversation updated via realtime:",i.new),s(i.new)}).on("postgres_changes",{event:"DELETE",schema:"public",table:"chat_conversations",filter:`organization_id=eq.${e}`},i=>{console.log("Conversation deleted via realtime:",i.old),r(i.old.id)});return t.subscribe(i=>{console.log(`Subscription status for organization conversations ${e}:`,i),i==="SUBSCRIBED"?console.log(`Successfully subscribed to organization conversations ${e}`):i==="CHANNEL_ERROR"?(console.error(`Error subscribing to organization conversations ${e}`),setTimeout(()=>{console.log(`Attempting to reconnect to organization conversations ${e}`),t.subscribe()},2e3)):i==="TIMED_OUT"&&(console.error(`Subscription timed out for organization conversations ${e}`),setTimeout(()=>{console.log(`Attempting to reconnect to organization conversations ${e}`),t.subscribe()},2e3))}),()=>{console.log(`Removing realtime subscription for organization conversations ${e}`),a.removeChannel(t)}},fe=(e,s)=>{console.log(`Setting up realtime subscription for conversation ${e}`);const r=`conversation:${e}`;console.log(`Creating channel with name: ${r}`),a.getChannels().forEach(t=>{t.topic.includes(`conversation:${e}`)&&(console.log(`Removing existing channel: ${t.topic}`),a.removeChannel(t))});const d=a.channel(r).on("postgres_changes",{event:"INSERT",schema:"public",table:"chat_messages",filter:`conversation_id=eq.${e}`},t=>{console.log("Received message via realtime:",t.new),s(t.new)}).on("postgres_changes",{event:"UPDATE",schema:"public",table:"chat_messages",filter:`conversation_id=eq.${e}`},t=>{console.log("Message updated via realtime:",t.new),s(t.new)}).on("postgres_changes",{event:"DELETE",schema:"public",table:"chat_messages",filter:`conversation_id=eq.${e}`},t=>{console.log("Message deleted via realtime:",t.old)});return d.subscribe(t=>{console.log(`Subscription status for conversation ${e}:`,t),t==="SUBSCRIBED"?console.log(`Successfully subscribed to conversation ${e}`):t==="CHANNEL_ERROR"?(console.error(`Error subscribing to conversation ${e}`),setTimeout(()=>{console.log(`Attempting to reconnect to conversation ${e}`),d.subscribe()},2e3)):t==="TIMED_OUT"&&(console.error(`Subscription timed out for conversation ${e}`),setTimeout(()=>{console.log(`Attempting to reconnect to conversation ${e}`),d.subscribe()},2e3))}),()=>{console.log(`Removing realtime subscription for conversation ${e}`),a.removeChannel(d)}},ge=async e=>{var n;const s=(n=(await a.auth.getUser()).data.user)==null?void 0:n.id;if(!s)throw new Error("User not authenticated");const{data:r,error:d}=await a.from("chat_conversations").select("is_group, created_by").eq("id",e).maybeSingle();if(!r)throw new Error("Conversation not found");if(d)throw new Error("Failed to fetch conversation");if(!r.is_group)throw new Error("Cannot leave a one-on-one conversation");if(r.created_by===s){const{data:f,error:E}=await a.from("chat_participants").select("id, user_id, is_admin").eq("conversation_id",e).neq("user_id",s);if(E)throw new Error("Failed to check other participants");if(f.length===0){console.log("No other participants, deleting the group");const{error:b}=await a.from("chat_messages").delete().eq("conversation_id",e);if(b)throw b;const{error:S}=await a.from("chat_participants").delete().eq("conversation_id",e);if(S)throw S;const{error:m}=await a.from("chat_conversations").delete().eq("id",e);if(m)throw m;return}if(f.filter(b=>b.is_admin).length===0){const{error:b}=await a.from("chat_participants").update({is_admin:!0}).eq("id",f[0].id);if(b)throw new Error("Failed to assign new admin")}}const{data:t,error:i}=await a.from("chat_participants").select("id, user_id").eq("conversation_id",e);if(i)throw new Error("Failed to check participants");if(t.length===1&&t[0].user_id===s){console.log("User is the only participant, deleting the group");const{error:f}=await a.from("chat_messages").delete().eq("conversation_id",e);if(f)throw f;const{error:E}=await a.from("chat_participants").delete().eq("conversation_id",e);if(E)throw E;const{error:h}=await a.from("chat_conversations").delete().eq("id",e);if(h)throw h;return}const{error:u}=await a.from("chat_participants").delete().eq("conversation_id",e).eq("user_id",s);if(u)throw u},he=async e=>{var h;const s=(h=(await a.auth.getUser()).data.user)==null?void 0:h.id;if(!s)throw new Error("User not authenticated");const{data:r,error:d}=await a.from("chat_conversations").select("created_by").eq("id",e).maybeSingle();if(d)throw new Error("Failed to fetch conversation");if(!r)throw new Error("Conversation not found");const{data:t,error:i}=await a.from("chat_participants").select("is_admin").eq("conversation_id",e).eq("user_id",s).maybeSingle();if(r.created_by===s)console.log("User is the creator, allowing deletion");else if(t&&t.is_admin)console.log("User is an admin, allowing deletion");else if(!i)throw new Error("Only admins or the creator can delete conversations");const{error:u}=await a.from("chat_messages").delete().eq("conversation_id",e);if(u)throw u;const{data:n}=await a.from("chat_messages").select("id").eq("conversation_id",e);if(n&&n.length>0){const b=n.map(m=>m.id),{error:S}=await a.from("chat_message_status").delete().in("message_id",b);S&&console.error("Error deleting message statuses:",S)}const{error:f}=await a.from("chat_participants").delete().eq("conversation_id",e);if(f)throw f;const{error:E}=await a.from("chat_conversations").delete().eq("id",e);if(E)throw E},k={"image/jpeg":{type:"image",ext:"jpg"},"image/png":{type:"image",ext:"png"},"image/gif":{type:"image",ext:"gif"},"image/webp":{type:"image",ext:"webp"},"image/svg+xml":{type:"image",ext:"svg"},"application/pdf":{type:"document",ext:"pdf"}},X=10*1024*1024,me=e=>e.size>X?{valid:!1,error:`File size exceeds the maximum allowed size (${X/(1024*1024)}MB)`}:k[e.type]?{valid:!0}:{valid:!1,error:"File type not allowed"},_e=async(e,s)=>{try{const r=me(e);if(!r.valid)throw new Error(r.error);const d=k[e.type].ext,t=`${Date.now()}_${Math.random().toString(36).substring(2,15)}.${d}`,i=`${s}/${t}`,{data:u,error:n}=await a.storage.from("chat-attachments").upload(i,e,{cacheControl:"3600",upsert:!1});if(n)throw console.error("Error uploading file:",n),n;const{data:{publicUrl:f}}=a.storage.from("chat-attachments").getPublicUrl(u.path);return{url:f,fileType:k[e.type].type,fileName:e.name,fileSize:e.size}}catch(r){return console.error("Error in uploadFile:",r),null}},K=y.createContext(void 0),Fe=()=>{const e=y.useContext(K);if(e===void 0)throw new Error("useChatContext must be used within a ChatProvider");return e},De=({children:e})=>{const{user:s}=ee(),{currentOrganization:r}=te(),[d,t]=y.useState([]),[i,u]=y.useState(null),[n,f]=y.useState(null),[E,h]=y.useState([]),[b,S]=y.useState(!1),[m,$]=y.useState(!1),[x,O]=y.useState(!1),[g,w]=y.useState(null),[M,_]=y.useState(0),[U,q]=y.useState(!0),[A,j]=y.useState(""),N=20;y.useEffect(()=>{if(s&&r){console.log(`Setting up chat for organization ${r.id}`),z(!0);const l=ue(r.id,o=>{console.log("Received conversation update via realtime:",o),t(c=>{const p=c.findIndex(C=>C.id===o.id);if(p!==-1){const C=[...c];return C[p]={...C[p],...o},C.sort((v,F)=>new Date(F.updated_at).getTime()-new Date(v.updated_at).getTime())}else return[...c,o].sort((v,F)=>new Date(F.updated_at).getTime()-new Date(v.updated_at).getTime())})},o=>{console.log("Received conversation deletion via realtime:",o),t(c=>c.filter(p=>p.id!==o)),n===o&&f(null)});return()=>{l()}}else t([]),u(null),h([])},[s==null?void 0:s.id,r==null?void 0:r.id]),y.useEffect(()=>()=>{W.current&&clearTimeout(W.current),G.current&&clearTimeout(G.current)},[]),y.useEffect(()=>{n?(H(),P(!0,!0)):(u(null),h([]))},[n]),y.useEffect(()=>{if(!n)return;console.log(`Setting up realtime subscription for conversation ${n}`);const l=fe(n,o=>{h(c=>{const p=c.findIndex(D=>D.id===o.id);let C=null;const v=c.find(D=>D.sender_id===o.sender_id);v!=null&&v.sender&&(C=v.sender);const F={...o,sender:C||null};if(p!==-1){const D=[...c];return D[p]=F,D}return o.sender_id===(s==null?void 0:s.id)?c:[...c,F]}),t(c=>c.map(p=>p.id===n?{...p,last_message:o,updated_at:o.created_at}:p).sort((p,C)=>new Date(C.updated_at).getTime()-new Date(p.updated_at).getTime()))});return()=>{console.log(`Cleaning up realtime subscription for conversation ${n}`),l()}},[n,s==null?void 0:s.id]);const B=y.useRef(0),W=y.useRef(null),G=y.useRef(null),z=y.useCallback(async(l=!0)=>{if(!s||!r)return;const o=Date.now();if(o-B.current<1e3){console.log("Skipping fetch - too frequent");return}B.current=o,l&&S(!0),w(null);try{const c=await ae(r.id);t(c)}catch(c){console.error("Error fetching conversations:",c),w(c.message||"Failed to load conversations")}finally{l&&S(!1)}},[]),H=async()=>{if(!(!n||!r))try{const l=await se(n,r.id);u(l)}catch(l){console.error("Error fetching current conversation:",l),w(l.message||"Failed to load conversation details")}},P=async(l=!1,o=!0)=>{if(n){o&&$(!0),w(null);try{const c=l?0:M,p=await oe(n,{limit:N,offset:c*N,searchQuery:A,organizationId:r==null?void 0:r.id});l?(h(p.reverse()),_(0)):h(C=>[...p.reverse(),...C]),q(p.length===N),_(l?1:c+1)}catch(c){console.error("Error fetching messages:",c),w(c.message||"Failed to load messages")}finally{o&&$(!1)}}},L=y.useCallback(async(l=!0)=>{await z(l),n&&await H()},[z,n]),I={conversations:d,currentConversation:i,messages:E,loadingConversations:b,loadingMessages:m,uploadingFile:x,error:g,searchQuery:A,setCurrentConversationId:f,refreshConversations:L,loadMoreMessages:async(l=!0)=>{m||!U||await P(!1,l)},sendNewMessage:async(l,o)=>{var c,p,C;if(!(!n||!l.trim()&&!o)&&s!=null&&s.id)try{let v;if(o){O(!0);const R=await _e(o,s.id);if(O(!1),!R)throw new Error("Failed to upload file");v={type:R.fileType,url:R.url,name:R.fileName,size:R.fileSize}}const F=l.trim()||(o?`Sent ${o.name}`:""),D=`temp-${Date.now()}`,Y={id:D,conversation_id:n,organization_id:(r==null?void 0:r.id)||"",sender_id:s.id,content:F,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),is_read:!0,sender:{id:s.id,first_name:((c=s.user_metadata)==null?void 0:c.first_name)||"",last_name:((p=s.user_metadata)==null?void 0:p.last_name)||"",avatar_url:((C=s.user_metadata)==null?void 0:C.avatar_url)||null},attachment_type:v==null?void 0:v.type,attachment_url:v==null?void 0:v.url,attachment_name:v==null?void 0:v.name,attachment_size:v==null?void 0:v.size};h(R=>[...R,Y]);const Q=await ne(n,F,v);Q&&h(R=>R.map(V=>V.id===D?{...Q,sender:Y.sender}:V))}catch(v){console.error("Error sending message:",v),w(v.message||"Failed to send message"),T.error(v.message||"Failed to send message")}finally{O(!1)}},startOneOnOneChat:async l=>{if(!r)throw new Error("No organization selected");try{const o=await de(r.id,l);return await L(),o.id}catch(o){throw console.error("Error starting one-on-one chat:",o),w(o.message||"Failed to start chat"),o}},startGroupChat:async(l,o,c)=>{if(!l)throw new Error("Organization ID is required");try{const p=await le(l,o,c);return await L(),p.id}catch(p){throw console.error("Error creating group chat:",p),w(p.message||"Failed to create group chat"),p}},markAsRead:async l=>{if(!(!n||l.length===0))try{await Z(n,l),h(o=>o.map(c=>l.includes(c.id)?{...c,is_read:!0}:c)),t(o=>o.map(c=>c.id===n?{...c,unread_count:0}:c)),window.refreshChatNotifications&&window.refreshChatNotifications()}catch(o){console.error("Error marking messages as read:",o)}},deleteUserMessage:async l=>{if(n)try{await ie(l),h(o=>o.filter(c=>c.id!==l)),T.success("Message deleted")}catch(o){console.error("Error deleting message:",o),w(o.message||"Failed to delete message"),T.error(o.message||"Failed to delete message")}},editUserMessage:async(l,o)=>{if(!(!n||!o.trim()))try{const c=await ce(l,o);h(p=>p.map(C=>C.id===l?{...C,content:c.content}:C)),T.success("Message updated")}catch(c){console.error("Error editing message:",c),w(c.message||"Failed to edit message"),T.error(c.message||"Failed to edit message")}},deleteConversation:async l=>{try{return await he(l),t(o=>o.filter(c=>c.id!==l)),n===l&&f(null),T.success("Conversation deleted successfully"),!0}catch(o){return console.error("Error deleting conversation:",o),w(o.message||"Failed to delete conversation"),T.error(o.message||"Failed to delete conversation"),!1}},leaveConversation:async l=>{try{return await ge(l),t(o=>o.filter(c=>c.id!==l)),n===l&&f(null),T.success("You have left the conversation"),!0}catch(o){return console.error("Error leaving conversation:",o),w(o.message||"Failed to leave conversation"),T.error(o.message||"Failed to leave conversation"),!1}},searchMessages:async l=>{j(l),await P(!0,!0)},clearSearch:()=>{j(""),P(!0,!0)}};return re.jsx(K.Provider,{value:I,children:e})};export{k as A,De as C,X as M,me as i,Fe as u};
