import{s as c}from"./index-C6AV3cVN.js";import{m as b,p as l}from"./index-DNTDRJcs.js";import{a as y}from"./index-idNacaog.js";import{d as q,g as k}from"./index-KY8jayTk.js";import{r as $}from"./index-Cn2wB4rc.js";function w(n,t,e){$(2,arguments);var a=q(n,t)/b;return k()(a)}const O=async(n,t)=>{try{let e=c.from("time_entries").select("*, employee:employee_id(*)",{count:"exact"}).eq("organization_id",n);if(t!=null&&t.startDate&&(t!=null&&t.endDate)){const r=new Date(t.startDate),i=new Date(t.endDate),s=`${r.getFullYear()}-${String(r.getMonth()+1).padStart(2,"0")}-${String(r.getDate()).padStart(2,"0")}`,d=`${i.getFullYear()}-${String(i.getMonth()+1).padStart(2,"0")}-${String(i.getDate()).padStart(2,"0")}`;s===d?e=e.eq("date",s):e=e.gte("date",s).lte("date",d)}else if(t!=null&&t.startDate){const r=new Date(t.startDate),i=`${r.getFullYear()}-${String(r.getMonth()+1).padStart(2,"0")}-${String(r.getDate()).padStart(2,"0")}`;e=e.gte("date",i)}else if(t!=null&&t.endDate){const r=new Date(t.endDate),i=`${r.getFullYear()}-${String(r.getMonth()+1).padStart(2,"0")}-${String(r.getDate()).padStart(2,"0")}`;e=e.lte("date",i)}t!=null&&t.employeeId&&(e=e.eq("employee_id",t.employeeId)),t!=null&&t.limit&&(e=e.limit(t.limit)),t!=null&&t.offset&&(e=e.range(t.offset,t.offset+(t.limit||10)-1)),e=e.order("date",{ascending:!1});const{data:a,error:o,count:u}=await e;if(o)throw console.error("Error in time entries query:",o),new Error(o.message);const m=a==null?void 0:a.map(r=>({id:r.id,date:r.date,dateStr:new Date(r.date).toISOString().split("T")[0],employee:r.employee_id}));return{entries:a,count:u||0}}catch{}},p=async(n,t)=>{try{const{data:e,error:a}=await c.from("time_entries").select("*, employee:employee_id(*)").eq("organization_id",n).eq("id",t).single();if(a)throw new Error(a.message);return{entry:e}}catch(e){return console.error("Error fetching time entry:",e),{error:e.message}}},z=async(n,t,e)=>{try{let a={...e};if(e.time_in&&e.time_out){const m=l(e.time_in),r=l(e.time_out);let i=r;r<m&&(i=y(r,1));let s=w(i,m),d=0;if(e.break_start&&e.break_end){const f=l(e.break_start),_=l(e.break_end);d=w(_,f)}e.exclude_lunch_break&&s>300&&(s-=60);const g=(s-d)/60;g>8?(a.regular_hours=8,a.overtime_hours=g-8):(a.regular_hours=g,a.overtime_hours=0),a.regular_hours&&(a.regular_hours=Math.round(a.regular_hours*2)/2),a.overtime_hours&&(a.overtime_hours=Math.round(a.overtime_hours*2)/2),a.night_diff_hours=e.night_diff_hours}if("exclude_lunch_break"in e){const{exclude_lunch_break:m,...r}=a;a=r}const{data:o,error:u}=await c.from("time_entries").update({...a,updated_at:new Date().toISOString()}).eq("organization_id",n).eq("id",t).select("*, employee:employee_id(*)").single();if(u)throw new Error(u.message);return{entry:o}}catch(a){return console.error("Error updating time entry:",a),{error:a.message}}},F=async(n,t)=>{try{const{error:e}=await c.from("time_entries").delete().eq("organization_id",n).eq("id",t);if(e)throw new Error(e.message);return{success:!0}}catch(e){return console.error("Error deleting time entry:",e),{success:!1,error:e.message}}},Y=async(n,t)=>{try{const e=t.map(r=>{let i=r.regular_hours||0,s=r.overtime_hours||0,d=r.night_diff_hours||0;if(r.time_in&&r.time_out){const f=l(r.time_in),_=l(r.time_out);let E=_;_<f&&(E=y(_,1));let h=w(E,f);r.exclude_lunch_break&&h>300&&(h-=60);const S=h/60;S>8?(i=8,s=S-8):(i=S,s=0),i=Math.round(i*2)/2,s=Math.round(s*2)/2,d=r.night_diff_hours||0}const{exclude_lunch_break:M,...g}=r;return{organization_id:n,...g,regular_hours:i,overtime_hours:s,night_diff_hours:d}}),a=[],o=[];for(const r of e){const{data:i}=await c.from("time_entries").select("id").eq("organization_id",n).eq("employee_id",r.employee_id).eq("date",r.date).maybeSingle(),s=`${r.employee_id}-${r.date}`;a.push({...r,shift_group:s})}let u=[];if(a.length>0){const{data:r,error:i}=await c.from("time_entries").insert(a).select("*, employee:employee_id(*)");if(i)throw new Error(i.message);u=r||[]}return{entries:[...o,...u]}}catch(e){return console.error("Error creating time entries:",e),{error:e.message}}};export{w as a,p as b,Y as c,F as d,O as g,z as u};
