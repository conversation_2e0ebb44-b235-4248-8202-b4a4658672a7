import{h as ve,b as fe,r as t,j as e,B as o,a0 as X,A,J as k,P as M,Q as be,a7 as I,i as E,_ as r,e as Z,D as q,a2 as ye,a3 as Ce,a4 as Ne,M as l,a6 as c,ad as $}from"./index-C6AV3cVN.js";import{C as Se}from"./Card-yj7fueH8.js";import{g as Pe,c as we,u as De,d as Ae}from"./jobPosition-DFMtfNvL.js";import{g as ke}from"./department-NDftHEGx.js";import{P as Ie}from"./Pagination-CVEzfctr.js";const Je=()=>{const{currentOrganization:a}=ve(),{user:Ee}=fe(),[Q,ee]=t.useState([]),[H,se]=t.useState([]),[te,L]=t.useState(!0),[R,T]=t.useState(null),[U,ie]=t.useState(0),[F,z]=t.useState(1),[O,re]=t.useState(""),[J,ae]=t.useState(""),[_,le]=t.useState(!0),[ne,x]=t.useState(!1),[oe,j]=t.useState(!1),[ce,p]=t.useState(!1),[d,Y]=t.useState(null),[g,v]=t.useState(!1),[f,n]=t.useState(null),[B,V]=t.useState(!1),[G,b]=t.useState(null),[h,y]=t.useState(""),[C,N]=t.useState(""),[S,P]=t.useState(""),[w,D]=t.useState(!0),[m,de]=t.useState(10);t.useEffect(()=>{a&&(u(),he())},[a,F,O,J,_,m]);const u=async()=>{if(a){L(!0),T(null);try{const{positions:s,count:i,error:W}=await Pe(a.id,{searchQuery:O,departmentId:J||void 0,isActive:_?!0:void 0,limit:m,offset:(F-1)*m});W?T(W):(ee(s),ie(i))}catch(s){T(s.message||"An error occurred while fetching job positions")}finally{L(!1)}}},he=async()=>{if(a)try{const{departments:s,error:i}=await ke(a.id,{isActive:!0});i||se(s)}catch(s){console.error("Error fetching departments:",s)}},me=s=>{s.preventDefault(),z(1),u()},K=()=>{y(""),N(""),P(""),D(!0),n(null),x(!0)},ue=s=>{Y(s),y(s.title),N(s.description||""),P(s.department_id||""),D(s.is_active!==!1),n(null),j(!0)},xe=s=>{Y(s),b(null),p(!0)},je=async()=>{if(a){if(!h.trim()){n("Position title is required");return}v(!0),n(null);try{const{position:s,error:i}=await we(a.id,{title:h.trim(),description:C.trim()||null,department_id:S||null,is_active:w});i?n(i):(x(!1),u())}catch(s){n(s.message||"An error occurred while creating the job position")}finally{v(!1)}}},pe=async()=>{if(!(!a||!d)){if(!h.trim()){n("Position title is required");return}v(!0),n(null);try{const{position:s,error:i}=await De(a.id,d.id,{title:h.trim(),description:C.trim()||null,department_id:S||null,is_active:w});i?n(i):(j(!1),u())}catch(s){n(s.message||"An error occurred while updating the job position")}finally{v(!1)}}},ge=async()=>{if(!(!a||!d)){V(!0),b(null);try{const{success:s,error:i}=await Ae(a.id,d.id);i?b(i):s&&(p(!1),u())}catch(s){b(s.message||"An error occurred while deleting the job position")}finally{V(!1)}}};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(Se,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Job Positions"}),e.jsxs(o,{color:"primary",onClick:K,children:[e.jsx(X,{className:"mr-2 h-5 w-5"}),"Add Position"]})]}),R&&e.jsxs(A,{color:"failure",className:"mb-4",children:[e.jsx(k,{className:"h-4 w-4 mr-2"}),R]}),e.jsxs("div",{className:"mb-4 grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsx("form",{onSubmit:me,className:"md:col-span-1",children:e.jsx(M,{type:"text",placeholder:"Search positions...",value:O,onChange:s=>re(s.target.value),icon:be,rightIcon:()=>e.jsx(o,{type:"submit",size:"xs",color:"light",children:"Search"})})}),e.jsxs(I,{value:J,onChange:s=>ae(s.target.value),className:"md:col-span-1",children:[e.jsx("option",{value:"",children:"All Departments"}),H.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]}),e.jsxs(I,{value:_?"active":"all",onChange:s=>le(s.target.value==="active"),className:"md:col-span-1",children:[e.jsx("option",{value:"active",children:"Active Only"}),e.jsx("option",{value:"all",children:"All"})]})]}),te?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(E,{size:"xl"})}):Q.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No job positions found"}),e.jsxs(o,{color:"primary",size:"sm",onClick:K,children:[e.jsx(X,{className:"mr-2 h-4 w-4"}),"Add Your First Position"]})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(r,{hoverable:!0,children:[e.jsxs(r.Head,{children:[e.jsx(r.HeadCell,{children:"Title"}),e.jsx(r.HeadCell,{children:"Department"}),e.jsx(r.HeadCell,{children:"Description"}),e.jsx(r.HeadCell,{children:"Status"}),e.jsx(r.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(r.Body,{children:Q.map(s=>{var i;return e.jsxs(r.Row,{children:[e.jsx(r.Cell,{className:"font-medium",children:s.title}),e.jsx(r.Cell,{children:((i=s.department)==null?void 0:i.name)||e.jsx("span",{className:"text-gray-400",children:"Not assigned"})}),e.jsx(r.Cell,{children:s.description||e.jsx("span",{className:"text-gray-400",children:"No description"})}),e.jsx(r.Cell,{children:s.is_active!==!1?e.jsx(Z,{color:"success",children:"Active"}):e.jsx(Z,{color:"gray",children:"Inactive"})}),e.jsx(r.Cell,{children:e.jsx("div",{className:"flex justify-end",children:e.jsxs(q,{label:"",dismissOnClick:!0,renderTrigger:()=>e.jsx(o,{color:"light",size:"xs",children:e.jsx(Ne,{className:"h-4 w-4"})}),children:[e.jsx(q.Item,{icon:ye,onClick:()=>ue(s),children:"Edit"}),e.jsx(q.Item,{icon:Ce,onClick:()=>xe(s),children:"Delete"})]})})})]},s.id)})})]})}),e.jsx(Ie,{currentPage:F,totalPages:Math.ceil(U/m),itemsPerPage:m,totalItems:U,onPageChange:z,onItemsPerPageChange:s=>{de(s),z(1)},itemName:"positions"})]}),e.jsxs(l,{show:ne,onClose:()=>x(!1),size:"md",children:[e.jsx(l.Header,{children:"Add New Job Position"}),e.jsxs(l.Body,{children:[f&&e.jsxs(A,{color:"failure",className:"mb-4",children:[e.jsx(k,{className:"h-4 w-4 mr-2"}),f]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"positionTitle",value:"Position Title *"})}),e.jsx(M,{id:"positionTitle",value:h,onChange:s=>y(s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"departmentId",value:"Department"})}),e.jsxs(I,{id:"departmentId",value:S,onChange:s=>P(s.target.value),children:[e.jsx("option",{value:"",children:"Select Department"}),H.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"positionDescription",value:"Description"})}),e.jsx($,{id:"positionDescription",value:C,onChange:s=>N(s.target.value),rows:3})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{id:"isActive",type:"checkbox",checked:w,onChange:s=>D(s.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),e.jsx(c,{htmlFor:"isActive",value:"Active Position"})]})]})]}),e.jsxs(l.Footer,{children:[e.jsx(o,{color:"primary",onClick:je,disabled:g,children:g?e.jsxs(e.Fragment,{children:[e.jsx(E,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Position"}),e.jsx(o,{color:"gray",onClick:()=>x(!1),children:"Cancel"})]})]}),e.jsxs(l,{show:oe,onClose:()=>j(!1),size:"md",children:[e.jsx(l.Header,{children:"Edit Job Position"}),e.jsxs(l.Body,{children:[f&&e.jsxs(A,{color:"failure",className:"mb-4",children:[e.jsx(k,{className:"h-4 w-4 mr-2"}),f]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"editPositionTitle",value:"Position Title *"})}),e.jsx(M,{id:"editPositionTitle",value:h,onChange:s=>y(s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"editDepartmentId",value:"Department"})}),e.jsxs(I,{id:"editDepartmentId",value:S,onChange:s=>P(s.target.value),children:[e.jsx("option",{value:"",children:"Select Department"}),H.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"editPositionDescription",value:"Description"})}),e.jsx($,{id:"editPositionDescription",value:C,onChange:s=>N(s.target.value),rows:3})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{id:"editIsActive",type:"checkbox",checked:w,onChange:s=>D(s.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),e.jsx(c,{htmlFor:"editIsActive",value:"Active Position"})]})]})]}),e.jsxs(l.Footer,{children:[e.jsx(o,{color:"primary",onClick:pe,disabled:g,children:g?e.jsxs(e.Fragment,{children:[e.jsx(E,{size:"sm",className:"mr-2"}),"Saving..."]}):"Update Position"}),e.jsx(o,{color:"gray",onClick:()=>j(!1),children:"Cancel"})]})]}),e.jsxs(l,{show:ce,onClose:()=>p(!1),size:"md",popup:!0,children:[e.jsx(l.Header,{}),e.jsx(l.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(k,{className:"mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500 dark:text-gray-400",children:["Are you sure you want to delete"," ",e.jsx("span",{className:"font-semibold",children:d==null?void 0:d.title}),"?"]}),G&&e.jsx(A,{color:"failure",className:"mb-4",children:G}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(o,{color:"failure",onClick:ge,disabled:B,children:B?e.jsx(E,{size:"sm"}):"Yes, delete"}),e.jsx(o,{color:"gray",onClick:()=>p(!1),disabled:B,children:"No, cancel"})]})]})})]})]})};export{Je as default};
