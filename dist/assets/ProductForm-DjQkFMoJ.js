import{h as L,r as n,j as e,A as O,J as T,a6 as c,P as d,ad as B,a7 as M,L as z,ae as R,a8 as K,B as H,i as J,s as C}from"./index-C6AV3cVN.js";import{b as V}from"./product-Ca8DWaNR.js";const W=({initialData:l,onSubmit:S,isSubmitting:p,error:v})=>{const{currentOrganization:m}=L(),[F,P]=n.useState([]),[w,j]=n.useState(!1),[g,U]=n.useState(null),[b,E]=n.useState((l==null?void 0:l.image_url)||null),[f,_]=n.useState(0),[y,k]=n.useState(!1),[t,h]=n.useState(()=>{var s;const r={name:"",description:"",category_id:null,sku:"",barcode:"",unit_price:0,cost_price:0,stock_quantity:0,min_stock_level:0,is_active:!0};if(l){const a={...r,...l};return l.category&&!l.category_id&&(a.category_id=((s=l.category)==null?void 0:s.id)||null),a}return r});n.useEffect(()=>{(async()=>{if(m){j(!0);try{const{categories:s,error:a}=await V(m.id);a?console.error("Error fetching categories:",a):P(s)}catch(s){console.error("Error fetching categories:",s)}finally{j(!1)}}})()},[m]);const i=r=>{const{name:s,value:a,type:x}=r.target;if(x==="number"){let o=null;if(a!==""&&a!==null){const u=parseFloat(a);!isNaN(u)&&u>=0&&(o=u)}h({...t,[s]:o})}else h(s==="category_id"?{...t,[s]:a===""?null:a}:{...t,[s]:a})},I=r=>{h({...t,is_active:r})},q=r=>{if(r.target.files&&r.target.files[0]){const s=r.target.files[0];U(s);const a=new FileReader;a.onload=x=>{var o;E((o=x.target)==null?void 0:o.result)},a.readAsDataURL(s)}},$=async()=>{if(!g||!m)return null;k(!0),_(0);try{const r=g.name.split(".").pop(),s=`${Date.now()}-${Math.random().toString(36).substring(2)}.${r}`,a=`products/${m.id}/${s}`,{data:x,error:o}=await C.storage.from("product-images").upload(a,g,{cacheControl:"3600",upsert:!1,onUploadProgress:N=>{_(Math.round(N.loaded/N.total*100))}});if(o)return console.error("Error uploading image:",o),null;const{data:u}=C.storage.from("product-images").getPublicUrl(a);return u.publicUrl}catch(r){return console.error("Error uploading image:",r),null}finally{k(!1)}},A=async r=>{if(r.preventDefault(),!t.name||t.name.trim()===""||t.unit_price===null||t.unit_price===void 0||t.unit_price<0)return;let s={...t};if(s.unit_price=s.unit_price||0,s.cost_price=s.cost_price||0,s.min_stock_level=s.min_stock_level||0,s.sku===""&&(s.sku=null),s.barcode===""&&(s.barcode=null),g){const a=await $();a&&(s.image_url=a)}await S(s)};return e.jsxs("form",{onSubmit:A,className:"space-y-6",children:[v&&e.jsx(O,{color:"failure",icon:T,children:v}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"name",value:"Product Name *"})}),e.jsx(d,{id:"name",name:"name",value:t.name||"",onChange:i,required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"description",value:"Description"})}),e.jsx(B,{id:"description",name:"description",value:t.description||"",onChange:i,rows:4})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"category_id",value:"Category"})}),e.jsxs(M,{id:"category_id",name:"category_id",value:t.category_id||"",onChange:i,disabled:w,children:[e.jsx("option",{value:"",children:"Select a category"}),F.map(r=>e.jsx("option",{value:r.id,children:r.name},r.id))]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"sku",value:"SKU (Optional)"})}),e.jsx(d,{id:"sku",name:"sku",value:t.sku||"",onChange:i,placeholder:"Enter SKU if available"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"barcode",value:"Barcode (Optional)"})}),e.jsx(d,{id:"barcode",name:"barcode",value:t.barcode||"",onChange:i,placeholder:"Enter barcode if available"})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"unit_price",value:"Selling Price *"})}),e.jsx(d,{id:"unit_price",name:"unit_price",type:"number",step:"0.01",min:"0",value:t.unit_price||"",onChange:i,required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"cost_price",value:"Cost Price"})}),e.jsx(d,{id:"cost_price",name:"cost_price",type:"number",step:"0.01",min:"0",value:t.cost_price||"",onChange:i})]})]}),(l==null?void 0:l.id)&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 block",children:[e.jsx(c,{htmlFor:"stock_quantity",value:"Current Stock"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:'Stock quantity is managed through inventory transactions. Use "Adjust Inventory" to change stock levels.'})]}),e.jsxs("div",{className:"relative",children:[e.jsx(d,{id:"stock_quantity",name:"stock_quantity",type:"number",step:"1",min:"0",value:t.stock_quantity||"",disabled:!0,className:"bg-gray-50"}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3",children:e.jsx(z,{to:`/inventory/adjust/${l==null?void 0:l.id}`,className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"Adjust Stock"})})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"min_stock_level",value:"Minimum Stock Level"})}),e.jsx(d,{id:"min_stock_level",name:"min_stock_level",type:"number",step:"1",min:"0",value:t.min_stock_level||"",onChange:i})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"image",value:"Product Image"})}),e.jsx(R,{id:"image",accept:"image/*",onChange:q,helperText:"Upload a product image (max 5MB)"}),b&&e.jsx("div",{className:"mt-2",children:e.jsx("img",{src:b,alt:"Product preview",className:"h-32 w-32 object-cover rounded-md border border-gray-200"})}),y&&e.jsxs("div",{className:"mt-2",children:[e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:`${f}%`}})}),e.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:["Uploading: ",f,"%"]})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(K,{checked:t.is_active||!1,onChange:I,label:"Active Product"})})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(H,{type:"submit",disabled:p||y,children:p?e.jsxs(e.Fragment,{children:[e.jsx(J,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Product"})})]})};export{W as P};
