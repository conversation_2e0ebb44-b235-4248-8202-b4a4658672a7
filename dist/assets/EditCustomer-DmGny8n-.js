import{ab as C,h as N,d as E,r as n,j as e,A as x,J as f,i as S}from"./index-C6AV3cVN.js";import{C as o}from"./Card-yj7fueH8.js";import{C as b}from"./CustomerForm-DEWqk2b_.js";import{a as v,u as z}from"./customer-COogBrXM.js";const D=()=>{const{id:i}=C(),{currentOrganization:s}=N(),h=E(),[c,j]=n.useState(null),[p,u]=n.useState(!0),[g,m]=n.useState(!1),[l,t]=n.useState(null);n.useEffect(()=>{(async()=>{if(!(!s||!i)){u(!0),t(null);try{const{customer:r,error:a}=await v(s.id,i);a?t(a):r?j(r):t("Customer not found")}catch(r){t(r.message||"An error occurred while fetching the customer")}finally{u(!1)}}})()},[s,i]);const y=async d=>{if(!s||!i){t("No organization selected or customer ID missing");return}m(!0),t(null);try{const{customer:r,error:a}=await z(s.id,i,d);a?t(a):r&&h("/customers")}catch(r){t(r.message||"An error occurred while updating the customer")}finally{m(!1)}};return s?p?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(o,{children:e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(S,{size:"xl"})})})}):l&&!c?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(o,{children:e.jsxs(x,{color:"failure",icon:f,children:[e.jsx("h3",{className:"text-lg font-medium",children:"Error"}),e.jsx("p",{children:l})]})})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(o,{children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsx("h1",{className:"text-2xl font-bold",children:"Edit Customer"})}),c&&e.jsx(b,{initialData:c,onSubmit:y,isSubmitting:g,error:l||void 0})]})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(o,{children:e.jsxs(x,{color:"failure",icon:f,children:[e.jsx("h3",{className:"text-lg font-medium",children:"No Organization Selected"}),e.jsx("p",{children:"Please select an organization before editing a customer."})]})})})};export{D as default};
