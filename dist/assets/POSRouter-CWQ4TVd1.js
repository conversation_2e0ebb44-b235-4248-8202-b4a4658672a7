import{d as r,a as i,r as n,j as s,i as c}from"./index-C6AV3cVN.js";const p=()=>{const e=r(),{settings:a,loading:t}=i();return n.useEffect(()=>{if(!t&&a)switch(a.business_type){case"retail":e("/sales/retail-pos");break;case"supermarket":e("/sales/retail-pos");break;case"restaurant":case"cafe":case"coffee_shop":case"bar":e("/sales/pos");break;default:e("/sales/pos");break}},[t,a,e]),s.jsx("div",{className:"flex items-center justify-center h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx(c,{size:"xl"}),s.jsx("p",{className:"mt-4 text-gray-600",children:"Loading POS Terminal..."})]})})};export{p as default};
