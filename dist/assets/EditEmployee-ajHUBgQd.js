import{h as xe,r as l,by as Xe,j as e,A,J as L,K as Ze,a6 as t,P as o,a7 as i,k as De,Y as es,a8 as ss,ad as rs,ae as ls,bs as ts,B as os,i as ie,s as he,ab as ns,d as ds,L as cs}from"./index-C6AV3cVN.js";import{C as k}from"./Card-yj7fueH8.js";import{g as ms}from"./department-NDftHEGx.js";import{g as as}from"./jobPosition-DFMtfNvL.js";import{g as us}from"./employmentType-B4DW1Mne.js";import{a as hs,u as xs}from"./employee-DWC25S7P.js";const is=({initialData:s,onSubmit:F,isSubmitting:m,error:d})=>{const{currentOrganization:c}=xe(),[w,P]=l.useState("personal"),[p,g]=l.useState((s==null?void 0:s.first_name)||""),[f,N]=l.useState((s==null?void 0:s.middle_name)||""),[S,v]=l.useState((s==null?void 0:s.last_name)||""),[_,T]=l.useState((s==null?void 0:s.email)||""),[a,j]=l.useState((s==null?void 0:s.phone)||""),[x,C]=l.useState(s!=null&&s.date_of_birth?new Date(s.date_of_birth).toISOString().split("T")[0]:""),[U,ge]=l.useState((s==null?void 0:s.gender)||""),[B,ve]=l.useState((s==null?void 0:s.marital_status)||""),[M,je]=l.useState((s==null?void 0:s.nationality)||""),[R,pe]=l.useState((s==null?void 0:s.address)||""),[z,be]=l.useState((s==null?void 0:s.city)||""),[H,ye]=l.useState((s==null?void 0:s.state)||""),[$,fe]=l.useState((s==null?void 0:s.postal_code)||""),[G,Ne]=l.useState((s==null?void 0:s.country)||""),[q,Se]=l.useState((s==null?void 0:s.emergency_contact_name)||""),[J,_e]=l.useState((s==null?void 0:s.emergency_contact_phone)||""),[K,Ce]=l.useState((s==null?void 0:s.emergency_contact_relationship)||""),[W,Ee]=l.useState((s==null?void 0:s.employee_number)||""),[Y,Ie]=l.useState((s==null?void 0:s.department_id)||""),[Q,ke]=l.useState((s==null?void 0:s.position_id)||""),[V,Fe]=l.useState((s==null?void 0:s.employment_type_id)||""),[E,we]=l.useState((s==null?void 0:s.user_id)||""),[X,Pe]=l.useState(s!=null&&s.hire_date?new Date(s.hire_date).toISOString().split("T")[0]:""),[Z,Te]=l.useState(s!=null&&s.end_date?new Date(s.end_date).toISOString().split("T")[0]:""),[D,Oe]=l.useState((s==null?void 0:s.status)||"active"),[ee,Ae]=l.useState((s==null?void 0:s.is_active)!==!1),[se,Le]=l.useState((s==null?void 0:s.notes)||""),[re,le]=l.useState(""),[te,oe]=l.useState(""),[ne,de]=l.useState(""),[ce,me]=l.useState("");l.useEffect(()=>{if(Array.isArray(s==null?void 0:s.government_ids)&&(s==null?void 0:s.government_ids.length)>0){const r=s.government_ids[0];r.sss_number&&le(r.sss_number),r.philhealth_number&&oe(r.philhealth_number),r.pagibig_number&&de(r.pagibig_number),r.tin_number&&me(r.tin_number)}},[s]);const[I,Ue]=l.useState(null),[b,gs]=l.useState((s==null?void 0:s.profile_image_url)||""),[vs,Be]=l.useState(0),[ae,O]=l.useState(null),[Me,Re]=l.useState([]),[ze,He]=l.useState([]),[$e,Ge]=l.useState([]),[qe,Je]=l.useState([]),[js,ue]=l.useState(!1);l.useEffect(()=>{c&&Ke()},[c]);const Ke=async()=>{if(c){ue(!0);try{const{departments:r}=await ms(c.id);Re(r);const{positions:u}=await as(c.id);He(u);const{employmentTypes:h}=await us(c.id);Ge(h);const{members:n,error:y}=await Xe(c.id);!y&&n&&Je(n)}catch(r){console.error("Error fetching reference data:",r)}finally{ue(!1)}}},We=r=>{r.target.files&&r.target.files[0]&&Ue(r.target.files[0])},Ye=async()=>{if(!I||!c)return null;Be(0),O(null);try{const r=I.name.split(".").pop(),u=`${Date.now()}-${Math.random().toString(36).substring(2,15)}.${r}`,h=`${c.id}/employee-images/${u}`,{error:n,data:y}=await he.storage.from("employee-images").upload(h,I,{cacheControl:"3600",upsert:!1});if(n)return O(n.message),null;const{data:Ve}=he.storage.from("employee-images").getPublicUrl(h);return Ve.publicUrl}catch(r){return O(r.message),null}},Qe=async r=>{r.preventDefault();let u=b;if(I&&!E){const y=await Ye();y&&(u=y)}const h={first_name:p,middle_name:f||null,last_name:S,email:_||null,phone:a||null,date_of_birth:x||null,gender:U||null,marital_status:B||null,nationality:M||null,address:R||null,city:z||null,state:H||null,postal_code:$||null,country:G||null,emergency_contact_name:q||null,emergency_contact_phone:J||null,emergency_contact_relationship:K||null,employee_number:W||null,department_id:Y||null,position_id:Q||null,employment_type_id:V||null,user_id:E||null,hire_date:X||null,end_date:Z||null,status:D||null,is_active:ee,profile_image_url:u||null,notes:se||null},n={};n.sss_number=re||null,n.philhealth_number=te||null,n.pagibig_number=ne||null,n.tin_number=ce||null,await F(h,n)};return e.jsxs("form",{onSubmit:Qe,children:[d&&e.jsxs(A,{color:"failure",className:"mb-4",children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),d]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(k,{children:[e.jsxs("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[e.jsx(Ze,{className:"mr-2 h-5 w-5 text-gray-600"}),"Personal Information"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"firstName",value:"First Name *"})}),e.jsx(o,{id:"firstName",value:p,onChange:r=>g(r.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"middleName",value:"Middle Name"})}),e.jsx(o,{id:"middleName",value:f,onChange:r=>N(r.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"lastName",value:"Last Name *"})}),e.jsx(o,{id:"lastName",value:S,onChange:r=>v(r.target.value),required:!0})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"email",value:"Email"})}),e.jsx(o,{id:"email",type:"email",value:_,onChange:r=>T(r.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"phone",value:"Phone"})}),e.jsx(o,{id:"phone",value:a,onChange:r=>j(r.target.value)})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"dateOfBirth",value:"Date of Birth"})}),e.jsx(o,{id:"dateOfBirth",type:"date",value:x,onChange:r=>C(r.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"gender",value:"Gender"})}),e.jsxs(i,{id:"gender",value:U,onChange:r=>ge(r.target.value),children:[e.jsx("option",{value:"",children:"Select Gender"}),e.jsx("option",{value:"male",children:"Male"}),e.jsx("option",{value:"female",children:"Female"}),e.jsx("option",{value:"other",children:"Other"}),e.jsx("option",{value:"prefer_not_to_say",children:"Prefer not to say"})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"maritalStatus",value:"Marital Status"})}),e.jsxs(i,{id:"maritalStatus",value:B,onChange:r=>ve(r.target.value),children:[e.jsx("option",{value:"",children:"Select Marital Status"}),e.jsx("option",{value:"single",children:"Single"}),e.jsx("option",{value:"married",children:"Married"}),e.jsx("option",{value:"divorced",children:"Divorced"}),e.jsx("option",{value:"widowed",children:"Widowed"}),e.jsx("option",{value:"separated",children:"Separated"})]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"nationality",value:"Nationality"})}),e.jsx(o,{id:"nationality",value:M,onChange:r=>je(r.target.value)})]}),e.jsx("h4",{className:"text-md font-medium mb-2",children:"Address"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"address",value:"Street Address"})}),e.jsx(o,{id:"address",value:R,onChange:r=>pe(r.target.value)})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"city",value:"City"})}),e.jsx(o,{id:"city",value:z,onChange:r=>be(r.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"state",value:"State/Province"})}),e.jsx(o,{id:"state",value:H,onChange:r=>ye(r.target.value)})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"postalCode",value:"Postal Code"})}),e.jsx(o,{id:"postalCode",value:$,onChange:r=>fe(r.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"country",value:"Country"})}),e.jsx(o,{id:"country",value:G,onChange:r=>Ne(r.target.value)})]})]}),e.jsx("h4",{className:"text-md font-medium mb-2",children:"Emergency Contact"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"emergencyContactName",value:"Contact Name"})}),e.jsx(o,{id:"emergencyContactName",value:q,onChange:r=>Se(r.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"emergencyContactPhone",value:"Contact Phone"})}),e.jsx(o,{id:"emergencyContactPhone",value:J,onChange:r=>_e(r.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"emergencyContactRelationship",value:"Relationship"})}),e.jsx(o,{id:"emergencyContactRelationship",value:K,onChange:r=>Ce(r.target.value)})]})]})]}),e.jsxs(k,{children:[e.jsxs("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[e.jsx(De,{className:"mr-2 h-5 w-5 text-gray-600"}),"Employment Details"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"employeeNumber",value:"Employee Number"})}),e.jsx(o,{id:"employeeNumber",value:W,onChange:r=>Ee(r.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"status",value:"Employment Status"})}),e.jsxs(i,{id:"status",value:D,onChange:r=>Oe(r.target.value),children:[e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"on_leave",children:"On Leave"}),e.jsx("option",{value:"terminated",children:"Terminated"}),e.jsx("option",{value:"resigned",children:"Resigned"}),e.jsx("option",{value:"retired",children:"Retired"})]})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsxs(t,{htmlFor:"userId",value:"Link to User Account",className:"flex items-center",children:[e.jsx("span",{children:"Link to User Account"}),e.jsx(es,{className:"ml-1 text-gray-500",title:"Link this employee to a user account if they need access to the system"})]})}),e.jsxs(i,{id:"userId",name:"userId",value:E,onChange:r=>we(r.target.value),children:[e.jsx("option",{value:"",children:"Not linked to any user"}),qe.map(r=>{var u,h,n;return e.jsxs("option",{value:r.user_id,children:[((u=r.profile)==null?void 0:u.email)||"No email"," (",(h=r.profile)==null?void 0:h.first_name," ",(n=r.profile)==null?void 0:n.last_name,")"]},r.user_id)})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Link this employee to a user account if they need access to the system"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"departmentId",value:"Department"})}),e.jsxs(i,{id:"departmentId",value:Y,onChange:r=>Ie(r.target.value),children:[e.jsx("option",{value:"",children:"Select Department"}),Me.map(r=>e.jsx("option",{value:r.id,children:r.name},r.id))]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"positionId",value:"Position"})}),e.jsxs(i,{id:"positionId",value:Q,onChange:r=>ke(r.target.value),children:[e.jsx("option",{value:"",children:"Select Position"}),ze.map(r=>e.jsx("option",{value:r.id,children:r.title},r.id))]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"employmentTypeId",value:"Employment Type"})}),e.jsxs(i,{id:"employmentTypeId",value:V,onChange:r=>Fe(r.target.value),children:[e.jsx("option",{value:"",children:"Select Employment Type"}),$e.map(r=>e.jsx("option",{value:r.id,children:r.name},r.id))]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"hireDate",value:"Hire Date"})}),e.jsx(o,{id:"hireDate",type:"date",value:X,onChange:r=>Pe(r.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"endDate",value:"End Date"})}),e.jsx(o,{id:"endDate",type:"date",value:Z,onChange:r=>Te(r.target.value)})]})]}),e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(ss,{checked:ee,onChange:Ae,label:"Active Employee"})})}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"notes",value:"Notes"})}),e.jsx(rs,{id:"notes",value:se,onChange:r=>Le(r.target.value),rows:4})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"profileImage",value:"Profile Image"})}),E?e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"This employee is linked to a user account. The profile image will be automatically used from the user account."}),b&&e.jsx("div",{className:"mt-2",children:e.jsx("div",{className:"w-20 h-20 overflow-hidden rounded-full border border-gray-200",children:e.jsx("img",{src:b,alt:"Profile",className:"w-full h-full object-cover"})})})]}):e.jsxs(e.Fragment,{children:[e.jsx(ls,{id:"profileImage",onChange:We,helperText:"Upload a profile image (optional)"}),b&&e.jsxs("div",{className:"mt-2",children:[e.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Current Image:"}),e.jsx("div",{className:"w-20 h-20 overflow-hidden rounded-full border border-gray-200",children:e.jsx("img",{src:b,alt:"Profile",className:"w-full h-full object-cover"})})]}),ae&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:ae})]})]})]}),e.jsxs(k,{children:[e.jsxs("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[e.jsx(ts,{className:"mr-2 h-5 w-5 text-gray-600"}),"Government IDs"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"sssNumber",value:"SSS Number"})}),e.jsx(o,{id:"sssNumber",value:re,onChange:r=>le(r.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"philhealthNumber",value:"PhilHealth Number"})}),e.jsx(o,{id:"philhealthNumber",value:te,onChange:r=>oe(r.target.value)})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"pagibigNumber",value:"Pag-IBIG Number"})}),e.jsx(o,{id:"pagibigNumber",value:ne,onChange:r=>de(r.target.value)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(t,{htmlFor:"tinNumber",value:"TIN Number"})}),e.jsx(o,{id:"tinNumber",value:ce,onChange:r=>me(r.target.value)})]})]})]})]}),e.jsx("div",{className:"mt-6 flex justify-end",children:e.jsx(os,{type:"submit",color:"primary",disabled:m,children:m?e.jsxs(e.Fragment,{children:[e.jsx(ie,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Employee"})})]})},_s=()=>{const{id:s}=ns(),F=ds(),{currentOrganization:m}=xe(),[d,c]=l.useState(null),[w,P]=l.useState(!0),[p,g]=l.useState(null),[f,N]=l.useState(!1),[S,v]=l.useState();l.useEffect(()=>{m&&s&&_()},[m,s]);const _=async()=>{if(!(!m||!s)){P(!0),g(null);try{const{employee:a,error:j}=await hs(m.id,s);j?g(j):a?c(a):g("Employee not found")}catch(a){g(a.message||"An error occurred while fetching the employee")}finally{P(!1)}}},T=async(a,j)=>{if(!m||!s){v("No organization selected or invalid employee ID");return}N(!0),v(void 0);try{const{employee:x,error:C}=await xs(m.id,s,a,j);C?v(C):x&&F(`/employees/details/${x.id}`)}catch(x){v(x.message||"An error occurred while updating the employee")}finally{N(!1)}};return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(k,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:w?"Loading...":`Edit Employee: ${d==null?void 0:d.first_name} ${d==null?void 0:d.last_name}`}),e.jsx(cs,{to:"/employees",className:"text-sm text-primary hover:underline",children:"Back to Employees"})]}),w?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(ie,{size:"xl"})}):p?e.jsxs(A,{color:"failure",children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),p]}):d?e.jsx(is,{initialData:d,onSubmit:T,isSubmitting:f,error:S}):e.jsxs(A,{color:"failure",children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),"Employee not found"]})]})})};export{_s as default};
