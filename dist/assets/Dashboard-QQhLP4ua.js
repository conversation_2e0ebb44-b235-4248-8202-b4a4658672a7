import{j as e,L as o,B as i,e as V,y as B,z as q,C as G,E as J,h as M,r as y,t as O,F as U,A as _,i as K,G as Q,T as b,I,J as P,K as T}from"./index-C6AV3cVN.js";import{C as r}from"./Card-yj7fueH8.js";import{g as z}from"./product-Ca8DWaNR.js";import{g as H}from"./customer-COogBrXM.js";import{g as A}from"./employee-DWC25S7P.js";import{g as R}from"./timeEntry-DfPyfZaA.js";import{f as F}from"./formatters-Cypx7G-j.js";import{S as W}from"./SalesSummary-D7VnYOyp.js";import"./index-DNTDRJcs.js";import"./index-Cn2wB4rc.js";import"./typeof-QjJsDpFa.js";import"./index-4YOzgfrD.js";import"./index-idNacaog.js";import"./index-KY8jayTk.js";import"./react-apexcharts.min-gcvzl5Eq.js";import"./refund-CcMk-dC8.js";import"./inventoryTransaction-1UXV5RDN.js";import"./floatInventory-k_pEQeIK.js";import"./currencyFormatter-BsFWv3sX.js";import"./index-BJwYa9ck.js";import"./index-qirzObrW.js";import"./index-DT2YvziZ.js";const $=({customers:s})=>e.jsxs(r,{children:[e.jsx("h3",{className:"text-xl font-semibold mb-4",children:"Sales Overview"}),e.jsxs("p",{className:"text-gray-500",children:["Total Customers: ",s.length]})]}),X=({products:s})=>e.jsxs(r,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-xl font-semibold",children:"Top Products"}),e.jsx(o,{to:"/products",children:e.jsx(i,{color:"light",size:"sm",children:"View All Products"})})]}),e.jsxs("p",{className:"text-gray-500",children:["Total Products: ",s.length]})]}),Y=({products:s,lowStockItems:d})=>{const m=s.length,n=s.reduce((h,c)=>{const t=c.stock_quantity||0,j=c.unit_price||0;return h+t*j},0);return e.jsxs(r,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-xl font-semibold",children:"Inventory Summary"}),e.jsx(o,{to:"/inventory",children:e.jsx(i,{color:"light",size:"sm",children:"View All Inventory"})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-blue-700",children:"Total Products"}),e.jsx("p",{className:"text-xl font-bold text-blue-700",children:m}),e.jsx("p",{className:"text-xs text-blue-600 mt-1",children:"In inventory"})]}),e.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-yellow-700",children:"Low Stock Items"}),e.jsx("p",{className:"text-xl font-bold text-yellow-700",children:d.length}),e.jsx("p",{className:"text-xs text-yellow-600 mt-1",children:"Need attention"})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("h4",{className:"text-lg font-medium mb-2",children:"Inventory Value"}),e.jsx("p",{className:"text-2xl font-bold",children:F(n)}),e.jsx("p",{className:"text-sm text-gray-500",children:"Total value of inventory"})]})]})},Z=({timeEntries:s,customers:d})=>e.jsxs(r,{className:"h-full",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-xl font-semibold",children:"Recent Activity"}),e.jsx(V,{color:"gray",size:"sm",children:"Today"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{className:"text-gray-500",children:["Time Entries: ",s.length]}),e.jsxs("p",{className:"text-gray-500",children:["Recent Customers: ",d.length]})]}),e.jsx("div",{className:"mt-4 text-center",children:e.jsx(o,{to:"/reports",children:e.jsx("span",{className:"text-sm text-blue-600 hover:underline",children:"View All Activity"})})})]}),ee=({employeeCount:s,timeEntries:d,payrollPeriods:m})=>{const n=d.reduce((j,x)=>j+(x.regular_hours||0)+(x.overtime_hours||0),0),h=s>0?(n/s).toFixed(1):"0",t=s>0?(15e4/s).toFixed(2):"0";return e.jsxs(r,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-xl font-semibold",children:"Employee Overview"}),e.jsx(o,{to:"/employees",children:e.jsx(i,{color:"light",size:"sm",children:"View All Employees"})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg flex items-center",children:[e.jsx("div",{className:"p-3 bg-blue-100 rounded-full mr-3",children:e.jsx(B,{className:"h-6 w-6 text-blue-700"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-blue-700",children:"Total Employees"}),e.jsx("p",{className:"text-xl font-bold text-blue-700",children:s})]})]}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg flex items-center",children:[e.jsx("div",{className:"p-3 bg-green-100 rounded-full mr-3",children:e.jsx(q,{className:"h-6 w-6 text-green-700"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-green-700",children:"Avg Hours/Employee"}),e.jsx("p",{className:"text-xl font-bold text-green-700",children:h})]})]}),e.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg flex items-center",children:[e.jsx("div",{className:"p-3 bg-purple-100 rounded-full mr-3",children:e.jsx(G,{className:"h-6 w-6 text-purple-700"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-purple-700",children:"Avg Salary"}),e.jsx("p",{className:"text-xl font-bold text-purple-700",children:F(Number(t))})]})]}),e.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg flex items-center",children:[e.jsx("div",{className:"p-3 bg-yellow-100 rounded-full mr-3",children:e.jsx(J,{className:"h-6 w-6 text-yellow-700"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-yellow-700",children:"Payroll Periods"}),e.jsx("p",{className:"text-xl font-bold text-yellow-700",children:m.length})]})]})]})]})},se=()=>{const{currentOrganization:s,loading:d}=M(),[m,n]=y.useState(!0),[h,c]=y.useState(null),[t,j]=y.useState({products:[],lowStockItems:[],customers:[],recentCustomers:[],employeeCount:0,timeEntries:[]}),[x,L]=y.useState("overview");y.useEffect(()=>{if(!s)return;(async()=>{n(!0),c(null);try{const{products:l,error:p}=await z(s.id,{limit:100});if(p)throw new Error(p);const w=l.filter(a=>a.stock_quantity<=a.min_stock_level),{customers:g,error:N}=await H(s.id,{limit:50});if(N)throw new Error(N);const E=[...g].sort((a,D)=>new Date(D.created_at).getTime()-new Date(a.created_at).getTime()).slice(0,5),{count:S,error:v}=await A(s.id,{limit:1});if(v)throw new Error(v);const{entries:C,error:f}=await R(s.id,{limit:10});if(f)throw new Error(f);j({products:l,lowStockItems:w,customers:g,recentCustomers:E,employeeCount:S,timeEntries:C||[]})}catch(l){console.error("Error fetching dashboard data:",l),c(l.message||"Failed to load dashboard data")}finally{n(!1)}})()},[s]);const k=()=>{if(!s)return;(async()=>{n(!0),c(null);try{const{products:l,error:p}=await z(s.id,{limit:100});if(p)throw new Error(p);const w=l.filter(a=>a.stock_quantity<=a.min_stock_level),{customers:g,error:N}=await H(s.id,{limit:50});if(N)throw new Error(N);const E=[...g].sort((a,D)=>new Date(D.created_at).getTime()-new Date(a.created_at).getTime()).slice(0,5),{count:S,error:v}=await A(s.id,{limit:1});if(v)throw new Error(v);const{entries:C,error:f}=await R(s.id,{limit:10});if(f)throw new Error(f);j({products:l,lowStockItems:w,customers:g,recentCustomers:E,employeeCount:S,timeEntries:C||[]})}catch(l){console.error("Error fetching dashboard data:",l),c(l.message||"Failed to load dashboard data")}finally{n(!1)}})()};return e.jsxs("div",{className:"grid grid-cols-12 gap-6",children:[e.jsx("div",{className:"col-span-12 mb-4",children:e.jsx(r,{children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold mb-2",children:"Business Dashboard"}),e.jsx("p",{className:"text-sm text-gray-500",children:s?s.name:"Loading organization..."})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(i,{color:"light",onClick:k,disabled:m,children:[e.jsx(O,{className:`h-5 w-5 ${m?"animate-spin":""}`}),e.jsx("span",{className:"ml-1",children:"Refresh"})]}),s&&e.jsx(o,{to:"/organization/settings",children:e.jsxs(i,{color:"light",children:[e.jsx(U,{className:"h-5 w-5 mr-1"}),e.jsx("span",{children:"Settings"})]})})]})]})})}),!s&&!d?e.jsx("div",{className:"col-span-12",children:e.jsxs(_,{color:"warning",children:[e.jsx("h3",{className:"font-medium",children:"No Organization Selected"}),e.jsx("p",{children:"Please select or create an organization to start using the POS system."}),e.jsx("div",{className:"mt-4",children:e.jsx(o,{to:"/organization/create",children:e.jsx(i,{color:"primary",children:"Create Organization"})})})]})}):m?e.jsxs("div",{className:"col-span-12 flex justify-center items-center p-12",children:[e.jsx(K,{size:"xl"}),e.jsx("span",{className:"ml-2",children:"Loading dashboard data..."})]}):h?e.jsx("div",{className:"col-span-12",children:e.jsxs(_,{color:"failure",icon:Q,children:[e.jsx("h3",{className:"font-medium",children:"Error Loading Dashboard"}),e.jsx("p",{children:h}),e.jsx("div",{className:"mt-4",children:e.jsxs(i,{color:"failure",onClick:k,children:[e.jsx(O,{className:"mr-2 h-5 w-5"}),"Retry"]})})]})}):e.jsx(e.Fragment,{children:e.jsx("div",{className:"col-span-12",children:e.jsxs(b,{"aria-label":"Dashboard tabs",onActiveTabChange:u=>L(u===0?"overview":u===1?"sales":u===2?"inventory":"employees"),className:"dashboard-tabs focus:outline-none",children:[e.jsx(b.Item,{title:"Business Overview",active:x==="overview",children:e.jsxs("div",{className:"grid grid-cols-12 gap-6 mt-4",children:[e.jsx("div",{className:"col-span-12",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6",children:[e.jsx(r,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-blue-100 mr-4",children:e.jsx(I,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-gray-500 text-sm",children:"Total Products"}),e.jsx("p",{className:"text-2xl font-bold",children:t.products.length})]})]})}),e.jsx(r,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-orange-100 mr-4",children:e.jsx(P,{className:"h-6 w-6 text-orange-600"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-gray-500 text-sm",children:"Low Stock"}),e.jsx("p",{className:"text-2xl font-bold text-orange-600",children:t.lowStockItems.length})]})]})}),e.jsx(r,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-green-100 mr-4",children:e.jsx(T,{className:"h-6 w-6 text-green-600"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-gray-500 text-sm",children:"Customers"}),e.jsx("p",{className:"text-2xl font-bold",children:t.customers.length})]})]})}),e.jsx(r,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-purple-100 mr-4",children:e.jsx(T,{className:"h-6 w-6 text-purple-600"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-gray-500 text-sm",children:"Employees"}),e.jsx("p",{className:"text-2xl font-bold",children:t.employeeCount})]})]})}),e.jsx(r,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-yellow-100 mr-4",children:e.jsx(T,{className:"h-6 w-6 text-yellow-600"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-gray-500 text-sm",children:"Recent Customers"}),e.jsx("p",{className:"text-2xl font-bold text-yellow-600",children:t.recentCustomers.length})]})]})}),e.jsx(r,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-indigo-100 mr-4",children:e.jsx(I,{className:"h-6 w-6 text-indigo-600"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-gray-500 text-sm",children:"Time Entries"}),e.jsx("p",{className:"text-2xl font-bold text-indigo-600",children:t.timeEntries.length})]})]})})]})}),e.jsx("div",{className:"lg:col-span-8 col-span-12",children:e.jsx($,{customers:t.customers})}),e.jsx("div",{className:"lg:col-span-4 col-span-12",children:e.jsx(Z,{timeEntries:t.timeEntries,customers:t.recentCustomers})}),e.jsx("div",{className:"lg:col-span-12 col-span-12",children:e.jsx(X,{products:t.products})})]})}),e.jsx(b.Item,{title:"Sales Summary",active:x==="sales",children:e.jsx("div",{className:"mt-4",children:e.jsx(W,{showHeader:!1})})}),e.jsx(b.Item,{title:"Inventory Status",active:x==="inventory",children:e.jsxs("div",{className:"grid grid-cols-12 gap-6 mt-4",children:[e.jsx("div",{className:"col-span-12",children:e.jsx(Y,{products:t.products,lowStockItems:t.lowStockItems})}),e.jsx("div",{className:"col-span-12 mt-4",children:e.jsxs(r,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Float Inventory Management"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(o,{to:"/inventory/float",children:e.jsxs(i,{color:"warning",size:"sm",children:[e.jsx(P,{className:"mr-2 h-5 w-5"}),"View Float Inventory"]})}),e.jsx(o,{to:"/inventory/float/report",children:e.jsxs(i,{color:"light",size:"sm",children:[e.jsx(I,{className:"mr-2 h-5 w-5"}),"View Reports"]})})]})]}),e.jsx("p",{className:"text-gray-600",children:"Float inventory occurs when items are sold without sufficient stock. Monitor and resolve float inventory to improve operational efficiency."})]})})]})}),e.jsx(b.Item,{title:"Employee Stats",active:x==="employees",children:e.jsx("div",{className:"grid grid-cols-12 gap-6 mt-4",children:e.jsx("div",{className:"col-span-12",children:e.jsx(ee,{employeeCount:t.employeeCount,timeEntries:t.timeEntries,payrollPeriods:[{id:1,name:"January 2023",start_date:"2023-01-01",end_date:"2023-01-15",status:"completed"},{id:2,name:"February 2023",start_date:"2023-02-01",end_date:"2023-02-15",status:"completed"}]})})})})]})})})]})},Ee=()=>e.jsx(se,{});export{Ee as default};
