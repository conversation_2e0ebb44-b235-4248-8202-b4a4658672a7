import{ab as g,d as y,h as E,r as t,j as e,bw as l,bx as N,A as v,J as I,i as w}from"./index-C6AV3cVN.js";import{C as A}from"./Card-yj7fueH8.js";import{b as z,u as C}from"./supplier-BJDz25mb.js";import{S as D}from"./SupplierForm-j86Gm3W5.js";const F=()=>{const{id:r}=g(),p=y(),{currentOrganization:i}=E(),[n,x]=t.useState(null),[f,o]=t.useState(!0),[d,u]=t.useState(null),[S,m]=t.useState(!1),[j,c]=t.useState(null);t.useEffect(()=>{(async()=>{if(!(!i||!r)){o(!0),u(null);try{const{supplier:s,error:a}=await z(i.id,r);a?u(a):s?x(s):u("Supplier not found")}catch(s){u(s.message||"An error occurred while fetching the supplier")}finally{o(!1)}}})()},[i,r]);const b=async h=>{if(!(!i||!r)){m(!0),c(null);try{const{supplier:s,error:a}=await C(i.id,r,h);a?c(a):p(`/suppliers/${r}`)}catch(s){c(s.message||"An error occurred while updating the supplier")}finally{m(!1)}}};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"mb-6",children:e.jsxs(l,{children:[e.jsx(l.Item,{href:"/",icon:N,children:"Dashboard"}),e.jsx(l.Item,{href:"/suppliers",children:"Suppliers"}),n&&e.jsx(l.Item,{href:`/suppliers/${r}`,children:n.name}),e.jsx(l.Item,{children:"Edit"})]})}),e.jsxs(A,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Edit Supplier"}),e.jsx("p",{className:"text-gray-500",children:"Update supplier information"})]}),d&&e.jsx(v,{color:"failure",icon:I,className:"mb-4",children:d}),f?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(w,{size:"xl"})}):n?e.jsx(D,{initialData:n,onSubmit:b,isSubmitting:S,error:j||void 0,onCancel:()=>p(`/suppliers/${r}`)}):e.jsx("div",{className:"p-8 text-center",children:e.jsx("p",{className:"text-gray-500",children:"Supplier not found"})})]})]})};export{F as default};
