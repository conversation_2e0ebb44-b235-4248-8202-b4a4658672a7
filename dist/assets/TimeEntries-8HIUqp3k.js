import{bM as fe,h as pe,r as l,j as e,i as je,B as i,a0 as G,A as F,G as B,a6 as N,a7 as L,t as ge,Q as ye,av as Ne,_ as a,U as W,e as p,z as Q,bN as ve,al as J,a2 as be,a3 as Se,M as v}from"./index-C6AV3cVN.js";import{C as X}from"./Card-yj7fueH8.js";import{D as ee}from"./react-datepicker-BrCvW-wJ.js";import{g as we}from"./employee-DWC25S7P.js";import{T as h}from"./payroll-j3fcCwK0.js";import{P as Ee}from"./PageTitle-FHPo8gWi.js";import{P as Te}from"./Pagination-CVEzfctr.js";import{g as De,d as Ce,u as Me}from"./timeEntry-DfPyfZaA.js";import{f as R}from"./index-qirzObrW.js";import{p as Ae}from"./index-DNTDRJcs.js";import{i as He}from"./index-4YOzgfrD.js";import"./index-idNacaog.js";import"./index-Cn2wB4rc.js";import"./typeof-QjJsDpFa.js";import"./index-KY8jayTk.js";import"./index-DT2YvziZ.js";function _e(c){return fe({attr:{viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M12.75 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM7.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM8.25 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM9.75 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM10.5 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM12.75 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM14.25 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 13.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z"},child:[]},{tag:"path",attr:{fillRule:"evenodd",d:"M6.75 2.25A.75.75 0 0 1 7.5 3v1.5h9V3A.75.75 0 0 1 18 3v1.5h.75a3 3 0 0 1 3 3v11.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V7.5a3 3 0 0 1 3-3H6V3a.75.75 0 0 1 .75-.75Zm13.5 9a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5Z",clipRule:"evenodd"},child:[]}]})(c)}const Qe=()=>{const{currentOrganization:c}=pe(),[o,se]=l.useState([]),[O,te]=l.useState([]),[le,V]=l.useState(!0),[Z,b]=l.useState(null),[C,ae]=l.useState(0),[P,I]=l.useState(1),[U,M]=l.useState(!1),[ke,Oe]=l.useState(!1),[d,ie]=l.useState(null),[A,H]=l.useState(!1),[_,S]=l.useState(null),[r,j]=l.useState(null),[n,g]=l.useState(null),[z,$]=l.useState(""),[m,w]=l.useState([]),[re,E]=l.useState(!1),[x,K]=l.useState(""),[T,D]=l.useState(null),[k,ne]=l.useState(10),f=async()=>{if(c){V(!0),b(null);try{r&&n&&console.log("Date Filter:",{startDate:r.toISOString().split("T")[0],endDate:n.toISOString().split("T")[0],isSameDay:r.toISOString().split("T")[0]===n.toISOString().split("T")[0]});const{entries:s,count:t,error:u}=await De(c.id,{limit:k,offset:(P-1)*k,startDate:r||void 0,endDate:n||void 0,employeeId:z||void 0});u?b(u):(console.log(`Fetched ${s.length} entries:`,s.map(y=>({id:y.id,date:y.date,employee:y.employee_id}))),se(s),ae(t))}catch(s){b(s.message)}finally{V(!1)}}},ce=async()=>{if(c)try{const{employees:s,error:t}=await we(c.id);t?b(t):te(s)}catch(s){b(s.message)}};l.useEffect(()=>{(r||n)&&console.log("Filter values changed:",{startDate:r?r.toISOString():null,endDate:n?n.toISOString():null,startDateStr:r?r.toISOString().split("T")[0]:null,endDateStr:n?n.toISOString().split("T")[0]:null,isSameDay:r&&n?r.toISOString().split("T")[0]===n.toISOString().split("T")[0]:!1}),f()},[c,P,r,n,z]),l.useEffect(()=>{ce()},[c]),l.useEffect(()=>{const s=t=>{t.target instanceof HTMLInputElement||t.target instanceof HTMLTextAreaElement||t.target instanceof HTMLSelectElement||((t.ctrlKey||t.metaKey)&&t.key==="a"&&(t.preventDefault(),window.location.href="/payroll/add-time-entry"),(t.ctrlKey||t.metaKey)&&t.key==="b"&&m.length>0&&(t.preventDefault(),E(!0)))};return document.addEventListener("keydown",s),()=>{document.removeEventListener("keydown",s)}},[m]);const oe=async()=>{if(!(!c||!(d!=null&&d.id))){H(!0),S(null);try{const{success:s,error:t}=await Ce(c.id,d.id);t?S(t):s&&(M(!1),f())}catch(s){S(s.message)}finally{H(!1)}}},de=()=>{j(null),g(null),$(""),I(1)},me=s=>{w(t=>t.includes(s)?t.filter(u=>u!==s):[...t,s])},he=()=>{m.length===o.length?w([]):w(o.map(s=>s.id))},xe=async()=>{if(!(!c||m.length===0||!x)){H(!0),S(null);try{const s=o.filter(t=>m.includes(t.id)).map(t=>({...t,[x]:T}));for(const t of s)await Me(c.id,t);f(),w([]),E(!1),K(""),D(null)}catch(s){S(s.message||"An error occurred while updating entries")}finally{H(!1)}}},ue=s=>{switch(s){case h.PRESENT:return e.jsx(p,{color:"success",children:"Present"});case h.ABSENT:return e.jsx(p,{color:"failure",children:"Absent"});case h.LEAVE:return e.jsx(p,{color:"info",children:"Leave"});case h.HOLIDAY:return e.jsx(p,{color:"purple",children:"Holiday"});default:return e.jsx(p,{color:"gray",children:s})}},Y=s=>{const t=O.find(u=>u.id===s);return t?`${t.first_name} ${t.last_name}`:"Unknown Employee"},q=s=>{if(!s)return"N/A";try{const t=Ae(s);return He(t)?R(t,"h:mm a"):"Invalid Time"}catch{return"Invalid Time"}};return le&&o.length===0?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(je,{size:"xl"})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx(Ee,{title:"Time & Attendance"}),e.jsxs(i,{color:"primary",as:"a",href:"/payroll/add-time-entry",children:[e.jsx(G,{className:"mr-2 h-5 w-5"}),"Add Time Entry"]})]}),Z&&e.jsxs(F,{color:"failure",icon:B,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",Z]}),e.jsx(X,{className:"mb-6",children:e.jsxs("div",{className:"p-2",children:[e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-sm font-medium text-gray-700 mb-1",children:e.jsx(N,{htmlFor:"filterStartDate",value:"Date Range"})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ee,{id:"filterStartDate",selected:r,onChange:s=>{if(s){const t=new Date(s);t.setHours(12,0,0,0),j(t)}else j(null)},dateFormat:"MMM d, yyyy",className:"w-full rounded-lg border border-gray-300 p-2 text-sm",placeholderText:"Start date"}),e.jsx("span",{className:"text-gray-500",children:"to"}),e.jsx(ee,{id:"filterEndDate",selected:n,onChange:s=>{if(s){const t=new Date(s);t.setHours(12,0,0,0),g(t)}else g(null)},dateFormat:"MMM d, yyyy",className:"w-full rounded-lg border border-gray-300 p-2 text-sm",placeholderText:"End date",minDate:r||void 0})]})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-sm font-medium text-gray-700 mb-1",children:e.jsx(N,{htmlFor:"filterEmployee",value:"Employee"})}),e.jsxs(L,{id:"filterEmployee",value:z,onChange:s=>$(s.target.value),className:"text-sm",children:[e.jsx("option",{value:"",children:"All Employees"}),O.map(s=>e.jsxs("option",{value:s.id,children:[s.first_name," ",s.last_name]},s.id))]})]}),e.jsxs("div",{className:"flex items-end space-x-2",children:[e.jsxs(i,{size:"sm",color:"light",onClick:de,className:"whitespace-nowrap",children:[e.jsx(ge,{className:"mr-1 h-4 w-4"}),"Reset"]}),e.jsxs(i,{size:"sm",color:"blue",onClick:()=>f(),className:"whitespace-nowrap",children:[e.jsx(ye,{className:"mr-1 h-4 w-4"}),"Apply Filters"]})]})]}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-2",children:[e.jsx(i,{size:"xs",color:"light",onClick:()=>{const s=new Date;s.setHours(12,0,0,0),j(s),g(s),setTimeout(()=>f(),0)},children:"Today"}),e.jsx(i,{size:"xs",color:"light",onClick:()=>{const s=new Date;s.setDate(s.getDate()-1),s.setHours(12,0,0,0),j(s),g(s),setTimeout(()=>f(),0)},children:"Yesterday"}),e.jsx(i,{size:"xs",color:"light",onClick:()=>{const s=new Date;s.setHours(12,0,0,0);const t=new Date(s);t.setDate(s.getDate()-s.getDay()),j(t),g(s),setTimeout(()=>f(),0)},children:"This Week"}),e.jsx(i,{size:"xs",color:"light",onClick:()=>{const s=new Date;s.setHours(12,0,0,0);const t=new Date(s.getFullYear(),s.getMonth(),1);t.setHours(12,0,0,0),j(t),g(s),setTimeout(()=>f(),0)},children:"This Month"})]})]})}),e.jsx(X,{children:e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-blue-50 rounded-lg p-3 border border-blue-100",children:[e.jsx("div",{className:"text-sm text-blue-700 font-medium",children:"Total Entries"}),e.jsx("div",{className:"text-2xl font-bold text-blue-800",children:C})]}),e.jsxs("div",{className:"bg-green-50 rounded-lg p-3 border border-green-100",children:[e.jsx("div",{className:"text-sm text-green-700 font-medium",children:"Present"}),e.jsx("div",{className:"text-2xl font-bold text-green-800",children:o.filter(s=>s.status===h.PRESENT).length})]}),e.jsxs("div",{className:"bg-red-50 rounded-lg p-3 border border-red-100",children:[e.jsx("div",{className:"text-sm text-red-700 font-medium",children:"Absent"}),e.jsx("div",{className:"text-2xl font-bold text-red-800",children:o.filter(s=>s.status===h.ABSENT).length})]}),e.jsxs("div",{className:"bg-purple-50 rounded-lg p-3 border border-purple-100",children:[e.jsx("div",{className:"text-sm text-purple-700 font-medium",children:"On Leave"}),e.jsx("div",{className:"text-2xl font-bold text-purple-800",children:o.filter(s=>s.status===h.LEAVE).length})]})]}),m.length>0&&e.jsxs("div",{className:"bg-blue-50 p-3 mb-4 rounded-lg border border-blue-100 flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs("span",{className:"font-medium text-blue-800",children:[m.length," entries selected"]}),e.jsx(i,{size:"xs",color:"light",onClick:()=>w([]),className:"ml-2",children:"Clear"})]}),e.jsxs(i,{size:"sm",color:"blue",onClick:()=>E(!0),children:[e.jsx(Ne,{className:"mr-1 h-4 w-4"}),"Batch Actions"]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(a,{striped:!0,className:"text-sm",children:[e.jsxs(a.Head,{className:"bg-gray-50",children:[e.jsx(a.HeadCell,{className:"py-2 w-10",children:e.jsx(W,{checked:o.length>0&&m.length===o.length,onChange:he})}),e.jsx(a.HeadCell,{className:"py-2",children:"Date"}),e.jsx(a.HeadCell,{className:"py-2",children:"Employee"}),e.jsx(a.HeadCell,{className:"py-2",children:"Status"}),e.jsx(a.HeadCell,{className:"py-2",children:"Time"}),e.jsx(a.HeadCell,{className:"py-2",children:"Hours"}),e.jsx(a.HeadCell,{className:"py-2",children:"Actions"})]}),e.jsx(a.Body,{children:o.length>0?o.map(s=>{var t,u;return e.jsxs(a.Row,{className:`hover:bg-gray-50 ${m.includes(s.id)?"bg-blue-50":""}`,children:[e.jsx(a.Cell,{className:"py-2",children:e.jsx(W,{checked:m.includes(s.id),onChange:()=>me(s.id)})}),e.jsxs(a.Cell,{className:"py-2 whitespace-nowrap",children:[e.jsx("div",{className:"font-medium",children:s.date?R(new Date(s.date+"T00:00:00"),"MMM d, yyyy"):"Unknown Date"}),e.jsxs("div",{className:"flex flex-wrap gap-1 mt-1",children:[s.is_rest_day&&e.jsx(p,{color:"purple",size:"xs",children:"Rest Day"}),s.is_holiday&&e.jsx(p,{color:"info",size:"xs",children:"Holiday"}),s.shift_group&&o.filter(y=>y.shift_group===s.shift_group).length>1&&e.jsx(p,{color:"dark",size:"xs",children:"Split Shift"})]})]}),e.jsxs(a.Cell,{className:"py-2",children:[e.jsx("div",{className:"font-medium",children:Y(s.employee_id)}),e.jsx("div",{className:"text-xs text-gray-500",children:((u=(t=O.find(y=>y.id===s.employee_id))==null?void 0:t.position)==null?void 0:u.title)||""})]}),e.jsx(a.Cell,{className:"py-2",children:ue(s.status)}),e.jsx(a.Cell,{className:"py-2",children:s.status===h.PRESENT?e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"mr-1 h-3 w-3 text-gray-500"}),e.jsx("span",{children:s.time_in?q(s.time_in):"N/A"})]}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(Q,{className:"mr-1 h-3 w-3 text-gray-500"}),e.jsx("span",{children:s.time_out?q(s.time_out):"N/A"})]})]}):e.jsx("span",{className:"text-gray-500",children:"N/A"})}),e.jsxs(a.Cell,{className:"py-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-blue-500 mr-1"}),e.jsxs("span",{children:[s.regular_hours," reg"]})]}),s.overtime_hours>0&&e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-orange-500 mr-1"}),e.jsxs("span",{children:[s.overtime_hours," OT"]})]}),s.night_diff_hours>0&&e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-indigo-500 mr-1"}),e.jsxs("span",{children:[s.night_diff_hours," night"]}),e.jsx(ve,{className:"ml-1 h-3 w-3 text-indigo-600"})]})]}),e.jsx(a.Cell,{className:"py-2",children:e.jsxs("div",{className:"flex space-x-1",children:[e.jsx(J,{content:"Edit",children:e.jsx(i,{size:"xs",color:"light",as:"a",href:`/payroll/edit-time-entry/${s.id}`,children:e.jsx(be,{className:"h-3 w-3"})})}),e.jsx(J,{content:"Delete",children:e.jsx(i,{size:"xs",color:"light",onClick:()=>{ie(s),M(!0)},children:e.jsx(Se,{className:"h-3 w-3 text-red-500"})})})]})})]},s.id)}):e.jsx(a.Row,{children:e.jsx(a.Cell,{colSpan:7,className:"text-center py-8",children:e.jsxs("div",{className:"flex flex-col items-center justify-center text-gray-500",children:[e.jsx(_e,{className:"h-12 w-12 mb-2"}),e.jsx("p",{className:"text-lg font-medium",children:"No time entries found"}),e.jsx("p",{className:"text-sm",children:"Try adjusting your filters or add a new time entry"}),e.jsxs(i,{color:"blue",size:"sm",className:"mt-4",as:"a",href:"/payroll/add-time-entry",children:[e.jsx(G,{className:"mr-2 h-4 w-4"}),"Add Time Entry"]})]})})})})]})}),C>0&&e.jsx(Te,{currentPage:P,totalPages:Math.ceil(C/k),itemsPerPage:k,totalItems:C,onPageChange:I,onItemsPerPageChange:s=>{ne(s),I(1)},itemName:"entries"})]})}),e.jsx("div",{className:`${U?"fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50":"hidden"}`,children:e.jsxs(v,{show:U,onClose:()=>M(!1),size:"md",className:"mx-auto",popup:!1,children:[e.jsx(v.Header,{children:"Delete Time Entry"}),e.jsx(v.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{children:["Are you sure you want to delete this time entry for"," ",e.jsx("span",{className:"font-medium",children:d!=null&&d.employee_id?Y(d.employee_id):"Unknown Employee"})," ","on"," ",e.jsx("span",{className:"font-medium",children:d!=null&&d.date?R(new Date(d.date+"T00:00:00"),"MMMM d, yyyy"):"Unknown Date"}),"?"]}),_&&e.jsx(F,{color:"failure",icon:B,children:_}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(i,{color:"gray",onClick:()=>M(!1),disabled:A,children:"Cancel"}),e.jsx(i,{color:"failure",onClick:oe,isProcessing:A,children:"Delete"})]})]})})]})}),e.jsxs(v,{show:re,onClose:()=>E(!1),size:"md",children:[e.jsxs(v.Header,{children:["Batch Update ",m.length," Entries"]}),e.jsx(v.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(N,{htmlFor:"batchAction",value:"Select Field to Update",className:"mb-2 block"}),e.jsxs(L,{id:"batchAction",value:x,onChange:s=>{K(s.target.value),D(null)},required:!0,children:[e.jsx("option",{value:"",children:"Select a field..."}),e.jsx("option",{value:"status",children:"Status"}),e.jsx("option",{value:"is_rest_day",children:"Rest Day"}),e.jsx("option",{value:"is_holiday",children:"Holiday"}),e.jsx("option",{value:"exclude_lunch_break",children:"Exclude Lunch Break"})]})]}),x==="status"&&e.jsxs("div",{children:[e.jsx(N,{htmlFor:"statusValue",value:"New Status",className:"mb-2 block"}),e.jsxs(L,{id:"statusValue",value:T||"",onChange:s=>D(s.target.value),required:!0,children:[e.jsx("option",{value:h.PRESENT,children:"Present"}),e.jsx("option",{value:h.ABSENT,children:"Absent"}),e.jsx("option",{value:h.LEAVE,children:"Leave"}),e.jsx("option",{value:h.HOLIDAY,children:"Holiday"})]})]}),(x==="is_rest_day"||x==="is_holiday"||x==="exclude_lunch_break")&&e.jsxs("div",{children:[e.jsx(N,{htmlFor:"boolValue",value:`Set ${x.replace("_"," ").replace("is","").trim()} to:`,className:"mb-2 block"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"boolTrue",type:"radio",name:"boolValue",value:"true",checked:T===!0,onChange:()=>D(!0),className:"h-4 w-4 text-blue-600"}),e.jsx(N,{htmlFor:"boolTrue",className:"ml-2",children:"Yes"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"boolFalse",type:"radio",name:"boolValue",value:"false",checked:T===!1,onChange:()=>D(!1),className:"h-4 w-4 text-blue-600"}),e.jsx(N,{htmlFor:"boolFalse",className:"ml-2",children:"No"})]})]})]}),_&&e.jsx(F,{color:"failure",icon:B,children:_}),e.jsxs("div",{className:"flex justify-end space-x-2 pt-2",children:[e.jsx(i,{color:"gray",onClick:()=>E(!1),disabled:A,children:"Cancel"}),e.jsxs(i,{color:"blue",onClick:xe,isProcessing:A,disabled:!x||T===null,children:["Apply to ",m.length," Entries"]})]})]})})]})]})};export{Qe as default};
