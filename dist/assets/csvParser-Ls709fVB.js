const m=d=>{const o=d.trim().split(`
`);if(o.length===0)return{headers:[],rows:[]};const e=r=>{const s=[];let t="",n=!1,a=0;for(;a<r.length;){const f=r[a],l=r[a+1];f==='"'?n&&l==='"'?(t+='"',a+=2):(n=!n,a++):f===","&&!n?(s.push(t.trim()),t="",a++):(t+=f,a++)}return s.push(t.trim()),s},c=e(o[0]).map(r=>r.replace(/^"|"$/g,"").trim()),i=o.slice(1).map(r=>e(r).map(s=>s.replace(/^"|"$/g,"").trim()));return{headers:c,rows:i}},u=(d,o)=>o.map(e=>{const c={};return d.forEach((i,r)=>{const s=e[r]||"";c[i]=s}),c}),h=(d,o)=>{const e=[],c=[];return d.forEach((i,r)=>{o.forEach(s=>{const t=i[s.field],n=r+2;if(s.required&&(t==null||t==="")){e.push(`Row ${n}: ${s.field} is required`);return}if(!(t==null||t==="")){if(s.type)switch(s.type){case"number":const a=Number(t);isNaN(a)?e.push(`Row ${n}: ${s.field} must be a valid number`):(i[s.field]=a,s.min!==void 0&&a<s.min&&e.push(`Row ${n}: ${s.field} must be at least ${s.min}`),s.max!==void 0&&a>s.max&&e.push(`Row ${n}: ${s.field} must be at most ${s.max}`));break;case"boolean":const f=String(t).toLowerCase().trim();["true","false","1","0","yes","no","t","f","y","n"].includes(f)?i[s.field]=["true","1","yes","t","y"].includes(f):e.push(`Row ${n}: ${s.field} must be true/false, yes/no, 1/0, or t/f`);break;case"email":/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(String(t))||e.push(`Row ${n}: ${s.field} must be a valid email address`);break;case"url":try{new URL(String(t))}catch{e.push(`Row ${n}: ${s.field} must be a valid URL`)}break}if(s.pattern&&!s.pattern.test(String(t))&&e.push(`Row ${n}: ${s.field} format is invalid`),s.customValidator){const a=s.customValidator(t);a&&e.push(`Row ${n}: ${s.field} - ${a}`)}}})}),{errors:e,warnings:c}},p=(d,o)=>{try{const{headers:e,rows:c}=m(d);if(e.length===0)return{success:!1,data:[],headers:[],errors:["CSV file is empty or has no headers"],warnings:[]};const i=u(e,c),{errors:r,warnings:s}=h(i,o);return{success:r.length===0,data:i,headers:e,errors:r,warnings:s}}catch(e){return{success:!1,data:[],headers:[],errors:[`Failed to parse CSV: ${e instanceof Error?e.message:"Unknown error"}`],warnings:[]}}};export{p};
