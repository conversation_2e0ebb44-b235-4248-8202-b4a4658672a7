import{s as i}from"./index-C6AV3cVN.js";const u=async(a,r)=>{try{let e=i.from("products").select(`
        *,
        category:category_id (
          id,
          name,
          description
        )
      `,{count:"exact"}).eq("organization_id",a);r!=null&&r.categoryId&&(e=e.eq("category_id",r.categoryId)),(r==null?void 0:r.isActive)!==void 0&&(e=e.eq("is_active",r.isActive)),r!=null&&r.searchQuery&&(e=e.or(`name.ilike.%${r.searchQuery}%,sku.ilike.%${r.searchQuery}%,barcode.ilike.%${r.searchQuery}%`)),r!=null&&r.tagIds&&r.tagIds.length>0,r!=null&&r.sortBy?e=e.order(r.sortBy,{ascending:r.sortOrder==="asc"}):e=e.order("name",{ascending:!0}),r!=null&&r.limit&&(e=e.limit(r.limit)),r!=null&&r.offset&&(e=e.range(r.offset,r.offset+(r.limit||10)-1));const{data:t,error:c,count:o}=await e;return c?(console.error("Error fetching products:",c),{products:[],count:0,error:c.message}):{products:t,count:o||0}}catch(e){return console.error("Error in getProducts:",e),{products:[],count:0,error:e.message}}},n=async(a,r)=>{try{const{data:e,error:t}=await i.from("products").select(`
        *,
        category:category_id (
          id,
          name,
          description
        )
      `).eq("organization_id",a).eq("id",r).single();return t?(console.error("Error fetching product:",t),{error:t.message}):{product:e}}catch(e){return console.error("Error in getProductById:",e),{error:e.message}}},d=async(a,r)=>{try{const{data:e,error:t}=await i.from("products").select(`
        *,
        category:category_id (
          id,
          name,
          description
        ),
        product_uoms (
          *,
          uom:uom_id (*)
        )
      `).eq("organization_id",a).eq("id",r).single();return t?(console.error("Error fetching product with UoMs:",t),{error:t.message}):{product:e}}catch(e){return console.error("Error in getProductByIdWithUoms:",e),{error:e.message}}},g=async(a,r)=>{try{const{data:e,error:t}=await i.from("products").insert({...r,organization_id:a}).select(`
        *,
        category:category_id (
          id,
          name,
          description
        )
      `).single();return t?(console.error("Error creating product:",t),t.code==="23505"?t.message.includes("sku")||t.message.includes("products_organization_sku_unique")?{error:"A product with this SKU already exists in your organization"}:t.message.includes("barcode")||t.message.includes("products_organization_barcode_unique")?{error:"A product with this barcode already exists in your organization"}:{error:"A product with these details already exists"}:t.code==="23503"?{error:"Invalid category selected"}:{error:t.message||"Failed to create product"}):{product:e}}catch(e){return console.error("Error in createProduct:",e),{error:e.message}}},l=async(a,r,e)=>{try{if(!a||!r)return{error:"Organization ID and Product ID are required"};console.log("Updating product with data:",e),console.log("Category ID type:",typeof e.category_id);const t={...e};if("category"in t&&(delete t.category,console.log("Removed category property from updates")),"stock_quantity"in t&&(delete t.stock_quantity,console.log("Removed stock_quantity from manual updates - use inventory transactions instead")),t.name!==void 0&&(!t.name||t.name.trim()===""))return{error:"Product name is required"};if(t.unit_price!==void 0&&(t.unit_price===null||t.unit_price<0))return{error:"Unit price must be a positive number"};t.unit_price!==void 0&&(t.unit_price=Number(t.unit_price)),t.cost_price!==void 0&&(t.cost_price=t.cost_price?Number(t.cost_price):null),t.tax_rate!==void 0&&(t.tax_rate=t.tax_rate?Number(t.tax_rate):null),t.stock_quantity!==void 0&&(t.stock_quantity=t.stock_quantity?Number(t.stock_quantity):null),t.min_stock_level!==void 0&&(t.min_stock_level=t.min_stock_level?Number(t.min_stock_level):null),console.log("Clean updates object:",t);const{data:c,error:o}=await i.from("products").update(t).eq("organization_id",a).eq("id",r).select(`
        *,
        category:category_id (
          id,
          name,
          description
        )
      `).single();return o?(console.error("Error updating product:",o),o.code==="23505"?o.message.includes("sku")||o.message.includes("products_organization_sku_unique")?{error:"A product with this SKU already exists in your organization"}:o.message.includes("barcode")||o.message.includes("products_organization_barcode_unique")?{error:"A product with this barcode already exists in your organization"}:{error:"A product with these details already exists"}:o.code==="23503"?{error:"Invalid category selected"}:{error:o.message||"Failed to update product"}):{product:c}}catch(t){return console.error("Error in updateProduct:",t),{error:t.message||"An unexpected error occurred while updating the product"}}},y=async(a,r)=>{try{const{error:e}=await i.from("products").delete().eq("organization_id",a).eq("id",r);return e?(console.error("Error deleting product:",e),{success:!1,error:e.message}):{success:!0}}catch(e){return console.error("Error in deleteProduct:",e),{success:!1,error:e.message}}},m=async(a,r)=>{try{let e=i.from("categories").select("*",{count:"exact"}).eq("organization_id",a);r!=null&&r.searchQuery&&(e=e.or(`name.ilike.%${r.searchQuery}%,description.ilike.%${r.searchQuery}%`)),r!=null&&r.sortBy?e=e.order(r.sortBy,{ascending:r.sortOrder==="asc"}):e=e.order("name",{ascending:!0}),r!=null&&r.limit&&(e=e.limit(r.limit)),r!=null&&r.offset&&(e=e.range(r.offset,r.offset+(r.limit||10)-1));const{data:t,error:c,count:o}=await e;return c?(console.error("Error fetching categories:",c),{categories:[],count:0,error:c.message}):{categories:t,count:o||0}}catch(e){return console.error("Error in getCategories:",e),{categories:[],count:0,error:e.message}}},f=async(a,r,e)=>{try{const{data:t,error:c}=await i.from("categories").insert({organization_id:a,name:r,description:e}).select("*").single();return c?(console.error("Error creating category:",c),{error:c.message}):{category:t}}catch(t){return console.error("Error in createCategory:",t),{error:t.message}}},_=async(a,r,e)=>{try{const{data:t,error:c}=await i.from("categories").update(e).eq("organization_id",a).eq("id",r).select("*").single();return c?(console.error("Error updating category:",c),{error:c.message}):{category:t}}catch(t){return console.error("Error in updateCategory:",t),{error:t.message}}},h=async(a,r)=>{try{const{error:e}=await i.from("categories").delete().eq("organization_id",a).eq("id",r);return e?(console.error("Error deleting category:",e),{success:!1,error:e.message}):{success:!0}}catch(e){return console.error("Error in deleteCategory:",e),{success:!1,error:e.message}}};export{n as a,m as b,g as c,y as d,f as e,_ as f,u as g,h,d as i,l as u};
