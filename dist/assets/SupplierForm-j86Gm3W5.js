import{h as M,r as l,j as e,A as T,J as A,a6 as c,P as m,a7 as I,ad as G,B as g,i as B}from"./index-C6AV3cVN.js";const E=[{code:"AF",name:"Afghanistan"},{code:"AL",name:"Albania"},{code:"DZ",name:"Algeria"},{code:"AS",name:"American Samoa"},{code:"AD",name:"Andorra"},{code:"AO",name:"Angola"},{code:"AI",name:"Anguilla"},{code:"AQ",name:"Antarctica"},{code:"AG",name:"Antigua and Barbuda"},{code:"AR",name:"Argentina"},{code:"AM",name:"Armenia"},{code:"AW",name:"Aruba"},{code:"AU",name:"Australia"},{code:"AT",name:"Austria"},{code:"AZ",name:"Azerbaijan"},{code:"BS",name:"Bahamas"},{code:"BH",name:"Bahrain"},{code:"BD",name:"Bangladesh"},{code:"BB",name:"Barbados"},{code:"BY",name:"Belarus"},{code:"BE",name:"Belgium"},{code:"BZ",name:"Belize"},{code:"BJ",name:"Benin"},{code:"BM",name:"Bermuda"},{code:"BT",name:"Bhutan"},{code:"BO",name:"Bolivia"},{code:"BA",name:"Bosnia and Herzegovina"},{code:"BW",name:"Botswana"},{code:"BV",name:"Bouvet Island"},{code:"BR",name:"Brazil"},{code:"IO",name:"British Indian Ocean Territory"},{code:"BN",name:"Brunei Darussalam"},{code:"BG",name:"Bulgaria"},{code:"BF",name:"Burkina Faso"},{code:"BI",name:"Burundi"},{code:"KH",name:"Cambodia"},{code:"CM",name:"Cameroon"},{code:"CA",name:"Canada"},{code:"CV",name:"Cape Verde"},{code:"KY",name:"Cayman Islands"},{code:"CF",name:"Central African Republic"},{code:"TD",name:"Chad"},{code:"CL",name:"Chile"},{code:"CN",name:"China"},{code:"CX",name:"Christmas Island"},{code:"CC",name:"Cocos (Keeling) Islands"},{code:"CO",name:"Colombia"},{code:"KM",name:"Comoros"},{code:"CG",name:"Congo"},{code:"CD",name:"Congo, Democratic Republic of the"},{code:"CK",name:"Cook Islands"},{code:"CR",name:"Costa Rica"},{code:"CI",name:"Cote D'Ivoire"},{code:"HR",name:"Croatia"},{code:"CU",name:"Cuba"},{code:"CY",name:"Cyprus"},{code:"CZ",name:"Czech Republic"},{code:"DK",name:"Denmark"},{code:"DJ",name:"Djibouti"},{code:"DM",name:"Dominica"},{code:"DO",name:"Dominican Republic"},{code:"EC",name:"Ecuador"},{code:"EG",name:"Egypt"},{code:"SV",name:"El Salvador"},{code:"GQ",name:"Equatorial Guinea"},{code:"ER",name:"Eritrea"},{code:"EE",name:"Estonia"},{code:"ET",name:"Ethiopia"},{code:"FK",name:"Falkland Islands (Malvinas)"},{code:"FO",name:"Faroe Islands"},{code:"FJ",name:"Fiji"},{code:"FI",name:"Finland"},{code:"FR",name:"France"},{code:"GF",name:"French Guiana"},{code:"PF",name:"French Polynesia"},{code:"TF",name:"French Southern Territories"},{code:"GA",name:"Gabon"},{code:"GM",name:"Gambia"},{code:"GE",name:"Georgia"},{code:"DE",name:"Germany"},{code:"GH",name:"Ghana"},{code:"GI",name:"Gibraltar"},{code:"GR",name:"Greece"},{code:"GL",name:"Greenland"},{code:"GD",name:"Grenada"},{code:"GP",name:"Guadeloupe"},{code:"GU",name:"Guam"},{code:"GT",name:"Guatemala"},{code:"GN",name:"Guinea"},{code:"GW",name:"Guinea-Bissau"},{code:"GY",name:"Guyana"},{code:"HT",name:"Haiti"},{code:"HM",name:"Heard Island and Mcdonald Islands"},{code:"VA",name:"Holy See (Vatican City State)"},{code:"HN",name:"Honduras"},{code:"HK",name:"Hong Kong"},{code:"HU",name:"Hungary"},{code:"IS",name:"Iceland"},{code:"IN",name:"India"},{code:"ID",name:"Indonesia"},{code:"IR",name:"Iran, Islamic Republic of"},{code:"IQ",name:"Iraq"},{code:"IE",name:"Ireland"},{code:"IL",name:"Israel"},{code:"IT",name:"Italy"},{code:"JM",name:"Jamaica"},{code:"JP",name:"Japan"},{code:"JO",name:"Jordan"},{code:"KZ",name:"Kazakhstan"},{code:"KE",name:"Kenya"},{code:"KI",name:"Kiribati"},{code:"KP",name:"Korea, Democratic People's Republic of"},{code:"KR",name:"Korea, Republic of"},{code:"KW",name:"Kuwait"},{code:"KG",name:"Kyrgyzstan"},{code:"LA",name:"Lao People's Democratic Republic"},{code:"LV",name:"Latvia"},{code:"LB",name:"Lebanon"},{code:"LS",name:"Lesotho"},{code:"LR",name:"Liberia"},{code:"LY",name:"Libyan Arab Jamahiriya"},{code:"LI",name:"Liechtenstein"},{code:"LT",name:"Lithuania"},{code:"LU",name:"Luxembourg"},{code:"MO",name:"Macao"},{code:"MK",name:"Macedonia, The Former Yugoslav Republic of"},{code:"MG",name:"Madagascar"},{code:"MW",name:"Malawi"},{code:"MY",name:"Malaysia"},{code:"MV",name:"Maldives"},{code:"ML",name:"Mali"},{code:"MT",name:"Malta"},{code:"MH",name:"Marshall Islands"},{code:"MQ",name:"Martinique"},{code:"MR",name:"Mauritania"},{code:"MU",name:"Mauritius"},{code:"YT",name:"Mayotte"},{code:"MX",name:"Mexico"},{code:"FM",name:"Micronesia, Federated States of"},{code:"MD",name:"Moldova, Republic of"},{code:"MC",name:"Monaco"},{code:"MN",name:"Mongolia"},{code:"MS",name:"Montserrat"},{code:"MA",name:"Morocco"},{code:"MZ",name:"Mozambique"},{code:"MM",name:"Myanmar"},{code:"NA",name:"Namibia"},{code:"NR",name:"Nauru"},{code:"NP",name:"Nepal"},{code:"NL",name:"Netherlands"},{code:"NC",name:"New Caledonia"},{code:"NZ",name:"New Zealand"},{code:"NI",name:"Nicaragua"},{code:"NE",name:"Niger"},{code:"NG",name:"Nigeria"},{code:"NU",name:"Niue"},{code:"NF",name:"Norfolk Island"},{code:"MP",name:"Northern Mariana Islands"},{code:"NO",name:"Norway"},{code:"OM",name:"Oman"},{code:"PK",name:"Pakistan"},{code:"PW",name:"Palau"},{code:"PS",name:"Palestinian Territory, Occupied"},{code:"PA",name:"Panama"},{code:"PG",name:"Papua New Guinea"},{code:"PY",name:"Paraguay"},{code:"PE",name:"Peru"},{code:"PH",name:"Philippines"},{code:"PN",name:"Pitcairn"},{code:"PL",name:"Poland"},{code:"PT",name:"Portugal"},{code:"PR",name:"Puerto Rico"},{code:"QA",name:"Qatar"},{code:"RE",name:"Reunion"},{code:"RO",name:"Romania"},{code:"RU",name:"Russian Federation"},{code:"RW",name:"Rwanda"},{code:"SH",name:"Saint Helena"},{code:"KN",name:"Saint Kitts and Nevis"},{code:"LC",name:"Saint Lucia"},{code:"PM",name:"Saint Pierre and Miquelon"},{code:"VC",name:"Saint Vincent and the Grenadines"},{code:"WS",name:"Samoa"},{code:"SM",name:"San Marino"},{code:"ST",name:"Sao Tome and Principe"},{code:"SA",name:"Saudi Arabia"},{code:"SN",name:"Senegal"},{code:"CS",name:"Serbia and Montenegro"},{code:"SC",name:"Seychelles"},{code:"SL",name:"Sierra Leone"},{code:"SG",name:"Singapore"},{code:"SK",name:"Slovakia"},{code:"SI",name:"Slovenia"},{code:"SB",name:"Solomon Islands"},{code:"SO",name:"Somalia"},{code:"ZA",name:"South Africa"},{code:"GS",name:"South Georgia and the South Sandwich Islands"},{code:"ES",name:"Spain"},{code:"LK",name:"Sri Lanka"},{code:"SD",name:"Sudan"},{code:"SR",name:"Suriname"},{code:"SJ",name:"Svalbard and Jan Mayen"},{code:"SZ",name:"Swaziland"},{code:"SE",name:"Sweden"},{code:"CH",name:"Switzerland"},{code:"SY",name:"Syrian Arab Republic"},{code:"TW",name:"Taiwan"},{code:"TJ",name:"Tajikistan"},{code:"TZ",name:"Tanzania, United Republic of"},{code:"TH",name:"Thailand"},{code:"TL",name:"Timor-Leste"},{code:"TG",name:"Togo"},{code:"TK",name:"Tokelau"},{code:"TO",name:"Tonga"},{code:"TT",name:"Trinidad and Tobago"},{code:"TN",name:"Tunisia"},{code:"TR",name:"Turkey"},{code:"TM",name:"Turkmenistan"},{code:"TC",name:"Turks and Caicos Islands"},{code:"TV",name:"Tuvalu"},{code:"UG",name:"Uganda"},{code:"UA",name:"Ukraine"},{code:"AE",name:"United Arab Emirates"},{code:"GB",name:"United Kingdom"},{code:"US",name:"United States"},{code:"UM",name:"United States Minor Outlying Islands"},{code:"UY",name:"Uruguay"},{code:"UZ",name:"Uzbekistan"},{code:"VU",name:"Vanuatu"},{code:"VE",name:"Venezuela"},{code:"VN",name:"Vietnam"},{code:"VG",name:"Virgin Islands, British"},{code:"VI",name:"Virgin Islands, U.S."},{code:"WF",name:"Wallis and Futuna"},{code:"EH",name:"Western Sahara"},{code:"YE",name:"Yemen"},{code:"ZM",name:"Zambia"},{code:"ZW",name:"Zimbabwe"}],f=({initialData:r,onSubmit:S,isSubmitting:u,error:h,viewMode:N=!1,onCancel:x})=>{const{currentOrganization:P}=M(),[a,p]=l.useState({name:"",contact_person:"",email:"",phone:"",address:"",city:"",state:"",postal_code:"",country:"",tax_id:"",notes:"",...r}),[i,j]=l.useState({});l.useEffect(()=>{r&&p({name:"",contact_person:"",email:"",phone:"",address:"",city:"",state:"",postal_code:"",country:"",tax_id:"",notes:"",...r})},[r]);const o=n=>{const{name:s,value:y}=n.target;p(t=>({...t,[s]:y})),i[s]&&j(t=>{const v={...t};return delete v[s],v})},b=()=>{var s;const n={};return(s=a.name)!=null&&s.trim()||(n.name="Supplier name is required"),a.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a.email)&&(n.email="Please enter a valid email address"),j(n),Object.keys(n).length===0},C=async n=>{n.preventDefault(),b()&&await S(a)},d=(n,s="-")=>n||e.jsx("span",{className:"text-gray-400",children:s});return N?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Supplier Name"}),e.jsx("p",{className:"mt-1 text-base",children:d(a.name)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Contact Person"}),e.jsx("p",{className:"mt-1 text-base",children:d(a.contact_person)})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Email"}),e.jsx("p",{className:"mt-1 text-base",children:d(a.email)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Phone"}),e.jsx("p",{className:"mt-1 text-base",children:d(a.phone)})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Address"}),e.jsx("p",{className:"mt-1 text-base",children:d(a.address)})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"City"}),e.jsx("p",{className:"mt-1 text-base",children:d(a.city)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"State/Province"}),e.jsx("p",{className:"mt-1 text-base",children:d(a.state)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Postal Code"}),e.jsx("p",{className:"mt-1 text-base",children:d(a.postal_code)})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Country"}),e.jsx("p",{className:"mt-1 text-base",children:d(a.country)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Tax ID"}),e.jsx("p",{className:"mt-1 text-base",children:d(a.tax_id)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Notes"}),e.jsx("p",{className:"mt-1 text-base whitespace-pre-line",children:d(a.notes)})]})]}):e.jsxs("form",{onSubmit:C,className:"space-y-4",children:[h&&e.jsx(T,{color:"failure",icon:A,children:h}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"name",value:"Supplier Name *"})}),e.jsx(m,{id:"name",name:"name",value:a.name||"",onChange:o,placeholder:"Enter supplier name",required:!0,color:i.name?"failure":void 0,helperText:i.name})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"contact_person",value:"Contact Person"})}),e.jsx(m,{id:"contact_person",name:"contact_person",value:a.contact_person||"",onChange:o,placeholder:"Enter contact person name"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"email",value:"Email"})}),e.jsx(m,{id:"email",name:"email",type:"email",value:a.email||"",onChange:o,placeholder:"Enter email address",color:i.email?"failure":void 0,helperText:i.email})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"phone",value:"Phone"})}),e.jsx(m,{id:"phone",name:"phone",value:a.phone||"",onChange:o,placeholder:"Enter phone number"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"address",value:"Address"})}),e.jsx(m,{id:"address",name:"address",value:a.address||"",onChange:o,placeholder:"Enter street address"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"city",value:"City"})}),e.jsx(m,{id:"city",name:"city",value:a.city||"",onChange:o,placeholder:"Enter city"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"state",value:"State/Province"})}),e.jsx(m,{id:"state",name:"state",value:a.state||"",onChange:o,placeholder:"Enter state or province"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"postal_code",value:"Postal Code"})}),e.jsx(m,{id:"postal_code",name:"postal_code",value:a.postal_code||"",onChange:o,placeholder:"Enter postal code"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"country",value:"Country"})}),e.jsxs(I,{id:"country",name:"country",value:a.country||"",onChange:o,children:[e.jsx("option",{value:"",children:"Select a country"}),E.map(n=>e.jsx("option",{value:n.name,children:n.name},n.code))]})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"tax_id",value:"Tax ID"})}),e.jsx(m,{id:"tax_id",name:"tax_id",value:a.tax_id||"",onChange:o,placeholder:"Enter tax ID"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(c,{htmlFor:"notes",value:"Notes"})}),e.jsx(G,{id:"notes",name:"notes",value:a.notes||"",onChange:o,placeholder:"Enter additional notes",rows:3})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[x&&e.jsx(g,{color:"gray",onClick:x,type:"button",children:"Cancel"}),e.jsx(g,{color:"primary",type:"submit",disabled:u,children:u?e.jsxs(e.Fragment,{children:[e.jsx(B,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Supplier"})]})]})};export{f as S};
