import{s as t}from"./index-C6AV3cVN.js";const n=async s=>{try{const{data:r,error:e}=await t.from("tags").insert(s).select().single();return e?(console.error("Error creating tag:",e),{success:!1,error:e.message}):{success:!0,tag:r}}catch(r){return console.error("Error in createTag:",r),{success:!1,error:r.message}}},c=async(s,r)=>{try{const{data:e,error:a}=await t.from("tags").update(r).eq("id",s).select().single();return a?(console.error("Error updating tag:",a),{success:!1,error:a.message}):{success:!0,tag:e}}catch(e){return console.error("Error in updateTag:",e),{success:!1,error:e.message}}},i=async s=>{try{const{error:r}=await t.from("tags").delete().eq("id",s);return r?(console.error("Error deleting tag:",r),{success:!1,error:r.message}):{success:!0}}catch(r){return console.error("Error in deleteTag:",r),{success:!1,error:r.message}}},g=async s=>{try{const{data:r,error:e}=await t.from("tags").select("*").eq("organization_id",s).order("name");return e?(console.error("Error fetching organization tags:",e),{success:!1,error:e.message}):{success:!0,tags:r}}catch(r){return console.error("Error in getOrganizationTags:",r),{success:!1,error:r.message}}},u=async s=>{try{const{data:r,error:e}=await t.rpc("get_tags_with_count",{p_organization_id:s});return e?(console.error("Error fetching tags with count:",e),{success:!1,error:e.message}):{success:!0,tags:r}}catch(r){return console.error("Error in getTagsWithCount:",r),{success:!1,error:r.message}}},d=async s=>{try{const{data:r,error:e}=await t.rpc("add_tag_to_entity",{p_tag_id:s.tag_id,p_entity_type:s.entity_type,p_entity_id:s.entity_id});return e?(console.error("Error adding tag to entity:",e),{success:!1,error:e.message}):{success:!0,id:r}}catch(r){return console.error("Error in addTagToEntity:",r),{success:!1,error:r.message}}},l=async s=>{try{const{data:r,error:e}=await t.rpc("remove_tag_from_entity",{p_tag_id:s.tag_id,p_entity_type:s.entity_type,p_entity_id:s.entity_id});return e?(console.error("Error removing tag from entity:",e),{success:!1,error:e.message}):{success:!0}}catch(r){return console.error("Error in removeTagFromEntity:",r),{success:!1,error:r.message}}},y=async(s,r)=>{try{const{data:e,error:a}=await t.rpc("find_entities_by_tag",{p_tag_id:s,p_entity_type:r});return a?(console.error("Error finding entities by tag:",a),{success:!1,error:a.message}):{success:!0,entities:e}}catch(e){return console.error("Error in findEntitiesByTag:",e),{success:!1,error:e.message}}},f=async s=>{try{const{data:r,error:e}=await t.from("tags").select("*").eq("id",s).single();return e?(console.error("Error fetching tag by ID:",e),{success:!1,error:e.message}):{success:!0,tag:r}}catch(r){return console.error("Error in getTagById:",r),{success:!1,error:r.message}}};export{d as a,u as b,n as c,i as d,f as e,y as f,g,l as r,c as u};
