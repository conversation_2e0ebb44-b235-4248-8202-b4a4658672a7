import{h as R,r as l,j as r,i as $,A as D,J as P,$ as K,_ as N,U as H,B as U,z as L,al as O,M as F}from"./index-C6AV3cVN.js";import{C as T}from"./Card-yj7fueH8.js";import{getProductUoms as A,updateProductUom as I}from"./productUom-k6aUg6b7.js";const W=({productId:e,onComplete:t})=>{const{currentOrganization:s}=R(),[i,u]=l.useState([]),[f,o]=l.useState(!0),[c,a]=l.useState(!1),[x,m]=l.useState(null),[S,b]=l.useState(null),[v,d]=l.useState({});l.useEffect(()=>{(async()=>{if(!(!e||!s)){o(!0),m(null);try{const{productUoms:h,error:g}=await A(e,s.id);if(g)m(g);else{u(h);const w={};h.forEach(E=>{w[E.id]=E.is_purchasing_unit}),d(w)}}catch(h){m(h.message||"Failed to load units of measurement")}finally{o(!1)}}})()},[e,s]);const y=n=>{d(h=>({...h,[n]:!h[n]}))},p=async()=>{if(s){a(!0),m(null),b(null);try{const n=i.map(async h=>{const g=v[h.id];return g!==h.is_purchasing_unit?I(h.id,{is_purchasing_unit:g},s.id):null});await Promise.all(n.filter(Boolean)),b("Units of measurement updated successfully"),t&&t()}catch(n){m(n.message||"An error occurred while updating units of measurement")}finally{a(!1)}}};return f?r.jsx("div",{className:"flex justify-center items-center p-8",children:r.jsx($,{size:"xl"})}):i.length===0?r.jsxs(D,{color:"warning",icon:P,children:[r.jsx("h3",{className:"font-medium",children:"No units of measurement found"}),r.jsx("p",{children:"This product doesn't have any units of measurement defined. Please add units of measurement first."})]}):r.jsxs(T,{children:[r.jsx("h3",{className:"text-lg font-medium mb-4",children:"Quick UoM Setup for Purchasing"}),x&&r.jsx(D,{color:"failure",icon:P,className:"mb-4",children:x}),S&&r.jsx(D,{color:"success",icon:K,className:"mb-4",children:S}),r.jsx("p",{className:"text-sm text-gray-500 mb-4",children:"Check the units of measurement that should be available for purchasing this product:"}),r.jsxs(N,{children:[r.jsxs(N.Head,{children:[r.jsx(N.HeadCell,{children:"Unit"}),r.jsx(N.HeadCell,{children:"Conversion Factor"}),r.jsx(N.HeadCell,{children:"Default"}),r.jsx(N.HeadCell,{children:"Use for Purchasing"})]}),r.jsx(N.Body,{className:"divide-y",children:i.map(n=>r.jsxs(N.Row,{children:[r.jsxs(N.Cell,{children:[n.uom.name," (",n.uom.code,")"]}),r.jsx(N.Cell,{children:n.conversion_factor}),r.jsx(N.Cell,{children:n.is_default?r.jsx("span",{className:"text-green-500",children:"Yes"}):r.jsx("span",{className:"text-gray-400",children:"No"})}),r.jsx(N.Cell,{children:r.jsx(H,{checked:v[n.id]||!1,onChange:()=>y(n.id)})})]},n.id))})]}),r.jsx("div",{className:"flex justify-end mt-4",children:r.jsx(U,{color:"primary",onClick:p,disabled:c,children:c?r.jsxs(r.Fragment,{children:[r.jsx($,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Changes"})})]})},_=(e,t)=>{try{const s={data:t,timestamp:new Date().toISOString()};localStorage.setItem(e,JSON.stringify(s))}catch(s){console.error("Error saving data to localStorage:",s)}},z=e=>{try{const t=localStorage.getItem(e);return t?JSON.parse(t):null}catch(t){return console.error("Error loading data from localStorage:",t),null}},B=e=>{try{localStorage.removeItem(e)}catch(t){console.error("Error clearing data from localStorage:",t)}},V=e=>{const t=new Date(e),i=new Date().getTime()-t.getTime(),u=Math.floor(i/1e3);if(u<60)return`${u} second${u!==1?"s":""} ago`;const f=Math.floor(u/60);if(f<60)return`${f} minute${f!==1?"s":""} ago`;const o=Math.floor(f/60);if(o<24)return`${o} hour${o!==1?"s":""} ago`;const c=Math.floor(o/24);return`${c} day${c!==1?"s":""} ago`};function Y({key:e,initialData:t,interval:s=5e3,loadOnMount:i=!0,enabled:u=!0,onSave:f,onLoad:o,onClear:c}){const[a,x]=l.useState(t),[m,S]=l.useState(null),[b,v]=l.useState(!1),[d,y]=l.useState(!1),[p,n]=l.useState(!1),h=l.useRef(a),g=l.useRef(null);l.useEffect(()=>{h.current=a,v(!0)},[a]),l.useEffect(()=>{if(i){const j=z(e);j&&(x(j.data),S(j.timestamp),n(!0),v(!1),o&&o(j.data))}},[e,i,o]),l.useEffect(()=>{try{const j=localStorage.getItem(e)!==null;n(j)}catch(j){console.error("Error checking for saved data:",j)}},[e]);const w=l.useCallback(()=>{if(u){y(!0);try{const j=h.current;_(e,j);const k=new Date().toISOString();S(k),v(!1),n(!0),f&&f(j)}catch(j){console.error("Error saving data:",j)}finally{y(!1)}}},[u,e,f]),E=l.useCallback(()=>{B(e),S(null),v(!1),n(!1),c&&c()},[e,c]);l.useEffect(()=>{if(u)return g.current&&clearInterval(g.current),g.current=setInterval(()=>{b&&w()},s),()=>{g.current&&clearInterval(g.current)}},[u,s,b,w]);const M=m?V(m):"Never";return{data:a,setData:x,save:w,clear:E,hasSavedData:p,lastSaved:m,lastSavedFormatted:M,isDirty:b,isSaving:d}}const G=(e,t)=>{const{isEnabled:s=!0,preventDefault:i=!0,stopPropagation:u=!1,callback:f}=t,o=l.useRef(f);l.useEffect(()=>{o.current=f},[f]);const c=l.useCallback(a=>{if(!s)return;const x=e.alt?a.altKey:!a.altKey,m=e.ctrl?a.ctrlKey:!a.ctrlKey,S=e.shift?a.shiftKey:!a.shiftKey,b=e.meta?a.metaKey:!a.metaKey,v=a.key.toLowerCase()===e.key.toLowerCase(),d=(!e.alt||x)&&(!e.ctrl||m)&&(!e.shift||S)&&(!e.meta||b);v&&d&&(i&&a.preventDefault(),u&&a.stopPropagation(),o.current(a))},[s,i,u,e.key,e.alt,e.ctrl,e.shift,e.meta]);l.useEffect(()=>(s&&document.addEventListener("keydown",c),()=>{document.removeEventListener("keydown",c)}),[s,c])},C=e=>{const t=[];e.ctrl&&t.push("Ctrl"),e.alt&&t.push("Alt"),e.shift&&t.push("Shift"),e.meta&&t.push(navigator.platform.includes("Mac")?"⌘":"Win");let s=e.key;switch(s.toLowerCase()){case"enter":s="↵";break;case"escape":s="Esc";break;case"arrowup":s="↑";break;case"arrowdown":s="↓";break;case"arrowleft":s="←";break;case"arrowright":s="→";break;case"delete":s="Del";break;default:s.length===1&&(s=s.toUpperCase())}return t.push(s),t.join(" + ")};function X(e,t){const[s,i]=l.useState(e),[u,f]=l.useState({}),[o,c]=l.useState([]),a=l.useCallback(d=>{const y=t.fields[d];if(!y)return!0;const p=[];for(const n of y)n.validate(s[d],s)||p.push(n.message);return f(n=>({...n,[d]:p.length>0?p:void 0})),p.length===0},[s,t.fields]),x=l.useCallback(()=>{let d=!0;const y={},p=[];for(const n in t.fields){const h=t.fields[n];if(h){const g=[];for(const w of h)w.validate(s[n],s)||g.push(w.message);g.length>0&&(y[n]=g,d=!1)}}if(t.form)for(const n of t.form)n.validate(null,s)||(p.push(n.message),d=!1);return f(y),c(p),d},[s,t.fields,t.form]),m=l.useCallback((d,y)=>{i(p=>({...p,[d]:y})),setTimeout(()=>{a(d)},100)},[a]),S=l.useCallback(()=>{i(e),f({}),c([])},[e]),b=l.useCallback(d=>{i(d)},[]),v=Object.keys(u).length===0&&o.length===0;return{formData:s,setFormData:i,errors:u,formErrors:o,isValid:v,validateField:a,validateForm:x,handleChange:m,resetForm:S,setFormDataWithoutValidation:b}}const Z={required:(e="This field is required")=>({validate:t=>t==null?!1:typeof t=="string"?t.trim()!=="":Array.isArray(t)?t.length>0:!0,message:e}),minLength:(e,t=`Must be at least ${e} characters`)=>({validate:s=>typeof s!="string"?!0:s.length>=e,message:t}),maxLength:(e,t=`Must be no more than ${e} characters`)=>({validate:s=>typeof s!="string"?!0:s.length<=e,message:t}),email:(e="Please enter a valid email address")=>({validate:t=>t?typeof t!="string"?!1:/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t):!0,message:e}),numeric:(e="Please enter a valid number")=>({validate:t=>t?!isNaN(Number(t)):!0,message:e}),min:(e,t=`Must be at least ${e}`)=>({validate:s=>s?Number(s)>=e:!0,message:t}),max:(e,t=`Must be no more than ${e}`)=>({validate:s=>s?Number(s)<=e:!0,message:t}),pattern:(e,t="Invalid format")=>({validate:s=>s?typeof s!="string"?!1:e.test(s):!0,message:t}),custom:(e,t)=>({validate:e,message:t})};function ee({errors:e,formErrors:t,fieldLabels:s,show:i,className:u=""}){if(!i)return null;const o=Object.values(e).reduce((c,a)=>c+((a==null?void 0:a.length)||0),0)+t.length;return o===0?null:r.jsxs(D,{color:"failure",icon:P,className:`mb-4 ${u}`,children:[r.jsxs("h4",{className:"text-lg font-medium mb-2",children:["Please fix the following ",o===1?"error":"errors",":"]}),r.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[t.map((c,a)=>r.jsx("li",{children:c},`form-${a}`)),Object.entries(e).map(([c,a])=>{if(!a||a.length===0)return null;const x=s[c]||c;return a.map((m,S)=>r.jsxs("li",{children:[r.jsxs("strong",{children:[x,":"]})," ",m]},`${c}-${S}`))})]})]})}const te=({isSaving:e,isDirty:t,lastSavedFormatted:s,className:i=""})=>r.jsx("div",{className:`flex items-center text-sm text-gray-500 ${i}`,children:e?r.jsxs(r.Fragment,{children:[r.jsx($,{size:"sm",className:"mr-2"}),r.jsx("span",{children:"Saving..."})]}):t?r.jsxs(r.Fragment,{children:[r.jsx(L,{className:"mr-2 h-4 w-4 text-yellow-500"}),r.jsx("span",{children:"Unsaved changes"})]}):r.jsx(O,{content:`Last saved: ${s}`,children:r.jsxs("div",{className:"flex items-center",children:[r.jsx(K,{className:"mr-2 h-4 w-4 text-green-500"}),r.jsx("span",{children:"Saved"})]})})}),se=({show:e,title:t,message:s,confirmText:i="Confirm",cancelText:u="Cancel",confirmColor:f="failure",isLoading:o=!1,onClose:c,onConfirm:a,children:x})=>r.jsxs(F,{show:e,onClose:c,size:"md",children:[r.jsx(F.Header,{children:t}),r.jsx(F.Body,{children:r.jsxs("div",{className:"text-center",children:[r.jsx(P,{className:"mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200"}),r.jsx("h3",{className:"mb-5 text-lg font-normal text-gray-500 dark:text-gray-400",children:s}),x&&r.jsx("div",{className:"mb-5",children:x}),r.jsxs("div",{className:"flex justify-center gap-4",children:[r.jsxs(U,{color:f,onClick:a,disabled:o,children:[o?r.jsx($,{size:"sm",className:"mr-2"}):null,i]}),r.jsx(U,{color:"gray",onClick:c,children:u})]})]})})]}),re=({shortcut:e,className:t=""})=>r.jsx("span",{className:`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 ${t}`,"aria-label":`Keyboard shortcut: ${C(e)}`,children:C(e)});export{te as A,se as C,ee as F,re as K,W as Q,Z as V,Y as a,G as b,X as u};
