import{s as c}from"./index-C6AV3cVN.js";const n=async(a,r)=>{try{let e=c.from("employment_types").select("*",{count:"exact"}).eq("organization_id",a);(r==null?void 0:r.isActive)!==void 0&&(e=e.eq("is_active",r.isActive)),r!=null&&r.searchQuery&&(e=e.ilike("name",`%${r.searchQuery}%`)),r!=null&&r.sortBy?e=e.order(r.sortBy,{ascending:r.sortOrder==="asc"}):e=e.order("name",{ascending:!0}),r!=null&&r.limit&&(e=e.limit(r.limit)),r!=null&&r.offset&&(e=e.range(r.offset,r.offset+(r.limit||10)-1));const{data:t,error:m,count:y}=await e;return m?(console.error("Error fetching employment types:",m),{employmentTypes:[],count:0,error:m.message}):{employmentTypes:t,count:y||0}}catch(e){return console.error("Error in getEmploymentTypes:",e),{employmentTypes:[],count:0,error:e.message}}},l=async(a,r)=>{try{const{data:e,error:t}=await c.from("employment_types").insert({...r,organization_id:a}).select().single();return t?(console.error("Error creating employment type:",t),{error:t.message}):{employmentType:e}}catch(e){return console.error("Error in createEmploymentType:",e),{error:e.message}}},o=async(a,r,e)=>{try{const{data:t,error:m}=await c.from("employment_types").update(e).eq("organization_id",a).eq("id",r).select().single();return m?(console.error("Error updating employment type:",m),{error:m.message}):{employmentType:t}}catch(t){return console.error("Error in updateEmploymentType:",t),{error:t.message}}},u=async(a,r)=>{try{const{error:e}=await c.from("employment_types").delete().eq("organization_id",a).eq("id",r);return e?(console.error("Error deleting employment type:",e),{success:!1,error:e.message}):{success:!0}}catch(e){return console.error("Error in deleteEmploymentType:",e),{success:!1,error:e.message}}};export{l as c,u as d,n as g,o as u};
