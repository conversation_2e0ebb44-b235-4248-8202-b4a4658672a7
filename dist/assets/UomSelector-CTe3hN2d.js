import{h as M,bl as B,bm as J,r as m,j as o,i as R,a7 as E,s as x}from"./index-C6AV3cVN.js";import{getProductUoms as k}from"./productUom-k6aUg6b7.js";const L=({productId:u,value:g,onChange:O,onUomChange:v,filter:y="all",disabled:F=!1,className:b="",preloadedUoms:_=[]})=>{const{currentOrganization:r}=M(),{uoms:S}=B(),$=J(),[h,D]=m.useState(_),[q,U]=m.useState(_.length===0),[T,p]=m.useState(null),[s,l]=m.useState(null),[P,d]=m.useState(!1);m.useEffect(()=>{if(!u||!r)return;const t=i=>{if(!i||i.length===0)return[];let e=i;if(y==="purchasing"){const n=i.filter(c=>c.is_purchasing_unit);e=n.length>0?n:i}else if(y==="selling"){const n=i.filter(c=>c.is_selling_unit);e=n.length>0?n:i}return e},f=i=>!i||i.length===0?null:i.find(e=>e.is_default)||i[0];if(_&&_.length>0){const i=t(_),e=f(i);D(i),e&&l(e),U(!1);return}(async()=>{const i=$.getFromCache(u);if(i&&i.length>0){const e=t(i),n=f(e);D(e),n&&l(n),U(!1);return}U(!0),p(null),d(!1);try{const{productUoms:e,error:n}=await k(u,r.id);if(n)p(n),d(!0),w();else if(!e||e.length===0)p("No units of measurement found for this product"),d(!0),w();else{$.addToCache(u,e);const c=t(e),N=f(c);D(c),N?l(N):c.length>0?l(c[0]):w()}}catch(e){p(e.message||"Failed to load units of measurement"),d(!0),w()}finally{U(!1)}})()},[u,r==null?void 0:r.id,y,JSON.stringify(_==null?void 0:_.map(t=>t.id))]);const w=async()=>{if(!(!r||!u))try{const{data:t,error:f}=await x.from("product_uoms").select(`
          *,
          uom:uom_id (*)
        `).eq("product_id",u).eq("organization_id",r.id);if(!f&&t&&t.length>0){const e=t[0],n=t;(()=>{l(e),d(!1),D(n),p(null)})();return}if(S&&S.length>0){const e=S.find(z=>z.code==="pcs");if(e){const z={id:`temp-${Date.now()}`,uom_id:e.id,uom:e,product_id:u,organization_id:r.id,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0,created_by:null,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};(()=>{l(z),d(!0)})();try{await x.from("product_uoms").insert({product_id:u,uom_id:e.id,organization_id:r.id,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0})}catch{}return}const n=S[0],c={id:`temp-${Date.now()}`,uom_id:n.id,uom:n,product_id:u,organization_id:r.id,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0,created_by:null,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};(()=>{l(c),d(!0)})();return}const{data:a,error:i}=await x.from("units_of_measurement").select("*").eq("organization_id",r.id).eq("code","pcs").single();if(i){const{data:e,error:n}=await x.from("units_of_measurement").select("*").eq("organization_id",r.id).limit(1);if(!n&&e&&e.length>0){const c={id:`temp-${Date.now()}`,uom_id:e[0].id,uom:e[0],product_id:u,organization_id:r.id,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0,created_by:null,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};l(c),d(!0)}}else if(a){const e={id:`temp-${Date.now()}`,uom_id:a.id,uom:a,product_id:u,organization_id:r.id,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0,created_by:null,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};l(e),d(!0);try{await x.from("product_uoms").insert({product_id:u,uom_id:a.id,organization_id:r.id,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0})}catch{}}}catch{}};m.useEffect(()=>{if(s&&!g&&s.uom_id){const t={...s};(t.conversion_factor===void 0||t.conversion_factor===null)&&(t.conversion_factor=1);const a=setTimeout(()=>{v?v(t):O(t.uom_id)},0);return()=>clearTimeout(a)}},[s==null?void 0:s.uom_id,g]);const C=t=>{const f=t.target.value;if(v){const a=h.find(i=>i.uom_id===f);a&&(a.conversion_factor===void 0||a.conversion_factor===null?a.conversion_factor=1:a.conversion_factor=Number(a.conversion_factor),v(a))}else O(f)};if(q)return o.jsxs("div",{className:"flex items-center h-9",children:[o.jsx(R,{size:"sm",className:"mr-1"}),o.jsx("span",{className:"text-xs text-gray-500",children:"Loading..."})]});if(T&&P)return o.jsx("div",{className:"flex flex-col",children:o.jsx(E,{value:(s==null?void 0:s.uom_id)||"",disabled:!s,onChange:C,className:`${b} text-sm`,children:s?o.jsxs("option",{value:s.uom_id,children:[s.uom.name," (",s.uom.code,")"]}):o.jsx("option",{value:"",children:"No units available"})})});if(T)return o.jsx("div",{className:"flex flex-col",children:o.jsx(E,{value:"",disabled:!0,className:`${b} text-sm`,children:o.jsx("option",{value:"",children:"No units available"})})});const j=g?h.find(t=>t.uom_id===g):s;return o.jsxs("div",{className:"flex flex-col",children:[o.jsx(E,{value:g||(s==null?void 0:s.uom_id)||"",onChange:C,disabled:q||h.length===0&&F,className:`${b} text-sm`,children:h.length===0?o.jsx("option",{value:"",children:"Select a unit"}):o.jsxs(o.Fragment,{children:[o.jsx("option",{value:"",children:"Select a unit"}),h.map(t=>o.jsxs("option",{value:t.uom_id,children:[t.uom.name," (",t.uom.code,") - CF: ",Number(t.conversion_factor)||1]},t.uom_id))]})}),j&&o.jsx("input",{type:"hidden",value:Number(j.conversion_factor)||1,"data-uom-id":j.uom_id,"data-conversion-factor":Number(j.conversion_factor)||1})]})};export{L as U};
