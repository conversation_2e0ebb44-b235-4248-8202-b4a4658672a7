import{d as Y,h as C,r as j,j as e}from"./index-C6AV3cVN.js";import{c as F}from"./payrollReporting-DzocG2hh.js";import{f as c}from"./formatters-Cypx7G-j.js";import{f as H}from"./index-qirzObrW.js";import{r as y,t as v}from"./index-Cn2wB4rc.js";import{t as P}from"./index-4YOzgfrD.js";import"./payroll-j3fcCwK0.js";import"./index-DT2YvziZ.js";import"./typeof-QjJsDpFa.js";function O(o,r){y(2,arguments);var a=v(o),u=P(r);if(isNaN(u))return new Date(NaN);if(!u)return a;var m=a.getDate(),i=new Date(a.getTime());i.setMonth(a.getMonth()+u+1,0);var h=i.getDate();return m>=h?i:(a.setFullYear(i.getFullYear(),i.getMonth(),m),a)}function V(o){y(1,arguments);var r=v(o),a=r.getFullYear();return r.setFullYear(a+1,0,0),r.setHours(23,59,59,999),r}function R(o){y(1,arguments);var r=v(o),a=new Date(0);return a.setFullYear(r.getFullYear(),0,1),a.setHours(0,0,0,0),a}function k(o,r){y(2,arguments);var a=P(r);return O(o,-a)}const J=()=>{const o=Y(),{currentOrganization:r}=C(),[a,u]=j.useState("year"),[m,i]=j.useState(!1),[h,b]=j.useState(null),[d,L]=j.useState([]);j.useEffect(()=>{(async()=>{if(r){i(!0),b(null);try{let t,l;const x=new Date;switch(a){case"quarter":t=k(x,3),l=x;break;case"half_year":t=k(x,6),l=x;break;case"year":default:t=R(x),l=V(x);break}const{data:M,error:D}=await F({organizationId:r.id,startDate:t,endDate:l});if(D)throw new Error(D);if(M){const E=[...M].sort((T,A)=>new Date(T.startDate).getTime()-new Date(A.startDate).getTime());L(E)}}catch(t){b(t.message)}finally{i(!1)}}})()},[r,a]);const n=d.reduce((s,t)=>s+t.totalGrossPay,0),p=d.reduce((s,t)=>s+t.totalNetPay,0),f=d.reduce((s,t)=>s+t.totalDeductions,0),w=d.reduce((s,t)=>s+t.totalAllowances,0),N=d.length>0?Math.round(d.reduce((s,t)=>s+t.totalEmployees,0)/d.length):0;N>0&&d.length>0&&p/(N*d.length);const z=d.reduce((s,t)=>{const l=H(new Date(t.startDate),"MMM yyyy");return s[l]||(s[l]={month:l,grossPay:0,netPay:0,deductions:0,allowances:0,employees:0,count:0}),s[l].grossPay+=t.totalGrossPay,s[l].netPay+=t.totalNetPay,s[l].deductions+=t.totalDeductions,s[l].allowances+=t.totalAllowances,s[l].employees+=t.totalEmployees,s[l].count+=1,s},{}),g=Object.values(z).map(s=>({...s,employees:Math.round(s.employees/s.count)}));return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-4 flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Payroll Analytics"}),e.jsx("p",{className:"text-gray-500",children:"Visualize and analyze payroll data"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Time Range:"}),e.jsxs("select",{className:"rounded-lg border border-gray-300 p-2",value:a,onChange:s=>u(s.target.value),children:[e.jsx("option",{value:"quarter",children:"Last Quarter"}),e.jsx("option",{value:"half_year",children:"Last 6 Months"}),e.jsx("option",{value:"year",children:"This Year"})]}),e.jsx("button",{className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",onClick:()=>o("/payroll/reports"),children:"View Reports"})]})]}),m&&e.jsx("div",{className:"flex justify-center items-center p-12",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}),h&&e.jsx("div",{className:"p-4 mb-6 bg-red-50 border-l-4 border-red-400",children:e.jsx("p",{className:"text-red-700",children:h})}),!m&&!h&&d.length===0&&e.jsx("div",{className:"p-4 mb-6 bg-yellow-50 border-l-4 border-yellow-400",children:e.jsx("p",{className:"text-yellow-700",children:"No payroll data available for the selected time range."})}),!m&&!h&&d.length>0&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-green-100 text-green-600",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h5",{className:"text-xl font-bold leading-none text-gray-900",children:c(p)}),e.jsx("p",{className:"text-sm font-normal text-gray-500",children:"Total Net Pay"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-blue-100 text-blue-600",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h5",{className:"text-xl font-bold leading-none text-gray-900",children:c(n)}),e.jsx("p",{className:"text-sm font-normal text-gray-500",children:"Total Gross Pay"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-red-100 text-red-600",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"})]})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h5",{className:"text-xl font-bold leading-none text-gray-900",children:c(f)}),e.jsx("p",{className:"text-sm font-normal text-gray-500",children:"Total Deductions"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"inline-flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-purple-100 text-purple-600",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h5",{className:"text-xl font-bold leading-none text-gray-900",children:N}),e.jsx("p",{className:"text-sm font-normal text-gray-500",children:"Average Employees"})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Payroll Distribution"}),e.jsx("div",{className:"h-64",children:e.jsx("div",{className:"relative h-full",children:e.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:e.jsxs("div",{className:"w-full max-w-xs",children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-sm font-medium text-green-700",children:"Net Pay"}),e.jsxs("span",{className:"text-sm font-medium text-green-700",children:[n>0?Math.round(p/n*100):0,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:e.jsx("div",{className:"bg-green-600 h-2.5 rounded-full",style:{width:`${n>0?Math.round(p/n*100):0}%`}})})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-sm font-medium text-red-700",children:"Deductions"}),e.jsxs("span",{className:"text-sm font-medium text-red-700",children:[n>0?Math.round(f/n*100):0,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:e.jsx("div",{className:"bg-red-600 h-2.5 rounded-full",style:{width:`${n>0?Math.round(f/n*100):0}%`}})})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-sm font-medium text-blue-700",children:"Allowances"}),e.jsxs("span",{className:"text-sm font-medium text-blue-700",children:[n>0?Math.round(w/n*100):0,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:`${n>0?Math.round(w/n*100):0}%`}})})]})]})})})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Employee Count Trend"}),e.jsx("div",{className:"h-64 flex items-center justify-center",children:g.length>0?e.jsx("div",{className:"w-full h-full flex items-end justify-between",children:g.map((s,t)=>e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"bg-indigo-600 w-12 rounded-t-lg",style:{height:`${Math.max(20,s.employees/Math.max(...g.map(l=>l.employees))*200)}px`}}),e.jsx("div",{className:"text-xs mt-2 text-gray-600",children:s.month}),e.jsx("div",{className:"text-xs font-semibold",children:s.employees})]},t))}):e.jsx("p",{className:"text-gray-500",children:"No trend data available"})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Payroll Trend"}),e.jsx("div",{className:"h-64",children:g.length>0?e.jsx("div",{className:"w-full h-full",children:e.jsxs("table",{className:"w-full text-sm text-left text-gray-500",children:[e.jsx("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Month"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Employees"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Gross Pay"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Deductions"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Net Pay"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Avg. Per Employee"})]})}),e.jsx("tbody",{children:g.map((s,t)=>e.jsxs("tr",{className:"bg-white border-b",children:[e.jsx("td",{className:"px-6 py-4 font-medium text-gray-900",children:s.month}),e.jsx("td",{className:"px-6 py-4",children:s.employees}),e.jsx("td",{className:"px-6 py-4",children:c(s.grossPay)}),e.jsx("td",{className:"px-6 py-4",children:c(s.deductions)}),e.jsx("td",{className:"px-6 py-4 font-medium text-green-600",children:c(s.netPay)}),e.jsx("td",{className:"px-6 py-4",children:c(s.employees>0?s.netPay/s.employees:0)})]},t))})]})}):e.jsx("p",{className:"text-gray-500",children:"No trend data available"})})]})]})]})};export{J as default};
