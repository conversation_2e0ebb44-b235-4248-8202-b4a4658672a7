import{h as de,b as me,r as t,j as e,B as c,a0 as J,A as T,J as E,P as O,Q as he,a7 as ue,i as A,_ as l,e as V,D as F,a2 as xe,a3 as pe,a4 as ye,M as a,a6 as m,ad as G}from"./index-C6AV3cVN.js";import{C as je}from"./Card-yj7fueH8.js";import{g as fe,c as ge,u as ve,d as Ne}from"./employmentType-B4DW1Mne.js";import{P as be}from"./Pagination-CVEzfctr.js";const Ae=()=>{const{currentOrganization:r}=de(),{user:Ce}=me(),[B,K]=t.useState([]),[W,M]=t.useState(!0),[_,k]=t.useState(null),[q,X]=t.useState(0),[D,P]=t.useState(1),[H,Z]=t.useState(""),[I,$]=t.useState(!0),[ee,x]=t.useState(!1),[se,p]=t.useState(!1),[te,y]=t.useState(!1),[o,Q]=t.useState(null),[j,f]=t.useState(!1),[g,i]=t.useState(null),[z,L]=t.useState(!1),[R,v]=t.useState(null),[d,N]=t.useState(""),[b,C]=t.useState(""),[w,S]=t.useState(!0),[h,le]=t.useState(10);t.useEffect(()=>{r&&u()},[r,D,H,I,h]);const u=async()=>{if(r){M(!0),k(null);try{const{employmentTypes:s,count:n,error:Y}=await fe(r.id,{searchQuery:H,isActive:I?!0:void 0,limit:h,offset:(D-1)*h});Y?k(Y):(K(s),X(n))}catch(s){k(s.message||"An error occurred while fetching employment types")}finally{M(!1)}}},ae=s=>{s.preventDefault(),P(1),u()},U=()=>{N(""),C(""),S(!0),i(null),x(!0)},re=s=>{Q(s),N(s.name),C(s.description||""),S(s.is_active!==!1),i(null),p(!0)},ie=s=>{Q(s),v(null),y(!0)},ne=async()=>{if(r){if(!d.trim()){i("Employment type name is required");return}f(!0),i(null);try{const{employmentType:s,error:n}=await ge(r.id,{name:d.trim(),description:b.trim()||null,is_active:w});n?i(n):(x(!1),u())}catch(s){i(s.message||"An error occurred while creating the employment type")}finally{f(!1)}}},ce=async()=>{if(!(!r||!o)){if(!d.trim()){i("Employment type name is required");return}f(!0),i(null);try{const{employmentType:s,error:n}=await ve(r.id,o.id,{name:d.trim(),description:b.trim()||null,is_active:w});n?i(n):(p(!1),u())}catch(s){i(s.message||"An error occurred while updating the employment type")}finally{f(!1)}}},oe=async()=>{if(!(!r||!o)){L(!0),v(null);try{const{success:s,error:n}=await Ne(r.id,o.id);n?v(n):s&&(y(!1),u())}catch(s){v(s.message||"An error occurred while deleting the employment type")}finally{L(!1)}}};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(je,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Employment Types"}),e.jsxs(c,{color:"primary",onClick:U,children:[e.jsx(J,{className:"mr-2 h-5 w-5"}),"Add Employment Type"]})]}),_&&e.jsxs(T,{color:"failure",className:"mb-4",children:[e.jsx(E,{className:"h-4 w-4 mr-2"}),_]}),e.jsxs("div",{className:"mb-4 flex flex-col md:flex-row gap-4",children:[e.jsx("form",{onSubmit:ae,className:"flex-1",children:e.jsx(O,{type:"text",placeholder:"Search employment types...",value:H,onChange:s=>Z(s.target.value),icon:he,rightIcon:()=>e.jsx(c,{type:"submit",size:"xs",color:"light",children:"Search"})})}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("label",{className:"mr-2",children:"Show:"}),e.jsxs(ue,{value:I?"active":"all",onChange:s=>$(s.target.value==="active"),className:"w-32",children:[e.jsx("option",{value:"active",children:"Active Only"}),e.jsx("option",{value:"all",children:"All"})]})]})]}),W?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(A,{size:"xl"})}):B.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No employment types found"}),e.jsxs(c,{color:"primary",size:"sm",onClick:U,children:[e.jsx(J,{className:"mr-2 h-4 w-4"}),"Add Your First Employment Type"]})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(l,{hoverable:!0,children:[e.jsxs(l.Head,{children:[e.jsx(l.HeadCell,{children:"Name"}),e.jsx(l.HeadCell,{children:"Description"}),e.jsx(l.HeadCell,{children:"Status"}),e.jsx(l.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(l.Body,{children:B.map(s=>e.jsxs(l.Row,{children:[e.jsx(l.Cell,{className:"font-medium",children:s.name}),e.jsx(l.Cell,{children:s.description||e.jsx("span",{className:"text-gray-400",children:"No description"})}),e.jsx(l.Cell,{children:s.is_active!==!1?e.jsx(V,{color:"success",children:"Active"}):e.jsx(V,{color:"gray",children:"Inactive"})}),e.jsx(l.Cell,{children:e.jsx("div",{className:"flex justify-end",children:e.jsxs(F,{label:"",dismissOnClick:!0,renderTrigger:()=>e.jsx(c,{color:"light",size:"xs",children:e.jsx(ye,{className:"h-4 w-4"})}),children:[e.jsx(F.Item,{icon:xe,onClick:()=>re(s),children:"Edit"}),e.jsx(F.Item,{icon:pe,onClick:()=>ie(s),children:"Delete"})]})})})]},s.id))})]})}),e.jsx(be,{currentPage:D,totalPages:Math.ceil(q/h),itemsPerPage:h,totalItems:q,onPageChange:P,onItemsPerPageChange:s=>{le(s),P(1)},itemName:"employment types"})]}),e.jsxs(a,{show:ee,onClose:()=>x(!1),size:"md",children:[e.jsx(a.Header,{children:"Add New Employment Type"}),e.jsxs(a.Body,{children:[g&&e.jsxs(T,{color:"failure",className:"mb-4",children:[e.jsx(E,{className:"h-4 w-4 mr-2"}),g]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(m,{htmlFor:"typeName",value:"Employment Type Name *"})}),e.jsx(O,{id:"typeName",value:d,onChange:s=>N(s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(m,{htmlFor:"typeDescription",value:"Description"})}),e.jsx(G,{id:"typeDescription",value:b,onChange:s=>C(s.target.value),rows:3})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{id:"isActive",type:"checkbox",checked:w,onChange:s=>S(s.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),e.jsx(m,{htmlFor:"isActive",value:"Active Employment Type"})]})]})]}),e.jsxs(a.Footer,{children:[e.jsx(c,{color:"primary",onClick:ne,disabled:j,children:j?e.jsxs(e.Fragment,{children:[e.jsx(A,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Employment Type"}),e.jsx(c,{color:"gray",onClick:()=>x(!1),children:"Cancel"})]})]}),e.jsxs(a,{show:se,onClose:()=>p(!1),size:"md",children:[e.jsx(a.Header,{children:"Edit Employment Type"}),e.jsxs(a.Body,{children:[g&&e.jsxs(T,{color:"failure",className:"mb-4",children:[e.jsx(E,{className:"h-4 w-4 mr-2"}),g]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(m,{htmlFor:"editTypeName",value:"Employment Type Name *"})}),e.jsx(O,{id:"editTypeName",value:d,onChange:s=>N(s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(m,{htmlFor:"editTypeDescription",value:"Description"})}),e.jsx(G,{id:"editTypeDescription",value:b,onChange:s=>C(s.target.value),rows:3})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{id:"editIsActive",type:"checkbox",checked:w,onChange:s=>S(s.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),e.jsx(m,{htmlFor:"editIsActive",value:"Active Employment Type"})]})]})]}),e.jsxs(a.Footer,{children:[e.jsx(c,{color:"primary",onClick:ce,disabled:j,children:j?e.jsxs(e.Fragment,{children:[e.jsx(A,{size:"sm",className:"mr-2"}),"Saving..."]}):"Update Employment Type"}),e.jsx(c,{color:"gray",onClick:()=>p(!1),children:"Cancel"})]})]}),e.jsxs(a,{show:te,onClose:()=>y(!1),size:"md",popup:!0,children:[e.jsx(a.Header,{}),e.jsx(a.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(E,{className:"mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500 dark:text-gray-400",children:["Are you sure you want to delete"," ",e.jsx("span",{className:"font-semibold",children:o==null?void 0:o.name}),"?"]}),R&&e.jsx(T,{color:"failure",className:"mb-4",children:R}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(c,{color:"failure",onClick:oe,disabled:z,children:z?e.jsx(A,{size:"sm"}):"Yes, delete"}),e.jsx(c,{color:"gray",onClick:()=>y(!1),disabled:z,children:"No, cancel"})]})]})})]})]})};export{Ae as default};
