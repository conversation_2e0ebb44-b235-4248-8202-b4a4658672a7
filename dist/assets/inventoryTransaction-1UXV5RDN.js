import{s as y}from"./index-C6AV3cVN.js";import{h as E}from"./floatInventory-k_pEQeIK.js";const w=async(n,i,e,r,d)=>{try{console.log(`🔄 Auto-resolving floating inventory for product ${i}, received: ${e}`);const{data:a,error:c}=await y.from("float_inventory").select("*").eq("organization_id",n).eq("product_id",i).eq("resolved",!1).order("created_at",{ascending:!0});if(c)throw new Error(`Error fetching floating inventory: ${c.message}`);if(!a||a.length===0)return console.log(`✅ No floating inventory to resolve for product ${i}`),{resolvedQuantity:0,remainingFloatQuantity:0,resolutionTransactions:[]};const t=a.reduce((u,p)=>u+Math.abs(p.quantity),0);console.log(`📊 Total floating quantity: ${t}, Received: ${e}`);const s=Math.min(t,e),l=t-s;if(console.log(`🎯 Will resolve: ${s}, Remaining float: ${l}`),s===0)return{resolvedQuantity:0,remainingFloatQuantity:t,resolutionTransactions:[]};const o=await E(n),f=[];let _=s;for(const u of a){if(_<=0)break;const p=Math.abs(u.quantity),m=Math.min(p,_);console.log(`🔧 Resolving float item ${u.id}: ${m} of ${p}`);const{data:v,error:$}=await y.from("inventory_transactions").insert({organization_id:n,product_id:i,transaction_type:"float_resolution",quantity:m,uom_id:o,reference_id:u.id,reference_type:"float_inventory",notes:`🤖 AUTO-RESOLVED: Floating inventory resolved by receipt ${d}. Original sale: ${u.sale_id}`,created_by:r}).select("id").single();if($){console.error(`❌ Error creating resolution transaction for float ${u.id}:`,$);continue}v&&f.push(v.id);const g=m>=p,q={resolved:g,resolved_at:new Date().toISOString(),resolution_type:"automatic",resolution_notes:`Auto-resolved by receipt ${d}`,resolved_by:r};g||(q.quantity=-(p-m));const{error:h}=await y.from("float_inventory").update(q).eq("id",u.id);h?console.error(`❌ Error updating float item ${u.id}:`,h):console.log(`✅ ${g?"Fully":"Partially"} resolved float item ${u.id}`),_-=m}return console.log(`🎉 Auto-resolution complete: Resolved ${s}, Created ${f.length} transactions`),{resolvedQuantity:s,remainingFloatQuantity:l,resolutionTransactions:f}}catch(a){return console.error("❌ Error in autoResolveFloatingInventory:",a),{resolvedQuantity:0,remainingFloatQuantity:0,resolutionTransactions:[],error:a.message}}},Q=async(n,i,e)=>{try{console.log("Creating inventory transaction:",e);let r,d=e.notes||"";(e.transactionType==="receipt"||e.transactionType==="purchase")&&e.quantity>0&&(console.log(`🔄 Auto-resolving floating inventory for ${e.transactionType} transaction`),r=await w(n,e.productId,e.quantity,i,e.referenceId||"manual transaction"),r.error?console.warn("⚠️ Auto-resolution warning:",r.error):r.resolvedQuantity>0&&(console.log(`✅ Auto-resolved ${r.resolvedQuantity} floating inventory`),d=d?`${d} | 🤖 AUTO-RESOLVED ${r.resolvedQuantity} floating inventory`:`🤖 AUTO-RESOLVED ${r.resolvedQuantity} floating inventory`));let a,c;try{const t=await y.from("inventory_transactions").insert({organization_id:n,product_id:e.productId,transaction_type:e.transactionType,quantity:e.quantity,uom_id:e.uomId,reference_id:e.referenceId||null,reference_type:e.referenceType||null,notes:d,created_by:i}).select().single();a=t.data,c=t.error}catch(t){if(console.error("Error with primary transaction type, trying fallback:",t),e.transactionType==="receipt"){const s=await y.from("inventory_transactions").insert({organization_id:n,product_id:e.productId,transaction_type:"purchase",quantity:e.quantity,uom_id:e.uomId,reference_id:e.referenceId||null,reference_type:e.referenceType||null,notes:(e.notes||"")+" (receipt transaction)",created_by:i}).select().single();a=s.data,c=s.error}else throw t}return c?(console.error("Error creating inventory transaction:",c),{error:c.message}):{transaction:a,autoResolution:r}}catch(r){return console.error("Error in createInventoryTransaction:",r),{error:r.message}}},R=async(n,i)=>{try{const{data:e,error:r}=await y.from("inventory_transactions").select(`
        *,
        product:product_id(id, name, sku),
        uom:uom_id(id, name, code)
      `).eq("organization_id",n).eq("product_id",i).order("created_at",{ascending:!1});return r?{transactions:[],error:r.message}:{transactions:e||[]}}catch(e){return{transactions:[],error:e.message}}},k=async(n,i)=>{try{const{data:e,error:r}=await y.from("inventory_transactions").select(`
        *,
        product:product_id(id, name, sku, cost_price),
        uom:uom_id(id, name, code)
      `).eq("organization_id",n).eq("id",i).single();return r?(console.error("Error fetching inventory transaction by ID:",r),{transaction:null,error:r.message}):{transaction:e,error:null}}catch(e){return console.error("Exception fetching inventory transaction by ID:",e),{transaction:null,error:e.message}}},A=async(n,i)=>{try{const{limit:e=50,offset:r=0,sortBy:d="created_at",sortOrder:a="desc",transactionType:c,startDate:t,endDate:s,searchQuery:l}=i||{};let o=y.from("inventory_transactions").select(`
        *,
        product:product_id(id, name, sku),
        uom:uom_id(id, name, code)
      `,{count:"exact"}).eq("organization_id",n);c&&(o=o.eq("transaction_type",c)),t&&(o=o.gte("created_at",t)),s&&(o=o.lte("created_at",s)),l&&(o=o.or(`product.name.ilike.%${l}%,product.sku.ilike.%${l}%`)),o=o.order(d,{ascending:a==="asc"}),o=o.range(r,r+e-1);const{data:f,error:_,count:u}=await o;return _?{transactions:[],count:0,error:_.message}:{transactions:f||[],count:u||0}}catch(e){return{transactions:[],count:0,error:e.message}}},O=async(n,i,e)=>{try{const{data:r,error:d}=await y.from("inventory_receipts").select("*").eq("id",i).eq("organization_id",n).single();if(d)return{success:!1,error:d.message};const{data:a,error:c}=await y.from("inventory_receipt_items").select("*").eq("inventory_receipt_id",i);if(c)return{success:!1,error:c.message};if(!a||a.length===0)return{success:!1,error:"No receipt items found"};for(const t of a){const{data:s}=await y.from("inventory_transactions").select("id").eq("reference_id",t.id).eq("reference_type","inventory_receipt_item");if(s&&s.length>0){console.log(`Transaction already exists for receipt item ${t.id}`);continue}console.log(`Creating transaction for item ${t.id}: Product ${t.product_id}, Quantity: ${t.base_quantity}`);let l;try{console.log(`🔄 Checking for floating inventory to resolve for product ${t.product_id}`);const o=await w(n,t.product_id,t.base_quantity,e,r.receipt_number||"unknown receipt");o.error?console.warn(`⚠️ Auto-resolution warning for product ${t.product_id}:`,o.error):o.resolvedQuantity>0&&console.log(`✅ Auto-resolved ${o.resolvedQuantity} floating inventory for product ${t.product_id}`);const f=o.resolvedQuantity>0?`Inventory receipt from ${r.receipt_number||"unknown receipt"} | 🤖 AUTO-RESOLVED ${o.resolvedQuantity} floating inventory`:`Inventory receipt from ${r.receipt_number||"unknown receipt"}`;if(l=(await y.from("inventory_transactions").insert({organization_id:n,product_id:t.product_id,transaction_type:"receipt",quantity:t.base_quantity,uom_id:t.uom_id,reference_id:t.id,reference_type:"inventory_receipt_item",notes:f,created_by:e})).error,l){console.error("Error with receipt transaction type, trying fallback:",l);const u=o.resolvedQuantity>0?`Inventory receipt from ${r.receipt_number||"unknown receipt"} (receipt transaction) | 🤖 AUTO-RESOLVED ${o.resolvedQuantity} floating inventory`:`Inventory receipt from ${r.receipt_number||"unknown receipt"} (receipt transaction)`;l=(await y.from("inventory_transactions").insert({organization_id:n,product_id:t.product_id,transaction_type:"purchase",quantity:t.base_quantity,uom_id:t.uom_id,reference_id:t.id,reference_type:"inventory_receipt_item",notes:u,created_by:e})).error}}catch(o){console.error("Exception creating transaction:",o),l={message:o.message}}l&&console.error(`Error creating transaction for item ${t.id}:`,l)}return{success:!0}}catch(r){return console.error("Error in createTransactionsFromReceiptItems:",r),{success:!1,error:r.message}}};export{k as a,A as b,Q as c,O as d,R as g};
