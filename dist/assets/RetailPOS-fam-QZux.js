const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/productUom-k6aUg6b7.js","assets/index-C6AV3cVN.js","assets/index-ClH3u6fg.css","assets/product-Ca8DWaNR.js"])))=>i.map(i=>d[i]);
import{c as Pt,g as zr,j as a,aT as fn,r as T,aU as Ze,e as mt,aV as Ii,aW as _i,i as nt,A as It,Y as Ti,a6 as de,B as J,P as Te,h as mn,M as O,K as Rr,aX as Ai,aY as Di,a8 as Li,J as Rn,a3 as nn,I as Fi,Q as zi,a7 as Ri,t as Mi,_ as ce,a1 as Oi,a as Bi,b as Hi,q as Mn,C as On,aS as Bn,a0 as Hn,aH as ut,af as Ui,ai as Vi,aR as qi,aZ as $i,s as Un,G as Ki}from"./index-C6AV3cVN.js";import{g as <PERSON>,c as Wi,a as Yi}from"./refund-CcMk-dC8.js";import{u as At}from"./currencyFormatter-BsFWv3sX.js";import{d as Mr,a as Xi}from"./formatters-Cypx7G-j.js";import{b as Gi,C as Ji,a as Zi,D as el}from"./ClearCartConfirmation-63Y8FzVT.js";import{C as tl}from"./Card-yj7fueH8.js";import{S as nl}from"./SaleReceipt-jLjyCjTQ.js";import{f as rl}from"./floatInventory-k_pEQeIK.js";import{c as il}from"./customer-COogBrXM.js";import{P as ll}from"./Pagination-CVEzfctr.js";import{R as sl}from"./RefundProcessor-BGtDGV8c.js";import{E as Vn,p as qn}from"./EnhancedProductSearchSelector-CWWD8-c1.js";import{E as al}from"./EnhancedNumberInput-Bwo1E3yF.js";import"./inventoryTransaction-1UXV5RDN.js";import"./product-Ca8DWaNR.js";import"./typeof-QjJsDpFa.js";function ol(e,t){const n={};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}const ul=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,cl=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,dl={};function $n(e,t){return(dl.jsx?cl:ul).test(e)}const hl=/[ \t\n\f\r]/g;function pl(e){return typeof e=="object"?e.type==="text"?Kn(e.value):!1:Kn(e)}function Kn(e){return e.replace(hl,"")===""}class xt{constructor(t,n,r){this.normal=n,this.property=t,r&&(this.space=r)}}xt.prototype.normal={};xt.prototype.property={};xt.prototype.space=void 0;function Or(e,t){const n={},r={};for(const i of e)Object.assign(n,i.property),Object.assign(r,i.normal);return new xt(n,r,t)}function rn(e){return e.toLowerCase()}class ke{constructor(t,n){this.attribute=n,this.property=t}}ke.prototype.attribute="";ke.prototype.booleanish=!1;ke.prototype.boolean=!1;ke.prototype.commaOrSpaceSeparated=!1;ke.prototype.commaSeparated=!1;ke.prototype.defined=!1;ke.prototype.mustUseProperty=!1;ke.prototype.number=!1;ke.prototype.overloadedBoolean=!1;ke.prototype.property="";ke.prototype.spaceSeparated=!1;ke.prototype.space=void 0;let fl=0;const R=Ye(),le=Ye(),Br=Ye(),j=Ye(),Z=Ye(),et=Ye(),Se=Ye();function Ye(){return 2**++fl}const ln=Object.freeze(Object.defineProperty({__proto__:null,boolean:R,booleanish:le,commaOrSpaceSeparated:Se,commaSeparated:et,number:j,overloadedBoolean:Br,spaceSeparated:Z},Symbol.toStringTag,{value:"Module"})),$t=Object.keys(ln);class gn extends ke{constructor(t,n,r,i){let l=-1;if(super(t,n),Qn(this,"space",i),typeof r=="number")for(;++l<$t.length;){const s=$t[l];Qn(this,$t[l],(r&ln[s])===ln[s])}}}gn.prototype.defined=!0;function Qn(e,t,n){n&&(e[t]=n)}function rt(e){const t={},n={};for(const[r,i]of Object.entries(e.properties)){const l=new gn(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(l.mustUseProperty=!0),t[r]=l,n[rn(r)]=r,n[rn(l.attribute)]=r}return new xt(t,n,e.space)}const Hr=rt({properties:{ariaActiveDescendant:null,ariaAtomic:le,ariaAutoComplete:null,ariaBusy:le,ariaChecked:le,ariaColCount:j,ariaColIndex:j,ariaColSpan:j,ariaControls:Z,ariaCurrent:null,ariaDescribedBy:Z,ariaDetails:null,ariaDisabled:le,ariaDropEffect:Z,ariaErrorMessage:null,ariaExpanded:le,ariaFlowTo:Z,ariaGrabbed:le,ariaHasPopup:null,ariaHidden:le,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:Z,ariaLevel:j,ariaLive:null,ariaModal:le,ariaMultiLine:le,ariaMultiSelectable:le,ariaOrientation:null,ariaOwns:Z,ariaPlaceholder:null,ariaPosInSet:j,ariaPressed:le,ariaReadOnly:le,ariaRelevant:null,ariaRequired:le,ariaRoleDescription:Z,ariaRowCount:j,ariaRowIndex:j,ariaRowSpan:j,ariaSelected:le,ariaSetSize:j,ariaSort:null,ariaValueMax:j,ariaValueMin:j,ariaValueNow:j,ariaValueText:null,role:null},transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function Ur(e,t){return t in e?e[t]:t}function Vr(e,t){return Ur(e,t.toLowerCase())}const ml=rt({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:et,acceptCharset:Z,accessKey:Z,action:null,allow:null,allowFullScreen:R,allowPaymentRequest:R,allowUserMedia:R,alt:null,as:null,async:R,autoCapitalize:null,autoComplete:Z,autoFocus:R,autoPlay:R,blocking:Z,capture:null,charSet:null,checked:R,cite:null,className:Z,cols:j,colSpan:null,content:null,contentEditable:le,controls:R,controlsList:Z,coords:j|et,crossOrigin:null,data:null,dateTime:null,decoding:null,default:R,defer:R,dir:null,dirName:null,disabled:R,download:Br,draggable:le,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:R,formTarget:null,headers:Z,height:j,hidden:R,high:j,href:null,hrefLang:null,htmlFor:Z,httpEquiv:Z,id:null,imageSizes:null,imageSrcSet:null,inert:R,inputMode:null,integrity:null,is:null,isMap:R,itemId:null,itemProp:Z,itemRef:Z,itemScope:R,itemType:Z,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:R,low:j,manifest:null,max:null,maxLength:j,media:null,method:null,min:null,minLength:j,multiple:R,muted:R,name:null,nonce:null,noModule:R,noValidate:R,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:R,optimum:j,pattern:null,ping:Z,placeholder:null,playsInline:R,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:R,referrerPolicy:null,rel:Z,required:R,reversed:R,rows:j,rowSpan:j,sandbox:Z,scope:null,scoped:R,seamless:R,selected:R,shadowRootClonable:R,shadowRootDelegatesFocus:R,shadowRootMode:null,shape:null,size:j,sizes:null,slot:null,span:j,spellCheck:le,src:null,srcDoc:null,srcLang:null,srcSet:null,start:j,step:null,style:null,tabIndex:j,target:null,title:null,translate:null,type:null,typeMustMatch:R,useMap:null,value:le,width:j,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:Z,axis:null,background:null,bgColor:null,border:j,borderColor:null,bottomMargin:j,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:R,declare:R,event:null,face:null,frame:null,frameBorder:null,hSpace:j,leftMargin:j,link:null,longDesc:null,lowSrc:null,marginHeight:j,marginWidth:j,noResize:R,noHref:R,noShade:R,noWrap:R,object:null,profile:null,prompt:null,rev:null,rightMargin:j,rules:null,scheme:null,scrolling:le,standby:null,summary:null,text:null,topMargin:j,valueType:null,version:null,vAlign:null,vLink:null,vSpace:j,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:R,disableRemotePlayback:R,prefix:null,property:null,results:j,security:null,unselectable:null},space:"html",transform:Vr}),gl=rt({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:Se,accentHeight:j,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:j,amplitude:j,arabicForm:null,ascent:j,attributeName:null,attributeType:null,azimuth:j,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:j,by:null,calcMode:null,capHeight:j,className:Z,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:j,diffuseConstant:j,direction:null,display:null,dur:null,divisor:j,dominantBaseline:null,download:R,dx:null,dy:null,edgeMode:null,editable:null,elevation:j,enableBackground:null,end:null,event:null,exponent:j,externalResourcesRequired:null,fill:null,fillOpacity:j,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:et,g2:et,glyphName:et,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:j,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:j,horizOriginX:j,horizOriginY:j,id:null,ideographic:j,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:j,k:j,k1:j,k2:j,k3:j,k4:j,kernelMatrix:Se,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:j,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:j,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:j,overlineThickness:j,paintOrder:null,panose1:null,path:null,pathLength:j,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:Z,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:j,pointsAtY:j,pointsAtZ:j,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Se,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Se,rev:Se,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Se,requiredFeatures:Se,requiredFonts:Se,requiredFormats:Se,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:j,specularExponent:j,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:j,strikethroughThickness:j,string:null,stroke:null,strokeDashArray:Se,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:j,strokeOpacity:j,strokeWidth:null,style:null,surfaceScale:j,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Se,tabIndex:j,tableValues:null,target:null,targetX:j,targetY:j,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Se,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:j,underlineThickness:j,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:j,values:null,vAlphabetic:j,vMathematical:j,vectorEffect:null,vHanging:j,vIdeographic:j,version:null,vertAdvY:j,vertOriginX:j,vertOriginY:j,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:j,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Ur}),qr=rt({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),$r=rt({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Vr}),Kr=rt({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),xl={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},yl=/[A-Z]/g,Wn=/-[a-z]/g,bl=/^data[-\w.:]+$/i;function wl(e,t){const n=rn(t);let r=t,i=ke;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&bl.test(t)){if(t.charAt(4)==="-"){const l=t.slice(5).replace(Wn,Sl);r="data"+l.charAt(0).toUpperCase()+l.slice(1)}else{const l=t.slice(4);if(!Wn.test(l)){let s=l.replace(yl,kl);s.charAt(0)!=="-"&&(s="-"+s),t="data"+s}}i=gn}return new i(r,t)}function kl(e){return"-"+e.toLowerCase()}function Sl(e){return e.charAt(1).toUpperCase()}const vl=Or([Hr,ml,qr,$r,Kr],"html"),xn=Or([Hr,gl,qr,$r,Kr],"svg");function Cl(e){return e.join(" ").trim()}var yn={},Yn=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,jl=/\n/g,Nl=/^\s*/,El=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,Pl=/^:\s*/,Il=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,_l=/^[;\s]*/,Tl=/^\s+|\s+$/g,Al=`
`,Xn="/",Gn="*",We="",Dl="comment",Ll="declaration",Fl=function(e,t){if(typeof e!="string")throw new TypeError("First argument must be a string");if(!e)return[];t=t||{};var n=1,r=1;function i(y){var w=y.match(jl);w&&(n+=w.length);var A=y.lastIndexOf(Al);r=~A?y.length-A:r+y.length}function l(){var y={line:n,column:r};return function(w){return w.position=new s(y),u(),w}}function s(y){this.start=y,this.end={line:n,column:r},this.source=t.source}s.prototype.content=e;function o(y){var w=new Error(t.source+":"+n+":"+r+": "+y);if(w.reason=y,w.filename=t.source,w.line=n,w.column=r,w.source=e,!t.silent)throw w}function c(y){var w=y.exec(e);if(w){var A=w[0];return i(A),e=e.slice(A.length),w}}function u(){c(Nl)}function d(y){var w;for(y=y||[];w=p();)w!==!1&&y.push(w);return y}function p(){var y=l();if(!(Xn!=e.charAt(0)||Gn!=e.charAt(1))){for(var w=2;We!=e.charAt(w)&&(Gn!=e.charAt(w)||Xn!=e.charAt(w+1));)++w;if(w+=2,We===e.charAt(w-1))return o("End of comment missing");var A=e.slice(2,w-2);return r+=2,i(A),e=e.slice(w),r+=2,y({type:Dl,comment:A})}}function g(){var y=l(),w=c(El);if(w){if(p(),!c(Pl))return o("property missing ':'");var A=c(Il),v=y({type:Ll,property:Jn(w[0].replace(Yn,We)),value:A?Jn(A[0].replace(Yn,We)):We});return c(_l),v}}function h(){var y=[];d(y);for(var w;w=g();)w!==!1&&(y.push(w),d(y));return y}return u(),h()};function Jn(e){return e?e.replace(Tl,We):We}var zl=Pt&&Pt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(yn,"__esModule",{value:!0});yn.default=Ml;var Rl=zl(Fl);function Ml(e,t){var n=null;if(!e||typeof e!="string")return n;var r=(0,Rl.default)(e),i=typeof t=="function";return r.forEach(function(l){if(l.type==="declaration"){var s=l.property,o=l.value;i?t(s,o,l):o&&(n=n||{},n[s]=o)}}),n}var Dt={};Object.defineProperty(Dt,"__esModule",{value:!0});Dt.camelCase=void 0;var Ol=/^--[a-zA-Z0-9_-]+$/,Bl=/-([a-z])/g,Hl=/^[^-]+$/,Ul=/^-(webkit|moz|ms|o|khtml)-/,Vl=/^-(ms)-/,ql=function(e){return!e||Hl.test(e)||Ol.test(e)},$l=function(e,t){return t.toUpperCase()},Zn=function(e,t){return"".concat(t,"-")},Kl=function(e,t){return t===void 0&&(t={}),ql(e)?e:(e=e.toLowerCase(),t.reactCompat?e=e.replace(Vl,Zn):e=e.replace(Ul,Zn),e.replace(Bl,$l))};Dt.camelCase=Kl;var Ql=Pt&&Pt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},Wl=Ql(yn),Yl=Dt;function sn(e,t){var n={};return!e||typeof e!="string"||(0,Wl.default)(e,function(r,i){r&&i&&(n[(0,Yl.camelCase)(r,t)]=i)}),n}sn.default=sn;var Xl=sn;const Gl=zr(Xl),Qr=Wr("end"),bn=Wr("start");function Wr(e){return t;function t(n){const r=n&&n.position&&n.position[e]||{};if(typeof r.line=="number"&&r.line>0&&typeof r.column=="number"&&r.column>0)return{line:r.line,column:r.column,offset:typeof r.offset=="number"&&r.offset>-1?r.offset:void 0}}}function Jl(e){const t=bn(e),n=Qr(e);if(t&&n)return{start:t,end:n}}function ht(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?er(e.position):"start"in e||"end"in e?er(e):"line"in e||"column"in e?an(e):""}function an(e){return tr(e&&e.line)+":"+tr(e&&e.column)}function er(e){return an(e&&e.start)+"-"+an(e&&e.end)}function tr(e){return e&&typeof e=="number"?e:1}class me extends Error{constructor(t,n,r){super(),typeof n=="string"&&(r=n,n=void 0);let i="",l={},s=!1;if(n&&("line"in n&&"column"in n?l={place:n}:"start"in n&&"end"in n?l={place:n}:"type"in n?l={ancestors:[n],place:n.position}:l={...n}),typeof t=="string"?i=t:!l.cause&&t&&(s=!0,i=t.message,l.cause=t),!l.ruleId&&!l.source&&typeof r=="string"){const c=r.indexOf(":");c===-1?l.ruleId=r:(l.source=r.slice(0,c),l.ruleId=r.slice(c+1))}if(!l.place&&l.ancestors&&l.ancestors){const c=l.ancestors[l.ancestors.length-1];c&&(l.place=c.position)}const o=l.place&&"start"in l.place?l.place.start:l.place;this.ancestors=l.ancestors||void 0,this.cause=l.cause||void 0,this.column=o?o.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=o?o.line:void 0,this.name=ht(l.place)||"1:1",this.place=l.place||void 0,this.reason=this.message,this.ruleId=l.ruleId||void 0,this.source=l.source||void 0,this.stack=s&&l.cause&&typeof l.cause.stack=="string"?l.cause.stack:"",this.actual,this.expected,this.note,this.url}}me.prototype.file="";me.prototype.name="";me.prototype.reason="";me.prototype.message="";me.prototype.stack="";me.prototype.column=void 0;me.prototype.line=void 0;me.prototype.ancestors=void 0;me.prototype.cause=void 0;me.prototype.fatal=void 0;me.prototype.place=void 0;me.prototype.ruleId=void 0;me.prototype.source=void 0;const wn={}.hasOwnProperty,Zl=new Map,es=/[A-Z]/g,ts=new Set(["table","tbody","thead","tfoot","tr"]),ns=new Set(["td","th"]),Yr="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function rs(e,t){if(!t||t.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if(typeof t.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=ds(n,t.jsxDEV)}else{if(typeof t.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof t.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");r=cs(n,t.jsx,t.jsxs)}const i={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:t.passKeys!==!1,passNode:t.passNode||!1,schema:t.space==="svg"?xn:vl,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:t.tableCellAlignToStyle!==!1},l=Xr(i,e,void 0);return l&&typeof l!="string"?l:i.create(e,i.Fragment,{children:l||void 0},void 0)}function Xr(e,t,n){if(t.type==="element")return is(e,t,n);if(t.type==="mdxFlowExpression"||t.type==="mdxTextExpression")return ls(e,t);if(t.type==="mdxJsxFlowElement"||t.type==="mdxJsxTextElement")return as(e,t,n);if(t.type==="mdxjsEsm")return ss(e,t);if(t.type==="root")return os(e,t,n);if(t.type==="text")return us(e,t)}function is(e,t,n){const r=e.schema;let i=r;t.tagName.toLowerCase()==="svg"&&r.space==="html"&&(i=xn,e.schema=i),e.ancestors.push(t);const l=Jr(e,t.tagName,!1),s=hs(e,t);let o=Sn(e,t);return ts.has(t.tagName)&&(o=o.filter(function(c){return typeof c=="string"?!pl(c):!0})),Gr(e,s,l,t),kn(s,o),e.ancestors.pop(),e.schema=r,e.create(t,l,s,n)}function ls(e,t){if(t.data&&t.data.estree&&e.evaluater){const r=t.data.estree.body[0];return r.type,e.evaluater.evaluateExpression(r.expression)}gt(e,t.position)}function ss(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);gt(e,t.position)}function as(e,t,n){const r=e.schema;let i=r;t.name==="svg"&&r.space==="html"&&(i=xn,e.schema=i),e.ancestors.push(t);const l=t.name===null?e.Fragment:Jr(e,t.name,!0),s=ps(e,t),o=Sn(e,t);return Gr(e,s,l,t),kn(s,o),e.ancestors.pop(),e.schema=r,e.create(t,l,s,n)}function os(e,t,n){const r={};return kn(r,Sn(e,t)),e.create(t,e.Fragment,r,n)}function us(e,t){return t.value}function Gr(e,t,n,r){typeof n!="string"&&n!==e.Fragment&&e.passNode&&(t.node=r)}function kn(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function cs(e,t,n){return r;function r(i,l,s,o){const u=Array.isArray(s.children)?n:t;return o?u(l,s,o):u(l,s)}}function ds(e,t){return n;function n(r,i,l,s){const o=Array.isArray(l.children),c=bn(r);return t(i,l,s,o,{columnNumber:c?c.column-1:void 0,fileName:e,lineNumber:c?c.line:void 0},void 0)}}function hs(e,t){const n={};let r,i;for(i in t.properties)if(i!=="children"&&wn.call(t.properties,i)){const l=fs(e,i,t.properties[i]);if(l){const[s,o]=l;e.tableCellAlignToStyle&&s==="align"&&typeof o=="string"&&ns.has(t.tagName)?r=o:n[s]=o}}if(r){const l=n.style||(n.style={});l[e.stylePropertyNameCase==="css"?"text-align":"textAlign"]=r}return n}function ps(e,t){const n={};for(const r of t.attributes)if(r.type==="mdxJsxExpressionAttribute")if(r.data&&r.data.estree&&e.evaluater){const l=r.data.estree.body[0];l.type;const s=l.expression;s.type;const o=s.properties[0];o.type,Object.assign(n,e.evaluater.evaluateExpression(o.argument))}else gt(e,t.position);else{const i=r.name;let l;if(r.value&&typeof r.value=="object")if(r.value.data&&r.value.data.estree&&e.evaluater){const o=r.value.data.estree.body[0];o.type,l=e.evaluater.evaluateExpression(o.expression)}else gt(e,t.position);else l=r.value===null?!0:r.value;n[i]=l}return n}function Sn(e,t){const n=[];let r=-1;const i=e.passKeys?new Map:Zl;for(;++r<t.children.length;){const l=t.children[r];let s;if(e.passKeys){const c=l.type==="element"?l.tagName:l.type==="mdxJsxFlowElement"||l.type==="mdxJsxTextElement"?l.name:void 0;if(c){const u=i.get(c)||0;s=c+"-"+u,i.set(c,u+1)}}const o=Xr(e,l,s);o!==void 0&&n.push(o)}return n}function fs(e,t,n){const r=wl(e.schema,t);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?ol(n):Cl(n)),r.property==="style"){let i=typeof n=="object"?n:ms(e,String(n));return e.stylePropertyNameCase==="css"&&(i=gs(i)),["style",i]}return[e.elementAttributeNameCase==="react"&&r.space?xl[r.property]||r.property:r.attribute,n]}}function ms(e,t){try{return Gl(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};const r=n,i=new me("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:r,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw i.file=e.filePath||void 0,i.url=Yr+"#cannot-parse-style-attribute",i}}function Jr(e,t,n){let r;if(!n)r={type:"Literal",value:t};else if(t.includes(".")){const i=t.split(".");let l=-1,s;for(;++l<i.length;){const o=$n(i[l])?{type:"Identifier",name:i[l]}:{type:"Literal",value:i[l]};s=s?{type:"MemberExpression",object:s,property:o,computed:!!(l&&o.type==="Literal"),optional:!1}:o}r=s}else r=$n(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};if(r.type==="Literal"){const i=r.value;return wn.call(e.components,i)?e.components[i]:i}if(e.evaluater)return e.evaluater.evaluateExpression(r);gt(e)}function gt(e,t){const n=new me("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=Yr+"#cannot-handle-mdx-estrees-without-createevaluater",n}function gs(e){const t={};let n;for(n in e)wn.call(e,n)&&(t[xs(n)]=e[n]);return t}function xs(e){let t=e.replace(es,ys);return t.slice(0,3)==="ms-"&&(t="-"+t),t}function ys(e){return"-"+e.toLowerCase()}const Kt={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},bs={};function ws(e,t){const n=bs,r=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,i=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return Zr(e,r,i)}function Zr(e,t,n){if(ks(e)){if("value"in e)return e.type==="html"&&!n?"":e.value;if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return nr(e.children,t,n)}return Array.isArray(e)?nr(e,t,n):""}function nr(e,t,n){const r=[];let i=-1;for(;++i<e.length;)r[i]=Zr(e[i],t,n);return r.join("")}function ks(e){return!!(e&&typeof e=="object")}const rr=document.createElement("i");function vn(e){const t="&"+e+";";rr.innerHTML=t;const n=rr.textContent;return n.charCodeAt(n.length-1)===59&&e!=="semi"||n===t?!1:n}function Oe(e,t,n,r){const i=e.length;let l=0,s;if(t<0?t=-t>i?0:i+t:t=t>i?i:t,n=n>0?n:0,r.length<1e4)s=Array.from(r),s.unshift(t,n),e.splice(...s);else for(n&&e.splice(t,n);l<r.length;)s=r.slice(l,l+1e4),s.unshift(t,0),e.splice(...s),l+=1e4,t+=1e4}function Ie(e,t){return e.length>0?(Oe(e,e.length,0,t),e):t}const ir={}.hasOwnProperty;function Ss(e){const t={};let n=-1;for(;++n<e.length;)vs(t,e[n]);return t}function vs(e,t){let n;for(n in t){const i=(ir.call(e,n)?e[n]:void 0)||(e[n]={}),l=t[n];let s;if(l)for(s in l){ir.call(i,s)||(i[s]=[]);const o=l[s];Cs(i[s],Array.isArray(o)?o:o?[o]:[])}}}function Cs(e,t){let n=-1;const r=[];for(;++n<t.length;)(t[n].add==="after"?e:r).push(t[n]);Oe(e,0,0,r)}function ei(e,t){const n=Number.parseInt(e,t);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"�":String.fromCodePoint(n)}function tt(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const Me=$e(/[A-Za-z]/),ve=$e(/[\dA-Za-z]/),js=$e(/[#-'*+\--9=?A-Z^-~]/);function on(e){return e!==null&&(e<32||e===127)}const un=$e(/\d/),Ns=$e(/[\dA-Fa-f]/),Es=$e(/[!-/:-@[-`{-~]/);function z(e){return e!==null&&e<-2}function we(e){return e!==null&&(e<0||e===32)}function X(e){return e===-2||e===-1||e===32}const Ps=$e(new RegExp("\\p{P}|\\p{S}","u")),Is=$e(/\s/);function $e(e){return t;function t(n){return n!==null&&n>-1&&e.test(String.fromCharCode(n))}}function it(e){const t=[];let n=-1,r=0,i=0;for(;++n<e.length;){const l=e.charCodeAt(n);let s="";if(l===37&&ve(e.charCodeAt(n+1))&&ve(e.charCodeAt(n+2)))i=2;else if(l<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(l))||(s=String.fromCharCode(l));else if(l>55295&&l<57344){const o=e.charCodeAt(n+1);l<56320&&o>56319&&o<57344?(s=String.fromCharCode(l,o),i=1):s="�"}else s=String.fromCharCode(l);s&&(t.push(e.slice(r,n),encodeURIComponent(s)),r=n+i+1,s=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function ee(e,t,n,r){const i=r?r-1:Number.POSITIVE_INFINITY;let l=0;return s;function s(c){return X(c)?(e.enter(n),o(c)):t(c)}function o(c){return X(c)&&l++<i?(e.consume(c),o):(e.exit(n),t(c))}}const _s={tokenize:Ts};function Ts(e){const t=e.attempt(this.parser.constructs.contentInitial,r,i);let n;return t;function r(o){if(o===null){e.consume(o);return}return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),ee(e,t,"linePrefix")}function i(o){return e.enter("paragraph"),l(o)}function l(o){const c=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=c),n=c,s(o)}function s(o){if(o===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(o);return}return z(o)?(e.consume(o),e.exit("chunkText"),l):(e.consume(o),s)}}const As={tokenize:Ds},lr={tokenize:Ls};function Ds(e){const t=this,n=[];let r=0,i,l,s;return o;function o(_){if(r<n.length){const H=n[r];return t.containerState=H[1],e.attempt(H[0].continuation,c,u)(_)}return u(_)}function c(_){if(r++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,i&&L();const H=t.events.length;let U=H,S;for(;U--;)if(t.events[U][0]==="exit"&&t.events[U][1].type==="chunkFlow"){S=t.events[U][1].end;break}v(r);let B=H;for(;B<t.events.length;)t.events[B][1].end={...S},B++;return Oe(t.events,U+1,0,t.events.slice(H)),t.events.length=B,u(_)}return o(_)}function u(_){if(r===n.length){if(!i)return g(_);if(i.currentConstruct&&i.currentConstruct.concrete)return y(_);t.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(lr,d,p)(_)}function d(_){return i&&L(),v(r),g(_)}function p(_){return t.parser.lazy[t.now().line]=r!==n.length,s=t.now().offset,y(_)}function g(_){return t.containerState={},e.attempt(lr,h,y)(_)}function h(_){return r++,n.push([t.currentConstruct,t.containerState]),g(_)}function y(_){if(_===null){i&&L(),v(0),e.consume(_);return}return i=i||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:i,contentType:"flow",previous:l}),w(_)}function w(_){if(_===null){A(e.exit("chunkFlow"),!0),v(0),e.consume(_);return}return z(_)?(e.consume(_),A(e.exit("chunkFlow")),r=0,t.interrupt=void 0,o):(e.consume(_),w)}function A(_,H){const U=t.sliceStream(_);if(H&&U.push(null),_.previous=l,l&&(l.next=_),l=_,i.defineSkip(_.start),i.write(U),t.parser.lazy[_.start.line]){let S=i.events.length;for(;S--;)if(i.events[S][1].start.offset<s&&(!i.events[S][1].end||i.events[S][1].end.offset>s))return;const B=t.events.length;let Q=B,V,$;for(;Q--;)if(t.events[Q][0]==="exit"&&t.events[Q][1].type==="chunkFlow"){if(V){$=t.events[Q][1].end;break}V=!0}for(v(r),S=B;S<t.events.length;)t.events[S][1].end={...$},S++;Oe(t.events,Q+1,0,t.events.slice(B)),t.events.length=S}}function v(_){let H=n.length;for(;H-- >_;){const U=n[H];t.containerState=U[1],U[0].exit.call(t,e)}n.length=_}function L(){i.write([null]),l=void 0,i=void 0,t.containerState._closeFlow=void 0}}function Ls(e,t,n){return ee(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function sr(e){if(e===null||we(e)||Is(e))return 1;if(Ps(e))return 2}function Cn(e,t,n){const r=[];let i=-1;for(;++i<e.length;){const l=e[i].resolveAll;l&&!r.includes(l)&&(t=l(t,n),r.push(l))}return t}const cn={name:"attention",resolveAll:Fs,tokenize:zs};function Fs(e,t){let n=-1,r,i,l,s,o,c,u,d;for(;++n<e.length;)if(e[n][0]==="enter"&&e[n][1].type==="attentionSequence"&&e[n][1]._close){for(r=n;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&t.sliceSerialize(e[r][1]).charCodeAt(0)===t.sliceSerialize(e[n][1]).charCodeAt(0)){if((e[r][1]._close||e[n][1]._open)&&(e[n][1].end.offset-e[n][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[n][1].end.offset-e[n][1].start.offset)%3))continue;c=e[r][1].end.offset-e[r][1].start.offset>1&&e[n][1].end.offset-e[n][1].start.offset>1?2:1;const p={...e[r][1].end},g={...e[n][1].start};ar(p,-c),ar(g,c),s={type:c>1?"strongSequence":"emphasisSequence",start:p,end:{...e[r][1].end}},o={type:c>1?"strongSequence":"emphasisSequence",start:{...e[n][1].start},end:g},l={type:c>1?"strongText":"emphasisText",start:{...e[r][1].end},end:{...e[n][1].start}},i={type:c>1?"strong":"emphasis",start:{...s.start},end:{...o.end}},e[r][1].end={...s.start},e[n][1].start={...o.end},u=[],e[r][1].end.offset-e[r][1].start.offset&&(u=Ie(u,[["enter",e[r][1],t],["exit",e[r][1],t]])),u=Ie(u,[["enter",i,t],["enter",s,t],["exit",s,t],["enter",l,t]]),u=Ie(u,Cn(t.parser.constructs.insideSpan.null,e.slice(r+1,n),t)),u=Ie(u,[["exit",l,t],["enter",o,t],["exit",o,t],["exit",i,t]]),e[n][1].end.offset-e[n][1].start.offset?(d=2,u=Ie(u,[["enter",e[n][1],t],["exit",e[n][1],t]])):d=0,Oe(e,r-1,n-r+3,u),n=r+u.length-d-2;break}}for(n=-1;++n<e.length;)e[n][1].type==="attentionSequence"&&(e[n][1].type="data");return e}function zs(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,i=sr(r);let l;return s;function s(c){return l=c,e.enter("attentionSequence"),o(c)}function o(c){if(c===l)return e.consume(c),o;const u=e.exit("attentionSequence"),d=sr(c),p=!d||d===2&&i||n.includes(c),g=!i||i===2&&d||n.includes(r);return u._open=!!(l===42?p:p&&(i||!g)),u._close=!!(l===42?g:g&&(d||!p)),t(c)}}function ar(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const Rs={name:"autolink",tokenize:Ms};function Ms(e,t,n){let r=0;return i;function i(h){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(h),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),l}function l(h){return Me(h)?(e.consume(h),s):h===64?n(h):u(h)}function s(h){return h===43||h===45||h===46||ve(h)?(r=1,o(h)):u(h)}function o(h){return h===58?(e.consume(h),r=0,c):(h===43||h===45||h===46||ve(h))&&r++<32?(e.consume(h),o):(r=0,u(h))}function c(h){return h===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(h),e.exit("autolinkMarker"),e.exit("autolink"),t):h===null||h===32||h===60||on(h)?n(h):(e.consume(h),c)}function u(h){return h===64?(e.consume(h),d):js(h)?(e.consume(h),u):n(h)}function d(h){return ve(h)?p(h):n(h)}function p(h){return h===46?(e.consume(h),r=0,d):h===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(h),e.exit("autolinkMarker"),e.exit("autolink"),t):g(h)}function g(h){if((h===45||ve(h))&&r++<63){const y=h===45?g:p;return e.consume(h),y}return n(h)}}const Lt={partial:!0,tokenize:Os};function Os(e,t,n){return r;function r(l){return X(l)?ee(e,i,"linePrefix")(l):i(l)}function i(l){return l===null||z(l)?t(l):n(l)}}const ti={continuation:{tokenize:Hs},exit:Us,name:"blockQuote",tokenize:Bs};function Bs(e,t,n){const r=this;return i;function i(s){if(s===62){const o=r.containerState;return o.open||(e.enter("blockQuote",{_container:!0}),o.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(s),e.exit("blockQuoteMarker"),l}return n(s)}function l(s){return X(s)?(e.enter("blockQuotePrefixWhitespace"),e.consume(s),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(s))}}function Hs(e,t,n){const r=this;return i;function i(s){return X(s)?ee(e,l,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(s):l(s)}function l(s){return e.attempt(ti,t,n)(s)}}function Us(e){e.exit("blockQuote")}const ni={name:"characterEscape",tokenize:Vs};function Vs(e,t,n){return r;function r(l){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(l),e.exit("escapeMarker"),i}function i(l){return Es(l)?(e.enter("characterEscapeValue"),e.consume(l),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(l)}}const ri={name:"characterReference",tokenize:qs};function qs(e,t,n){const r=this;let i=0,l,s;return o;function o(p){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(p),e.exit("characterReferenceMarker"),c}function c(p){return p===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(p),e.exit("characterReferenceMarkerNumeric"),u):(e.enter("characterReferenceValue"),l=31,s=ve,d(p))}function u(p){return p===88||p===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(p),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),l=6,s=Ns,d):(e.enter("characterReferenceValue"),l=7,s=un,d(p))}function d(p){if(p===59&&i){const g=e.exit("characterReferenceValue");return s===ve&&!vn(r.sliceSerialize(g))?n(p):(e.enter("characterReferenceMarker"),e.consume(p),e.exit("characterReferenceMarker"),e.exit("characterReference"),t)}return s(p)&&i++<l?(e.consume(p),d):n(p)}}const or={partial:!0,tokenize:Ks},ur={concrete:!0,name:"codeFenced",tokenize:$s};function $s(e,t,n){const r=this,i={partial:!0,tokenize:U};let l=0,s=0,o;return c;function c(S){return u(S)}function u(S){const B=r.events[r.events.length-1];return l=B&&B[1].type==="linePrefix"?B[2].sliceSerialize(B[1],!0).length:0,o=S,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),d(S)}function d(S){return S===o?(s++,e.consume(S),d):s<3?n(S):(e.exit("codeFencedFenceSequence"),X(S)?ee(e,p,"whitespace")(S):p(S))}function p(S){return S===null||z(S)?(e.exit("codeFencedFence"),r.interrupt?t(S):e.check(or,w,H)(S)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),g(S))}function g(S){return S===null||z(S)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),p(S)):X(S)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),ee(e,h,"whitespace")(S)):S===96&&S===o?n(S):(e.consume(S),g)}function h(S){return S===null||z(S)?p(S):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),y(S))}function y(S){return S===null||z(S)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),p(S)):S===96&&S===o?n(S):(e.consume(S),y)}function w(S){return e.attempt(i,H,A)(S)}function A(S){return e.enter("lineEnding"),e.consume(S),e.exit("lineEnding"),v}function v(S){return l>0&&X(S)?ee(e,L,"linePrefix",l+1)(S):L(S)}function L(S){return S===null||z(S)?e.check(or,w,H)(S):(e.enter("codeFlowValue"),_(S))}function _(S){return S===null||z(S)?(e.exit("codeFlowValue"),L(S)):(e.consume(S),_)}function H(S){return e.exit("codeFenced"),t(S)}function U(S,B,Q){let V=0;return $;function $(C){return S.enter("lineEnding"),S.consume(C),S.exit("lineEnding"),I}function I(C){return S.enter("codeFencedFence"),X(C)?ee(S,N,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(C):N(C)}function N(C){return C===o?(S.enter("codeFencedFenceSequence"),q(C)):Q(C)}function q(C){return C===o?(V++,S.consume(C),q):V>=s?(S.exit("codeFencedFenceSequence"),X(C)?ee(S,G,"whitespace")(C):G(C)):Q(C)}function G(C){return C===null||z(C)?(S.exit("codeFencedFence"),B(C)):Q(C)}}}function Ks(e,t,n){const r=this;return i;function i(s){return s===null?n(s):(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),l)}function l(s){return r.parser.lazy[r.now().line]?n(s):t(s)}}const Qt={name:"codeIndented",tokenize:Ws},Qs={partial:!0,tokenize:Ys};function Ws(e,t,n){const r=this;return i;function i(u){return e.enter("codeIndented"),ee(e,l,"linePrefix",5)(u)}function l(u){const d=r.events[r.events.length-1];return d&&d[1].type==="linePrefix"&&d[2].sliceSerialize(d[1],!0).length>=4?s(u):n(u)}function s(u){return u===null?c(u):z(u)?e.attempt(Qs,s,c)(u):(e.enter("codeFlowValue"),o(u))}function o(u){return u===null||z(u)?(e.exit("codeFlowValue"),s(u)):(e.consume(u),o)}function c(u){return e.exit("codeIndented"),t(u)}}function Ys(e,t,n){const r=this;return i;function i(s){return r.parser.lazy[r.now().line]?n(s):z(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),i):ee(e,l,"linePrefix",5)(s)}function l(s){const o=r.events[r.events.length-1];return o&&o[1].type==="linePrefix"&&o[2].sliceSerialize(o[1],!0).length>=4?t(s):z(s)?i(s):n(s)}}const Xs={name:"codeText",previous:Js,resolve:Gs,tokenize:Zs};function Gs(e){let t=e.length-4,n=3,r,i;if((e[n][1].type==="lineEnding"||e[n][1].type==="space")&&(e[t][1].type==="lineEnding"||e[t][1].type==="space")){for(r=n;++r<t;)if(e[r][1].type==="codeTextData"){e[n][1].type="codeTextPadding",e[t][1].type="codeTextPadding",n+=2,t-=2;break}}for(r=n-1,t++;++r<=t;)i===void 0?r!==t&&e[r][1].type!=="lineEnding"&&(i=r):(r===t||e[r][1].type==="lineEnding")&&(e[i][1].type="codeTextData",r!==i+2&&(e[i][1].end=e[r-1][1].end,e.splice(i+2,r-i-2),t-=r-i-2,r=i+2),i=void 0);return e}function Js(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function Zs(e,t,n){let r=0,i,l;return s;function s(p){return e.enter("codeText"),e.enter("codeTextSequence"),o(p)}function o(p){return p===96?(e.consume(p),r++,o):(e.exit("codeTextSequence"),c(p))}function c(p){return p===null?n(p):p===32?(e.enter("space"),e.consume(p),e.exit("space"),c):p===96?(l=e.enter("codeTextSequence"),i=0,d(p)):z(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),c):(e.enter("codeTextData"),u(p))}function u(p){return p===null||p===32||p===96||z(p)?(e.exit("codeTextData"),c(p)):(e.consume(p),u)}function d(p){return p===96?(e.consume(p),i++,d):i===r?(e.exit("codeTextSequence"),e.exit("codeText"),t(p)):(l.type="codeTextData",u(p))}}class ea{constructor(t){this.left=t?[...t]:[],this.right=[]}get(t){if(t<0||t>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+t+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return t<this.left.length?this.left[t]:this.right[this.right.length-t+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(t,n){const r=n??Number.POSITIVE_INFINITY;return r<this.left.length?this.left.slice(t,r):t>this.left.length?this.right.slice(this.right.length-r+this.left.length,this.right.length-t+this.left.length).reverse():this.left.slice(t).concat(this.right.slice(this.right.length-r+this.left.length).reverse())}splice(t,n,r){const i=n||0;this.setCursor(Math.trunc(t));const l=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return r&&ct(this.left,r),l.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(t){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(t)}pushMany(t){this.setCursor(Number.POSITIVE_INFINITY),ct(this.left,t)}unshift(t){this.setCursor(0),this.right.push(t)}unshiftMany(t){this.setCursor(0),ct(this.right,t.reverse())}setCursor(t){if(!(t===this.left.length||t>this.left.length&&this.right.length===0||t<0&&this.left.length===0))if(t<this.left.length){const n=this.left.splice(t,Number.POSITIVE_INFINITY);ct(this.right,n.reverse())}else{const n=this.right.splice(this.left.length+this.right.length-t,Number.POSITIVE_INFINITY);ct(this.left,n.reverse())}}}function ct(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function ii(e){const t={};let n=-1,r,i,l,s,o,c,u;const d=new ea(e);for(;++n<d.length;){for(;n in t;)n=t[n];if(r=d.get(n),n&&r[1].type==="chunkFlow"&&d.get(n-1)[1].type==="listItemPrefix"&&(c=r[1]._tokenizer.events,l=0,l<c.length&&c[l][1].type==="lineEndingBlank"&&(l+=2),l<c.length&&c[l][1].type==="content"))for(;++l<c.length&&c[l][1].type!=="content";)c[l][1].type==="chunkText"&&(c[l][1]._isInFirstContentOfListItem=!0,l++);if(r[0]==="enter")r[1].contentType&&(Object.assign(t,ta(d,n)),n=t[n],u=!0);else if(r[1]._container){for(l=n,i=void 0;l--;)if(s=d.get(l),s[1].type==="lineEnding"||s[1].type==="lineEndingBlank")s[0]==="enter"&&(i&&(d.get(i)[1].type="lineEndingBlank"),s[1].type="lineEnding",i=l);else if(!(s[1].type==="linePrefix"||s[1].type==="listItemIndent"))break;i&&(r[1].end={...d.get(i)[1].start},o=d.slice(i,n),o.unshift(r),d.splice(i,n-i+1,o))}}return Oe(e,0,Number.POSITIVE_INFINITY,d.slice(0)),!u}function ta(e,t){const n=e.get(t)[1],r=e.get(t)[2];let i=t-1;const l=[];let s=n._tokenizer;s||(s=r.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(s._contentTypeTextTrailing=!0));const o=s.events,c=[],u={};let d,p,g=-1,h=n,y=0,w=0;const A=[w];for(;h;){for(;e.get(++i)[1]!==h;);l.push(i),h._tokenizer||(d=r.sliceStream(h),h.next||d.push(null),p&&s.defineSkip(h.start),h._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=!0),s.write(d),h._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=void 0)),p=h,h=h.next}for(h=n;++g<o.length;)o[g][0]==="exit"&&o[g-1][0]==="enter"&&o[g][1].type===o[g-1][1].type&&o[g][1].start.line!==o[g][1].end.line&&(w=g+1,A.push(w),h._tokenizer=void 0,h.previous=void 0,h=h.next);for(s.events=[],h?(h._tokenizer=void 0,h.previous=void 0):A.pop(),g=A.length;g--;){const v=o.slice(A[g],A[g+1]),L=l.pop();c.push([L,L+v.length-1]),e.splice(L,2,v)}for(c.reverse(),g=-1;++g<c.length;)u[y+c[g][0]]=y+c[g][1],y+=c[g][1]-c[g][0]-1;return u}const na={resolve:ia,tokenize:la},ra={partial:!0,tokenize:sa};function ia(e){return ii(e),e}function la(e,t){let n;return r;function r(o){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),i(o)}function i(o){return o===null?l(o):z(o)?e.check(ra,s,l)(o):(e.consume(o),i)}function l(o){return e.exit("chunkContent"),e.exit("content"),t(o)}function s(o){return e.consume(o),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,i}}function sa(e,t,n){const r=this;return i;function i(s){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),ee(e,l,"linePrefix")}function l(s){if(s===null||z(s))return n(s);const o=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&o&&o[1].type==="linePrefix"&&o[2].sliceSerialize(o[1],!0).length>=4?t(s):e.interrupt(r.parser.constructs.flow,n,t)(s)}}function li(e,t,n,r,i,l,s,o,c){const u=c||Number.POSITIVE_INFINITY;let d=0;return p;function p(v){return v===60?(e.enter(r),e.enter(i),e.enter(l),e.consume(v),e.exit(l),g):v===null||v===32||v===41||on(v)?n(v):(e.enter(r),e.enter(s),e.enter(o),e.enter("chunkString",{contentType:"string"}),w(v))}function g(v){return v===62?(e.enter(l),e.consume(v),e.exit(l),e.exit(i),e.exit(r),t):(e.enter(o),e.enter("chunkString",{contentType:"string"}),h(v))}function h(v){return v===62?(e.exit("chunkString"),e.exit(o),g(v)):v===null||v===60||z(v)?n(v):(e.consume(v),v===92?y:h)}function y(v){return v===60||v===62||v===92?(e.consume(v),h):h(v)}function w(v){return!d&&(v===null||v===41||we(v))?(e.exit("chunkString"),e.exit(o),e.exit(s),e.exit(r),t(v)):d<u&&v===40?(e.consume(v),d++,w):v===41?(e.consume(v),d--,w):v===null||v===32||v===40||on(v)?n(v):(e.consume(v),v===92?A:w)}function A(v){return v===40||v===41||v===92?(e.consume(v),w):w(v)}}function si(e,t,n,r,i,l){const s=this;let o=0,c;return u;function u(h){return e.enter(r),e.enter(i),e.consume(h),e.exit(i),e.enter(l),d}function d(h){return o>999||h===null||h===91||h===93&&!c||h===94&&!o&&"_hiddenFootnoteSupport"in s.parser.constructs?n(h):h===93?(e.exit(l),e.enter(i),e.consume(h),e.exit(i),e.exit(r),t):z(h)?(e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),d):(e.enter("chunkString",{contentType:"string"}),p(h))}function p(h){return h===null||h===91||h===93||z(h)||o++>999?(e.exit("chunkString"),d(h)):(e.consume(h),c||(c=!X(h)),h===92?g:p)}function g(h){return h===91||h===92||h===93?(e.consume(h),o++,p):p(h)}}function ai(e,t,n,r,i,l){let s;return o;function o(g){return g===34||g===39||g===40?(e.enter(r),e.enter(i),e.consume(g),e.exit(i),s=g===40?41:g,c):n(g)}function c(g){return g===s?(e.enter(i),e.consume(g),e.exit(i),e.exit(r),t):(e.enter(l),u(g))}function u(g){return g===s?(e.exit(l),c(s)):g===null?n(g):z(g)?(e.enter("lineEnding"),e.consume(g),e.exit("lineEnding"),ee(e,u,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),d(g))}function d(g){return g===s||g===null||z(g)?(e.exit("chunkString"),u(g)):(e.consume(g),g===92?p:d)}function p(g){return g===s||g===92?(e.consume(g),d):d(g)}}function pt(e,t){let n;return r;function r(i){return z(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):X(i)?ee(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}const aa={name:"definition",tokenize:ua},oa={partial:!0,tokenize:ca};function ua(e,t,n){const r=this;let i;return l;function l(h){return e.enter("definition"),s(h)}function s(h){return si.call(r,e,o,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(h)}function o(h){return i=tt(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),h===58?(e.enter("definitionMarker"),e.consume(h),e.exit("definitionMarker"),c):n(h)}function c(h){return we(h)?pt(e,u)(h):u(h)}function u(h){return li(e,d,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(h)}function d(h){return e.attempt(oa,p,p)(h)}function p(h){return X(h)?ee(e,g,"whitespace")(h):g(h)}function g(h){return h===null||z(h)?(e.exit("definition"),r.parser.defined.push(i),t(h)):n(h)}}function ca(e,t,n){return r;function r(o){return we(o)?pt(e,i)(o):n(o)}function i(o){return ai(e,l,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(o)}function l(o){return X(o)?ee(e,s,"whitespace")(o):s(o)}function s(o){return o===null||z(o)?t(o):n(o)}}const da={name:"hardBreakEscape",tokenize:ha};function ha(e,t,n){return r;function r(l){return e.enter("hardBreakEscape"),e.consume(l),i}function i(l){return z(l)?(e.exit("hardBreakEscape"),t(l)):n(l)}}const pa={name:"headingAtx",resolve:fa,tokenize:ma};function fa(e,t){let n=e.length-2,r=3,i,l;return e[r][1].type==="whitespace"&&(r+=2),n-2>r&&e[n][1].type==="whitespace"&&(n-=2),e[n][1].type==="atxHeadingSequence"&&(r===n-1||n-4>r&&e[n-2][1].type==="whitespace")&&(n-=r+1===n?2:4),n>r&&(i={type:"atxHeadingText",start:e[r][1].start,end:e[n][1].end},l={type:"chunkText",start:e[r][1].start,end:e[n][1].end,contentType:"text"},Oe(e,r,n-r+1,[["enter",i,t],["enter",l,t],["exit",l,t],["exit",i,t]])),e}function ma(e,t,n){let r=0;return i;function i(d){return e.enter("atxHeading"),l(d)}function l(d){return e.enter("atxHeadingSequence"),s(d)}function s(d){return d===35&&r++<6?(e.consume(d),s):d===null||we(d)?(e.exit("atxHeadingSequence"),o(d)):n(d)}function o(d){return d===35?(e.enter("atxHeadingSequence"),c(d)):d===null||z(d)?(e.exit("atxHeading"),t(d)):X(d)?ee(e,o,"whitespace")(d):(e.enter("atxHeadingText"),u(d))}function c(d){return d===35?(e.consume(d),c):(e.exit("atxHeadingSequence"),o(d))}function u(d){return d===null||d===35||we(d)?(e.exit("atxHeadingText"),o(d)):(e.consume(d),u)}}const ga=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],cr=["pre","script","style","textarea"],xa={concrete:!0,name:"htmlFlow",resolveTo:wa,tokenize:ka},ya={partial:!0,tokenize:va},ba={partial:!0,tokenize:Sa};function wa(e){let t=e.length;for(;t--&&!(e[t][0]==="enter"&&e[t][1].type==="htmlFlow"););return t>1&&e[t-2][1].type==="linePrefix"&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e}function ka(e,t,n){const r=this;let i,l,s,o,c;return u;function u(m){return d(m)}function d(m){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(m),p}function p(m){return m===33?(e.consume(m),g):m===47?(e.consume(m),l=!0,w):m===63?(e.consume(m),i=3,r.interrupt?t:f):Me(m)?(e.consume(m),s=String.fromCharCode(m),A):n(m)}function g(m){return m===45?(e.consume(m),i=2,h):m===91?(e.consume(m),i=5,o=0,y):Me(m)?(e.consume(m),i=4,r.interrupt?t:f):n(m)}function h(m){return m===45?(e.consume(m),r.interrupt?t:f):n(m)}function y(m){const ye="CDATA[";return m===ye.charCodeAt(o++)?(e.consume(m),o===ye.length?r.interrupt?t:N:y):n(m)}function w(m){return Me(m)?(e.consume(m),s=String.fromCharCode(m),A):n(m)}function A(m){if(m===null||m===47||m===62||we(m)){const ye=m===47,Ae=s.toLowerCase();return!ye&&!l&&cr.includes(Ae)?(i=1,r.interrupt?t(m):N(m)):ga.includes(s.toLowerCase())?(i=6,ye?(e.consume(m),v):r.interrupt?t(m):N(m)):(i=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(m):l?L(m):_(m))}return m===45||ve(m)?(e.consume(m),s+=String.fromCharCode(m),A):n(m)}function v(m){return m===62?(e.consume(m),r.interrupt?t:N):n(m)}function L(m){return X(m)?(e.consume(m),L):$(m)}function _(m){return m===47?(e.consume(m),$):m===58||m===95||Me(m)?(e.consume(m),H):X(m)?(e.consume(m),_):$(m)}function H(m){return m===45||m===46||m===58||m===95||ve(m)?(e.consume(m),H):U(m)}function U(m){return m===61?(e.consume(m),S):X(m)?(e.consume(m),U):_(m)}function S(m){return m===null||m===60||m===61||m===62||m===96?n(m):m===34||m===39?(e.consume(m),c=m,B):X(m)?(e.consume(m),S):Q(m)}function B(m){return m===c?(e.consume(m),c=null,V):m===null||z(m)?n(m):(e.consume(m),B)}function Q(m){return m===null||m===34||m===39||m===47||m===60||m===61||m===62||m===96||we(m)?U(m):(e.consume(m),Q)}function V(m){return m===47||m===62||X(m)?_(m):n(m)}function $(m){return m===62?(e.consume(m),I):n(m)}function I(m){return m===null||z(m)?N(m):X(m)?(e.consume(m),I):n(m)}function N(m){return m===45&&i===2?(e.consume(m),W):m===60&&i===1?(e.consume(m),te):m===62&&i===4?(e.consume(m),se):m===63&&i===3?(e.consume(m),f):m===93&&i===5?(e.consume(m),ne):z(m)&&(i===6||i===7)?(e.exit("htmlFlowData"),e.check(ya,xe,q)(m)):m===null||z(m)?(e.exit("htmlFlowData"),q(m)):(e.consume(m),N)}function q(m){return e.check(ba,G,xe)(m)}function G(m){return e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),C}function C(m){return m===null||z(m)?q(m):(e.enter("htmlFlowData"),N(m))}function W(m){return m===45?(e.consume(m),f):N(m)}function te(m){return m===47?(e.consume(m),s="",oe):N(m)}function oe(m){if(m===62){const ye=s.toLowerCase();return cr.includes(ye)?(e.consume(m),se):N(m)}return Me(m)&&s.length<8?(e.consume(m),s+=String.fromCharCode(m),oe):N(m)}function ne(m){return m===93?(e.consume(m),f):N(m)}function f(m){return m===62?(e.consume(m),se):m===45&&i===2?(e.consume(m),f):N(m)}function se(m){return m===null||z(m)?(e.exit("htmlFlowData"),xe(m)):(e.consume(m),se)}function xe(m){return e.exit("htmlFlow"),t(m)}}function Sa(e,t,n){const r=this;return i;function i(s){return z(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),l):n(s)}function l(s){return r.parser.lazy[r.now().line]?n(s):t(s)}}function va(e,t,n){return r;function r(i){return e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),e.attempt(Lt,t,n)}}const Ca={name:"htmlText",tokenize:ja};function ja(e,t,n){const r=this;let i,l,s;return o;function o(f){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(f),c}function c(f){return f===33?(e.consume(f),u):f===47?(e.consume(f),U):f===63?(e.consume(f),_):Me(f)?(e.consume(f),Q):n(f)}function u(f){return f===45?(e.consume(f),d):f===91?(e.consume(f),l=0,y):Me(f)?(e.consume(f),L):n(f)}function d(f){return f===45?(e.consume(f),h):n(f)}function p(f){return f===null?n(f):f===45?(e.consume(f),g):z(f)?(s=p,te(f)):(e.consume(f),p)}function g(f){return f===45?(e.consume(f),h):p(f)}function h(f){return f===62?W(f):f===45?g(f):p(f)}function y(f){const se="CDATA[";return f===se.charCodeAt(l++)?(e.consume(f),l===se.length?w:y):n(f)}function w(f){return f===null?n(f):f===93?(e.consume(f),A):z(f)?(s=w,te(f)):(e.consume(f),w)}function A(f){return f===93?(e.consume(f),v):w(f)}function v(f){return f===62?W(f):f===93?(e.consume(f),v):w(f)}function L(f){return f===null||f===62?W(f):z(f)?(s=L,te(f)):(e.consume(f),L)}function _(f){return f===null?n(f):f===63?(e.consume(f),H):z(f)?(s=_,te(f)):(e.consume(f),_)}function H(f){return f===62?W(f):_(f)}function U(f){return Me(f)?(e.consume(f),S):n(f)}function S(f){return f===45||ve(f)?(e.consume(f),S):B(f)}function B(f){return z(f)?(s=B,te(f)):X(f)?(e.consume(f),B):W(f)}function Q(f){return f===45||ve(f)?(e.consume(f),Q):f===47||f===62||we(f)?V(f):n(f)}function V(f){return f===47?(e.consume(f),W):f===58||f===95||Me(f)?(e.consume(f),$):z(f)?(s=V,te(f)):X(f)?(e.consume(f),V):W(f)}function $(f){return f===45||f===46||f===58||f===95||ve(f)?(e.consume(f),$):I(f)}function I(f){return f===61?(e.consume(f),N):z(f)?(s=I,te(f)):X(f)?(e.consume(f),I):V(f)}function N(f){return f===null||f===60||f===61||f===62||f===96?n(f):f===34||f===39?(e.consume(f),i=f,q):z(f)?(s=N,te(f)):X(f)?(e.consume(f),N):(e.consume(f),G)}function q(f){return f===i?(e.consume(f),i=void 0,C):f===null?n(f):z(f)?(s=q,te(f)):(e.consume(f),q)}function G(f){return f===null||f===34||f===39||f===60||f===61||f===96?n(f):f===47||f===62||we(f)?V(f):(e.consume(f),G)}function C(f){return f===47||f===62||we(f)?V(f):n(f)}function W(f){return f===62?(e.consume(f),e.exit("htmlTextData"),e.exit("htmlText"),t):n(f)}function te(f){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),oe}function oe(f){return X(f)?ee(e,ne,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(f):ne(f)}function ne(f){return e.enter("htmlTextData"),s(f)}}const jn={name:"labelEnd",resolveAll:Ia,resolveTo:_a,tokenize:Ta},Na={tokenize:Aa},Ea={tokenize:Da},Pa={tokenize:La};function Ia(e){let t=-1;const n=[];for(;++t<e.length;){const r=e[t][1];if(n.push(e[t]),r.type==="labelImage"||r.type==="labelLink"||r.type==="labelEnd"){const i=r.type==="labelImage"?4:2;r.type="data",t+=i}}return e.length!==n.length&&Oe(e,0,e.length,n),e}function _a(e,t){let n=e.length,r=0,i,l,s,o;for(;n--;)if(i=e[n][1],l){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[n][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(s){if(e[n][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(l=n,i.type!=="labelLink")){r=2;break}}else i.type==="labelEnd"&&(s=n);const c={type:e[l][1].type==="labelLink"?"link":"image",start:{...e[l][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[l][1].start},end:{...e[s][1].end}},d={type:"labelText",start:{...e[l+r+2][1].end},end:{...e[s-2][1].start}};return o=[["enter",c,t],["enter",u,t]],o=Ie(o,e.slice(l+1,l+r+3)),o=Ie(o,[["enter",d,t]]),o=Ie(o,Cn(t.parser.constructs.insideSpan.null,e.slice(l+r+4,s-3),t)),o=Ie(o,[["exit",d,t],e[s-2],e[s-1],["exit",u,t]]),o=Ie(o,e.slice(s+1)),o=Ie(o,[["exit",c,t]]),Oe(e,l,e.length,o),e}function Ta(e,t,n){const r=this;let i=r.events.length,l,s;for(;i--;)if((r.events[i][1].type==="labelImage"||r.events[i][1].type==="labelLink")&&!r.events[i][1]._balanced){l=r.events[i][1];break}return o;function o(g){return l?l._inactive?p(g):(s=r.parser.defined.includes(tt(r.sliceSerialize({start:l.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(g),e.exit("labelMarker"),e.exit("labelEnd"),c):n(g)}function c(g){return g===40?e.attempt(Na,d,s?d:p)(g):g===91?e.attempt(Ea,d,s?u:p)(g):s?d(g):p(g)}function u(g){return e.attempt(Pa,d,p)(g)}function d(g){return t(g)}function p(g){return l._balanced=!0,n(g)}}function Aa(e,t,n){return r;function r(p){return e.enter("resource"),e.enter("resourceMarker"),e.consume(p),e.exit("resourceMarker"),i}function i(p){return we(p)?pt(e,l)(p):l(p)}function l(p){return p===41?d(p):li(e,s,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(p)}function s(p){return we(p)?pt(e,c)(p):d(p)}function o(p){return n(p)}function c(p){return p===34||p===39||p===40?ai(e,u,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(p):d(p)}function u(p){return we(p)?pt(e,d)(p):d(p)}function d(p){return p===41?(e.enter("resourceMarker"),e.consume(p),e.exit("resourceMarker"),e.exit("resource"),t):n(p)}}function Da(e,t,n){const r=this;return i;function i(o){return si.call(r,e,l,s,"reference","referenceMarker","referenceString")(o)}function l(o){return r.parser.defined.includes(tt(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(o):n(o)}function s(o){return n(o)}}function La(e,t,n){return r;function r(l){return e.enter("reference"),e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),i}function i(l){return l===93?(e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),e.exit("reference"),t):n(l)}}const Fa={name:"labelStartImage",resolveAll:jn.resolveAll,tokenize:za};function za(e,t,n){const r=this;return i;function i(o){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(o),e.exit("labelImageMarker"),l}function l(o){return o===91?(e.enter("labelMarker"),e.consume(o),e.exit("labelMarker"),e.exit("labelImage"),s):n(o)}function s(o){return o===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(o):t(o)}}const Ra={name:"labelStartLink",resolveAll:jn.resolveAll,tokenize:Ma};function Ma(e,t,n){const r=this;return i;function i(s){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(s),e.exit("labelMarker"),e.exit("labelLink"),l}function l(s){return s===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(s):t(s)}}const Wt={name:"lineEnding",tokenize:Oa};function Oa(e,t){return n;function n(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),ee(e,t,"linePrefix")}}const Nt={name:"thematicBreak",tokenize:Ba};function Ba(e,t,n){let r=0,i;return l;function l(u){return e.enter("thematicBreak"),s(u)}function s(u){return i=u,o(u)}function o(u){return u===i?(e.enter("thematicBreakSequence"),c(u)):r>=3&&(u===null||z(u))?(e.exit("thematicBreak"),t(u)):n(u)}function c(u){return u===i?(e.consume(u),r++,c):(e.exit("thematicBreakSequence"),X(u)?ee(e,o,"whitespace")(u):o(u))}}const be={continuation:{tokenize:qa},exit:Ka,name:"list",tokenize:Va},Ha={partial:!0,tokenize:Qa},Ua={partial:!0,tokenize:$a};function Va(e,t,n){const r=this,i=r.events[r.events.length-1];let l=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,s=0;return o;function o(h){const y=r.containerState.type||(h===42||h===43||h===45?"listUnordered":"listOrdered");if(y==="listUnordered"?!r.containerState.marker||h===r.containerState.marker:un(h)){if(r.containerState.type||(r.containerState.type=y,e.enter(y,{_container:!0})),y==="listUnordered")return e.enter("listItemPrefix"),h===42||h===45?e.check(Nt,n,u)(h):u(h);if(!r.interrupt||h===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),c(h)}return n(h)}function c(h){return un(h)&&++s<10?(e.consume(h),c):(!r.interrupt||s<2)&&(r.containerState.marker?h===r.containerState.marker:h===41||h===46)?(e.exit("listItemValue"),u(h)):n(h)}function u(h){return e.enter("listItemMarker"),e.consume(h),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||h,e.check(Lt,r.interrupt?n:d,e.attempt(Ha,g,p))}function d(h){return r.containerState.initialBlankLine=!0,l++,g(h)}function p(h){return X(h)?(e.enter("listItemPrefixWhitespace"),e.consume(h),e.exit("listItemPrefixWhitespace"),g):n(h)}function g(h){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(h)}}function qa(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(Lt,i,l);function i(o){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,ee(e,t,"listItemIndent",r.containerState.size+1)(o)}function l(o){return r.containerState.furtherBlankLines||!X(o)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,s(o)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(Ua,t,s)(o))}function s(o){return r.containerState._closeFlow=!0,r.interrupt=void 0,ee(e,e.attempt(be,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o)}}function $a(e,t,n){const r=this;return ee(e,i,"listItemIndent",r.containerState.size+1);function i(l){const s=r.events[r.events.length-1];return s&&s[1].type==="listItemIndent"&&s[2].sliceSerialize(s[1],!0).length===r.containerState.size?t(l):n(l)}}function Ka(e){e.exit(this.containerState.type)}function Qa(e,t,n){const r=this;return ee(e,i,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function i(l){const s=r.events[r.events.length-1];return!X(l)&&s&&s[1].type==="listItemPrefixWhitespace"?t(l):n(l)}}const dr={name:"setextUnderline",resolveTo:Wa,tokenize:Ya};function Wa(e,t){let n=e.length,r,i,l;for(;n--;)if(e[n][0]==="enter"){if(e[n][1].type==="content"){r=n;break}e[n][1].type==="paragraph"&&(i=n)}else e[n][1].type==="content"&&e.splice(n,1),!l&&e[n][1].type==="definition"&&(l=n);const s={type:"setextHeading",start:{...e[r][1].start},end:{...e[e.length-1][1].end}};return e[i][1].type="setextHeadingText",l?(e.splice(i,0,["enter",s,t]),e.splice(l+1,0,["exit",e[r][1],t]),e[r][1].end={...e[l][1].end}):e[r][1]=s,e.push(["exit",s,t]),e}function Ya(e,t,n){const r=this;let i;return l;function l(u){let d=r.events.length,p;for(;d--;)if(r.events[d][1].type!=="lineEnding"&&r.events[d][1].type!=="linePrefix"&&r.events[d][1].type!=="content"){p=r.events[d][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||p)?(e.enter("setextHeadingLine"),i=u,s(u)):n(u)}function s(u){return e.enter("setextHeadingLineSequence"),o(u)}function o(u){return u===i?(e.consume(u),o):(e.exit("setextHeadingLineSequence"),X(u)?ee(e,c,"lineSuffix")(u):c(u))}function c(u){return u===null||z(u)?(e.exit("setextHeadingLine"),t(u)):n(u)}}const Xa={tokenize:Ga};function Ga(e){const t=this,n=e.attempt(Lt,r,e.attempt(this.parser.constructs.flowInitial,i,ee(e,e.attempt(this.parser.constructs.flow,i,e.attempt(na,i)),"linePrefix")));return n;function r(l){if(l===null){e.consume(l);return}return e.enter("lineEndingBlank"),e.consume(l),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}function i(l){if(l===null){e.consume(l);return}return e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),t.currentConstruct=void 0,n}}const Ja={resolveAll:ui()},Za=oi("string"),eo=oi("text");function oi(e){return{resolveAll:ui(e==="text"?to:void 0),tokenize:t};function t(n){const r=this,i=this.parser.constructs[e],l=n.attempt(i,s,o);return s;function s(d){return u(d)?l(d):o(d)}function o(d){if(d===null){n.consume(d);return}return n.enter("data"),n.consume(d),c}function c(d){return u(d)?(n.exit("data"),l(d)):(n.consume(d),c)}function u(d){if(d===null)return!0;const p=i[d];let g=-1;if(p)for(;++g<p.length;){const h=p[g];if(!h.previous||h.previous.call(r,r.previous))return!0}return!1}}}function ui(e){return t;function t(n,r){let i=-1,l;for(;++i<=n.length;)l===void 0?n[i]&&n[i][1].type==="data"&&(l=i,i++):(!n[i]||n[i][1].type!=="data")&&(i!==l+2&&(n[l][1].end=n[i-1][1].end,n.splice(l+2,i-l-2),i=l+2),l=void 0);return e?e(n,r):n}}function to(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||e[n][1].type==="lineEnding")&&e[n-1][1].type==="data"){const r=e[n-1][1],i=t.sliceStream(r);let l=i.length,s=-1,o=0,c;for(;l--;){const u=i[l];if(typeof u=="string"){for(s=u.length;u.charCodeAt(s-1)===32;)o++,s--;if(s)break;s=-1}else if(u===-2)c=!0,o++;else if(u!==-1){l++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(o=0),o){const u={type:n===e.length||c||o<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?s:r.start._bufferIndex+s,_index:r.start._index+l,line:r.end.line,column:r.end.column-o,offset:r.end.offset-o},end:{...r.end}};r.end={...u.start},r.start.offset===r.end.offset?Object.assign(r,u):(e.splice(n,0,["enter",u,t],["exit",u,t]),n+=2)}n++}return e}const no={42:be,43:be,45:be,48:be,49:be,50:be,51:be,52:be,53:be,54:be,55:be,56:be,57:be,62:ti},ro={91:aa},io={[-2]:Qt,[-1]:Qt,32:Qt},lo={35:pa,42:Nt,45:[dr,Nt],60:xa,61:dr,95:Nt,96:ur,126:ur},so={38:ri,92:ni},ao={[-5]:Wt,[-4]:Wt,[-3]:Wt,33:Fa,38:ri,42:cn,60:[Rs,Ca],91:Ra,92:[da,ni],93:jn,95:cn,96:Xs},oo={null:[cn,Ja]},uo={null:[42,95]},co={null:[]},ho=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:uo,contentInitial:ro,disable:co,document:no,flow:lo,flowInitial:io,insideSpan:oo,string:so,text:ao},Symbol.toStringTag,{value:"Module"}));function po(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const i={},l=[];let s=[],o=[];const c={attempt:B(U),check:B(S),consume:L,enter:_,exit:H,interrupt:B(S,{interrupt:!0})},u={code:null,containerState:{},defineSkip:w,events:[],now:y,parser:e,previous:null,sliceSerialize:g,sliceStream:h,write:p};let d=t.tokenize.call(u,c);return t.resolveAll&&l.push(t),u;function p(I){return s=Ie(s,I),A(),s[s.length-1]!==null?[]:(Q(t,0),u.events=Cn(l,u.events,u),u.events)}function g(I,N){return mo(h(I),N)}function h(I){return fo(s,I)}function y(){const{_bufferIndex:I,_index:N,line:q,column:G,offset:C}=r;return{_bufferIndex:I,_index:N,line:q,column:G,offset:C}}function w(I){i[I.line]=I.column,$()}function A(){let I;for(;r._index<s.length;){const N=s[r._index];if(typeof N=="string")for(I=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===I&&r._bufferIndex<N.length;)v(N.charCodeAt(r._bufferIndex));else v(N)}}function v(I){d=d(I)}function L(I){z(I)?(r.line++,r.column=1,r.offset+=I===-3?2:1,$()):I!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===s[r._index].length&&(r._bufferIndex=-1,r._index++)),u.previous=I}function _(I,N){const q=N||{};return q.type=I,q.start=y(),u.events.push(["enter",q,u]),o.push(q),q}function H(I){const N=o.pop();return N.end=y(),u.events.push(["exit",N,u]),N}function U(I,N){Q(I,N.from)}function S(I,N){N.restore()}function B(I,N){return q;function q(G,C,W){let te,oe,ne,f;return Array.isArray(G)?xe(G):"tokenize"in G?xe([G]):se(G);function se(re){return Ce;function Ce(De){const Be=De!==null&&re[De],Le=De!==null&&re.null,Xe=[...Array.isArray(Be)?Be:Be?[Be]:[],...Array.isArray(Le)?Le:Le?[Le]:[]];return xe(Xe)(De)}}function xe(re){return te=re,oe=0,re.length===0?W:m(re[oe])}function m(re){return Ce;function Ce(De){return f=V(),ne=re,re.partial||(u.currentConstruct=re),re.name&&u.parser.constructs.disable.null.includes(re.name)?Ae():re.tokenize.call(N?Object.assign(Object.create(u),N):u,c,ye,Ae)(De)}}function ye(re){return I(ne,f),C}function Ae(re){return f.restore(),++oe<te.length?m(te[oe]):W}}}function Q(I,N){I.resolveAll&&!l.includes(I)&&l.push(I),I.resolve&&Oe(u.events,N,u.events.length-N,I.resolve(u.events.slice(N),u)),I.resolveTo&&(u.events=I.resolveTo(u.events,u))}function V(){const I=y(),N=u.previous,q=u.currentConstruct,G=u.events.length,C=Array.from(o);return{from:G,restore:W};function W(){r=I,u.previous=N,u.currentConstruct=q,u.events.length=G,o=C,$()}}function $(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}function fo(e,t){const n=t.start._index,r=t.start._bufferIndex,i=t.end._index,l=t.end._bufferIndex;let s;if(n===i)s=[e[n].slice(r,l)];else{if(s=e.slice(n,i),r>-1){const o=s[0];typeof o=="string"?s[0]=o.slice(r):s.shift()}l>0&&s.push(e[i].slice(0,l))}return s}function mo(e,t){let n=-1;const r=[];let i;for(;++n<e.length;){const l=e[n];let s;if(typeof l=="string")s=l;else switch(l){case-5:{s="\r";break}case-4:{s=`
`;break}case-3:{s=`\r
`;break}case-2:{s=t?" ":"	";break}case-1:{if(!t&&i)continue;s=" ";break}default:s=String.fromCharCode(l)}i=l===-2,r.push(s)}return r.join("")}function go(e){const r={constructs:Ss([ho,...(e||{}).extensions||[]]),content:i(_s),defined:[],document:i(As),flow:i(Xa),lazy:{},string:i(Za),text:i(eo)};return r;function i(l){return s;function s(o){return po(r,l,o)}}}function xo(e){for(;!ii(e););return e}const hr=/[\0\t\n\r]/g;function yo(){let e=1,t="",n=!0,r;return i;function i(l,s,o){const c=[];let u,d,p,g,h;for(l=t+(typeof l=="string"?l.toString():new TextDecoder(s||void 0).decode(l)),p=0,t="",n&&(l.charCodeAt(0)===65279&&p++,n=void 0);p<l.length;){if(hr.lastIndex=p,u=hr.exec(l),g=u&&u.index!==void 0?u.index:l.length,h=l.charCodeAt(g),!u){t=l.slice(p);break}if(h===10&&p===g&&r)c.push(-3),r=void 0;else switch(r&&(c.push(-5),r=void 0),p<g&&(c.push(l.slice(p,g)),e+=g-p),h){case 0:{c.push(65533),e++;break}case 9:{for(d=Math.ceil(e/4)*4,c.push(-2);e++<d;)c.push(-1);break}case 10:{c.push(-4),e=1;break}default:r=!0,e=1}p=g+1}return o&&(r&&c.push(-5),t&&c.push(t),c.push(null)),c}}const bo=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function wo(e){return e.replace(bo,ko)}function ko(e,t,n){if(t)return t;if(n.charCodeAt(0)===35){const i=n.charCodeAt(1),l=i===120||i===88;return ei(n.slice(l?2:1),l?16:10)}return vn(n)||e}const ci={}.hasOwnProperty;function So(e,t,n){return typeof t!="string"&&(n=t,t=void 0),vo(n)(xo(go(n).document().write(yo()(e,t,!0))))}function vo(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:l(Fe),autolinkProtocol:V,autolinkEmail:V,atxHeading:l(Ke),blockQuote:l(Le),characterEscape:V,characterReference:V,codeFenced:l(Xe),codeFencedFenceInfo:s,codeFencedFenceMeta:s,codeIndented:l(Xe,s),codeText:l(bt,s),codeTextData:V,data:V,codeFlowValue:V,definition:l(Rt),definitionDestinationString:s,definitionLabelString:s,definitionTitleString:s,emphasis:l(lt),hardBreakEscape:l(Ge),hardBreakTrailing:l(Ge),htmlFlow:l(wt,s),htmlFlowData:V,htmlText:l(wt,s),htmlTextData:V,image:l(Mt),label:s,link:l(Fe),listItem:l(st),listItemValue:g,listOrdered:l(Ue,p),listUnordered:l(Ue),paragraph:l(pe),reference:m,referenceString:s,resourceDestinationString:s,resourceTitleString:s,setextHeading:l(Ke),strong:l(Ot),thematicBreak:l(Ht)},exit:{atxHeading:c(),atxHeadingSequence:U,autolink:c(),autolinkEmail:Be,autolinkProtocol:De,blockQuote:c(),characterEscapeValue:$,characterReferenceMarkerHexadecimal:Ae,characterReferenceMarkerNumeric:Ae,characterReferenceValue:re,characterReference:Ce,codeFenced:c(A),codeFencedFence:w,codeFencedFenceInfo:h,codeFencedFenceMeta:y,codeFlowValue:$,codeIndented:c(v),codeText:c(C),codeTextData:$,data:$,definition:c(),definitionDestinationString:H,definitionLabelString:L,definitionTitleString:_,emphasis:c(),hardBreakEscape:c(N),hardBreakTrailing:c(N),htmlFlow:c(q),htmlFlowData:$,htmlText:c(G),htmlTextData:$,image:c(te),label:ne,labelText:oe,lineEnding:I,link:c(W),listItem:c(),listOrdered:c(),listUnordered:c(),paragraph:c(),referenceString:ye,resourceDestinationString:f,resourceTitleString:se,resource:xe,setextHeading:c(Q),setextHeadingLineSequence:B,setextHeadingText:S,strong:c(),thematicBreak:c()}};di(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(b){let E={type:"root",children:[]};const F={stack:[E],tokenStack:[],config:t,enter:o,exit:u,buffer:s,resume:d,data:n},M=[];let Y=-1;for(;++Y<b.length;)if(b[Y][1].type==="listOrdered"||b[Y][1].type==="listUnordered")if(b[Y][0]==="enter")M.push(Y);else{const ue=M.pop();Y=i(b,ue,Y)}for(Y=-1;++Y<b.length;){const ue=t[b[Y][0]];ci.call(ue,b[Y][1].type)&&ue[b[Y][1].type].call(Object.assign({sliceSerialize:b[Y][2].sliceSerialize},F),b[Y][1])}if(F.tokenStack.length>0){const ue=F.tokenStack[F.tokenStack.length-1];(ue[1]||pr).call(F,void 0,ue[0])}for(E.position={start:qe(b.length>0?b[0][1].start:{line:1,column:1,offset:0}),end:qe(b.length>0?b[b.length-2][1].end:{line:1,column:1,offset:0})},Y=-1;++Y<t.transforms.length;)E=t.transforms[Y](E)||E;return E}function i(b,E,F){let M=E-1,Y=-1,ue=!1,je,fe,He,_e;for(;++M<=F;){const he=b[M];switch(he[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{he[0]==="enter"?Y++:Y--,_e=void 0;break}case"lineEndingBlank":{he[0]==="enter"&&(je&&!_e&&!Y&&!He&&(He=M),_e=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:_e=void 0}if(!Y&&he[0]==="enter"&&he[1].type==="listItemPrefix"||Y===-1&&he[0]==="exit"&&(he[1].type==="listUnordered"||he[1].type==="listOrdered")){if(je){let Ne=M;for(fe=void 0;Ne--;){const D=b[Ne];if(D[1].type==="lineEnding"||D[1].type==="lineEndingBlank"){if(D[0]==="exit")continue;fe&&(b[fe][1].type="lineEndingBlank",ue=!0),D[1].type="lineEnding",fe=Ne}else if(!(D[1].type==="linePrefix"||D[1].type==="blockQuotePrefix"||D[1].type==="blockQuotePrefixWhitespace"||D[1].type==="blockQuoteMarker"||D[1].type==="listItemIndent"))break}He&&(!fe||He<fe)&&(je._spread=!0),je.end=Object.assign({},fe?b[fe][1].start:he[1].end),b.splice(fe||M,0,["exit",je,he[2]]),M++,F++}if(he[1].type==="listItemPrefix"){const Ne={type:"listItem",_spread:!1,start:Object.assign({},he[1].start),end:void 0};je=Ne,b.splice(M,0,["enter",Ne,he[2]]),M++,F++,He=void 0,_e=!0}}}return b[E][1]._spread=ue,F}function l(b,E){return F;function F(M){o.call(this,b(M),M),E&&E.call(this,M)}}function s(){this.stack.push({type:"fragment",children:[]})}function o(b,E,F){this.stack[this.stack.length-1].children.push(b),this.stack.push(b),this.tokenStack.push([E,F||void 0]),b.position={start:qe(E.start),end:void 0}}function c(b){return E;function E(F){b&&b.call(this,F),u.call(this,F)}}function u(b,E){const F=this.stack.pop(),M=this.tokenStack.pop();if(M)M[0].type!==b.type&&(E?E.call(this,b,M[0]):(M[1]||pr).call(this,b,M[0]));else throw new Error("Cannot close `"+b.type+"` ("+ht({start:b.start,end:b.end})+"): it’s not open");F.position.end=qe(b.end)}function d(){return ws(this.stack.pop())}function p(){this.data.expectingFirstListItemValue=!0}function g(b){if(this.data.expectingFirstListItemValue){const E=this.stack[this.stack.length-2];E.start=Number.parseInt(this.sliceSerialize(b),10),this.data.expectingFirstListItemValue=void 0}}function h(){const b=this.resume(),E=this.stack[this.stack.length-1];E.lang=b}function y(){const b=this.resume(),E=this.stack[this.stack.length-1];E.meta=b}function w(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function A(){const b=this.resume(),E=this.stack[this.stack.length-1];E.value=b.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function v(){const b=this.resume(),E=this.stack[this.stack.length-1];E.value=b.replace(/(\r?\n|\r)$/g,"")}function L(b){const E=this.resume(),F=this.stack[this.stack.length-1];F.label=E,F.identifier=tt(this.sliceSerialize(b)).toLowerCase()}function _(){const b=this.resume(),E=this.stack[this.stack.length-1];E.title=b}function H(){const b=this.resume(),E=this.stack[this.stack.length-1];E.url=b}function U(b){const E=this.stack[this.stack.length-1];if(!E.depth){const F=this.sliceSerialize(b).length;E.depth=F}}function S(){this.data.setextHeadingSlurpLineEnding=!0}function B(b){const E=this.stack[this.stack.length-1];E.depth=this.sliceSerialize(b).codePointAt(0)===61?1:2}function Q(){this.data.setextHeadingSlurpLineEnding=void 0}function V(b){const F=this.stack[this.stack.length-1].children;let M=F[F.length-1];(!M||M.type!=="text")&&(M=Bt(),M.position={start:qe(b.start),end:void 0},F.push(M)),this.stack.push(M)}function $(b){const E=this.stack.pop();E.value+=this.sliceSerialize(b),E.position.end=qe(b.end)}function I(b){const E=this.stack[this.stack.length-1];if(this.data.atHardBreak){const F=E.children[E.children.length-1];F.position.end=qe(b.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(E.type)&&(V.call(this,b),$.call(this,b))}function N(){this.data.atHardBreak=!0}function q(){const b=this.resume(),E=this.stack[this.stack.length-1];E.value=b}function G(){const b=this.resume(),E=this.stack[this.stack.length-1];E.value=b}function C(){const b=this.resume(),E=this.stack[this.stack.length-1];E.value=b}function W(){const b=this.stack[this.stack.length-1];if(this.data.inReference){const E=this.data.referenceType||"shortcut";b.type+="Reference",b.referenceType=E,delete b.url,delete b.title}else delete b.identifier,delete b.label;this.data.referenceType=void 0}function te(){const b=this.stack[this.stack.length-1];if(this.data.inReference){const E=this.data.referenceType||"shortcut";b.type+="Reference",b.referenceType=E,delete b.url,delete b.title}else delete b.identifier,delete b.label;this.data.referenceType=void 0}function oe(b){const E=this.sliceSerialize(b),F=this.stack[this.stack.length-2];F.label=wo(E),F.identifier=tt(E).toLowerCase()}function ne(){const b=this.stack[this.stack.length-1],E=this.resume(),F=this.stack[this.stack.length-1];if(this.data.inReference=!0,F.type==="link"){const M=b.children;F.children=M}else F.alt=E}function f(){const b=this.resume(),E=this.stack[this.stack.length-1];E.url=b}function se(){const b=this.resume(),E=this.stack[this.stack.length-1];E.title=b}function xe(){this.data.inReference=void 0}function m(){this.data.referenceType="collapsed"}function ye(b){const E=this.resume(),F=this.stack[this.stack.length-1];F.label=E,F.identifier=tt(this.sliceSerialize(b)).toLowerCase(),this.data.referenceType="full"}function Ae(b){this.data.characterReferenceType=b.type}function re(b){const E=this.sliceSerialize(b),F=this.data.characterReferenceType;let M;F?(M=ei(E,F==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):M=vn(E);const Y=this.stack[this.stack.length-1];Y.value+=M}function Ce(b){const E=this.stack.pop();E.position.end=qe(b.end)}function De(b){$.call(this,b);const E=this.stack[this.stack.length-1];E.url=this.sliceSerialize(b)}function Be(b){$.call(this,b);const E=this.stack[this.stack.length-1];E.url="mailto:"+this.sliceSerialize(b)}function Le(){return{type:"blockquote",children:[]}}function Xe(){return{type:"code",lang:null,meta:null,value:""}}function bt(){return{type:"inlineCode",value:""}}function Rt(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function lt(){return{type:"emphasis",children:[]}}function Ke(){return{type:"heading",depth:0,children:[]}}function Ge(){return{type:"break"}}function wt(){return{type:"html",value:""}}function Mt(){return{type:"image",title:null,url:"",alt:null}}function Fe(){return{type:"link",title:null,url:"",children:[]}}function Ue(b){return{type:"list",ordered:b.type==="listOrdered",start:null,spread:b._spread,children:[]}}function st(b){return{type:"listItem",spread:b._spread,checked:null,children:[]}}function pe(){return{type:"paragraph",children:[]}}function Ot(){return{type:"strong",children:[]}}function Bt(){return{type:"text",value:""}}function Ht(){return{type:"thematicBreak"}}}function qe(e){return{line:e.line,column:e.column,offset:e.offset}}function di(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?di(e,r):Co(e,r)}}function Co(e,t){let n;for(n in t)if(ci.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function pr(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+ht({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+ht({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+ht({start:t.start,end:t.end})+") is still open")}function jo(e){const t=this;t.parser=n;function n(r){return So(r,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function No(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)}function Eo(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:`
`}]}function Po(e,t){const n=t.value?t.value+`
`:"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i=e.applyData(t,i),i={type:"element",tagName:"pre",properties:{},children:[i]},e.patch(t,i),i}function Io(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function _o(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function To(e,t){const n=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),i=it(r.toLowerCase()),l=e.footnoteOrder.indexOf(r);let s,o=e.footnoteCounts.get(r);o===void 0?(o=0,e.footnoteOrder.push(r),s=e.footnoteOrder.length):s=l+1,o+=1,e.footnoteCounts.set(r,o);const c={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+i,id:n+"fnref-"+i+(o>1?"-"+o:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(s)}]};e.patch(t,c);const u={type:"element",tagName:"sup",properties:{},children:[c]};return e.patch(t,u),e.applyData(t,u)}function Ao(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function Do(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}}function hi(e,t){const n=t.referenceType;let r="]";if(n==="collapsed"?r+="[]":n==="full"&&(r+="["+(t.label||t.identifier)+"]"),t.type==="imageReference")return[{type:"text",value:"!["+t.alt+r}];const i=e.all(t),l=i[0];l&&l.type==="text"?l.value="["+l.value:i.unshift({type:"text",value:"["});const s=i[i.length-1];return s&&s.type==="text"?s.value+=r:i.push({type:"text",value:r}),i}function Lo(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return hi(e,t);const i={src:it(r.url||""),alt:t.alt};r.title!==null&&r.title!==void 0&&(i.title=r.title);const l={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,l),e.applyData(t,l)}function Fo(e,t){const n={src:it(t.url)};t.alt!==null&&t.alt!==void 0&&(n.alt=t.alt),t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)}function zo(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)}function Ro(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return hi(e,t);const i={href:it(r.url||"")};r.title!==null&&r.title!==void 0&&(i.title=r.title);const l={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,l),e.applyData(t,l)}function Mo(e,t){const n={href:it(t.url)};t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function Oo(e,t,n){const r=e.all(t),i=n?Bo(n):pi(t),l={},s=[];if(typeof t.checked=="boolean"){const d=r[0];let p;d&&d.type==="element"&&d.tagName==="p"?p=d:(p={type:"element",tagName:"p",properties:{},children:[]},r.unshift(p)),p.children.length>0&&p.children.unshift({type:"text",value:" "}),p.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let o=-1;for(;++o<r.length;){const d=r[o];(i||o!==0||d.type!=="element"||d.tagName!=="p")&&s.push({type:"text",value:`
`}),d.type==="element"&&d.tagName==="p"&&!i?s.push(...d.children):s.push(d)}const c=r[r.length-1];c&&(i||c.type!=="element"||c.tagName!=="p")&&s.push({type:"text",value:`
`});const u={type:"element",tagName:"li",properties:l,children:s};return e.patch(t,u),e.applyData(t,u)}function Bo(e){let t=!1;if(e.type==="list"){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=pi(n[r])}return t}function pi(e){const t=e.spread;return t??e.children.length>1}function Ho(e,t){const n={},r=e.all(t);let i=-1;for(typeof t.start=="number"&&t.start!==1&&(n.start=t.start);++i<r.length;){const s=r[i];if(s.type==="element"&&s.tagName==="li"&&s.properties&&Array.isArray(s.properties.className)&&s.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const l={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,l),e.applyData(t,l)}function Uo(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function Vo(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)}function qo(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function $o(e,t){const n=e.all(t),r=n.shift(),i=[];if(r){const s={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],s),i.push(s)}if(n.length>0){const s={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},o=bn(t.children[1]),c=Qr(t.children[t.children.length-1]);o&&c&&(s.position={start:o,end:c}),i.push(s)}const l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,l),e.applyData(t,l)}function Ko(e,t,n){const r=n?n.children:void 0,l=(r?r.indexOf(t):1)===0?"th":"td",s=n&&n.type==="table"?n.align:void 0,o=s?s.length:t.children.length;let c=-1;const u=[];for(;++c<o;){const p=t.children[c],g={},h=s?s[c]:void 0;h&&(g.align=h);let y={type:"element",tagName:l,properties:g,children:[]};p&&(y.children=e.all(p),e.patch(p,y),y=e.applyData(p,y)),u.push(y)}const d={type:"element",tagName:"tr",properties:{},children:e.wrap(u,!0)};return e.patch(t,d),e.applyData(t,d)}function Qo(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}const fr=9,mr=32;function Wo(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),i=0;const l=[];for(;r;)l.push(gr(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return l.push(gr(t.slice(i),i>0,!1)),l.join("")}function gr(e,t,n){let r=0,i=e.length;if(t){let l=e.codePointAt(r);for(;l===fr||l===mr;)r++,l=e.codePointAt(r)}if(n){let l=e.codePointAt(i-1);for(;l===fr||l===mr;)i--,l=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}function Yo(e,t){const n={type:"text",value:Wo(String(t.value))};return e.patch(t,n),e.applyData(t,n)}function Xo(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)}const Go={blockquote:No,break:Eo,code:Po,delete:Io,emphasis:_o,footnoteReference:To,heading:Ao,html:Do,imageReference:Lo,image:Fo,inlineCode:zo,linkReference:Ro,link:Mo,listItem:Oo,list:Ho,paragraph:Uo,root:Vo,strong:qo,table:$o,tableCell:Qo,tableRow:Ko,text:Yo,thematicBreak:Xo,toml:vt,yaml:vt,definition:vt,footnoteDefinition:vt};function vt(){}const fi=-1,Ft=0,ft=1,_t=2,Nn=3,En=4,Pn=5,In=6,mi=7,gi=8,xr=typeof self=="object"?self:globalThis,Jo=(e,t)=>{const n=(i,l)=>(e.set(l,i),i),r=i=>{if(e.has(i))return e.get(i);const[l,s]=t[i];switch(l){case Ft:case fi:return n(s,i);case ft:{const o=n([],i);for(const c of s)o.push(r(c));return o}case _t:{const o=n({},i);for(const[c,u]of s)o[r(c)]=r(u);return o}case Nn:return n(new Date(s),i);case En:{const{source:o,flags:c}=s;return n(new RegExp(o,c),i)}case Pn:{const o=n(new Map,i);for(const[c,u]of s)o.set(r(c),r(u));return o}case In:{const o=n(new Set,i);for(const c of s)o.add(r(c));return o}case mi:{const{name:o,message:c}=s;return n(new xr[o](c),i)}case gi:return n(BigInt(s),i);case"BigInt":return n(Object(BigInt(s)),i);case"ArrayBuffer":return n(new Uint8Array(s).buffer,s);case"DataView":{const{buffer:o}=new Uint8Array(s);return n(new DataView(o),s)}}return n(new xr[l](s),i)};return r},yr=e=>Jo(new Map,e)(0),Je="",{toString:Zo}={},{keys:eu}=Object,dt=e=>{const t=typeof e;if(t!=="object"||!e)return[Ft,t];const n=Zo.call(e).slice(8,-1);switch(n){case"Array":return[ft,Je];case"Object":return[_t,Je];case"Date":return[Nn,Je];case"RegExp":return[En,Je];case"Map":return[Pn,Je];case"Set":return[In,Je];case"DataView":return[ft,n]}return n.includes("Array")?[ft,n]:n.includes("Error")?[mi,n]:[_t,n]},Ct=([e,t])=>e===Ft&&(t==="function"||t==="symbol"),tu=(e,t,n,r)=>{const i=(s,o)=>{const c=r.push(s)-1;return n.set(o,c),c},l=s=>{if(n.has(s))return n.get(s);let[o,c]=dt(s);switch(o){case Ft:{let d=s;switch(c){case"bigint":o=gi,d=s.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+c);d=null;break;case"undefined":return i([fi],s)}return i([o,d],s)}case ft:{if(c){let g=s;return c==="DataView"?g=new Uint8Array(s.buffer):c==="ArrayBuffer"&&(g=new Uint8Array(s)),i([c,[...g]],s)}const d=[],p=i([o,d],s);for(const g of s)d.push(l(g));return p}case _t:{if(c)switch(c){case"BigInt":return i([c,s.toString()],s);case"Boolean":case"Number":case"String":return i([c,s.valueOf()],s)}if(t&&"toJSON"in s)return l(s.toJSON());const d=[],p=i([o,d],s);for(const g of eu(s))(e||!Ct(dt(s[g])))&&d.push([l(g),l(s[g])]);return p}case Nn:return i([o,s.toISOString()],s);case En:{const{source:d,flags:p}=s;return i([o,{source:d,flags:p}],s)}case Pn:{const d=[],p=i([o,d],s);for(const[g,h]of s)(e||!(Ct(dt(g))||Ct(dt(h))))&&d.push([l(g),l(h)]);return p}case In:{const d=[],p=i([o,d],s);for(const g of s)(e||!Ct(dt(g)))&&d.push(l(g));return p}}const{message:u}=s;return i([o,{name:c,message:u}],s)};return l},br=(e,{json:t,lossy:n}={})=>{const r=[];return tu(!(t||n),!!t,new Map,r)(e),r},Tt=typeof structuredClone=="function"?(e,t)=>t&&("json"in t||"lossy"in t)?yr(br(e,t)):structuredClone(e):(e,t)=>yr(br(e,t));function nu(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function ru(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}function iu(e){const t=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||nu,r=e.options.footnoteBackLabel||ru,i=e.options.footnoteLabel||"Footnotes",l=e.options.footnoteLabelTagName||"h2",s=e.options.footnoteLabelProperties||{className:["sr-only"]},o=[];let c=-1;for(;++c<e.footnoteOrder.length;){const u=e.footnoteById.get(e.footnoteOrder[c]);if(!u)continue;const d=e.all(u),p=String(u.identifier).toUpperCase(),g=it(p.toLowerCase());let h=0;const y=[],w=e.footnoteCounts.get(p);for(;w!==void 0&&++h<=w;){y.length>0&&y.push({type:"text",value:" "});let L=typeof n=="string"?n:n(c,h);typeof L=="string"&&(L={type:"text",value:L}),y.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+g+(h>1?"-"+h:""),dataFootnoteBackref:"",ariaLabel:typeof r=="string"?r:r(c,h),className:["data-footnote-backref"]},children:Array.isArray(L)?L:[L]})}const A=d[d.length-1];if(A&&A.type==="element"&&A.tagName==="p"){const L=A.children[A.children.length-1];L&&L.type==="text"?L.value+=" ":A.children.push({type:"text",value:" "}),A.children.push(...y)}else d.push(...y);const v={type:"element",tagName:"li",properties:{id:t+"fn-"+g},children:e.wrap(d,!0)};e.patch(u,v),o.push(v)}if(o.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:l,properties:{...Tt(s),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(o,!0)},{type:"text",value:`
`}]}}const xi=function(e){if(e==null)return ou;if(typeof e=="function")return zt(e);if(typeof e=="object")return Array.isArray(e)?lu(e):su(e);if(typeof e=="string")return au(e);throw new Error("Expected function, string, or object as test")};function lu(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=xi(e[n]);return zt(r);function r(...i){let l=-1;for(;++l<t.length;)if(t[l].apply(this,i))return!0;return!1}}function su(e){const t=e;return zt(n);function n(r){const i=r;let l;for(l in e)if(i[l]!==t[l])return!1;return!0}}function au(e){return zt(t);function t(n){return n&&n.type===e}}function zt(e){return t;function t(n,r,i){return!!(uu(n)&&e.call(this,n,typeof r=="number"?r:void 0,i||void 0))}}function ou(){return!0}function uu(e){return e!==null&&typeof e=="object"&&"type"in e}const yi=[],cu=!0,wr=!1,du="skip";function hu(e,t,n,r){let i;typeof t=="function"&&typeof n!="function"?(r=n,n=t):i=t;const l=xi(i),s=r?-1:1;o(e,void 0,[])();function o(c,u,d){const p=c&&typeof c=="object"?c:{};if(typeof p.type=="string"){const h=typeof p.tagName=="string"?p.tagName:typeof p.name=="string"?p.name:void 0;Object.defineProperty(g,"name",{value:"node ("+(c.type+(h?"<"+h+">":""))+")"})}return g;function g(){let h=yi,y,w,A;if((!t||l(c,u,d[d.length-1]||void 0))&&(h=pu(n(c,d)),h[0]===wr))return h;if("children"in c&&c.children){const v=c;if(v.children&&h[0]!==du)for(w=(r?v.children.length:-1)+s,A=d.concat(v);w>-1&&w<v.children.length;){const L=v.children[w];if(y=o(L,w,A)(),y[0]===wr)return y;w=typeof y[1]=="number"?y[1]:w+s}}return h}}}function pu(e){return Array.isArray(e)?e:typeof e=="number"?[cu,e]:e==null?yi:[e]}function bi(e,t,n,r){let i,l,s;typeof t=="function"?(l=void 0,s=t,i=n):(l=t,s=n,i=r),hu(e,l,o,i);function o(c,u){const d=u[u.length-1],p=d?d.children.indexOf(c):void 0;return s(c,p,d)}}const dn={}.hasOwnProperty,fu={};function mu(e,t){const n=t||fu,r=new Map,i=new Map,l=new Map,s={...Go,...n.handlers},o={all:u,applyData:xu,definitionById:r,footnoteById:i,footnoteCounts:l,footnoteOrder:[],handlers:s,one:c,options:n,patch:gu,wrap:bu};return bi(e,function(d){if(d.type==="definition"||d.type==="footnoteDefinition"){const p=d.type==="definition"?r:i,g=String(d.identifier).toUpperCase();p.has(g)||p.set(g,d)}}),o;function c(d,p){const g=d.type,h=o.handlers[g];if(dn.call(o.handlers,g)&&h)return h(o,d,p);if(o.options.passThrough&&o.options.passThrough.includes(g)){if("children"in d){const{children:w,...A}=d,v=Tt(A);return v.children=o.all(d),v}return Tt(d)}return(o.options.unknownHandler||yu)(o,d,p)}function u(d){const p=[];if("children"in d){const g=d.children;let h=-1;for(;++h<g.length;){const y=o.one(g[h],d);if(y){if(h&&g[h-1].type==="break"&&(!Array.isArray(y)&&y.type==="text"&&(y.value=kr(y.value)),!Array.isArray(y)&&y.type==="element")){const w=y.children[0];w&&w.type==="text"&&(w.value=kr(w.value))}Array.isArray(y)?p.push(...y):p.push(y)}}}return p}}function gu(e,t){e.position&&(t.position=Jl(e))}function xu(e,t){let n=t;if(e&&e.data){const r=e.data.hName,i=e.data.hChildren,l=e.data.hProperties;if(typeof r=="string")if(n.type==="element")n.tagName=r;else{const s="children"in n?n.children:[n];n={type:"element",tagName:r,properties:{},children:s}}n.type==="element"&&l&&Object.assign(n.properties,Tt(l)),"children"in n&&n.children&&i!==null&&i!==void 0&&(n.children=i)}return n}function yu(e,t){const n=t.data||{},r="value"in t&&!(dn.call(n,"hProperties")||dn.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function bu(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:`
`});++r<e.length;)r&&n.push({type:"text",value:`
`}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:`
`}),n}function kr(e){let t=0,n=e.charCodeAt(t);for(;n===9||n===32;)t++,n=e.charCodeAt(t);return e.slice(t)}function Sr(e,t){const n=mu(e,t),r=n.one(e,void 0),i=iu(n),l=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&l.children.push({type:"text",value:`
`},i),l}function wu(e,t){return e&&"run"in e?async function(n,r){const i=Sr(n,{file:r,...t});await e.run(i,r)}:function(n,r){return Sr(n,{file:r,...e||t})}}function vr(e){if(e)throw e}var Et=Object.prototype.hasOwnProperty,wi=Object.prototype.toString,Cr=Object.defineProperty,jr=Object.getOwnPropertyDescriptor,Nr=function(t){return typeof Array.isArray=="function"?Array.isArray(t):wi.call(t)==="[object Array]"},Er=function(t){if(!t||wi.call(t)!=="[object Object]")return!1;var n=Et.call(t,"constructor"),r=t.constructor&&t.constructor.prototype&&Et.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!n&&!r)return!1;var i;for(i in t);return typeof i>"u"||Et.call(t,i)},Pr=function(t,n){Cr&&n.name==="__proto__"?Cr(t,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):t[n.name]=n.newValue},Ir=function(t,n){if(n==="__proto__")if(Et.call(t,n)){if(jr)return jr(t,n).value}else return;return t[n]},ku=function e(){var t,n,r,i,l,s,o=arguments[0],c=1,u=arguments.length,d=!1;for(typeof o=="boolean"&&(d=o,o=arguments[1]||{},c=2),(o==null||typeof o!="object"&&typeof o!="function")&&(o={});c<u;++c)if(t=arguments[c],t!=null)for(n in t)r=Ir(o,n),i=Ir(t,n),o!==i&&(d&&i&&(Er(i)||(l=Nr(i)))?(l?(l=!1,s=r&&Nr(r)?r:[]):s=r&&Er(r)?r:{},Pr(o,{name:n,newValue:e(d,s,i)})):typeof i<"u"&&Pr(o,{name:n,newValue:i}));return o};const Yt=zr(ku);function hn(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function Su(){const e=[],t={run:n,use:r};return t;function n(...i){let l=-1;const s=i.pop();if(typeof s!="function")throw new TypeError("Expected function as last argument, not "+s);o(null,...i);function o(c,...u){const d=e[++l];let p=-1;if(c){s(c);return}for(;++p<i.length;)(u[p]===null||u[p]===void 0)&&(u[p]=i[p]);i=u,d?vu(d,o)(...u):s(null,...u)}}function r(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return e.push(i),t}}function vu(e,t){let n;return r;function r(...s){const o=e.length>s.length;let c;o&&s.push(i);try{c=e.apply(this,s)}catch(u){const d=u;if(o&&n)throw d;return i(d)}o||(c&&c.then&&typeof c.then=="function"?c.then(l,i):c instanceof Error?i(c):l(c))}function i(s,...o){n||(n=!0,t(s,...o))}function l(s){i(null,s)}}const Re={basename:Cu,dirname:ju,extname:Nu,join:Eu,sep:"/"};function Cu(e,t){if(t!==void 0&&typeof t!="string")throw new TypeError('"ext" argument must be a string');yt(e);let n=0,r=-1,i=e.length,l;if(t===void 0||t.length===0||t.length>e.length){for(;i--;)if(e.codePointAt(i)===47){if(l){n=i+1;break}}else r<0&&(l=!0,r=i+1);return r<0?"":e.slice(n,r)}if(t===e)return"";let s=-1,o=t.length-1;for(;i--;)if(e.codePointAt(i)===47){if(l){n=i+1;break}}else s<0&&(l=!0,s=i+1),o>-1&&(e.codePointAt(i)===t.codePointAt(o--)?o<0&&(r=i):(o=-1,r=s));return n===r?r=s:r<0&&(r=e.length),e.slice(n,r)}function ju(e){if(yt(e),e.length===0)return".";let t=-1,n=e.length,r;for(;--n;)if(e.codePointAt(n)===47){if(r){t=n;break}}else r||(r=!0);return t<0?e.codePointAt(0)===47?"/":".":t===1&&e.codePointAt(0)===47?"//":e.slice(0,t)}function Nu(e){yt(e);let t=e.length,n=-1,r=0,i=-1,l=0,s;for(;t--;){const o=e.codePointAt(t);if(o===47){if(s){r=t+1;break}continue}n<0&&(s=!0,n=t+1),o===46?i<0?i=t:l!==1&&(l=1):i>-1&&(l=-1)}return i<0||n<0||l===0||l===1&&i===n-1&&i===r+1?"":e.slice(i,n)}function Eu(...e){let t=-1,n;for(;++t<e.length;)yt(e[t]),e[t]&&(n=n===void 0?e[t]:n+"/"+e[t]);return n===void 0?".":Pu(n)}function Pu(e){yt(e);const t=e.codePointAt(0)===47;let n=Iu(e,!t);return n.length===0&&!t&&(n="."),n.length>0&&e.codePointAt(e.length-1)===47&&(n+="/"),t?"/"+n:n}function Iu(e,t){let n="",r=0,i=-1,l=0,s=-1,o,c;for(;++s<=e.length;){if(s<e.length)o=e.codePointAt(s);else{if(o===47)break;o=47}if(o===47){if(!(i===s-1||l===1))if(i!==s-1&&l===2){if(n.length<2||r!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(c=n.lastIndexOf("/"),c!==n.length-1){c<0?(n="",r=0):(n=n.slice(0,c),r=n.length-1-n.lastIndexOf("/")),i=s,l=0;continue}}else if(n.length>0){n="",r=0,i=s,l=0;continue}}t&&(n=n.length>0?n+"/..":"..",r=2)}else n.length>0?n+="/"+e.slice(i+1,s):n=e.slice(i+1,s),r=s-i-1;i=s,l=0}else o===46&&l>-1?l++:l=-1}return n}function yt(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const _u={cwd:Tu};function Tu(){return"/"}function pn(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}function Au(e){if(typeof e=="string")e=new URL(e);else if(!pn(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if(e.protocol!=="file:"){const t=new TypeError("The URL must be of scheme file");throw t.code="ERR_INVALID_URL_SCHEME",t}return Du(e)}function Du(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const t=e.pathname;let n=-1;for(;++n<t.length;)if(t.codePointAt(n)===37&&t.codePointAt(n+1)===50){const r=t.codePointAt(n+2);if(r===70||r===102){const i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(t)}const Xt=["history","path","basename","stem","extname","dirname"];class ki{constructor(t){let n;t?pn(t)?n={path:t}:typeof t=="string"||Lu(t)?n={value:t}:n=t:n={},this.cwd="cwd"in n?"":_u.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<Xt.length;){const l=Xt[r];l in n&&n[l]!==void 0&&n[l]!==null&&(this[l]=l==="history"?[...n[l]]:n[l])}let i;for(i in n)Xt.includes(i)||(this[i]=n[i])}get basename(){return typeof this.path=="string"?Re.basename(this.path):void 0}set basename(t){Jt(t,"basename"),Gt(t,"basename"),this.path=Re.join(this.dirname||"",t)}get dirname(){return typeof this.path=="string"?Re.dirname(this.path):void 0}set dirname(t){_r(this.basename,"dirname"),this.path=Re.join(t||"",this.basename)}get extname(){return typeof this.path=="string"?Re.extname(this.path):void 0}set extname(t){if(Gt(t,"extname"),_r(this.dirname,"extname"),t){if(t.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Re.join(this.dirname,this.stem+(t||""))}get path(){return this.history[this.history.length-1]}set path(t){pn(t)&&(t=Au(t)),Jt(t,"path"),this.path!==t&&this.history.push(t)}get stem(){return typeof this.path=="string"?Re.basename(this.path,this.extname):void 0}set stem(t){Jt(t,"stem"),Gt(t,"stem"),this.path=Re.join(this.dirname||"",t+(this.extname||""))}fail(t,n,r){const i=this.message(t,n,r);throw i.fatal=!0,i}info(t,n,r){const i=this.message(t,n,r);return i.fatal=void 0,i}message(t,n,r){const i=new me(t,n,r);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(t){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(t||void 0).decode(this.value)}}function Gt(e,t){if(e&&e.includes(Re.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+Re.sep+"`")}function Jt(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function _r(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}function Lu(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const Fu=function(e){const r=this.constructor.prototype,i=r[e],l=function(){return i.apply(l,arguments)};return Object.setPrototypeOf(l,r),l},zu={}.hasOwnProperty;class _n extends Fu{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=Su()}copy(){const t=new _n;let n=-1;for(;++n<this.attachers.length;){const r=this.attachers[n];t.use(...r)}return t.data(Yt(!0,{},this.namespace)),t}data(t,n){return typeof t=="string"?arguments.length===2?(tn("data",this.frozen),this.namespace[t]=n,this):zu.call(this.namespace,t)&&this.namespace[t]||void 0:t?(tn("data",this.frozen),this.namespace=t,this):this.namespace}freeze(){if(this.frozen)return this;const t=this;for(;++this.freezeIndex<this.attachers.length;){const[n,...r]=this.attachers[this.freezeIndex];if(r[0]===!1)continue;r[0]===!0&&(r[0]=void 0);const i=n.call(t,...r);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(t){this.freeze();const n=jt(t),r=this.parser||this.Parser;return Zt("parse",r),r(String(n),n)}process(t,n){const r=this;return this.freeze(),Zt("process",this.parser||this.Parser),en("process",this.compiler||this.Compiler),n?i(void 0,n):new Promise(i);function i(l,s){const o=jt(t),c=r.parse(o);r.run(c,o,function(d,p,g){if(d||!p||!g)return u(d);const h=p,y=r.stringify(h,g);Ou(y)?g.value=y:g.result=y,u(d,g)});function u(d,p){d||!p?s(d):l?l(p):n(void 0,p)}}}processSync(t){let n=!1,r;return this.freeze(),Zt("processSync",this.parser||this.Parser),en("processSync",this.compiler||this.Compiler),this.process(t,i),Ar("processSync","process",n),r;function i(l,s){n=!0,vr(l),r=s}}run(t,n,r){Tr(t),this.freeze();const i=this.transformers;return!r&&typeof n=="function"&&(r=n,n=void 0),r?l(void 0,r):new Promise(l);function l(s,o){const c=jt(n);i.run(t,c,u);function u(d,p,g){const h=p||t;d?o(d):s?s(h):r(void 0,h,g)}}}runSync(t,n){let r=!1,i;return this.run(t,n,l),Ar("runSync","run",r),i;function l(s,o){vr(s),i=o,r=!0}}stringify(t,n){this.freeze();const r=jt(n),i=this.compiler||this.Compiler;return en("stringify",i),Tr(t),i(t,r)}use(t,...n){const r=this.attachers,i=this.namespace;if(tn("use",this.frozen),t!=null)if(typeof t=="function")c(t,n);else if(typeof t=="object")Array.isArray(t)?o(t):s(t);else throw new TypeError("Expected usable value, not `"+t+"`");return this;function l(u){if(typeof u=="function")c(u,[]);else if(typeof u=="object")if(Array.isArray(u)){const[d,...p]=u;c(d,p)}else s(u);else throw new TypeError("Expected usable value, not `"+u+"`")}function s(u){if(!("plugins"in u)&&!("settings"in u))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");o(u.plugins),u.settings&&(i.settings=Yt(!0,i.settings,u.settings))}function o(u){let d=-1;if(u!=null)if(Array.isArray(u))for(;++d<u.length;){const p=u[d];l(p)}else throw new TypeError("Expected a list of plugins, not `"+u+"`")}function c(u,d){let p=-1,g=-1;for(;++p<r.length;)if(r[p][0]===u){g=p;break}if(g===-1)r.push([u,...d]);else if(d.length>0){let[h,...y]=d;const w=r[g][1];hn(w)&&hn(h)&&(h=Yt(!0,w,h)),r[g]=[u,h,...y]}}}}const Ru=new _n().freeze();function Zt(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function en(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function tn(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Tr(e){if(!hn(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Ar(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function jt(e){return Mu(e)?e:new ki(e)}function Mu(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function Ou(e){return typeof e=="string"||Bu(e)}function Bu(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const Hu="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",Dr=[],Lr={allowDangerousHtml:!0},Uu=/^(https?|ircs?|mailto|xmpp)$/i,Vu=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function qu(e){const t=$u(e),n=Ku(e);return Qu(t.runSync(t.parse(n),n),e)}function $u(e){const t=e.rehypePlugins||Dr,n=e.remarkPlugins||Dr,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...Lr}:Lr;return Ru().use(jo).use(n).use(wu,r).use(t)}function Ku(e){const t=e.children||"",n=new ki;return typeof t=="string"&&(n.value=t),n}function Qu(e,t){const n=t.allowedElements,r=t.allowElement,i=t.components,l=t.disallowedElements,s=t.skipHtml,o=t.unwrapDisallowed,c=t.urlTransform||Wu;for(const d of Vu)Object.hasOwn(t,d.from)&&(""+d.from+(d.to?"use `"+d.to+"` instead":"remove it")+Hu+d.id,void 0);return bi(e,u),rs(e,{Fragment:a.Fragment,components:i,ignoreInvalidStyle:!0,jsx:a.jsx,jsxs:a.jsxs,passKeys:!0,passNode:!0});function u(d,p,g){if(d.type==="raw"&&g&&typeof p=="number")return s?g.children.splice(p,1):g.children[p]={type:"text",value:d.value},p;if(d.type==="element"){let h;for(h in Kt)if(Object.hasOwn(Kt,h)&&Object.hasOwn(d.properties,h)){const y=d.properties[h],w=Kt[h];(w===null||w.includes(d.tagName))&&(d.properties[h]=c(String(y||""),h,d))}}if(d.type==="element"){let h=n?!n.includes(d.tagName):l?l.includes(d.tagName):!1;if(!h&&r&&typeof p=="number"&&(h=!r(d,p,g)),h&&g&&typeof p=="number")return o&&d.children?g.children.splice(p,1,...d.children):g.children.splice(p,1),p}}}function Wu(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return t===-1||i!==-1&&t>i||n!==-1&&t>n||r!==-1&&t>r||Uu.test(e.slice(0,t))?e:""}const Yu=({customer:e,onApplyPoints:t,disabled:n=!1,totalAmount:r})=>{const{settings:i,getCustomerProfile:l,calculateDiscount:s,calculatePoints:o}=fn(),c=At(),[u,d]=T.useState(null),[p,g]=T.useState(!1),[h,y]=T.useState(null),[w,A]=T.useState(0),[v,L]=T.useState(0),[_,H]=T.useState(0),[U,S]=T.useState(!1);T.useEffect(()=>{(async()=>{if(!e){d(null);return}g(!0);try{const N=await l(e.id);d(N),A(0),L(0)}catch(N){console.error("Error fetching customer profile:",N)}finally{g(!1)}})()},[e,l]),T.useEffect(()=>{(async()=>{if(!e||!(i!=null&&i.is_enabled)||r<=0){H(0);return}try{const N=await o(r,e.id);H(N)}catch(N){console.error("Error calculating potential points:",N),H(0)}})()},[e,r,o,i]);const B=I=>{const N=parseInt(I.target.value);if(isNaN(N)||N<0){A(0),L(0);return}u&&N>u.current_points_balance?A(u.current_points_balance):A(N),Q(N)},Q=async I=>{if(!e||I<=0){L(0);return}try{const{discount:N,error:q}=await s(I,e.id);q?(y(q),L(0)):(y(null),L(N))}catch(N){console.error("Error calculating discount:",N),y(N.message||"Error calculating discount"),L(0)}},V=()=>{w>0&&v>0&&t(w,v)},$=async()=>{if(!e||!u)return;const I=u.current_points_balance;A(I);try{const{discount:N,error:q}=await s(I,e.id);q?(y(q),L(0)):(y(null),L(N))}catch(N){console.error("Error calculating max discount:",N),y(N.message||"Error calculating discount"),L(0)}};return!(i!=null&&i.is_enabled)||!e||e.loyalty_eligible===!1?null:a.jsxs(tl,{className:"mb-4",children:[a.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>S(!U),children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx(Ze,{className:"mr-2 h-5 w-5 text-blue-500"}),a.jsx("h3",{className:"text-lg font-medium",children:"Loyalty Points"})]}),a.jsxs("div",{className:"flex items-center",children:[u&&a.jsxs(mt,{color:"info",className:"text-xs mr-2",children:[u.current_points_balance," points"]}),U?a.jsx(Ii,{className:"h-5 w-5 text-gray-500"}):a.jsx(_i,{className:"h-5 w-5 text-gray-500"})]})]}),U&&a.jsx("div",{className:"mt-3 pt-3 border-t border-gray-100",children:p?a.jsx("div",{className:"flex justify-center py-2",children:a.jsx(nt,{size:"sm"})}):u?a.jsxs(a.Fragment,{children:[h&&a.jsx(It,{color:"failure",className:"mb-2 text-sm",children:h}),a.jsxs("div",{className:"space-y-3",children:[_>0&&a.jsxs("div",{className:"text-sm text-green-600 flex items-center",children:[a.jsx(Ti,{className:"mr-1 h-4 w-4"}),"Customer will earn ",_," points with this purchase"]}),a.jsxs("div",{children:[a.jsxs("div",{className:"flex items-center justify-between mb-1",children:[a.jsx(de,{htmlFor:"pointsToRedeem",className:"text-sm mb-0",children:"Points to Redeem"}),a.jsx(J,{size:"xs",color:"light",onClick:$,disabled:n||!u||u.current_points_balance<=0,children:"Use Max"})]}),a.jsx(Te,{id:"pointsToRedeem",type:"number",min:"0",max:(u==null?void 0:u.current_points_balance)||0,value:w,onChange:B,disabled:n||!u||u.current_points_balance<=0,className:"mb-2"}),v>0&&a.jsxs("div",{className:"text-sm text-blue-600 mb-2",children:["Discount value: ",c(v)]}),a.jsx(J,{color:"primary",size:"sm",className:"w-full",onClick:V,disabled:n||!u||w<=0||v<=0||w>u.current_points_balance,children:"Apply Points"})]})]})]}):a.jsx("p",{className:"text-sm text-gray-500",children:"This customer doesn't have a loyalty profile yet. They'll earn points with this purchase."})})]})},Xu=({customer:e})=>{const{settings:t,getCustomerProfile:n}=fn(),r=At(),[i,l]=T.useState(!1),[s,o]=T.useState(null);if(T.useEffect(()=>{(async()=>{if(!e||!(t!=null&&t.is_enabled)||e.loyalty_eligible===!1){o(null);return}l(!0);try{const d=await n(e.id);o(d)}catch(d){console.error("Error fetching loyalty profile:",d)}finally{l(!1)}})()},[e,t,n]),!e||!(t!=null&&t.is_enabled)||e.loyalty_eligible===!1)return null;if(i)return a.jsxs("div",{className:"flex items-center text-sm text-gray-500 mt-1",children:[a.jsx(Ze,{className:"mr-1 h-4 w-4 text-blue-500"}),a.jsx(nt,{size:"sm",className:"mr-1"}),"Loading loyalty info..."]});if(!s)return a.jsxs("div",{className:"flex items-center text-sm text-gray-500 mt-1",children:[a.jsx(Ze,{className:"mr-1 h-4 w-4 text-blue-500"}),"No loyalty profile yet"]});const c=s.current_points_balance*((t==null?void 0:t.points_redemption_rate)||0);return a.jsx("div",{className:"mt-2 p-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx(Ze,{className:"mr-2 h-5 w-5 text-blue-500"}),a.jsx("span",{className:"text-gray-700 font-medium",children:"Loyalty Points:"})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsxs(mt,{color:"blue",size:"lg",className:"font-bold",children:[s.current_points_balance," points"]}),a.jsxs("span",{className:"text-xs text-gray-600 font-medium",children:["≈ ",r(c)]})]})]})})},Gu=({show:e,onClose:t,onCustomerCreated:n})=>{const{currentOrganization:r}=mn(),[i,l]=T.useState({name:"",email:"",phone:"",address:"",city:"",state:"",postal_code:"",country:"",loyalty_eligible:!0}),[s,o]=T.useState(!1),[c,u]=T.useState(null),d=h=>{const{name:y,value:w}=h.target;l(A=>({...A,[y]:w}))},p=h=>{l(y=>({...y,loyalty_eligible:h}))},g=async h=>{if(h.preventDefault(),!r){u("No organization selected");return}if(!i.name.trim()){u("Customer name is required");return}o(!0),u(null);try{const{customer:y,error:w}=await il(r.id,{name:i.name,email:i.email||null,phone:i.phone||null,address:i.address||null,city:i.city||null,state:i.state||null,postal_code:i.postal_code||null,country:i.country||null,loyalty_eligible:i.loyalty_eligible});if(w)throw new Error(w);l({name:"",email:"",phone:"",address:"",city:"",state:"",postal_code:"",country:"",loyalty_eligible:!0}),typeof n=="function"?(console.log("Calling onCustomerCreated with customer ID:",y.id),n(y.id)):console.error("onCustomerCreated is not a function:",n),t()}catch(y){console.error("Error creating customer:",y),u(y.message||"Failed to create customer")}finally{o(!1)}};return a.jsxs(O,{show:e,onClose:t,size:"lg",children:[a.jsx(O.Header,{children:"Create New Customer"}),a.jsxs(O.Body,{children:[c&&a.jsx(It,{color:"failure",className:"mb-4",children:c}),a.jsxs("form",{onSubmit:g,className:"space-y-4",children:[a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Basic Information"}),a.jsxs("div",{children:[a.jsxs("div",{className:"mb-2 flex items-center",children:[a.jsx(Rr,{className:"mr-2 h-5 w-5 text-gray-500"}),a.jsx(de,{htmlFor:"name",value:"Customer Name *"})]}),a.jsx(Te,{id:"name",name:"name",value:i.name,onChange:d,required:!0})]}),a.jsxs("div",{children:[a.jsxs("div",{className:"mb-2 flex items-center",children:[a.jsx(Ai,{className:"mr-2 h-5 w-5 text-gray-500"}),a.jsx(de,{htmlFor:"email",value:"Email"})]}),a.jsx(Te,{id:"email",name:"email",type:"email",value:i.email,onChange:d})]}),a.jsxs("div",{children:[a.jsxs("div",{className:"mb-2 flex items-center",children:[a.jsx(Di,{className:"mr-2 h-5 w-5 text-gray-500"}),a.jsx(de,{htmlFor:"phone",value:"Phone"})]}),a.jsx(Te,{id:"phone",name:"phone",value:i.phone,onChange:d})]})]}),a.jsxs("div",{className:"pt-4 border-t border-gray-200",children:[a.jsxs("div",{className:"flex items-center justify-between mb-2",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx(Ze,{className:"mr-2 h-5 w-5 text-blue-500"}),a.jsx(de,{htmlFor:"loyalty_eligible",value:"Loyalty Program Eligible",className:"mb-0"})]}),a.jsx(Li,{id:"loyalty_eligible",checked:i.loyalty_eligible,onChange:p,label:""})]}),a.jsx("p",{className:"text-sm text-gray-500",children:"Enable this to allow the customer to earn and redeem loyalty points"})]})]})]}),a.jsxs(O.Footer,{children:[a.jsx(J,{color:"primary",onClick:g,disabled:s,children:s?a.jsxs(a.Fragment,{children:[a.jsx(nt,{size:"sm",className:"mr-2"}),"Creating..."]}):"Create Customer"}),a.jsx(J,{color:"gray",onClick:t,children:"Cancel"})]})]})},Ju=({show:e,onClose:t,onConfirm:n,itemName:r})=>{const i=()=>{n(),t()};return T.useEffect(()=>{const l=s=>{s.key==="Escape"&&e&&t()};return e&&document.addEventListener("keydown",l),()=>{document.removeEventListener("keydown",l)}},[e,t]),a.jsxs(O,{show:e,onClose:t,size:"sm",children:[a.jsx(O.Header,{children:a.jsxs("div",{className:"flex items-center",children:[a.jsx(Rn,{className:"mr-2 h-6 w-6 text-red-500"}),"Confirm Item Removal"]})}),a.jsx(O.Body,{children:a.jsxs("div",{className:"text-center",children:[a.jsx(Rn,{className:"mx-auto mb-4 h-14 w-14 text-gray-400"}),a.jsxs("p",{className:"text-gray-700 text-lg",children:["Are you sure you want to remove ",a.jsx("span",{className:"font-medium",children:r})," from the cart?"]}),a.jsx("p",{className:"text-gray-500 text-sm mt-2",children:"This action cannot be undone."})]})}),a.jsx(O.Footer,{children:a.jsxs("div",{className:"flex justify-center gap-4 w-full",children:[a.jsx(J,{color:"gray",onClick:t,children:"Cancel"}),a.jsxs(J,{color:"failure",onClick:i,className:"flex items-center",children:[a.jsx(nn,{className:"mr-2 h-4 w-4"}),"Remove Item"]})]})})]})},Fr=({show:e,onClose:t,onViewReceipt:n})=>{const{currentOrganization:r}=mn(),i=At(),[l,s]=T.useState([]),[o,c]=T.useState(!1),[u,d]=T.useState(null),[p,g]=T.useState(""),[h,y]=T.useState("today"),[w,A]=T.useState(new Date().toISOString().split("T")[0]),[v,L]=T.useState(new Date().toISOString().split("T")[0]),[_,H]=T.useState(1),[U,S]=T.useState(10),B=async()=>{if(r){c(!0),d(null);try{let C=new Date;const W=new Date;switch(h){case"today":C=new Date,C.setHours(0,0,0,0);break;case"yesterday":C=new Date,C.setDate(C.getDate()-1),C.setHours(0,0,0,0),W.setDate(W.getDate()-1),W.setHours(23,59,59,999);break;case"week":C=new Date,C.setDate(C.getDate()-7),C.setHours(0,0,0,0);break;case"month":C=new Date,C.setMonth(C.getMonth()-1),C.setHours(0,0,0,0);break;case"custom":C=new Date(w),C.setHours(0,0,0,0),new Date(v).setHours(23,59,59,999);break}const{sales:te,error:oe}=await Qi(r.id,{startDate:C.toISOString(),endDate:h==="custom"?new Date(v+"T23:59:59").toISOString():W.toISOString(),limit:100,sortBy:"created_at",sortOrder:"desc"});if(oe)throw new Error(oe);s(te)}catch(C){console.error("Error fetching sales:",C),d(C.message||"Failed to fetch sales")}finally{c(!1)}}};T.useEffect(()=>{e&&B()},[e,h,w,v,r]);const Q=l.filter(C=>{if(!p)return!0;const W=p.toLowerCase();return C.invoice_number&&C.invoice_number.toLowerCase().includes(W)||C.customer&&C.customer.name&&C.customer.name.toLowerCase().includes(W)}),V=Math.ceil(Q.length/U),$=_*U,I=$-U,N=Q.slice(I,$),q=Q.length,G=Q.reduce((C,W)=>C+W.total_amount,0);return T.useEffect(()=>{const C=W=>{W.key==="Escape"&&e&&t()};return window.addEventListener("keydown",C),()=>{window.removeEventListener("keydown",C)}},[e,t]),a.jsxs(O,{show:e,onClose:t,size:"7xl",children:[a.jsx(O.Header,{children:a.jsxs("div",{className:"flex items-center",children:[a.jsx(Fi,{className:"mr-2 h-5 w-5 text-blue-500"}),"Sales History",a.jsx("span",{className:"ml-2 text-xs bg-gray-200 px-1.5 py-0.5 rounded",children:"F8"})]})}),a.jsx(O.Body,{children:a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"flex flex-wrap gap-3 items-end",children:[a.jsxs("div",{className:"flex-1",children:[a.jsx(de,{htmlFor:"search",value:"Search"}),a.jsx(Te,{id:"search",type:"text",icon:zi,placeholder:"Search by invoice # or customer",value:p,onChange:C=>g(C.target.value)})]}),a.jsxs("div",{children:[a.jsx(de,{htmlFor:"dateFilter",value:"Date Range"}),a.jsxs(Ri,{id:"dateFilter",value:h,onChange:C=>y(C.target.value),children:[a.jsx("option",{value:"today",children:"Today"}),a.jsx("option",{value:"yesterday",children:"Yesterday"}),a.jsx("option",{value:"week",children:"Last 7 Days"}),a.jsx("option",{value:"month",children:"Last 30 Days"}),a.jsx("option",{value:"custom",children:"Custom Range"})]})]}),h==="custom"&&a.jsxs(a.Fragment,{children:[a.jsxs("div",{children:[a.jsx(de,{htmlFor:"startDate",value:"Start Date"}),a.jsx(Te,{id:"startDate",type:"date",value:w,onChange:C=>A(C.target.value)})]}),a.jsxs("div",{children:[a.jsx(de,{htmlFor:"endDate",value:"End Date"}),a.jsx(Te,{id:"endDate",type:"date",value:v,onChange:C=>L(C.target.value)})]})]}),a.jsxs(J,{color:"light",onClick:B,children:[a.jsx(Mi,{className:"mr-2 h-4 w-4"}),"Refresh"]})]}),a.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[a.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100",children:[a.jsx("div",{className:"text-sm text-blue-700",children:"Total Sales"}),a.jsx("div",{className:"text-xl font-bold",children:q})]}),a.jsxs("div",{className:"bg-green-50 p-3 rounded-lg border border-green-100",children:[a.jsx("div",{className:"text-sm text-green-700",children:"Total Amount"}),a.jsx("div",{className:"text-xl font-bold",children:i(G)})]})]}),o?a.jsx("div",{className:"flex justify-center py-8",children:a.jsx(nt,{size:"xl"})}):u?a.jsx("div",{className:"text-center py-8 text-red-500",children:u}):Q.length===0?a.jsx("div",{className:"text-center py-8 text-gray-500",children:"No sales found for the selected period"}):a.jsxs("div",{className:"overflow-x-auto",children:[a.jsxs(ce,{hoverable:!0,children:[a.jsxs(ce.Head,{children:[a.jsx(ce.HeadCell,{children:"Invoice #"}),a.jsx(ce.HeadCell,{children:"Date"}),a.jsx(ce.HeadCell,{children:"Customer"}),a.jsx(ce.HeadCell,{children:"Cashier"}),a.jsx(ce.HeadCell,{children:"Payment"}),a.jsx(ce.HeadCell,{children:"Amount"}),a.jsx(ce.HeadCell,{children:"Actions"})]}),a.jsx(ce.Body,{className:"divide-y",children:N.map(C=>a.jsxs(ce.Row,{className:"bg-white",children:[a.jsx(ce.Cell,{className:"font-medium",children:C.invoice_number}),a.jsx(ce.Cell,{children:Mr(C.created_at)}),a.jsx(ce.Cell,{children:C.customer?C.customer.name:"Walk-in Customer"}),a.jsx(ce.Cell,{children:C.cashier?a.jsx("div",{className:"text-sm",children:a.jsx("div",{className:"font-medium",children:`${C.cashier.first_name||""} ${C.cashier.last_name||""}`.trim()||"Unknown User"})}):a.jsx("span",{className:"text-gray-400 text-sm",children:"Unknown"})}),a.jsx(ce.Cell,{children:a.jsx(mt,{color:C.payment_method==="cash"?"success":"info",children:C.payment_method==="cash"?"Cash":"Card"})}),a.jsx(ce.Cell,{className:"font-medium",children:i(C.total_amount)}),a.jsx(ce.Cell,{children:a.jsxs(J,{size:"xs",color:"light",onClick:()=>n(C.id),children:[a.jsx(Oi,{className:"mr-1 h-3 w-3"}),"View"]})})]},C.id))})]}),Q.length>0&&a.jsx(ll,{currentPage:_,totalPages:V,itemsPerPage:U,totalItems:Q.length,onPageChange:H,onItemsPerPageChange:C=>{S(C),H(1)},itemName:"sales"})]})]})}),a.jsx(O.Footer,{children:a.jsxs("div",{className:"flex justify-between w-full items-center",children:[a.jsxs("p",{className:"text-sm text-gray-500",children:["Press ",a.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"ESC"})," to close"]}),a.jsx(J,{color:"gray",onClick:t,children:"Close"})]})})]})},mc=()=>{const{currentOrganization:e}=mn(),{settings:t}=Bi(),{settings:n}=fn(),{user:r}=Hi(),i=At(),[l,s]=T.useState(null),[o,c]=T.useState([]),[u,d]=T.useState(null),[p,g]=T.useState(""),[h,y]=T.useState(1),w=T.useRef(null),[A,v]=T.useState({amount:0,isPercentage:!1}),[L,_]=T.useState(0),[H,U]=T.useState(0),[S,B]=T.useState(!1),[Q,V]=T.useState(0),[$,I]=T.useState(!1),[N,q]=T.useState(null),[G,C]=T.useState(!1),[W,te]=T.useState(!1),[oe,ne]=T.useState(!1),[f,se]=T.useState("cash"),[xe,m]=T.useState(""),[ye,Ae]=T.useState(!1),[re,Ce]=T.useState(!1),De=()=>{o.length>0&&C(!0)},Be=()=>{c([]),d(null),_(0),U(0),v({amount:0,isPercentage:!1}),y(1),V(x=>x+1)},Le=T.useCallback(x=>{if(!(x.target instanceof HTMLInputElement||x.target instanceof HTMLTextAreaElement||x.target instanceof HTMLSelectElement)){if((x.key==="p"||x.key==="P")&&(x.ctrlKey||x.metaKey)){console.log("Ctrl+P detected"),x.preventDefault(),x.stopPropagation(),o.length>0?(se("cash"),m(pe().toFixed(2)),ne(!0)):console.log("Cart is empty, not showing payment modal");return}if((x.key==="k"||x.key==="K")&&(x.ctrlKey||x.metaKey)){console.log("Ctrl+K detected"),x.preventDefault(),x.stopPropagation(),o.length>0?(se("card"),ne(!0)):console.log("Cart is empty, not showing payment modal");return}switch((x.key==="f"&&x.ctrlKey||x.key==="b"&&x.ctrlKey||x.key==="d"&&x.ctrlKey||x.key==="c"&&x.ctrlKey||x.key==="/"&&x.ctrlKey||x.key==="F1"||x.key==="F2"||x.key==="F3"||x.key==="F4"||x.key==="F5"||x.key==="Escape"||x.key==="1"||x.key==="5"||x.key==="0")&&x.preventDefault(),!0){case(x.key==="f"&&x.ctrlKey):const k=document.querySelector(".product-search-container");if(k){const Pe=k.querySelector('input[type="text"]');Pe&&Pe instanceof HTMLElement&&Pe.focus()}break;case(x.key==="b"&&x.ctrlKey):w.current&&w.current.focus();break;case(x.key==="d"&&x.ctrlKey):const P=document.querySelector('[data-testid="discount-selector-button"]');P&&P instanceof HTMLElement&&P.click();break;case(x.key==="c"&&x.ctrlKey):const K=document.querySelector('[data-testid="customer-selector-button"]');K&&K instanceof HTMLElement&&K.click();break;case(x.key==="/"&&x.ctrlKey||x.key==="F1"):te(!0);break;case x.key==="F2":_e(!0);break;case x.key==="F3":if(o.length>0){const Pe=o[o.length-1].id;Ge(Pe)}break;case x.key==="F4":console.log("Tax exempt shortcut triggered");break;case x.key==="F5":Ce(!0);break;case x.key==="Escape":W?te(!1):re?Ce(!1):S?B(!1):$?I(!1):G?C(!1):oe?ne(!1):F?M(!1):Y?ue(!1):je?fe(!1):He?(_e(!1),at(null)):he?Ne(!1):o.length>0&&C(!0);break;case x.key==="1":y(1);break;case x.key==="5":y(5);break;case x.key==="0":y(10);break}}},[o,se,ne,W,re,S,$,G,oe]);T.useEffect(()=>{window.addEventListener("keydown",Le);const x=P=>{(P.key==="p"||P.key==="P")&&(P.ctrlKey||P.metaKey)&&(console.log("Direct Ctrl+P handler"),P.preventDefault(),P.stopPropagation(),o.length>0&&(se("cash"),m(pe().toFixed(2)),ne(!0)))},k=P=>{(P.key==="k"||P.key==="K")&&(P.ctrlKey||P.metaKey)&&(console.log("Direct Ctrl+K handler"),P.preventDefault(),P.stopPropagation(),o.length>0&&(se("card"),ne(!0)))};return document.addEventListener("keydown",x,!0),document.addEventListener("keydown",k,!0),()=>{window.removeEventListener("keydown",Le),document.removeEventListener("keydown",x,!0),document.removeEventListener("keydown",k,!0)}},[Le,o]),T.useEffect(()=>{const x=()=>{fe(!0)},k=()=>{Ln()},P=()=>{te(!0)},K=()=>{Ce(!0)};return window.addEventListener("pos-show-sales-modal",x),window.addEventListener("pos-show-manual-modal",k),window.addEventListener("pos-show-shortcuts-modal",P),window.addEventListener("pos-show-refund-modal",K),()=>{window.removeEventListener("pos-show-sales-modal",x),window.removeEventListener("pos-show-manual-modal",k),window.removeEventListener("pos-show-shortcuts-modal",P),window.removeEventListener("pos-show-refund-modal",K)}},[]),T.useEffect(()=>{const x=setTimeout(()=>{var k;w.current&&!((k=document.activeElement)!=null&&k.closest(".product-search-container"))&&w.current.focus()},100);return()=>clearTimeout(x)},[]),T.useEffect(()=>{(async()=>{if(e)try{await qn.searchProducts(e.id,"",1,20)}catch(k){console.error("Error preloading products:",k)}})()},[e]);const Xe=async x=>{x.preventDefault(),await bt(p)},bt=async x=>{if(!(!x||!e)){try{const{products:k}=await qn.searchProducts(e.id,x,1,10),P=k.find(K=>K.barcode===x);P?(lt(P,h),g(""),y(1),ut.success(`Added ${P.name} to cart`)):(s(`Product with barcode ${x} not found`),setTimeout(()=>s(null),3e3))}catch(k){s(k.message||"Error searching for product"),setTimeout(()=>s(null),3e3)}w.current&&w.current.focus()}},Rt=async x=>{const k=x.target.value;g(k),k.length>=8&&k.length<=13&&/^[a-zA-Z0-9]+$/.test(k)&&setTimeout(async()=>{var P;((P=w.current)==null?void 0:P.value)===k&&document.activeElement===w.current&&await bt(k)},150)},lt=(x,k=1)=>{const P=x.stock_quantity||0,K=o.filter(ae=>ae.product.id===x.id).reduce((ae,ie)=>ae+ie.quantity,0);P<K+k&&An.warn_on_low_inventory&&ut.custom(ae=>a.jsxs("div",{className:`${ae.visible?"animate-enter":"animate-leave"} max-w-md w-full bg-white shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5`,children:[a.jsx("div",{className:"flex-1 w-0 p-4",children:a.jsxs("div",{className:"flex items-start",children:[a.jsx("div",{className:"flex-shrink-0 pt-0.5",children:a.jsx(Ki,{className:"h-6 w-6 text-yellow-500"})}),a.jsxs("div",{className:"ml-3 flex-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Low Inventory Warning"}),a.jsxs("p",{className:"mt-1 text-sm text-gray-500",children:["Adding ",k," of ",x.name," will exceed available stock (",P," available).",An.allow_negative_inventory?"":" This will create float inventory."]})]})]})}),a.jsx("div",{className:"flex border-l border-gray-200",children:a.jsx("button",{onClick:()=>ut.dismiss(ae.id),className:"w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-indigo-600 hover:text-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500",children:"Dismiss"})})]}),{duration:5e3}),c(ae=>ae.find(ge=>ge.product.id===x.id)?ae.map(ge=>ge.product.id===x.id?{...ge,quantity:ge.quantity+k}:ge):[...ae,{id:x.id,product:x,quantity:k}])},Ke=(x,k)=>{if(k<=0){Ge(x);return}c(P=>P.map(K=>K.id===x?{...K,quantity:k}:K))},Ge=x=>{const k=o.find(P=>P.id===x);k&&(q({id:x,name:k.product.name}),I(!0))},wt=()=>{N&&(c(x=>x.filter(k=>k.id!==N.id)),q(null))},Mt=(x,k)=>{c(P=>P.map(K=>K.id===x?{...K,notes:k}:K))},Fe=()=>o.reduce((x,k)=>x+k.product.unit_price*k.quantity,0),Ue=()=>{const x=Fe();return A.isPercentage?x*(A.amount/100):A.amount},st=()=>{const x=t==null?void 0:t.tax_rate,k=(t==null?void 0:t.tax_settings)||{vat_rate:0,vat_inclusive:!0,tax_enabled:!1},P=x===0?0:k.vat_rate;if(!k.tax_enabled||P===0)return 0;const K=Fe()-Ue();return k.vat_inclusive?K-K/(1+P/100):K*(P/100)},pe=()=>{const x=t==null?void 0:t.tax_rate,k=(t==null?void 0:t.tax_settings)||{vat_rate:0,vat_inclusive:!0,tax_enabled:!1},P=x===0?0:k.vat_rate,Pe=Fe()-Ue()-H,ae=Math.max(0,Pe);return!k.tax_enabled||P===0||k.vat_inclusive?ae:ae+st()},Ot=(x,k)=>{v({amount:x,isPercentage:k})},Bt=(x,k)=>{_(x),U(k)},Ht=async x=>{try{if(!e){console.log("No current organization found");return}console.log("Fetching newly created customer with ID:",x);const{data:k,error:P}=await Un.from("customers").select("*").eq("id",x).single();if(P){console.error("Error fetching new customer:",P);return}console.log("Customer fetched successfully:",k),d(k)}catch(k){console.error("Error handling new customer:",k)}},b=x=>{const k=parseInt(x.target.value);!isNaN(k)&&k>0&&y(k)},E=[{key:"Ctrl+B",description:"Focus on barcode input",category:"Navigation"},{key:"Ctrl+F",description:"Focus on search input",category:"Navigation"},{key:"Ctrl+C",description:"Focus on customer search",category:"Navigation"},{key:"Ctrl+P",description:"Cash payment",category:"Payment"},{key:"Ctrl+K",description:"Card payment",category:"Payment"},{key:"Ctrl+D",description:"Apply discount",category:"Payment"},{key:"1",description:"Set quantity to 1",category:"Quantity"},{key:"5",description:"Set quantity to 5",category:"Quantity"},{key:"0",description:"Set quantity to 10",category:"Quantity"},{key:"F1",description:"Show keyboard shortcuts",category:"Help"},{key:"F2",description:"Price check",category:"Functions"},{key:"F3",description:"Void selected item",category:"Functions"},{key:"F4",description:"Toggle tax exempt",category:"Functions"},{key:"F5",description:"Process refund",category:"Functions"},{key:"F7",description:"View user manual",category:"Help"},{key:"F8",description:"View sales list",category:"Sales"},{key:"F9",description:"View receipt (after sale)",category:"Receipt"},{key:"Esc",description:"Close modal / Clear cart",category:"Navigation"},{key:"Ctrl+/",description:"Show keyboard shortcuts",category:"Help"}],[F,M]=T.useState(!1),[Y,ue]=T.useState(!1),[je,fe]=T.useState(!1),[He,_e]=T.useState(!1),[he,Ne]=T.useState(!1),[D,Ut]=T.useState(null),[Ee,at]=T.useState(null),[Si,Tn]=T.useState(""),[An,vi]=T.useState({allow_negative_inventory:!0,warn_on_low_inventory:!0,auto_create_purchase_requests:!0});T.useEffect(()=>{(async()=>{if(e)try{const{settings:k,error:P}=await rl(e.id);P||vi(k)}catch(k){console.error("Error fetching inventory settings:",k)}})()},[e]);const Ci=async()=>{if(!e||!r){s("User or organization not found");return}if(o.length===0){s("Cart is empty");return}Ae(!0),s(null);try{const x=await Promise.all(o.map(async ie=>{var zn;let ge=(zn=ie.product.product_uoms)==null?void 0:zn.find(ze=>ze.is_default),Ve=(ge==null?void 0:ge.uom_id)||null;if(!Ve)try{const{getDefaultProductUom:ze}=await $i(async()=>{const{getDefaultProductUom:Pi}=await import("./productUom-k6aUg6b7.js");return{getDefaultProductUom:Pi}},__vite__mapDeps([0,1,2,3])),{productUom:qt}=await ze(ie.product.id,e.id);Ve=(qt==null?void 0:qt.uom_id)||null}catch(ze){console.error("Error fetching default UoM for product:",ie.product.id,ze)}if(!Ve)try{const{data:ze}=await Un.from("units_of_measurement").select("id").eq("organization_id",e.id).eq("code","pcs").single();ze&&(Ve=ze.id,console.log(`Using fallback 'pieces' UoM for product: ${ie.product.name}`))}catch(ze){console.error("Error finding pieces UoM:",ze)}if(!Ve)throw new Error(`No default unit of measure found for product: ${ie.product.name}. Please set up units of measurement for this product.`);const Vt=t==null?void 0:t.tax_rate,kt=(t==null?void 0:t.tax_settings)||{},ot=Vt===0?0:kt.vat_rate,Qe=kt.tax_enabled&&ot>0?ot:0,St=ie.product.unit_price*ie.quantity,Fn=Fe(),Ei=Fn>0?St/Fn*Ue():0;return{product_id:ie.product.id,quantity:ie.quantity,unit_price:ie.product.unit_price,uom_id:Ve,base_quantity:ie.quantity,tax_rate:Qe,discount_amount:Ei,notes:ie.notes||""}}));let k=0;if(u!=null&&u.loyalty_eligible&&(n!=null&&n.is_enabled)){const ie=(n==null?void 0:n.points_earning_rate)||.005;k=Math.floor(pe()*ie),k<0&&(k=0);const ge=100;k>ge&&(k=ge)}let P,K;f==="cash"&&xe&&(P=parseFloat(xe),K=Math.max(0,P-pe()));const{sale:Pe,error:ae}=await Wi(e.id,r.id,{customer_id:u==null?void 0:u.id,items:x,subtotal:Fe(),tax_amount:st(),discount_amount:Ue(),total_amount:pe(),payment_method:f,cash_tendered:P,change_amount:K,notes:u?`Sale to ${u.name}`:void 0,loyalty_points_used:L,loyalty_points_discount:H,loyalty_points_earned:k});if(ae)throw new Error(ae);Ut({...Pe,loyalty_points_earned:k}),ne(!1),M(!0),Be(),d(null),_(0),U(0),v({amount:0,isPercentage:!1}),V(ie=>ie+1)}catch(x){console.error("Error processing payment:",x),s(x.message||"An error occurred while processing the payment"),setTimeout(()=>{s(null)},5e3)}finally{Ae(!1)}};T.useEffect(()=>{const x=k=>{k.key==="F9"&&F&&D&&(k.preventDefault(),ue(!0)),k.key==="F7"&&(k.preventDefault(),Ln()),k.key==="F8"&&(k.preventDefault(),ji())};return window.addEventListener("keydown",x),()=>{window.removeEventListener("keydown",x)}},[F,Y,je,He,he,D]);const ji=()=>{e&&fe(!0)},Dn=async x=>{if(e){Ut(null),ue(!0),fe(!1);try{const{sale:k,error:P}=await Yi(e.id,x);if(P)throw new Error(P);Ut(k)}catch(k){console.error("Error fetching sale:",k),s(k.message||"Failed to fetch sale"),setTimeout(()=>s(null),3e3)}}},Ni=()=>{var ot;if(!D)return;const x=window.open("","_blank","width=800,height=600");if(!x)return;const k=Mr(D.sale_date),P=i(D.subtotal),K=i(D.tax_amount),Pe=i(D.total_amount),ae=D.discount_amount>0?i(D.discount_amount):"",ie=D.loyalty_points_discount>0?i(D.loyalty_points_discount):"",ge=D.cash_tendered?i(D.cash_tendered):"",Ve=D.change_amount>0?i(D.change_amount):"",Vt=D.cashier?((D.cashier.first_name||"")+" "+(D.cashier.last_name||"")).trim()||"Unknown User":"",kt=`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt - ${D.invoice_number}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              font-size: 14px;
              line-height: 1.4;
            }
            .receipt {
              max-width: 400px;
              margin: 0 auto;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #000;
              padding-bottom: 10px;
              margin-bottom: 15px;
            }
            .company-name {
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 5px;
            }
            .receipt-info {
              margin-bottom: 15px;
            }
            .receipt-info div {
              margin-bottom: 3px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin: 15px 0;
            }
            th, td {
              padding: 8px 4px;
              text-align: left;
              border-bottom: 1px solid #ddd;
            }
            th {
              font-weight: bold;
              background-color: #f5f5f5;
            }
            .text-right {
              text-align: right;
            }
            .text-center {
              text-align: center;
            }
            .summary {
              margin-top: 15px;
            }
            .summary-row {
              display: flex;
              justify-content: space-between;
              margin: 5px 0;
            }
            .total-row {
              border-top: 2px solid #000;
              padding-top: 8px;
              margin-top: 10px;
              font-weight: bold;
              font-size: 16px;
            }
            .footer {
              text-align: center;
              margin-top: 20px;
              font-size: 12px;
              color: #666;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="receipt">
            <!-- Header -->
            <div class="header">
              <div class="company-name">${(e==null?void 0:e.name)||"Your Business"}</div>
              ${e!=null&&e.address?`<div>${e.address}</div>`:""}
              ${e!=null&&e.phone||e!=null&&e.email?`
                <div style="margin-top: 5px;">
                  ${e!=null&&e.phone?`Phone: ${e.phone}`:""}
                  ${e!=null&&e.phone&&(e!=null&&e.email)?" | ":""}
                  ${e!=null&&e.email?`Email: ${e.email}`:""}
                </div>
              `:""}
              ${e!=null&&e.website?`<div>${e.website}</div>`:""}
            </div>

            <!-- Receipt Info -->
            <div class="receipt-info">
              <div><strong>Receipt: ${D.invoice_number}</strong></div>
              <div>Date: ${k}</div>
              ${D.customer?`<div>Customer: ${D.customer.name}</div>`:""}
              ${D.cashier?`<div>Cashier: ${Vt}</div>`:""}
            </div>

            <!-- Items Table -->
            <table>
              <thead>
                <tr>
                  <th>Item</th>
                  <th class="text-right">Qty</th>
                  <th class="text-right">Price</th>
                  <th class="text-right">Total</th>
                </tr>
              </thead>
              <tbody>
                ${(ot=D.items)==null?void 0:ot.map(Qe=>{var St;return`
                  <tr>
                    <td>${((St=Qe.product)==null?void 0:St.name)||"Product"}</td>
                    <td class="text-right">${Qe.quantity} pcs</td>
                    <td class="text-right">${i(Qe.unit_price)}</td>
                    <td class="text-right">${i(Qe.unit_price*Qe.quantity)}</td>
                  </tr>
                `}).join("")}
              </tbody>
            </table>

            <!-- Summary -->
            <div class="summary">
              <div class="summary-row">
                <span>Subtotal</span>
                <span>${P}</span>
              </div>
              ${D.discount_amount>0?`
                <div class="summary-row">
                  <span>Discount</span>
                  <span>-${ae}</span>
                </div>
              `:""}
              ${D.loyalty_points_discount&&D.loyalty_points_discount>0?`
                <div class="summary-row">
                  <span>Loyalty Points (${D.loyalty_points_used} pts)</span>
                  <span>-${ie}</span>
                </div>
              `:""}
              <div class="summary-row">
                <span>Tax</span>
                <span>${K}</span>
              </div>
              <div class="summary-row total-row">
                <span>Total</span>
                <span>${Pe}</span>
              </div>
            </div>

            ${D.payment_method==="cash"&&D.cash_tendered?`
              <div class="summary" style="border-top: 1px solid #ccc; padding-top: 10px; margin-top: 15px;">
                <div class="summary-row">
                  <span>Cash Tendered:</span>
                  <span>${ge}</span>
                </div>
                ${D.change_amount&&D.change_amount>0?`
                  <div class="summary-row">
                    <span>Change:</span>
                    <span>${Ve}</span>
                  </div>
                `:""}
              </div>
            `:""}

            <!-- Footer -->
            <div class="footer">
              <p>Thank you for your business!</p>
              <p>Payment Method: ${D.payment_method}</p>
              ${D.notes?`<p style="font-style: italic;">${D.notes}</p>`:""}
              <p style="margin-top: 15px;">Generated on ${new Date().toLocaleDateString()}</p>
            </div>
          </div>

          <script>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              };
            };
          <\/script>
        </body>
      </html>
    `;x.document.write(kt),x.document.close()},Ln=async()=>{try{const x=await fetch("/docs/POS_Terminal_User_Manual.md");if(!x.ok)throw new Error("Failed to load manual");const k=await x.text();Tn(k),Ne(!0)}catch(x){ut.error(x.message||"Failed to load manual"),Tn(`# POS Terminal User Manual

The manual could not be loaded. Please try again later or contact support.`),Ne(!0)}};return a.jsxs("div",{className:"w-full h-full bg-gray-50 flex flex-col",children:[a.jsxs(O,{show:F,onClose:()=>M(!1),size:"xl",children:[a.jsx(O.Header,{children:"Sale Completed Successfully"}),a.jsx(O.Body,{children:a.jsxs("div",{className:"space-y-4 text-center",children:[a.jsx("div",{className:"flex justify-center",children:a.jsx("div",{className:"rounded-full bg-green-100 p-3",children:a.jsx("svg",{className:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Sale Completed"}),a.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Invoice Number"}),a.jsx("p",{className:"text-lg font-bold",children:D==null?void 0:D.invoice_number})]}),a.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[a.jsx("p",{className:"text-sm text-gray-500",children:"Total Amount"}),a.jsx("p",{className:"text-lg font-bold text-green-600",children:i((D==null?void 0:D.total_amount)||0)})]}),(D==null?void 0:D.loyalty_points_earned)>0&&a.jsx("div",{className:"bg-purple-50 p-4 rounded-lg border border-purple-100",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx(Ze,{className:"mr-2 h-5 w-5 text-purple-600"}),a.jsx("p",{className:"text-sm text-purple-700",children:"Loyalty Points Earned"})]}),a.jsxs("p",{className:"text-lg font-bold text-purple-600",children:["+",D.loyalty_points_earned," points"]})]})}),(D==null?void 0:D.loyalty_points_used)>0&&a.jsx("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-100",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("p",{className:"text-sm text-blue-700",children:"Loyalty Points Used"}),a.jsxs("p",{className:"text-lg font-bold text-blue-600",children:[D.loyalty_points_used," points"]})]})}),a.jsxs("p",{className:"text-sm text-gray-500",children:["Press ",a.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:"ESC"})," to close this window"]})]})}),a.jsx(O.Footer,{children:a.jsxs("div",{className:"flex justify-between w-full",children:[a.jsx(J,{color:"light",onClick:()=>M(!1),children:"Close"}),a.jsxs(J,{color:"info",onClick:()=>ue(!0),children:[a.jsx(Mn,{className:"mr-2 h-5 w-5"}),"View Receipt (F9)"]})]})})]}),a.jsxs(O,{show:oe,onClose:()=>ne(!1),size:"md",children:[a.jsx(O.Header,{children:f==="cash"?"Cash Payment":"Card Payment"}),a.jsx(O.Body,{children:a.jsxs("div",{className:"space-y-4",children:[l&&a.jsx(It,{color:"failure",className:"mb-4",children:l}),a.jsxs("div",{className:"text-center mb-4",children:[a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:i(pe())}),a.jsx("p",{className:"text-gray-500",children:"Total Amount"})]}),f==="cash"?a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx(de,{htmlFor:"cash-tendered",value:"Cash Tendered"}),a.jsx(al,{value:xe||pe().toFixed(2),onChange:x=>m(x.target.value),onBlur:x=>{(x.target.value===""||parseFloat(x.target.value)<pe())&&m(pe().toFixed(2))},min:pe(),step:"0.01",className:"mt-1",autoSelect:!0,preventScrollChange:!0})]}),a.jsxs("div",{children:[a.jsx(de,{value:"Change"}),a.jsx("div",{className:"p-2 bg-gray-50 rounded border mt-1 text-lg font-medium",children:i(Math.max(0,parseFloat(xe||"0")-pe()))})]})]}):a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx(de,{htmlFor:"card-number",value:"Card Number"}),a.jsx(Te,{id:"card-number",type:"text",placeholder:"•••• •••• •••• ••••",className:"mt-1"})]}),a.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[a.jsxs("div",{children:[a.jsx(de,{htmlFor:"expiry",value:"Expiry Date"}),a.jsx(Te,{id:"expiry",type:"text",placeholder:"MM/YY",className:"mt-1"})]}),a.jsxs("div",{children:[a.jsx(de,{htmlFor:"cvv",value:"CVV"}),a.jsx(Te,{id:"cvv",type:"text",placeholder:"123",className:"mt-1"})]})]})]})]})}),a.jsx(O.Footer,{children:a.jsxs("div",{className:"flex justify-between w-full",children:[a.jsx(J,{color:"light",onClick:()=>ne(!1),children:"Cancel"}),a.jsx(J,{color:f==="cash"?"success":"info",onClick:Ci,disabled:ye,children:ye?a.jsxs(a.Fragment,{children:[a.jsx(nt,{size:"sm",className:"mr-2"}),"Processing..."]}):a.jsxs(a.Fragment,{children:[f==="cash"?a.jsx(On,{className:"mr-2 h-5 w-5"}):a.jsx(Bn,{className:"mr-2 h-5 w-5"}),"Process Payment"]})})]})})]}),a.jsxs(O,{show:Y,onClose:()=>ue(!1),size:"md",children:[a.jsx(O.Header,{children:"Receipt"}),a.jsx(O.Body,{children:D?a.jsx(nl,{sale:D,showPrintButton:!1}):a.jsxs("div",{className:"text-center py-4",children:[a.jsx(nt,{size:"xl"}),a.jsx("p",{className:"mt-2",children:"Loading receipt..."})]})}),a.jsx(O.Footer,{children:a.jsxs("div",{className:"flex justify-between w-full",children:[a.jsx(J,{color:"light",onClick:()=>ue(!1),children:"Close"}),a.jsxs(J,{color:"success",onClick:Ni,children:[a.jsx(Mn,{className:"mr-2 h-5 w-5"}),"Print Receipt"]})]})})]}),a.jsxs(O,{show:He,onClose:()=>{_e(!1),at(null)},size:"md",children:[a.jsx(O.Header,{children:"Price Check"}),a.jsx(O.Body,{children:a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"mb-4",children:[a.jsx(de,{htmlFor:"price-check-product",value:"Search Product",className:"mb-2 block"}),a.jsx(Vn,{value:(Ee==null?void 0:Ee.id)||"",onChange:(x,k)=>{k&&at(k)},placeholder:"Search by name, SKU, or barcode",pageSize:10,instanceId:"price-check-product-search"})]}),Ee&&a.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[a.jsxs("div",{className:"flex items-start",children:[a.jsxs("div",{className:"flex-grow",children:[a.jsx("h3",{className:"text-lg font-medium",children:Ee.name}),a.jsxs("div",{className:"text-sm text-gray-500 mt-1",children:[Ee.sku&&a.jsxs("div",{children:["SKU: ",Ee.sku]}),Ee.barcode&&a.jsxs("div",{children:["Barcode: ",Ee.barcode]}),a.jsxs("div",{children:["Stock: ",Xi(Ee.stock_quantity||0)," units"]})]})]}),a.jsx("div",{className:"text-right",children:a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:i(Ee.unit_price)})})]}),a.jsx("div",{className:"mt-4 flex justify-end",children:a.jsxs(J,{size:"sm",onClick:()=>{lt(Ee),_e(!1),at(null)},children:[a.jsx(Hn,{className:"mr-2 h-4 w-4"}),"Add to Cart"]})})]})]})}),a.jsx(O.Footer,{children:a.jsx(J,{color:"gray",onClick:()=>{_e(!1),at(null)},children:"Close"})})]}),a.jsx(Fr,{show:je,onClose:()=>fe(!1),onViewReceipt:Dn}),a.jsxs(O,{show:he,onClose:()=>Ne(!1),size:"7xl",children:[a.jsx(O.Header,{children:"POS Terminal User Manual"}),a.jsx(O.Body,{className:"max-h-[70vh] overflow-y-auto",children:a.jsx("div",{className:"prose prose-sm max-w-none",children:a.jsx(qu,{children:Si})})}),a.jsx(O.Footer,{children:a.jsx(J,{color:"gray",onClick:()=>Ne(!1),children:"Close"})})]}),a.jsxs(O,{show:W,onClose:()=>te(!1),size:"md",children:[a.jsx(O.Header,{children:"Keyboard Shortcuts"}),a.jsx(O.Body,{children:a.jsxs("div",{className:"space-y-4",children:[a.jsx("p",{className:"text-sm text-gray-500 mb-4",children:"Use these keyboard shortcuts to speed up your workflow in the POS terminal."}),["Navigation","Payment","Quantity","Functions","Help","Other"].map(x=>{const k=E.filter(P=>P.category===x);return k.length===0?null:a.jsxs("div",{className:"mb-4",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:x}),a.jsxs("table",{className:"w-full text-sm",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsxs("tr",{children:[a.jsx("th",{className:"py-2 px-4 text-left font-medium text-gray-700",children:"Shortcut"}),a.jsx("th",{className:"py-2 px-4 text-left font-medium text-gray-700",children:"Action"})]})}),a.jsx("tbody",{className:"divide-y divide-gray-200",children:k.map((P,K)=>a.jsxs("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"py-2 px-4",children:a.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg",children:P.key})}),a.jsx("td",{className:"py-2 px-4",children:P.description})]},K))})]})]},x)})]})}),a.jsx(O.Footer,{children:a.jsx(J,{onClick:()=>te(!1),children:"Close"})})]}),a.jsx(Gu,{show:S,onClose:()=>B(!1),onCustomerCreated:Ht}),a.jsx(Ju,{show:$,onClose:()=>I(!1),onConfirm:wt,itemName:(N==null?void 0:N.name)||""}),a.jsx(Gi,{show:G,onClose:()=>C(!1),onConfirm:Be,itemCount:o.length}),a.jsx(Fr,{show:je,onClose:()=>fe(!1),onViewReceipt:Dn}),a.jsxs(O,{show:re,onClose:()=>Ce(!1),size:"7xl",children:[a.jsx(O.Header,{children:"Process Refund"}),a.jsx(O.Body,{children:a.jsx(sl,{onRefundCreated:x=>{Ce(!1),ut.success(`Refund ${x.refund_number} created successfully`)},onClose:()=>Ce(!1)})})]}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 flex-1 min-h-0",children:[a.jsx("div",{className:"lg:col-span-1 p-4 h-full",children:a.jsxs("div",{className:"bg-white rounded-lg shadow h-full flex flex-col",children:[a.jsxs("div",{className:"p-4 border-b border-gray-200",children:[a.jsxs("div",{className:"mb-4",children:[a.jsx(de,{htmlFor:"customer",value:"Customer",className:"mb-2 block"}),a.jsx(Ji,{onSelectCustomer:d,selectedCustomerId:u==null?void 0:u.id,onCreateCustomer:()=>B(!0)})]}),a.jsx("form",{onSubmit:Xe,className:"mb-4",children:a.jsxs("div",{children:[a.jsx(de,{htmlFor:"barcode",value:"Scan Barcode",className:"mb-2 block font-bold"}),a.jsxs("div",{className:"flex gap-2",children:[a.jsx(Te,{ref:w,id:"barcode",type:"text",placeholder:"Focus here and scan barcode (auto-detects)",value:p,onChange:Rt,className:"flex-1",icon:Ui,sizing:"lg"}),a.jsx("div",{className:"w-20",children:a.jsx("input",{type:"number",min:"1",value:h,onFocus:x=>{x.target.select()},onChange:b,onWheel:x=>{x.currentTarget.blur()},className:"w-full h-11 text-center text-sm font-medium bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-150 hover:border-gray-400",placeholder:"Qty"})})]}),a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Automatically adds to cart when barcode is scanned (or press Enter)"})]})}),l&&a.jsx(It,{color:"failure",className:"mb-4",children:l}),a.jsxs("div",{className:"mb-4 product-search-container",children:[a.jsx(de,{htmlFor:"search",value:"Search Products",className:"mb-2 block"}),a.jsxs("div",{className:"flex gap-2",children:[a.jsx("div",{className:"flex-grow",children:a.jsx(Vn,{value:"",onChange:(x,k)=>{k&&(lt(k,h),V(P=>P+1))},placeholder:"Search by name, SKU, or barcode",pageSize:10,instanceId:`pos-product-search-${Q}`},Q)}),a.jsx("div",{className:"w-20",children:a.jsx("input",{type:"number",min:"1",value:h,onFocus:x=>{x.target.select()},onChange:b,onWheel:x=>{x.currentTarget.blur()},className:"w-full h-10 text-center text-sm font-medium bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-150 hover:border-gray-400",placeholder:"Qty"})})]}),a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Type to search products by name, SKU, or barcode"})]})]}),a.jsxs("div",{className:"p-4 border-t border-gray-200 mt-auto",children:[a.jsx("h3",{className:"font-medium text-sm mb-2",children:"Quick Quantity"}),a.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[a.jsx(J,{color:"light",size:"sm",onClick:()=>y(1),children:a.jsxs("div",{className:"flex flex-col items-center",children:[a.jsx("span",{children:"Qty: 1"}),a.jsx("span",{className:"text-xs text-gray-500",children:"1"})]})}),a.jsx(J,{color:"light",size:"sm",onClick:()=>y(5),children:a.jsxs("div",{className:"flex flex-col items-center",children:[a.jsx("span",{children:"Qty: 5"}),a.jsx("span",{className:"text-xs text-gray-500",children:"5"})]})}),a.jsx(J,{color:"light",size:"sm",onClick:()=>y(10),children:a.jsxs("div",{className:"flex flex-col items-center",children:[a.jsx("span",{children:"Qty: 10"}),a.jsx("span",{className:"text-xs text-gray-500",children:"0"})]})})]})]})]})}),a.jsx("div",{className:"lg:col-span-2 p-4 h-full",children:a.jsxs("div",{className:"bg-white rounded-lg shadow h-full flex flex-col",children:[a.jsxs("div",{className:"flex justify-between items-center p-4 border-b border-gray-200 flex-shrink-0",children:[a.jsx("h2",{className:"text-xl font-bold",children:"Current Sale"}),o.length>0&&a.jsx("div",{className:"flex gap-2",children:a.jsxs(J,{color:"failure",size:"sm",onClick:De,children:[a.jsx(nn,{className:"mr-1 h-4 w-4"}),"Clear"]})})]}),a.jsx("div",{className:"flex-1 overflow-y-auto p-4 min-h-0",children:o.length===0?a.jsxs("div",{className:"text-center py-12 h-full flex flex-col justify-center items-center",children:[a.jsx(Vi,{className:"mx-auto h-16 w-16 text-gray-300"}),a.jsx("p",{className:"mt-4 text-gray-500 text-lg",children:"Your cart is empty"}),a.jsx("p",{className:"text-gray-400",children:"Scan a barcode or search for products to add"})]}):a.jsx("div",{className:"space-y-4",children:a.jsxs("table",{className:"w-full text-sm",children:[a.jsx("thead",{className:"bg-gray-50 text-gray-700",children:a.jsxs("tr",{children:[a.jsx("th",{className:"py-3 px-4 text-left",children:"Item"}),a.jsx("th",{className:"py-3 px-4 text-center",children:"Qty"}),a.jsx("th",{className:"py-3 px-4 text-right",children:"Price"}),a.jsx("th",{className:"py-3 px-4 text-right",children:"Total"}),a.jsx("th",{className:"py-3 px-4 text-center",children:"Actions"})]})}),a.jsx("tbody",{className:"divide-y divide-gray-200",children:o.map(x=>a.jsxs("tr",{className:"hover:bg-gray-50",children:[a.jsxs("td",{className:"py-3 px-4",children:[a.jsx("div",{className:"font-medium",children:x.product.name}),a.jsxs("div",{className:"text-xs text-gray-500",children:[x.product.sku&&a.jsxs("span",{className:"mr-2",children:["SKU: ",x.product.sku]}),x.notes&&a.jsxs("span",{className:"italic text-gray-500",children:["Note: ",x.notes]})]})]}),a.jsx("td",{className:"py-3 px-4 text-center",children:a.jsxs("div",{className:"flex items-center justify-center gap-1",children:[a.jsx("button",{type:"button",className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",onClick:()=>Ke(x.id,x.quantity-1),disabled:x.quantity<=1,children:a.jsx(qi,{className:"h-4 w-4"})}),a.jsx("div",{className:"relative",children:a.jsx("input",{type:"number",min:"1",value:x.quantity,onFocus:k=>{k.target.select()},onChange:k=>{const P=k.target.value;if(P==="")return;const K=parseInt(P);!isNaN(K)&&K>0&&Ke(x.id,K)},onBlur:k=>{(k.target.value===""||parseInt(k.target.value)<=0)&&Ke(x.id,1)},onWheel:k=>{k.currentTarget.blur()},className:"w-16 h-8 text-center text-sm font-medium bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-150 hover:border-gray-400"})}),a.jsx("button",{type:"button",className:"flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",onClick:()=>Ke(x.id,x.quantity+1),children:a.jsx(Hn,{className:"h-4 w-4"})})]})}),a.jsx("td",{className:"py-3 px-4 text-right",children:i(x.product.unit_price)}),a.jsx("td",{className:"py-3 px-4 text-right font-medium",children:i(x.product.unit_price*x.quantity)}),a.jsx("td",{className:"py-3 px-4 text-center",children:a.jsxs("div",{className:"flex justify-center gap-1",children:[a.jsx(Zi,{itemId:x.id,initialNotes:x.notes,onSave:Mt}),a.jsx(J,{size:"xs",color:"failure",className:"p-1",onClick:()=>Ge(x.id),title:"Remove item (requires confirmation code)",children:a.jsx(nn,{className:"h-3 w-3"})})]})})]},x.id))})]})})}),a.jsxs("div",{className:"border-t border-gray-200 bg-gray-50 flex-shrink-0",children:[a.jsx("div",{className:"p-4 max-h-64 overflow-y-auto",children:a.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[a.jsxs("div",{children:[u&&a.jsxs("div",{className:"mb-3 p-3 bg-blue-50 rounded-lg border border-blue-100",children:[a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(Rr,{className:"h-4 w-4 text-blue-500"}),a.jsx("span",{className:"font-medium text-sm",children:u.name}),u.loyalty_eligible===!0&&a.jsx(mt,{color:"purple",size:"xs",children:"Loyalty"})]}),u.email&&a.jsx("div",{className:"text-xs text-gray-600 mt-1",children:u.email}),u.phone&&a.jsx("div",{className:"text-xs text-gray-600",children:u.phone}),a.jsx(Xu,{customer:u})]}),a.jsx(Yu,{customer:u,onApplyPoints:Bt,disabled:o.length===0,totalAmount:pe()})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Subtotal"}),a.jsx("span",{children:i(Fe())})]}),a.jsxs("div",{className:"flex justify-between items-center text-sm",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("span",{className:"text-gray-600 mr-1",children:"Discount"}),a.jsx(el,{subtotal:Fe(),onApplyDiscount:Ot,currentDiscount:A})]}),a.jsxs("span",{className:"text-red-500",children:["-",i(Ue())]})]}),H>0&&a.jsxs("div",{className:"flex justify-between items-center text-sm",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("span",{className:"text-gray-600 mr-1",children:"Loyalty Points"}),a.jsxs(mt,{color:"purple",className:"text-xs",children:[L," points"]})]}),a.jsxs("span",{className:"text-purple-500",children:["-",i(H)]})]}),a.jsxs("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:(()=>{const x=t==null?void 0:t.tax_rate,k=(t==null?void 0:t.tax_settings)||{},P=x===0?0:k.vat_rate;return k.tax_enabled&&P>0?`VAT ${P}%${k.vat_inclusive?" (incl.)":""}`:"No Tax"})()}),a.jsx("span",{children:i(st())})]}),a.jsxs("div",{className:"flex justify-between font-bold text-lg pt-2 border-t border-gray-200",children:[a.jsx("span",{children:"Total"}),a.jsx("span",{className:"text-blue-600",children:i(pe())})]})]})]})}),a.jsx("div",{className:"border-t border-gray-300 p-4 bg-white",children:a.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[a.jsx(J,{color:"success",size:"lg",disabled:o.length===0,className:"py-3",onClick:()=>{se("cash"),m(pe().toFixed(2)),ne(!0)},children:a.jsxs("div",{className:"flex items-center",children:[a.jsx(On,{className:"mr-2 h-5 w-5"}),a.jsx("span",{children:"Cash"}),a.jsx("span",{className:"ml-2 text-xs bg-green-700 px-1.5 py-0.5 rounded",children:"Ctrl+P"})]})}),a.jsx(J,{color:"info",size:"lg",disabled:o.length===0,className:"py-3",onClick:()=>{se("card"),ne(!0)},children:a.jsxs("div",{className:"flex items-center",children:[a.jsx(Bn,{className:"mr-2 h-5 w-5"}),a.jsx("span",{children:"Card"}),a.jsx("span",{className:"ml-2 text-xs bg-blue-700 px-1.5 py-0.5 rounded",children:"Ctrl+K"})]})})]})})]})]})})]})]})};export{mc as default};
