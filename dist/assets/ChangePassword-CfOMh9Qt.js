import{b as C,r,j as s,A as f,a6 as n,P as l,c4 as c,B as y,i as S,s as v}from"./index-C6AV3cVN.js";import{C as E}from"./Card-yj7fueH8.js";const q=()=>{const{user:d}=C(),[i,u]=r.useState(""),[t,m]=r.useState(""),[w,h]=r.useState(""),[x,p]=r.useState(!1),[g,a]=r.useState(null),[P,j]=r.useState(null),N=async e=>{if(e.preventDefault(),!d){a("You must be logged in to change your password");return}if(t!==w){a("New passwords do not match");return}if(t.length<8){a("Password must be at least 8 characters long");return}p(!0),a(null),j(null);try{const{error:o}=await v.auth.signInWithPassword({email:d.email||"",password:i});if(o)throw new Error("Current password is incorrect");const{error:b}=await v.auth.updateUser({password:t});if(b)throw b;j("Password changed successfully"),u(""),m(""),h("")}catch(o){console.error("Error changing password:",o),a(o.message||"Failed to change password")}finally{p(!1)}};return s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsxs(E,{children:[s.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Change Password"}),g&&s.jsx(f,{color:"failure",className:"mb-4",children:g}),P&&s.jsx(f,{color:"success",className:"mb-4",children:P}),s.jsxs("form",{onSubmit:N,children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("div",{className:"mb-2 block",children:s.jsx(n,{htmlFor:"currentPassword",value:"Current Password"})}),s.jsx(l,{id:"currentPassword",type:"password",icon:c,value:i,onChange:e=>u(e.target.value),required:!0})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("div",{className:"mb-2 block",children:s.jsx(n,{htmlFor:"newPassword",value:"New Password"})}),s.jsx(l,{id:"newPassword",type:"password",icon:c,value:t,onChange:e=>m(e.target.value),required:!0}),s.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Password must be at least 8 characters long"})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("div",{className:"mb-2 block",children:s.jsx(n,{htmlFor:"confirmPassword",value:"Confirm New Password"})}),s.jsx(l,{id:"confirmPassword",type:"password",icon:c,value:w,onChange:e=>h(e.target.value),required:!0})]}),s.jsxs(y,{type:"submit",disabled:x,children:[x?s.jsx(S,{size:"sm",className:"mr-2"}):null,"Change Password"]})]})]})})};export{q as default};
