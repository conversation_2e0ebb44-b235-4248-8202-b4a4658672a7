import{d as w,r as n,j as e,A as p,a6 as y,P as b,B as N,L as k}from"./index-C6AV3cVN.js";import{F as S}from"./FullLogo-CXg185jT.js";import{a as A}from"./auth-DeOTzV2I.js";const P=()=>{const j=w(),[t,m]=n.useState({email:"",password:""}),[u,r]=n.useState(null),[g,o]=n.useState(!1),x=d=>{const{id:a,value:l}=d.target;m(i=>({...i,[a]:l}))},[f,c]=n.useState(null),v=async d=>{var a,l,i;d.preventDefault(),r(null),c(null),o(!0);try{if(t.password.length<6){r("Password must be at least 6 characters long"),o(!1);return}const{data:s,error:h}=await A(t);if(h)throw h;if(((l=(a=s==null?void 0:s.user)==null?void 0:a.identities)==null?void 0:l.length)===0||((i=s==null?void 0:s.user)==null?void 0:i.email_confirmed_at)===null){c("Registration successful! Please check your email to confirm your account. After confirming, you'll be able to log in and complete your profile setup."),m({email:"",password:""});return}c("Registration successful! You can now log in. After logging in, you'll be prompted to complete your profile setup."),setTimeout(()=>{j("/auth/login")},3e3)}catch(s){s.message.includes("already registered")?r("This email is already registered. Please use a different email or try to log in."):s.message.includes("network")?r("Network error. Please check your internet connection and try again."):r(s.message||"An error occurred during registration")}finally{o(!1)}};return e.jsxs(e.Fragment,{children:[u&&e.jsx(p,{color:"failure",className:"mb-4",children:u}),f&&e.jsx(p,{color:"success",className:"mb-4",children:f}),e.jsxs("form",{onSubmit:v,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"email",value:"Email Address"})}),e.jsx(b,{id:"email",type:"email",sizing:"md",required:!0,value:t.email,onChange:x,className:"form-control form-rounded-xl"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"password",value:"Password"})}),e.jsx(b,{id:"password",type:"password",sizing:"md",required:!0,value:t.password,onChange:x,className:"form-control form-rounded-xl"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Password must be at least 6 characters long"})]}),e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:"After registration, you'll be prompted to set up your profile and organization."})}),e.jsx(N,{color:"primary",type:"submit",className:"w-full",disabled:g,children:g?"Creating Account...":"Sign Up"})]})]})},F={background:"linear-gradient(45deg, rgb(238, 119, 82,0.2), rgb(231, 60, 126,0.2), rgb(35, 166, 213,0.2), rgb(35, 213, 171,0.2))",backgroundSize:"400% 400%",animation:"gradient 15s ease infinite",height:"100vh",overflow:"hidden"},R=()=>e.jsx("div",{style:F,className:"relative overflow-hidden h-screen",children:e.jsx("div",{className:"flex h-full justify-center items-center px-4",children:e.jsx("div",{className:"rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative break-words md:w-96 w-full border-none",children:e.jsxs("div",{className:"flex h-full flex-col justify-center gap-2 p-0 w-full",children:[e.jsx("div",{className:"mx-auto",children:e.jsx(S,{})}),e.jsx("p",{className:"text-sm text-center text-dark my-3",children:"Sign Up for POS System"}),e.jsx("p",{className:"text-xs text-center text-gray-500 mb-4",children:"Create your account to manage your retail business. After registration, you'll set up your profile and organization."}),e.jsx(P,{}),e.jsxs("div",{className:"flex gap-2 text-base text-ld font-medium mt-6 items-center justify-center",children:[e.jsx("p",{children:"Already have an Account?"}),e.jsx(k,{to:"/auth/login",className:"text-primary text-sm font-medium",children:"Sign in"})]})]})})})});export{R as default};
