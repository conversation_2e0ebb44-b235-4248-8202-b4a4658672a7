import{s as x,j as e,M as v,aL as F,bB as G,ah as Y,J as T,U as S,a6 as E,k as D,aM as B,A as z,B as $,h as U,b as V,r as j}from"./index-C6AV3cVN.js";import{P as O}from"./payroll-j3fcCwK0.js";import{P as A,b as M}from"./payables-q7zOb02j.js";import{u as R}from"./currencyFormatter-BsFWv3sX.js";const J=async(r,a,s,t)=>{var m;try{console.log(`🏗️ Creating payables from payroll period ${a}`);const o=await K(r,a);if(!o.success)return o;if((m=o.period)!=null&&m.payables_created)return{success:!1,error:"Payables already created for this payroll period"};const i=await te(r,a);if(i.length>0)return console.warn(`Found ${i.length} existing payables for this payroll period`),{success:!1,error:`Found ${i.length} existing payables for this payroll period. Please check the payables list.`};const c=await Q(r,a);if(!c.success||!c.items)return{success:!1,error:c.error||"No payroll items found"};console.log(`📊 Found ${c.items.length} payroll items to process`),await X(r);const y=[];let n=0,l=0,u=0;if(t.createEmployeePayables){console.log("💰 Creating employee salary payables...");const p=await Z(r,o.period,c.items,s,t);p.success&&p.payables&&(y.push(...p.payables),n=p.payables.length)}if(t.createGovernmentPayables){console.log("🏛️ Creating government remittance payables...");const p=await I(r,o.period,c.items,s,t);p.success&&p.payables&&(y.push(...p.payables),l=p.payables.length)}t.createThirdPartyPayables&&console.log("🏢 Creating third-party payables..."),await re(a,s);const h=y.reduce((p,f)=>p+Number(f.amount),0);return console.log(`✅ Successfully created ${y.length} payables totaling ₱${h.toFixed(2)}`),{success:!0,payables:y,summary:{employeePayables:n,governmentPayables:l,thirdPartyPayables:u,totalAmount:h}}}catch(o){return console.error("Error in createPayablesFromPayroll:",o),{success:!1,error:o.message}}},K=async(r,a)=>{try{const{data:s,error:t}=await x.from("payroll_periods").select("*").eq("organization_id",r).eq("id",a).single();return t||!s?{success:!1,error:"Payroll period not found"}:s.status!==O.APPROVED?{success:!1,error:"Payroll period must be approved before creating payables"}:{success:!0,period:s}}catch(s){return{success:!1,error:s.message}}},Q=async(r,a)=>{try{const{data:s,error:t}=await x.from("payroll_items").select(`
        *,
        employee:employees(
          id, first_name, last_name, employee_number, email
        )
      `).eq("organization_id",r).eq("payroll_period_id",a);return t?{success:!1,error:t.message}:!s||s.length===0?{success:!1,error:"No payroll items found for this period"}:{success:!0,items:s}}catch(s){return{success:!1,error:s.message}}},X=async r=>{try{console.log("🏛️ Ensuring government agencies exist as suppliers...");const{error:a}=await x.rpc("add_government_agencies_as_suppliers",{p_organization_id:r});a?console.warn("Warning: Could not add government agencies:",a.message):console.log("✅ Government agencies ensured as suppliers")}catch(a){console.warn("Warning: Could not add government agencies:",a.message)}},Z=async(r,a,s,t,m)=>{var o,i,c,y,n,l,u,h,p,f;try{const _=[],b=[];for(const d of s){if(Number(d.net_pay)<=0){console.log(`⏭️ Skipping ${(o=d.employee)==null?void 0:o.first_name} ${(i=d.employee)==null?void 0:i.last_name} - zero/negative net pay`);continue}const w={source_type:A.PAYROLL,source_id:d.id,employee_id:d.employee_id,reference_number:`SAL-${a.name.replace(/\s+/g,"-")}-${(c=d.employee)==null?void 0:c.employee_number}`,invoice_date:a.end_date,due_date:((y=m.customDueDates)==null?void 0:y.salary)||a.payment_date,amount:Number(d.net_pay),vat_amount:0,withholding_tax_rate:0,withholding_tax_amount:0,currency:"PHP",category:"salary",notes:`Salary payable for ${(n=d.employee)==null?void 0:n.first_name} ${(l=d.employee)==null?void 0:l.last_name} - ${a.name}`},g=await M(r,w,t);g.success&&g.payable?(_.push(g.payable),b.push({organization_id:r,payroll_period_id:a.id,payroll_item_id:d.id,payable_id:g.payable.id,payable_type:"employee_salary",amount:Number(d.net_pay),created_by:t}),console.log(`✅ Created salary payable for ${(u=d.employee)==null?void 0:u.first_name} ${(h=d.employee)==null?void 0:h.last_name}: ₱${Number(d.net_pay).toFixed(2)}`)):console.error(`❌ Failed to create salary payable for ${(p=d.employee)==null?void 0:p.first_name} ${(f=d.employee)==null?void 0:f.last_name}:`,g.error)}return b.length>0&&await q(b),{success:!0,payables:_}}catch(_){return console.error("Error creating employee salary payables:",_),{success:!1,error:_.message}}},I=async(r,a,s,t,m)=>{try{const o=[],i=[],c=ee(s),y=await ae(r);if(c.sss.total>0){const n=y.find(l=>l.name.includes("SSS"));if(n){const l=await N({organizationId:r,payrollPeriod:a,supplierId:n.id,agencyName:"SSS",amount:c.sss.total,payableType:"sss_contribution",userId:t,options:m});l.success&&l.payable&&(o.push(l.payable),i.push({organization_id:r,payroll_period_id:a.id,payable_id:l.payable.id,payable_type:"sss_contribution",amount:c.sss.total,created_by:t}))}}if(c.philhealth.total>0){const n=y.find(l=>l.name.includes("PhilHealth"));if(n){const l=await N({organizationId:r,payrollPeriod:a,supplierId:n.id,agencyName:"PhilHealth",amount:c.philhealth.total,payableType:"philhealth_contribution",userId:t,options:m});l.success&&l.payable&&(o.push(l.payable),i.push({organization_id:r,payroll_period_id:a.id,payable_id:l.payable.id,payable_type:"philhealth_contribution",amount:c.philhealth.total,created_by:t}))}}if(c.pagibig.total>0){const n=y.find(l=>l.name.includes("Pag-IBIG"));if(n){const l=await N({organizationId:r,payrollPeriod:a,supplierId:n.id,agencyName:"Pag-IBIG",amount:c.pagibig.total,payableType:"pagibig_contribution",userId:t,options:m});l.success&&l.payable&&(o.push(l.payable),i.push({organization_id:r,payroll_period_id:a.id,payable_id:l.payable.id,payable_type:"pagibig_contribution",amount:c.pagibig.total,created_by:t}))}}if(c.withholding_tax>0){const n=y.find(l=>l.name.includes("BIR"));if(n){const l=await N({organizationId:r,payrollPeriod:a,supplierId:n.id,agencyName:"BIR",amount:c.withholding_tax,payableType:"withholding_tax",userId:t,options:m});l.success&&l.payable&&(o.push(l.payable),i.push({organization_id:r,payroll_period_id:a.id,payable_id:l.payable.id,payable_type:"withholding_tax",amount:c.withholding_tax,created_by:t}))}}return i.length>0&&await q(i),console.log(`✅ Created ${o.length} government remittance payables`),{success:!0,payables:o}}catch(o){return console.error("Error creating government remittance payables:",o),{success:!1,error:o.message}}},ee=r=>{const a={sss:{employee:0,employer:0,total:0},philhealth:{employee:0,employer:0,total:0},pagibig:{employee:0,employer:0,total:0},withholding_tax:0};return r.forEach(s=>{const t=Number(s.sss_contribution)||0,m=Number(s.philhealth_contribution)||0,o=Number(s.pagibig_contribution)||0,i=Number(s.withholding_tax)||0;a.sss.employee+=t,a.philhealth.employee+=m,a.pagibig.employee+=o,a.withholding_tax+=i,a.sss.employer+=0,a.philhealth.employer+=0,a.pagibig.employer+=0}),a.sss.total=a.sss.employee,a.philhealth.total=a.philhealth.employee,a.pagibig.total=a.pagibig.employee,console.log("Government Contributions for Payables (matching calculation details):",{sss:{employee:a.sss.employee,total:a.sss.total},philhealth:{employee:a.philhealth.employee,total:a.philhealth.total},pagibig:{employee:a.pagibig.employee,total:a.pagibig.total},withholdingTax:a.withholding_tax}),a},ae=async r=>{try{const{data:a,error:s}=await x.from("suppliers").select("id, name, supplier_type, payment_terms_days").eq("organization_id",r).eq("supplier_type","government");return s?(console.error("Error fetching government suppliers:",s),[]):a||[]}catch(a){return console.error("Error fetching government suppliers:",a),[]}},N=async r=>{var a;try{const{organizationId:s,payrollPeriod:t,supplierId:m,agencyName:o,amount:i,payableType:c,userId:y,options:n}=r,l=se(t.end_date,o),u=`${t.id}-${c}`,h={source_type:A.PAYROLL,source_id:u,supplier_id:m,reference_number:`${o.toUpperCase()}-${t.name.replace(/\s+/g,"-")}`,invoice_date:t.end_date,due_date:((a=n.customDueDates)==null?void 0:a.government)||l,amount:i,vat_amount:0,withholding_tax_rate:0,withholding_tax_amount:0,currency:"PHP",category:"government_remittance",notes:`${o} remittance for payroll period: ${t.name}`},p=await M(s,h,y);return p.success?console.log(`✅ Created ${o} payable: ₱${i.toFixed(2)}`):console.error(`❌ Failed to create ${o} payable:`,p.error),p}catch(s){return console.error(`Error creating ${r.agencyName} payable:`,s),{success:!1,error:s.message}}},se=(r,a)=>{const s=new Date(r),t=new Date(s.getFullYear(),s.getMonth()+1,1);return a.includes("BIR")?new Date(t.getFullYear(),t.getMonth(),15).toISOString().split("T")[0]:new Date(t.getFullYear(),t.getMonth(),30).toISOString().split("T")[0]},q=async r=>{try{const{error:a}=await x.from("payroll_payable_mappings").insert(r);a?console.error("Error creating payroll payable mappings:",a):console.log(`✅ Created ${r.length} payroll payable mappings`)}catch(a){console.error("Error creating payroll payable mappings:",a)}},re=async(r,a)=>{try{const{error:s}=await x.from("payroll_periods").update({payables_created:!0,payables_created_at:new Date().toISOString(),payables_created_by:a}).eq("id",r);s?console.error("Error marking payroll payables created:",s):console.log("✅ Marked payroll period as having payables created")}catch(s){console.error("Error marking payroll payables created:",s)}},ye=async(r,a)=>{try{const{data:s,error:t}=await x.from("payroll_periods").select("payables_created").eq("organization_id",r).eq("id",a).single();return t||!s?!1:s.payables_created||!1}catch(s){return console.error("Error checking payroll payables status:",s),!1}},te=async(r,a)=>{try{const{data:s,error:t}=await x.from("payroll_payable_mappings").select(`
        payable_id,
        payables!inner(id, source_id, reference_number, category)
      `).eq("organization_id",r).eq("payroll_period_id",a);return t?(console.error("Error checking existing payables via mappings:",t),[]):(s==null?void 0:s.map(o=>o.payables).filter(Boolean))||[]}catch(s){return console.error("Error checking existing payables:",s),[]}},le=({show:r,onClose:a,onConfirm:s,payrollPeriodName:t,totalNetPay:m,totalEmployees:o,options:i,onOptionsChange:c,loading:y,result:n})=>{const l=R(),u=m*.15;return e.jsxs(v,{show:r,onClose:a,size:"lg",children:[e.jsx(v.Header,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(F,{className:"h-5 w-5 text-blue-600"}),"Create Payables from Payroll"]})}),e.jsx(v.Body,{children:e.jsx("div",{className:"space-y-6",children:n?e.jsx("div",{className:"space-y-4",children:n.success?e.jsxs("div",{className:"flex items-start gap-3 p-4 bg-green-50 rounded-lg border border-green-200",children:[e.jsx(B,{className:"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-green-800",children:"Success!"}),e.jsx("p",{className:"text-sm text-green-700 mt-1",children:n.message}),n.summary&&e.jsxs("div",{className:"mt-3 text-sm text-green-700 space-y-1",children:[n.summary.employeePayables>0&&e.jsxs("div",{children:["• ",n.summary.employeePayables," employee salary payables"]}),n.summary.governmentPayables>0&&e.jsxs("div",{children:["• ",n.summary.governmentPayables," government remittance payables"]}),n.summary.thirdPartyPayables>0&&e.jsxs("div",{children:["• ",n.summary.thirdPartyPayables," third-party payables"]})]})]})]}):e.jsxs(z,{color:"failure",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(T,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:"Error"})]}),e.jsx("div",{className:"mt-2 text-sm",children:n.message})]})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[e.jsxs("h4",{className:"font-medium text-blue-800 mb-2",children:["Payroll Period: ",t]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{className:"flex items-center gap-2 text-blue-700",children:[e.jsx(G,{className:"h-4 w-4"}),e.jsxs("span",{children:[o," employees"]})]}),e.jsxs("div",{className:"flex items-center gap-2 text-blue-700",children:[e.jsx(Y,{className:"h-4 w-4"}),e.jsxs("span",{children:["Total Net Pay: ",l(m)]})]})]})]}),e.jsxs("div",{className:"flex items-start gap-3 p-4 bg-yellow-50 rounded-lg border border-yellow-200",children:[e.jsx(T,{className:"h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-yellow-800",children:"Confirm Action"}),e.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"This will create payables for the approved payroll period. Choose which types of payables to create:"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(S,{checked:i.createEmployeePayables,onChange:h=>c({...i,createEmployeePayables:h.target.checked}),className:"mt-1"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(G,{className:"h-4 w-4 text-green-600"}),e.jsx(E,{className:"font-medium text-gray-900",children:"Employee Salary Payables"})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Create individual payables for each employee's net pay"}),e.jsxs("div",{className:"text-sm font-medium text-green-600",children:[o," payables • ",l(m)]})]})]})}),e.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(S,{checked:i.createGovernmentPayables,onChange:h=>c({...i,createGovernmentPayables:h.target.checked}),className:"mt-1"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(D,{className:"h-4 w-4 text-blue-600"}),e.jsx(E,{className:"font-medium text-gray-900",children:"Government Remittances"})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Create payables for government contributions and withholding tax"}),e.jsxs("div",{className:"text-xs text-gray-500 space-y-1",children:[e.jsx("div",{children:"• SSS Contributions (Employee + Employer)"}),e.jsx("div",{children:"• PhilHealth Contributions (Employee + Employer)"}),e.jsx("div",{children:"• Pag-IBIG Contributions (Employee + Employer)"}),e.jsx("div",{children:"• BIR Withholding Tax"})]}),e.jsxs("div",{className:"text-sm font-medium text-blue-600 mt-2",children:["~4 payables • ",l(u)," (estimated)"]})]})]})}),e.jsx("div",{className:"border border-gray-200 rounded-lg p-4 opacity-50",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(S,{checked:i.createThirdPartyPayables,onChange:h=>c({...i,createThirdPartyPayables:h.target.checked}),disabled:!0,className:"mt-1"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(D,{className:"h-4 w-4 text-gray-400"}),e.jsx(E,{className:"font-medium text-gray-500",children:"Third-party Deductions"}),e.jsx("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:"Coming Soon"})]}),e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Create payables for loan deductions and other third-party payments"})]})]})})]}),(i.createEmployeePayables||i.createGovernmentPayables)&&e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg border",children:[e.jsx("h5",{className:"font-medium text-gray-900 mb-2",children:"Summary"}),e.jsxs("div",{className:"text-sm text-gray-600 space-y-1",children:[i.createEmployeePayables&&e.jsxs("div",{children:["• ",o," employee salary payables"]}),i.createGovernmentPayables&&e.jsx("div",{children:"• ~4 government remittance payables"}),e.jsxs("div",{className:"font-medium text-gray-900 mt-2",children:["Total estimated: ",l((i.createEmployeePayables?m:0)+(i.createGovernmentPayables?u:0))]})]})]})]})})}),e.jsx(v.Footer,{children:e.jsxs("div",{className:"flex justify-between w-full",children:[e.jsx($,{color:"gray",onClick:a,disabled:y,children:n!=null&&n.success?"Close":"Cancel"}),!n&&e.jsx($,{color:"primary",onClick:s,disabled:y||!i.createEmployeePayables&&!i.createGovernmentPayables,isProcessing:y,children:y?"Creating Payables...":"Create Payables"})]})})]})},me=({payrollPeriodId:r,payrollPeriodName:a,payrollStatus:s,totalNetPay:t,totalEmployees:m,payablesCreated:o=!1,onSuccess:i,className:c=""})=>{const{currentOrganization:y}=U(),{user:n}=V(),l=R(),[u,h]=j.useState(!1),[p,f]=j.useState(!1),[_,b]=j.useState(null),[d,w]=j.useState({createEmployeePayables:!0,createGovernmentPayables:!0,createThirdPartyPayables:!1,groupByEmployee:!0}),g=s===O.APPROVED&&!o&&y&&n,H=async()=>{if(!(!y||!n)){h(!0),b(null);try{const{success:C,summary:P,error:k}=await J(y.id,r,n.id,d);if(k)b({success:!1,message:k});else if(C&&P){const W=P.employeePayables+P.governmentPayables+P.thirdPartyPayables;b({success:!0,message:`Successfully created ${W} payables totaling ${l(P.totalAmount)}`,summary:P}),i&&i()}}catch(C){b({success:!1,message:C.message||"Failed to create payables"})}finally{h(!1)}}},L=()=>{f(!1),b(null)};return o?e.jsx("div",{className:`flex items-center gap-2 ${c}`,children:e.jsxs("div",{className:"flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm",children:[e.jsx(B,{className:"mr-1 h-4 w-4"}),"Sent to Payables"]})}):e.jsxs(e.Fragment,{children:[e.jsxs($,{color:g?"primary":"gray",size:"sm",disabled:!g||u,onClick:()=>f(!0),className:c,children:[e.jsx(F,{className:"mr-2 h-4 w-4"}),u?"Creating...":"Send to Payables"]}),e.jsx(le,{show:p,onClose:L,onConfirm:H,payrollPeriodName:a,totalNetPay:t,totalEmployees:m,options:d,onOptionsChange:w,loading:u,result:_})]})};export{me as S,ye as h};
