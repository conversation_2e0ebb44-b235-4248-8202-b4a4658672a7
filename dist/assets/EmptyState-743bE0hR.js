import{j as e,B as x}from"./index-C6AV3cVN.js";const i=({icon:s,title:a,description:t,actionText:r,onAction:m,className:l=""})=>e.jsxs("div",{className:`p-8 flex flex-col items-center justify-center text-center ${l}`,children:[s&&e.jsx("div",{className:"text-gray-400 mb-4",children:s}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:a}),t&&e.jsx("p",{className:"text-gray-500 mb-4 max-w-md",children:t}),r&&m&&e.jsx(x,{color:"primary",size:"sm",onClick:m,children:r})]});export{i as E};
