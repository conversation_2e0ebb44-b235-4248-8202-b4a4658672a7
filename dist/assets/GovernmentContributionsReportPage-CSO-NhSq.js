import{s as A,r as f,j as e,a6 as H,a7 as D,B as R,q as O,A as F,G as B,_ as s,e as k,i as Y,I as T,T as G,d as W,h as K,o as Q,k as U,P as $}from"./index-C6AV3cVN.js";import{C as M}from"./Card-yj7fueH8.js";import{a as q}from"./payrollReporting-DzocG2hh.js";import{P as X}from"./PageTitle-FHPo8gWi.js";import{f as o}from"./formatters-Cypx7G-j.js";import"./payroll-j3fcCwK0.js";const Z=async n=>{try{const{organizationId:a,month:i,year:y,reportTypes:j,format:b}=n,v=new Date(y,i-1,1),u=new Date(y,i,0),{data:w,error:S}=await A.from("organizations").select("*").eq("id",a).single();if(S)throw new Error(`Error fetching organization details: ${S.message}`);const{data:m,error:p}=await A.from("organization_government_ids").select("*").eq("organization_id",a).single();if(p&&p.code!=="PGRST116")throw new Error(`Error fetching organization government IDs: ${p.message}`);const{data:g,error:c}=await q({organizationId:a,startDate:v,endDate:u});if(c)throw new Error(`Error fetching government contributions data: ${c}`);if(!g||!g.items||g.items.length===0)return{success:!1,reports:[],error:"No contribution data found for the specified period"};const d=await Promise.all(j.map(async x=>{try{if(x==="sss"&&!(m!=null&&m.sss_number))return{type:x,error:"Missing SSS employer number"};if(x==="philhealth"&&!(m!=null&&m.philhealth_number))return{type:x,error:"Missing PhilHealth employer number"};if(x==="pagibig"&&!(m!=null&&m.pagibig_number))return{type:x,error:"Missing Pag-IBIG employer number"};const P=`${x}_report_${y}_${i.toString().padStart(2,"0")}.${b}`;return{type:x,url:`/reports/${P}`}}catch(P){return{type:x,error:`Error processing ${x} report: ${P.message}`}}}));return{success:d.every(x=>!x.error),reports:d}}catch(a){return console.error("Error in batch processing government reports:",a),{success:!1,reports:[],error:a.message}}},J=async(n,a)=>{try{let i=A.from("employees").select("id, first_name, last_name, employee_number");a==="sss"?i=i.is("sss_number",null):a==="philhealth"?i=i.is("philhealth_number",null):a==="pagibig"&&(i=i.is("pagibig_number",null)),i=i.eq("organization_id",n);const{data:y,error:j}=await i;if(j)throw new Error(`Error validating employee data: ${j.message}`);const{data:b,error:v}=await A.from("organization_government_ids").select("*").eq("organization_id",n).single();if(v&&v.code!=="PGRST116")throw new Error(`Error fetching organization government IDs: ${v.message}`);const u=[];return a==="sss"&&!(b!=null&&b.sss_number)&&u.push("SSS Employer Number"),a==="philhealth"&&!(b!=null&&b.philhealth_number)&&u.push("PhilHealth Employer Number"),a==="pagibig"&&!(b!=null&&b.pagibig_number)&&u.push("Pag-IBIG Employer Number"),{valid:(y==null?void 0:y.length)===0&&u.length===0,missingEmployees:y,missingOrganizationData:u}}catch(i){return console.error("Error validating government contribution data:",i),{valid:!1,error:i.message}}},ee=async n=>{try{const{data:a,error:i}=await A.from("organization_government_ids").select("*").eq("organization_id",n).single();if(i){if(i.code==="PGRST116")return{data:{sssNumber:"",philHealthNumber:"",pagibigNumber:"",tinNumber:""}};throw new Error(i.message)}return{data:{sssNumber:a.sss_number||"",philHealthNumber:a.philhealth_number||"",pagibigNumber:a.pagibig_number||"",tinNumber:a.tin_number||""}}}catch(a){return console.error("Error getting organization government IDs:",a),{error:a.message}}},se=({data:n,month:a,year:i,employerName:y,employerSSS:j,employerAddress:b})=>{const[v,u]=f.useState("r3"),[w,S]=f.useState(!1),m=new Map;n.items.forEach(l=>{const t=l.employee_id,h=l.employee;m.has(t)||m.set(t,{id:t,firstName:h.first_name,middleName:h.middle_name||"",lastName:h.last_name,sssNumber:h.sss_number||"",totalSSS:0,employeeShare:0,employerShare:0,ecContribution:0,periods:[]});const N=m.get(t),E=Number(l.sss_contribution||0),z=E*(8.5/4.5),V=10;N.totalSSS+=E,N.employeeShare+=E,N.employerShare+=z,N.ecContribution+=V,N.periods.push({periodId:l.payroll_period.id,periodName:l.payroll_period.name,startDate:l.payroll_period.start_date,endDate:l.payroll_period.end_date,sssContribution:E})});const p=Array.from(m.values()),g=p.reduce((l,t)=>l+t.employeeShare,0),c=p.reduce((l,t)=>l+t.employerShare,0),d=p.reduce((l,t)=>l+t.ecContribution,0),C=g+c+d,x=p.filter(l=>!l.sssNumber),P=x.length>0,_=["January","February","March","April","May","June","July","August","September","October","November","December"][a-1],r=()=>{S(!0),!P&&alert("Export functionality would generate an official SSS R-3 form here")};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(M,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"SSS Contribution Report (R-3)"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx(H,{htmlFor:"report-type",value:"Report Type"}),e.jsxs(D,{id:"report-type",value:v,onChange:l=>u(l.target.value),className:"w-40",children:[e.jsx("option",{value:"r3",children:"R-3 (Monthly)"}),e.jsx("option",{value:"r3a",children:"R-3A (Adjustment)"})]})]}),e.jsxs(R,{color:"light",onClick:r,children:[e.jsx(O,{className:"mr-2 h-5 w-5"}),"Export for SSS"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Employer Name"}),e.jsx("p",{className:"text-lg",children:y})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"SSS Employer Number"}),e.jsx("p",{className:"text-lg",children:j})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Employer Address"}),e.jsx("p",{className:"text-lg",children:b})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Applicable Month/Year"}),e.jsxs("p",{className:"text-lg",children:[_," ",i]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-blue-700",children:"Total Employee Share"}),e.jsx("p",{className:"text-xl font-bold text-blue-700",children:o(g)})]}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-green-700",children:"Total Employer Share"}),e.jsx("p",{className:"text-xl font-bold text-green-700",children:o(c)})]}),e.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-purple-700",children:"Grand Total"}),e.jsx("p",{className:"text-xl font-bold text-purple-700",children:o(C)})]})]}),w&&P&&e.jsxs(F,{color:"failure",icon:B,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Validation Error!"})," ",x.length," employee(s) missing SSS numbers. Please update employee records before exporting."]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(s,{children:[e.jsxs(s.Head,{children:[e.jsx(s.HeadCell,{className:"w-10",children:"No."}),e.jsx(s.HeadCell,{children:"SSS Number"}),e.jsx(s.HeadCell,{children:"Employee Name"}),e.jsx(s.HeadCell,{children:"Employee Share"}),e.jsx(s.HeadCell,{children:"Employer Share"}),e.jsx(s.HeadCell,{children:"EC"}),e.jsx(s.HeadCell,{children:"Total"}),e.jsx(s.HeadCell,{children:"Remarks"})]}),e.jsx(s.Body,{children:p.map((l,t)=>{const h=!l.sssNumber,N=l.employeeShare+l.employerShare+l.ecContribution;return e.jsxs(s.Row,{className:`${h?"bg-red-50":"bg-white"}`,children:[e.jsx(s.Cell,{children:t+1}),e.jsx(s.Cell,{children:l.sssNumber||e.jsx(k,{color:"failure",children:"Missing"})}),e.jsx(s.Cell,{className:"font-medium",children:`${l.lastName}, ${l.firstName} ${l.middleName.charAt(0)}`.trim()}),e.jsx(s.Cell,{children:o(l.employeeShare)}),e.jsx(s.Cell,{children:o(l.employerShare)}),e.jsx(s.Cell,{children:o(l.ecContribution)}),e.jsx(s.Cell,{className:"font-medium",children:o(N)}),e.jsx(s.Cell,{children:h&&e.jsx("span",{className:"text-red-600 text-xs",children:"Missing SSS number"})})]},l.id)})}),e.jsx(s.Footer,{children:e.jsxs(s.Row,{className:"font-semibold text-gray-900",children:[e.jsx(s.Cell,{colSpan:3,children:"Totals"}),e.jsx(s.Cell,{children:o(g)}),e.jsx(s.Cell,{children:o(c)}),e.jsx(s.Cell,{children:o(d)}),e.jsx(s.Cell,{children:o(C)}),e.jsx(s.Cell,{})]})})]})}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Remittance Instructions"}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"This report should be submitted to SSS along with payment on or before the 10th day (for employers with 10 or more employees) or the 15th day (for employers with less than 10 employees) of the month following the applicable month."}),e.jsx("p",{className:"text-sm text-gray-600",children:"Payment can be made through SSS accredited banks, GCash, or the SSS website."})]})]})},re=({data:n,month:a,year:i,employerName:y,employerPhilHealth:j,employerAddress:b})=>{const[v,u]=f.useState("rf1"),[w,S]=f.useState(!1),m=new Map;n.items.forEach(r=>{const l=r.employee_id,t=r.employee;m.has(l)||m.set(l,{id:l,firstName:t.first_name,middleName:t.middle_name||"",lastName:t.last_name,philHealthNumber:t.philhealth_number||"",totalPhilHealth:0,employeeShare:0,employerShare:0,periods:[]});const h=m.get(l),N=Number(r.philhealth_contribution||0),E=N;h.totalPhilHealth+=N,h.employeeShare+=N,h.employerShare+=E,h.periods.push({periodId:r.payroll_period.id,periodName:r.payroll_period.name,startDate:r.payroll_period.start_date,endDate:r.payroll_period.end_date,philHealthContribution:N})});const p=Array.from(m.values()),g=p.reduce((r,l)=>r+l.employeeShare,0),c=p.reduce((r,l)=>r+l.employerShare,0),d=g+c,C=p.filter(r=>!r.philHealthNumber),x=C.length>0,I=["January","February","March","April","May","June","July","August","September","October","November","December"][a-1],_=()=>{S(!0),!x&&alert("Export functionality would generate an official PhilHealth RF-1 form here")};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(M,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"PhilHealth Contribution Report (RF-1)"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx(H,{htmlFor:"report-type",value:"Report Type"}),e.jsxs(D,{id:"report-type",value:v,onChange:r=>u(r.target.value),className:"w-40",children:[e.jsx("option",{value:"rf1",children:"RF-1 (Monthly)"}),e.jsx("option",{value:"rf1a",children:"RF-1A (Adjustment)"})]})]}),e.jsxs(R,{color:"light",onClick:_,children:[e.jsx(O,{className:"mr-2 h-5 w-5"}),"Export for PhilHealth"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Employer Name"}),e.jsx("p",{className:"text-lg",children:y})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"PhilHealth Employer Number (PEN)"}),e.jsx("p",{className:"text-lg",children:j})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Employer Address"}),e.jsx("p",{className:"text-lg",children:b})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Applicable Month/Year"}),e.jsxs("p",{className:"text-lg",children:[I," ",i]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-blue-700",children:"Total Employee Share"}),e.jsx("p",{className:"text-xl font-bold text-blue-700",children:o(g)})]}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-green-700",children:"Total Employer Share"}),e.jsx("p",{className:"text-xl font-bold text-green-700",children:o(c)})]}),e.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-purple-700",children:"Grand Total"}),e.jsx("p",{className:"text-xl font-bold text-purple-700",children:o(d)})]})]}),w&&x&&e.jsxs(F,{color:"failure",icon:B,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Validation Error!"})," ",C.length," employee(s) missing PhilHealth numbers. Please update employee records before exporting."]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(s,{children:[e.jsxs(s.Head,{children:[e.jsx(s.HeadCell,{className:"w-10",children:"No."}),e.jsx(s.HeadCell,{children:"PhilHealth Number"}),e.jsx(s.HeadCell,{children:"Employee Name"}),e.jsx(s.HeadCell,{children:"Employee Share"}),e.jsx(s.HeadCell,{children:"Employer Share"}),e.jsx(s.HeadCell,{children:"Total"}),e.jsx(s.HeadCell,{children:"Remarks"})]}),e.jsx(s.Body,{children:p.map((r,l)=>{const t=!r.philHealthNumber,h=r.employeeShare+r.employerShare;return e.jsxs(s.Row,{className:`${t?"bg-red-50":"bg-white"}`,children:[e.jsx(s.Cell,{children:l+1}),e.jsx(s.Cell,{children:r.philHealthNumber||e.jsx(k,{color:"failure",children:"Missing"})}),e.jsx(s.Cell,{className:"font-medium",children:`${r.lastName}, ${r.firstName} ${r.middleName.charAt(0)}`.trim()}),e.jsx(s.Cell,{children:o(r.employeeShare)}),e.jsx(s.Cell,{children:o(r.employerShare)}),e.jsx(s.Cell,{className:"font-medium",children:o(h)}),e.jsx(s.Cell,{children:t&&e.jsx("span",{className:"text-red-600 text-xs",children:"Missing PhilHealth number"})})]},r.id)})}),e.jsx(s.Footer,{children:e.jsxs(s.Row,{className:"font-semibold text-gray-900",children:[e.jsx(s.Cell,{colSpan:3,children:"Totals"}),e.jsx(s.Cell,{children:o(g)}),e.jsx(s.Cell,{children:o(c)}),e.jsx(s.Cell,{children:o(d)}),e.jsx(s.Cell,{})]})})]})}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Remittance Instructions"}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"This report should be submitted to PhilHealth along with payment on or before the 10th day of the month following the applicable month."}),e.jsx("p",{className:"text-sm text-gray-600",children:"Payment can be made through PhilHealth accredited banks, GCash, or the PhilHealth Electronic Premium Remittance System (EPRS)."})]})]})},le=({data:n,month:a,year:i,employerName:y,employerPagibig:j,employerAddress:b})=>{const[v,u]=f.useState("mcrf"),[w,S]=f.useState(!1),m=new Map;n.items.forEach(r=>{const l=r.employee_id,t=r.employee;m.has(l)||m.set(l,{id:l,firstName:t.first_name,middleName:t.middle_name||"",lastName:t.last_name,pagibigNumber:t.pagibig_number||"",totalPagibig:0,employeeShare:0,employerShare:0,periods:[]});const h=m.get(l),N=Number(r.pagibig_contribution||0),E=N;h.totalPagibig+=N,h.employeeShare+=N,h.employerShare+=E,h.periods.push({periodId:r.payroll_period.id,periodName:r.payroll_period.name,startDate:r.payroll_period.start_date,endDate:r.payroll_period.end_date,pagibigContribution:N})});const p=Array.from(m.values()),g=p.reduce((r,l)=>r+l.employeeShare,0),c=p.reduce((r,l)=>r+l.employerShare,0),d=g+c,C=p.filter(r=>!r.pagibigNumber),x=C.length>0,I=["January","February","March","April","May","June","July","August","September","October","November","December"][a-1],_=()=>{S(!0),!x&&alert("Export functionality would generate an official Pag-IBIG MCRF form here")};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(M,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Pag-IBIG Contribution Report (MCRF)"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx(H,{htmlFor:"report-type",value:"Report Type"}),e.jsxs(D,{id:"report-type",value:v,onChange:r=>u(r.target.value),className:"w-40",children:[e.jsx("option",{value:"mcrf",children:"MCRF (Monthly)"}),e.jsx("option",{value:"mcrf_adj",children:"MCRF-Adjustment"})]})]}),e.jsxs(R,{color:"light",onClick:_,children:[e.jsx(O,{className:"mr-2 h-5 w-5"}),"Export for Pag-IBIG"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Employer Name"}),e.jsx("p",{className:"text-lg",children:y})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Pag-IBIG Employer Number"}),e.jsx("p",{className:"text-lg",children:j})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Employer Address"}),e.jsx("p",{className:"text-lg",children:b})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"Applicable Month/Year"}),e.jsxs("p",{className:"text-lg",children:[I," ",i]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-blue-700",children:"Total Employee Share"}),e.jsx("p",{className:"text-xl font-bold text-blue-700",children:o(g)})]}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-green-700",children:"Total Employer Share"}),e.jsx("p",{className:"text-xl font-bold text-green-700",children:o(c)})]}),e.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-purple-700",children:"Grand Total"}),e.jsx("p",{className:"text-xl font-bold text-purple-700",children:o(d)})]})]}),w&&x&&e.jsxs(F,{color:"failure",icon:B,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Validation Error!"})," ",C.length," employee(s) missing Pag-IBIG numbers. Please update employee records before exporting."]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(s,{children:[e.jsxs(s.Head,{children:[e.jsx(s.HeadCell,{className:"w-10",children:"No."}),e.jsx(s.HeadCell,{children:"Pag-IBIG MID Number"}),e.jsx(s.HeadCell,{children:"Employee Name"}),e.jsx(s.HeadCell,{children:"Employee Share"}),e.jsx(s.HeadCell,{children:"Employer Share"}),e.jsx(s.HeadCell,{children:"Total"}),e.jsx(s.HeadCell,{children:"Remarks"})]}),e.jsx(s.Body,{children:p.map((r,l)=>{const t=!r.pagibigNumber,h=r.employeeShare+r.employerShare;return e.jsxs(s.Row,{className:`${t?"bg-red-50":"bg-white"}`,children:[e.jsx(s.Cell,{children:l+1}),e.jsx(s.Cell,{children:r.pagibigNumber||e.jsx(k,{color:"failure",children:"Missing"})}),e.jsx(s.Cell,{className:"font-medium",children:`${r.lastName}, ${r.firstName} ${r.middleName.charAt(0)}`.trim()}),e.jsx(s.Cell,{children:o(r.employeeShare)}),e.jsx(s.Cell,{children:o(r.employerShare)}),e.jsx(s.Cell,{className:"font-medium",children:o(h)}),e.jsx(s.Cell,{children:t&&e.jsx("span",{className:"text-red-600 text-xs",children:"Missing Pag-IBIG number"})})]},r.id)})}),e.jsx(s.Footer,{children:e.jsxs(s.Row,{className:"font-semibold text-gray-900",children:[e.jsx(s.Cell,{colSpan:3,children:"Totals"}),e.jsx(s.Cell,{children:o(g)}),e.jsx(s.Cell,{children:o(c)}),e.jsx(s.Cell,{children:o(d)}),e.jsx(s.Cell,{})]})})]})}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Remittance Instructions"}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"This report should be submitted to Pag-IBIG along with payment on or before the 15th day of the month following the applicable month."}),e.jsx("p",{className:"text-sm text-gray-600",children:"Payment can be made through Pag-IBIG accredited banks, GCash, or the Pag-IBIG Fund Electronic Collection System."})]})]})},te=({data:n,organizationName:a,organizationDetails:i,isLoading:y=!1,onBatchProcess:j})=>{const[b,v]=f.useState(0),[u,w]=f.useState(new Date().getMonth()+1),[S,m]=f.useState(new Date().getFullYear()),p=["January","February","March","April","May","June","July","August","September","October","November","December"],g=()=>{j?j():alert("Batch processing would generate all reports at once")};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(M,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Government Contributions Reports"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx(H,{htmlFor:"month-select",value:"Month"}),e.jsx(D,{id:"month-select",value:u,onChange:c=>w(parseInt(c.target.value)),className:"w-40",children:p.map((c,d)=>e.jsx("option",{value:d+1,children:c},d))})]}),e.jsxs("div",{children:[e.jsx(H,{htmlFor:"year-select",value:"Year"}),e.jsx(D,{id:"year-select",value:S,onChange:c=>m(parseInt(c.target.value)),className:"w-32",children:Array.from({length:5},(c,d)=>new Date().getFullYear()-2+d).map(c=>e.jsx("option",{value:c,children:c},c))})]}),e.jsx(R,{color:"primary",onClick:g,disabled:y,children:y?e.jsxs(e.Fragment,{children:[e.jsx(Y,{size:"sm",className:"mr-2"}),"Processing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(T,{className:"mr-2 h-5 w-5"}),"Batch Process All"]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-blue-700",children:"Total SSS"}),e.jsx("p",{className:"text-xl font-bold text-blue-700",children:o(n.summary.totalSSS)})]}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-green-700",children:"Total PhilHealth"}),e.jsx("p",{className:"text-xl font-bold text-green-700",children:o(n.summary.totalPhilHealth)})]}),e.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-yellow-700",children:"Total Pag-IBIG"}),e.jsx("p",{className:"text-xl font-bold text-yellow-700",children:o(n.summary.totalPagibig)})]}),e.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-purple-700",children:"Total Contributions"}),e.jsx("p",{className:"text-xl font-bold text-purple-700",children:o(n.summary.totalSSS+n.summary.totalPhilHealth+n.summary.totalPagibig)})]})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg mb-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Compliance Status"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`h-3 w-3 rounded-full mr-2 ${n.summary.totalSSS>0?"bg-green-500":"bg-red-500"}`}),e.jsxs("span",{className:"text-sm",children:["SSS Contributions: ",n.summary.totalSSS>0?"Ready to submit":"No data"]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`h-3 w-3 rounded-full mr-2 ${n.summary.totalPhilHealth>0?"bg-green-500":"bg-red-500"}`}),e.jsxs("span",{className:"text-sm",children:["PhilHealth Contributions: ",n.summary.totalPhilHealth>0?"Ready to submit":"No data"]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`h-3 w-3 rounded-full mr-2 ${n.summary.totalPagibig>0?"bg-green-500":"bg-red-500"}`}),e.jsxs("span",{className:"text-sm",children:["Pag-IBIG Contributions: ",n.summary.totalPagibig>0?"Ready to submit":"No data"]})]})]})]})]}),e.jsxs(G.Group,{"aria-label":"Government contribution reports",style:"underline",onActiveTabChange:v,children:[e.jsx(G.Item,{active:!0,title:"SSS (R-3)",icon:T,children:e.jsx(se,{data:{items:n.items,summary:{totalSSS:n.summary.totalSSS,employeeCount:n.summary.employeeCount}},month:u,year:S,employerName:a,employerSSS:i.sssNumber,employerAddress:i.address})}),e.jsx(G.Item,{title:"PhilHealth (RF-1)",icon:T,children:e.jsx(re,{data:{items:n.items,summary:{totalPhilHealth:n.summary.totalPhilHealth,employeeCount:n.summary.employeeCount}},month:u,year:S,employerName:a,employerPhilHealth:i.philHealthNumber,employerAddress:i.address})}),e.jsx(G.Item,{title:"Pag-IBIG (MCRF)",icon:T,children:e.jsx(le,{data:{items:n.items,summary:{totalPagibig:n.summary.totalPagibig,employeeCount:n.summary.employeeCount}},month:u,year:S,employerName:a,employerPagibig:i.pagibigNumber,employerAddress:i.address})})]}),e.jsx("div",{className:"bg-blue-50 border-l-4 border-blue-400 p-4",children:e.jsx("div",{className:"flex",children:e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-800",children:"Remittance Deadlines"}),e.jsx("div",{className:"mt-2 text-sm text-blue-700",children:e.jsxs("ul",{className:"list-disc pl-5 space-y-1",children:[e.jsx("li",{children:"SSS: 10th day (10+ employees) or 15th day (less than 10 employees) of the following month"}),e.jsx("li",{children:"PhilHealth: 10th day of the following month"}),e.jsx("li",{children:"Pag-IBIG: 15th day of the following month"})]})})]})})})]})},de=()=>{const n=W(),{currentOrganization:a}=K(),[i,y]=f.useState(new Date().getMonth()+1),[j,b]=f.useState(new Date().getFullYear()),[v,u]=f.useState(!1),[w,S]=f.useState(!1),[m,p]=f.useState(null),[g,c]=f.useState(null),[d,C]=f.useState({sssNumber:"",philHealthNumber:"",pagibigNumber:"",address:""}),[x,P]=f.useState({sss:{valid:!0,missingEmployees:[]},philhealth:{valid:!0,missingEmployees:[]},pagibig:{valid:!0,missingEmployees:[]}}),I=["January","February","March","April","May","June","July","August","September","October","November","December"];f.useEffect(()=>{a&&(_(),r())},[a,i,j]);const _=async()=>{if(a){u(!0),p(null);try{const t=new Date(j,i-1,1),h=new Date(j,i,0),{data:N,error:E}=await q({organizationId:a.id,startDate:t,endDate:h});if(E)throw new Error(E);c(N);const z=await J(a.id,"sss"),V=await J(a.id,"philhealth"),L=await J(a.id,"pagibig");P({sss:z,philhealth:V,pagibig:L})}catch(t){p(t.message)}finally{u(!1)}}},r=async()=>{if(a)try{const{data:t,error:h}=await ee(a.id);if(h)throw new Error(h);t&&C({...d,...t,address:a.address||"123 Main St, Manila, Philippines"})}catch(t){console.error("Error fetching organization details:",t)}},l=async()=>{if(a){S(!0);try{const t=await Z({organizationId:a.id,month:i,year:j,reportTypes:["sss","philhealth","pagibig"],format:"pdf"});if(!t.success)throw new Error(t.error||"Failed to process reports");alert("Reports processed successfully!"),console.log("Generated reports:",t.reports)}catch(t){p(t.message)}finally{S(!1)}}};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsxs(R,{color:"gray",className:"mr-4",onClick:()=>n("/payroll/reports"),children:[e.jsx(Q,{className:"mr-2 h-5 w-5"}),"Back to Reports"]}),e.jsx(X,{title:"Government Contributions Reports"})]})}),m&&e.jsxs(F,{color:"failure",icon:B,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",m]}),!x.sss.valid||!x.philhealth.valid||!x.pagibig.valid?e.jsxs(F,{color:"warning",icon:B,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Warning!"})," Some employees are missing government ID numbers. Please update employee records for accurate reporting."]}):null,v?e.jsxs("div",{className:"flex justify-center items-center p-12",children:[e.jsx(Y,{size:"xl"}),e.jsx("span",{className:"ml-2",children:"Loading report data..."})]}):g?e.jsx(te,{data:g,organizationName:(a==null?void 0:a.name)||"",organizationDetails:d,isLoading:w,onBatchProcess:l}):e.jsx(M,{children:e.jsxs("div",{className:"p-6 text-center",children:[e.jsx(T,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-lg font-medium text-gray-900",children:"No Report Data"}),e.jsxs("p",{className:"mt-1 text-sm text-gray-500",children:["There is no government contribution data available for ",I[i-1]," ",j,"."]}),e.jsx("div",{className:"mt-6",children:e.jsx(R,{color:"primary",onClick:_,children:"Refresh Data"})})]})}),e.jsxs(M,{className:"mt-6",children:[e.jsxs("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[e.jsx(U,{className:"mr-2 h-5 w-5"}),"Organization Government IDs"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(H,{htmlFor:"sss-number",value:"SSS Employer Number"}),e.jsx($,{id:"sss-number",value:d.sssNumber,onChange:t=>C({...d,sssNumber:t.target.value}),placeholder:"Enter SSS employer number",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(H,{htmlFor:"philhealth-number",value:"PhilHealth Employer Number"}),e.jsx($,{id:"philhealth-number",value:d.philHealthNumber,onChange:t=>C({...d,philHealthNumber:t.target.value}),placeholder:"Enter PhilHealth employer number",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(H,{htmlFor:"pagibig-number",value:"Pag-IBIG Employer Number"}),e.jsx($,{id:"pagibig-number",value:d.pagibigNumber,onChange:t=>C({...d,pagibigNumber:t.target.value}),placeholder:"Enter Pag-IBIG employer number",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(H,{htmlFor:"address",value:"Employer Address"}),e.jsx($,{id:"address",value:d.address,onChange:t=>C({...d,address:t.target.value}),placeholder:"Enter employer address",className:"mt-1"})]})]}),e.jsx("div",{className:"mt-4",children:e.jsx(R,{color:"primary",children:"Save Organization Details"})})]})]})};export{de as default};
