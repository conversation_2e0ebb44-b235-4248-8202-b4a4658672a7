import{s as i}from"./index-C6AV3cVN.js";const c=async t=>{try{const{data:e,error:r}=await i.from("suppliers").select("*").eq("organization_id",t).eq("is_default",!0).single();if(r&&r.code==="PGRST116"){console.log("No default supplier found, creating one for organization:",t);const{supplier:s,error:a}=await o(t);return a?{error:a}:{supplier:s}}else if(r)return console.error("Error fetching default supplier:",r),{error:r.message};return{supplier:e}}catch(e){return console.error("Error in getDefaultSupplier:",e),{error:e.message}}},o=async t=>{try{const{data:e,error:r}=await i.from("suppliers").insert({organization_id:t,name:"Default Supplier",contact_person:"To Be Determined",email:null,phone:null,address:null,notes:"This is the default supplier used for purchase orders when the actual supplier is not yet determined. You can edit the purchase order later to assign the correct supplier.",is_default:!0}).select("*").single();return r?(console.error("Error creating default supplier:",r),{error:r.message}):(console.log("Created default supplier for organization:",t),{supplier:e})}catch(e){return console.error("Error in createDefaultSupplier:",e),{error:e.message}}},n=async(t,e)=>{try{let r=i.from("suppliers").select("*",{count:"exact"}).eq("organization_id",t);e!=null&&e.searchQuery&&(r=r.or(`name.ilike.%${e.searchQuery}%,email.ilike.%${e.searchQuery}%,contact_person.ilike.%${e.searchQuery}%`)),e!=null&&e.sortBy?r=r.order(e.sortBy,{ascending:e.sortOrder==="asc"}):r=r.order("name",{ascending:!0}),e!=null&&e.limit&&(r=r.limit(e.limit)),e!=null&&e.offset&&(r=r.range(e.offset,e.offset+(e.limit||10)-1));const{data:s,error:a,count:l}=await r;return a?(console.error("Error fetching suppliers:",a),{suppliers:[],count:0,error:a.message}):{suppliers:s,count:l||0}}catch(r){return console.error("Error in getSuppliers:",r),{suppliers:[],count:0,error:r.message}}},p=async(t,e)=>{try{const{data:r,error:s}=await i.from("suppliers").select("*").eq("organization_id",t).eq("id",e).single();return s?(console.error("Error fetching supplier:",s),{error:s.message}):{supplier:r}}catch(r){return console.error("Error in getSupplierById:",r),{error:r.message}}},f=async(t,e)=>{try{const{data:r,error:s}=await i.from("suppliers").insert({...e,organization_id:t}).select("*").single();return s?(console.error("Error creating supplier:",s),{error:s.message}):{supplier:r}}catch(r){return console.error("Error in createSupplier:",r),{error:r.message}}},d=async(t,e,r)=>{try{const{data:s,error:a}=await i.from("suppliers").update(r).eq("organization_id",t).eq("id",e).select("*").single();return a?(console.error("Error updating supplier:",a),{error:a.message}):{supplier:s}}catch(s){return console.error("Error in updateSupplier:",s),{error:s.message}}},g=async(t,e)=>{try{const{data:r,error:s}=await i.from("suppliers").select("is_default").eq("organization_id",t).eq("id",e).single();if(s)return console.error("Error fetching supplier for deletion check:",s),{success:!1,error:s.message};if(r!=null&&r.is_default)return{success:!1,error:"Cannot delete the default supplier. This supplier is required for the organization."};const{error:a}=await i.from("suppliers").delete().eq("organization_id",t).eq("id",e);return a?(console.error("Error deleting supplier:",a),{success:!1,error:a.message}):{success:!0}}catch(r){return console.error("Error in deleteSupplier:",r),{success:!1,error:r.message}}};export{c as a,p as b,f as c,g as d,n as g,d as u};
