import{j as e}from"./index-C6AV3cVN.js";import{C as r}from"./Card-yj7fueH8.js";const n=()=>e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(r,{children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Inventory Reports"}),e.jsx("p",{className:"text-gray-500",children:"View and generate inventory reports. Track stock levels, inventory value, and movement."}),e.jsx("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:e.jsx("p",{className:"text-center text-gray-400",children:"Inventory reports will be displayed here"})})]})});export{n as default};
