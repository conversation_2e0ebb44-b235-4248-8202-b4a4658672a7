import{r,j as e,A as c,a6 as h,P as g,B as f,L as b}from"./index-C6AV3cVN.js";import{r as j}from"./auth-DeOTzV2I.js";import{F as p}from"./FullLogo-CXg185jT.js";const y={background:"linear-gradient(45deg, rgb(238, 119, 82,0.2), rgb(231, 60, 126,0.2), rgb(35, 166, 213,0.2), rgb(35, 213, 171,0.2))",backgroundSize:"400% 400%",animation:"gradient 15s ease infinite",height:"100vh"},S=()=>{const[t,m]=r.useState(""),[l,i]=r.useState(null),[u,n]=r.useState(!1),[o,d]=r.useState(!1),x=async a=>{a.preventDefault(),i(null),n(!1),d(!0);try{const{error:s}=await j(t);if(s)throw s;n(!0)}catch(s){i(s.message||"An error occurred while sending the reset link")}finally{d(!1)}};return e.jsx("div",{style:y,className:"relative overflow-hidden h-screen",children:e.jsx("div",{className:"flex h-full justify-center items-center px-4",children:e.jsx("div",{className:"rounded-xl shadow-md bg-white dark:bg-darkgray p-6 w-full md:w-96 border-none",children:e.jsxs("div",{className:"flex flex-col gap-2 p-0 w-full",children:[e.jsx("div",{className:"mx-auto",children:e.jsx(p,{})}),e.jsx("p",{className:"text-sm text-center text-dark my-3",children:"Forgot Password"}),u?e.jsx(c,{color:"success",className:"mb-4",children:"Password reset link has been sent to your email."}):e.jsxs(e.Fragment,{children:[l&&e.jsx(c,{color:"failure",className:"mb-4",children:l}),e.jsxs("form",{onSubmit:x,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(h,{htmlFor:"email",value:"Email"})}),e.jsx(g,{id:"email",type:"email",sizing:"md",required:!0,value:t,onChange:a=>m(a.target.value),className:"form-control form-rounded-xl",placeholder:"Enter your email address"})]}),e.jsx(f,{type:"submit",color:"primary",className:"w-full bg-primary text-white rounded-xl",disabled:o,children:o?"Sending...":"Send Reset Link"})]})]}),e.jsxs("div",{className:"flex gap-2 text-base text-ld font-medium mt-6 items-center justify-center",children:[e.jsx("p",{children:"Remember your password?"}),e.jsx(b,{to:"/auth/login",className:"text-primary text-sm font-medium",children:"Sign in"})]})]})})})})};export{S as default};
