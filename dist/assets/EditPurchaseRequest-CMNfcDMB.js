import{ab as ye,d as _e,h as qe,b as Ne,r as p,j as e,i as be,A as X,J as Z,B as y,aD as z,al as N,ay as ee,a6 as k,Y as F,P as te,a7 as we,ad as Ce,a0 as Se,_ as h,F as Ie,a3 as Pe,M as A,s as O}from"./index-C6AV3cVN.js";import{C as Ee}from"./Card-yj7fueH8.js";import{U as ke}from"./UomSelector-CTe3hN2d.js";import{u as Fe,a as Ae,b as se,A as Ue,F as Re,K as De,Q as Te,C as He,V as ze}from"./KeyboardShortcutTooltip-DeEIPKJI.js";import{p as B,E as Oe}from"./EnhancedProductSearchSelector-CWWD8-c1.js";import{a as Be,b as Me}from"./purchaseRequest-Bff56uzH.js";import{E as $e}from"./EnhancedNumberInput-Bwo1E3yF.js";import"./productUom-k6aUg6b7.js";import"./product-Ca8DWaNR.js";import"./typeof-QjJsDpFa.js";const et=()=>{const{id:g}=ye(),w=_e(),{currentOrganization:u}=qe(),{user:C}=Ne(),[re,b]=p.useState(!0),[ae,oe]=p.useState(null),ie={fields:{notes:[ze.maxLength(500,"Notes must be less than 500 characters")]},form:[{validate:(t,s)=>s.items&&s.items.length>0,message:"Please add at least one item to the purchase request"}]},{formData:x,setFormData:M,errors:_,formErrors:$,isValid:ne,validateForm:ce,handleChange:L,setFormDataWithoutValidation:V}=Fe({id:g||"",organization_id:(u==null?void 0:u.id)||"",request_number:"",requester_id:(C==null?void 0:C.id)||"",status:"draft",notes:"",items:[]},ie),[U,R]=p.useState([]),[D,K]=p.useState(!1),[S,d]=p.useState(null),[le,I]=p.useState(!1),[T,de]=p.useState(""),[ue,H]=p.useState(!1),[Q,me]=p.useState(null),{save:he,clear:Y,lastSavedFormatted:fe,isDirty:G,isSaving:pe}=Ae({key:`purchase-request-edit-${g}`,initialData:x,interval:5e3,enabled:!0,onSave:t=>{}});se({key:"s",ctrl:!0},{callback:()=>{D||E()},isEnabled:!0}),se({key:"a",ctrl:!0},{callback:()=>{W()},isEnabled:!0});const m=x.items||[],f=t=>{if(typeof t=="function"){const s=t(m);V({...x,items:s})}else V({...x,items:t})};p.useEffect(()=>{(async()=>{if(!(!g||!u)){b(!0),d(null);try{const{purchaseRequest:s,error:n}=await Be(u.id,g);if(n){d(n),b(!1);return}if(!s){d("Purchase request not found"),b(!1);return}if(oe(s),s.status!=="draft"&&s.status!=="pending"){d("Only draft or pending purchase requests can be edited"),b(!1);return}const r=s.items.map(o=>({id:o.id,product_id:o.product_id,product:o.product,quantity:o.quantity,uom_id:o.uom_id,uom:o.uom?{...o.uom,uom:o.uom}:void 0,notes:o.notes||""}));M({id:s.id,organization_id:s.organization_id,request_number:s.request_number,requester_id:s.requester_id,status:s.status,notes:s.notes||"",items:r});const i=r.filter(o=>o.product_id&&!o.product).map(o=>o.product_id);if(i.length>0)try{for(const o of i){const c=await B.getProduct(u.id,o);c&&R(l=>l.some(j=>j.id===c.id)?l:[...l,c])}}catch(o){console.error("Error fetching products:",o)}}catch(s){d(s.message||"An error occurred while fetching the purchase request")}finally{b(!1)}}})()},[g,u,M]),p.useEffect(()=>{(async()=>{if(u)try{const{products:s}=await B.searchProducts(u.id,"",1,20);R(s)}catch(s){console.error("Error fetching products:",s)}})()},[u]);const W=()=>{if(U.length===0)return;const t={product_id:"",quantity:1,uom_id:""};f(s=>[...s,t])},ge=t=>{f(s=>s.filter((n,r)=>r!==t))},P=(t,s,n)=>{f(r=>{const i=[...r];return i[t]={...i[t],[s]:n},s==="product_id"&&(i[t].uom_id="",i[t].uom=void 0),i}),s==="product_id"&&n&&(async()=>{try{if(m[t].product){const i=m[t].product;if(i&&i.product_uoms&&i.product_uoms.length>0){const o=i.product_uoms.find(c=>c.is_default);o&&f(c=>{const l=[...c];return l[t]&&l[t].product_id===n&&(l[t].uom_id=o.uom_id,l[t].uom=o),l})}}else{const i=await ve(n);if(i&&(f(o=>{const c=[...o];return c[t]&&c[t].product_id===n&&(c[t].product=i),c}),i.product_uoms&&i.product_uoms.length>0)){const o=i.product_uoms.find(c=>c.is_default);if(o)f(c=>{const l=[...c];return l[t]&&l[t].product_id===n&&(l[t].uom_id=o.uom_id,l[t].uom=o),l});else if(i.product_uoms.length>0){const c=i.product_uoms[0];f(l=>{const j=[...l];return j[t]&&j[t].product_id===n&&(j[t].uom_id=c.uom_id,j[t].uom=c),j})}}}}catch(i){console.error("Error setting default UoM:",i)}})()},xe=(t,s,n)=>{!s||!n||f(r=>{const i=[...r];return t>=0&&t<i.length&&(i[t]={...i[t],uom_id:s,uom:{...n}}),i})},J=t=>{G?(me(t),H(!0)):w(t)},je=()=>{Q&&(Y(),w(Q)),H(!1)},E=async t=>{if(t&&t.preventDefault(),!u||!C||!g){d("Organization, user or purchase request ID not found");return}if(!ce()){d("Please fix the validation errors before submitting");return}for(let n=0;n<m.length;n++){const r=m[n];if(!r.product_id){d(`Please select a product for item #${n+1}`);return}if(!r.uom_id){d(`Please select a unit of measurement for item #${n+1}`);return}if(r.quantity<=0){d(`Please enter a valid quantity for item #${n+1}`);return}}K(!0),d(null);try{he();const n={notes:x.notes||null},r=new Map(ae.items.map(a=>[a.id,a])),i=new Set(m.filter(a=>a.id).map(a=>a.id)),o=m.filter(a=>a.id).map(a=>({id:a.id,product_id:a.product_id,quantity:a.quantity,uom_id:a.uom_id,notes:a.notes||null})),c=m.filter(a=>!a.id).map(a=>({purchase_request_id:g,product_id:a.product_id,quantity:a.quantity,uom_id:a.uom_id,notes:a.notes||null})),l=Array.from(r.keys()).filter(a=>!i.has(a)),{error:j}=await Me(u.id,g,n);if(j){console.error("Error updating purchase request:",j),d(j);return}let v=!1;if(c.length>0)try{const{error:a}=await O.from("purchase_request_items").insert(c);a&&(console.error("Error adding new items:",a),d(a.message||"Failed to add new items"),v=!0)}catch(a){console.error("Exception adding new items:",a),d(a.message||"An error occurred while adding new items"),v=!0}if(!v&&o.length>0)for(const a of o)try{const{error:q}=await O.from("purchase_request_items").update({product_id:a.product_id,quantity:a.quantity,uom_id:a.uom_id,notes:a.notes}).eq("id",a.id);if(q){console.error("Error updating item:",q),d(q.message||"Failed to update items"),v=!0;break}}catch(q){console.error("Exception updating item:",q),d(q.message||"An error occurred while updating items"),v=!0;break}if(!v&&l.length>0)try{const{error:a}=await O.from("purchase_request_items").delete().in("id",l);a&&(console.error("Error deleting items:",a),d(a.message||"Failed to delete items"),v=!0)}catch(a){console.error("Exception deleting items:",a),d(a.message||"An error occurred while deleting items"),v=!0}v||(Y(),w(`/purchases/requests/${g}`))}catch(n){d(n.message||"An error occurred while updating the purchase request")}finally{K(!1)}},ve=p.useCallback(async t=>{const s=m.find(r=>r.product_id===t&&r.product);if(s&&s.product)return s.product;const n=U.find(r=>r.id===t);if(n)return n;if(u)try{const r=await B.getProduct(u.id,t);if(r)return R(i=>i.some(o=>o.id===r.id)?i:[...i,r]),r}catch(r){console.error("Error fetching product from cache:",r)}},[m,U,u]);return re?e.jsx("div",{className:"container mx-auto px-4 py-8 flex justify-center",children:e.jsx(be,{size:"xl"})}):S&&!x.id?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(X,{color:"failure",icon:Z,children:[e.jsx("h3",{className:"font-medium",children:"Error"}),e.jsx("p",{children:S}),e.jsx("div",{className:"mt-4",children:e.jsxs(y,{color:"gray",onClick:()=>w("/purchases/requests"),children:[e.jsx(z,{className:"mr-2 h-5 w-5"}),"Back to Purchase Requests"]})})]})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(Ee,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Edit Purchase Request"}),e.jsx("p",{className:"text-gray-500",children:x.request_number}),e.jsx(Ue,{isSaving:pe,isDirty:G,lastSavedFormatted:fe,className:"mt-1"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(y,{color:"gray",onClick:()=>J(`/purchases/requests/${g}`),title:"Go back to purchase request details",children:[e.jsx(z,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(N,{content:"Save changes (Ctrl+S)",children:e.jsxs(y,{color:"primary",onClick:E,disabled:D,children:[e.jsx(ee,{className:"mr-2 h-5 w-5"}),"Save"]})})]})]}),e.jsx(Re,{errors:_,formErrors:$,fieldLabels:{notes:"Notes",items:"Items"},show:!ne&&Object.keys(_).length>0||$.length>0,className:"mb-4"}),S&&e.jsx(X,{color:"failure",icon:Z,className:"mb-4",children:S}),e.jsxs("form",{onSubmit:E,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx(k,{htmlFor:"request_number",value:"Request Number",className:"text-sm font-medium"}),e.jsx(N,{content:"Unique identifier for this purchase request",children:e.jsx(F,{className:"ml-1 h-4 w-4 text-gray-400 hover:text-gray-600"})})]}),e.jsx(te,{id:"request_number",name:"request_number",value:x.request_number,disabled:!0})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx(k,{htmlFor:"status",value:"Status",className:"text-sm font-medium"}),e.jsx(N,{content:"Current status of the purchase request",children:e.jsx(F,{className:"ml-1 h-4 w-4 text-gray-400 hover:text-gray-600"})})]}),e.jsxs(we,{id:"status",name:"status",value:x.status,onChange:t=>L("status",t.target.value),disabled:x.status!=="draft",children:[e.jsx("option",{value:"draft",children:"Draft"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"approved",children:"Approved"}),e.jsx("option",{value:"rejected",children:"Rejected"}),e.jsx("option",{value:"cancelled",children:"Cancelled"})]})]})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx(k,{htmlFor:"notes",value:"Notes",className:"text-sm font-medium"}),e.jsx(N,{content:"Add any additional information about this purchase request",children:e.jsx(F,{className:"ml-1 h-4 w-4 text-gray-400 hover:text-gray-600"})})]}),e.jsx(Ce,{id:"notes",name:"notes",value:x.notes||"",onChange:t=>L("notes",t.target.value),placeholder:"Enter any additional notes",rows:3,color:_.notes&&_.notes.length>0?"failure":void 0,helperText:_.notes&&_.notes.length>0?e.jsx("ul",{className:"mt-1 text-sm text-red-600 list-disc list-inside",children:_.notes.map((t,s)=>e.jsx("li",{children:t},s))}):null})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(k,{value:"Items",className:"text-sm font-medium"}),e.jsx(N,{content:"Products included in this purchase request",children:e.jsx(F,{className:"ml-1 h-4 w-4 text-gray-400 hover:text-gray-600"})})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(De,{shortcut:{key:"a",ctrl:!0}}),e.jsxs(y,{size:"xs",color:"primary",onClick:W,title:"Add item (Ctrl+A)",children:[e.jsx(Se,{className:"mr-2 h-4 w-4"}),"Add Item"]})]})]}),m.length===0?e.jsx("div",{className:"p-4 bg-gray-50 rounded-lg text-center text-gray-500",children:'No items added. Click "Add Item" to add products to this purchase request.'}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(h,{children:[e.jsxs(h.Head,{children:[e.jsx(h.HeadCell,{children:"Product"}),e.jsx(h.HeadCell,{children:"Quantity"}),e.jsx(h.HeadCell,{children:"Unit"}),e.jsx(h.HeadCell,{children:"Notes"}),e.jsx(h.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(h.Body,{className:"divide-y",children:m.map((t,s)=>{var n;return e.jsxs(h.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(h.Cell,{children:e.jsx(Oe,{value:t.product_id,onChange:(r,i)=>{i?f(o=>{const c=[...o];return c[s]={...c[s],product_id:r,product:i},c}):P(s,"product_id",r)},required:!0,className:"text-sm",placeholder:"Search for a product...",pageSize:5})}),e.jsx(h.Cell,{children:e.jsx($e,{min:"0.01",step:"0.01",value:t.quantity,onChange:r=>P(s,"quantity",parseFloat(r.target.value)||0),onBlur:r=>{(r.target.value===""||parseFloat(r.target.value)<=0)&&P(s,"quantity",1)},required:!0,className:"text-sm",sizing:"sm",autoSelect:!0,preventScrollChange:!0})}),e.jsx(h.Cell,{children:t.product_id?e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex-grow",children:e.jsx(ke,{productId:t.product_id,value:t.uom_id||"",onChange:r=>{f(i=>{const o=[...i];return s>=0&&s<o.length&&(o[s]={...o[s],uom_id:r}),o})},onUomChange:r=>{xe(s,r.uom_id,r)},filter:"all",disabled:!1,preloadedUoms:(n=t.product)==null?void 0:n.product_uoms},`uom-selector-${t.product_id}-${s}`)}),e.jsx("div",{children:e.jsx(y,{size:"xs",color:"light",className:"p-1",title:"Setup Units of Measurement",onClick:()=>{de(t.product_id),I(!0)},children:e.jsx(Ie,{className:"h-3 w-3"})})})]}):e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex-grow",children:e.jsx("div",{className:"text-sm text-gray-500 p-2 border border-gray-300 rounded-lg bg-gray-50",children:"Select a product first"})}),e.jsx("div",{className:"w-8"})]})}),e.jsx(h.Cell,{children:e.jsx(te,{value:t.notes||"",onChange:r=>P(s,"notes",r.target.value),placeholder:"Optional notes",className:"text-sm",sizing:"sm"})}),e.jsx(h.Cell,{children:e.jsx(y,{color:"failure",size:"xs",onClick:()=>ge(s),children:e.jsx(Pe,{className:"h-4 w-4"})})})]},s)})})]})})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsxs(y,{color:"gray",onClick:()=>J(`/purchases/requests/${g}`),title:"Go back to purchase request details",children:[e.jsx(z,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(N,{content:"Save changes (Ctrl+S)",children:e.jsxs(y,{color:"primary",onClick:E,disabled:D,children:[e.jsx(ee,{className:"mr-2 h-5 w-5"}),"Save"]})})]})]})]}),e.jsxs(A,{show:le,onClose:()=>I(!1),size:"xl",children:[e.jsx(A.Header,{children:"Configure Units of Measurement for Purchasing"}),e.jsx(A.Body,{children:T&&e.jsx(Te,{productId:T,onComplete:()=>{I(!1);const t=m.findIndex(s=>s.product_id===T);if(t>=0){const s=m[t].product_id;f(n=>{const r=[...n];return r[t]={...r[t],uom_id:""},r}),setTimeout(()=>{f(n=>{const r=[...n];return r[t]={...r[t],product_id:s},r})},100)}}})}),e.jsx(A.Footer,{children:e.jsx(y,{color:"gray",onClick:()=>I(!1),children:"Close"})})]}),e.jsx(He,{show:ue,title:"Unsaved Changes",message:"You have unsaved changes. Are you sure you want to leave this page? Your changes will be lost.",confirmText:"Leave Page",cancelText:"Stay on Page",confirmColor:"failure",onClose:()=>H(!1),onConfirm:je})]})};export{et as default};
