import{ab as de,d as ue,h as he,b as me,r as a,j as e,i as xe,A as y,G as V,B as b,o as X,Y as Z,t as ee,U as pe,a6 as j,P as w,ay as be,M as C}from"./index-C6AV3cVN.js";import{C as ge}from"./Card-yj7fueH8.js";import{f as ye,h as je,r as fe}from"./payroll-DcVgVc3z.js";import{getEmployeeContributionPreferences as ve,saveEmployeeContributionPreferences as _e}from"./employeeContributionPreferences-DopMjMg8.js";import{P as se}from"./payroll-j3fcCwK0.js";import{P as Ne}from"./PageTitle-FHPo8gWi.js";import{f as x}from"./formatters-Cypx7G-j.js";const Re=()=>{var A,z,L,U,W,Y,q;const{id:n}=de(),f=ue(),{currentOrganization:c}=he(),{user:O}=me(),[s,D]=a.useState(null),[ie,G]=a.useState(!0),[B,S]=a.useState(null),[P,M]=a.useState(!1),[H,g]=a.useState(null),[te,T]=a.useState(!1),[ae,v]=a.useState(!1),[E,$]=a.useState(!1),[l,k]=a.useState(0),[o,R]=a.useState(0),[r,F]=a.useState(0),[d,I]=a.useState(0),[p,_]=a.useState(!1);a.useEffect(()=>{(async()=>{var h;if(!(!c||!n))try{G(!0);const{item:i,error:N}=await ye(c.id,n);if(N)S(N);else if(i){if(D(i),k(i.sss_contribution||0),R(i.philhealth_contribution||0),F(i.pagibig_contribution||0),I(i.withholding_tax||0),_(i.contributions_manually_edited||!1),(h=i.employee)!=null&&h.id){const{preferences:m}=await ve(c.id,i.employee.id);m&&(m.sss_contribution_override===i.sss_contribution||m.philhealth_contribution_override===i.philhealth_contribution||m.pagibig_contribution_override===i.pagibig_contribution||m.withholding_tax_override===i.withholding_tax)&&_(!0)}}else S("Payroll item not found")}catch(i){S(i.message)}finally{G(!1)}})()},[c,n]);const u=((A=s==null?void 0:s.payroll_period)==null?void 0:A.status)===se.DRAFT||((z=s==null?void 0:s.payroll_period)==null?void 0:z.status)===se.PROCESSING,ne=async t=>{var h;if(t.preventDefault(),!(!c||!n||!s||!O)){M(!0),g(null);try{const i=l+o+r+d,N=s.total_deductions-(s.sss_contribution+s.philhealth_contribution+s.pagibig_contribution+s.withholding_tax),m=i+N,J=s.gross_pay-m,oe=s.gross_pay-l-o-r,re={sss_contribution:l,philhealth_contribution:o,pagibig_contribution:r,withholding_tax:d,taxable_income:oe,total_deductions:m,net_pay:J,contributions_manually_edited:p,sss_contribution_override:p?l:null,philhealth_contribution_override:p?o:null,pagibig_contribution_override:p?r:null,withholding_tax_override:p?d:null},{item:ce,error:K}=await je(c.id,n,re);if(K)g(K);else if(ce){if(p&&((h=s.employee)!=null&&h.id)){const{error:Q}=await _e(c.id,s.employee.id,{sss_contribution_override:l,philhealth_contribution_override:o,pagibig_contribution_override:r,withholding_tax_override:d,notes:`Updated on ${new Date().toLocaleDateString()} via payroll item edit`},O.id);Q&&console.warn("Failed to save employee preferences:",Q)}T(!0),setTimeout(()=>{f(`/payroll/items/${n}`)},2e3)}}catch(i){g(i.message)}finally{M(!1)}}},le=async()=>{if(!(!c||!n)){$(!0),g(null);try{const{item:t,error:h}=await fe(c.id,n);h?g(h):t&&(k(t.sss_contribution||0),R(t.philhealth_contribution||0),F(t.pagibig_contribution||0),I(t.withholding_tax||0),_(!1),D(i=>i?{...i,...t}:null),v(!1),T(!0),setTimeout(()=>T(!1),3e3))}catch(t){g(t.message)}finally{$(!1)}}};return ie?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(xe,{size:"xl"})})}):B?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(y,{color:"failure",icon:V,children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",B]}),e.jsx("div",{className:"mt-4",children:e.jsxs(b,{color:"gray",onClick:()=>f("/payroll"),children:[e.jsx(X,{className:"mr-2 h-5 w-5"}),"Back to Payroll"]})})]}):s?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs(b,{color:"gray",className:"mr-4",onClick:()=>f(`/payroll/items/${n}`),children:[e.jsx(X,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(Ne,{title:"Edit Government Contributions",subtitle:`${(L=s.employee)==null?void 0:L.first_name} ${(U=s.employee)==null?void 0:U.last_name} - ${(W=s.payroll_period)==null?void 0:W.name}`})]}),e.jsx("div",{className:"flex space-x-2",children:e.jsxs(b,{color:"light",onClick:()=>v(!0),disabled:!u,children:[e.jsx(ee,{className:"mr-2 h-5 w-5"}),"Recalculate"]})})]}),!u&&e.jsx(y,{color:"warning",className:"mb-4",icon:Z,children:"This payroll period has been approved or paid. Government contributions cannot be edited."}),te&&e.jsx(y,{color:"success",className:"mb-4",children:"Government contributions updated successfully! Redirecting..."}),e.jsx(ge,{children:e.jsxs("form",{onSubmit:ne,children:[H&&e.jsx(y,{color:"failure",icon:V,className:"mb-4",children:H}),e.jsxs("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Employee Information"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Basic Pay:"})," ",x(s.basic_pay)]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Gross Pay:"})," ",x(s.gross_pay)]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Current Net Pay:"})," ",x(s.net_pay)]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Total Deductions:"})," ",x(s.total_deductions)]})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(pe,{id:"useManualOverrides",checked:p,onChange:t=>_(t.target.checked),disabled:!u}),e.jsx(j,{htmlFor:"useManualOverrides",children:"Save as employee-specific contribution preferences"})]}),e.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:["When enabled, these values will be saved as ",e.jsxs("strong",{children:[(Y=s==null?void 0:s.employee)==null?void 0:Y.first_name," ",(q=s==null?void 0:s.employee)==null?void 0:q.last_name,"'s"]})," personal contribution preferences and automatically applied to all future payroll periods for this employee only."]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(j,{htmlFor:"sssContribution",value:"SSS Contribution"})}),e.jsx(w,{id:"sssContribution",type:"number",step:"0.01",min:"0",value:l,onChange:t=>k(parseFloat(t.target.value)||0),disabled:!u,placeholder:"0.00"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Employee share only"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(j,{htmlFor:"philhealthContribution",value:"PhilHealth Contribution"})}),e.jsx(w,{id:"philhealthContribution",type:"number",step:"0.01",min:"0",value:o,onChange:t=>R(parseFloat(t.target.value)||0),disabled:!u,placeholder:"0.00"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Employee share only"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(j,{htmlFor:"pagibigContribution",value:"Pag-IBIG Contribution"})}),e.jsx(w,{id:"pagibigContribution",type:"number",step:"0.01",min:"0",value:r,onChange:t=>F(parseFloat(t.target.value)||0),disabled:!u,placeholder:"0.00"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Employee share only"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(j,{htmlFor:"withholdingTax",value:"Withholding Tax"})}),e.jsx(w,{id:"withholdingTax",type:"number",step:"0.01",min:"0",value:d,onChange:t=>I(parseFloat(t.target.value)||0),disabled:!u,placeholder:"0.00"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Based on taxable income"})]})]}),e.jsxs("div",{className:"mb-6 p-4 bg-blue-50 rounded-lg",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Calculation Summary"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Total Government Deductions:"}),x(l+o+r+d)]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Taxable Income:"}),x(s.gross_pay-l-o-r)]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"New Total Deductions:"}),x(l+o+r+d+(s.total_deductions-(s.sss_contribution+s.philhealth_contribution+s.pagibig_contribution+s.withholding_tax)))]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"New Net Pay:"}),x(s.gross_pay-(l+o+r+d+(s.total_deductions-(s.sss_contribution+s.philhealth_contribution+s.pagibig_contribution+s.withholding_tax))))]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-2 pt-4",children:[e.jsx(b,{color:"gray",onClick:()=>f(`/payroll/items/${n}`),disabled:P,children:"Cancel"}),u&&e.jsxs(b,{type:"submit",color:"primary",isProcessing:P,disabled:P,children:[e.jsx(be,{className:"mr-2 h-5 w-5"}),"Save Changes"]})]})]})}),e.jsxs(C,{show:ae,onClose:()=>v(!1),size:"md",children:[e.jsx(C.Header,{children:"Recalculate Government Contributions"}),e.jsx(C.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"This will recalculate all government contributions based on current Philippine law and the employee's basic pay."}),e.jsxs("div",{className:"p-4 bg-yellow-50 rounded-lg",children:[e.jsx("h4",{className:"font-semibold text-yellow-800 mb-2",children:"This will:"}),e.jsxs("ul",{className:"list-disc list-inside text-sm text-yellow-700 space-y-1",children:[e.jsx("li",{children:"Reset SSS, PhilHealth, and Pag-IBIG contributions to legal amounts"}),e.jsx("li",{children:"Recalculate withholding tax based on taxable income"}),e.jsx("li",{children:"Clear any manual overrides"}),e.jsx("li",{children:"Update net pay accordingly"})]})]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Are you sure you want to proceed? This action cannot be undone."})]})}),e.jsxs(C.Footer,{children:[e.jsxs(b,{color:"primary",onClick:le,isProcessing:E,disabled:E,children:[e.jsx(ee,{className:"mr-2 h-5 w-5"}),"Recalculate"]}),e.jsx(b,{color:"gray",onClick:()=>v(!1),disabled:E,children:"Cancel"})]})]})]}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(y,{color:"warning",icon:Z,children:"Payroll item not found."})})};export{Re as default};
