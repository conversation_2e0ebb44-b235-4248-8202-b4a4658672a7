import{ab as E,h as T,d as _,r as o,j as e,p as $,B,o as S,A as f,J as A,i as H,_ as l,e as I,L as O}from"./index-C6AV3cVN.js";import{C as P}from"./Card-yj7fueH8.js";import{e as D,f as L}from"./tagService-sPq402Av.js";import{a as z}from"./product-Ca8DWaNR.js";import{a as U}from"./customer-COogBrXM.js";import{b as q}from"./supplier-BJDz25mb.js";const M=()=>{const{tagId:c}=E(),{currentOrganization:d}=T(),N=_(),[u,k]=o.useState(null),[s,b]=o.useState([]),[v,p]=o.useState(!0),[y,x]=o.useState(null),[i,h]=o.useState("all");o.useEffect(()=>{(async()=>{if(c)try{const{tag:a,error:m}=await D(c);m?console.error("Error fetching tag details:",m):a&&k(a)}catch(a){console.error("Error in fetchTagDetails:",a)}})()},[c]),o.useEffect(()=>{(async()=>{if(!(!c||!d)){p(!0),x(null);try{const{entities:a,error:m}=await L(c);if(m){x(m),p(!1);return}if(!a||a.length===0){b([]),p(!1);return}const g=[];for(const n of a)try{if(n.entity_type==="product"){const{product:t}=await z(d.id,n.entity_id);t&&g.push({id:t.id,name:t.name,type:"product",detailsUrl:`/products/details/${t.id}`})}else if(n.entity_type==="customer"){const{customer:t}=await U(d.id,n.entity_id);t&&g.push({id:t.id,name:t.name,type:"customer",detailsUrl:`/customers/details/${t.id}`})}else if(n.entity_type==="supplier"){const{supplier:t}=await q(d.id,n.entity_id);t&&g.push({id:t.id,name:t.name,type:"supplier",detailsUrl:`/suppliers/${t.id}`})}}catch(t){console.error(`Error fetching details for ${n.entity_type} ${n.entity_id}:`,t)}b(g)}catch(a){x(a.message||"An error occurred while fetching tagged entities")}finally{p(!1)}}})()},[c,d]);const j=i==="all"?s:s.filter(r=>i==="products"?r.type==="product":i==="customers"?r.type==="customer":i==="suppliers"?r.type==="supplier":!1),w=r=>{switch(r){case"product":return"Product";case"customer":return"Customer";case"supplier":return"Supplier";case"purchase_request":return"Purchase Request";case"purchase_order":return"Purchase Order";default:return r}},C=r=>{switch(r){case"product":return"blue";case"customer":return"green";case"supplier":return"purple";case"purchase_request":return"yellow";case"purchase_order":return"red";default:return"gray"}};return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(P,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx($,{className:"h-6 w-6 mr-2 text-blue-500"}),e.jsx("h1",{className:"text-2xl font-bold",children:"Tagged Items"})]}),e.jsxs(B,{color:"light",size:"sm",onClick:()=>N("/settings/tags"),children:[e.jsx(S,{className:"mr-2 h-4 w-4"}),"Back to Tags"]})]}),u&&e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-4 h-4 rounded-full mr-2",style:{backgroundColor:u.color||"#3b82f6"}}),e.jsx("span",{className:"text-lg font-medium",children:u.name})]}),e.jsxs("p",{className:"text-gray-500",children:["Viewing all items tagged with ",u?`"${u.name}"`:"this tag"]})]}),y?e.jsx(f,{color:"failure",icon:A,children:y}):v?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(H,{size:"xl"})}):s.length===0?e.jsx(f,{color:"info",children:"No items found with this tag."}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700",children:e.jsxs("ul",{className:"flex flex-wrap -mb-px text-sm font-medium text-center",children:[e.jsx("li",{className:"mr-2",children:e.jsxs("button",{className:`inline-block p-4 rounded-t-lg ${i==="all"?"text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500":"hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"}`,onClick:()=>h("all"),children:["All (",s.length,")"]})}),s.some(r=>r.type==="product")&&e.jsx("li",{className:"mr-2",children:e.jsxs("button",{className:`inline-block p-4 rounded-t-lg ${i==="products"?"text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500":"hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"}`,onClick:()=>h("products"),children:["Products (",s.filter(r=>r.type==="product").length,")"]})}),s.some(r=>r.type==="customer")&&e.jsx("li",{className:"mr-2",children:e.jsxs("button",{className:`inline-block p-4 rounded-t-lg ${i==="customers"?"text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500":"hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"}`,onClick:()=>h("customers"),children:["Customers (",s.filter(r=>r.type==="customer").length,")"]})}),s.some(r=>r.type==="supplier")&&e.jsx("li",{className:"mr-2",children:e.jsxs("button",{className:`inline-block p-4 rounded-t-lg ${i==="suppliers"?"text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500":"hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"}`,onClick:()=>h("suppliers"),children:["Suppliers (",s.filter(r=>r.type==="supplier").length,")"]})})]})})}),j.length===0?e.jsx(f,{color:"info",className:"mt-4",children:"No items found in the selected category."}):e.jsxs(l,{children:[e.jsxs(l.Head,{children:[e.jsx(l.HeadCell,{children:"Name"}),e.jsx(l.HeadCell,{children:"Type"}),e.jsx(l.HeadCell,{children:"Actions"})]}),e.jsx(l.Body,{className:"divide-y",children:j.map(r=>e.jsxs(l.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(l.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:r.name}),e.jsx(l.Cell,{children:e.jsx(I,{color:C(r.type),children:w(r.type)})}),e.jsx(l.Cell,{children:e.jsx(O,{to:r.detailsUrl,className:"font-medium text-blue-600 hover:underline dark:text-blue-500",children:"View Details"})})]},`${r.type}-${r.id}`))})]})]})]})})};export{M as default};
