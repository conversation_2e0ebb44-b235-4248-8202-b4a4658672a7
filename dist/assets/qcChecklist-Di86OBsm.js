import{s as c}from"./index-C6AV3cVN.js";const g=async s=>{try{console.log("Fetching QC checklist templates for organization:",s);const{data:r,error:e}=await c.from("qc_checklist_templates").select("*").eq("organization_id",s).order("name");return e?(console.error("Error fetching QC checklist templates:",e),{templates:[],error:e.message}):{templates:r||[]}}catch(r){return console.error("Error in getQcChecklistTemplates:",r),{templates:[],error:r.message}}},n=async(s,r)=>{try{console.log("Fetching QC checklist template by ID:",r);const{data:e,error:t}=await c.from("qc_checklist_templates").select(`
        *,
        items:qc_checklist_items(*)
      `).eq("organization_id",s).eq("id",r).single();return t?(console.error("Error fetching QC checklist template:",t),{error:t.message}):(e&&e.items&&e.items.sort((a,i)=>a.sort_order-i.sort_order),{template:e})}catch(e){return console.error("Error in getQcChecklistTemplateById:",e),{error:e.message}}},u=async(s,r)=>{var e;try{console.log("Creating QC checklist template:",r);const{data:t,error:a}=await c.from("qc_checklist_templates").insert({organization_id:s,name:r.name,description:r.description,is_active:r.isActive!==!1,created_by:((e=(await c.auth.getUser()).data.user)==null?void 0:e.id)||""}).select().single();if(a)return console.error("Error creating QC checklist template:",a),{error:a.message};if(console.log("QC checklist template created:",t),r.items.length>0){const i=r.items.map((o,m)=>({template_id:t.id,name:o.name,description:o.description,item_type:o.itemType,is_required:o.isRequired!==!1,min_value:o.minValue,max_value:o.maxValue,unit:o.unit,options:o.options?JSON.stringify(o.options):null,pass_criteria:o.passCriteria?JSON.stringify(o.passCriteria):null,sort_order:o.sortOrder||m})),{error:l}=await c.from("qc_checklist_items").insert(i);if(l)return console.error("Error adding QC checklist items:",l),{error:l.message}}return await n(s,t.id)}catch(t){return console.error("Error in createQcChecklistTemplate:",t),{error:t.message}}},h=async(s,r,e)=>{try{console.log("Updating QC checklist template:",{templateId:r,data:e});const{error:t}=await c.from("qc_checklist_templates").update({name:e.name,description:e.description,is_active:e.isActive,updated_at:new Date().toISOString()}).eq("id",r).eq("organization_id",s);return t?(console.error("Error updating QC checklist template:",t),{error:t.message}):await n(s,r)}catch(t){return console.error("Error in updateQcChecklistTemplate:",t),{error:t.message}}},d=async(s,r)=>{try{console.log("Deleting QC checklist template:",r);const{error:e}=await c.from("qc_checklist_templates").delete().eq("id",r).eq("organization_id",s);return e?(console.error("Error deleting QC checklist template:",e),{success:!1,error:e.message}):{success:!0}}catch(e){return console.error("Error in deleteQcChecklistTemplate:",e),{success:!1,error:e.message}}},_=async(s,r)=>{try{console.log("Fetching QC checklist templates for product:",r);const{data:e,error:t}=await c.from("product_qc_templates").select(`
        template:template_id(*)
      `).eq("product_id",r);return t?(console.error("Error fetching QC checklist templates for product:",t),{templates:[],error:t.message}):{templates:e.map(i=>i.template)||[]}}catch(e){return console.error("Error in getQcChecklistTemplatesForProduct:",e),{templates:[],error:e.message}}};export{n as a,g as b,u as c,d,_ as g,h as u};
