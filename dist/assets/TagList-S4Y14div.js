import{s as l,j as a,e as i,al as _}from"./index-C6AV3cVN.js";const x=async t=>{try{const{data:e,error:r}=await l.rpc("get_entity_tags",{p_entity_type:"product",p_entity_id:t});return r?(console.error("Error fetching product tags:",r),{tags:[],error:r.message}):{tags:e||[]}}catch(e){return console.error("Error in getProductTags:",e),{tags:[],error:e.message}}},h=async(t,e)=>{try{const{data:r,error:o}=await l.rpc("add_tag_to_entity",{p_tag_id:e,p_entity_type:"product",p_entity_id:t});return o?(console.error("Error adding tag to product:",o),{success:!1,error:o.message}):{success:!0}}catch(r){return console.error("Error in addTagToProduct:",r),{success:!1,error:r.message}}},T=async(t,e)=>{try{const{data:r,error:o}=await l.rpc("remove_tag_from_entity",{p_tag_id:e,p_entity_type:"product",p_entity_id:t});return o?(console.error("Error removing tag from product:",o),{success:!1,error:o.message}):{success:!0}}catch(r){return console.error("Error in removeTagFromProduct:",r),{success:!1,error:r.message}}},j=({tags:t,maxDisplay:e=3,showTooltip:r=!0,className:o="",onClick:c})=>{if(!t||t.length===0)return null;const d=s=>{if(!s)return"blue";const p={red:"red",green:"green",blue:"blue",yellow:"yellow",purple:"purple",pink:"pink",indigo:"indigo",gray:"gray"},g=s.toLowerCase();for(const[m,y]of Object.entries(p))if(g.includes(m))return y;return"blue"},u=t.slice(0,e),n=t.length-e;return a.jsxs("div",{className:`flex flex-wrap gap-1.5 ${o}`,children:[u.map(s=>a.jsx(i,{color:d(s.color),className:`text-xs ${c?"cursor-pointer":""}`,onClick:c?()=>c(s):void 0,children:s.name},s.id)),n>0&&(r?a.jsx(_,{content:a.jsx("div",{className:"py-1",children:t.slice(e).map(s=>a.jsx("div",{className:"px-2 py-0.5 text-sm",children:s.name},s.id))}),children:a.jsxs(i,{color:"gray",className:"text-xs cursor-help",children:["+",n," more"]})}):a.jsxs(i,{color:"gray",className:"text-xs",children:["+",n," more"]}))]})};export{j as T,h as a,x as g,T as r};
