import{ab as K,d as Y,h as G,r as g,j as e,i as w,A as E,J as U,B as d,aD as _,aj as J,k as M,E as X,e as b,al as A,a2 as Z,b0 as ee,ai as B,aE as I,ao as C,_ as a}from"./index-C6AV3cVN.js";import{C as se}from"./Card-yj7fueH8.js";import{g as te,s as re}from"./purchaseOrder-DppPMsdd.js";import{c as q,a as j,d as z}from"./formatters-Cypx7G-j.js";import{u as ae}from"./currencyFormatter-BsFWv3sX.js";const xe=()=>{const{id:c}=K(),o=Y(),{currentOrganization:n}=G(),u=ae(),[t,P]=g.useState(null),[L,S]=g.useState(!0),[O,x]=g.useState(null),[f,k]=g.useState(!1);g.useEffect(()=>{(async()=>{if(!(!c||!n)){S(!0),x(null);try{const{purchaseOrder:r,error:l}=await te(n.id,c);l?x(l):r?r&&P(r):x("Purchase order not found")}catch(r){x(r.message||"An error occurred while fetching the purchase order")}finally{S(!1)}}})()},[c,n]);const Q=s=>{switch(s){case"sent":return"info";case"partially_received":return"warning";case"received":return"success";case"cancelled":return"failure";default:return"gray"}},$=s=>{switch(s){case"draft":return"Draft";case"sent":return"Sent";case"partially_received":return"Partially Received";case"received":return"Received";case"cancelled":return"Cancelled";default:return s.charAt(0).toUpperCase()+s.slice(1)}},N=()=>{o("/purchases/orders")},H=async()=>{if(!(!n||!c)){k(!0),x(null);try{const{purchaseOrder:s,error:r}=await re(n.id,c);r?x(r):s&&P(s)}catch(s){x(s.message||"An error occurred while sending the purchase order")}finally{k(!1)}}},F=()=>{if(!t)return;const s=window.open("","_blank");if(s){const r=W(t);s.document.write(r),s.document.close(),s.focus(),s.print()}},V=()=>!(t!=null&&t.items)||t.items.length===0?!1:t.items.every(s=>(s.received_quantity||0)>=s.quantity),R=()=>(t.status==="sent"||t.status==="partially_received")&&!V(),W=s=>{var l,p,y,m;const r=new Date().toLocaleDateString();return`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Purchase Order - ${s.order_number}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              font-size: 12px;
              line-height: 1.4;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #000;
              padding-bottom: 20px;
              margin-bottom: 20px;
            }
            .company-name {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 5px;
            }
            .document-title {
              font-size: 18px;
              font-weight: bold;
              margin-top: 15px;
            }
            .info-section {
              display: flex;
              justify-content: space-between;
              margin-bottom: 20px;
            }
            .info-box {
              width: 48%;
            }
            .info-box h3 {
              margin: 0 0 10px 0;
              font-size: 14px;
              font-weight: bold;
              border-bottom: 1px solid #ccc;
              padding-bottom: 5px;
            }
            .info-box p {
              margin: 5px 0;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            th, td {
              border: 1px solid #000;
              padding: 8px;
              text-align: left;
            }
            th {
              background-color: #f0f0f0;
              font-weight: bold;
            }
            .text-right {
              text-align: right;
            }
            .text-center {
              text-align: center;
            }
            .total-section {
              margin-top: 20px;
              text-align: right;
            }
            .total-row {
              font-size: 16px;
              font-weight: bold;
              margin-top: 10px;
            }
            .notes-section {
              margin-top: 30px;
              border-top: 1px solid #ccc;
              padding-top: 15px;
            }
            .signature-section {
              margin-top: 40px;
              display: flex;
              justify-content: space-between;
            }
            .signature-box {
              width: 30%;
              text-align: center;
            }
            .signature-line {
              border-top: 1px solid #000;
              margin-top: 40px;
              padding-top: 5px;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <!-- Header -->
          <div class="header">
            <div class="company-name">${(n==null?void 0:n.name)||"Your Company Name"}</div>
            <div>${(n==null?void 0:n.address)||""}</div>
            <div class="document-title">PURCHASE ORDER</div>
          </div>

          <!-- Purchase Order Info -->
          <div class="info-section">
            <div class="info-box">
              <h3>Purchase Order Details</h3>
              <p><strong>PO Number:</strong> ${s.order_number}</p>
              <p><strong>Date:</strong> ${q(s.order_date)}</p>
              <p><strong>Expected Delivery:</strong> ${s.expected_delivery_date?q(s.expected_delivery_date):"Not specified"}</p>
              <p><strong>Status:</strong> ${s.status.charAt(0).toUpperCase()+s.status.slice(1)}</p>
            </div>
            <div class="info-box">
              <h3>Supplier Information</h3>
              <p><strong>Supplier:</strong> ${((l=s.supplier)==null?void 0:l.name)||"Unknown Supplier"}</p>
              <p><strong>Contact:</strong> ${((p=s.supplier)==null?void 0:p.contact_person)||"N/A"}</p>
              <p><strong>Phone:</strong> ${((y=s.supplier)==null?void 0:y.phone)||"N/A"}</p>
              <p><strong>Email:</strong> ${((m=s.supplier)==null?void 0:m.email)||"N/A"}</p>
            </div>
          </div>

          <!-- Items Table -->
          <table>
            <thead>
              <tr>
                <th style="width: 40%">Product</th>
                <th style="width: 10%" class="text-center">Quantity</th>
                <th style="width: 10%" class="text-center">Unit</th>
                <th style="width: 15%" class="text-right">Unit Price</th>
                <th style="width: 15%" class="text-right">Total</th>
                <th style="width: 10%" class="text-center">Received</th>
              </tr>
            </thead>
            <tbody>
              ${s.items.map(i=>{var v,T,D;const h=i.base_quantity?i.base_quantity*i.unit_price:i.quantity*i.unit_price;return`
                  <tr>
                    <td>
                      <strong>${((v=i.product)==null?void 0:v.name)||"Unknown Product"}</strong>
                      ${(T=i.product)!=null&&T.sku?`<br><small>SKU: ${i.product.sku}</small>`:""}
                    </td>
                    <td class="text-center">${j(i.quantity)}</td>
                    <td class="text-center">${((D=i.uom)==null?void 0:D.code)||"pcs"}</td>
                    <td class="text-right">${u(i.unit_price)}</td>
                    <td class="text-right">${u(h)}</td>
                    <td class="text-center">${j(i.received_quantity||0)}</td>
                  </tr>
                `}).join("")}
            </tbody>
          </table>

          <!-- Total Section -->
          <div class="total-section">
            <div class="total-row">
              Total Amount: ${u(s.items.reduce((i,h)=>{const v=h.base_quantity?h.base_quantity*h.unit_price:h.quantity*h.unit_price;return i+v},0))}
            </div>
          </div>

          <!-- Notes Section -->
          ${s.notes?`
            <div class="notes-section">
              <h3>Notes:</h3>
              <p>${s.notes}</p>
            </div>
          `:""}

          <!-- Signature Section -->
          <div class="signature-section">
            <div class="signature-box">
              <div class="signature-line">Prepared By</div>
            </div>
            <div class="signature-box">
              <div class="signature-line">Approved By</div>
            </div>
            <div class="signature-box">
              <div class="signature-line">Received By</div>
            </div>
          </div>

          <!-- Footer -->
          <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #666;">
            Generated on ${r} | Purchase Order: ${s.order_number}
          </div>
        </body>
      </html>
    `};return L?e.jsx("div",{className:"container mx-auto px-4 py-8 flex justify-center",children:e.jsx(w,{size:"xl"})}):O?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(E,{color:"failure",icon:U,children:[e.jsx("h3",{className:"font-medium",children:"Error"}),e.jsx("p",{children:O}),e.jsx("div",{className:"mt-4",children:e.jsxs(d,{color:"gray",onClick:N,children:[e.jsx(_,{className:"mr-2 h-5 w-5"}),"Back to Purchase Orders"]})})]})}):t?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(se,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold flex items-center",children:[e.jsx(J,{className:"mr-2 h-6 w-6"}),"Purchase Order: ",t.order_number]}),e.jsxs("div",{className:"flex flex-wrap gap-4 mt-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(M,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("button",{onClick:()=>o(`/suppliers/${t.supplier_id}`),className:"text-blue-600 hover:text-blue-800 hover:underline font-medium focus:outline-none",title:"View supplier details",children:t.supplier_name})]}),e.jsxs("div",{className:"flex items-center text-gray-500",children:[e.jsx(X,{className:"mr-1 h-5 w-5"}),e.jsxs("span",{children:["Date: ",q(t.order_date)]})]}),e.jsx("div",{className:"flex items-center",children:e.jsx(b,{color:Q(t.status),children:$(t.status)})})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(d,{color:"gray",onClick:N,children:[e.jsx(_,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(A,{content:"Edit Purchase Order",children:e.jsx(d,{color:"primary",onClick:()=>o(`/purchases/orders/edit/${t.id}`),children:e.jsx(Z,{className:"h-5 w-5"})})}),e.jsx(A,{content:"Print Purchase Order",children:e.jsx(d,{color:"light",onClick:F,children:e.jsx(ee,{className:"h-5 w-5"})})}),t.status==="draft"&&e.jsxs(d,{color:"success",onClick:H,disabled:f,children:[f?e.jsx(w,{size:"sm",className:"mr-2"}):e.jsx(B,{className:"mr-2 h-5 w-5"}),"Send to Supplier"]}),R()&&e.jsxs(d,{color:"success",onClick:()=>o(`/inventory/receipts/create?purchaseOrderId=${t.id}`),children:[e.jsx(I,{className:"mr-2 h-5 w-5"}),"Receive Items"]})]})]}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-lg font-semibold mb-2 flex items-center",children:[e.jsx(C,{className:"mr-2 h-5 w-5"}),"Items"]}),t.items&&t.items.length>0?e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs(a,{children:[e.jsxs(a.Head,{children:[e.jsx(a.HeadCell,{children:"Product"}),e.jsx(a.HeadCell,{children:"Quantity"}),e.jsx(a.HeadCell,{children:"Unit"}),e.jsx(a.HeadCell,{children:"Unit Price"}),e.jsx(a.HeadCell,{children:"Total"}),e.jsx(a.HeadCell,{children:"Received"})]}),e.jsx(a.Body,{className:"divide-y",children:t.items.map(s=>{var r,l,p;return e.jsxs(a.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsxs(a.Cell,{className:"font-medium",children:[s.product?e.jsx("button",{onClick:()=>o(`/products/details/${s.product.id}`),className:"text-primary-600 hover:text-primary-800 hover:underline focus:outline-none",title:"View product details",children:s.product.name}):"Unknown Product",((r=s.product)==null?void 0:r.sku)&&e.jsxs("div",{className:"text-xs text-gray-500",children:["SKU: ",s.product.sku]})]}),e.jsx(a.Cell,{children:j(s.quantity)}),e.jsxs(a.Cell,{children:[((l=s.uom)==null?void 0:l.name)||"Unknown Unit",((p=s.uom)==null?void 0:p.code)&&e.jsxs("span",{className:"text-xs text-gray-500 block",children:["(",s.uom.code,")"]})]}),e.jsx(a.Cell,{children:u(s.unit_price)}),e.jsx(a.Cell,{children:(()=>{var m;const y=s.base_quantity?s.base_quantity*s.unit_price:s.quantity*s.unit_price;return e.jsxs(e.Fragment,{children:[u(y),e.jsxs("div",{className:"text-xs text-gray-500",children:[j(s.quantity)," ",((m=s.uom)==null?void 0:m.code)||"unit"," × ",u(s.unit_price)]})]})})()}),e.jsxs(a.Cell,{children:[j(s.received_quantity||0),s.received_quantity>0&&s.quantity>s.received_quantity&&e.jsx(b,{color:"warning",className:"ml-2",children:"Partial"}),s.received_quantity>=s.quantity&&e.jsx(b,{color:"success",className:"ml-2",children:"Complete"})]})]},s.id)})})]}),e.jsx("div",{className:"flex justify-end mt-4",children:e.jsxs("div",{className:"text-lg font-semibold",children:["Total: ",u(t.items.reduce((s,r)=>{const l=r.base_quantity?r.base_quantity*r.unit_price:r.quantity*r.unit_price;return s+l},0))]})})]}):e.jsx("div",{className:"p-4 bg-gray-50 rounded-lg text-center text-gray-500",children:"No items found in this purchase order."})]}),t.purchase_request_id&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("h2",{className:"text-lg font-semibold mb-2 flex items-center",children:[e.jsx(C,{className:"mr-2 h-5 w-5"}),"Source Purchase Request"]}),e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(C,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("button",{onClick:()=>o(`/purchases/requests/${t.purchase_request_id}`),className:"text-blue-600 hover:text-blue-800 hover:underline font-medium focus:outline-none",title:"View purchase request details",children:t.purchase_request_number||`Request #${t.purchase_request_id.substring(0,8)}`})]}),e.jsx(d,{color:"light",size:"xs",onClick:()=>o(`/purchases/requests/${t.purchase_request_id}`),children:"View Request"})]}),t.purchase_request_status&&e.jsxs("div",{className:"mt-2 flex items-center",children:[e.jsx("span",{className:"text-sm text-gray-500 mr-2",children:"Status:"}),e.jsx(b,{color:ie(t.purchase_request_status),children:ne(t.purchase_request_status)})]})]})]}),e.jsxs("div",{className:"flex justify-end mt-6 gap-2",children:[t.status==="draft"&&e.jsxs(d,{color:"success",onClick:H,disabled:f,children:[f?e.jsx(w,{size:"sm",className:"mr-2"}):e.jsx(B,{className:"mr-2 h-5 w-5"}),"Send to Supplier"]}),R()&&e.jsxs(d,{color:"success",onClick:()=>o(`/inventory/receipts/create?purchaseOrderId=${t.id}`),children:[e.jsx(I,{className:"mr-2 h-5 w-5"}),"Receive Items"]})]}),e.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[e.jsx("h2",{className:"text-sm font-medium text-gray-500 mb-2",children:"System Information"}),e.jsxs("div",{className:"flex flex-wrap gap-x-6 gap-y-2",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Purchase Order ID"}),e.jsx("p",{className:"text-sm",children:t.id})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Order Number"}),e.jsx("p",{className:"text-sm",children:t.order_number})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Status"}),e.jsx("p",{className:"text-sm",children:$(t.status)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Created"}),e.jsx("p",{className:"text-sm",children:z(t.created_at)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Last Updated"}),e.jsx("p",{className:"text-sm",children:z(t.updated_at)})]}),t.created_by&&e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Created By"}),e.jsx("p",{className:"text-sm",children:t.creator_name||t.created_by})]})]})]})]})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(E,{color:"warning",icon:U,children:[e.jsx("h3",{className:"font-medium",children:"Purchase Order Not Found"}),e.jsx("p",{children:"The requested purchase order could not be found."}),e.jsx("div",{className:"mt-4",children:e.jsxs(d,{color:"gray",onClick:N,children:[e.jsx(_,{className:"mr-2 h-5 w-5"}),"Back to Purchase Orders"]})})]})})},ie=c=>{switch(c){case"approved":return"success";case"pending":return"warning";case"rejected":return"failure";case"cancelled":return"dark";default:return"gray"}},ne=c=>c.charAt(0).toUpperCase()+c.slice(1);export{xe as default};
