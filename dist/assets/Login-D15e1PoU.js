import{r as n,j as e,A as h,J as I,B as f,t as v,c9 as L,d as O,a6 as x,P as k,U as A,L as S,ca as E}from"./index-C6AV3cVN.js";import{F as P}from"./FullLogo-CXg185jT.js";import{s as D}from"./auth-DeOTzV2I.js";import{checkUserOrganizations as T}from"./userProfile-Crkf42K4.js";const U=({variant:i="button",className:t=""})=>{const[r,c]=n.useState(!1),s=async()=>{if(!r){c(!0);try{await L()}catch(m){console.error("Error during cleanup:",m),window.location.href="/auth/login"}}};return i==="alert"?e.jsx(h,{color:"warning",className:t,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(I,{className:"mr-2 h-5 w-5"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Authentication Issue Detected"}),e.jsx("p",{className:"mt-1 text-sm",children:"There seems to be an issue with your authentication session. Click the button below to fix this issue."})]}),e.jsxs(f,{color:"warning",size:"sm",onClick:s,disabled:r,className:"ml-4",children:[e.jsx(v,{className:`mr-2 h-4 w-4 ${r?"animate-spin":""}`}),r?"Fixing...":"Fix Now"]})]})}):e.jsxs(f,{color:"gray",size:"sm",onClick:s,disabled:r,className:t,children:[e.jsx(v,{className:`mr-2 h-4 w-4 ${r?"animate-spin":""}`}),r?"Cleaning up...":"Fix Auth Issues"]})},R=()=>{const i=O(),[t,r]=n.useState({email:"",password:""}),[c,s]=n.useState(null),[m,d]=n.useState(!1),[p,z]=n.useState(!1),j=u=>{const{id:a,value:o}=u.target;r(l=>({...l,[a==="email"?"email":"password"]:o}))},[b,w]=n.useState(null),C=async u=>{u.preventDefault(),s(null),w(null),d(!0);try{if(!t.email||!t.password){s("Please enter both email and password"),d(!1);return}const{data:a,error:o}=await D(t);if(o)throw console.error("Login error:",o),o;if(a.session){w("Login successful!");try{const{hasOrganizations:l,organizations:g}=await T(a.user.id);if(!l)setTimeout(()=>{i("/onboarding")},1e3);else if(g&&g.length===1){const N=g[0].organization.id;localStorage.setItem("selectedOrganizationId",N);try{(await E()).find(F=>F.id===N)?window.location.href="/":setTimeout(()=>{i("/")},1e3)}catch(y){console.error("Error fetching organization details:",y),setTimeout(()=>{i("/")},1e3)}}else setTimeout(()=>{i("/auth/select-organization")},1e3)}catch(l){console.error("Error checking organizations:",l),setTimeout(()=>{i("/")},1e3)}}else console.error("No session returned from login"),s("Login failed. Please check your email and password.")}catch(a){console.error("Login error details:",a),a.message.includes("Invalid login credentials")?s("Invalid email or password. Please try again."):a.message.includes("Email not confirmed")?s("Please confirm your email address before logging in."):a.message.includes("network")?s("Network error. Please check your internet connection and try again."):s(a.message||"An error occurred during login")}finally{d(!1)}};return e.jsxs(e.Fragment,{children:[c&&e.jsx(h,{color:"failure",className:"mb-4",children:c}),b&&e.jsx(h,{color:"success",className:"mb-4",children:b}),e.jsxs("form",{onSubmit:C,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(x,{htmlFor:"email",value:"Email"})}),e.jsx(k,{id:"email",type:"email",sizing:"md",required:!0,value:t.email,onChange:j,className:"form-control form-rounded-xl"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(x,{htmlFor:"password",value:"Password"})}),e.jsx(k,{id:"password",type:"password",sizing:"md",required:!0,value:t.password,onChange:j,className:"form-control form-rounded-xl"})]}),e.jsxs("div",{className:"flex justify-between my-5",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(A,{id:"remember",className:"checkbox",checked:p,onChange:()=>z(!p)}),e.jsx(x,{htmlFor:"remember",className:"opacity-90 font-normal cursor-pointer",children:"Remember this Device"})]}),e.jsx(S,{to:"/auth/forgot-password",className:"text-primary text-sm font-medium",children:"Forgot Password?"})]}),e.jsx(f,{type:"submit",color:"primary",className:"w-full bg-primary text-white rounded-xl",disabled:m,children:m?"Signing in...":"Sign in"}),e.jsxs("div",{className:"mt-4 text-center",children:[e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Having login issues?"}),e.jsx(U,{variant:"button",className:"text-sm"})]})]})]})},B={background:"linear-gradient(45deg, rgb(238, 119, 82,0.2), rgb(231, 60, 126,0.2), rgb(35, 166, 213,0.2), rgb(35, 213, 171,0.2))",backgroundSize:"400% 400%",animation:"gradient 15s ease infinite",height:"100vh"},K=()=>e.jsx("div",{style:B,className:"relative overflow-hidden h-screen",children:e.jsx("div",{className:"flex h-full justify-center items-center px-4",children:e.jsx("div",{className:"rounded-xl shadow-md bg-white dark:bg-darkgray p-6 w-full md:w-96 border-none",children:e.jsxs("div",{className:"flex flex-col gap-2 p-0 w-full",children:[e.jsx("div",{className:"mx-auto",children:e.jsx(P,{})}),e.jsx("p",{className:"text-sm text-center text-dark my-3",children:"Sign In to POS System"}),e.jsx("p",{className:"text-xs text-center text-gray-500 mb-4",children:"Access your multi-tenant retail business management system"}),e.jsx(R,{}),e.jsxs("div",{className:"flex gap-2 text-base text-ld font-medium mt-6 items-center justify-center",children:[e.jsx("p",{children:"New to Airyx?"}),e.jsx(S,{to:"/auth/register",className:"text-primary text-sm font-medium",children:"Create an account"})]})]})})})});export{K as default};
