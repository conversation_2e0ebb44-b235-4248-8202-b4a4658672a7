import{d as le,h as ce,b as de,r as h,j as e,B as g,aD as ue,al as y,ay as me,A as he,J as fe,a6 as C,Y as S,P as $,a7 as pe,ad as xe,a0 as ge,_ as c,F as je,a3 as ve,i as ye,M as q}from"./index-C6AV3cVN.js";import{C as _e}from"./Card-yj7fueH8.js";import{U as be}from"./UomSelector-CTe3hN2d.js";import{u as Ne,a as Ce,b as L,A as Se,F as qe,K as V,Q as we,C as Pe,V as Ie}from"./KeyboardShortcutTooltip-DeEIPKJI.js";import{p as B,E as Fe}from"./EnhancedProductSearchSelector-CWWD8-c1.js";import{c as ke}from"./purchaseRequest-Bff56uzH.js";import{E as Ee}from"./EnhancedNumberInput-Bwo1E3yF.js";import"./productUom-k6aUg6b7.js";import"./product-Ca8DWaNR.js";import"./typeof-QjJsDpFa.js";const Le=()=>{const w=le(),{currentOrganization:l}=ce(),{user:j}=de(),Y={fields:{notes:[Ie.maxLength(500,"Notes must be less than 500 characters")]},form:[{validate:(t,r)=>r.items&&r.items.length>0,message:"Please add at least one item to the purchase request"}]},{formData:f,setFormData:K,errors:x,formErrors:U,isValid:Q,validateForm:W,handleChange:P,setFormDataWithoutValidation:A}=Ne({organization_id:(l==null?void 0:l.id)||"",request_number:"",requester_id:(j==null?void 0:j.id)||"",status:"draft",notes:"",items:[]},Y),[I,D]=h.useState([]),[_,H]=h.useState(!1),[z,p]=h.useState(null),[G,b]=h.useState(!1),[F,J]=h.useState(""),[X,k]=h.useState(!1),[R,Z]=h.useState(null),{save:ee,clear:T,lastSavedFormatted:te,isDirty:M,isSaving:se}=Ce({key:"purchase-request-draft",initialData:f,interval:5e3,enabled:!0,onSave:t=>{}});L({key:"s",ctrl:!0},{callback:()=>{_||E()},isEnabled:!0}),L({key:"a",ctrl:!0},{callback:()=>{O()},isEnabled:!0});const u=f.items||[],m=t=>{if(typeof t=="function"){const r=t(u);A({...f,items:r})}else A({...f,items:t})};h.useEffect(()=>{if(l){const t=new Date,r=t.getFullYear(),o=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0"),a=Math.floor(Math.random()*1e3).toString().padStart(3,"0");K(n=>({...n,organization_id:l.id,request_number:`PR-${r}${o}${s}-${a}`}))}},[l]),h.useEffect(()=>{(async()=>{if(l)try{const{products:r}=await B.searchProducts(l.id,"",1,20);D(r)}catch(r){console.error("Error fetching products:",r)}})()},[l]);const O=()=>{if(I.length===0)return;const t={product_id:"",quantity:1,uom_id:""};m(r=>[...r,t])},re=t=>{m(r=>r.filter((o,s)=>s!==t))},N=(t,r,o)=>{m(s=>{const a=[...s];return a[t]={...a[t],[r]:o},r==="product_id"&&(a[t].uom_id="",a[t].uom=void 0),a}),r==="product_id"&&o&&(async()=>{try{if(u[t].product){const a=u[t].product;if(a&&a.product_uoms&&a.product_uoms.length>0){const n=a.product_uoms.find(i=>i.is_default);n&&m(i=>{const d=[...i];return d[t]&&d[t].product_id===o&&(d[t].uom_id=n.uom_id,d[t].uom=n),d})}}else{const a=await ie(o);if(a&&(m(n=>{const i=[...n];return i[t]&&i[t].product_id===o&&(i[t].product=a),i}),a.product_uoms&&a.product_uoms.length>0)){const n=a.product_uoms.find(i=>i.is_default);if(n)m(i=>{const d=[...i];return d[t]&&d[t].product_id===o&&(d[t].uom_id=n.uom_id,d[t].uom=n),d});else if(a.product_uoms.length>0){const i=a.product_uoms[0];m(d=>{const v=[...d];return v[t]&&v[t].product_id===o&&(v[t].uom_id=i.uom_id,v[t].uom=i),v})}}}}catch(a){console.error("Error setting default UoM:",a)}})()},ae=(t,r,o)=>{!r||!o||m(s=>{const a=[...s];return t>=0&&t<a.length&&(a[t]={...a[t],uom_id:r,uom:{...o}}),a})},oe=t=>{M?(Z(t),k(!0)):w(t)},ne=()=>{R&&(T(),w(R)),k(!1)},E=async t=>{if(t&&t.preventDefault(),!l||!j){p("Organization or user not found");return}if(!W()){p("Please fix the validation errors before submitting");return}for(let o=0;o<u.length;o++){const s=u[o];if(!s.product_id){p(`Please select a product for item #${o+1}`);return}if(!s.uom_id){p(`Please select a unit of measurement for item #${o+1}`);return}if(s.quantity<=0){p(`Please enter a valid quantity for item #${o+1}`);return}}H(!0),p(null);try{ee();const o={organization_id:l.id,request_number:f.request_number,requester_id:j.id,status:"draft",notes:f.notes||null},s=u.map(n=>({product_id:n.product_id,quantity:n.quantity,uom_id:n.uom_id,base_quantity:n.quantity,notes:n.notes||null})),{error:a}=await ke(o,s);a?(console.error("Error creating purchase request:",a),p(a)):(T(),w("/purchases/requests"))}catch(o){p(o.message||"An error occurred while creating the purchase request")}finally{H(!1)}},ie=h.useCallback(async t=>{const r=u.find(s=>s.product_id===t&&s.product);if(r&&r.product)return r.product;const o=I.find(s=>s.id===t);if(o)return o;if(l)try{const s=await B.getProduct(l.id,t);if(s)return D(a=>a.some(n=>n.id===s.id)?a:[...a,s]),s}catch(s){console.error("Error fetching product from cache:",s)}},[u,I,l]);return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(_e,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Create Purchase Request"}),e.jsx(Se,{isSaving:se,isDirty:M,lastSavedFormatted:te,className:"mt-1"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(g,{color:"gray",onClick:()=>oe("/purchases/requests"),title:"Go back to purchase requests list",children:[e.jsx(ue,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(y,{content:"Save purchase request (Ctrl+S)",children:e.jsxs(g,{color:"primary",onClick:E,disabled:_,children:[e.jsx(me,{className:"mr-2 h-5 w-5"}),"Save"]})})]})]}),e.jsx(qe,{errors:x,formErrors:U,fieldLabels:{notes:"Notes",items:"Items"},show:!Q&&Object.keys(x).length>0||U.length>0,className:"mb-4"}),z&&e.jsx(he,{color:"failure",icon:fe,className:"mb-4",children:z}),e.jsxs("form",{onSubmit:E,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx(C,{htmlFor:"request_number",value:"Request Number",className:"text-sm font-medium"}),e.jsx(y,{content:"Automatically generated unique identifier for this purchase request",children:e.jsx(S,{className:"ml-1 h-4 w-4 text-gray-400 hover:text-gray-600"})})]}),e.jsx($,{id:"request_number",name:"request_number",value:f.request_number,onChange:t=>P("request_number",t.target.value),disabled:!0})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx(C,{htmlFor:"status",value:"Status",className:"text-sm font-medium"}),e.jsx(y,{content:"Purchase requests start as drafts and can be submitted for approval later",children:e.jsx(S,{className:"ml-1 h-4 w-4 text-gray-400 hover:text-gray-600"})})]}),e.jsxs(pe,{id:"status",name:"status",value:f.status,onChange:t=>P("status",t.target.value),disabled:!0,children:[e.jsx("option",{value:"draft",children:"Draft"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"approved",children:"Approved"}),e.jsx("option",{value:"rejected",children:"Rejected"}),e.jsx("option",{value:"cancelled",children:"Cancelled"})]})]})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx(C,{htmlFor:"notes",value:"Notes",className:"text-sm font-medium"}),e.jsx(y,{content:"Add any additional information about this purchase request",children:e.jsx(S,{className:"ml-1 h-4 w-4 text-gray-400 hover:text-gray-600"})})]}),e.jsx(xe,{id:"notes",name:"notes",value:f.notes||"",onChange:t=>P("notes",t.target.value),placeholder:"Enter any additional notes",rows:3,color:x.notes&&x.notes.length>0?"failure":void 0,helperText:x.notes&&x.notes.length>0?e.jsx("ul",{className:"mt-1 text-sm text-red-600 list-disc list-inside",children:x.notes.map((t,r)=>e.jsx("li",{children:t},r))}):null})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(C,{value:"Items",className:"text-sm font-medium"}),e.jsx(y,{content:"Products to be included in this purchase request",children:e.jsx(S,{className:"ml-1 h-4 w-4 text-gray-400 hover:text-gray-600"})})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(V,{shortcut:{key:"a",ctrl:!0}}),e.jsxs(g,{size:"xs",color:"primary",onClick:O,title:"Add item (Ctrl+A)",children:[e.jsx(ge,{className:"mr-2 h-4 w-4"}),"Add Item"]})]})]}),u.length===0?e.jsx("div",{className:"p-4 bg-gray-50 rounded-lg text-center text-gray-500",children:'No items added. Click "Add Item" to add products to this purchase request.'}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(c,{children:[e.jsxs(c.Head,{children:[e.jsx(c.HeadCell,{children:"Product"}),e.jsx(c.HeadCell,{children:"Quantity"}),e.jsx(c.HeadCell,{children:"Unit"}),e.jsx(c.HeadCell,{children:"Notes"}),e.jsx(c.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(c.Body,{className:"divide-y",children:u.map((t,r)=>{var o;return e.jsxs(c.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(c.Cell,{children:e.jsx(Fe,{value:t.product_id,onChange:(s,a)=>{a?m(n=>{const i=[...n];return i[r]={...i[r],product_id:s,product:a},i}):N(r,"product_id",s)},required:!0,className:"text-sm",placeholder:"Search for a product...",pageSize:5,instanceId:`pr-product-${r}`})}),e.jsx(c.Cell,{children:e.jsx(Ee,{min:"0.01",step:"0.01",value:t.quantity,onChange:s=>N(r,"quantity",parseFloat(s.target.value)||0),onBlur:s=>{(s.target.value===""||parseFloat(s.target.value)<=0)&&N(r,"quantity",1)},required:!0,className:"text-sm",sizing:"sm",autoSelect:!0,preventScrollChange:!0})}),e.jsx(c.Cell,{children:t.product_id?e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex-grow",children:e.jsx(be,{productId:t.product_id,value:t.uom_id||"",onChange:s=>{m(a=>{const n=[...a];return r>=0&&r<n.length&&(n[r]={...n[r],uom_id:s}),n})},onUomChange:s=>{ae(r,s.uom_id,s)},filter:"all",disabled:!1,preloadedUoms:(o=t.product)==null?void 0:o.product_uoms},`uom-selector-${t.product_id}-${r}`)}),e.jsx("div",{children:e.jsx(g,{size:"xs",color:"light",className:"p-1",title:"Setup Units of Measurement",onClick:()=>{J(t.product_id),b(!0)},children:e.jsx(je,{className:"h-3 w-3"})})})]}):e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex-grow",children:e.jsx("div",{className:"text-sm text-gray-500 p-2 border border-gray-300 rounded-lg bg-gray-50",children:"Select a product first"})}),e.jsx("div",{className:"w-8"})]})}),e.jsx(c.Cell,{children:e.jsx($,{value:t.notes||"",onChange:s=>N(r,"notes",s.target.value),placeholder:"Optional notes",className:"text-sm",sizing:"sm"})}),e.jsx(c.Cell,{children:e.jsx(g,{color:"failure",size:"xs",onClick:()=>re(r),children:e.jsx(ve,{className:"h-4 w-4"})})})]},r)})})]})})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(V,{shortcut:{key:"s",ctrl:!0},className:"mr-2"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Press Ctrl+S to save"})]}),e.jsx(g,{type:"submit",color:"primary",disabled:_,children:_?e.jsxs(e.Fragment,{children:[e.jsx(ye,{size:"sm",className:"mr-2"}),"Saving..."]}):"Create Purchase Request"})]})]})]}),e.jsxs(q,{show:G,onClose:()=>b(!1),size:"xl",children:[e.jsx(q.Header,{children:"Configure Units of Measurement for Purchasing"}),e.jsx(q.Body,{children:F&&e.jsx(we,{productId:F,onComplete:()=>{b(!1);const t=u.findIndex(r=>r.product_id===F);if(t>=0){const r=u[t].product_id;m(o=>{const s=[...o];return s[t]={...s[t],uom_id:""},s}),setTimeout(()=>{m(o=>{const s=[...o];return s[t]={...s[t],product_id:r},s})},100)}}})}),e.jsx(q.Footer,{children:e.jsx(g,{color:"gray",onClick:()=>b(!1),children:"Close"})})]}),e.jsx(Pe,{show:X,title:"Unsaved Changes",message:"You have unsaved changes. Are you sure you want to leave this page? Your changes will be lost.",confirmText:"Leave Page",cancelText:"Stay on Page",confirmColor:"failure",onClose:()=>k(!1),onConfirm:ne})]})};export{Le as default};
