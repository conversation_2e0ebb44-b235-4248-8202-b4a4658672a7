import{s as k,aT as X,h as V,b as K,r as l,j as e,aU as E,A as j,i as A,B as m,T as Y,t as Q,e as Z,bp as ee,M as b,a6 as q,aR as se,P as U,a0 as te,bq as G,br as re,ab as ae,d as le,J as $,L as R,o as J,a2 as ie,a3 as ne,aX as ce,aY as oe,bs as de,aj as me,p as xe,bt as he,k as ue,bu as je}from"./index-C6AV3cVN.js";import{C as x}from"./Card-yj7fueH8.js";import{a as fe,d as ge}from"./customer-COogBrXM.js";import{T as ye}from"./TagSelector-DMJHWHGW.js";import{u as pe}from"./currencyFormatter-BsFWv3sX.js";import{c as Ne}from"./formatters-Cypx7G-j.js";import"./tagService-sPq402Av.js";const ve=async r=>{try{const{data:a,error:i}=await k.rpc("get_entity_tags",{p_entity_type:"customer",p_entity_id:r});return i?(console.error("Error fetching customer tags:",i),{tags:[],error:i.message}):{tags:a||[]}}catch(a){return console.error("Error in getCustomerTags:",a),{tags:[],error:a.message}}},be=async(r,a)=>{try{const{data:i,error:s}=await k.rpc("add_tag_to_entity",{p_tag_id:a,p_entity_type:"customer",p_entity_id:r});return s?(console.error("Error adding tag to customer:",s),{success:!1,error:s.message}):{success:!0}}catch(i){return console.error("Error in addTagToCustomer:",i),{success:!1,error:i.message}}},we=async(r,a)=>{try{const{data:i,error:s}=await k.rpc("remove_tag_from_entity",{p_tag_id:a,p_entity_type:"customer",p_entity_id:r});return s?(console.error("Error removing tag from customer:",s),{success:!1,error:s.message}):{success:!0}}catch(i){return console.error("Error in removeTagFromCustomer:",i),{success:!1,error:i.message}}},_e=({customerId:r})=>{const{settings:a,getCustomerProfile:i,refreshSettings:s}=X(),{currentOrganization:f}=V(),{user:D}=K(),O=pe(),[g,p]=l.useState(null),[y,N]=l.useState([]),[I,w]=l.useState(!1),[S,L]=l.useState(null),[M,h]=l.useState(!1),[_,v]=l.useState(0),[C,T]=l.useState(""),[H,z]=l.useState(!1),[F,n]=l.useState(null),[o,u]=l.useState(null);l.useEffect(()=>{(async()=>{if(!(!f||!r)){w(!0);try{const{data:c,error:P}=await k.from("customers").select("loyalty_eligible").eq("id",r).single();if(P)throw new Error(P.message);if(u(c.loyalty_eligible),c.loyalty_eligible===!1){w(!1);return}const B=await i(r);p(B);const{transactions:W}=await G(f.id,r);N(W)}catch(c){console.error("Error fetching loyalty profile:",c),L(c.message||"Failed to load loyalty profile")}finally{w(!1)}}})()},[f,r,i]);const d=async()=>{if(!f||!r||!D){n("Missing required information");return}if(_===0){n("Please enter a non-zero point value");return}if(!C.trim()){n("Please provide a reason for the adjustment");return}z(!0),n(null);try{const{success:t,error:c}=await re(f.id,r,D.id,_,C);if(c)n(c);else{const P=await i(r);p(P);const{transactions:B}=await G(f.id,r);N(B),h(!1),v(0),T("")}}catch(t){console.error("Error adjusting points:",t),n(t.message||"Failed to adjust points")}finally{z(!1)}};return a!=null&&a.is_enabled?o===!1?e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(E,{className:"mr-2 h-6 w-6 text-gray-400"}),e.jsx("h2",{className:"text-xl font-bold",children:"Loyalty Program"})]}),e.jsx(j,{color:"warning",children:"This customer is not eligible for the loyalty program. You can enable eligibility in the customer edit form."})]}):I?e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(E,{className:"mr-2 h-6 w-6 text-blue-500"}),e.jsx("h2",{className:"text-xl font-bold",children:"Loyalty Program"})]}),e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(A,{size:"lg"})})]}):S?e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(E,{className:"mr-2 h-6 w-6 text-blue-500"}),e.jsx("h2",{className:"text-xl font-bold",children:"Loyalty Program"})]}),e.jsx(j,{color:"failure",children:S})]}):g?e.jsxs(e.Fragment,{children:[e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(E,{className:"mr-2 h-6 w-6 text-blue-500"}),e.jsx("h2",{className:"text-xl font-bold",children:"Loyalty Program"})]}),e.jsx(m,{color:"light",size:"xs",onClick:()=>h(!0),children:"Adjust Points"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Available Points"}),e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:g.current_points_balance}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:["Value: ",O(g.current_points_balance*((a==null?void 0:a.points_redemption_rate)||0))]})]}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Lifetime Earned"}),e.jsx("div",{className:"text-2xl font-bold text-green-600",children:g.lifetime_points_earned})]}),e.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Lifetime Redeemed"}),e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:g.lifetime_points_redeemed})]})]}),e.jsxs(Y,{"aria-label":"Loyalty tabs",children:[e.jsx(Y.Item,{title:"Transactions",icon:Q,children:y.length===0?e.jsx("div",{className:"text-center py-4 text-gray-500",children:"No loyalty transactions yet"}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm text-left text-gray-500",children:[e.jsx("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2",children:"Date"}),e.jsx("th",{className:"px-4 py-2",children:"Type"}),e.jsx("th",{className:"px-4 py-2",children:"Points"}),e.jsx("th",{className:"px-4 py-2",children:"Notes"})]})}),e.jsx("tbody",{children:y.map(t=>e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"px-4 py-2",children:Ne(t.created_at||"")}),e.jsx("td",{className:"px-4 py-2",children:e.jsx(Z,{color:t.transaction_type==="earn"?"success":t.transaction_type==="redeem"?"purple":t.transaction_type==="expire"?"warning":"info",children:t.transaction_type})}),e.jsxs("td",{className:"px-4 py-2 font-medium",children:[t.points>0?"+":"",t.points]}),e.jsx("td",{className:"px-4 py-2",children:t.notes})]},t.id))})]})})}),e.jsx(Y.Item,{title:"Statistics",icon:ee,children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Points Earned This Year"}),e.jsx("div",{className:"text-xl font-bold",children:y.filter(t=>t.transaction_type==="earn"&&new Date(t.created_at||"").getFullYear()===new Date().getFullYear()).reduce((t,c)=>t+Math.abs(c.points),0)})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Points Redeemed This Year"}),e.jsx("div",{className:"text-xl font-bold",children:y.filter(t=>t.transaction_type==="redeem"&&new Date(t.created_at||"").getFullYear()===new Date().getFullYear()).reduce((t,c)=>t+Math.abs(c.points),0)})]})]})})]})]}),e.jsxs(b,{show:M,onClose:()=>h(!1),children:[e.jsx(b.Header,{children:"Adjust Loyalty Points"}),e.jsxs(b.Body,{children:[F&&e.jsx(j,{color:"failure",className:"mb-4",children:F}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(q,{htmlFor:"adjustPoints",children:"Points"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(m,{color:"light",size:"sm",onClick:()=>v(t=>t-100),children:e.jsx(se,{className:"h-4 w-4"})}),e.jsx(U,{id:"adjustPoints",type:"number",value:_,onChange:t=>v(parseInt(t.target.value)||0),className:"mx-2"}),e.jsx(m,{color:"light",size:"sm",onClick:()=>v(t=>t+100),children:e.jsx(te,{className:"h-4 w-4"})})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Use positive values to add points, negative to deduct points"})]}),e.jsxs("div",{children:[e.jsx(q,{htmlFor:"adjustNotes",children:"Reason for Adjustment"}),e.jsx(U,{id:"adjustNotes",type:"text",value:C,onChange:t=>T(t.target.value),placeholder:"e.g., Customer service compensation"})]})]})]}),e.jsxs(b.Footer,{children:[e.jsx(m,{color:"primary",onClick:d,disabled:H,children:H?e.jsxs(e.Fragment,{children:[e.jsx(A,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Adjustment"}),e.jsx(m,{color:"gray",onClick:()=>h(!1),children:"Cancel"})]})]})]}):e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(E,{className:"mr-2 h-6 w-6 text-blue-500"}),e.jsx("h2",{className:"text-xl font-bold",children:"Loyalty Program"})]}),e.jsx(j,{color:"info",children:"This customer doesn't have a loyalty profile yet. They'll earn points with their first purchase."})]}):e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(E,{className:"mr-2 h-6 w-6 text-gray-400"}),e.jsx("h2",{className:"text-xl font-bold",children:"Loyalty Program"})]}),e.jsx(j,{color:"info",children:"The loyalty program is currently disabled. Enable it in organization settings."})]})},De=()=>{const{id:r}=ae(),{currentOrganization:a}=V(),i=le(),[s,f]=l.useState(null),[D,O]=l.useState(!0),[g,p]=l.useState(null),[y,N]=l.useState([]),[I,w]=l.useState(!1),[S,L]=l.useState(null),[M,h]=l.useState(!1),[_,v]=l.useState(!1),[C,T]=l.useState(null);l.useEffect(()=>{(async()=>{if(!(!a||!r)){O(!0),p(null);try{const{customer:o,error:u}=await fe(a.id,r);u?p(u):o?f(o):p("Customer not found")}catch(o){p(o.message||"An error occurred while fetching the customer")}finally{O(!1)}}})()},[a,r]),l.useEffect(()=>{(async()=>{if(r){w(!0),L(null);try{const{tags:o,error:u}=await ve(r);u?L(u):N(o)}catch(o){L(o.message||"An error occurred while fetching tags")}finally{w(!1)}}})()},[r]);const H=()=>{h(!0)},z=async()=>{if(!(!a||!s)){v(!0),T(null);try{const{success:n,error:o}=await ge(a.id,s.id);o?T(o):n&&(h(!1),i("/customers"))}catch(n){T(n.message||"An error occurred while deleting the customer")}finally{v(!1)}}},F=async n=>{if(!r)return;const o=n.filter(d=>!y.some(t=>t.id===d.id)),u=y.filter(d=>!n.some(t=>t.id===d.id));N(n);for(const d of o)try{await be(r,d.id)}catch(t){console.error(`Failed to add tag ${d.name}:`,t),N(c=>c.filter(P=>P.id!==d.id))}for(const d of u)try{await we(r,d.id)}catch(t){console.error(`Failed to remove tag ${d.name}:`,t),N(c=>[...c,d])}};return a?D?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(x,{children:e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(A,{size:"xl"})})})}):g||!s?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(x,{children:e.jsxs(j,{color:"failure",icon:$,children:[e.jsx("h3",{className:"text-lg font-medium",children:"Error"}),e.jsx("p",{children:g||"Customer not found"}),e.jsx("div",{className:"mt-4",children:e.jsx(R,{to:"/customers",children:e.jsxs(m,{color:"gray",size:"sm",children:[e.jsx(J,{className:"mr-2 h-4 w-4"}),"Back to Customers"]})})})]})})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(x,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsx("div",{className:"flex items-center",children:e.jsx("h1",{className:"text-2xl font-bold",children:s.name})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(R,{to:"/customers",children:e.jsxs(m,{color:"gray",children:[e.jsx(J,{className:"mr-2 h-5 w-5"}),"Back"]})}),e.jsx(R,{to:`/customers/edit/${s.id}`,children:e.jsxs(m,{color:"primary",children:[e.jsx(ie,{className:"mr-2 h-5 w-5"}),"Edit"]})}),e.jsxs(m,{color:"failure",onClick:H,children:[e.jsx(ne,{className:"mr-2 h-5 w-5"}),"Delete"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Contact Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx(ce,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),e.jsx("p",{children:s.email||"Not provided"})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(oe,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),e.jsx("p",{children:s.phone||"Not provided"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Additional Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx(de,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Tax ID"}),e.jsx("p",{children:s.tax_id||"Not provided"})]})]}),s.notes&&e.jsxs("div",{className:"flex items-start",children:[e.jsx(me,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Notes"}),e.jsx("p",{className:"whitespace-pre-line",children:s.notes})]})]}),e.jsxs("div",{className:"flex items-start mt-4",children:[e.jsx(xe,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{className:"w-full",children:[e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Tags"}),I?e.jsxs("div",{className:"flex items-center",children:[e.jsx(A,{size:"sm",className:"mr-2"}),e.jsx("span",{className:"text-gray-500",children:"Loading tags..."})]}):S?e.jsx(j,{color:"failure",className:"mb-4",children:S}):e.jsx("div",{children:e.jsx(ye,{entityType:"customer",entityId:s.id,selectedTags:y,onTagsChange:F})})]})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Address Information"}),e.jsxs("div",{className:"space-y-4",children:[s.address&&e.jsxs("div",{className:"flex items-start",children:[e.jsx(he,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Street Address"}),e.jsx("p",{children:s.address})]})]}),(s.city||s.state)&&e.jsxs("div",{className:"flex items-start",children:[e.jsx(ue,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"City/State"}),e.jsx("p",{children:s.city&&s.state?`${s.city}, ${s.state}`:s.city||s.state})]})]}),(s.postal_code||s.country)&&e.jsxs("div",{className:"flex items-start",children:[e.jsx(je,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:s.postal_code&&s.country?"Postal Code/Country":s.postal_code?"Postal Code":"Country"}),e.jsx("p",{children:s.postal_code&&s.country?`${s.postal_code}, ${s.country}`:s.postal_code||s.country})]})]}),!s.address&&!s.city&&!s.state&&!s.postal_code&&!s.country&&e.jsx("p",{className:"text-gray-500",children:"No address information provided"})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(_e,{customerId:s.id})}),e.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[e.jsx("h2",{className:"text-sm font-medium text-gray-500 mb-2",children:"System Information"}),e.jsxs("div",{className:"flex flex-wrap gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Customer ID"}),e.jsx("p",{className:"text-sm",children:s.id})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Created"}),e.jsx("p",{className:"text-sm",children:new Date(s.created_at).toLocaleString()})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Last Updated"}),e.jsx("p",{className:"text-sm",children:new Date(s.updated_at).toLocaleString()})]})]})]})]}),e.jsxs(b,{show:M,onClose:()=>h(!1),children:[e.jsx(b.Header,{children:"Confirm Deletion"}),e.jsx(b.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx($,{className:"mx-auto mb-4 h-14 w-14 text-red-500"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500",children:["Are you sure you want to delete ",s.name,"?"]}),C&&e.jsx(j,{color:"failure",className:"mb-4",children:C}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(m,{color:"failure",onClick:z,disabled:_,children:_?e.jsx(A,{size:"sm"}):"Yes, delete"}),e.jsx(m,{color:"gray",onClick:()=>h(!1),children:"No, cancel"})]})]})})]})]}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(x,{children:e.jsxs(j,{color:"failure",icon:$,children:[e.jsx("h3",{className:"text-lg font-medium",children:"No Organization Selected"}),e.jsx("p",{children:"Please select an organization to view customer details."})]})})})};export{De as default};
