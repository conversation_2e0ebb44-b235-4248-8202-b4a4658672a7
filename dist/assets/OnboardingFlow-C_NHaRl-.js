const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/userProfile-Crkf42K4.js","assets/index-C6AV3cVN.js","assets/index-ClH3u6fg.css"])))=>i.map(i=>d[i]);
import{r as p,j as e,k as R,A as v,a6 as b,P as f,B as g,bo as O,aN as q,cb as w,cc as k,F as _,Q as G,cd as C,bB as F,ce as L,aY as M,K as A,aM as Y,i as K,b as Q,d as V,bK as Z,s as B,aZ as J}from"./index-C6AV3cVN.js";import{C as X}from"./Card-yj7fueH8.js";const ee=({data:r,updateData:n,onNext:t})=>{const[o,l]=p.useState(null),i=()=>{if(l(null),!r.organizationName.trim()){l("Organization name is required");return}if(r.organizationName.trim().length<2){l("Organization name must be at least 2 characters long");return}t()},c=d=>{n({organizationName:d}),o&&l(null)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4",children:e.jsx(R,{className:"w-8 h-8 text-blue-600 dark:text-blue-400"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"What's your organization name?"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"This will be the name of your business or company in the system"})]}),o&&e.jsx(v,{color:"failure",children:o}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(b,{htmlFor:"organizationName",value:"Organization/Company Name"})}),e.jsx(f,{id:"organizationName",type:"text",placeholder:"Enter your organization name",value:r.organizationName,onChange:d=>c(d.target.value),required:!0,autoFocus:!0}),e.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:`Examples: "ABC Store", "Maria's Restaurant", "Tech Solutions Inc."`})]})}),e.jsx("div",{className:"flex justify-end",children:e.jsx(g,{onClick:i,disabled:!r.organizationName.trim(),className:"px-8",children:"Continue"})})]})},E=[{id:"retail_store",name:"Retail Store",description:"Clothing, electronics, general merchandise",icon:O,posType:"retail",popular:!0},{id:"sari_sari_store",name:"Sari-Sari Store",description:"Neighborhood convenience store",icon:q,posType:"retail",popular:!0},{id:"restaurant",name:"Restaurant",description:"Full-service dining establishment",icon:w,posType:"restaurant",popular:!0},{id:"cafe_coffee_shop",name:"Café/Coffee Shop",description:"Coffee, pastries, light meals",icon:k,posType:"cafe",popular:!0},{id:"bakery",name:"Bakery",description:"Bread, cakes, pastries",icon:k,posType:"retail"},{id:"pharmacy",name:"Pharmacy",description:"Medicines and health products",icon:k,posType:"retail"},{id:"beauty_salon",name:"Beauty Salon/Barbershop",description:"Hair, beauty, and grooming services",icon:w,posType:"general"},{id:"auto_parts",name:"Auto Parts Store",description:"Car parts and accessories",icon:_,posType:"retail"},{id:"hardware",name:"Hardware Store",description:"Tools, construction materials",icon:_,posType:"retail"},{id:"grocery",name:"Grocery Store",description:"Food and household items",icon:O,posType:"retail"},{id:"fast_food",name:"Fast Food",description:"Quick service restaurant",icon:w,posType:"restaurant"},{id:"other",name:"Other",description:"Different type of business",icon:_,posType:"general"}],re=({data:r,updateData:n,onNext:t,onPrev:o})=>{const[l,i]=p.useState(null),c=a=>{n({businessType:a.id,posType:a.posType}),i(null)},d=()=>{if(!r.businessType){i("Please select your business type");return}t()},s=E.find(a=>a.id===r.businessType);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"What type of business do you have?"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"This helps us configure the right POS system for your needs"})]}),l&&e.jsx(v,{color:"failure",children:l}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:E.map(a=>{const u=a.icon,m=r.businessType===a.id;return e.jsxs("div",{onClick:()=>c(a),className:`
                relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                ${m?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"}
                ${a.popular?"ring-2 ring-orange-200 dark:ring-orange-800":""}
              `,children:[a.popular&&e.jsx("div",{className:"absolute -top-2 -right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full",children:"Popular"}),e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:`
                  p-2 rounded-lg
                  ${m?"bg-blue-100 dark:bg-blue-800":"bg-gray-100 dark:bg-gray-800"}
                `,children:e.jsx(u,{className:`
                    w-6 h-6
                    ${m?"text-blue-600 dark:text-blue-400":"text-gray-600 dark:text-gray-400"}
                  `})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:`
                    font-semibold
                    ${m?"text-blue-900 dark:text-blue-100":"text-gray-900 dark:text-white"}
                  `,children:a.name}),e.jsx("p",{className:`
                    text-sm
                    ${m?"text-blue-700 dark:text-blue-300":"text-gray-600 dark:text-gray-400"}
                  `,children:a.description})]})]})]},a.id)})}),s&&e.jsx("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:e.jsxs("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:[e.jsx("strong",{children:"Selected:"})," ",s.name," - We'll configure a ",s.posType," POS system for you."]})}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(g,{color:"gray",onClick:o,children:"Back"}),e.jsx(g,{onClick:d,disabled:!r.businessType,children:"Continue"})]})]})},se=[{id:"google_search",name:"Google Search",description:"Found us through search results",icon:G},{id:"facebook",name:"Facebook",description:"Social media or Facebook ads",icon:C},{id:"friend_referral",name:"Friend/Family Referral",description:"Recommended by someone I know",icon:F},{id:"business_referral",name:"Business Partner Referral",description:"Recommended by another business",icon:F},{id:"online_ad",name:"Online Advertisement",description:"Saw an ad online (not Facebook)",icon:C},{id:"news_article",name:"News/Article",description:"Read about us in news or blog",icon:L},{id:"sales_call",name:"Sales Call/Email",description:"Contacted by our sales team",icon:M},{id:"existing_customer",name:"Existing Customer",description:"Already using AIRyx products",icon:k},{id:"other",name:"Other",description:"Different source",icon:C}],ae=({data:r,updateData:n,onNext:t,onPrev:o})=>{const[l,i]=p.useState(null),c=s=>{n({discoverySource:s.id}),i(null)},d=()=>{if(!r.discoverySource){i("Please select how you found out about us");return}t()};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"How did you find out about AIRyx?"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"This helps us understand how to better serve our customers"})]}),l&&e.jsx(v,{color:"failure",children:l}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:se.map(s=>{const a=s.icon,u=r.discoverySource===s.id;return e.jsx("div",{onClick:()=>c(s),className:`
                p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                ${u?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"}
              `,children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:`
                  p-2 rounded-lg
                  ${u?"bg-blue-100 dark:bg-blue-800":"bg-gray-100 dark:bg-gray-800"}
                `,children:e.jsx(a,{className:`
                    w-5 h-5
                    ${u?"text-blue-600 dark:text-blue-400":"text-gray-600 dark:text-gray-400"}
                  `})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:`
                    font-medium
                    ${u?"text-blue-900 dark:text-blue-100":"text-gray-900 dark:text-white"}
                  `,children:s.name}),e.jsx("p",{className:`
                    text-sm
                    ${u?"text-blue-700 dark:text-blue-300":"text-gray-600 dark:text-gray-400"}
                  `,children:s.description})]})]})},s.id)})}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(g,{color:"gray",onClick:o,children:"Back"}),e.jsx(g,{onClick:d,disabled:!r.discoverySource,children:"Continue"})]})]})},te=({data:r,updateData:n,onNext:t,onPrev:o})=>{const[l,i]=p.useState(null),c=()=>{if(i(null),!r.firstName.trim()){i("First name is required");return}if(!r.lastName.trim()){i("Last name is required");return}if(r.firstName.trim().length<2){i("First name must be at least 2 characters long");return}if(r.lastName.trim().length<2){i("Last name must be at least 2 characters long");return}t()},d=(s,a)=>{n({[s]:a}),l&&i(null)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4",children:e.jsx(A,{className:"w-8 h-8 text-blue-600 dark:text-blue-400"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Tell us about yourself"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"We need your personal details to complete your profile"})]}),l&&e.jsx(v,{color:"failure",children:l}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(b,{htmlFor:"firstName",value:"First Name *"})}),e.jsx(f,{id:"firstName",type:"text",placeholder:"Enter your first name",value:r.firstName,onChange:s=>d("firstName",s.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(b,{htmlFor:"lastName",value:"Last Name *"})}),e.jsx(f,{id:"lastName",type:"text",placeholder:"Enter your last name",value:r.lastName,onChange:s=>d("lastName",s.target.value),required:!0})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(b,{htmlFor:"phoneNumber",value:"Phone Number (Optional)"})}),e.jsx(f,{id:"phoneNumber",type:"tel",placeholder:"e.g., +63 ************",value:r.phoneNumber||"",onChange:s=>d("phoneNumber",s.target.value)}),e.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"We may use this to contact you about your account"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(b,{htmlFor:"address",value:"Business Address (Optional)"})}),e.jsx(f,{id:"address",type:"text",placeholder:"Enter your business address",value:r.address||"",onChange:s=>d("address",s.target.value)}),e.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"This helps us provide location-specific features"})]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(g,{color:"gray",onClick:o,children:"Back"}),e.jsx(g,{onClick:c,disabled:!r.firstName.trim()||!r.lastName.trim(),children:"Continue"})]})]})},ie=r=>({retail_store:"Retail Store",sari_sari_store:"Sari-Sari Store",restaurant:"Restaurant",cafe_coffee_shop:"Café/Coffee Shop",bakery:"Bakery",pharmacy:"Pharmacy",beauty_salon:"Beauty Salon/Barbershop",auto_parts:"Auto Parts Store",hardware:"Hardware Store",grocery:"Grocery Store",fast_food:"Fast Food",other:"Other"})[r]||r,H=r=>({retail:"Retail POS",cafe:"Café POS",restaurant:"Restaurant POS",general:"General POS"})[r]||r,ne=r=>({google_search:"Google Search",facebook:"Facebook",friend_referral:"Friend/Family Referral",business_referral:"Business Partner",online_ad:"Online Advertisement",news_article:"News/Article",sales_call:"Sales Call/Email",existing_customer:"Existing Customer",other:"Other"})[r]||r,oe=({data:r,onComplete:n,onPrev:t,isLoading:o})=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4",children:e.jsx(Y,{className:"w-8 h-8 text-green-600 dark:text-green-400"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Almost done!"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Please review your information before we set up your account"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(R,{className:"w-5 h-5 text-gray-600 dark:text-gray-400"}),e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Organization"})]}),e.jsx("p",{className:"text-gray-700 dark:text-gray-300 ml-8",children:r.organizationName})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(O,{className:"w-5 h-5 text-gray-600 dark:text-gray-400"}),e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Business Type"})]}),e.jsx("p",{className:"text-gray-700 dark:text-gray-300 ml-8",children:ie(r.businessType)}),e.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400 ml-8",children:["We'll set up a ",H(r.posType)," for you"]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(A,{className:"w-5 h-5 text-gray-600 dark:text-gray-400"}),e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Personal Details"})]}),e.jsxs("div",{className:"ml-8 space-y-1",children:[e.jsxs("p",{className:"text-gray-700 dark:text-gray-300",children:[r.firstName," ",r.lastName]}),r.phoneNumber&&e.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Phone: ",r.phoneNumber]}),r.address&&e.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Address: ",r.address]})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white mb-2",children:"How you found us"}),e.jsx("p",{className:"text-gray-700 dark:text-gray-300",children:ne(r.discoverySource)})]})]}),e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4",children:[e.jsx("h3",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:"What happens next?"}),e.jsxs("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[e.jsx("li",{children:"• We'll create your organization and profile"}),e.jsxs("li",{children:["• Your ",H(r.posType)," will be configured"]}),e.jsx("li",{children:"• You'll be redirected to your dashboard"}),e.jsx("li",{children:"• You can start adding products and making sales"})]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(g,{color:"gray",onClick:t,disabled:o,children:"Back"}),e.jsx(g,{onClick:n,disabled:o,children:o?e.jsxs(e.Fragment,{children:[e.jsx(K,{size:"sm",className:"mr-2"}),"Setting up your account..."]}):"Complete Setup"})]})]}),de=()=>{const{user:r}=Q(),n=V(),[t,o]=p.useState(1),[l,i]=p.useState(!1),[c,d]=p.useState({organizationName:"",businessType:"",posType:"",discoverySource:"",firstName:"",lastName:"",phoneNumber:"",address:""}),s=5,a=t/s*100;p.useEffect(()=>{(async()=>{if(r)try{const{data:h,error:S}=await B.from("profiles").select("onboarding_completed").eq("id",r.id).maybeSingle();if(!S&&h&&h.onboarding_completed){console.log("User already completed onboarding, redirecting to dashboard"),n("/");return}if(localStorage.getItem(`onboarding_completed_${r.id}`)){console.log("User completed onboarding in localStorage, redirecting to dashboard"),n("/");return}r.user_metadata&&d(j=>({...j,firstName:r.user_metadata.first_name||"",lastName:r.user_metadata.last_name||"",organizationName:r.user_metadata.organization_name||""}))}catch(h){console.error("Error checking onboarding status:",h)}})()},[r,n]);const u=y=>{d(h=>({...h,...y}))},m=()=>{t<s&&o(t+1)},N=()=>{t>1&&o(t-1)},D=async()=>{if(r){i(!0);try{const{checkUserProfile:y,createUserProfile:h,checkUserOrganizations:S,createUserOrganization:P}=await J(async()=>{const{checkUserProfile:x,createUserProfile:I,checkUserOrganizations:U,createUserOrganization:W}=await import("./userProfile-Crkf42K4.js");return{checkUserProfile:x,createUserProfile:I,checkUserOrganizations:U,createUserOrganization:W}},__vite__mapDeps([0,1,2]));console.log("Checking existing profile and organization for user:",r.id);const j=await y(r.id);console.log("Profile check result:",j);const T=await S(r.id);if(console.log("Organization check result:",T),j.hasProfile)console.log("Profile already exists, skipping creation");else{console.log("Creating profile...");const x=await h(r.id,c.firstName,c.lastName);if(console.log("Profile creation result:",x),!x.success)throw new Error(`Profile creation failed: ${x.error}`)}if(T.hasOrganizations)console.log("Organization already exists, skipping creation");else{console.log("Creating organization...");const x=await P(r.id,c.organizationName);if(console.log("Organization creation result:",x),!x.success)throw new Error(`Organization creation failed: ${x.error}`)}console.log("Marking onboarding as completed for user:",r.id);const{error:z}=await B.rpc("mark_onboarding_completed",{user_id:r.id});z&&console.error("Error marking onboarding as completed:",z),localStorage.setItem(`onboarding_completed_${r.id}`,"true"),localStorage.removeItem(`onboarding_data_${r.id}`),window.location.href="/"}catch(y){console.error("Error completing onboarding:",y),alert(`Setup failed: ${y.message}. Please try again.`)}finally{i(!1)}}},$=()=>{switch(t){case 1:return e.jsx(ee,{data:c,updateData:u,onNext:m});case 2:return e.jsx(re,{data:c,updateData:u,onNext:m,onPrev:N});case 3:return e.jsx(ae,{data:c,updateData:u,onNext:m,onPrev:N});case 4:return e.jsx(te,{data:c,updateData:u,onNext:m,onPrev:N});case 5:return e.jsx(oe,{data:c,onComplete:D,onPrev:N,isLoading:l});default:return null}};return r?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4",children:e.jsxs("div",{className:"w-full max-w-2xl",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Welcome to AIRyx"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Let's set up your business in just a few steps"})]}),e.jsxs(X,{className:"mb-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["Step ",t," of ",s]}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[Math.round(a),"%"]})]}),e.jsx(Z,{progress:a,color:"blue"})]}),$()]}),e.jsx("div",{className:"text-center text-sm text-gray-500 dark:text-gray-400",children:e.jsx("p",{children:"Need help? Contact our support team"})})]})}):null};export{de as default};
