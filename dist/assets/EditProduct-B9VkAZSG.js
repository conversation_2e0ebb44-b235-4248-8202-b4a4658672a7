import{h as F,r as i,j as e,A as w,J as E,a6 as L,a7 as V,i as z,P as X,a8 as T,B as y,a9 as Z,Y as ee,aa as se,L as D,a0 as Y,_ as o,e as B,a2 as te,a3 as re,M as p,ab as ie,d as ne,T as ae,ac as G}from"./index-C6AV3cVN.js";import{C as A}from"./Card-yj7fueH8.js";import{P as le}from"./ProductForm-DjQkFMoJ.js";import{getProductUoms as ce,createProductUom as oe,updateProductUom as de,deleteProductUom as ue}from"./productUom-k6aUg6b7.js";import{a as R,u as he}from"./product-Ca8DWaNR.js";import{u as me}from"./useProductPermissions-DyxXl-nR.js";const q=({productId:a,initialData:r,onSubmit:f,isSubmitting:S,error:j})=>{const{currentOrganization:v}=F(),[U,N]=i.useState([]),[C,_]=i.useState(!1),[b,l]=i.useState(null),[m,g]=i.useState(()=>({product_id:a,uom_id:"",conversion_factor:1,is_default:!1,is_purchasing_unit:!1,is_selling_unit:!1,...r}));i.useEffect(()=>{(async()=>{if(v){_(!0),l(null);try{const{uoms:c,error:d}=await Z(v.id,{isActive:!0});d?l(d):N(c)}catch(c){l(c.message||"Failed to load units of measurement")}finally{_(!1)}}})()},[v]),i.useEffect(()=>{r&&g(t=>({...t,...r}))},[r]);const P=t=>{const{name:c,value:d,type:k}=t.target;g(k==="number"?u=>({...u,[c]:d===""?"":parseFloat(d)}):u=>({...u,[c]:d}))},h=t=>c=>{g(d=>({...d,[t]:c}))},n=async t=>{t.preventDefault(),await f(m)};return e.jsxs("form",{onSubmit:n,className:"space-y-6",children:[j&&e.jsx(w,{color:"failure",icon:E,children:j}),b&&e.jsx(w,{color:"failure",icon:E,children:b}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(L,{htmlFor:"uom_id",value:"Unit of Measurement *"})}),e.jsxs(V,{id:"uom_id",name:"uom_id",value:m.uom_id||"",onChange:P,required:!0,disabled:C||!!(r!=null&&r.id),children:[e.jsx("option",{value:"",children:"Select a unit of measurement"}),U.map(t=>e.jsxs("option",{value:t.id,children:[t.name," (",t.code,")"]},t.id))]}),C&&e.jsxs("div",{className:"mt-2 flex items-center",children:[e.jsx(z,{size:"sm",className:"mr-2"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Loading units of measurement..."})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(L,{htmlFor:"conversion_factor",value:"Conversion Factor *"})}),e.jsx(X,{id:"conversion_factor",name:"conversion_factor",type:"number",step:"0.00001",min:"0.00001",value:m.conversion_factor||"",onChange:P,required:!0}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Multiply by this factor to convert to the base unit (e.g., 12 for a dozen)"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(T,{checked:m.is_default||!1,onChange:h("is_default"),label:"Default Unit"}),e.jsx("p",{className:"text-sm text-gray-500",children:"This is the primary unit for this product"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(T,{checked:m.is_purchasing_unit||!1,onChange:h("is_purchasing_unit"),label:"Purchasing Unit"}),e.jsx("p",{className:"text-sm text-gray-500",children:"This unit can be used for purchasing"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(T,{checked:m.is_selling_unit||!1,onChange:h("is_selling_unit"),label:"Selling Unit"}),e.jsx("p",{className:"text-sm text-gray-500",children:"This unit can be used for selling"})]})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(y,{type:"submit",color:"primary",disabled:S,children:S?e.jsxs(e.Fragment,{children:[e.jsx(z,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Unit"})})]})},xe=({productId:a,productName:r})=>e.jsx(A,{className:"mb-4",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx(ee,{className:"mr-3 h-6 w-6 text-blue-500 flex-shrink-0 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Units of Measurement Guide"}),e.jsxs("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:["Units of Measurement (UoM) allow you to track inventory in different units (e.g., pieces, boxes, kilograms).",r&&` You need to set up UoMs for ${r} before you can use it in transactions.`]})]})]}),e.jsxs(w,{color:"info",icon:se,children:[e.jsx("div",{className:"font-medium",children:"Why set up Units of Measurement?"}),e.jsxs("ul",{className:"mt-1.5 list-disc list-inside text-sm",children:[e.jsx("li",{children:"Purchase products in one unit (e.g., boxes) and sell in another (e.g., pieces)"}),e.jsx("li",{children:"Track inventory in multiple units with automatic conversion"}),e.jsx("li",{children:"Generate reports with quantities in your preferred units"})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg",children:[e.jsx("h4",{className:"font-medium mb-2 text-gray-900 dark:text-white",children:"How to add Units of Measurement:"}),e.jsxs("ol",{className:"list-decimal list-inside text-sm text-gray-500 dark:text-gray-400 space-y-2",children:[e.jsxs("li",{children:["Click the ",e.jsx("strong",{children:'"Add Unit"'})," button above"]}),e.jsx("li",{children:"Select a unit from the dropdown (e.g., Pieces, Box, Kilogram)"}),e.jsxs("li",{children:["Set the ",e.jsx("strong",{children:"conversion factor"})," (e.g., 1 box = 12 pieces, so conversion factor is 12)"]}),e.jsxs("li",{children:["Check the appropriate options:",e.jsxs("ul",{className:"list-disc list-inside ml-5 mt-1",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Default Unit:"})," The primary unit for this product"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Purchasing Unit:"})," Can be used when purchasing this product"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Selling Unit:"})," Can be used when selling this product"]})]})]}),e.jsxs("li",{children:["Click ",e.jsx("strong",{children:'"Save Unit"'})," to add it"]})]})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(D,{to:"/settings/uom",children:e.jsx(y,{color:"light",size:"sm",children:"Manage Global Units"})}),e.jsx(D,{to:`/products/${a}`,children:e.jsx(y,{color:"light",size:"sm",children:"Back to Product Details"})})]})]})}),fe=({productId:a})=>{const{currentOrganization:r}=F(),[f,S]=i.useState([]),[j,v]=i.useState(!0),[U,N]=i.useState(null),[C,_]=i.useState(""),[b,l]=i.useState(!1),[m,g]=i.useState(!1),[P,h]=i.useState(!1),[n,t]=i.useState(null),[c,d]=i.useState(!1),[k,u]=i.useState(null),H=async()=>{if(!(!a||!r)){v(!0),N(null);try{const{productUoms:s,error:x}=await ce(a,r.id);x?N(x):S(s)}catch(s){N(s.message||"An error occurred while fetching product UoMs")}finally{v(!1)}}},$=async()=>{if(!(!a||!r))try{const{product:s}=await R(r.id,a);s&&_(s.name)}catch(s){console.error("Error fetching product details:",s)}};i.useEffect(()=>{H(),$()},[a,r]);const O=()=>{t(null),u(null),l(!0)},J=s=>{t(s),u(null),g(!0)},K=s=>{t(s),h(!0)},W=async s=>{if(!(!a||!r)){d(!0),u(null);try{const{productUom:x,error:M}=await oe({product_id:a,uom_id:s.uom_id,conversion_factor:s.conversion_factor,is_default:s.is_default||!1,is_purchasing_unit:s.is_purchasing_unit||!1,is_selling_unit:s.is_selling_unit||!1},r.id);M?u(M):(l(!1),H())}catch(x){u(x.message||"An error occurred while creating the product UoM")}finally{d(!1)}}},I=async s=>{if(!(!n||!r)){d(!0),u(null);try{const{productUom:x,error:M}=await de(n.id,{conversion_factor:s.conversion_factor,is_default:s.is_default,is_purchasing_unit:s.is_purchasing_unit,is_selling_unit:s.is_selling_unit},r.id);M?u(M):(g(!1),H())}catch(x){u(x.message||"An error occurred while updating the product UoM")}finally{d(!1)}}},Q=async()=>{if(!(!n||!r)){d(!0),u(null);try{const{success:s,error:x}=await ue(n.id,r.id);x?u(x):s&&(h(!1),t(null),H())}catch(s){u(s.message||"An error occurred while deleting the product UoM")}finally{d(!1)}}};return e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Units of Measurement"}),e.jsxs(y,{color:"primary",size:"sm",onClick:O,children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Add Unit"]})]}),e.jsx(xe,{productId:a,productName:C}),U&&e.jsx(w,{color:"failure",icon:E,className:"mb-4",children:U}),j?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(z,{size:"xl"})}):f.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No units of measurement defined for this product"}),e.jsxs(y,{color:"primary",size:"sm",onClick:O,children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Add Your First Unit"]})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(o,{hoverable:!0,children:[e.jsxs(o.Head,{children:[e.jsx(o.HeadCell,{children:"Unit"}),e.jsx(o.HeadCell,{children:"Conversion Factor"}),e.jsx(o.HeadCell,{children:"Default"}),e.jsx(o.HeadCell,{children:"Purchasing"}),e.jsx(o.HeadCell,{children:"Selling"}),e.jsx(o.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(o.Body,{className:"divide-y",children:f.map(s=>e.jsxs(o.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsxs(o.Cell,{className:"font-medium",children:[s.uom.name," (",s.uom.code,")"]}),e.jsx(o.Cell,{children:s.conversion_factor}),e.jsx(o.Cell,{children:s.is_default?e.jsx(B,{color:"success",children:"Default"}):e.jsx("span",{className:"text-gray-400",children:"No"})}),e.jsx(o.Cell,{children:s.is_purchasing_unit?e.jsx(B,{color:"info",children:"Yes"}):e.jsx("span",{className:"text-gray-400",children:"No"})}),e.jsx(o.Cell,{children:s.is_selling_unit?e.jsx(B,{color:"info",children:"Yes"}):e.jsx("span",{className:"text-gray-400",children:"No"})}),e.jsx(o.Cell,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(y,{color:"light",size:"xs",onClick:()=>J(s),children:e.jsx(te,{className:"h-4 w-4"})}),e.jsx(y,{color:"failure",size:"xs",onClick:()=>K(s),children:e.jsx(re,{className:"h-4 w-4"})})]})})]},s.id))})]})}),e.jsxs(p,{show:b,onClose:()=>l(!1),children:[e.jsx(p.Header,{children:"Add Unit of Measurement"}),e.jsx(p.Body,{children:e.jsx(q,{productId:a,onSubmit:W,isSubmitting:c,error:k||void 0})})]}),e.jsxs(p,{show:m,onClose:()=>g(!1),children:[e.jsx(p.Header,{children:"Edit Unit of Measurement"}),e.jsx(p.Body,{children:e.jsx(q,{productId:a,initialData:n||void 0,onSubmit:I,isSubmitting:c,error:k||void 0})})]}),e.jsxs(p,{show:P,onClose:()=>h(!1),children:[e.jsx(p.Header,{children:"Confirm Deletion"}),e.jsx(p.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(E,{className:"mx-auto mb-4 h-14 w-14 text-red-500"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500",children:["Are you sure you want to delete ",n==null?void 0:n.uom.name," (",n==null?void 0:n.uom.code,")?"]}),k&&e.jsx(w,{color:"failure",className:"mb-4",children:k}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(y,{color:"failure",onClick:Q,disabled:c,children:c?e.jsx(z,{size:"sm"}):"Yes, delete"}),e.jsx(y,{color:"gray",onClick:()=>h(!1),children:"No, cancel"})]})]})})]})]})},be=()=>{const{id:a}=ie(),r=ne(),{currentOrganization:f}=F(),{canUpdateProducts:S}=me(),[j,v]=i.useState(null),[U,N]=i.useState(!0),[C,_]=i.useState(!1),[b,l]=i.useState(null),[m,g]=i.useState("basic");i.useEffect(()=>{(async()=>{if(!(!f||!a)){N(!0),l(null);try{const{product:n,error:t}=await R(f.id,a);t?l(t):n?v(n):l("Product not found")}catch(n){l(n.message||"An error occurred while fetching the product")}finally{N(!1)}}})()},[f,a]);const P=async h=>{if(!f||!a){l("No organization selected or product ID missing");return}_(!0),l(null);const n={...h};"category"in n&&delete n.category;try{const{product:t,error:c}=await he(f.id,a,n);c?l(c):t&&r("/products")}catch(t){l(t.message||"An error occurred while updating the product")}finally{_(!1)}};return S?U?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(A,{children:e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(z,{size:"xl"})})})}):b||!j?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(A,{children:e.jsxs(w,{color:"failure",icon:E,children:[e.jsx("h3",{className:"text-lg font-medium",children:"Error"}),e.jsx("p",{children:b||"Product not found"})]})})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(A,{children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsxs("h1",{className:"text-2xl font-bold",children:["Edit Product: ",j.name]})}),e.jsxs(ae,{"aria-label":"Product tabs",variant:"underline",onActiveTabChange:h=>g(String(h)),children:[e.jsx(G,{title:"Basic Information",active:m==="0",children:e.jsx(le,{initialData:j,onSubmit:P,isSubmitting:C,error:b||void 0})}),e.jsx(G,{title:"Units of Measurement",active:m==="1",children:j&&e.jsx(fe,{productId:j.id})})]})]})}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(A,{children:e.jsxs(w,{color:"failure",icon:E,children:[e.jsx("h3",{className:"text-lg font-medium",children:"Permission Denied"}),e.jsx("p",{children:"You don't have permission to edit products. Only owners, admins, and inventory managers can edit products."})]})})})};export{be as default};
