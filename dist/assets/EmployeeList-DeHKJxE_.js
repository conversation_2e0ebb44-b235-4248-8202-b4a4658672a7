import{h as ye,r as c,by as Be,j as e,A as ne,J as oe,a6 as y,ae as ze,P as N,a7 as H,Y as Ye,a8 as Ve,B as I,i as le,s as O,b as qe,Z as Ge,X as Je,a0 as ue,Q as Qe,_ as j,k as pe,bz as he,L as ge,D as se,a1 as Xe,a2 as Ze,a3 as Ke,a4 as We,M as T,e as J}from"./index-C6AV3cVN.js";import{C as De}from"./Card-yj7fueH8.js";import{c as fe,g as es,a as ss,u as rs,d as ts}from"./employee-DWC25S7P.js";import{g as _e}from"./department-NDftHEGx.js";import{g as je}from"./jobPosition-DFMtfNvL.js";import{g as be}from"./employmentType-B4DW1Mne.js";import{P as ns}from"./Pagination-CVEzfctr.js";import{g as os}from"./excelExport-BekG2cQR.js";import{G as ls}from"./GenericImport-Bi3EwZHp.js";import{p as ve}from"./csvParser-Ls709fVB.js";const xe=({initialData:s,onSubmit:n,isSubmitting:g,error:h,onCancel:l})=>{const{currentOrganization:a}=ye(),[m,x]=c.useState(()=>({first_name:(s==null?void 0:s.first_name)||"",middle_name:(s==null?void 0:s.middle_name)||"",last_name:(s==null?void 0:s.last_name)||"",email:(s==null?void 0:s.email)||"",phone:(s==null?void 0:s.phone)||"",employee_number:(s==null?void 0:s.employee_number)||"",department_id:(s==null?void 0:s.department_id)||"",position_id:(s==null?void 0:s.position_id)||"",employment_type_id:(s==null?void 0:s.employment_type_id)||"",status:(s==null?void 0:s.status)||"active",is_active:(s==null?void 0:s.is_active)!==!1,user_id:(s==null?void 0:s.user_id)||null,date_of_birth:(s==null?void 0:s.date_of_birth)||null,gender:(s==null?void 0:s.gender)||null,marital_status:(s==null?void 0:s.marital_status)||null,nationality:(s==null?void 0:s.nationality)||null,address:(s==null?void 0:s.address)||null,city:(s==null?void 0:s.city)||null,state:(s==null?void 0:s.state)||null,postal_code:(s==null?void 0:s.postal_code)||null,country:(s==null?void 0:s.country)||null,emergency_contact_name:(s==null?void 0:s.emergency_contact_name)||null,emergency_contact_phone:(s==null?void 0:s.emergency_contact_phone)||null,emergency_contact_relationship:(s==null?void 0:s.emergency_contact_relationship)||null,hire_date:(s==null?void 0:s.hire_date)||null,end_date:(s==null?void 0:s.end_date)||null,notes:(s==null?void 0:s.notes)||null,profile_image_url:(s==null?void 0:s.profile_image_url)||null})),[b,Q]=c.useState(()=>{const t={sss_number:"",philhealth_number:"",pagibig_number:"",tin_number:""};if(Array.isArray(s==null?void 0:s.government_ids)&&(s==null?void 0:s.government_ids.length)>0){const d=s.government_ids[0];d.sss_number&&(t.sss_number=d.sss_number),d.philhealth_number&&(t.philhealth_number=d.philhealth_number),d.pagibig_number&&(t.pagibig_number=d.pagibig_number),d.tin_number&&(t.tin_number=d.tin_number)}return t}),[z,w]=c.useState([]),[U,P]=c.useState([]),[$,M]=c.useState([]),[o,E]=c.useState([]),[C,A]=c.useState(!1);c.useEffect(()=>{a&&F()},[a]);const F=async()=>{if(a){A(!0);try{const{departments:t}=await _e(a.id);w(t);const{positions:d}=await je(a.id);P(d);const{employmentTypes:u}=await be(a.id);M(u);const{members:p,error:q}=await Be(a.id);!q&&p&&E(p)}catch(t){console.error("Error fetching reference data:",t)}finally{A(!1)}}},f=t=>{const{name:d,value:u}=t.target;["department_id","position_id","employment_type_id"].includes(d)?x(p=>({...p,[d]:u===""?null:u})):x(p=>({...p,[d]:u}))},v=t=>{const{name:d,value:u}=t.target;Q(p=>({...p,[d]:u}))},X=t=>{x(d=>({...d,is_active:t}))},[B,ce]=c.useState(null),[Y,Z]=c.useState(null),re=t=>{t.target.files&&t.target.files.length>0&&ce(t.target.files[0])},V=async()=>{if(!B||!a)return null;try{Z(null);const t=B.name.split(".").pop(),d=`${Date.now()}-${Math.random().toString(36).substring(2,15)}.${t}`,u=`${a.id}/employee-images/${d}`,{error:p}=await O.storage.from("employee-images").upload(u,B);if(p)return Z(p.message),null;const{data:q}=await O.storage.from("employee-images").getPublicUrl(u);return q.publicUrl}catch(t){return Z(t.message),null}},S=async t=>{t.preventDefault();const d={...m};if(["department_id","position_id","employment_type_id","user_id"].forEach(p=>{d[p]===""&&(d[p]=null)}),B&&!d.user_id){const p=await V();p&&(d.profile_image_url=p)}const u={};u.sss_number=b.sss_number||null,u.philhealth_number=b.philhealth_number||null,u.pagibig_number=b.pagibig_number||null,u.tin_number=b.tin_number||null,await n(d,u)};return e.jsxs("form",{onSubmit:S,className:"space-y-6",children:[h&&e.jsx(ne,{color:"failure",icon:oe,children:h}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Basic Information"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"profileImage",value:"Profile Image"})}),m.user_id?e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"This employee is linked to a user account. The profile image will be automatically used from the user account."}),m.profile_image_url&&e.jsx("div",{className:"mt-2",children:e.jsx("div",{className:"w-20 h-20 overflow-hidden rounded-full border border-gray-200",children:e.jsx("img",{src:m.profile_image_url,alt:"Profile",className:"w-full h-full object-cover"})})})]}):e.jsxs(e.Fragment,{children:[e.jsx(ze,{id:"profileImage",onChange:re,helperText:"Upload a profile image (optional)"}),m.profile_image_url&&e.jsxs("div",{className:"mt-2",children:[e.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Current Image:"}),e.jsx("div",{className:"w-20 h-20 overflow-hidden rounded-full border border-gray-200",children:e.jsx("img",{src:m.profile_image_url,alt:"Profile",className:"w-full h-full object-cover"})})]}),Y&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:Y})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"first_name",value:"First Name *"})}),e.jsx(N,{id:"first_name",name:"first_name",value:m.first_name||"",onChange:f,required:!0})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"middle_name",value:"Middle Name"})}),e.jsx(N,{id:"middle_name",name:"middle_name",value:m.middle_name||"",onChange:f})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"last_name",value:"Last Name *"})}),e.jsx(N,{id:"last_name",name:"last_name",value:m.last_name||"",onChange:f,required:!0})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"email",value:"Email"})}),e.jsx(N,{id:"email",name:"email",type:"email",value:m.email||"",onChange:f})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"phone",value:"Phone"})}),e.jsx(N,{id:"phone",name:"phone",value:m.phone||"",onChange:f})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Employment Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"employee_number",value:"Employee Number"})}),e.jsx(N,{id:"employee_number",name:"employee_number",value:m.employee_number||"",onChange:f})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"status",value:"Status"})}),e.jsxs(H,{id:"status",name:"status",value:m.status||"active",onChange:f,children:[e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"on_leave",children:"On Leave"}),e.jsx("option",{value:"terminated",children:"Terminated"}),e.jsx("option",{value:"resigned",children:"Resigned"}),e.jsx("option",{value:"retired",children:"Retired"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"department_id",value:"Department"})}),e.jsxs(H,{id:"department_id",name:"department_id",value:m.department_id||"",onChange:f,children:[e.jsx("option",{value:"",children:"Select Department"}),z.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"position_id",value:"Position"})}),e.jsxs(H,{id:"position_id",name:"position_id",value:m.position_id||"",onChange:f,children:[e.jsx("option",{value:"",children:"Select Position"}),U.map(t=>e.jsx("option",{value:t.id,children:t.title},t.id))]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"employment_type_id",value:"Employment Type"})}),e.jsxs(H,{id:"employment_type_id",name:"employment_type_id",value:m.employment_type_id||"",onChange:f,children:[e.jsx("option",{value:"",children:"Select Employment Type"}),$.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsxs(y,{htmlFor:"user_id",value:"Link to User Account",className:"flex items-center",children:[e.jsx("span",{children:"Link to User Account"}),e.jsx(Ye,{className:"ml-1 text-gray-500",title:"Link this employee to a user account if they need access to the system"})]})}),e.jsxs(H,{id:"user_id",name:"user_id",value:m.user_id||"",onChange:f,children:[e.jsx("option",{value:"",children:"Not linked to any user"}),o.map(t=>e.jsxs("option",{value:t.id,children:[t.email," (",t.first_name," ",t.last_name,")"]},t.id))]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Link this employee to a user account if they need access to the system"})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(Ve,{checked:m.is_active!==!1,onChange:X,label:"Active Employee"})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Government IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"sss_number",value:"SSS Number"})}),e.jsx(N,{id:"sss_number",name:"sss_number",value:b.sss_number||"",onChange:v})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"philhealth_number",value:"PhilHealth Number"})}),e.jsx(N,{id:"philhealth_number",name:"philhealth_number",value:b.philhealth_number||"",onChange:v})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"pagibig_number",value:"Pag-IBIG Number"})}),e.jsx(N,{id:"pagibig_number",name:"pagibig_number",value:b.pagibig_number||"",onChange:v})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(y,{htmlFor:"tin_number",value:"TIN Number"})}),e.jsx(N,{id:"tin_number",name:"tin_number",value:b.tin_number||"",onChange:v})]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[l&&e.jsx(I,{color:"gray",onClick:l,children:"Cancel"}),e.jsx(I,{type:"submit",color:"primary",disabled:g,children:g?e.jsxs(e.Fragment,{children:[e.jsx(le,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Employee"})]})]})},Ne=[{field:"first_name",required:!0,type:"string",customValidator:s=>typeof s=="string"&&s.length>100?"First name must be 100 characters or less":null},{field:"last_name",required:!0,type:"string",customValidator:s=>typeof s=="string"&&s.length>100?"Last name must be 100 characters or less":null},{field:"email",type:"email"},{field:"date_of_birth",type:"string",customValidator:s=>{if(s&&typeof s=="string"){const n=new Date(s);if(isNaN(n.getTime()))return"Date of birth must be a valid date (YYYY-MM-DD format)"}return null}},{field:"hire_date",type:"string",customValidator:s=>{if(s&&typeof s=="string"){const n=new Date(s);if(isNaN(n.getTime()))return"Hire date must be a valid date (YYYY-MM-DD format)"}return null}},{field:"gender",type:"string",customValidator:s=>{if(s&&typeof s=="string"){const n=["male","female","other","prefer_not_to_say"];if(!n.includes(s.toLowerCase()))return`Gender must be one of: ${n.join(", ")}`}return null}},{field:"marital_status",type:"string",customValidator:s=>{if(s&&typeof s=="string"){const n=["single","married","divorced","widowed","separated"];if(!n.includes(s.toLowerCase()))return`Marital status must be one of: ${n.join(", ")}`}return null}},{field:"status",type:"string",customValidator:s=>{if(s&&typeof s=="string"){const n=["active","on_leave","terminated","resigned","retired"];if(!n.includes(s.toLowerCase()))return`Status must be one of: ${n.join(", ")}`}return null}}],cs=["employee_number","first_name","middle_name","last_name","email","phone","date_of_birth","gender","marital_status","nationality","address","city","state","postal_code","country","emergency_contact_name","emergency_contact_phone","emergency_contact_relationship","department","position","employment_type","hire_date","status","notes"],as=s=>{const n=ve(s,Ne);return{isValid:n.success,headers:n.headers,sampleData:n.data.slice(0,5),errors:n.errors,warnings:n.warnings,totalRows:n.data.length}},ms=async(s,n,g)=>{if(!n||n.trim()==="")return null;const h=n.trim();try{const{data:l,error:a}=await O.from("departments").select("id").eq("organization_id",s).eq("name",h).maybeSingle();if(a)return console.error("Error finding department:",a),null;if(l)return l.id;const{data:m,error:x}=await O.from("departments").insert({organization_id:s,name:h,description:"Auto-created during employee import"}).select("id").single();return x?(console.error("Error creating department:",x),null):m.id}catch(l){return console.error("Unexpected error in getOrCreateDepartment:",l),null}},ds=async(s,n,g)=>{if(!n||n.trim()==="")return null;const h=n.trim();try{const{data:l,error:a}=await O.from("job_positions").select("id").eq("organization_id",s).eq("title",h).maybeSingle();if(a)return console.error("Error finding job position:",a),null;if(l)return l.id;const{data:m,error:x}=await O.from("job_positions").insert({organization_id:s,title:h,description:"Auto-created during employee import"}).select("id").single();return x?(console.error("Error creating job position:",x),null):m.id}catch(l){return console.error("Unexpected error in getOrCreateJobPosition:",l),null}},is=async(s,n,g)=>{if(!n||n.trim()==="")return null;const h=n.trim();try{const{data:l,error:a}=await O.from("employment_types").select("id").eq("organization_id",s).eq("name",h).maybeSingle();if(a)return console.error("Error finding employment type:",a),null;if(l)return l.id;const{data:m,error:x}=await O.from("employment_types").insert({organization_id:s,name:h,description:"Auto-created during employee import"}).select("id").single();return x?(console.error("Error creating employment type:",x),null):m.id}catch(l){return console.error("Unexpected error in getOrCreateEmploymentType:",l),null}},us=async(s,n)=>{const g=n.filter(l=>l.employee_number).map(l=>l.employee_number),h=[];if(g.length>0){const{data:l}=await O.from("employees").select("employee_number").eq("organization_id",s).in("employee_number",g);l&&h.push(...l.map(a=>a.employee_number).filter(Boolean))}return h},ps=async(s,n,g,h=!0)=>{var U,P,$;const l=ve(n,Ne);if(!l.success)return{success:!1,totalRows:l.data.length,successCount:0,errorCount:l.data.length,errors:l.errors,warnings:l.warnings,createdEmployees:[],createdItems:[]};const a=l.data,m=[...l.errors],x=[...l.warnings],b=[],Q=await us(s,a);let z=0,w=0;for(let M=0;M<a.length;M++){const o=a[M],E=M+2;try{if(o.employee_number&&Q.includes(o.employee_number))if(h){x.push(`Row ${E}: Skipped - Employee number '${o.employee_number}' already exists`);continue}else{m.push(`Row ${E}: Employee number '${o.employee_number}' already exists`),w++;continue}let C=null;o.department&&o.department.trim()&&(C=await ms(s,o.department,g));let A=null;o.position&&o.position.trim()&&(A=await ds(s,o.position,g));let F=null;o.employment_type&&o.employment_type.trim()&&(F=await is(s,o.employment_type,g));const f={employee_number:o.employee_number||null,first_name:o.first_name,middle_name:o.middle_name||null,last_name:o.last_name,email:o.email||null,phone:o.phone||null,date_of_birth:o.date_of_birth||null,gender:((U=o.gender)==null?void 0:U.toLowerCase())||null,marital_status:((P=o.marital_status)==null?void 0:P.toLowerCase())||null,nationality:o.nationality||null,address:o.address||null,city:o.city||null,state:o.state||null,postal_code:o.postal_code||null,country:o.country||null,emergency_contact_name:o.emergency_contact_name||null,emergency_contact_phone:o.emergency_contact_phone||null,emergency_contact_relationship:o.emergency_contact_relationship||null,department_id:C,position_id:A,employment_type_id:F,hire_date:o.hire_date||null,status:(($=o.status)==null?void 0:$.toLowerCase())||"active",is_active:!0,notes:o.notes||null},v=await fe(s,f);if(v.employee&&!v.error)b.push(v.employee),z++;else{const X=v.error||"Failed to create employee";m.push(`Row ${E}: ${X}`),w++}}catch(C){const A=C instanceof Error?C.message:"Unknown error";m.push(`Row ${E}: ${A}`),w++}}return{success:w===0,totalRows:a.length,successCount:z,errorCount:w,errors:m,warnings:x,createdEmployees:b,createdItems:b}},hs=()=>{const s=cs,n=['"EMP001","John","M","Doe","<EMAIL>","555-0101","1990-01-15","male","single","American","123 Main St","New York","NY","10001","USA","Jane Doe","555-0102","spouse","Sales","Sales Manager","Full-time","2023-01-15","active","Experienced sales professional"','"EMP002","Jane","","Smith","<EMAIL>","555-0103","1985-05-20","female","married","Canadian","456 Oak Ave","Los Angeles","CA","90210","USA","Bob Smith","555-0104","spouse","Marketing","Marketing Specialist","Full-time","2023-02-01","active","Creative marketing expert"','"","Bob","R","Johnson","","555-0105","1992-08-10","male","single","British","789 Pine Rd","Chicago","IL","60601","USA","Mary Johnson","555-0106","mother","IT","Developer","Contract","2023-03-01","active","Skilled software developer"'],g=[s.map(m=>`"${m}"`).join(","),...n].join(`
`),h=new Blob([g],{type:"text/csv;charset=utf-8;"}),l=URL.createObjectURL(h),a=document.createElement("a");a.setAttribute("href",l),a.setAttribute("download","employee_import_template.csv"),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(l)},Es=()=>{const{currentOrganization:s}=ye(),{user:n}=qe(),[g,h]=c.useState([]),[l,a]=c.useState([]),[m,x]=c.useState([]),[b,Q]=c.useState([]),[z,w]=c.useState(!0),[U,P]=c.useState(null),[$,M]=c.useState(0),[o,E]=c.useState(1),[C,A]=c.useState(""),[F,f]=c.useState(""),[v,X]=c.useState(""),[B,ce]=c.useState(""),[Y,Z]=c.useState(""),[re,V]=c.useState(!1),[S,t]=c.useState(null),[d,u]=c.useState(null),[p,q]=c.useState(!1),[we,W]=c.useState(!1),[Ee,D]=c.useState(!1),[k,Ce]=c.useState(null),[ae,ee]=c.useState(!1),[me,L]=c.useState(null),[Se,te]=c.useState(!1),[K,ke]=c.useState(10);c.useEffect(()=>{s&&(G(),Ie(),Pe(),Ae())},[s,o,C,F,v,B,Y,K]);const G=async()=>{if(s){w(!0),P(null);try{const{employees:r,count:i,error:_}=await es(s.id,{searchQuery:C,departmentId:F||void 0,positionId:v||void 0,employmentTypeId:B||void 0,status:Y||void 0,limit:K,offset:(o-1)*K});_?P(_):(h(r),M(i))}catch(r){P(r.message||"An error occurred while fetching employees")}finally{w(!1)}}},Ie=async()=>{if(s)try{const{departments:r,error:i}=await _e(s.id);i||a(r)}catch(r){console.error("Error fetching departments:",r)}},Pe=async()=>{if(s)try{const{positions:r,error:i}=await je(s.id);i||x(r)}catch(r){console.error("Error fetching positions:",r)}},Ae=async()=>{if(s)try{const{employmentTypes:r,error:i}=await be(s.id);i||Q(r)}catch(r){console.error("Error fetching employment types:",r)}},Te=r=>{r.preventDefault(),E(1),G()},de=()=>{L(null),W(!0)},Oe=async r=>{if(s){L(null);try{const{employee:i,error:_}=await ss(s.id,r.id);_?console.error("Error fetching employee details:",_):i&&(Ce(i),D(!0))}catch(i){console.error("Error fetching employee details:",i)}}},Me=r=>{t(r),u(null),V(!0)},Fe=async(r,i)=>{if(s){ee(!0),L(null);try{const{employee:_,error:R}=await fe(s.id,r,i);R?L(R):(W(!1),G())}catch(_){L(_.message||"An error occurred while creating the employee")}finally{ee(!1)}}},Le=async(r,i)=>{if(!(!s||!k)){ee(!0),L(null);try{const{employee:_,error:R}=await rs(s.id,k.id,r,i);R?L(R):(D(!1),G())}catch(_){L(_.message||"An error occurred while updating the employee")}finally{ee(!1)}}},Re=async()=>{if(!(!s||!S)){q(!0),u(null);try{const{success:r,error:i}=await ts(s.id,S.id);i?u(i):r&&(V(!1),G())}catch(r){u(r.message||"An error occurred while deleting the employee")}finally{q(!1)}}},He=()=>{if(g.length===0){P("No employees to export");return}os(g)},Ue=r=>{r.successCount>0&&G(),te(!1)},$e=r=>{if(!r)return null;switch(r){case"active":return e.jsx(J,{color:"success",children:"Active"});case"on_leave":return e.jsx(J,{color:"warning",children:"On Leave"});case"terminated":return e.jsx(J,{color:"failure",children:"Terminated"});case"resigned":return e.jsx(J,{color:"gray",children:"Resigned"});case"retired":return e.jsx(J,{color:"purple",children:"Retired"});default:return e.jsx(J,{color:"gray",children:r})}};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(De,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Employees"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(I,{color:"light",onClick:He,children:[e.jsx(Ge,{className:"mr-2 h-4 w-4"}),"Export"]}),e.jsxs(I,{color:"light",onClick:()=>te(!0),children:[e.jsx(Je,{className:"mr-2 h-4 w-4"}),"Import"]}),e.jsxs(I,{color:"primary",onClick:de,children:[e.jsx(ue,{className:"mr-2 h-5 w-5"}),"Add Employee"]})]})]}),U&&e.jsxs(ne,{color:"failure",className:"mb-4",children:[e.jsx(oe,{className:"h-4 w-4 mr-2"}),U]}),e.jsxs("div",{className:"mb-4 grid grid-cols-1 md:grid-cols-5 gap-4",children:[e.jsx("form",{onSubmit:Te,className:"md:col-span-2",children:e.jsx(N,{type:"text",placeholder:"Search employees...",value:C,onChange:r=>A(r.target.value),icon:Qe,rightIcon:()=>e.jsx(I,{type:"submit",size:"xs",color:"light",children:"Search"})})}),e.jsxs(H,{value:F,onChange:r=>f(r.target.value),className:"md:col-span-1",children:[e.jsx("option",{value:"",children:"All Departments"}),l.map(r=>e.jsx("option",{value:r.id,children:r.name},r.id))]}),e.jsxs(H,{value:v,onChange:r=>X(r.target.value),className:"md:col-span-1",children:[e.jsx("option",{value:"",children:"All Positions"}),m.map(r=>e.jsx("option",{value:r.id,children:r.title},r.id))]}),e.jsxs(H,{value:Y,onChange:r=>Z(r.target.value),className:"md:col-span-1",children:[e.jsx("option",{value:"",children:"All Statuses"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"on_leave",children:"On Leave"}),e.jsx("option",{value:"terminated",children:"Terminated"}),e.jsx("option",{value:"resigned",children:"Resigned"}),e.jsx("option",{value:"retired",children:"Retired"})]})]}),z?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(le,{size:"xl"})}):g.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No employees found"}),e.jsxs(I,{color:"primary",size:"sm",onClick:de,children:[e.jsx(ue,{className:"mr-2 h-4 w-4"}),"Add Your First Employee"]})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(j,{hoverable:!0,children:[e.jsxs(j.Head,{children:[e.jsx(j.HeadCell,{children:"Employee"}),e.jsx(j.HeadCell,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(pe,{className:"mr-2 h-4 w-4 text-gray-500"}),"Department"]})}),e.jsx(j.HeadCell,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(he,{className:"mr-2 h-4 w-4 text-gray-500"}),"Position"]})}),e.jsx(j.HeadCell,{children:"Status"}),e.jsx(j.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(j.Body,{children:g.map(r=>{var i,_,R,ie;return e.jsxs(j.Row,{children:[e.jsx(j.Cell,{children:e.jsxs("div",{className:"flex items-center",children:[r.profile_image_url?e.jsx("div",{className:"w-10 h-10 overflow-hidden rounded-full border border-gray-200 mr-3",children:e.jsx("img",{src:r.profile_image_url,alt:`${r.first_name} ${r.last_name}`,className:"w-full h-full object-cover"})}):e.jsx("div",{className:"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3",children:e.jsxs("span",{className:"text-gray-500 font-semibold",children:[(i=r.first_name)==null?void 0:i[0],(_=r.last_name)==null?void 0:_[0]]})}),e.jsxs("div",{children:[e.jsxs(ge,{to:`/employees/details/${r.id}`,className:"font-medium hover:text-primary hover:underline",children:[r.first_name," ",r.last_name]}),e.jsx("p",{className:"text-xs text-gray-500",children:r.email||"No email"})]})]})}),e.jsx(j.Cell,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(pe,{className:"mr-2 h-4 w-4 text-gray-500"}),((R=r.department)==null?void 0:R.name)||"Not assigned"]})}),e.jsx(j.Cell,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(he,{className:"mr-2 h-4 w-4 text-gray-500"}),((ie=r.position)==null?void 0:ie.title)||"Not assigned"]})}),e.jsx(j.Cell,{children:$e(r.status)}),e.jsx(j.Cell,{children:e.jsx("div",{className:"flex justify-end",children:e.jsxs(se,{label:"",dismissOnClick:!0,renderTrigger:()=>e.jsx(I,{color:"light",size:"xs",children:e.jsx(We,{className:"h-4 w-4"})}),children:[e.jsx(se.Item,{icon:Xe,children:e.jsx(ge,{to:`/employees/details/${r.id}`,children:"View Details"})}),e.jsx(se.Item,{icon:Ze,onClick:()=>Oe(r),children:"Edit"}),e.jsx(se.Item,{icon:Ke,onClick:()=>Me(r),children:"Delete"})]})})})]},r.id)})})]})}),e.jsx(ns,{currentPage:o,totalPages:Math.ceil($/K),itemsPerPage:K,totalItems:$,onPageChange:E,onItemsPerPageChange:r=>{ke(r),E(1)},itemName:"employees"})]}),e.jsxs(T,{show:we,onClose:()=>W(!1),size:"xl",children:[e.jsx(T.Header,{children:"Add New Employee"}),e.jsx(T.Body,{children:e.jsx(xe,{onSubmit:Fe,isSubmitting:ae,error:me||void 0,onCancel:()=>W(!1)})})]}),e.jsxs(T,{show:Ee,onClose:()=>D(!1),size:"xl",children:[e.jsxs(T.Header,{children:["Edit Employee: ",k==null?void 0:k.first_name," ",k==null?void 0:k.last_name]}),e.jsx(T.Body,{children:k&&e.jsx(xe,{initialData:k,onSubmit:Le,isSubmitting:ae,error:me||void 0,onCancel:()=>D(!1)})})]}),e.jsxs(T,{show:re,onClose:()=>V(!1),size:"md",popup:!0,children:[e.jsx(T.Header,{}),e.jsx(T.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(oe,{className:"mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500 dark:text-gray-400",children:["Are you sure you want to delete"," ",e.jsxs("span",{className:"font-semibold",children:[S==null?void 0:S.first_name," ",S==null?void 0:S.last_name]}),"?"]}),d&&e.jsx(ne,{color:"failure",className:"mb-4",children:d}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(I,{color:"failure",onClick:Re,disabled:p,children:p?e.jsx(le,{size:"sm"}):"Yes, delete"}),e.jsx(I,{color:"gray",onClick:()=>V(!1),disabled:p,children:"No, cancel"})]})]})})]}),e.jsx(ls,{show:Se,onClose:()=>te(!1),onImportComplete:Ue,title:"Import Employees",entityName:"employees",previewFunction:as,importFunction:ps,downloadTemplateFunction:hs})]})};export{Es as default};
