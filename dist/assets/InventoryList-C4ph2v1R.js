import{r as i,h as te,j as e,i as le,B as r,an as b,L as h,Z as ae,a0 as re,ao as ne,J as ce,ap as ie,t as z,al as oe,aq as de,P as me,D as w,ar as xe,as as he,_ as t,at as p,au as _,e as A}from"./index-C6AV3cVN.js";import{C as j}from"./Card-yj7fueH8.js";import{g as Q}from"./product-Ca8DWaNR.js";import{u as je}from"./currencyFormatter-BsFWv3sX.js";import{P as ue}from"./Pagination-CVEzfctr.js";import{a as L}from"./formatters-Cypx7G-j.js";import{a as ge}from"./excelExport-BekG2cQR.js";const _e=()=>{const[N,g]=i.useState(!0),[n,C]=i.useState([]),[S,f]=i.useState(null),[o,M]=i.useState(""),[B,F]=i.useState(1),[I,V]=i.useState(10),[d,K]=i.useState("name"),[m,T]=i.useState("asc"),[u,P]=i.useState("all"),O=je(),{currentOrganization:a,organizations:l,setCurrentOrganization:D,loading:y,error:q}=te();i.useEffect(()=>{if(console.log("Organization context value:",a?`ID: ${a.id}, Name: ${a.name}`:"Not available"),console.log("Organization context state:",{availableOrgs:(l==null?void 0:l.length)||0,loading:y,error:q?q.message:"No error"}),a){console.log("Using current organization:",a.name),H();return}if(!a&&(l==null?void 0:l.length)>0&&!y){console.log("Organizations available but none selected. Setting first one:",l[0].name),D(l[0]),localStorage.setItem("selectedOrganizationId",l[0].id);return}!a&&!y&&(l==null?void 0:l.length)===0&&(console.log("No organizations available and not loading - showing fallback UI"),g(!1))},[a,l,y,q,D]);const H=async()=>{try{if(g(!0),f(null),!a||!a.id)if(console.warn("Organization ID not available when fetchProducts was called"),(l==null?void 0:l.length)>0){console.log("Using first organization as fallback:",l[0].name);const{products:x,error:$}=await Q(l[0].id,{sortBy:d,sortOrder:m,searchQuery:o.length>0?o:void 0});if($)throw new Error($);C(x||[]);return}else throw new Error("Organization ID is not available");console.log("Fetching products for organization:",a.id);const{products:s,error:c}=await Q(a.id,{sortBy:d,sortOrder:m,searchQuery:o.length>0?o:void 0});if(console.log("API response:",s?`Found ${s.length} products`:"No products returned",c||"No error"),c)throw new Error(c);C(s||[])}catch(s){console.error("Error fetching products:",s),f(s instanceof Error?s.message:"Failed to fetch products"),C([])}finally{g(!1)}},E=[...n.filter(s=>u==="in_stock"&&(s.stock_quantity||0)<=0||u==="low_stock"&&((s.stock_quantity||0)>(s.min_stock_level||0)||(s.stock_quantity||0)<=0)||u==="out_of_stock"&&(s.stock_quantity||0)>0?!1:o?s.name.toLowerCase().includes(o.toLowerCase())||s.sku&&s.sku.toLowerCase().includes(o.toLowerCase())||s.barcode&&s.barcode.toLowerCase().includes(o.toLowerCase()):!0)].sort((s,c)=>{let x=0;switch(d){case"name":x=s.name.localeCompare(c.name);break;case"stock_quantity":x=(s.stock_quantity||0)-(c.stock_quantity||0);break;case"unit_price":x=(Number(s.unit_price)||0)-(Number(c.unit_price)||0);break;case"cost_price":x=(Number(s.cost_price)||0)-(Number(c.cost_price)||0);break;default:x=s.name.localeCompare(c.name)}return m==="desc"?-x:x}),R=B*I,W=R-I,U=E.slice(W,R),J=Math.ceil(E.length/I),v=s=>{d===s?T(m==="asc"?"desc":"asc"):(K(s),T("asc"))},Y=s=>!s.stock_quantity||s.stock_quantity<=0?e.jsx(A,{color:"failure",children:"Out of Stock"}):s.min_stock_level&&s.stock_quantity<=s.min_stock_level?e.jsx(A,{color:"warning",children:"Low Stock"}):e.jsx(A,{color:"success",children:"In Stock"}),Z=n.length,k=n.filter(s=>(s.stock_quantity||0)>0&&s.min_stock_level&&(s.stock_quantity||0)<=s.min_stock_level).length,G=n.filter(s=>!s.stock_quantity||s.stock_quantity<=0).length,X=n.reduce((s,c)=>s+(c.stock_quantity||0)*Number(c.cost_price||0),0),ee=()=>{H()},se=()=>{if(n.length===0){f("No inventory data to export");return}ge(n)};return i.useEffect(()=>{if(N){const s=setTimeout(()=>{N&&(console.log("Loading timeout reached, forcing loading to end"),g(!1),!S&&n.length===0&&f("Unable to fetch inventory data. Please try again later."))},3e3);return()=>clearTimeout(s)}},[N,n.length,S]),N?e.jsxs("div",{className:"flex flex-col justify-center items-center h-64",children:[e.jsx(le,{size:"xl"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Loading inventory data..."}),e.jsx(r,{color:"light",size:"sm",className:"mt-4",onClick:()=>{g(!1),C([]),f("Loading canceled. Click refresh to try again.")},children:"Cancel Loading"})]}):!a&&!N&&!y&&(l==null?void 0:l.length)===0?e.jsx(j,{className:"mb-4",children:e.jsxs("div",{className:"p-6 text-center",children:[e.jsx(b,{className:"w-12 h-12 text-yellow-500 mx-auto mb-4"}),e.jsx("h5",{className:"text-xl font-bold mb-2",children:"No Organizations Available"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"Please create an organization before managing inventory."}),e.jsx(h,{to:"/organization/create",children:e.jsx(r,{color:"primary",children:"Create Organization"})})]})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-500 mt-1",children:"View and manage your inventory levels. Track stock quantities and monitor low stock items."})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(r,{color:"light",onClick:se,children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Export"]}),e.jsx(h,{to:"/inventory/receipts/create",children:e.jsxs(r,{color:"primary",className:"bg-primary hover:bg-primary-700",children:[e.jsx(re,{className:"mr-2 h-5 w-5"}),"Receive Inventory"]})}),e.jsx(h,{to:"/inventory/transactions",children:e.jsxs(r,{color:"light",children:[e.jsx(ne,{className:"mr-2 h-5 w-5"}),"Transactions"]})}),e.jsx(h,{to:"/inventory/float",children:e.jsxs(r,{color:"warning",className:"bg-yellow-500 hover:bg-yellow-600 text-white",children:[e.jsx(ce,{className:"mr-2 h-5 w-5"}),"Float Inventory"]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6",children:[e.jsx(j,{className:"border-l-4 border-blue-500",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Total Products"}),e.jsx("p",{className:"text-2xl font-bold",children:Z})]}),e.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:e.jsx(ie,{className:"h-6 w-6 text-blue-500"})})]})}),e.jsx(j,{className:"border-l-4 border-yellow-500",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Low Stock Items"}),e.jsx("p",{className:"text-2xl font-bold",children:k})]}),e.jsx("div",{className:"p-3 bg-yellow-100 rounded-full",children:e.jsx(b,{className:"h-6 w-6 text-yellow-500"})})]})}),e.jsx(j,{className:"border-l-4 border-red-500",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Out of Stock"}),e.jsx("p",{className:"text-2xl font-bold",children:G})]}),e.jsx("div",{className:"p-3 bg-red-100 rounded-full",children:e.jsx(b,{className:"h-6 w-6 text-red-500"})})]})}),e.jsx(j,{className:"border-l-4 border-green-500",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Total Value"}),e.jsx("p",{className:"text-2xl font-bold",children:O(X)})]}),e.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:e.jsx(z,{className:"h-6 w-6 text-green-500"})})]})}),e.jsx(j,{className:"border-l-4 border-orange-500",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Float Inventory"}),e.jsx("p",{className:"text-2xl font-bold",children:n.filter(s=>(s.stock_quantity||0)<0).length})]}),e.jsx("div",{className:"p-3 bg-orange-100 rounded-full",children:e.jsx(b,{className:"h-6 w-6 text-orange-500"})})]})})]}),e.jsxs(j,{className:"mb-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("h5",{className:"text-xl font-bold",children:"Inventory List"}),e.jsx(oe,{content:"Refresh inventory data",children:e.jsx(r,{color:"light",size:"xs",pill:!0,className:"ml-2",onClick:ee,children:e.jsx(z,{})})})]}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(de,{className:"text-gray-500"})}),e.jsx(me,{type:"text",placeholder:"Search products...",value:o,onChange:s=>M(s.target.value),className:"pl-10"})]}),e.jsxs(w,{label:e.jsxs("div",{className:"flex items-center",children:[e.jsx(xe,{className:"mr-2"}),e.jsx("span",{children:u==="all"?"All Items":u==="in_stock"?"In Stock":u==="low_stock"?"Low Stock":"Out of Stock"})]}),color:"light",children:[e.jsx(w.Item,{onClick:()=>P("all"),children:"All Items"}),e.jsx(w.Item,{onClick:()=>P("in_stock"),children:"In Stock"}),e.jsx(w.Item,{onClick:()=>P("low_stock"),children:"Low Stock"}),e.jsx(w.Item,{onClick:()=>P("out_of_stock"),children:"Out of Stock"})]}),e.jsxs(r,{color:"light",children:[e.jsx(he,{className:"mr-2 h-5 w-5"}),"Export"]})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(t,{hoverable:!0,children:[e.jsxs(t.Head,{children:[e.jsx(t.HeadCell,{className:"cursor-pointer",onClick:()=>v("name"),children:e.jsxs("div",{className:"flex items-center",children:["Product Name",d==="name"&&(m==="asc"?e.jsx(p,{className:"ml-1"}):e.jsx(_,{className:"ml-1"}))]})}),e.jsx(t.HeadCell,{children:"SKU / Barcode"}),e.jsx(t.HeadCell,{className:"cursor-pointer",onClick:()=>v("stock_quantity"),children:e.jsxs("div",{className:"flex items-center",children:["Quantity",d==="stock_quantity"&&(m==="asc"?e.jsx(p,{className:"ml-1"}):e.jsx(_,{className:"ml-1"}))]})}),e.jsx(t.HeadCell,{className:"cursor-pointer",onClick:()=>v("min_stock_level"),children:e.jsxs("div",{className:"flex items-center",children:["Min Stock",d==="min_stock_level"&&(m==="asc"?e.jsx(p,{className:"ml-1"}):e.jsx(_,{className:"ml-1"}))]})}),e.jsx(t.HeadCell,{children:"Status"}),e.jsx(t.HeadCell,{className:"cursor-pointer",onClick:()=>v("unit_price"),children:e.jsxs("div",{className:"flex items-center",children:["Unit Price",d==="unit_price"&&(m==="asc"?e.jsx(p,{className:"ml-1"}):e.jsx(_,{className:"ml-1"}))]})}),e.jsx(t.HeadCell,{className:"cursor-pointer",onClick:()=>v("cost_price"),children:e.jsxs("div",{className:"flex items-center",children:["Cost",d==="cost_price"&&(m==="asc"?e.jsx(p,{className:"ml-1"}):e.jsx(_,{className:"ml-1"}))]})}),e.jsx(t.HeadCell,{children:"Value"}),e.jsx(t.HeadCell,{children:"Actions"})]}),e.jsx(t.Body,{className:"divide-y",children:U.length>0?U.map(s=>e.jsxs(t.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsxs(t.Cell,{className:"font-medium text-gray-900 dark:text-white",children:[e.jsx(h,{to:`/inventory/details/${s.id}`,className:"text-blue-600 hover:underline",children:s.name}),s.category&&e.jsx("div",{className:"text-xs text-gray-500 mt-1",children:s.category.name})]}),e.jsxs(t.Cell,{children:[s.sku&&e.jsxs("div",{children:["SKU: ",s.sku]}),s.barcode&&e.jsxs("div",{children:["Barcode: ",s.barcode]})]}),e.jsx(t.Cell,{children:e.jsx("span",{className:"font-semibold",children:L(s.stock_quantity||0)})}),e.jsx(t.Cell,{children:L(s.min_stock_level||0)}),e.jsx(t.Cell,{children:Y(s)}),e.jsx(t.Cell,{children:O(s.unit_price)}),e.jsx(t.Cell,{children:O(s.cost_price)}),e.jsx(t.Cell,{className:"font-medium",children:O((s.stock_quantity||0)*Number(s.cost_price||0))}),e.jsx(t.Cell,{children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(h,{to:`/inventory/adjust/${s.id}`,children:e.jsx(r,{size:"xs",color:"light",children:"Adjust"})}),e.jsx(h,{to:`/inventory/details/${s.id}`,children:e.jsx(r,{size:"xs",color:"light",children:"History"})})]})})]},s.id)):e.jsx(t.Row,{children:e.jsx(t.Cell,{colSpan:8,className:"text-center py-4",children:o||u!=="all"?"No products match your search criteria.":"No products found in inventory."})})})]})}),e.jsx(ue,{currentPage:B,totalPages:J,itemsPerPage:I,totalItems:E.length,onPageChange:F,onItemsPerPageChange:s=>{V(s),F(1)},itemName:"products"})]}),k>0&&e.jsxs(j,{className:"bg-yellow-50 border-yellow-200 mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(b,{className:"h-6 w-6 text-yellow-500 mr-2"}),e.jsx("h2",{className:"text-lg font-semibold text-yellow-700",children:"Low Stock Alert"})]}),e.jsxs("p",{className:"text-yellow-600 mb-4",children:["You have ",k," items that are running low on inventory."]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm text-left",children:[e.jsx("thead",{className:"text-xs text-gray-700 uppercase bg-yellow-100",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2",children:"Product Name"}),e.jsx("th",{className:"px-4 py-2",children:"Current Stock"}),e.jsx("th",{className:"px-4 py-2",children:"Min. Stock Level"}),e.jsx("th",{className:"px-4 py-2",children:"Action"})]})}),e.jsx("tbody",{children:n.filter(s=>(s.stock_quantity||0)>0&&s.min_stock_level&&(s.stock_quantity||0)<=s.min_stock_level).slice(0,5).map(s=>e.jsxs("tr",{className:"border-b border-yellow-200",children:[e.jsx("td",{className:"px-4 py-2 font-medium",children:e.jsx(h,{to:`/inventory/details/${s.id}`,className:"text-blue-600 hover:underline",children:s.name})}),e.jsx("td",{className:"px-4 py-2",children:L(s.stock_quantity)}),e.jsx("td",{className:"px-4 py-2",children:L(s.min_stock_level)}),e.jsx("td",{className:"px-4 py-2",children:e.jsx(h,{to:`/inventory/receipts/create?product=${s.id}`,children:e.jsx(r,{size:"xs",color:"warning",children:"Order More"})})})]},s.id))})]})}),k>5&&e.jsx("div",{className:"text-right mt-2",children:e.jsxs(r,{color:"warning",size:"xs",children:["View All ",k," Low Stock Items"]})})]}),S&&e.jsx("div",{className:"mt-4",children:e.jsx(j,{color:"failure",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-lg",children:"Error Loading Inventory"}),e.jsx("p",{className:"mt-2",children:S}),n.length>0&&e.jsx("p",{className:"mt-2 italic text-sm",children:"Note: Showing sample or partial data."})]}),e.jsxs(r,{color:"failure",onClick:()=>{f(null),g(!0),H()},children:[e.jsx(z,{className:"mr-2 h-5 w-5"}),"Retry"]})]})})})]})};export{_e as default};
