import{b as Z,h as ee,r as i,j as e,B as x,ag as X,M as _,e as j,Y as ae,bK as ie,A as U,$ as le,V as te,a0 as oe,i as Q,H as ce,a7 as B,P as M,_ as l,bT as ne,bd as de,ad as ue,U as J}from"./index-C6AV3cVN.js";import{C as se}from"./Card-yj7fueH8.js";import{P as pe}from"./Pagination-CVEzfctr.js";import{c as re,E as D,R as me,b as xe,d as ge,u as he}from"./operationalExpenses-C7_pxLV1.js";const F=[{code:"RENT",name:"Rent & Utilities",category:"operational",description:"Monthly rent, electricity, water, internet",is_recurring:!0,requires_approval:!0,approval_limit:5e4,icon:"🏢",color:"blue"},{code:"SALARY",name:"Salaries & Wages",category:"operational",description:"Employee salaries, wages, and benefits",is_recurring:!0,requires_approval:!0,approval_limit:1e5,icon:"👥",color:"green"},{code:"OFFICE",name:"Office Supplies",category:"office_supplies",description:"Stationery, printing, office equipment",is_recurring:!1,requires_approval:!1,approval_limit:5e3,icon:"📝",color:"gray"},{code:"TRANSPORT",name:"Transportation",category:"travel",description:"Fuel, vehicle maintenance, delivery costs",is_recurring:!1,requires_approval:!1,approval_limit:1e4,icon:"🚗",color:"orange"},{code:"MARKETING",name:"Marketing & Advertising",category:"operational",description:"Social media ads, flyers, promotional materials",is_recurring:!1,requires_approval:!0,approval_limit:25e3,icon:"📢",color:"purple"},{code:"INSURANCE",name:"Insurance",category:"operational",description:"Business insurance, health insurance",is_recurring:!0,requires_approval:!0,approval_limit:3e4,icon:"🛡️",color:"indigo"},{code:"BANK",name:"Bank Charges",category:"financial",description:"Bank fees, transaction charges, loan interest",is_recurring:!1,requires_approval:!1,approval_limit:2e3,icon:"🏦",color:"red"},{code:"GOVT",name:"Government Fees",category:"administrative",description:"Business permits, licenses, taxes",is_recurring:!1,requires_approval:!0,approval_limit:2e4,icon:"🏛️",color:"yellow"}],ve=[{id:"sari_sari_store",name:"Sari-Sari Store",description:"Perfect for neighborhood convenience stores",icon:"🏪",color:"green",expense_types:[...F,{code:"INVENTORY",name:"Inventory Purchase",category:"operational",description:"Buying products to sell (snacks, drinks, household items)",is_recurring:!0,requires_approval:!0,approval_limit:5e4,icon:"📦",color:"blue"},{code:"LOAD",name:"Load & E-Money",category:"operational",description:"Mobile load, GCash, PayMaya top-ups for resale",is_recurring:!0,requires_approval:!1,approval_limit:2e4,icon:"📱",color:"cyan"}]},{id:"retail_store",name:"Retail Store",description:"For clothing, electronics, and general retail",icon:"🛍️",color:"purple",expense_types:[...F,{code:"MERCHANDISE",name:"Merchandise Purchase",category:"operational",description:"Buying products for resale",is_recurring:!0,requires_approval:!0,approval_limit:2e5,icon:"👕",color:"blue"},{code:"SECURITY",name:"Security Services",category:"professional_services",description:"Security guards, CCTV, alarm systems",is_recurring:!0,requires_approval:!0,approval_limit:25e3,icon:"🔒",color:"red"}]},{id:"restaurant",name:"Restaurant & Food Service",description:"For restaurants, cafes, and food businesses",icon:"🍽️",color:"orange",expense_types:[...F,{code:"INGREDIENTS",name:"Food Ingredients",category:"operational",description:"Raw materials, ingredients, beverages",is_recurring:!0,requires_approval:!0,approval_limit:1e5,icon:"🥘",color:"green"},{code:"KITCHEN",name:"Kitchen Equipment",category:"operational",description:"Cooking equipment, utensils, maintenance",is_recurring:!1,requires_approval:!0,approval_limit:75e3,icon:"🍳",color:"red"}]},{id:"service_business",name:"Service Business",description:"For salons, repair shops, consulting",icon:"🔧",color:"blue",expense_types:[...F,{code:"TOOLS",name:"Tools & Equipment",category:"operational",description:"Professional tools, equipment, maintenance",is_recurring:!1,requires_approval:!0,approval_limit:5e4,icon:"🔨",color:"gray"}]},{id:"accounting_services",name:"Accounting & Professional Services",description:"For accounting firms, consultants, and professional services",icon:"📊",color:"indigo",expense_types:[...F,{code:"SOFTWARE",name:"Accounting Software",category:"operational",description:"QuickBooks, Xero, accounting software subscriptions",is_recurring:!0,requires_approval:!0,approval_limit:2e4,icon:"💻",color:"blue"},{code:"LICENSES",name:"Professional Licenses",category:"professional_services",description:"CPA licenses, professional certifications",is_recurring:!0,requires_approval:!0,approval_limit:15e3,icon:"📜",color:"purple"}]},{id:"online_business",name:"Online Business",description:"For e-commerce and digital businesses",icon:"💻",color:"cyan",expense_types:[...F,{code:"PLATFORM",name:"Platform Fees",category:"operational",description:"Shopee, Lazada, Facebook Marketplace fees",is_recurring:!0,requires_approval:!1,approval_limit:1e4,icon:"🛒",color:"orange"},{code:"SHIPPING",name:"Shipping & Logistics",category:"operational",description:"Courier fees, packaging, logistics",is_recurring:!0,requires_approval:!1,approval_limit:2e4,icon:"📦",color:"blue"}]}],fe=({onExpenseTypesAdded:S,className:b=""})=>{const{user:w}=Z(),{currentOrganization:H}=ee(),[$,N]=i.useState(!1),[t,n]=i.useState(null),[h,u]=i.useState(new Set),[v,E]=i.useState(!1),[R,q]=i.useState(0),[m,f]=i.useState(null),[I,k]=i.useState(null),P=a=>{n(a);const r=new Set(a.expense_types.map(o=>o.code));u(r)},Y=a=>{const r=new Set(h);r.has(a)?r.delete(a):r.add(a),u(r)},z=async()=>{var a,r,o,C,T,L;if(!(!H||!w||!t)){E(!0),f(null),q(0);try{const A=t.expense_types.filter(d=>h.has(d.code));let y=0,g=0;const G=A.length;for(const d of A){try{const p=await re(H.id,{code:d.code,name:d.name,category:d.category,description:d.description,is_recurring_type:d.is_recurring,requires_approval:d.requires_approval,approval_limit:d.approval_limit},w.id);p.success?y++:((a=p.error)!=null&&a.toLowerCase().includes("duplicate")||(r=p.error)!=null&&r.toLowerCase().includes("already exists")||(o=p.error)!=null&&o.toLowerCase().includes("unique constraint"))&&g++}catch(p){console.error(`Error creating expense type ${d.code}:`,p),((C=p.message)!=null&&C.toLowerCase().includes("duplicate")||(T=p.message)!=null&&T.toLowerCase().includes("already exists")||(L=p.message)!=null&&L.toLowerCase().includes("unique constraint"))&&g++}q(Math.round((y+g)/G*100))}let O="";if(y>0&&g>0)O=`Created ${y} new expense types, skipped ${g} existing ones.`;else if(y>0)O=`Successfully created ${y} expense types!`;else if(g>0)O=`All ${g} expense types already exist - no new ones created.`;else{f("Failed to create expense types. Please try again.");return}k(O),S==null||S(),setTimeout(()=>{N(!1),n(null),u(new Set),k(null)},3e3)}catch(A){f(A.message)}finally{E(!1),q(0)}}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:b,children:e.jsxs(x,{color:"light",onClick:()=>N(!0),className:"w-full",children:[e.jsx(X,{className:"mr-2 h-5 w-5"}),"Browse Expense Type Collections"]})}),e.jsxs(_,{show:$,onClose:()=>{N(!1),n(null),u(new Set),f(null),k(null)},size:"6xl",children:[e.jsx(_.Header,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(X,{className:"h-6 w-6"}),"Expense Type Collections"]})}),e.jsx(_.Body,{children:t?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(x,{color:"light",size:"sm",onClick:()=>n(null),children:"← Back"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:t.icon}),t.name]}),e.jsx("p",{className:"text-sm text-gray-600",children:t.description})]})]}),e.jsxs(j,{color:t.color,children:[h.size," of ",t.expense_types.length," selected"]})]}),v&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Creating expense types..."}),e.jsxs("span",{children:[R,"%"]})]}),e.jsx(ie,{progress:R,color:"blue"})]}),m&&e.jsx(U,{color:"failure",onDismiss:()=>f(null),children:m}),I&&e.jsx(U,{color:"success",children:I}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 max-h-96 overflow-y-auto",children:t.expense_types.map(a=>e.jsx("div",{className:`p-4 border-2 rounded-lg cursor-pointer transition-all ${h.has(a.code)?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>Y(a.code),children:e.jsx("div",{className:"flex items-start justify-between",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("span",{className:"text-lg",children:a.icon}),e.jsx("h5",{className:"font-medium",children:a.name}),h.has(a.code)&&e.jsx(le,{className:"h-4 w-4 text-blue-600"})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:a.description}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.jsx(j,{color:a.color,size:"xs",children:a.category.replace("_"," ")}),a.is_recurring&&e.jsx(j,{color:"green",size:"xs",children:"Recurring"}),a.requires_approval&&e.jsx(j,{color:"yellow",size:"xs",children:"Needs Approval"})]})]})})},a.code))})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Choose Your Business Type"}),e.jsx("p",{className:"text-gray-600",children:"Select a business type to see pre-configured expense categories that match your needs."})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:ve.map(a=>e.jsx(se,{className:"cursor-pointer hover:shadow-lg transition-all duration-200 border-2 border-transparent hover:border-blue-200",onClick:()=>P(a),children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-4xl mb-3",children:a.icon}),e.jsx("h4",{className:"text-lg font-semibold mb-2",children:a.name}),e.jsx("p",{className:"text-sm text-gray-600 mb-3",children:a.description}),e.jsxs(j,{color:a.color,className:"mb-2",children:[a.expense_types.length," expense types"]})]})},a.id))}),e.jsx("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(ae,{className:"h-5 w-5 text-blue-600 mt-0.5"}),e.jsxs("div",{className:"text-sm text-blue-800",children:[e.jsx("p",{className:"font-medium mb-1",children:"What are expense type collections?"}),e.jsx("p",{children:"These are pre-configured expense categories tailored for different business types. You can select which ones to add to your organization and customize them later."})]})]})})]})}),e.jsx(_.Footer,{children:t&&e.jsxs("div",{className:"flex justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(x,{color:"light",onClick:()=>{const a=new Set(t.expense_types.map(r=>r.code));u(a)},size:"sm",children:"Select All"}),e.jsx(x,{color:"light",onClick:()=>u(new Set),size:"sm",children:"Clear All"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(x,{color:"light",onClick:()=>{N(!1),n(null),u(new Set)},disabled:v,children:[e.jsx(te,{className:"mr-2 h-4 w-4"}),"Cancel"]}),e.jsxs(x,{color:"primary",onClick:z,disabled:h.size===0||v,children:[e.jsx(oe,{className:"mr-2 h-4 w-4"}),v?"Creating...":`Add ${h.size} Expense Types`]})]})]})})]})]})},Ne=()=>{var W;const{user:S}=Z(),{currentOrganization:b}=ee(),[w,H]=i.useState([]),[$,N]=i.useState(!0),[t,n]=i.useState(null),[h,u]=i.useState(!1),[v,E]=i.useState(null),[R,q]=i.useState(!1),[m,f]=i.useState({}),[I,k]=i.useState(1),[P,Y]=i.useState(10),[z,a]=i.useState(0),[r,o]=i.useState({name:"",code:"",description:"",category:D.OPERATIONAL,requires_approval:!0,approval_limit:0,default_account_code:"",is_recurring_type:!1,default_frequency:void 0}),C=async()=>{if(b)try{N(!0);const s=await xe(b.id,m);s.success&&s.data?(H(s.data),a(s.data.length)):n(s.error||"Failed to load expense types")}catch(s){n(s.message)}finally{N(!1)}};i.useEffect(()=>{C()},[b,m]);const T=Math.ceil(z/P),L=(I-1)*P,A=L+P,y=w.slice(L,A),g=async s=>{if(s.preventDefault(),!(!b||!S))try{q(!0),n(null);let c;v?c=await he(v.id,r):c=await re(b.id,r,S.id),c.success?(u(!1),E(null),d(),C()):n(c.error||"Failed to save expense type")}catch(c){n(c.message)}finally{q(!1)}},G=s=>{E(s),o({name:s.name,code:s.code,description:s.description||"",category:s.category,requires_approval:s.requires_approval,approval_limit:s.approval_limit,default_account_code:s.default_account_code||"",is_recurring_type:s.is_recurring_type,default_frequency:s.default_frequency}),u(!0)},O=async s=>{if(confirm(`Are you sure you want to delete "${s.name}"?`))try{const c=await ge(s.id);c.success?C():n(c.error||"Failed to delete expense type")}catch(c){n(c.message)}},d=()=>{o({name:"",code:"",description:"",category:D.OPERATIONAL,requires_approval:!0,approval_limit:0,default_account_code:"",is_recurring_type:!1,default_frequency:void 0})},p=()=>{u(!1),E(null),d(),n(null)},K=s=>s.replace(/_/g," ").replace(/\b\w/g,c=>c.toUpperCase()),V=s=>s?s.replace(/_/g," ").replace(/\b\w/g,c=>c.toUpperCase()):"-";return $?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(Q,{size:"xl"})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(se,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Expense Types"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Manage expense categories and approval workflows"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(fe,{onExpenseTypesAdded:C,className:"flex-1"}),e.jsxs(x,{onClick:()=>u(!0),className:"bg-primary",children:[e.jsx(ce,{className:"mr-2 h-4 w-4"}),"Add Custom Type"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsxs(B,{value:m.category||"",onChange:s=>f({...m,category:s.target.value||void 0}),children:[e.jsx("option",{value:"",children:"All Categories"}),Object.values(D).map(s=>e.jsx("option",{value:s,children:K(s)},s))]}),e.jsxs(B,{value:((W=m.is_active)==null?void 0:W.toString())||"",onChange:s=>f({...m,is_active:s.target.value?s.target.value==="true":void 0}),children:[e.jsx("option",{value:"",children:"All Status"}),e.jsx("option",{value:"true",children:"Active"}),e.jsx("option",{value:"false",children:"Inactive"})]}),e.jsx(M,{placeholder:"Search expense types...",value:m.search||"",onChange:s=>f({...m,search:s.target.value||void 0})})]}),t&&e.jsx(U,{color:"failure",className:"mb-4",children:t}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(l,{children:[e.jsxs(l.Head,{children:[e.jsx(l.HeadCell,{children:"Name"}),e.jsx(l.HeadCell,{children:"Code"}),e.jsx(l.HeadCell,{children:"Category"}),e.jsx(l.HeadCell,{children:"Approval Limit"}),e.jsx(l.HeadCell,{children:"Recurring"}),e.jsx(l.HeadCell,{children:"Status"}),e.jsx(l.HeadCell,{children:"Actions"})]}),e.jsx(l.Body,{className:"divide-y",children:y.map(s=>e.jsxs(l.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(l.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold",children:s.name}),s.description&&e.jsx("div",{className:"text-sm text-gray-500",children:s.description})]})}),e.jsx(l.Cell,{children:e.jsx(j,{color:"gray",children:s.code})}),e.jsx(l.Cell,{children:K(s.category)}),e.jsx(l.Cell,{children:s.requires_approval?e.jsxs("span",{children:["₱",s.approval_limit.toLocaleString()]}):e.jsx("span",{className:"text-gray-500",children:"No approval required"})}),e.jsx(l.Cell,{children:s.is_recurring_type?e.jsxs("div",{children:[e.jsx(j,{color:"blue",children:"Recurring"}),e.jsx("div",{className:"text-xs text-gray-500 mt-1",children:V(s.default_frequency)})]}):e.jsx(j,{color:"gray",children:"One-time"})}),e.jsx(l.Cell,{children:e.jsx(j,{color:s.is_active?"green":"red",children:s.is_active?"Active":"Inactive"})}),e.jsx(l.Cell,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{size:"xs",color:"gray",onClick:()=>G(s),children:e.jsx(ne,{className:"h-3 w-3"})}),e.jsx(x,{size:"xs",color:"failure",onClick:()=>O(s),disabled:!s.is_active,children:e.jsx(de,{className:"h-3 w-3"})})]})})]},s.id))})]})}),w.length===0&&e.jsx("div",{className:"text-center py-8 text-gray-500",children:"No expense types found. Create your first expense type to get started."}),w.length>0&&e.jsx(pe,{currentPage:I,totalPages:T,itemsPerPage:P,totalItems:z,onPageChange:k,onItemsPerPageChange:s=>{Y(s),k(1)},itemName:"expense types"})]}),e.jsxs(_,{show:h,onClose:p,size:"lg",children:[e.jsx(_.Header,{children:v?"Edit Expense Type":"Create Expense Type"}),e.jsx(_.Body,{children:e.jsxs("form",{onSubmit:g,className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Name *"}),e.jsx(M,{value:r.name,onChange:s=>o({...r,name:s.target.value}),required:!0,placeholder:"e.g., Office Rent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Code *"}),e.jsx(M,{value:r.code,onChange:s=>o({...r,code:s.target.value.toUpperCase()}),required:!0,placeholder:"e.g., RENT",maxLength:10})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Description"}),e.jsx(ue,{value:r.description,onChange:s=>o({...r,description:s.target.value}),placeholder:"Optional description",rows:3})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Category *"}),e.jsx(B,{value:r.category,onChange:s=>o({...r,category:s.target.value}),required:!0,children:Object.values(D).map(s=>e.jsx("option",{value:s,children:K(s)},s))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Account Code"}),e.jsx(M,{value:r.default_account_code,onChange:s=>o({...r,default_account_code:s.target.value}),placeholder:"e.g., 5100"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(J,{checked:r.requires_approval,onChange:s=>o({...r,requires_approval:s.target.checked})}),e.jsx("label",{className:"text-sm font-medium",children:"Requires Approval"})]}),r.requires_approval&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Approval Limit (₱)"}),e.jsx(M,{type:"number",value:r.approval_limit,onChange:s=>o({...r,approval_limit:parseFloat(s.target.value)||0}),min:"0",step:"0.01",placeholder:"0.00"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(J,{checked:r.is_recurring_type,onChange:s=>o({...r,is_recurring_type:s.target.checked})}),e.jsx("label",{className:"text-sm font-medium",children:"Can be Recurring"})]}),r.is_recurring_type&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Default Frequency"}),e.jsxs(B,{value:r.default_frequency||"",onChange:s=>o({...r,default_frequency:s.target.value||void 0}),children:[e.jsx("option",{value:"",children:"Select frequency"}),Object.values(me).map(s=>e.jsx("option",{value:s,children:V(s)},s))]})]}),t&&e.jsx(U,{color:"failure",children:t})]})}),e.jsxs(_.Footer,{children:[e.jsxs(x,{onClick:g,disabled:R,className:"bg-primary",children:[R?e.jsx(Q,{size:"sm",className:"mr-2"}):null,v?"Update":"Create"]}),e.jsx(x,{color:"gray",onClick:p,children:"Cancel"})]})]})]})};export{Ne as default};
