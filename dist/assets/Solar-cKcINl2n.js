import{j as s}from"./index-C6AV3cVN.js";const r=()=>s.jsx(s.Fragment,{children:s.jsxs("div",{className:"rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words",children:[s.jsx("h5",{className:"card-title",children:"Icons"}),s.jsx("div",{className:"mt-6",children:s.jsx("iframe",{src:"https://icon-sets.iconify.design/solar/",title:"Inline Frame Example",width:"100%",height:"650"})})]})});export{r as default};
