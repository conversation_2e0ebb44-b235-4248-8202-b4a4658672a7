import{j as b,P as j}from"./index-C6AV3cVN.js";const g=({value:n,onChange:r,onBlur:t,autoSelect:s=!0,preventScrollChange:u=!0,className:o="",sizing:a="md",min:c,max:l,step:h,required:m,placeholder:p,...d})=>{const i=e=>{s&&e.target.select()},x=e=>{u&&e.currentTarget.blur()},f=e=>{t&&t(e)};return b.jsx(j,{type:"number",value:n,onChange:r,onFocus:i,onWheel:x,onBlur:f,className:o,sizing:a,min:c,max:l,step:h,required:m,placeholder:p,...d})};export{g as E};
