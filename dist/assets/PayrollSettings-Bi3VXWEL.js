import{j as e,h as X,r as i,M as I,B as f,b0 as ce,Z as ee,i as se,A,G as U,F as V,ah as oe,aj as de,a6 as d,a7 as D,P as w,a8 as y,bL as me,a1 as he,bc as xe,Y as ue,ay as J}from"./index-C6AV3cVN.js";import{C as S}from"./Card-yj7fueH8.js";import{b as pe,e as Q}from"./payroll-DcVgVc3z.js";import{b as L}from"./payroll-j3fcCwK0.js";import{P as je}from"./PageTitle-FHPo8gWi.js";import{f as o}from"./formatters-Cypx7G-j.js";import{g as ye}from"./employee-DWC25S7P.js";import{g as K,c as be}from"./department-NDftHEGx.js";import{g as Z,c as ge}from"./jobPosition-DFMtfNvL.js";const fe=({employee:l,payPeriod:x,earnings:t,deductions:s,company:m,template:u})=>{const b=Object.values(t).reduce((j,g)=>j+(g||0),0),r=Object.values(s).reduce((j,g)=>j+(g||0),0),v=b-r,h=m.brand_color||"#3B82F6";return e.jsxs("div",{className:"bg-white p-8 shadow-lg max-w-2xl mx-auto",style:{fontFamily:"Arial, sans-serif"},children:[e.jsx("div",{className:"border-b-2 pb-4 mb-6",style:{borderColor:h},children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[m.logo&&e.jsx("img",{src:m.logo,alt:"Company Logo",className:"h-16 mb-2"}),e.jsx("h1",{className:"text-2xl font-bold",style:{color:h},children:m.name}),u.show_company_address&&m.address&&e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:m.address})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"PAYSLIP"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Pay Period: ",new Date(x.start_date).toLocaleDateString()," to"," ",new Date(x.end_date).toLocaleDateString()]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Payment Date: ",new Date(x.payment_date).toLocaleDateString()]})]})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-8 mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-800 mb-2",children:"Employee Information"}),e.jsxs("div",{className:"space-y-1 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Name:"}),e.jsx("span",{className:"font-medium",children:l.name})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Employee ID:"}),e.jsx("span",{className:"font-medium",children:l.employee_id})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Position:"}),e.jsx("span",{className:"font-medium",children:l.position&&l.position!=="N/A"&&l.position!=="null"&&l.position!=="undefined"?l.position:"Sales Associate"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Department:"}),e.jsx("span",{className:"font-medium",children:l.department&&l.department!=="N/A"&&l.department!=="null"&&l.department!=="undefined"?l.department:"Sales"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-800 mb-2",children:"Payroll Information"}),e.jsxs("div",{className:"space-y-1 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Payroll Period:"}),e.jsxs("span",{className:"font-medium",children:[new Date(x.start_date).toLocaleDateString("en-US",{month:"short",day:"numeric"})," - ",new Date(x.end_date).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Payslip #:"}),e.jsxs("span",{className:"font-medium",children:["PS",Date.now().toString().slice(-6)]})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-8 mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-800 mb-3 pb-1 border-b",style:{borderColor:h},children:"Earnings"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Basic Pay"}),e.jsx("span",{className:"font-medium",children:o(t.basic_pay)})]}),t.overtime_pay&&t.overtime_pay>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Overtime Pay"}),e.jsx("span",{className:"font-medium",children:o(t.overtime_pay)})]}),t.holiday_pay&&t.holiday_pay>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Holiday Pay"}),e.jsx("span",{className:"font-medium",children:o(t.holiday_pay)})]}),t.night_differential&&t.night_differential>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Night Differential"}),e.jsx("span",{className:"font-medium",children:o(t.night_differential)})]}),t.allowances&&t.allowances>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Allowances"}),e.jsx("span",{className:"font-medium",children:o(t.allowances)})]}),e.jsxs("div",{className:"border-t pt-2 mt-2 flex justify-between font-semibold",children:[e.jsx("span",{children:"Total Earnings"}),e.jsx("span",{children:o(b)})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-800 mb-3 pb-1 border-b",style:{borderColor:h},children:"Deductions"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[s.sss_contribution&&s.sss_contribution>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"SSS Contribution"}),e.jsx("span",{className:"font-medium",children:o(s.sss_contribution)})]}),s.philhealth_contribution&&s.philhealth_contribution>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"PhilHealth"}),e.jsx("span",{className:"font-medium",children:o(s.philhealth_contribution)})]}),s.pagibig_contribution&&s.pagibig_contribution>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Pag-IBIG"}),e.jsx("span",{className:"font-medium",children:o(s.pagibig_contribution)})]}),s.withholding_tax&&s.withholding_tax>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Withholding Tax"}),e.jsx("span",{className:"font-medium",children:o(s.withholding_tax)})]}),s.loans&&s.loans>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Loans"}),e.jsx("span",{className:"font-medium",children:o(s.loans)})]}),s.other_deductions&&s.other_deductions>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Other Deductions"}),e.jsx("span",{className:"font-medium",children:o(s.other_deductions)})]}),e.jsxs("div",{className:"border-t pt-2 mt-2 flex justify-between font-semibold",children:[e.jsx("span",{children:"Total Deductions"}),e.jsx("span",{children:o(r)})]})]})]})]}),e.jsx("div",{className:"border-t-2 pt-4",style:{borderColor:h},children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-lg font-semibold text-gray-800",children:"Net Pay"}),e.jsx("span",{className:"text-2xl font-bold",style:{color:h},children:o(v)})]})}),e.jsx("div",{className:"mt-8 pt-4 border-t text-xs text-gray-500",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{children:"This is a computer-generated payslip."}),e.jsx("p",{children:"For questions, contact HR Department."})]}),u.include_qr_code&&e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-200 border border-gray-300 flex items-center justify-center text-xs",children:"QR Code"}),e.jsx("p",{className:"mt-1",children:"Scan to verify"})]})]})})]})},ve=({isOpen:l,onClose:x})=>{const{currentOrganization:t}=X(),[s,m]=i.useState(null),[u,b]=i.useState([]),[r,v]=i.useState([]),[h,j]=i.useState(!1),g=async c=>{try{const{departments:n}=await K(c,{limit:1});n.length===0&&await be(c,{name:"Sales",description:"Sales Department",is_active:!0,manager_id:null});const{positions:p}=await Z(c,{limit:1});p.length===0&&await ge(c,{title:"Sales Associate",description:"Sales Associate Position",department_id:null,is_active:!0})}catch(n){console.error("Error ensuring basic structure:",n)}};i.useEffect(()=>{(async()=>{if(!(!t||!l)){j(!0);try{await g(t.id);const[n,p,R]=await Promise.all([ye(t.id,{limit:10}),K(t.id,{limit:5}),Z(t.id,{limit:5})]);if(b(p.departments),v(R.positions),n.employees.length>0){let P=n.employees.find(F=>F.position&&F.department);P||(P=n.employees[0]),m(P)}}catch(n){console.error("Error fetching data for preview:",n)}finally{j(!1)}}})()},[t,l]);const C=()=>{var c;if((c=s==null?void 0:s.position)!=null&&c.title)return s.position.title;if(s!=null&&s.position_id&&r.length>0){const n=r.find(p=>p.id===s.position_id);return n?n.title:"Position Not Found"}return r.length>0?r[0].title:"Sales Associate"},O=()=>{var c;if((c=s==null?void 0:s.department)!=null&&c.name)return s.department.name;if(s!=null&&s.department_id&&u.length>0){const n=u.find(p=>p.id===s.department_id);return n?n.name:"Department Not Found"}return u.length>0?u[0].name:"Sales"},E=C(),T=O(),N={employee:{name:s?`${s.first_name} ${s.last_name}`:"Raulito Cadahing Borreros",employee_id:(s==null?void 0:s.employee_number)||(s==null?void 0:s.id)||"1",position:E||"Sales Associate",department:T||"Sales"},payPeriod:{start_date:"2025-05-15",end_date:"2025-05-30",payment_date:"2025-06-03"},earnings:{basic_pay:9900,overtime_pay:1200,holiday_pay:500,night_differential:300,allowances:2e3},deductions:{sss_contribution:495,philhealth_contribution:275,pagibig_contribution:100,withholding_tax:850,loans:1e3,other_deductions:200},company:{name:"Rapverse",address:"123 Business Street, Makati City, Philippines",brand_color:"#3B82F6"},template:{style:"modern",show_company_address:!0,include_qr_code:!0,paper_size:"a4"}},k=()=>{console.log("Downloading payslip template...")},M=()=>{window.print()};return e.jsxs(I,{show:l,onClose:x,size:"4xl",children:[e.jsx(I.Header,{children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Payslip Template Preview"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(f,{color:"gray",size:"sm",onClick:M,children:[e.jsx(ce,{className:"mr-2 h-4 w-4"}),"Print"]}),e.jsxs(f,{color:"blue",size:"sm",onClick:k,children:[e.jsx(ee,{className:"mr-2 h-4 w-4"}),"Download PDF"]})]})]})}),e.jsx(I.Body,{className:"p-0",children:e.jsx("div",{className:"max-h-[70vh] overflow-y-auto p-6 bg-gray-50",children:h?e.jsxs("div",{className:"flex justify-center items-center h-64",children:[e.jsx(se,{size:"xl"}),e.jsx("span",{className:"ml-3 text-gray-600",children:"Loading employee data..."})]}):e.jsx(fe,{...N})})}),e.jsx(I.Footer,{children:e.jsxs("div",{className:"flex justify-between w-full",children:[e.jsx("div",{className:"text-sm text-gray-500",children:s?`Preview using real employee data: ${s.first_name} ${s.last_name}`:"This is a preview using sample data. Actual payslips will use real employee information."}),e.jsx(f,{color:"gray",onClick:x,children:"Close"})]})})]})},ke=()=>{const{currentOrganization:l}=X(),[x,t]=i.useState(null),[s,m]=i.useState(!0),[u,b]=i.useState(null),[r,v]=i.useState("general"),[h,j]=i.useState(!1),[g,C]=i.useState(null),[O,E]=i.useState(!1),[T,H]=i.useState(L.SEMI_MONTHLY),[N,k]=i.useState([15,30]),[M,c]=i.useState(8.5),[n,p]=i.useState(2),[R,P]=i.useState(2),[F,B]=i.useState("2023"),[Y,z]=i.useState(!0),[ae,q]=i.useState(!0),[te,G]=i.useState(!1),[le,W]=i.useState(!1),ie=async()=>{if(l){m(!0),b(null);try{const{settings:a,error:_}=await pe(l.id);_?b(_):a&&(t(a),H(a.pay_schedule),k(a.semi_monthly_days),z(a.pay_based_on_time_entries??!0),c(a.sss_employer_contribution_rate),p(a.philhealth_employer_contribution_rate),P(a.pagibig_employer_contribution_rate),B(a.tax_table_version),q(!0),G(!1))}catch(a){b(a.message)}finally{m(!1)}}};i.useEffect(()=>{ie()},[l]);const ne=async a=>{if(a.preventDefault(),!!l){j(!0),C(null),E(!1);try{const _={pay_schedule:T,semi_monthly_days:N,pay_based_on_time_entries:Y,sss_employer_contribution_rate:M,philhealth_employer_contribution_rate:n,pagibig_employer_contribution_rate:R,tax_table_version:F},{settings:re,error:$}=x?await Q(l.id,_):await Q(l.id,_);$?C($):(t(re||null),E(!0),setTimeout(()=>{E(!1)},3e3))}catch(_){C(_.message)}finally{j(!1)}}};return s?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(se,{size:"xl"})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsx(je,{title:"Payroll Settings"})}),u&&e.jsxs(A,{color:"failure",icon:U,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",u]}),e.jsxs("form",{onSubmit:ne,children:[e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("ul",{className:"flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500",children:[e.jsx("li",{className:"mr-2",children:e.jsxs("button",{type:"button",className:`inline-flex items-center p-4 border-b-2 rounded-t-lg ${r==="general"?"text-blue-600 border-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,onClick:()=>v("general"),children:[e.jsx(V,{className:"w-4 h-4 mr-2"}),"General Settings"]})}),e.jsx("li",{className:"mr-2",children:e.jsxs("button",{type:"button",className:`inline-flex items-center p-4 border-b-2 rounded-t-lg ${r==="contributions"?"text-blue-600 border-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,onClick:()=>v("contributions"),children:[e.jsx(oe,{className:"w-4 h-4 mr-2"}),"Government Contributions"]})}),e.jsx("li",{className:"mr-2",children:e.jsxs("button",{type:"button",className:`inline-flex items-center p-4 border-b-2 rounded-t-lg ${r==="templates"?"text-blue-600 border-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,onClick:()=>v("templates"),children:[e.jsx(de,{className:"w-4 h-4 mr-2"}),"Payslip Templates"]})})]})})}),r==="general"&&e.jsxs(S,{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Pay Schedule"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"paySchedule",value:"Pay Schedule"})}),e.jsxs(D,{id:"paySchedule",value:T,onChange:a=>H(a.target.value),required:!0,children:[e.jsx("option",{value:L.MONTHLY,children:"Monthly"}),e.jsx("option",{value:L.SEMI_MONTHLY,children:"Semi-Monthly"}),e.jsx("option",{value:L.WEEKLY,children:"Weekly"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Select how often employees are paid."})]}),T===L.SEMI_MONTHLY&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"firstPayDay",value:"First Pay Day"})}),e.jsx(w,{id:"firstPayDay",type:"number",min:1,max:31,value:N[0],onChange:a=>k([parseInt(a.target.value),N[1]]),required:!0}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Day of the month for the first pay period."})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"secondPayDay",value:"Second Pay Day"})}),e.jsx(w,{id:"secondPayDay",type:"number",min:1,max:31,value:N[1],onChange:a=>k([N[0],parseInt(a.target.value)]),required:!0}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Day of the month for the second pay period."})]})]}),e.jsx("h3",{className:"text-lg font-medium mb-4 mt-6",children:"Payroll Calculation"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(y,{checked:Y,onChange:z,label:"Calculate pay based on actual time entries"})}),e.jsxs("p",{className:"mt-1 text-sm text-gray-500",children:["When enabled, payroll is calculated based on actual time entries. When disabled, uses scheduled period length.",e.jsx("br",{}),e.jsx("strong",{children:"Recommended:"})," Enable this for accurate payroll based on actual work days."]})]}),e.jsx("h3",{className:"text-lg font-medium mb-4 mt-6",children:"13th Month Pay"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(y,{checked:ae,onChange:q,label:"Automatically calculate 13th month pay"})}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"When enabled, the system will automatically calculate 13th month pay based on basic salary."})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(y,{checked:te,onChange:G,label:"Include allowances in 13th month pay calculation"})}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"When enabled, allowances will be included in the 13th month pay calculation."})]})]}),r==="contributions"&&e.jsxs(S,{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Employer Contribution Rates"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"sssEmployerRate",value:"SSS Employer Rate (%)"})}),e.jsx(w,{id:"sssEmployerRate",type:"number",step:"0.01",min:0,max:100,value:M,onChange:a=>c(parseFloat(a.target.value)),required:!0}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Default: 8.5%"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"philhealthEmployerRate",value:"PhilHealth Employer Rate (%)"})}),e.jsx(w,{id:"philhealthEmployerRate",type:"number",step:"0.01",min:0,max:100,value:n,onChange:a=>p(parseFloat(a.target.value)),required:!0}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Default: 2.0%"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"pagibigEmployerRate",value:"Pag-IBIG Employer Rate (%)"})}),e.jsx(w,{id:"pagibigEmployerRate",type:"number",step:"0.01",min:0,max:100,value:R,onChange:a=>P(parseFloat(a.target.value)),required:!0}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Default: 2.0%"})]})]}),e.jsx("h3",{className:"text-lg font-medium mb-4 mt-6",children:"Tax Settings"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"taxTableVersion",value:"Tax Table Version"})}),e.jsxs(D,{id:"taxTableVersion",value:F,onChange:a=>B(a.target.value),required:!0,children:[e.jsx("option",{value:"2023",children:"2023 (Current)"}),e.jsx("option",{value:"2022",children:"2022"}),e.jsx("option",{value:"2021",children:"2021"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Select which tax table version to use for withholding tax calculations."})]})]}),r==="templates"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs(S,{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Company Branding"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"companyLogo",value:"Company Logo"})}),e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[e.jsx(me,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Click to upload logo or drag and drop"}),e.jsx("p",{className:"text-xs text-gray-400",children:"PNG, JPG up to 2MB (Recommended: 200x80px)"})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"brandColor",value:"Brand Color"})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(w,{id:"brandColor",type:"color",value:"#3B82F6",className:"w-16"}),e.jsx(w,{value:"#3B82F6",placeholder:"Hex color code",className:"flex-1"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Primary color for headers and accents"})]})]})]}),e.jsxs(S,{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Template Layout"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"templateStyle",value:"Template Style"})}),e.jsxs(D,{id:"templateStyle",value:"modern",children:[e.jsx("option",{value:"modern",children:"Modern (Recommended)"}),e.jsx("option",{value:"classic",children:"Classic"}),e.jsx("option",{value:"minimal",children:"Minimal"}),e.jsx("option",{value:"detailed",children:"Detailed"})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"paperSize",value:"Paper Size"})}),e.jsxs(D,{id:"paperSize",value:"a4",children:[e.jsx("option",{value:"a4",children:"A4 (210 × 297 mm)"}),e.jsx("option",{value:"letter",children:"Letter (8.5 × 11 in)"}),e.jsx("option",{value:"legal",children:"Legal (8.5 × 14 in)"})]})]})]}),e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(y,{checked:!0,label:"Show company address on payslip"})})}),e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(y,{checked:!0,label:"Include QR code for verification"})})})]}),e.jsxs(S,{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Field Configuration"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Employee Information"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:["Employee Name","Employee ID","Position","Department","Hire Date","Employee Photo"].map(a=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{checked:!["Employee Photo"].includes(a),size:"sm"}),e.jsx("span",{className:"text-sm",children:a})]},a))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Earnings"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:["Basic Pay","Overtime Pay","Holiday Pay","Night Differential","Allowances","Bonuses"].map(a=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{checked:!0,size:"sm"}),e.jsx("span",{className:"text-sm",children:a})]},a))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Deductions"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:["SSS Contribution","PhilHealth","Pag-IBIG","Withholding Tax","Loans","Other Deductions"].map(a=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{checked:!0,size:"sm"}),e.jsx("span",{className:"text-sm",children:a})]},a))})]})]})]}),e.jsxs(S,{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Language & Localization"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"primaryLanguage",value:"Primary Language"})}),e.jsxs(D,{id:"primaryLanguage",value:"en",children:[e.jsx("option",{value:"en",children:"English"}),e.jsx("option",{value:"fil",children:"Filipino"}),e.jsx("option",{value:"ceb",children:"Cebuano"})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(d,{htmlFor:"dateFormat",value:"Date Format"})}),e.jsxs(D,{id:"dateFormat",value:"mm/dd/yyyy",children:[e.jsx("option",{value:"mm/dd/yyyy",children:"MM/DD/YYYY (US)"}),e.jsx("option",{value:"dd/mm/yyyy",children:"DD/MM/YYYY (PH)"}),e.jsx("option",{value:"yyyy-mm-dd",children:"YYYY-MM-DD (ISO)"})]})]})]}),e.jsx("div",{className:"mt-4",children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(y,{checked:!1,label:"Enable bilingual payslips (English + Filipino)"})})})]}),e.jsxs(S,{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Preview & Actions"}),e.jsxs("div",{className:"flex flex-wrap gap-3",children:[e.jsxs(f,{color:"gray",size:"sm",onClick:()=>W(!0),children:[e.jsx(he,{className:"mr-2 h-4 w-4"}),"Preview Template"]}),e.jsxs(f,{color:"blue",size:"sm",children:[e.jsx(ee,{className:"mr-2 h-4 w-4"}),"Download Sample"]}),e.jsxs(f,{color:"green",size:"sm",children:[e.jsx(xe,{className:"mr-2 h-4 w-4"}),"Duplicate Template"]}),e.jsxs(f,{color:"purple",size:"sm",children:[e.jsx(V,{className:"mr-2 h-4 w-4"}),"Advanced Settings"]})]}),e.jsx("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(ue,{className:"h-5 w-5 text-blue-500 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-blue-900",children:"Template Tips"}),e.jsxs("ul",{className:"mt-1 text-sm text-blue-700 space-y-1",children:[e.jsx("li",{children:"• Use your company logo for professional appearance"}),e.jsx("li",{children:"• Include all legally required information for compliance"}),e.jsx("li",{children:"• Test print templates before mass distribution"}),e.jsx("li",{children:"• Consider employee feedback on readability"})]})]})]})})]})]}),e.jsxs("div",{className:"mt-6",children:[g&&e.jsxs(A,{color:"failure",icon:U,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",g]}),O&&e.jsxs(A,{color:"success",icon:J,className:"mb-4",children:[e.jsx("span",{className:"font-medium",children:"Success!"})," Payroll settings have been saved."]}),e.jsx("div",{className:"flex justify-end",children:e.jsxs(f,{type:"submit",color:"primary",isProcessing:h,disabled:h,children:[e.jsx(J,{className:"mr-2 h-5 w-5"}),"Save Settings"]})})]})]}),e.jsx(ve,{isOpen:le,onClose:()=>W(!1)})]})};export{ke as default};
