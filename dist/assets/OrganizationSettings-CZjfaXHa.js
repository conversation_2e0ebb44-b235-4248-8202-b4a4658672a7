import{h as T,a as B,r as s,j as e,A as N,a6 as g,a7 as W,B as F,i as M,bo as J,k as X,aP as Q,aQ as ee,cg as K,aK as se,a8 as H,P as C,ch as ae,aT as te,aU as ne,al as D,Y as k,ci as le,ah as ie,cj as re,T as L,L as oe,ck as ce}from"./index-C6AV3cVN.js";import{C as A}from"./Card-yj7fueH8.js";import{u as de}from"./currencyFormatter-BsFWv3sX.js";import"./formatters-Cypx7G-j.js";const U=[{value:"retail",label:"Retail Store",icon:e.jsx(J,{className:"h-8 w-8 text-blue-500"}),description:"General merchandise, clothing, electronics, etc."},{value:"restaurant",label:"Restaurant",icon:e.jsx(X,{className:"h-8 w-8 text-red-500"}),description:"Full-service restaurant with table service"},{value:"cafe",label:"Cafe",icon:e.jsx(Q,{className:"h-8 w-8 text-green-500"}),description:"Coffee shop, bakery, or casual dining"},{value:"bar",label:"Bar",icon:e.jsx(ee,{className:"h-8 w-8 text-purple-500"}),description:"Bar, pub, or nightclub"}],me=()=>{const{currentOrganization:a}=T(),{settings:c,refreshSettings:o}=B(),[b,h]=s.useState("retail"),[r,n]=s.useState(!1),[m,p]=s.useState(null),[t,d]=s.useState(null);s.useEffect(()=>{c&&c.business_type&&h(c.business_type)},[c]);const f=async()=>{if(a){n(!0),p(null),d(null);try{const l={vat_rate:12,vat_inclusive:!0,tax_enabled:!0},v={...c,business_type:b,tax_settings:{...c.tax_settings||{},...l}};await K(a.id,v),await o(),d("Business type updated successfully")}catch(l){p(l.message||"Failed to update business type")}finally{n(!1)}}};return e.jsxs(A,{children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Business Type"}),m&&e.jsx(N,{color:"failure",className:"mb-4",children:m}),t&&e.jsx(N,{color:"success",className:"mb-4",children:t}),e.jsxs("div",{className:"mb-4",children:[e.jsx(g,{htmlFor:"businessType",value:"Select your business type"}),e.jsx(W,{id:"businessType",value:b,onChange:l=>h(l.target.value),className:"mt-2",children:U.map(l=>e.jsx("option",{value:l.value,children:l.label},l.value))})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:U.map(l=>e.jsx("div",{className:`p-4 border rounded-lg cursor-pointer ${b===l.value?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>h(l.value),children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"mr-3",children:l.icon}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:l.label}),e.jsx("p",{className:"text-sm text-gray-500",children:l.description})]})]})},l.value))}),e.jsx(F,{color:"blue",onClick:f,disabled:r,children:r?e.jsxs(e.Fragment,{children:[e.jsx(M,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Changes"})]})},ue=({isOwner:a})=>{const{currentOrganization:c}=T(),{settings:o,refreshSettings:b}=B(),h={vat_rate:12,vat_inclusive:!0,tax_enabled:!0},[r,n]=s.useState(h),[m,p]=s.useState(!1),[t,d]=s.useState(null),[f,l]=s.useState(null);s.useEffect(()=>{o&&o.tax_settings&&n({...h,...o.tax_settings})},[o]);const v=j=>{const _=parseFloat(j.target.value);!isNaN(_)&&_>=0&&_<=100&&n(z=>({...z,vat_rate:_}))},y=()=>{n(j=>({...j,vat_inclusive:!j.vat_inclusive}))},P=()=>{n(j=>({...j,tax_enabled:!j.tax_enabled}))},S=async()=>{if(c){p(!0),d(null),l(null);try{const j={...o,tax_settings:r};await K(c.id,j),await b(),l("Tax settings updated successfully")}catch(j){d(j.message||"Failed to update tax settings")}finally{p(!1)}}};return a?e.jsxs(A,{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(se,{className:"mr-2 h-6 w-6 text-blue-500"}),e.jsx("h2",{className:"text-xl font-bold",children:"Tax Settings"})]}),t&&e.jsx(N,{color:"failure",className:"mb-4",children:t}),f&&e.jsx(N,{color:"success",className:"mb-4",children:f}),e.jsxs("div",{className:"space-y-4 mb-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx(g,{htmlFor:"taxEnabled",className:"mb-0",children:"Enable Tax Calculation"}),e.jsx(H,{id:"taxEnabled",checked:r.tax_enabled,onChange:P,label:""})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Enable or disable tax calculation for all transactions"})]}),r.tax_enabled&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx(g,{htmlFor:"vatRate",value:"VAT Rate (%)"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(C,{id:"vatRate",type:"number",min:0,max:100,step:.01,value:r.vat_rate,onChange:v,className:"flex-1"}),e.jsx("span",{className:"ml-2",children:"%"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Standard VAT rate in the Philippines is 12%"})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx(g,{htmlFor:"vatInclusive",className:"mb-0",children:"VAT-Inclusive Pricing"}),e.jsx(H,{id:"vatInclusive",checked:r.vat_inclusive,onChange:y,label:""})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"When enabled, product prices are treated as VAT-inclusive. When disabled, VAT is added on top of the product price."})]})]})]}),e.jsx(F,{color:"blue",onClick:S,disabled:m,children:m?e.jsxs(e.Fragment,{children:[e.jsx(M,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Changes"})]}):null},he=({isOwner:a})=>{const{currentOrganization:c}=T(),{settings:o,refreshSettings:b}=B(),[h,r]=s.useState({chat_enabled:!0}),[n,m]=s.useState(!1),[p,t]=s.useState(null),[d,f]=s.useState(null);s.useEffect(()=>{o&&r({chat_enabled:o.chat_enabled!==!1})},[o]);const l=()=>{r(y=>({...y,chat_enabled:!y.chat_enabled}))},v=async()=>{if(!c){t("No organization selected");return}m(!0),t(null),f(null);try{const y={...o,chat_enabled:h.chat_enabled};await K(c.id,y),await b(),f("Chat settings updated successfully")}catch(y){console.error("Error updating chat settings:",y),t(y.message||"Failed to update chat settings")}finally{m(!1)}};return e.jsxs(A,{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(ae,{className:"mr-2 h-6 w-6 text-blue-500"}),e.jsx("h2",{className:"text-xl font-bold",children:"Chat Settings"})]}),p&&e.jsx(N,{color:"failure",className:"mb-4",children:p}),d&&e.jsx(N,{color:"success",className:"mb-4",children:d}),e.jsx("div",{className:"space-y-4 mb-4",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx(g,{htmlFor:"chatEnabled",className:"mb-0",children:"Enable Chat Functionality"}),e.jsx(H,{id:"chatEnabled",checked:h.chat_enabled,onChange:l,label:"",disabled:!a})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Enable or disable the chat functionality for all users in this organization. When disabled, the chat feature will not be available and no chat-related data will be loaded."}),!h.chat_enabled&&e.jsx(N,{color:"warning",className:"mt-2",children:e.jsxs("p",{className:"text-sm",children:[e.jsx("strong",{children:"Note:"})," Disabling chat will hide the chat menu item and prevent users from accessing chat functionality. Existing chat data will be preserved but not accessible until chat is enabled again."]})})]})}),e.jsx(F,{color:"blue",onClick:v,disabled:n||!a,children:n?e.jsxs(e.Fragment,{children:[e.jsx(M,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Changes"})]})},xe=({isOwner:a})=>{const{currentOrganization:c}=T(),{settings:o,refreshSettings:b}=te(),h=de(),r={is_enabled:!1,points_earning_rate:1,points_redemption_rate:.01,minimum_points_for_redemption:100,points_expiration_months:null},[n,m]=s.useState(r),[p,t]=s.useState(!1),[d,f]=s.useState(null),[l,v]=s.useState(null);s.useEffect(()=>{o&&m({is_enabled:o.is_enabled,points_earning_rate:o.points_earning_rate,points_redemption_rate:o.points_redemption_rate,minimum_points_for_redemption:o.minimum_points_for_redemption,points_expiration_months:o.points_expiration_months})},[o]);const y=u=>{const x=parseFloat(u.target.value);!isNaN(x)&&x>=0&&m(E=>({...E,points_earning_rate:x}))},P=u=>{const x=parseFloat(u.target.value);!isNaN(x)&&x>=0&&m(E=>({...E,points_redemption_rate:x}))},S=u=>{const x=parseInt(u.target.value);!isNaN(x)&&x>=0&&m(E=>({...E,minimum_points_for_redemption:x}))},j=u=>{const x=u.target.value===""?null:parseInt(u.target.value);(x===null||!isNaN(x)&&x>=0)&&m(E=>({...E,points_expiration_months:x}))},_=()=>{m(u=>({...u,is_enabled:!u.is_enabled}))},z=async()=>{if(!c){f("No organization selected");return}t(!0),f(null),v(null);try{await le(c.id,n),await b(),v("Loyalty program settings updated successfully")}catch(u){console.error("Error updating loyalty settings:",u),f(u.message||"Failed to update loyalty settings")}finally{t(!1)}},R=100,$=Math.floor(R*n.points_earning_rate),O=Math.floor(1e3*n.points_redemption_rate);return e.jsxs(A,{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(ne,{className:"mr-2 h-6 w-6 text-blue-500"}),e.jsx("h2",{className:"text-xl font-bold",children:"Loyalty Program Settings"})]}),d&&e.jsx(N,{color:"failure",className:"mb-4",children:d}),l&&e.jsx(N,{color:"success",className:"mb-4",children:l}),e.jsxs("div",{className:"space-y-4 mb-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx(g,{htmlFor:"loyaltyEnabled",className:"mb-0",children:"Enable Loyalty Program"}),e.jsx(H,{id:"loyaltyEnabled",checked:n.is_enabled,onChange:_,disabled:!a,label:""})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Enable or disable the customer loyalty points program"})]}),n.is_enabled&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(g,{htmlFor:"earningRate",className:"mb-0",children:"Points Earning Rate"}),e.jsx(D,{content:"Number of points earned per currency unit spent",children:e.jsx(k,{className:"ml-2 h-4 w-4 text-gray-400"})})]}),e.jsx(C,{id:"earningRate",type:"number",step:"0.001",min:"0",value:n.points_earning_rate,onChange:y,disabled:!a,helperText:`Example: ${$} points earned for a ${h(R)} purchase`})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(g,{htmlFor:"redemptionRate",className:"mb-0",children:"Points Redemption Rate"}),e.jsx(D,{content:"Currency value of each point when redeemed",children:e.jsx(k,{className:"ml-2 h-4 w-4 text-gray-400"})})]}),e.jsx(C,{id:"redemptionRate",type:"number",step:"0.001",min:"0",value:n.points_redemption_rate,onChange:P,disabled:!a,helperText:`Example: 1,000 points = ${h(O)} discount`})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(g,{htmlFor:"minimumPoints",className:"mb-0",children:"Minimum Points for Redemption"}),e.jsx(D,{content:"Minimum number of points required to redeem",children:e.jsx(k,{className:"ml-2 h-4 w-4 text-gray-400"})})]}),e.jsx(C,{id:"minimumPoints",type:"number",min:"0",value:n.minimum_points_for_redemption,onChange:S,disabled:!a})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(g,{htmlFor:"expirationMonths",className:"mb-0",children:"Points Expiration (Months)"}),e.jsx(D,{content:"Number of months after which points expire (leave empty for no expiration)",children:e.jsx(k,{className:"ml-2 h-4 w-4 text-gray-400"})})]}),e.jsx(C,{id:"expirationMonths",type:"number",min:"0",value:n.points_expiration_months===null?"":n.points_expiration_months,onChange:j,disabled:!a,placeholder:"Never expire"})]})]})]}),a&&e.jsx(F,{color:"primary",onClick:z,disabled:p,children:p?e.jsxs(e.Fragment,{children:[e.jsx(M,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Changes"})]})},I=[{code:"PHP",name:"Philippine Peso",symbol:"₱",locale:"en-PH",flag:"🇵🇭"},{code:"USD",name:"US Dollar",symbol:"$",locale:"en-US",flag:"🇺🇸"},{code:"EUR",name:"Euro",symbol:"€",locale:"en-EU",flag:"🇪🇺"},{code:"GBP",name:"British Pound",symbol:"£",locale:"en-GB",flag:"🇬🇧"},{code:"JPY",name:"Japanese Yen",symbol:"¥",locale:"ja-JP",flag:"🇯🇵"},{code:"SGD",name:"Singapore Dollar",symbol:"S$",locale:"en-SG",flag:"🇸🇬"},{code:"HKD",name:"Hong Kong Dollar",symbol:"HK$",locale:"en-HK",flag:"🇭🇰"},{code:"MYR",name:"Malaysian Ringgit",symbol:"RM",locale:"ms-MY",flag:"🇲🇾"},{code:"THB",name:"Thai Baht",symbol:"฿",locale:"th-TH",flag:"🇹🇭"},{code:"IDR",name:"Indonesian Rupiah",symbol:"Rp",locale:"id-ID",flag:"🇮🇩"},{code:"VND",name:"Vietnamese Dong",symbol:"₫",locale:"vi-VN",flag:"🇻🇳"},{code:"KRW",name:"South Korean Won",symbol:"₩",locale:"ko-KR",flag:"🇰🇷"},{code:"CNY",name:"Chinese Yuan",symbol:"¥",locale:"zh-CN",flag:"🇨🇳"},{code:"INR",name:"Indian Rupee",symbol:"₹",locale:"en-IN",flag:"🇮🇳"},{code:"AUD",name:"Australian Dollar",symbol:"A$",locale:"en-AU",flag:"🇦🇺"},{code:"CAD",name:"Canadian Dollar",symbol:"C$",locale:"en-CA",flag:"🇨🇦"},{code:"CHF",name:"Swiss Franc",symbol:"Fr",locale:"de-CH",flag:"🇨🇭"},{code:"NZD",name:"New Zealand Dollar",symbol:"NZ$",locale:"en-NZ",flag:"🇳🇿"},{code:"SEK",name:"Swedish Krona",symbol:"kr",locale:"sv-SE",flag:"🇸🇪"},{code:"NOK",name:"Norwegian Krone",symbol:"kr",locale:"nb-NO",flag:"🇳🇴"},{code:"DKK",name:"Danish Krone",symbol:"kr",locale:"da-DK",flag:"🇩🇰"},{code:"AED",name:"UAE Dirham",symbol:"د.إ",locale:"ar-AE",flag:"🇦🇪"},{code:"SAR",name:"Saudi Riyal",symbol:"﷼",locale:"ar-SA",flag:"🇸🇦"},{code:"ZAR",name:"South African Rand",symbol:"R",locale:"en-ZA",flag:"🇿🇦"},{code:"BRL",name:"Brazilian Real",symbol:"R$",locale:"pt-BR",flag:"🇧🇷"},{code:"MXN",name:"Mexican Peso",symbol:"$",locale:"es-MX",flag:"🇲🇽"},{code:"ARS",name:"Argentine Peso",symbol:"$",locale:"es-AR",flag:"🇦🇷"}],ge=({value:a,onChange:c,disabled:o=!1,error:b,showPreview:h=!0})=>{const[r,n]=s.useState(null);s.useEffect(()=>{const t=I.find(d=>d.code===a);n(t||I[0])},[a]);const m=t=>{const d=I.find(f=>f.code===t);d&&(n(d),c(d))},p=t=>{if(!r)return`${t}`;try{return new Intl.NumberFormat(r.locale,{style:"currency",currency:r.code,minimumFractionDigits:2,maximumFractionDigits:2}).format(t)}catch{return`${r.symbol} ${t.toFixed(2)}`}};return e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(ie,{className:"h-4 w-4 text-gray-500"}),e.jsx(g,{htmlFor:"currency",value:"Currency"})]}),e.jsx(W,{id:"currency",value:a,onChange:t=>m(t.target.value),disabled:o,className:b?"border-red-500":"",children:I.map(t=>e.jsxs("option",{value:t.code,children:[t.flag," ",t.code," - ",t.name," (",t.symbol,")"]},t.code))}),b&&e.jsx(N,{color:"failure",className:"mt-2",children:b})]}),h&&r&&e.jsxs("div",{className:"p-3 bg-gray-50 rounded-lg border",children:[e.jsx("div",{className:"text-sm text-gray-600 mb-2",children:e.jsx("strong",{children:"Preview:"})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-500",children:"Currency Code:"}),e.jsx("div",{className:"font-medium",children:r.code})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-500",children:"Symbol:"}),e.jsx("div",{className:"font-medium text-lg",children:r.symbol})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-500",children:"Sample Amount:"}),e.jsx("div",{className:"font-medium",children:p(1234.56)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-500",children:"Locale:"}),e.jsx("div",{className:"font-medium",children:r.locale})]})]})]})]})},ye=()=>{const{currentOrganization:a,currentMember:c}=T(),{refreshSettings:o}=B(),[b,h]=s.useState(""),[r,n]=s.useState(""),[m,p]=s.useState(""),[t,d]=s.useState(""),[f,l]=s.useState(""),[v,y]=s.useState({}),[P,S]=s.useState(null),[j,_]=s.useState(null),[z,R]=s.useState(!1),[$,O]=s.useState(!0),[u,x]=s.useState(!1);s.useEffect(()=>{a&&(h(a.name),n(a.address||""),p(a.phone||""),d(a.email||""),l(a.website||""),E(),c&&x(c.role==="owner"))},[a,c]);const E=async()=>{if(a)try{O(!0);const i=await re(a.id);y(i.settings)}catch(i){S(i.message||"Failed to load organization settings")}finally{O(!1)}},V=async i=>{if(i.preventDefault(),!a){S("No organization selected");return}S(null),_(null),R(!0);try{await ce(a.id,{name:b,address:r,phone:m,email:t,website:f}),_("Organization updated successfully")}catch(w){S(w.message||"An error occurred while updating the organization")}finally{R(!1)}},G=async i=>{if(i.preventDefault(),!a){S("No organization selected");return}S(null),_(null),R(!0);try{const w={...v,tax_settings:{...v.tax_settings||{},vat_rate:v.tax_rate||0}};await K(a.id,w),await o(),_("Settings updated successfully")}catch(w){S(w.message||"An error occurred while updating settings")}finally{R(!1)}},Y=(i,w)=>{y(q=>({...q,[i]:w}))},Z=i=>{y(w=>({...w,currency:i.code,currency_settings:{code:i.code,name:i.name,symbol:i.symbol,locale:i.locale}}))};return a?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Organization Settings"}),P&&e.jsx(N,{color:"failure",className:"mb-4",children:P}),j&&e.jsx(N,{color:"success",className:"mb-4",children:j}),e.jsxs(L,{"aria-label":"Organization settings tabs",children:[e.jsx(L.Item,{active:!0,title:"General",children:e.jsx("div",{className:"mt-4",children:e.jsxs("form",{onSubmit:V,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(g,{htmlFor:"organizationName",value:"Organization Name"})}),e.jsx(C,{id:"organizationName",type:"text",sizing:"md",required:!0,value:b,onChange:i=>h(i.target.value),className:"form-control form-rounded-xl"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(g,{htmlFor:"organizationAddress",value:"Address"})}),e.jsx(C,{id:"organizationAddress",type:"text",sizing:"md",value:r,onChange:i=>n(i.target.value),className:"form-control form-rounded-xl",placeholder:"Enter your business address"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(g,{htmlFor:"organizationPhone",value:"Phone Number"})}),e.jsx(C,{id:"organizationPhone",type:"tel",sizing:"md",value:m,onChange:i=>p(i.target.value),className:"form-control form-rounded-xl",placeholder:"Enter phone number"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(g,{htmlFor:"organizationEmail",value:"Email Address"})}),e.jsx(C,{id:"organizationEmail",type:"email",sizing:"md",value:t,onChange:i=>d(i.target.value),className:"form-control form-rounded-xl",placeholder:"Enter email address"})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(g,{htmlFor:"organizationWebsite",value:"Website"})}),e.jsx(C,{id:"organizationWebsite",type:"url",sizing:"md",value:f,onChange:i=>l(i.target.value),className:"form-control form-rounded-xl",placeholder:"https://www.example.com"})]}),e.jsx(F,{type:"submit",color:"primary",className:"bg-primary text-white rounded-xl",disabled:z,children:z?"Saving...":"Save Changes"})]})})}),e.jsx(L.Item,{title:"Business Settings",children:e.jsxs("div",{className:"mt-4",children:[$?e.jsx("p",{children:"Loading settings..."}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[e.jsx("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"💰 Currency Configuration"}),e.jsx("p",{className:"text-blue-800 text-sm",children:"Select your business currency. This will be used throughout the system for pricing, sales reports, and financial calculations. Philippines Peso (PHP) is set as the default."})]}),e.jsxs("form",{onSubmit:G,children:[e.jsx("div",{className:"mb-6",children:e.jsx(ge,{value:v.currency||"PHP",onChange:Z,disabled:z,showPreview:!0})}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(g,{htmlFor:"taxRate",value:"Default Tax Rate (%)"})}),e.jsx(C,{id:"taxRate",type:"number",sizing:"md",required:!0,value:v.tax_rate||0,onChange:i=>Y("tax_rate",parseFloat(i.target.value)),className:"form-control form-rounded-xl"})]}),e.jsx(F,{type:"submit",color:"primary",className:"bg-primary text-white rounded-xl",disabled:z,children:z?"Saving...":"Save Settings"})]})]}),u&&e.jsx("div",{className:"mt-8",children:e.jsx(me,{})}),e.jsx("div",{className:"mt-8",children:e.jsx(ue,{isOwner:u})}),e.jsx("div",{className:"mt-8",children:e.jsx(he,{isOwner:u})}),e.jsx("div",{className:"mt-8",children:e.jsx(xe,{isOwner:u})})]})}),e.jsx(L.Item,{title:"Additional Settings",children:e.jsx("div",{className:"mt-4 space-y-4",children:e.jsxs(A,{children:[e.jsx("h3",{className:"text-lg font-medium",children:"Inventory Settings"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"Configure units of measurement and other inventory-related settings."}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(oe,{to:"/settings/uom",children:e.jsx(F,{color:"primary",children:"Manage Units of Measurement"})}),e.jsx("p",{className:"text-sm text-gray-500",children:"Define and manage units of measurement for your products (pieces, boxes, kg, etc.)"})]})]})})})]})]}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(N,{color:"warning",children:"No organization selected. Please select or create an organization."})})};export{ye as default};
