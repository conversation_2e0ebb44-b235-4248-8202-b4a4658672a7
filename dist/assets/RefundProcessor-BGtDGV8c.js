import{h as oe,a as ue,r as x,j as e,P as me,B as N,aq as xe,A as F,an as q,a7 as S,ad as he,_ as d,U as je,bd as _e,H as pe,M as D}from"./index-C6AV3cVN.js";import{C as b}from"./Card-yj7fueH8.js";import{R as H}from"./refund-CcMk-dC8.js";import{u as fe}from"./currencyFormatter-BsFWv3sX.js";import{E as J}from"./EnhancedNumberInput-Bwo1E3yF.js";var r=(t=>(t.FULL="full",t.PARTIAL="partial",t.EXCHANGE="exchange",t.STORE_CREDIT="store_credit",t))(r||{}),Ne=(t=>(t.PENDING="pending",t.APPROVED="approved",t.REJECTED="rejected",t.PROCESSED="processed",t.CANCELLED="cancelled",t))(Ne||{}),_=(t=>(t.DEFECTIVE="defective",t.WRONG_ITEM="wrong_item",t.CUSTOMER_CHANGED_MIND="customer_changed_mind",t.DAMAGED_IN_TRANSIT="damaged_in_transit",t.NOT_AS_DESCRIBED="not_as_described",t.DUPLICATE_ORDER="duplicate_order",t.OTHER="other",t))(_||{}),C=(t=>(t.CASH="cash",t.CARD="card",t.STORE_CREDIT="store_credit",t.ORIGINAL_PAYMENT="original_payment",t))(C||{}),p=(t=>(t.NEW="new",t.USED="used",t.DAMAGED="damaged",t.DEFECTIVE="defective",t))(p||{});const Te=({onRefundCreated:t,onClose:ge})=>{var Q;const{currentOrganization:g}=oe(),{settings:ve,refreshSettings:Z}=ue(),h=fe(),[I,w]=x.useState(""),[M,k]=x.useState([]),[o,U]=x.useState(null),[u,G]=x.useState(null),[c,$]=x.useState(r.PARTIAL),[P,ee]=x.useState(_.CUSTOMER_CHANGED_MIND),[W,z]=x.useState(""),[O,se]=x.useState(C.ORIGINAL_PAYMENT),[m,j]=x.useState([]),[A,L]=x.useState(0),[v,y]=x.useState(!1),[B,f]=x.useState(null),[ae,T]=x.useState(!1);x.useEffect(()=>{Z()},[]),x.useEffect(()=>{if(o&&(u!=null&&u.can_refund))if(c===r.FULL||c===r.EXCHANGE||c===r.STORE_CREDIT){const s=u.eligible_items.map(a=>{const n=o.sale_items.find(i=>i.id===a.sale_item_id),l=n==null?void 0:n.product;return{sale_item_id:a.sale_item_id,product_id:a.product_id,product_name:(l==null?void 0:l.name)||"Unknown Product",sku:(l==null?void 0:l.sku)||"",original_quantity:(n==null?void 0:n.quantity)||0,refund_quantity:a.max_quantity,unit_price:a.unit_price,condition:p.NEW,restore_inventory:!0}});j(s)}else c===r.PARTIAL&&j([])},[c,o,u]);const V=async()=>{if(!(!I.trim()||!g)){y(!0);try{const s=await H.searchOriginalSale(g.id,I);s.success?k(s.data||[]):f(s.error||"Failed to search sales")}catch{f("Failed to search sales")}finally{y(!1)}}},ne=async s=>{if(g){y(!0);try{const a=await H.validateRefund(s.id,g.id);if(G(a),a.can_refund)if(U(s),k([]),w(""),c===r.FULL||c===r.EXCHANGE||c===r.STORE_CREDIT){const n=a.eligible_items.map(l=>{const i=s.sale_items.find(de=>de.id===l.sale_item_id),E=i==null?void 0:i.product;return{sale_item_id:l.sale_item_id,product_id:l.product_id,product_name:(E==null?void 0:E.name)||"Unknown Product",sku:(E==null?void 0:E.sku)||"",original_quantity:(i==null?void 0:i.quantity)||0,refund_quantity:l.max_quantity,unit_price:l.unit_price,condition:p.NEW,restore_inventory:!0}});j(n)}else j([]);else f(a.reasons.join(", "))}catch{f("Failed to validate refund")}finally{y(!1)}}},te=s=>{const a=o.sale_items.find(i=>i.id===s.sale_item_id),n=a==null?void 0:a.product,l={sale_item_id:s.sale_item_id,product_id:s.product_id,product_name:(n==null?void 0:n.name)||"Unknown Product",sku:(n==null?void 0:n.sku)||"",original_quantity:(a==null?void 0:a.quantity)||0,refund_quantity:1,unit_price:s.unit_price,condition:p.NEW,restore_inventory:!0};j([...m,l])},le=s=>{j(m.filter((a,n)=>n!==s))},X=(s,a)=>{const n=[...m],l=u==null?void 0:u.eligible_items.find(i=>i.sale_item_id===n[s].sale_item_id);l&&a<=l.max_quantity&&a>0&&(n[s].refund_quantity=a,j(n))},ie=(s,a)=>{const n=[...m];n[s].condition=a,n[s].restore_inventory=a===p.NEW,j(n)},Y=()=>{const s=m.reduce((n,l)=>n+l.refund_quantity*l.unit_price,0),a=s-A;return{subtotal:s,total:a}},re=async()=>{var s;if(!(!g||!o||m.length===0)){y(!0);try{const a=m.map(i=>({sale_item_id:i.sale_item_id,product_id:i.product_id,quantity:i.refund_quantity,unit_price:i.unit_price,condition:i.condition,restore_inventory:i.restore_inventory,notes:i.notes})),n={original_sale_id:o.id,refund_type:c,reason:P,reason_notes:W,refund_method:O,customer_id:(s=o.customer)==null?void 0:s.id,items:a,restocking_fee:A,requires_approval:Y().total>1e3},l=await H.createRefund(n,g.id);l.success?(t==null||t(l.data),K(),T(!1)):f(l.error||"Failed to create refund")}catch{f("Failed to process refund")}finally{y(!1)}}},K=()=>{U(null),G(null),j([]),w(""),k([]),z(""),L(0),f(null)},{subtotal:ce,total:R}=Y();return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(b,{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Find Original Sale"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(me,{placeholder:"Enter invoice number...",value:I,onChange:s=>w(s.target.value),onKeyPress:s=>s.key==="Enter"&&V(),className:"flex-1"}),e.jsxs(N,{onClick:V,disabled:v,children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),"Search"]})]}),M.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Search Results"}),e.jsx("div",{className:"space-y-2",children:M.map(s=>{var a,n;return e.jsx("div",{className:"p-3 border rounded-lg cursor-pointer hover:bg-gray-50",onClick:()=>ne(s),children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium",children:["Invoice: ",s.invoice_number]}),e.jsxs("p",{className:"text-sm text-gray-600",children:[new Date(s.sale_date).toLocaleDateString()," - ",((a=s.customer)==null?void 0:a.name)||"Walk-in Customer"]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"font-medium",children:h(s.total_amount)}),e.jsxs("p",{className:"text-sm text-gray-600",children:[((n=s.sale_items)==null?void 0:n.length)||0," items"]})]})]})},s.id)})})]})]}),B&&e.jsxs(F,{color:"failure",onDismiss:()=>f(null),children:[e.jsx(q,{className:"h-4 w-4"}),B]}),o&&(u==null?void 0:u.can_refund)&&e.jsxs(e.Fragment,{children:[e.jsxs(b,{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Original Sale Information"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Invoice Number"}),e.jsx("p",{className:"text-sm",children:o.invoice_number})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Sale Date"}),e.jsx("p",{className:"text-sm",children:new Date(o.sale_date).toLocaleDateString()})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Customer"}),e.jsx("p",{className:"text-sm",children:((Q=o.customer)==null?void 0:Q.name)||"Walk-in Customer"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Total Amount"}),e.jsx("p",{className:"text-sm font-medium",children:h(o.total_amount)})]})]})]}),e.jsxs(b,{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Refund Configuration"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Refund Type"}),e.jsxs(S,{value:c,onChange:s=>$(s.target.value),children:[e.jsx("option",{value:r.FULL,children:"Full Refund"}),e.jsx("option",{value:r.PARTIAL,children:"Partial Refund"}),e.jsx("option",{value:r.EXCHANGE,children:"Exchange"}),e.jsx("option",{value:r.STORE_CREDIT,children:"Store Credit"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Reason"}),e.jsxs(S,{value:P,onChange:s=>ee(s.target.value),children:[e.jsx("option",{value:_.DEFECTIVE,children:"Defective"}),e.jsx("option",{value:_.WRONG_ITEM,children:"Wrong Item"}),e.jsx("option",{value:_.CUSTOMER_CHANGED_MIND,children:"Customer Changed Mind"}),e.jsx("option",{value:_.DAMAGED_IN_TRANSIT,children:"Damaged in Transit"}),e.jsx("option",{value:_.NOT_AS_DESCRIBED,children:"Not as Described"}),e.jsx("option",{value:_.DUPLICATE_ORDER,children:"Duplicate Order"}),e.jsx("option",{value:_.OTHER,children:"Other"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Refund Method"}),e.jsxs(S,{value:O,onChange:s=>se(s.target.value),children:[e.jsx("option",{value:C.ORIGINAL_PAYMENT,children:"Original Payment Method"}),e.jsx("option",{value:C.CASH,children:"Cash"}),e.jsx("option",{value:C.CARD,children:"Card"}),e.jsx("option",{value:C.STORE_CREDIT,children:"Store Credit"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Restocking Fee"}),e.jsx(J,{min:"0",step:"0.01",value:A,onChange:s=>L(parseFloat(s.target.value)||0),onBlur:s=>{(s.target.value===""||parseFloat(s.target.value)<0)&&L(0)},autoSelect:!0,preventScrollChange:!0})]})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Reason Notes"}),e.jsx(he,{value:W,onChange:s=>z(s.target.value),placeholder:"Additional details about the refund reason...",rows:3})]})]}),m.length>0&&e.jsxs(b,{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Items to Refund"}),e.jsxs(d,{children:[e.jsxs(d.Head,{children:[e.jsx(d.HeadCell,{children:"Product"}),e.jsx(d.HeadCell,{children:"Quantity"}),e.jsx(d.HeadCell,{children:"Unit Price"}),e.jsx(d.HeadCell,{children:"Condition"}),e.jsx(d.HeadCell,{children:"Restore Inventory"}),e.jsx(d.HeadCell,{children:"Total"}),c===r.PARTIAL&&e.jsx(d.HeadCell,{children:"Actions"})]}),e.jsx(d.Body,{children:m.map((s,a)=>{const n=u==null?void 0:u.eligible_items.find(l=>l.sale_item_id===s.sale_item_id);return e.jsxs(d.Row,{children:[e.jsx(d.Cell,{children:e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:s.product_name}),e.jsxs("p",{className:"text-sm text-gray-600",children:["SKU: ",s.sku]})]})}),e.jsx(d.Cell,{children:c===r.PARTIAL?e.jsxs("div",{children:[e.jsx(J,{min:"0.01",step:"0.01",max:(n==null?void 0:n.max_quantity)||1,value:s.refund_quantity,onChange:l=>X(a,parseFloat(l.target.value)||.01),onBlur:l=>{(l.target.value===""||parseFloat(l.target.value)<=0)&&X(a,.01)},className:"w-20",autoSelect:!0,preventScrollChange:!0}),e.jsxs("p",{className:"text-xs text-gray-500",children:["Max: ",n==null?void 0:n.max_quantity]})]}):e.jsx("span",{className:"font-medium",children:s.refund_quantity})}),e.jsx(d.Cell,{children:h(s.unit_price)}),e.jsx(d.Cell,{children:c===r.PARTIAL?e.jsxs(S,{value:s.condition,onChange:l=>ie(a,l.target.value),className:"w-32",children:[e.jsx("option",{value:p.NEW,children:"New"}),e.jsx("option",{value:p.USED,children:"Used"}),e.jsx("option",{value:p.DAMAGED,children:"Damaged"}),e.jsx("option",{value:p.DEFECTIVE,children:"Defective"})]}):e.jsx("span",{className:"capitalize",children:s.condition})}),e.jsx(d.Cell,{children:c===r.PARTIAL?e.jsx(je,{checked:s.restore_inventory,onChange:l=>{const i=[...m];i[a].restore_inventory=l.target.checked,j(i)}}):e.jsx("span",{children:s.restore_inventory?"Yes":"No"})}),e.jsx(d.Cell,{className:"font-medium",children:h(s.refund_quantity*s.unit_price)}),c===r.PARTIAL&&e.jsx(d.Cell,{children:e.jsx(N,{size:"sm",color:"failure",onClick:()=>le(a),children:e.jsx(_e,{className:"h-4 w-4"})})})]},s.sale_item_id)})})]})]}),c===r.PARTIAL&&e.jsxs(b,{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Select Items to Refund"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Available Items"}),e.jsx("div",{className:"space-y-2",children:u.eligible_items.filter(s=>!m.some(a=>a.sale_item_id===s.sale_item_id)).map(s=>{const a=o.sale_items.find(l=>l.id===s.sale_item_id),n=a==null?void 0:a.product;return e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"font-medium",children:n==null?void 0:n.name}),e.jsxs("p",{className:"text-sm text-gray-600",children:["SKU: ",n==null?void 0:n.sku]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Available: ",s.max_quantity," @ ",h(s.unit_price)," each"]})]}),e.jsxs(N,{size:"sm",onClick:()=>te(s),children:[e.jsx(pe,{className:"h-4 w-4 mr-1"}),"Add"]})]},s.sale_item_id)})})]})]}),(m.length>0||c!==r.PARTIAL)&&e.jsxs(b,{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Refund Summary"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Subtotal:"}),e.jsx("span",{children:h(ce)})]}),A>0&&e.jsxs("div",{className:"flex justify-between text-red-600",children:[e.jsx("span",{children:"Restocking Fee:"}),e.jsxs("span",{children:["-",h(A)]})]}),e.jsxs("div",{className:"flex justify-between text-lg font-bold border-t pt-2",children:[e.jsx("span",{children:"Total Refund:"}),e.jsx("span",{children:h(R)})]})]}),R>1e3&&e.jsxs(F,{color:"warning",className:"mt-4",children:[e.jsx(q,{className:"h-4 w-4"}),"This refund requires manager approval due to the amount exceeding ",h(1e3),"."]}),e.jsxs("div",{className:"flex gap-4 mt-6",children:[e.jsx(N,{onClick:()=>T(!0),disabled:v||c===r.PARTIAL&&m.length===0,className:"flex-1",children:"Process Refund"}),e.jsx(N,{color:"gray",onClick:K,disabled:v,children:"Cancel"})]})]})]}),e.jsxs(D,{show:ae,onClose:()=>T(!1),children:[e.jsx(D.Header,{children:"Confirm Refund"}),e.jsx(D.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"Are you sure you want to process this refund?"}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Refund Details"}),e.jsxs("div",{className:"space-y-1 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Invoice:"}),e.jsx("span",{children:o==null?void 0:o.invoice_number})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Type:"}),e.jsx("span",{className:"capitalize",children:c.replace("_"," ")})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Reason:"}),e.jsx("span",{className:"capitalize",children:P.replace("_"," ")})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Method:"}),e.jsx("span",{className:"capitalize",children:O.replace("_"," ")})]}),e.jsxs("div",{className:"flex justify-between font-medium",children:[e.jsx("span",{children:"Total Amount:"}),e.jsx("span",{children:h(R)})]})]})]}),m.length>0&&e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Items to Refund"}),e.jsx("div",{className:"space-y-2",children:m.map((s,a)=>e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:s.product_name}),e.jsxs("span",{className:"text-gray-600 ml-2",children:["x",s.refund_quantity]})]}),e.jsx("span",{children:h(s.refund_quantity*s.unit_price)})]},a))})]}),R>1e3&&e.jsxs(F,{color:"warning",children:[e.jsx(q,{className:"h-4 w-4"}),"This refund will be pending manager approval."]})]})}),e.jsxs(D.Footer,{children:[e.jsx(N,{onClick:re,disabled:v,children:v?"Processing...":"Confirm Refund"}),e.jsx(N,{color:"gray",onClick:()=>T(!1),disabled:v,children:"Cancel"})]})]})]})};export{Te as R,Ne as a,r as b,C as c};
