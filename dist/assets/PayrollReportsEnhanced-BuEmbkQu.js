import{d as I,h as k,r as d,j as e}from"./index-C6AV3cVN.js";import{g as M,a as F,b as W,c as H}from"./payrollReporting-DzocG2hh.js";import{f as t}from"./formatters-Cypx7G-j.js";import{f as S}from"./index-qirzObrW.js";import"./payroll-j3fcCwK0.js";import"./index-4YOzgfrD.js";import"./typeof-QjJsDpFa.js";import"./index-Cn2wB4rc.js";import"./index-DT2YvziZ.js";const q=()=>{const P=I(),{currentOrganization:h}=k(),[n,D]=d.useState("summary"),[g,R]=d.useState(new Date(new Date().getFullYear(),0,1)),[u,C]=d.useState(new Date),[b,T]=d.useState("html"),[i,y]=d.useState(!1),[j,p]=d.useState(null),[o,N]=d.useState(null),x=async s=>{if(h){y(!0),p(null),N(null),D(s);try{const a={organizationId:h.id,startDate:g,endDate:u,reportType:s,format:b};let r;switch(s){case"summary":r=await H(a);break;case"employee_earnings":r=await W(a);break;case"government_contributions":r=await F(a);break;case"bir_withholding_tax":r=await M(a);break;default:throw new Error("Invalid report type")}r.error?p(r.error):N(r.data)}catch(a){p(a.message)}finally{y(!1)}}},v=(s,a)=>{const r=new Date(s.target.value);a?R(r):C(r)},f=s=>s.toISOString().split("T")[0],G=()=>{if(!o||o.length===0)return e.jsx("div",{className:"p-4 bg-yellow-50 border-l-4 border-yellow-400",children:e.jsx("p",{className:"text-yellow-700",children:"No data available for the selected period."})});const s=o.reduce((l,c)=>l+c.totalGrossPay,0),a=o.reduce((l,c)=>l+c.totalNetPay,0),r=o.reduce((l,c)=>l+c.totalDeductions,0),m=o.reduce((l,c)=>l+c.totalAllowances,0);return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-blue-700",children:"Total Gross Pay"}),e.jsx("p",{className:"text-xl font-bold text-blue-700",children:t(s)})]}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-green-700",children:"Total Net Pay"}),e.jsx("p",{className:"text-xl font-bold text-green-700",children:t(a)})]}),e.jsxs("div",{className:"bg-red-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-red-700",children:"Total Deductions"}),e.jsx("p",{className:"text-xl font-bold text-red-700",children:t(r)})]}),e.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-purple-700",children:"Total Allowances"}),e.jsx("p",{className:"text-xl font-bold text-purple-700",children:t(m)})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm text-left text-gray-500",children:[e.jsx("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Period"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Date Range"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Employees"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Gross Pay"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Deductions"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Net Pay"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Status"})]})}),e.jsx("tbody",{children:o.map(l=>e.jsxs("tr",{className:"bg-white border-b hover:bg-gray-50",children:[e.jsxs("td",{className:"px-6 py-4 font-medium text-gray-900",children:[l.periodName,l.isThirteenthMonth&&" (13th Month)"]}),e.jsxs("td",{className:"px-6 py-4",children:[S(new Date(l.startDate),"MMM d, yyyy")," - ",S(new Date(l.endDate),"MMM d, yyyy")]}),e.jsx("td",{className:"px-6 py-4",children:l.totalEmployees}),e.jsx("td",{className:"px-6 py-4",children:t(l.totalGrossPay)}),e.jsx("td",{className:"px-6 py-4",children:t(l.totalDeductions)}),e.jsx("td",{className:"px-6 py-4 font-medium text-green-600",children:t(l.totalNetPay)}),e.jsx("td",{className:"px-6 py-4",children:e.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${l.status==="paid"?"bg-green-100 text-green-800":l.status==="approved"?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"}`,children:l.status.charAt(0).toUpperCase()+l.status.slice(1)})})]},l.periodId))}),e.jsx("tfoot",{children:e.jsxs("tr",{className:"font-semibold text-gray-900",children:[e.jsx("th",{scope:"row",className:"px-6 py-3 text-base",colSpan:3,children:"Totals"}),e.jsx("td",{className:"px-6 py-3",children:t(s)}),e.jsx("td",{className:"px-6 py-3",children:t(r)}),e.jsx("td",{className:"px-6 py-3 text-green-600",children:t(a)}),e.jsx("td",{className:"px-6 py-3"})]})})]})})]})},_=()=>{if(!o||!o.items||o.items.length===0)return e.jsx("div",{className:"p-4 bg-yellow-50 border-l-4 border-yellow-400",children:e.jsx("p",{className:"text-yellow-700",children:"No data available for the selected period."})});const{items:s,summary:a}=o;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-blue-700",children:"Total SSS"}),e.jsx("p",{className:"text-xl font-bold text-blue-700",children:t(a.totalSSS)})]}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-green-700",children:"Total PhilHealth"}),e.jsx("p",{className:"text-xl font-bold text-green-700",children:t(a.totalPhilHealth)})]}),e.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-yellow-700",children:"Total Pag-IBIG"}),e.jsx("p",{className:"text-xl font-bold text-yellow-700",children:t(a.totalPagibig)})]}),e.jsxs("div",{className:"bg-red-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-red-700",children:"Total Withholding Tax"}),e.jsx("p",{className:"text-xl font-bold text-red-700",children:t(a.totalWithholdingTax)})]}),e.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-purple-700",children:"Total Contributions"}),e.jsx("p",{className:"text-xl font-bold text-purple-700",children:t(a.totalContributions+a.totalWithholdingTax)})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm text-left text-gray-500",children:[e.jsx("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Employee"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"SSS"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"PhilHealth"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Pag-IBIG"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Withholding Tax"}),e.jsx("th",{scope:"col",className:"px-6 py-3",children:"Total"})]})}),e.jsx("tbody",{children:s.map(r=>{const m=Number(r.sss_contribution||0),l=Number(r.philhealth_contribution||0),c=Number(r.pagibig_contribution||0),w=Number(r.withholding_tax||0),E=m+l+c+w;return e.jsxs("tr",{className:"bg-white border-b hover:bg-gray-50",children:[e.jsxs("td",{className:"px-6 py-4 font-medium text-gray-900",children:[r.employee.first_name," ",r.employee.last_name,e.jsx("div",{className:"text-xs text-gray-500",children:r.employee.employee_number||"No ID"})]}),e.jsx("td",{className:"px-6 py-4",children:t(m)}),e.jsx("td",{className:"px-6 py-4",children:t(l)}),e.jsx("td",{className:"px-6 py-4",children:t(c)}),e.jsx("td",{className:"px-6 py-4",children:t(w)}),e.jsx("td",{className:"px-6 py-4 font-medium",children:t(E)})]},r.id)})}),e.jsx("tfoot",{children:e.jsxs("tr",{className:"font-semibold text-gray-900",children:[e.jsx("th",{scope:"row",className:"px-6 py-3 text-base",children:"Totals"}),e.jsx("td",{className:"px-6 py-3",children:t(a.totalSSS)}),e.jsx("td",{className:"px-6 py-3",children:t(a.totalPhilHealth)}),e.jsx("td",{className:"px-6 py-3",children:t(a.totalPagibig)}),e.jsx("td",{className:"px-6 py-3",children:t(a.totalWithholdingTax)}),e.jsx("td",{className:"px-6 py-3",children:t(a.totalContributions+a.totalWithholdingTax)})]})})]})})]})};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Payroll Reports"}),e.jsx("p",{className:"text-gray-500",children:"Generate and view payroll reports"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Report Filters"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",className:"w-full rounded-lg border border-gray-300 p-2.5",value:f(g),onChange:s=>v(s,!0)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",className:"w-full rounded-lg border border-gray-300 p-2.5",value:f(u),onChange:s=>v(s,!1)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Report Format"}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 p-2.5",value:b,onChange:s=>T(s.target.value),children:[e.jsx("option",{value:"html",children:"HTML"}),e.jsx("option",{value:"pdf",children:"PDF"}),e.jsx("option",{value:"csv",children:"CSV"}),e.jsx("option",{value:"excel",children:"Excel"})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:`border rounded-lg p-4 hover:bg-gray-50 cursor-pointer ${n==="summary"?"bg-blue-50 border-blue-300":""}`,onClick:()=>x("summary"),children:[e.jsx("h3",{className:"text-lg font-medium mb-2",children:"Summary Report"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Generate a summary report of payroll periods showing total amounts for each period."}),e.jsx("button",{className:`px-4 py-2 rounded ${n==="summary"?"bg-blue-600 text-white":"bg-gray-200 text-gray-800"} hover:bg-blue-700 hover:text-white`,onClick:s=>{s.stopPropagation(),x("summary")},disabled:i,children:i&&n==="summary"?"Generating...":"Generate Report"})]}),e.jsxs("div",{className:`border rounded-lg p-4 hover:bg-gray-50 cursor-pointer ${n==="government_contributions"?"bg-blue-50 border-blue-300":""}`,onClick:()=>x("government_contributions"),children:[e.jsx("h3",{className:"text-lg font-medium mb-2",children:"Government Contributions"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Generate a report of government contributions (SSS, PhilHealth, Pag-IBIG, and Withholding Tax)."}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{className:`px-4 py-2 rounded ${n==="government_contributions"?"bg-blue-600 text-white":"bg-gray-200 text-gray-800"} hover:bg-blue-700 hover:text-white`,onClick:s=>{s.stopPropagation(),x("government_contributions")},disabled:i,children:i&&n==="government_contributions"?"Generating...":"Generate Report"}),e.jsx("button",{className:"px-4 py-2 rounded bg-green-600 text-white hover:bg-green-700",onClick:s=>{s.stopPropagation(),P("/payroll/reports/government-contributions")},children:"Enhanced View"})]})]})]}),j&&e.jsx("div",{className:"p-4 mb-6 bg-red-50 border-l-4 border-red-400",children:e.jsx("p",{className:"text-red-700",children:j})}),i&&e.jsx("div",{className:"flex justify-center items-center p-12",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}),!i&&o&&e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold",children:n==="summary"?"Payroll Summary Report":n==="government_contributions"?"Government Contributions Report":"Report Results"}),e.jsxs("button",{className:"px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 flex items-center",onClick:()=>window.print(),children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Print/Download"]})]}),n==="summary"&&G(),n==="government_contributions"&&_()]})]})};export{q as default};
