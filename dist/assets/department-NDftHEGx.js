import{s}from"./index-C6AV3cVN.js";const d=async(a,e)=>{try{let r=s.from("departments").select("*",{count:"exact"}).eq("organization_id",a);(e==null?void 0:e.isActive)!==void 0&&(r=r.eq("is_active",e.isActive)),e!=null&&e.searchQuery&&(r=r.ilike("name",`%${e.searchQuery}%`)),e!=null&&e.sortBy?r=r.order(e.sortBy,{ascending:e.sortOrder==="asc"}):r=r.order("name",{ascending:!0}),e!=null&&e.limit&&(r=r.limit(e.limit)),e!=null&&e.offset&&(r=r.range(e.offset,e.offset+(e.limit||10)-1));const{data:t,error:c,count:n}=await r;return c?(console.error("Error fetching departments:",c),{departments:[],count:0,error:c.message}):{departments:t,count:n||0}}catch(r){return console.error("Error in getDepartments:",r),{departments:[],count:0,error:r.message}}},u=async(a,e)=>{try{const{data:r,error:t}=await s.from("departments").insert({...e,organization_id:a}).select().single();return t?(console.error("Error creating department:",t),{error:t.message}):{department:r}}catch(r){return console.error("Error in createDepartment:",r),{error:r.message}}},i=async(a,e,r)=>{try{const{data:t,error:c}=await s.from("departments").update(r).eq("organization_id",a).eq("id",e).select().single();return c?(console.error("Error updating department:",c),{error:c.message}):{department:t}}catch(t){return console.error("Error in updateDepartment:",t),{error:t.message}}},l=async(a,e)=>{try{const{error:r}=await s.from("departments").delete().eq("organization_id",a).eq("id",e);return r?(console.error("Error deleting department:",r),{success:!1,error:r.message}):{success:!0}}catch(r){return console.error("Error in deleteDepartment:",r),{success:!1,error:r.message}}};export{u as c,l as d,d as g,i as u};
