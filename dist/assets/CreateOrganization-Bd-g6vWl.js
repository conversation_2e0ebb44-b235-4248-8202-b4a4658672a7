import{d as y,b as z,h as b,r as s,j as e,A as m,a6 as j,P as N,B as v,cf as w}from"./index-C6AV3cVN.js";import{C}from"./Card-yj7fueH8.js";const S=()=>{const n=y(),{user:r}=z(),{organizations:o,setCurrentOrganization:x}=b(),[i,f]=s.useState(""),[u,t]=s.useState(null),[d,l]=s.useState(null),[g,h]=s.useState(!1);s.useEffect(()=>{r||n("/auth/login")},[r,n]);const p=async c=>{if(c.preventDefault(),!r){t("You must be logged in to create an organization"),n("/auth/login");return}if(!i.trim()){t("Organization name cannot be empty");return}t(null),l(null),h(!0);try{console.log("Creating organization:",i),l("Creating organization... This may take a moment.");const a=await w(i,r.id);console.log("Organization created:",a),l(`Organization "${a.name}" created successfully!`),x(a),setTimeout(()=>{n("/")},1500)}catch(a){console.error("Error creating organization:",a),a.message.includes("violates row-level security policy")?t("Permission error: Unable to create organization due to security policies. Please contact support."):t(a.message||"An error occurred while creating the organization")}finally{h(!1)}};return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"max-w-md mx-auto",children:e.jsxs(C,{children:[e.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Create New Organization"}),e.jsx("p",{className:"text-sm text-gray-500 mb-6",children:"Each organization is a separate tenant with isolated data"}),u&&e.jsx(m,{color:"failure",className:"mb-4",children:u}),d&&e.jsx(m,{color:"success",className:"mb-4",children:d}),o&&o.length>0&&e.jsxs(m,{color:"info",className:"mb-4",children:[e.jsx("h3",{className:"font-medium",children:"You already have organizations"}),e.jsxs("p",{className:"text-sm",children:["You currently have ",o.length," organization(s). Creating a new one will give you a separate tenant with isolated data."]})]}),e.jsxs("form",{onSubmit:p,children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"mb-2 block",children:[e.jsx(j,{htmlFor:"name",value:"Organization Name"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"This will be the name of your business in the system"})]}),e.jsx(N,{id:"name",type:"text",sizing:"md",required:!0,value:i,onChange:c=>f(c.target.value),className:"form-control form-rounded-xl",placeholder:"Enter your organization name"})]}),e.jsx(v,{type:"submit",color:"primary",className:"w-full bg-primary text-white rounded-xl",disabled:g,children:g?"Creating...":"Create Organization"})]})]})})})};export{S as default};
