import{d as L,h as Q,r as a,j as e,bo as w,B as h,a0 as $,A as U,J as W,Q as J,P as M,a7 as V,W as q,t as G,i as K,_ as r,aj as X,k as Y,e as Z}from"./index-C6AV3cVN.js";import{C as ee}from"./Card-yj7fueH8.js";import{b as se}from"./purchaseOrder-DppPMsdd.js";import{c as re,d as ae}from"./formatters-Cypx7G-j.js";import{u as te}from"./currencyFormatter-BsFWv3sX.js";import{E as le}from"./EmptyState-743bE0hR.js";import{P as ne}from"./Pagination-CVEzfctr.js";const xe=()=>{const u=L(),{currentOrganization:m}=Q(),O=te(),[P,p]=a.useState(!0),[f,x]=a.useState(null),[c,H]=a.useState([]),[t,j]=a.useState(""),[l,g]=a.useState("all"),[v,y]=a.useState(!1),[C,N]=a.useState(1),[d,R]=a.useState(10),o=async(s=t,n=l)=>{if(m){p(!0),x(null);try{const i={};s&&(i.searchQuery=s),n!=="all"&&(i.status=n);const{purchaseOrders:F,error:b}=await se(m.id,i);b?x(b):H(F||[])}catch(i){console.error("Error fetching purchase orders:",i),x(i.message||"An error occurred while fetching purchase orders")}finally{p(!1),y(!1)}}};a.useEffect(()=>{o()},[m]);const E=s=>{s.preventDefault(),o(t,l)},_=s=>{const n=s.target.value;g(n),o(t,n)},k=()=>{y(!0),o(t,l)},A=()=>{u("/purchases/orders/create")},D=s=>{switch(s){case"sent":return"info";case"partially_received":return"warning";case"received":return"success";case"cancelled":return"failure";default:return"gray"}},I=s=>{switch(s){case"draft":return"Draft";case"sent":return"Sent";case"partially_received":return"Partially Received";case"received":return"Received";case"cancelled":return"Cancelled";default:return s.charAt(0).toUpperCase()+s.slice(1)}},T=Math.ceil(c.length/d),S=C*d,z=S-d,B=c.slice(z,S);return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(ee,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold flex items-center",children:[e.jsx(w,{className:"mr-2 h-6 w-6"}),"Purchase Orders"]}),e.jsx("p",{className:"text-gray-500",children:"Create and manage purchase orders sent to suppliers."})]}),e.jsxs(h,{color:"primary",onClick:A,className:"px-4 py-2 font-medium shadow-sm hover:shadow-md transition-all",size:"lg",children:[e.jsx($,{className:"mr-2 h-5 w-5"}),"Create Purchase Order"]})]}),f&&e.jsx(U,{color:"failure",icon:W,className:"mb-4",children:f}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-3 mb-4",children:[e.jsx("form",{onSubmit:E,className:"flex-grow",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(J,{className:"w-5 h-5 text-gray-500"})}),e.jsx(M,{type:"search",placeholder:"Search by order number or supplier...",value:t,onChange:s=>j(s.target.value),className:"w-full pl-10"}),e.jsx(h,{type:"submit",color:"blue",size:"sm",className:"absolute right-1 top-1 bottom-1",children:"Search"})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"min-w-[170px]",children:e.jsxs(V,{value:l,onChange:_,icon:q,children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"draft",children:"Draft"}),e.jsx("option",{value:"sent",children:"Sent"}),e.jsx("option",{value:"partially_received",children:"Partially Received"}),e.jsx("option",{value:"received",children:"Received"}),e.jsx("option",{value:"cancelled",children:"Cancelled"})]})}),e.jsx(h,{color:"light",onClick:k,disabled:v,children:e.jsx(G,{className:`h-5 w-5 ${v?"animate-spin":""}`})})]})]}),P?e.jsxs("div",{className:"flex justify-center items-center p-12",children:[e.jsx(K,{size:"xl"}),e.jsx("span",{className:"ml-2",children:"Loading purchase orders..."})]}):c.length===0?e.jsx(le,{icon:e.jsx(w,{className:"h-12 w-12"}),title:"No purchase orders found",description:t||l!=="all"?"Try changing your search terms or filters":"Use the 'Create Purchase Order' button above to get started",actionText:t||l!=="all"?"Clear filters":void 0,onAction:t||l!=="all"?()=>{j(""),g("all"),o("","all")}:void 0}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(r,{hoverable:!0,children:[e.jsxs(r.Head,{children:[e.jsx(r.HeadCell,{children:"Order Number"}),e.jsx(r.HeadCell,{children:"Supplier"}),e.jsx(r.HeadCell,{children:"Status"}),e.jsx(r.HeadCell,{children:"Date"}),e.jsx(r.HeadCell,{children:"Total"}),e.jsx(r.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(r.Body,{className:"divide-y",children:B.map(s=>e.jsxs(r.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800 hover:bg-gray-50",onClick:()=>u(`/purchases/orders/${s.id}`),style:{cursor:"pointer"},children:[e.jsx(r.Cell,{className:"font-medium",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(X,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{children:s.order_number})]})}),e.jsx(r.Cell,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Y,{className:"mr-2 h-4 w-4 text-gray-500"}),e.jsx("span",{children:s.supplier_name})]})}),e.jsx(r.Cell,{children:e.jsx(Z,{color:D(s.status),children:I(s.status)})}),e.jsx(r.Cell,{children:e.jsx("div",{title:ae(s.order_date),children:re(s.order_date)})}),e.jsx(r.Cell,{className:"font-medium",children:O(s.total_amount)}),e.jsx(r.Cell,{children:e.jsx(h,{color:"light",size:"xs",onClick:n=>{n.stopPropagation(),u(`/purchases/orders/${s.id}`)},children:"View"})})]},s.id))})]})}),c.length>0&&e.jsx(ne,{currentPage:C,totalPages:T,itemsPerPage:d,totalItems:c.length,onPageChange:N,onItemsPerPageChange:s=>{R(s),N(1)},itemName:"orders"})]})})};export{xe as default};
