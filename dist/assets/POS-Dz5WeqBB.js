import{r as t,j as e,B as n,aN as je,y as pe,aO as O,M as C,aP as be,a8 as V,z as Ne,aQ as ye,p as T,h as ve,a as we,P as F,af as Ce,A as Se,Q,i as Te,e as Oe,a0 as G,a3 as K,ai as _e,aR as ke,C as ze,aS as He}from"./index-C6AV3cVN.js";import{g as Ie}from"./product-Ca8DWaNR.js";import{u as Be}from"./currencyFormatter-BsFWv3sX.js";import{C as Pe,a as qe,D as De,b as Me}from"./ClearCartConfirmation-63Y8FzVT.js";import"./formatters-Cypx7G-j.js";import"./customer-COogBrXM.js";import"./Pagination-CVEzfctr.js";import"./EnhancedNumberInput-Bwo1E3yF.js";const Ae=({onSelectTable:x,selectedTable:r})=>{const[l,o]=t.useState(!1),u=[{id:"1",name:"Table 1",seats:2,status:"available"},{id:"2",name:"Table 2",seats:4,status:"occupied"},{id:"3",name:"Table 3",seats:4,status:"available"},{id:"4",name:"Table 4",seats:6,status:"reserved"},{id:"5",name:"Table 5",seats:2,status:"available"},{id:"6",name:"Table 6",seats:8,status:"available"},{id:"7",name:"Bar 1",seats:1,status:"occupied"},{id:"8",name:"Bar 2",seats:1,status:"available"},{id:"9",name:"Bar 3",seats:1,status:"available"},{id:"10",name:"Bar 4",seats:1,status:"available"}],h=i=>{x(i),o(!1)},b=i=>{switch(i){case"available":return"bg-green-100 text-green-800 border-green-200";case"occupied":return"bg-red-100 text-red-800 border-red-200";case"reserved":return"bg-yellow-100 text-yellow-800 border-yellow-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}};return e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs(n,{color:"light",className:"w-full flex items-center justify-between",onClick:()=>o(!0),children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(je,{className:"mr-2 h-5 w-5"}),r?r.name:"Select Table"]}),r&&e.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${b(r.status)}`,children:r.status.charAt(0).toUpperCase()+r.status.slice(1)})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs(n,{color:"light",size:"sm",children:[e.jsx(pe,{className:"mr-2 h-4 w-4"}),"Split Bill"]}),e.jsxs(n,{color:"light",size:"sm",children:[e.jsx(O,{className:"mr-2 h-4 w-4"}),"Order History"]})]})]}),e.jsxs(C,{show:l,onClose:()=>o(!1),size:"lg",children:[e.jsx(C.Header,{children:"Select Table"}),e.jsx(C.Body,{children:e.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4",children:u.map(i=>e.jsxs("div",{className:`border rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow ${i.status!=="available"?"opacity-50":""}`,onClick:()=>i.status==="available"&&h(i),children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsx("h3",{className:"font-medium",children:i.name}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${b(i.status)}`,children:i.status.charAt(0).toUpperCase()+i.status.slice(1)})]}),e.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:[i.seats," ",i.seats===1?"seat":"seats"]})]},i.id))})}),e.jsx(C.Footer,{children:e.jsx(n,{color:"gray",onClick:()=>o(!1),children:"Cancel"})})]})]})},Ee=({onToggleTakeaway:x,isTakeaway:r})=>{const[l,o]=t.useState(!1),u=()=>{x(!r)};return e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(be,{className:"mr-2 h-5 w-5 text-gray-600"}),e.jsx("span",{className:"text-sm font-medium",children:"Order Type"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-sm mr-2",children:r?"Takeaway":"Dine In"}),e.jsx(V,{checked:r,onChange:u,label:""})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs(n,{color:"light",size:"sm",children:[e.jsx(Ne,{className:"mr-2 h-4 w-4"}),"Quick Items"]}),e.jsxs(n,{color:"light",size:"sm",children:[e.jsx(O,{className:"mr-2 h-4 w-4"}),"Order History"]})]})]})})},Le=({onToggleTabMode:x,isTabMode:r})=>{const[l,o]=t.useState(!1),u=()=>{x(!r)};return e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ye,{className:"mr-2 h-5 w-5 text-gray-600"}),e.jsx("span",{className:"text-sm font-medium",children:"Order Type"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-sm mr-2",children:r?"Running Tab":"Direct Payment"}),e.jsx(V,{checked:r,onChange:u,label:""})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs(n,{color:"light",size:"sm",children:[e.jsx(T,{className:"mr-2 h-4 w-4"}),"Happy Hour"]}),e.jsxs(n,{color:"light",size:"sm",children:[e.jsx(O,{className:"mr-2 h-4 w-4"}),"Order History"]})]})]})})},We=()=>{var L,$,R;const{currentOrganization:x,currentMember:r}=ve(),{settings:l}=we(),o=Be(),u=(r==null?void 0:r.role)==="owner",[h,b]=t.useState("retail"),[i,W]=t.useState([]),[_,k]=t.useState([]),[Y,J]=t.useState([]),[N,X]=t.useState("all"),[g,Z]=t.useState(""),[ee,z]=t.useState(!0),[H,f]=t.useState(null),[m,j]=t.useState([]),[S,se]=t.useState(null),[y,I]=t.useState(""),p=t.useRef(null),[v,ae]=t.useState({amount:0,isPercentage:!1}),[te,B]=t.useState(!1),[le,re]=t.useState(null),[ie,ne]=t.useState(!1),[ce,oe]=t.useState(!1);t.useEffect(()=>{l&&l.business_type&&b(l.business_type)},[l]),t.useEffect(()=>{p.current&&p.current.focus()},[]),t.useEffect(()=>{(async()=>{if(x){z(!0),f(null);try{const{products:a,error:d}=await Ie(x.id,{limit:100});if(d)f(d);else{W(a),k(a);const c=Array.from(new Set(a.map(fe=>{var U;return((U=fe.category)==null?void 0:U.name)||"Uncategorized"})));J(["all",...c])}}catch(a){f(a.message||"Failed to fetch products")}finally{z(!1)}}})()},[x]),t.useEffect(()=>{let s=i;g&&(s=s.filter(a=>a.name.toLowerCase().includes(g.toLowerCase())||a.sku&&a.sku.toLowerCase().includes(g.toLowerCase())||a.barcode&&a.barcode.toLowerCase().includes(g.toLowerCase()))),N!=="all"&&(s=s.filter(a=>{var d;return(((d=a.category)==null?void 0:d.name)||"Uncategorized")===N})),k(s)},[i,g,N]);const de=s=>{if(s.preventDefault(),!y)return;const a=i.find(d=>d.barcode===y);a?(P(a),I("")):(f(`Product with barcode ${y} not found`),setTimeout(()=>f(null),3e3)),p.current&&p.current.focus()},P=s=>{j(a=>a.find(c=>c.product.id===s.id)?a.map(c=>c.product.id===s.id?{...c,quantity:c.quantity+1}:c):[...a,{id:s.id,product:s,quantity:1}])},q=(s,a)=>{if(a<=0){D(s);return}j(d=>d.map(c=>c.id===s?{...c,quantity:a}:c))},D=s=>{j(a=>a.filter(d=>d.id!==s))},xe=(s,a)=>{j(d=>d.map(c=>c.id===s?{...c,notes:a}:c))},w=()=>m.reduce((s,a)=>s+a.product.unit_price*a.quantity,0),M=()=>{const s=w();return v.isPercentage?s*(v.amount/100):v.amount},A=()=>w()-M(),E=()=>{const s=(l==null?void 0:l.tax_settings)||{vat_rate:12,vat_inclusive:!0,tax_enabled:!0};if(!s.tax_enabled)return 0;const a=A();return s.vat_inclusive?a-a/(1+s.vat_rate/100):a*(s.vat_rate/100)},ue=()=>{const s=(l==null?void 0:l.tax_settings)||{vat_inclusive:!0,tax_enabled:!0},a=A();return!s.tax_enabled||s.vat_inclusive?a:a+E()},me=(s,a)=>{ae({amount:s,isPercentage:a})},he=()=>{m.length>0&&B(!0)},ge=()=>{j([])};return e.jsxs("div",{className:"w-full h-full bg-gray-50",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 h-full",children:[e.jsx("div",{className:"lg:col-span-3 p-4",children:e.jsxs("div",{className:"bg-white rounded-lg shadow h-full flex flex-col",children:[e.jsxs("div",{className:"p-4 border-b border-gray-200",children:[e.jsx("div",{className:"flex justify-between items-center mb-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-sm font-medium mr-2",children:"Business Type:"}),e.jsx("span",{className:"bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full capitalize",children:h}),u&&e.jsx(n,{size:"xs",color:"light",pill:!0,className:"ml-2",onClick:()=>window.open("/organization/settings","_blank"),title:"Change in Organization Settings",children:e.jsx(T,{className:"h-3 w-3"})})]})}),e.jsx("form",{onSubmit:de,className:"mb-4",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(F,{ref:p,type:"text",placeholder:"Scan barcode or enter product code",value:y,onChange:s=>I(s.target.value),className:"flex-1",icon:Ce}),e.jsx(n,{type:"submit",color:"blue",children:"Add"})]})}),H&&e.jsx(Se,{color:"failure",className:"mb-4",children:H}),e.jsx("div",{children:e.jsx(F,{type:"text",placeholder:"Search products by name, SKU, or barcode",value:g,onChange:s=>Z(s.target.value),className:"w-full",icon:Q})})]}),e.jsx("div",{className:"border-b border-gray-200 bg-gray-50",children:e.jsx("div",{className:"flex overflow-x-auto",children:Y.map(s=>e.jsx("button",{className:`px-4 py-3 text-sm font-medium whitespace-nowrap ${N===s?"text-blue-600 border-b-2 border-blue-600 bg-white":"text-gray-500 hover:text-gray-700 hover:bg-gray-100"}`,onClick:()=>X(s),children:s==="all"?"All Products":s},s))})}),e.jsx("div",{className:"flex-grow overflow-y-auto p-4",children:ee?e.jsx("div",{className:"flex justify-center items-center h-full",children:e.jsx(Te,{size:"xl"})}):_.length===0?e.jsxs("div",{className:"text-center py-8 h-full flex flex-col justify-center",children:[e.jsx(Q,{className:"mx-auto h-12 w-12 text-gray-300"}),e.jsx("p",{className:"text-gray-500 mt-2",children:"No products found"})]}):e.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4",children:_.map(s=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md cursor-pointer transition-all hover:border-blue-300 hover:scale-105",onClick:()=>P(s),children:[e.jsxs("div",{className:"relative",children:[s.image_url?e.jsx("img",{src:s.image_url,alt:s.name,className:"w-full h-32 object-cover rounded-t-lg"}):e.jsx("div",{className:"w-full h-32 bg-gray-100 flex items-center justify-center rounded-t-lg",children:e.jsx(T,{className:"h-10 w-10 text-gray-300"})}),s.stock_quantity!==null&&e.jsx("div",{className:"absolute top-2 right-2",children:e.jsx(Oe,{color:s.stock_quantity>0?"success":"failure",size:"sm",children:s.stock_quantity})})]}),e.jsxs("div",{className:"p-3",children:[e.jsx("h3",{className:"text-sm font-medium truncate mb-1",children:s.name}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm font-bold text-blue-600",children:o(s.unit_price)}),e.jsx(n,{size:"xs",color:"light",className:"p-1",children:e.jsx(G,{className:"h-3 w-3"})})]})]})]},s.id))})})]})}),e.jsxs("div",{className:"bg-white shadow-lg border-l border-gray-200 h-screen sticky top-0 right-0 flex flex-col",children:[e.jsxs("div",{className:"flex-shrink-0 bg-white z-10 shadow-sm",children:[e.jsxs("div",{className:"flex justify-between items-center p-3 border-b border-gray-200",children:[e.jsx("h2",{className:"text-lg font-bold",children:"Current Sale"}),e.jsxs(n,{color:"failure",size:"xs",onClick:he,className:"px-2",children:[e.jsx(K,{className:"h-3 w-3 mr-1"}),"Clear"]})]}),e.jsxs("div",{className:"border-b border-gray-200 p-2",children:[e.jsx("div",{className:"mb-2",children:e.jsx(Pe,{onSelectCustomer:se,selectedCustomerId:S==null?void 0:S.id})}),e.jsxs("div",{className:"text-xs",children:[h==="restaurant"&&e.jsx(Ae,{onSelectTable:re,selectedTable:le}),h==="cafe"&&e.jsx(Ee,{onToggleTakeaway:ne,isTakeaway:ie}),h==="bar"&&e.jsx(Le,{onToggleTabMode:oe,isTabMode:ce})]})]})]}),e.jsx("div",{className:"flex-grow overflow-y-auto p-2 pb-4",children:m.length===0?e.jsxs("div",{className:"text-center py-6 h-full flex flex-col justify-center items-center",children:[e.jsx(_e,{className:"mx-auto h-12 w-12 text-gray-300"}),e.jsx("p",{className:"mt-2 text-gray-500",children:"Your cart is empty"})]}):e.jsx("div",{className:"space-y-2 pb-2",children:m.map(s=>e.jsx("div",{className:"border-b border-gray-100 p-2 text-sm",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("h3",{className:"font-medium text-gray-800 mr-2",children:s.product.name}),e.jsxs("div",{className:"flex items-center bg-gray-50 rounded-md ml-auto",children:[e.jsx(n,{size:"xs",color:"light",className:"rounded-l-md rounded-r-none border-0 p-1",onClick:()=>q(s.id,s.quantity-1),children:e.jsx(ke,{className:"h-3 w-3"})}),e.jsx("span",{className:"px-1 min-w-[1.5rem] text-center text-xs font-medium",children:s.quantity}),e.jsx(n,{size:"xs",color:"light",className:"rounded-r-md rounded-l-none border-0 p-1",onClick:()=>q(s.id,s.quantity+1),children:e.jsx(G,{className:"h-3 w-3"})})]})]}),e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 mt-1",children:[e.jsxs("span",{children:[o(s.product.unit_price)," each"]}),e.jsx("span",{className:"font-bold text-gray-800",children:o(s.product.unit_price*s.quantity)})]}),s.notes&&e.jsx("div",{className:"mt-1 text-xs italic text-gray-500 bg-gray-50 p-1 rounded",children:s.notes})]}),e.jsxs("div",{className:"ml-2 flex flex-col gap-1",children:[e.jsx(qe,{itemId:s.id,initialNotes:s.notes,onSave:xe}),e.jsx(n,{size:"xs",color:"failure",className:"p-1",onClick:()=>D(s.id),children:e.jsx(K,{className:"h-3 w-3"})})]})]})},s.id))})}),e.jsxs("div",{className:"border-t border-gray-200 bg-gray-50 p-2 flex-shrink-0 sticky bottom-0 shadow-md z-10",children:[e.jsxs("div",{className:"space-y-1 mb-2",children:[e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-600",children:"Subtotal"}),e.jsx("span",{children:o(w())})]}),e.jsxs("div",{className:"flex justify-between items-center text-xs",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-gray-600 mr-1",children:"Discount"}),e.jsx(De,{subtotal:w(),onApplyDiscount:me,currentDiscount:v})]}),e.jsxs("span",{className:"text-red-500",children:["-",o(M())]})]}),e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-600",children:(L=l==null?void 0:l.tax_settings)!=null&&L.tax_enabled?`VAT ${(($=l==null?void 0:l.tax_settings)==null?void 0:$.vat_rate)||12}%${(R=l==null?void 0:l.tax_settings)!=null&&R.vat_inclusive?" (incl.)":""}`:"No Tax"}),e.jsx("span",{children:o(E())})]}),e.jsxs("div",{className:"flex justify-between font-bold text-base pt-1 border-t border-gray-200",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"text-blue-600",children:o(ue())})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs(n,{color:"success",size:"sm",disabled:m.length===0,className:"py-1",children:[e.jsx(ze,{className:"mr-1 h-4 w-4"}),"Cash"]}),e.jsxs(n,{color:"info",size:"sm",disabled:m.length===0,className:"py-1",children:[e.jsx(He,{className:"mr-1 h-4 w-4"}),"Card"]})]})]})]})]}),e.jsx(Me,{show:te,onClose:()=>B(!1),onConfirm:ge,itemCount:m.length})]})};export{We as default};
