import{s as n}from"./index-C6AV3cVN.js";var v=(a=>(a.OPERATIONAL="operational",a.ADMINISTRATIVE="administrative",a.FINANCIAL="financial",a.MAINTENANCE="maintenance",a.PROFESSIONAL_SERVICES="professional_services",a.UTILITIES="utilities",a.OFFICE_SUPPLIES="office_supplies",a.TRAVEL="travel",a))(v||{}),p=(a=>(a.WEEKLY="weekly",a.BI_WEEKLY="bi_weekly",a.MONTHLY="monthly",a.QUARTERLY="quarterly",a.SEMI_ANNUAL="semi_annual",a.ANNUAL="annual",a))(p||{}),A=(a=>(a.PURCHASE_RECEIPT="purchase_receipt",a.PAYROLL="payroll",a.UTILITY_BILL="utility_bill",a.GOVERNMENT_REMITTANCE="government_remittance",a.LOAN_REPAYMENT="loan_repayment",a.MANUAL_ENTRY="manual_entry",a.RECURRING_EXPENSE="recurring_expense",a.OFFICE_SUPPLIES="office_supplies",a.MAINTENANCE="maintenance",a.PROFESSIONAL_SERVICES="professional_services",a.RENT="rent",a.INSURANCE="insurance",a.SUBSCRIPTION="subscription",a))(A||{}),N=(a=>(a.DRAFT="draft",a.OPEN="open",a.PARTIALLY_PAID="partially_paid",a.PAID="paid",a.CANCELLED="cancelled",a))(N||{}),m=(a=>(a.PENDING="pending",a.APPROVED="approved",a.REJECTED="rejected",a.REQUIRES_HIGHER_APPROVAL="requires_higher_approval",a))(m||{});const b=async(a,e)=>{try{let r=n.from("expense_types").select("*").eq("organization_id",a).order("name");e!=null&&e.category&&(r=r.eq("category",e.category)),(e==null?void 0:e.is_active)!==void 0&&(r=r.eq("is_active",e.is_active)),(e==null?void 0:e.is_recurring_type)!==void 0&&(r=r.eq("is_recurring_type",e.is_recurring_type)),e!=null&&e.search&&(r=r.or(`name.ilike.%${e.search}%,code.ilike.%${e.search}%,description.ilike.%${e.search}%`));const{data:s,error:t}=await r;return t?(console.error("Error fetching expense types:",t),{success:!1,error:t.message}):{success:!0,data:s||[]}}catch(r){return console.error("Error in getExpenseTypes:",r),{success:!1,error:r.message}}},O=async(a,e,r)=>{try{const{data:s,error:t}=await n.from("expense_types").insert({organization_id:a,...e,created_by:r}).select().single();return t?(console.error("Error creating expense type:",t),{success:!1,error:t.message}):{success:!0,data:s,message:"Expense type created successfully"}}catch(s){return console.error("Error in createExpenseType:",s),{success:!1,error:s.message}}},T=async(a,e)=>{try{const{data:r,error:s}=await n.from("expense_types").update(e).eq("id",a).select().single();return s?(console.error("Error updating expense type:",s),{success:!1,error:s.message}):{success:!0,data:r,message:"Expense type updated successfully"}}catch(r){return console.error("Error in updateExpenseType:",r),{success:!1,error:r.message}}},M=async a=>{try{const{error:e}=await n.from("expense_types").update({is_active:!1}).eq("id",a);return e?(console.error("Error deleting expense type:",e),{success:!1,error:e.message}):{success:!0,message:"Expense type deleted successfully"}}catch(e){return console.error("Error in deleteExpenseType:",e),{success:!1,error:e.message}}},D=async(a,e)=>{try{let r=n.from("recurring_expenses").select(`
        *,
        expense_type:expense_types(*),
        supplier:suppliers(id, name, contact_person, email, phone),
        employee:employees(id, first_name, last_name, employee_number)
      `).eq("organization_id",a).order("next_due_date");if(e!=null&&e.expense_type_id&&(r=r.eq("expense_type_id",e.expense_type_id)),e!=null&&e.supplier_id&&(r=r.eq("supplier_id",e.supplier_id)),e!=null&&e.frequency&&(r=r.eq("frequency",e.frequency)),(e==null?void 0:e.is_active)!==void 0&&(r=r.eq("is_active",e.is_active)),e!=null&&e.due_soon){const c=new Date;c.setDate(c.getDate()+30),r=r.lte("next_due_date",c.toISOString().split("T")[0])}e!=null&&e.search&&(r=r.or(`name.ilike.%${e.search}%,description.ilike.%${e.search}%`));const{data:s,error:t}=await r;return t?(console.error("Error fetching recurring expenses:",t),{success:!1,error:t.message}):{success:!0,data:s||[]}}catch(r){return console.error("Error in getRecurringExpenses:",r),{success:!1,error:r.message}}},P=async(a,e,r)=>{try{const s=new Date(e.start_date),t=R(s,e.frequency),{data:c,error:o}=await n.from("recurring_expenses").insert({organization_id:a,...e,next_due_date:t.toISOString().split("T")[0],created_by:r}).select().single();return o?(console.error("Error creating recurring expense:",o),{success:!1,error:o.message}):{success:!0,data:c,message:"Recurring expense created successfully"}}catch(s){return console.error("Error in createRecurringExpense:",s),{success:!1,error:s.message}}},S=async(a,e)=>{try{const{data:r,error:s}=await n.from("recurring_expenses").update(e).eq("id",a).select().single();return s?(console.error("Error updating recurring expense:",s),{success:!1,error:s.message}):{success:!0,data:r,message:"Recurring expense updated successfully"}}catch(r){return console.error("Error in updateRecurringExpense:",r),{success:!1,error:r.message}}},U=async(a,e)=>{try{let r=n.from("payables").select(`
        *,
        expense_type:expense_types(*),
        recurring_expense:recurring_expenses(*),
        supplier:suppliers(id, name, contact_person, email, phone),
        employee:employees(id, first_name, last_name, employee_number),
        payments:payable_payments(*)
      `).eq("organization_id",a).order("created_at",{ascending:!1});if(e!=null&&e.status&&(r=r.eq("status",e.status)),e!=null&&e.approval_status&&(r=r.eq("approval_status",e.approval_status)),e!=null&&e.source_type&&(r=r.eq("source_type",e.source_type)),e!=null&&e.expense_type_id&&(r=r.eq("expense_type_id",e.expense_type_id)),e!=null&&e.supplier_id&&(r=r.eq("supplier_id",e.supplier_id)),e!=null&&e.department&&(r=r.eq("department",e.department)),e!=null&&e.project_code&&(r=r.eq("project_code",e.project_code)),e!=null&&e.date_from&&(r=r.gte("invoice_date",e.date_from)),e!=null&&e.date_to&&(r=r.lte("invoice_date",e.date_to)),e!=null&&e.amount_from&&(r=r.gte("amount",e.amount_from)),e!=null&&e.amount_to&&(r=r.lte("amount",e.amount_to)),e!=null&&e.overdue){const o=new Date().toISOString().split("T")[0];r=r.lt("due_date",o).in("status",["open","partially_paid"])}e!=null&&e.search&&(r=r.or(`reference_number.ilike.%${e.search}%,notes.ilike.%${e.search}%`));const{data:s,error:t}=await r;return t?(console.error("Error fetching enhanced payables:",t),{success:!1,error:t.message}):{success:!0,data:(s||[]).map(o=>{var u;return{...o,total_paid:((u=o.payments)==null?void 0:u.reduce((x,_)=>x+_.amount_paid,0))||0}})}}catch(r){return console.error("Error in getEnhancedPayables:",r),{success:!1,error:r.message}}},k=async(a,e,r)=>{try{const s=e.amount-(e.withholding_tax_amount||0),{data:t,error:c}=await n.from("payables").insert({organization_id:a,...e,balance:s,status:N.OPEN,approval_status:m.PENDING,created_by:r}).select().single();return c?(console.error("Error creating enhanced payable:",c),{success:!1,error:c.message}):{success:!0,data:t,message:"Payable created successfully"}}catch(s){return console.error("Error in createEnhancedPayable:",s),{success:!1,error:s.message}}},R=(a,e)=>{const r=new Date(a);switch(e){case p.WEEKLY:r.setDate(r.getDate()+7);break;case p.BI_WEEKLY:r.setDate(r.getDate()+14);break;case p.MONTHLY:r.setMonth(r.getMonth()+1);break;case p.QUARTERLY:r.setMonth(r.getMonth()+3);break;case p.SEMI_ANNUAL:r.setMonth(r.getMonth()+6);break;case p.ANNUAL:r.setFullYear(r.getFullYear()+1);break;default:r.setMonth(r.getMonth()+1)}return r},Y=async(a,e)=>{try{const{data:r,error:s}=await n.from("payables").update({approval_status:m.APPROVED,approved_by:e,approved_at:new Date().toISOString()}).eq("id",a).select().single();return s?(console.error("Error approving payable:",s),{success:!1,error:s.message}):{success:!0,data:r,message:"Payable approved successfully"}}catch(r){return console.error("Error in approvePayable:",r),{success:!1,error:r.message}}},C=async(a,e,r)=>{try{const{data:s,error:t}=await n.from("payables").update({approval_status:m.REJECTED,approved_by:e,approved_at:new Date().toISOString(),rejection_reason:r}).eq("id",a).select().single();return t?(console.error("Error rejecting payable:",t),{success:!1,error:t.message}):{success:!0,data:s,message:"Payable rejected successfully"}}catch(s){return console.error("Error in rejectPayable:",s),{success:!1,error:s.message}}},j=async a=>D(a,{is_active:!0,due_soon:!0}),z=async a=>{try{const{data:e,error:r}=await n.from("payables").select("id").eq("organization_id",a).eq("approval_status","pending"),s=new Date().toISOString().split("T")[0],{data:t,error:c}=await n.from("payables").select("id").eq("organization_id",a).in("status",["open","partially_paid"]).lt("due_date",s),o=new Date;o.setDate(o.getDate()+30);const{data:u,error:x}=await n.from("recurring_expenses").select("id").eq("organization_id",a).eq("is_active",!0).lte("next_due_date",o.toISOString().split("T")[0]),_=new Date;_.setMonth(_.getMonth()-6);const{data:l,error:q}=await n.from("payables").select(`
        category,
        amount,
        expense_type:expense_types(category)
      `).eq("organization_id",a).gte("created_at",_.toISOString()),y=new Map;let E=0;l&&l.forEach(d=>{var h;const i=((h=d.expense_type)==null?void 0:h.category)||d.category||"uncategorized",g=d.amount||0;y.set(i,(y.get(i)||0)+g),E+=g});const I=Array.from(y.entries()).map(([d,i])=>({category:d.replace(/_/g," ").replace(/\b\w/g,g=>g.toUpperCase()),amount:i,percentage:E>0?i/E*100:0}));return{success:!0,data:{total_pending_approvals:(e==null?void 0:e.length)||0,total_overdue_payments:(t==null?void 0:t.length)||0,upcoming_recurring_expenses:(u==null?void 0:u.length)||0,monthly_expense_trend:[],expense_by_category:I,vendor_performance:[]}}}catch(e){return console.error("Error in getExpenseDashboardData:",e),{success:!1,error:e.message}}};export{m as A,v as E,N as P,p as R,j as a,b,O as c,M as d,D as e,S as f,z as g,P as h,A as i,k as j,U as k,Y as l,C as r,T as u};
