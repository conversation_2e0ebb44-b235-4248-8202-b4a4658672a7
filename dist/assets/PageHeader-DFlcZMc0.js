import{j as e}from"./index-C6AV3cVN.js";const l=({title:r,description:s,icon:t,actions:a})=>e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[t&&e.jsx("div",{className:"text-gray-700 dark:text-gray-300",children:t}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:r}),s&&e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:s})]})]}),a&&e.jsx("div",{className:"flex gap-2",children:a})]});export{l as P};
