import{h as ge,r,j as t,I as U,a6 as C,P,Q as ye,a7 as je,B as y,t as ve,Z as we,C as Se,i as Y,A as be,_ as n,e as _,a1 as Ce,b0 as _e,M as D}from"./index-C6AV3cVN.js";import{C as R}from"./Card-yj7fueH8.js";import{g as De,a as V,R as Re}from"./refund-CcMk-dC8.js";import{d as q}from"./formatters-Cypx7G-j.js";import{u as $e}from"./currencyFormatter-BsFWv3sX.js";import{S as Ne}from"./SaleReceipt-jLjyCjTQ.js";import{P as Pe}from"./PageHeader-DFlcZMc0.js";import{P as He}from"./Pagination-CVEzfctr.js";import{b as Te}from"./excelExport-BekG2cQR.js";import"./inventoryTransaction-1UXV5RDN.js";import"./floatInventory-k_pEQeIK.js";const qe=()=>{const{currentOrganization:s}=ge(),o=$e(),Q=e=>{if(!e||e.refundStatus==="none")return null;switch(e.refundStatus){case"fully_refunded":return t.jsx(_,{color:"failure",size:"sm",children:"Fully Refunded"});case"partially_refunded":return t.jsxs(_,{color:"warning",size:"sm",children:["Partially Refunded (",e.refundPercentage.toFixed(1),"%)"]});case"pending_refund":return t.jsx(_,{color:"gray",size:"sm",children:"Refund Pending"});default:return null}},G=e=>!e||e.refundStatus==="none"?null:t.jsxs("div",{className:"text-xs text-gray-600 mt-1",children:[e.refundCount," refund",e.refundCount!==1?"s":""," •",o(e.totalRefunded)," refunded"]}),[Ee,Z]=r.useState([]),[J,j]=r.useState([]),[K,H]=r.useState(!1),[T,x]=r.useState(null),[$,O]=r.useState(""),[h,X]=r.useState("today"),[v,z]=r.useState(new Date().toISOString().split("T")[0]),[f,ee]=r.useState(new Date().toISOString().split("T")[0]),[te,N]=r.useState(!1),[w,E]=r.useState(null),[se,k]=r.useState(!1),[F,I]=r.useState(1),[S,ae]=r.useState(10),L=async()=>{if(s){H(!0),x(null);try{let e=new Date;const a=new Date;switch(h){case"today":e=new Date,e.setHours(0,0,0,0);break;case"yesterday":e=new Date,e.setDate(e.getDate()-1),e.setHours(0,0,0,0),a.setDate(a.getDate()-1),a.setHours(23,59,59,999);break;case"week":e=new Date,e.setDate(e.getDate()-7),e.setHours(0,0,0,0);break;case"month":e=new Date,e.setMonth(e.getMonth()-1),e.setHours(0,0,0,0);break;case"custom":e=new Date(v),e.setHours(0,0,0,0),new Date(f).setHours(23,59,59,999);break}const{sales:i,error:m}=await De(s.id,{startDate:e.toISOString(),endDate:h==="custom"?new Date(f+"T23:59:59").toISOString():a.toISOString(),limit:100,sortBy:"created_at",sortOrder:"desc"});if(m)throw new Error(m);Z(i),await ne(i)}catch(e){console.error("Error fetching sales:",e),x(e.message||"Failed to fetch sales")}finally{H(!1)}}},ne=async e=>{if(!s||!e.length){j(e);return}try{const a=await Re.getRefunds(s.id,{limit:1e3});if(a.success&&a.data){const i=a.data,m=new Map;i.forEach(d=>{const l=d.original_sale_id;m.has(l)||m.set(l,{refunds:[],totalRefunded:0,refundCount:0,hasProcessedRefunds:!1});const c=m.get(l);c.refunds.push(d),c.refundCount++,d.status==="processed"&&(c.totalRefunded+=Number(d.total_amount),c.hasProcessedRefunds=!0)});const b=e.map(d=>{const l=m.get(d.id);if(l){const c=d.total_amount>0?l.totalRefunded/d.total_amount*100:0;let p="none";return c>=100?p="fully_refunded":c>0?p="partially_refunded":l.refundCount>0&&(p="pending_refund"),{...d,refundData:{...l,refundPercentage:c,refundStatus:p}}}return{...d,refundData:{refunds:[],totalRefunded:0,refundCount:0,hasProcessedRefunds:!1,refundPercentage:0,refundStatus:"none"}}});j(b)}else j(e)}catch(a){console.error("Error fetching refund data:",a),j(e)}};r.useEffect(()=>{L()},[h,v,f,s]);const u=J.filter(e=>{if(!$)return!0;const a=$.toLowerCase();return e.invoice_number&&e.invoice_number.toLowerCase().includes(a)||e.customer&&e.customer.name&&e.customer.name.toLowerCase().includes(a)}),re=u.length,oe=u.reduce((e,a)=>e+a.total_amount,0),ie=Math.ceil(u.length/S),M=F*S,de=M-S,le=u.slice(de,M),ce=async e=>{if(s){k(!0),E(null),N(!0);try{const{sale:a,error:i}=await V(s.id,e);if(i)throw new Error(i);E(a)}catch(a){console.error("Error fetching sale:",a),x(a.message||"Failed to fetch sale")}finally{k(!1)}}},ue=async e=>{if(s)try{const{sale:a,error:i}=await V(s.id,e);if(i)throw new Error(i);B(a)}catch(a){console.error("Error fetching sale for print:",a),x(a.message||"Failed to fetch sale for printing")}},me=()=>{w&&B(w)},he=()=>{if(u.length===0){x("No sales data to export");return}const e=(()=>{switch(h){case"today":return"Today";case"yesterday":return"Yesterday";case"week":return"Last 7 Days";case"month":return"Last 30 Days";case"custom":return`${v} to ${f}`;default:return"All Time"}})();Te(u,e)},B=e=>{var A;if(!e||!s)return;const a=window.open("","_blank","width=800,height=600");if(!a)return;const i=q(e.sale_date),m=o(e.subtotal),b=o(e.tax_amount),d=o(e.total_amount),l=e.discount_amount>0?o(e.discount_amount):"",c=e.loyalty_points_discount>0?o(e.loyalty_points_discount):"",p=e.cash_tendered?o(e.cash_tendered):"",pe=e.change_amount>0?o(e.change_amount):"",xe=e.cashier?((e.cashier.first_name||"")+" "+(e.cashier.last_name||"")).trim()||"Unknown User":"",fe=`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt - ${e.invoice_number}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              font-size: 14px;
              line-height: 1.4;
            }
            .receipt {
              max-width: 400px;
              margin: 0 auto;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #000;
              padding-bottom: 10px;
              margin-bottom: 15px;
            }
            .company-name {
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 5px;
            }
            .receipt-info {
              margin-bottom: 15px;
            }
            .receipt-info div {
              margin-bottom: 3px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin: 15px 0;
            }
            th, td {
              padding: 8px 4px;
              text-align: left;
              border-bottom: 1px solid #ddd;
            }
            th {
              font-weight: bold;
              background-color: #f5f5f5;
            }
            .text-right {
              text-align: right;
            }
            .text-center {
              text-align: center;
            }
            .summary {
              margin-top: 15px;
            }
            .summary-row {
              display: flex;
              justify-content: space-between;
              margin: 5px 0;
            }
            .total-row {
              border-top: 2px solid #000;
              padding-top: 8px;
              margin-top: 10px;
              font-weight: bold;
              font-size: 16px;
            }
            .footer {
              text-align: center;
              margin-top: 20px;
              font-size: 12px;
              color: #666;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="receipt">
            <!-- Header -->
            <div class="header">
              <div class="company-name">${(s==null?void 0:s.name)||"Your Business"}</div>
              ${s!=null&&s.address?`<div>${s.address}</div>`:""}
              ${s!=null&&s.phone||s!=null&&s.email?`
                <div style="margin-top: 5px;">
                  ${s!=null&&s.phone?`Phone: ${s.phone}`:""}
                  ${s!=null&&s.phone&&(s!=null&&s.email)?" | ":""}
                  ${s!=null&&s.email?`Email: ${s.email}`:""}
                </div>
              `:""}
              ${s!=null&&s.website?`<div>${s.website}</div>`:""}
            </div>

            <!-- Receipt Info -->
            <div class="receipt-info">
              <div><strong>Receipt: ${e.invoice_number}</strong></div>
              <div>Date: ${i}</div>
              ${e.customer?`<div>Customer: ${e.customer.name}</div>`:""}
              ${e.cashier?`<div>Cashier: ${xe}</div>`:""}
            </div>

            <!-- Items Table -->
            <table>
              <thead>
                <tr>
                  <th>Item</th>
                  <th class="text-right">Qty</th>
                  <th class="text-right">Price</th>
                  <th class="text-right">Total</th>
                </tr>
              </thead>
              <tbody>
                ${(A=e.items)==null?void 0:A.map(g=>{var W;return`
                  <tr>
                    <td>${((W=g.product)==null?void 0:W.name)||"Product"}</td>
                    <td class="text-right">${g.quantity} pcs</td>
                    <td class="text-right">${o(g.unit_price)}</td>
                    <td class="text-right">${o(g.unit_price*g.quantity)}</td>
                  </tr>
                `}).join("")}
              </tbody>
            </table>

            <!-- Summary -->
            <div class="summary">
              <div class="summary-row">
                <span>Subtotal</span>
                <span>${m}</span>
              </div>
              ${e.discount_amount>0?`
                <div class="summary-row">
                  <span>Discount</span>
                  <span>-${l}</span>
                </div>
              `:""}
              ${e.loyalty_points_discount&&e.loyalty_points_discount>0?`
                <div class="summary-row">
                  <span>Loyalty Points (${e.loyalty_points_used} pts)</span>
                  <span>-${c}</span>
                </div>
              `:""}
              <div class="summary-row">
                <span>Tax</span>
                <span>${b}</span>
              </div>
              <div class="summary-row total-row">
                <span>Total</span>
                <span>${d}</span>
              </div>
            </div>

            ${e.payment_method==="cash"&&e.cash_tendered?`
              <div class="summary" style="border-top: 1px solid #ccc; padding-top: 10px; margin-top: 15px;">
                <div class="summary-row">
                  <span>Cash Tendered:</span>
                  <span>${p}</span>
                </div>
                ${e.change_amount&&e.change_amount>0?`
                  <div class="summary-row">
                    <span>Change:</span>
                    <span>${pe}</span>
                  </div>
                `:""}
              </div>
            `:""}

            <!-- Footer -->
            <div class="footer">
              <p>Thank you for your business!</p>
              <p>Payment Method: ${e.payment_method}</p>
              ${e.notes?`<p style="font-style: italic;">${e.notes}</p>`:""}
              <p style="margin-top: 15px;">Generated on ${new Date().toLocaleDateString()}</p>
            </div>
          </div>

          <script>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              };
            };
          <\/script>
        </body>
      </html>
    `;a.document.write(fe),a.document.close()};return t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx(Pe,{title:"Sales History",description:"View and manage your sales records",icon:t.jsx(U,{className:"h-8 w-8"})}),t.jsx(R,{className:"mb-6",children:t.jsxs("div",{className:"flex flex-wrap gap-4 items-end",children:[t.jsxs("div",{className:"flex-1 min-w-[200px]",children:[t.jsx(C,{htmlFor:"search",value:"Search"}),t.jsx(P,{id:"search",type:"text",icon:ye,placeholder:"Search by invoice # or customer",value:$,onChange:e=>O(e.target.value)})]}),t.jsxs("div",{className:"w-40",children:[t.jsx(C,{htmlFor:"dateFilter",value:"Date Range"}),t.jsxs(je,{id:"dateFilter",value:h,onChange:e=>X(e.target.value),children:[t.jsx("option",{value:"today",children:"Today"}),t.jsx("option",{value:"yesterday",children:"Yesterday"}),t.jsx("option",{value:"week",children:"Last 7 Days"}),t.jsx("option",{value:"month",children:"Last 30 Days"}),t.jsx("option",{value:"custom",children:"Custom Range"})]})]}),h==="custom"&&t.jsxs(t.Fragment,{children:[t.jsxs("div",{children:[t.jsx(C,{htmlFor:"startDate",value:"Start Date"}),t.jsx(P,{id:"startDate",type:"date",value:v,onChange:e=>z(e.target.value)})]}),t.jsxs("div",{children:[t.jsx(C,{htmlFor:"endDate",value:"End Date"}),t.jsx(P,{id:"endDate",type:"date",value:f,onChange:e=>ee(e.target.value)})]})]}),t.jsxs(y,{color:"blue",onClick:L,children:[t.jsx(ve,{className:"mr-2 h-4 w-4"}),"Refresh"]}),t.jsxs(y,{color:"light",onClick:he,children:[t.jsx(we,{className:"mr-2 h-4 w-4"}),"Export"]})]})}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[t.jsx(R,{children:t.jsxs("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-3 rounded-full bg-blue-100 mr-4",children:t.jsx(U,{className:"h-6 w-6 text-blue-600"})}),t.jsxs("div",{children:[t.jsx("h5",{className:"text-gray-500 text-sm",children:"Total Sales"}),t.jsx("p",{className:"text-2xl font-bold",children:re})]})]})}),t.jsx(R,{children:t.jsxs("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-3 rounded-full bg-green-100 mr-4",children:t.jsx(Se,{className:"h-6 w-6 text-green-600"})}),t.jsxs("div",{children:[t.jsx("h5",{className:"text-gray-500 text-sm",children:"Total Revenue"}),t.jsx("p",{className:"text-2xl font-bold",children:o(oe)})]})]})})]}),t.jsxs(R,{children:[K?t.jsx("div",{className:"flex justify-center py-8",children:t.jsx(Y,{size:"xl"})}):T?t.jsx(be,{color:"failure",children:T}):u.length===0?t.jsx("div",{className:"text-center py-8 text-gray-500",children:"No sales found for the selected period"}):t.jsx("div",{className:"overflow-x-auto",children:t.jsxs(n,{hoverable:!0,children:[t.jsxs(n.Head,{children:[t.jsx(n.HeadCell,{children:"Invoice #"}),t.jsx(n.HeadCell,{children:"Date"}),t.jsx(n.HeadCell,{children:"Customer"}),t.jsx(n.HeadCell,{children:"Cashier"}),t.jsx(n.HeadCell,{children:"Payment"}),t.jsx(n.HeadCell,{children:"Amount"}),t.jsx(n.HeadCell,{children:"Refund Status"}),t.jsx(n.HeadCell,{children:"Actions"})]}),t.jsx(n.Body,{className:"divide-y",children:le.map(e=>t.jsxs(n.Row,{className:"bg-white",children:[t.jsx(n.Cell,{className:"font-medium",children:e.invoice_number}),t.jsx(n.Cell,{children:q(e.created_at)}),t.jsx(n.Cell,{children:e.customer?e.customer.name:"Walk-in Customer"}),t.jsx(n.Cell,{children:e.cashier?t.jsx("div",{className:"text-sm",children:t.jsx("div",{className:"font-medium",children:`${e.cashier.first_name||""} ${e.cashier.last_name||""}`.trim()||"Unknown User"})}):t.jsx("span",{className:"text-gray-400 text-sm",children:"Unknown"})}),t.jsx(n.Cell,{children:t.jsx(_,{color:e.payment_method==="cash"?"success":"info",children:e.payment_method==="cash"?"Cash":"Card"})}),t.jsx(n.Cell,{className:"font-medium",children:t.jsxs("div",{children:[o(e.total_amount),e.refundData&&e.refundData.refundStatus!=="none"&&t.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:["Net: ",o(e.total_amount-e.refundData.totalRefunded)]})]})}),t.jsx(n.Cell,{children:t.jsxs("div",{children:[Q(e.refundData),G(e.refundData)]})}),t.jsx(n.Cell,{children:t.jsxs("div",{className:"flex gap-2",children:[t.jsxs(y,{size:"xs",color:"light",onClick:()=>ce(e.id),children:[t.jsx(Ce,{className:"mr-1 h-3 w-3"}),"View"]}),t.jsxs(y,{size:"xs",color:"light",onClick:()=>ue(e.id),children:[t.jsx(_e,{className:"mr-1 h-3 w-3"}),"Print"]})]})})]},e.id))})]})}),u.length>0&&t.jsx(He,{currentPage:F,totalPages:ie,itemsPerPage:S,totalItems:u.length,onPageChange:I,onItemsPerPageChange:e=>{ae(e),I(1)},itemName:"sales"})]}),t.jsxs(D,{show:te,onClose:()=>N(!1),children:[t.jsx(D.Header,{children:"Sale Receipt"}),t.jsx(D.Body,{children:se?t.jsx("div",{className:"flex justify-center py-8",children:t.jsx(Y,{size:"xl"})}):w?t.jsx("div",{children:t.jsx(Ne,{sale:w,showPrintButton:!0,onPrint:me})}):t.jsx("div",{className:"text-center py-4 text-gray-500",children:"Receipt not found"})}),t.jsx(D.Footer,{children:t.jsx(y,{color:"gray",onClick:()=>N(!1),children:"Close"})})]})]})};export{qe as default};
