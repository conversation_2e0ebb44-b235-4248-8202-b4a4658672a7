import{h as V,aT as Z,r as l,j as e,B as d,K as T,M as m,A as D,J as L,P as ee,Q as se,i as ae,a0 as A,e as te,aX as le,aY as ne,aU as re,a_ as ie,aj as ce,ad as oe,a as de,a$ as I,p as me,C as xe,a6 as B,a3 as $}from"./index-C6AV3cVN.js";import{g as he}from"./customer-COogBrXM.js";import{P as fe}from"./Pagination-CVEzfctr.js";import{u as je}from"./currencyFormatter-BsFWv3sX.js";import{E as _}from"./EnhancedNumberInput-Bwo1E3yF.js";const be=({onSelectCustomer:i,selectedCustomerId:o,onCreateCustomer:a})=>{const{currentOrganization:x}=V(),{settings:t}=Z(),[c,j]=l.useState(!1),[h,p]=l.useState([]),[f,g]=l.useState([]),[u,y]=l.useState(""),[P,b]=l.useState(!1),[w,v]=l.useState(null),[N,n]=l.useState(null),[S,M]=l.useState({}),[H,z]=l.useState(1),[k,K]=l.useState(15);l.useEffect(()=>{if(!o)n(null);else if(h.length>0){const s=h.find(r=>r.id===o);s&&n(s)}},[o,h]),l.useEffect(()=>{(async()=>{if(!(!x||!c)){b(!0),v(null);try{const{customers:r,error:C}=await he(x.id,{limit:100});if(C)v(C);else if(p(r),g(r),t!=null&&t.is_enabled){const O=r.filter(q=>q.loyalty_eligible);U(O)}}catch(r){v(r.message||"Failed to fetch customers")}finally{b(!1)}}})()},[x,c,t==null?void 0:t.is_enabled]);const U=async s=>{if(!(!x||s.length===0))try{const{pointsMap:r,error:C}=await ie(x.id,s.map(O=>O.id));if(C){console.error("Error fetching loyalty points:",C);return}M(r)}catch(r){console.error("Error fetching loyalty points:",r)}};l.useEffect(()=>{if(!u){g(h);return}const s=h.filter(r=>r.name&&r.name.toLowerCase().includes(u.toLowerCase())||r.email&&r.email.toLowerCase().includes(u.toLowerCase())||r.phone&&r.phone.includes(u));g(s)},[h,u]),l.useEffect(()=>{const s=r=>{r.key==="Escape"&&c&&E()};return c&&document.addEventListener("keydown",s),()=>{document.removeEventListener("keydown",s)}},[c]);const R=()=>{j(!0)},E=()=>{j(!1)},G=s=>{n(s),i(s),j(!1)},J=()=>{n(null),i(null)},Q=s=>s?s.name||"Unnamed Customer":"Select Customer",W=Math.ceil(f.length/k),F=H*k,X=F-k,Y=f.slice(X,F);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs(d,{color:"light",className:"w-full flex items-center justify-between",onClick:R,"data-testid":"customer-selector-button",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(T,{className:"mr-2 h-5 w-5"}),N?Q(N):"Select Customer"]}),N&&e.jsx(d,{size:"xs",color:"gray",onClick:s=>{s.stopPropagation(),J()},children:"Clear"})]})}),e.jsxs(m,{show:c,onClose:E,size:"lg",children:[e.jsx(m.Header,{children:"Select Customer"}),e.jsxs(m.Body,{children:[w&&e.jsxs(D,{color:"failure",className:"mb-4",children:[e.jsx(L,{className:"mr-2 h-5 w-5"}),w]}),e.jsx("div",{className:"mb-4",children:e.jsx(ee,{id:"customer-search",type:"text",placeholder:"Search by name, email, or phone",value:u,onChange:s=>y(s.target.value),icon:se})}),P?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(ae,{size:"xl"})}):f.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No customers found"}),e.jsxs(d,{size:"sm",onClick:a,children:[e.jsx(A,{className:"mr-2 h-4 w-4"}),"Add New Customer"]})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"grid grid-cols-1 gap-2 max-h-[60vh] overflow-y-auto",children:Y.map(s=>e.jsx("div",{className:"cursor-pointer hover:bg-blue-50 transition-colors p-2.5 rounded-md border border-gray-200 hover:border-blue-300 hover:shadow-sm",onClick:()=>G(s),children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(T,{className:"h-4 w-4 text-blue-500 flex-shrink-0"}),e.jsx("h5",{className:"text-sm font-semibold text-gray-900 truncate",children:s.name||"Unnamed Customer"}),s.loyalty_eligible&&e.jsx(te,{color:"purple",size:"sm",className:"text-xs",children:"Loyalty"})]}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[s.email&&e.jsxs("div",{className:"flex items-center min-w-0",children:[e.jsx(le,{className:"mr-1 h-3 w-3 text-gray-400 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:s.email})]}),s.phone&&e.jsxs("div",{className:"flex items-center flex-shrink-0",children:[e.jsx(ne,{className:"mr-1 h-3 w-3 text-gray-400"}),e.jsx("span",{children:s.phone})]})]})]}),s.loyalty_eligible&&(t==null?void 0:t.is_enabled)&&e.jsx("div",{className:"flex items-center ml-3 flex-shrink-0",children:e.jsxs("div",{className:"flex items-center bg-purple-100 px-2 py-1 rounded-full",children:[e.jsx(re,{className:"mr-1 h-3 w-3 text-purple-600"}),e.jsx("span",{className:"text-xs font-semibold text-purple-700",children:S[s.id]||0})]})})]})},s.id))}),f.length>0&&e.jsx(fe,{currentPage:H,totalPages:W,itemsPerPage:k,totalItems:f.length,onPageChange:z,onItemsPerPageChange:s=>{K(s),z(1)},itemName:"customers"})]})]}),e.jsx(m.Footer,{children:e.jsxs("div",{className:"flex justify-between w-full",children:[e.jsx(d,{color:"light",onClick:E,children:"Cancel"}),e.jsxs(d,{color:"blue",onClick:()=>{j(!1),a&&a()},children:[e.jsx(A,{className:"mr-2 h-4 w-4"}),"New Customer"]})]})})]})]})},ve=({itemId:i,initialNotes:o="",onSave:a})=>{const[x,t]=l.useState(!1),[c,j]=l.useState(o),h=()=>{t(!0)},p=()=>{t(!1)},f=()=>{a(i,c),t(!1)};return e.jsxs(e.Fragment,{children:[e.jsxs(d,{size:"xs",color:"light",onClick:h,className:"text-xs",children:[e.jsx(ce,{className:"mr-1 h-3 w-3"}),o?"Edit Notes":"Add Notes"]}),e.jsxs(m,{show:x,onClose:p,size:"md",children:[e.jsx(m.Header,{children:"Add Special Instructions"}),e.jsx(m.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Add any special instructions or notes for this item."}),e.jsx(oe,{value:c,onChange:g=>j(g.target.value),placeholder:"E.g., No onions, extra sauce, etc.",rows:4})]})}),e.jsx(m.Footer,{children:e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(d,{color:"gray",onClick:p,children:"Cancel"}),e.jsx(d,{onClick:f,children:"Save Notes"})]})})]})]})},Ce=({subtotal:i,onApplyDiscount:o,currentDiscount:a})=>{const{settings:x}=de(),t=je(),[c,j]=l.useState(!1),[h,p]=l.useState(a!=null&&a.isPercentage?"percentage":"fixed"),[f,g]=l.useState(a!=null&&a.isPercentage?a.amount:0),[u,y]=l.useState(a!=null&&a.isPercentage?0:(a==null?void 0:a.amount)||0);l.useEffect(()=>{!a||a.amount===0?(p("fixed"),g(0),y(0)):(p(a.isPercentage?"percentage":"fixed"),a.isPercentage?(g(a.amount),y(0)):(y(a.amount),g(0)))},[a]),l.useEffect(()=>{const n=S=>{S.key==="Escape"&&c&&b()};return c&&document.addEventListener("keydown",n),()=>{document.removeEventListener("keydown",n)}},[c]);const P=()=>{j(!0)},b=()=>{j(!1)},w=()=>{h==="percentage"?o(f,!0):o(u,!1),j(!1)},v=()=>{o(0,!1),j(!1)},N=a&&a.amount>0;return e.jsxs(e.Fragment,{children:[e.jsxs(d,{color:N?"success":"light",size:"sm",onClick:P,className:"flex items-center","data-testid":"discount-selector-button",children:[e.jsx(I,{className:"mr-2 h-4 w-4"}),N?`Discount: ${a.isPercentage?`${a.amount}%`:t(a.amount)}`:"Add Discount"]}),e.jsxs(m,{show:c,onClose:b,size:"md",children:[e.jsx(m.Header,{children:"Apply Discount"}),e.jsx(m.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex border-b border-gray-200",children:[e.jsxs("button",{type:"button",className:`flex items-center px-4 py-2 text-sm font-medium border-b-2 transition-colors ${h==="percentage"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,onClick:()=>p("percentage"),children:[e.jsx(me,{className:"mr-2 h-4 w-4"}),"Percentage"]}),e.jsxs("button",{type:"button",className:`flex items-center px-4 py-2 text-sm font-medium border-b-2 transition-colors ${h==="fixed"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,onClick:()=>p("fixed"),children:[e.jsx(xe,{className:"mr-2 h-4 w-4"}),"Fixed Amount"]})]}),h==="percentage"&&e.jsxs("div",{className:"space-y-4 py-2",children:[e.jsxs("div",{children:[e.jsx(B,{htmlFor:"percentageDiscount",children:"Discount Percentage"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(_,{id:"percentageDiscount",min:0,max:100,value:f,onChange:n=>g(Number(n.target.value)),className:"flex-1"}),e.jsx("span",{className:"ml-2 text-lg",children:"%"})]})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-gray-600",children:"Subtotal:"}),e.jsx("span",{children:t(i)})]}),e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsxs("span",{className:"text-gray-600",children:["Discount (",f,"%):"]}),e.jsxs("span",{className:"text-red-500",children:["-",t(i*f/100)]})]}),e.jsxs("div",{className:"flex justify-between font-bold pt-2 border-t border-gray-200 mt-2",children:[e.jsx("span",{children:"New Subtotal:"}),e.jsx("span",{children:t(i-i*f/100)})]})]}),e.jsx("div",{className:"grid grid-cols-4 gap-2 mt-2",children:[5,10,15,20].map(n=>e.jsxs(d,{size:"xs",color:f===n?"blue":"light",onClick:()=>g(n),children:[n,"%"]},n))})]}),h==="fixed"&&e.jsxs("div",{className:"space-y-4 py-2",children:[e.jsxs("div",{children:[e.jsx(B,{htmlFor:"fixedDiscount",children:"Discount Amount"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx("span",{className:"mr-2 text-lg",children:(x==null?void 0:x.currency)||"$"}),e.jsx(_,{id:"fixedDiscount",min:0,max:i,value:u,onChange:n=>y(Number(n.target.value)),className:"flex-1"})]})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-gray-600",children:"Subtotal:"}),e.jsx("span",{children:t(i)})]}),e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-gray-600",children:"Discount:"}),e.jsxs("span",{className:"text-red-500",children:["-",t(u)]})]}),e.jsxs("div",{className:"flex justify-between font-bold pt-2 border-t border-gray-200 mt-2",children:[e.jsx("span",{children:"New Subtotal:"}),e.jsx("span",{children:t(i-u)})]})]}),e.jsx("div",{className:"grid grid-cols-3 gap-2 mt-2",children:[5,10,20].map(n=>e.jsx(d,{size:"xs",color:u===n?"blue":"light",onClick:()=>y(n),children:t(n)},n))})]})]})}),e.jsx(m.Footer,{children:e.jsxs("div",{className:"flex justify-between w-full",children:[N&&e.jsxs(d,{color:"failure",onClick:v,className:"flex items-center",children:[e.jsx($,{className:"mr-2 h-4 w-4"}),"Remove Discount"]}),e.jsxs("div",{className:"flex gap-2 ml-auto",children:[e.jsx(d,{color:"gray",onClick:b,children:"Cancel"}),e.jsxs(d,{color:"success",onClick:w,className:"flex items-center",children:[e.jsx(I,{className:"mr-2 h-4 w-4"}),"Apply Discount"]})]})]})})]})]})},we=({show:i,onClose:o,onConfirm:a,itemCount:x})=>{const t=()=>{a(),o()};return l.useEffect(()=>{const c=j=>{j.key==="Escape"&&i&&o()};return i&&document.addEventListener("keydown",c),()=>{document.removeEventListener("keydown",c)}},[i,o]),e.jsxs(m,{show:i,onClose:o,size:"sm",children:[e.jsx(m.Header,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(L,{className:"mr-2 h-6 w-6 text-red-500"}),"Clear Cart"]})}),e.jsx(m.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(L,{className:"mx-auto mb-4 h-14 w-14 text-gray-400"}),e.jsx("p",{className:"text-gray-700 text-lg",children:"Are you sure you want to clear the entire cart?"}),e.jsxs("p",{className:"text-gray-500 text-sm mt-2",children:["This will remove all ",x," item",x!==1?"s":""," from your cart."]}),e.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"This action cannot be undone."})]})}),e.jsx(m.Footer,{children:e.jsxs("div",{className:"flex justify-center gap-4 w-full",children:[e.jsx(d,{color:"gray",onClick:o,children:"Cancel"}),e.jsxs(d,{color:"failure",onClick:t,className:"flex items-center",children:[e.jsx($,{className:"mr-2 h-4 w-4"}),"Clear Cart"]})]})})]})};export{be as C,Ce as D,ve as a,we as b};
