import{s as n}from"./index-C6AV3cVN.js";const a=async(i,o)=>{try{const{data:e,error:r}=await n.from("employee_contribution_preferences").select("*").eq("organization_id",i).eq("employee_id",o).eq("is_active",!0).single();return r&&r.code!=="PGRST116"?(console.error("Error fetching employee contribution preferences:",r),{error:r.message}):{preferences:e||void 0}}catch(e){return console.error("Error in getEmployeeContributionPreferences:",e),{error:e.message}}},l=async(i,o,e,r)=>{try{const{error:t}=await n.from("employee_contribution_preferences").update({is_active:!1}).eq("organization_id",i).eq("employee_id",o).eq("is_active",!0);if(t)return console.error("Error deactivating existing preferences:",t),{error:t.message};const{data:c,error:s}=await n.from("employee_contribution_preferences").insert({organization_id:i,employee_id:o,sss_contribution_override:e.sss_contribution_override,philhealth_contribution_override:e.philhealth_contribution_override,pagibig_contribution_override:e.pagibig_contribution_override,withholding_tax_override:e.withholding_tax_override,notes:e.notes,is_active:!0,created_by:r}).select("*").single();return s?(console.error("Error saving employee contribution preferences:",s),{error:s.message}):{preferences:c}}catch(t){return console.error("Error in saveEmployeeContributionPreferences:",t),{error:t.message}}},u=async(i,o)=>{try{const{error:e}=await n.from("employee_contribution_preferences").update({is_active:!1}).eq("organization_id",i).eq("employee_id",o).eq("is_active",!0);return e?(console.error("Error deleting employee contribution preferences:",e),{success:!1,error:e.message}):{success:!0}}catch(e){return console.error("Error in deleteEmployeeContributionPreferences:",e),{success:!1,error:e.message}}},p=async i=>{try{const{data:o,error:e}=await n.from("employee_contribution_preferences").select(`
        *,
        employee:employee_id (
          id,
          first_name,
          middle_name,
          last_name,
          employee_number
        )
      `).eq("organization_id",i).eq("is_active",!0).order("created_at",{ascending:!1});return e?(console.error("Error fetching employees with contribution preferences:",e),{error:e.message}):{employees:o}}catch(o){return console.error("Error in getEmployeesWithContributionPreferences:",o),{error:o.message}}},d=async(i,o,e)=>{try{const{preferences:r,error:t}=await a(i,o);return t?{contributions:{...e,contributions_manually_edited:!1},error:t}:r?{contributions:{sss_contribution:r.sss_contribution_override??e.sss_contribution,philhealth_contribution:r.philhealth_contribution_override??e.philhealth_contribution,pagibig_contribution:r.pagibig_contribution_override??e.pagibig_contribution,withholding_tax:r.withholding_tax_override??e.withholding_tax,contributions_manually_edited:!0,sss_contribution_override:r.sss_contribution_override,philhealth_contribution_override:r.philhealth_contribution_override,pagibig_contribution_override:r.pagibig_contribution_override,withholding_tax_override:r.withholding_tax_override}}:{contributions:{...e,contributions_manually_edited:!1}}}catch(r){return console.error("Error in applyEmployeeContributionPreferences:",r),{contributions:{...e,contributions_manually_edited:!1},error:r.message}}};export{d as applyEmployeeContributionPreferences,u as deleteEmployeeContributionPreferences,a as getEmployeeContributionPreferences,p as getEmployeesWithContributionPreferences,l as saveEmployeeContributionPreferences};
