import{_ as n}from"./typeof-QjJsDpFa.js";import{r,t as a}from"./index-Cn2wB4rc.js";function i(t){if(t===null||t===!0||t===!1)return NaN;var e=Number(t);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}function o(t){return r(1,arguments),t instanceof Date||n(t)==="object"&&Object.prototype.toString.call(t)==="[object Date]"}function u(t){if(r(1,arguments),!o(t)&&typeof t!="number")return!1;var e=a(t);return!isNaN(Number(e))}export{u as i,i as t};
