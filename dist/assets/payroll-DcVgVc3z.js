const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/payrollCalculation-DBybei6f.js","assets/index-C6AV3cVN.js","assets/index-ClH3u6fg.css","assets/payroll-j3fcCwK0.js"])))=>i.map(i=>d[i]);
import{s as i,aZ as D}from"./index-C6AV3cVN.js";const T=async(a,e)=>{try{let r=i.from("payroll_periods").select("*",{count:"exact"}).eq("organization_id",a).order("created_at",{ascending:!1});e!=null&&e.status&&(r=r.eq("status",e.status)),e!=null&&e.startDate&&(r=r.gte("start_date",e.startDate.toISOString().split("T")[0])),e!=null&&e.endDate&&(r=r.lte("end_date",e.endDate.toISOString().split("T")[0])),e!=null&&e.limit&&(r=r.limit(e.limit)),e!=null&&e.offset&&(r=r.range(e.offset,e.offset+(e.limit||10)-1));const{data:t,error:o,count:l}=await r;return o?(console.error("Error fetching payroll periods:",o),{periods:[],count:0,error:o.message}):{periods:t,count:l||0}}catch(r){return console.error("Error in getPayrollPeriods:",r),{periods:[],count:0,error:r.message}}},z=async(a,e,r=!1)=>{try{const{data:t,error:o}=await i.from("payroll_periods").select("*").eq("organization_id",a).eq("id",e).single();if(o)return console.error("Error fetching payroll period:",o),{error:o.message};const l=t;if(r){const{data:d,error:c}=await i.from("payroll_items").select(`
          *,
          employee:employee_id (
            id,
            first_name,
            middle_name,
            last_name,
            employee_number,
            profile_image_url
          )
        `).eq("organization_id",a).eq("payroll_period_id",e);if(c)return console.error("Error fetching payroll items:",c),{period:l,error:c.message};l.payroll_items=d}return{period:l}}catch(t){return console.error("Error in getPayrollPeriodById:",t),{error:t.message}}},B=async(a,e)=>{try{const{data:r,error:t}=await i.from("payroll_periods").insert({organization_id:a,...e}).select("*").single();return t?(console.error("Error creating payroll period:",t),{error:t.message}):{period:r}}catch(r){return console.error("Error in createPayrollPeriod:",r),{error:r.message}}},R=async(a,e,r)=>{try{const{data:t,error:o}=await i.from("payroll_periods").update(r).eq("organization_id",a).eq("id",e).select("*").single();return o?(console.error("Error updating payroll period:",o),{error:o.message}):{period:t}}catch(t){return console.error("Error in updatePayrollPeriod:",t),{error:t.message}}},G=async(a,e)=>{try{const{error:r}=await i.from("payroll_periods").delete().eq("organization_id",a).eq("id",e);return r?(console.error("Error deleting payroll period:",r),{success:!1,error:r.message}):{success:!0}}catch(r){return console.error("Error in deletePayrollPeriod:",r),{success:!1,error:r.message}}},x=async(a,e)=>{try{const{data:r,error:t}=await i.from("payroll_items").select(`
        *,
        employee:employee_id (
          id,
          first_name,
          middle_name,
          last_name,
          employee_number,
          profile_image_url
        ),
        payroll_period:payroll_period_id (
          id,
          name,
          start_date,
          end_date,
          payment_date,
          status
        )
      `).eq("organization_id",a).eq("id",e).single();return t?(console.error("Error fetching payroll item:",t),{error:t.message}):{item:r}}catch(r){return console.error("Error in getPayrollItemById:",r),{error:r.message}}},C=async(a,e,r)=>{try{const{data:t,error:o}=await i.from("payroll_items").update(r).eq("organization_id",a).eq("id",e).select("*").single();return o?(console.error("Error updating payroll item:",o),{error:o.message}):{item:t}}catch(t){return console.error("Error in updatePayrollItem:",t),{error:t.message}}},N=async(a,e)=>{try{const{item:r,error:t}=await x(a,e);if(t||!r)return{error:t||"Payroll item not found"};const{calculateSSSContribution:o,calculatePhilHealthContribution:l,calculatePagIbigContribution:d,calculateWithholdingTax:c}=await D(async()=>{const{calculateSSSContribution:S,calculatePhilHealthContribution:E,calculatePagIbigContribution:w,calculateWithholdingTax:q}=await import("./payrollCalculation-DBybei6f.js");return{calculateSSSContribution:S,calculatePhilHealthContribution:E,calculatePagIbigContribution:w,calculateWithholdingTax:q}},__vite__mapDeps([0,1,2,3])),{data:n}=await i.from("employee_salary").select("*").eq("organization_id",a).eq("employee_id",r.employee_id).single();if(!n)return{error:"Employee salary not found"};let s=0;n.rate_type==="daily"?s=Number(n.daily_rate)*22:s=Number(n.basic_salary),console.log("Recalculation Debug:",{employeeId:r.employee_id,rateType:n.rate_type,dailyRate:n.daily_rate,basicSalary:n.basic_salary,calculatedMonthlySalary:s,originalBasicPay:r.basic_pay});const y=o(s),u=l(s),_=d(s),m=r.gross_pay-y.employeeShare-u.employeeShare-_.employeeShare,g=c(m),h=y.employeeShare+u.employeeShare+_.employeeShare+g,f=r.total_deductions-(r.sss_contribution+r.philhealth_contribution+r.pagibig_contribution+r.withholding_tax),p=h+f,b=r.gross_pay-p,P={sss_contribution:y.employeeShare,philhealth_contribution:u.employeeShare,pagibig_contribution:_.employeeShare,withholding_tax:g,taxable_income:m,total_deductions:p,net_pay:b,contributions_manually_edited:!1,sss_contribution_override:null,philhealth_contribution_override:null,pagibig_contribution_override:null,withholding_tax_override:null};return await C(a,e,P)}catch(r){return console.error("Error in recalculateGovernmentContributions:",r),{error:r.message}}},O=async a=>{try{const{data:e,error:r}=await i.from("payroll_settings").select("*").eq("organization_id",a).single();return r?r.code==="PGRST116"?v(a,{pay_schedule:"semi-monthly",semi_monthly_days:[15,30],pay_based_on_time_entries:!0,sss_employer_contribution_rate:8.5,philhealth_employer_contribution_rate:2,pagibig_employer_contribution_rate:2,tax_table_version:"2023"}):(console.error("Error fetching payroll settings:",r),{error:r.message}):{settings:e}}catch(e){return console.error("Error in getPayrollSettings:",e),{error:e.message}}},v=async(a,e)=>{try{const{data:r,error:t}=await i.from("payroll_settings").insert({organization_id:a,...e}).select("*").single();return t?(console.error("Error creating payroll settings:",t),{error:t.message}):{settings:r}}catch(r){return console.error("Error in createPayrollSettings:",r),{error:r.message}}};export{z as a,O as b,B as c,G as d,v as e,x as f,T as g,C as h,N as r,R as u};
