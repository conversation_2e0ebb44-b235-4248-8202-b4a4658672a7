import{r as a,j as e,A as q,G,a6 as F,P as Z,U as ee,B as m,M as x,a7 as te,i as U,h as se,d as ae,D as V,aU as re,a0 as le,E as ne,_ as n,e as M,$ as oe,V as ie,L as A,a1 as ce,a2 as de,a3 as he,ah as me,aj as ue}from"./index-C6AV3cVN.js";import{C as xe}from"./Card-yj7fueH8.js";import{g as ye,c as je,d as pe}from"./payroll-DcVgVc3z.js";import{P as u}from"./payroll-j3fcCwK0.js";import{S as fe,h as ge}from"./SendPayrollToPayableButton-DAdxPmzk.js";import{D as B}from"./react-datepicker-BrCvW-wJ.js";import{p as Pe}from"./payrollProcessing-DLMQSSy3.js";import{P as De}from"./PageTitle-FHPo8gWi.js";import{E as be}from"./EmptyState-DxqWNypU.js";import{P as Ce}from"./Pagination-CVEzfctr.js";import{f as L}from"./index-qirzObrW.js";import"./payables-q7zOb02j.js";import"./currencyFormatter-BsFWv3sX.js";import"./formatters-Cypx7G-j.js";import"./payrollCalculation-DBybei6f.js";import"./employee-DWC25S7P.js";import"./CardBox-YV_4IKGE.js";import"./index-4YOzgfrD.js";import"./typeof-QjJsDpFa.js";import"./index-Cn2wB4rc.js";import"./index-DT2YvziZ.js";const Se=({onSubmit:h,isSubmitting:p,error:f,onCancel:g})=>{const[P,D]=a.useState(""),[r,b]=a.useState(null),[l,y]=a.useState(null),[o,S]=a.useState(null),[C,i]=a.useState(!1),[d,v]=a.useState({}),z=()=>{const s={};return P.trim()||(s.name="Period name is required"),r||(s.startDate="Start date is required"),l?r&&l<r&&(s.endDate="End date must be after start date"):s.endDate="End date is required",o?l&&o<l&&(s.paymentDate="Payment date must be on or after end date"):s.paymentDate="Payment date is required",v(s),Object.keys(s).length===0},T=async s=>{if(s.preventDefault(),!z()||!r||!l||!o)return;const c={name:P,start_date:r.toISOString().split("T")[0],end_date:l.toISOString().split("T")[0],payment_date:o.toISOString().split("T")[0],status:u.DRAFT,is_thirteenth_month:C};await h(c)},$=()=>{if(r&&l){const s=r.toLocaleString("default",{month:"long"}),c=r.getDate(),w=l.toLocaleString("default",{month:"long"}),O=l.getDate(),N=r.getFullYear();D(s===w?`${s} ${c}-${O}, ${N}`:`${s} ${c} - ${w} ${O}, ${N}`)}};return e.jsxs("form",{onSubmit:T,className:"space-y-4",children:[f&&e.jsx(q,{color:"failure",icon:G,children:f}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(F,{htmlFor:"name",value:"Period Name"})}),e.jsx(Z,{id:"name",value:P,onChange:s=>D(s.target.value),placeholder:"e.g., January 1-15, 2023",color:d.name?"failure":void 0,helperText:d.name})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(F,{htmlFor:"startDate",value:"Start Date"})}),e.jsx(B,{id:"startDate",selected:r,onChange:s=>{if(b(s),s&&(!l||l<s)){const c=new Date(s);c.setDate(s.getDate()+14),y(c);const w=new Date(c);w.setDate(c.getDate()+5),S(w)}setTimeout($,100)},dateFormat:"MMMM d, yyyy",className:`w-full rounded-lg ${d.startDate?"border-red-500":"border-gray-300"} p-2.5`,placeholderText:"Select start date"}),d.startDate&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:d.startDate})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(F,{htmlFor:"endDate",value:"End Date"})}),e.jsx(B,{id:"endDate",selected:l,onChange:s=>{if(y(s),setTimeout($,100),s&&(!o||o<s)){const c=new Date(s);c.setDate(s.getDate()+5),S(c)}},dateFormat:"MMMM d, yyyy",className:`w-full rounded-lg ${d.endDate?"border-red-500":"border-gray-300"} p-2.5`,placeholderText:"Select end date",minDate:r||void 0}),d.endDate&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:d.endDate})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(F,{htmlFor:"paymentDate",value:"Payment Date"})}),e.jsx(B,{id:"paymentDate",selected:o,onChange:s=>S(s),dateFormat:"MMMM d, yyyy",className:`w-full rounded-lg ${d.paymentDate?"border-red-500":"border-gray-300"} p-2.5`,placeholderText:"Select payment date",minDate:l||void 0}),d.paymentDate&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:d.paymentDate})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ee,{id:"isThirteenthMonth",checked:C,onChange:s=>i(s.target.checked)}),e.jsx(F,{htmlFor:"isThirteenthMonth",children:"This is a 13th month pay period"})]}),e.jsxs("div",{className:"flex justify-end space-x-2 pt-4",children:[e.jsx(m,{color:"gray",onClick:g,disabled:p,children:"Cancel"}),e.jsx(m,{type:"submit",color:"primary",isProcessing:p,disabled:p,children:"Create Payroll Period"})]})]})},ve=a.createContext({organizations:[],currentOrganization:null,setCurrentOrganization:()=>{},isLoading:!1,error:null,fetchOrganizations:async()=>{}}),we=()=>a.useContext(ve),Ne=({show:h,onClose:p,onSuccess:f})=>{const{organization:g}=we(),[P,D]=a.useState(new Date().getFullYear()),[r,b]=a.useState(!1),[l,y]=a.useState(null),o=new Date().getFullYear(),S=[{value:o,label:o.toString()},{value:o-1,label:(o-1).toString()},{value:o-2,label:(o-2).toString()}],C=async()=>{if(g!=null&&g.id){b(!0),y(null);try{const i=await Pe(g.id,P);i.success&&i.periodId?f(i.periodId):y(i.error||"Failed to process 13th month pay")}catch(i){y(i.message||"An error occurred while processing 13th month pay")}finally{b(!1)}}};return e.jsxs(x,{show:h,onClose:p,size:"md",children:[e.jsx(x.Header,{children:"Process 13th Month Pay"}),e.jsx(x.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-500",children:"This will calculate and process 13th month pay for all active employees based on their basic pay for the selected year. The 13th month pay is calculated as 1/12 of the total basic pay received during the year, or prorated based on months worked."}),l&&e.jsx(q,{color:"failure",icon:G,children:l}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(F,{htmlFor:"year",value:"Year"})}),e.jsx(te,{id:"year",value:P,onChange:i=>D(parseInt(i.target.value)),disabled:r,children:S.map(i=>e.jsx("option",{value:i.value,children:i.label},i.value))})]}),e.jsxs("div",{className:"bg-yellow-50 p-3 rounded-lg border border-yellow-200",children:[e.jsx("h4",{className:"text-yellow-800 font-medium text-sm",children:"Important Notes:"}),e.jsxs("ul",{className:"mt-1 text-sm text-yellow-700 list-disc list-inside",children:[e.jsx("li",{children:"13th month pay is tax-exempt up to ₱90,000"}),e.jsx("li",{children:"All active employees will be included in the calculation"}),e.jsx("li",{children:"The payment will be processed as a separate payroll period"}),e.jsx("li",{children:"This action cannot be undone once the period is approved"})]})]})]})}),e.jsxs(x.Footer,{children:[e.jsx(m,{color:"primary",onClick:C,disabled:r,children:r?e.jsxs(e.Fragment,{children:[e.jsx(U,{size:"sm",className:"mr-2"}),"Processing..."]}):"Process 13th Month Pay"}),e.jsx(m,{color:"gray",onClick:p,disabled:r,children:"Cancel"})]})]})},Xe=()=>{const{currentOrganization:h}=se(),p=ae(),[f,g]=a.useState([]),[P,D]=a.useState(!0),[r,b]=a.useState(null),[l,y]=a.useState({}),[o,S]=a.useState(0),[C,i]=a.useState(1),[d,v]=a.useState(!1),[z,T]=a.useState(!1),[$,s]=a.useState(!1),[c,w]=a.useState(null),[O,N]=a.useState(!1),[R,E]=a.useState(null),[k,J]=a.useState(10),H=async()=>{if(h){D(!0),b(null);try{const{periods:t,count:j,error:I}=await ye(h.id,{limit:k,offset:(C-1)*k});if(I)b(I);else{g(t),S(j);const Y={};for(const _ of t)if(_.status===u.APPROVED){const W=await ge(h.id,_.id);Y[_.id]=W}y(Y)}}catch(t){b(t.message)}finally{D(!1)}}};a.useEffect(()=>{H()},[h,C,k]);const X=async t=>{if(h){N(!0),E(null);try{const{period:j,error:I}=await je(h.id,t);I?E(I):(v(!1),H())}catch(j){E(j.message)}finally{N(!1)}}},K=async()=>{if(!(!h||!c)){N(!0),E(null);try{const{success:t,error:j}=await pe(h.id,c);j?E(j):t&&(T(!1),H())}catch(t){E(t.message)}finally{N(!1)}}},Q=t=>{switch(t){case u.DRAFT:return e.jsx(M,{color:"gray",children:"Draft"});case u.PROCESSING:return e.jsx(M,{color:"blue",children:"Processing"});case u.APPROVED:return e.jsx(M,{color:"green",children:"Approved"});case u.PAID:return e.jsx(M,{color:"purple",children:"Paid"});default:return e.jsx(M,{color:"gray",children:t})}};return P&&f.length===0?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(U,{size:"xl"})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx(De,{title:"Payroll Periods"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(V,{label:"Actions",color:"light",dismissOnClick:!0,children:e.jsx(V.Item,{icon:re,onClick:()=>s(!0),children:"Process 13th Month Pay"})}),e.jsxs(m,{color:"primary",onClick:()=>v(!0),children:[e.jsx(le,{className:"mr-2 h-5 w-5"}),"Create Payroll Period"]})]})]}),r&&e.jsx("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:e.jsx("p",{children:r})}),f.length===0?e.jsx(be,{title:"No payroll periods found",description:"Create your first payroll period to start processing payroll.",icon:e.jsx(ne,{className:"h-12 w-12 text-gray-400"}),actionLabel:"Create Payroll Period",onAction:()=>v(!0)}):e.jsxs(xe,{children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(n,{striped:!0,children:[e.jsxs(n.Head,{children:[e.jsx(n.HeadCell,{children:"Period Name"}),e.jsx(n.HeadCell,{children:"Start Date"}),e.jsx(n.HeadCell,{children:"End Date"}),e.jsx(n.HeadCell,{children:"Payment Date"}),e.jsx(n.HeadCell,{children:"Status"}),e.jsx(n.HeadCell,{children:"13th Month"}),e.jsx(n.HeadCell,{children:"Actions"})]}),e.jsx(n.Body,{children:f.map(t=>e.jsxs(n.Row,{children:[e.jsx(n.Cell,{className:"font-medium",children:t.name}),e.jsx(n.Cell,{children:L(new Date(t.start_date),"MMM d, yyyy")}),e.jsx(n.Cell,{children:L(new Date(t.end_date),"MMM d, yyyy")}),e.jsx(n.Cell,{children:L(new Date(t.payment_date),"MMM d, yyyy")}),e.jsx(n.Cell,{children:Q(t.status)}),e.jsx(n.Cell,{children:t.is_thirteenth_month?e.jsx(M,{color:"success",icon:oe,children:"Yes"}):e.jsx(M,{color:"gray",icon:ie,children:"No"})}),e.jsx(n.Cell,{children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(A,{to:`/payroll/periods/${t.id}`,children:e.jsxs(m,{size:"xs",color:"info",children:[e.jsx(ce,{className:"mr-1 h-4 w-4"}),"View"]})}),t.status===u.DRAFT&&e.jsxs(e.Fragment,{children:[e.jsx(A,{to:`/payroll/periods/${t.id}/edit`,children:e.jsxs(m,{size:"xs",color:"warning",children:[e.jsx(de,{className:"mr-1 h-4 w-4"}),"Edit"]})}),e.jsxs(m,{size:"xs",color:"failure",onClick:()=>{w(t.id),T(!0)},children:[e.jsx(he,{className:"mr-1 h-4 w-4"}),"Delete"]})]}),t.status===u.DRAFT&&e.jsx(A,{to:`/payroll/periods/${t.id}/process`,children:e.jsxs(m,{size:"xs",color:"success",children:[e.jsx(me,{className:"mr-1 h-4 w-4"}),"Process"]})}),t.status!==u.DRAFT&&e.jsx(A,{to:`/payroll/periods/${t.id}/payslips`,children:e.jsxs(m,{size:"xs",color:"light",children:[e.jsx(ue,{className:"mr-1 h-4 w-4"}),"Payslips"]})}),t.status===u.APPROVED&&e.jsx(fe,{payrollPeriodId:t.id,payrollPeriodName:t.name,payrollStatus:t.status,totalNetPay:0,totalEmployees:0,payablesCreated:l[t.id]||!1,onSuccess:()=>{y(j=>({...j,[t.id]:!0})),H()},className:"text-xs"})]})})]},t.id))})]})}),e.jsx(Ce,{currentPage:C,totalPages:Math.ceil(o/k),itemsPerPage:k,totalItems:o,onPageChange:i,onItemsPerPageChange:t=>{J(t),i(1)},itemName:"payroll periods"})]}),e.jsxs(x,{show:d,onClose:()=>v(!1),size:"lg",children:[e.jsx(x.Header,{children:"Create Payroll Period"}),e.jsx(x.Body,{children:e.jsx(Se,{onSubmit:X,isSubmitting:O,error:R,onCancel:()=>v(!1)})})]}),e.jsxs(x,{show:z,onClose:()=>T(!1),size:"md",children:[e.jsx(x.Header,{children:"Delete Payroll Period"}),e.jsx(x.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"Are you sure you want to delete this payroll period? This action cannot be undone."}),R&&e.jsx("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:e.jsx("p",{children:R})}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(m,{color:"gray",onClick:()=>T(!1),disabled:O,children:"Cancel"}),e.jsx(m,{color:"failure",onClick:K,isProcessing:O,children:"Delete"})]})]})})]}),e.jsx(Ne,{show:$,onClose:()=>s(!1),onSuccess:t=>{s(!1),p(`/payroll/periods/${t}`),H()}})]})};export{Xe as default};
