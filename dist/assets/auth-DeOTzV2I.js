import{s as n}from"./index-C6AV3cVN.js";const u=async({email:o,password:e,firstName:r,lastName:t,organizationName:a})=>{var i;try{console.log("Signing up user:",{email:o});const s=await n.auth.signUp({email:o,password:e,options:{data:{...r&&{first_name:r},...t&&{last_name:t},...a&&{organization_name:a}},emailRedirectTo:`${window.location.origin}/auth/login`}});if(console.log("Auth response:",s),s.error)throw console.error("Auth error:",s.error),s.error;return localStorage.setItem("pendingProfileSetup",JSON.stringify({userId:(i=s.data.user)==null?void 0:i.id})),s}catch(s){throw console.error("Signup error:",s),s}},g=async({email:o,password:e})=>{try{console.log("Signing in user:",{email:o});const r=await n.auth.signInWithPassword({email:o,password:e});return console.log("Sign in response:",{success:!r.error,hasSession:!!r.data.session,error:r.error?r.error.message:null}),r}catch(r){throw console.error("Sign in error:",r),r}},l=async o=>{const{error:e}=await n.auth.resetPasswordForEmail(o,{redirectTo:`${window.location.origin}/reset-password`});return{error:e}},d=async o=>{const{error:e}=await n.auth.updateUser({password:o});return{error:e}};export{u as a,l as r,g as s,d as u};
