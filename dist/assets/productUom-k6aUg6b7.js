import{s as i}from"./index-C6AV3cVN.js";import{a as p}from"./product-Ca8DWaNR.js";const M=async(e,r)=>{console.log("getProductUoms called with:",{productId:e,organizationId:r});try{if(!r){console.log("No organizationId provided, fetching from product");const{product:a,error:l}=await p(e);if(console.log("Product fetch result:",{product:a,error:l}),l||!a)return console.error("Failed to get product:",l),{productUoms:[],error:l||"Product not found"};r=a.organization_id,console.log("Using organization_id from product:",r)}console.log("Querying product_uoms with:",{productId:e,organizationId:r});const{data:o,error:t}=await i.from("products").select("id").eq("id",e).eq("organization_id",r).single();if(t)return console.error("Product does not exist or does not belong to this organization:",t),{productUoms:[],error:"Product not found in this organization"};const{data:c,error:n}=await i.from("units_of_measurement").select("id, code, name").eq("organization_id",r);n?console.error("Error checking organization UoMs:",n):console.log("Organization has UoMs:",c);const{data:s,error:d}=await i.from("product_uoms").select(`
        *,
        uom:uom_id (*)
      `).eq("product_id",e).eq("organization_id",r).order("is_default",{ascending:!1});if(console.log("Product UoMs query result:",{data:s,error:d,count:(s==null?void 0:s.length)||0}),d)return console.error("Error fetching product UoMs:",d),{productUoms:[],error:d.message};if(!s||s.length===0){console.warn("No UoMs found for product. Checking for default UoM in organization");const{data:a,error:l}=await i.from("units_of_measurement").select("*").eq("organization_id",r).eq("code","pcs").single();if(l){console.error("Error finding pieces UoM:",l);try{console.log("No pieces UoM found, creating one");const{data:u,error:f}=await i.from("units_of_measurement").insert({name:"Pieces",code:"pcs",organization_id:r,is_base_unit:!0}).select("*").single();if(f)console.error("Error creating pieces UoM:",f);else{console.log("Created pieces UoM:",u);try{console.log("Creating default UoM for product with new pieces UoM");const{data:m,error:g}=await i.from("product_uoms").insert({product_id:e,uom_id:u.id,organization_id:r,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0}).select(`
                  *,
                  uom:uom_id (*)
                `).single();if(g)console.error("Error creating default UoM with new pieces UoM:",g);else return console.log("Created default UoM with new pieces UoM:",m),{productUoms:[m]}}catch(m){console.error("Exception creating default UoM with new pieces UoM:",m)}}}catch(u){console.error("Exception creating pieces UoM:",u)}}else if(a){console.log("Found pieces UoM:",a);try{console.log("Creating default UoM for product");const{data:u,error:f}=await i.from("product_uoms").insert({product_id:e,uom_id:a.id,organization_id:r,conversion_factor:1,is_default:!0,is_purchasing_unit:!0,is_selling_unit:!0}).select(`
              *,
              uom:uom_id (*)
            `).single();if(f)console.error("Error creating default UoM:",f);else return console.log("Created default UoM:",u),{productUoms:[u]}}catch(u){console.error("Exception creating default UoM:",u)}}}return{productUoms:s}}catch(o){return console.error("Error in getProductUoms:",o),{productUoms:[],error:o.message}}},E=async(e,r)=>{try{if(!r)return{error:"Organization ID is required"};const{data:o,error:t}=await i.from("product_uoms").select(`
        *,
        uom:uom_id (*)
      `).eq("product_id",e).eq("organization_id",r).eq("is_default",!0).single();return t?(console.error("Error fetching default product UoM:",t),{error:t.message}):{productUom:o}}catch(o){return console.error("Error in getDefaultProductUom:",o),{error:o.message}}},w=async(e,r)=>{try{if(!r)return{error:"Organization ID is required"};const{data:o,error:t}=await i.from("units_of_measurement").select("organization_id").eq("id",e.uom_id).single();if(t)return{error:"Unit of measurement not found"};if(o.organization_id!==r)return{error:"Unit of measurement must belong to the same organization as the product"};const{data:c,error:n}=await i.from("product_uoms").insert({...e,organization_id:r}).select(`
        *,
        uom:uom_id (*)
      `).single();return n?(console.error("Error creating product UoM:",n),{error:n.message}):{productUom:c}}catch(o){return console.error("Error in createProductUom:",o),{error:o.message}}},y=async(e,r,o)=>{try{if(o){const{data:n,error:s}=await i.from("product_uoms").select("organization_id").eq("id",e).single();if(s)return{error:"Product UoM not found"};if(n.organization_id!==o)return{error:"You do not have permission to update this product UoM"}}const{data:t,error:c}=await i.from("product_uoms").update(r).eq("id",e).select(`
        *,
        uom:uom_id (*)
      `).single();return c?(console.error("Error updating product UoM:",c),{error:c.message}):{productUom:t}}catch(t){return console.error("Error in updateProductUom:",t),{error:t.message}}},P=async(e,r)=>{try{if(r){const{data:t,error:c}=await i.from("product_uoms").select("organization_id").eq("id",e).single();if(c)return{success:!1,error:"Product UoM not found"};if(t.organization_id!==r)return{success:!1,error:"You do not have permission to delete this product UoM"}}const{error:o}=await i.from("product_uoms").delete().eq("id",e);return o?(console.error("Error deleting product UoM:",o),{success:!1,error:o.message}):{success:!0}}catch(o){return console.error("Error in deleteProductUom:",o),{success:!1,error:o.message}}},q=async(e,r,o,t,c)=>{try{let n,s,d;if(typeof r=="number"?(n=r,s=o,d=t):(s=r,d=o,n=t,c=c),console.log("Converting quantity:",{productId:e,quantity:n,fromUomId:s,toUomId:d,organizationId:c}),s===d)return{convertedQuantity:n};if(!c)return{error:"Organization ID is required"};const{data:a,error:l}=await i.from("product_uoms").select("uom_id, conversion_factor").eq("product_id",e).eq("organization_id",c).in("uom_id",[s,d]);if(l)return console.error("Error fetching conversion factors:",l),{error:l.message};if(a.length!==2)return console.error("Could not find conversion factors for both UoMs. Found:",a.length),{error:"Could not find conversion factors for both UoMs"};const u=a.find(_=>_.uom_id===s),f=a.find(_=>_.uom_id===d);if(!u||!f)return console.error("Missing UoM data:",{fromUom:u,toUom:f}),{error:"Could not find conversion factors for both UoMs"};console.log("Conversion factors:",{fromUom:u.conversion_factor,toUom:f.conversion_factor});const m=n*u.conversion_factor,g=m/f.conversion_factor;return console.log("Conversion result:",{quantity:n,baseQuantity:m,convertedQuantity:g}),{convertedQuantity:g}}catch(n){return console.error("Error in convertQuantity:",n),{error:n.message}}};export{q as convertQuantity,w as createProductUom,P as deleteProductUom,E as getDefaultProductUom,M as getProductUoms,y as updateProductUom};
