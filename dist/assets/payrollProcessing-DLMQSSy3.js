const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/employeeContributionPreferences-DopMjMg8.js","assets/index-C6AV3cVN.js","assets/index-ClH3u6fg.css"])))=>i.map(i=>d[i]);
import{s,aZ as q}from"./index-C6AV3cVN.js";import{P as d,a as T}from"./payroll-j3fcCwK0.js";import{processEmployeePayroll as L,calculate13thMonthPay as M}from"./payrollCalculation-DBybei6f.js";import{a as G,u as C}from"./payroll-DcVgVc3z.js";import{g as k}from"./employee-DWC25S7P.js";const J=async(o,r,n,i)=>{var E,D,g,b,_,h,f,w,y,l;try{const{period:c,error:P}=await G(o,r);if(P||!c)throw new Error(P||"Payroll period not found");if(c.status!==d.DRAFT&&!(i!=null&&i.forceProcess))throw new Error("Payroll period is not in draft status");await C(o,r,{status:d.PROCESSING}),i!=null&&i.reprocess&&await s.from("payroll_items").delete().eq("organization_id",o).eq("payroll_period_id",r).in("employee_id",n);let v=0;const x=[];for(const m of n)try{const t=await L(o,r,m),$={organization_id:o,payroll_period_id:r,employee_id:m,basic_pay:t.basicPay,gross_pay:t.grossPay,net_pay:t.netPay,total_allowances:t.totalAllowances,total_deductions:t.totalDeductions,regular_pay:((E=t.timeEntryDetails)==null?void 0:E.regularPay)||0,overtime_pay:((D=t.timeEntryDetails)==null?void 0:D.overtimePay)||0,rest_day_pay:((g=t.timeEntryDetails)==null?void 0:g.restDayPay)||0,holiday_pay:((b=t.timeEntryDetails)==null?void 0:b.holidayPay)||0,night_differential_pay:((_=t.timeEntryDetails)==null?void 0:_.nightDifferentialPay)||0,total_regular_hours:((h=t.timeEntryDetails)==null?void 0:h.totalRegularHours)||0,total_overtime_hours:((f=t.timeEntryDetails)==null?void 0:f.totalOvertimeHours)||0,total_rest_day_hours:((w=t.timeEntryDetails)==null?void 0:w.totalRestDayHours)||0,total_holiday_hours:((y=t.timeEntryDetails)==null?void 0:y.totalHolidayHours)||0,total_night_diff_hours:((l=t.timeEntryDetails)==null?void 0:l.totalNightDiffHours)||0,is_thirteenth_month:c.is_thirteenth_month||!1,status:T.CALCULATED};let u;try{const{applyEmployeeContributionPreferences:p}=await q(async()=>{const{applyEmployeeContributionPreferences:A}=await import("./employeeContributionPreferences-DopMjMg8.js");return{applyEmployeeContributionPreferences:A}},__vite__mapDeps([0,1,2])),{contributions:e}=await p(o,m,{sss_contribution:t.sssContribution.employeeShare,philhealth_contribution:t.philhealthContribution.employeeShare,pagibig_contribution:t.pagibigContribution.employeeShare,withholding_tax:t.withholdingTax});let a=t.taxableIncome,F=t.netPay,S=t.totalDeductions;if(e.contributions_manually_edited){a=t.grossPay-e.sss_contribution-e.philhealth_contribution-e.pagibig_contribution;const A=e.sss_contribution+e.philhealth_contribution+e.pagibig_contribution+e.withholding_tax,O=t.totalDeductions-(t.sssContribution.employeeShare+t.philhealthContribution.employeeShare+t.pagibigContribution.employeeShare+t.withholdingTax);S=A+O,F=t.grossPay-S}const H={...$,taxable_income:a,withholding_tax:e.withholding_tax,sss_contribution:e.sss_contribution,philhealth_contribution:e.philhealth_contribution,pagibig_contribution:e.pagibig_contribution,total_deductions:S,net_pay:F,contributions_manually_edited:e.contributions_manually_edited,sss_contribution_override:e.sss_contribution_override,philhealth_contribution_override:e.philhealth_contribution_override,pagibig_contribution_override:e.pagibig_contribution_override,withholding_tax_override:e.withholding_tax_override},{data:N,error:R}=await s.from("payroll_items").insert(H).select().single();if(R)throw console.error("Error inserting payroll item with government contributions:",R),R;u=N.id,v++}catch{console.warn("Falling back to basic payroll item without government contributions");const{data:e,error:a}=await s.from("payroll_items").insert($).select().single();if(a)throw new Error(a.message);u=e.id,v++}if(t.allowances.length>0&&u){const p=t.allowances.map(a=>({organization_id:o,payroll_item_id:u,name:a.name,amount:a.amount,type:a.type})),{error:e}=await s.from("payroll_allowances").insert(p);e&&console.error("Error creating payroll allowances:",e)}if(t.deductions.length>0&&u){const p=t.deductions.map(a=>({organization_id:o,payroll_item_id:u,name:a.name,amount:a.amount,type:a.type})),{error:e}=await s.from("payroll_deductions").insert(p);e&&console.error("Error creating payroll deductions:",e)}}catch(t){console.error(`Error processing payroll for employee ${m}:`,t),x.push(`Employee ${m}: ${t.message}`),i!=null&&i.createErrorItems&&await s.from("payroll_items").insert({organization_id:o,payroll_period_id:r,employee_id:m,basic_pay:0,gross_pay:0,net_pay:0,total_allowances:0,total_deductions:0,taxable_income:0,withholding_tax:0,sss_contribution:0,philhealth_contribution:0,pagibig_contribution:0,status:T.ERROR,error_message:t.message})}return v===n.length&&await C(o,r,{status:d.PROCESSING}),{success:x.length===0,processedItems:v,error:x.length>0?x.join("; "):void 0}}catch(c){return console.error("Error processing payroll:",c),await C(o,r,{status:d.DRAFT}),{success:!1,processedItems:0,error:c.message}}},K=async(o,r)=>{try{const n=`13th Month Pay - ${r}`,i=`${r}-01-01`,E=`${r}-12-31`,D=`${r}-12-15`,{data:g}=await s.from("payroll_periods").select("id").eq("organization_id",o).eq("name",n).eq("is_thirteenth_month",!0);if(g&&g.length>0)return{success:!1,error:`A 13th month pay period for ${r} already exists`};const{data:b,error:_}=await s.from("payroll_periods").insert({organization_id:o,name:n,start_date:i,end_date:E,payment_date:D,status:d.DRAFT,is_thirteenth_month:!0}).select().single();if(_||!b)throw new Error((_==null?void 0:_.message)||"Failed to create 13th month pay period");const h=b.id,{employees:f,error:w}=await k(o,{isActive:!0});if(w||!f)throw new Error(w||"Failed to get employees");for(const y of f)try{const l=await M(o,y.id,r);if(l<=0)continue;const c={organization_id:o,payroll_period_id:h,employee_id:y.id,basic_pay:l,gross_pay:l,net_pay:l,total_allowances:0,total_deductions:0,is_thirteenth_month:!0,status:T.CALCULATED};try{const P={...c,taxable_income:0,withholding_tax:0,sss_contribution:0,philhealth_contribution:0,pagibig_contribution:0};await s.from("payroll_items").insert(P)}catch{console.warn("Falling back to basic 13th month payroll item without government contributions"),await s.from("payroll_items").insert(c)}}catch(l){console.error(`Error processing 13th month pay for employee ${y.id}:`,l)}return await C(o,h,{status:d.PROCESSING}),{success:!0,periodId:h}}catch(n){return console.error("Error processing 13th month pay:",n),{success:!1,error:n.message}}};export{J as a,K as p};
