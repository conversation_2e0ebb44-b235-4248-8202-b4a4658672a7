import{h as ee,r as d,j as e,B as C,a1 as pe,b0 as re,e as q,i as ie,A as de,t as fe,a7 as Z,b5 as me,b6 as he,b as ve,Z as we,b7 as Re,H as Ce,b8 as le,b9 as ue,T as ne,P as je,_ as r,ba as _e,bb as Se,bc as De,M}from"./index-C6AV3cVN.js";import{C as p}from"./Card-yj7fueH8.js";import{a as Me,R as X}from"./refund-CcMk-dC8.js";import{u as se}from"./currencyFormatter-BsFWv3sX.js";import{a as k,b as B,c as Q,R as Ee}from"./RefundProcessor-BGtDGV8c.js";import{f as z}from"./index-qirzObrW.js";import{C as Pe,s as W,a as K}from"./react-apexcharts.min-gcvzl5Eq.js";import{e as Ae}from"./index-BJwYa9ck.js";import{P as Te}from"./Pagination-CVEzfctr.js";import{c as He}from"./excelExport-BekG2cQR.js";import"./inventoryTransaction-1UXV5RDN.js";import"./floatInventory-k_pEQeIK.js";import"./formatters-Cypx7G-j.js";import"./EnhancedNumberInput-Bwo1E3yF.js";import"./index-4YOzgfrD.js";import"./typeof-QjJsDpFa.js";import"./index-Cn2wB4rc.js";import"./index-DT2YvziZ.js";import"./index-idNacaog.js";const ke=({refund:l,onClose:E})=>{const{currentOrganization:t}=ee(),m=se(),[T,f]=d.useState(!1),P=d.useRef(null),A=()=>{const y=P.current;if(y){const N=window.open("","_blank");N&&(N.document.write(`
          <html>
            <head>
              <title>Credit Memo - ${l.refund_number}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .print-content { max-width: 800px; margin: 0 auto; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .text-right { text-align: right; }
                .text-center { text-align: center; }
                .font-bold { font-weight: bold; }
                .text-lg { font-size: 1.125rem; }
                .text-xl { font-size: 1.25rem; }
                .text-3xl { font-size: 1.875rem; }
                .mb-2 { margin-bottom: 0.5rem; }
                .mb-4 { margin-bottom: 1rem; }
                .mb-6 { margin-bottom: 1.5rem; }
                .mb-8 { margin-bottom: 2rem; }
                .pt-2 { padding-top: 0.5rem; }
                .pt-6 { padding-top: 1.5rem; }
                .pb-6 { padding-bottom: 1.5rem; }
                .border-t { border-top: 1px solid #ddd; }
                .border-b-2 { border-bottom: 2px solid #ddd; }
                .bg-gray-50 { background-color: #f9fafb; }
                .p-4 { padding: 1rem; }
                .rounded { border-radius: 0.25rem; }
                .grid { display: grid; }
                .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
                .gap-8 { gap: 2rem; }
                .flex { display: flex; }
                .justify-between { justify-content: space-between; }
                .justify-end { justify-content: flex-end; }
                .items-start { align-items: flex-start; }
                .space-y-2 > * + * { margin-top: 0.5rem; }
                .w-64 { width: 16rem; }
                .text-gray-600 { color: #4b5563; }
                .text-gray-900 { color: #111827; }
                .text-red-600 { color: #dc2626; }
              </style>
            </head>
            <body>
              <div class="print-content">
                ${y.innerHTML}
              </div>
            </body>
          </html>
        `),N.document.close(),N.print(),N.close())}},i=()=>`CM-${l.refund_number.replace("REF-","")}`,F=()=>{var y,N,I,L,a,o,D,x;return e.jsxs("div",{ref:P,className:"bg-white p-8 max-w-4xl mx-auto",children:[e.jsx("div",{className:"border-b-2 border-gray-300 pb-6 mb-6",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"CREDIT MEMO"}),e.jsxs("p",{className:"text-lg text-gray-600 mt-2",children:["Credit Memo #: ",i()]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900",children:(t==null?void 0:t.name)||"Your Business"}),(t==null?void 0:t.address)&&e.jsx("p",{className:"text-gray-600",children:t.address}),(t==null?void 0:t.phone)&&e.jsxs("p",{className:"text-gray-600",children:["Phone: ",t.phone]}),(t==null?void 0:t.email)&&e.jsxs("p",{className:"text-gray-600",children:["Email: ",t.email]}),(t==null?void 0:t.website)&&e.jsx("p",{className:"text-gray-600",children:t.website})]})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-8 mb-8",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Credit To:"}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded",children:[e.jsx("p",{className:"font-medium",children:((N=(y=l.original_sale)==null?void 0:y.customer)==null?void 0:N.name)||"Walk-in Customer"}),((L=(I=l.original_sale)==null?void 0:I.customer)==null?void 0:L.email)&&e.jsx("p",{className:"text-gray-600",children:l.original_sale.customer.email}),((o=(a=l.original_sale)==null?void 0:a.customer)==null?void 0:o.phone)&&e.jsx("p",{className:"text-gray-600",children:l.original_sale.customer.phone})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Credit Details:"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Date Issued:"}),e.jsx("span",{className:"font-medium",children:z(new Date(l.created_at),"MMM dd, yyyy")})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Original Invoice:"}),e.jsx("span",{className:"font-medium",children:(D=l.original_sale)==null?void 0:D.invoice_number})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Refund Type:"}),e.jsx("span",{className:"font-medium capitalize",children:l.refund_type})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Reason:"}),e.jsx("span",{className:"font-medium capitalize",children:l.reason.replace(/_/g," ")})]})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Refunded Items:"}),e.jsxs("table",{className:"w-full border-collapse border border-gray-300",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100",children:[e.jsx("th",{className:"border border-gray-300 px-4 py-2 text-left",children:"Item"}),e.jsx("th",{className:"border border-gray-300 px-4 py-2 text-center",children:"Qty"}),e.jsx("th",{className:"border border-gray-300 px-4 py-2 text-right",children:"Unit Price"}),e.jsx("th",{className:"border border-gray-300 px-4 py-2 text-center",children:"Condition"}),e.jsx("th",{className:"border border-gray-300 px-4 py-2 text-right",children:"Total"})]})}),e.jsx("tbody",{children:(x=l.refund_items)==null?void 0:x.map((n,_)=>{var v,S;return e.jsxs("tr",{children:[e.jsx("td",{className:"border border-gray-300 px-4 py-2",children:e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:(v=n.product)==null?void 0:v.name}),e.jsxs("p",{className:"text-sm text-gray-600",children:["SKU: ",(S=n.product)==null?void 0:S.sku]})]})}),e.jsx("td",{className:"border border-gray-300 px-4 py-2 text-center",children:n.quantity}),e.jsx("td",{className:"border border-gray-300 px-4 py-2 text-right",children:m(n.unit_price)}),e.jsx("td",{className:"border border-gray-300 px-4 py-2 text-center",children:e.jsx(q,{color:n.condition==="new"?"success":n.condition==="used"?"warning":n.condition==="damaged"?"failure":"gray",children:n.condition})}),e.jsx("td",{className:"border border-gray-300 px-4 py-2 text-right",children:m(n.total_price)})]},_)})})]})]}),e.jsx("div",{className:"flex justify-end mb-8",children:e.jsx("div",{className:"w-64",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Subtotal:"}),e.jsx("span",{children:m(l.subtotal)})]}),l.restocking_fee>0&&e.jsxs("div",{className:"flex justify-between text-red-600",children:[e.jsx("span",{children:"Restocking Fee:"}),e.jsxs("span",{children:["-",m(l.restocking_fee)]})]}),e.jsxs("div",{className:"border-t pt-2 flex justify-between font-bold text-lg",children:[e.jsx("span",{children:"Total Credit:"}),e.jsx("span",{children:m(l.total_amount)})]})]})})}),e.jsxs("div",{className:"border-t pt-6 text-center text-gray-600",children:[e.jsxs("p",{className:"mb-2",children:[e.jsx("strong",{children:"Refund Method:"})," ",l.refund_method.replace(/_/g," ").toUpperCase()]}),l.reason_notes&&e.jsxs("p",{className:"mb-2",children:[e.jsx("strong",{children:"Notes:"})," ",l.reason_notes]}),e.jsxs("p",{className:"text-sm",children:["This credit memo was generated on ",z(new Date,"MMM dd, yyyy HH:mm")]}),e.jsx("p",{className:"text-sm mt-2",children:"Thank you for your business!"})]})]})};return e.jsxs("div",{className:"space-y-6",children:[e.jsx(p,{className:"p-4",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Credit Memo"}),e.jsxs("p",{className:"text-gray-600",children:["Generate and manage credit memo for refund ",l.refund_number]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs(C,{color:"light",onClick:()=>f(!T),children:[e.jsx(pe,{className:"mr-2 h-4 w-4"}),T?"Hide":"Preview"]}),e.jsxs(C,{color:"info",onClick:A,children:[e.jsx(re,{className:"mr-2 h-4 w-4"}),"Print"]})]})]})}),T&&e.jsx(p,{className:"p-0",children:e.jsx("div",{className:"max-h-96 overflow-y-auto",children:e.jsx(F,{})})}),e.jsx("div",{className:"hidden",children:e.jsx(F,{})})]})},Fe=({saleId:l,onClose:E})=>{const{currentOrganization:t}=ee(),m=se(),[T,f]=d.useState(!0),[P,A]=d.useState(null),[i,F]=d.useState(null),[y,N]=d.useState(!1),I=d.useRef(null);d.useEffect(()=>{L()},[l]);const L=async()=>{var x;if(!t||!l){A("Missing organization or sale ID"),f(!1);return}console.log("Fetching receipt data for sale ID:",l),f(!0),A(null);try{const{sale:n,error:_}=await Me(t.id,l);if(_||!n)throw new Error(_||"Sale not found");const v=await X.getRefunds(t.id,{filters:{},limit:100}),S=v.success?((x=v.data)==null?void 0:x.filter(w=>w.original_sale_id===l))||[]:[],u=S.filter(w=>w.status==="processed").reduce((w,U)=>w+Number(U.total_amount),0),b=n.total_amount>0?u/n.total_amount*100:0;F({sale:n,refunds:S,totalRefunded:u,refundPercentage:b})}catch(n){A(n.message||"Failed to fetch receipt data")}finally{f(!1)}},a=()=>{const x=I.current;if(x){const n=window.open("","_blank");n&&(n.document.write(`
          <html>
            <head>
              <title>Receipt - ${i==null?void 0:i.sale.invoice_number}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .receipt { max-width: 400px; margin: 0 auto; font-size: 12px; }
                .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px; }
                .item-row { display: flex; justify-content: space-between; margin: 2px 0; }
                .total-row { border-top: 1px solid #000; padding-top: 5px; margin-top: 10px; font-weight: bold; }
                .refund-section { border-top: 2px dashed #000; margin-top: 15px; padding-top: 10px; }
                .refund-item { color: #dc2626; }
                .text-center { text-align: center; }
                .font-bold { font-weight: bold; }
                .text-red { color: #dc2626; }
                .text-green { color: #059669; }
                .border-dashed { border-style: dashed; }
              </style>
            </head>
            <body>
              ${x.innerHTML}
            </body>
          </html>
        `),n.document.close(),n.print(),n.close())}},o=()=>i?i.refundPercentage>=100?{label:"FULLY REFUNDED",color:"failure"}:i.refundPercentage>0?{label:`PARTIALLY REFUNDED (${i.refundPercentage.toFixed(1)}%)`,color:"warning"}:null:null,D=()=>{var S;if(!i)return null;const{sale:x,refunds:n,totalRefunded:_}=i,v=o();return e.jsxs("div",{ref:I,className:"receipt bg-white p-6 max-w-md mx-auto border",children:[e.jsxs("div",{className:"header text-center border-b-2 border-black pb-4 mb-4",children:[e.jsx("h2",{className:"text-lg font-bold",children:(t==null?void 0:t.name)||"Your Business"}),(t==null?void 0:t.address)&&e.jsx("p",{className:"text-sm",children:t.address}),(t==null?void 0:t.phone)&&e.jsxs("p",{className:"text-sm",children:["Phone: ",t.phone]}),(t==null?void 0:t.email)&&e.jsxs("p",{className:"text-sm",children:["Email: ",t.email]}),(t==null?void 0:t.website)&&e.jsx("p",{className:"text-sm",children:t.website})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Receipt #:"}),e.jsx("span",{className:"font-bold",children:x.invoice_number})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Date:"}),e.jsx("span",{children:z(new Date(x.sale_date),"MMM dd, yyyy HH:mm")})]}),x.customer&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Customer:"}),e.jsx("span",{children:x.customer.name})]})]}),v&&e.jsx("div",{className:"mb-4 text-center",children:e.jsx(q,{color:v.color,size:"lg",children:v.label})}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"font-bold mb-2",children:"Items:"}),(S=x.items)==null?void 0:S.map((u,b)=>{var w;return e.jsxs("div",{className:"item-row flex justify-between mb-1",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium",children:(w=u.product)==null?void 0:w.name}),e.jsxs("div",{className:"text-sm text-gray-600",children:[u.quantity," x ",m(u.unit_price)]})]}),e.jsx("div",{className:"text-right",children:m(u.total_amount)})]},b)})]}),e.jsxs("div",{className:"border-t border-black pt-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Subtotal:"}),e.jsx("span",{children:m(x.subtotal)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Tax:"}),e.jsx("span",{children:m(x.tax_amount)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Discount:"}),e.jsxs("span",{children:["-",m(x.discount_amount)]})]}),e.jsxs("div",{className:"flex justify-between font-bold text-lg border-t pt-1 mt-1",children:[e.jsx("span",{children:"Total:"}),e.jsx("span",{children:m(x.total_amount)})]})]}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Payment Method:"}),e.jsx("span",{className:"capitalize",children:x.payment_method||"Cash"})]}),x.cash_tendered&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Cash Tendered:"}),e.jsx("span",{children:m(x.cash_tendered)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Change:"}),e.jsx("span",{children:m(x.change_amount||0)})]})]})]}),n.length>0&&e.jsxs("div",{className:"refund-section border-t-2 border-dashed border-black mt-6 pt-4",children:[e.jsx("h3",{className:"font-bold mb-2 text-red-600",children:"REFUND HISTORY:"}),n.map((u,b)=>e.jsxs("div",{className:"mb-3 text-sm",children:[e.jsxs("div",{className:"flex justify-between font-medium",children:[e.jsxs("span",{children:["Refund #",u.refund_number]}),e.jsxs("span",{className:"text-red-600",children:["-",m(u.total_amount)]})]}),e.jsxs("div",{className:"text-gray-600",children:[e.jsxs("div",{children:["Date: ",z(new Date(u.created_at),"MMM dd, yyyy")]}),e.jsxs("div",{children:["Reason: ",u.reason.replace(/_/g," ")]}),e.jsxs("div",{children:["Method: ",u.refund_method.replace(/_/g," ")]})]})]},b)),e.jsxs("div",{className:"border-t pt-2 mt-2",children:[e.jsxs("div",{className:"flex justify-between font-bold text-red-600",children:[e.jsx("span",{children:"Total Refunded:"}),e.jsxs("span",{children:["-",m(_)]})]}),e.jsxs("div",{className:"flex justify-between font-bold",children:[e.jsx("span",{children:"Net Amount:"}),e.jsx("span",{children:m(x.total_amount-_)})]})]})]}),e.jsxs("div",{className:"text-center mt-6 text-sm text-gray-600",children:[e.jsx("p",{children:"Thank you for your business!"}),e.jsxs("p",{className:"mt-2",children:["Receipt reprinted on ",z(new Date,"MMM dd, yyyy HH:mm")]})]})]})};return T?e.jsx(p,{className:"p-8",children:e.jsxs("div",{className:"flex justify-center items-center",children:[e.jsx(ie,{size:"xl"}),e.jsx("span",{className:"ml-3",children:"Loading receipt data..."})]})}):P?e.jsx(p,{className:"p-6",children:e.jsxs(de,{color:"failure",children:[e.jsx("h3",{className:"font-medium",children:"Error Loading Receipt"}),e.jsx("p",{children:P}),e.jsx("div",{className:"mt-4",children:e.jsxs(C,{size:"sm",onClick:L,children:[e.jsx(fe,{className:"mr-2 h-4 w-4"}),"Retry"]})})]})}):e.jsxs("div",{className:"space-y-6",children:[e.jsx(p,{className:"p-4",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Annotated Receipt"}),e.jsxs("p",{className:"text-gray-600",children:["Receipt with refund annotations for ",i==null?void 0:i.sale.invoice_number]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs(C,{color:"light",onClick:()=>N(!y),children:[e.jsx(pe,{className:"mr-2 h-4 w-4"}),y?"Hide":"Preview"]}),e.jsxs(C,{color:"info",onClick:a,children:[e.jsx(re,{className:"mr-2 h-4 w-4"}),"Print Receipt"]})]})]})}),y&&e.jsx(p,{className:"p-0",children:e.jsx("div",{className:"max-h-96 overflow-y-auto",children:e.jsx(D,{})})}),e.jsx("div",{className:"hidden",children:e.jsx(D,{})})]})},Ie=()=>{const{currentOrganization:l}=ee(),E=se(),[t,m]=d.useState(!1),[T,f]=d.useState(null),[P,A]=d.useState("30days"),[i,F]=d.useState({totalRefunds:0,totalAmount:0,averageRefundAmount:0,refundRate:0,byReason:{},byMethod:{},byCondition:{},dailyTrend:[],topRefundedProducts:[]});d.useEffect(()=>{l&&N()},[l,P]);const y=()=>{const a=Ae(new Date);let o;switch(P){case"today":o=W(new Date);break;case"7days":o=W(K(new Date,7));break;case"30days":o=W(K(new Date,30));break;case"90days":o=W(K(new Date,90));break;default:o=W(K(new Date,30))}return{startDate:o,endDate:a}},N=async()=>{if(l){m(!0),f(null);try{const{startDate:a,endDate:o}=y(),D=await X.getRefunds(l.id,{filters:{date_from:a.toISOString(),date_to:o.toISOString()},limit:1e3});if(!D.success||!D.data)throw new Error("Failed to fetch refund data");const n=D.data.filter(h=>h.status==="processed"),_=n.length,v=n.reduce((h,c)=>h+Number(c.total_amount),0),S=_>0?v/_:0,u={};n.forEach(h=>{const c=h.reason.replace(/_/g," ");u[c]||(u[c]={count:0,amount:0}),u[c].count++,u[c].amount+=Number(h.total_amount)});const b={};n.forEach(h=>{const c=h.refund_method.replace(/_/g," ");b[c]||(b[c]={count:0,amount:0}),b[c].count++,b[c].amount+=Number(h.total_amount)});const w={};n.forEach(h=>{var c;(c=h.refund_items)==null||c.forEach(j=>{const R=j.condition;w[R]||(w[R]={count:0,amount:0}),w[R].count++,w[R].amount+=Number(j.total_price)})});const U=[],te=Math.ceil((o.getTime()-a.getTime())/(1e3*60*60*24));for(let h=0;h<te;h++){const c=new Date(a);c.setDate(c.getDate()+h);const j=z(c,"yyyy-MM-dd"),R=n.filter($=>z(new Date($.created_at),"yyyy-MM-dd")===j);U.push({date:z(c,"MMM dd"),count:R.length,amount:R.reduce(($,O)=>$+Number(O.total_amount),0)})}const g={};n.forEach(h=>{var c;(c=h.refund_items)==null||c.forEach(j=>{var O;const R=j.product_id,$=((O=j.product)==null?void 0:O.name)||"Unknown Product";g[R]||(g[R]={count:0,amount:0,name:$}),g[R].count+=j.quantity,g[R].amount+=Number(j.total_price)})});const G=Object.values(g).sort((h,c)=>c.amount-h.amount).slice(0,10).map(h=>({product_name:h.name,count:h.count,amount:h.amount}));F({totalRefunds:_,totalAmount:v,averageRefundAmount:S,refundRate:0,byReason:u,byMethod:b,byCondition:w,dailyTrend:U,topRefundedProducts:G})}catch(a){f(a.message||"Failed to fetch analytics data")}finally{m(!1)}}},I={chart:{type:"area",height:300,toolbar:{show:!1}},dataLabels:{enabled:!1},stroke:{curve:"smooth",width:2},xaxis:{categories:i.dailyTrend.map(a=>a.date),labels:{style:{fontSize:"12px"}}},yaxis:[{title:{text:"Refund Count"},labels:{style:{fontSize:"12px"}}},{opposite:!0,title:{text:"Amount"},labels:{style:{fontSize:"12px"},formatter:a=>E(a)}}],colors:["#3B82F6","#EF4444"],fill:{type:"gradient",gradient:{shadeIntensity:1,opacityFrom:.7,opacityTo:.3}}},L=[{name:"Refund Count",data:i.dailyTrend.map(a=>a.count),yAxisIndex:0},{name:"Refund Amount",data:i.dailyTrend.map(a=>a.amount),yAxisIndex:1}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Refund Analytics"}),e.jsx("p",{className:"text-gray-600",children:"Analyze refund patterns and trends"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs(Z,{value:P,onChange:a=>A(a.target.value),className:"w-40",children:[e.jsx("option",{value:"today",children:"Today"}),e.jsx("option",{value:"7days",children:"Last 7 days"}),e.jsx("option",{value:"30days",children:"Last 30 days"}),e.jsx("option",{value:"90days",children:"Last 90 days"})]}),e.jsx(C,{color:"light",onClick:N,disabled:t,children:e.jsx(fe,{className:`h-4 w-4 ${t?"animate-spin":""}`})})]})]}),T&&e.jsxs(de,{color:"failure",children:[e.jsx("h3",{className:"font-medium",children:"Error"}),e.jsx("p",{children:T})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsx(p,{className:"p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total Refunds"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.totalRefunds})]}),e.jsx(me,{className:"h-8 w-8 text-blue-500"})]})}),e.jsx(p,{className:"p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total Amount"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:E(i.totalAmount)})]}),e.jsx(he,{className:"h-8 w-8 text-red-500"})]})}),e.jsx(p,{className:"p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Average Refund"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:E(i.averageRefundAmount)})]}),e.jsx(me,{className:"h-8 w-8 text-yellow-500"})]})}),e.jsx(p,{className:"p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Refund Rate"}),e.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[i.refundRate.toFixed(1),"%"]})]}),e.jsx(he,{className:"h-8 w-8 text-green-500"})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(p,{className:"p-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Refund Trend"}),t?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(ie,{size:"xl"})}):e.jsx(Pe,{options:I,series:L,type:"area",height:300})]}),e.jsxs(p,{className:"p-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Most Refunded Products"}),e.jsx("div",{className:"space-y-3",children:i.topRefundedProducts.slice(0,5).map((a,o)=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"font-medium truncate",children:a.product_name}),e.jsxs("p",{className:"text-sm text-gray-600",children:[a.count," items"]})]}),e.jsx("div",{className:"text-right",children:e.jsx("p",{className:"font-medium text-red-600",children:E(a.amount)})})]},o))})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs(p,{className:"p-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Refunds by Reason"}),e.jsx("div",{className:"space-y-2",children:Object.entries(i.byReason).map(([a,o])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium capitalize",children:a}),e.jsxs("p",{className:"text-sm text-gray-600",children:[o.count," refunds"]})]}),e.jsx("p",{className:"font-medium",children:E(o.amount)})]},a))})]}),e.jsxs(p,{className:"p-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Refunds by Method"}),e.jsx("div",{className:"space-y-2",children:Object.entries(i.byMethod).map(([a,o])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium capitalize",children:a}),e.jsxs("p",{className:"text-sm text-gray-600",children:[o.count," refunds"]})]}),e.jsx("p",{className:"font-medium",children:E(o.amount)})]},a))})]}),e.jsxs(p,{className:"p-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Items by Condition"}),e.jsx("div",{className:"space-y-2",children:Object.entries(i.byCondition).map(([a,o])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx(q,{color:a==="new"?"success":a==="used"?"warning":a==="damaged"?"failure":"gray",children:a}),e.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:[o.count," items"]})]}),e.jsx("p",{className:"font-medium",children:E(o.amount)})]},a))})]})]})]})},as=()=>{const{currentOrganization:l}=ee(),{user:E}=ve(),t=se(),[m,T]=d.useState([]),[f,P]=d.useState(null),[A,i]=d.useState(!1),[F,y]=d.useState(null),[N,I]=d.useState("list"),[L,a]=d.useState(!1),[o,D]=d.useState(!1),[x,n]=d.useState(null),[_,v]=d.useState(null),[S,u]=d.useState(1),[b,w]=d.useState(10),[U,te]=d.useState(0),[g,G]=d.useState({}),[h,c]=d.useState(!1),[j,R]=d.useState(null),[$,O]=d.useState(!1),J=async()=>{if(l){i(!0);try{const s=await X.getRefunds(l.id,{limit:b,offset:(S-1)*b,filters:g,sortBy:"created_at",sortOrder:"desc"});s.success?(T(s.data||[]),te(s.total_count||0)):y(s.error||"Failed to load refunds")}catch{y("Failed to load refunds")}finally{i(!1)}}},ae=async()=>{if(l)try{const s=await X.getRefundSummary(l.id);P(s)}catch(s){console.error("Failed to load refund summary:",s)}},ce=async(s,H,Y)=>{if(!(!l||!E)){i(!0);try{const V=await X.processRefund({refund_id:s,approved:H,approval_notes:Y,processed_by:E.id});V.success?(await J(),await ae(),R(null),O(!1)):y(V.error||"Failed to process refund")}catch{y("Failed to process refund")}finally{i(!1)}}},ye=s=>{console.log("Credit memo for refund:",s),n(s),a(!0)},ge=s=>{var Y;console.log("Refund object:",s),console.log("Original sale ID:",s.original_sale_id),console.log("Original sale object:",s.original_sale);const H=s.original_sale_id||((Y=s.original_sale)==null?void 0:Y.id);if(!H){console.error("No sale ID found in refund object");return}v(H),D(!0)};d.useEffect(()=>{J()},[l,S,b,g]),d.useEffect(()=>{ae()},[l]);const oe=s=>{switch(s){case k.PENDING:return"warning";case k.APPROVED:return"info";case k.PROCESSED:return"success";case k.REJECTED:return"failure";case k.CANCELLED:return"gray";default:return"gray"}},xe=s=>{switch(s){case B.FULL:return"blue";case B.PARTIAL:return"purple";case B.EXCHANGE:return"green";case B.STORE_CREDIT:return"yellow";default:return"gray"}},be=Math.ceil(U/b),Ne=()=>{if(m.length===0){y("No refunds to export");return}He(m)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Refund Management"}),e.jsx("p",{className:"text-gray-600",children:"Process and manage customer refunds"})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs(C,{onClick:Ne,color:"light",children:[e.jsx(we,{className:"h-4 w-4 mr-2"}),"Export"]}),e.jsxs(C,{onClick:J,disabled:A,color:"light",children:[e.jsx(Re,{className:`h-4 w-4 mr-2 ${A?"animate-spin":""}`}),"Refresh"]}),e.jsxs(C,{onClick:()=>c(!0),children:[e.jsx(Ce,{className:"h-4 w-4 mr-2"}),"New Refund"]})]})]}),F&&e.jsx(de,{color:"failure",onDismiss:()=>y(null),children:F}),f&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx(p,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-blue-100 mr-4",children:e.jsx(le,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-gray-500 text-sm",children:"Total Refunds"}),e.jsx("p",{className:"text-2xl font-bold",children:f.total_refunds}),e.jsx("p",{className:"text-sm text-gray-600",children:t(f.total_amount)})]})]})}),e.jsx(p,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-yellow-100 mr-4",children:e.jsx(le,{className:"h-6 w-6 text-yellow-600"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-gray-500 text-sm",children:"Pending Approval"}),e.jsx("p",{className:"text-2xl font-bold text-yellow-600",children:f.pending_approvals}),e.jsx("p",{className:"text-sm text-gray-600",children:t(f.pending_amount)})]})]})}),e.jsx(p,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-green-100 mr-4",children:e.jsx(ue,{className:"h-6 w-6 text-green-600"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-gray-500 text-sm",children:"Processed Today"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:f.processed_today}),e.jsx("p",{className:"text-sm text-gray-600",children:t(f.processed_amount_today)})]})]})}),e.jsx(p,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-purple-100 mr-4",children:e.jsx(le,{className:"h-6 w-6 text-purple-600"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-gray-500 text-sm",children:"Avg Refund"}),e.jsx("p",{className:"text-2xl font-bold text-purple-600",children:t(f.total_refunds>0?f.total_amount/f.total_refunds:0)})]})]})})]}),e.jsx(p,{children:e.jsxs(ne,{"aria-label":"Refund tabs",onActiveTabChange:s=>I(s===0?"list":"analytics"),children:[e.jsxs(ne.Item,{title:"Refund List",active:N==="list",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4 mb-6",children:[e.jsxs(Z,{value:g.status||"",onChange:s=>G({...g,status:s.target.value||void 0}),children:[e.jsx("option",{value:"",children:"All Statuses"}),e.jsx("option",{value:k.PENDING,children:"Pending"}),e.jsx("option",{value:k.APPROVED,children:"Approved"}),e.jsx("option",{value:k.PROCESSED,children:"Processed"}),e.jsx("option",{value:k.REJECTED,children:"Rejected"}),e.jsx("option",{value:k.CANCELLED,children:"Cancelled"})]}),e.jsxs(Z,{value:g.refund_type||"",onChange:s=>G({...g,refund_type:s.target.value||void 0}),children:[e.jsx("option",{value:"",children:"All Types"}),e.jsx("option",{value:B.FULL,children:"Full Refund"}),e.jsx("option",{value:B.PARTIAL,children:"Partial Refund"}),e.jsx("option",{value:B.EXCHANGE,children:"Exchange"}),e.jsx("option",{value:B.STORE_CREDIT,children:"Store Credit"})]}),e.jsxs(Z,{value:g.refund_method||"",onChange:s=>G({...g,refund_method:s.target.value||void 0}),children:[e.jsx("option",{value:"",children:"All Methods"}),e.jsx("option",{value:Q.CASH,children:"Cash"}),e.jsx("option",{value:Q.CARD,children:"Card"}),e.jsx("option",{value:Q.STORE_CREDIT,children:"Store Credit"}),e.jsx("option",{value:Q.ORIGINAL_PAYMENT,children:"Original Payment"})]}),e.jsx(je,{type:"date",value:g.date_from||"",onChange:s=>G({...g,date_from:s.target.value||void 0}),placeholder:"From Date"}),e.jsx(je,{placeholder:"Search refunds...",value:g.search||"",onChange:s=>G({...g,search:s.target.value||void 0})})]}),A?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(ie,{size:"lg"})}):e.jsxs(e.Fragment,{children:[e.jsxs(r,{children:[e.jsxs(r.Head,{children:[e.jsx(r.HeadCell,{children:"Refund #"}),e.jsx(r.HeadCell,{children:"Original Sale"}),e.jsx(r.HeadCell,{children:"Customer"}),e.jsx(r.HeadCell,{children:"Type"}),e.jsx(r.HeadCell,{children:"Amount"}),e.jsx(r.HeadCell,{children:"Status"}),e.jsx(r.HeadCell,{children:"Date"}),e.jsx(r.HeadCell,{children:"Actions"})]}),e.jsx(r.Body,{className:"divide-y",children:m.map(s=>{var H,Y,V;return e.jsxs(r.Row,{children:[e.jsx(r.Cell,{className:"font-medium",children:s.refund_number}),e.jsx(r.Cell,{children:((H=s.original_sale)==null?void 0:H.invoice_number)||"N/A"}),e.jsx(r.Cell,{children:((V=(Y=s.original_sale)==null?void 0:Y.customer)==null?void 0:V.name)||"Walk-in Customer"}),e.jsx(r.Cell,{children:e.jsx(q,{color:xe(s.refund_type),children:s.refund_type.replace("_"," ")})}),e.jsx(r.Cell,{className:"font-medium",children:t(s.total_amount)}),e.jsx(r.Cell,{children:e.jsx(q,{color:oe(s.status),children:s.status})}),e.jsx(r.Cell,{children:new Date(s.created_at).toLocaleDateString()}),e.jsx(r.Cell,{children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(C,{size:"sm",color:"light",onClick:()=>{R(s),O(!0)},children:e.jsx(_e,{className:"h-4 w-4"})}),s.status==="pending"&&e.jsxs(e.Fragment,{children:[e.jsx(C,{size:"sm",color:"success",onClick:()=>ce(s.id,!0),children:e.jsx(ue,{className:"h-4 w-4"})}),e.jsx(C,{size:"sm",color:"failure",onClick:()=>ce(s.id,!1),children:e.jsx(Se,{className:"h-4 w-4"})})]}),s.status==="processed"&&e.jsxs(e.Fragment,{children:[e.jsx(C,{size:"sm",color:"info",onClick:()=>ye(s),title:"Generate Credit Memo",children:e.jsx(re,{className:"h-4 w-4"})}),e.jsx(C,{size:"sm",color:"purple",onClick:()=>ge(s),title:"View Annotated Receipt",children:e.jsx(De,{className:"h-4 w-4"})})]})]})})]},s.id)})})]}),m.length===0&&e.jsx("div",{className:"text-center py-8 text-gray-500",children:"No refunds found. Create your first refund to get started."}),m.length>0&&e.jsx(Te,{currentPage:S,totalPages:be,itemsPerPage:b,totalItems:U,onPageChange:u,onItemsPerPageChange:s=>{w(s),u(1)},itemName:"refunds"})]})]}),e.jsx(ne.Item,{title:"Analytics",active:N==="analytics",children:e.jsx(Ie,{})})]})}),e.jsxs(M,{show:h,onClose:()=>c(!1),size:"7xl",children:[e.jsx(M.Header,{children:"Process New Refund"}),e.jsx(M.Body,{children:e.jsx(Ee,{onRefundCreated:s=>{c(!1),J(),ae()},onClose:()=>c(!1)})})]}),e.jsxs(M,{show:$,onClose:()=>O(!1),size:"4xl",children:[e.jsx(M.Header,{children:"Refund Details"}),e.jsx(M.Body,{children:j&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Refund Number"}),e.jsx("p",{className:"text-sm",children:j.refund_number})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),e.jsx(q,{color:oe(j.status),children:j.status})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Type"}),e.jsx(q,{color:xe(j.refund_type),children:j.refund_type.replace("_"," ")})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Amount"}),e.jsx("p",{className:"text-sm font-medium",children:t(j.total_amount)})]})]}),j.reason_notes&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Reason Notes"}),e.jsx("p",{className:"text-sm",children:j.reason_notes})]}),j.refund_items&&j.refund_items.length>0&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Refunded Items"}),e.jsxs(r,{children:[e.jsxs(r.Head,{children:[e.jsx(r.HeadCell,{children:"Product"}),e.jsx(r.HeadCell,{children:"Quantity"}),e.jsx(r.HeadCell,{children:"Unit Price"}),e.jsx(r.HeadCell,{children:"Total"})]}),e.jsx(r.Body,{children:j.refund_items.map(s=>{var H;return e.jsxs(r.Row,{children:[e.jsx(r.Cell,{children:((H=s.product)==null?void 0:H.name)||"Unknown Product"}),e.jsx(r.Cell,{children:s.quantity}),e.jsx(r.Cell,{children:t(s.unit_price)}),e.jsx(r.Cell,{children:t(s.total_price)})]},s.id)})})]})]})]})}),e.jsx(M.Footer,{children:e.jsx(C,{color:"gray",onClick:()=>O(!1),children:"Close"})})]}),e.jsxs(M,{show:L,onClose:()=>{a(!1),n(null)},size:"6xl",children:[e.jsx(M.Header,{children:"Credit Memo Generator"}),e.jsx(M.Body,{children:x&&e.jsx(ke,{refund:x,onClose:()=>{a(!1),n(null)}})})]}),e.jsxs(M,{show:o,onClose:()=>{D(!1),v(null)},size:"5xl",children:[e.jsx(M.Header,{children:"Annotated Receipt"}),e.jsx(M.Body,{children:_&&e.jsx(Fe,{saleId:_,onClose:()=>{D(!1),v(null)}})})]})]})};export{as as default};
