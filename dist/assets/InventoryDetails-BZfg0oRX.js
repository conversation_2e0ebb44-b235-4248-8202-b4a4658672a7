import{ab as oe,h as de,r as i,j as e,i as _,A as S,J as T,L as F,B as p,o as Q,t as W,av as xe,aw as me,q as $,e as l,P as he,Q as ue,a7 as je,ax as U,W as pe,_ as a}from"./index-C6AV3cVN.js";import{C as v}from"./Card-yj7fueH8.js";import{a as ge}from"./product-Ca8DWaNR.js";import{g as ye}from"./inventoryTransaction-1UXV5RDN.js";import{b as V,c as J,d as be}from"./formatters-Cypx7G-j.js";import{u as fe}from"./currencyFormatter-BsFWv3sX.js";import{C as K,r as ve,B as Ne,a as we,L as Ce,P as _e,b as Se,c as Te,p as ke,d as De,e as Ie,T as Pe}from"./index-DAKkILcO.js";import"./floatInventory-k_pEQeIK.js";K.register(...ve);K.register(we,Ce,_e,Se,Te,ke,<PERSON>,Ie,Pe);const Me=()=>{var z;const{id:d}=oe(),{currentOrganization:x}=de(),N=fe(),[t,G]=i.useState(null),[X,k]=i.useState(!0),[D,y]=i.useState(null),[m,Y]=i.useState([]),[I,P]=i.useState(!0),[b,w]=i.useState(null),[Z,O]=i.useState(""),[A,B]=i.useState(null),[E,H]=i.useState(null),[ee,R]=i.useState(""),[h,C]=i.useState("details"),se={responsive:!0,interaction:{mode:"index",intersect:!1},scales:{y:{type:"linear",display:!0,position:"left",title:{display:!0,text:"Running Total"}},y1:{type:"linear",display:!0,position:"right",grid:{drawOnChartArea:!1},title:{display:!0,text:"Daily Transactions"}}},plugins:{legend:{position:"top"},title:{display:!0,text:"Inventory Movement Over Time"}}};i.useEffect(()=>{x&&d&&(L(),f())},[x,d]);const L=async()=>{if(!(!x||!d)){k(!0),y(null);try{const{product:s,error:n}=await ge(x.id,d);n?y(n):s?G(s):y("Product not found")}catch(s){y(s.message||"An error occurred while fetching the product")}finally{k(!1)}}},f=async()=>{if(!(!x||!d)){P(!0),w(null);try{const{transactions:s,error:n}=await ye(x.id,d);n?w(n):Y(s)}catch(s){w(s.message||"An error occurred while fetching transactions")}finally{P(!1)}}},te=()=>{L(),f()},re=()=>{f()},ae=()=>{O(""),B(null),H(null),R(""),f()},ne=s=>{switch(s.toLowerCase()){case"purchase":case"receipt":return e.jsx(l,{color:"success",children:"Purchase"});case"sale":return e.jsx(l,{color:"info",children:"Sale"});case"adjustment":return e.jsx(l,{color:"warning",children:"Adjustment"});case"transfer":return e.jsx(l,{color:"purple",children:"Transfer"});case"return":return e.jsx(l,{color:"pink",children:"Return"});default:return e.jsx(l,{color:"gray",children:s})}},le=s=>!s.stock_quantity||s.stock_quantity<=0?e.jsx(l,{color:"failure",children:"Out of Stock"}):s.min_stock_level&&s.stock_quantity<=s.min_stock_level?e.jsx(l,{color:"warning",children:"Low Stock"}):e.jsx(l,{color:"success",children:"In Stock"}),ie=()=>t?(t.stock_quantity||0)*(t.cost_price||0):0,ce=()=>{if(!t||!t.product_uoms||t.product_uoms.length===0)return null;const s=t.product_uoms.find(n=>n.is_default);return s?s.uom:null},q=()=>{if(!m||m.length===0)return null;const n=[...m].sort((r,o)=>new Date(r.created_at).getTime()-new Date(o.created_at).getTime()).reduce((r,o)=>{const c=new Date(o.created_at).toISOString().split("T")[0];r[c]||(r[c]={receipts:0,sales:0,adjustments:0,runningTotal:0});const j=Number(o.quantity);return["receipt","purchase"].includes(o.transaction_type.toLowerCase())?(r[c].receipts+=j,r[c].runningTotal+=j):o.transaction_type.toLowerCase()==="sale"?(r[c].sales+=Math.abs(j),r[c].runningTotal-=Math.abs(j)):o.transaction_type.toLowerCase()==="adjustment"&&(r[c].adjustments+=j,r[c].runningTotal+=j),r},{});let M=0;const g=Object.keys(n).sort();return{labels:g,datasets:[{label:"Running Total",data:g.map(r=>(M+=n[r].runningTotal,M)),borderColor:"rgb(53, 162, 235)",backgroundColor:"rgba(53, 162, 235, 0.5)",tension:.3,yAxisID:"y",type:"line"},{label:"Receipts",data:g.map(r=>n[r].receipts),backgroundColor:"rgba(75, 192, 192, 0.5)",borderColor:"rgb(75, 192, 192)",borderWidth:1,yAxisID:"y1",type:"bar"},{label:"Sales",data:g.map(r=>n[r].sales),backgroundColor:"rgba(255, 99, 132, 0.5)",borderColor:"rgb(255, 99, 132)",borderWidth:1,yAxisID:"y1",type:"bar"},{label:"Adjustments",data:g.map(r=>n[r].adjustments),backgroundColor:"rgba(255, 205, 86, 0.5)",borderColor:"rgb(255, 205, 86)",borderWidth:1,yAxisID:"y1",type:"bar"}]}};if(X)return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(v,{children:e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(_,{size:"xl"})})})});if(D||!t)return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(v,{children:e.jsxs(S,{color:"failure",icon:T,children:[e.jsx("h3",{className:"text-lg font-medium",children:"Error"}),e.jsx("p",{children:D||"Product not found"}),e.jsx("div",{className:"mt-4",children:e.jsx(F,{to:"/inventory",children:e.jsxs(p,{color:"gray",size:"sm",children:[e.jsx(Q,{className:"mr-2 h-4 w-4"}),"Back to Inventory"]})})})]})})});const u=ce();return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(v,{className:"mb-6",children:e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:t.name}),e.jsxs("p",{className:"text-gray-500",children:[t.sku&&`SKU: ${t.sku}`,t.barcode&&` | Barcode: ${t.barcode}`]})]}),e.jsxs("div",{className:"flex gap-2 mt-4 md:mt-0",children:[e.jsx(F,{to:"/inventory",children:e.jsxs(p,{color:"light",children:[e.jsx(Q,{className:"mr-2 h-5 w-5"}),"Back to Inventory"]})}),e.jsxs(p,{color:"light",onClick:te,children:[e.jsx(W,{className:"mr-2 h-5 w-5"}),"Refresh"]})]})]})}),e.jsxs(v,{children:[e.jsx("div",{className:"mb-4 border-b border-gray-200",children:e.jsxs("ul",{className:"flex flex-wrap -mb-px text-sm font-medium text-center",role:"tablist",children:[e.jsx("li",{className:"mr-2",role:"presentation",children:e.jsx("button",{className:`inline-block p-4 border-b-2 rounded-t-lg ${h==="details"?"text-blue-600 border-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,type:"button",role:"tab",onClick:()=>C("details"),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(xe,{className:"mr-2 h-5 w-5"}),"Product Details"]})})}),e.jsx("li",{className:"mr-2",role:"presentation",children:e.jsx("button",{className:`inline-block p-4 border-b-2 rounded-t-lg ${h==="chart"?"text-blue-600 border-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,type:"button",role:"tab",onClick:()=>C("chart"),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(me,{className:"mr-2 h-5 w-5"}),"Inventory Movement"]})})}),e.jsx("li",{className:"mr-2",role:"presentation",children:e.jsx("button",{className:`inline-block p-4 border-b-2 rounded-t-lg ${h==="transactions"?"text-blue-600 border-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,type:"button",role:"tab",onClick:()=>C("transactions"),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx($,{className:"mr-2 h-5 w-5"}),"Transaction History"]})})})]})}),h==="details"&&e.jsxs("div",{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"text-xl font-bold mb-4",children:"Inventory Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Current Stock"}),e.jsx("p",{className:"text-xl font-bold",children:V(t.stock_quantity||0,u==null?void 0:u.code)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Status"}),le(t)]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Reorder Point"}),e.jsx("p",{children:V(t.min_stock_level||0,u==null?void 0:u.code)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Inventory Value"}),e.jsx("p",{children:N(ie())})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"text-xl font-bold mb-4",children:"Product Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Category"}),e.jsx("p",{children:((z=t.category)==null?void 0:z.name)||"Uncategorized"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Unit Price"}),e.jsx("p",{children:N(t.unit_price||0)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Cost Price"}),e.jsx("p",{children:N(t.cost_price||0)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Status"}),e.jsx(l,{color:t.is_active?"success":"gray",children:t.is_active?"Active":"Inactive"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"text-xl font-bold mb-4",children:"Units of Measurement"}),t.product_uoms&&t.product_uoms.length>0?e.jsx("div",{className:"space-y-4",children:t.product_uoms.map(s=>e.jsxs("div",{className:"p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("p",{className:"font-medium",children:s.uom.name}),s.is_default&&e.jsx(l,{color:"blue",children:"Default"})]}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Code: ",s.uom.code]}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Conversion Factor: ",s.conversion_factor]}),e.jsxs("div",{className:"flex gap-2 mt-2",children:[s.is_purchasing_unit&&e.jsx(l,{color:"purple",size:"sm",children:"Purchasing"}),s.is_selling_unit&&e.jsx(l,{color:"green",size:"sm",children:"Selling"})]})]},s.id))}):e.jsx("p",{className:"text-gray-500",children:"No units of measurement defined"})]})]}),e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4 mb-6",children:[e.jsx("h5",{className:"text-xl font-bold mb-4",children:"Product Description"}),e.jsx("p",{className:"whitespace-pre-line",children:t.description||"No description available"})]})]}),h==="chart"&&e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6",children:e.jsx("h5",{className:"text-xl font-bold",children:"Inventory Movement Chart"})}),I?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(_,{size:"xl"})}):b?e.jsx(S,{color:"failure",icon:T,children:b}):m.length===0?e.jsx("div",{className:"p-8 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No transactions found for this product"})}):e.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[e.jsx("div",{className:"h-96",children:q()&&e.jsx(Ne,{options:se,data:q()||{labels:[],datasets:[]}})}),e.jsxs("div",{className:"mt-4 text-sm text-gray-500 text-center",children:[e.jsx("p",{children:"This chart shows the inventory movement over time, including purchases, sales, and adjustments."}),e.jsx("p",{children:"The line represents the running total inventory level."})]})]})]}),h==="transactions"&&e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6",children:[e.jsx("h5",{className:"text-xl font-bold",children:"Transaction History"}),e.jsxs(p,{color:"light",onClick:()=>window.print(),children:[e.jsx($,{className:"mr-2 h-5 w-5"}),"Export"]})]}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[e.jsx("div",{className:"flex-1",children:e.jsx(he,{id:"search",type:"text",placeholder:"Search transactions...",value:ee,onChange:s=>R(s.target.value),icon:ue})}),e.jsx("div",{className:"w-full md:w-48",children:e.jsxs(je,{id:"transactionType",value:Z,onChange:s=>O(s.target.value),children:[e.jsx("option",{value:"",children:"All Types"}),e.jsx("option",{value:"purchase",children:"Purchases"}),e.jsx("option",{value:"receipt",children:"Receipts"}),e.jsx("option",{value:"sale",children:"Sales"}),e.jsx("option",{value:"adjustment",children:"Adjustments"}),e.jsx("option",{value:"transfer",children:"Transfers"}),e.jsx("option",{value:"return",children:"Returns"})]})}),e.jsx("div",{className:"w-full md:w-48",children:e.jsx(U,{value:A?J(A.toISOString()):"",onSelectedDateChanged:B,placeholder:"Start Date"})}),e.jsx("div",{className:"w-full md:w-48",children:e.jsx(U,{value:E?J(E.toISOString()):"",onSelectedDateChanged:H,placeholder:"End Date"})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(p,{color:"primary",onClick:re,children:[e.jsx(pe,{className:"mr-2 h-5 w-5"}),"Filter"]}),e.jsxs(p,{color:"gray",onClick:ae,children:[e.jsx(W,{className:"mr-2 h-5 w-5"}),"Reset"]})]})]}),I?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(_,{size:"xl"})}):b?e.jsx(S,{color:"failure",icon:T,children:b}):m.length===0?e.jsx("div",{className:"p-8 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No transactions found for this product"})}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(a,{hoverable:!0,children:[e.jsxs(a.Head,{children:[e.jsx(a.HeadCell,{children:"Date/Time"}),e.jsx(a.HeadCell,{children:"Type"}),e.jsx(a.HeadCell,{children:"Quantity"}),e.jsx(a.HeadCell,{children:"Reference"}),e.jsx(a.HeadCell,{children:"Notes"})]}),e.jsx(a.Body,{className:"divide-y",children:m.map(s=>e.jsxs(a.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(a.Cell,{children:be(s.created_at)}),e.jsx(a.Cell,{children:ne(s.transaction_type)}),e.jsx(a.Cell,{children:s.quantity}),e.jsx(a.Cell,{children:s.reference_type&&s.reference_id?e.jsx("span",{className:"text-sm",children:s.reference_type.replace(/_/g," ")}):e.jsx("span",{className:"text-gray-500",children:"-"})}),e.jsx(a.Cell,{children:e.jsx("div",{className:"max-w-xs truncate",children:s.notes||"-"})})]},s.id))})]})})]})]})]})};export{Me as default};
