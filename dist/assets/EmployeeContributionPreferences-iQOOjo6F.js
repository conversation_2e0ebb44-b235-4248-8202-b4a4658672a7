import{d as C,h as w,r as t,j as e,i as _,B as f,o as E,A as P,G as S,Y as H,_ as r,e as c,a3 as B}from"./index-C6AV3cVN.js";import{C as O}from"./Card-yj7fueH8.js";import{getEmployeesWithContributionPreferences as T,deleteEmployeeContributionPreferences as z}from"./employeeContributionPreferences-DopMjMg8.js";import{P as k}from"./PageTitle-FHPo8gWi.js";import{f as o}from"./formatters-Cypx7G-j.js";const G=()=>{const g=C(),{currentOrganization:a}=w(),[d,x]=t.useState([]),[y,m]=t.useState(!0),[h,n]=t.useState(null),[u,j]=t.useState(null);t.useEffect(()=>{(async()=>{if(a)try{m(!0);const{employees:i,error:l}=await T(a.id);l?n(l):i&&x(i)}catch(i){n(i.message)}finally{m(!1)}})()},[a]);const b=async(s,i)=>{if(a){j(i);try{const{success:l,error:p}=await z(a.id,s);p?n(p):l&&x(N=>N.filter(v=>v.id!==i))}catch(l){n(l.message)}finally{j(null)}}};return y?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(_,{size:"xl"})})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsxs(f,{color:"gray",className:"mr-4",onClick:()=>g("/payroll/settings"),children:[e.jsx(E,{className:"mr-2 h-5 w-5"}),"Back to Payroll Settings"]}),e.jsx(k,{title:"Employee Contribution Preferences"})]})}),h&&e.jsx(P,{color:"failure",icon:S,className:"mb-4",children:h}),e.jsx(O,{children:d.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(H,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Custom Preferences"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No employees have custom contribution preferences set. Employees will use standard Philippine law calculations."}),e.jsx("p",{className:"text-sm text-gray-500",children:`To set custom preferences, edit an employee's contributions in any payroll period and enable "Save as employee-specific contribution preferences".`})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(r,{striped:!0,children:[e.jsxs(r.Head,{children:[e.jsx(r.HeadCell,{children:"Employee"}),e.jsx(r.HeadCell,{children:"SSS"}),e.jsx(r.HeadCell,{children:"PhilHealth"}),e.jsx(r.HeadCell,{children:"Pag-IBIG"}),e.jsx(r.HeadCell,{children:"Withholding Tax"}),e.jsx(r.HeadCell,{children:"Notes"}),e.jsx(r.HeadCell,{children:"Actions"})]}),e.jsx(r.Body,{children:d.map(s=>e.jsxs(r.Row,{children:[e.jsx(r.Cell,{className:"font-medium",children:e.jsxs("div",{children:[e.jsxs("div",{className:"font-semibold",children:[s.employee.first_name," ",s.employee.last_name]}),s.employee.employee_number&&e.jsxs("div",{className:"text-sm text-gray-500",children:["#",s.employee.employee_number]})]})}),e.jsx(r.Cell,{children:s.sss_contribution_override?e.jsx(c,{color:"warning",children:o(s.sss_contribution_override)}):e.jsx("span",{className:"text-gray-500",children:"Standard"})}),e.jsx(r.Cell,{children:s.philhealth_contribution_override?e.jsx(c,{color:"warning",children:o(s.philhealth_contribution_override)}):e.jsx("span",{className:"text-gray-500",children:"Standard"})}),e.jsx(r.Cell,{children:s.pagibig_contribution_override?e.jsx(c,{color:"warning",children:o(s.pagibig_contribution_override)}):e.jsx("span",{className:"text-gray-500",children:"Standard"})}),e.jsx(r.Cell,{children:s.withholding_tax_override?e.jsx(c,{color:"warning",children:o(s.withholding_tax_override)}):e.jsx("span",{className:"text-gray-500",children:"Standard"})}),e.jsx(r.Cell,{children:e.jsx("div",{className:"max-w-xs truncate",title:s.notes||"",children:s.notes||"-"})}),e.jsx(r.Cell,{children:e.jsx("div",{className:"flex space-x-2",children:e.jsx(f,{size:"xs",color:"failure",onClick:()=>b(s.employee_id,s.id),disabled:u===s.id,isProcessing:u===s.id,children:e.jsx(B,{className:"h-3 w-3"})})})})]},s.id))})]})})}),e.jsxs("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-800 mb-2",children:"How Employee Preferences Work"}),e.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[e.jsx("li",{children:"• Each employee can have personalized government contribution amounts"}),e.jsx("li",{children:"• These preferences automatically apply to all future payroll periods"}),e.jsx("li",{children:'• You can set preferences by editing any payroll item and enabling "Save as employee-specific contribution preferences"'}),e.jsx("li",{children:"• Employees without custom preferences use standard Philippine law calculations"}),e.jsx("li",{children:"• You can always recalculate any payroll item to reset to legal amounts"})]})]})]})};export{G as default};
