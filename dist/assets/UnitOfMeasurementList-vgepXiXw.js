import{r as a,j as e,A as O,J as H,a6 as P,P as _,ad as Z,a8 as $,B as m,i as F,h as ee,a0 as B,Q as se,_ as n,e as re,a2 as ae,a3 as te,M as c,a9 as ne,cl as le,cm as ie,cn as ce}from"./index-C6AV3cVN.js";import{C as oe}from"./Card-yj7fueH8.js";import{P as de}from"./Pagination-CVEzfctr.js";const T=({initialData:t,onSubmit:h,isSubmitting:y,error:N})=>{const[o,f]=a.useState(()=>({code:"",name:"",description:"",is_active:!0,...t}));a.useEffect(()=>{t&&f(i=>({...i,...t}))},[t]);const u=i=>{const{name:j,value:g}=i.target;f(U=>({...U,[j]:g}))},C=i=>{f(j=>({...j,is_active:i}))},E=async i=>{i.preventDefault(),await h(o)};return e.jsxs("form",{onSubmit:E,className:"space-y-6",children:[N&&e.jsx(O,{color:"failure",icon:H,children:N}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(P,{htmlFor:"code",value:"Code *"})}),e.jsx(_,{id:"code",name:"code",value:o.code||"",onChange:u,required:!0,placeholder:"e.g., pcs, box, kg"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Short code used in reports and labels"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(P,{htmlFor:"name",value:"Name *"})}),e.jsx(_,{id:"name",name:"name",value:o.name||"",onChange:u,required:!0,placeholder:"e.g., Pieces, Box, Kilogram"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Full name displayed in forms"})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(P,{htmlFor:"description",value:"Description"})}),e.jsx(Z,{id:"description",name:"description",value:o.description||"",onChange:u,rows:3,placeholder:"Optional description of this unit of measurement"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{checked:o.is_active||!1,onChange:C,label:"Active"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Inactive units won't appear in selection dropdowns"})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(m,{type:"submit",color:"primary",disabled:y,children:y?e.jsxs(e.Fragment,{children:[e.jsx(F,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Unit of Measurement"})})]})},xe=()=>{const{currentOrganization:t}=ee(),[h,y]=a.useState([]),[N,o]=a.useState(!0),[f,u]=a.useState(null),[C,E]=a.useState(""),[i,j]=a.useState(1),[g,U]=a.useState(10),[L,A]=a.useState(!1),[Q,k]=a.useState(!1),[D,b]=a.useState(!1),[d,S]=a.useState(null),[w,p]=a.useState(!1),[M,l]=a.useState(null),v=async()=>{if(t){o(!0),u(null);try{const{uoms:s,error:r}=await ne(t.id,{searchQuery:C||void 0});r?u(r):y(s)}catch(s){u(s.message||"An error occurred while fetching units of measurement")}finally{o(!1)}}};a.useEffect(()=>{v()},[t]);const q=s=>{s.preventDefault(),v()},I=()=>{S(null),l(null),A(!0)},R=s=>{S(s),l(null),k(!0)},Y=s=>{S(s),b(!0)},J=async s=>{if(t){p(!0),l(null);try{const{uom:r,error:x}=await le(t.id,{code:s.code||"",name:s.name||"",description:s.description,is_active:s.is_active!==void 0?s.is_active:!0,created_by:null});x?(l(x),console.error("Error creating UoM:",x)):(console.log("UoM created successfully:",r),A(!1),v())}catch(r){console.error("Exception creating UoM:",r),l(r.message||"An error occurred while creating the unit of measurement")}finally{p(!1)}}},K=async s=>{if(!(!t||!d)){p(!0),l(null);try{const{uom:r,error:x}=await ie(t.id,d.id,{code:s.code,name:s.name,description:s.description,is_active:s.is_active});x?(l(x),console.error("Error updating UoM:",x)):(console.log("UoM updated successfully:",r),k(!1),v())}catch(r){console.error("Exception updating UoM:",r),l(r.message||"An error occurred while updating the unit of measurement")}finally{p(!1)}}},G=async()=>{if(!(!t||!d)){p(!0),l(null);try{const{success:s,error:r}=await ce(t.id,d.id);r?l(r):s&&(b(!1),S(null),v())}catch(s){l(s.message||"An error occurred while deleting the unit of measurement")}finally{p(!1)}}},V=Math.ceil(h.length/g),z=i*g,W=z-g,X=h.slice(W,z);return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(oe,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Units of Measurement"}),e.jsx("p",{className:"text-gray-500",children:"Manage units of measurement for your products"})]}),e.jsxs(m,{color:"primary",onClick:I,children:[e.jsx(B,{className:"mr-2 h-5 w-5"}),"Add Unit"]})]}),e.jsx("form",{onSubmit:q,className:"mb-6",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(_,{id:"search",type:"text",placeholder:"Search by name or code",value:C,onChange:s=>E(s.target.value),className:"flex-1",icon:se}),e.jsx(m,{type:"submit",children:"Search"})]})}),f&&e.jsx(O,{color:"failure",icon:H,className:"mb-4",children:f}),N?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(F,{size:"xl"})}):h.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No units of measurement found"}),e.jsxs(m,{color:"primary",size:"sm",onClick:I,children:[e.jsx(B,{className:"mr-2 h-4 w-4"}),"Add Your First Unit"]})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(n,{hoverable:!0,children:[e.jsxs(n.Head,{children:[e.jsx(n.HeadCell,{children:"Code"}),e.jsx(n.HeadCell,{children:"Name"}),e.jsx(n.HeadCell,{children:"Description"}),e.jsx(n.HeadCell,{children:"Status"}),e.jsx(n.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(n.Body,{className:"divide-y",children:X.map(s=>e.jsxs(n.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(n.Cell,{className:"font-medium",children:s.code}),e.jsx(n.Cell,{children:s.name}),e.jsx(n.Cell,{children:s.description||e.jsx("span",{className:"text-gray-400",children:"No description"})}),e.jsx(n.Cell,{children:e.jsx(re,{color:s.is_active?"success":"gray",children:s.is_active?"Active":"Inactive"})}),e.jsx(n.Cell,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(m,{color:"light",size:"xs",onClick:()=>R(s),children:e.jsx(ae,{className:"h-4 w-4"})}),e.jsx(m,{color:"failure",size:"xs",onClick:()=>Y(s),children:e.jsx(te,{className:"h-4 w-4"})})]})})]},s.id))})]})}),h.length>0&&e.jsx(de,{currentPage:i,totalPages:V,itemsPerPage:g,totalItems:h.length,onPageChange:j,onItemsPerPageChange:s=>{U(s),j(1)},itemName:"units"})]}),e.jsxs(c,{show:L,onClose:()=>A(!1),children:[e.jsx(c.Header,{children:"Add Unit of Measurement"}),e.jsx(c.Body,{children:e.jsx(T,{onSubmit:J,isSubmitting:w,error:M||void 0})})]}),e.jsxs(c,{show:Q,onClose:()=>k(!1),children:[e.jsx(c.Header,{children:"Edit Unit of Measurement"}),e.jsx(c.Body,{children:e.jsx(T,{initialData:d||void 0,onSubmit:K,isSubmitting:w,error:M||void 0})})]}),e.jsxs(c,{show:D,onClose:()=>b(!1),children:[e.jsx(c.Header,{children:"Confirm Deletion"}),e.jsx(c.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(H,{className:"mx-auto mb-4 h-14 w-14 text-red-500"}),e.jsxs("h3",{className:"mb-5 text-lg font-normal text-gray-500",children:["Are you sure you want to delete ",d==null?void 0:d.name,"?"]}),M&&e.jsx(O,{color:"failure",className:"mb-4",children:M}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(m,{color:"failure",onClick:G,disabled:w,children:w?e.jsx(F,{size:"sm"}):"Yes, delete"}),e.jsx(m,{color:"gray",onClick:()=>b(!1),children:"No, cancel"})]})]})})]})]})};export{xe as default};
