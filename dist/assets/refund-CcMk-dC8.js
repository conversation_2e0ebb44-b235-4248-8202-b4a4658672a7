import{s as c}from"./index-C6AV3cVN.js";import{c as q}from"./inventoryTransaction-1UXV5RDN.js";const S=async y=>{try{const r=new Date,e=r.getFullYear().toString()+(r.getMonth()+1).toString().padStart(2,"0")+r.getDate().toString().padStart(2,"0"),{count:o,error:s}=await c.from("sales").select("*",{count:"exact",head:!0}).eq("organization_id",y).like("invoice_number",`INV-${e}%`);if(s)return console.error("Error getting sales count:",s),{invoiceNumber:"",error:s.message};const n=((o||0)+1).toString().padStart(4,"0");return{invoiceNumber:`INV-${e}-${n}`}}catch(r){return console.error("Error generating invoice number:",r),{invoiceNumber:"",error:r.message}}},x=async(y,r,e)=>{try{const{invoiceNumber:o,error:s}=await S(y);if(s)return{error:s};const{data:n,error:u}=await c.rpc("create_sale_with_items",{p_organization_id:y,p_customer_id:e.customer_id||null,p_invoice_number:o,p_status:"completed",p_subtotal:e.subtotal,p_tax_amount:e.tax_amount,p_discount_amount:e.discount_amount,p_total_amount:e.total_amount,p_payment_method:e.payment_method,p_notes:e.notes||null,p_created_by:r,p_loyalty_points_used:e.loyalty_points_used||0,p_loyalty_points_discount:e.loyalty_points_discount||0,p_cash_tendered:e.cash_tendered||null,p_change_amount:e.change_amount||null,p_items:e.items.map(i=>({product_id:i.product_id,quantity:i.quantity,unit_price:i.unit_price,uom_id:i.uom_id,base_quantity:i.base_quantity,tax_rate:i.tax_rate,tax_amount:i.unit_price*i.quantity*i.tax_rate/100,discount_amount:i.discount_amount,total_amount:i.unit_price*i.quantity-i.discount_amount,notes:i.notes||""}))});if(u)return console.error("Error creating sale:",u),{error:u.message};const{data:d,error:l}=await c.from("sales").select(`
        *,
        customer:customer_id(*),
        items:sale_items(
          *,
          product:product_id(*)
        )
      `).eq("id",n.sale_id).single();return l?(console.error("Error fetching created sale:",l),{error:l.message}):{sale:d}}catch(o){return console.error("Error in createSale:",o),{error:o.message}}},k=async(y,r)=>{try{const{data:e,error:o}=await c.from("sales").select(`
        *,
        customer:customer_id(*),
        items:sale_items(
          *,
          product:product_id(*)
        )
      `).eq("organization_id",y).eq("id",r).single();if(o)return console.error("Error fetching sale:",o),{error:o.message};let s=null;if(e.created_by){const{data:n,error:u}=await c.from("profiles").select("id, first_name, last_name, avatar_url").eq("id",e.created_by).single();!u&&n&&(s=n)}return{sale:{...e,cashier:s}}}catch(e){return console.error("Error in getSaleById:",e),{error:e.message}}},N=async(y,r)=>{try{const{limit:e=50,offset:o=0,sortBy:s="sale_date",sortOrder:n="desc",startDate:u,endDate:d,status:l,customerId:i,searchQuery:a}=r||{};let t=c.from("sales").select(`
        *,
        customer:customer_id(id, name, email, phone),
        items:sale_items(
          id,
          product_id,
          quantity,
          unit_price,
          total_amount
        )
      `,{count:"exact"}).eq("organization_id",y);u&&(t=t.gte("sale_date",u)),d&&(t=t.lte("sale_date",d)),l&&(t=t.eq("status",l)),i&&(t=t.eq("customer_id",i)),a&&(t=t.or(`invoice_number.ilike.%${a}%,notes.ilike.%${a}%`)),t=t.order(s,{ascending:n==="asc"}),t=t.range(o,o+e-1);const{data:_,error:f,count:m}=await t;if(f)return console.error("Error fetching sales:",f),{sales:[],count:0,error:f.message};if(!_||_.length===0)return{sales:[],count:m||0};const g=[...new Set(_.map(h=>h.created_by).filter(Boolean))];let p={};if(g.length>0){const{data:h,error:E}=await c.from("profiles").select("id, first_name, last_name, avatar_url").in("id",g);!E&&h&&(p=h.reduce((v,w)=>(v[w.id]=w,v),{}))}return{sales:_.map(h=>({...h,cashier:p[h.created_by]||null})),count:m||0}}catch(e){return console.error("Error in getSales:",e),{sales:[],count:0,error:e.message}}};class F{static async validateRefund(r,e){var o;try{const{data:s,error:n}=await c.from("sales").select(`
          *,
          customer:customers (
            id,
            name,
            email,
            phone
          ),
          sale_items (
            id,
            product_id,
            quantity,
            unit_price,
            total_amount,
            product:products (
              id,
              name,
              sku
            )
          )
        `).eq("id",r).eq("organization_id",e).single();if(n||!s)return{can_refund:!1,reasons:[n?`Database error: ${n.message}`:"Sale not found"],max_refund_amount:0,eligible_items:[]};const u=new Date(s.sale_date),d=Math.floor((Date.now()-u.getTime())/(1e3*60*60*24)),l=30;if(d>l)return{can_refund:!1,reasons:[`Sale is older than ${l} days`],max_refund_amount:0,eligible_items:[]};const{data:i}=await c.from("refunds").select(`
          id,
          status,
          refund_items (
            sale_item_id,
            quantity
          )
        `).eq("original_sale_id",r).neq("status","cancelled").neq("status","rejected"),a={};i==null||i.forEach(f=>{var m;(m=f.refund_items)==null||m.forEach(g=>{a[g.sale_item_id]=(a[g.sale_item_id]||0)+g.quantity})});const t=((o=s.sale_items)==null?void 0:o.map(f=>{const m=a[f.id]||0,g=f.quantity-m;return{sale_item_id:f.id,product_id:f.product_id,max_quantity:g,unit_price:f.unit_price}}).filter(f=>f.max_quantity>0))||[],_=t.reduce((f,m)=>f+m.max_quantity*m.unit_price,0);return{can_refund:t.length>0,reasons:t.length===0?["All items have already been refunded"]:[],max_refund_amount:_,eligible_items:t}}catch(s){return console.error("Error validating refund:",s),{can_refund:!1,reasons:["Error validating refund"],max_refund_amount:0,eligible_items:[]}}}static async createRefund(r,e){try{const o=await this.validateRefund(r.original_sale_id,e);if(!o.can_refund)return{success:!1,error:`Cannot process refund: ${o.reasons.join(", ")}`};const{data:s,error:n}=await c.rpc("generate_refund_number",{org_id:e});if(n)return console.error("Error generating refund number:",n),{success:!1,error:`Failed to generate refund number: ${n.message}`};if(!s)return{success:!1,error:"Failed to generate refund number - no number returned"};const u=r.items.reduce((p,b)=>p+b.quantity*b.unit_price,0),d=0,l=u-(r.restocking_fee||0),{data:{user:i}}=await c.auth.getUser();if(!i)return{success:!1,error:"User not authenticated"};const a={organization_id:e,refund_number:s,original_sale_id:r.original_sale_id,customer_id:r.customer_id,refund_type:r.refund_type,reason:r.reason,reason_notes:r.reason_notes,subtotal:u,tax_amount:d,total_amount:l,restocking_fee:r.restocking_fee||0,refund_method:r.refund_method,requires_approval:r.requires_approval||l>1e3,status:r.requires_approval||l>1e3?"pending":"approved",created_by:i.id},{data:t,error:_}=await c.from("refunds").insert(a).select().single();if(_||!t)return{success:!1,error:"Failed to create refund"};const f=r.items.map(p=>({refund_id:t.id,sale_item_id:p.sale_item_id,product_id:p.product_id,quantity:p.quantity,unit_price:p.unit_price,total_price:p.quantity*p.unit_price,condition:p.condition,restore_inventory:p.restore_inventory,notes:p.notes})),{error:m}=await c.from("refund_items").insert(f);return m?(await c.from("refunds").delete().eq("id",t.id),{success:!1,error:"Failed to create refund items"}):(t.status==="approved"&&await this.processRefund({refund_id:t.id,approved:!0,processed_by:i.id}),{success:!0,data:(await this.getRefundById(t.id)).data})}catch(o){return console.error("Error creating refund:",o),{success:!1,error:"Failed to create refund"}}}static async processRefund(r){try{const{data:e,error:o}=await c.from("refunds").select(`
          *,
          refund_items (
            *,
            products (id, name, sku)
          )
        `).eq("id",r.refund_id).single();if(o||!e)return{success:!1,error:"Refund not found"};if(e.status!=="pending"&&e.status!=="approved")return{success:!1,error:"Refund cannot be processed in current status"};const s={status:r.approved?"processed":"rejected",approved_by:r.processed_by,approved_at:new Date().toISOString(),approval_notes:r.approval_notes,processed_by:r.processed_by,processed_at:new Date().toISOString()},{error:n}=await c.from("refunds").update(s).eq("id",r.refund_id);if(n)return{success:!1,error:"Failed to update refund status"};if(r.approved){const{data:{user:d}}=await c.auth.getUser(),l=(d==null?void 0:d.id)||r.processed_by;await this.restoreInventory(e.refund_items,r.refund_id,l),e.refund_method==="store_credit"&&await this.createStoreCredit(e),await this.updateOriginalSaleStatus(e.original_sale_id,e.refund_type,e.total_amount)}return{success:!0,data:(await this.getRefundById(r.refund_id)).data}}catch(e){return console.error("Error processing refund:",e),{success:!1,error:"Failed to process refund"}}}static async getRefundById(r){try{const{data:e,error:o}=await c.from("refunds").select(`
          *,
          original_sale:sales (
            id,
            invoice_number,
            total_amount,
            sale_date,
            customer:customers (
              id,
              name,
              email,
              phone
            )
          ),
          refund_items (
            *,
            product:products (
              id,
              name,
              sku,
              unit_price,
              image_url
            ),
            original_sale_item:sale_items (
              id,
              quantity,
              unit_price,
              total_amount
            )
          )
        `).eq("id",r).single();return o||!e?{success:!1,error:"Refund not found"}:{success:!0,data:e}}catch(e){return console.error("Error getting refund:",e),{success:!1,error:"Failed to get refund"}}}static async restoreInventory(r,e,o){var s;for(const n of r)try{const{data:u,error:d}=await c.from("products").select(`
            id,
            name,
            organization_id,
            stock_quantity,
            product_uoms (
              id,
              uom_id,
              is_default,
              conversion_factor
            )
          `).eq("id",n.product_id).single();if(d||!u){console.error("Error fetching product for refund inventory:",d);continue}const l=(s=u.product_uoms)==null?void 0:s.find(g=>g.is_default),i=l==null?void 0:l.uom_id;if(!i){console.error("No default unit of measure found for product:",n.product_id);continue}let a="adjustment",t=0,_="";switch(n.condition){case"new":n.restore_inventory?(a="return",t=n.quantity,_=`Refund - Item returned in new condition, restored to inventory (Refund: ${e})`):(a="adjustment",t=0,_=`Refund - Item in new condition but not restored to inventory (Refund: ${e})`);break;case"used":n.restore_inventory?(a="return",t=n.quantity,_=`Refund - Used item returned, restored to inventory (Refund: ${e})`):(a="adjustment",t=0,_=`Refund - Used item returned but not restored to inventory (Refund: ${e})`);break;case"damaged":a="adjustment",t=0,_=`Refund - Damaged item, sent for disposal/repair, not restored to inventory (Refund: ${e})`;break;case"defective":a="adjustment",t=0,_=`Refund - Defective item, sent for warranty/disposal, not restored to inventory (Refund: ${e})`;break;default:a="adjustment",t=0,_=`Refund - Unknown condition, not restored to inventory (Refund: ${e})`}const{transaction:f,error:m}=await q(u.organization_id,o,{productId:n.product_id,transactionType:a,quantity:t,uomId:i,referenceId:e,referenceType:"refund",notes:_});m?console.error("Error creating refund inventory transaction:",m):console.log(`Created refund inventory transaction: ${t} units of product ${n.product_id}, condition: ${n.condition}, restore: ${n.restore_inventory}`)}catch(u){console.error("Error processing refund inventory for item:",n.product_id,u)}}static async createStoreCredit(r){const{data:e}=await c.rpc("generate_credit_number",{org_id:r.organization_id});await c.from("store_credits").insert({organization_id:r.organization_id,customer_id:r.customer_id,refund_id:r.id,credit_number:e,original_amount:r.total_amount,remaining_balance:r.total_amount,created_by:r.processed_by})}static async updateOriginalSaleStatus(r,e,o){try{const{data:s,error:n}=await c.from("sales").select("id, total_amount, status").eq("id",r).single();if(n||!s){console.error("Error fetching original sale for status update:",n);return}const{data:u,error:d}=await c.from("refunds").select("total_amount").eq("original_sale_id",r).eq("status","processed");if(d){console.error("Error fetching refunds for sale status update:",d);return}const i=((u==null?void 0:u.reduce((t,_)=>t+Number(_.total_amount),0))||0)/s.total_amount*100;let a=s.status;if(e==="full"||i>=100?a="refunded":i>0&&(a="completed"),a!==s.status){const{error:t}=await c.from("sales").update({status:a,updated_at:new Date().toISOString()}).eq("id",r);t?console.error("Error updating sale status:",t):console.log(`Updated sale ${r} status to ${a} (${i.toFixed(1)}% refunded)`)}}catch(s){console.error("Error updating original sale status:",s)}}static async getRefunds(r,e={}){try{let o=c.from("refunds").select(`
          *,
          original_sale:sales (
            id,
            invoice_number,
            total_amount,
            sale_date,
            customer:customers (
              id,
              name,
              email,
              phone
            )
          ),
          refund_items (
            *,
            product:products (
              id,
              name,
              sku,
              unit_price
            )
          )
        `,{count:"exact"}).eq("organization_id",r);if(e.filters){const{status:i,refund_type:a,date_from:t,date_to:_,customer_id:f}=e.filters;i&&(o=o.eq("status",i)),a&&(o=o.eq("refund_type",a)),t&&(o=o.gte("created_at",t)),_&&(o=o.lte("created_at",_)),f&&(o=o.eq("customer_id",f))}const s=e.sort_by||"created_at",n=e.sort_order||"desc";o=o.order(s,{ascending:n==="asc"}),e.limit&&(o=o.limit(e.limit)),e.offset&&(o=o.range(e.offset,e.offset+(e.limit||10)-1));const{data:u,error:d,count:l}=await o;return d?{success:!1,error:d.message}:{success:!0,data:u||[],total_count:l||0}}catch(o){return console.error("Error getting refunds:",o),{success:!1,error:"Failed to fetch refunds"}}}static async getRefundSummary(r,e,o){try{let s=c.from("refunds").select("*").eq("organization_id",r);if(e&&(s=s.gte("created_at",e)),o){const a=new Date(o);a.setDate(a.getDate()+1),s=s.lt("created_at",a.toISOString())}const{data:n,error:u}=await s;if(u||!n)throw console.error("❌ Refund query error:",u),new Error("Failed to fetch refund data");const d={total_refunds:n.length,total_amount:n.reduce((a,t)=>a+Number(t.total_amount),0),pending_approvals:n.filter(a=>a.status==="pending").length,pending_amount:n.filter(a=>a.status==="pending").reduce((a,t)=>a+Number(t.total_amount),0),processed_today:0,processed_amount_today:0,by_reason:{},by_method:{}},l=new Date().toISOString().split("T")[0],i=n.filter(a=>a.status==="processed"&&a.processed_at&&a.processed_at.startsWith(l));return d.processed_today=i.length,d.processed_amount_today=i.reduce((a,t)=>a+Number(t.total_amount),0),n.forEach(a=>{const t=a.reason;d.by_reason[t]||(d.by_reason[t]={count:0,amount:0}),d.by_reason[t].count++,d.by_reason[t].amount+=Number(a.total_amount)}),n.forEach(a=>{const t=a.refund_method;d.by_method[t]||(d.by_method[t]={count:0,amount:0}),d.by_method[t].count++,d.by_method[t].amount+=Number(a.total_amount)}),d}catch(s){return console.error("Error getting refund summary:",s),{total_refunds:0,total_amount:0,pending_approvals:0,pending_amount:0,processed_today:0,processed_amount_today:0,by_reason:{},by_method:{}}}}static async searchOriginalSale(r,e){try{const{data:o,error:s}=await c.from("sales").select(`
          *,
          customer:customers (
            id,
            name,
            email,
            phone
          ),
          sale_items (
            *,
            product:products (
              id,
              name,
              sku,
              unit_price,
              image_url
            )
          )
        `).eq("organization_id",r).or(`invoice_number.ilike.%${e}%`).order("sale_date",{ascending:!1}).limit(10);return s?{success:!1,error:"Failed to search sales"}:{success:!0,data:o||[]}}catch(o){return console.error("Error searching original sale:",o),{success:!1,error:"Failed to search sales"}}}}export{F as R,k as a,x as c,N as g};
