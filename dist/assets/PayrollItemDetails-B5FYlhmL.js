import{ab as H,d as R,h as W,r as m,j as e,i as z,A as I,G as F,B as c,o as j,a2 as L,aj as k,ah as q}from"./index-C6AV3cVN.js";import{C as t}from"./Card-yj7fueH8.js";import{a as V}from"./payslipGeneration-21BXX9ZZ.js";import{P as $}from"./payroll-j3fcCwK0.js";import{P as J}from"./PageTitle-FHPo8gWi.js";import{f as l}from"./formatters-Cypx7G-j.js";import{f as K}from"./index-qirzObrW.js";import"./index-4YOzgfrD.js";import"./typeof-QjJsDpFa.js";import"./index-Cn2wB4rc.js";import"./index-DT2YvziZ.js";const ne=()=>{var b,f,g,v,_,S,w,P,C,T,E,M,B,D;const{id:x}=H(),n=R(),{currentOrganization:h}=W(),[s,O]=m.useState(null),[A,u]=m.useState(!0),[y,d]=m.useState(null),[o,N]=m.useState("overview"),G=async()=>{if(!(!h||!x)){u(!0),d(null);try{const{item:a,error:i}=await V(h.id,x);i?d(i):a?O(a):d("Payroll item not found")}catch(a){d(a.message)}finally{u(!1)}}};m.useEffect(()=>{G()},[h,x]);const r=s!=null&&s.deductions?s.deductions.reduce((a,i)=>{const p=i.type;return a[p]||(a[p]=[]),a[p].push(i),a},{}):{};return A?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(z,{size:"xl"})}):y?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(I,{color:"failure",icon:F,children:[e.jsx("span",{className:"font-medium",children:"Error!"})," ",y]}),e.jsx("div",{className:"mt-4",children:e.jsxs(c,{color:"gray",onClick:()=>n(-1),children:[e.jsx(j,{className:"mr-2 h-5 w-5"}),"Back"]})})]}):s?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs(c,{color:"gray",className:"mr-4",onClick:()=>n(`/payroll/periods/${s.payroll_period.id}`),children:[e.jsx(j,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(J,{title:`Payslip: ${(b=s.employee)==null?void 0:b.first_name} ${(f=s.employee)==null?void 0:f.last_name}`,subtitle:`Payroll Period: ${(g=s.payroll_period)==null?void 0:g.name}`})]}),e.jsxs("div",{className:"flex space-x-2",children:[(((v=s.payroll_period)==null?void 0:v.status)===$.DRAFT||((_=s.payroll_period)==null?void 0:_.status)===$.PROCESSING)&&e.jsxs(c,{color:"warning",onClick:()=>n(`/payroll/items/${s.id}/edit`),children:[e.jsx(L,{className:"mr-2 h-5 w-5"}),"Edit Contributions"]}),e.jsxs(c,{color:"primary",onClick:()=>n(`/payroll/periods/${s.payroll_period.id}/payslips`),children:[e.jsx(k,{className:"mr-2 h-5 w-5"}),"View Payslip"]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("ul",{className:"flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500",children:[e.jsx("li",{className:"mr-2",children:e.jsxs("button",{className:`inline-flex items-center p-4 border-b-2 rounded-t-lg ${o==="overview"?"text-blue-600 border-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,onClick:()=>N("overview"),children:[e.jsx(q,{className:"w-4 h-4 mr-2"}),"Overview"]})}),e.jsx("li",{className:"mr-2",children:e.jsxs("button",{className:`inline-flex items-center p-4 border-b-2 rounded-t-lg ${o==="details"?"text-blue-600 border-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,onClick:()=>N("details"),children:[e.jsx(k,{className:"w-4 h-4 mr-2"}),"Calculation Details"]})})]})})}),o==="overview"&&e.jsxs(e.Fragment,{children:[e.jsx(t,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Employee Information"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Name"}),e.jsxs("p",{className:"font-medium",children:[(S=s.employee)==null?void 0:S.first_name," ",(w=s.employee)==null?void 0:w.last_name]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Employee ID"}),e.jsx("p",{children:((P=s.employee)==null?void 0:P.employee_number)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Position"}),e.jsx("p",{children:((T=(C=s.employee)==null?void 0:C.position)==null?void 0:T.title)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Department"}),e.jsx("p",{children:((M=(E=s.employee)==null?void 0:E.department)==null?void 0:M.name)||"N/A"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Payroll Information"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Period"}),e.jsx("p",{children:(B=s.payroll_period)==null?void 0:B.name})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Pay Date"}),e.jsx("p",{children:K(new Date((D=s.payroll_period)==null?void 0:D.payment_date),"MMMM d, yyyy")})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Status"}),e.jsx("p",{children:s.status})]})]})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6",children:[e.jsxs(t,{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Earnings"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Basic Pay"}),e.jsx("p",{className:"font-medium",children:l(Number(s.basic_pay))})]}),s.allowances&&s.allowances.length>0&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"border-t border-gray-200 pt-2 mt-2",children:e.jsx("p",{className:"text-sm font-medium",children:"Allowances"})}),s.allowances.map((a,i)=>e.jsxs("div",{className:"flex justify-between",children:[e.jsx("p",{className:"text-sm text-gray-500",children:a.name}),e.jsx("p",{children:l(Number(a.amount))})]},i))]}),e.jsx("div",{className:"border-t border-gray-200 pt-2 mt-2",children:e.jsxs("div",{className:"flex justify-between font-medium",children:[e.jsx("p",{children:"Total Gross Pay"}),e.jsx("p",{children:l(Number(s.gross_pay))})]})})]})]}),e.jsxs(t,{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Deductions"}),e.jsxs("div",{className:"space-y-3",children:[r.government&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"border-b border-gray-200 pb-1",children:e.jsx("p",{className:"text-sm font-medium",children:"Government Contributions"})}),r.government.map((a,i)=>e.jsxs("div",{className:"flex justify-between",children:[e.jsx("p",{className:"text-sm text-gray-500",children:a.name}),e.jsx("p",{children:l(Number(a.amount))})]},i))]}),r.tax&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"border-b border-gray-200 pb-1 mt-3",children:e.jsx("p",{className:"text-sm font-medium",children:"Taxes"})}),r.tax.map((a,i)=>e.jsxs("div",{className:"flex justify-between",children:[e.jsx("p",{className:"text-sm text-gray-500",children:a.name}),e.jsx("p",{children:l(Number(a.amount))})]},i))]}),r.other&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"border-b border-gray-200 pb-1 mt-3",children:e.jsx("p",{className:"text-sm font-medium",children:"Other Deductions"})}),r.other.map((a,i)=>e.jsxs("div",{className:"flex justify-between",children:[e.jsx("p",{className:"text-sm text-gray-500",children:a.name}),e.jsx("p",{children:l(Number(a.amount))})]},i))]}),e.jsx("div",{className:"border-t border-gray-200 pt-2 mt-2",children:e.jsxs("div",{className:"flex justify-between font-medium",children:[e.jsx("p",{children:"Total Deductions"}),e.jsx("p",{children:l(Number(s.total_deductions))})]})})]})]})]}),e.jsx(t,{className:"mt-6",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:"Net Pay"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Total amount to be paid"})]}),e.jsx("div",{className:"text-2xl font-bold text-green-600",children:l(Number(s.net_pay))})]})})]}),o==="details"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs(t,{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Calculation Details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Taxable Income"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Gross pay minus tax-exempt deductions"}),e.jsx("p",{className:"font-medium mt-1",children:l(Number(s.taxable_income))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Government Contributions"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-2",children:[e.jsxs("div",{className:"border rounded p-3",children:[e.jsx("p",{className:"text-sm font-medium",children:"SSS"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Employee Share"}),e.jsx("p",{className:"font-medium",children:l(Number(s.sss_contribution))})]}),e.jsxs("div",{className:"border rounded p-3",children:[e.jsx("p",{className:"text-sm font-medium",children:"PhilHealth"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Employee Share"}),e.jsx("p",{className:"font-medium",children:l(Number(s.philhealth_contribution))})]}),e.jsxs("div",{className:"border rounded p-3",children:[e.jsx("p",{className:"text-sm font-medium",children:"Pag-IBIG"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Employee Share"}),e.jsx("p",{className:"font-medium",children:l(Number(s.pagibig_contribution))})]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Withholding Tax"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Based on taxable income after deductions"}),e.jsx("p",{className:"font-medium mt-1",children:l(Number(s.withholding_tax))})]})]})]}),e.jsxs(t,{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Government Contribution Formulas"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"font-medium text-blue-600",children:"SSS Contribution"}),e.jsxs("div",{className:"bg-gray-50 p-3 rounded-md my-2",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Monthly Salary:"}),e.jsx("span",{className:"ml-2 font-mono",children:l(Number(s.basic_pay)*(s.payroll_period.name.includes("Semi-Monthly")?2:1))})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Employee Share:"}),e.jsx("span",{className:"ml-2 font-mono",children:l(Number(s.sss_contribution))})]})]}),e.jsxs("div",{className:"text-sm text-gray-600 mt-2",children:[e.jsx("p",{className:"font-medium",children:"Formula:"}),e.jsx("p",{children:"Based on SSS Contribution Table (2023)"}),e.jsxs("ul",{className:"list-disc list-inside mt-1 ml-2 space-y-1",children:[e.jsx("li",{children:"Employee pays a fixed amount based on salary bracket"}),e.jsx("li",{children:"Brackets range from ₱135 (for salary up to ₱3,249.99) to ₱1,125 (for salary ₱24,750 and above)"}),e.jsx("li",{children:"Employer pays 1.89 times the employee contribution"})]})]})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"font-medium text-blue-600",children:"PhilHealth Contribution"}),e.jsxs("div",{className:"bg-gray-50 p-3 rounded-md my-2",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Monthly Salary:"}),e.jsx("span",{className:"ml-2 font-mono",children:l(Number(s.basic_pay)*(s.payroll_period.name.includes("Semi-Monthly")?2:1))})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Employee Share:"}),e.jsx("span",{className:"ml-2 font-mono",children:l(Number(s.philhealth_contribution))})]})]}),e.jsxs("div",{className:"text-sm text-gray-600 mt-2",children:[e.jsx("p",{className:"font-medium",children:"Formula: 2% of monthly salary (employee share)"}),e.jsxs("p",{className:"font-mono mt-1",children:[l(Number(s.basic_pay)*(s.payroll_period.name.includes("Semi-Monthly")?2:1))," × 2% =",l(Number(s.basic_pay)*(s.payroll_period.name.includes("Semi-Monthly")?2:1)*.02)]}),e.jsx("p",{className:"font-medium mt-2",children:"Notes:"}),e.jsxs("ul",{className:"list-disc list-inside mt-1 ml-2 space-y-1",children:[e.jsx("li",{children:"Total PhilHealth contribution is 4% of monthly salary"}),e.jsx("li",{children:"Split equally between employee (2%) and employer (2%)"}),e.jsx("li",{children:"Minimum monthly salary for calculation: ₱10,000"}),e.jsx("li",{children:"Maximum monthly salary for calculation: ₱80,000"})]})]})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"font-medium text-blue-600",children:"Pag-IBIG Contribution"}),e.jsxs("div",{className:"bg-gray-50 p-3 rounded-md my-2",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Monthly Salary:"}),e.jsx("span",{className:"ml-2 font-mono",children:l(Number(s.basic_pay)*(s.payroll_period.name.includes("Semi-Monthly")?2:1))})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Employee Share:"}),e.jsx("span",{className:"ml-2 font-mono",children:l(Number(s.pagibig_contribution))})]})]}),e.jsxs("div",{className:"text-sm text-gray-600 mt-2",children:[e.jsx("p",{className:"font-medium",children:"Formula:"}),e.jsxs("ul",{className:"list-disc list-inside mt-1 ml-2 space-y-1",children:[e.jsx("li",{children:"For monthly salary up to ₱1,500: 1% employee contribution"}),e.jsx("li",{children:"For monthly salary over ₱1,500: 2% employee contribution"})]}),e.jsx("p",{className:"font-mono mt-2",children:Number(s.basic_pay)*(s.payroll_period.name.includes("Semi-Monthly")?2:1)<=1500?`${l(Number(s.basic_pay)*(s.payroll_period.name.includes("Semi-Monthly")?2:1))} × 1% = ${l(Number(s.basic_pay)*(s.payroll_period.name.includes("Semi-Monthly")?2:1)*.01)}`:`${l(Number(s.basic_pay)*(s.payroll_period.name.includes("Semi-Monthly")?2:1))} × 2% = ${l(Number(s.basic_pay)*(s.payroll_period.name.includes("Semi-Monthly")?2:1)*.02)}`}),e.jsx("p",{className:"font-medium mt-2",children:"Notes:"}),e.jsxs("ul",{className:"list-disc list-inside mt-1 ml-2 space-y-1",children:[e.jsx("li",{children:"Employer always contributes 2% of monthly salary"}),e.jsx("li",{children:"Maximum monthly contribution: ₱100 for employee, ₱100 for employer"})]})]})]})]}),Number(s.withholding_tax)>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-blue-600",children:"Withholding Tax"}),e.jsxs("div",{className:"bg-gray-50 p-3 rounded-md my-2",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Taxable Income:"}),e.jsx("span",{className:"ml-2 font-mono",children:l(Number(s.taxable_income))})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Withholding Tax:"}),e.jsx("span",{className:"ml-2 font-mono",children:l(Number(s.withholding_tax))})]})]}),e.jsxs("div",{className:"text-sm text-gray-600 mt-2",children:[e.jsx("p",{className:"font-medium",children:"Formula: Based on BIR Withholding Tax Table (2023)"}),e.jsx("p",{className:"font-medium mt-2",children:"Tax Brackets:"}),e.jsxs("ul",{className:"list-disc list-inside mt-1 ml-2 space-y-1",children:[e.jsx("li",{children:"₱0 to ₱20,833: 0%"}),e.jsx("li",{children:"₱20,834 to ₱33,332: 15% of excess over ₱20,833"}),e.jsx("li",{children:"₱33,333 to ₱66,666: ₱1,875 + 20% of excess over ₱33,333"}),e.jsx("li",{children:"₱66,667 to ₱166,666: ₱8,541.80 + 25% of excess over ₱66,667"}),e.jsx("li",{children:"₱166,667 to ₱666,666: ₱33,541.80 + 30% of excess over ₱166,667"}),e.jsx("li",{children:"Over ₱666,667: ₱183,541.80 + 35% of excess over ₱666,667"})]})]})]})]})]})]})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(I,{color:"failure",icon:F,children:[e.jsx("span",{className:"font-medium",children:"Error!"})," Payroll item not found"]}),e.jsx("div",{className:"mt-4",children:e.jsxs(c,{color:"gray",onClick:()=>n(-1),children:[e.jsx(j,{className:"mr-2 h-5 w-5"}),"Back"]})})]})};export{ne as default};
