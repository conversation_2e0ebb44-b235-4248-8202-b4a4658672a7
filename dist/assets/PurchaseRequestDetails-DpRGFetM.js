import{ab as ae,d as te,h as ce,r as c,j as e,i as N,A as v,J as L,B as a,aD as q,aj as T,K as le,E as ie,e as $,al as ne,a2 as de,$ as k,V,bn as oe,bo as ue,a0 as M,ao as he,_ as l,M as i,a6 as R,ad as _}from"./index-C6AV3cVN.js";import{C as xe}from"./Card-yj7fueH8.js";import{a as K,u as me}from"./purchaseRequest-Bff56uzH.js";import{a as je}from"./purchaseOrder-DppPMsdd.js";import{c as P,f as pe,a as fe,d as O}from"./formatters-Cypx7G-j.js";const be=()=>{const{id:m}=ae(),o=te(),{currentOrganization:d}=ce(),[r,S]=c.useState(null),[A,Q]=c.useState([]),[G,H]=c.useState(!0),[J,B]=c.useState(!1),[E,y]=c.useState(null),[W,U]=c.useState(!1),[X,F]=c.useState(!1),[Y,z]=c.useState(!1),[f,u]=c.useState(""),[j,I]=c.useState(!1),[p,h]=c.useState(null);c.useEffect(()=>{(async()=>{if(!(!m||!d)){H(!0),y(null);try{const{purchaseRequest:t,error:n}=await K(d.id,m);n?y(n):t?S(t):y("Purchase request not found")}catch(t){console.error("Error fetching purchase request:",t),y(t.message||"An error occurred while fetching the purchase request")}finally{H(!1)}}})()},[m,d]),c.useEffect(()=>{(async()=>{if(!(!r||!d||r.status!=="approved")){B(!0);try{const{purchaseOrders:t,error:n}=await je(d.id,r.id);n?console.error("Error fetching related purchase orders:",n):Q(t||[])}catch(t){console.error("Exception fetching related purchase orders:",t)}finally{B(!1)}}})()},[r,d]);const Z=s=>{switch(s){case"approved":return"success";case"pending":return"warning";case"rejected":return"failure";case"cancelled":return"dark";default:return"gray"}},C=()=>{o("/purchases/requests")},b=()=>{u(""),h(null),U(!0)},D=()=>{u(""),h(null),F(!0)},ee=()=>{u(""),h(null),z(!0)},x=()=>{U(!1),F(!1),z(!1),u(""),h(null)},w=async s=>{if(!(!m||!d)){I(!0),h(null);try{const{success:t,error:n}=await me(d.id,m,s,f||void 0);if(n)h(n);else if(t){x();const{purchaseRequest:g}=await K(d.id,m);g&&S(g)}}catch(t){h(t.message||"An error occurred while updating the status")}finally{I(!1)}}},se=s=>{switch(s){case"sent":return"info";case"partially_received":return"warning";case"received":return"success";case"cancelled":return"failure";default:return"gray"}},re=s=>{switch(s){case"draft":return"Draft";case"sent":return"Sent";case"partially_received":return"Partially Received";case"received":return"Received";case"cancelled":return"Cancelled";default:return s.charAt(0).toUpperCase()+s.slice(1)}};return G?e.jsx("div",{className:"container mx-auto px-4 py-8 flex justify-center",children:e.jsx(N,{size:"xl"})}):E?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(v,{color:"failure",icon:L,children:[e.jsx("h3",{className:"font-medium",children:"Error"}),e.jsx("p",{children:E}),e.jsx("div",{className:"mt-4",children:e.jsxs(a,{color:"gray",onClick:C,children:[e.jsx(q,{className:"mr-2 h-5 w-5"}),"Back to Purchase Requests"]})})]})}):r?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(xe,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold flex items-center",children:[e.jsx(T,{className:"mr-2 h-6 w-6"}),"Purchase Request: ",r.request_number]}),e.jsxs("div",{className:"flex flex-wrap gap-4 mt-2",children:[e.jsxs("div",{className:"flex items-center text-gray-500",children:[e.jsx(le,{className:"mr-1 h-5 w-5"}),e.jsxs("span",{children:["Requester: ",r.requester_name]})]}),e.jsxs("div",{className:"flex items-center text-gray-500",children:[e.jsx(ie,{className:"mr-1 h-5 w-5"}),e.jsxs("span",{children:["Created: ",P(r.created_at)]})]}),e.jsx("div",{className:"flex items-center",children:e.jsx($,{color:Z(r.status),children:r.status.charAt(0).toUpperCase()+r.status.slice(1)})})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(a,{color:"gray",onClick:C,children:[e.jsx(q,{className:"mr-2 h-5 w-5"}),"Back"]}),r.status==="draft"&&e.jsxs(e.Fragment,{children:[e.jsx(ne,{content:"Edit purchase request",children:e.jsx(a,{color:"primary",onClick:()=>o(`/edit-purchase-request/${r.id}`),children:e.jsx(de,{className:"h-5 w-5"})})}),e.jsxs(a,{color:"success",onClick:b,children:[e.jsx(k,{className:"mr-2 h-5 w-5"}),"Approve"]}),e.jsxs(a,{color:"failure",onClick:D,children:[e.jsx(V,{className:"mr-2 h-5 w-5"}),"Reject"]})]}),r.status==="pending"&&e.jsxs(e.Fragment,{children:[e.jsxs(a,{color:"success",onClick:b,children:[e.jsx(k,{className:"mr-2 h-5 w-5"}),"Approve"]}),e.jsxs(a,{color:"failure",onClick:D,children:[e.jsx(V,{className:"mr-2 h-5 w-5"}),"Reject"]})]}),r.status==="approved"&&e.jsx(e.Fragment,{children:e.jsxs(a,{color:"failure",onClick:ee,children:[e.jsx(oe,{className:"mr-2 h-5 w-5"}),"Cancel"]})}),r.status==="rejected"&&e.jsxs(a,{color:"success",onClick:b,children:[e.jsx(k,{className:"mr-2 h-5 w-5"}),"Approve"]})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-6",children:e.jsxs("div",{className:"space-y-4",children:[r.notes&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Notes"}),e.jsx("div",{className:"p-4 bg-gray-50 rounded-lg whitespace-pre-line",children:r.notes})]}),r.purchase_orders&&r.purchase_orders.length>0&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Related Purchase Orders"}),e.jsx("div",{className:"space-y-2",children:r.purchase_orders.map(s=>e.jsx("div",{className:"p-3 bg-gray-50 rounded-lg",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:s.order_number}),e.jsx("p",{className:"text-sm text-gray-500",children:P(s.created_at)})]}),e.jsx(a,{size:"xs",color:"primary",onClick:()=>o(`/purchases/orders/details/${s.id}`),children:"View"})]})},s.id))})]}),r.status==="approved"&&e.jsxs("div",{className:"mt-6",children:[e.jsxs("h2",{className:"text-lg font-semibold mb-3 flex items-center",children:[e.jsx(ue,{className:"mr-2 h-5 w-5"}),"Related Purchase Orders"]}),J?e.jsxs("div",{className:"flex justify-center items-center p-4",children:[e.jsx(N,{size:"sm"}),e.jsx("span",{className:"ml-2",children:"Loading purchase orders..."})]}):A.length===0?e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg text-center",children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"No purchase orders have been created from this request yet."}),e.jsxs(a,{color:"primary",size:"sm",onClick:()=>o(`/purchases/orders/from-request/${r.id}`),children:[e.jsx(M,{className:"mr-2 h-4 w-4"}),"Create Purchase Order"]})]}):e.jsxs("div",{className:"space-y-2",children:[A.map(s=>e.jsx("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(T,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{className:"font-medium",children:s.order_number}),e.jsx($,{color:se(s.status),className:"ml-2",children:re(s.status)})]}),e.jsxs("div",{className:"mt-1 text-sm text-gray-500",children:[e.jsxs("span",{children:["Supplier: ",s.supplier_name," | "]}),e.jsxs("span",{children:["Date: ",P(s.order_date)," | "]}),e.jsxs("span",{children:["Total: ",pe(s.total_amount)]})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsx(a,{color:"light",size:"xs",onClick:()=>o(`/purchases/orders/${s.id}`),children:"View"})})]})},s.id)),e.jsx("div",{className:"flex justify-end mt-2",children:e.jsxs(a,{color:"primary",size:"sm",onClick:()=>o(`/purchases/orders/from-request/${r.id}`),children:[e.jsx(M,{className:"mr-2 h-4 w-4"}),"Create Another Purchase Order"]})})]})]})]})}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-lg font-semibold mb-2 flex items-center",children:[e.jsx(he,{className:"mr-2 h-5 w-5"}),"Items"]}),r.items&&r.items.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(l,{children:[e.jsxs(l.Head,{children:[e.jsx(l.HeadCell,{children:"Product"}),e.jsx(l.HeadCell,{children:"Quantity"}),e.jsx(l.HeadCell,{children:"Unit"}),e.jsx(l.HeadCell,{children:"Notes"})]}),e.jsx(l.Body,{className:"divide-y",children:r.items.map(s=>{var t,n,g;return e.jsxs(l.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsxs(l.Cell,{className:"font-medium",children:[s.product?e.jsx("button",{onClick:()=>o(`/products/details/${s.product.id}`),className:"text-primary-600 hover:text-primary-800 hover:underline focus:outline-none",title:"View product details",children:s.product.name}):"Unknown Product",((t=s.product)==null?void 0:t.sku)&&e.jsxs("div",{className:"text-xs text-gray-500",children:["SKU: ",s.product.sku]})]}),e.jsx(l.Cell,{children:fe(s.quantity)}),e.jsxs(l.Cell,{children:[((n=s.uom)==null?void 0:n.name)||"Unknown Unit",((g=s.uom)==null?void 0:g.code)&&e.jsxs("span",{className:"text-xs text-gray-500 block",children:["(",s.uom.code,")"]})]}),e.jsx(l.Cell,{children:s.notes||"-"})]},s.id)})})]})}):e.jsx("div",{className:"p-4 bg-gray-50 rounded-lg text-center text-gray-500",children:"No items found in this purchase request."})]}),e.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[e.jsx("h2",{className:"text-sm font-medium text-gray-500 mb-2",children:"System Information"}),e.jsxs("div",{className:"flex flex-wrap gap-x-6 gap-y-2",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Purchase Request ID"}),e.jsx("p",{className:"text-sm",children:r.id})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Request Number"}),e.jsx("p",{className:"text-sm",children:r.request_number})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Status"}),e.jsx("p",{className:"text-sm",children:r.status.charAt(0).toUpperCase()+r.status.slice(1)})]}),r.created_at&&e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Created"}),e.jsx("p",{className:"text-sm",children:O(r.created_at)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Last Updated"}),e.jsx("p",{className:"text-sm",children:O(r.updated_at)})]}),r.status_updated_at&&e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Status Updated"}),e.jsx("p",{className:"text-sm",children:O(r.status_updated_at)})]}),r.requester_id&&e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Requester ID"}),e.jsx("p",{className:"text-sm",children:r.requester_id})]})]})]})]}),e.jsxs(i,{show:W,onClose:x,children:[e.jsx(i.Header,{children:"Approve Purchase Request"}),e.jsx(i.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"Are you sure you want to approve this purchase request?"}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(R,{htmlFor:"approval_notes",value:"Notes (Optional)"})}),e.jsx(_,{id:"approval_notes",placeholder:"Add any notes about this approval",value:f,onChange:s=>u(s.target.value),rows:3})]}),p&&e.jsx(v,{color:"failure",children:p})]})}),e.jsxs(i.Footer,{children:[e.jsx(a,{color:"success",onClick:()=>w("approved"),disabled:j,children:j?e.jsxs(e.Fragment,{children:[e.jsx(N,{size:"sm",className:"mr-2"}),"Approving..."]}):"Approve"}),e.jsx(a,{color:"gray",onClick:x,children:"Cancel"})]})]}),e.jsxs(i,{show:X,onClose:x,children:[e.jsx(i.Header,{children:"Reject Purchase Request"}),e.jsx(i.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"Are you sure you want to reject this purchase request?"}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(R,{htmlFor:"rejection_notes",value:"Reason for Rejection"})}),e.jsx(_,{id:"rejection_notes",placeholder:"Please provide a reason for rejection",value:f,onChange:s=>u(s.target.value),rows:3,required:!0})]}),p&&e.jsx(v,{color:"failure",children:p})]})}),e.jsxs(i.Footer,{children:[e.jsx(a,{color:"failure",onClick:()=>w("rejected"),disabled:j||!f.trim(),children:j?e.jsxs(e.Fragment,{children:[e.jsx(N,{size:"sm",className:"mr-2"}),"Rejecting..."]}):"Reject"}),e.jsx(a,{color:"gray",onClick:x,children:"Cancel"})]})]}),e.jsxs(i,{show:Y,onClose:x,children:[e.jsx(i.Header,{children:"Cancel Purchase Request"}),e.jsx(i.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"Are you sure you want to cancel this purchase request?"}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 block",children:e.jsx(R,{htmlFor:"cancellation_notes",value:"Reason for Cancellation"})}),e.jsx(_,{id:"cancellation_notes",placeholder:"Please provide a reason for cancellation",value:f,onChange:s=>u(s.target.value),rows:3})]}),p&&e.jsx(v,{color:"failure",children:p})]})}),e.jsxs(i.Footer,{children:[e.jsx(a,{color:"failure",onClick:()=>w("cancelled"),disabled:j,children:j?e.jsxs(e.Fragment,{children:[e.jsx(N,{size:"sm",className:"mr-2"}),"Cancelling..."]}):"Cancel Request"}),e.jsx(a,{color:"gray",onClick:x,children:"Go Back"})]})]})]}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(v,{color:"warning",icon:L,children:[e.jsx("h3",{className:"font-medium",children:"Purchase Request Not Found"}),e.jsx("p",{children:"The requested purchase request could not be found."}),e.jsx("div",{className:"mt-4",children:e.jsxs(a,{color:"gray",onClick:C,children:[e.jsx(q,{className:"mr-2 h-5 w-5"}),"Back to Purchase Requests"]})})]})})};export{be as default};
