import{b as M,h as B,r as i,j as e,i as T,B as A,bb as U,a7 as v,P as c,ad as W,A as R,bU as Y}from"./index-C6AV3cVN.js";import{C as G}from"./Card-yj7fueH8.js";import{i as O,b as Q,j as V}from"./operationalExpenses-C7_pxLV1.js";import{g as X}from"./supplier-BJDz25mb.js";import{m as J}from"./payables-q7zOb02j.js";import{E as f}from"./EnhancedNumberInput-Bwo1E3yF.js";const ne=({onSuccess:N,onCancel:S,prefilledData:w})=>{const{user:b}=M(),{currentOrganization:o}=B(),[g,I]=i.useState([]),[_,F]=i.useState([]),[q,k]=i.useState(!0),[C,D]=i.useState(!1),[E,m]=i.useState(null),[P,j]=i.useState(null),[a,n]=i.useState({source_type:O.MANUAL_ENTRY,source_id:"",supplier_id:"",employee_id:"",reference_number:"",invoice_date:new Date().toISOString().split("T")[0],due_date:"",amount:0,vat_amount:0,withholding_tax_rate:0,withholding_tax_amount:0,currency:"PHP",category:"",invoice_url:"",notes:"",expense_type_id:"",department:"",project_code:"",...w}),[u,p]=i.useState("unpaid"),[l,h]=i.useState({payment_date:new Date().toISOString().split("T")[0],payment_method:"cash",reference_number:"",remarks:""}),L=async()=>{if(o)try{k(!0);const[t,s]=await Promise.all([Q(o.id,{is_active:!0}),X(o.id)]);t.success&&t.data&&I(t.data),s.suppliers&&F(s.suppliers)}catch(t){m(t.message)}finally{k(!1)}};i.useEffect(()=>{L()},[o]),i.useEffect(()=>{if(!a.reference_number&&a.expense_type_id){const t=g.find(s=>s.id===a.expense_type_id);if(t){const s=Date.now().toString().slice(-6);n(r=>({...r,reference_number:`${t.code}-${s}`}))}}},[a.expense_type_id,g]),i.useEffect(()=>{if(a.supplier_id&&a.invoice_date&&!a.due_date){const t=_.find(d=>d.id===a.supplier_id),s=(t==null?void 0:t.payment_terms_days)||30,r=new Date(a.invoice_date),x=new Date(r);x.setDate(x.getDate()+s),n(d=>({...d,due_date:x.toISOString().split("T")[0]}))}},[a.supplier_id,a.invoice_date,_]),i.useEffect(()=>{if(a.withholding_tax_rate>0&&a.amount>0){const t=a.amount*a.withholding_tax_rate/100;n(s=>({...s,withholding_tax_amount:t}))}else n(t=>({...t,withholding_tax_amount:0}))},[a.amount,a.withholding_tax_rate]);const H=async t=>{if(t.preventDefault(),!(!o||!b))try{if(D(!0),m(null),j(null),!a.reference_number){m("Reference number is required");return}if(!a.amount||a.amount<=0){m("Amount must be greater than 0");return}if(!a.supplier_id&&!a.employee_id){m("Please select either a supplier or employee");return}const s=`manual-${Date.now()}`,r=a.due_date||(()=>{const y=new Date(a.invoice_date);return y.setDate(y.getDate()+30),y.toISOString().split("T")[0]})(),x={...a,source_id:s,due_date:r,supplier_id:a.supplier_id||null,employee_id:a.employee_id||null,expense_type_id:a.expense_type_id||void 0},d=await V(o.id,x,b.id);d.success&&d.data?(u==="paid"?(await J(o.id,{payable_id:d.data.id,payment_date:l.payment_date,amount_paid:a.amount,payment_method:l.payment_method,reference_number:l.reference_number,attachment_url:"",remarks:l.remarks||"Payment recorded during expense entry"},b.id)).success?j("Expense created and payment recorded successfully!"):j("Expense created but failed to record payment. Please add payment manually."):j("Expense created successfully!"),n({source_type:O.MANUAL_ENTRY,source_id:"",supplier_id:a.supplier_id,employee_id:"",reference_number:"",invoice_date:new Date().toISOString().split("T")[0],due_date:"",amount:0,vat_amount:0,withholding_tax_rate:a.withholding_tax_rate,withholding_tax_amount:0,currency:"PHP",category:"",invoice_url:"",notes:"",expense_type_id:a.expense_type_id,department:a.department,project_code:"",...w}),p("unpaid"),h({payment_date:new Date().toISOString().split("T")[0],payment_method:"cash",reference_number:"",remarks:""}),N&&N()):m(d.error||"Failed to create expense")}catch(s){m(s.message)}finally{D(!1)}},z=t=>{const s=g.find(r=>r.id===t);n(r=>({...r,expense_type_id:t,category:(s==null?void 0:s.category)||"",withholding_tax_rate:(s==null?void 0:s.category)==="professional_services"?10:0}))},$=t=>{const s=_.find(r=>r.id===t);n(r=>({...r,supplier_id:t,employee_id:"",withholding_tax_rate:(s==null?void 0:s.default_withholding_tax_rate)||r.withholding_tax_rate}))};return q?e.jsx("div",{className:"flex justify-center items-center h-32",children:e.jsx(T,{size:"lg"})}):e.jsxs(G,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"Quick Expense Entry"}),S&&e.jsxs(A,{color:"gray",size:"sm",onClick:S,children:[e.jsx(U,{className:"mr-1 h-4 w-4"}),"Cancel"]})]}),e.jsxs("form",{onSubmit:H,className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Expense Type *"}),e.jsxs(v,{value:a.expense_type_id,onChange:t=>z(t.target.value),required:!0,children:[e.jsx("option",{value:"",children:"Select expense type"}),g.map(t=>e.jsxs("option",{value:t.id,children:[t.name," (",t.code,")"]},t.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Reference Number *"}),e.jsx(c,{value:a.reference_number,onChange:t=>n({...a,reference_number:t.target.value}),required:!0,placeholder:"Auto-generated or enter manually"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Supplier *"}),e.jsxs(v,{value:a.supplier_id,onChange:t=>$(t.target.value),required:!0,children:[e.jsx("option",{value:"",children:"Select supplier"}),_.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Amount (₱) *"}),e.jsx(f,{value:a.amount,onChange:t=>n({...a,amount:parseFloat(t.target.value)||0}),required:!0,min:"0",step:"0.01",placeholder:"0.00"})]})]}),e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("label",{className:"block text-sm font-medium mb-3",children:"Payment Status *"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx("div",{className:`p-4 border-2 rounded-lg cursor-pointer transition-all ${u==="unpaid"?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>p("unpaid"),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"paymentStatus",value:"unpaid",checked:u==="unpaid",onChange:()=>p("unpaid"),className:"mr-3"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Not Yet Paid"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Create payable for future payment"})]})]})}),e.jsx("div",{className:`p-4 border-2 rounded-lg cursor-pointer transition-all ${u==="paid"?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>p("paid"),children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"paymentStatus",value:"paid",checked:u==="paid",onChange:()=>p("paid"),className:"mr-3"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Already Paid"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Record expense and payment together"})]})]})})]})]}),u==="paid"&&e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[e.jsx("h4",{className:"font-medium mb-3 text-green-800",children:"Payment Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Payment Date *"}),e.jsx(c,{type:"date",value:l.payment_date,onChange:t=>h({...l,payment_date:t.target.value}),required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Payment Method *"}),e.jsxs(v,{value:l.payment_method,onChange:t=>h({...l,payment_method:t.target.value}),required:!0,children:[e.jsx("option",{value:"cash",children:"Cash"}),e.jsx("option",{value:"check",children:"Check"}),e.jsx("option",{value:"bank_transfer",children:"Bank Transfer"}),e.jsx("option",{value:"credit_card",children:"Credit Card"}),e.jsx("option",{value:"gcash",children:"GCash"}),e.jsx("option",{value:"paymaya",children:"PayMaya"}),e.jsx("option",{value:"other",children:"Other"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Payment Reference"}),e.jsx(c,{type:"text",value:l.reference_number,onChange:t=>h({...l,reference_number:t.target.value}),placeholder:"Check number, transaction ID, etc."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Payment Remarks"}),e.jsx(c,{type:"text",value:l.remarks,onChange:t=>h({...l,remarks:t.target.value}),placeholder:"Additional payment notes"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Invoice Date *"}),e.jsx(c,{type:"date",value:a.invoice_date,onChange:t=>n({...a,invoice_date:t.target.value}),required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Due Date"}),e.jsx(c,{type:"date",value:a.due_date,onChange:t=>n({...a,due_date:t.target.value}),placeholder:"Auto-calculated from payment terms"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"VAT Amount (₱)"}),e.jsx(f,{value:a.vat_amount,onChange:t=>n({...a,vat_amount:parseFloat(t.target.value)||0}),min:"0",step:"0.01",placeholder:"0.00"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Withholding Tax (%)"}),e.jsx(f,{value:a.withholding_tax_rate,onChange:t=>n({...a,withholding_tax_rate:parseFloat(t.target.value)||0}),min:"0",max:"100",step:"0.01",placeholder:"0.00"}),a.withholding_tax_amount>0&&e.jsxs("div",{className:"text-sm text-gray-500 mt-1",children:["WHT Amount: ₱",a.withholding_tax_amount.toLocaleString()]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Department"}),e.jsx(c,{value:a.department,onChange:t=>n({...a,department:t.target.value}),placeholder:"e.g., IT, HR, Finance"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Project Code"}),e.jsx(c,{value:a.project_code,onChange:t=>n({...a,project_code:t.target.value}),placeholder:"Optional project code"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Notes"}),e.jsx(W,{value:a.notes,onChange:t=>n({...a,notes:t.target.value}),placeholder:"Optional notes or description",rows:3})]}),E&&e.jsx(R,{color:"failure",children:E}),P&&e.jsx(R,{color:"success",children:P}),e.jsx("div",{className:"flex justify-end space-x-2",children:e.jsx(A,{type:"submit",disabled:C,className:"bg-primary",children:C?e.jsxs(e.Fragment,{children:[e.jsx(T,{size:"sm",className:"mr-2"}),"Creating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Create Expense"]})})})]}),a.amount>0&&e.jsxs("div",{className:"mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsx("h3",{className:"text-sm font-medium mb-2",children:"Summary"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"Gross Amount:"}),e.jsxs("div",{className:"font-semibold",children:["₱",a.amount.toLocaleString()]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"VAT:"}),e.jsxs("div",{className:"font-semibold",children:["₱",a.vat_amount.toLocaleString()]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"Withholding Tax:"}),e.jsxs("div",{className:"font-semibold",children:["₱",a.withholding_tax_amount.toLocaleString()]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"Net Payable:"}),e.jsxs("div",{className:"font-semibold text-blue-600",children:["₱",(a.amount-a.withholding_tax_amount).toLocaleString()]})]})]})]})]})};export{ne as default};
