import{h as te,r as a,j as e,M as H,A as U,P as ie,Q as oe,i as A,U as ee,e as K,V as ue,B as b,W as De,s as se,b as he,R as Ee,X as G,Y as Re,Z as xe,_ as x,$ as Ie,J as ce,T as ne,L as q,a0 as de,p as Oe,D as J,a1 as $e,a2 as Le,a3 as ze,a4 as He,a5 as Ae}from"./index-C6AV3cVN.js";import{C as me}from"./Card-yj7fueH8.js";import{c as Fe,g as Ue,d as Be}from"./product-Ca8DWaNR.js";import{u as Me}from"./useProductPermissions-DyxXl-nR.js";import{T as Ve,a as qe,r as Ke,g as We}from"./TagList-S4Y14div.js";import{g as ge}from"./tagService-sPq402Av.js";import{P as Qe}from"./Pagination-CVEzfctr.js";import{u as Ye}from"./currencyFormatter-BsFWv3sX.js";import{e as Xe}from"./excelExport-BekG2cQR.js";import{p as pe}from"./csvParser-Ls709fVB.js";import"./formatters-Cypx7G-j.js";const Ze=({show:t,onClose:c,selectedIds:y,entityType:g,onApplyTags:d})=>{const{currentOrganization:o}=te(),[l,u]=a.useState([]),[k,_]=a.useState([]),[S,T]=a.useState(!1),[i,P]=a.useState(!1),[r,p]=a.useState(null),[n,f]=a.useState(""),[j,N]=a.useState("add");a.useEffect(()=>{if(!o||!t)return;(async()=>{T(!0);try{const{success:v,tags:D}=await ge(o.id);v&&D&&u(D)}catch(v){console.error("Error fetching tags:",v)}finally{T(!1)}})()},[o,t]),a.useEffect(()=>{t&&(_([]),f(""),p(null),N("add"))},[t]);const E=l.filter(h=>n===""||h.name.toLowerCase().includes(n.toLowerCase())||h.description&&h.description.toLowerCase().includes(n.toLowerCase())),z=h=>{_(v=>v.includes(h)?v.filter(D=>D!==h):[...v,h])},L=async()=>{if(k.length===0){p("Please select at least one tag");return}P(!0),p(null);try{await d(k,j),c()}catch(h){p(h.message||"Failed to apply tags")}finally{P(!1)}};return e.jsxs(H,{show:t,onClose:c,size:"md",children:[e.jsxs(H.Header,{children:["Manage Tags for ",y.length," ",g,y.length>1?"s":""]}),e.jsxs(H.Body,{children:[r&&e.jsx(U,{color:"failure",className:"mb-4",children:r}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex space-x-4 mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"radio",id:"add-tags",name:"tag-action",value:"add",checked:j==="add",onChange:()=>N("add"),className:"mr-2"}),e.jsx("label",{htmlFor:"add-tags",children:"Add tags"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"radio",id:"remove-tags",name:"tag-action",value:"remove",checked:j==="remove",onChange:()=>N("remove"),className:"mr-2"}),e.jsx("label",{htmlFor:"remove-tags",children:"Remove tags"})]})]}),e.jsx(ie,{type:"text",placeholder:"Search tags...",icon:oe,onChange:h=>f(h.target.value),value:n,className:"mb-4"}),S?e.jsx("div",{className:"flex justify-center items-center p-4",children:e.jsx(A,{size:"md"})}):E.length===0?e.jsx("div",{className:"text-center py-4 text-gray-500",children:e.jsx("p",{children:"No matching tags found"})}):e.jsx("div",{className:"max-h-60 overflow-y-auto border rounded-lg p-2",children:E.map(h=>e.jsxs("div",{className:"flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded cursor-pointer",onClick:()=>z(h.id),children:[e.jsx(ee,{checked:k.includes(h.id),onChange:()=>{},className:"mr-3"}),e.jsx("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:h.color||"#3b82f6"}}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"font-medium",children:h.name}),h.description&&e.jsx("p",{className:"text-xs text-gray-500 truncate",children:h.description})]})]},h.id))})]}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("p",{className:"text-sm text-gray-500 mb-2",children:["Selected tags (",k.length,"):"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:k.length===0?e.jsx("p",{className:"text-sm text-gray-400 italic",children:"No tags selected"}):k.map(h=>{const v=l.find(D=>D.id===h);return v?e.jsxs(K,{color:"light",className:"flex items-center",style:{borderLeft:`3px solid ${v.color||"#3b82f6"}`},children:[v.name,e.jsx("button",{onClick:D=>{D.stopPropagation(),z(v.id)},className:"ml-2 text-gray-500 hover:text-gray-700",children:e.jsx(ue,{className:"h-3 w-3"})})]},v.id):null})})]})]}),e.jsx(H.Footer,{children:e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(b,{color:"gray",onClick:c,children:"Cancel"}),e.jsxs(b,{onClick:L,disabled:i||k.length===0,children:[i?e.jsx(A,{size:"sm",className:"mr-2"}):null,j==="add"?"Add":"Remove"," Tags"]})]})})]})},Je=({selectedTags:t,onTagsChange:c})=>{const{currentOrganization:y}=te(),[g,d]=a.useState([]),[o,l]=a.useState(!1),[u,k]=a.useState(""),[_,S]=a.useState(!1),T=a.useRef(null);a.useEffect(()=>{if(!y)return;(async()=>{l(!0);try{const{success:f,tags:j}=await ge(y.id);f&&j&&d(j)}catch(f){console.error("Error fetching tags:",f)}finally{l(!1)}})()},[y]),a.useEffect(()=>{const n=f=>{T.current&&!T.current.contains(f.target)&&S(!1)};return document.addEventListener("mousedown",n),()=>{document.removeEventListener("mousedown",n)}},[]);const i=g.filter(n=>u===""||n.name.toLowerCase().includes(u.toLowerCase())||n.description&&n.description.toLowerCase().includes(u.toLowerCase())),P=n=>{t.includes(n.id)||c([...t,n.id]),k("")},r=n=>{c(t.filter(f=>f!==n))},p=()=>{c([])};return e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsxs("div",{className:"relative",ref:T,children:[e.jsxs(b,{size:"xs",color:"light",onClick:()=>S(!_),className:"flex items-center text-xs",children:[e.jsx(De,{className:"mr-1 h-4 w-4"}),"Filter by Tag"]}),_&&e.jsx("div",{className:"absolute z-10 mt-2 w-64 bg-white rounded-lg shadow-lg dark:bg-gray-800 border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"p-3",children:[e.jsx(ie,{type:"text",placeholder:"Search tags...",value:u,onChange:n=>k(n.target.value),className:"mb-2",icon:oe}),e.jsx("div",{className:"max-h-40 overflow-y-auto",children:o?e.jsx("div",{className:"text-center py-2 text-sm text-gray-500",children:"Loading tags..."}):i.length>0?e.jsx("div",{className:"py-1",children:i.map(n=>e.jsxs("button",{onClick:()=>{P(n)},className:`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center ${t.includes(n.id)?"bg-blue-50 dark:bg-blue-900":""}`,children:[e.jsx("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:n.color||"#3b82f6"}}),e.jsx("span",{children:n.name})]},n.id))}):e.jsx("div",{className:"text-center py-2 text-sm text-gray-500",children:"No matching tags found"})})]})})]}),t.length>0&&e.jsx(b,{size:"xs",color:"light",onClick:p,className:"ml-2 text-xs",children:"Clear Filters"})]}),t.length>0&&e.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:t.map(n=>{const f=g.find(j=>j.id===n);return f?e.jsxs(K,{color:"light",className:"flex items-center",style:{borderLeft:`3px solid ${f.color||"#3b82f6"}`},children:[f.name,e.jsx("button",{onClick:()=>r(f.id),className:"ml-2 text-gray-500 hover:text-gray-700",children:e.jsx(ue,{className:"h-3 w-3"})})]},f.id):null})})]})},fe=[{field:"name",required:!0,type:"string",customValidator:t=>typeof t=="string"&&t.length>255?"Product name must be 255 characters or less":null},{field:"sku",type:"string",customValidator:t=>t&&typeof t=="string"?t.length>50?"SKU must be 50 characters or less":ls(t):null},{field:"barcode",type:"string",customValidator:t=>t&&typeof t=="string"?t.length>50?"Barcode must be 50 characters or less":ns(t):null},{field:"unit_price",required:!0,type:"number",min:0},{field:"cost_price",type:"number",min:0},{field:"stock_quantity",type:"number",min:0},{field:"min_stock_level",type:"number",min:0},{field:"is_active",type:"boolean"}],Ge=["name","sku","barcode","description","category","unit_price","cost_price","stock_quantity","min_stock_level","is_active","tags"],es=t=>{const c=pe(t,fe);return{isValid:c.success,headers:c.headers,sampleData:c.data.slice(0,5),errors:c.errors,warnings:c.warnings,totalRows:c.data.length}},ss=async(t,c,y)=>{if(!c||c.trim()==="")return null;const g=c.trim();console.log(`Looking for category: "${g}" in organization: ${t}`);try{const{data:d,error:o}=await se.from("categories").select("id").eq("organization_id",t).eq("name",g).maybeSingle();if(o)return console.error("Error finding category:",o),null;if(d)return console.log(`Found existing category with ID: ${d.id}`),d.id;console.log(`Category not found, creating new category: "${g}"`);const{data:l,error:u}=await se.from("categories").insert({organization_id:t,name:g,description:"Auto-created during product import",created_by:y}).select("id").single();return u?(console.error("Error creating category:",u),null):(console.log(`Created new category with ID: ${l.id}`),l.id)}catch(d){return console.error("Unexpected error in getOrCreateCategory:",d),null}},ts=async(t,c)=>{const y=c.filter(l=>l.sku).map(l=>l.sku),g=c.filter(l=>l.barcode).map(l=>l.barcode),d=[],o=[];if(y.length>0){const{data:l}=await se.from("products").select("sku").eq("organization_id",t).in("sku",y);l&&d.push(...l.map(u=>u.sku))}if(g.length>0){const{data:l}=await se.from("products").select("barcode").eq("organization_id",t).in("barcode",g);l&&o.push(...l.map(u=>u.barcode))}return{skuDuplicates:d,barcodeDuplicates:o}},rs=async(t,c,y,g=!0)=>{const d=pe(c,fe);if(!d.success)return{success:!1,totalRows:d.data.length,successCount:0,errorCount:d.data.length,errors:d.errors,warnings:d.warnings,createdProducts:[]};const o=d.data,l=[...d.errors],u=[...d.warnings],k=[],{skuDuplicates:_,barcodeDuplicates:S}=await ts(t,o);let T=0,i=0;for(let P=0;P<o.length;P++){const r=o[P],p=P+2;try{if(console.log(`Processing row ${p}:`,r),r.sku&&_.includes(r.sku))if(g){u.push(`Row ${p}: Skipped - SKU '${r.sku}' already exists`);continue}else{l.push(`Row ${p}: SKU '${r.sku}' already exists`),i++;continue}if(r.barcode&&S.includes(r.barcode))if(g){u.push(`Row ${p}: Skipped - Barcode '${r.barcode}' already exists`);continue}else{l.push(`Row ${p}: Barcode '${r.barcode}' already exists`),i++;continue}let n=null;r.category&&r.category.trim()&&(console.log(`Creating/finding category: ${r.category}`),n=await ss(t,r.category,y),console.log(`Category ID: ${n}`));const f={name:r.name,sku:r.sku||null,barcode:r.barcode||null,description:r.description||null,category_id:n,unit_price:Number(r.unit_price),cost_price:r.cost_price?Number(r.cost_price):null,stock_quantity:r.stock_quantity?Number(r.stock_quantity):null,min_stock_level:r.min_stock_level?Number(r.min_stock_level):null,is_active:r.is_active!==void 0?r.is_active:!0};console.log("Creating product with data:",f);const j=await Fe(t,f);if(console.log("Create result:",j),j.product&&!j.error){if(k.push(j.product),T++,console.log(`Successfully created product: ${j.product.name}`),r.tags&&r.tags.trim()){const N=r.tags.split(",").map(E=>E.trim()).filter(E=>E);N.length>0&&u.push(`Row ${p}: Tags '${N.join(", ")}' not processed (feature not implemented)`)}}else{const N=j.error||"Failed to create product";console.error(`Failed to create product for row ${p}:`,N),l.push(`Row ${p}: ${N}`),i++}}catch(n){const f=n instanceof Error?n.message:"Unknown error";console.error(`Error processing row ${p}:`,n),l.push(`Row ${p}: ${f}`),i++}}return{success:i===0,totalRows:o.length,successCount:T,errorCount:i,errors:l,warnings:u,createdProducts:k}},as=()=>{const t=Ge,c=['"Sample Product 1","SP001","123456789012","A sample product description","Electronics","29.99","15.00","100","10","true","electronics,gadget"','"Sample Product 2","SP002","123456789013","Another sample product","Clothing","19.99","8.00","50","5","true","clothing,apparel"','"Sample Product 3","","","Product without SKU","Books","12.99","6.00","25","5","true","books,education"'],y=[t.map(l=>`"${l}"`).join(","),...c].join(`
`),g=new Blob([y],{type:"text/csv;charset=utf-8;"}),d=URL.createObjectURL(g),o=document.createElement("a");o.setAttribute("href",d),o.setAttribute("download","product_import_template.csv"),o.style.visibility="hidden",document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(d)},ls=t=>t?/^[A-Za-z0-9_-]+$/.test(t)?null:"SKU can only contain letters, numbers, hyphens, and underscores":null,ns=t=>{if(!t)return null;if(!/^\d+$/.test(t))return"Barcode should contain only numbers";const y=t.length,g=[8,12,13,14];return g.includes(y)?null:`Barcode length should be one of: ${g.join(", ")} digits`},cs=({show:t,onClose:c,onImportComplete:y})=>{const{user:g}=he(),{currentOrganization:d}=te(),o=a.useRef(null),[l,u]=a.useState("upload"),[k,_]=a.useState(null),[S,T]=a.useState(""),[i,P]=a.useState(null),[r,p]=a.useState(null),[n,f]=a.useState(!0),[j,N]=a.useState(null),[E,z]=a.useState(!1);Ee.useEffect(()=>{t&&(u("upload"),_(null),T(""),P(null),p(null),N(null),z(!1))},[t]);const L=m=>{var O;const C=(O=m.target.files)==null?void 0:O[0];if(!C)return;if(!C.name.toLowerCase().endsWith(".csv")){N("Please select a CSV file");return}if(C.size>5*1024*1024){N("File size must be less than 5MB");return}_(C),N(null);const I=new FileReader;I.onload=F=>{var X;const Q=(X=F.target)==null?void 0:X.result;T(Q);const Y=es(Q);P(Y),u("preview")},I.onerror=()=>{N("Failed to read file")},I.readAsText(C)},h=async()=>{if(!(!d||!g||!S)){z(!0),u("importing"),N(null);try{const m=await rs(d.id,S,g.id,n);p(m),u("complete"),y(m)}catch(m){N(m.message||"Import failed"),u("preview")}finally{z(!1)}}},v=()=>{o.current&&(o.current.value=""),c()},D=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(G,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-lg font-medium text-gray-900",children:"Import Products"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Upload a CSV file to bulk import products into your catalog"})]}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(Re,{className:"h-5 w-5 text-blue-400 mt-0.5"}),e.jsxs("div",{className:"ml-3 flex-1",children:[e.jsx("h4",{className:"text-sm font-medium text-blue-800",children:"Need a template?"}),e.jsx("p",{className:"mt-1 text-sm text-blue-700",children:"Download our CSV template with sample data to get started quickly."}),e.jsxs(b,{size:"xs",color:"light",className:"mt-2",onClick:as,children:[e.jsx(xe,{className:"mr-1 h-4 w-4"}),"Download Template"]})]})]})}),e.jsx("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx("input",{ref:o,type:"file",accept:".csv",onChange:L,className:"hidden"}),e.jsxs(b,{color:"primary",onClick:()=>{var m;return(m=o.current)==null?void 0:m.click()},children:[e.jsx(G,{className:"mr-2 h-5 w-5"}),"Choose CSV File"]}),e.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"or drag and drop your CSV file here"})]})}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"CSV Requirements:"}),e.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[e.jsx("li",{children:"• First row must contain column headers"}),e.jsx("li",{children:"• Required columns: name, unit_price"}),e.jsx("li",{children:"• Optional columns: sku, barcode, description, category, cost_price, stock_quantity, etc."}),e.jsx("li",{children:"• Maximum file size: 5MB"}),e.jsx("li",{children:"• Use comma (,) as field separator"})]})]}),j&&e.jsx(U,{color:"failure",children:j})]}),W=()=>i?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Import Preview"}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Review your data before importing ",i.totalRows," products"]})]}),e.jsx(K,{color:i.isValid?"success":"failure",children:i.isValid?"Valid":"Has Errors"})]}),(i.errors.length>0||i.warnings.length>0)&&e.jsxs("div",{className:"space-y-3",children:[i.errors.length>0&&e.jsxs(U,{color:"failure",children:[e.jsxs("div",{className:"font-medium",children:["Errors found (",i.errors.length,"):"]}),e.jsxs("ul",{className:"mt-2 text-sm list-disc list-inside",children:[i.errors.slice(0,5).map((m,C)=>e.jsx("li",{children:m},C)),i.errors.length>5&&e.jsxs("li",{children:["... and ",i.errors.length-5," more errors"]})]})]}),i.warnings.length>0&&e.jsxs(U,{color:"warning",children:[e.jsxs("div",{className:"font-medium",children:["Warnings (",i.warnings.length,"):"]}),e.jsxs("ul",{className:"mt-2 text-sm list-disc list-inside",children:[i.warnings.slice(0,3).map((m,C)=>e.jsx("li",{children:m},C)),i.warnings.length>3&&e.jsxs("li",{children:["... and ",i.warnings.length-3," more warnings"]})]})]})]}),i.sampleData.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"Sample Data (first 5 rows):"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(x,{children:[e.jsx(x.Head,{children:i.headers.map(m=>e.jsx(x.HeadCell,{children:m},m))}),e.jsx(x.Body,{children:i.sampleData.map((m,C)=>e.jsx(x.Row,{children:i.headers.map(I=>e.jsx(x.Cell,{children:String(m[I]||"")},I))},C))})]})})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"Import Options:"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(ee,{id:"skipDuplicates",checked:n,onChange:m=>f(m.target.checked)}),e.jsx("label",{htmlFor:"skipDuplicates",className:"ml-2 text-sm text-gray-700",children:"Skip products with duplicate SKU or barcode (recommended)"})]})]}),j&&e.jsx(U,{color:"failure",children:j})]}):null,M=()=>e.jsxs("div",{className:"space-y-6 text-center",children:[e.jsx(A,{size:"xl"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Importing Products..."}),e.jsx("p",{className:"text-sm text-gray-500",children:"Please wait while we process your file. This may take a few moments."})]})]}),re=()=>r?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[r.success?e.jsx(Ie,{className:"mx-auto h-12 w-12 text-green-500"}):e.jsx(ce,{className:"mx-auto h-12 w-12 text-red-500"}),e.jsxs("h3",{className:"mt-2 text-lg font-medium text-gray-900",children:["Import ",r.success?"Completed":"Completed with Errors"]})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:r.successCount}),e.jsx("div",{className:"text-sm text-green-700",children:"Imported"})]}),e.jsxs("div",{className:"text-center p-4 bg-red-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:r.errorCount}),e.jsx("div",{className:"text-sm text-red-700",children:"Errors"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-gray-600",children:r.totalRows}),e.jsx("div",{className:"text-sm text-gray-700",children:"Total"})]})]}),(r.errors.length>0||r.warnings.length>0)&&e.jsxs(ne,{"aria-label":"Import results",children:[r.errors.length>0&&e.jsx(ne.Item,{title:`Errors (${r.errors.length})`,children:e.jsx("div",{className:"max-h-60 overflow-y-auto",children:e.jsx("ul",{className:"text-sm text-red-600 space-y-1",children:r.errors.map((m,C)=>e.jsxs("li",{children:["• ",m]},C))})})}),r.warnings.length>0&&e.jsx(ne.Item,{title:`Warnings (${r.warnings.length})`,children:e.jsx("div",{className:"max-h-60 overflow-y-auto",children:e.jsx("ul",{className:"text-sm text-yellow-600 space-y-1",children:r.warnings.map((m,C)=>e.jsxs("li",{children:["• ",m]},C))})})})]})]}):null;return e.jsxs(H,{show:t,onClose:v,size:"4xl",children:[e.jsx(H.Header,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(G,{className:"h-6 w-6"}),"Product Import"]})}),e.jsxs(H.Body,{children:[l==="upload"&&D(),l==="preview"&&W(),l==="importing"&&M(),l==="complete"&&re()]}),e.jsx(H.Footer,{children:e.jsxs("div",{className:"flex justify-between w-full",children:[e.jsx("div",{children:l==="preview"&&e.jsx(b,{color:"light",onClick:()=>u("upload"),children:"Back"})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{color:"gray",onClick:v,children:l==="complete"?"Close":"Cancel"}),l==="preview"&&(i==null?void 0:i.isValid)&&e.jsx(b,{color:"primary",onClick:h,disabled:E,children:E?e.jsxs(e.Fragment,{children:[e.jsx(A,{size:"sm",className:"mr-2"}),"Importing..."]}):"Import Products"})]})]})})]})},ys=()=>{const{user:t}=he(),{currentOrganization:c}=te(),{canCreateProducts:y,canUpdateProducts:g,canDeleteProducts:d}=Me(),o=Ye(),[l,u]=a.useState(null),[k,_]=a.useState(!0);a.useEffect(()=>{(async()=>{if(!c||!t){_(!1);return}try{const{isOwner:w,error:R}=await Ae(c.id,t.id);u(w)}catch{}finally{_(!1)}})()},[c,t]);const[S,T]=a.useState([]),[i,P]=a.useState(!0),[r,p]=a.useState(null),[n,f]=a.useState(""),[j,N]=a.useState(1),[E,z]=a.useState(0),[L,h]=a.useState(10),[v,D]=a.useState(!1),[W,M]=a.useState(null),[re,m]=a.useState(!1),[C,I]=a.useState(null),[O,F]=a.useState([]),[Q,Y]=a.useState(!1),[X,ae]=a.useState(!1),[Z,je]=a.useState([]),[le,ye]=a.useState({}),V=async()=>{if(c){P(!0),p(null);try{const{products:s,count:w,error:R}=await Ue(c.id,{searchQuery:n||void 0,limit:L,offset:(j-1)*L,tagIds:Z.length>0?Z:void 0});R?p(R):(T(s),z(w),we(s))}catch(s){p(s.message||"An error occurred while fetching products")}finally{P(!1)}}};a.useEffect(()=>{V()},[c,j,n,Z,L]);const Ne=s=>{s.preventDefault(),N(1),V()},we=async s=>{const w={},R=s.map(async $=>{try{const{tags:B}=await We($.id);w[$.id]=B}catch(B){console.error(`Error fetching tags for product ${$.id}:`,B),w[$.id]=[]}});await Promise.all(R),ye(w)},be=s=>{I(s),m(!0)},ve=async()=>{if(!(!c||!C)){D(!0),M(null);try{const{success:s,error:w}=await Be(c.id,C.id);w?M(w):s&&(m(!1),I(null),V())}catch(s){M(s.message||"An error occurred while deleting the product")}finally{D(!1)}}},Ce=Math.ceil(E/L),ke=s=>{s.target.checked?F(S.map(w=>w.id)):F([])},Se=s=>{F(w=>w.includes(s)?w.filter(R=>R!==s):[...w,s])},Pe=async(s,w)=>{if(!c||O.length===0||s.length===0)return;const R=[];for(const $ of O)for(const B of s)w==="add"?R.push(qe($,B)):R.push(Ke($,B));try{return await Promise.all(R),V(),Promise.resolve()}catch($){return console.error("Error during bulk tagging:",$),Promise.reject($)}},Te=()=>{if(S.length===0){p("No products to export");return}Xe(S)},_e=s=>{s.successCount>0&&(V(),F([])),ae(!1)};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(me,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Products"}),e.jsx("p",{className:"text-gray-500",children:"Manage your product catalog here. You can add, edit, and delete products."})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(b,{color:"light",onClick:Te,children:[e.jsx(xe,{className:"mr-2 h-4 w-4"}),"Export"]}),(y||l)&&e.jsxs(b,{color:"light",onClick:()=>ae(!0),children:[e.jsx(G,{className:"mr-2 h-4 w-4"}),"Import"]}),k?e.jsxs(b,{color:"primary",disabled:!0,children:[e.jsx(A,{size:"sm",className:"mr-2"}),"Loading..."]}):(y||l)&&e.jsx(q,{to:"/products/create",children:e.jsxs(b,{color:"primary",children:[e.jsx(de,{className:"mr-2 h-5 w-5"}),"Add Product"]})})]})]}),e.jsx("form",{onSubmit:Ne,className:"mb-4",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(ie,{id:"search",type:"text",placeholder:"Search products by name, SKU, or barcode",value:n,onChange:s=>f(s.target.value),className:"flex-1",icon:oe}),e.jsx(b,{type:"submit",children:"Search"})]})}),e.jsx("div",{className:"mb-4",children:e.jsx(Je,{selectedTags:Z,onTagsChange:s=>{je(s),N(1)}})}),O.length>0&&e.jsxs("div",{className:"mb-4 flex items-center justify-between bg-blue-50 dark:bg-blue-900 p-3 rounded-lg",children:[e.jsxs("div",{className:"text-sm",children:[e.jsx("span",{className:"font-medium",children:O.length})," products selected"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{size:"xs",color:"light",onClick:()=>F([]),children:"Clear Selection"}),e.jsxs(b,{size:"xs",color:"info",onClick:()=>Y(!0),children:[e.jsx(Oe,{className:"mr-1 h-4 w-4"}),"Manage Tags"]})]})]}),r&&e.jsx(U,{color:"failure",icon:ce,className:"mb-4",children:r}),W&&e.jsx(U,{color:"failure",icon:ce,className:"mb-4",children:W}),i?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(A,{size:"xl"})}):S.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No products found"}),k?e.jsxs(b,{color:"primary",size:"sm",disabled:!0,children:[e.jsx(A,{size:"sm",className:"mr-2"}),"Loading..."]}):(y||l)&&e.jsx(q,{to:"/products/create",children:e.jsxs(b,{color:"primary",size:"sm",children:[e.jsx(de,{className:"mr-2 h-4 w-4"}),"Add Your First Product"]})})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(x,{hoverable:!0,children:[e.jsxs(x.Head,{children:[e.jsx(x.HeadCell,{className:"w-4",children:e.jsx(ee,{checked:O.length===S.length&&S.length>0,onChange:ke})}),e.jsx(x.HeadCell,{children:"Product"}),e.jsx(x.HeadCell,{children:"SKU"}),e.jsx(x.HeadCell,{children:"Price"}),e.jsx(x.HeadCell,{children:"Stock"}),e.jsx(x.HeadCell,{children:"Status"}),e.jsx(x.HeadCell,{children:"Tags"}),e.jsx(x.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(x.Body,{className:"divide-y",children:S.map(s=>{var w;return e.jsxs(x.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(x.Cell,{className:"w-4",children:e.jsx(ee,{checked:O.includes(s.id),onChange:()=>Se(s.id)})}),e.jsx(x.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:e.jsxs("div",{className:"flex items-center gap-3",children:[s.image_url?e.jsx("img",{src:s.image_url,alt:s.name,className:"h-10 w-10 rounded-md object-cover"}):e.jsx("div",{className:"h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center text-gray-500",children:"No img"}),e.jsxs("div",{children:[e.jsx(q,{to:`/products/details/${s.id}`,className:"font-medium hover:text-primary hover:underline",children:s.name}),e.jsx("p",{className:"text-xs text-gray-500",children:s.category_id?((w=s.category)==null?void 0:w.name)||"Category":"Uncategorized"})]})]})}),e.jsx(x.Cell,{children:s.sku||"-"}),e.jsx(x.Cell,{children:o(s.unit_price)}),e.jsx(x.Cell,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:`mr-2 ${(s.stock_quantity||0)<=(s.min_stock_level||0)?"text-red-500":"text-green-500"}`,children:s.stock_quantity||0}),(s.stock_quantity||0)<=(s.min_stock_level||0)&&e.jsx(K,{color:"failure",size:"sm",children:"Low"})]})}),e.jsx(x.Cell,{children:e.jsx(K,{color:s.is_active?"success":"gray",className:"whitespace-nowrap",children:s.is_active?"Active":"Inactive"})}),e.jsx(x.Cell,{children:le[s.id]&&le[s.id].length>0?e.jsx("div",{className:"flex flex-wrap gap-1 max-w-xs",children:e.jsx(Ve,{tags:le[s.id],maxDisplay:3,showTooltip:!0})}):e.jsx("span",{className:"text-gray-400 text-sm italic",children:"No tags"})}),e.jsx(x.Cell,{children:e.jsx("div",{className:"flex items-center",children:e.jsxs(J,{label:"",dismissOnClick:!0,renderTrigger:()=>e.jsx(b,{color:"gray",size:"xs",children:e.jsx(He,{className:"h-4 w-4"})}),children:[e.jsxs(J.Item,{as:q,to:`/products/details/${s.id}`,className:"text-blue-600 hover:bg-blue-50",children:[e.jsx($e,{className:"mr-2 h-4 w-4"}),"View Details"]}),g&&e.jsxs(J.Item,{as:q,to:`/products/edit/${s.id}`,className:"text-primary hover:bg-primary/10",children:[e.jsx(Le,{className:"mr-2 h-4 w-4"}),"Edit"]}),d&&e.jsxs(J.Item,{onClick:()=>be(s),className:"text-red-600 hover:bg-red-50",children:[e.jsx(ze,{className:"mr-2 h-4 w-4"}),"Delete"]})]})})})]},s.id)})})]})}),e.jsx(Qe,{currentPage:j,totalPages:Ce,itemsPerPage:L,totalItems:E,onPageChange:N,onItemsPerPageChange:s=>{h(s),N(1)},itemName:"products"})]})]}),re&&C&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs(me,{className:"max-w-md w-full",children:[e.jsx("h3",{className:"text-xl font-bold mb-2",children:"Delete Product"}),e.jsxs("p",{className:"text-gray-700 mb-4",children:["Are you sure you want to delete ",e.jsx("strong",{children:C.name}),"? This action cannot be undone."]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(b,{color:"gray",onClick:()=>{m(!1),I(null)},disabled:v,children:"Cancel"}),e.jsx(b,{color:"failure",onClick:ve,disabled:v,children:v?e.jsxs(e.Fragment,{children:[e.jsx(A,{size:"sm",className:"mr-2"}),"Deleting..."]}):"Delete"})]})]})}),e.jsx(Ze,{show:Q,onClose:()=>Y(!1),selectedIds:O,entityType:"product",onApplyTags:Pe}),e.jsx(cs,{show:X,onClose:()=>ae(!1),onImportComplete:_e})]})};export{ys as default};
