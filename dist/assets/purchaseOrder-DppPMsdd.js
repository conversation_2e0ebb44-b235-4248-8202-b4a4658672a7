import{s as d}from"./index-C6AV3cVN.js";const O=async(i,t)=>{try{console.log("Fetching purchase orders for organization:",i);let r=d.from("purchase_orders").select(`
        *,
        items:purchase_order_items(
          *,
          product:product_id(id, name, sku, description),
          uom:uom_id(id, code, name)
        ),
        supplier:supplier_id(id, name)
      `).eq("organization_id",i);t!=null&&t.status&&(r=r.eq("status",t.status)),t!=null&&t.supplierId&&(r=r.eq("supplier_id",t.supplierId)),t!=null&&t.searchQuery&&(r=r.ilike("order_number",`%${t.searchQuery}%`)),r=r.order("created_at",{ascending:!1});const{data:s,error:o}=await r;return o?(console.error("Error fetching purchase orders:",o),{purchaseOrders:[],error:o.message}):!s||s.length===0?{purchaseOrders:[]}:(console.log("Found purchase orders:",s.length),{purchaseOrders:s.map(a=>{var c;return{...a,supplier_name:((c=a.supplier)==null?void 0:c.name)||"Unknown Supplier",creator_name:"Staff"}})})}catch(r){return console.error("Error in getPurchaseOrders:",r),{purchaseOrders:[],error:r.message}}},f=async(i,t,r)=>{var s;try{console.log("Fetching purchase order by ID:",t);let o=`
      *,
      items:purchase_order_items(
        *,
        product:product_id(*)
    `;r!=null&&r.includeDetails&&(o+=`, 
        product_uoms:product_id(product_uoms(
          *,
          uom:uom_id(*)
        ))
      `),o+=`,
        uom:uom_id(*)
      ),
      supplier:supplier_id(*)
    `;const u=d.from("purchase_orders").select(o).eq("organization_id",i).eq("id",t).single(),{data:a,error:c}=await u;if(c)return console.error("Error fetching purchase order:",c),{error:c.message};const n={...a,supplier_name:((s=a.supplier)==null?void 0:s.name)||"Unknown Supplier",creator_name:"Staff"};return n.items&&Array.isArray(n.items)&&(n.items=n.items.map(p=>{let l=[];return p.product_uoms&&p.product_uoms.product_uoms&&(l=p.product_uoms.product_uoms),p.product&&l.length>0&&(p.product.product_uoms=l),p})),{purchaseOrder:n}}catch(o){return console.error("Error in getPurchaseOrderById:",o),{error:o.message}}},P=async(i,t,r)=>{var s;try{console.log("Creating purchase order from request:",{purchaseRequestId:t,data:r});const o=new Date,u=o.getFullYear(),a=String(o.getMonth()+1).padStart(2,"0"),c=String(o.getDate()).padStart(2,"0"),n=Math.floor(Math.random()*1e3).toString().padStart(3,"0"),p=`PO-${u}${a}${c}-${n}`,l=r.items.reduce((e,m)=>e+m.quantity*m.unitPrice,0),{data:g,error:_}=await d.from("purchase_orders").insert({organization_id:i,purchase_request_id:t,supplier_id:r.supplierId,order_number:p,status:"draft",order_date:new Date().toISOString(),expected_delivery_date:r.expectedDeliveryDate,total_amount:l,notes:r.notes,created_by:((s=(await d.auth.getUser()).data.user)==null?void 0:s.id)||""}).select().single();if(_)return console.error("Error creating purchase order:",_),{error:_.message};if(console.log("Purchase order created:",g),r.items.length>0){const e=r.items.map(h=>({purchase_order_id:g.id,product_id:"",quantity:h.quantity,unit_price:h.unitPrice,uom_id:h.uomId,received_quantity:0,base_quantity:0})),{data:m}=await d.from("purchase_request_items").select("id, product_id").in("id",r.items.map(h=>h.requestItemId));if(m)for(let h=0;h<e.length;h++){const q=m.find(E=>E.id===r.items[h].requestItemId);q&&(e[h].product_id=q.product_id)}const{error:y}=await d.from("purchase_order_items").insert(e);if(y)return console.error("Error adding purchase order items:",y),{error:y.message}}return await f(i,g.id)}catch(o){return console.error("Error in createPurchaseOrderFromRequest:",o),{error:o.message}}},S=async(i,t,r)=>{try{console.log("Updating purchase order:",{purchaseOrderId:t,data:r});const{purchaseOrder:s,error:o}=await f(i,t);if(o||!s)return{error:o||"Purchase order not found"};if(s.status!=="draft"&&!r.status)return{error:"Only draft purchase orders can be edited"};const u={};if(r.supplierId&&(u.supplier_id=r.supplierId),r.expectedDeliveryDate&&(u.expected_delivery_date=r.expectedDeliveryDate),r.notes!==void 0&&(u.notes=r.notes),r.status&&(u.status=r.status),r.items&&r.items.length>0){const a=r.items.reduce((c,n)=>c+n.quantity*n.unitPrice,0);u.total_amount=a}if(Object.keys(u).length>0){const{error:a}=await d.from("purchase_orders").update(u).eq("organization_id",i).eq("id",t);if(a)return console.error("Error updating purchase order:",a),{error:a.message}}if(r.items&&r.items.length>0){const{data:a,error:c}=await d.from("purchase_order_items").select("id, product_id").eq("purchase_order_id",t);if(c)return console.error("Error fetching existing items:",c),{error:c.message};const n=[],p=[];if(r.items.forEach(e=>{e.id?n.push({id:e.id,product_id:e.productId,quantity:e.quantity,unit_price:e.unitPrice,uom_id:e.uomId}):p.push({purchase_order_id:t,product_id:e.productId,quantity:e.quantity,unit_price:e.unitPrice,uom_id:e.uomId,received_quantity:0,base_quantity:0})}),n.length>0)for(const e of n){const{error:m}=await d.from("purchase_order_items").update({product_id:e.product_id,quantity:e.quantity,unit_price:e.unit_price,uom_id:e.uom_id}).eq("id",e.id).eq("purchase_order_id",t);if(m)return console.error("Error updating purchase order item:",m),{error:m.message}}if(p.length>0){const{error:e}=await d.from("purchase_order_items").insert(p);if(e)return console.error("Error creating purchase order items:",e),{error:e.message}}const l=a.map(e=>e.id),g=n.map(e=>e.id),_=l.filter(e=>!g.includes(e));if(_.length>0){const{error:e}=await d.from("purchase_order_items").delete().in("id",_);if(e)return console.error("Error deleting purchase order items:",e),{error:e.message}}}return await f(i,t)}catch(s){return console.error("Error in updatePurchaseOrder:",s),{error:s.message}}},D=async(i,t)=>{try{console.log("Sending purchase order to supplier:",t);const{purchaseOrder:r,error:s}=await f(i,t);if(s||!r)return{error:s||"Purchase order not found"};if(r.status!=="draft")return{error:`Cannot send purchase order with status "${r.status}" to supplier`};const{error:o}=await d.from("purchase_orders").update({status:"sent",updated_at:new Date().toISOString()}).eq("organization_id",i).eq("id",t);return o?(console.error("Error updating purchase order status:",o),{error:o.message}):await f(i,t)}catch(r){return console.error("Error in sendPurchaseOrderToSupplier:",r),{error:r.message}}},I=async(i,t)=>{var r;try{console.log("Creating purchase order:",t);const s=new Date,o=s.getFullYear(),u=String(s.getMonth()+1).padStart(2,"0"),a=String(s.getDate()).padStart(2,"0"),c=Math.floor(Math.random()*1e3).toString().padStart(3,"0"),n=`PO-${o}${u}${a}-${c}`,p=t.items.reduce((_,e)=>_+e.quantity*e.unitPrice,0),{data:l,error:g}=await d.from("purchase_orders").insert({organization_id:i,supplier_id:t.supplierId,order_number:n,status:"draft",order_date:new Date().toISOString(),expected_delivery_date:t.expectedDeliveryDate,total_amount:p,notes:t.notes,created_by:((r=(await d.auth.getUser()).data.user)==null?void 0:r.id)||""}).select().single();if(g)return console.error("Error creating purchase order:",g),{error:g.message};if(console.log("Purchase order created:",l),t.items.length>0){const _=t.items.map(m=>({purchase_order_id:l.id,product_id:m.productId,quantity:m.quantity,unit_price:m.unitPrice,uom_id:m.uomId,received_quantity:0,base_quantity:0})),{error:e}=await d.from("purchase_order_items").insert(_);if(e)return console.error("Error adding purchase order items:",e),{error:e.message}}return await f(i,l.id)}catch(s){return console.error("Error in createPurchaseOrder:",s),{error:s.message}}},b=async(i,t)=>{try{if(!i||!t)return{purchaseOrders:[],error:"Organization ID and purchase request ID are required"};const{data:r,error:s}=await d.from("purchase_orders").select(`
        *,
        supplier:supplier_id(name)
      `).eq("organization_id",i).eq("purchase_request_id",t).order("created_at",{ascending:!1});return s?(console.error("Error fetching purchase orders by request ID:",s),{purchaseOrders:[],error:s.message}):{purchaseOrders:r.map(u=>({...u,supplier_name:u.supplier?u.supplier.name:"Unknown Supplier"})),error:null}}catch(r){return console.error("Exception in getPurchaseOrdersByRequestId:",r),{purchaseOrders:[],error:r.message||"An error occurred while fetching purchase orders"}}};export{b as a,O as b,I as c,P as d,f as g,D as s,S as u};
