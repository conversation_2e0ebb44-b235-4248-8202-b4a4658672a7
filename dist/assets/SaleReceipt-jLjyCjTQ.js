import{h,j as e,aK as o,e as i,E as j,K as d,aU as p}from"./index-C6AV3cVN.js";import{C as b}from"./Card-yj7fueH8.js";import{u}from"./currencyFormatter-BsFWv3sX.js";import{d as y}from"./formatters-Cypx7G-j.js";const g=({sale:s,showPrintButton:x=!1,onPrint:l})=>{var n;const a=u(),{currentOrganization:t}=h();return e.jsxs(b,{className:"print:shadow-none print:border-none",children:[e.jsxs("div",{className:"text-center border-b border-gray-200 pb-4 mb-4",children:[e.jsx("h1",{className:"text-xl font-bold text-gray-900",children:(t==null?void 0:t.name)||"Your Business"}),(t==null?void 0:t.address)&&e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:t.address}),e.jsxs("div",{className:"flex justify-center space-x-4 text-sm text-gray-600 mt-1",children:[(t==null?void 0:t.phone)&&e.jsxs("span",{children:["Phone: ",t.phone]}),(t==null?void 0:t.email)&&e.jsxs("span",{children:["Email: ",t.email]})]}),(t==null?void 0:t.website)&&e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:t.website})]}),e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(o,{className:"mr-2 h-6 w-6 text-blue-500"}),e.jsx("h2",{className:"text-xl font-bold",children:"Receipt"})]}),e.jsx("div",{children:e.jsx(i,{color:"blue",children:s.invoice_number})})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(j,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{children:y(s.sale_date)})]}),s.customer&&e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(d,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{children:s.customer.name})]}),s.cashier&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(d,{className:"mr-2 h-5 w-5 text-blue-500"}),e.jsx("div",{children:e.jsxs("span",{className:"font-medium",children:["Cashier: ",`${s.cashier.first_name||""} ${s.cashier.last_name||""}`.trim()||"Unknown User"]})})]})]}),e.jsx("div",{className:"border-t border-b border-gray-200 py-4 mb-4",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{className:"text-left",children:e.jsxs("tr",{className:"border-b border-gray-200",children:[e.jsx("th",{className:"pb-2",children:"Item"}),e.jsx("th",{className:"pb-2 text-right",children:"Qty"}),e.jsx("th",{className:"pb-2 text-right",children:"Price"}),e.jsx("th",{className:"pb-2 text-right",children:"Total"})]})}),e.jsx("tbody",{children:(n=s.items)==null?void 0:n.map((r,m)=>{var c;return e.jsxs("tr",{className:"border-b border-gray-100",children:[e.jsx("td",{className:"py-2",children:((c=r.product)==null?void 0:c.name)||"Product"}),e.jsxs("td",{className:"py-2 text-right",children:[r.quantity," pcs"]}),e.jsx("td",{className:"py-2 text-right",children:a(r.unit_price)}),e.jsx("td",{className:"py-2 text-right",children:a(r.unit_price*r.quantity)})]},r.id||m)})})]})}),e.jsxs("div",{className:"space-y-2 mb-4",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Subtotal"}),e.jsx("span",{children:a(s.subtotal)})]}),s.discount_amount>0&&e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Discount"}),e.jsxs("span",{className:"text-red-500",children:["-",a(s.discount_amount)]})]}),s.loyalty_points_discount>0&&s.loyalty_points_used>0&&e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"mr-1",children:"Loyalty Points"}),e.jsxs(i,{color:"purple",className:"text-xs",children:[s.loyalty_points_used," points"]})]}),e.jsxs("span",{className:"text-purple-500",children:["-",a(s.loyalty_points_discount)]})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Tax"}),e.jsx("span",{children:a(s.tax_amount)})]}),e.jsxs("div",{className:"flex justify-between font-bold text-lg pt-2 border-t border-gray-200",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{children:a(s.total_amount)})]})]}),s.loyalty_points_earned>0&&e.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg mb-4 flex items-center",children:[e.jsx(p,{className:"mr-2 h-5 w-5 text-blue-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Points Earned"}),e.jsxs("p",{className:"text-sm text-blue-600",children:[s.loyalty_points_earned," points added to your account"]})]})]}),s.payment_method==="cash"&&s.cash_tendered&&e.jsxs("div",{className:"space-y-2 mb-4 border-t border-gray-200 pt-4",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Cash Tendered:"}),e.jsx("span",{children:a(s.cash_tendered)})]}),s.change_amount&&s.change_amount>0&&e.jsxs("div",{className:"flex justify-between text-sm font-medium",children:[e.jsx("span",{children:"Change:"}),e.jsx("span",{children:a(s.change_amount)})]})]}),e.jsxs("div",{className:"text-center text-sm text-gray-500",children:[e.jsx("p",{children:"Thank you for your business!"}),e.jsxs("p",{className:"mt-1",children:["Payment Method: ",s.payment_method]}),s.notes&&e.jsx("p",{className:"mt-2 italic",children:s.notes})]}),x&&l&&e.jsx("div",{className:"mt-4 print:hidden",children:e.jsx("button",{onClick:l,className:"w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded",children:"Print Receipt"})})]})};export{g as S};
