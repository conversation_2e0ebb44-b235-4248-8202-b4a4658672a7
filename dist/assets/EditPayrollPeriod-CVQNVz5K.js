import{ab as A,d as z,h as H,r as i,j as t,i as R,A as C,G as _,B as f,o as F,a6 as h,P as G,U as J,ay as U}from"./index-C6AV3cVN.js";import{C as Y}from"./Card-yj7fueH8.js";import{D as b}from"./react-datepicker-BrCvW-wJ.js";import{a as K,u as Q}from"./payroll-DcVgVc3z.js";import{P as V}from"./payroll-j3fcCwK0.js";import{P as W}from"./PageTitle-FHPo8gWi.js";const re=()=>{const{id:l}=A(),x=z(),{currentOrganization:c}=H(),[O,v]=i.useState(!0),[P,m]=i.useState(null),[D,N]=i.useState(!1),[w,g]=i.useState(null),[j,y]=i.useState(""),[r,M]=i.useState(null),[s,S]=i.useState(null),[o,p]=i.useState(null),[E,T]=i.useState(!1),[n,I]=i.useState({}),L=async()=>{if(!(!c||!l)){v(!0),m(null);try{const{period:e,error:a}=await K(c.id,l);if(a)m(a);else if(e){if(e.status!==V.DRAFT){m("Only payroll periods in draft status can be edited");return}y(e.name),M(e.start_date?new Date(e.start_date):null),S(e.end_date?new Date(e.end_date):null),p(e.payment_date?new Date(e.payment_date):null),T(e.is_thirteenth_month||!1)}else m("Payroll period not found")}catch(e){m(e.message)}finally{v(!1)}}};i.useEffect(()=>{L()},[c,l]);const B=()=>{const e={};return j.trim()||(e.name="Period name is required"),r||(e.startDate="Start date is required"),s?r&&s<r&&(e.endDate="End date must be after start date"):e.endDate="End date is required",o?s&&o<s&&(e.paymentDate="Payment date must be on or after end date"):e.paymentDate="Payment date is required",I(e),Object.keys(e).length===0},q=async e=>{if(e.preventDefault(),!(!B()||!c||!l)&&!(!r||!s||!o)){N(!0),g(null);try{const a={name:j,start_date:r.toISOString().split("T")[0],end_date:s.toISOString().split("T")[0],payment_date:o.toISOString().split("T")[0],is_thirteenth_month:E},{period:d,error:u}=await Q(c.id,l,a);u?g(u):d&&x(`/payroll/periods/${l}`)}catch(a){g(a.message)}finally{N(!1)}}},$=()=>{if(r&&s){const e=r.toLocaleString("default",{month:"long"}),a=r.getDate(),d=s.toLocaleString("default",{month:"long"}),u=s.getDate(),k=r.getFullYear();y(e===d?`${e} ${a}-${u}, ${k}`:`${e} ${a} - ${d} ${u}, ${k}`)}};return O?t.jsx("div",{className:"flex justify-center items-center h-64",children:t.jsx(R,{size:"xl"})}):P?t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsxs(C,{color:"failure",icon:_,children:[t.jsx("span",{className:"font-medium",children:"Error!"})," ",P]}),t.jsx("div",{className:"mt-4",children:t.jsxs(f,{color:"gray",onClick:()=>x("/payroll"),children:[t.jsx(F,{className:"mr-2 h-5 w-5"}),"Back to Payroll Periods"]})})]}):t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx("div",{className:"flex justify-between items-center mb-6",children:t.jsxs("div",{className:"flex items-center",children:[t.jsxs(f,{color:"gray",className:"mr-4",onClick:()=>x(`/payroll/periods/${l}`),children:[t.jsx(F,{className:"mr-2 h-5 w-5"}),"Back"]}),t.jsx(W,{title:"Edit Payroll Period"})]})}),t.jsx(Y,{children:t.jsxs("form",{onSubmit:q,className:"space-y-4",children:[w&&t.jsx(C,{color:"failure",icon:_,children:w}),t.jsxs("div",{children:[t.jsx("div",{className:"mb-2 block",children:t.jsx(h,{htmlFor:"name",value:"Period Name"})}),t.jsx(G,{id:"name",value:j,onChange:e=>y(e.target.value),placeholder:"e.g., January 1-15, 2023",color:n.name?"failure":void 0,helperText:n.name})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{children:[t.jsx("div",{className:"mb-2 block",children:t.jsx(h,{htmlFor:"startDate",value:"Start Date"})}),t.jsx(b,{id:"startDate",selected:r,onChange:e=>{if(M(e),e&&(!s||s<e)){const a=new Date(e);a.setDate(e.getDate()+14),S(a);const d=new Date(a);d.setDate(a.getDate()+5),p(d)}setTimeout($,100)},dateFormat:"MMMM d, yyyy",className:`w-full rounded-lg ${n.startDate?"border-red-500":"border-gray-300"} p-2.5`,placeholderText:"Select start date"}),n.startDate&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:n.startDate})]}),t.jsxs("div",{children:[t.jsx("div",{className:"mb-2 block",children:t.jsx(h,{htmlFor:"endDate",value:"End Date"})}),t.jsx(b,{id:"endDate",selected:s,onChange:e=>{if(S(e),setTimeout($,100),e&&(!o||o<e)){const a=new Date(e);a.setDate(e.getDate()+5),p(a)}},dateFormat:"MMMM d, yyyy",className:`w-full rounded-lg ${n.endDate?"border-red-500":"border-gray-300"} p-2.5`,placeholderText:"Select end date",minDate:r||void 0}),n.endDate&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:n.endDate})]})]}),t.jsxs("div",{children:[t.jsx("div",{className:"mb-2 block",children:t.jsx(h,{htmlFor:"paymentDate",value:"Payment Date"})}),t.jsx(b,{id:"paymentDate",selected:o,onChange:e=>p(e),dateFormat:"MMMM d, yyyy",className:`w-full rounded-lg ${n.paymentDate?"border-red-500":"border-gray-300"} p-2.5`,placeholderText:"Select payment date",minDate:s||void 0}),n.paymentDate&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:n.paymentDate})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(J,{id:"isThirteenthMonth",checked:E,onChange:e=>T(e.target.checked)}),t.jsx(h,{htmlFor:"isThirteenthMonth",children:"This is a 13th month pay period"})]}),t.jsxs("div",{className:"flex justify-end space-x-2 pt-4",children:[t.jsx(f,{color:"gray",onClick:()=>x(`/payroll/periods/${l}`),disabled:D,children:"Cancel"}),t.jsxs(f,{type:"submit",color:"primary",isProcessing:D,disabled:D,children:[t.jsx(U,{className:"mr-2 h-5 w-5"}),"Save Changes"]})]})]})})]})};export{re as default};
