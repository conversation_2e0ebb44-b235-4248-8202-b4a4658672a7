import{d as ie,h as X,r,j as e,P as ne,Q as oe,B as T,A as $,J as V,i as R,_ as a,al as ce,Y as Se,e as Y,bv as ve,a2 as de,a3 as me,aB as be,M as P,s as U,a0 as _e,ab as Ce,aD as we,p as Te}from"./index-C6AV3cVN.js";import{C as Ee}from"./Card-yj7fueH8.js";import{b as ke}from"./supplier-BJDz25mb.js";import{c as He,d as De,S as Pe}from"./SimpleSupplierProductModal-3fg5k0TC.js";import{u as ue}from"./currencyFormatter-BsFWv3sX.js";import{g as ze}from"./product-Ca8DWaNR.js";import{T as Ae}from"./TagSelector-DMJHWHGW.js";import"./formatters-Cypx7G-j.js";import"./tagService-sPq402Av.js";const Fe=({supplierId:l,onProductCountChange:i,refreshTrigger:n=0})=>{var re;const t=ie(),{currentOrganization:j}=X(),u=ue(),[E,x]=r.useState([]),[y,N]=r.useState(0),[b,z]=r.useState(!0),[k,S]=r.useState(null),[f,d]=r.useState(1),[_]=r.useState(10),[C,c]=r.useState(""),[o,H]=r.useState(null),[h,D]=r.useState(!1),[B,M]=r.useState(null),[w,p]=r.useState(!1),[A,m]=r.useState(null),[F,O]=r.useState(!1),[I,xe]=r.useState(null),[G,Z]=r.useState(!1),[ee,J]=r.useState(null);r.useEffect(()=>{q()},[j,f,l,n]),r.useEffect(()=>{o&&clearTimeout(o);const s=setTimeout(()=>{q()},300);return H(s),()=>{o&&clearTimeout(o)}},[C]),r.useEffect(()=>{i&&i(y)},[y,i]);const q=async()=>{if(!(!j||!l)){z(!0),S(null);try{const{supplierProducts:s,count:v,error:g}=await He(j.id,l,{searchQuery:C,limit:_,offset:(f-1)*_,sortBy:"product.name",sortOrder:"asc"});g?S(g):(x(s),N(v))}catch(s){S(s.message||"An error occurred while fetching supplier products")}finally{z(!1)}}},he=async s=>{console.log("Opening edit modal for supplier product:",s),m(null);try{const{data:v,error:g}=await U.from("supplier_products").select(`
          *,
          product:product_id (*),
          uom:uom_id (
            id,
            code,
            name
          )
        `).eq("id",s.id).single();g?(console.error("Error fetching supplier product for editing:",g),M(s)):v?(console.log("Fetched latest supplier product data for editing:",v),M(v)):(console.warn("No data found for supplier product:",s.id),M(s))}catch(v){console.error("Exception fetching supplier product for editing:",v),M(s)}D(!0)},pe=s=>{xe(s),J(null),O(!0)},ge=async s=>{var v;if(B){p(!0),m(null);try{console.log("Updating supplier product with data:",s);const g=s.unit_price===null||s.unit_price===void 0?((v=B.product)==null?void 0:v.unit_price)||0:s.unit_price,Q=g*s.conversion_factor;console.log("Calculated base price:",{unit_price:g,conversion_factor:s.conversion_factor,base_price:Q});const K={unit_price:g,minimum_order_quantity:s.minimum_order_quantity,lead_time_days:s.lead_time_days,is_preferred:s.is_preferred,notes:s.notes,uom_id:s.uom_id,uom_name:s.uom_name,conversion_factor:s.conversion_factor,organization_id:j==null?void 0:j.id,base_price:Q};console.log("Final data being updated directly in database:",K);const{data:W,error:L}=await U.from("supplier_products").update(K).eq("id",B.id).select("*").single();L?(console.error("Error updating supplier product:",L),m(L.message)):(console.log("Supplier product updated successfully:",W),D(!1),q())}catch(g){console.error("Exception updating supplier product:",g),m(g.message||"An error occurred while updating the product")}finally{p(!1)}}},fe=async()=>{if(I){Z(!0),J(null);try{console.log("Deleting supplier product:",I.id);const{error:s}=await U.from("supplier_products").delete().eq("id",I.id);s?(console.error("Error deleting supplier product:",s),J(s.message)):(console.log("Supplier product deleted successfully"),O(!1),E.length===1&&f>1?d(f-1):q())}catch(s){console.error("Exception deleting supplier product:",s),J(s.message||"An error occurred while removing the product")}finally{Z(!1)}}},je=s=>{d(s)},ye=s=>{s.preventDefault(),d(1),q()},Ne=s=>{t(`/products/details/${s}`)},se=Math.ceil(y/_);return e.jsxs("div",{className:"space-y-4",children:[e.jsx("form",{onSubmit:ye,className:"mb-4",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(ne,{id:"search",type:"text",placeholder:"Search products by name, SKU, or barcode",value:C,onChange:s=>c(s.target.value),className:"flex-1",icon:oe}),e.jsx(T,{type:"submit",children:"Search"})]})}),k&&e.jsx($,{color:"failure",icon:V,children:k}),b?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(R,{size:"xl"})}):E.length===0?e.jsx("div",{className:"p-8 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No products found for this supplier"})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(a,{hoverable:!0,children:[e.jsxs(a.Head,{children:[e.jsx(a.HeadCell,{children:"Product"}),e.jsx(a.HeadCell,{children:"SKU"}),e.jsx(a.HeadCell,{children:"Unit"}),e.jsx(a.HeadCell,{children:"Supplier Price"}),e.jsx(a.HeadCell,{children:e.jsxs("div",{className:"flex items-center",children:["Total Price",e.jsx(ce,{content:"Price per base unit (Supplier Price × Conversion Factor)",children:e.jsx(Se,{className:"ml-1 h-4 w-4 text-gray-400"})})]})}),e.jsx(a.HeadCell,{children:"Min. Order"}),e.jsx(a.HeadCell,{children:"Lead Time"}),e.jsx(a.HeadCell,{children:"Status"}),e.jsx(a.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(a.Body,{className:"divide-y",children:E.map(s=>{var v,g,Q,K,W,L,te,ae;return e.jsxs(a.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(a.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:e.jsxs("div",{className:"flex items-center gap-3",children:[(v=s.product)!=null&&v.image_url?e.jsx("img",{src:s.product.image_url,alt:(g=s.product)==null?void 0:g.name,className:"h-10 w-10 rounded-md object-cover"}):e.jsx("div",{className:"h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center",children:e.jsx("span",{className:"text-gray-500 text-xs",children:"No image"})}),e.jsxs("div",{children:[e.jsx("button",{onClick:()=>{var le;return((le=s.product)==null?void 0:le.id)&&Ne(s.product.id)},className:"font-medium text-left hover:text-blue-600 hover:underline cursor-pointer focus:outline-none",children:(Q=s.product)==null?void 0:Q.name}),e.jsx("p",{className:"text-xs text-gray-500",children:((W=(K=s.product)==null?void 0:K.category)==null?void 0:W.name)||"Uncategorized"})]})]})}),e.jsx(a.Cell,{children:((L=s.product)==null?void 0:L.sku)||"-"}),e.jsx(a.Cell,{children:s.uom?e.jsxs(Y,{color:"info",children:[s.uom.name," (",s.uom.code,")"]}):s.uom_name?e.jsx(Y,{color:"info",children:s.uom_name}):e.jsx(Y,{color:"gray",children:"Default Unit"})}),e.jsx(a.Cell,{children:s.unit_price!==null?u(s.unit_price):u(((te=s.product)==null?void 0:te.unit_price)||0)}),e.jsx(a.Cell,{children:s.unit_price!==null&&s.conversion_factor?e.jsx(ce,{content:`Calculation: ${u(s.unit_price)} × ${s.conversion_factor} = ${u(s.unit_price*s.conversion_factor)}`,children:e.jsxs("div",{className:"cursor-help",children:[u(s.unit_price*s.conversion_factor),e.jsx("div",{className:"text-xs text-gray-500",children:"per base unit"})]})}):s.unit_price!==null?e.jsxs("div",{children:[u(s.unit_price),e.jsx("div",{className:"text-xs text-gray-500",children:"(no conversion)"})]}):e.jsxs("div",{children:[u(((ae=s.product)==null?void 0:ae.unit_price)||0),e.jsx("div",{className:"text-xs text-gray-500",children:"(default price)"})]})}),e.jsx(a.Cell,{children:s.minimum_order_quantity!==null?s.minimum_order_quantity:"-"}),e.jsx(a.Cell,{children:s.lead_time_days!==null?`${s.lead_time_days} days`:"-"}),e.jsx(a.Cell,{children:s.is_preferred&&e.jsx(Y,{color:"success",icon:ve,children:"Preferred"})}),e.jsx(a.Cell,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(T,{color:"light",size:"xs",onClick:()=>he(s),title:"Edit Product",children:e.jsx(de,{className:"h-4 w-4"})}),e.jsx(T,{color:"light",size:"xs",onClick:()=>pe(s),title:"Remove Product",children:e.jsx(me,{className:"h-4 w-4"})})]})})]},s.id)})})]})}),se>1&&e.jsx("div",{className:"flex justify-center mt-4",children:e.jsx(be,{currentPage:f,totalPages:se,onPageChange:je,showIcons:!0})})]}),e.jsxs(P,{show:h,onClose:()=>D(!1),size:"lg",children:[e.jsx(P.Header,{children:"Edit Supplier Product"}),e.jsx(P.Body,{children:B&&B.product&&e.jsx(De,{product:B.product,supplierId:l,onSubmit:ge,onCancel:()=>D(!1),isSubmitting:w,error:A||void 0})})]}),e.jsxs(P,{show:F,onClose:()=>O(!1),size:"md",children:[e.jsx(P.Header,{children:"Confirm Removal"}),e.jsxs(P.Body,{children:[ee&&e.jsx($,{color:"failure",icon:V,className:"mb-4",children:ee}),e.jsxs("p",{className:"text-gray-500",children:["Are you sure you want to remove ",e.jsx("span",{className:"font-semibold",children:(re=I==null?void 0:I.product)==null?void 0:re.name})," from this supplier?"]}),e.jsx("p",{className:"text-gray-500 mt-2",children:"This action cannot be undone. This will permanently remove the product from this supplier."}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-4",children:[e.jsx(T,{color:"gray",onClick:()=>O(!1),disabled:G,children:"Cancel"}),e.jsx(T,{color:"failure",onClick:fe,disabled:G,children:G?e.jsxs(e.Fragment,{children:[e.jsx(R,{size:"sm",className:"mr-2"}),"Removing..."]}):"Remove Product"})]})]})]})]})},Be=({show:l,onClose:i,onSelect:n,onSearch:t,isLoading:j=!1,error:u=null})=>{const E=ue(),[x,y]=r.useState(""),[N,b]=r.useState([]),[z,k]=r.useState(0),[S,f]=r.useState(!1),[d,_]=r.useState(null);r.useEffect(()=>{l||(y(""),b([]),k(0),_(null))},[l]);const C=async c=>{if(c&&c.preventDefault(),!!x.trim()){f(!0),_(null);try{const{products:o,count:H}=await t(x);b(o),k(H)}catch(o){console.error("Error in handleSearch:",o),_(o.message||"An error occurred while searching")}finally{f(!1)}}};return r.useEffect(()=>{const c=setTimeout(()=>{x.trim().length>=2&&C()},500);return()=>clearTimeout(c)},[x]),e.jsxs(P,{show:l,onClose:i,size:"xl",children:[e.jsx(P.Header,{children:"Search Products"}),e.jsxs(P.Body,{children:[u&&e.jsx($,{color:"failure",icon:V,className:"mb-4",children:u}),d&&e.jsx($,{color:"failure",icon:V,className:"mb-4",children:d}),e.jsx("form",{onSubmit:C,className:"mb-4",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(ne,{id:"product-search",type:"text",placeholder:"Search products by name, SKU, or barcode",value:x,onChange:c=>y(c.target.value),className:"flex-1",icon:oe,autoFocus:!0}),e.jsx(T,{type:"submit",disabled:S||j,children:S?e.jsxs(e.Fragment,{children:[e.jsx(R,{size:"sm",className:"mr-2"}),"Searching..."]}):"Search"})]})}),j||S?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(R,{size:"xl"})}):N.length===0?e.jsx("div",{className:"p-8 text-center",children:e.jsx("p",{className:"text-gray-500",children:x.trim()?"No products found matching your search":"Enter a search term to find products"})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(a,{hoverable:!0,children:[e.jsxs(a.Head,{children:[e.jsx(a.HeadCell,{children:"Product"}),e.jsx(a.HeadCell,{children:"SKU"}),e.jsx(a.HeadCell,{children:"Price"}),e.jsx(a.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(a.Body,{className:"divide-y",children:N.map(c=>{var o;return e.jsxs(a.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(a.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:e.jsxs("div",{className:"flex items-center gap-3",children:[c.image_url?e.jsx("img",{src:c.image_url,alt:c.name,className:"h-10 w-10 rounded-md object-cover"}):e.jsx("div",{className:"h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center",children:e.jsx("span",{className:"text-gray-500 text-xs",children:"No image"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:c.name}),e.jsx("p",{className:"text-xs text-gray-500",children:((o=c.category)==null?void 0:o.name)||"Uncategorized"})]})]})}),e.jsx(a.Cell,{children:c.sku||"-"}),e.jsx(a.Cell,{children:E(c.unit_price)}),e.jsx(a.Cell,{children:e.jsx(T,{color:"primary",size:"xs",onClick:()=>n(c),children:"Select"})})]},c.id)})})]})}),e.jsxs("div",{className:"mt-4 text-sm text-gray-500",children:["Showing ",N.length," of ",z," results"]})]})]}),e.jsx(P.Footer,{children:e.jsx(T,{color:"gray",onClick:i,children:"Cancel"})})]})},Oe=({supplierId:l,onSuccess:i,buttonText:n="Add Product",buttonSize:t="md",buttonColor:j="primary"})=>{const{currentOrganization:u}=X(),[E,x]=r.useState(!1),[y,N]=r.useState(!1),[b,z]=r.useState(null),[k,S]=r.useState(!1),[f,d]=r.useState(null),_=async o=>{console.log("Selected product:",o),z(o),x(!1),N(!0)},C=async o=>{if(!u)return{products:[],count:0};S(!0),d(null);try{const{products:H,count:h,error:D}=await ze(u.id,{searchQuery:o,limit:10,offset:0});return D?(console.error("Error searching products:",D),d(D),{products:[],count:0}):{products:H,count:h}}catch(H){return console.error("Exception searching products:",H),d(H.message||"An error occurred while searching products"),{products:[],count:0}}finally{S(!1)}},c=()=>{N(!1),z(null),i()};return e.jsxs(e.Fragment,{children:[e.jsxs(T,{size:t,color:j,onClick:()=>x(!0),children:[e.jsx(_e,{className:"mr-2 h-4 w-4"}),n]}),e.jsx(Be,{show:E,onClose:()=>x(!1),onSelect:_,onSearch:C,isLoading:k,error:f}),b&&e.jsx(Pe,{show:y,onClose:()=>N(!1),onSuccess:c,supplierId:l,product:b})]})},Ie=async l=>{try{const{data:i,error:n}=await U.rpc("get_entity_tags",{p_entity_type:"supplier",p_entity_id:l});return n?(console.error("Error fetching supplier tags:",n),{tags:[],error:n.message}):{tags:i||[]}}catch(i){return console.error("Error in getSupplierTags:",i),{tags:[],error:i.message}}},Me=async(l,i)=>{try{const{data:n,error:t}=await U.rpc("add_tag_to_entity",{p_tag_id:i,p_entity_type:"supplier",p_entity_id:l});return t?(console.error("Error adding tag to supplier:",t),{success:!1,error:t.message}):{success:!0}}catch(n){return console.error("Error in addTagToSupplier:",n),{success:!1,error:n.message}}},Le=async(l,i)=>{try{const{data:n,error:t}=await U.rpc("remove_tag_from_entity",{p_tag_id:i,p_entity_type:"supplier",p_entity_id:l});return t?(console.error("Error removing tag from supplier:",t),{success:!1,error:t.message}):{success:!0}}catch(n){return console.error("Error in removeTagFromSupplier:",n),{success:!1,error:n.message}}},Ye=()=>{const{id:l}=Ce(),i=ie(),{currentOrganization:n}=X(),[t,j]=r.useState(null),[u,E]=r.useState(!0),[x,y]=r.useState(null),[N,b]=r.useState([]),[z,k]=r.useState(!1),[S,f]=r.useState(null),[d,_]=r.useState("details"),[C,c]=r.useState(0),[o,H]=r.useState(0);r.useEffect(()=>{(async()=>{if(!(!n||!l)){E(!0),y(null);try{const{supplier:p,error:A}=await ke(n.id,l);A?y(A):p?j(p):y("Supplier not found")}catch(p){y(p.message||"An error occurred while fetching the supplier")}finally{E(!1)}}})()},[n,l]),r.useEffect(()=>{(async()=>{if(l){k(!0),f(null);try{const{tags:p,error:A}=await Ie(l);A?f(A):b(p)}catch(p){f(p.message||"An error occurred while fetching tags")}finally{k(!1)}}})()},[l]);const h=(w,p="-")=>w||e.jsx("span",{className:"text-gray-400",children:p}),D=()=>{i(`/suppliers/edit/${l}`)},B=()=>{i(`/suppliers?delete=${l}`)},M=async w=>{if(!l)return;const p=w.filter(m=>!N.some(F=>F.id===m.id)),A=N.filter(m=>!w.some(F=>F.id===m.id));b(w);for(const m of p)try{await Me(l,m.id)}catch(F){console.error(`Failed to add tag ${m.name}:`,F),b(O=>O.filter(I=>I.id!==m.id))}for(const m of A)try{await Le(l,m.id)}catch(F){console.error(`Failed to remove tag ${m.name}:`,F),b(O=>[...O,m])}};return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(Ee,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Supplier Details"}),e.jsx("p",{className:"text-gray-500",children:"View detailed information about this supplier"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(T,{color:"gray",onClick:()=>i("/suppliers"),children:[e.jsx(we,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsxs(T,{color:"primary",onClick:D,children:[e.jsx(de,{className:"mr-2 h-5 w-5"}),"Edit"]}),e.jsxs(T,{color:"failure",onClick:B,children:[e.jsx(me,{className:"mr-2 h-5 w-5"}),"Delete"]})]})]}),x&&e.jsx($,{color:"failure",icon:V,className:"mb-4",children:x}),u?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(R,{size:"xl"})}):t?e.jsxs("div",{children:[e.jsx("div",{className:"mb-4",children:e.jsxs("ul",{className:"flex flex-wrap -mb-px text-sm font-medium text-center",role:"tablist",children:[e.jsx("li",{className:"mr-2",role:"presentation",children:e.jsx("button",{className:`inline-block p-4 border-b-2 rounded-t-lg ${d==="details"?"border-blue-600 text-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,type:"button",role:"tab","aria-selected":d==="details",onClick:()=>_("details"),children:"Details"})}),e.jsx("li",{className:"mr-2",role:"presentation",children:e.jsx("button",{className:`inline-block p-4 border-b-2 rounded-t-lg ${d==="products"?"border-blue-600 text-blue-600":"border-transparent hover:text-gray-600 hover:border-gray-300"}`,type:"button",role:"tab","aria-selected":d==="products",onClick:()=>_("products"),children:"Products"})})]})}),d==="details"&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Basic Information"}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Supplier Name"}),e.jsx("p",{className:"mt-1 text-base font-medium",children:h(t.name)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Contact Person"}),e.jsx("p",{className:"mt-1 text-base",children:h(t.contact_person)})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Email"}),e.jsx("p",{className:"mt-1 text-base",children:h(t.email)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Phone"}),e.jsx("p",{className:"mt-1 text-base",children:h(t.phone)})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Tax ID"}),e.jsx("p",{className:"mt-1 text-base",children:h(t.tax_id)})]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Notes"}),e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:e.jsx("p",{className:"whitespace-pre-line",children:h(t.notes,"No notes available")})})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Tags"}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(Te,{className:"mt-1 mr-3 h-5 w-5 text-gray-500"}),e.jsx("div",{className:"w-full",children:z?e.jsxs("div",{className:"flex items-center",children:[e.jsx(R,{size:"sm",className:"mr-2"}),e.jsx("span",{className:"text-gray-500",children:"Loading tags..."})]}):S?e.jsx($,{color:"failure",className:"mb-4",children:S}):e.jsx("div",{children:e.jsx(Ae,{entityType:"supplier",entityId:t.id,selectedTags:N,onTagsChange:M})})})]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Address Information"}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Street Address"}),e.jsx("p",{className:"mt-1 text-base",children:h(t.address)})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"City"}),e.jsx("p",{className:"mt-1 text-base",children:h(t.city)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"State/Province"}),e.jsx("p",{className:"mt-1 text-base",children:h(t.state)})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Postal Code"}),e.jsx("p",{className:"mt-1 text-base",children:h(t.postal_code)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Country"}),e.jsx("p",{className:"mt-1 text-base",children:h(t.country)})]})]})]})]})]}),d==="products"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Supplier Products"}),C>0&&e.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:["This supplier has ",C," product",C!==1?"s":""]})]}),e.jsx(Oe,{supplierId:l||"",onSuccess:()=>H(w=>w+1),buttonSize:"sm",buttonText:"Add Product"})]}),e.jsx(Fe,{supplierId:l||"",onProductCountChange:c,refreshTrigger:o})]}),e.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[e.jsx("h2",{className:"text-sm font-medium text-gray-500 mb-2",children:"System Information"}),e.jsxs("div",{className:"flex flex-wrap gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Supplier ID"}),e.jsx("p",{className:"text-sm",children:t==null?void 0:t.id})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Created"}),e.jsx("p",{className:"text-sm",children:t!=null&&t.created_at?new Date(t.created_at).toLocaleString():"-"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Last Updated"}),e.jsx("p",{className:"text-sm",children:t!=null&&t.updated_at?new Date(t.updated_at).toLocaleString():"-"})]})]})]})]}):e.jsx("div",{className:"p-8 text-center",children:e.jsx("p",{className:"text-gray-500",children:"Supplier not found"})})]})})};export{Ye as default};
