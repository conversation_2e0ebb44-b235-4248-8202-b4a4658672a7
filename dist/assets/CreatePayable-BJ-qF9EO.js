import{d as N,h as w,b as C,r as h,j as e,B as x,o as A,A as S,a6 as r,P as s,a7 as P,ad as T,ay as F}from"./index-C6AV3cVN.js";import{C as D}from"./Card-yj7fueH8.js";import{u as I}from"./currencyFormatter-BsFWv3sX.js";import{P as O,b as q,v as E}from"./payables-q7zOb02j.js";import{P as L}from"./PageTitle-FHPo8gWi.js";import"./formatters-Cypx7G-j.js";const M=()=>{const d=N(),{currentOrganization:c}=w(),{user:g}=C(),v=I(),[m,p]=h.useState(!1),[_,o]=h.useState(null),[t,j]=h.useState({source_type:O.MANUAL_ENTRY,source_id:"manual-"+Date.now(),reference_number:"",invoice_date:new Date().toISOString().split("T")[0],due_date:new Date(Date.now()+30*24*60*60*1e3).toISOString().split("T")[0],amount:0,vat_amount:0,withholding_tax_rate:0,withholding_tax_amount:0,currency:"PHP",category:"",notes:""}),n=(a,u)=>{j(l=>{const i={...l,[a]:u};if(a==="amount"||a==="vat_amount"||a==="withholding_tax_rate"){const{calculatedAmount:y}=E(Number(i.amount)||0,Number(i.vat_amount)||0,Number(i.withholding_tax_rate)||0);i.withholding_tax_amount=y}return i})},b=()=>t.reference_number.trim()?t.invoice_date?t.due_date?!t.amount||t.amount<=0?"Amount must be greater than zero":t.vat_amount<0?"VAT amount cannot be negative":t.withholding_tax_rate<0||t.withholding_tax_rate>100?"Withholding tax rate must be between 0 and 100":null:"Due date is required":"Invoice date is required":"Reference number is required",f=async a=>{if(a.preventDefault(),!c||!g)return;const u=b();if(u){o(u);return}p(!0),o(null);try{const{success:l,error:i}=await q(c.id,t,g.id);if(i){o(i);return}l&&d("/payables")}catch(l){o(l.message||"Failed to create payable")}finally{p(!1)}};return c?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsxs(x,{color:"light",onClick:()=>d("/payables"),children:[e.jsx(A,{className:"mr-2 h-5 w-5"}),"Back"]}),e.jsx(L,{title:"Create Manual Payable",subtitle:"Add a manual payable entry"})]}),e.jsxs(D,{children:[e.jsx("h5",{className:"text-lg font-bold mb-4",children:"Payable Information"}),_&&e.jsx(S,{color:"failure",className:"mb-4",children:_}),e.jsxs("form",{onSubmit:f,className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"reference_number",value:"Reference Number *"}),e.jsx(s,{id:"reference_number",type:"text",placeholder:"INV-001, BILL-001, etc.",value:t.reference_number,onChange:a=>n("reference_number",a.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"category",value:"Category"}),e.jsxs(P,{id:"category",value:t.category,onChange:a=>n("category",a.target.value),children:[e.jsx("option",{value:"",children:"Select category"}),e.jsx("option",{value:"office_supplies",children:"Office Supplies"}),e.jsx("option",{value:"utilities",children:"Utilities"}),e.jsx("option",{value:"rent",children:"Rent"}),e.jsx("option",{value:"maintenance",children:"Maintenance"}),e.jsx("option",{value:"professional_services",children:"Professional Services"}),e.jsx("option",{value:"other",children:"Other"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"invoice_date",value:"Invoice Date *"}),e.jsx(s,{id:"invoice_date",type:"date",value:t.invoice_date,onChange:a=>n("invoice_date",a.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"due_date",value:"Due Date *"}),e.jsx(s,{id:"due_date",type:"date",value:t.due_date,onChange:a=>n("due_date",a.target.value),required:!0})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"amount",value:"Total Amount *"}),e.jsx(s,{id:"amount",type:"number",step:"0.01",min:"0.01",value:t.amount,onChange:a=>n("amount",Number(a.target.value)),required:!0})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"vat_amount",value:"VAT Amount"}),e.jsx(s,{id:"vat_amount",type:"number",step:"0.01",min:"0",value:t.vat_amount,onChange:a=>n("vat_amount",Number(a.target.value))})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"withholding_tax_rate",value:"Withholding Tax Rate (%)"}),e.jsx(s,{id:"withholding_tax_rate",type:"number",step:"0.01",min:"0",max:"100",value:t.withholding_tax_rate,onChange:a=>n("withholding_tax_rate",Number(a.target.value))})]})]}),t.withholding_tax_rate>0&&e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx(r,{value:"Calculated Withholding Tax Amount"}),e.jsx("p",{className:"font-medium text-blue-900",children:v(t.withholding_tax_amount)}),e.jsxs("p",{className:"text-sm text-blue-700",children:["Net Amount: ",v((t.amount||0)-(t.vat_amount||0))," × ",t.withholding_tax_rate,"%"]})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"notes",value:"Notes"}),e.jsx(T,{id:"notes",rows:3,placeholder:"Additional notes about this payable",value:t.notes,onChange:a=>n("notes",a.target.value)})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx(x,{color:"light",onClick:()=>d("/payables"),disabled:m,children:"Cancel"}),e.jsxs(x,{type:"submit",color:"primary",disabled:m,children:[e.jsx(F,{className:"mr-2 h-5 w-5"}),m?"Creating...":"Create Payable"]})]})]})]})]}):e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-500",children:"Loading organization..."})})})};export{M as default};
