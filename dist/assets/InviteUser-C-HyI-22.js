import{b as oe,h as ce,r as u,j as e,A as H,a6 as $,P as W,aX as de,a7 as ue,B as b,i as R,_ as h,e as me,t as he,a3 as fe,M as w,bZ as xe,J as ve,b_ as ge,b$ as je,c0 as pe,c1 as ye}from"./index-C6AV3cVN.js";import{C as J}from"./Card-yj7fueH8.js";import{P as be}from"./Pagination-CVEzfctr.js";import{g as Ie,l as Ne,a as Z}from"./index-DT2YvziZ.js";import{r as I,t as x}from"./index-Cn2wB4rc.js";import{e as Me}from"./index-BJwYa9ck.js";import{d as we,g as Se}from"./index-KY8jayTk.js";import"./typeof-QjJsDpFa.js";function P(t,a){I(2,arguments);var s=x(t),o=x(a),i=s.getTime()-o.getTime();return i<0?-1:i>0?1:i}function Ce(t,a){I(2,arguments);var s=x(t),o=x(a),i=s.getFullYear()-o.getFullYear(),v=s.getMonth()-o.getMonth();return i*12+v}function De(t){I(1,arguments);var a=x(t),s=a.getMonth();return a.setFullYear(a.getFullYear(),s+1,0),a.setHours(23,59,59,999),a}function Ee(t){I(1,arguments);var a=x(t);return Me(a).getTime()===De(a).getTime()}function Te(t,a){I(2,arguments);var s=x(t),o=x(a),i=P(s,o),v=Math.abs(Ce(s,o)),r;if(v<1)r=0;else{s.getMonth()===1&&s.getDate()>27&&s.setDate(30),s.setMonth(s.getMonth()-i*v);var g=P(s,o)===-i;Ee(x(t))&&v===1&&P(t,o)===1&&(g=!1),r=i*(v-Number(g))}return r===0?0:r}function ke(t,a,s){I(2,arguments);var o=we(t,a)/1e3;return Se()(o)}function Q(t,a){if(t==null)throw new TypeError("assign requires that input parameter not be null or undefined");for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(t[s]=a[s]);return t}function Oe(t){return Q({},t)}var G=1440,_e=2520,z=43200,He=86400;function Pe(t,a,s){var o,i;I(2,arguments);var v=Ie(),r=(o=(i=s==null?void 0:s.locale)!==null&&i!==void 0?i:v.locale)!==null&&o!==void 0?o:Ne;if(!r.formatDistance)throw new RangeError("locale must contain formatDistance property");var g=P(t,a);if(isNaN(g))throw new RangeError("Invalid time value");var l=Q(Oe(s),{addSuffix:!!(s!=null&&s.addSuffix),comparison:g}),c,j;g>0?(c=x(a),j=x(t)):(c=x(t),j=x(a));var f=ke(j,c),N=(Z(j)-Z(c))/1e3,d=Math.round((f-N)/60),p;if(d<2)return s!=null&&s.includeSeconds?f<5?r.formatDistance("lessThanXSeconds",5,l):f<10?r.formatDistance("lessThanXSeconds",10,l):f<20?r.formatDistance("lessThanXSeconds",20,l):f<40?r.formatDistance("halfAMinute",0,l):f<60?r.formatDistance("lessThanXMinutes",1,l):r.formatDistance("xMinutes",1,l):d===0?r.formatDistance("lessThanXMinutes",1,l):r.formatDistance("xMinutes",d,l);if(d<45)return r.formatDistance("xMinutes",d,l);if(d<90)return r.formatDistance("aboutXHours",1,l);if(d<G){var E=Math.round(d/60);return r.formatDistance("aboutXHours",E,l)}else{if(d<_e)return r.formatDistance("xDays",1,l);if(d<z){var C=Math.round(d/G);return r.formatDistance("xDays",C,l)}else if(d<He)return p=Math.round(d/z),r.formatDistance("aboutXMonths",p,l)}if(p=Te(j,c),p<12){var S=Math.round(d/z);return r.formatDistance("xMonths",S,l)}else{var T=p%12,y=Math.floor(p/12);return T<3?r.formatDistance("aboutXYears",y,l):T<9?r.formatDistance("overXYears",y,l):r.formatDistance("almostXYears",y+1,l)}}function K(t,a){return I(1,arguments),Pe(t,Date.now(),a)}const Be=()=>{const{user:t}=oe(),{currentOrganization:a}=ce(),[s,o]=u.useState(""),[i,v]=u.useState("member"),[r,g]=u.useState(!1),[l,c]=u.useState(null),[j,f]=u.useState(null),[N,d]=u.useState([]),[p,E]=u.useState(!1),[C,S]=u.useState(null),[T,y]=u.useState(!1),[V,k]=u.useState(!1),[F,X]=u.useState(null),[A,O]=u.useState(!1),[U,Y]=u.useState(1),[_,ee]=u.useState(10),L=async()=>{if(a){E(!0);try{const{invitations:n,error:m}=await ge(a.id);m?console.error("Error fetching invitations:",m):d(n)}catch(n){console.error("Error fetching invitations:",n)}finally{E(!1)}}};u.useEffect(()=>{L()},[a]);const se=async n=>{if(n.preventDefault(),!a||!t){c("You must be logged in and have an organization selected");return}if(!s){c("Email is required");return}g(!0),c(null),f(null),S(null);try{const{success:m,invitationUrl:D,error:M}=await je(a.id,s,i,t.id);m?(M?c(M):f(`Invitation sent to ${s}`),D&&(S(D),y(!0)),o(""),L()):c(M||"Failed to invite user")}catch(m){console.error("Error inviting user:",m),c(m.message||"Failed to invite user")}finally{g(!1)}},ae=async()=>{if(F){O(!0);try{const{success:n,error:m}=await ye(F);n?(f("Invitation deleted successfully"),L()):c(m||"Failed to delete invitation")}catch(n){c(n.message||"Failed to delete invitation")}finally{O(!1),k(!1),X(null)}}},te=async n=>{O(!0),c(null),f(null),S(null);try{const{success:m,invitationUrl:D,error:M}=await pe(n);m?(M?c(M):f("Invitation resent successfully"),D&&(S(D),y(!0))):c(M||"Failed to resend invitation")}catch(m){console.error("Error resending invitation:",m),c(m.message||"Failed to resend invitation")}finally{O(!1)}},B=()=>{C&&(navigator.clipboard.writeText(C),f("Invitation link copied to clipboard"))},ne=n=>{switch(n){case"owner":return"purple";case"admin":return"red";case"cashier":return"green";case"inventory_manager":return"blue";case"purchaser":return"yellow";case"employee":return"gray";default:return"indigo"}},re=Math.ceil(N.length/_),q=U*_,ie=q-_,le=N.slice(ie,q);return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(J,{children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Invite User"}),l&&e.jsx(H,{color:"failure",className:"mb-4",children:l}),j&&e.jsx(H,{color:"success",className:"mb-4",children:j}),e.jsxs("form",{onSubmit:se,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx($,{htmlFor:"email",value:"Email Address"})}),e.jsx(W,{id:"email",type:"email",icon:de,placeholder:"<EMAIL>",value:s,onChange:n=>o(n.target.value),required:!0})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"mb-2 block",children:e.jsx($,{htmlFor:"role",value:"Role"})}),e.jsxs(ue,{id:"role",value:i,onChange:n=>v(n.target.value),required:!0,children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"member",children:"Member"}),e.jsx("option",{value:"cashier",children:"Cashier"}),e.jsx("option",{value:"inventory_manager",children:"Inventory Manager"}),e.jsx("option",{value:"purchaser",children:"Purchaser"}),e.jsx("option",{value:"employee",children:"Employee"})]}),e.jsxs("p",{className:"mt-1 text-sm text-gray-500",children:[i==="admin"&&"Admins can manage users, products, inventory, and settings.",i==="member"&&"Members have basic access to view products and inventory.",i==="cashier"&&"Cashiers can process sales and manage customers.",i==="inventory_manager"&&"Inventory managers can manage products and inventory.",i==="purchaser"&&"Purchasers can create purchase orders and manage suppliers.",i==="employee"&&"Employees have limited access to view products and inventory."]})]}),e.jsxs(b,{type:"submit",disabled:r,children:[r?e.jsx(R,{size:"sm",className:"mr-2"}):null,"Send Invitation"]})]})]}),e.jsxs(J,{className:"mt-8",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Pending Invitations"}),p?e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(R,{size:"xl"})}):N.length===0?e.jsx("p",{className:"text-gray-500",children:"No pending invitations"}):e.jsxs(h,{children:[e.jsxs(h.Head,{children:[e.jsx(h.HeadCell,{children:"Email"}),e.jsx(h.HeadCell,{children:"Role"}),e.jsx(h.HeadCell,{children:"Sent"}),e.jsx(h.HeadCell,{children:"Expires"}),e.jsx(h.HeadCell,{children:"Actions"})]}),e.jsx(h.Body,{className:"divide-y",children:le.map(n=>e.jsxs(h.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(h.Cell,{className:"whitespace-nowrap font-medium text-gray-900 dark:text-white",children:n.email}),e.jsx(h.Cell,{children:e.jsx(me,{color:ne(n.role),children:n.role})}),e.jsx(h.Cell,{children:K(new Date(n.created_at),{addSuffix:!0})}),e.jsx(h.Cell,{children:K(new Date(n.expires_at),{addSuffix:!0})}),e.jsx(h.Cell,{children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(b,{color:"light",size:"xs",onClick:()=>te(n.id),disabled:A,children:e.jsx(he,{className:"h-4 w-4"})}),e.jsx(b,{color:"failure",size:"xs",onClick:()=>{X(n.id),k(!0)},disabled:A,children:e.jsx(fe,{className:"h-4 w-4"})})]})})]},n.id))})]}),N.length>0&&e.jsx(be,{currentPage:U,totalPages:re,itemsPerPage:_,totalItems:N.length,onPageChange:Y,onItemsPerPageChange:n=>{ee(n),Y(1)},itemName:"invitations"})]}),e.jsxs(w,{show:T,onClose:()=>y(!1),children:[e.jsx(w.Header,{children:"Invitation Link"}),e.jsx(w.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs(H,{color:"warning",children:[e.jsx("p",{className:"font-medium",children:"Email sending is not configured"}),e.jsx("p",{className:"text-sm mt-1",children:"Please share this invitation link with the user manually:"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(W,{id:"invitationLink",type:"text",value:C||"",readOnly:!0,className:"flex-1"}),e.jsx(b,{color:"light",onClick:B,children:e.jsx(xe,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Instructions for the invited user:"}),e.jsxs("ol",{className:"list-decimal list-inside text-sm space-y-1",children:[e.jsx("li",{children:"Click the link above or copy and paste it into your browser"}),e.jsx("li",{children:"Create an account or sign in if you already have one"}),e.jsx("li",{children:"You'll be automatically added to the organization"})]})]}),e.jsx(H,{color:"info",children:e.jsx("p",{className:"text-sm",children:"This link will expire in 48 hours. The recipient will need to create an account or sign in to accept the invitation."})})]})}),e.jsxs(w.Footer,{children:[e.jsx(b,{color:"primary",onClick:B,children:"Copy Link"}),e.jsx(b,{color:"gray",onClick:()=>y(!1),children:"Close"})]})]}),e.jsxs(w,{show:V,size:"md",popup:!0,onClose:()=>k(!1),children:[e.jsx(w.Header,{}),e.jsx(w.Body,{children:e.jsxs("div",{className:"text-center",children:[e.jsx(ve,{className:"mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200"}),e.jsx("h3",{className:"mb-5 text-lg font-normal text-gray-500 dark:text-gray-400",children:"Are you sure you want to delete this invitation?"}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsxs(b,{color:"failure",onClick:ae,children:[A?e.jsx(R,{size:"sm",className:"mr-2"}):null,"Yes, I'm sure"]}),e.jsx(b,{color:"gray",onClick:()=>k(!1),children:"No, cancel"})]})]})})]})]})};export{Be as default};
