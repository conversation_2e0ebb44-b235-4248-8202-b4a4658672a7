import{d as U,h as z,r as i,j as e,i as S,B as d,t as G,Q as $,P as Q,a7 as R,Z as V,a0 as q,A as W,_ as l,a1 as Z,aS as J,e as K}from"./index-C6AV3cVN.js";import{C as m}from"./Card-yj7fueH8.js";import{u as X}from"./currencyFormatter-BsFWv3sX.js";import{c as ee}from"./formatters-Cypx7G-j.js";import{d as t,P as a,e as se,f as le}from"./payables-q7zOb02j.js";import{P as te}from"./Pagination-CVEzfctr.js";import{h as ae}from"./excelExport-BekG2cQR.js";import{P as re}from"./PageTitle-FHPo8gWi.js";const he=()=>{const N=U(),{currentOrganization:x}=z(),o=X(),[h,O]=i.useState([]),[c,w]=i.useState(null),[I,b]=i.useState(!0),[E,j]=i.useState(null),[P,y]=i.useState(1),[u,D]=i.useState(10),[v,H]=i.useState(0),[p,A]=i.useState({}),[C,L]=i.useState(""),T=async()=>{if(x){b(!0),j(null);try{const{payables:s,total_count:r,error:n}=await se(x.id,{limit:u,offset:(P-1)*u,sortBy:"created_at",sortOrder:"desc",filters:{...p,search_query:C||void 0}});if(n){j(n);return}O(s||[]),H(r||0);const{summary:f,error:g}=await le(x.id);!g&&f&&w(f)}catch(s){j(s.message||"Failed to fetch payables")}finally{b(!1)}}};i.useEffect(()=>{T()},[x,P,u,p,C]);const M=s=>{const n={[t.DRAFT]:{color:"gray",label:"Draft"},[t.OPEN]:{color:"blue",label:"Open"},[t.PARTIALLY_PAID]:{color:"yellow",label:"Partially Paid"},[t.PAID]:{color:"green",label:"Paid"},[t.CANCELLED]:{color:"red",label:"Cancelled"}}[s];return e.jsx(K,{color:n.color,children:n.label})},B=s=>({[a.PURCHASE_RECEIPT]:"Purchase Receipt",[a.PAYROLL]:"Payroll",[a.UTILITY_BILL]:"Utility Bill",[a.GOVERNMENT_REMITTANCE]:"Government Remittance",[a.LOAN_REPAYMENT]:"Loan Repayment",[a.MANUAL_ENTRY]:"Manual Entry"})[s]||s,Y=s=>{const r=new Date,n=new Date(s),f=r.getTime()-n.getTime(),g=Math.ceil(f/(1e3*60*60*24));return g>0?g:0},_=(s,r)=>{A(n=>({...n,[s]:r||void 0})),y(1)},k=()=>{A({}),L(""),y(1)},F=()=>{if(h.length===0){j("No payables to export");return}ae(h)};return x?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(re,{title:"Accounts Payable",subtitle:"Manage supplier invoices, payroll obligations, and other payables"}),c&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[e.jsx(m,{className:"border-l-4 border-blue-500",children:e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Total Payables"}),e.jsx("p",{className:"text-2xl font-bold",children:c.total_payables}),e.jsx("p",{className:"text-sm text-gray-600",children:o(c.total_amount)})]})})}),e.jsx(m,{className:"border-l-4 border-green-500",children:e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Total Paid"}),e.jsx("p",{className:"text-2xl font-bold",children:o(c.total_paid)})]})})}),e.jsx(m,{className:"border-l-4 border-yellow-500",children:e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Outstanding"}),e.jsx("p",{className:"text-2xl font-bold",children:o(c.total_outstanding)})]})})}),e.jsx(m,{className:"border-l-4 border-red-500",children:e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm text-gray-500",children:"Overdue"}),e.jsx("p",{className:"text-2xl font-bold",children:c.overdue_count}),e.jsx("p",{className:"text-sm text-gray-600",children:o(c.overdue_amount)})]})})})]}),e.jsxs(m,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h5",{className:"text-xl font-bold",children:"Payables List"}),e.jsx(d,{color:"light",size:"xs",pill:!0,onClick:T,children:e.jsx(G,{})})]}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx($,{className:"text-gray-500"})}),e.jsx(Q,{type:"text",placeholder:"Search payables...",value:C,onChange:s=>L(s.target.value),className:"pl-10"})]}),e.jsxs(R,{value:p.status||"",onChange:s=>_("status",s.target.value),children:[e.jsx("option",{value:"",children:"All Status"}),e.jsx("option",{value:t.DRAFT,children:"Draft"}),e.jsx("option",{value:t.OPEN,children:"Open"}),e.jsx("option",{value:t.PARTIALLY_PAID,children:"Partially Paid"}),e.jsx("option",{value:t.PAID,children:"Paid"}),e.jsx("option",{value:t.CANCELLED,children:"Cancelled"})]}),e.jsxs(R,{value:p.source_type||"",onChange:s=>_("source_type",s.target.value),children:[e.jsx("option",{value:"",children:"All Sources"}),e.jsx("option",{value:a.PURCHASE_RECEIPT,children:"Purchase Receipt"}),e.jsx("option",{value:a.PAYROLL,children:"Payroll"}),e.jsx("option",{value:a.UTILITY_BILL,children:"Utility Bill"}),e.jsx("option",{value:a.GOVERNMENT_REMITTANCE,children:"Government Remittance"}),e.jsx("option",{value:a.LOAN_REPAYMENT,children:"Loan Repayment"}),e.jsx("option",{value:a.MANUAL_ENTRY,children:"Manual Entry"})]}),e.jsx(d,{color:"light",onClick:k,children:"Clear Filters"}),e.jsxs(d,{color:"light",onClick:F,children:[e.jsx(V,{className:"mr-2 h-4 w-4"}),"Export"]}),e.jsxs(d,{color:"primary",onClick:()=>N("/payables/create"),children:[e.jsx(q,{className:"mr-2 h-5 w-5"}),"Add Payable"]})]})]}),E&&e.jsx(W,{color:"failure",className:"mb-4",children:E}),I?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(S,{size:"xl"})}):h.length===0?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-500",children:"No payables found"})}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(l,{hoverable:!0,children:[e.jsxs(l.Head,{children:[e.jsx(l.HeadCell,{children:"Reference"}),e.jsx(l.HeadCell,{children:"Supplier/Employee"}),e.jsx(l.HeadCell,{children:"Source"}),e.jsx(l.HeadCell,{children:"Amount"}),e.jsx(l.HeadCell,{children:"Balance"}),e.jsx(l.HeadCell,{children:"Due Date"}),e.jsx(l.HeadCell,{children:"Status"}),e.jsx(l.HeadCell,{children:"Actions"})]}),e.jsx(l.Body,{className:"divide-y",children:h.map(s=>{const r=Y(s.due_date);return e.jsxs(l.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(l.Cell,{className:"font-medium",children:s.reference_number}),e.jsx(l.Cell,{children:s.supplier?s.supplier.name:s.employee?`${s.employee.first_name} ${s.employee.last_name}`:"N/A"}),e.jsx(l.Cell,{children:B(s.source_type)}),e.jsx(l.Cell,{children:o(s.amount)}),e.jsx(l.Cell,{children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{children:o(s.balance)}),r>0&&e.jsxs("span",{className:"text-xs text-red-600",children:[r," days overdue"]})]})}),e.jsx(l.Cell,{children:ee(s.due_date)}),e.jsx(l.Cell,{children:M(s.status)}),e.jsx(l.Cell,{children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(d,{color:"light",size:"xs",onClick:()=>N(`/payables/${s.id}`),children:e.jsx(Z,{className:"h-4 w-4"})}),s.status!==t.PAID&&s.status!==t.CANCELLED&&e.jsx(d,{color:"primary",size:"xs",onClick:()=>N(`/payables/${s.id}/pay`),children:e.jsx(J,{className:"h-4 w-4"})})]})})]},s.id)})})]})}),v>0&&e.jsx(te,{currentPage:P,totalPages:Math.ceil(v/u),itemsPerPage:u,totalItems:v,onPageChange:y,onItemsPerPageChange:s=>{D(s),y(1)},itemName:"payables"})]})]}):e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(S,{size:"xl"})})};export{he as default};
