import{h as B,b as M,r as l,j as e,B as i,aL as H,M as m,J as S,A as g,aM as Q,ab as W,i as L,L as D,o as A,t as J,e as E,$ as F,_ as t,aj as K}from"./index-C6AV3cVN.js";import{C as T}from"./Card-yj7fueH8.js";import{a as z}from"./inventoryReceipt-CBnPt-rA.js";import{d as G}from"./inventoryTransaction-1UXV5RDN.js";import{c as X,a as Y}from"./payables-q7zOb02j.js";import{u as U}from"./currencyFormatter-BsFWv3sX.js";import"./floatInventory-k_pEQeIK.js";import"./formatters-Cypx7G-j.js";const Z=({receiptId:o,receiptNumber:n,receiptStatus:p,onSuccess:N,className:s=""})=>{const{currentOrganization:y}=B(),{user:b}=M(),w=U(),[x,u]=l.useState(!1),[P,v]=l.useState(!1),[c,d]=l.useState(null),k=p==="completed"&&y&&b,_=async()=>{if(!(!y||!b)){u(!0),d(null);try{const{success:j,payable:f,error:R}=await X(y.id,o,b.id);R?d({success:!1,message:R}):j&&f&&(d({success:!0,message:`Payable created successfully with reference: ${f.reference_number}`,payable:f}),N&&N())}catch(j){d({success:!1,message:j.message||"Failed to create payable"})}finally{u(!1)}}},C=()=>{v(!1),d(null)};return e.jsxs(e.Fragment,{children:[e.jsxs(i,{color:k?"primary":"gray",size:"sm",disabled:!k||x,onClick:()=>v(!0),className:s,children:[e.jsx(H,{className:"mr-2 h-4 w-4"}),x?"Sending...":"Send to Payable"]}),e.jsxs(m,{show:P,onClose:C,size:"md",children:[e.jsx(m.Header,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(H,{className:"h-5 w-5 text-blue-600"}),"Send Receipt to Payable"]})}),e.jsx(m.Body,{children:c?e.jsx("div",{className:"space-y-4",children:c.success?e.jsxs("div",{className:"flex items-start gap-3 p-4 bg-green-50 rounded-lg border border-green-200",children:[e.jsx(Q,{className:"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-green-800",children:"Success!"}),e.jsx("p",{className:"text-sm text-green-700 mt-1",children:c.message}),c.payable&&e.jsxs("div",{className:"mt-3 text-sm text-green-700",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Amount:"})," ",w(c.payable.amount)]}),e.jsxs("p",{children:[e.jsx("strong",{children:"VAT:"})," ",w(c.payable.vat_amount)]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Due Date:"})," ",new Date(c.payable.due_date).toLocaleDateString()]})]})]})]}):e.jsx(g,{color:"failure",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(S,{className:"h-5 w-5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Error Creating Payable"}),e.jsx("p",{className:"text-sm mt-1",children:c.message})]})]})})}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start gap-3 p-4 bg-yellow-50 rounded-lg border border-yellow-200",children:[e.jsx(S,{className:"h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-yellow-800",children:"Confirm Action"}),e.jsxs("p",{className:"text-sm text-yellow-700 mt-1",children:["This will manually create a payable from receipt ",e.jsx("strong",{children:n}),"."]}),e.jsx("p",{className:"text-sm text-yellow-700 mt-2",children:"The payable will be created with:"}),e.jsxs("ul",{className:"text-sm text-yellow-700 mt-1 ml-4 list-disc",children:[e.jsx("li",{children:"Amount calculated from receipt items"}),e.jsx("li",{children:"VAT based on business settings"}),e.jsx("li",{children:"Due date based on supplier payment terms"}),e.jsxs("li",{children:["Reference number: INV-",n]})]})]})]}),p!=="completed"&&e.jsx(g,{color:"warning",children:"Receipt must be completed before creating a payable."})]})}),e.jsx(m.Footer,{children:e.jsx("div",{className:"flex justify-end gap-2 w-full",children:c?e.jsx(i,{color:c.success?"success":"light",onClick:C,children:c.success?"Sent to Enhanced Payables!":"Close"}):e.jsxs(e.Fragment,{children:[e.jsx(i,{color:"light",onClick:C,disabled:x,children:"Cancel"}),e.jsxs(i,{color:"primary",onClick:_,disabled:x||p!=="completed",children:[e.jsx(H,{className:"mr-2 h-4 w-4"}),x?"Sending to Enhanced Payables...":"Send to Enhanced Payables"]})]})})})]})]})},de=()=>{const{id:o}=W(),{currentOrganization:n}=B(),{user:p}=M(),N=U(),[s,y]=l.useState(null),[b,w]=l.useState(!0),[x,u]=l.useState(null),[P,v]=l.useState(!1),[c,d]=l.useState(null),[k,_]=l.useState(!1),[C,j]=l.useState(!1),[f,R]=l.useState(!1),[ee,O]=l.useState(!1);l.useEffect(()=>{(async()=>{if(!(!n||!o)){w(!0),u(null);try{const{receipt:r,error:h}=await z(n.id,o);h?u(h):r?(y(r),r.status==="completed"&&q(r.id)):u("Receipt not found")}catch(r){u(r.message||"An error occurred while fetching the receipt")}finally{w(!1)}}})()},[n,o]);const q=async a=>{if(n){O(!0);try{const{hasPendingPayable:r}=await Y(n.id,a);R(r)}catch(r){console.error("Error checking payable status:",r)}finally{O(!1)}}},V=async()=>{if(!(!n||!o||!p)){v(!0),d(null),_(!1);try{const{success:a,error:r}=await G(n.id,o,p.id);if(r)d(r);else{_(!0);const{receipt:h}=await z(n.id,o);h&&y(h)}}catch(a){d(a.message||"An error occurred while creating transactions")}finally{v(!1)}}},$=a=>new Date(a).toLocaleDateString();return n?b?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(T,{children:e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx(L,{size:"xl"})})})}):x||!s?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(T,{children:e.jsxs(g,{color:"failure",icon:S,children:[e.jsx("h3",{className:"text-lg font-medium",children:"Error"}),e.jsx("p",{children:x||"Receipt not found"}),e.jsx("div",{className:"mt-4",children:e.jsx(D,{to:"/inventory/receipts",children:e.jsxs(i,{color:"gray",size:"sm",children:[e.jsx(A,{className:"mr-2 h-4 w-4"}),"Back to Receipts"]})})})]})})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold",children:["Receipt #",s.receipt_number]}),e.jsx("p",{className:"text-gray-500",children:s.purchase_order?`From PO #${s.purchase_order.order_number}`:"Manual Receipt"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(D,{to:"/inventory/receipts",children:e.jsxs(i,{color:"gray",children:[e.jsx(A,{className:"mr-2 h-5 w-5"}),"Back"]})}),s.status==="completed"&&e.jsxs(e.Fragment,{children:[e.jsxs(i,{color:"primary",onClick:()=>j(!0),children:[e.jsx(J,{className:"mr-2 h-5 w-5"}),"Create Transactions"]}),!f&&e.jsx(Z,{receiptId:s.id,receiptNumber:s.receipt_number,receiptStatus:s.status}),f&&e.jsxs(E,{color:"success",className:"flex items-center",children:[e.jsx(F,{className:"mr-1 h-4 w-4"}),"Sent to Payables"]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Receipt Information"}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Receipt Number"}),e.jsx("p",{className:"font-medium",children:s.receipt_number})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Date"}),e.jsx("p",{children:$(s.receipt_date)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Status"}),e.jsx(E,{color:s.status==="completed"?"success":"warning",children:s.status==="completed"?"Completed":"Draft"})]})]})]}),s.purchase_order&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Purchase Order"}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"PO Number"}),e.jsx("p",{className:"font-medium",children:s.purchase_order.order_number})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Supplier"}),e.jsx("p",{children:s.purchase_order.supplier_name||"Unknown Supplier"})]}),e.jsx("div",{children:e.jsx(D,{to:`/purchases/orders/${s.purchase_order.id}`,children:e.jsx(i,{size:"xs",color:"gray",children:"View Purchase Order"})})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Notes"}),e.jsx("div",{className:"bg-gray-50 p-4 rounded-lg min-h-[100px]",children:s.notes?e.jsx("p",{className:"whitespace-pre-line",children:s.notes}):e.jsx("p",{className:"text-gray-400 italic",children:"No notes"})})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Items"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(t,{children:[e.jsxs(t.Head,{children:[e.jsx(t.HeadCell,{children:"Product"}),e.jsx(t.HeadCell,{children:"Quantity"}),e.jsx(t.HeadCell,{children:"Unit"}),e.jsx(t.HeadCell,{children:"Unit Cost"}),e.jsx(t.HeadCell,{children:"Total"}),e.jsx(t.HeadCell,{children:"QC Status"})]}),e.jsx(t.Body,{className:"divide-y",children:s.items.map(a=>{var r,h,I;return e.jsxs(t.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800",children:[e.jsx(t.Cell,{children:e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:((r=a.product)==null?void 0:r.name)||"Unknown Product"}),((h=a.product)==null?void 0:h.sku)&&e.jsxs("div",{className:"text-xs text-gray-500",children:["SKU: ",a.product.sku]})]})}),e.jsx(t.Cell,{children:a.quantity}),e.jsx(t.Cell,{children:((I=a.uom)==null?void 0:I.name)||"Unknown"}),e.jsx(t.Cell,{children:N(a.unit_cost)}),e.jsx(t.Cell,{children:N(a.quantity*a.unit_cost)}),e.jsx(t.Cell,{children:e.jsx(E,{color:a.qc_status==="passed"?"success":a.qc_status==="failed"?"failure":"warning",children:a.qc_status||"Not Checked"})})]},a.id)})})]})})]}),e.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[e.jsx("h2",{className:"text-sm font-medium text-gray-500 mb-2",children:"System Information"}),e.jsxs("div",{className:"flex flex-wrap gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Receipt ID"}),e.jsx("p",{className:"text-sm",children:s.id})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Created By"}),e.jsx("p",{className:"text-sm",children:s.creator_name||"Unknown"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Created"}),e.jsx("p",{className:"text-sm",children:new Date(s.created_at).toLocaleString()})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Last Updated"}),e.jsx("p",{className:"text-sm",children:new Date(s.updated_at).toLocaleString()})]})]})]})]}),e.jsxs(m,{show:C,onClose:()=>j(!1),children:[e.jsx(m.Header,{children:"Create Inventory Transactions"}),e.jsx(m.Body,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"This will create inventory transactions for all items in this receipt. This is usually done automatically when a receipt is completed, but you can use this function if transactions were not created properly."}),e.jsx("p",{children:"Are you sure you want to create inventory transactions for this receipt?"}),c&&e.jsx(g,{color:"failure",icon:S,children:c}),k&&e.jsx(g,{color:"success",icon:F,children:"Inventory transactions created successfully!"})]})}),e.jsxs(m.Footer,{children:[e.jsx(i,{color:"gray",onClick:()=>j(!1),children:"Cancel"}),e.jsx(i,{color:"primary",onClick:V,disabled:P,children:P?e.jsxs(e.Fragment,{children:[e.jsx(L,{size:"sm",className:"mr-2"}),"Creating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(K,{className:"mr-2 h-5 w-5"}),"Create Transactions"]})})]})]})]}):e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(T,{children:e.jsxs(g,{color:"failure",icon:S,children:[e.jsx("h3",{className:"text-lg font-medium",children:"No Organization Selected"}),e.jsx("p",{children:"Please select an organization to view receipt details."})]})})})};export{de as default};
