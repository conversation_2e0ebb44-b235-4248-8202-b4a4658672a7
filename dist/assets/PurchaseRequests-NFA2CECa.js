import{d as F,h as L,b as Q,r as t,j as e,ao as U,B as d,a0 as $,A as J,J as K,Q as M,P as V,a7 as W,W as Y,t as G,i as X,aj as R,_ as a,e as Z,K as q}from"./index-C6AV3cVN.js";import{C as ee}from"./Card-yj7fueH8.js";import{g as se}from"./purchaseRequest-Bff56uzH.js";import{c as ae,d as te}from"./formatters-Cypx7G-j.js";import{E as re}from"./EmptyState-743bE0hR.js";import{P as le}from"./Pagination-CVEzfctr.js";const he=()=>{const h=F(),{currentOrganization:m}=L(),{user:j}=Q(),[H,p]=t.useState(!0),[f,x]=t.useState(null),[i,O]=t.useState([]),[r,g]=t.useState(""),[l,v]=t.useState("all"),[N,C]=t.useState(!1),[y,b]=t.useState(1),[u,k]=t.useState(10),o=async(s=r,n=l)=>{if(m){p(!0),x(null);try{const c={};s&&(c.searchQuery=s),n!=="all"&&(c.status=n);const{purchaseRequests:B,error:P}=await se(m.id,c);P?x(P):O(B||[])}catch(c){console.error("Error fetching purchase requests:",c),x(c.message||"An error occurred while fetching purchase requests")}finally{p(!1),C(!1)}}};t.useEffect(()=>{o()},[m]);const A=s=>{s.preventDefault(),o(r,l)},E=s=>{const n=s.target.value;v(n),o(r,n)},I=()=>{C(!0),o(r,l)},w=()=>{h("/purchases/requests/create")},D=s=>{switch(s){case"approved":return"success";case"pending":return"warning";case"rejected":return"failure";case"cancelled":return"dark";default:return"gray"}},T=Math.ceil(i.length/u),S=y*u,_=S-u,z=i.slice(_,S);return e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs(ee,{children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold flex items-center",children:[e.jsx(U,{className:"mr-2 h-6 w-6"}),"Purchase Requests"]}),e.jsx("p",{className:"text-gray-500",children:"Create and manage purchase requests for inventory replenishment."})]}),e.jsxs(d,{color:"primary",onClick:w,children:[e.jsx($,{className:"mr-2 h-5 w-5"}),"Create Request"]})]}),f&&e.jsx(J,{color:"failure",icon:K,className:"mb-4",children:f}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-3 mb-4",children:[e.jsx("form",{onSubmit:A,className:"flex-grow",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(M,{className:"w-5 h-5 text-gray-500"})}),e.jsx(V,{type:"search",placeholder:"Search by request number...",value:r,onChange:s=>g(s.target.value),className:"w-full pl-10"}),e.jsx(d,{type:"submit",color:"blue",size:"sm",className:"absolute right-1 top-1 bottom-1",children:"Search"})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"min-w-[150px]",children:e.jsxs(W,{value:l,onChange:E,icon:Y,children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"draft",children:"Draft"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"approved",children:"Approved"}),e.jsx("option",{value:"rejected",children:"Rejected"}),e.jsx("option",{value:"cancelled",children:"Cancelled"})]})}),e.jsx(d,{color:"light",onClick:I,disabled:N,children:e.jsx(G,{className:`h-5 w-5 ${N?"animate-spin":""}`})})]})]}),H?e.jsxs("div",{className:"flex justify-center items-center p-12",children:[e.jsx(X,{size:"xl"}),e.jsx("span",{className:"ml-2",children:"Loading purchase requests..."})]}):i.length===0?e.jsx(re,{icon:e.jsx(R,{className:"h-12 w-12"}),title:"No purchase requests found",description:r||l!=="all"?"Try changing your search terms or filters":"Create your first purchase request to get started",actionText:r||l!=="all"?"Clear filters":"Create Purchase Request",onAction:r||l!=="all"?()=>{g(""),v("all"),o("","all")}:w}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(a,{hoverable:!0,children:[e.jsxs(a.Head,{children:[e.jsx(a.HeadCell,{children:"Request Number"}),e.jsx(a.HeadCell,{children:"Status"}),e.jsx(a.HeadCell,{children:"Requester"}),e.jsx(a.HeadCell,{children:"Date Created"}),e.jsx(a.HeadCell,{children:e.jsx("span",{className:"sr-only",children:"Actions"})})]}),e.jsx(a.Body,{className:"divide-y",children:z.map(s=>e.jsxs(a.Row,{className:"bg-white dark:border-gray-700 dark:bg-gray-800 hover:bg-gray-50",onClick:()=>h(`/purchases/requests/${s.id}`),style:{cursor:"pointer"},children:[e.jsx(a.Cell,{className:"font-medium",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(R,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{children:s.request_number})]})}),e.jsx(a.Cell,{children:e.jsx(Z,{color:D(s.status),children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})}),e.jsx(a.Cell,{children:j&&s.requester_id===j.id?e.jsxs("div",{className:"flex items-center",children:[e.jsx(q,{className:"mr-1 h-4 w-4 text-blue-500"}),e.jsx("span",{className:"text-blue-500 font-medium",children:"You"})]}):e.jsxs("div",{className:"flex items-center",children:[e.jsx(q,{className:"mr-1 h-4 w-4 text-gray-500"}),e.jsx("span",{children:s.requester_name||"Unknown User"})]})}),e.jsx(a.Cell,{children:e.jsx("div",{title:te(s.created_at),children:ae(s.created_at)})}),e.jsx(a.Cell,{children:e.jsx(d,{color:"light",size:"xs",onClick:n=>{n.stopPropagation(),h(`/purchases/requests/${s.id}`)},children:"View"})})]},s.id))})]})}),i.length>0&&e.jsx(le,{currentPage:y,totalPages:T,itemsPerPage:u,totalItems:i.length,onPageChange:b,onItemsPerPageChange:s=>{k(s),b(1)},itemName:"requests"})]})})};export{he as default};
