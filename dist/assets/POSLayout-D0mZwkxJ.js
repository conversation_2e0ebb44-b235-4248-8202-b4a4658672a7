import{d as m,h as x,a as u,j as s,B as e,o as w,p,q as g,t as j,v as f,w as N,O as v}from"./index-C6AV3cVN.js";import{O as k,S as O}from"./OnboardingCheck-Tv64W2rm.js";const C=()=>{const l=m(),{currentMember:a}=x(),{settings:n}=u(),t=(a==null?void 0:a.role)==="owner",i=()=>{l("/")},o=()=>{window.open("/organization/settings","_blank")},c=()=>{window.dispatchEvent(new CustomEvent("pos-show-sales-modal"))},r=()=>{window.dispatchEvent(new CustomEvent("pos-show-manual-modal"))},d=()=>{window.dispatchEvent(new CustomEvent("pos-show-shortcuts-modal"))},h=()=>{window.dispatchEvent(new CustomEvent("pos-show-refund-modal"))};return s.jsxs(s.Fragment,{children:[s.jsx(k,{}),s.jsxs("div",{className:"flex flex-col w-full h-screen bg-gray-50 dark:bg-darkgray",children:[s.jsxs("div",{className:"bg-white dark:bg-darkgray shadow-sm py-2 px-4 flex items-center justify-between flex-shrink-0",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsxs(e,{color:"light",size:"sm",onClick:i,className:"mr-4",children:[s.jsx(w,{className:"mr-2 h-5 w-5"}),"Back to Dashboard"]}),s.jsx("h1",{className:"text-xl font-bold",children:"POS Terminal"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs("div",{className:"flex items-center mr-4",children:[s.jsx("span",{className:"text-sm font-medium mr-2",children:"Business Type:"}),s.jsx("span",{className:"bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full capitalize",children:(n==null?void 0:n.business_type)||"Retail"}),t&&s.jsx(e,{size:"xs",color:"light",pill:!0,className:"ml-2",onClick:o,title:"Change in Organization Settings",children:s.jsx(p,{className:"h-3 w-3"})})]}),s.jsxs(e,{size:"sm",color:"light",onClick:c,title:"View Sales (F8)",className:"flex items-center gap-1",children:[s.jsx(g,{className:"h-4 w-4"}),s.jsx("span",{children:"Sales"})]}),s.jsxs(e,{size:"sm",color:"warning",onClick:h,title:"Process Refund (F5)",className:"flex items-center gap-1",children:[s.jsx(j,{className:"h-4 w-4"}),s.jsx("span",{children:"Refund"})]}),s.jsxs(e,{size:"sm",color:"light",onClick:r,title:"User Manual (F7)",className:"flex items-center gap-1",children:[s.jsx(f,{className:"h-4 w-4"}),s.jsx("span",{children:"Manual"})]}),s.jsxs(e,{size:"sm",color:"light",onClick:d,title:"Keyboard Shortcuts",className:"flex items-center gap-1",children:[s.jsx(N,{className:"h-4 w-4"}),s.jsx("span",{children:"Shortcuts"})]})]})]}),s.jsx("div",{className:"flex-1 w-full min-h-0",children:s.jsx(O,{children:s.jsx(v,{})})})]})]})};export{C as default};
