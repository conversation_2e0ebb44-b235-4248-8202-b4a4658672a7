{"name": "airyx-react-vite-ts", "homepage": "https://themewagon.github.io/Airyx/", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:with-types": "tsc && vite build", "typecheck": "tsc --noEmit", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist", "download-models": "node scripts/download-models.js", "setup-face-recognition": "npm run download-models"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@supabase/supabase-js": "^2.49.4", "@svgr/rollup": "8.1.0", "@tabler/icons-react": "^2.39.0", "@types/react-datepicker": "^7.0.0", "@types/uuid": "^10.0.0", "@vladmandic/face-api": "^1.7.15", "aos": "^2.3.4", "apexcharts": "^4.7.0", "chance": "^1.1.11", "chart.js": "^4.4.9", "date-fns": "^2.30.0", "face-api.js": "^0.22.2", "flowbite": "^2.5.2", "flowbite-react": "^0.10.1", "formik": "^2.4.5", "formik-mui": "^5.0.0-alpha.0", "gray-matter": "^4.0.3", "i18next": "^23.5.1", "lodash": "^4.17.21", "moment": "^2.29.4", "prop-types": "^15.7.2", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-helmet": "^6.1.0", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.5.2", "react-markdown": "^10.1.0", "react-router": "^7.0.2", "react-router-dom": "^7.6.0", "react-select": "^5.10.1", "remark": "^15.0.1", "remark-html": "^16.0.1", "simplebar": "^6.2.7", "simplebar-react": "^3.2.4", "stylis-plugin-rtl": "^2.1.1", "uuid": "^11.1.0", "yup": "^0.32.11"}, "devDependencies": {"@iconify/react": "^5.1.0", "@types/aos": "^3.0.7", "@types/chance": "^1.1.6", "@types/node": "20.10.5", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-helmet": "^6.1.11", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "latest", "eslint-config-next": "latest", "gh-pages": "^6.3.0", "postcss": "^8.4.49", "supabase": "^2.22.12", "tailwindcss": "^3.4.16", "typescript": "5.5.4", "vite": "^5.4.11"}}