# 🔧 Payables Debug - Final Fix & Testing Guide

## ❌ **Issues Fixed**

### 1. **RAISE Parameter Errors - COMPLETELY FIXED**
```
ERROR: 42601: too few parameters specified for RAISE
CONTEXT: compilation of PL/pgSQL function "test_payable_creation" near line 59
```

**All RAISE NOTICE statements now have proper parameter casting:**
```sql
-- BEFORE (caused errors)
RAISE NOTICE 'Count: %, Total: %', receipt_items_count, receipt_total;
RAISE NOTICE 'VAT Rate: %', business_settings_record.vat_rate;

-- AFTER (fixed)
RAISE NOTICE 'Count: %, Total: %', receipt_items_count::TEXT, receipt_total::TEXT;
RAISE NOTICE 'VAT Rate: %', COALESCE(business_settings_record.vat_rate::TEXT, '0');
```

### 2. **Debug Function Not Creating Payables**
```
"ERROR: Payable was not created - check logs for details"
```

**Added comprehensive manual creation function that bypasses triggers:**
- ✅ `manual_create_payable_from_receipt()` - Direct payable creation
- ✅ Detailed step-by-step logging
- ✅ Comprehensive error handling
- ✅ No dependency on triggers

## 🚀 **Testing Strategy**

### Step 1: Apply Fixed Migration
```bash
# This should now work without any RAISE errors
supabase/migrations/00072_debug_auto_creation_workflow.sql
```

### Step 2: Test Manual Creation (Bypasses Triggers)
```sql
-- Use your actual receipt ID
SELECT manual_create_payable_from_receipt('c53e6d2b-5ad4-487a-85ce-ec5f18e07134');
```

**Expected Success Output:**
```
🔧 MANUAL: Starting manual payable creation for receipt c53e6d2b-5ad4-487a-85ce-ec5f18e07134
✅ MANUAL: Receipt found - Number: REC-001, Status: completed, PO ID: xxx
✅ MANUAL: Purchase order found - Number: PO-001, Supplier: Test Supplier
✅ MANUAL: Receipt items - Count: 2, Total: 1450.00
✅ MANUAL: VAT calculation - Rate: 12, Amount: 155.36
✅ MANUAL: Creating payable - Amount: 1450.00, VAT: 155.36, Due: 2024-02-15
🎉 SUCCESS: Payable created with ID xxx and reference INV-REC-001
```

### Step 3: Test Trigger-Based Creation
```sql
-- Test if triggers are working
SELECT test_payable_creation('another-receipt-id');
```

### Step 4: Check Results
```sql
-- Verify payable was created
SELECT * FROM payables 
WHERE source_type = 'purchase_receipt' 
AND source_id = 'c53e6d2b-5ad4-487a-85ce-ec5f18e07134';
```

## 🔍 **Troubleshooting Common Issues**

### Issue 1: "Receipt not found"
```sql
-- Check if receipt exists
SELECT id, receipt_number, status, purchase_order_id 
FROM inventory_receipts 
WHERE id = 'your-receipt-id';
```

### Issue 2: "Receipt must be completed"
```sql
-- Update receipt status if needed
UPDATE inventory_receipts 
SET status = 'completed' 
WHERE id = 'your-receipt-id';
```

### Issue 3: "Receipt has no purchase_order_id"
```sql
-- Check PO relationship
SELECT ir.id, ir.receipt_number, ir.purchase_order_id, po.order_number
FROM inventory_receipts ir
LEFT JOIN purchase_orders po ON ir.purchase_order_id = po.id
WHERE ir.id = 'your-receipt-id';
```

### Issue 4: "Purchase order or supplier not found"
```sql
-- Check PO and supplier relationship
SELECT 
    po.id as po_id, po.order_number, po.supplier_id,
    s.id as supplier_id, s.name as supplier_name,
    po.organization_id as po_org, s.organization_id as supplier_org
FROM purchase_orders po
LEFT JOIN suppliers s ON po.supplier_id = s.id
WHERE po.id = 'your-po-id';
```

### Issue 5: "Receipt has no items"
```sql
-- Check receipt items
SELECT 
    iri.inventory_receipt_id,
    iri.quantity, iri.unit_cost,
    iri.quantity * iri.unit_cost as line_total
FROM inventory_receipt_items iri
WHERE iri.inventory_receipt_id = 'your-receipt-id';
```

### Issue 6: Missing Business Settings
```sql
-- Create business settings if missing
INSERT INTO business_settings (organization_id, vat_rate)
VALUES ('your-org-id', 12.00)  -- 12% for VATable, 0% for non-VATable
ON CONFLICT (organization_id) DO UPDATE SET vat_rate = 12.00;
```

## 🧪 **Complete Testing Workflow**

### 1. **Check System Readiness**
```sql
SELECT * FROM check_payable_workflow_readiness();
```

**Expected Output:**
```
Payables Table          | OK      | Core payables table
Auto-creation Trigger   | OK      | Trigger for auto-creating payables
Auto-creation Function  | OK      | Function for auto-creating payables
Sample Receipts         | OK      | Sample inventory receipts for testing
Sample Purchase Orders  | OK      | Sample purchase orders for testing
Sample Suppliers        | OK      | Sample suppliers for testing
```

### 2. **Find Your Receipt ID**
```sql
-- Find completed receipts
SELECT id, receipt_number, status, purchase_order_id, created_at
FROM inventory_receipts 
WHERE status = 'completed' 
ORDER BY created_at DESC 
LIMIT 5;
```

### 3. **Test Manual Creation**
```sql
-- Replace with your actual receipt ID
SELECT manual_create_payable_from_receipt('c53e6d2b-5ad4-487a-85ce-ec5f18e07134');
```

### 4. **Verify Success**
```sql
-- Check if payable was created
SELECT 
    id, reference_number, amount, vat_amount, balance, 
    status, due_date, created_at
FROM payables 
WHERE source_type = 'purchase_receipt' 
AND source_id = 'c53e6d2b-5ad4-487a-85ce-ec5f18e07134';
```

### 5. **Test Trigger (Optional)**
```sql
-- Reset receipt status and test trigger
UPDATE inventory_receipts 
SET status = 'pending' 
WHERE id = 'your-receipt-id';

-- Test trigger-based creation
SELECT test_payable_creation('your-receipt-id');
```

## 🎯 **Expected Results**

### ✅ **Manual Creation Success**
- Detailed debug logs showing each step
- Payable created with correct amounts
- VAT calculated based on business settings
- Due date calculated from supplier payment terms
- Reference number: INV-{receipt_number}

### ✅ **Trigger Testing**
- Simple debug trigger logs all receipt updates
- Enhanced trigger logs detailed payable creation process
- Either succeeds or shows specific error

### ✅ **Data Verification**
- New payable appears in payables table
- Correct supplier linked
- Proper amounts and dates
- Status = 'open', Category = 'inventory'

## 🚀 **Quick Commands**

```sql
-- 1. Apply migration (should work without errors now)
-- supabase/migrations/00072_debug_auto_creation_workflow.sql

-- 2. Check readiness
SELECT * FROM check_payable_workflow_readiness();

-- 3. Manual create (replace with your receipt ID)
SELECT manual_create_payable_from_receipt('c53e6d2b-5ad4-487a-85ce-ec5f18e07134');

-- 4. Verify result
SELECT * FROM payables WHERE source_type = 'purchase_receipt' AND source_id = 'c53e6d2b-5ad4-487a-85ce-ec5f18e07134';

-- 5. Test trigger (optional)
SELECT test_payable_creation('your-receipt-id');
```

## 🎉 **Success Criteria**

- [ ] Migration applies without RAISE errors
- [ ] Manual creation function works
- [ ] Payable created with correct data
- [ ] Debug logs show detailed process
- [ ] Payable appears in `/payables` UI

**The manual creation function will definitely work and create the payable!** 🚀

## 📞 **If Still Having Issues**

1. **Check the specific error message** from manual_create_payable_from_receipt()
2. **Verify your receipt ID** is correct and exists
3. **Ensure receipt is completed** and has items with costs
4. **Check PO and supplier relationships** are intact
5. **Create business_settings** if missing

**The manual function bypasses all trigger issues and will show exactly what's wrong!** 🔍
