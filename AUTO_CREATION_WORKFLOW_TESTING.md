# 🔧 Auto-Creation Workflow Testing Guide

## 🚨 **Issue**: Payables Not Auto-Creating

You've completed the full workflow:
1. ✅ Create Purchase Request
2. ✅ Convert to Purchase Order  
3. ✅ Create Inventory Receipt from PO
4. ✅ Set receipt status to 'completed'
5. ❌ **No payable auto-created**

## 🔍 **Debugging Steps**

### Step 1: Apply Debug Migration
```bash
# Apply the debug migration first
supabase/migrations/00072_debug_auto_creation_workflow.sql
```

### Step 2: Use Debug Interface
```
Navigate to: /payables/debug
```

This debug page will show:
- ✅ **Workflow readiness checks** (tables, triggers, functions)
- 📋 **Recent inventory receipts** with test buttons
- 🧪 **Manual test interface** for specific receipt IDs

### Step 3: Check Workflow Readiness
The debug page will show if:
- ✅ Payables table exists
- ✅ Auto-creation trigger exists  
- ✅ Auto-creation function exists
- ✅ Sample data exists (receipts, POs, suppliers)

### Step 4: Test Specific Receipt
1. Find your completed receipt in the debug interface
2. Click the "Test" button next to it
3. Check the result message

## 🔧 **Common Issues & Fixes**

### Issue 1: Trigger Not Firing
**Symptoms**: No debug logs, no errors
**Cause**: Trigger doesn't exist or is disabled
**Fix**: Check trigger exists in database

```sql
-- Check if trigger exists
SELECT * FROM information_schema.triggers 
WHERE trigger_name LIKE '%auto_create_payable%';
```

### Issue 2: Function Errors
**Symptoms**: Debug logs show errors
**Cause**: Function logic issues (missing PO, supplier, etc.)
**Fix**: Check debug logs for specific error

### Issue 3: Missing Purchase Order Link
**Symptoms**: "No valid purchase order found"
**Cause**: Receipt not properly linked to PO
**Fix**: Verify receipt.purchase_order_id is correct

```sql
-- Check receipt-PO relationship
SELECT 
    ir.id as receipt_id,
    ir.receipt_number,
    ir.purchase_order_id,
    po.id as po_id,
    po.order_number
FROM inventory_receipts ir
LEFT JOIN purchase_orders po ON ir.purchase_order_id = po.id
WHERE ir.status = 'completed'
ORDER BY ir.created_at DESC;
```

### Issue 4: Missing Supplier Relationship
**Symptoms**: "No valid purchase order found" (supplier check fails)
**Cause**: PO not linked to supplier or supplier in different org
**Fix**: Verify supplier relationship

```sql
-- Check PO-supplier relationship
SELECT 
    po.id as po_id,
    po.order_number,
    po.supplier_id,
    s.id as supplier_id_check,
    s.name as supplier_name,
    po.organization_id as po_org,
    s.organization_id as supplier_org
FROM purchase_orders po
LEFT JOIN suppliers s ON po.supplier_id = s.id
WHERE po.id = 'your-po-id';
```

### Issue 5: Zero Receipt Total
**Symptoms**: "Receipt has zero or negative total"
**Cause**: No receipt items or zero costs
**Fix**: Verify receipt items exist with costs

```sql
-- Check receipt items
SELECT 
    iri.inventory_receipt_id,
    iri.quantity,
    iri.unit_cost,
    iri.quantity * iri.unit_cost as line_total
FROM inventory_receipt_items iri
WHERE iri.inventory_receipt_id = 'your-receipt-id';
```

## 🧪 **Manual Testing Process**

### Test 1: Check Database State
```sql
-- Run these queries to check current state

-- 1. Check if migrations applied
SELECT version FROM supabase_migrations.schema_migrations 
WHERE version LIKE '%payables%' 
ORDER BY version;

-- 2. Check trigger exists
SELECT trigger_name, event_manipulation, event_object_table 
FROM information_schema.triggers 
WHERE trigger_name LIKE '%payable%';

-- 3. Check function exists
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_name LIKE '%payable%';
```

### Test 2: Manual Trigger Test
```sql
-- Use the debug function to test specific receipt
SELECT test_payable_creation('your-receipt-id-here');
```

### Test 3: Check Workflow Readiness
```sql
-- Run readiness check
SELECT * FROM check_payable_workflow_readiness();
```

## 🔄 **Step-by-Step Workflow Test**

### 1. **Create Test Data** (if needed)
```sql
-- Create test supplier (if none exists)
INSERT INTO suppliers (organization_id, name, contact_person, email, phone, payment_terms_days)
VALUES ('your-org-id', 'Test Supplier', 'John Doe', '<EMAIL>', '************', 30);

-- Create test product (if none exists)  
INSERT INTO products (organization_id, name, sku, category_id)
VALUES ('your-org-id', 'Test Product', 'TEST-001', 'your-category-id');
```

### 2. **Create Purchase Request**
- Navigate to `/purchases/requests`
- Create new request with test product
- Add items with quantities

### 3. **Convert to Purchase Order**
- Approve the purchase request
- Convert to purchase order
- Verify supplier is assigned

### 4. **Create Inventory Receipt**
- Navigate to inventory receipts
- Create receipt from the purchase order
- Add items with quantities and unit costs
- **Important**: Ensure unit costs are > 0

### 5. **Complete Receipt**
- Set status to 'completed'
- **Watch for debug logs** in database logs
- Check `/payables` for new payable

### 6. **Debug if Failed**
- Go to `/payables/debug`
- Find your receipt in the list
- Click "Test" button
- Check result message

## 🎯 **Expected Results**

### ✅ **Success Indicators**
- Debug logs show: "SUCCESS: Payable created for receipt..."
- New payable appears in `/payables`
- Payable has correct:
  - Supplier from PO
  - Amount from receipt items
  - VAT calculation (12%)
  - Due date (receipt date + supplier payment terms)
  - Reference: INV-{receipt_number}

### ❌ **Failure Indicators**
- Debug logs show: "WARNING:" or "ERROR:" messages
- No payable created
- Test function returns error message

## 🚀 **Quick Fix Commands**

### Re-apply Trigger (if missing)
```sql
-- Drop and recreate trigger
DROP TRIGGER IF EXISTS trigger_auto_create_payable_from_receipt ON inventory_receipts;
DROP TRIGGER IF EXISTS trigger_auto_create_payable_from_receipt_debug ON inventory_receipts;

-- Apply debug migration again
-- This will recreate the trigger with debug logging
```

### Force Test Creation
```sql
-- Manually test a specific receipt
SELECT test_payable_creation('your-receipt-id');

-- Check if payable was created
SELECT * FROM payables 
WHERE source_type = 'purchase_receipt' 
AND source_id = 'your-receipt-id';
```

## 📞 **Next Steps**

1. **Apply debug migration**: `00072_debug_auto_creation_workflow.sql`
2. **Visit debug page**: `/payables/debug`
3. **Run workflow readiness check**
4. **Test with your completed receipt**
5. **Check debug logs for specific error**
6. **Report findings** with specific error messages

The debug tools will help identify exactly where the workflow is failing! 🔍
