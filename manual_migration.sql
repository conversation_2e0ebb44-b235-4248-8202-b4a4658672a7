-- Manual migration script to update employee_salary table
-- Run this script manually in the Supabase SQL editor

-- Add organization_id column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'employee_salary' AND column_name = 'organization_id'
    ) THEN
        ALTER TABLE employee_salary ADD COLUMN organization_id UUID;
    END IF;
END $$;

-- Add daily_rate column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'employee_salary' AND column_name = 'daily_rate'
    ) THEN
        ALTER TABLE employee_salary ADD COLUMN daily_rate NUMERIC(12, 2);
    END IF;
END $$;

-- Add rate_type column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'employee_salary' AND column_name = 'rate_type'
    ) THEN
        ALTER TABLE employee_salary ADD COLUMN rate_type TEXT DEFAULT 'monthly';
    END IF;
END $$;

-- Update existing records to set organization_id based on employee's organization
UPDATE employee_salary es
SET organization_id = e.organization_id
FROM employees e
WHERE es.employee_id = e.id
AND es.organization_id IS NULL;

-- Add check constraint for rate_type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.constraint_column_usage 
        WHERE constraint_name = 'employee_salary_rate_type_check'
    ) THEN
        ALTER TABLE employee_salary ADD CONSTRAINT employee_salary_rate_type_check
        CHECK (rate_type IN ('monthly', 'daily'));
    END IF;
END $$;
