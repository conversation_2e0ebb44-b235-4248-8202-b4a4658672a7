# 🏗️ **Payroll to Payables Integration**

## 📋 **Overview**

This document outlines the complete integration between the payroll system and centralized payables system, enabling manual creation of payables from approved payroll periods following accounting best practices.

## 🎯 **Key Features**

### ✅ **Employee Salary Payables**
- Individual payables for each employee's net pay
- Automatic reference number generation (`SAL-{period}-{employee_number}`)
- Due date based on payroll payment date
- Category: `salary`

### ✅ **Government Remittance Payables**
- SSS Contributions (Employee + Employer)
- PhilHealth Contributions (Employee + Employer)
- Pag-IBIG Contributions (Employee + Employer)
- BIR Withholding Tax
- Automatic due date calculation (15th for BIR, 30th for others)
- Category: `government_remittance`

### ✅ **Accounting Compliance**
- Proper accrual accounting principles
- Complete audit trail from payroll to payment
- Multi-tenant security
- Government agency suppliers auto-creation

## 🏗️ **Architecture**

### **Database Schema**

#### **1. Payroll Period Tracking**
```sql
ALTER TABLE payroll_periods 
ADD COLUMN payables_created BOOLEAN DEFAULT FALSE,
ADD COLUMN payables_created_at TIMESTAMPTZ,
ADD COLUMN payables_created_by UUID REFERENCES auth.users(id);
```

#### **2. Payroll-Payables Mapping**
```sql
CREATE TABLE payroll_payable_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    payroll_period_id UUID NOT NULL REFERENCES payroll_periods(id),
    payroll_item_id UUID REFERENCES payroll_items(id), -- NULL for aggregated
    payable_id UUID NOT NULL REFERENCES payables(id),
    payable_type TEXT NOT NULL CHECK (payable_type IN (
        'employee_salary', 'sss_contribution', 'philhealth_contribution',
        'pagibig_contribution', 'withholding_tax', 'loan_deduction', 'other'
    )),
    amount NUMERIC(12,2) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES auth.users(id)
);
```

#### **3. Government Agencies as Suppliers**
```sql
-- Automatic creation of government agencies as suppliers
INSERT INTO suppliers (organization_id, name, supplier_type, payment_terms_days)
VALUES 
  ('org-id', 'Social Security System (SSS)', 'government', 30),
  ('org-id', 'Philippine Health Insurance Corporation (PhilHealth)', 'government', 30),
  ('org-id', 'Home Development Mutual Fund (Pag-IBIG)', 'government', 30),
  ('org-id', 'Bureau of Internal Revenue (BIR)', 'government', 15);
```

### **Service Layer**

#### **Core Service: `payrollPayables.ts`**

```typescript
export const createPayablesFromPayroll = async (
  organizationId: string,
  payrollPeriodId: string,
  userId: string,
  options: PayrollPayableOptions
): Promise<PayrollPayableResult>
```

**Options:**
- `createEmployeePayables`: Individual salary payables
- `createGovernmentPayables`: Government remittances
- `createThirdPartyPayables`: Loans, insurance (future)
- `groupByEmployee`: Grouping strategy
- `customDueDates`: Override default due dates

#### **Helper Functions:**
- `calculateGovernmentContributions()`: Aggregate contributions
- `createEmployeeSalaryPayables()`: Individual employee payables
- `createGovernmentRemittancePayables()`: Government agency payables
- `hasPayrollPayablesBeenCreated()`: Check creation status
- `getPayrollPayablesSummary()`: Summary reporting

### **UI Components**

#### **1. SendPayrollToPayableButton**
```typescript
interface SendPayrollToPayableButtonProps {
  payrollPeriodId: string;
  payrollPeriodName: string;
  payrollStatus: PayrollPeriodStatus;
  totalNetPay: number;
  totalEmployees: number;
  payablesCreated?: boolean;
  onSuccess?: () => void;
}
```

#### **2. PayrollPayablesModal**
- Configuration options for payable creation
- Real-time cost estimation
- Success/error feedback
- Detailed breakdown of created payables

## 🔄 **Workflow**

### **Step 1: Payroll Processing**
1. Create payroll period (Draft)
2. Process payroll calculations (Processing)
3. Review and approve payroll (Approved)

### **Step 2: Payables Creation**
1. Click "Send to Payables" button
2. Configure payable options in modal
3. System creates payables based on selections:
   - Employee salary payables (individual)
   - Government remittance payables (aggregated)
   - Third-party payables (future)

### **Step 3: Payables Management**
1. Payables appear in centralized payables system
2. Due dates automatically calculated
3. Payment processing through payables workflow
4. Complete audit trail maintained

## 💰 **Financial Calculations**

### **Employee Salary Payables**
```typescript
Amount: employee.net_pay
VAT: 0 (salaries not subject to VAT)
Due Date: payroll.payment_date
Reference: SAL-{period_name}-{employee_number}
```

### **Government Remittances**
```typescript
SSS Total: employee_contribution + employer_contribution
PhilHealth Total: employee_contribution + employer_contribution
Pag-IBIG Total: employee_contribution + employer_contribution
BIR Withholding: sum_of_all_withholding_tax

Due Dates:
- BIR: 15th of following month
- SSS/PhilHealth/Pag-IBIG: 30th of following month
```

## 🎛️ **Integration Points**

### **Payroll Period Details View**
- Send to Payables button for approved periods
- Status indicator showing if payables created
- Success callback refreshes period data

### **Payroll List View**
- Send to Payables button in actions column
- Bulk status tracking for multiple periods
- Real-time status updates

### **Payables System**
- Source type: `payroll`
- Source ID: `payroll_period_id`
- Employee/Supplier linkage
- Category-based filtering

## 🧪 **Testing Guide**

### **Prerequisites**
1. Approved payroll period with calculated items
2. Employees with net pay > 0
3. Government contributions calculated

### **Test Scenarios**

#### **Scenario 1: Employee Salary Payables**
1. Navigate to approved payroll period
2. Click "Send to Payables"
3. Enable "Employee Salary Payables"
4. Verify individual payables created for each employee
5. Check amounts match net pay from payroll

#### **Scenario 2: Government Remittances**
1. Enable "Government Remittances" option
2. Verify 4 payables created (SSS, PhilHealth, Pag-IBIG, BIR)
3. Check amounts include both employee and employer contributions
4. Verify due dates (15th for BIR, 30th for others)

#### **Scenario 3: Status Tracking**
1. After successful creation, button shows "Sent to Payables"
2. Payroll period marked as `payables_created: true`
3. Mapping records created in `payroll_payable_mappings`

### **Error Scenarios**
- ❌ Payroll period not approved
- ❌ Payables already created
- ❌ No payroll items found
- ❌ Zero net pay amounts
- ❌ Missing government agency suppliers

## 📊 **Reporting & Analytics**

### **Payroll Payables Summary**
```sql
SELECT 
    payable_type,
    COUNT(*) as count,
    SUM(amount) as total_amount,
    MIN(due_date) as earliest_due_date
FROM payroll_payable_mappings ppm
JOIN payables p ON p.id = ppm.payable_id
WHERE ppm.payroll_period_id = ?
GROUP BY payable_type;
```

### **Government Remittance Tracking**
- Monthly remittance obligations
- Due date monitoring
- Payment status tracking
- Compliance reporting

## 🔒 **Security & Permissions**

### **Multi-tenant Security**
- All operations scoped to organization
- User permissions validated
- Audit trail maintained

### **Data Integrity**
- Mapping table prevents orphaned records
- Referential integrity constraints
- Transaction-based operations

## 🚀 **Future Enhancements**

### **Phase 2: Third-party Payables**
- Loan deduction payables
- Insurance premium payables
- Union dues payables

### **Phase 3: Automation**
- Auto-create payables on approval
- Scheduled government remittances
- Payment calendar integration

### **Phase 4: Advanced Reporting**
- Cash flow projections
- Government compliance reports
- Payroll cost analysis

## 📝 **Maintenance Notes**

### **Government Agency Updates**
- Contact information changes
- Payment terms modifications
- New agency requirements

### **Calculation Updates**
- Contribution rate changes
- Tax law modifications
- Due date adjustments

### **Performance Monitoring**
- Payable creation times
- Database query optimization
- User experience metrics

---

## 🎉 **Success Metrics**

✅ **Functional**
- Payables created accurately from payroll
- Government remittances calculated correctly
- Due dates set appropriately
- Status tracking working

✅ **User Experience**
- Intuitive button placement
- Clear configuration options
- Helpful success/error messages
- Fast response times

✅ **Business Value**
- Reduced manual data entry
- Improved compliance tracking
- Better cash flow visibility
- Complete audit trail

This integration provides a robust foundation for payroll-to-payables workflow while maintaining flexibility for future enhancements and compliance requirements.
