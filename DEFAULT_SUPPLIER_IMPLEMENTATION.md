# Default Supplier System Implementation

## Overview

This implementation creates a "Default Supplier" system that allows purchase orders to be created without requiring a specific supplier selection. Each organization automatically gets a "Default Supplier" that cannot be deleted, providing flexibility for various business scenarios.

## Business Benefits

### ✅ **Supported Scenarios**
1. **Emergency Purchases** - Create POs quickly when supplier details are unknown
2. **Internal Transfers** - Handle inventory movements between departments/locations
3. **Direct Purchases** - Walk-in purchases from unknown vendors
4. **Draft Purchase Orders** - Create POs for budgeting/planning before supplier selection
5. **Multi-Supplier Orders** - Initial creation before supplier assignment
6. **Small Business Flexibility** - Reduced friction for businesses that don't always know suppliers upfront

### ✅ **Key Features**
- **Automatic Creation** - Default supplier created automatically for each organization
- **Cannot Be Deleted** - System prevents deletion of default suppliers
- **Optional Supplier Selection** - Users can leave supplier field empty
- **Clear UI Indicators** - Shows when default supplier is being used
- **Easy Supplier Change** - Can update purchase orders later with actual supplier

## Implementation Details

### **Database Changes**

#### 1. **New Column Added**
- `suppliers.is_default` (BOOLEAN) - Identifies the default supplier for each organization

#### 2. **Constraints Added**
- Unique constraint ensuring only one default supplier per organization
- Trigger preventing deletion of default suppliers

#### 3. **Functions Created**
- `create_default_supplier(organization_id)` - Creates default supplier for an organization
- `get_default_supplier(organization_id)` - Gets or creates default supplier
- Triggers for automatic creation and deletion prevention

### **Application Changes**

#### 1. **Supplier Service Updates**
- **File**: `src/services/supplier.ts`
- **Changes**:
  - Added `getDefaultSupplier()` function
  - Updated `deleteSupplier()` to prevent default supplier deletion
  - Enhanced type definitions to include `is_default` field

#### 2. **Purchase Order Creation Forms**
- **Files**: 
  - `src/views/purchases/CreatePurchaseOrder.tsx`
  - `src/views/purchases/CreatePurchaseOrderFromRequest.tsx`
- **Changes**:
  - Made supplier selection optional in UI
  - Automatic default supplier selection when no supplier chosen
  - Clear indicators showing when default supplier is used
  - Enhanced validation to use default supplier when needed

#### 3. **UI Improvements**
- Supplier field labeled as "Supplier (Optional)"
- Helper text explaining default supplier usage
- Visual indicator showing which default supplier is being used
- Filters out default supplier from regular supplier selection (shows only actual suppliers)

## Migration Scripts

### **1. SKU/Barcode Fix** (Apply First)
- **File**: `manual_sku_barcode_fix.sql`
- **Purpose**: Fixes SKU and barcode constraints to allow multiple NULL values

### **2. Default Supplier System** (Apply Second)
- **File**: `manual_default_supplier_migration.sql`
- **Purpose**: Implements the complete default supplier system

## How to Apply

### **Step 1: Apply SKU/Barcode Fix**
1. Open Supabase Dashboard → SQL Editor
2. Copy and paste contents of `manual_sku_barcode_fix.sql`
3. Execute the script
4. Verify success messages

### **Step 2: Apply Default Supplier System**
1. Open Supabase Dashboard → SQL Editor
2. Copy and paste contents of `manual_default_supplier_migration.sql`
3. Execute the script
4. Verify success messages showing default suppliers created

## User Experience

### **Before Implementation**
- Users **must** select a supplier to create purchase orders
- Error shown if no supplier selected
- Friction for emergency or unknown supplier scenarios

### **After Implementation**
- Users **can** leave supplier field empty
- System automatically uses "Default Supplier"
- Clear indication of which supplier is being used
- Can change supplier later if needed

### **UI Changes**
```
Before: "Supplier *" (Required field)
After:  "Supplier (Optional)" with helper text:
        "Leave empty to use default supplier, or select a specific supplier"
        
When empty: "ℹ️ Using 'Default Supplier' - you can change the supplier later"
```

## Technical Implementation

### **Automatic Supplier Selection Logic**
1. **User selects specific supplier** → Use selected supplier
2. **User leaves field empty** → Use default supplier automatically
3. **No default supplier exists** → Create one automatically
4. **Default supplier missing** → Show error and contact support

### **Database Integrity**
- Only one default supplier per organization (enforced by unique index)
- Default suppliers cannot be deleted (enforced by trigger)
- Automatic creation for new organizations (enforced by trigger)

## Testing Scenarios

### **Test Cases to Verify**
1. ✅ **Create PO without selecting supplier** - Should use default supplier
2. ✅ **Create PO with specific supplier** - Should use selected supplier
3. ✅ **Try to delete default supplier** - Should show error and prevent deletion
4. ✅ **Create new organization** - Should automatically get default supplier
5. ✅ **Edit PO to change supplier** - Should allow changing from default to specific supplier
6. ✅ **Multiple products without SKU/barcode** - Should allow creation

## Rollback Plan

If issues arise, the changes can be rolled back:

### **Database Rollback**
```sql
-- Remove triggers
DROP TRIGGER IF EXISTS create_default_supplier_on_org_creation ON public.organizations;
DROP TRIGGER IF EXISTS prevent_default_supplier_deletion_trigger ON public.suppliers;

-- Remove functions
DROP FUNCTION IF EXISTS create_default_supplier(UUID);
DROP FUNCTION IF EXISTS get_default_supplier(UUID);
DROP FUNCTION IF EXISTS create_default_supplier_trigger();
DROP FUNCTION IF EXISTS prevent_default_supplier_deletion();

-- Remove column (optional - may want to keep data)
-- ALTER TABLE public.suppliers DROP COLUMN IF EXISTS is_default;
```

### **Application Rollback**
- Revert the modified files to their previous versions
- Re-deploy the application

## Support

If you encounter any issues:
1. Check the Supabase logs for error messages
2. Verify both migration scripts were applied successfully
3. Test with a simple purchase order creation
4. Contact support if problems persist

## Summary

This implementation provides a robust solution for making supplier selection optional in purchase orders while maintaining data integrity and providing clear user feedback. The system is designed to be backwards compatible and provides flexibility for various business scenarios.
