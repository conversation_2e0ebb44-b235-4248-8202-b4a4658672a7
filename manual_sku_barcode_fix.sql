-- Manual SQL script to fix SKU and Barcode constraints
-- This allows small businesses to create products without SKU or barcode
-- Run this script in your Supabase SQL Editor

-- IMPORTANT: Apply the SKU/Barcode fix first, then apply the Default Supplier migration

-- Step 1: Check current constraints
SELECT
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint
WHERE conrelid = 'public.products'::regclass
AND (conname LIKE '%sku%' OR conname LIKE '%barcode%');

-- Step 2: Drop existing unique constraints if they exist
DO $$
BEGIN
    -- Drop SKU constraint if it exists
    IF EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conrelid = 'public.products'::regclass
        AND conname = 'products_organization_id_sku_key'
    ) THEN
        ALTER TABLE public.products DROP CONSTRAINT products_organization_id_sku_key;
        RAISE NOTICE 'Dropped products_organization_id_sku_key constraint';
    END IF;

    -- Drop barcode constraint if it exists
    IF EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conrelid = 'public.products'::regclass
        AND conname = 'products_organization_id_barcode_key'
    ) THEN
        ALTER TABLE public.products DROP CONSTRAINT products_organization_id_barcode_key;
        RAISE NOTICE 'Dropped products_organization_id_barcode_key constraint';
    END IF;
END $$;

-- Step 3: Create partial unique indexes that ignore NULL values
-- This allows multiple products with NULL SKU/barcode but ensures uniqueness when they are provided

-- Drop existing indexes if they exist
DROP INDEX IF EXISTS products_organization_sku_unique;
DROP INDEX IF EXISTS products_organization_barcode_unique;

-- Create new partial unique indexes
CREATE UNIQUE INDEX products_organization_sku_unique
ON public.products (organization_id, sku)
WHERE sku IS NOT NULL;

CREATE UNIQUE INDEX products_organization_barcode_unique
ON public.products (organization_id, barcode)
WHERE barcode IS NOT NULL;

-- Step 4: Verify the changes
SELECT
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'products'
AND (indexname LIKE '%sku%' OR indexname LIKE '%barcode%');

-- Step 5: Test the changes by trying to insert products with NULL SKU/barcode
-- (This is just for verification - remove these test inserts after verification)
/*
-- Test insert with NULL SKU and barcode (should work)
INSERT INTO public.products (
    organization_id,
    name,
    unit_price,
    sku,
    barcode,
    created_at,
    updated_at
) VALUES (
    'test-org-id',
    'Test Product 1',
    10.00,
    NULL,
    NULL,
    NOW(),
    NOW()
);

-- Test insert with another NULL SKU and barcode (should work)
INSERT INTO public.products (
    organization_id,
    name,
    unit_price,
    sku,
    barcode,
    created_at,
    updated_at
) VALUES (
    'test-org-id',
    'Test Product 2',
    15.00,
    NULL,
    NULL,
    NOW(),
    NOW()
);

-- Clean up test data
DELETE FROM public.products WHERE name LIKE 'Test Product%';
*/

-- Step 6: Final confirmation messages
DO $$
BEGIN
    RAISE NOTICE 'SKU and Barcode constraints have been updated successfully!';
    RAISE NOTICE 'Products can now be created without SKU or barcode values.';
    RAISE NOTICE 'When SKU or barcode values are provided, they must still be unique within the organization.';
END $$;
