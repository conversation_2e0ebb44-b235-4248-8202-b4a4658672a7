# Payroll System Database Schema Design

## Core Tables

### 1. `payroll_periods`
- `id` (UUID, PK)
- `organization_id` (UUID, FK to organizations)
- `name` (text) - e.g., "January 1-15, 2023"
- `start_date` (date)
- `end_date` (date)
- `payment_date` (date)
- `status` (text) - 'draft', 'processing', 'approved', 'paid'
- `is_thirteenth_month` (boolean) - Flag for 13th month pay periods
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 2. `payroll_items`
- `id` (UUID, PK)
- `organization_id` (UUID, FK to organizations)
- `payroll_period_id` (UUID, FK to payroll_periods)
- `employee_id` (UUID, FK to employees)
- `status` (text) - 'draft', 'calculated', 'approved', 'paid'
- `basic_pay` (numeric) - Base salary for the period
- `gross_pay` (numeric) - Total pay before deductions
- `net_pay` (numeric) - Take-home pay after deductions
- `total_deductions` (numeric) - Sum of all deductions
- `total_allowances` (numeric) - Sum of all allowances
- `total_overtime` (numeric) - Total overtime pay
- `regular_hours` (numeric) - Regular hours worked
- `overtime_hours` (numeric) - Overtime hours worked
- `absent_days` (numeric) - Number of absent days
- `late_minutes` (numeric) - Total minutes late
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 3. `payroll_deductions`
- `id` (UUID, PK)
- `organization_id` (UUID, FK to organizations)
- `payroll_item_id` (UUID, FK to payroll_items)
- `type` (text) - 'sss', 'philhealth', 'pagibig', 'tax', 'loan', 'other'
- `amount` (numeric)
- `description` (text)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 4. `payroll_allowances`
- `id` (UUID, PK)
- `organization_id` (UUID, FK to organizations)
- `payroll_item_id` (UUID, FK to payroll_items)
- `type` (text) - 'transportation', 'meal', 'housing', 'other'
- `amount` (numeric)
- `description` (text)
- `taxable` (boolean) - Whether this allowance is taxable
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 5. `payroll_settings`
- `id` (UUID, PK)
- `organization_id` (UUID, FK to organizations)
- `pay_schedule` (text) - 'monthly', 'semi-monthly', 'weekly'
- `semi_monthly_days` (jsonb) - e.g., [15, 30] for 15th and 30th
- `sss_employer_contribution_rate` (numeric) - Default 8.5%
- `philhealth_employer_contribution_rate` (numeric) - Default 2%
- `pagibig_employer_contribution_rate` (numeric) - Default 2%
- `tax_table_version` (text) - Version of tax table to use
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 6. `time_entries`
- `id` (UUID, PK)
- `organization_id` (UUID, FK to organizations)
- `employee_id` (UUID, FK to employees)
- `date` (date)
- `time_in` (timestamp)
- `time_out` (timestamp)
- `break_start` (timestamp)
- `break_end` (timestamp)
- `status` (text) - 'present', 'absent', 'leave', 'holiday'
- `regular_hours` (numeric)
- `overtime_hours` (numeric)
- `night_diff_hours` (numeric)
- `is_rest_day` (boolean)
- `is_holiday` (boolean)
- `holiday_type` (text) - 'regular', 'special'
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 7. `employee_salary`
- `id` (UUID, PK)
- `organization_id` (UUID, FK to organizations)
- `employee_id` (UUID, FK to employees)
- `rate_type` (text) - 'monthly', 'daily', 'hourly'
- `rate_amount` (numeric)
- `effective_date` (date)
- `end_date` (date) - NULL if current
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 8. `employee_allowances`
- `id` (UUID, PK)
- `organization_id` (UUID, FK to organizations)
- `employee_id` (UUID, FK to employees)
- `type` (text) - 'transportation', 'meal', 'housing', 'other'
- `amount` (numeric)
- `frequency` (text) - 'per_payroll', 'monthly', 'annual'
- `taxable` (boolean)
- `description` (text)
- `effective_date` (date)
- `end_date` (date) - NULL if current
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 9. `employee_deductions`
- `id` (UUID, PK)
- `organization_id` (UUID, FK to organizations)
- `employee_id` (UUID, FK to employees)
- `type` (text) - 'loan', 'advance', 'other'
- `amount` (numeric)
- `frequency` (text) - 'per_payroll', 'monthly', 'one_time'
- `remaining_amount` (numeric) - For loans/advances
- `description` (text)
- `effective_date` (date)
- `end_date` (date) - NULL if current
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 10. `tax_tables`
- `id` (UUID, PK)
- `version` (text) - e.g., "2023"
- `income_from` (numeric)
- `income_to` (numeric)
- `base_tax` (numeric)
- `excess_rate` (numeric)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 11. `sss_contribution_table`
- `id` (UUID, PK)
- `version` (text) - e.g., "2023"
- `salary_from` (numeric)
- `salary_to` (numeric)
- `employee_contribution` (numeric)
- `employer_contribution` (numeric)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 12. `philhealth_contribution_table`
- `id` (UUID, PK)
- `version` (text) - e.g., "2023"
- `salary_from` (numeric)
- `salary_to` (numeric)
- `employee_contribution_rate` (numeric)
- `employer_contribution_rate` (numeric)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 13. `pagibig_contribution_table`
- `id` (UUID, PK)
- `version` (text) - e.g., "2023"
- `salary_from` (numeric)
- `salary_to` (numeric)
- `employee_contribution_rate` (numeric)
- `employer_contribution_rate` (numeric)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 14. `holidays`
- `id` (UUID, PK)
- `organization_id` (UUID, FK to organizations)
- `name` (text)
- `date` (date)
- `type` (text) - 'regular', 'special'
- `created_at` (timestamp)
- `updated_at` (timestamp)
