-- Simple test to create a refund record for dashboard testing
-- Run this in Supabase SQL Editor

-- First, let's see what organizations and sales exist
SELECT 'Organizations:' as info;
SELECT id, name FROM public.organizations LIMIT 3;

SELECT 'Sales:' as info;
SELECT id, invoice_number, total_amount, status, organization_id FROM public.sales WHERE status = 'completed' LIMIT 3;

SELECT 'Users:' as info;
SELECT id, email FROM auth.users LIMIT 3;

-- Now create a simple test refund
-- Replace the UUIDs below with actual values from your database

-- Example: Insert a test refund (you'll need to replace the UUIDs)
/*
INSERT INTO public.refunds (
    organization_id,
    refund_number,
    original_sale_id,
    refund_type,
    status,
    reason,
    reason_notes,
    subtotal,
    tax_amount,
    total_amount,
    restocking_fee,
    refund_method,
    requires_approval,
    created_by,
    created_at,
    processed_at,
    processed_by
) VALUES (
    'YOUR_ORG_ID_HERE',  -- Replace with actual org ID
    'REF-TEST-001',
    'YOUR_SALE_ID_HERE', -- Replace with actual sale ID
    'partial',
    'processed',
    'defective',
    'Test refund for dashboard',
    200.00,
    0.00,
    200.00,
    0.00,
    'cash',
    false,
    'YOUR_USER_ID_HERE', -- Replace with actual user ID
    NOW(),
    NOW(),
    'YOUR_USER_ID_HERE'  -- Replace with actual user ID
);
*/

-- Check if any refunds exist
SELECT 'Current Refunds:' as info;
SELECT 
    refund_number,
    total_amount,
    status,
    created_at,
    organization_id
FROM public.refunds 
ORDER BY created_at DESC 
LIMIT 5;
